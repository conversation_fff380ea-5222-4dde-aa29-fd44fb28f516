package com.gzairports.hz.business.arrival.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @author: lan
 * @Desc: 进港费用查询参数
 * @create: 2024-11-27 17:49
 **/
@Data
public class CostWaybillQuery {

    /** 结算客户 多个用逗号隔开 */
    private String settleUser;

    /** 结算客户 */
    private String[] settleUserArr;

    /** 结算客户简称 多个用逗号隔开 */
    private String settleUserAbb;

    /** 结算客户简称 */
    private String[] settleUserAbbArr;

    /** 运单类型 */
    private String waybillType;

    /** 运单号 */
    private String waybillCode;

    /** 办单开始时间查询参数 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 办单结束时间查询参数 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 代理人 */
    private String agent;

    /** 运单状态 */
    private String status;

    /** 流水号 */
    private String serialNo;

    /** 支付方式 */
    private String payMethod;

    /** 是否作废 */
    private String isCancel;

    private Integer pageNum;

    private Integer pageSize;
}
