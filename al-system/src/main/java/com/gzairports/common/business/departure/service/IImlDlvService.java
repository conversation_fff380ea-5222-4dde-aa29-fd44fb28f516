package com.gzairports.common.business.departure.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.business.departure.domain.vo.TImpDlv;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 运单提货记录Service接口
 *
 * <AUTHOR>
 * @date 2023-09-25
 */
public interface IImlDlvService extends IService<TImpDlv> {

    /***
     *
     * @param startTime
     * @param endTime
     * @return
     */
    Map<String, List<TImpDlv>> listByTime(Date startTime, Date endTime);

    Map<String, List<TImpDlv>> listByBillid(Set<String> stocknoSet);
}
