package com.gzairports.hz.business.abnormal.domain;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;


/**
 * 无标签货物对象 hz_no_label
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
@Data
@TableName("hz_no_label")
public class HzNoLabel {

    /** 主键id */
    private Long id;

    /** 航班号 */
    @Excel(name = "航班号")
    private String flightNo;

    /** 始发站 */
    @Excel(name = "始发站")
    private String sourcePort;

    /** 目的站 */
    @Excel(name = "目的站")
    private String desPort;

    /** 件数 */
    @Excel(name = "件数")
    private Integer pieces;

    /** 登记人 */
    @Excel(name = "登记人")
    private String username;

    /** 登记时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "登记时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date registerTime;

    /** 是否结案 0 否 1 是 */
    @Excel(name = "是否结案 0 否 1 是")
    private Integer closeCase;

    /** 图片地址 */
    private String imgUrl;

    /** 备注 */
    private String remark;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 处理记录 */
    @TableField(exist = false)
    private List<HzNoLabelRecord> records;

}
