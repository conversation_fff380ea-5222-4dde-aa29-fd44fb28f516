package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BaseCargoIncompatible;
import com.gzairports.common.basedata.domain.query.CargoIncompatibleQuery;
import com.gzairports.common.basedata.service.ICargoIncompatibleService;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 货物不兼容性Controller
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@RestController
@RequestMapping("/base/incompatible")
@Api(tags = "货物不兼容性数据接口")
public class CargoIncompatibleController extends BaseController {

    @Autowired
    private ICargoIncompatibleService cargoIncompatibleService;

    /**
     * 查询货物不兼容性列表
     */
//    @PreAuthorize("@ss.hasPermi('base:incompatible:cargoIncompatibleList')")
    @GetMapping("/cargoIncompatibleList")
    @ApiOperation(value = "查询货物不兼容性列表")
    public TableDataInfo cargoIncompatibleList(CargoIncompatibleQuery query)
    {
        startPage();
        List<BaseCargoIncompatible> list = cargoIncompatibleService.selectCargoIncompatibleList(query);
        return getDataTable(list);
    }

    /**
     * 导出货物不兼容性
     */
    @PreAuthorize("@ss.hasPermi('base:incompatible:exportCargoIncompatible')")
    @Log(title = "导出货物不兼容性", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCargoIncompatible")
    @ApiOperation(value = "导出货物不兼容性")
    public void exportCargoIncompatible(HttpServletResponse response, CargoIncompatibleQuery query)
    {
        List<BaseCargoIncompatible> list = cargoIncompatibleService.selectCargoIncompatibleList(query);
        ExcelUtil<BaseCargoIncompatible> util = new ExcelUtil<BaseCargoIncompatible>(BaseCargoIncompatible.class);
        util.exportExcel(response, list, "货物不兼容性");
    }


    /**
     * 导出货物不兼容性模板
     */
    @PreAuthorize("@ss.hasPermi('base:incompatible:exportCargoIncompatible')")
    @Log(title = "导出货物不兼容性模板", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCargoIncompatibleNull")
    @ApiOperation(value = "导出货物不兼容性模板")
    public void exportCargoIncompatibleNull(HttpServletResponse response)
    {
        List<BaseCargoIncompatible> list = new ArrayList<>();
        ExcelUtil<BaseCargoIncompatible> util = new ExcelUtil<BaseCargoIncompatible>(BaseCargoIncompatible.class);
        util.exportExcel(response, list, "货物不兼容性模板");
    }

    /**
     * 导入货物不兼容性
     */
    @PreAuthorize("@ss.hasPermi('base:incompatible:importCargoIncompatible')")
    @Log(title = "导入货物不兼容性", businessType = BusinessType.IMPORT)
    @PostMapping("/importCargoIncompatible")
    @ApiOperation(value = "导入货物不兼容性")
    public AjaxResult importCargoIncompatible(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<BaseCargoIncompatible> util = new ExcelUtil<BaseCargoIncompatible>(BaseCargoIncompatible.class);
        List<BaseCargoIncompatible> incompatibles = util.importExcel(file.getInputStream());
        String message = cargoIncompatibleService.importCargoIncompatible(incompatibles, updateSupport);
        return success(message);
    }

    /**
     * 新增货物不兼容性
     */
    @PreAuthorize("@ss.hasPermi('base:incompatible:addCargoIncompatible')")
    @Log(title = "新增货物不兼容性", businessType = BusinessType.INSERT)
    @PostMapping("/addCargoIncompatible")
    @ApiOperation(value = "新增货物不兼容性")
    public AjaxResult addCargoIncompatible(@RequestBody BaseCargoIncompatible incompatible)
    {
        return toAjax(cargoIncompatibleService.addCargoIncompatible(incompatible));
    }

    /**
     * 修改货物不兼容性
     */
    @PreAuthorize("@ss.hasPermi('base:incompatible:editCargoIncompatible')")
    @Log(title = "修改货物不兼容性", businessType = BusinessType.UPDATE)
    @PostMapping("/editCargoIncompatible")
    @ApiOperation(value = "修改货物不兼容性")
    public AjaxResult editCargoIncompatible(@RequestBody BaseCargoIncompatible incompatible)
    {
        return toAjax(cargoIncompatibleService.editCargoIncompatible(incompatible));
    }

    /**
     * 删除货物不兼容性
     */
    @PreAuthorize("@ss.hasPermi('base:incompatible:delCargoIncompatible')")
    @Log(title = "删除货物不兼容性", businessType = BusinessType.UPDATE)
    @GetMapping("/delCargoIncompatible/{ids}")
    @ApiOperation(value = "删除货物不兼容性")
    public AjaxResult delCargoIncompatible(@PathVariable("ids") Long[] ids)
    {
        return toAjax(cargoIncompatibleService.delCargoIncompatible(ids));
    }
}
