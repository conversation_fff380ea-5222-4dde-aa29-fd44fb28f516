package com.gzairports.wl.charge.domain.vo;

import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 航司运价条目返回数据
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@Data
public class FreightRateAirItemVO {

    /** 主键id */
    @Excel(name = "id")
    private Long id;

    /** 所属运价名称 */
    @Excel(name = "运价名称")
    private String rateName;

    /** 条款名称 */
    @Excel(name = "条款名称")
    private String clauseName;

    /** 货品大类 */
    private String cargoCategory;

    /** 货品代码 */
    @Excel(name = "货品代码")
    private String cargoCode;

    /** 特货代码 */
    @Excel(name = "特货代码")
    private String specialCode;

    /** 始发城市 */
    @Excel(name = "始发城市")
    private String departureCity;

    /** 目的城市 */
    @Excel(name = "目的城市")
    private String destinationCity;

    /** 中转城市 */
    @Excel(name = "中转城市")
    private String transitCity;

    /** 特货代码 */
    @Excel(name = "特货代码")
    private String specialCargoCode;

    /** 主单前缀 */
    @Excel(name = "主单前缀")
    private String masterPrefix;

    /** 结算最低运费 */
    @Excel(name = "结算最低运费")
    private BigDecimal minimumFreight;

    /** 票面最低运费 */
    @Excel(name = "票面最低运费")
    private BigDecimal faceMinimumFreight;

    /** 储运注意事项 */
    @Excel(name = "储运注意事项")
    private String storageShippingNotes;

    /** 结算注意事项 */
    @Excel(name = "结算注意事项")
    private String settlementNotes;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    /** 生效开始时间 */
    private Date startTime;

    /** 生效截止时间 */
    private Date endTime;

    /** 是否生效中 */
    private Integer isEffective;

}
