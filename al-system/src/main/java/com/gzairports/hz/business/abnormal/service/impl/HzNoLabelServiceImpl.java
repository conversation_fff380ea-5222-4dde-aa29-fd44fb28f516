package com.gzairports.hz.business.abnormal.service.impl;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.utils.DateUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.hz.business.abnormal.domain.HzNoLabel;
import com.gzairports.hz.business.abnormal.domain.HzNoLabelRecord;
import com.gzairports.hz.business.abnormal.domain.vo.HzNoLabelVo;
import com.gzairports.hz.business.abnormal.mapper.HzNoLabelMapper;
import com.gzairports.hz.business.abnormal.mapper.HzNoLabelRecordMapper;
import com.gzairports.hz.business.abnormal.service.IHzNoLabelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 无标签货物Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-26
 */
@Service
public class HzNoLabelServiceImpl extends ServiceImpl<HzNoLabelMapper, HzNoLabel> implements IHzNoLabelService
{
    @Autowired
    private HzNoLabelMapper hzNoLabelMapper;

    @Autowired
    private HzNoLabelRecordMapper recordMapper;

    /**
     * 查询无标签货物
     * 
     * @param id 无标签货物ID
     * @return 无标签货物
     */
    @Override
    public HzNoLabel selectHzNoLabelById(Long id)
    {
        HzNoLabel hzNoLabel = hzNoLabelMapper.selectHzNoLabelById(id);
        if (hzNoLabel != null){
            List<HzNoLabelRecord> records = recordMapper.selectList(new QueryWrapper<HzNoLabelRecord>().eq("label_id", id));
            if (!CollectionUtils.isEmpty(records)){
                hzNoLabel.setRecords(records);
            }
        }
        return hzNoLabel;
    }

    /**
     * 查询无标签货物列表
     * 
     * @param hzNoLabel 无标签货物
     * @return 无标签货物
     */
    @Override
    public List<HzNoLabel> selectHzNoLabelList(HzNoLabel hzNoLabel)
    {
        return hzNoLabelMapper.selectHzNoLabelList(hzNoLabel);
    }

    /**
     * 新增无标签货物
     * 
     * @param hzNoLabel 无标签货物
     * @return 结果
     */
    @Override
    public int insertHzNoLabel(HzNoLabel hzNoLabel)
    {
        hzNoLabel.setCreateTime(DateUtils.getNowDate());
        hzNoLabel.setCreateBy(SecurityUtils.getUsername());
        return hzNoLabelMapper.insertHzNoLabel(hzNoLabel);
    }

    /**
     * 修改无标签货物
     * 
     * @param hzNoLabel 无标签货物
     * @return 结果
     */
    @Override
    public int updateHzNoLabel(HzNoLabel hzNoLabel)
    {
        return getBaseMapper().updateHzNoLabel(hzNoLabel);
    }

    /**
     * 处理无标签货物
     * @param vo 处理数据
     * @return 结果
     */
    @Override
    public int handleHzNoLabel(HzNoLabelVo vo) {
        HzNoLabel hzNoLabel = hzNoLabelMapper.selectById(vo.getId());
        hzNoLabel.setCloseCase(vo.getCloseCase());
        hzNoLabelMapper.updateById(hzNoLabel);
        HzNoLabelRecord record = new HzNoLabelRecord();
        record.setRecordBy(SecurityUtils.getUsername());
        record.setLabelId(vo.getId());
        record.setRecordRemark(vo.getRecordRemark());
        record.setRecordTime(new Date());
        return recordMapper.insert(record);
    }
}
