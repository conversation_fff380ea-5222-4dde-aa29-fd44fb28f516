package com.gzairports.common.business.arrival.mapper;



import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzairports.common.business.departure.domain.vo.YesterdayFlightInfo;
import com.gzairports.common.securitySubmit.domain.WaybillFlightLoadInfoData;
import com.gzairports.hz.business.arrival.domain.query.FlightFileQuery;
import com.gzairports.hz.business.arrival.domain.vo.AppFlightListVo;
import com.gzairports.hz.business.arrival.domain.vo.AppFlightVo;
import com.gzairports.hz.business.arrival.domain.vo.TallyManifestVo;
import com.gzairports.common.business.departure.domain.FlightInfo;
import com.gzairports.hz.business.cable.domain.vo.MsgFlightInfoVO;
import com.gzairports.hz.business.cable.domain.vo.QHDBIdVO;
import com.gzairports.hz.business.departure.domain.query.AddQuery;
import com.gzairports.hz.business.departure.domain.query.BillExportQuery;
import com.gzairports.hz.business.departure.domain.query.FlightInfoQuery;
import com.gzairports.hz.business.departure.domain.vo.FlightInfoVO;
import com.gzairports.hz.business.departure.domain.vo.FlightLoadWaybillVO;
import com.gzairports.hz.business.departure.domain.vo.LoadFlightVo;
import com.gzairports.hz.business.departure.domain.vo.PullDownInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 航班信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-09-18
 */
@Mapper
public interface FlightInfoMapper extends BaseMapper<FlightInfo>
{
    /**
     * 查询航班信息
     * 
     * @param flightId 航班信息ID
     * @return 航班信息
     */
    FlightInfo selectBusinessFlightInfoById(Long flightId);

    /**
     * 查询航班信息列表
     * 
     * @param query 航班信息
     * @return 航班信息集合
     */
    List<FlightInfoVO> selectBusinessFlightInfoList(FlightInfoQuery query);

    /**
     * 修改航班信息
     * 
     * @param businessFlightInfo 航班信息
     * @return 结果
     */
    int updateBusinessFlightInfo(FlightInfo businessFlightInfo);

    /**
     * 排班显示
     *
     * @return 当天未起飞的航班
     */
    List<FlightInfoVO> selectShowList();

    /**
     * 根据航班号和航班日期查询航班是否起飞
     * @param flightNo1 航班号
     * @param execDate 航班日期
     * @return 结果
     */
    boolean selectFlightIsFly(@Param("flightNo1") String flightNo1,@Param("execDate") Date execDate);

    /**
     * 根据航班号和航班日期查询航班信息
     * */
    FlightInfo selectByFlightNoAndDate(@Param("flightNo") String flightNo,@Param("execDate") String execDate,@Param("isOffin") String isOffin);


    /**
     * 根据航段id查询航班信息
     * @param legId 航段id
     * @return 结果
     */
    TallyManifestVo selectByLegId(Long legId);

    /**
     * app进港理货航班列表
     * @param page 分页参数
     * @param query 查询条件
     * @return 航班列表
     */
    Page<AppFlightListVo> appFlightList(@Param("page") Page<AppFlightListVo> page,@Param("query") FlightFileQuery query);

    /**
     * 根据航段查询航班操作
     * @param legId 航段id
     * @return 结果
     */
    String getFlightOper(Long legId);

    /**
     * 根据理货id1查询航班信息
     * @param tallyId 理货id
     * @return 结果
     */
    FlightInfo selectInfoByTallyId(Long tallyId);

    /**
     * 查询航班日期
     * @param flightNo 航班号
     * @return 航班信息
     */
    String getDateByNo(String flightNo);

    /**
     * 根据配载信息查询航班id
     * @param vo 配载信息
     * @return 航班id
     */
    Long selectIdByVo(PullDownInfoVo vo);

    /**
     * 查询当前条件下的航班数据
     * @param query 查询条件
     * @return 条数
     */
    Integer selectTotalCount(FlightFileQuery query);

    /**
     * 查询当前条件下的航班数据
     * @param query 查询条件
     * @return 条数
     */
    Integer selectTallyInfoCount(FlightFileQuery query);

    /**
     * 重新开放制单
     * @param flightId 航班id
     * @return 结果
     */
    int openCreate(Long flightId);

    /**
     * 关闭开放制单
     * @param flightId 航班id
     */
    void closeCreate(Long flightId);

    /**
     * 修改航班配载状态
     * @param flightId 航班id
     */
    void updatePre(Long flightId);

    /**
     * 根据航段查询航班号
     * @param legId 航段id
     * @return 航班号
     */
    FlightInfo selectFlightNo(Long legId);

    LoadFlightVo selectLoadVo(AddQuery query);

    int compFlight(Long flightId);

    int openFlight(Long flightId);

    List<YesterdayFlightInfo> selectYesterdayList(String format);

    void updateYesterdayInfo(Long flightId);

    void updateYesterdayInfoVo(Long flightId);

    List<YesterdayFlightInfo> selectYesterdayListVo();

    /**
     * 根据航班id查询实际起飞时间
     * */
    String selectStartTime(String flightId);

    /**
     * 根据运单id查询配载的航班id
     * */
    List<QHDBIdVO> selectFlightIdByWaybillId(@Param("waybillIdList") List<Long> waybillIdList);

    List<MsgFlightInfoVO> selectFlightInfoById(@Param("flightId") Long flightId,@Param("legId") Long legId);

    int selectIsComp(Long id);

    FlightInfoVO selectInfoVo(Long id);

    /** 根据航班id查询配载运单数据 */
    List<WaybillFlightLoadInfoData> selectWaybillDataByFlightId(Long flightId);

    /** 根据理货id查询航班 */
    FlightInfo selectArrFlight(Long tallyId);

    List<Long> selectFlightIds(BillExportQuery query);

    List<FlightLoadWaybillVO> selectFlightWaybills(BillExportQuery query);
}
