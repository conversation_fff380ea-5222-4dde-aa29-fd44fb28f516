package com.gzairports.web.controller.departure;

import com.gzairports.common.business.departure.service.IWaybillTraceService;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.wl.departure.domain.vo.NewWaybillTraceVO;
import com.gzairports.wl.departure.domain.vo.WaybillTraceVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 运单跟踪Controller
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@RestController
@RequestMapping("/dep/trace")
public class WaybillTraceController extends BaseController {

    @Autowired
    private IWaybillTraceService waybillTraceService;

    /**
     * 查询出港运单跟踪数据
     * 1-AWBF分单, 0-AW<PERSON>主单, 2-AWBM邮件单
     */
//    @PreAuthorize("@ss.hasPermi('dep:trace:info')")
    @GetMapping("/info")
    @ApiOperation(value = "查询运单跟踪数据")
    public AjaxResult list(Integer type, String waybillCode){
        WaybillTraceVo vo = waybillTraceService.selectTraceInfo(type,waybillCode);
        return AjaxResult.success(vo);
    }

    /**
     * 查询进港出港运单跟踪数据
     * 1-AWBF分单, 0-AWBA主单, 2-AWBM邮件单
     */
//    @PreAuthorize("@ss.hasPermi('dep:trace:info')")
    @GetMapping("/all/waybill/info")
    @ApiOperation(value = "查询进港出港运单跟踪数据")
    public AjaxResult selectAllStatusInfo(Integer type, String waybillCode){
        List<WaybillTraceVo> vo = waybillTraceService.selectAllStatusInfo(type,waybillCode);
//        Map<String, WaybillTraceVo> mapResult = vo.stream()
//                .collect(Collectors.toMap(WaybillTraceVo::getBusinessType, WaybillTraceVo -> WaybillTraceVo));
        return AjaxResult.success(vo);
    }

    /**
     * 新运单查询接口
     */
//    @PreAuthorize("@ss.hasPermi('dep:trace:info')")
    @GetMapping("/newWaybill/info")
    @ApiOperation(value = "查询进港出港运单跟踪数据")
    public AjaxResult selectWaybillStatusInfo(String waybillCode){
        List<NewWaybillTraceVO> vo = waybillTraceService.selectWaybillStatusInfo(waybillCode);
        return AjaxResult.success(vo);
    }


    /**
     * 根据后八位查询运单号
     * */
    @GetMapping("/infoWaybillCode/{code}")
    @ApiOperation(value = "查询运单跟踪数据")
    public AjaxResult infoWaybillCode(@PathVariable String code){
        List<String> waybillCodeList = waybillTraceService.selectInfoWaybillCode(code);
        return AjaxResult.success(waybillCodeList);
    }
}
