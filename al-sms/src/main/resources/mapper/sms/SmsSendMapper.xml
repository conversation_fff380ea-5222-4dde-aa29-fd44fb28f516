<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gzairports.sms.mapper.sms.SmsSendMapper">


    <insert id="agentSendSmsConfig">
        insert into hz_fbm_sms_agent_user (agent_id, user_id)
        values
        <foreach collection="agentUserList" item="agentUser" separator=",">
            (#{agentUser.agentId}, #{agentUser.userId})
        </foreach>
    </insert>
    <delete id="deleteAgentSendSmsConfig">
        delete from hz_fbm_sms_agent_user where agent_id in
        <foreach collection="agentIds" item="agentId" open="(" separator="," close=")">
            #{agentId}
        </foreach>
    </delete>
    <select id="selectSmsReceiveUser" resultType="com.gzairports.sms.domain.vo.SmsReceiveUserVo">
        select u.user_id, u.user_name, u.phonenumber,
               d.dept_id, d.dept_name,
               ba.id as agent_id,
               r.id as role_id, r.role_name
        from sys_user u
        left join sys_dept d on u.dept_id = d.dept_id
        left join base_agent ba on ba.id = d.dept_id
        left join sys_user_role ur on u.user_id = ur.user_id
        left join sys_role r on ur.role_id = r.role_id
        <where>
            <if test="username != null and username != ''">
                u.user_name like concat('%', #{username}, '%')
            </if>
            <if test="phonenumber != null and phonenumber != ''">
                u.phonenumber = #{phonenumber}
            </if>
            <if test="deptIds != null and deptIds.size() > 0">
                and
                <foreach collection="deptIds" item="deptId" open="(" separator="OR" close=")">
                    find_in_set(#{deptId}, d.ancestors)
                </foreach>
            </if>
            <if test="roleIds != null and roleIds.size() > 0">
                and r.role_id in
                <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
                    {roleId}
                </foreach>
            </if>
        </where>
    </select>
</mapper>