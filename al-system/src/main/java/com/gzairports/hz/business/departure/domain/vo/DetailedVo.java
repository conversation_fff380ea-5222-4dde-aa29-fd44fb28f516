package com.gzairports.hz.business.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 运单明细状态列表返回数据
 * <AUTHOR>
 * @date 2024-06-24
 */
@Data
public class DetailedVo {

    /** 主键id */
    private Long id;

    /** 时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;

    /** 航班号 */
    private String flightNo;

    /** 是否真实收运 */
    private Integer isReal;

    /** 运单id */
    private Long waybillId;

    /** 运单号 */
    private String waybillCode;

    /** uld号 */
    private String uld;

    /** 装配舱位 */
    private String cabin;

    /** 操作人 */
    private String operName;

    /** 操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operTime;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 仓库 */
    private String storeName;

    /** 库位 */
    private String locator;

    /** 状态 */
    private String status;

    /** 备注 */
    private String remark;

}
