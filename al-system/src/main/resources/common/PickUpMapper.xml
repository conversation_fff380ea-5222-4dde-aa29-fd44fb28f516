<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.arrival.mapper.PickUpMapper">
    <update id="updatePrintCount">
        update all_in_pick_up set print_count = print_count + 1 where id = #{id}
    </update>
    <select id="selectByQuery" resultType="com.gzairports.common.business.arrival.domain.vo.PickedUpVo">
        select aipu.id, aipu.serial_no, aipu.total_count, aipu.total_cost, aipu.customer_name, aipu.customer_id_no,
        aipu.handle_by, aipu.pick_up_time, aaw.waybill_code,
        aipu.is_pay, aipu.pay_status, aaw.flight_no1 as flight_no, aaw.waybill_code, aaw.consign as consignee,
        aw.can_pick_up_weight as waybillWeight,aw.can_pick_up_quantity as waybillQuantity,aw.cost_sum as waybillCost
        from all_in_pick_up aipu
        left join all_pick_up_waybill aw on aipu.id = aw.pick_up_id
        left join all_air_waybill aaw on aaw.waybill_code = aw.waybill_code
        <where>
            <if test="serialNo != null and serialNo != ''">
                and aipu.serial_no like '%${serialNo}%'
            </if>
            <if test="startTime != null">
                and aipu.pick_up_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and aipu.pick_up_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="waybillCode != null and waybillCode != ''">
                and aaw.waybill_code like '%${waybillCode}%'
            </if>
            <if test="customerName != null and customerName != ''">
                and aipu.customer_name like '%${customerName}%'
            </if>
            <if test="customerIdNo != null and customerIdNo != ''">
                and aipu.customer_id_no like '%${customerIdNo}%'
            </if>
            <if test="handleBy != null and handleBy != ''">
                and aipu.handle_by = #{handleBy}
            </if>
            <if test="deptIdList != null and deptIdList.size() > 0">
                and aipu.dept_id in
                <foreach item="deptId" index="index" collection="deptIdList" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="isPay != null">
                and aipu.is_pay = #{isPay}
            </if>
            <if test="flightNo != null">
                and aaw.flight_no1 = #{flightNo}
            </if>
            <if test="consignee != null and consignee != ''">
                and aaw.consign = #{consignee}
            </if>
            <if test="waybillCode != null and waybillCode != ''">
                and aaw.waybill_code like CONCAT('%',#{waybillCode},'%')
            </if>
        </where>
    </select>
    <select id="selectWaybillByQuery"
            resultType="com.gzairports.common.business.arrival.domain.vo.PickedUpWaybillVo">
        select aipu.id, aipu.serial_no, aaw.waybill_code,aw.tally_id,
               aw.can_pick_up_weight as waybillWeight,aw.can_pick_up_quantity as waybillQuantity
        from all_in_pick_up aipu
        left join all_pick_up_waybill aw on aipu.id = aw.pick_up_id
        left join all_air_waybill aaw on aaw.waybill_code = aw.waybill_code
        <where>
            aaw.type = 'ARR'
            <if test="serialNo != null and serialNo != ''">
                and aipu.serial_no like '%${serialNo}%'
            </if>
            <if test="startTime != null">
                and aipu.pick_up_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and aipu.pick_up_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="waybillCode != null and waybillCode != ''">
                and aaw.waybill_code like '%${waybillCode}%'
            </if>
            <if test="customerName != null and customerName != ''">
                and aipu.customer_name like '%${customerName}%'
            </if>
            <if test="customerIdNo != null and customerIdNo != ''">
                and aipu.customer_id_no like '%${customerIdNo}%'
            </if>
            <if test="handleBy != null and handleBy != ''">
                and aipu.handle_by = #{handleBy}
            </if>
            <if test="deptIdList != null and deptIdList.size() > 0">
                and aipu.dept_id in
                <foreach item="deptId" index="index" collection="deptIdList" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="isPay != null">
                and aipu.is_pay = #{isPay}
            </if>
            <if test="flightNo != null">
                and aaw.flight_no1 = #{flightNo}
            </if>
            <if test="consignee != null and consignee != ''">
                and aaw.consign = #{consignee}
            </if>
            <if test="waybillCode != null and waybillCode != ''">
                and aaw.waybill_code like CONCAT('%',#{waybillCode},'%')
            </if>
        </where>
    </select>
    <select id="selectApplyList" resultType="com.gzairports.common.business.arrival.domain.vo.ApplyEditVo">
        select pu.id, pu.serial_no, pu.total_count, pu.total_quantity, pu.total_cost, pu.total_weight, pu.handle_by, ae.apply_time,ae.apply_status
        from all_in_apply_edit ae
            left join all_in_pick_up pu on pu.serial_no = ae.serial_no
        <where>
            <if test="serialNo != null and serialNo != ''">
                and pu.serial_no like '%${serialNo}%'
            </if>
            <if test="startTime != null">
                and pu.pick_up_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and pu.pick_up_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="applyStatus != null and applyStatus != ''">
                and ae.apply_status = #{applyStatus}
            </if>
            <if test="customerName != null and customerName != ''">
                and pu.customer_name = #{customerName}
            </if>
            <if test="customerIdNo != null and customerIdNo != ''">
                and pu.customer_id_no = #{customerIdNo}
            </if>
            <if test="handleBy != null and handleBy != ''">
                and pu.handle_by = #{handleBy}
            </if>
        </where>
    </select>
    <select id="selectOutOrderById"
            resultType="com.gzairports.common.business.arrival.domain.vo.PrintOutOrderVo">
        select id, customer_name, customer_id_type, customer_id_no, serial_no, pick_up_code, out_time, customer_phone,consign, consign_id_no, consign_phone
        from all_in_pick_up
        where id = #{pickUpId}
    </select>
    <select id="selectEditById" resultType="com.gzairports.common.business.arrival.domain.vo.HzApplyEditVo">
        select au.id, au.serial_no, au.total_quantity, au.total_weight, ae.id as editId, ae.apply_edit
        from all_in_pick_up au
            left join all_in_apply_edit ae on ae.serial_no = au.serial_no where au.id = #{id}
    </select>
    <select id="selectEditBySerialNo" resultType="com.gzairports.common.business.arrival.domain.vo.HzApplyEditVo">
        select au.id, au.serial_no, au.total_quantity, au.total_weight
        from all_in_pick_up au
        where au.serial_no = #{serialNo}
    </select>
    <select id="selectOutList" resultType="com.gzairports.common.business.arrival.domain.vo.PickOrderVo">
        select id, serial_no, total_count, total_quantity, total_charge_weight, total_cost from all_in_pick_up
        <where>
            is_pay = 1 and pick_up_code is not null
            <if test="serialNo != null and serialNo != ''">
                and serial_no = #{serialNo}
            </if>
            <if test="pickUpCode != null and pickUpCode != ''">
                and pick_up_code = #{pickUpCode} and DATE_FORMAT(pick_up_time, '%Y-%m') = DATE_FORMAT(#{queryDate}, '%Y-%m')
            </if>
        </where>
        order by pick_up_time desc limit 1
    </select>
    <select id="selectPickUpOutVo" resultType="com.gzairports.common.business.arrival.domain.vo.PickUpOutVo">
        select total_count, total_quantity, total_weight, out_time, pay_method, dept_id, total_cost as paid from all_in_pick_up where id = #{pickUpId}
    </select>
    <select id="selectQueryList" resultType="com.gzairports.hz.business.arrival.domain.vo.WaybillQueryVo">
        select aaw.id as waybillId, aaw.waybill_code, aaw.replenish_bill, aaw.file_arr, aaw.prioritize, aaw.transfer_bill, aaw.source_port,
               aaw.carrier1, aaw.des1, aaw.carrier2, aaw.des2, aaw.carrier3, aaw.des3, aaw.des_port, aaw.quantity, aaw.weight, aaw.charge_weight,
               au.total_quantity as cabinQuantity, au.total_weight as cabinWeight, aaw.shipper, aaw.consign, aaw.consign_id_car, aaw.consign_phone,
               aaw.cargo_name, aaw.special_cargo_code1, aaw.cold_store, aaw.customs_supervision, aaw.is_transfer, aaw.transfer_no, aaw.arr_pay,
               aaw.cost_sum, aaw.remark
        from all_in_pick_up au
            left join all_pick_up_waybill aw on au.id = aw.pick_up_id
            left join all_air_waybill aaw on aaw.waybill_code = aw.waybill_code
            left join hz_arr_tally hat on hat.id = aw.tally_id
            left join hz_flight_load hfl on hfl.id = hat.leg_id
            left join all_flight_info afi on hfl.flight_id = afi.flight_id
        where aaw.is_del = 0
        <if test="waybillCode != null and waybillCode !=''">
            and aaw.waybill_code = #{waybillCode}
        </if>
        <if test="domint != null and domint != ''">
            and aaw.domint = #{domint}
        </if>
        <if test="type == 'ARR'">
            and aaw.type = #{type}
        </if>
        <if test="type == 'TRANSFER'">
            and aaw.transfer_bill = 1
        </if>
        <if test="sourcePort != null and sourcePort !=''">
            and aaw.source_port = #{sourcePort}
        </if>
        <if test="desPort != null and desPort !=''">
            and aaw.des_port = #{desPort}
        </if>
        <if test="shipper != null and shipper !=''">
            and aaw.shipper = #{shipper}
        </if>
        <if test="consign != null and consign !=''">
            and aaw.consign = #{consign}
        </if>
        <if test="agentCode != null and agentCode !=''">
            and aaw.agent_code = #{agentCode}
        </if>
        <if test="specialCode != null and specialCode !=''">
            and aaw.special_cargo_code = #{specialCode}
        </if>
        <if test="carrier != null and carrier !=''">
            and aaw.carrier = #{carrier}
        </if>
        <if test="flightNo != null and flightNo !=''">
            and CONCAT(afi.air_ways,afi.flight_no) = #{flightNo}
        </if>
        <if test="cargoCode != null and cargoCode !=''">
            and aaw.cargo_code = #{cargoCode}
        </if>
        <if test="startTime != null">
            and au.pick_up_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            and au.pick_up_time <![CDATA[<=]]> #{endTime}
        </if>
    </select>
    <select id="selectBdList" resultType="com.gzairports.hz.business.arrival.domain.vo.BdVo">
        select aipu.pick_up_time, aipu.customer_name, aipu.customer_id_type, aipu.customer_id_no, apuw.can_pick_up_quantity as orderQuantity,
               apuw.can_pick_up_weight as orderWeight, apuw.cost_sum,
               aipu.handle_by, aipu.serial_no, aipu.remark, apuw.tally_id
        from all_pick_up_waybill apuw left join all_in_pick_up aipu on aipu.id = apuw.pick_up_id where apuw.waybill_code = #{waybillCode}
    </select>
    <select id="arrH5List" resultType="com.gzairports.common.business.arrival.domain.vo.H5PickUpOutVo">
        select id as pickUpId, pick_up_time, total_count, total_quantity, total_weight, serial_no from all_in_pick_up
        where dept_id = #{deptId}
        <if test="serialNo != null and serialNo != ''">
            and serial_no like '%${serialNo}%'
        </if>
        <if test="startTime != null">
            and pick_up_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            and pick_up_time <![CDATA[<=]]> #{endTime}
        </if>
    </select>
    <select id="selectH5Info" resultType="com.gzairports.common.business.arrival.domain.vo.H5PickUpOutVo">
        select id as pickUpId, total_quantity, total_weight, serial_no, handle_by from all_in_pick_up where id = #{pickUpId}
    </select>
    <select id="selectByCode" resultType="com.gzairports.common.business.arrival.domain.vo.ScanOutVo">
        select au.id as pickUpId, au.pick_up_code, au.serial_no, au.total_count, au.total_weight, au.total_quantity, au.is_pay
        from all_in_pick_up au
             where au.pick_up_code = #{pickUpCode}
               and DATE_FORMAT(au.pick_up_time, '%Y-%m') = DATE_FORMAT(#{date}, '%Y-%m')
        order by au.pick_up_time desc limit 1
    </select>
    <select id="selectRapidAgent" resultType="java.lang.Long">
        select dept_id from hz_arr_quick_delivery where status = 1
    </select>
    <select id="selectBillList" resultType="com.gzairports.common.business.arrival.domain.vo.BillListVo">
        select id, serial_no, total_count, pick_up_time, is_pay from all_in_pick_up
        <where>
            <if test="query.serialNo != null and query.serialNo != ''">
                and serial_no like '%${query.serialNo}%'
            </if>
            <if test="query.startTime != null">
                and pick_up_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and pick_up_time <![CDATA[<=]]> #{query.endTime}
            </if>
            <if test="query.deptId != null and deptIdList == null">
                and dept_id = #{query.deptId}
            </if>
            <if test="deptIdList != null">
                AND dept_id IN
                <foreach item="deptId" index="index" collection="deptIdList"
                         open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectH5PrintData"
            resultType="com.gzairports.common.business.arrival.domain.vo.PrintOutOrderVo">
        select id, customer_name, customer_id_type, customer_id_no, serial_no, pick_up_code, out_time,
               customer_phone,consign, consign_id_no, consign_phone, print_count, is_pay
        from all_in_pick_up
        where pick_up_code = #{pickUpCode}
        and pick_up_time BETWEEN DATE_SUB(#{date}, INTERVAL 30 DAY)
                         AND DATE_ADD(#{date}, INTERVAL 1 DAY)
        order by pick_up_time desc limit 1
    </select>
    <select id="selectBySerial" resultType="com.gzairports.common.business.arrival.domain.PickUp">
        select id, serial_no, customer_name, total_cost, total_charge_weight, total_quantity, total_weight, total_count, customer_id_type, customer_id_no,
               customer_phone, pick_up_time, pay_method, handle_by, remark, pick_up_code, is_pay, pay_time, pay_status, out_time, dept_id, consign, consign_id_no,
               consign_phone, print_count, settle_user
        from all_in_pick_up
        <where>
            <if test="serialNo != null and serialNo != ''">
                and serial_no like '%${serialNo}%'
            </if>
            <if test="pickUpCode != null and pickUpCode != ''">
                and pick_up_code = #{pickUpCode}
                and pick_up_time BETWEEN DATE_SUB(#{queryDate}, INTERVAL 30 DAY) AND #{queryDate}
            </if>
            <if test="deptId != null">
                and dept_id = #{deptId}
            </if>
            <if test="deptIdList != null and deptIdList.size() > 0">
                and dept_id in
                <foreach item="deptId" index="index" collection="deptIdList" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
        </where>
        order by pick_up_time desc limit 1
    </select>
    <select id="selectReportPuckUpList"
            resultType="com.gzairports.common.business.reporter.domain.ReportPickUp">
        select id, serial_no, customer_name, customer_id_type, customer_id_no, customer_phone, handle_by, pay_status, pay_method, total_cost, total_charge_weight,
               total_quantity, total_weight, total_count, pick_up_time from all_in_pick_up
        <where>
            1 = 1
            <if test="lastSyncTime != null">
                and update_time > #{lastSyncTime}
            </if>
            <if test="dateNow != null">
                and update_time <![CDATA[<=]]> #{dateNow}
            </if>
        </where>
    </select>
    <select id="existsByCodeAndTime" resultType="java.lang.Boolean">
        select exists (
                       select 1 from all_in_pick_up
                       where pick_up_code = #{code}
                         and pick_up_time BETWEEN DATE_SUB(#{date}, INTERVAL 30 DAY) AND #{date}
                   )
    </select>

</mapper>
