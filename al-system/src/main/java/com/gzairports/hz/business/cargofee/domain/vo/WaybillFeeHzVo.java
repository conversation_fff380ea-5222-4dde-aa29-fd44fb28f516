package com.gzairports.hz.business.cargofee.domain.vo;

import com.gzairports.hz.business.cargofee.domain.WaybillFeeHz;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 运单费用返回数据
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@Data
public class WaybillFeeHzVo {

    /** 账户余额 */
    private BigDecimal balance;

    /** 结算方式 */
    private String settleMethod;

    /** 预授权总金额 */
    private BigDecimal totalPay;

    /** 结算支付总金额 */
    private BigDecimal totalSettle;

    /** 结算退款总金额 */
    private BigDecimal totalRefund;

    /** 运单费用列表 */
    private List<WaybillFeeHz> list;

}
