package com.gzairports.common.business.departure.domain.vo;

import lombok.Data;

import java.util.Date;

/**
 * 出港费用明细新增参数
 *
 * <AUTHOR>
 * @date 2024-07-12
 */
@Data
public class CostDetailVo {

    /** 收费项目id */
    private Long chargeId;

    /** 入库时间 */
    private Date collectTime;

    /** 份数 */
    private Integer num;

    /** 尺寸类型 小件 small 大件 large 超大 oversize*/
    private String sizeType;
}
