package com.gzairports.hz.business.reporter.domain.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-06
 */
@Data
public class FieldKeyValue {

    /** 字段键 */
    private String fieldName;

    /** 字段值 */
    private String fieldValue;

    /** 字段类型 */
    private String fieldType;

    /** 操作符 0等于 1大于 2小于 3大于小于 4模糊 5 包含*/
    private String fieldFilterType;

    /** 逻辑连接符（AND/OR，默认为AND）*/
    private String logic = "AND";

    /** 大值 */
    private String greaterValue;

    /** 小值 */
    private String lessValue;

    private List<String> selectValue;
}
