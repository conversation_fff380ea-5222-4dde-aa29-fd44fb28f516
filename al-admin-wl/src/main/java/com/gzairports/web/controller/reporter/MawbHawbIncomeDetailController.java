package com.gzairports.web.controller.reporter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.wl.reporter.domain.query.MawbHawbIncomeQuery;
import com.gzairports.wl.reporter.domain.vo.AHIncomeDetailVO;
import com.gzairports.wl.reporter.mapper.MawbHawbIncomeMapper;
import com.gzairports.wl.reporter.service.IMawbHawbIncomeDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * @author: lan
 * @Desc: 主分单收入明细报表controller
 * @create: 2025-03-12 10:04
 **/

@RestController
@RequestMapping("/wl/mawbHawbIncomeDetail")
@Api(tags = "主分单收入明细报表")
public class MawbHawbIncomeDetailController extends BaseController {

    @Autowired
    private IMawbHawbIncomeDetailService mawbHawbIncomeDetailService;

    /**
     * 报表列表查询/导出
     */
    @GetMapping("/export")
    @ApiOperation(value = "报表列表查询/导出")
    public void export(MawbHawbIncomeQuery query, HttpServletResponse response) {
        Page<AHIncomeDetailVO> pageData = mawbHawbIncomeDetailService.queryExportDataList(query);
        mawbHawbIncomeDetailService.export(pageData.getRecords(), response);
    }

    /**
     * 列表数据条数
     * */
    @GetMapping("/page")
    @ApiOperation(value = "报表列表分页数据")
    public TableDataInfo pageQuery(MawbHawbIncomeQuery query) {
        Page<AHIncomeDetailVO> pageData = mawbHawbIncomeDetailService.queryExportDataList(query);
        return new TableDataInfo(pageData.getRecords(),(int)pageData.getTotal());
    }

    /**
     * 报表打印
     * */
    @GetMapping("/print/pdf")
    @ApiOperation(value = "报表打印-PDF文件")
    public void printPDF(MawbHawbIncomeQuery query, HttpServletResponse response) {
        mawbHawbIncomeDetailService.printPDF(query,response);
    }

}
