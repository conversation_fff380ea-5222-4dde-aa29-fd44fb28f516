package com.gzairports.framework.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class RabbitConfig {

    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);

        // 启用发布确认（Publisher Confirms）
        rabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {
            if (ack) {
                log.info("消息成功发送到交换机: " + correlationData.getId());
            } else {
                log.error("消息未能发送到交换机: " + cause);
            }
        });

        // 启用返回机制（Return Listener）
        rabbitTemplate.setReturnsCallback(returned -> {
            log.error("消息未被路由到队列，原因: " + returned.getReplyText());
        });

        return rabbitTemplate;
    }
}