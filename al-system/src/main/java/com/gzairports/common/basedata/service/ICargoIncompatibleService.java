package com.gzairports.common.basedata.service;

import com.gzairports.common.basedata.domain.BaseCargoIncompatible;
import com.gzairports.common.basedata.domain.query.CargoIncompatibleQuery;

import java.util.List;

/**
 * 货物不兼容性Service接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */

public interface ICargoIncompatibleService {

    /**
     * 查询货物不兼容性列表
     *
     * @param query 查询参数
     * @return 货物不兼容性列表
     */
    List<BaseCargoIncompatible> selectCargoIncompatibleList(CargoIncompatibleQuery query);

    /**
     * 导入货物不兼容性
     *
     * @param incompatibles 货物不兼容性列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    String importCargoIncompatible(List<BaseCargoIncompatible> incompatibles, boolean updateSupport);

    /**
     * 新增货物不兼容性
     *
     * @param incompatible 货物不兼容性
     * @return 结果
     */
    int addCargoIncompatible(BaseCargoIncompatible incompatible);

    /**
     * 修改货物不兼容性
     *
     * @param incompatible 货物不兼容性
     * @return 结果
     */
    int editCargoIncompatible(BaseCargoIncompatible incompatible);

    /**
     * 删除货物不兼容性
     *
     * @param ids 货物不兼容性id集合
     * @return 结果
     */
    int delCargoIncompatible(Long[] ids);
}
