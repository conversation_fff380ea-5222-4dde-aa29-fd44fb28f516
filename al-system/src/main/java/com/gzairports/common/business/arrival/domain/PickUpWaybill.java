package com.gzairports.common.business.arrival.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 办理提货运单关联表
 *
 * <AUTHOR>
 * @date 2024-06-03
 */
@Data
@TableName(value = "all_pick_up_waybill")
public class PickUpWaybill {

    /** 主键id */
    private Long id;

    /** 提货办单id */
    private Long pickUpId;

    /** 理货id */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long tallyId;

    /** 运单号 */
    private String waybillCode;

    /** 可提货件数 */
    private Integer canPickUpQuantity;

    /** 可提货重量 */
    private BigDecimal canPickUpWeight;

    /** 费用合计 */
    private BigDecimal costSum;

    /** 创建时间 */
    private Date createTime;

    /** 创建人 */
    private String createBy;

    /** 更新时间 */
    private Date updateTime;

    /** 更新人 */
    private String updateBy;

    /** 是否取消办单 */
    @TableLogic
    private Integer isCancel;
}
