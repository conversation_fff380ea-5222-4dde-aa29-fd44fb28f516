package com.gzairports.sms.domain.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 *
 * 航班备降短信对象
 * Created by david on 2023/9/22
 * <AUTHOR>
 */
@Data
public class SmsFlightAlternate {

    /***
     * 运单号
     */
    @JSONField(ordinal = 1)
    private String billId;

    /***
     * 航班号
     */
    @JSONField(ordinal = 2)
    private String flightNo;

    /***
     * 备降机场
     */
    @JSONField(ordinal = 3)
    private Integer alternateAirport;
}
