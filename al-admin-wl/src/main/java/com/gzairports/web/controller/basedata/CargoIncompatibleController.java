package com.gzairports.web.controller.basedata;


import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.common.basedata.domain.BaseCargoIncompatible;
import com.gzairports.common.basedata.domain.query.CargoIncompatibleQuery;
import com.gzairports.common.basedata.service.ICargoIncompatibleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 货物不兼容性Controller
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@RestController
@RequestMapping("/base/incompatible")
@Api(tags = "货物不兼容性数据接口")
public class CargoIncompatibleController extends BaseController {

    @Autowired
    private ICargoIncompatibleService cargoIncompatibleService;

    /**
     * 查询货物不兼容性列表
     */
//    @PreAuthorize("@ss.hasPermi('base:incompatible:cargoIncompatibleList')")
    @GetMapping("/cargoIncompatibleList")
    @ApiOperation(value = "查询货物不兼容性列表")
    public TableDataInfo cargoIncompatibleList(CargoIncompatibleQuery query)
    {
        startPage();
        List<BaseCargoIncompatible> list = cargoIncompatibleService.selectCargoIncompatibleList(query);
        return getDataTable(list);
    }

    /**
     * 导出货物不兼容性
     */
    @PreAuthorize("@ss.hasPermi('base:incompatible:exportCargoIncompatible')")
    @Log(title = "导出货物不兼容性", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCargoIncompatible")
    @ApiOperation(value = "导出货物不兼容性")
    public void exportCargoIncompatible(HttpServletResponse response, CargoIncompatibleQuery query)
    {
        List<BaseCargoIncompatible> list = cargoIncompatibleService.selectCargoIncompatibleList(query);
        ExcelUtil<BaseCargoIncompatible> util = new ExcelUtil<BaseCargoIncompatible>(BaseCargoIncompatible.class);
        util.exportExcel(response, list, "货物不兼容性");
    }
}
