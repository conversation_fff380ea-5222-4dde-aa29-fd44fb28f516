<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.AirportCodeMapper">
    <select id="selectAirportCodeList" resultType="com.gzairports.common.basedata.domain.BaseAirportCode">
        select id, code, four_code, city_code, city_name, chinese_name, english_name, is_common, is_domestic from base_airport_code
        <where>
        and is_del = 0
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="code != null  and code != ''"> and code like concat('%',#{code},'%')</if>
            <if test="fourCode != null and fourCode != ''"> and four_code like concat('%',#{fourCode},'%')</if>
            <if test="cityCode != null and cityCode != ''"> and city_code like concat('%',#{cityCode},'%')</if>
            <if test="cityName != null and cityName != ''"> and city_name like concat('%',#{cityName},'%')</if>
            <if test="chineseName != null and chineseName != ''"> and chinese_name like concat('%',#{chineseName},'%')</if>
            <if test="englishName != null and englishName != ''"> and english_name like concat('%',#{englishName},'%')</if>
            <if test="isDomestic != null and isDomestic != ''"> and is_domestic like concat('%',#{isDomestic},'%')</if>
        </where>
    </select>
    <select id="selectCityByName" resultType="com.gzairports.common.basedata.domain.BaseAirportCode">
        select id, code, four_code, city_code, city_name, chinese_name, english_name, is_common, is_domestic from base_airport_code
        where chinese_name = #{departureCity} and is_del = 0
    </select>
</mapper>
