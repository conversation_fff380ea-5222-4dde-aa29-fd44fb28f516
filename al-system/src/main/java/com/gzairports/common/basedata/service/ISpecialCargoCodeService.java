package com.gzairports.common.basedata.service;

import com.gzairports.common.basedata.domain.BaseSpecialCargoCode;
import com.gzairports.common.basedata.domain.query.SpecialCargoCodeQuery;

import java.util.List;

/**
 * 装载特货代码Service接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
public interface ISpecialCargoCodeService {

    /**
     * 查询装载特货代码列表
     *
     * @param query 查询参数
     * @return 装载特货代码列表
     */
    List<BaseSpecialCargoCode> selectSpecialCargoCodeList(SpecialCargoCodeQuery query);

    /**
     * 导入装载特货代码
     *
     * @param codes 装载特货代码列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    String importSpecialCargoCode(List<BaseSpecialCargoCode> codes, boolean updateSupport);

    /**
     * 新增装载特货代码
     *
     * @param code 装载特货代码
     * @return 结果
     */
    int addSpecialCargoCode(BaseSpecialCargoCode code);

    /**
     * 修改装载特货代码
     *
     * @param code 装载特货代码
     * @return 结果
     */
    int editSpecialCargoCode(BaseSpecialCargoCode code);

    /**
     * 删除装载特货代码
     *
     * @param ids 装载特货代码id集合
     * @return 结果
     */
    int delSpecialCargoCode(Long[] ids);
}
