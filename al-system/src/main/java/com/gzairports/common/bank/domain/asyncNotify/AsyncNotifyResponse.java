package com.gzairports.common.bank.domain.asyncNotify;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * @author: lan
 * @Desc: 异步通知字段
 * @create: 2025-03-18 14:51
 **/

@Data
@XmlRootElement(name = "bodylist")
@XmlAccessorType(XmlAccessType.FIELD)
public class AsyncNotifyResponse implements Serializable {
    private static final long serialVersionUID = 1L;


    /** 币种 */
    @XmlElement(name = "currencyCategory")
    private String currencyCategory;

    /** 交易金额 */
    @XmlElement(name = "tranAmount")
    private Double tranAmount;

    /** 原交易状态 */
    @XmlElement(name = "tradeStatus")
    private String tradeStatus;

    /** 通知类型 */
    @XmlElement(name = "svrType")
    private Integer svrType;

    /** 通知地址 */
    @XmlElement(name = "notifyUrl")
    private String notifyUrl;

    /** 通道支付信息附加域 */
    @XmlElement(name = "centerInfo")
    private String centerInfo;

    /** 外部流水 */
    @XmlElement(name = "centerSeqId")
    private String centerSeqId;

    /** 通道流水 */
    @XmlElement(name = "bankOrderNo")
    private String bankOrderNo;

    /** 内部流水号 */
    @XmlElement(name = "bankTradeNo")
    private String bankTradeNo;

    /** 商户号 */
    @XmlElement(name = "merchantNo")
    private String merchantNo;

    /** 流水号 */
    @XmlElement(name = "serialNo")
    private String serialNo;

    /** 手续费金额 */
    @XmlElement(name = "fee")
    private Double fee;

    /** 订单号 */
    @XmlElement(name = "orderNo")
    private String orderNo;

    /** 错误代码 */
    @XmlElement(name = "errCode")
    private String errCode;

    /** 错误信息 */
    @XmlElement(name = "errMsg")
    private String errMsg;

    /** 平台号 */
    @XmlElement(name = "platformId")
    private String platformId;

    /** 支付类型 */
    @XmlElement(name = "tradeType")
    private Integer tradeType;

    /** 客户协议编号 */
    @XmlElement(name = "protocolId")
    private String protocolId;

    /** 付款人账号 */
    @XmlElement(name = "draweeAccNo")
    private String draweeAccNo;

    /** 付款人名称 */
    @XmlElement(name = "draweeAccName")
    private String draweeAccName;

    /** 付款账户开户行 */
    @XmlElement(name = "draweePartyId")
    private String draweePartyId;

    /** 电话号码 */
    @XmlElement(name = "phoneId")
    private String phoneId;
}
