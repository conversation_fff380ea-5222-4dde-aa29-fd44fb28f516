package com.gzairports.common.basedata.service.impl;

import com.gzairports.common.basedata.domain.BaseRegionCode;
import com.gzairports.common.basedata.domain.query.RegionCodeQuery;
import com.gzairports.common.basedata.mapper.RegionCodeMapper;
import com.gzairports.common.basedata.service.IRegionCodeService;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.exception.ServiceException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.bean.BeanValidators;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.Validator;
import java.util.List;

/**
 * 国家或地区代码Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Service
public class RegionCodeServiceImpl implements IRegionCodeService {

    private static final Logger log = LoggerFactory.getLogger(RegionCodeServiceImpl.class);

    @Autowired
    private RegionCodeMapper regionCodeMapper;

    @Autowired
    private Validator validator;


    /**
     * 查询国家或地区代码列表
     *
     * @param query 查询参数
     * @return 国家或地区代码列表
     */
    @Override
    public List<BaseRegionCode> selectRegionCodeList(RegionCodeQuery query) {
        List<BaseRegionCode> baseRegionCodes = regionCodeMapper.selectRegionCodeList(query);
        for (BaseRegionCode baseRegionCode:baseRegionCodes) {
            if (baseRegionCode.getRemark() == null || baseRegionCode.getRemark().equals("")){
                //如果remark为空，则设置remark为code+continent
                baseRegionCode.setRemark(baseRegionCode.getCode()+ "/" + baseRegionCode.getContinent());
            }
        }
        return baseRegionCodes;
    }

    /**
     * 导入国家或地区代码
     *
     * @param regionCodeList 国家或地区代码数据列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importRegionCode(List<BaseRegionCode> regionCodeList, boolean updateSupport) {
        if (StringUtils.isNull(regionCodeList) || regionCodeList.size() == 0)
        {
            throw new ServiceException("导入国家或地区代码数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (BaseRegionCode baseRegionCode : regionCodeList) {
            try {
                // 验证是否存在相同的国家或地区代码
                BaseRegionCode region = regionCodeMapper.selectRegionByCode(baseRegionCode.getCode());
                if (StringUtils.isNull(region)) {
                    BeanValidators.validateWithException(validator, baseRegionCode);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    baseRegionCode.setCreateBy(loginUser.getUsername());
                    baseRegionCode.setCreateTime(DateUtils.getNowDate());
                    regionCodeMapper.insertRegionCode(baseRegionCode);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、代码 " + baseRegionCode.getCode() + " 导入成功");
                }
                else if (updateSupport) {
                    BeanValidators.validateWithException(validator, baseRegionCode);
                    baseRegionCode.setId(region.getId());
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    baseRegionCode.setCreateBy(loginUser.getUsername());
                    baseRegionCode.setCreateTime(DateUtils.getNowDate());
                    regionCodeMapper.updateRegionCode(baseRegionCode);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、代码 " + baseRegionCode.getCode() + " 更新成功");
                }
                else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、代码 " + baseRegionCode.getCode() + " 已存在");
                }
            }catch (Exception e){
                failureNum++;
                String msg = "<br/>" + failureNum + "、代码 " + baseRegionCode.getCode() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 新增国家或地区代码
     *
     * @param regionCode 国家或地区代码
     * @return 结果
     */
    @Override
    public int addRegionCode(BaseRegionCode regionCode) {
        if(!StringUtils.isNull(regionCodeMapper.selectRegionByCode(regionCode.getCode())))
        {
            throw new CustomException("国家或地区代码已存在");
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        regionCode.setCreateBy(loginUser.getUsername());
        regionCode.setCreateTime(DateUtils.getNowDate());
        return regionCodeMapper.insertRegionCode(regionCode);
    }

    /**
     * 修改国家或地区代码
     *
     * @param regionCode 国家或地区代码
     * @return 结果
     */
    @Override
    public int editRegionCode(BaseRegionCode regionCode) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        regionCode.setUpdateBy(loginUser.getUsername());
        regionCode.setUpdateTime(DateUtils.getNowDate());
        return regionCodeMapper.updateRegionCode(regionCode);
    }

    /**
     * 删除国家或地区代码
     *
     * @param ids 国家或地区代码id集合
     * @return 结果
     */
    @Override
    public int delRegionCode(Long[] ids) {
        return regionCodeMapper.delRegionCode(ids);
    }

}
