package com.gzairports.common.basedata.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.basedata.domain.BaseAirCargoCode;
import com.gzairports.common.basedata.domain.query.AirCargoCodeQuery;
import com.gzairports.common.basedata.mapper.AirCargoCodeMapper;
import com.gzairports.common.basedata.mapper.AirSpecialMapper;
import com.gzairports.common.basedata.mapper.SpecialCodeMapper;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.exception.ServiceException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.bean.BeanValidators;
import com.gzairports.common.basedata.service.IAirCargoCodeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import javax.validation.Validator;
import java.util.Date;
import java.util.List;

/**
 * 航空公司货品代码Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Service
public class AirCargoCodeServiceImpl implements IAirCargoCodeService {

    private static final Logger log = LoggerFactory.getLogger(WeightServiceImpl.class);

    @Autowired
    private Validator validator;

    @Autowired
    private AirCargoCodeMapper airCargoCodeMapper;

    @Autowired
    private AirSpecialMapper airSpecialMapper;

    @Autowired
    private SpecialCodeMapper specialCodeMapper;

    /**
     * 查询航空公司货品代码列表
     *
     * @param query 查询参数
     * @return 航空公司货品代码列表
     */
    @Override
    public List<BaseAirCargoCode> selectAirCargoCodeList(AirCargoCodeQuery query) {
        query.setUserName(SecurityUtils.getUsername());
        return airCargoCodeMapper.selectAirCargoCodeList(query);
    }

    /**
     * 导入航空公司货品代码
     *
     * @param airCargoCodes 航空公司货品代码列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importAirCargoCode(List<BaseAirCargoCode> airCargoCodes, boolean updateSupport) {
        if (StringUtils.isNull(airCargoCodes) || airCargoCodes.size() == 0)
        {
            throw new ServiceException("导入航空公司货品代码数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (BaseAirCargoCode airCargoCode : airCargoCodes) {
            try {
                // 验证是否存在相同的航空公司货品代码
                BaseAirCargoCode baseAirCargoCode = airCargoCodeMapper.selectAirCargoCodeByCode(airCargoCode.getCode());
                if (StringUtils.isNull(baseAirCargoCode)) {
                    BeanValidators.validateWithException(validator, airCargoCode);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    airCargoCode.setCreateBy(loginUser.getUsername());
                    airCargoCode.setCreateTime(DateUtils.getNowDate());
                    airCargoCodeMapper.insertAirCargoCode(airCargoCode);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、航空公司货品代码 " + airCargoCode.getCode() + " 导入成功");
                }
                else if (updateSupport) {
                    BeanValidators.validateWithException(validator, airCargoCode);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    airCargoCode.setId(baseAirCargoCode.getId());
                    airCargoCode.setUpdateBy(loginUser.getUsername());
                    airCargoCode.setUpdateTime(DateUtils.getNowDate());
                    airCargoCodeMapper.updateAirCargoCode(airCargoCode);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、航空公司货品代码 " + airCargoCode.getCode() + " 更新成功");
                }
                else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、航空公司货品代码 " + airCargoCode.getCode() + " 已存在");
                }
            }catch (Exception e){
                failureNum++;
                String msg = "<br/>" + failureNum + "、航空公司货品代码 " + airCargoCode.getCode() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 新增航空公司货品代码
     *
     * @param airCargoCode 航空公司货品代码
     * @return 结果
     */
    @Override
    public int addAirCargoCode(BaseAirCargoCode airCargoCode) {
        BaseAirCargoCode baseAirCargoCode = airCargoCodeMapper.selectOne(new QueryWrapper<BaseAirCargoCode>()
                .eq("code", airCargoCode.getCargoCode())
                .eq("carrier_code",airCargoCode.getCarrierCode()));
        if (baseAirCargoCode != null){
            throw new CustomException("已存在相同数据");
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        airCargoCode.setCreateBy(loginUser.getUsername());
        airCargoCode.setCreateTime(DateUtils.getNowDate());
        //默认设置为私用,这样才查得到,后面有需求再改
        airCargoCode.setIsCommon(0);
        return airCargoCodeMapper.insertAirCargoCode(airCargoCode);
    }

    /**
     * 修改航空公司货品代码
     *
     * @param airCargoCode 航空公司货品代码
     * @return 结果
     */
    @Override
    public int editAirCargoCode(BaseAirCargoCode airCargoCode) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        airCargoCode.setUpdateBy(loginUser.getUsername());
        airCargoCode.setUpdateTime(new Date());
        return airCargoCodeMapper.updateAirCargoCode(airCargoCode);
    }

    /**
     * 删除航空公司货品代码
     *
     * @param ids 航空公司货品代码id集合
     * @return 结果
     */
    @Override
    public int delAirCargoCode(Long[] ids) {
        return airCargoCodeMapper.delAirCargoCode(ids);
    }

    /**
     * 删除航空公司货品代码
     *
     * @param id 航空公司货品代码id集合
     * @return 结果
     */
    @Override
    public int delAirCargoCode(Long id) {
        BaseAirCargoCode baseAirCargoCode = airCargoCodeMapper.selectById(id);
        baseAirCargoCode.setIsDel(1);
        return airCargoCodeMapper.updateById(baseAirCargoCode);
    }

    /**
     * 根据id获取航空公司货品代码
     *
     * @param id 航空公司货品代码id
     * @return 航空公司货品代码
     */
    @Override
    public BaseAirCargoCode getInfo(Long id) {
/*        List<Long> list = airSpecialMapper.selectByCargoId(id);
        BaseAirCargoCode baseAirCargoCode = airCargoCodeMapper.selectById(id);
        if (!CollectionUtils.isEmpty(list)){
            List<String> list1 = specialCodeMapper.selectListById(list);
            if (!CollectionUtils.isEmpty(list1)){
                baseAirCargoCode.setSpecialCode(list1);
            }
        }
        return baseAirCargoCode;*/
        return airCargoCodeMapper.selectById(id);
    }
}
