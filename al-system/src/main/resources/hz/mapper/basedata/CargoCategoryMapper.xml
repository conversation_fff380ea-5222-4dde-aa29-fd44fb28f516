<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.CargoCategoryMapper">
    <update id="delCargoCategory" parameterType="long">
        update base_cargo_category set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <select id="selectCargoCategoryList" resultType="com.gzairports.common.basedata.domain.BaseCargoCategory">
        select id, code, chinese_name, english_name, remark, create_time, update_by, update_time, create_by,
               in_store, in_locator, out_store, out_locator
        from base_cargo_category
        <where>
            is_del = 0
            <if test="code != null  and code != ''"> and code like concat('%', #{code}, '%')</if>
            <if test="chineseName != null and chineseName != ''"> and chinese_name like concat('%', #{chineseName}, '%')</if>
        </where>
    </select>
    <select id="selectByCode" resultType="java.lang.String">
        select chinese_name from base_cargo_category where code = #{categoryName} and is_del = 0
    </select>
    <select id="selectBatchByCodes" resultType="com.gzairports.common.basedata.domain.BaseCargoCategory">
        select id, code, chinese_name, english_name, remark, create_time, update_by, update_time, create_by,
               in_store, in_locator, out_store, out_locator
        from base_cargo_category where is_del = 0 and code in
        <foreach item="code" collection="categoryCodes" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>
</mapper>