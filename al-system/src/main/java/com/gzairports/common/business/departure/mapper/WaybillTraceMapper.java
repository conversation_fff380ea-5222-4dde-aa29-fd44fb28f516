package com.gzairports.common.business.departure.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.departure.domain.WaybillTrace;
import com.gzairports.common.business.departure.domain.vo.CarrierFlightVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 运单跟踪Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Mapper
public interface WaybillTraceMapper extends BaseMapper<WaybillTrace> {

    /**
     * 查询承运航班
     * @param waybillId 运单id
     * @return 结果
     */
    List<Long> selectFlight(Long waybillId);

    /**
     * 根据航班id查询航班号
     * @param collect 航班id
     * @return 航班号
     */
    List<CarrierFlightVo> selectFlightNo(@Param("collect") List<Long> collect);
}
