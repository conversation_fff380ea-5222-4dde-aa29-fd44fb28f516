package com.gzairports.common.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.basedata.domain.BaseCargoDanger;
import com.gzairports.common.basedata.domain.query.CargoDangerQuery;
import com.gzairports.common.basedata.mapper.CargoDangerMapper;
import com.gzairports.common.basedata.service.ICargoDangerService;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 危险品管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-14
 */
@Service
public class CargoDangerServiceImpl implements ICargoDangerService {

    @Autowired
    private CargoDangerMapper dangerMapper;

    /**
     * 查询危险品管理列表
     *
     * @param query 查询参数
     * @return 危险品管理列表
     */
    @Override
    public List<BaseCargoDanger> selectDangerList(CargoDangerQuery query) {
        return dangerMapper.selectDangerList(query);
    }

    /**
     * 新增危险品管理
     *
     * @param danger 危险品管理
     * @return 结果
     */
    @Override
    public int addDanger(BaseCargoDanger danger) {
        BaseCargoDanger baseCargoDanger = dangerMapper.selectOne(new QueryWrapper<BaseCargoDanger>()
                .eq("un_code", danger.getUnCode())
                .eq("is_del", 0));
        if (baseCargoDanger != null){
            throw new CustomException("已存在相同数据");
        }
        danger.setCreateTime(new Date());
        danger.setCreateBy(SecurityUtils.getUsername());
        danger.setIsDel(0);
        return dangerMapper.insert(danger);
    }

    /**
     * 修改危险品管理
     *
     * @param danger 危险品管理
     * @return 结果
     */
    @Override
    public int editDanger(BaseCargoDanger danger) {
        danger.setUpdateBy(SecurityUtils.getUsername());
        danger.setUpdateTime(new Date());
        return dangerMapper.updateById(danger);
    }

    /**
     * 删除危险品管理
     *
     * @param ids 危险品管理id集合
     * @return 结果
     */
    @Override
    public int delDanger(Long[] ids) {
        return dangerMapper.delDanger(ids);
    }
}
