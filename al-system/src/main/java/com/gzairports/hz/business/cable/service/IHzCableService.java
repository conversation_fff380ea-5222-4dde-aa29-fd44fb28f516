package com.gzairports.hz.business.cable.service;

import com.gzairports.hz.business.cable.domain.HzCable;
import com.gzairports.hz.business.cable.domain.HzCableType;
import com.gzairports.hz.business.cable.domain.query.GenerateCableQuery;
import com.gzairports.hz.business.cable.domain.query.HzCableQuery;
import com.gzairports.hz.business.cable.domain.vo.CableMsgTypeVO;
import com.gzairports.hz.business.cable.domain.vo.CableUldVo;
import com.gzairports.hz.business.departure.domain.query.CableMawbQuery;
import com.gzairports.hz.business.departure.domain.vo.CableMawbVo;
import com.gzairports.hz.business.departure.domain.vo.PullCargoVo;

import java.util.List;


/**
 * 电报数据Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
public interface IHzCableService 
{
    /**
     * 查询电报数据
     * 
     * @param id 电报数据主键
     * @return 电报数据
     */
    public HzCable selectHzCableById(Long id);

    /**
     * 查询电报数据列表
     * 
     * @param query 电报数据
     * @return 电报数据集合
     */
    public List<HzCable> selectHzCableList(HzCableQuery query);

    /**
     * 新增电报数据
     * 
     * @param hzCable 电报数据
     * @return 结果
     */
    public int insertHzCable(HzCable hzCable);

    /**
     * 修改电报数据
     * 
     * @param hzCable 电报数据
     * @return 结果
     */
    public int updateHzCable(HzCable hzCable);

    /**
     * 删除电报数据信息
     * 
     * @param id 电报数据主键
     * @return 结果
     */
    public int deleteHzCableById(Long id);

    /**
     * 报文数据收集运单列表数据
     * @param query 查询参数
     * @return 运单列表
     */
    List<CableMawbVo> selectWaybillList(CableMawbQuery query);

    /**
     * 生成报文
     * @param query 生成报文参数
     * @return 报文
     */
    List<CableMsgTypeVO> generateCable(GenerateCableQuery query);

    /**
     * 报文数据收集集装器列表数据
     * @param query 查询参数
     * @return 集装器列表
     */
    List<CableUldVo> uldList(CableMawbQuery query);

    /**
     * 查询出港拉货报文运单数据
     * @param query 查询条件
     * @return 运单数据
     */
    List<PullCargoVo> selectPullCargoList(CableMawbQuery query);

    /**
     * 根据电报类型查询电报模板
     * @param type
     * @date 2024/11/26
     * @return com.gzairports.hz.business.cable.domain.HzCableType
     */
    HzCableType selectTemplateByType(String type);
}
