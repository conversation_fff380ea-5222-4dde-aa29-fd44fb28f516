package com.gzairports.common.bankUtils;

import org.apache.commons.io.IOUtils;
import org.springframework.core.io.ClassPathResource;

import java.io.*;
import java.util.Properties;

/**
 * @author: lan
 * @create: 2025-03-18 16:23
 **/

public class Config {

//    private static final String propsPath = "/config.properties";
//    private static Properties props = new Properties();
//    private static boolean initFlag = false;
//
//    /**
//     * 初始化配置文件
//     */
//    private static synchronized void init() {
//        if (!initFlag) {
//            InputStream in = null;
//            try {
//                in = Config.class.getResourceAsStream(propsPath);
//                props.load(in);
//            } catch (IOException e) {
//                throw new RuntimeException(e.getMessage(), e);
//            } finally {
//                if (in != null) {
//                    try {
//                        in.close();
//                    } catch (IOException e) {
//                    }
//                }
//            }
//            initFlag = true;
//        }
//    }
//
//    /**
//     * 获取初始化参数
//     * @param propName
//     * @return
//     */
//    public static String getProperty(String propName) {
//        init();
//        return props.getProperty(propName);
//    }

    public static String getKeyCer(String s){
        ClassPathResource resource = new ClassPathResource(s);
//        byte[] content;
//        try {
//            InputStream inputStream = resource.getInputStream();
//            content = IOUtils.toByteArray(inputStream);
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
//        return new String(content);
        // 创建临时文件
        File tempFile = null;
        try {
            tempFile = File.createTempFile("temp", ".cer");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        tempFile.deleteOnExit(); // 确保程序退出时删除临时文件

        try (InputStream is = resource.getInputStream();
             OutputStream os = new FileOutputStream(tempFile)) {
            // 将输入流的数据复制到临时文件输出流中
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = is.read(buffer)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return tempFile.getAbsolutePath();

    }


    public static String getKeySm2(String s){
        ClassPathResource resource = new ClassPathResource(s);
//        byte[] content;
//        try {
//            InputStream inputStream = resource.getInputStream();
//            content = IOUtils.toByteArray(inputStream);
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
//        return new String(content);
        // 创建临时文件
        File tempFile = null;
        try {
            tempFile = File.createTempFile("temp", ".sm2");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        tempFile.deleteOnExit(); // 确保程序退出时删除临时文件

        try (InputStream is = resource.getInputStream();
             OutputStream os = new FileOutputStream(tempFile)) {
            // 将输入流的数据复制到临时文件输出流中
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = is.read(buffer)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return tempFile.getAbsolutePath();

    }


}
