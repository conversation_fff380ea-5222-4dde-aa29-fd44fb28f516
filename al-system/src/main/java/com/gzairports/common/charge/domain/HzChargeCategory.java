package com.gzairports.common.charge.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

/**
 * 收费货物类别表
 *
 * <AUTHOR>
 * @date 2024-06-13
 */
@Data
@TableName("hz_charge_category")
public class HzChargeCategory {

    /**
     * 主键id
     */
    @Excel(name = "主键id", prompt = "用户编号")
    private Long id;

    /**
     * 类别名称
     */
    @Excel(name = "类别名称")
    private String categoryName;

    /**
     * 包含货物
     */
    @Excel(name = "包含货物")
    private String containCargo;

    /**
     * 是否删除 0 否 1 是
     */
    private Integer isDel;
}
