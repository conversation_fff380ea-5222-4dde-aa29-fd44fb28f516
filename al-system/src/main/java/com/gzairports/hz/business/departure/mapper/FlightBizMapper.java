package com.gzairports.hz.business.departure.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 航班配载Mapper接口
 *
 * <AUTHOR>
 * @date 2023-09-18
 */
@Mapper
public interface FlightBizMapper {

    /**
     * 根据航班号查询航班配载id
     * @param flightNo 航班号
     * @param flightDate 航班日期
     * @param desPort 目的站
     * @return 结果
     */
    Long selectBizByFlight(@Param("flightNo") String flightNo, @Param("flightDate") String flightDate,@Param("desPort") String desPort);
}
