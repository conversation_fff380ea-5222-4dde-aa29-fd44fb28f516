package com.gzairports.common.system.domain;

import com.gzairports.common.annotation.Excel;
import com.gzairports.common.core.domain.BizEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * 代码生成测试对象 a_test
 *
 * <AUTHOR>
 * @date 2023-09-18
 */
@Data
public class ATest extends BizEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 姓名 */
    @Excel(name = "姓名")
    private String namceCn;




    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("namceCn", getNamceCn())
            .toString();
    }
}
