package com.gzairports.common.infoquery.domain.query;

import com.gzairports.wl.departure.domain.query.BasePageQuery;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * 航司 VO
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
public class AirlineWaybillQuery extends BasePageQuery {

    /** 进出港类型 ARR、DEP */
    private String type;

    /** 运单类型 AWBA、AWBM */
    private String waybillType;

    /** 制单时间（起始） */
    private LocalDateTime writeTimeStart;

    /** 制单时间（结束） */
    private LocalDateTime writeTimeEnd;

    /** 运单号 */
    private String waybillCode;

    /** 进出港标志 */
    private String inOutType;

    /** 运单状态 */
    private String status;

    /** 特货代码 */
    private String specialCargoCode;

    /** 货品代码 */
    private String cargoCode;

    /** 目的站 */
    private String desPort;

    /** 配载航班号 */
    private String flightNo;

    /** 可查询的运单前缀 */
    private Set<String> enabledWaybillCodePrefixList;

}
