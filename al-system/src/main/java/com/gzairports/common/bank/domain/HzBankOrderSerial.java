package com.gzairports.common.bank.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @author: lan
 * @Desc: 订单号与流水号对应
 * @create: 2025-03-20 11:08
 **/

@Data
@TableName("hz_bank_order_serial")
public class HzBankOrderSerial {

    /** 主键id */
    private Long id;

    /** 商户订单号与运单关联表id */
    private Long orderWaybillId;

    /** 运单号 当进港批量挑单多个运单办单支付时使用 */
    private String waybillCode;

    /** 流水号 */
    private String serialNo;

    /** 时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 类型 1下单/网关支付 2确认支付 3退款 4支付结果查询 5交易明细单下载 6订单分账信息查询 7异步通知 */
    private Integer type;

}
