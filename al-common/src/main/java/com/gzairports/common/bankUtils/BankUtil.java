package com.gzairports.common.bankUtils;

import cfca.sm2.signature.SM2PrivateKey;
import cfca.sm2rsa.common.Mechanism;
import cfca.sm2rsa.common.PKIException;
import cfca.util.CertUtil;
import cfca.util.EnvelopeUtil;
import cfca.util.KeyUtil;
import cfca.util.SignatureUtil2;
import cfca.util.cipher.lib.JCrypto;
import cfca.util.cipher.lib.Session;
import cfca.x509.certificate.X509Cert;
import cfca.x509.certificate.X509CertHelper;
import com.alibaba.fastjson2.JSONObject;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.HttpException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.HashMap;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public class BankUtil {

	private static Session session;
	
	static {
		try {
			JCrypto.getInstance().initialize(JCrypto.JSOFT_LIB, null);
			session = JCrypto.getInstance().openSession(JCrypto.JSOFT_LIB);
		} catch (PKIException e) {
			e.printStackTrace();
		}
	}

	private static final Logger logger = LoggerFactory.getLogger(BankUtil.class);


	
	public static String execute(String request,String requestUrl,String waybillCode,KeyPath keyPath){
		System.out.println("1.开始签名:");
		String sign = getSign(request,keyPath);
		System.out.println("得到签名串签:"+sign);

		System.out.println("2.将签名串与报文按规定拼接:");
		String signContext = createRequest(sign, request);
		logger.info("运单" + waybillCode + "请求民生接口" + requestUrl + ",准备报文:" + signContext);
		System.out.println("得到待加密的报文:"+signContext);

		System.out.println("3.对步骤2的待加密报文进行加密:");
		String encryptContext = encrypt(signContext,keyPath);
		System.out.println("得到加密报文:"+encryptContext);

		System.out.println("4.请求民生接口:");
		logger.info("运单" + waybillCode + "请求民生接口" + requestUrl + ",内容:" + encryptContext);
		JSONObject fur = new JSONObject();
		fur.put("businessContext", encryptContext);
		String resp = post2Cmbc(fur.toJSONString(),requestUrl);
		System.out.println("5.得到民生响应报文密文:"+resp);
		
		String dncryptContext = dncrypt(resp,keyPath);
		System.out.println("6.解密民生响应报文，得到明文:"+dncryptContext);
		String signChkResult = signCheck(dncryptContext,keyPath);
		System.out.println("7.验证签名结果:"+signChkResult);
		logger.info("运单" + waybillCode + "得到民生响应报文明文内容:" + dncryptContext);
		if(requestUrl.contains("fileDownload")){
			//交易明细文件 接口需要继续处理文件
			JSONObject obj = JSONObject.parseObject(dncryptContext);
			String bodyStr = (String) obj.get("body");
			JSONObject body = JSONObject.parseObject(bodyStr);
			String fileBase64 = body.getString("segmentContent");
			String fileMd5 = body.getString("fileMd5");
			//解base64
			try {
				String file = new String(Base64.decodeBase64(fileBase64),"UTF-8");
				System.out.println("块儿文件原文："+file);
				
				if(fileMd5.equals(getStrMD5(file))){
					System.out.println("校验块儿文件MD5,校验通过");
				}
				
			} catch (UnsupportedEncodingException e) {
				
			}
		}
		return dncryptContext;
	}
	
	
	public static String executeFormReq(String request,String requestUrl,String waybillCode,KeyPath keyPath){
		System.out.println("1.开始签名:");
		String sign = getSign(request,keyPath);
		logger.info("运单" + waybillCode + "请求民生接口" + requestUrl + ",签名报签:" + sign);
		System.out.println("得到签名串签:"+sign);

		System.out.println("2.将签名串与报文按规定拼接:");
		String signContext = createRequest(sign, request);
		logger.info("运单" + waybillCode + "请求民生接口" + requestUrl + ",准备报文:" + signContext);
		System.out.println("得到待加密的报文:"+signContext);

		System.out.println("3.对步骤2的待加密报文进行加密:");
		String encryptContext = encrypt(signContext,keyPath);
		logger.info("运单" + waybillCode + "请求民生接口" + requestUrl + ",加密报文:" + encryptContext);
		System.out.println("得到加密报文:"+encryptContext);

		System.out.println("4.请求民生接口:");
		logger.info("运单" + waybillCode + "请求民生接口" + requestUrl + ",内容:" + encryptContext);
		Map<String,String> context = new HashMap<String,String>();
		context.put("context", encryptContext);
		
		String resp = "";
		try {
			HttpClient client = new HttpClient(requestUrl);
			resp = client.postFormRequest(context);
		} catch (HttpException e) {
			e.printStackTrace();
		}
		System.out.println("5.得到民生响应报文密文:"+resp);
		String dncryptContext = dncrypt(resp,keyPath);
		System.out.println("6.解密民生响应报文，得到明文:"+dncryptContext);
		String signChkResult = signCheck(dncryptContext,keyPath);
		System.out.println("7.验证签名结果:"+signChkResult);
		logger.info("运单" + waybillCode + "得到民生响应报文明文内容:" + dncryptContext);

		return dncryptContext;
	}
	
	
	/**
	 * 请求民生接口
	 * @param param
	 * @return
	 */
	private static String post2Cmbc(String param,String requestUrl) {
		String resp = "";
		try {
			HttpClient client = new HttpClient(requestUrl);
			resp = client.doPost(param);
		} catch (HttpException e) {
			e.printStackTrace();
		}
		return resp;
	}
	

	/**
	 * 组装报文
	 * @param sign
	 * @param context
	 * @return
	 */
	public static String createRequest(String sign, String context) {
		GsonBuilder builder = new GsonBuilder();
		builder.disableHtmlEscaping();
		Gson gson = builder.create();
		Map<String, String> paramMap = new HashMap<String, String>();
		paramMap.put("sign", sign);
		paramMap.put("body", context);
		String signInfo = gson.toJson(paramMap); // 待加密字符串
		return signInfo;
	}

	/**
	 * 加密
	 * 
	 * @param signContext
	 *            需要加密的报文
	 * @return
	 */
	@SuppressWarnings("deprecation")
	public static String encrypt(String signContext,KeyPath keyPath){
//		String certAbsPath = Config.getProperty("bankPublicKey");
		String certAbsPath = Config.getKeyCer(keyPath.getBankPublicKey());
		X509Cert cert = null;
		try {
//			cert = X509CertHelper.parse(certAbsPath);
			cert = X509CertHelper.parse(certAbsPath);
		} catch (IOException e) {
			e.printStackTrace();
		} catch (PKIException e) {
			e.printStackTrace();
		}
		X509Cert[] certs = { cert };
		byte[] encryptedData = null;
		try {
			encryptedData = EnvelopeUtil.envelopeMessage(signContext.getBytes("UTF8"), Mechanism.SM4_CBC, certs);
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		} catch (PKIException e) {
			e.printStackTrace();
		}
		String encodeText = null;
		try {
			encodeText = new String(encryptedData, "UTF-8");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		return encodeText;
	}

	/**
	 * 解密
	 * 
	 * @param encryptContext
	 *            需要解密的报文
	 * @return
	 */
	public static String dncrypt(String encryptContext,KeyPath keyPath) {
		JSONObject resp = JSONObject.parseObject(encryptContext);
		String context = resp.getString("businessContext");
//		String priKeyAbsPath = Config.getProperty("merchantPrivateKey");
//		String priKeyPWD = Config.getProperty("merchantPwd");
		String priKeyAbsPath = Config.getKeySm2(keyPath.getPrivateKey());
		String priKeyPWD = keyPath.getPwd();
		String decodeText = null;
		try {
			PrivateKey priKey = KeyUtil.getPrivateKeyFromSM2(priKeyAbsPath, priKeyPWD);
			X509Cert cert = CertUtil.getCertFromSM2(priKeyAbsPath);
			byte[] sourceData = EnvelopeUtil.openEvelopedMessage(context.getBytes("UTF8"), priKey, cert, session);
			decodeText = new String(sourceData, "UTF8");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return decodeText;
	}


	/**
	 * 验证签名
	 * 
	 * @param dncryptContext
	 *            需要验证签名的明文
	 * @return
	 */
	public static String signCheck(String dncryptContext,KeyPath keyPath) {
//		String certAbsPath = Config.getProperty("bankPublicKey");
		String certAbsPath = Config.getKeyCer(keyPath.getPublicKey());
		Gson gson = new Gson();
		@SuppressWarnings("unchecked")
		Map<String, Object> paraMap = gson.fromJson(dncryptContext, Map.class);
		String sign = paraMap.get("sign").toString();
		String body = paraMap.get("body").toString();
		boolean isSignOK = false;
		try {
			X509Cert cert = X509CertHelper.parse(certAbsPath);
			PublicKey pubKey = cert.getPublicKey();
			isSignOK = new SignatureUtil2().p1VerifyMessage(Mechanism.SM3_SM2, body.getBytes("UTF8"),
					sign.getBytes(), pubKey, session);
		} catch (Exception e) {
			e.printStackTrace();
		}
		if (isSignOK) {
			return "验签通过";
		} else {
			return "验签不通过";
		}
	}

	/**
	 * 签名
	 * @param context
	 * @return
	 */
	private static String getSign(String context,KeyPath keyPath) {
		String sign = "";
		try {
//			String priKeyAbsPath = Config.getProperty("merchantPrivateKey");
//			String priKeyPWD = Config.getProperty("merchantPwd");
			String priKeyAbsPath = Config.getKeySm2(keyPath.getPrivateKey());
			String priKeyPWD = keyPath.getPwd();
			SM2PrivateKey priKey = KeyUtil.getPrivateKeyFromSM2(priKeyAbsPath, priKeyPWD);
			sign = new String(new SignatureUtil2().p1SignMessage(Mechanism.SM3_SM2, context.getBytes("UTF8"), priKey, session));
		} catch (PKIException e) {
			e.printStackTrace();
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		return sign;
	}
	
	
	public static String dncryptNoticeBody(String encryptContext,KeyPath keyPath) {
		JSONObject resp = JSONObject.parseObject(encryptContext);
		String context = resp.getString("context");
//		String priKeyAbsPath = Config.getProperty("merchantPrivateKey");
//		String priKeyPWD = Config.getProperty("merchantPwd");
		String priKeyAbsPath = Config.getKeySm2(keyPath.getPrivateKey());
		String priKeyPWD = keyPath.getPwd();
		String decodeText = null;
		try {
			PrivateKey priKey = KeyUtil.getPrivateKeyFromSM2(priKeyAbsPath, priKeyPWD);
			X509Cert cert = CertUtil.getCertFromSM2(priKeyAbsPath);
			byte[] sourceData = EnvelopeUtil.openEvelopedMessage(context.getBytes("UTF8"), priKey, cert, session);
			decodeText = new String(sourceData, "UTF8");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return decodeText;
	}
	
	public static String getStrMD5(String s) {
		try {
			byte[] btInput = s.getBytes();
			// 获得MD5摘要算法的 MessageDigest 对象
			MessageDigest digest = MessageDigest.getInstance("MD5");
			// 使用指定的字节更新摘要
			digest.update(btInput);
			// 获得密文
			StringBuffer sb = new StringBuffer();
			byte[] dbs = digest.digest();
			for (int i = 0; i < dbs.length; i++) {
				String tmp = Integer.toHexString(0xFF & dbs[i]);
				sb.append(tmp.length() == 1 ? "0" : "");
				sb.append(tmp);
			}
			return sb.toString();
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}
}
