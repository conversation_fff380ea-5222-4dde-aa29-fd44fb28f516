<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.departure.mapper.AllAirWaybillMapper">
    <update id="updateDelById">
        update all_air_waybill set is_del = 1, version = version + 1 where id = #{id} and version = #{version}
    </update>
    <update id="updateDataById">
        update all_air_waybill set status = 'out_stock', version = version + 1 where id = #{id} and version = #{version}
    </update>
    <select id="selectWaybillStatusListForRecordOrder"
            resultType="com.gzairports.hz.business.departure.domain.vo.WaybillStatusVo">
        select distinct aaw.id, aaw.waybill_code, aaw.shipping_agent, aaw.switch_bill, aaw.transfer_bill, aaw.special_cargo_code1, aaw.cargo_name, aaw.quantity,
            aaw.weight, aaw.charge_weight, aaw.volume, aaw.size, aaw.status, aaw.source_port, aaw.des_port, aaw.flight_no1, aaw.flight_date1,
            aaw.carrier1, aaw.flight_no2, aaw.flight_date2, aaw.carrier2, aaw.flight_no3, aaw.flight_date3, aaw.carrier3, aaw.shipper_abb,
            aaw.shipper, aaw.shipper_address, aaw.shipper_phone, aaw.consign_abb, aaw.consign, aaw.consign_address, aaw.consign_phone,
            aaw.agent_company, aaw.agent_code, aaw.city, aaw.settlement_notes, aaw.storage_transport_notes, aaw.customs_supervision, aaw.write_time,
            aaw.write_location, aaw.writer, aaw.dept_id, aaw.special_cargo_code2, aaw.special_cargo_code3, aaw.other_special_cargo_code
        from all_air_waybill aaw
        <where>
            aaw.is_del = 0 and aaw.type = 'DEP'
            <if test="status == null or status == ''"> and aaw.status != 'staging' </if>
            <if test="status != null and status != ''">
                <choose>
                    <when test="status == 'pre_pay'">
                        and aaw.pay_status in ('1', '2', '3', '4')
                    </when>
                    <otherwise>
                        and aaw.status = #{status}
                    </otherwise>
                </choose>
            </if>
            <if test="waybillCode != null  and waybillCode != ''"> and aaw.waybill_code like CONCAT('%',#{waybillCode},'%')</if>
            <if test="sourcePort != null  and sourcePort != ''"> and aaw.source_port = #{sourcePort}</if>
            <if test="desPort != null  and desPort != ''"> and aaw.des_port = #{desPort}</if>
            <if test="flightNo1 != null  and flightNo1 != ''"> and aaw.flight_no1 like CONCAT('%',#{flightNo1},'%')</if>
            <if test="flightDate1 != null"> and aaw.flight_date1 = #{flightDate1}</if>
            <if test="carrier1 != null  and carrier1 != ''"> and aaw.carrier1 = #{carrier1}</if>
            <if test="des1 != null  and des1 != ''"> and aaw.des1 = #{des1}</if>
            <if test="shipper != null  and shipper != ''"> and aaw.shipper = #{shipper}</if>
            <if test="consign != null  and consign != ''"> and aaw.consign = #{consign}</if>
            <if test="specialCargoCode1 != null  and specialCargoCode1 != ''"> and aaw.special_cargo_code1 = #{specialCargoCode1}</if>
            <if test="cargoCode != null  and cargoCode != ''"> and aaw.cargo_code = #{cargoCode}</if>
            <if test="type != null  and type != ''"> and aaw.type = #{type}</if>
            <if test="startTime != null">
                and aaw.write_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and aaw.write_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="domint != null  and domint != ''"> and aaw.domint = #{domint}</if>
        </where>
    </select>
    <select id="getInfo" resultType="com.gzairports.hz.business.departure.domain.vo.WaybillInfoVo">
        select all_air_waybill.id, waybill_code, arr_waybill_code,special_cargo_code1,cargo_code, cargo_name, quantity, category_name, cold_store, pressure_chamber,
               weight, charge_weight, source_port, des_port, flight_no1, flight_date1, volume, size, pack, pack_code, carrier1, des1, carrier2, des2,
               carrier3, des3, shipper, shipper_phone, shipper_address, shipper_region, consign, consign_phone, consign_address, consign_region,
               agent_company, all_air_waybill.agent_code, write_location, customs_supervision, security_submit, security_submit_wl, write_time, write_location,
               writer, remark as chineseDescription, cross_air, is_load, sys_dept.dept_name as agent, all_air_waybill.status, pdf_url,storage_transport_notes,
               security_url, transport_file,transport_file_pdf, is_south, pay_status, collect_status
        from all_air_waybill
        left join sys_dept on sys_dept.dept_id = all_air_waybill.dept_id
        <where>
            and all_air_waybill.type = 'DEP' and all_air_waybill.status != 'staging'
            <if test="waybillCode != null  and waybillCode != ''"> and waybill_code = #{waybillCode}</if>
            <if test="id != null"> and all_air_waybill.id = #{id}</if>
        </where>
    </select>
    <select id="selectByWaybillId" resultType="com.gzairports.hz.business.departure.domain.vo.TransferWaybillVo">
        select id, waybill_code, des_port, flight_no1 as flight, cargo_code, cargo_name, quantity, weight, charge_weight, status
        from all_air_waybill where is_del = 0 and id = #{id}
    </select>
    <select id="getInfoById" resultType="com.gzairports.hz.business.departure.domain.vo.WaybillInfoVo">
        select id, waybill_code, special_cargo_code1, cargo_name, quantity, category_name,
        weight, charge_weight, source_port, des_port, flight_no1, flight_date1,
        carrier1, des1, carrier2, des2, carrier3, des3, shipper, consign, customs_supervision,
        write_time, write_location, writer, remark, storage_transport_notes
        from all_air_waybill where id = #{id} and is_del = 0
    </select>
    <select id="virtualList" resultType="com.gzairports.hz.business.departure.domain.vo.VirtualListVo">
        select aaw.id, aaw.waybill_code, aaw.special_cargo_code1, aaw.cargo_name, aaw.quantity, aaw.weight, aaw.volume, aaw.size,
               aaw.des_port, aaw.flight_no1, aaw.storage_transport_notes, aaw.write_time, ba.agent_abb, ba.agent
        from all_air_waybill aaw left join base_agent ba on aaw.dept_id = ba.dept_id
        <where>
            aaw.is_del = 0 and aaw.status = 'been_sent' and type = 'DEP' and aaw.waybill_type = 'AWBA'
            <if test="agent != null and agent != ''"> and aaw.agent_code = #{agent}</if>
            <if test="des1 != null and des1 != ''"> and aaw.des1 = #{des1}</if>
            <if test="waybillCode != null and waybillCode != ''"> and aaw.waybill_code = #{waybillCode}</if>
            <if test="startTime != null">
                and aaw.write_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and aaw.write_time <![CDATA[<=]]> #{endTime}
            </if>
        </where>
        order by aaw.write_time desc
    </select>
    <select id="selectChargeList" resultType="com.gzairports.hz.business.departure.domain.vo.ChargeWaybillVo">
        select aaw.id, aaw.switch_bill, aaw.write_time, aaw.flight_no1, aaw.flight_date1 as execDate, aaw.waybill_code, sd.dept_name as agentCode, aaw.category_name, aaw.cargo_code,
                aaw.cargo_name, aaw.special_cargo_code1, aaw.quantity, aaw.weight, aaw.charge_weight, aaw.status, aaw.des_port, aaw.carrier1,aaw.pay_time,
                aaw.pay_status, aaw.settle_time, aaw.refund, aaw.dept_id
        from all_air_waybill aaw
            left join hz_collect_waybill hcw on aaw.id = hcw.waybill_id
            left join sys_dept sd on sd.dept_id = aaw.dept_id
            left join (
                select hflw.waybill_id as waybill_id, hfl1.flight_id as flight_id
                from hz_flight_load_waybill hflw
                left join hz_flight_load hfl1 on hfl1.id = hflw.flight_load_id

                union all

                select hfluw.waybill_id as waybill_id, hfl2.flight_id as flight_id
                from hz_flight_load_uld_waybill hfluw
                left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
                left join hz_flight_load hfl2 on hfl2.id = hflu.flight_load_id
            ) combined_flights on aaw.id = combined_flights.waybill_id
            left join all_flight_info afi on afi.flight_id = combined_flights.flight_id
        <where>
            aaw.type = 'DEP' and aaw.is_del = 0
            <if test="query.waybillCode != null and query.waybillCode != ''"> and aaw.waybill_code like CONCAT('%',#{query.waybillCode},'%')</if>
            <if test="query.agentCode != null and !query.agentCode.isEmpty()">
                and (
                <foreach item="code" index="index" collection="query.agentCode" open="" separator=" or " close="">
                    sd.dept_name = #{code}
                </foreach>
                )
            </if>
            <if test="query.status != null and query.status != ''">
                <choose>
                    <when test="query.status == 'been_settle'">
                        and aaw.status = #{query.status}
                    </when>
                    <otherwise>
                        and aaw.status != 'been_settle'
                    </otherwise>
                </choose>
            </if>
            <if test="query.payStatus != null">
                <choose>
                    <when test="query.payStatus.contains(0) and query.payStatus.contains(1) and query.payStatus.contains(2)">
                        AND (aaw.pay_status IN (1, 5, 9) OR aaw.pay_status IN (3,4,7,8,11,12) OR aaw.pay_status IN (2,6,10))
                    </when>
                    <when test="query.payStatus.contains(0) and query.payStatus.contains(1)">
                        AND (aaw.pay_status IN (1, 5, 9) OR aaw.pay_status IN (3,4,7,8,11,12))
                    </when>
                    <when test="query.payStatus.contains(0) and query.payStatus.contains(2)">
                        AND (aaw.pay_status IN (1, 5, 9) OR aaw.pay_status IN (2,6,10))
                    </when>
                    <when test="query.payStatus.contains(1) and query.payStatus.contains(2)">
                        AND (aaw.pay_status IN (3,4,7,8,11,12) OR aaw.pay_status IN (2,6,10))
                    </when>
                    <when test="query.payStatus.contains(0)">
                        AND aaw.pay_status IN (1, 5, 9)
                    </when>
                    <when test="query.payStatus.contains(1)">
                        AND aaw.pay_status IN (3,4,7,8,11,12)
                    </when>
                    <when test="query.payStatus.contains(2)">
                        AND aaw.pay_status IN (2,6,10)
                    </when>
                </choose>
            </if>
            <if test="query.specialCargoCode1 != null and query.specialCargoCode1 != ''"> and aaw.special_cargo_code1 like CONCAT('%',#{query.specialCargoCode1},'%')</if>
            <if test="query.flightLoadStartTime != null">
                and afi.start_scheme_takeoff_time <![CDATA[>=]]> #{query.flightLoadStartTime}
            </if>
            <if test="query.flightLoadEndTime != null">
                and afi.start_scheme_takeoff_time <![CDATA[<=]]> #{query.flightLoadEndTime}
            </if>
            <if test="query.startTime != null">
                and hcw.collect_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and hcw.collect_time <![CDATA[<=]]> #{query.endTime}
            </if>
            <if test="query.startTimeSettle != null">
                and aaw.settle_time <![CDATA[>=]]> #{query.startTimeSettle}
            </if>
            <if test="query.endTimeSettle != null">
                and aaw.settle_time <![CDATA[<=]]> #{query.endTimeSettle}
            </if>
            <if test="query.writeStartTime != null">
                and aaw.write_time <![CDATA[>=]]> #{query.writeStartTime}
            </if>
            <if test="query.writeEndTime != null">
                and aaw.write_time <![CDATA[<=]]> #{query.writeEndTime}
            </if>
        </where>
        group by aaw.id
    </select>
    <select id="selectCargoName" resultType="com.gzairports.hz.business.departure.domain.HzColdRegister">
        select id, cargo_name
        from all_air_waybill
        where waybill_code = #{waybillCode} and type = #{type}
        <if test="deptId != null">
            and dept_id = #{deptId}
        </if>
    </select>
    <select id="getInfoByCode" resultType="com.gzairports.hz.business.arrival.domain.vo.EnterWaybillVo">
        select id, waybill_code, replenish_bill, file_arr, prioritize, transfer_bill, source_port, flight_no1, flight_date1, carrier1, des1, carrier2, des2, carrier3, des3,
               des_port, quantity, weight, charge_weight, shipper, agent_code, consign, consign_id_car, consign_phone, cargo_code, cargo_name, category_name,
               special_cargo_code1, is_cold, cold_store, customs_supervision, is_transfer, transfer_no, arr_pay, cost_sum, remark
        from all_air_waybill
        <where>
            type = 'ARR'
            <if test="waybillCode != null  and waybillCode != ''"> and waybill_code = #{waybillCode}</if>
        </where>
    </select>
<!--    and is_del = 0-->
    <select id="selectWaybillStatusListForFlightAcceptance"
            resultType="com.gzairports.hz.business.departure.domain.vo.WaybillStatusVo">
        (
        select aaw.id, aaw.waybill_code, aaw.shipping_agent, aaw.switch_bill, aaw.transfer_bill,
        aaw.special_cargo_code1, aaw.cargo_name, aaw.quantity,
        aaw.weight, aaw.charge_weight, aaw.volume, aaw.size, aaw.status, aaw.source_port, aaw.des_port, aaw.flight_no1,
        aaw.flight_date1,
        aaw.carrier1, aaw.flight_no2, aaw.flight_date2, aaw.carrier2, aaw.flight_no3, aaw.flight_date3, aaw.carrier3,
        aaw.shipper_abb,
        aaw.shipper, aaw.shipper_address, aaw.shipper_phone, aaw.consign_abb, aaw.consign, aaw.consign_address,
        aaw.consign_phone,
        aaw.agent_company, aaw.agent_code, aaw.city, aaw.settlement_notes, aaw.storage_transport_notes,
        aaw.customs_supervision, aaw.write_time,
        aaw.write_location, aaw.writer, aaw.special_cargo_code2,
        aaw.special_cargo_code3, aaw.other_special_cargo_code
        from hz_flight_load hfl
        left join hz_flight_load_waybill hflw on hflw.flight_load_id = hfl.id
        left join all_air_waybill aaw on aaw.id = hflw.waybill_id
        where aaw.id is NOT NULL and aaw.is_del = 0 and hfl.flight_id in
        (select flight_id
        from all_flight_info afi
        <where>
            <if test="startTime != null">
                and DATE_FORMAT(afi.exec_date,'%Y%m%d %H:%i:%s') <![CDATA[>=]]>
                DATE_FORMAT(#{startTime, jdbcType=DATE},'%Y%m%d %H:%i:%s')
            </if>
            <if test="endTime != null">
                and DATE_FORMAT(afi.exec_date,'%Y%m%d %H:%i:%s') <![CDATA[>=]]>
                DATE_FORMAT(#{endTime, jdbcType=DATE},'%Y%m%d %H:%i:%s')
            </if>
        </where>
        )
        <if test="status != null and status != ''">
            <choose>
                <when test="status == 'pre_pay'">
                    and aaw.pay_status in ('1', '2', '3', '4')
                </when>
                <otherwise>
                    and aaw.status = #{status}
                </otherwise>
            </choose>
        </if>
        <if test="waybillCode != null  and waybillCode != ''">and aaw.waybill_code like
            CONCAT('%',#{waybillCode},'%')
        </if>
        <if test="sourcePort != null  and sourcePort != ''">and aaw.source_port = #{sourcePort}</if>
        <if test="desPort != null  and desPort != ''">and aaw.des_port = #{desPort}</if>
        <if test="flightNo1 != null  and flightNo1 != ''">and aaw.flight_no1 like CONCAT('%',#{flightNo1},'%')</if>
        <if test="flightDate1 != null">and aaw.flight_date1 = #{flightDate1}</if>
        <if test="carrier1 != null  and carrier1 != ''">and aaw.carrier1 = #{carrier1}</if>
        <if test="des1 != null  and des1 != ''">and aaw.des1 = #{des1}</if>
        <if test="shipper != null  and shipper != ''">and aaw.shipper = #{shipper}</if>
        <if test="consign != null  and consign != ''">and aaw.consign = #{consign}</if>
        <if test="specialCargoCode1 != null  and specialCargoCode1 != ''">and aaw.special_cargo_code1 =
            #{specialCargoCode1}
        </if>
        <if test="cargoCode != null  and cargoCode != ''">and aaw.cargo_code = #{cargoCode}</if>
        <if test="type != null  and type != ''">and aaw.type = #{type}</if>
        <if test="domint != null  and domint != ''">and aaw.domint = #{domint}</if>

        )
        union
        (select aaw.id, aaw.waybill_code, aaw.shipping_agent, aaw.switch_bill, aaw.transfer_bill,
        aaw.special_cargo_code1, aaw.cargo_name, aaw.quantity,
        aaw.weight, aaw.charge_weight, aaw.volume, aaw.size, aaw.status, aaw.source_port, aaw.des_port, aaw.flight_no1,
        aaw.flight_date1,
        aaw.carrier1, aaw.flight_no2, aaw.flight_date2, aaw.carrier2, aaw.flight_no3, aaw.flight_date3, aaw.carrier3,
        aaw.shipper_abb,
        aaw.shipper, aaw.shipper_address, aaw.shipper_phone, aaw.consign_abb, aaw.consign, aaw.consign_address,
        aaw.consign_phone,
        aaw.agent_company, aaw.agent_code, aaw.city, aaw.settlement_notes, aaw.storage_transport_notes,
        aaw.customs_supervision, aaw.write_time,
        aaw.write_location, aaw.writer, aaw.special_cargo_code2,
        aaw.special_cargo_code3, aaw.other_special_cargo_code
        from hz_flight_load hfl
        left join hz_flight_load_uld hflu on hflu.flight_load_id = hfl.id
        left join hz_flight_load_uld_waybill hfluw on hfluw.load_uld_id = hflu.id
        left join all_air_waybill aaw on aaw.id = hfluw.waybill_id
        where aaw.id is NOT NULL and aaw.is_del = 0 and hfl.flight_id in
        (select flight_id
        from all_flight_info afi
        <where>
            <if test="startTime != null">
                and DATE_FORMAT(afi.exec_date,'%Y%m%d %H:%i:%s') <![CDATA[>=]]>
                DATE_FORMAT(#{startTime, jdbcType=DATE},'%Y%m%d %H:%i:%s')
            </if>
            <if test="endTime != null">
                and DATE_FORMAT(afi.exec_date,'%Y%m%d %H:%i:%s') <![CDATA[>=]]>
                DATE_FORMAT(#{endTime, jdbcType=DATE},'%Y%m%d %H:%i:%s')
            </if>
        </where>
        )
        <if test="status != null and status != ''">
            <choose>
                <when test="status == 'pre_pay'">
                    and aaw.pay_status in ('1', '2', '3', '4')
                </when>
                <otherwise>
                    and aaw.status = #{status}
                </otherwise>
            </choose>
        </if>
        <if test="waybillCode != null  and waybillCode != ''">and aaw.waybill_code like
            CONCAT('%',#{waybillCode},'%')
        </if>
        <if test="sourcePort != null  and sourcePort != ''">and aaw.source_port = #{sourcePort}</if>
        <if test="desPort != null  and desPort != ''">and aaw.des_port = #{desPort}</if>
        <if test="flightNo1 != null  and flightNo1 != ''">and aaw.flight_no1 like CONCAT('%',#{flightNo1},'%')</if>
        <if test="flightDate1 != null">and aaw.flight_date1 = #{flightDate1}</if>
        <if test="carrier1 != null  and carrier1 != ''">and aaw.carrier1 = #{carrier1}</if>
        <if test="des1 != null  and des1 != ''">and aaw.des1 = #{des1}</if>
        <if test="shipper != null  and shipper != ''">and aaw.shipper = #{shipper}</if>
        <if test="consign != null  and consign != ''">and aaw.consign = #{consign}</if>
        <if test="specialCargoCode1 != null  and specialCargoCode1 != ''">and aaw.special_cargo_code1 =
            #{specialCargoCode1}
        </if>
        <if test="cargoCode != null  and cargoCode != ''">and aaw.cargo_code = #{cargoCode}</if>
        <if test="type != null  and type != ''">and aaw.type = #{type}</if>
        <if test="domint != null  and domint != ''">and aaw.domint = #{domint}</if>
        )
    </select>

    <select id="detail" resultType="com.gzairports.hz.business.arrival.domain.vo.WaybillDetailVo">
        select waybill_code, source_port, carrier1, des1, carrier2, des2, carrier3, des3, des_port, special_cargo_code1, cargo_code, cargo_name, category_name, quantity,
               weight, charge_weight, customs_supervision, is_transfer, remark, consign
        from all_air_waybill where type = 'ARR' and is_del = 0 and waybill_code = #{waybillCode}
    </select>
    <select id="pickOne" resultType="com.gzairports.hz.business.transfer.domain.vo.TransferPickVo">
        select aaw.id as waybillId, aaw.waybill_code, aaw.quantity, aaw.weight, aaw.charge_weight, aaw.shipper,
               aaw.special_cargo_code1, aaw.cargo_name, aaw.remark,aaw.transfer_bill
        from all_air_waybill aaw
        where aaw.is_del = 0 and aaw.type = 'ARR' and  aaw.transfer_bill = 1 and aaw.waybill_code = #{waybillCode}
    </select>
    <select id="batchPick" resultType="com.gzairports.hz.business.transfer.domain.vo.TransferPickVo">
        select haro.id as orderId, aaw.id as waybillId, haro.waybill_code,  aaw.special_cargo_code1, aaw.cargo_name, aaw.quantity, aaw.weight, aaw.shipper, aaw.remark,
               aaw.charge_weight,  CONCAT(afi.air_ways,afi.flight_no) as flightNo
        from hz_arr_record_order haro
            left join all_air_waybill aaw on aaw.waybill_code = haro.waybill_code
            left join hz_flight_load hfl on hfl.id = haro.leg_id
            left join all_flight_info afi on hfl.flight_id = afi.flight_id
        where aaw.is_del = 0 and aaw.type = 'ARR' and aaw.transfer_bill = 1
        <if test="execDate != null "> and afi.exec_date = #{execDate}</if>
        <if test="flightNo != null  and flightNo != ''"> and CONCAT(afi.air_ways,afi.flight_no) = #{flightNo} </if>
        <if test="shipper != null  and shipper != ''"> and aaw.shipper = #{shipper}</if>
        <if test="startTime != null">
            and haro.order_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            and haro.order_time <![CDATA[<=]]> #{endTime}
        </if>
        <if test="isTransfer != null"> and aaw.is_transfer = #{isTransfer}</if>
    </select>
    <select id="selectByIds" resultType="com.gzairports.hz.business.transfer.domain.vo.TransferPickVo">
        select haro.id as orderId, aaw.id as waybillId, haro.waybill_code, aaw.quantity, aaw.weight, aaw.charge_weight, aaw.shipper,
                aaw.special_cargo_code1, aaw.cargo_name, aaw.remark, hat.pieces as tallyQuantity, hat.weight as tallyWeight,
                CONCAT(afi.air_ways,afi.flight_no) as flightNo
        from hz_arr_record_order haro
            left join hz_arr_tally hat on hat.record_order_id = haro.id
            left join all_air_waybill aaw on aaw.waybill_code = haro.waybill_code
            left join hz_flight_load hfl on hfl.id = haro.leg_id
            left join all_flight_info afi on hfl.flight_id = afi.flight_id
        <where>
            <if test="waybillIds != null and waybillIds.size() > 0">
                and aaw.id in
                <foreach collection="waybillIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>

    </select>
    <select id="selectInfoById" resultType="com.gzairports.hz.business.arrival.domain.vo.EnterWaybillVo">
        select waybill_code, source_port, carrier1, des1, carrier2, des2, carrier3, des3, des_port, quantity, weight,
               charge_weight, shipper, consign, consign_id_car, consign_phone, cargo_code, cargo_name, category_name,
               special_cargo_code1, cold_store, customs_supervision, is_transfer, transfer_no, arr_pay, cost_sum,
               remark as waybillRemark
        from all_air_waybill where id = #{waybillId}
    </select>
    <select id="pickList" resultType="com.gzairports.hz.business.transfer.domain.vo.TransferPickVo">
        select id as waybillId, waybill_code, quantity, weight, charge_weight, shipper, special_cargo_code1, cargo_name, remark
        from all_air_waybill
        <where>
            id in
            <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </where>
    </select>
    <select id="selectChargeInfo" resultType="com.gzairports.hz.business.departure.domain.vo.ChargeStatusVo">
        select pay_status, settle_time, pay_time, dept_id from all_air_waybill where type = 'DEP'
        <if test="waybillCode != null and waybillCode != ''">
            and waybill_code = #{waybillCode}
        </if>
    </select>
    <select id="selectCargoNames" resultType="java.lang.String">
        select cargo_name from all_air_waybill
        <where>
            id in
            <foreach collection="collect" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </where>
    </select>
    <select id="selectEightCodeList"
            resultType="com.gzairports.hz.business.departure.domain.vo.WaybillIdAndCodeVo">
        select id as waybillId, waybill_code
        from all_air_waybill
        where type = 'DEP'
          and waybill_code like concat('%', #{code}, '%')
          and write_time >= (NOW() - INTERVAL 7 DAY)
    </select>
    <select id="securitySubmitList" resultType="com.gzairports.hz.business.departure.domain.vo.SecurityVo">
        <bind name="isDel" value="'0'" />
        <bind name="typeDep" value="'DEP'" />
        <choose>
            <when test="securitySubmit == 1">
                <bind name="securitySubmitWl" value="'2'" />
            </when>
            <when test="securitySubmit == 0">
                <bind name="securitySubmitWl" value="'1'" />
            </when>
            <otherwise>
                <bind name="securitySubmitWl" value="securitySubmit" />
            </otherwise>
        </choose>
        select id, waybill_code, shipping_agent as agent, special_cargo_code1,
        cargo_name, quantity, weight, volume, size, des_port, flight_no1, flight_date1 as flightDate,
        storage_transport_notes, security_submit, security_submit_wl, security_url, declaration_consistent,
        is_examine, 1 as type
        from all_air_waybill
        <where>
            is_del = #{isDel} and type = #{typeDep} and security_submit_wl != '0'
            <if test="agent != null and agent != ''"> and shipping_agent = #{agent}</if>
            <if test="des1 != null and des1 != ''"> and des_port = #{des1}</if>
            <if test="startTime != null"> and write_time <![CDATA[>=]]> #{startTime}</if>
            <if test="endTime != null"> and write_time <![CDATA[<=]]> #{endTime}</if>
            <if test="waybillCode != null and waybillCode != ''"> and waybill_code = #{waybillCode}</if>
            <if test="securitySubmitWl != null"> and security_submit_wl = #{securitySubmitWl}</if>
        </where>
        union all
        select id, waybill_code, agent, null as special_cargo_code1,
        cargo_name, quantity, weight, null as volume, null as size, des_port, flight_no1, flight_date1 as flightDate,
        null as storage_transport_notes, security_submit, security_submit_wl, security_url, declaration_consistent,
        is_examine, 0 as type
        from all_security_waybill
        <where>
            security_submit_wl != '0'
            <if test="agent != null and agent != ''"> and agent = #{agent}</if>
            <if test="des1 != null and des1 != ''"> and des_port = #{des1}</if>
            <if test="startTime != null"> and create_time <![CDATA[>=]]> #{startTime}</if>
            <if test="endTime != null"> and create_time <![CDATA[<=]]> #{endTime}</if>
            <if test="waybillCode != null and waybillCode != ''"> and waybill_code = #{waybillCode}</if>
            <if test="securitySubmitWl != null"> and security_submit_wl = #{securitySubmitWl}</if>
        </where>
    </select>
    <select id="selectWaybillbyid" resultType="com.gzairports.hz.business.departure.domain.vo.SecurityVo">
        select id, waybill_code, agent_code as agent, special_cargo_code1,
        cargo_name, quantity, weight, volume, size, des_port, flight_no1,
        storage_transport_notes, security_submit, security_url
        from all_air_waybill
        where id = #{id}
    </select>
    <select id="selectWaybillStatusListForCollect"
            resultType="com.gzairports.hz.business.departure.domain.vo.WaybillStatusVo">
        select distinct aaw.id, aaw.waybill_code, aaw.shipping_agent, aaw.switch_bill, aaw.transfer_bill, aaw.special_cargo_code1, aaw.cargo_name, aaw.quantity,
        aaw.weight, aaw.charge_weight, aaw.volume, aaw.size, aaw.status, aaw.source_port, aaw.des_port, aaw.flight_no1, aaw.flight_date1,
        aaw.carrier1, aaw.flight_no2, aaw.flight_date2, aaw.carrier2, aaw.flight_no3, aaw.flight_date3, aaw.carrier3, aaw.shipper_abb,
        aaw.shipper, aaw.shipper_address, aaw.shipper_phone, aaw.consign_abb, aaw.consign, aaw.consign_address, aaw.consign_phone,
        aaw.agent_company, aaw.agent_code, aaw.city, aaw.settlement_notes, aaw.storage_transport_notes, aaw.customs_supervision, aaw.write_time,
        aaw.write_location, aaw.writer, aaw.special_cargo_code2, aaw.special_cargo_code3, aaw.other_special_cargo_code
        from hz_collect_waybill hcw
        left join all_air_waybill aaw on aaw.id = hcw.waybill_id
        <where>
            aaw.is_del = 0 and aaw.type = "DEP"
            <if test="status != null and status != ''">
                <choose>
                    <when test="status == 'pre_pay'">
                        and aaw.pay_status in ('1', '2', '3', '4')
                    </when>
                    <otherwise>
                        and aaw.status = #{status}
                    </otherwise>
                </choose>
            </if>
            <if test="startTime != null">
                and hcw.collect_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and hcw.collect_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="waybillCode != null  and waybillCode != ''"> and aaw.waybill_code like CONCAT('%',#{waybillCode},'%')</if>
            <if test="sourcePort != null  and sourcePort != ''"> and aaw.source_port = #{sourcePort}</if>
            <if test="desPort != null  and desPort != ''"> and aaw.des_port = #{desPort}</if>
            <if test="flightNo1 != null  and flightNo1 != ''"> and aaw.flight_no1 like CONCAT('%',#{flightNo1},'%')</if>
            <if test="flightDate1 != null"> and aaw.flight_date1 = #{flightDate1}</if>
            <if test="carrier1 != null  and carrier1 != ''"> and aaw.carrier1 = #{carrier1}</if>
            <if test="des1 != null  and des1 != ''"> and aaw.des1 = #{des1}</if>
            <if test="shipper != null  and shipper != ''"> and aaw.shipper = #{shipper}</if>
            <if test="consign != null  and consign != ''"> and aaw.consign = #{consign}</if>
            <if test="specialCargoCode1 != null  and specialCargoCode1 != ''"> and aaw.special_cargo_code1 = #{specialCargoCode1}</if>
            <if test="cargoCode != null  and cargoCode != ''"> and aaw.cargo_code = #{cargoCode}</if>
            <if test="type != null  and type != ''"> and aaw.type = #{type}</if>
            <if test="domint != null  and domint != ''"> and aaw.domint = #{domint}</if>
        </where>
    </select>

    <select id="selectWaybillStatusListForExitCargo"
            resultType="com.gzairports.hz.business.departure.domain.vo.WaybillStatusVo">
        select distinct aaw.id, aaw.waybill_code, aaw.shipping_agent, aaw.switch_bill, aaw.transfer_bill, aaw.special_cargo_code1, aaw.cargo_name, aaw.quantity,
        aaw.weight, aaw.charge_weight, aaw.volume, aaw.size, aaw.status, aaw.source_port, aaw.des_port, aaw.flight_no1, aaw.flight_date1,
        aaw.carrier1, aaw.flight_no2, aaw.flight_date2, aaw.carrier2, aaw.flight_no3, aaw.flight_date3, aaw.carrier3, aaw.shipper_abb,
        aaw.shipper, aaw.shipper_address, aaw.shipper_phone, aaw.consign_abb, aaw.consign, aaw.consign_address, aaw.consign_phone,
        aaw.agent_company, aaw.agent_code, aaw.city, aaw.settlement_notes, aaw.storage_transport_notes, aaw.customs_supervision, aaw.write_time,
        aaw.write_location, aaw.writer, aaw.special_cargo_code2, aaw.special_cargo_code3, aaw.other_special_cargo_code
        from hz_dep_exit_cargo hdec
        left join all_air_waybill aaw on aaw.waybill_code = hdec.waybill_code
        <where>
            aaw.is_del = 0 and aaw.type = "DEP"
            <if test="status != null and status != ''">
                <choose>
                    <when test="status == 'pre_pay'">
                        and aaw.pay_status in ('1', '2', '3', '4')
                    </when>
                    <otherwise>
                        and aaw.status = #{status}
                    </otherwise>
                </choose>
            </if>
            <if test="startTime != null">
                and hdec.start_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and hdec.start_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="waybillCode != null  and waybillCode != ''"> and aaw.waybill_code like CONCAT('%',#{waybillCode},'%')</if>
            <if test="sourcePort != null  and sourcePort != ''"> and aaw.source_port = #{sourcePort}</if>
            <if test="desPort != null  and desPort != ''"> and aaw.des_port = #{desPort}</if>
            <if test="flightNo1 != null  and flightNo1 != ''"> and aaw.flight_no1 like CONCAT('%',#{flightNo1},'%')</if>
            <if test="flightDate1 != null"> and aaw.flight_date1 = #{flightDate1}</if>
            <if test="carrier1 != null  and carrier1 != ''"> and aaw.carrier1 = #{carrier1}</if>
            <if test="des1 != null  and des1 != ''"> and aaw.des1 = #{des1}</if>
            <if test="shipper != null  and shipper != ''"> and aaw.shipper = #{shipper}</if>
            <if test="consign != null  and consign != ''"> and aaw.consign = #{consign}</if>
            <if test="specialCargoCode1 != null  and specialCargoCode1 != ''"> and aaw.special_cargo_code1 = #{specialCargoCode1}</if>
            <if test="cargoCode != null  and cargoCode != ''"> and aaw.cargo_code = #{cargoCode}</if>
            <if test="type != null  and type != ''"> and aaw.type = #{type}</if>
            <if test="domint != null  and domint != ''"> and aaw.domint = #{domint}</if>
        </where>
    </select>
    <select id="getStatusInfo" resultType="com.gzairports.hz.business.departure.domain.vo.WaybillInfoVo">
        select id, waybill_code, special_cargo_code1, cargo_code, cargo_name, quantity, category_name,
                weight, charge_weight, source_port, des_port, flight_no1, flight_date1, carrier1, des1, carrier2, des2,
                carrier3, des3, shipper, consign, customs_supervision, write_time, writer, remark, agent_company as agent,
               is_south, cross_air, collect_status, storage_transport_notes
        from all_air_waybill
        <where>
            and type = 'DEP'
            <if test="waybillCode != null  and waybillCode != ''"> and waybill_code = #{waybillCode}</if>
            <if test="id != null"> and all_air_waybill.id = #{id}</if>
        </where>
    </select>
    <select id="selectQueryList" resultType="com.gzairports.hz.business.arrival.domain.vo.WaybillQueryVo">
        select  aaw.id as waybillId, aaw.waybill_code, aaw.replenish_bill, aaw.file_arr, aaw.prioritize, aaw.transfer_bill, aaw.source_port,
                aaw.carrier1, aaw.des1, aaw.carrier2, aaw.des2, aaw.carrier3, aaw.des3, aaw.des_port, aaw.quantity, aaw.weight, aaw.charge_weight,
                haro.cabin_pieces as cabinQuantity, haro.cabin_weight, aaw.shipper, aaw.consign, aaw.consign_id_car, aaw.consign_phone, aaw.cargo_name,aaw.flight_date1,
                aaw.special_cargo_code1, aaw.cold_store, aaw.flight_no1,
                aaw.customs_supervision, aaw.is_transfer, aaw.transfer_no, aaw.arr_pay, aaw.cost_sum, aaw.storage_transport_notes as remark, haro.order_time
        from all_air_waybill aaw
            left join hz_arr_record_order haro on aaw.waybill_code = haro.waybill_code
            left join hz_arr_tally hat on aaw.waybill_code = hat.waybill_code
            left join all_pick_up_waybill aw on aaw.waybill_code = aw.waybill_code
            left join all_in_pick_up au on au.id = aw.pick_up_id
            left join all_pick_up_out ao on aaw.waybill_code = ao.waybill_code
            left join hz_flight_load hfl on hfl.id = haro.leg_id
            left join all_flight_info afi on hfl.flight_id = afi.flight_id
        where aaw.is_del = 0
        <if test="(waybillType != null and waybillType !='') or (prefix != null and prefix !='') or (waybillCode != null and waybillCode !='')">
            and aaw.waybill_code like CONCAT(IFNULL(#{waybillType}, ''), '%', IFNULL(#{prefix}, ''), '%', IFNULL(#{waybillCode}, ''), '%')
        </if>
        <if test="domint != null and domint != ''">
            and aaw.domint = #{domint}
        </if>
        <if test="type == 'ARR'">
            and aaw.type = #{type}
        </if>
        <if test="type == 'TRANSFER'">
            and aaw.transfer_bill = 1
        </if>
        <if test="sourcePort != null and sourcePort !=''">
            and aaw.source_port = #{sourcePort}
        </if>
        <if test="desPort != null and desPort !=''">
            and aaw.des_port = #{desPort}
        </if>
        <if test="shipper != null and shipper !=''">
            and aaw.shipper = #{shipper}
        </if>
        <if test="consign != null and consign !=''">
            and aaw.consign = #{consign}
        </if>
        <if test="agentCode != null and agentCode !=''">
            and aaw.agent_code = #{agentCode}
        </if>
        <if test="specialCode != null and specialCode !=''">
            and aaw.special_cargo_code1 = #{specialCode}
        </if>
        <if test="carrier != null and carrier !=''">
            and aaw.carrier = #{carrier}
        </if>
        <if test="flightNo != null and flightNo !=''">
            and CONCAT(afi.air_ways,afi.flight_no) like '%${flightNo}%'
        </if>
        <if test="cargoCode != null and cargoCode !=''">
            and aaw.cargo_code = #{cargoCode}
        </if>
        <if test="waybillStatus != null and waybillStatus !=''">
            and aaw.status = #{waybillStatus}
        </if>
        <if test="status == 'record_order'">
            and haro.order_time BETWEEN #{startTime} and #{endTime}
        </if>
        <if test="status == 'tally_comp'">
            and hat.tally_time BETWEEN #{startTime} and #{endTime}
        </if>
        <if test="status == 'comp_order'">
            and au.pick_up_time BETWEEN #{startTime} and #{endTime}
        </if>
        <if test="status == 'out_stock'">
            and ao.out_time BETWEEN #{startTime} and #{endTime}
        </if>
      group by waybillId, haro.cabin_pieces, haro.cabin_weight, haro.order_time
      order by haro.order_time desc
    </select>
    <select id="selectDeptIdByCode" resultType="java.lang.Long">
        select dept_id from all_air_waybill where waybill_code = #{waybillCode} and type = 'ARR' and is_del  = 0
    </select>
    <select id="selectWaybillItemInfo"
            resultType="com.gzairports.common.business.departure.domain.vo.ItemWaybillVo">
        select dept_id, category_name, cargo_name, cross_air from all_air_waybill where waybill_code = #{waybillCode} and type = #{type} and is_del  = 0
    </select>
    <select id="selectDeptId" resultType="java.lang.Long">
        select dept_id from all_air_waybill
        where
        waybill_code = #{waybillCode} and type = #{type} and is_del = 0
    </select>
    <select id="chargeExportList" resultType="com.gzairports.hz.business.departure.domain.vo.ChargeImportVo">
        select aaw.id as waybillId, aaw.waybill_code, aaw.quantity, aaw.weight, aaw.cargo_name, aaw.cargo_code, bcc.chinese_name as cargoName1
        from all_air_waybill aaw
        left join base_cargo_code bcc on bcc.code = aaw.cargo_code
        left join hz_collect_waybill hcw on aaw.id = hcw.waybill_id
        <where>
            aaw.type = 'DEP' and aaw.is_del = 0
            <if test="query.waybillCode != null and query.waybillCode != ''"> and aaw.waybill_code like CONCAT('%',#{query.waybillCode},'%')</if>
            <if test="query.agentCode != null and !query.agentCode.isEmpty()">
                and (
                <foreach item="code" index="index" collection="query.agentCode" open="" separator=" or " close="">
                    aaw.agent_company like CONCAT('%', #{code}, '%')
                </foreach>
                )
            </if>
            <if test="query.status != null and query.status != ''"> and aaw.status = #{query.status}</if>
            <if test="query.specialCargoCode1 != null and query.specialCargoCode1 != ''"> and aaw.special_cargo_code1 like CONCAT('%',#{query.specialCargoCode1},'%')</if>
            <if test="query.startTime != null">
                and hcw.collect_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and hcw.collect_time <![CDATA[<=]]> #{query.endTime}
            </if>
            <if test="query.startTimeSettle != null">
                and aaw.settle_time <![CDATA[>=]]> #{query.startTimeSettle}
            </if>
            <if test="query.endTimeSettle != null">
                and aaw.settle_time <![CDATA[<=]]> #{query.endTimeSettle}
            </if>
            <if test="query.writeStartTime != null">
                and aaw.write_time <![CDATA[>=]]> #{query.writeStartTime}
            </if>
            <if test="query.writeEndTime != null">
                and aaw.write_time <![CDATA[<=]]> #{query.writeEndTime}
            </if>
        </where>
    </select>
    <select id="selectDangerInfo" resultType="com.gzairports.hz.business.departure.domain.vo.PrintCaptainVo">
        select id, waybill_code, des_port, danger_code, danger_type
        from all_air_waybill
        where id = #{id} and danger_code is not null and danger_code != ''
    </select>
    <select id="selectSpecialInfo" resultType="com.gzairports.hz.business.departure.domain.vo.PrintCaptainVo">
        select id, waybill_code, des_port, special_cargo_code1, cargo_name
        from all_air_waybill
        where id = #{id} and special_cargo_code1 is not null and special_cargo_code1 != ''
    </select>
    <select id="selectWaybillCode" resultType="java.lang.String">
        select waybill_code from all_air_waybill where id = #{waybillId}
    </select>
    <select id="selectDelInfoById" resultType="com.gzairports.hz.business.departure.domain.AirWaybill">
        select waybill_code, status, version from all_air_waybill where id = #{id}
    </select>
    <select id="selectComeInfo" resultType="com.gzairports.hz.business.departure.domain.AirWaybill">
        select id, agent_company, waybill_code, quantity, cargo_code, cross_air, is_south, weight, charge_weight, dept_id
        from all_air_waybill
        where waybill_code = #{waybillCode}
          and type = #{type}
          <if test="deptId != null">
              and dept_id = #{deptId}
          </if>
          and is_del = 0
    </select>
    <select id="selectComeInfoNew" resultType="com.gzairports.hz.business.departure.domain.AirWaybill">
        select id, agent_company, waybill_code, quantity, cargo_code, cross_air, is_south, weight, charge_weight, dept_id
        from all_air_waybill
        where waybill_code = #{waybillCode}
        and type = #{type}
        <if test="deptIds != null">
            and dept_id in
            <foreach item="deptId" collection="deptIds" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        and is_del = 0
    </select>
    <select id="selectPayCount" resultType="java.lang.Integer">
        select COUNT(DISTINCT aaw.id)
        from all_air_waybill aaw
        left join hz_collect_waybill hcw on aaw.id = hcw.waybill_id
        left join sys_dept sd on sd.dept_id = aaw.dept_id
        left join (
            select hflw.waybill_id as waybill_id, hfl1.flight_id as flight_id
            from hz_flight_load_waybill hflw
            left join hz_flight_load hfl1 on hfl1.id = hflw.flight_load_id

            union all

            select hfluw.waybill_id as waybill_id, hfl2.flight_id as flight_id
            from hz_flight_load_uld_waybill hfluw
            left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
            left join hz_flight_load hfl2 on hfl2.id = hflu.flight_load_id
        ) combined_flights on aaw.id = combined_flights.waybill_id
        left join all_flight_info afi on afi.flight_id = combined_flights.flight_id
        <where>
            aaw.type = 'DEP' and aaw.is_del = 0
            <if test="query.waybillCode != null and query.waybillCode != ''"> and aaw.waybill_code like CONCAT('%',#{query.waybillCode},'%')</if>
            <if test="query.agentCode != null and !query.agentCode.isEmpty()">
                and (
                <foreach item="code" index="index" collection="query.agentCode" open="" separator=" or " close="">
                    sd.dept_name like CONCAT('%', #{code}, '%')
                </foreach>
                )
            </if>
            <if test="query.status != null and query.status != ''">
                <choose>
                    <when test="query.status == 'been_settle'">
                        and aaw.status = #{query.status}
                    </when>
                    <otherwise>
                        and aaw.status != 'been_settle'
                    </otherwise>
                </choose>
            </if>
            <if test="query.flightLoadStartTime != null">
                and afi.start_scheme_takeoff_time <![CDATA[>=]]> #{query.flightLoadStartTime}
            </if>
            <if test="query.flightLoadEndTime != null">
                and afi.start_scheme_takeoff_time <![CDATA[<=]]> #{query.flightLoadEndTime}
            </if>
            <if test="query.payStatus != null"> and aaw.pay_status = #{query.payStatus}</if>
            <if test="query.specialCargoCode1 != null and query.specialCargoCode1 != ''"> and aaw.special_cargo_code1 like CONCAT('%',#{query.specialCargoCode1},'%')</if>
            <if test="query.startTime != null">
                and hcw.collect_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and hcw.collect_time <![CDATA[<=]]> #{query.endTime}
            </if>
            <if test="query.startTimeSettle != null">
                and aaw.settle_time <![CDATA[>=]]> #{query.startTimeSettle}
            </if>
            <if test="query.endTimeSettle != null">
                and aaw.settle_time <![CDATA[<=]]> #{query.endTimeSettle}
            </if>
            <if test="query.writeStartTime != null">
                and aaw.write_time <![CDATA[>=]]> #{query.writeStartTime}
            </if>
            <if test="query.writeEndTime != null">
                and aaw.write_time <![CDATA[<=]]> #{query.writeEndTime}
            </if>
        </where>
    </select>
    <select id="selectRefund" resultType="com.gzairports.wl.departure.domain.vo.OnlineWaybillVo">
        select aaw.waybill_code, aaw.status, aaw.dept_id
        from all_air_waybill aaw
        left join hz_collect_waybill hcw on aaw.id = hcw.waybill_id
        left join sys_dept sd on sd.dept_id = aaw.dept_id
        left join (
            select hflw.waybill_id as waybill_id, hfl1.flight_id as flight_id
            from hz_flight_load_waybill hflw
            left join hz_flight_load hfl1 on hfl1.id = hflw.flight_load_id

            union all

            select hfluw.waybill_id as waybill_id, hfl2.flight_id as flight_id
            from hz_flight_load_uld_waybill hfluw
            left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
            left join hz_flight_load hfl2 on hfl2.id = hflu.flight_load_id
            ) combined_flights on aaw.id = combined_flights.waybill_id
        left join all_flight_info afi on afi.flight_id = combined_flights.flight_id
        <where>
            aaw.type = 'DEP' and aaw.is_del = 0
            <if test="query.waybillCode != null and query.waybillCode != ''"> and aaw.waybill_code like CONCAT('%',#{query.waybillCode},'%')</if>
            <if test="query.agentCode != null and !query.agentCode.isEmpty()">
                and (
                <foreach item="code" index="index" collection="query.agentCode" open="" separator=" or " close="">
                    sd.dept_name like CONCAT('%', #{code}, '%')
                </foreach>
                )
            </if>
            <if test="query.status != null and query.status != ''">
                <choose>
                    <when test="query.status == 'been_settle'">
                        and aaw.status = #{query.status}
                    </when>
                    <otherwise>
                        and aaw.status != 'been_settle'
                    </otherwise>
                </choose>
            </if>
            <if test="query.payStatus != null"> and aaw.pay_status = #{query.payStatus}</if>
            <if test="query.specialCargoCode1 != null and query.specialCargoCode1 != ''"> and aaw.special_cargo_code1 like CONCAT('%',#{query.specialCargoCode1},'%')</if>
            <if test="query.flightLoadStartTime != null">
                and afi.start_scheme_takeoff_time <![CDATA[>=]]> #{query.flightLoadStartTime}
            </if>
            <if test="query.flightLoadEndTime != null">
                and afi.start_scheme_takeoff_time <![CDATA[<=]]> #{query.flightLoadEndTime}
            </if>
            <if test="query.startTime != null">
                and hcw.collect_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and hcw.collect_time <![CDATA[<=]]> #{query.endTime}
            </if>
            <if test="query.startTimeSettle != null">
                and aaw.settle_time <![CDATA[>=]]> #{query.startTimeSettle}
            </if>
            <if test="query.endTimeSettle != null">
                and aaw.settle_time <![CDATA[<=]]> #{query.endTimeSettle}
            </if>
            <if test="query.writeStartTime != null">
                and aaw.write_time <![CDATA[>=]]> #{query.writeStartTime}
            </if>
            <if test="query.writeEndTime != null">
                and aaw.write_time <![CDATA[<=]]> #{query.writeEndTime}
            </if>
        </where>
    </select>
    <select id="selectByStatusByNull" resultType="com.gzairports.wl.departure.domain.vo.OnlineWaybillVo">
        select aaw.waybill_code, aaw.status, aaw.dept_id
        from all_air_waybill aaw
        left join hz_collect_waybill hcw on aaw.id = hcw.waybill_id
        left join sys_dept sd on sd.dept_id = aaw.dept_id
        left join (
            select hflw.waybill_id as waybill_id, hfl1.flight_id as flight_id
            from hz_flight_load_waybill hflw
            left join hz_flight_load hfl1 on hfl1.id = hflw.flight_load_id

            union all

            select hfluw.waybill_id as waybill_id, hfl2.flight_id as flight_id
            from hz_flight_load_uld_waybill hfluw
            left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
            left join hz_flight_load hfl2 on hfl2.id = hflu.flight_load_id
        ) combined_flights on aaw.id = combined_flights.waybill_id
        left join all_flight_info afi on afi.flight_id = combined_flights.flight_id
        <where>
            aaw.type = 'DEP' and aaw.is_del = 0
            <if test="query.waybillCode != null and query.waybillCode != ''"> and aaw.waybill_code like CONCAT('%',#{query.waybillCode},'%')</if>
            <if test="query.agentCode != null and !query.agentCode.isEmpty()">
                and (
                <foreach item="code" index="index" collection="query.agentCode" open="" separator=" or " close="">
                    sd.dept_name like CONCAT('%', #{code}, '%')
                </foreach>
                )
            </if>
            <if test="query.status != null and query.status != ''">
                <choose>
                    <when test="query.status == 'been_settle'">
                        and aaw.status = #{query.status}
                    </when>
                    <otherwise>
                        and aaw.status != 'been_settle'
                    </otherwise>
                </choose>
            </if>
            <if test="status == 4">
                and aaw.pay_status in (1,2,3,4)
            </if>
            <if test="status == 8">
                and aaw.pay_status in (5,6,7,8)
            </if>
            <if test="status == 0">
                and aaw.pay_status = 0
            </if>
            <if test="status == 9">
                and aaw.pay_status in (9,10,11,12)
            </if>
            <if test="query.specialCargoCode1 != null and query.specialCargoCode1 != ''"> and aaw.special_cargo_code1 like CONCAT('%',#{query.specialCargoCode1},'%')</if>
            <if test="query.flightLoadStartTime != null">
                and afi.start_scheme_takeoff_time <![CDATA[>=]]> #{query.flightLoadStartTime}
            </if>
            <if test="query.flightLoadStartTime != null">
                and afi.start_scheme_takeoff_time <![CDATA[<=]]> #{query.flightLoadEndTime}
            </if>
            <if test="query.startTime != null">
                and hcw.collect_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and hcw.collect_time <![CDATA[<=]]> #{query.endTime}
            </if>
            <if test="query.startTimeSettle != null">
                and aaw.settle_time <![CDATA[>=]]> #{query.startTimeSettle}
            </if>
            <if test="query.endTimeSettle != null">
                and aaw.settle_time <![CDATA[<=]]> #{query.endTimeSettle}
            </if>
            <if test="query.writeStartTime != null">
                and aaw.write_time <![CDATA[>=]]> #{query.writeStartTime}
            </if>
            <if test="query.writeEndTime != null">
                and aaw.write_time <![CDATA[<=]]> #{query.writeEndTime}
            </if>
        </where>
        group by aaw.id
    </select>
    <select id="selectByStatusForPayStatus" resultType="com.gzairports.wl.departure.domain.vo.OnlineWaybillVo">
        select aaw.waybill_code, aaw.status, aaw.dept_id
        from all_air_waybill aaw
        left join hz_collect_waybill hcw on aaw.id = hcw.waybill_id
        left join sys_dept sd on sd.dept_id = aaw.dept_id
        left join (
            select hflw.waybill_id as waybill_id, hfl1.flight_id as flight_id
            from hz_flight_load_waybill hflw
            left join hz_flight_load hfl1 on hfl1.id = hflw.flight_load_id

            union all

            select hfluw.waybill_id as waybill_id, hfl2.flight_id as flight_id
            from hz_flight_load_uld_waybill hfluw
            left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
            left join hz_flight_load hfl2 on hfl2.id = hflu.flight_load_id
        ) combined_flights on aaw.id = combined_flights.waybill_id
        left join all_flight_info afi on afi.flight_id = combined_flights.flight_id
        <where>
            aaw.type = 'DEP' and aaw.is_del = 0
            <if test="query.waybillCode != null and query.waybillCode != ''"> and aaw.waybill_code like CONCAT('%',#{query.waybillCode},'%')</if>
            <if test="query.agentCode != null and !query.agentCode.isEmpty()">
                and (
                <foreach item="code" index="index" collection="query.agentCode" open="" separator=" or " close="">
                    sd.dept_name like CONCAT('%', #{code}, '%')
                </foreach>
                )
            </if>
            <if test="query.status != null and query.status != ''">
                <choose>
                    <when test="query.status == 'been_settle'">
                        and aaw.status = #{query.status}
                    </when>
                    <otherwise>
                        and aaw.status != 'been_settle'
                    </otherwise>
                </choose>
            </if>
            <if test="status == 0">
                and aaw.pay_status in (1,5,9)
            </if>
            <if test="status == 1">
                and aaw.pay_status in (3,4,7,8,11,12)
            </if>
            <if test="status == 2">
                and aaw.pay_status in (2,6,10)
            </if>
            <if test="query.specialCargoCode1 != null and query.specialCargoCode1 != ''"> and aaw.special_cargo_code1 like CONCAT('%',#{query.specialCargoCode1},'%')</if>
            <if test="query.flightLoadStartTime != null">
                and afi.start_scheme_takeoff_time <![CDATA[>=]]> #{query.flightLoadStartTime}
            </if>
            <if test="query.flightLoadEndTime != null">
                and afi.start_scheme_takeoff_time <![CDATA[<=]]> #{query.flightLoadEndTime}
            </if>
            <if test="query.startTime != null">
                and hcw.collect_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and hcw.collect_time <![CDATA[<=]]> #{query.endTime}
            </if>
            <if test="query.startTimeSettle != null">
                and aaw.settle_time <![CDATA[>=]]> #{query.startTimeSettle}
            </if>
            <if test="query.endTimeSettle != null">
                and aaw.settle_time <![CDATA[<=]]> #{query.endTimeSettle}
            </if>
            <if test="query.writeStartTime != null">
                and aaw.write_time <![CDATA[>=]]> #{query.writeStartTime}
            </if>
            <if test="query.writeEndTime != null">
                and aaw.write_time <![CDATA[<=]]> #{query.writeEndTime}
            </if>
        </where>
        group by aaw.id
    </select>
    <select id="selectWeight" resultType="java.math.BigDecimal">
        select weight from all_air_waybill where waybill_code = #{waybillCode} and type = 'ARR' and is_del = 0
    </select>
    <select id="selectErrorData" resultType="java.lang.Long">
        select aaw.id
        from all_air_waybill aaw
        left join hz_collect_waybill hcw on aaw.id = hcw.waybill_id
        left join sys_dept sd on sd.dept_id = aaw.dept_id
        <where>
            aaw.type = 'DEP' and aaw.is_del = 0
            <if test="waybillCode != null and waybillCode != ''"> and aaw.waybill_code like CONCAT('%',#{waybillCode},'%')</if>
            <if test="agentCode != null and !agentCode.isEmpty()">
                and (
                <foreach item="code" index="index" collection="agentCode" open="" separator=" or " close="">
                    sd.dept_name like CONCAT('%', #{code}, '%')
                </foreach>
                )
            </if>
            <if test="startTime != null">
                and hcw.collect_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and hcw.collect_time <![CDATA[<=]]> #{endTime}
            </if>
        </where>
        group by aaw.id
    </select>
    <select id="selectTotalOrderList" resultType="com.gzairports.wl.departure.domain.vo.OnlineWaybillVo">
        select aaw.waybill_code, aaw.status, aaw.dept_id
        from all_air_waybill aaw
        left join hz_collect_waybill hcw on aaw.id = hcw.waybill_id
        left join sys_dept sd on sd.dept_id = aaw.dept_id
        left join (
        select hflw.waybill_id as waybill_id, hfl1.flight_id as flight_id
        from hz_flight_load_waybill hflw
        left join hz_flight_load hfl1 on hfl1.id = hflw.flight_load_id

        union all

        select hfluw.waybill_id as waybill_id, hfl2.flight_id as flight_id
        from hz_flight_load_uld_waybill hfluw
        left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
        left join hz_flight_load hfl2 on hfl2.id = hflu.flight_load_id
        ) combined_flights on aaw.id = combined_flights.waybill_id
        left join all_flight_info afi on afi.flight_id = combined_flights.flight_id
        <where>
            aaw.type = 'DEP' and aaw.is_del = 0
            <if test="query.waybillCode != null and query.waybillCode != ''"> and aaw.waybill_code like CONCAT('%',#{query.waybillCode},'%')</if>
            <if test="query.agentCode != null and !query.agentCode.isEmpty()">
                and (
                <foreach item="code" index="index" collection="query.agentCode" open="" separator=" or " close="">
                    sd.dept_name like CONCAT('%', #{code}, '%')
                </foreach>
                )
            </if>
            <if test="query.status != null and query.status != ''">
                <choose>
                    <when test="query.status == 'been_settle'">
                        and aaw.status = #{query.status}
                    </when>
                    <otherwise>
                        and aaw.status != 'been_settle'
                    </otherwise>
                </choose>
            </if>
            <if test="!query.payStatus.isEmpty() and query.payStatus.size() > 0">
                and aaw.pay_status in
                <foreach collection="query.payStatus" item="payStatus" index="index" open="(" close=")" separator=",">
                     #{query.payStatus}
                </foreach>
            </if>
            <if test="query.flightLoadStartTime != null">
                and afi.start_scheme_takeoff_time <![CDATA[>=]]> #{query.flightLoadStartTime}
            </if>
            <if test="query.flightLoadEndTime != null">
                and afi.start_scheme_takeoff_time <![CDATA[<=]]> #{query.flightLoadEndTime}
            </if>
            <if test="query.specialCargoCode1 != null and query.specialCargoCode1 != ''"> and aaw.special_cargo_code1 like CONCAT('%',#{query.specialCargoCode1},'%')</if>
            <if test="query.startTime != null">
                and hcw.collect_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and hcw.collect_time <![CDATA[<=]]> #{query.endTime}
            </if>
            <if test="query.startTimeSettle != null">
                and aaw.settle_time <![CDATA[>=]]> #{query.startTimeSettle}
            </if>
            <if test="query.endTimeSettle != null">
                and aaw.settle_time <![CDATA[<=]]> #{query.endTimeSettle}
            </if>
            <if test="query.writeStartTime != null">
                and aaw.write_time <![CDATA[>=]]> #{query.writeStartTime}
            </if>
            <if test="query.writeEndTime != null">
                and aaw.write_time <![CDATA[<=]]> #{query.writeEndTime}
            </if>
        </where>
        group by aaw.id
    </select>
    <select id="selectByIdSome" resultType="com.gzairports.hz.business.departure.domain.AirWaybill">
        select switch_bill, waybill_code from all_air_waybill where id = #{waybillId}
    </select>
    <select id="selectWaybill" resultType="java.lang.String">
        SELECT aaw.waybill_code
        FROM (
                 SELECT hflw.waybill_id, hfl1.flight_id
                 FROM hz_flight_load_waybill hflw
                          LEFT JOIN hz_flight_load hfl1 ON hfl1.id = hflw.flight_load_id

                 UNION ALL

                 SELECT hfluw.waybill_id, hfl2.flight_id
                 FROM hz_flight_load_uld_waybill hfluw
                          LEFT JOIN hz_flight_load_uld hflu ON hflu.id = hfluw.load_uld_id
                          LEFT JOIN hz_flight_load hfl2 ON hfl2.id = hflu.flight_load_id
             ) combined_flights left join all_air_waybill aaw ON aaw.id = combined_flights.waybill_id
                                LEFT JOIN all_flight_info afi ON afi.flight_id = combined_flights.flight_id
        WHERE aaw.is_del = 0 and afi.start_scheme_takeoff_time <![CDATA[>]]> #{endTime}
          AND aaw.type = 'DEP' and waybill_code in
        <foreach collection="waybillCodeList" item="code" index="index" open="(" close=")" separator=",">
            #{code}
        </foreach>
    </select>
</mapper>
