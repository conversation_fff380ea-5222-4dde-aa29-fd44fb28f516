package com.gzairports.hz.business.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 代运导入运单返回数据
 * <AUTHOR>
 * @date 2024-06-28
 */
@Data
public class ForwardImportWaybillVo {

    /** 航班预配散舱id */
    private Long id;

    /** 收运id */
    private Long collectId;

    /** 过磅记录id */
    private Long weightId;

    /** 运单id */
    private Long waybillId;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date flightDate;

    /** 品名 */
    private String cargoName;

    /** 特货 */
    private String specialCargoCode1;

    /** 运单号 */
    private String waybillCode;

    /** 始发站 */
    private String sourcePort;

    /** 到达站1 */
    private String des1;

    /** 收运件数 */
    private Integer quantity;

    /** 收运重量 */
    private BigDecimal weight;

    /** 垫板重量 */
    private BigDecimal plateWeight;

    /** 板箱重量 */
    private BigDecimal boardWeight;

    /** 判断类型 1为拉下 */
    private Integer type;

    /** 运单件数 */
    private Integer waybillQuantity;

    /** 运单重量 */
    private BigDecimal waybillWeight;

    /** 运单储运注意事项 */
    private String storageTransportNotes;

    /** 代理人简称 */
    private String deptNameAbb;

    /** 制单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date writeTime;
}
