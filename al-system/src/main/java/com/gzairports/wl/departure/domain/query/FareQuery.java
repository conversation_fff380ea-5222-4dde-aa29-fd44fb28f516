package com.gzairports.wl.departure.domain.query;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 分单自动运价参数
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
@Data
public class FareQuery {

    /** 运单号 */
    private String waybillCode;

    /** 航班1 */
    private String flightNo1;

    /** 运价类型 */
    private Integer rateType;

    /** 运价类型 */
    private String categoryName;

    /** 承运人1 */
    private String carrier1;

    /** 航班2 */
    private String flightNo2;

    /** 发货人简称 */
    private String shipperAbb;

    /** 货品代码 */
    private String cargoCode;

    /** 特货代码1 */
    private String specialCargoCode1;

    /** 始发站 */
    private String sourcePort;

    /** 目的站 */
    private String desPort;

    /** 重量 */
    private BigDecimal weight;

    /** 计费重量 */
    private BigDecimal chargeWeight;

    /** 件数 */
    private Integer quantity;

    /** 体积（M3） */
    private BigDecimal volume;

    /** 运输保险价值 */
    private BigDecimal transportInsureValue;

    /** 是否包仓单 */
    private Integer bulkWarehouse;

    /** 费率/公斤 */
    private BigDecimal ratePerKg;
}
