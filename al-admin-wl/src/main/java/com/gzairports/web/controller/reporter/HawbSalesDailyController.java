package com.gzairports.web.controller.reporter;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gzairports.common.business.reporter.domain.ReportDataHawb;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.utils.poi.ExcelToPdfUtil;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.wl.reporter.domain.GoodsWeightStatistics;
import com.gzairports.wl.reporter.domain.query.GoodsWeightStatisticsQuery;
import com.gzairports.wl.reporter.domain.query.HawbSalesQuery;
import com.gzairports.wl.reporter.domain.query.ReportExportData;
import com.gzairports.wl.reporter.domain.vo.GoodsWeightStatisticsVO;
import com.gzairports.wl.reporter.service.IHawbSalesDailyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-20
 **/
@Validated
@RestController
@RequestMapping("/wl/hawbSales")
@Api(tags = "分单销售日报")
public class HawbSalesDailyController extends BaseController {

    @Autowired
    private IHawbSalesDailyService dailyService;

    /**
     * 报表列表导出
     */
    @GetMapping("/export")
    @ApiOperation(value = "报表列表导出")
    public void export(HawbSalesQuery query, HttpServletResponse response) {
        ReportExportData reportExportData = dailyService.queryExportDataList(query);
        dailyService.export(reportExportData, response);
    }

    /**
     * 报表列表查询
     */
    @GetMapping("/page")
    @ApiOperation(value = "报表列表分页数据")
    public TableDataInfo pageQuery(HawbSalesQuery query) {
        startPage();
        IPage<ReportDataHawb> pageData = dailyService.pageQuery(query);
        return new TableDataInfo(Collections.emptyList(), (int) pageData.getTotal());
    }

    /**
     * 报表打印-PDF文件
     */
    @GetMapping("/print/pdf")
    @ApiOperation(value = "报表打印-PDF文件")
    public void printPDF(HawbSalesQuery query, HttpServletResponse response) {
        ReportExportData reportExportData = dailyService.queryExportDataList(query);
        dailyService.printPDF(reportExportData, response);
    }

    @GetMapping("/goods/weight/statistics")
    @ApiOperation(value = "货量统计")
    public AjaxResult selectGoodsWeightStatistics(GoodsWeightStatisticsQuery query) {
        GoodsWeightStatisticsVO vo = dailyService.selectGoodsWeightStatistics(query);
        return AjaxResult.success(vo);
    }

    @PostMapping("/goods/weight/export")
    @ApiOperation(value = "货量统计导出")
    public void selectGoodsWeightExport(GoodsWeightStatisticsQuery query, HttpServletResponse response) {
        //导出所有数据
        query.setPageNum(1);
        query.setPageSize(Integer.MAX_VALUE);

        GoodsWeightStatisticsVO vo = dailyService.selectGoodsWeightStatistics(query);
        List<GoodsWeightStatistics> statistics = vo.getStatistics();
        statistics.stream()
                .filter(v -> v.getWriteDate() == null)
                .forEach(v -> v.setWriteDate("无日期"));
        GoodsWeightStatistics last = new GoodsWeightStatistics("合计", vo.getWeightTotal());
        statistics.add(last);

        ExcelUtil<GoodsWeightStatistics> util = new ExcelUtil<>(GoodsWeightStatistics.class);
        util.exportExcel(response, statistics, "货量统计");
    }

    @GetMapping("/goods/weight/print")
    @ApiOperation(value = "货量统计打印")
    public void selectGoodsWeightPrint(GoodsWeightStatisticsQuery query, HttpServletResponse response) {
        //导出所有数据
        query.setPageNum(1);
        query.setPageSize(Integer.MAX_VALUE);

        GoodsWeightStatisticsVO vo = dailyService.selectGoodsWeightStatistics(query);
        List<GoodsWeightStatistics> statistics = vo.getStatistics();
        statistics.stream()
                .filter(v -> v.getWriteDate() == null)
                .forEach(v -> v.setWriteDate("无日期"));
        GoodsWeightStatistics last = new GoodsWeightStatistics("合计", vo.getWeightTotal());
        statistics.add(last);

        String sheetName = "货量统计";

        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            ExcelUtil<GoodsWeightStatistics> util = new ExcelUtil<>(GoodsWeightStatistics.class);
            //将Excel文件流写如 baos 中
            util.exportExcelToOutputStream(baos, statistics, sheetName);
            //将输出流 baos 写入 response 中
            ExcelToPdfUtil.convertOutputStreamToPDF(baos, response, sheetName);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    
}
