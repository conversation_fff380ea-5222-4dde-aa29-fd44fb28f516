package com.gzairports.wl.ticket.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.mapper.BillSourceMapper;
import com.gzairports.common.basedata.mapper.BillTypeMapper;
import com.gzairports.common.basedata.mapper.CarrierMapper;
import com.gzairports.common.core.domain.entity.SysDept;
import com.gzairports.common.core.domain.entity.SysUser;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.exception.ServiceException;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.system.mapper.SysUserMapper;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.hz.business.departure.domain.AirWaybill;
import com.gzairports.hz.business.departure.mapper.AllAirWaybillMapper;
import com.gzairports.wl.departure.domain.Hawb;
import com.gzairports.wl.departure.mapper.HawbMapper;
import com.gzairports.wl.ticket.domain.Ticket;
import com.gzairports.wl.ticket.domain.TicketGrant;
import com.gzairports.wl.ticket.domain.TicketNum;
import com.gzairports.wl.ticket.domain.query.TicketNumQuery;
import com.gzairports.wl.ticket.domain.query.TicketQuery;
import com.gzairports.wl.ticket.domain.vo.*;
import com.gzairports.wl.ticket.mapper.TicketGrantMapper;
import com.gzairports.wl.ticket.mapper.TicketLogMapper;
import com.gzairports.wl.ticket.mapper.TicketMapper;
import com.gzairports.wl.ticket.mapper.TicketNumMapper;
import com.gzairports.wl.ticket.service.TicketService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 单证信息service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class TicketServiceImpl extends ServiceImpl<TicketMapper,Ticket> implements TicketService {

    @Autowired
    private TicketMapper ticketMapper;

    @Autowired
    private TicketNumMapper numMapper;

    @Autowired
    private TicketGrantMapper recordMapper;

    @Autowired
    private SysDeptMapper deptMapper;

    @Autowired
    private TicketLogMapper logMapper;

    @Autowired
    private BillSourceMapper billSourceMapper;

    @Autowired
    private BillTypeMapper billTypeMapper;

    @Autowired
    private CarrierMapper carrierMapper;

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private AllAirWaybillMapper airWaybillMapper;

    @Autowired
    private HawbMapper hawbMapper;

    /**
     * 查询单证信息列表列表
     *
     * @param query 查询参数
     * @return 单证信息列表列表
     */
    @Override
    public List<TicketVO> selectList(TicketQuery query) {
        query.setDeptId(SecurityUtils.getHighParentId());
        List<TicketVO> voList = ticketMapper.selectListByQuery(query);
        voList.forEach(e-> {
            //这个integer查的是没有发放的票证的数量
            Integer integer = numMapper.selectCount(new QueryWrapper<TicketNum>()
                    .eq("ticket_id", e.getId())
                    .ne("status","NOTGRANT"));
            Integer count = numMapper.selectCount(new QueryWrapper<TicketNum>()
                    .eq("ticket_id", e.getId())
                    .in("status", "GRANT","USED","CANCEL","INVALID"));
            e.setUsedQuantity(count);
            SysDept sysDept = deptMapper.selectDeptById(e.getDeptId());
            e.setDepartment(sysDept.getDeptName());
            //对每个条目根据范围和使用数量，计算出总数量和剩余数量
            Integer end = e.getEndNumber();
            Integer start = e.getStartNumber();
            e.setQuantity(end != null ? end - start + 1 : null);
            e.setRemainQuantity(e.getQuantity() != null && integer != null ? e.getQuantity() - integer : null);
            e.setCharter(e.getIsChartering() == 0 ? "否" : "是");

            if(e.getBillPrefix() != null && !(("DN").equals(e.getBillPrefix()))){
                String d = String.valueOf(end);
                e.setEndNumber(Integer.valueOf(d + e.getEndCode()));
                String s = String.valueOf(start);
                e.setStartNumber(Integer.valueOf(s + e.getStartCode()));
            }
            e.setStatusStr(STATUS.get(e.getStatus()));
        });
        return voList;
    }

    /**
     * 查询单证详情
     *
     * @param id 查询参数
     * @return 单证信息详情
     */
    @Override
    public TicketDetailVO detail(Long id) {
        TicketDetailVO vo = ticketMapper.selectDetail(id);
//        Integer count = numMapper.selectCount(new QueryWrapper<TicketNum>()
//                .in("status", "GRANT","USED","CANCEL","INVALID")
//                .eq("ticket_id", id));

        //根据运单的使用情况确定已使用数量
        Integer usedTicketCount = 0;
        Ticket ticketById = ticketMapper.selectById(id);
        LambdaQueryWrapper<TicketNum> tnLqw = Wrappers.lambdaQuery(TicketNum.class)
                .in(TicketNum::getTicketId, id);
        List<TicketNum> ticketNumById = numMapper.selectList(tnLqw);
        if (ticketById != null && ticketById.getBillPrefix() != null) {
            List<String> waybillCodeList = ticketNumById.stream()
                    // AWBA 847 1234567 5
                    .map(e -> ticketById.getBillType() + ticketById.getBillPrefix() + e.getNum() + e.getCode())
                    .collect(Collectors.toList());
            //查询已使用的单号数量(当前代理人 & 出港 & 未作废)
            if("AWBA".equals(ticketById.getBillType())){
                LambdaQueryWrapper<AirWaybill> inCountLqw = Wrappers.lambdaQuery(AirWaybill.class)
                        .eq(AirWaybill::getDeptId, SecurityUtils.getDeptId())
                        .eq(AirWaybill::getType, "DEP")
                        .in(AirWaybill::getWaybillCode, waybillCodeList);
                usedTicketCount = airWaybillMapper.selectCount(inCountLqw);
            }else if("HAWB".equals(ticketById.getBillType())){
                LambdaQueryWrapper<Hawb> inCountLqw = Wrappers.lambdaQuery(Hawb.class)
                        .eq(Hawb::getDeptId, SecurityUtils.getDeptId())
                        // .eq(Hawb::getType, "DEP")
                        .in(Hawb::getWaybillCode, waybillCodeList);
                usedTicketCount = hawbMapper.selectCount(inCountLqw);
            }

        }

        //查询已使用的
//        LambdaQueryWrapper<TicketNum> tnLqw = Wrappers.lambdaQuery(TicketNum.class)
//                .in(TicketNum::getTicketId, id)
//                .eq(TicketNum::getStatus, "USED");
////                .ne(TicketNum::getStatus, "NOTGRANT");
//        usedTicketCount = numMapper.selectCount(tnLqw);
        vo.setUsedQuantity(usedTicketCount);
        Integer integer = numMapper.selectCount(new QueryWrapper<TicketNum>().eq("ticket_id", vo.getId()).ne("status","NOTGRANT"));

        Integer end = vo.getEndNumber();
        Integer start = vo.getStartNumber();
        vo.setQuantity(end - start + 1);
        vo.setRemainQuantity(vo.getQuantity() - integer);
        /*SysDept sysDept = deptMapper.selectDeptById(vo.getDeptId());
        vo.setDepartment(sysDept.getDeptName());*/

        if(vo.getBillPrefix() != null && !(("DN").equals(vo.getBillPrefix()))) {
            String d = String.valueOf(end);
            vo.setEndNumber(Integer.valueOf(d + vo.getEndCode()));
            String s = String.valueOf(start);
            vo.setStartNumber(Integer.valueOf(s + vo.getStartCode()));
        }
        List<TicketGrantVO> list = recordMapper.selectListByTicketId(vo.getId());
        if (!CollectionUtils.isEmpty(list)){
            vo.setNumList(list);
        }
        return vo;
    }

    /**
     * 查询单证使用log列表
     *
     * @param id 查询参数
     * @return 单证log详情
     */
    @Override
    public List<TicketOprLogVO> log(Long id) {
        return logMapper.selectList(id);
    }

    /**
     * 单证入库
     *
     * @param ticket 单证信息
     * @return 结果
     */
    @Override
    public Long add(Ticket ticket) {
        //无论主单分单还是邮件单前端只会传相同的BillSourceId过来,需要根据传递的票证类型票证前缀得到真正的BillSourceId
//        Long billSourceId = getBillSourceId(ticket.getBillSourceId(),ticket.getTicketType(),ticket.getTicketPrefix());
//        if (StringUtils.isNotNull(billSourceId)){
//            ticket.setBillSourceId(billSourceId);
//        }
        Long currHighParentId = SecurityUtils.getHighParentId();
        //重复性校验(同一个票证来源的不能有交集，specialDeptName除外)
        List<Ticket> hadTicket = ticketMapper.selectRepeatTicket(ticket);
        if (CollUtil.isNotEmpty(hadTicket)) {
            List<String> hadDeptName = hadTicket.stream().map(t -> {
                return Optional.ofNullable(deptMapper.selectDeptById(t.getDeptId()))
                        .map(SysDept::getDeptName).orElse("");
            }).collect(Collectors.toList());
            String currDeptName = Optional.ofNullable(deptMapper.selectDeptById(currHighParentId))
                    .map(SysDept::getDeptName).orElse("");
            List<String> specialDeptName = CollUtil.newArrayList("贵州航投立达物流有限责任公司", "国内货运部");
            //判断hadDeptName中的元素是否有非specialDeptName的
            if (hadDeptName.contains(currDeptName)
                    || !CollUtil.containsAll(specialDeptName, hadDeptName)) {
                throw new CustomException("此票证信息已入库，请核对");
            }
        }

        ticket.setDeptId(currHighParentId);
        ticket.setRecorder(SecurityUtils.getUsername());
        ticket.setRecordTime(DateUtils.getNowDate());
        ticketMapper.insert(ticket);
        Long id = ticket.getId();
        List<TicketNum> list = new ArrayList<>();
        for (int i = ticket.getStartNumber(); i <= ticket.getEndNumber(); i++) {
            TicketNum ticketNum = new TicketNum();
            ticketNum.setTicketId(id);
            ticketNum.setNum(i);
            if ("AWBA".equals(ticket.getBillType())){
                ticketNum.setCode(i % 7);
            }
            if ("HAWB".equals(ticket.getBillType())){
                ticketNum.setCode(i % 9);
            }
            ticketNum.setStatus("NOTGRANT");
            list.add(ticketNum);
        }
        numMapper.insertBatchSomeColumn(list);
        return id;
    }

//    private Long getBillSourceId(Long billSourceId, String ticketType, String ticketPrefix) {
//        try{
//        //首先根据billSourceId找到name
//        BaseBillSource baseBillSource = billSourceMapper.selectOne(new QueryWrapper<BaseBillSource>()
//                .eq("id", billSourceId)
//                .eq("is_del", 0));
//        String name = baseBillSource.getName();
//        //再根据ticketType得到billTypeId
//        BaseBillType baseBillType = billTypeMapper.selectOne(new QueryWrapper<BaseBillType>()
//                .eq("name", ticketType)
//                .eq("is_del", 0));
//        Long id = baseBillType.getId();
//        //最后根据name,billTypeId,prefix锁定billsourceid
//        BaseBillSource baseBillSourceNew = billSourceMapper.selectOne(new QueryWrapper<BaseBillSource>()
//                .eq("name", name)
//                .eq("bill_type_id", id)
//                .eq("prefix", ticketPrefix)
//                .eq("is_del", 0));
//        return baseBillSourceNew.getId();
//        }catch (Exception e){
//            throw new CustomException("此票证信息入库失败,请重试");
//        }
//    }

    public static void main(String[] args) {
        String currDeptName = "国内货运部";
        List<String> hadDeptName = CollUtil.newArrayList("贵州航投立达物流有限责任公司", "国内货运部");
        if (CollUtil.isNotEmpty(hadDeptName)) {
            String[] specialDeptName = {"贵州航投立达物流有限责任公司", "国内货运部"};
            //判断hadDeptName中的元素是否有非specialDeptName的
            if (hadDeptName.contains(currDeptName)
                    || !CollUtil.containsAll(Arrays.asList(specialDeptName), hadDeptName)) {
                throw new CustomException("此票证信息已入库，请核对");
            }
        }
    }

    /**
     * 根据id查询单证信息
     *
     * @param id 单证id
     * @return 结果
     */
    @Override
    public TicketInfoVO getInfo(Long id) {

        TicketInfoVO vo = ticketMapper.getInfo(id);

        Integer end = vo.getEndNumber();
        Integer start = vo.getStartNumber();

        vo.setNumber(end - start + 1);

        if(vo.getBillPrefix() != null && !(("DN").equals(vo.getBillPrefix()))) {
            String d = String.valueOf(end);
            vo.setEndNumber(Integer.valueOf(d + vo.getEndCode()));
            String s = String.valueOf(start);
            vo.setStartNumber(Integer.valueOf(s + vo.getStartCode()));
        }

        // 查询未发放票号数据
        List<Integer> unissuedList = numMapper.selectByTicketId(vo.getId(),"NOTGRANT");
        List<String> unissued = extracted(unissuedList);
        vo.setUnissued(unissued);

        // 查询发放票号数据
        List<Integer> issueList = numMapper.selectByTicketId(vo.getId(),"GRANT");
        List<String> issue = extracted(issueList);
        vo.setIssue(issue);

        // 查询销号票号数据
        List<Integer> cancelNumberList = numMapper.selectByTicketId(vo.getId(),"CANCEL");
        List<String> cancelNumber = extracted(cancelNumberList);
        vo.setCancelNumber(cancelNumber);

        // 查询已使用票号数据
        List<Integer> usedList = numMapper.selectByTicketId(vo.getId(),"USED");
        List<String> used = extracted(usedList);
        vo.setUsed(used);

        return vo;
    }

    /**
     *
     * 获取合并后票号的开始与结尾集合数据
     *
     * @param data 不同类型的票号
     * @return 集合数据
     */
    private List<String> extracted(List<Integer> data) {
        List<String> strings = new ArrayList<>();
        if (!CollectionUtils.isEmpty(data)){
            if (data.size() == 1){
                strings.add(data.get(0).toString());
                return strings;
            }else {
                List<List<Integer>> list = groupBy(data);
                if (list.size() == 1){
                    Integer head = list.get(0).get(0);
                    Integer tail = list.get(0).get(list.get(0).size() - 1);
                    strings.add(head + "~" + tail);
                    return strings;
                }else {
                    for (List<Integer> integerList : list) {
                        Integer head = integerList.get(0);
                        Integer tail = integerList.get(integerList.size() - 1);
                        if (head.equals(tail)){
                            strings.add(head.toString());
                        }else {
                            strings.add(head + "~" + tail);
                        }
                    }
                    return strings;
                }
            }
        }
        return strings;
    }

    /**
     * 单证票号销号
     *
     * @param query 销号票号信息
     * @return 结果
     */
    @Override
    public int cancel(TicketNumQuery query) {
        Ticket ticket = ticketMapper.selectById(query.getId());
        if (query.getStartNum() <ticket.getStartNumber() || query.getStartNum() > query.getEndNum()){
            throw new ServiceException("销号运单号无效");
        }
        if (query.getEndNum() < ticket.getStartNumber() || query.getEndNum() > ticket.getEndNumber()){
            throw new ServiceException("销号运单号无效");
        }
        query.setStatus(2);
        ticketMapper.editRemark(query);
        return numMapper.cancelByQuery(query);
    }

    /**
     * 单证票号发放
     *
     * @param query 发放票号数量
     * @return 结果
     */
    @Override
    public int grant(TicketNumQuery query) {
        Ticket ticket = ticketMapper.selectById(query.getId());
        if (query.getStartNum() < ticket.getStartNumber() || query.getStartNum() > query.getEndNum()){
            throw new ServiceException("发放运单号无效");
        }
        if (query.getEndNum() < ticket.getStartNumber() || query.getEndNum() > ticket.getEndNumber()){
            throw new ServiceException("发放运单号无效");
        }

        TicketGrant ticketGrant = recordMapper.selectNew(query.getId());
        if (ticketGrant != null){
            String[] split = ticketGrant.getGrantNum().split("-");
            String s = split[1];
            int integer = 0;
            //这里邮件单和主单都是8位 分单是七位 只有主单校验的时候需要去掉最后一位校验位
            //只有主单需要去掉最后一位
            Ticket ticketById = ticketMapper.selectById(ticketGrant.getTicketId());
            if("DN".equals(ticketById.getBillPrefix()) ||s.length() == 7){
                integer = Integer.parseInt(s);
            }else{
                integer = Integer.parseInt(s.substring(0, s.length() - 1));
            }
//            if(s.length() == 8){
//                integer = Integer.parseInt(s);
//            }else{
//                integer = Integer.parseInt(s.substring(0, s.length() - 1));
//            }
            if (!query.getStartNum().equals(integer + 1)){
                throw new CustomException("请按顺序发放，上次结束号段为"+integer+"");
            }
        }

        String start = String.valueOf(query.getStartNum());
        String end = String.valueOf(query.getEndNum());

        TicketGrant record = new TicketGrant();
        record.setGrantTime(DateUtils.getNowDate());
        record.setTicketId(query.getId());
        record.setUserId(query.getUseBy());
        if(ticket.getBillPrefix() != null && !(("DN").equals(ticket.getBillPrefix()))) {
            record.setGrantNum(start + ticket.getStartCode() + "-" + end + ticket.getEndCode());
        }else{
            record.setGrantNum(start + "-" + end);
        }
        record.setTotal(query.getEndNum() - query.getStartNum() + 1);
        recordMapper.insert(record);
        Integer usedTotal = recordMapper.selectTotal(query.getId());

        Integer total = ticket.getEndNumber() - ticket.getStartNumber() + 1;
        if (total.equals(usedTotal)){
            query.setStatus(3);
        }else {
            query.setStatus(2);
        }
        ticketMapper.editRemark(query);
        //12.19 使用人不默认为领单人
        query.setUseBy(null);
        numMapper.grant(query, record.getId());
        //12.19 发放完之后 去查找是否有单号被使用过 如果有则设置为已使用
        Long ticketId = query.getId();
        Ticket ticketById = ticketMapper.selectById(ticketId);
        List<TicketNum> ticketNumById = numMapper.selectList(new QueryWrapper<TicketNum>().eq("ticket_id", ticketId));
        if(ticketById.getBillPrefix()!=null && ticketById.getBillType().equals("AWBA")){
                ticketNumById.forEach(e->{
                    AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                            .eq("waybill_code", "AWBA" + ticketById.getBillPrefix() + e.getNum() + e.getCode())
                            .last("limit 1"));
                    if(airWaybill!=null){
                        e.setStatus("USED");
                        e.setUserName(airWaybill.getWriter());
                        SysUser sysUser = userMapper.selectUserById(e.getUseBy());
                        if(sysUser != null){
                            e.setUseBy(sysUser.getUserId());
                        }
                        e.setUseTime(airWaybill.getWriteTime().toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDateTime());
                        numMapper.updateById(e);
                    }
                });
        }else if(ticketById.getBillType().equals("AWBM")){
            ticketNumById.forEach(e->{
                AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                        .eq("waybill_code", "AWBMDN" + e.getNum())
                        .last("limit 1"));
                if(airWaybill!=null){
                    e.setStatus("USED");
                    e.setUserName(airWaybill.getWriter());
                    SysUser sysUser = userMapper.selectUserById(e.getUseBy());
                    if(sysUser != null){
                        e.setUseBy(sysUser.getUserId());
                    }
                    e.setUseTime(airWaybill.getWriteTime().toInstant()
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime());
                    numMapper.updateById(e);
                }
            });
        }else if(ticketById.getBillType().equals("HAWB")){
            ticketNumById.forEach(e->{
                Hawb hawb =  hawbMapper.selectOne(new QueryWrapper<Hawb>()
                        .eq("waybill_code", "HAWB" + e.getNum())
                        .last("limit 1"));
                if(hawb!=null){
                    e.setStatus("USED");
                    e.setUserName(hawb.getWriter());
                    SysUser sysUser = userMapper.selectUserById(e.getUseBy());
                    if(sysUser != null){
                        e.setUseBy(sysUser.getUserId());
                    }
                    e.setUseTime(hawb.getWriteTime().toInstant()
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime());
                    numMapper.updateById(e);
                }
            });
        }
        return 1;
    }

    /**
     * 取消发放
     *
     * @param recordId 发放日志id
     * @return 结果
     */
    @Override
    public int cancelGrant(Long recordId) {
        TicketGrant ticketGrant = recordMapper.selectById(recordId);
        Ticket ticket = ticketMapper.selectById(ticketGrant.getTicketId());
//        Integer count = numMapper.selectCount(new QueryWrapper<TicketNum>()
//                .eq("ticket_id", ticket.getId())
//                .in("status", "GRANT","USED","CANCEL","INVALID"));
//        if (count == 0){
//            ticket.setStatus(0);
//            ticketMapper.updateById(ticket);
//        }
        numMapper.updateStatus(recordId);
        //直接删除
//      return recordMapper.updateByRecordId(recordId);
        recordMapper.deleteById(recordId);
        Integer usedTotal = recordMapper.selectTotal(ticket.getId());
        Integer total = ticket.getEndNumber() - ticket.getStartNumber() + 1;
        if(StringUtils.isNull(usedTotal)){
            ticket.setStatus(0);
        }else if(total.equals(usedTotal)){
            ticket.setStatus(3);
        }else{
            ticket.setStatus(2);
        }
        return  ticketMapper.updateById(ticket);
    }

    /**
     * 取消入库
     *
     * @param query 取消入库参数
     * @return 结果
     */
    @Override
    public int warehouse(TicketNumQuery query) {
        List<Integer> list = numMapper.selectByTicketId(query.getId(), "GRANT");
        if (!CollectionUtils.isEmpty(list)){
            throw new ServiceException("已发放，不能取消入库");
        }
        return ticketMapper.warehouse(query);
    }

    /**
     * 详情-使用明细
     *
     * @param recordId 发放记录id
     * @return 票号使用明细列表
     */
    @Override
    public List<TicketNum> usage(Long recordId) {
        List<TicketNum> list = numMapper.selectListById(recordId);
        for(TicketNum e : list){
            //如果是邮件单则不用拼接
            if(e.getNum().toString().length() == 8) {
                continue;
            }
            String num = String.valueOf(e.getNum());
            String s = num + e.getCode();
            e.setNum(Integer.valueOf(s));
        }
        return list;
    }

    /**
     * 删除单证
     *
     * @param id 单证id
     * @return 结果
     */
    @Override
    public int removeByTicketId(Long id) {
        Ticket ticket = ticketMapper.selectById(id);
        if (ticket.getStatus() != 0){
            throw new ServiceException("只有入库状态的票证可删除");
        }
        List<TicketNum> nums = numMapper.selectList(new QueryWrapper<TicketNum>().eq("ticket_id", id));
        if (!CollectionUtils.isEmpty(nums)){
            List<TicketNum> collect = nums.stream().filter(e -> !"NOTGRANT".equals(e.getStatus())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect)){
                throw new CustomException("删除失败，含有已使用状态运单");
            }
        }
        return ticketMapper.removeByTicketId(id);
    }

    /**
     * 修改单证
     *
     * @param ticket 单证信息
     * @return 结果
     */
    @Override
    public int edit(Ticket ticket) {
        numMapper.delete(new QueryWrapper<TicketNum>().eq("ticket_id",ticket.getId()));
        for (int i = ticket.getStartNumberNew(); i <= ticket.getEndNumberNew(); i++) {
            TicketNum ticketNum = new TicketNum();
            ticketNum.setTicketId(ticket.getId());
            ticketNum.setNum(i);
            ticketNum.setCode(i % 7);
            ticketNum.setStatus("NOTGRANT");
            numMapper.insert(ticketNum);
        }
        if(ticket.getBillPrefix().equals("DN")){
            ticket.setStartNumber(ticket.getStartNumber());
            ticket.setEndNumber(ticket.getEndNumber());
            ticket.setStartCode(null);
            ticket.setEndCode(null);
        }else {
            ticket.setStartNumber(ticket.getStartNumberNew());
            ticket.setEndNumber(ticket.getEndNumberNew());
        }
        return ticketMapper.updateById(ticket);
    }

    /**
     * 重新发放
     * @param recordId 发放记录id
     * @return 结果
     */
    @Override
    public int reissue(Long recordId) {
        numMapper.reissue(recordId);
        return recordMapper.reissue(recordId);
    }

    @Override
    public List<TicketSourceVo> getTicketSource(String type) {
        if (!"AWBA".equals(type)){
            return null;
        }
        return carrierMapper.getTicketSource(type);
    }

    /**
     * 导出单证详情
     * */
    @Override
    public List<TicketNum> selectDetailList(TicketQuery query) {
        QueryWrapper<TicketNum> ticketNumQueryWrapper = new QueryWrapper<>();
        ticketNumQueryWrapper.eq("ticket_id",query.getTicketId());
        if(StringUtils.isNotEmpty(query.getStatusDetail())){
            ticketNumQueryWrapper.eq("status",query.getStatusDetail());
        }
        List<TicketNum> ticketNums = numMapper.selectList(ticketNumQueryWrapper);
        ticketNums.forEach(e -> {
            switch (e.getStatus()){
                case "NOTGRANT":
                    e.setState("未发放");
                    break;
                case "GRANT":
                    e.setState("发放");
                    break;
                case "USED":
                    e.setState("已使用");
                    break;
                case "CANCEL":
                    e.setState("销号");
                    break;
                default:
                    e.setState("无");
            }
            SysUser sysUser = userMapper.selectUserById(e.getUseBy());
            if(sysUser != null){
                e.setUserName(sysUser.getUserName());
            }
        });
        return ticketNums;
    }

    @Override
    public List<TicketSourceVo> getSource(String type) {
        return carrierMapper.getTicketSource(type);
    }


    /**
     * 合并票号
     *
     * @param data 取消入库参数
     * @return 结果
     */
    public static List<List<Integer>> groupBy(List<Integer> data) {
        if (data == null) {
            return new ArrayList<>();
        }
        Collections.sort(data);
        List<List<Integer>> result = new ArrayList<>();
        List<Integer> group = null;
        for (Integer value : data) {
            if (group == null || group.get(group.size() - 1) + 1 != value) {
                group = new ArrayList<>();
                result.add(group);
            }
            group.add(value);
        }
        return result;
    }

    private static final Map<Integer,String> STATUS = new HashMap<>();
    static {
        STATUS.put(0,"新增");
        STATUS.put(1,"入库");
        STATUS.put(2,"发放");
        STATUS.put(3,"耗尽");
        STATUS.put(4,"取消发放");
    }
}
