package com.gzairports.common.system.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.system.domain.ATest;

/**
 * 代码生成测试Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-09-18
 */
public interface ATestMapper extends BaseMapper<ATest>
{
    /**
     * 查询代码生成测试
     * 
     * @param id 代码生成测试ID
     * @return 代码生成测试
     */
    ATest selectATestById(Long id);

    /**
     * 查询代码生成测试列表
     * 
     * @param aTest 代码生成测试
     * @return 代码生成测试集合
     */
    List<ATest> selectATestList(ATest aTest);

    /**
     * 新增代码生成测试
     * 
     * @param aTest 代码生成测试
     * @return 结果
     */
    int insertATest(ATest aTest);

    /**
     * 修改代码生成测试
     * 
     * @param aTest 代码生成测试
     * @return 结果
     */
    int updateATest(ATest aTest);

    /**
     * 删除代码生成测试
     * 
     * @param id 代码生成测试ID
     * @return 结果
     */
    int deleteATestById(Long id);

    /**
     * 批量删除代码生成测试
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteATestByIds(Long[] ids);
}
