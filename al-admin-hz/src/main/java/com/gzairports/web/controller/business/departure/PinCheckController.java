package com.gzairports.web.controller.business.departure;

import com.gzairports.hz.business.departure.domain.PinCheck;
import com.gzairports.hz.business.departure.domain.query.PinCheckQuery;
import com.gzairports.hz.business.departure.domain.vo.PinCheckVo;
import com.gzairports.hz.business.departure.domain.vo.WaybillIdAndCodeVo;
import com.gzairports.hz.business.departure.service.IPinCheckService;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.hibernate.validator.constraints.Length;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 开箱抽查Controller
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@RestController
@RequestMapping("/dep/pinCheck")
@Api(tags = "开箱抽查")
public class PinCheckController extends BaseController {

    @Autowired
    private IPinCheckService checkService;

    /**
     * 查询开箱抽查列表
     */
//    @PreAuthorize("@ss.hasPermi('dep:pinCheck:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询开箱抽查列表")
    public TableDataInfo list(PinCheckQuery query)
    {
        startPage();
        List<PinCheckVo> list = checkService.selectPinCheckList(query);
        return getDataTable(list);
    }

    /**
     * 导出开箱抽查列表
     */
    @PreAuthorize("@ss.hasPermi('dep:pinCheck:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出开箱抽查列表")
    public void export(HttpServletResponse response, PinCheckQuery query)
    {
        List<PinCheckVo> list = checkService.selectPinCheckList(query);
        ExcelUtil<PinCheckVo> util = new ExcelUtil<PinCheckVo>(PinCheckVo.class);
        util.exportExcel(response, list, "开箱抽查");
    }

    /**
     * 查询详情
     */
    @PreAuthorize("@ss.hasPermi('dep:pinCheck:getInfo')")
    @GetMapping("/getInfo/{id}")
    @ApiOperation(value = "查询详情")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(checkService.getInfo(id));
    }

    /**
     * 修改开箱抽查
     */
    @PreAuthorize("@ss.hasPermi('dep:pinCheck:edit')")
    @PostMapping("/edit")
    @ApiOperation(value = "修改开箱抽查")
    public AjaxResult edit(@RequestBody PinCheckVo vo)
    {
        return toAjax(checkService.edit(vo));
    }

    /**
     * H5端查询开箱抽查列表->app
     */
    @PreAuthorize("@ss.hasPermi('dep:pinCheck:h5List')")
    @GetMapping("/h5List")
    @ApiOperation(value = "H5端查询开箱抽查列表")
    public AjaxResult h5List(PinCheckQuery query)
    {
        return AjaxResult.success(checkService.selectH5List(query));
    }

    /**
     * H5端新增开箱抽查->app
     */
    @PreAuthorize("@ss.hasPermi('dep:pinCheck:h5Add')")
    @PostMapping("/h5Add")
    @ApiOperation(value = "H5端新增开箱抽查")
    public AjaxResult h5Add(@RequestBody PinCheck pinCheck)
    {
        return toAjax(checkService.addH5PinCheck(pinCheck));
    }

    /**
     * H5抽检根据后四位运单号查询运单号下拉列表->app
     * 搜索范围：所有运单号
     */
    @GetMapping("/getFourCode")
    @ApiOperation("H5抽检根据后四位运单号查询运单号下拉列表")
    @PreAuthorize("@ss.hasPermi('dep:pinchCheck:getFourCode')")
    public AjaxResult getFourCode(@Length(max = 8,min = 4) String fourCode){
        List<WaybillIdAndCodeVo> list = checkService.getFourCode(fourCode);
        return AjaxResult.success(list);
    }

    /**
     * 根据运单号查询运单信息
     */
    @GetMapping("/waybillInfo/{waybillId}")
    @ApiOperation("根据运单号查询运单信息")
    @PreAuthorize("@ss.hasPermi('dep:pinchCheck:waybillInfo')")
    public AjaxResult waybillInfo(@PathVariable("waybillId") @NotNull Long waybillId){
        PinCheckVo vo = checkService.waybillInfo(waybillId);
        return AjaxResult.success(vo);
    }

    /**
     * 根据运单号查询运单信息
     */
    @GetMapping("/waybillGetInfo/{waybillCode}")
    @ApiOperation("根据运单号查询运单信息")
    @PreAuthorize("@ss.hasPermi('dep:pinchCheck:waybillInfo')")
    public AjaxResult waybillGetInfo(@PathVariable("waybillCode") @NotNull String waybillCode){
        PinCheckVo vo = checkService.waybillGetInfo(waybillCode);
        return AjaxResult.success(vo);
    }


    @PostMapping("/getStagingList")
    @ApiOperation("暂存列表查询")
    @PreAuthorize("@ss.hasPermi('business:h5:inspection')")
    public AjaxResult getStagingList(@RequestBody PinCheckQuery query){
        List<PinCheckVo> list = checkService.selectStagingList(query);
        return AjaxResult.success(list);
    }

    @PostMapping("/addStaging")
    @ApiOperation("暂存提交")
    @PreAuthorize("@ss.hasPermi('dep:pinchCheck:addStaging')")
    public AjaxResult addStaging(@RequestBody @Valid PinCheck check){
        boolean b = checkService.addStaging(check);
        if (b) {
            return AjaxResult.success();
        }else {
            return AjaxResult.error("新增失败");
        }
    }

    @GetMapping("/stagingDetail/{checkId}")
    @ApiOperation("H5端，根据抽检记录id，获取暂存记录详情")
    @PreAuthorize("@ss.hasPermi('business:h5:inspection')")
    public AjaxResult stagingDetail(@PathVariable("checkId") @NotNull Long checkId){
        PinCheck check = checkService.stagingDetail(checkId);
        return AjaxResult.success(check);
    }

    @GetMapping("/delStaging/{checkId}")
    @ApiOperation("暂存删除")
    @PreAuthorize("@ss.hasPermi('business:h5:inspection')")
    public AjaxResult delStaging(@PathVariable("checkId") Long checkId){
        boolean b = checkService.delStaging(checkId);
        if (b) {
            return AjaxResult.success();
        }else {
            return AjaxResult.error("删除失败");
        }
    }

    @PostMapping("/editStaging")
    @ApiOperation("暂存修改")
    @PreAuthorize("@ss.hasPermi('business:h5:inspection')")
    @Log(title = "暂存提交", businessType = BusinessType.UPDATE)
    public AjaxResult editStaging(@RequestBody @Valid PinCheck check){
        boolean b = checkService.editStaging(check);
        if (b) {
            return AjaxResult.success();
        }else {
            return AjaxResult.error("修改失败");
        }
    }

    /**
     * 根据八位运单号查询运单
     */
    @PreAuthorize("@ss.hasPermi('business:h5:inspection')")
    @GetMapping("/getEightCode/{code}")
    @ApiOperation(value = "根据八位运单号查询运单")
    public AjaxResult getEightCode(@PathVariable String code)
    {
        return AjaxResult.success(checkService.getEightCode(code));
    }


}
