<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.departure.mapper.PinCheckMapper">
    <select id="selectPinCheckList" resultType="com.gzairports.hz.business.departure.domain.vo.PinCheckVo">
        select dpc.id, dpc.waybill_id, aaw.waybill_code, aaw.agent_company as agent, aaw.des_port, aaw.cargo_name, su.nick_name as userName, aaw.quantity, dpc.opened_pieces,
               dpc.declaration_consistent, dpc.inspect_time
        from hz_dep_pin_check dpc
            left join all_air_waybill aaw on dpc.waybill_id = aaw.id
            left join sys_user su on su.user_id = dpc.user_id
        <where>
            dpc.is_del = 0 and dpc.check_status != 1 and dpc.waybill_id != 0
            <if test="waybillCode != null  and waybillCode != ''"> and aaw.waybill_code like '%${waybillCode}%'</if>
            <if test="declarationConsistent != null"> and dpc.declaration_consistent = #{declarationConsistent}</if>
            <if test="agent != null  and agent != ''"> and aaw.shipper = #{agent}</if>
            <if test="userName != null  and userName != ''"> and su.nick_name = #{userName}</if>
            <if test="startTime != null">
                and dpc.inspect_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and dpc.inspect_time <![CDATA[<=]]> #{endTime}
            </if>
        </where>
    </select>
    <select id="getInfo" resultType="com.gzairports.hz.business.departure.domain.vo.PinCheckVo">
        select dpc.id, dpc.waybill_id, aaw.waybill_code, aaw.shipper as agent, aaw.des_port, aaw.cargo_name, su.user_name, aaw.quantity, aaw.weight,
               dpc.opened_pieces, dpc.declaration_consistent, dpc.inspect_time, dpc.remark, dpc.inspect_images, dpc.documents_complete,
               dpc.packaging_compliant from hz_dep_pin_check dpc
             left join all_air_waybill aaw on dpc.waybill_id = aaw.id
             left join sys_user su on su.user_id = dpc.user_id where dpc.id = #{id}
    </select>
    <select id="selectH5List" resultType="com.gzairports.hz.business.departure.domain.vo.PinCheckVo">
        select dpc.id, aaw.waybill_code, aaw.agent_company as agent, aaw.cargo_name, aaw.quantity, aaw.weight, dpc.inspect_time, dpc.declaration_consistent
        from hz_dep_pin_check dpc
                 left join all_air_waybill aaw on dpc.waybill_id = aaw.id
        <where>
            dpc.user_id = #{userId} and check_status != 1 and waybill_id != 0
            <if test="query.waybillCode != null and query.waybillCode != ''">
                <if test="query.waybillCode.length() == 4">
                    AND aaw.waybill_code LIKE CONCAT('%',#{query.waybillCode})
                </if>
                <if test="query.waybillCode.length() != 4">
                    AND aaw.waybill_code = #{query.waybillCode}
                </if>
            </if>
            <if test="query.startTime != null">
                and dpc.inspect_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and dpc.inspect_time <![CDATA[<=]]> #{query.endTime}
            </if>
        </where>
    </select>
    <select id="selectStagingList" resultType="com.gzairports.hz.business.departure.domain.vo.PinCheckVo">
        select id, opened_pieces, declaration_consistent, inspect_time, check_status, packaging_compliant
        from hz_dep_pin_check
        <where>
            user_id = #{userId} and waybill_id = 0 and check_status = 1
            <if test="query.startTime != null">
                and inspect_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and inspect_time <![CDATA[<=]]> #{query.endTime}
            </if>
        </where>
    </select>
</mapper>