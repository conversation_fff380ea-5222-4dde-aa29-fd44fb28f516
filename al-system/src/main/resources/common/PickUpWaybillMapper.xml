<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.arrival.mapper.PickUpWaybillMapper">

    <select id="selectOutOrderById"
            resultType="com.gzairports.common.business.arrival.domain.vo.OutOrderWaybillVo">
        select apuw.tally_id,
               apuw.waybill_code,
               aaw.cargo_name,
               aaw.remark,
               apuw.can_pick_up_quantity as quantity,
               apuw.can_pick_up_weight   as charge_weight,
               apuw.can_pick_up_quantity,
               aaw.source_port,
               aipu.out_time,
               aaw.quantity as waybillQuantity,
               aaw.weight as waybillWeight,
               CONCAT(air_ways, flight_no,'/',DATE_FORMAT(aaw.flight_date1, '%m%d')) AS flight_no
        from all_pick_up_waybill apuw
                 left join all_in_pick_up aipu on aipu.id = apuw.pick_up_id
                 left join all_air_waybill aaw on aaw.waybill_code = apuw.waybill_code
                 left join hz_arr_tally hat on apuw.tally_id = hat.id
                 left join hz_flight_load hfl on hfl.id = hat.leg_id
                 left join all_flight_info afi on hfl.flight_id = afi.flight_id
        where apuw.pick_up_id = #{pickUpId}
          and aaw.type = 'ARR'
    </select>
    <select id="getInfo" resultType="com.gzairports.common.business.arrival.domain.vo.PickUpOutWaybillVo">
        select aw.id, aw.pick_up_id, aw.tally_id, aw.waybill_code, aw.can_pick_up_quantity as orderQuantity, aw.can_pick_up_weight as orderWeight,
               au.customer_name, au.customer_id_type, au.customer_id_no, au.pay_method, au.handle_by, aw.can_pick_up_quantity as pieces,
               aw.can_pick_up_weight as weight, au.remark, au.is_pay, aaw.cargo_name, au.pick_up_code
        from all_pick_up_waybill aw
            left join all_in_pick_up au on au.id = aw.pick_up_id
            left join all_air_waybill aaw on aaw.waybill_code = aw.waybill_code where aaw.type = 'ARR' and aw.id = #{id}
    </select>
    <select id="selectTime" resultType="java.util.Date">
        select au.pick_up_time from all_pick_up_waybill aw left join all_in_pick_up au on aw.pick_up_id = au.id
        where aw.tally_id = #{tallyId} and aw.waybill_code = #{waybillCode} and aw.is_cancel = 0
    </select>
    <select id="planPickUpOut" resultType="com.gzairports.common.business.arrival.domain.vo.PickUpVo">
        select ao.waybill_code, aaw.cargo_name, aaw.special_cargo_code1, aaw.quantity, aaw.weight, ao.can_pick_up_quantity,
        ao.can_pick_up_weight, aaw.shipper, aaw.consign_id_car, aaw.consign, haro.order_time, hat.tally_time, au.serial_no, au.pick_up_code
        from all_pick_up_waybill ao
        left join all_in_pick_up au on au.id = ao.pick_up_id
        left join all_air_waybill aaw on aaw.waybill_code = ao.waybill_code
        left join hz_arr_tally hat on hat.id = ao.tally_id
        left join hz_arr_record_order haro on haro.id = hat.record_order_id
        left join all_pick_up_out apuo on au.id = apuo.pick_up_id
        <where>
            aaw.is_del = 0 and aaw.type = 'ARR' and apuo.out_time is null and au.is_pay = 1
            and ao.is_cancel = 0
            <if test="waybillCode != null and waybillCode != ''">
                and ao.waybill_code like '%${waybillCode}%'
            </if>
            <if test="startTime != null">
                and haro.order_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and haro.order_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="shipper != null and shipper != ''">
                and aaw.shipper like '%${shipper}%'
            </if>
            <if test="consignIdCar != null and consignIdCar != ''">
                and aaw.consign_id_car like '%${consignIdCar}%'
            </if>
            <if test="consign != null and consign != ''">
                and aaw.consign like '%${consign}%'
            </if>
            <if test="serialNo != null and serialNo != ''">
                and au.serial_no = #{serialNo}
            </if>
            <if test="pickUpCode != null and pickUpCode != ''">
                and au.pick_up_code = #{pickUpCode}
            </if>
        </where>
    </select>
    <select id="selectTallyIds" resultType="java.lang.Long">
        select tally_id
        from all_pick_up_waybill aw
            left join all_in_pick_up au on au.id = aw.pick_up_id
        where au.is_pay = 1 and au.pick_up_code is not null
        and aw.tally_id in
        <foreach collection="ids" item="tallyId" index="index" open="(" close=")" separator=",">
            #{tallyId}
        </foreach>
    </select>
    <select id="selectTallyId" resultType="com.gzairports.common.business.arrival.domain.PickUp">
        select au.id, au.serial_no, au.is_pay, au.pick_up_code
        from all_pick_up_waybill aw
            left join all_in_pick_up au on au.id = aw.pick_up_id
        where aw.tally_id = #{tallyId}
    </select>
    <select id="selectH5Info" resultType="com.gzairports.common.business.arrival.domain.vo.H5OutInfoVo">
        select aw.waybill_code, aaw.cargo_name, aw.can_pick_up_weight, aw.can_pick_up_quantity
        from all_pick_up_waybill aw left join all_air_waybill aaw on aaw.waybill_code = aw.waybill_code
        where aw.pick_up_id = #{pickUpId}
    </select>
    <select id="appPickUpList" resultType="com.gzairports.common.business.arrival.domain.vo.AppPickUpVo">
        select tally_id, pick_up_id, exec_date, flight_no, waybill_code, serial_no, pick_up_code,
        COALESCE(outTime1,null) as out_time, agentAbb
        from
        ((select ao.tally_id, ao.pick_up_id, afi.exec_date, CONCAT(afi.air_ways,afi.flight_no) AS flight_no,
        ao.waybill_code, au.serial_no, au.pick_up_code, ao.out_time as outTime1,
        aaw.agent_code as agentAbb
        from all_pick_up_out ao
        left join all_in_pick_up au on au.id = ao.pick_up_id
        left join hz_arr_tally hat on hat.id = ao.tally_id
        left join hz_flight_load hfl on hfl.id = hat.leg_id
        left join all_flight_info afi on hfl.flight_id = afi.flight_id
        left join all_air_waybill aaw on aaw.waybill_code = ao.waybill_code
        <where>
            ao.is_cancel = 0
            <if test="execDate != null">
                and afi.exec_date = #{execDate}
            </if>
            <if test="code != null and code != ''">
                and (ao.waybill_code like '%${code}%'
                or CONCAT(afi.air_ways,afi.flight_no) like '%${code}%'
                or au.serial_no like '%${code}%')
            </if>
        </where>
        )
        union all
        (select ao.tally_id,ao.pick_up_id,afi.exec_date, CONCAT(afi.air_ways,afi.flight_no) AS flight_no,
        ao.waybill_code,au.serial_no,au.pick_up_code, null,
        aaw.agent_code as agentAbb
        from all_in_pick_up au
        left join all_pick_up_waybill ao on ao.pick_up_id = au.id
        left join hz_arr_tally hat on hat.id = ao.tally_id
        left join hz_flight_load hfl on hfl.id = hat.leg_id
        left join all_flight_info afi on hfl.flight_id = afi.flight_id
        left join all_air_waybill aaw on aaw.waybill_code = ao.waybill_code
        <where>
            ao.is_cancel = 0
            <if test="execDate != null">
                and afi.exec_date = #{execDate}
            </if>
            <if test="code != null and code != ''">
                and (ao.waybill_code like '%${code}%'
                or CONCAT(afi.air_ways,afi.flight_no) like '%${code}%'
                or au.serial_no like '%${code}%')
            </if>
        </where>
        ))
        as table1
    </select>
    <select id="selectListByPickUpId"
            resultType="com.gzairports.common.business.arrival.domain.vo.ScanWaybillVo">
        select aw.tally_id, aw.waybill_code, aw.can_pick_up_weight, aw.can_pick_up_quantity, aaw.consign, aaw.shipper
        from all_pick_up_waybill aw
            left join all_air_waybill aaw on aaw.waybill_code = aw.waybill_code  where aw.pick_up_id = #{pickUpId}
    </select>
    <select id="selectPickUpInfo" resultType="com.gzairports.common.business.arrival.domain.vo.PickUpInfoVo">
        select aw.pick_up_id, aw.can_pick_up_weight, aw.can_pick_up_quantity, aw.waybill_code, aaw.quantity, aaw.weight, aw.can_pick_up_weight as orderWeight,
               aw.can_pick_up_quantity as orderQuantity, aaw.cargo_name, au.is_pay, aaw.special_cargo_code1, aaw.shipper, aaw.consign,
               au.pick_up_code
        from all_pick_up_waybill aw
            left join all_in_pick_up au on au.id = aw.pick_up_id
            left join all_air_waybill aaw on aaw.waybill_code = aw.waybill_code
        where aw.waybill_code = #{waybillCode} and aw.tally_id = #{tallyId} and aw.is_cancel = 0
    </select>
    <select id="rapidDeliver" resultType="com.gzairports.common.business.arrival.domain.vo.AppPickUpVo">
        (select aw.tally_id, aw.pick_up_id, afi.exec_date, CONCAT(afi.air_ways,afi.flight_no) AS flight_no,
               aw.waybill_code, au.serial_no, au.pick_up_code, aaw.shipper as agentAbb
        from all_pick_up_waybill aw
            left join all_in_pick_up au on au.id = aw.pick_up_id
            left join hz_arr_tally hat on hat.id = aw.tally_id
            left join hz_flight_load hfl on hfl.id = hat.leg_id
            left join all_flight_info afi on hfl.flight_id = afi.flight_id
            left join all_air_waybill aaw on aaw.waybill_code = aw.waybill_code
        where au.is_pay = 1 and au.pick_up_code is not null and aaw.type = "ARR"
        <if test="query.execDate != null">
            and afi.exec_date = #{query.execDate}
        </if>
        <if test="query.code != null and query.code != ''">
            and (aw.waybill_code like '%${query.code}%' or CONCAT(afi.air_ways,afi.flight_no) like '%${query.code}%')
        </if>
        <if test="list != null and list.size() > 0">
            and aaw.dept_id in
            <foreach collection="list" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        )
        union
        (
        select null, null, afi.exec_date, CONCAT(afi.air_ways,afi.flight_no) AS flight_no,
        aaw.waybill_code, null, null, aaw.shipper as agentAbb
        from all_air_waybill aaw
        left join hz_arr_record_order haro on haro.waybill_code = aaw.waybill_code
        left join hz_flight_load hfl on hfl.id = haro.leg_id
        left join all_flight_info afi on hfl.flight_id = afi.flight_id
        where aaw.type = "ARR"
            <if test="query.execDate != null">
                and afi.exec_date = #{query.execDate}
            </if>
            <if test="query.code != null and query.code != ''">
                and (aaw.waybill_code like '%${query.code}%' or CONCAT(afi.air_ways,afi.flight_no) like '%${query.code}%')
            </if>
            <if test="list != null and list.size() > 0">
                and aaw.dept_id in
                <foreach collection="list" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        )
    </select>
    <select id="selectWaybill" resultType="com.gzairports.common.business.arrival.domain.PickUpWaybill">
        select can_pick_up_weight, can_pick_up_quantity from all_pick_up_waybill where pick_up_id = #{id}
    </select>

    <select id="waybillCodeQuery" resultType="com.gzairports.common.business.arrival.domain.vo.PickUpInfoVo">
        select pick_up_id,waybill_code,quantity,weight,cargo_name,is_pay,special_cargo_code1,shipper,pick_up_code,canPickUpQuantity,canPickUpWeight,tally_id,orderQuantity,orderWeight,timeNew,deliverStatus
        from ((select ao.pick_up_id,ao.waybill_code,aaw.quantity,aaw.weight,
                      aaw.cargo_name,au.is_pay,aaw.special_cargo_code1,aaw.shipper,
                      au.pick_up_code,ao.pieces as canPickUpQuantity,ao.weight as canPickUpWeight,ao.tally_id,
                      ho.cabin_pieces as orderQuantity,ho.cabin_weight as orderWeight,
                      ao.out_time as timeNew, 1 as deliverStatus
               from all_pick_up_out ao
                        left join all_in_pick_up au on au.id = ao.pick_up_id
                        left join all_air_waybill aaw on aaw.waybill_code = ao.waybill_code
                        left join hz_arr_record_order ho on ho.waybill_code = ao.waybill_code
               where ao.waybill_code = #{waybillCode}
                 and aaw.type = "ARR")
              union all
              (select ao.pick_up_id,ao.waybill_code,aaw.quantity,aaw.weight,
                      aaw.cargo_name,au.is_pay,aaw.special_cargo_code1,aaw.shipper,
                      au.pick_up_code,ao.can_pick_up_weight as canPickUpWeight,ao.can_pick_up_quantity as canPickUpQuantity,ao.tally_id,
                      ho.cabin_pieces as orderQuantity,ho.cabin_weight as orderWeight,
                      ao.create_time as timeNew, 0 as deliverStatus
               from all_pick_up_waybill ao
                        left join all_in_pick_up au on au.id = ao.pick_up_id
                        left join all_air_waybill aaw on aaw.waybill_code = ao.waybill_code
                        left join hz_arr_record_order ho on ho.waybill_code = ao.waybill_code
               where ao.waybill_code = #{waybillCode} and aaw.type = "ARR")
              union all
              (select null,ho.waybill_code,aaw.quantity,aaw.weight,aaw.cargo_name,null,aaw.special_cargo_code1,aaw.shipper,null,aaw.quantity as canPickUpWeight,aaw.weight as canPickUpQuantity,null,ho.cabin_pieces as orderQuantity,ho.cabin_weight as orderWeight,aaw.write_time as timeNew, 0 as deliverStatus
                from hz_arr_record_order ho
                left join all_air_waybill aaw on ho.waybill_code = aaw.waybill_code
                where ho.waybill_code = #{waybillCode} and aaw.type = "ARR")
              )
                 as table1
                where canPickUpQuantity IS NOT NULL and canPickUpWeight IS NOT NULL
                order by timeNew desc
                limit 1
    </select>
    <select id="getWaybillCodeByFour" resultType="java.lang.String">
        select waybill_code from all_air_waybill
        where waybill_code like concat('%',#{code})
        and type = #{type}
    </select>
    <select id="selectPickUpInfoForWaybillCode"
            resultType="com.gzairports.common.business.arrival.domain.vo.PickUpInfoVo">
        select aaw.waybill_code, aaw.quantity, aaw.weight, aaw.cargo_name,aaw.special_cargo_code1, aaw.shipper, aaw.consign
        from all_air_waybill aaw
        where aaw.waybill_code = #{waybillCode}
    </select>
    <select id="selectWaybillCode" resultType="java.lang.String">
        select waybill_code from all_pick_up_waybill where pick_up_id = #{id}
    </select>
    <select id="selectPayTime" resultType="java.util.Date">
        select au.pay_time from all_pick_up_waybill aw left join all_in_pick_up au on aw.pick_up_id = au.id
        where aw.tally_id = #{tallyId} and aw.waybill_code = #{waybillCode} and aw.is_cancel = 0
    </select>
    <select id="selectReportUpTime" resultType="java.lang.String">
        select au.pick_up_time from all_pick_up_waybill aw left join all_in_pick_up au on aw.pick_up_id = au.id
        where aw.tally_id = #{tallyId} and aw.waybill_code = #{waybillCode} and aw.is_cancel = 0 order by au.pick_up_time desc limit 1
    </select>
    <select id="selectReportPayTime" resultType="java.lang.String">
        select au.pay_time from all_pick_up_waybill aw left join all_in_pick_up au on aw.pick_up_id = au.id
        where aw.tally_id = #{tallyId} and aw.waybill_code = #{waybillCode} and aw.is_cancel = 0 order by au.pay_time desc limit 1
    </select>
    <select id="selectFlightNo" resultType="java.lang.String">
        select distinct aaw.flight_no1 from all_pick_up_waybill apuw
            left join all_air_waybill aaw on apuw.waybill_code = aaw.waybill_code where apuw.pick_up_id = #{id}
    </select>
    <select id="selectListByWaybillCode" resultType="com.gzairports.hz.business.departure.domain.vo.ChargeBillArrSettleExportVO">
        select aipu.serial_no, aaw.waybill_code,aipu.settle_user, aaw.consign, aipu.total_cost,apuw.can_pick_up_quantity as quantity,
               apuw.can_pick_up_weight as weight, aaw.cargo_name,aaw.charge_weight, aipu.pick_up_time as settleTime, aipu.id as pickUpId
        from all_pick_up_waybill apuw
                 left join all_in_pick_up aipu on apuw.pick_up_id = aipu.id
                 left join all_air_waybill aaw on aaw.waybill_code = apuw.waybill_code
        where aaw.type = 'ARR' and (aipu.pay_method = 2 or aipu.pay_method IS NULL)
        <if test="arrWaybillCodeList != null and arrWaybillCodeList.size() > 0">
            and aaw.waybill_code in
            <foreach collection="arrWaybillCodeList" item="code" index="index" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
    </select>
    <select id="selectBatch" resultType="com.gzairports.common.business.arrival.domain.PickUpWaybill">
        select au.pick_up_time as createTime, aw.waybill_code, aw.tally_id
        from all_pick_up_waybill aw
            left join all_in_pick_up au on aw.pick_up_id = au.id
        <where>
            aw.is_cancel = 0 and (tally_id, waybill_code) in
        <foreach collection="tallyKeys" item="key" open="(" separator="," close=")">
            (#{key.tallyId}, #{key.waybillCode})
        </foreach>
    </where>
    </select>
    <select id="selectPickWaybillList"
            resultType="com.gzairports.common.business.arrival.domain.PickUpWaybill">
        select id, pick_up_id, waybill_code, can_pick_up_weight, can_pick_up_quantity, cost_sum,
               create_time, create_by, update_time, update_by, tally_id, is_cancel
        from all_pick_up_waybill
        where waybill_code in
        <foreach item="code" index="index" collection="waybillCodeList" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>

    <update id="updatePickUpWaybillById">
        update all_pick_up_waybill
        <trim prefix="SET" suffixOverrides=",">
            <if test="canPickUpQuantity != null">can_pick_up_quantity = #{canPickUpQuantity},</if>
            <if test="canPickUpWeight != null">can_pick_up_weight = #{canPickUpWeight},</if>
            <if test="costSum != null">cost_sum = #{costSum},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="isCancel != null">is_cancel = #{isCancel},</if>
        </trim>
        where id = #{id}
    </update>

</mapper>