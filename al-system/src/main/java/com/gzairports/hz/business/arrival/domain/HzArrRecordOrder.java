package com.gzairports.hz.business.arrival.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 运单录单数据表
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
@Data
@TableName(value = "hz_arr_record_order")
public class HzArrRecordOrder {

    /** 主键id */
    private Long id;

    /** 进港运单号 */
    private String waybillCode;

    /** 舱单件数 */
    private Integer cabinPieces;

    /** 舱单重量 */
    private BigDecimal cabinWeight;

    /** 录单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;

    /** 状态 ld 录单 cd 生成舱单 */
    private String status;

    /** 航段id */
    private Long legId;

    /** 是否转南航 */
    private String isSouth;

    /** 是否审核 0 否 1 是 */
    private Integer isExamine;
}
