package com.gzairports.web.controller.basedata;

import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.basedata.domain.BasePost;
import com.gzairports.common.basedata.domain.query.BasePostQuery;
import com.gzairports.common.basedata.service.IPostService;
import com.gzairports.common.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 邮局维护Controller
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@RestController
@RequestMapping("/base/post")
@Api(tags = "邮局维护")
public class PostController extends BaseController {

    @Autowired
    private IPostService postService;

    /**
     * 查询邮局维护列表
     */
//    @PreAuthorize("@ss.hasPermi('base:post:BasePostList')")
    @GetMapping("/BasePostList")
    @ApiOperation(value = "查询邮局维护列表")
    public TableDataInfo basePostList(BasePostQuery query)
    {
        startPage();
        query.setIsCommon(SecurityUtils.getHighParentId());
        List<BasePost> list = postService.selectBasePostList(query);
        return getDataTable(list);
    }

    /**
     * 新增邮局维护
     */
    @PreAuthorize("@ss.hasPermi('base:post:addBasePost')")
    @Log(title = "新增邮局维护", businessType = BusinessType.INSERT)
    @PostMapping("/addBasePost")
    @ApiOperation(value = "新增邮局维护")
    public AjaxResult addBasePost(@RequestBody BasePost basePost)
    {
        basePost.setIsCommon(SecurityUtils.getHighParentId());
        return toAjax(postService.addBasePost(basePost));
    }

    /**
     * 邮局维护详情
     */
    @PreAuthorize("@ss.hasPermi('base:post:getInfo')")
    @GetMapping("/{id}")
    @ApiOperation(value = "邮局维护详情")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(postService.getInfo(id));
    }

    /**
     * 修改邮局维护
     */
    @PreAuthorize("@ss.hasPermi('base:post:editBasePost')")
    @Log(title = "修改邮局维护", businessType = BusinessType.UPDATE)
    @PostMapping("/editBasePost")
    @ApiOperation(value = "修改邮局维护")
    public AjaxResult editBasePost(@RequestBody BasePost basePost)
    {
        return toAjax(postService.editBasePost(basePost));
    }

    /**
     * 删除邮局维护
     */
    @PreAuthorize("@ss.hasPermi('base:post:delBasePost')")
    @Log(title = "删除邮局维护", businessType = BusinessType.UPDATE)
    @GetMapping("/delBasePost/{id}")
    @ApiOperation(value = "删除邮局维护")
    public AjaxResult delBasePost(@PathVariable("id") Long id)
    {
        return toAjax(postService.delBasePost(id));
    }

    /**
     * 根据接收局/托运局名称查询邮局信息
     */
    @GetMapping("/getInfoByAbb")
    @ApiOperation(value = "根据接收局/托运局名称查询邮局信息")
    public AjaxResult getInfoByCode(String abb,Integer type)
    {
        return AjaxResult.success(postService.getInfoByCode(abb,type));
    }

    /**
     * 根据接收局/托运局名称查询邮局信息
     */
    @GetMapping("/getInfoByPort")
    @ApiOperation(value = "根据接收局/托运局名称查询邮局信息")
    public AjaxResult getInfoByPort(String port,Integer type)
    {
        return AjaxResult.success(postService.getInfoByPost(port,type));
    }


}
