package com.gzairports.common.business.arrival.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 办理提货申请修改表
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@Data
@TableName(value = "all_in_apply_edit")
public class ApplyEdit {

    /** 主键id */
    private Long id;

    /** 流水号 */
    private String serialNo;

    /** 申请修改描述 */
    private String applyEdit;

    /** 申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;

    /** 状态 */
    private String applyStatus;

    /** 拒绝修改描述 */
    private String refuseEdit;

    /** 拒绝时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refuseTime;

    /** 创建时间 */
    private Date createTime;

    /** 创建人 */
    private String createBy;

    /** 更新时间 */
    private Date updateTime;

    /** 更新人 */
    private String updateBy;

    /** 部门id    */
    @TableField(exist = false)
    private Long deptId;
}
