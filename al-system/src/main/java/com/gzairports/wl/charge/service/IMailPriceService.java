package com.gzairports.wl.charge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.wl.charge.domain.MailPrice;
import com.gzairports.wl.charge.domain.MailPriceItem;
import com.gzairports.wl.charge.domain.query.FreightRateAirQuery;
import com.gzairports.wl.charge.domain.vo.MailPriceItemVo;
import com.gzairports.wl.charge.domain.vo.MailPriceVo;

import java.util.List;

/**
 * 邮件运价Service接口
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
public interface IMailPriceService extends IService<MailPrice> {
    /**
     * 查询邮件运价列表
     *
     * @param query 查询参数
     * @return 邮件运价列表
     */
    List<MailPriceVo> selectList(FreightRateAirQuery query);

    /**
     * 查询邮件运价
     *
     * @param id 邮件运价主键
     * @return 邮件运价
     */
    MailPriceVo selectRateAirById(Long id);

    /**
     * 新增邮件运价
     *
     * @param price 邮件运价
     * @return 结果
     */
    Long insertFreightRateAir(MailPrice price);

    /**
     * 修改邮件运价
     *
     * @param price 邮件运价
     * @return 结果
     */
    int updateFreightRateAir(MailPrice price);

    /**
     * 查看运价条目
     *
     * @param id 邮件运价id
     * @return 邮件运价条目集合
     */
    List<MailPriceItemVo> selectRateItemList(Long id);

    /**
     * 删除邮件运价
     *
     * @param id 邮件运价主键
     * @return 结果
     */
    int deleteFreightRateAir(Long id);

    String importMailPrice(List<MailPriceVo> mailPriceVos, boolean updateSupport);

    Long importItem(List<MailPriceItem> priceItems, Long rateId, boolean updateSupport);
}
