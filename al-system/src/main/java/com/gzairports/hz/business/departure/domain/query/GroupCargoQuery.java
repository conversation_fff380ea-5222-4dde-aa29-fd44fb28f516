package com.gzairports.hz.business.departure.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 组货调度查询参数
 *
 * <AUTHOR>
 * @date 2024-07-03
 */
@Data
public class GroupCargoQuery {

    /** 航班日期 */
    private Date execDate;

    /** 航班号 */
    private String flightNo;

    /** 状态 */
    private String state;

    /** 组货员 */
    private String groupUser;

    /** 计飞 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 计飞 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
}
