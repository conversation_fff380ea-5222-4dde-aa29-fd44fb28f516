package com.gzairports.hz.business.departure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.hz.business.departure.domain.HzDepGroupUld;
import com.gzairports.hz.business.departure.domain.vo.TruckInfoVo;
import org.apache.ibatis.annotations.Mapper;

/**
 * 组货调度板箱Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
@Mapper
public interface HzDepGroupUldMapper extends BaseMapper<HzDepGroupUld> {

    /**
     * 根据组货板车id查询板车详情
     * @param id 组货板车id
     * @return 结果
     */
    TruckInfoVo selectTruckInfoById(Long id);
}
