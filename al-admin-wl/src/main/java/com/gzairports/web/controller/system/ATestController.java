package com.gzairports.web.controller.system;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.system.domain.ATest;
import com.gzairports.common.system.service.IATestService;
import com.gzairports.common.core.page.TableDataInfo;

/**
 * 代码生成测试Controller
 * 
 * <AUTHOR>
 * @date 2023-09-18
 */
@RestController
@RequestMapping("/system/test")
public class ATestController extends BaseController
{
    @Autowired
    private IATestService aTestService;

    /**
     * 查询代码生成测试列表
     */
    @PreAuthorize("@ss.hasPermi('system:test:list')")
    @GetMapping("/list")
    public TableDataInfo list(ATest aTest)
    {
        startPage();
        List<ATest> list = aTestService.selectATestList(aTest);
        return getDataTable(list);
    }

    /**
     * 导出代码生成测试列表
     */
    @PreAuthorize("@ss.hasPermi('system:test:export')")
    @Log(title = "代码生成测试", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(ATest aTest)
    {
//        List<ATest> list = aTestService.selectATestList(aTest);
//        ExcelUtil<ATest> util = new ExcelUtil<ATest>(ATest.class);
        return null;
    }

    /**
     * 获取代码生成测试详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:test:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(aTestService.selectATestById(id));
    }

    /**
     * 新增代码生成测试
     */
    @PreAuthorize("@ss.hasPermi('system:test:add')")
    @Log(title = "代码生成测试", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ATest aTest)
    {
        return toAjax(aTestService.insertATest(aTest));
    }

    /**
     * 修改代码生成测试
     */
    @PreAuthorize("@ss.hasPermi('system:test:edit')")
    @Log(title = "代码生成测试", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ATest aTest)
    {
        return toAjax(aTestService.updateATest(aTest));
    }

    /**
     * 删除代码生成测试
     */
    @PreAuthorize("@ss.hasPermi('system:test:remove')")
    @Log(title = "代码生成测试", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(aTestService.deleteATestByIds(ids));
    }
}
