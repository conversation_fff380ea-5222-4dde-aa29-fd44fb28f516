package com.gzairports.wl.reporter.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzairports.common.business.reporter.mapper.ReportDataItemMapper;
import com.gzairports.common.business.reporter.mapper.ReportDepSettleCostMapper;
import com.gzairports.common.utils.poi.ExcelToPdfUtil;
import com.gzairports.wl.reporter.domain.query.MawbHawbIncomeQuery;
import com.gzairports.wl.reporter.domain.vo.AHIncomeDetailVO;
import com.gzairports.wl.reporter.domain.vo.AHIncomeTotalVO;
import com.gzairports.wl.reporter.domain.vo.ChargeDetailItemVO;
import com.gzairports.wl.reporter.mapper.MawbHawbIncomeMapper;
import com.gzairports.wl.reporter.service.IMawbHawbIncomeTotalService;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: lan
 * @create: 2025-03-13 14:31
 **/
@Service
public class MawbHawbIncomeTotalServiceImpl implements IMawbHawbIncomeTotalService {

    @Autowired
    private MawbHawbIncomeMapper mawbHawbIncomeMapper;

    @Override
    public Page<AHIncomeTotalVO> queryExportDataList(MawbHawbIncomeQuery query) {
        Page<AHIncomeTotalVO> page = new Page<>(query.getPageNum() != null ? query.getPageNum() : 1, query.getPageSize() != null ? query.getPageSize() : -1);
        Page<AHIncomeTotalVO> pageData = mawbHawbIncomeMapper.selectExportDataTotalList(query,page);
        List<AHIncomeTotalVO> records = pageData.getRecords();
        if (records.size() == 0) {
            records = new ArrayList<AHIncomeTotalVO>();
        }
        BigDecimal AirChargerTotal = new BigDecimal(0);
        BigDecimal SfChargeTotal = new BigDecimal(0);
        BigDecimal profitChargeTotal = new BigDecimal(0);
        String waybillCode = null;
        boolean isFirstHawbForMawb = true;
        for(AHIncomeTotalVO record:records){
            BigDecimal totalChargingMawb = mawbHawbIncomeMapper.selectTotalCharging(record.getMasterWaybillCode());
            BigDecimal totalChargingHawb = mawbHawbIncomeMapper.selectTotalCharging(record.getWaybillCode());
            BigDecimal totalChargingHz = mawbHawbIncomeMapper.selectSettleTotalCharging(record.getMasterWaybillCode());
            record.setAirCharge(totalChargingMawb == null || totalChargingMawb.compareTo(new BigDecimal(0)) == 0 ? "0" : "-" + totalChargingMawb);
            record.setHzCharge(totalChargingHz == null || totalChargingHz.compareTo(new BigDecimal(0)) == 0 ? "0" : "-" + totalChargingHz);
            record.setSfCharge((totalChargingHawb == null ? new BigDecimal(0) : totalChargingHawb).toString());
            AirChargerTotal = AirChargerTotal.add(totalChargingMawb == null ? new BigDecimal(0) : totalChargingMawb);
            SfChargeTotal = SfChargeTotal.add(totalChargingHawb == null ? new BigDecimal(0) : totalChargingHawb);
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            record.setWriteTimeStr(formatter.format(record.getWriteTime()));
            if (!record.getMasterWaybillCode().equals(waybillCode)) {
                isFirstHawbForMawb = true;
            }
            if (isFirstHawbForMawb) {
                BigDecimal sfCharging = mawbHawbIncomeMapper.selectHawbChargingByWaybillCode(record.getMasterWaybillCode());
                BigDecimal profit = (sfCharging == null ? new BigDecimal(0) : sfCharging)
                        .subtract(totalChargingMawb == null ? new BigDecimal(0) : totalChargingMawb)
                        .subtract(totalChargingHz == null ? new BigDecimal(0) : totalChargingHz);
                record.setProfit(profit.toString());
                profitChargeTotal = profitChargeTotal.add(profit);
                isFirstHawbForMawb = false;
            } else {
                record.setProfit(null);
            }
            waybillCode = record.getMasterWaybillCode();
        }
        AHIncomeTotalVO ahIncomeTotalVO = new AHIncomeTotalVO();
        ahIncomeTotalVO.setMasterWaybillCode("合计");
        ahIncomeTotalVO.setAirCharge("-" + AirChargerTotal);
        ahIncomeTotalVO.setSfCharge(SfChargeTotal.toString());
        ahIncomeTotalVO.setProfit(profitChargeTotal.toString());
        records.add(ahIncomeTotalVO);
        if(query.getProfit() != null){
            List<AHIncomeTotalVO> collect;
            if(ObjectUtil.equal(1,query.getProfit())){
                collect = records.stream().filter(item -> item.getProfit().contains("-")).collect(Collectors.toList());
                pageData.setRecords(collect);
                pageData.setTotal(collect.size());
            }else{
                collect = records.stream().filter(item -> !item.getProfit().contains("-")).collect(Collectors.toList());
                pageData.setRecords(collect);
                pageData.setTotal(collect.size());
            }
        }

        return pageData;
    }

    @Override
    public void export(List<AHIncomeTotalVO> data, HttpServletResponse response) {
        try (Workbook workbook = writeDataToWorkbook(data)) {
            //对文件名进行 URL 编码
            String fileName = "主分单收入汇总" + "-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + encodedFileName + ".xlsx");
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private Workbook writeDataToWorkbook(List<AHIncomeTotalVO> data) {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("收入汇总");

        // 创建表头
        String[] headers = {"序号", "主单号", "承运航班", "利润", "制单日期","起点站","目的站", "件数", "重量","计费重量","特货代码","品名",
                "分单号", "分单件数", "分单重量", "分单计费重量", "航空公司结算金额", "货站结算金额", "收发货人结算金额"};
        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < headers.length; i++) {
            headerRow.createCell(i).setCellValue(headers[i]);
        }

        // 获取数据并写入 Excel
        List<List<Object>> excelData = convertToExcelData(data);
        for (int rowIndex = 0; rowIndex < excelData.size(); rowIndex++) {
            Row row = sheet.createRow(rowIndex + 1);
            List<Object> rowData = excelData.get(rowIndex);
            for (int colIndex = 0; colIndex < rowData.size(); colIndex++) {
                Cell cell = row.createCell(colIndex);
                Object value = rowData.get(colIndex);
                if (value instanceof Number) {
                    cell.setCellValue(((Number) value).doubleValue());
                } else {
                    cell.setCellValue(value == null ? "" : value.toString());
                }
            }
        }
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
            sheet.setColumnWidth(i, (int) ((10 + 0.72) * 256));
        }

        return workbook;
    }

    /**
     * 将数据转换为 Excel 数据格式
     */
    public List<List<Object>> convertToExcelData(List<AHIncomeTotalVO> data) {
        List<List<Object>> excelData = new ArrayList<>();
        int index = 1;

        for (AHIncomeTotalVO order : data) {
                List<Object> row = new ArrayList<>();
                row.add(index++);  // 序号
                row.add(order.getMasterWaybillCode());  // 主单号
                row.add(order.getFlightNo());  // 分单号
                row.add(order.getProfit()); //利润
                row.add(order.getWriteTimeStr()); //制单日期
                row.add(order.getSourcePort());  // 始发站
                row.add(order.getDesPort());  // 目的站
                row.add(order.getQuantity());  // 件数
                row.add(order.getWeight());  // 重量
                row.add(order.getChargeWeight()); //计费重量
                row.add(order.getSpecialCargoCode1()); //特货代码
                row.add(order.getCargoName()); // 品名
                row.add(order.getWaybillCode()); //分单号
                row.add(order.getHawbQuantity()); //分单件数
                row.add(order.getHawbWeight()); //分单重量
                row.add(order.getHawbChargeWeight()); //分单计费重量
                row.add(order.getAirCharge()); //航空公司结算金额
                row.add(order.getHzCharge()); //货站结算金额
                row.add(order.getSfCharge()); //收发货人结算金额
                excelData.add(row);
        }
        return excelData;
    }

    /**
     * 报表打印
     * */
    @Override
    public void printPDF(MawbHawbIncomeQuery query, HttpServletResponse response) {
        query.setPageNum(1);
        query.setPageSize(-1);
        Page<AHIncomeTotalVO> pageData = this.queryExportDataList(query);
        // 设置响应头
        try (Workbook workbook = writeDataToWorkbook(pageData.getRecords())) {

            //对文件名进行 URL 编码
            String fileName = "主分单收入汇总" + "-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "attachment; filename=" + encodedFileName + ".pdf");

            // 将 Workbook 写入 ByteArrayOutputStream
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            workbook.write(baos);
            baos.close();
            // 从 ByteArrayOutputStream 获取 ByteArrayInputStream
            ByteArrayInputStream inputStream = new ByteArrayInputStream(baos.toByteArray());
            ExcelToPdfUtil.excelToPdf(inputStream, response.getOutputStream(), ".xlsx");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
