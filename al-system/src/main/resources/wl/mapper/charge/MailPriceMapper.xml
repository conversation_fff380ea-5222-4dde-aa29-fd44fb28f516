<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.wl.charge.mapper.MailPriceMapper">
    <update id="removeRateAir">
        update wl_sf_mail_price set is_del = 1 where id = #{id}
    </update>
    <select id="selectListByQuery" resultType="com.gzairports.wl.charge.domain.vo.MailPriceVo">
        select wcfr.id, wcfr.rate_name, wcfr.rate_type, wcfr.applicable_documents, wcfr.automatic_from_low,
        wcfr.rounding_method, wcfr.effective_date, wcfr.expiration_date, wcfr.remarks, wcfr.carrier_code
        from wl_sf_mail_price wcfr
        <where>
            wcfr.is_del = 0 and wcfr.dept_id = #{deptId}
            <if test="ids != null  and ids.size() > 0"> and wcfr.id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="rateName != null  and rateName != ''"> and wcfr.rate_name = #{rateName}</if>
            <if test="rateType != null  and rateType != ''"> and wcfr.rate_type = #{rateType}</if>
            <if test="applicableDocuments != null and applicableDocuments != ''"> and wcfr.applicable_documents = #{applicableDocuments}</if>
            <if test="effectiveDate != null and effectiveDate != ''">
                AND wcfr.effective_date &gt;= #{effectiveDate}
            </if>
            <if test="expirationDate != null and expirationDate != ''">
                AND wcfr.expiration_date &lt;= #{effectiveDate}
            </if>
        </where>
    </select>
</mapper>