package com.gzairports.common.basedata.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.basedata.domain.BaseCargoMapping;
import com.gzairports.common.basedata.domain.query.CargoCodeMappingQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 货品代码映射Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Mapper
public interface CargoMappingMapper extends BaseMapper<BaseCargoMapping> {

    /**
     * 查询货品代码映射列表
     *
     * @param query 查询参数
     * @return 货品代码映射列表
     */
    List<BaseCargoMapping> selectCargoCodeMappingList(CargoCodeMappingQuery query);

    /**
     * 根据货站货品代码 航空公司货品代码 航空公司代码 查询货品代码映射
     *
     * @param cargoCode 货站货品代码
     * @param airCargoCode 航空公司货品代码
     * @param airCode 航空公司代码
     * @return 结果
     */
    BaseCargoMapping selectCargoMappingByCode(@Param("cargoCode") String cargoCode,@Param("airCargoCode") String airCargoCode,@Param("airCode") String airCode);

    /**
     * 新增货品代码映射
     *
     * @param mapping 货品代码映射
     * @return 结果
     */
    int insertCargoMapping(BaseCargoMapping mapping);

    /**
     * 修改货品代码映射
     *
     * @param mapping 货品代码映射
     * @return 结果
     */
    int updateCargoMapping(BaseCargoMapping mapping);

    /**
     * 删除货站货品代码
     *
     * @param ids 货站货品代码id集合
     * @return 结果
     */
    int delCargoMapping(Long[] ids);
}
