package com.gzairports.hz.business.arrival.domain.query;

import com.gzairports.wl.departure.domain.query.BasePageQuery;
import lombok.Data;

import java.util.Date;

/**
 * 运单节点查询参数
 * <AUTHOR>
 * @date 2024-07-26
 */
@Data
public class NodeQuery extends BasePageQuery {

    /** 入库时间 */
    private Date startTime;

    /** 入库时间 */
    private Date endTime;

    /** 航班号 */
    private String flightNo;

    /** 收货代理人 */
    private String consign;

    /** 运单号 */
    private String waybillCode;
}
