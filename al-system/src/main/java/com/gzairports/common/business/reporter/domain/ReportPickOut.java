package com.gzairports.common.business.reporter.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-02-19
 */
@Data
@TableName("all_report_pick_out")
public class ReportPickOut {

    /** 主键id */
    private Long id;

    /** 统计提货办单id */
    private Long reportPickUpId;

    /** 提货件数 */
    private Integer pieces;

    /** 提货重量 */
    private BigDecimal weight;

    /** 提货码 */
    private String pickUpCode;

    /** 提货时间 */
    private String outTime;

    /** 流水号 */
    private String serialNo;

    /** 提货人 */
    private String customerName;

    /** 总条数 */
    private String totalCount;

    /** 运单号 */
    private String waybillCode;

    //操作人（昵称）、操作时间-(提货人,提货时间)
}
