package com.gzairports.hz.business.departure.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 航班配载板箱表
 *
 * <AUTHOR>
 * @date 2024-06-26
 */
@Data
@TableName("hz_flight_load_uld")
public class FlightLoadUld {

    /** 主键id */
    private Long id;

    /** 航班预配表id */
    private Long flightLoadId;

    /** 集装器id */
    private String uld;

    /** 舱位 */
    private String cabin;

    /** 仓库 */
    private String store;

    /** 库位 */
    private String locator;

    /** 是否修改 */
    private Integer isEdit;
}
