package com.gzairports.web.controller.business.abnormal;

import java.util.List;

import com.gzairports.hz.business.abnormal.domain.HzNoLabel;
import com.gzairports.hz.business.abnormal.domain.vo.HzNoLabelVo;
import com.gzairports.hz.business.abnormal.service.IHzNoLabelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.common.core.page.TableDataInfo;

/**
 * 无标签货物Controller
 * 
 * <AUTHOR>
 * @date 2024-07-26
 */
@RestController
@RequestMapping("/arr/noLabel")
@Api(tags = "无标签货物")
public class HzNoLabelController extends BaseController
{
    @Autowired
    private IHzNoLabelService hzNoLabelService;

    /**
     * 查询无标签货物列表
     */
//    @PreAuthorize("@ss.hasPermi('arr:noLabel:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询无标签货物列表")
    public TableDataInfo list(HzNoLabel hzNoLabel)
    {
        startPage();
        List<HzNoLabel> list = hzNoLabelService.selectHzNoLabelList(hzNoLabel);
        return getDataTable(list);
    }

    /**
     * 导出无标签货物列表
     */
    @PreAuthorize("@ss.hasPermi('arr:noLabel:export')")
    @GetMapping("/export")
    @ApiOperation(value = "导出无标签货物列表")
    public AjaxResult export(HzNoLabel hzNoLabel)
    {
        List<HzNoLabel> list = hzNoLabelService.selectHzNoLabelList(hzNoLabel);
        ExcelUtil<HzNoLabel> util = new ExcelUtil<HzNoLabel>(HzNoLabel.class);
        return util.exportExcel(list, "无标签货物数据");
    }

    /**
     * 获取无标签货物详细信息
     */
    @PreAuthorize("@ss.hasPermi('arr:noLabel:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取无标签货物详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(hzNoLabelService.selectHzNoLabelById(id));
    }

    /**
     * 新增无标签货物
     */
    @PreAuthorize("@ss.hasPermi('arr:noLabel:add')")
    @PostMapping("/add")
    @ApiOperation(value = "新增无标签货物")
    public AjaxResult add(@RequestBody HzNoLabel hzNoLabel)
    {
        return toAjax(hzNoLabelService.insertHzNoLabel(hzNoLabel));
    }

    /**
     * 修改无标签货物
     */
    @PreAuthorize("@ss.hasPermi('arr:noLabel:edit')")
    @PostMapping("/edit")
    @ApiOperation(value = "修改无标签货物")
    public AjaxResult edit(@RequestBody HzNoLabel hzNoLabel)
    {
        return toAjax(hzNoLabelService.updateHzNoLabel(hzNoLabel));
    }

    /**
     * 处理无标签货物
     */
    @PreAuthorize("@ss.hasPermi('arr:noLabel:handle')")
    @PostMapping("/handle")
    @ApiOperation(value = "处理无标签货物")
    public AjaxResult handle(@RequestBody HzNoLabelVo vo)
    {
        return toAjax(hzNoLabelService.handleHzNoLabel(vo));
    }


}
