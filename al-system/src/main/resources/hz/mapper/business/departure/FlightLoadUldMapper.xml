<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.departure.mapper.FlightLoadUldMapper">

    <select id="selectLoadUldList" resultType="com.gzairports.hz.business.departure.domain.vo.ForwardImportUldVo">
        select id, uld, cabin from hz_flight_load_uld where flight_load_id = #{flightLoadId}
    </select>
    <select id="selectListByFlightLoadId"
            resultType="com.gzairports.hz.business.departure.domain.vo.FormalUldVo">
        select id, cabin, uld as waybillCode, is_edit from hz_flight_load_uld where flight_load_id = #{id}
        <if test="type != null">
            and is_edit = #{type}
        </if>
    </select>
    <select id="selectListByFlightLoadIdAndUldIds"
            resultType="com.gzairports.hz.business.departure.domain.vo.FormalUldVo">
        select id, cabin, uld as waybillCode, is_edit from hz_flight_load_uld where flight_load_id = #{id}
        and id in
        <foreach collection="uldIds" item="uldId" open="(" separator="," close=")">
            #{uldId}
        </foreach>
    </select>
    <select id="selectWaybillNum" resultType="java.lang.Integer">
        select count(DISTINCT hfluw.waybill_id)
        from hz_flight_load_uld hflu
        left join hz_flight_load_uld_waybill hfluw on hflu.id = hfluw.load_uld_id
        where hflu.id = #{id}
    </select>
    <select id="selectWaybillIdsByFlightLoadId" resultType="java.lang.Long">
        select hfluw.waybill_id
        from hz_flight_load_uld_waybill hfluw
            left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
        where hflu.flight_load_id = #{id}
    </select>

</mapper>