<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.wrong.mapper.WrongMapper">

    <select id="selectListByQuery" resultType="com.gzairports.common.business.wrong.domain.Wrong">
          select id, waybill_code, type, agent, status, wrong_type, wrong_remark, register_time, pro_method, pro_remark
          from all_wrong
          <where>
              and dept_id = #{query.deptId} and agent = #{agent}
              <if test="query.startTime != null">
                  and register_time <![CDATA[>=]]> #{query.startTime}
              </if>
              <if test="query.endTime != null">
                  and register_time <![CDATA[<=]]> #{query.endTime}
              </if>
              <if test="query.waybillCode != null and query.waybillCode != ''">
                  and waybill_code = #{query.waybillCode}
              </if>
              <if test="query.wrongType != null and query.wrongType != ''">
                  and wrong_type = #{query.wrongType}
              </if>
              <if test="query.status != null">
                  and status = #{query.status}
              </if>
          </where>
    </select>
    <select id="selectListAllByQuery" resultType="com.gzairports.common.business.wrong.domain.Wrong">
        select aw.id, aw.waybill_code, aw.type, agent, aw.status, wrong_type, wrong_remark,
               register_time, pro_method, pro_remark, method_remark, pull_id,
               pull_quantity, pull_weight, tally_id
        from all_wrong aw
            left join all_air_waybill aaw on aaw.waybill_code = aw.waybill_code
        <where>
            <if test="query.startTime != null">
                and register_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and register_time <![CDATA[<=]]> #{query.endTime}
            </if>
            <if test="query.waybillCode != null and query.waybillCode != ''">
                and aw.waybill_code like concat('%',#{query.waybillCode},'%')
            </if>
            <if test="query.wrongType != null and query.wrongType != ''">
                and wrong_type = #{query.wrongType}
            </if>
            <if test="query.status != null">
                and aw.status = #{query.status}
            </if>
            <if test="query.deptId != null">
                and aw.dept_id = #{query.deptId}
            </if>
            <if test="query.proMethod != null and query.proMethod != ''">
                and pro_method = #{query.proMethod}
            </if>
            <if test="query.type != null and query.type != ''">
                and aw.type = #{query.type}
            </if>
        </where>
        order by aaw.write_time desc, aw.register_time desc
    </select>
    <select id="getWaybillCodeByFour" resultType="java.lang.String">
        select waybill_code from all_air_waybill
        WHERE RIGHT(waybill_code,#{length}) = #{waybillCode}
         <if test="deptId != null">
             and dept_id = #{deptId}
         </if>
    </select>

    <select id="selectAgent" resultType="com.gzairports.common.business.wrong.domain.Wrong">
        select sd.dept_name as agent, type
        from all_air_waybill aaw left join sys_dept sd on sd.dept_id = aaw.dept_id
        where waybill_code = #{waybillCode}
    </select>
    <select id="isOperateWrong" resultType="java.lang.Integer">
        select count(1) from all_wrong
        where waybill_code = #{waybillCode}
        and (status != 1 or pro_method != 0 or is_over != 0)
    </select>
</mapper>