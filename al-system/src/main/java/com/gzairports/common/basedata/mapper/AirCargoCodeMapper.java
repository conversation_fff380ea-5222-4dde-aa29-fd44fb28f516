package com.gzairports.common.basedata.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.basedata.domain.BaseAirCargoCode;
import com.gzairports.common.basedata.domain.query.AirCargoCodeQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 航空公司货品代码Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Mapper
public interface AirCargoCodeMapper extends BaseMapper<BaseAirCargoCode> {

    /**
     * 查询航空公司货品代码列表
     *
     * @param query 查询参数
     * @return 航空公司货品代码列表
     */
    List<BaseAirCargoCode> selectAirCargoCodeList(AirCargoCodeQuery query);

    /**
     * 根据航空公司货品代码查询航空公司货品代码
     *
     * @param code 航空公司货品代码
     * @return 结果
     */
    BaseAirCargoCode selectAirCargoCodeByCode(String code);

    /**
     * 新增航空公司货品代码
     *
     * @param airCargoCode 航空公司货品代码
     * @return 结果
     */
    int insertAirCargoCode(BaseAirCargoCode airCargoCode);

    /**
     * 修改航空公司货品代码
     *
     * @param airCargoCode 航空公司货品代码
     * @return 结果
     */
    int updateAirCargoCode(BaseAirCargoCode airCargoCode);

    /**
     * 删除航空公司货品代码
     *
     * @param ids 航空公司货品代码id集合
     * @return 结果
     */
    int delAirCargoCode(Long[] ids);
}
