package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BaseSpecialCode;
import com.gzairports.common.basedata.domain.query.SpecialCodeQuery;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.common.basedata.service.ISpecialCodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 货物特殊处理代码Controller
 *
 * <AUTHOR>
 * @date 2024-02-20
 */
@RestController
@RequestMapping("/base/specialCargo")
@Api(tags = "货物特殊处理代码数据接口")
public class SpecialCodeController extends BaseController {

    @Autowired
    private ISpecialCodeService specialCodeService;

    /**
     * 查询货物特殊处理代码列表
     */
//    @PreAuthorize("@ss.hasPermi('base:specialCargo:specialCodeList')")
    @GetMapping("/specialCodeList")
    @ApiOperation(value = "查询货物特殊处理代码列表")
    public TableDataInfo specialCodeList(SpecialCodeQuery query)
    {
        startPage();
        List<BaseSpecialCode> list = specialCodeService.selectSpecialCodeList(query);
        return getDataTable(list);
    }

    /**
     * 导出货物特殊处理代码
     */
    @PreAuthorize("@ss.hasPermi('base:specialCargo:exportSpecialCode')")
    @Log(title = "导出货物特殊处理代码", businessType = BusinessType.EXPORT)
    @PostMapping("/exportSpecialCode")
    @ApiOperation(value = "导出货物特殊处理代码")
    public void exportSpecialCode(HttpServletResponse response, SpecialCodeQuery query)
    {
        List<BaseSpecialCode> list = specialCodeService.selectSpecialCodeList(query);
        ExcelUtil<BaseSpecialCode> util = new ExcelUtil<BaseSpecialCode>(BaseSpecialCode.class);
        util.exportExcel(response, list, "货物特殊处理代码");
    }
}
