package com.gzairports.hz.business.departure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.hz.business.departure.domain.HzDisBoard;
import com.gzairports.hz.business.departure.domain.query.ForwardImportQuery;
import com.gzairports.hz.business.departure.domain.vo.ForwardImportUldVo;
import com.gzairports.hz.business.departure.domain.vo.ForwardImportWaybillVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 运单卸下Mapper接口
 *
 * <AUTHOR>
 * @date 2024-10-12
 */
@Mapper
public interface HzDisBoardMapper extends BaseMapper<HzDisBoard> {
    List<ForwardImportWaybillVo> selectImportDate(@Param("query") ForwardImportQuery query);

    List<ForwardImportUldVo> selectImportUldData(@Param("query") ForwardImportQuery query);

    List<ForwardImportWaybillVo> selectListByCollectIds(@Param("collect2") List<Long> collect2);

    void updateLoad(Long collectId);

    ForwardImportWaybillVo selectByCollectId(Long collectId);

    List<Long> selectImportDataByCode(Long waybillId);

    List<Long> selectImportUldDataByCode(@Param("waybillId") Long waybillId, @Param("uld") String uld);
}
