package com.gzairports.hz.business.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.pdf.PdfPrintAnnotation;
import lombok.Data;

import java.math.BigDecimal;

import static com.gzairports.common.pdf.PdfPrintAnnotation.PdfFieldType.IMAGE;
import static com.gzairports.common.pdf.PdfPrintAnnotation.PdfFieldType.TEXT;

/**
 * 打印装机指示牌参数
 *
 * <AUTHOR>
 * @date 2024-10-07
 */
@Data
public class PrintRepeatVo {

    private String uld;

    /** 舱位 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "cabin")
    private String cabin;

    /** 目的站 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "des1")
    private String des1;

    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "cargoQuantity")
    private Integer cargoQuantity;

    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "cargoWeight")
    private BigDecimal cargoWeight;

    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "mailQuantity")
    private Integer mailQuantity;

    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "mailWeight")
    private BigDecimal mailWeight;

    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "execDate")
    private String execDate;

    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "flightNo")
    private String flightNo;

    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "craftNo")
    private String craftNo;

    @PdfPrintAnnotation(pdfFieldType = IMAGE, pdfFieldName = "qrCode")
    private String qrCode;
}
