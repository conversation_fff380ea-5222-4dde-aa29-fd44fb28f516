package com.gzairports.web.controller.business.departure;

import com.gzairports.hz.business.departure.domain.HzDepPullDown;
import com.gzairports.hz.business.departure.domain.query.PullDownQuery;
import com.gzairports.hz.business.departure.domain.vo.PullDownInfoVo;
import com.gzairports.hz.business.departure.domain.vo.PullDownVo;
import com.gzairports.hz.business.departure.service.IPullDownService;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 临时拉下Controller
 *
 * <AUTHOR>
 * @date 2024-07-02
 */
@RestController
@RequestMapping("/dep/pullDown")
@Api(tags = "临时拉下")
public class PullDownController extends BaseController {

    @Autowired
    private IPullDownService pullDownService;

    /**
     * 查询临时拉下列表
     */
//    @PreAuthorize("@ss.hasPermi('dep:pullDown:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询临时拉下列表")
    public AjaxResult list(PullDownQuery query)
    {
        return AjaxResult.success(pullDownService.selectList(query));
    }

    /**
     *新增拉下数据
     */
    @PreAuthorize("@ss.hasPermi('dep:pullDown:add')")
    @PostMapping("/add")
    @ApiOperation(value = "新增拉下")
    public AjaxResult add(@RequestBody HzDepPullDown pullDown){
        return toAjax(pullDownService.add(pullDown));
    }

    /**
     * 查询配载件数和重量
     */
    @PreAuthorize("@ss.hasPermi('dep:pullDown:selectLoadInfo')")
    @PostMapping("/selectLoadInfo")
    @ApiOperation(value = "查询配载件数和重量")
    public AjaxResult selectLoadInfo(@RequestBody PullDownInfoVo vo){
        return AjaxResult.success(pullDownService.selectLoadInfo(vo));
    }

    /**
     * 查看详情
     */
    @PreAuthorize("@ss.hasPermi('dep:pullDown:getInfo')")
    @GetMapping("/getInfo/{id}")
    @ApiOperation(value = "查看详情")
    public AjaxResult getInfo(@PathVariable("id") Long id){
        return AjaxResult.success(pullDownService.getInfo(id));
    }

    /**
     *修改拉下数据
     */
    @PreAuthorize("@ss.hasPermi('dep:pullDown:edit')")
    @PostMapping("/edit")
    @ApiOperation(value = "修改拉下数据")
    public AjaxResult edit(@RequestBody HzDepPullDown pullDown){
        return toAjax(pullDownService.edit(pullDown));
    }

    /**
     * 据运单后四位或八位运单号查询运单
     */
    @PreAuthorize("@ss.hasPermi('dep:pullDown:getWaybill')")
    @GetMapping("/getWaybill/{waybillCode}")
    @ApiOperation(value = "据运单后四位或八位运单号查询运单")
    public AjaxResult getWaybill(@PathVariable("waybillCode") String waybillCode){
        return AjaxResult.success(pullDownService.getWaybill(waybillCode));
    }
}
