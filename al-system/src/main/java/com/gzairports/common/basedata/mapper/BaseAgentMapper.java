package com.gzairports.common.basedata.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.basedata.domain.BaseAgent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 代理人配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-17
 */
@Mapper
public interface BaseAgentMapper extends BaseMapper<BaseAgent>
{
    /**
     * 查询代理人配置
     * 
     * @param id 代理人配置主键
     * @return 代理人配置
     */
    public BaseAgent selectBaseAgentById(Long id);

    /**
     * 查询代理人配置列表
     * 
     * @param baseAgent 代理人配置
     * @return 代理人配置集合
     */
    public List<BaseAgent> selectBaseAgentList(BaseAgent baseAgent);

    /**
     * 新增代理人配置
     * 
     * @param baseAgent 代理人配置
     * @return 结果
     */
    public int insertBaseAgent(BaseAgent baseAgent);

    /**
     * 修改代理人配置
     * 
     * @param baseAgent 代理人配置
     * @return 结果
     */
    public int updateBaseAgent(BaseAgent baseAgent);

    /**
     * 删除代理人配置
     * 
     * @param id 代理人配置主键
     * @return 结果
     */
    public int deleteBaseAgentById(Long id);

    /**
     * 批量删除代理人配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBaseAgentByIds(Long[] ids);

    /**
     * 根据代理公司名称查询代理人配置
     * @param agentCompany 代理人公司
     * @return 结果
     */
    BaseAgent selectBaseAgentByName(String agentCompany);

    /**
     * 条件查询代理人配置并连表查询金额详细数据
     * @param baseAgent
     * @return
     */
    List<BaseAgent> selectExportBaseAgentList(BaseAgent baseAgent);

    BaseAgent selectByDeptId(String deptId);

    List<BaseAgent> selectListByAgentCode(@Param("agentCodes") List<String> agentCodes);
}
