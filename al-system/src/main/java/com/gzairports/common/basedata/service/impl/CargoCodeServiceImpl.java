package com.gzairports.common.basedata.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.basedata.domain.BaseCargoCategory;
import com.gzairports.common.basedata.domain.BaseCargoCode;
import com.gzairports.common.basedata.domain.query.CargoCategoryQuery;
import com.gzairports.common.basedata.domain.query.CargoCodeQuery;
import com.gzairports.common.basedata.mapper.CargoCategoryMapper;
import com.gzairports.common.basedata.mapper.CargoCodeMapper;
import com.gzairports.common.basedata.service.ICargoCodeService;
import com.gzairports.common.core.domain.TreeSelect;
import com.gzairports.common.core.domain.entity.SysDept;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.exception.ServiceException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.bean.BeanValidators;
import com.gzairports.common.utils.spring.SpringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.validation.Validator;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 货站货品代码Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Service
public class CargoCodeServiceImpl implements ICargoCodeService {

    private static final Logger log = LoggerFactory.getLogger(WeightServiceImpl.class);

    @Autowired
    private Validator validator;

    @Autowired
    private CargoCodeMapper cargoCodeMapper;

    @Autowired
    private CargoCategoryMapper cargoCategoryMapper;

    /**
     * 查询货站货品代码列表
     *
     * @param query 查询参数
     * @return 货站货品代码列表
     */
    @Override
    public List<BaseCargoCode> selectCargoCodeList(CargoCodeQuery query) {
        return cargoCodeMapper.selectCargoCodeList(query);
    }

    /**
     * 导入货站货品代码
     *
     * @param cargoCodes 货站货品代码列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importCargoCode(List<BaseCargoCode> cargoCodes, boolean updateSupport) {
        if (StringUtils.isNull(cargoCodes) || cargoCodes.size() == 0)
        {
            throw new ServiceException("导入货站货品代码数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (BaseCargoCode cargoCode : cargoCodes) {
            try {
                // 验证是否存在相同的货站货品代码
                BaseCargoCode baseCargoCode = cargoCodeMapper.selectCargoCodeByCode(cargoCode.getCode());
                if (StringUtils.isNull(baseCargoCode)) {
                    BeanValidators.validateWithException(validator, cargoCode);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    cargoCode.setCreateBy(loginUser.getUsername());
                    cargoCode.setCreateTime(DateUtils.getNowDate());
                    cargoCodeMapper.insertCargoCode(cargoCode);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、货站货品代码 " + cargoCode.getCode() + " 导入成功");
                }
                else if (updateSupport) {
                    BeanValidators.validateWithException(validator, cargoCode);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    cargoCode.setId(baseCargoCode.getId());
                    cargoCode.setUpdateBy(loginUser.getUsername());
                    cargoCode.setUpdateTime(DateUtils.getNowDate());
                    cargoCodeMapper.updateCargoCode(cargoCode);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、货站货品代码 " + cargoCode.getCode() + " 更新成功");
                }
                else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、货站货品代码 " + cargoCode.getCode() + " 已存在");
                }
            }catch (Exception e){
                failureNum++;
                String msg = "<br/>" + failureNum + "、货站货品代码 " + cargoCode.getCode() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 新增货站货品代码
     *
     * @param cargoCode 货站货品代码
     * @return 结果
     */
    @Override
    public int addCargoCode(BaseCargoCode cargoCode) {
        BaseCargoCode baseCargoCode = cargoCodeMapper.selectOne(new QueryWrapper<BaseCargoCode>()
                .eq("code", cargoCode.getCode())
                .eq("is_del", 0));

        if (StringUtils.isNotNull(baseCargoCode)) {
            throw new CustomException("货品代码重复");
        }

        /*//判断货站货品代码是否重复
        if (!StringUtils.isNotNull(cargoCodeMapper.selectByCode(cargoCode.getCode()))) {
            throw new CustomException("货品代码已存在");
        }*/
        LoginUser loginUser = SecurityUtils.getLoginUser();
        cargoCode.setCreateBy(loginUser.getUsername());
        cargoCode.setCreateTime(DateUtils.getNowDate());
        return cargoCodeMapper.insertCargoCode(cargoCode);
    }

    /**
     * 修改货站货品代码
     *
     * @param cargoCode 货站货品代码
     * @return 结果
     */
    @Override
    public int editCargoCode(BaseCargoCode cargoCode) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        cargoCode.setUpdateBy(loginUser.getUsername());
        cargoCode.setUpdateTime(DateUtils.getNowDate());
        return cargoCodeMapper.updateCargoCode(cargoCode);
    }

    /**
     * 货站货品代码
     *
     * @param ids 货站货品代码id集合
     * @return 结果
     */
    @Override
    public int delCargoCode(Long[] ids) {
        return cargoCodeMapper.delCargoCode(ids);
    }

    /**
     * 根据代码查询货品信息
     *
     * @param code 货站货品代码code
     * @return 货站货品代码
     */
    @Override
    public BaseCargoCode selectByCode(String code) {
        return cargoCodeMapper.selectByCode(code);
    }

    @Override
    public List<BaseCargoCategory> selectCargoCodeListTree() {
        List<BaseCargoCategory> baseCargoCategories = cargoCategoryMapper.selectCargoCategoryList(new CargoCategoryQuery());
        for (BaseCargoCategory baseCargoCategory : baseCargoCategories) {
            List<BaseCargoCode> baseCargoCodes = cargoCodeMapper.selectList(new QueryWrapper<BaseCargoCode>()
                    .eq("category_code", baseCargoCategory.getCode())
                    .eq("is_del","0"));
            baseCargoCategory.setCargoCodeList(baseCargoCodes);
        }
        return baseCargoCategories;
    }

    @Override
    public List<BaseCargoCode> selectCargoCodeBatch(String[] codes) {
        if (codes != null && codes.length > 0) {
            return cargoCodeMapper.selectList(new QueryWrapper<BaseCargoCode>()
                    .in("category_code", codes)
                    .eq("is_del",0));
        } else {
            return cargoCodeMapper.selectCargoCodeList(new CargoCodeQuery());
        }
    }

    @Override
    public List<BaseCargoCode> selectCodeBatch(String[] codes) {
        if (codes != null && codes.length > 0) {
            return cargoCodeMapper.selectList(new QueryWrapper<BaseCargoCode>()
                    .in("code", codes)
                    .eq("is_del",0));
        } else {
            return cargoCodeMapper.selectCargoCodeList(new CargoCodeQuery());
        }
    }
}
