package com.gzairports.wl.ticket.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.wl.ticket.domain.TicketNum;
import com.gzairports.wl.ticket.domain.Ticket;
import com.gzairports.wl.ticket.domain.query.TicketNumQuery;
import com.gzairports.wl.ticket.domain.query.TicketQuery;
import com.gzairports.wl.ticket.domain.vo.*;

import java.util.List;

/**
 * 单证信息Service接口
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
public interface TicketService extends IService<Ticket> {

    /**
     * 查询单证信息列表列表
     *
     * @param query 查询参数
     * @return 单证信息列表列表
     */
    List<TicketVO> selectList(TicketQuery query);

    /**
     * 查询单证详情
     *
     * @param id 查询参数
     * @return 单证信息详情
     */
    TicketDetailVO detail(Long id);


    /**
     * 查询单证使用log列表
     *
     * @param id 查询参数
     * @return 单证log详情
     */
    List<TicketOprLogVO> log(Long id);

    /**
     * 单证入库
     *
     * @param ticket 单证信息
     * @return 结果
     */
    Long add(Ticket ticket);

    /**
     * 根据id查询单证信息
     *
     * @param id 单证id
     * @return 结果
     */
    TicketInfoVO getInfo(Long id);

    /**
     * 单证票号销号
     *
     * @param query 销号票号数量
     * @return 结果
     */
    int cancel(TicketNumQuery query);

    /**
     * 单证票号发放
     *
     * @param query 发放票号数量
     * @return 结果
     */
    int grant(TicketNumQuery query);

    /**
     * 取消发放
     *
     * @param recordId 发放日志id
     * @return 结果
     */
    int cancelGrant(Long recordId);


    /**
     * 取消入库
     *
     * @param query 取消入库参数
     * @return 结果
     */
    int warehouse(TicketNumQuery query);

    /**
     * 详情-使用明细
     *
     * @param recordId 发放记录id
     * @return 票号使用明细列表
     */
    List<TicketNum> usage(Long recordId);

    /**
     * 删除单证
     *
     * @param id 单证id
     * @return 结果
     */
    int removeByTicketId(Long id);

    /**
     * 修改单证
     *
     * @param ticket 单证信息
     * @return 结果
     */
    int edit(Ticket ticket);

    /**
     * 重新发放
     * @param recordId 发放记录id
     * @return 结果
     */
    int reissue(Long recordId);

    List<TicketSourceVo> getTicketSource(String type);

    List<TicketNum> selectDetailList(TicketQuery query);

    List<TicketSourceVo> getSource(String type);
}
