package com.gzairports.web.controller.basedata;


import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.common.basedata.domain.Customer;
import com.gzairports.common.basedata.service.ICustomerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 客户Controller
 * 
 * <AUTHOR>
 * @date 2024-01-31
 */
@RestController
@RequestMapping("/base/customer")
@Api(tags = "客户管理")
public class CustomerController extends BaseController
{
    @Autowired
    private ICustomerService customerService;

    @Autowired
    private SysDeptMapper deptMapper;

    /**
     * 查询客户列表
     */
//    @PreAuthorize("@ss.hasPermi('base:customer:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询客户列表")
    public TableDataInfo list(Customer customer)
    {
        startPage();
        List<Customer> list = customerService.selectCustomerList(customer);
        return getDataTable(list);
    }

    /**
     * 查询当前代理人客户列表
     */
//    @PreAuthorize("@ss.hasPermi('base:customer:list')")
    @GetMapping("/listForProfire")
    @ApiOperation(value = "查询客户列表")
    public AjaxResult listForProfire(Customer customer)
    {
        //得到最父级的部门id
        Long deptId = SecurityUtils.getHighParentId();
//        while(true){
//            Long parentId = deptMapper.selectParentIdByDeptId(deptId);
//            if (StringUtils.isNotNull(parentId)){
//                deptId = parentId;
//                if (deptMapper.selectParentIdByDeptId(deptId) == 0){
//                    break;
//                }
//            }
//        }
        customer.setDeptId(deptId);
        return success(customerService.selectCustomerList(customer));
    }



    /**
     * 导出客户列表
     */
    @PreAuthorize("@ss.hasPermi('base:customer:export')")
    @Log(title = "导出客户列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出客户列表")
    public void export(HttpServletResponse response, Customer customer)
    {
        List<Customer> list = customerService.selectCustomerList(customer);
        ExcelUtil<Customer> util = new ExcelUtil<Customer>(Customer.class);
        util.exportExcel(response, list, "客户数据");
    }

    /**
     * 获取客户详细信息
     */
    @PreAuthorize("@ss.hasPermi('base:customer:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取客户详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(customerService.selectCustomerById(id));
    }

    /**
     * 新增客户
     */
    @PreAuthorize("@ss.hasPermi('base:customer:add')")
    @Log(title = "新增客户", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增客户")
    public AjaxResult add(@RequestBody Customer customer)
    {
        return toAjax(customerService.insertCustomer(customer));
    }

    /**
     * 修改客户
     */
    @PreAuthorize("@ss.hasPermi('base:customer:edit')")
    @Log(title = "修改客户", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改客户")
    public AjaxResult edit(@RequestBody Customer customer)
    {
        return toAjax(customerService.updateCustomer(customer));
    }

    /**
     * 删除客户
     */
    @PreAuthorize("@ss.hasPermi('base:customer:remove')")
    @Log(title = "删除客户", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @ApiOperation(value = "删除客户")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(customerService.deleteCustomerByIds(ids));
    }

    /**
     *
     * 根据简称查询客户信息
     */
    @GetMapping("/abb/{consigneeAbb}")
    @ApiOperation(value = "根据简称查询客户信息")
    public AjaxResult abb(@PathVariable("consigneeAbb") String consigneeAbb)
    {
        return AjaxResult.success(customerService.abb(consigneeAbb));
    }

}
