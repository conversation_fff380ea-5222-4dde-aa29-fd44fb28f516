package com.gzairports.common.enums;

/**
 * <AUTHOR>
 * @date 2025-02-18
 */
public enum ReportEnum {

    SUB_ORDER("国内分单", new String[]{
            "运单号", "始发站", "目的站", "航班", "关联主单号", "承运人1", "中转换单", "补重单", "结算客户", "发货人",
            "收货人", "品名编码", "品名", "特货代码1", "包装", "件数", "重量", "计费重量", "体积", "尺寸", "运输保险价值",
            "出港付费方式", "费用总计", "填开时间", "费用备注", "费用类型", "费率", "计费金额"
    }),
    MAIN_ORDER("国内主单", new String[]{
            "运单号", "交运代理人", "换单", "中转换单", "换单单号", "状态", "始发站", "目的站", "航班1", "航班日期1", "承运人1",
            "发货人简称", "发货人", "发货人电话", "收货人简称", "收货人", "收货人电话", "代理公司", "城市", "运输声明价值", "运输保险价值",
            "海关监管", "公务单", "包量/包仓", "特货代码1", "品名编码", "品名", "包装", "件数", "毛量", "计费重量", "体积", "尺寸",
            "费率", "航空运费", "费用类型", "费率", "计费金额"
    }),
    MAIL_ORDER("邮件单", new String[]{
            "邮件单号", "始发港", "目的港", "邮件种类", "托运局名称", "托运局电话", "托运局联系人", "托运局地址", "接收局名称", "接收局电话",
            "接收局联系人", "接收局地址", "承运人1", "航班号1", "航班日期1", "到达站1", "件数", "实际重量", "计费重量", "体积", "费率/公斤",
            "费用总额", "制单人", "制单时间"
    });

    private final String name;
    private final String[] fields;

    ReportEnum(String name, String[] fields) {
        this.name = name;
        this.fields = fields;
    }

    public String getName() {
        return name;
    }

    public String[] getFields() {
        return fields;
    }
}
