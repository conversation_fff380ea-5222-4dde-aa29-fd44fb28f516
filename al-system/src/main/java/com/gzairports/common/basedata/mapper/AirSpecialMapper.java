package com.gzairports.common.basedata.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 航空公司货品代码与特货关联Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
@Mapper
public interface AirSpecialMapper {

    /**
     * 根据航空货品id查询特货代码id
     *
     * @return 特货代码id
     */
    List<Long> selectByCargoId(Long id);

    /**
     * 新增航空公司与特货代码关联数据
     *
     */
    void insert(@Param("id") Long id,@Param("aLong") Long aLong);
}
