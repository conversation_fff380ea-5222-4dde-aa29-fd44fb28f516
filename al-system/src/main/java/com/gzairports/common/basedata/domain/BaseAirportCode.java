package com.gzairports.common.basedata.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.util.Date;


/**
 * 机场代码
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Data
@TableName("base_airport_code")
public class BaseAirportCode {

    /** 主键id */
    private Long id;

    /** 机场代码 */
    @Excel(name = "机场代码")
    private String code;

    /** 机场四字码 */
    @Excel(name = "机场四字码")
    private String fourCode;

    /** 城市代码 */
    @Excel(name = "城市代码")
    private String cityCode;

    /** 城市名称 */
    @Excel(name = "城市名称")
    private String cityName;

    /** 中文名称 */
    @Excel(name = "中文名称")
    private String chineseName;

    /** 英文名称 */
    @Excel(name = "英文名称")
    private String englishName;

    /** 是否常用 */
    @Excel(name = "是否常用",readConverterExp ="0=否,1=是" )
    private Integer isCommon;

    /** 国际国内 */
    @Excel(name = "国际国内")
    private String isDomestic;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;
}
