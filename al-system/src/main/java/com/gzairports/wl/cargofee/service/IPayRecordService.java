package com.gzairports.wl.cargofee.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.core.domain.PageQuery;
import com.gzairports.wl.cargofee.domain.PayRecord;
import com.gzairports.wl.cargofee.domain.query.PayRecordQuery;

import java.util.List;

/**
 * 支付流水记录Service接口
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
public interface IPayRecordService extends IService<PayRecord> {

    /**
     * 查询支付流水记录
     * @param query 查询条件
     * @return 结果
     */
    List<PayRecord> selectList(PayRecordQuery query);
}
