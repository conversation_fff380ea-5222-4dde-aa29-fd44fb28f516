package com.gzairports.common.business.reporter.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-02-19
 */
@Data
@TableName("all_report_data_waybill")
public class ReportDataWaybill {

    /** 主键id */
    private Long id;

    /** 运单号 */
    private String waybillCode;

    /** 进出港类型 */
    private String type;

    /** 运单前缀 */
    private String waybillPrefix;

    /** 运单类型 */
    private String waybillType;

    /** 状态 */
    private String status;

    /** 交运货站 */
    private String shippingStation;

    /** 代理人 */
    private String agentCompany;

    /** 进港中转 */
    private String transferBill;

    /** 原单单号 */
    private String originBill;

    /** 可补货重量 */
    private String canRestockWeight;

    /** 跨航司运输 */
    private String crossAir;

    /** 始发站 */
    private String sourcePort;

    /** 承运人1 */
    private String carrier1;

    /** 到达站1 */
    private String des1;

    /** 承运人2 */
    private String carrier2;

    /** 到达站2 */
    private String des2;

    /** 承运人3 */
    private String carrier3;

    /** 到达站3 */
    private String des3;

    /** 目的站 */
    private String desPort;

    /** 航班 */
    private String flightNo1;

    /** 航班日期 */
    private String flightDate1;

    /** 航班2 */
    private String flightNo2;

    /** 航班日期2 */
    private String flightDate2;

    /** 发货人 */
    private String shipper;

    /** 城市 */
    private String city;

    /** 储运注意事项 */
    private String storageTransportNotes;

    /** 结算注意事项 */
    private String settlementNotes;

    /** 海关监管 */
    private String customsSupervision;

    /** 公务单 */
    private String officialForm;

    /** 包量/包仓 */
    private String bulkWarehouse;

    /** 货品大类 */
    private String categoryName;

    /** 品名编码 */
    private String cargoCode;

    /** 品名 */
    private String cargoName;

    /** 件数 */
    private String quantity;

    /** 重量 */
    private String weight;

    /** 计费重量 */
    private String chargeWeight;

    /** 包装 */
    private String pack;

    /** 尺寸 */
    private String size;

    /** 体积 */
    private String volume;

    /** 特货代码1 */
    private String specialCargoCode1;

    /** 特货代码2 */
    private String specialCargoCode2;

    /** 特货代码3 */
    private String specialCargoCode3;

    /** 其他特货代码 */
    private String otherSpecialCargoCode;

    /** UN编号 */
    private String dangerCode;

    /** 危险品类型 */
    private String dangerType;

    /** 总金额 */
    private String costSum;

    /** 运价种类 */
    private String rateType;

    /** 填开时间 */
    private String writeTime;

    /** 填开地点 */
    private String writeLocation;

    /** 填开人 */
    private String writer;

    /** 费用备注 */
    private String remark;

    /** 支付状态 */
    private String payStatus;

    /** 支付时间 */
    private String payTime;

    /** 单位id */
    private Long deptId;

    /** 总金额 */
    private String payMoney;

    /** 收运时间 */
    private String collectTime;

    /** 货物已出港件数 */
    private Integer depQuantity;

    /** 货物已出港重量 */
    private String depWeight;

    /** 邮件已出港件数 */
    private Integer mailDepQuantity;

    /** 邮件已出港重量 */
    private String mailDepWeight;

    /** 收货人 */
    private String consign;

    /** 货物件数 */
    private Integer cargoQuantity;

    /** 货物重量 */
    private BigDecimal cargoWeight;

    /** 邮件件数 */
    private Integer mailQuantity;

    /** 邮件重量 */
    private BigDecimal mailWeight;

    /** 主单/邮件单 */
    private String mainOrEmail;

    /** 应付运价 */
    private BigDecimal wCostSum;

    /** 应收运价 */
    private BigDecimal rCostSum;

    @TableField(exist = false)
    private Integer isDel;
}
