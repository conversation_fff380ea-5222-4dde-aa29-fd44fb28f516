package com.gzairports.common.basedata.service;


import com.gzairports.common.basedata.domain.BaseRegionCode;
import com.gzairports.common.basedata.domain.query.RegionCodeQuery;

import java.util.List;

/**
 * 国家或地区代码Service接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
public interface IRegionCodeService {

    /**
     * 查询国家或地区代码列表
     *
     * @param query 查询参数
     * @return 国家或地区代码列表
     */
    List<BaseRegionCode> selectRegionCodeList(RegionCodeQuery query);

    /**
     * 导入国家或地区代码
     *
     * @param regionCodeList 国家或地区代码数据列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    String importRegionCode(List<BaseRegionCode> regionCodeList, boolean updateSupport);

    /**
     * 新增国家或地区代码
     *
     * @param regionCode 国家或地区代码
     * @return 结果
     */
    int addRegionCode(BaseRegionCode regionCode);

    /**
     * 修改国家或地区代码
     *
     * @param regionCode 国家或地区代码
     * @return 结果
     */
    int editRegionCode(BaseRegionCode regionCode);

    /**
     * 删除国家或地区代码
     *
     * @param ids 国家或地区代码id集合
     * @return 结果
     */
    int delRegionCode(Long[] ids);


}
