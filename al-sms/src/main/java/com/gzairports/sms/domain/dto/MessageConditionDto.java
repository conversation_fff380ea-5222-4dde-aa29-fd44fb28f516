package com.gzairports.sms.domain.dto;

import com.gzairports.common.core.domain.BaseEntity;

import java.util.Date;
import java.util.List;

public class MessageConditionDto extends BaseEntity {

    private List<Long> depts;

    private String mobile;

    private Date startTime;

    private Date endTime;

    private Integer pageNum;

    private Integer pageSize;

    public List<Long> getDepts() {
        return depts;
    }

    public void setDepts(List<Long> depts) {
        this.depts = depts;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
