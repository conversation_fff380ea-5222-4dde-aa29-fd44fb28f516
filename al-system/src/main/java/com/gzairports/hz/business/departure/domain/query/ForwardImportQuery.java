package com.gzairports.hz.business.departure.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 代运导入查询条件
 * <AUTHOR>
 * @date 2024-06-28
 */
@Data
public class ForwardImportQuery {

    /** 运单号 */
    private String waybillCode;

    /** 始发站 */
    private String sourcePort;

    /** 起始站 */
    private String startStation;

    /** 航班号 */
    private String flightNo;

    /** 航班号1 */
    private String flightNo1;

    /** 航班日期1 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date flightDate1;

    /** 承运人1 */
    private String carrier1;

    /** 到达站1 */
    private String des1;

    /** 特货代码 */
    private String specialCargoCode1;

    /** 代理人 */
    private String shipper;

    /** 制单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 制单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    private Set<String> carrierList;
}
