package com.gzairports.web.controller.charge;

import com.gzairports.common.annotation.Log;
import com.gzairports.wl.charge.domain.FreightRateItem;
import com.gzairports.wl.charge.domain.MailPrice;
import com.gzairports.wl.charge.domain.MailPriceItem;
import com.gzairports.wl.charge.domain.query.FreightRateAirQuery;
import com.gzairports.wl.charge.domain.vo.FreightRateAirVO;
import com.gzairports.wl.charge.domain.vo.MailPriceItemVo;
import com.gzairports.wl.charge.domain.vo.MailPriceVo;
import com.gzairports.wl.charge.service.IMailPriceService;
import com.gzairports.common.annotation.OperLog;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.wl.log.service.WlOperLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 邮件运价Controller
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@RestController
@RequestMapping("/mailPrice")
@Api(tags = "邮件运价接口")
public class MailPriceController extends BaseController {

    @Autowired
    private IMailPriceService mailService;

    @Autowired
    private WlOperLogService logService;

    /**
     * 查询邮件运价列表
     */
//    @PreAuthorize("@ss.hasPermi('charge:mailPrice:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询邮件运价列表")
    public TableDataInfo list(FreightRateAirQuery query){
        startPage();
        List<MailPriceVo> list = mailService.selectList(query);
        return getDataTable(list);
    }

    /**
     * 导出邮件运价列表
     */
    @PreAuthorize("@ss.hasPermi('charge:mailPrice:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出邮件运价")
    public void export(HttpServletResponse response, FreightRateAirQuery query){
        List<MailPriceVo> list = mailService.selectList(query);
        ExcelUtil<MailPriceVo> util = new ExcelUtil<MailPriceVo>(MailPriceVo.class);
        util.exportExcel(response, list, "邮件运价");
    }

    /**
     * 导出邮件运价列表模板
     */
    @PreAuthorize("@ss.hasPermi('charge:mailPrice:export')")
    @PostMapping("/exportNull")
    @ApiOperation(value = "导出邮件运价模板")
    public void exportNull(HttpServletResponse response){
        List<MailPriceVo> list = new ArrayList<>();
        ExcelUtil<MailPriceVo> util = new ExcelUtil<MailPriceVo>(MailPriceVo.class);
        util.exportExcel(response, list, "邮件运价模板");
    }

    /**
     * 导入邮件运价
     */
    @PreAuthorize("@ss.hasPermi('charge:mailPrice:import')")
    @Log(title = "邮件运价", businessType = BusinessType.IMPORT)
    @PostMapping("/importMailfreightrate")
    @ApiOperation(value = "导入邮件运价")
    public AjaxResult importMailfreightrate(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<MailPriceVo> util = new ExcelUtil<MailPriceVo>(MailPriceVo.class);
        List<MailPriceVo> mailPriceVos = util.importExcel(file.getInputStream());
        String message = mailService.importMailPrice(mailPriceVos, updateSupport);
        return success(message);
    }

    /**
     * 导入邮件运价条目
     */
//    @PreAuthorize("@ss.hasPermi('charge:freightAir:importItem')")
    @OperLog(title = "邮件运价", businessType = BusinessType.IMPORT)
    @PostMapping("/importItem")
    @ApiOperation(value = "导入公布运价条目")
    public AjaxResult importItem(@RequestParam("file") MultipartFile file, @RequestParam("rateId") Long rateId, @RequestParam("updateSupport") boolean updateSupport) throws Exception
    {
        if (file == null){
            return warn("请上传文件");
        }
        ExcelUtil<MailPriceItem> util = new ExcelUtil<MailPriceItem>(MailPriceItem.class);
        List<MailPriceItem> priceItems = util.importRateExcel(file.getInputStream(),1);
        Long message = mailService.importItem(priceItems,rateId, updateSupport);
        return success(message);
    }

    /**
     * 获取邮件运价详细信息
     */
    @PreAuthorize("@ss.hasPermi('charge:mailPrice:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取邮件运价详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(mailService.selectRateAirById(id));
    }

    /**
     * 新增邮件运价
     */
    @PreAuthorize("@ss.hasPermi('charge:mailPrice:add')")
    @OperLog(title = "邮件运价", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ApiOperation(value = "新增邮件运价")
    public AjaxResult add(@RequestBody MailPrice price)
    {
        return success(mailService.insertFreightRateAir(price));
    }

    /**
     * 修改邮件运价
     */
    @PreAuthorize("@ss.hasPermi('charge:mailPrice:edit')")
    @OperLog(title = "邮件运价", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ApiOperation(value = "修改邮件运价")
    public AjaxResult edit(@RequestBody MailPrice price)
    {
        return toAjax(mailService.updateFreightRateAir(price));
    }

    /**
     * 查看运价条目
     */
    @PreAuthorize("@ss.hasPermi('charge:mailPrice:rateItemList')")
    @GetMapping("/rateItemList/{id}")
    @ApiOperation(value = "查看运价条目")
    public TableDataInfo rateItemList(@PathVariable Long id)
    {
        startPage();
        List<MailPriceItemVo> vos = mailService.selectRateItemList(id);
        return getDataTable(vos);
    }

    /**
     * 删除邮件运价
     */
    @PreAuthorize("@ss.hasPermi('charge:mailPrice:del')")
    @OperLog(title = "邮件运价", businessType = BusinessType.DELETE)
    @GetMapping("/del/{id}")
    @ApiOperation(value = "删除邮件运价")
    public AjaxResult del(@PathVariable Long id)
    {
        return toAjax(mailService.deleteFreightRateAir(id));
    }

    /**
     * 邮件运价操作日志查询
     */
    @PreAuthorize("@ss.hasPermi('charge:mailPrice:logList')")
    @GetMapping("/logList/{id}")
    @ApiOperation(value = "邮件运价操作日志查询")
    public AjaxResult logList(@PathVariable Long id)
    {
        return success(logService.log("邮件运价",id));
    }

    /**
     * 邮件运价操作日志详情
     */
    @PreAuthorize("@ss.hasPermi('charge:mailPrice:logInfo')")
    @GetMapping("/logInfo/{operId}")
    @ApiOperation(value = "邮件运价操作日志详情")
    public AjaxResult logInfo(@PathVariable Long operId)
    {
        return success(logService.logInfo(operId));
    }
}
