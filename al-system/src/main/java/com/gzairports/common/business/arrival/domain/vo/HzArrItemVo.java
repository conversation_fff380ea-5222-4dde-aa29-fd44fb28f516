package com.gzairports.common.business.arrival.domain.vo;

import com.gzairports.common.business.arrival.domain.HzArrItem;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 费用明细详情
 *
 * <AUTHOR>
 * @date 2024-08-05
 */
@Data
public class HzArrItemVo {

    /** 运单号 */
    private String waybillCode;

    /** 费用合计 */
    private BigDecimal totalCost;

    /** 货品代码 */
    private String cargoCode;

    /** 货物品名 */
    private String cargoName;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 可提货件数 */
    private Integer canPickUpQuantity;

    /** 可提货重量 */
    private BigDecimal canPickUpWeight;

    /** 费用列表 */
    private List<HzArrItem> items;
}
