package com.gzairports.hz.business.cable.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gzairports.common.annotation.Excel;
import com.gzairports.common.core.domain.BaseEntity;
import com.gzairports.common.utils.StringUtils;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 电报地址管理对象 hz_cable_address
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
@TableName("hz_cable_address")
@Data
public class HzCableAddress extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 机场三字码 */
    @Excel(name = "机场三字码")
    private String airportCode;

    /** 航司二字码 */
    @Excel(name = "航司二字码")
    private String airlinesCode;

    /** 电报地址 */
    @Excel(name = "电报地址")
    private String cableAddress;

    /** 民航局地址 */
    @Excel(name = "民航局地址")
    private String caacAddress;

    /** 邮箱地址 */
    @Excel(name = "邮箱地址")
    private String emailAddress;

    /** RabbitMQ 发报队列 */
    @Excel(name = "rabbitmq发报队列")
    private String mqQueue;

    /** FTP 发报目录 */
    @Excel(name = "FTP发报目录")
    private String ftpList;

    /** 交互方式（逗号分隔）*/
    @Excel(name = "交互方式")
    private String interactionTypes;

    @TableField(exist = false)
    private List<String> interactionTypeList;

    /** 描述 */
    @Excel(name = "描述")
    private String remark;

    /** 是否删除 0 否 1 是 */
    private Integer isDel;

//    private List<String> getTypeList(String interactionTypes){
//        if (StringUtils.isEmpty(interactionTypes)){
//            return Collections.emptyList();
//        }
//        return Arrays.asList(interactionTypes.split(","));
//    }
}
