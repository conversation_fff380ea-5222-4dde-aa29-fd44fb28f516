package com.gzairports.web.controller.message;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.message.domain.Message;
import com.gzairports.common.message.domain.query.MessageQuery;
import com.gzairports.common.message.service.IMessageService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 消息管理Controller
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@RestController
@RequestMapping("/message")
public class MessageController extends BaseController {

    @Autowired
    private IMessageService messageService;

    /**
     * 查询消息列表
     */
//    @PreAuthorize("@ss.hasPermi('bus:message:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询消息列表")
    public TableDataInfo list(MessageQuery query){
        startPage();
        List<Message> list = messageService.selectList(query);
        return getDataTable(list);
    }

    /**
     * 查看详情
     */
    @PreAuthorize("@ss.hasPermi('bus:message:getInfo')")
    @GetMapping("/getInfo/{id}")
    @ApiOperation(value = "查看详情")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(messageService.getInfo(id));
    }
}
