package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BaseAirportCode;
import com.gzairports.common.basedata.domain.query.AirportCodeQuery;
import com.gzairports.common.basedata.service.IAirportCodeService;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 机场代码Controller
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@RestController
@RequestMapping("/base/airportCode")
@Api(tags = "机场代码数据接口")
public class AirportCodeController extends BaseController {

    @Autowired
    private IAirportCodeService airportCodeService;

    /**
     * 查询机场代码列表
     */
//    @PreAuthorize("@ss.hasPermi('base:airportCode:airportCodeList')")
    @GetMapping("/airportCodeList")
    @ApiOperation(value = "查询机场代码列表")
    public TableDataInfo airportCodeList(AirportCodeQuery query)
    {
        startPage();
        List<BaseAirportCode> list = airportCodeService.selectAirportCodeList(query);
        return getDataTable(list);
    }

    /**
     * 导出机场代码
     */
    @PreAuthorize("@ss.hasPermi('base:airportCode:exportAirportCode')")
    @Log(title = "导出机场代码", businessType = BusinessType.EXPORT)
    @PostMapping("/exportAirportCode")
    @ApiOperation(value = "导出机场代码")
    public void exportAirportCode(HttpServletResponse response, AirportCodeQuery query)
    {
        List<BaseAirportCode> list = airportCodeService.selectAirportCodeList(query);
        ExcelUtil<BaseAirportCode> util = new ExcelUtil<BaseAirportCode>(BaseAirportCode.class);
        util.exportExcel(response, list, "机场代码");
    }

    /**
     * 导出机场代码模板
     */
    @PreAuthorize("@ss.hasPermi('base:airportCode:exportAirportCode')")
    @Log(title = "导出机场代码模板", businessType = BusinessType.EXPORT)
    @PostMapping("/exportAirportCodeNull")
    @ApiOperation(value = "导出机场代码模板")
    public void exportAirportCodeNull(HttpServletResponse response)
    {
        List<BaseAirportCode> list = new ArrayList<>();
        ExcelUtil<BaseAirportCode> util = new ExcelUtil<BaseAirportCode>(BaseAirportCode.class);
        util.exportExcel(response, list, "机场代码模板");
    }

    /**
     * 导入机场代码
     */
    @PreAuthorize("@ss.hasPermi('base:airportCode:importAirportCode')")
    @Log(title = "导入机场代码", businessType = BusinessType.IMPORT)
    @PostMapping("/importAirportCode")
    @ApiOperation(value = "导入机场代码")
    public AjaxResult importAirportCode(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<BaseAirportCode> util = new ExcelUtil<BaseAirportCode>(BaseAirportCode.class);
        List<BaseAirportCode> airportCodes = util.importExcel(file.getInputStream());
        String message = airportCodeService.importAirportCode(airportCodes, updateSupport);
        return success(message);
    }

    /**
     * 新增机场代码
     */
    @PreAuthorize("@ss.hasPermi('base:airportCode:addAirportCode')")
    @Log(title = "新增机场代码", businessType = BusinessType.INSERT)
    @PostMapping("/addAirportCode")
    @ApiOperation(value = "新增机场代码")
    public AjaxResult addAirportCode(@RequestBody BaseAirportCode code)
    {
        return toAjax(airportCodeService.addAirportCode(code));
    }

    /**
     * 修改机场代码
     */
    @PreAuthorize("@ss.hasPermi('base:airportCode:editAirportCode')")
    @Log(title = "修改机场代码", businessType = BusinessType.UPDATE)
    @PostMapping("/editAirportCode")
    @ApiOperation(value = "修改机场代码")
    public AjaxResult editAirportCode(@RequestBody BaseAirportCode code)
    {
        return toAjax(airportCodeService.editAirportCode(code));
    }

    /**
     * 删除机场代码
     */
    @PreAuthorize("@ss.hasPermi('base:airportCode:delAirportCode')")
    @Log(title = "删除机场代码", businessType = BusinessType.UPDATE)
    @GetMapping("/delAirportCode/{ids}")
    @ApiOperation(value = "删除机场代码")
    public AjaxResult delAirportCode(@PathVariable("ids") Long[] ids)
    {
        return toAjax(airportCodeService.delAirportCode(ids));
    }
}
