package com.gzairports.common.business.reporter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.reporter.domain.ReportDepSettleCost;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-03
 */
@Mapper
public interface ReportDepSettleCostMapper extends BaseMapper<ReportDepSettleCost> {

    void deleteBatch(@Param("reportIds") List<Long> reportIds);

    int insertBatchSomeColumn(List<ReportDepSettleCost> costList);
}
