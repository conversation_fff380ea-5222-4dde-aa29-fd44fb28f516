package com.gzairports.common.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.BaseBillSource;
import com.gzairports.common.basedata.domain.BaseBillType;
import com.gzairports.common.basedata.mapper.BillSourceMapper;
import com.gzairports.common.basedata.mapper.BillTypeMapper;
import com.gzairports.common.basedata.service.IBillSourceService;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 票证来源Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-17
 */
@Service
public class BillSourceServiceImpl extends ServiceImpl<BillSourceMapper, BaseBillSource> implements IBillSourceService {

    @Autowired
    private BillSourceMapper sourceMapper;

    @Autowired
    private BillTypeMapper typeMapper;

    /**
     * 查询票证来源列表
     *
     * @return 票证来源列表
     */
    @Override
    public List<BaseBillSource> selectList() {
        return sourceMapper.selectBillList();
    }

    /**
     * 查询票证来源详情
     *
     * @param id 票证来源id
     * @return 票证来源详情
     */
    @Override
    public BaseBillSource getInfo(Long id) {
        BaseBillSource baseBillSource = sourceMapper.selectById(id);
        BaseBillType baseBillType = typeMapper.selectById(baseBillSource.getBillTypeId());
        baseBillSource.setTypeName(baseBillType.getName());
        return baseBillSource;
    }

    /**
     * 新增票证来源
     *
     * @param baseBillSource 票证来源
     * @return 结果
     */
    @Override
    public int insertSource(BaseBillSource baseBillSource) {
        //新增票证来源需不需要做一个校验 需要 是票证类型+名称+前缀不能重复
        List<BaseBillSource> baseBillSources = sourceMapper.selectList(new QueryWrapper<BaseBillSource>()
                .eq("bill_type_id", baseBillSource.getBillTypeId())
                .eq("name", baseBillSource.getName())
                .eq("prefix", baseBillSource.getPrefix()));
        if(!CollectionUtils.isEmpty(baseBillSources)){
            throw new RuntimeException("数据重复,请重新输入");
        }
        baseBillSource.setDeptId(SecurityUtils.getHighParentId());
        return sourceMapper.insert(baseBillSource);
    }

    /**
     * 修改票证来源
     *
     * @param baseBillSource 票证来源
     * @return 结果
     */
    @Override
    public int updateSource(BaseBillSource baseBillSource) {
        return sourceMapper.updateById(baseBillSource);
    }

    /**
     * 根据id查询票证类型和前缀
     *
     * @param id 票证来源id
     * @return 结果
     */
    @Override
    public BaseBillSource byId(Long id) {
        BaseBillSource baseBillSource = sourceMapper.selectById(id);
        BaseBillType baseBillType = typeMapper.selectById(baseBillSource.getBillTypeId());
        if (StringUtils.isNotNull(baseBillType)){
            baseBillSource.setTypeName(baseBillType.getName());
        }
        return baseBillSource;
    }

    /**
     * 根据id逻辑删除票证来源
     *
     * @param id 票证来源id
     * @return 结果
     */
    @Override
    public int deleteById(Long id) {
        BaseBillSource baseBillSource = new BaseBillSource();
        baseBillSource.setId(id);
        baseBillSource.setIsDel(1);
        return sourceMapper.updateById(baseBillSource);
    }
}
