package com.gzairports.wl.reporter.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
public class AHIncomeDetailVO {
    /**
     * 主单id
     * */
    private Long id;
    /**
     * 主单运单号
     */
    private String awbaWaybillCode;

    /**
     * 分单运单号
     */
    private String hawbWaybillCode;


    /**
     * 运单号
     */
    private String waybillCode;

    /**
     * 运单类型
     */
//    private String waybillType;

    /**
     * 制单日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date writeTime;

    /**
     * 起点站
     */
    private String sourcePort;

    /**
     * 目的站
     */
    private String desPort;

    /**
     * 件数
     */
    private String quantity;

    /**
     * 重量
     */
    private String weight;

    /**
     * 计费重量
     */
    private String chargeWeight;

    /**
     * 特货代码1
     */
    private String specialCargoCode1;

    /**
     * 特货代码2
     */
    private String specialCargoCode2;

    /**
     * 特货代码3
     */
    private String specialCargoCode3;

    /**
     * 其他特货代码
     */
    private String otherSpecialCargoCode;

    /**
     * 品名
     */
    private String cargoName;

    /**
     * 费用
     */
    private List<ChargeDetailItemVO> chargeDetailItems;


}
