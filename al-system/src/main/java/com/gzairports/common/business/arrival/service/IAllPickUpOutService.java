package com.gzairports.common.business.arrival.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.business.arrival.domain.AllPickUpOut;
import com.gzairports.common.business.arrival.domain.HzArrItem;
import com.gzairports.common.business.arrival.domain.query.AppPickUpQuery;
import com.gzairports.common.business.arrival.domain.query.PickUpOutQuery;
import com.gzairports.common.business.arrival.domain.vo.*;
import com.gzairports.common.core.domain.AjaxResult;

import java.util.List;

/**
 * 国内进港提货出库Service接口
 *
 * <AUTHOR>
 * @date 2024-07-19
 */
public interface IAllPickUpOutService extends IService<AllPickUpOut> {


    /**
     * 提货出库查询
     * @param query 查询条件
     * @return 数据
     */
    PickOrderVo selectList(PickUpOutQuery query);

    /**
     * 提货办单信息
     * @param id 运单id
     * @return 结果
     */
    PickUpOutWaybillVo getInfo(Long id);

    /**
     * 保存提货件数和提货重量
     * @param vo 参数
     * @return 结果
     */
    int savePickUpData(PickUpOutWaybillVo vo);

    /**
     * 提货出库
     * @param pickUpId 办理提货id
     * @return 结果
     */
    PickUpOutVo pickUpOut(Long pickUpId);
    /**
     * 费用明细数据
     * @param waybillCode 进港运单号
     * @param tallyId 理货id
     * @return 结果
     */
    HzArrItemVo cost(String waybillCode, Long tallyId);

    /**
     * 挑单费用明细详情
     * @param id 费用明细id
     * @return 详情
     */
    HzArrItem costInfo(Long id);

    /**
     * 编辑费用明细数据
     * @param item 更新后的费用明细数据
     * @return 结果
     */
    int editCost(HzArrItem item);

    /**
     * 删除费用明细
     * @param id 费用明细id
     * @return 结果
     */
    int delCost(Long id);

    /**
     * 新增费用明细
     * @param item 费用明细数据
     * @return 结果
     */
    int addCost(HzArrItem item);

    /**
     * 计算总费用
     * @param item 计算参数
     * @return 结果
     */
    String countCost(HzArrItem item);

    /**
     * 备出库查询列表
     * @param query 查询参数
     * @return 备出库列表
     */
    List<PickUpVo> planPickUpOut(PickUpOutQuery query);

    /**
     * 已提货出库查询列表
     * @param query 查询条件
     * @return 已提货出库列表
     */
    List<PickUpVo> pickedUp(PickUpOutQuery query);

    /**
     * app提货出库数据
     * @param query 查询条件
     * @return 提货出库数据
     */
    AppPickOutVo appPickUpList(AppPickUpQuery query);

    /**
     * 查询扫描出库数据
     * @param pickUpCode 提货码
     * @return 扫描出库数据
     */
    ScanOutVo scanOut(String pickUpCode);

    /**
     * app提货出库详情
     * @param waybillCode 运单号
     * @param tallyId 理货id
     * @return 详情
     */
    PickUpInfoVo pickUpInfo(String waybillCode, Long tallyId);

    /**
     * app交付
     * @param out 交付数据
     * @return 结果
     */
    int deliver(AllPickUpOut out);

    /**
     *  app快速交付
     * @param query 查询参数
     * @return 快速交付列表
     */
    AppPickOutVo rapidDeliver(AppPickUpQuery query);

    /**
     * app新增快速交付运单号查询
     * @param waybillCode 运单号
     * @return 详情数据
     */
    PickUpInfoVo waybillCodeQuery(String waybillCode);

    /**
     * 根据后四位或者后八位运单号查询
     */
    List<String> checkWaybillCode(String code);

    /**
     * 修改提货信息
     * @param vo 提货信息
     * @return 结果
     */
    int editPickUpData(PickUpOutWaybillVo vo);

    /**
     * 转南航
     * @param pickUpId 提货码
     * @return 结果
     */
    int transferSouthernAirlines(Long pickUpId);


    PickUpOutVo batchPickUpOut(Long pickUpId);
}
