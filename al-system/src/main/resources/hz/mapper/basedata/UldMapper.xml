<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.UldMapper">
    <insert id="addULD" parameterType="BaseCargoUld">
        insert into base_cargo_uld
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="code != null and code != ''">code,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="airCompany != null and airCompany != ''">air_company,</if>
            <if test="entranceFlight != null and entranceFlight != ''">entrance_flight,</if>
            <if test="deadWeight != null">dead_weight,</if>
            <if test="loadWeight != null">load_weight,</if>
            <if test="loadSize != null">load_size,</if>
            <if test="entranceTime != null">entrance_time,</if>
            <if test="exitFlight != null">exit_flight,</if>
            <if test="exitTime != null">exit_time,</if>
            <if test="imageUrl != null and imageUrl != ''">image_url,</if>
            <if test="remark != null and remark != ''">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="code != null and code != ''">#{code},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="airCompany != null and airCompany != ''">#{airCompany},</if>
            <if test="entranceFlight != null and entranceFlight != ''">#{entranceFlight},</if>
            <if test="deadWeight != null">#{deadWeight},</if>
            <if test="loadWeight != null">#{loadWeight},</if>
            <if test="loadSize != null">#{loadSize},</if>
            <if test="entranceTime != null">#{entranceTime},</if>
            <if test="exitFlight != null">#{exitFlight},</if>
            <if test="exitTime != null">#{exitTime},</if>
            <if test="imageUrl != null and imageUrl != ''">#{imageUrl},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
        </trim>
    </insert>

    <update id="editULD" parameterType="BaseCargoUld">
        update base_cargo_uld
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="airCompany != null and airCompany != ''">air_company = #{airCompany},</if>
            <if test="entranceFlight != null and entranceFlight != ''">entrance_flight = #{entranceFlight},</if>
            <if test="deadWeight != null">dead_weight = #{deadWeight},</if>
            <if test="loadWeight != null">load_weight = #{loadWeight},</if>
            <if test="loadSize != null">load_size = #{loadSize},</if>
            <if test="entranceTime != null">entrance_time = #{entranceTime},</if>
            <if test="exitFlight != null">exit_flight = #{exitFlight},</if>
            <if test="exitTime != null">exit_time = #{exitTime},</if>
            <if test="imageUrl != null and imageUrl != ''">image_url = #{imageUrl},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="removeUld">
        update base_cargo_uld set is_del = 1 where id = #{id}
    </update>

    <select id="ULDList" resultType="com.gzairports.common.basedata.domain.BaseCargoUld">
        select id, code, type, air_company, entrance_flight, dead_weight, load_weight, load_size, entrance_time, exit_flight,
               exit_time, image_url, remark, create_time  from base_cargo_uld
        <where>
        and is_del = 0
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="code != null  and code != ''"> and code like CONCAT('%',#{code},'%')</if>
            <if test="type != null and type != ''"> and type = #{type}</if>
            <if test="airCompany != null and airCompany != ''"> and air_company like CONCAT('%',#{airCompany},'%')</if>
            <if test="entranceTime != null and entranceTime != ''">
                AND DATE_FORMAT(entrance_time,'%Y%m%d') <![CDATA[>=]]> DATE_FORMAT(#{entranceTime, jdbcType=DATE},'%Y%m%d')
            </if>
            <if test="exitTime != null and exitTime != ''">
                AND DATE_FORMAT(exit_time,'%Y%m%d') <![CDATA[<=]]> DATE_FORMAT(#{exitTime, jdbcType=DATE},'%Y%m%d')
            </if>
        </where>
    </select>
    <select id="getInfo" resultType="com.gzairports.common.basedata.domain.BaseCargoUld">
        select id, code, type, air_company, entrance_flight, dead_weight, load_weight, load_size, entrance_time, exit_flight,
               exit_time, image_url, remark, create_time  from base_cargo_uld where id = #{id} and is_del = 0
    </select>
    <select id="selectByCode" resultType="com.gzairports.common.basedata.domain.BaseCargoUld">
        select id, code, type, air_company, entrance_flight, dead_weight, load_weight, load_size, entrance_time, exit_flight,
               exit_time, image_url, remark, create_time  from base_cargo_uld where code = #{code} and is_del = 0
    </select>
    <select id="selectUldList" resultType="com.gzairports.hz.business.cable.domain.vo.CableUldVo">
        select bcu.id, bcu.code, bcu.type, bcu.air_company, bcu.entrance_flight, bcu.entrance_time, bcu.exit_flight, bcu.exit_time
        from base_cargo_uld bcu
        <where>
            bcu.is_del = 0
            <if test="inOut == 'IN'">
                and bcu.exit_time is null
                <if test="flightNo != null and flightNo != ''">
                    AND bcu.entrance_flight like '%${flightNo}%'
                </if>
                <if test="queryDate != null and queryDate != ''">
                    AND DATE_FORMAT(bcu.entrance_time,'%Y-%m-%d') = #{queryDate}
                </if>
            </if>
            <if test="inOut == 'OUT'">
                and bcu.exit_time is not null
                <if test="flightNo != null and flightNo != ''">
                    AND bcu.exit_flight like '%${flightNo}%'
                </if>
                <if test="queryDate != null and queryDate != ''">
                    AND DATE_FORMAT(bcu.exit_time,'%Y-%m-%d') = #{queryDate}
                </if>
            </if>
            <if test="inOut != 'OUT' and inOut != 'IN'">
                <if test="flightNo != null and flightNo != ''">
                    AND ( bcu.exit_flight like '%${flightNo}%' or  bcu.entrance_flight like '%${flightNo}%' )
                </if>
                <if test="queryDate != null and queryDate != ''">
                    AND ( DATE_FORMAT(bcu.entrance_time,'%Y-%m-%d') = #{queryDate} or DATE_FORMAT(bcu.exit_time,'%Y-%m-%d') = #{queryDate} )
                </if>
                <if test="airCompany != null and airCompany != ''">
                    AND bcu.air_company = #{airCompany}
                </if>
            </if>
        </where>
    </select>
    <select id="selectOneByUld" resultType="java.lang.Integer">
        select count(1) from base_cargo_uld where code = #{uld}
    </select>
    <select id="selectUldByFlightId" resultType="com.gzairports.hz.business.cable.domain.vo.UldVO">
        (select 'BLK' as uldNo
        from hz_flight_load_waybill hflw
            left join hz_flight_load hfl on hfl.id = hflw.flight_load_id
        where hfl.flight_id = #{flightId}
        <if test="waybillIds != null and waybillIds.size() > 0">
            and hflw.waybill_id in
            <foreach collection="waybillIds" item="waybillId" open="(" separator="," close=")">
                #{waybillId}
            </foreach>
        </if>
        group by uldNo
        )
        UNION ALL
        (select uld as uldNo
        from hz_flight_load_uld_waybill hfluw
            left join hz_flight_load_uld hflu on hfluw.load_uld_id = hflu.id
            left join hz_flight_load hfl on hfl.id = hflu.flight_load_id
        where hfl.flight_id = #{flightId}
        <if test="waybillIds != null and waybillIds.size() > 0">
            and hfluw.waybill_id in
            <foreach collection="waybillIds" item="waybillId" open="(" separator="," close=")">
                #{waybillId}
            </foreach>
        </if>
        group by uldNo
        )
    </select>
</mapper>