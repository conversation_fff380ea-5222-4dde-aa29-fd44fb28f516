package com.gzairports.wl.departure.domain.query;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 待运查询入参
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AwaitTransportWaybillQuery extends BasePageQuery{

    /** 制单时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /** 制单时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 运单号
     */
    private String waybillCode;

    /**
     * 发货人
     */
    private String shipper;
}
