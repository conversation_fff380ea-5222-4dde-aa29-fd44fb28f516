package com.gzairports.web.controller.basedata;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.domain.entity.SysDept;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.basedata.domain.BaseBillType;
import com.gzairports.common.basedata.service.IBillTypeService;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 票证管理Controller
 *
 * <AUTHOR>
 * @date 2024-05-17
 */
@RestController
@RequestMapping("/base/billType")
@Api(tags = "票证管理数据接口")
public class BillTypeController extends BaseController {

    @Autowired
    private IBillTypeService typeService;
    @Autowired
    private SysDeptMapper deptMapper;



    /**
     * 查询最父级的部门名称
     */
    //SysProfileController无法编译,暂时放在这个接口
    @GetMapping("/getParentDeptName")
    public AjaxResult getParentDeptName()
    {
        Long deptId = SecurityUtils.getHighParentId();
//        while(true){
//            Long parentId = deptMapper.selectParentIdByDeptId(deptId);
//            if (StringUtils.isNotNull(parentId)){
//                deptId = parentId;
//                if (deptMapper.selectParentIdByDeptId(deptId) == 0){
//                    break;
//                }
//            }
//        }
        SysDept sysDept = deptMapper.selectDeptById(deptId);
        sysDept.setUserName(SecurityUtils.getNickName());
        return AjaxResult.success("操作成功", sysDept);
    }

    /**
     * 查询票证管理列表
     */
//    @PreAuthorize("@ss.hasPermi('base:billType:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询票证管理列表")
    public TableDataInfo list()
    {
        startPage();
        List<BaseBillType> list = typeService.selectList();
        return getDataTable(list);
    }

    /**
     * 票证管理详情
     */
    @PreAuthorize("@ss.hasPermi('base:billType:getInfo')")
    @GetMapping("/{id}")
    @ApiOperation(value = "票证管理详情")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(typeService.getInfo(id));
    }

    /**
     * 新增票证管理
     */
    @PreAuthorize("@ss.hasPermi('base:billType:add')")
    @PostMapping
    @ApiOperation(value = "新增票证管理")
    public AjaxResult add(@RequestBody BaseBillType baseBillType)
    {
        return toAjax(typeService.insertType(baseBillType));
    }

    /**
     * 修改票证管理
     */
    @PreAuthorize("@ss.hasPermi('base:billType:edit')")
    @PutMapping
    @ApiOperation(value = "修改票证管理")
    public AjaxResult edit(@RequestBody BaseBillType baseBillType)
    {
        return toAjax(typeService.updateType(baseBillType));
    }
}
