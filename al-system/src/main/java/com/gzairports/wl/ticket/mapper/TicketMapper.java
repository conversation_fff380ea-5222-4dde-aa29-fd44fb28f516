package com.gzairports.wl.ticket.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.wl.ticket.domain.Ticket;
import com.gzairports.wl.ticket.domain.query.TicketNumQuery;
import com.gzairports.wl.ticket.domain.query.TicketQuery;
import com.gzairports.wl.ticket.domain.vo.TicketInfoVO;
import com.gzairports.wl.ticket.domain.vo.TicketDetailVO;
import com.gzairports.wl.ticket.domain.vo.TicketVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 票证信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@Mapper
public interface TicketMapper extends BaseMapper<Ticket> {

    /**
     * 查询单证信息列表列表
     *
     * @param query 查询参数
     * @return 单证信息列表列表
     */
    List<TicketVO> selectListByQuery(TicketQuery query);

    /**
     * 根据id查询单证信息
     *
     * @param id 单证id
     * @return 结果
     */
    TicketInfoVO getInfo(Long id);

    /**
     * 修改单证备注
     *
     * @param query 票证id
     */
    void editRemark(TicketNumQuery query);

    /**
     * 查询单证详情
     *
     * @param id 查询参数
     * @return 单证信息详情
     */
    TicketDetailVO selectDetail(Long id);

    /**
     * 取消入库
     *
     * @param query 取消入库参数
     * @return 结果
     */
    int warehouse(TicketNumQuery query);

    /**
     * 删除单证
     *
     * @param id 单证id
     * @return 结果
     */
    int removeByTicketId(Long id);

    /**
     * 根据运单类型 运单前缀 票号查询单证信息
     *
     * @param ticketType 运单类型
     * @param prefix 运单前缀
     * @param ticketNum 票号
     * @return 单证信息
     */
    default Long selectCheck(@Param("ticketType") String ticketType, @Param("prefix") String prefix, @Param("ticketNum") Integer ticketNum) {
        return selectCheck(ticketType, prefix, ticketNum, SecurityUtils.getDeptId());
    }

    /**
     * 根据运单类型 运单前缀 票号查询单证信息
     *
     * @param ticketType 运单类型
     * @param prefix 运单前缀
     * @param ticketNum 票号
     * @return 单证信息
     */
    Long selectCheck(@Param("ticketType") String ticketType, @Param("prefix") String prefix, @Param("ticketNum") Integer ticketNum, @Param("deptId") Long deptId);

    /**
     * 根据票号查询单证信息
     * */
    Long selectCheckHawb(Integer ticketNum);
    /**
     * 根据部门id查询单证
     *
     * @param deptId 运单类型
     * @return 单证信息
     */
    List<TicketVO> selectListByDeptId(Long deptId);

    List<Ticket>  selectRepeatTicket(Ticket ticket);

    Long selectHawbCheck(@Param("code") String code,@Param("ticketNum") Integer ticketNum);


}
