package com.gzairports.common.basedata.domain;

import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 多重量单位
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Data
public class BaseWeight {

    /** 主键id */
    private Long id;

    /** 重量单位 */
    @Excel(name = "重量单位")
    private String weightUnit;

    /** 中文名称 */
    @Excel(name = "中文名称")
    private String chineseName;

    /** 英文名称 */
    @Excel(name = "英文名称")
    private String englishName;

    /** 与公斤转换率 */
    @Excel(name = "与公斤转换率")
    private BigDecimal conversionRate;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;
}
