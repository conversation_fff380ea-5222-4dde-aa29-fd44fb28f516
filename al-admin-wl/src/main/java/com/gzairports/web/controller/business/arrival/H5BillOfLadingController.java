package com.gzairports.web.controller.business.arrival;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.basedata.domain.BaseAgent;
import com.gzairports.common.basedata.mapper.BaseAgentMapper;
import com.gzairports.common.business.arrival.domain.query.BillOfLadingQuery;
import com.gzairports.common.business.arrival.domain.query.PickUpClickQuery;
import com.gzairports.common.business.arrival.domain.query.PickUpQuery;
import com.gzairports.common.business.arrival.domain.vo.PickOrderVo;
import com.gzairports.common.business.arrival.domain.vo.PickUpListVo;
import com.gzairports.common.business.arrival.service.IPickUpService;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * 代理人H5提货办单
 *
 * <AUTHOR>
 * @date 2024-08-06
 */
@RestController
@RequestMapping("/arrH5/billOfLading")
public class H5BillOfLadingController extends BaseController {

    @Autowired
    private IPickUpService pickUpService;

    @Autowired
    private BaseAgentMapper baseAgentMapper;

    /**
     * 查询未提货办单数据
     */
    @PreAuthorize("@ss.hasPermi('arrH5:billOfLading:billList')")
    @PostMapping("/billList")
    @ApiOperation(value = "H5提货办单列表")
    public AjaxResult billList(@RequestBody BillOfLadingQuery query){
        Long deptId = SecurityUtils.getHighParentId();
        query.setDeptId(deptId);
        String deptIds = null;
        BaseAgent baseAgent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", deptId));
        if(baseAgent != null){
            if(StringUtils.isNotEmpty(baseAgent.getDeptIds())){
                deptIds = baseAgent.getDeptIds();
            }
        }
        return AjaxResult.success(pickUpService.billList(query,deptIds));
    }

    /**
     * 挑单
     */
    @PreAuthorize("@ss.hasPermi('arrH5:billOfLading:one')")
    @PostMapping("/one")
    @ApiOperation(value = "挑单")
    public AjaxResult one(@RequestBody PickUpClickQuery query){
        Long deptId = SecurityUtils.getHighParentId();
        query.setDeptId(deptId);
        String deptIds = null;
        BaseAgent baseAgent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", deptId));
        if(baseAgent != null){
            if(StringUtils.isNotEmpty(baseAgent.getDeptIds())){
                deptIds = baseAgent.getDeptIds();
            }
        }
        query.setType("wl");
        return AjaxResult.success(pickUpService.one(query,deptIds));
    }

    /**
     * 批量挑单
     */
    @PreAuthorize("@ss.hasPermi('arrH5:billOfLading:batch')")
    @PostMapping("/batch")
    @ApiOperation(value = "批量挑单")
    public AjaxResult batch(@RequestBody PickUpQuery query){
        if (query.getDeptId() == null) {
            Long deptId = SecurityUtils.getHighParentId();
            query.setDeptId(deptId);
        }
        String deptIds = null;
        BaseAgent baseAgent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", query.getDeptId()));
        if(baseAgent != null){
            if(StringUtils.isNotEmpty(baseAgent.getDeptIds())){
                deptIds = baseAgent.getDeptIds();
            }
        }
        //前端默认传了一个有值的agent
        query.setAgent(null);
        return AjaxResult.success(pickUpService.batch(query,deptIds));
    }

    /**
     * 费用明细
     */
    @PreAuthorize("@ss.hasPermi('arrH5:billOfLading:cost')")
    @GetMapping("/cost")
    @ApiOperation(value = "费用明细")
    public AjaxResult cost(@NotNull @RequestParam String waybillCode, @NotNull @RequestParam Long tallyId)
    {
        return AjaxResult.success(pickUpService.cost(waybillCode,tallyId));
    }

    /**
     * 费用明细详情
     */
    @PreAuthorize("@ss.hasPermi('arrH5:billOfLading:costInfo')")
    @GetMapping("/costInfo/{id}")
    @ApiOperation(value = "费用明细详情")
    public AjaxResult costInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(pickUpService.costInfo(id));
    }

    /**
     * 保存
     */
    @PreAuthorize("@ss.hasPermi('arrH5:billOfLading:add')")
    @PostMapping("/add")
    @ApiOperation(value = "保存")
    public AjaxResult add(@RequestBody PickOrderVo vo)
    {
        vo.setDeptId(SecurityUtils.getHighParentId());
        vo.setType("wl");
        return AjaxResult.success(pickUpService.add(vo));
    }

    /**
     * 流水号查询
     */
    @PreAuthorize("@ss.hasPermi('arrH5:billOfLading:serial')")
    @GetMapping("/serial/{serialNo}")
    @ApiOperation(value = "流水号查询")
    public AjaxResult serial(@PathVariable("serialNo") String serialNo){
        Long deptId = SecurityUtils.getHighParentId();
        return AjaxResult.success(pickUpService.serialNo(serialNo,deptId));
    }

    /**
     * 加入提货列表
     */
    @PreAuthorize("@ss.hasPermi('arrH5:billOfLading:pickList')")
    @PostMapping("/pickList")
    @ApiOperation(value = "加入提货列表")
    public AjaxResult pickList(@RequestBody PickUpListVo vo){
        vo.setType("wl");
        return AjaxResult.success(pickUpService.pickList(vo));
    }


    /**
     * 结算
     */
    @PreAuthorize("@ss.hasPermi('arrH5:billOfLading:settle')")
    @GetMapping("/settle/{waybillCode}")
    @ApiOperation(value = "结算")
    public AjaxResult settle(@PathVariable("waybillCode") String waybillCode)
    {
        return AjaxResult.success(pickUpService.settle(waybillCode, "WL"));
    }

    /**
     * 余额支付
     */
    @PreAuthorize("@ss.hasPermi('arrH5:billOfLading:balance')")
    @PostMapping("/balance")
    @ApiOperation(value = "保存")
    public AjaxResult balance(@RequestBody PickOrderVo vo)
    {
        vo.setDeptId(SecurityUtils.getHighParentId());
        vo.setType("wl");
        return AjaxResult.success(pickUpService.balance(vo));
    }

    /**
     *
     * 生成提货码
     */
    @GetMapping("/getPickUpCode/{pickUpId}")
    @ApiOperation(value = "生成提货码")
    public AjaxResult printOutOrder(@PathVariable Long pickUpId){
        return AjaxResult.success(pickUpService.printOutOrder(pickUpId));
    }

//    /**
//     * 查询运单列表
//     */
//    @GetMapping("/list/waybill")
//    @ApiOperation(value = "查询运单列表")
//    public AjaxResult listWaybill(WayBillH5Query query){
//        return AjaxResult.success(pickUpService.listWaybill(query));
//    }

}
