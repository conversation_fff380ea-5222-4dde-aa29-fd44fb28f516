package com.gzairports.common.securitySubmit.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lan
 * @Desc: 新增安检申报
 * @create: 2024-12-16 13:38
 **/
@Data
@TableName("all_security_waybill")
public class AllSecurityWaybill {
    /** 主键id */
    private Long id;
    /** 运单id */
    private Long waybillId;
    /** 运单号 */
    private String waybillCode;
    /** 品名 */
    private String cargoName;
    /** 代理人 */
    private String agent;
    /** 托运人 */
    private String shipper;
    /** 安检申报单 */
    private String securityUrl;
    /** 是否与申报一致 0 否 1 是  -> 审单结果 0退回 1符合运输 */
    private Integer declarationConsistent;
    /** 是否审核 0 否 1 是 */
    private Integer isExamine;
    /** 最终安检提交状态 0 未提交 1 已提交  -1退回 -2不合格 */
    private Integer securitySubmit;
    /** 物流端安检提交状态 0 未提交 1 物流提交 2货站提交 -1退回 -2不合格 */
    private Integer securitySubmitWl;
    /** 货站选择安检提交时的操作人 */
    private String securitySubmitOperator;
    /** 代理人签章 */
    private String agentSignature;
    /** 托运人签章 */
    private String shipperSignature;
    /** 交货人信息 身份证 */
    private String deliveryIdNo;
    /** 交货人信息 头像拍照 */
    private String deliveryProfilePhoto;
    /** 交货人信息 随附文件拍照 */
    private String deliveryFilePhoto;
    /** 始发站 */
    private String sourcePort;
    /** 目的站 */
    private String desPort;
    /** 航班号 */
    private String flightNo1;
    /** 航班时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date flightDate1;
    /** 件数 */
    private Integer quantity;
    /** 重量 */
    private BigDecimal weight;
    /** 创建人 */
    private String createBy;
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /** 部门id */
    private Long deptId;
    /** 货物类型  0普货 1特货 2危险品  */
    private Integer cargoType;
    /** 是否特货 */
    private Integer isSpecial;
}
