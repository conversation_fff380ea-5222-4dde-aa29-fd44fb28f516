package com.gzairports.hz.business.departure.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 货站出港组货表
 *
 * <AUTHOR>
 * @date 2024-07-03
 */
@Data
@TableName("hz_dep_group")
public class HzDepGroup {

    /** 主键id */
    private Long id;

    /** 板箱号 */
    private String uld;

    /** 板箱id */
    private Long oldUldId;

    /** 主单id */
    private Long waybillId;

    /** 组货件数 */
    private Integer groupPrice;

    /** 组货重量 */
    private BigDecimal groupWeight;

    /** 是否入库 0 否 1 是 */
    private Integer isStore;
}
