package com.gzairports.common.serviceRequest.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 服务申请查询参数
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Data
public class ServiceRequestQuery {

    /** 服务编号 */
    private String serviceNo;

    /** 运单号 */
    private String waybillCode;

    /** 服务项目 */
    private String serviceItem;

    /** 状态 */
    private String status;

    /** 代理人 */
    private String agent;

    /** 日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 所属单位 */
    private Long deptId;

    /** 系统状态 */
    private String sysStatus;

    /**页数*/
    private Integer startRow;

    /**条数*/
    private Integer endRow;
}
