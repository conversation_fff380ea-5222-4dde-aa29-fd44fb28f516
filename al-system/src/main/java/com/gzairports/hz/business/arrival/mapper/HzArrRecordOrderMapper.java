package com.gzairports.hz.business.arrival.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gzairports.common.business.departure.domain.vo.FlightVo;
import com.gzairports.common.business.reporter.domain.ReportNodeQuery;
import com.gzairports.hz.business.arrival.domain.HzArrRecordOrder;
import com.gzairports.hz.business.arrival.domain.query.FlightFileQuery;
import com.gzairports.hz.business.arrival.domain.query.NodeQuery;
import com.gzairports.hz.business.arrival.domain.query.WaybillQuery;
import com.gzairports.hz.business.arrival.domain.vo.*;
import com.gzairports.hz.business.cable.domain.vo.ConsignmentDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 进港录单Mapper接口
 *
 * <AUTHOR>
 * @date 2023-07-25
 */
@Mapper
public interface HzArrRecordOrderMapper extends BaseMapper<HzArrRecordOrder> {

    /**
     * 查询运单列表
     * @param legId 查询条件
     * @param waybillIds 运单id条件
     * @param status 状态条件
     * @return 运单列表
     */
    List<FlightFileWaybillVo> waybillList(@Param("legId") Long legId, @Param("waybillIds") List<Long> waybillIds, @Param("status") String status);

    /**
     * 查询理货运单详情
     * @param waybillCode 运单号
     * @param legId 航段id
     * @return 理货详情
     */
    TallyWaybillVo getTallyInfo(@Param("waybillCode") String waybillCode, @Param("legId") Long legId);

    /**
     * 查询录单数据
     * @param query 查询条件
     * @return 结果
     */
    List<WaybillQueryVo> selectQueryList(WaybillQuery query);

    /**
     * 查询录单时间和航班信息
     * @param waybillCode 运单号
     * @return 结果
     */
    List<ArrFlightVo> selectArrFlights(String waybillCode);

    /**
     * 运单保障节点查询列表数据
     * @param query 查询条件
     * @return 运单保障节点列表
     */
    IPage<NodeWaybillVo> selectNodeList(@Param("query") NodeQuery query, @Param("page") IPage<NodeWaybillVo> page);

    /**
     * 根据运单号查询进港航班
     * @param waybillCode 运单号
     * @return 进港航班数据
     */
    List<String> selectListByCode(String waybillCode);

    /**
     * 根据航班id查询运单列表
     * @param legIds 航班id
     * @return 运单列表
     */
    List<AppWaybillListVo> selectWaybillList(@Param("legIds") List<Long> legIds);

    /**
     * 根据录单id查询运单详情
     * @param orderId 录单id
     * @return 运单详情
     */
    EnterWaybillVo getWaybillInfo(Long orderId);

    /**
     * 根据信息查询录单id
     * @param desPort 目的站
     * @param waybillCode 运单号
     * @return 结果
     */
    HzArrRecordOrder selectId(@Param("desPort") String desPort,@Param("waybillCode") String waybillCode);

    /**
     * 根据运单id查询进港航班号
     * @param waybillId 运单id
     * @return 航班号列表
     */
    List<String> selectFlightList(Long waybillId);

    /**
     * 根据orderId查询航班信息
     * @param orderId 录单id
     * @return 结果
     */
    FlightVo selectFlightById(Long orderId);

    List<ReportNodeQuery> selectReportNodeList(@Param("lastSyncTime") Date lastSyncTime,@Param("dateNow") Date dateNow);

    HzArrRecordOrder selectByTallyId(Long tallyId);

    List<ConsignmentDetail> selectDetailList(@Param("legIds") List<Long> legId);

    int selectCountByLegId(@Param("waybillCode") String waybillCode,@Param("legId") Long legId);

    int updateExamine(Long orderId);

    int cancelExamine(Long orderId);

    List<FlightFileWaybillVo> waybillListQuery(FlightFileQuery query);
}
