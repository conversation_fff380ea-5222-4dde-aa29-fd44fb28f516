package com.gzairports.common.basedata.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.basedata.domain.BaseBalance;
import com.gzairports.hz.business.departure.domain.query.BillExportQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 代理人配置余额明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-17
 */
@Mapper
public interface BaseBalanceMapper extends BaseMapper<BaseBalance>
{

    /**
     * 新增代理人配置余额明细
     * 
     * @param baseBalance 代理人配置余额明细
     * @return 结果
     */
    public int insertBaseBalance(BaseBalance baseBalance);

    List<String> selectNotWaybillCode(@Param("query") BillExportQuery query,@Param("id") Long id);
}
