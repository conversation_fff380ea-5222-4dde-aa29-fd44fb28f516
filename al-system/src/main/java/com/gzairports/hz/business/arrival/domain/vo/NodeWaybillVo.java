package com.gzairports.hz.business.arrival.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 运单节点查询列表数据
 * <AUTHOR>
 * @date 2024-07-26
 */
@Data
public class NodeWaybillVo {

    /** 理货id */
    private Long tallyId;

    /** 入库时间 */
    @Excel(name = "入库时间",dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;

    /** 航班号 */
    @Excel(name = "航班号")
    private String flightNo;

    /** 航班日期 */
    private Date execDate;

    /** 运单号 */
    @Excel(name = "运单号")
    private String waybillCode;

    /** 收货代理人 */
    @Excel(name = "收货代理人")
    private String consign;

    /** 件数 */
    @Excel(name = "件数")
    private Integer quantity;

    /** 重量 */
    @Excel(name = "重量")
    private BigDecimal weight;

    /** 总耗时（分） */
    @Excel(name = "总耗时（分）")
    private Long totalTime;

    /** 降落时间 */
    @Excel(name = "降落时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date terminalAlteratelAndInTime;

    /** 站坪交接时间 */
    @Excel(name = "站坪交接时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date handoverTime;

    /** 站坪耗时（分） */
    @Excel(name = "站坪耗时（分）")
    private Long siteConsume;

    /** 理货完成时间 */
    @Excel(name = "理货完成时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date tallyTime;

    /** 理货耗时（分） */
    @Excel(name = "理货耗时（分）")
    private Long tallyConsume;

    /** 提货办单时间 */
    @Excel(name = "提货办单时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date pickUpTime;

    /** 办单耗时（分） */
    @Excel(name = "办单耗时（分）")
    private Long pickUpConsume;

    /** 交付时间 */
    @Excel(name = "交付时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date outTime;

    /** 交付耗时 */
    @Excel(name = "交付耗时")
    private Long outConsume;

    /** 制单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date writeTime;
}
