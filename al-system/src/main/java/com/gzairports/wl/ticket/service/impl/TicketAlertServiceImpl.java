package com.gzairports.wl.ticket.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.core.domain.entity.SysDept;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.wl.ticket.domain.TicketAlert;
import com.gzairports.wl.ticket.domain.TicketNum;
import com.gzairports.wl.ticket.domain.query.TicketQuery;
import com.gzairports.wl.ticket.domain.vo.TicketVO;
import com.gzairports.wl.ticket.mapper.TicketAlertMapper;
import com.gzairports.wl.ticket.mapper.TicketMapper;
import com.gzairports.wl.ticket.mapper.TicketNumMapper;
import com.gzairports.wl.ticket.service.TicketAlertService;
import com.gzairports.wl.websocket.WlWebSocketServer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 单证预警管理service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Service
public class TicketAlertServiceImpl extends ServiceImpl<TicketAlertMapper, TicketAlert> implements TicketAlertService {

    @Autowired
    private TicketAlertMapper alertMapper;

    @Autowired
    private TicketMapper ticketMapper;

    @Autowired
    private TicketNumMapper numMapper;

    @Autowired
    private WlWebSocketServer webSocketServer;

    @Autowired
    private SysDeptMapper deptMapper;

    /**
     * 查询单证预警列表
     *
     * @param query 查询参数
     * @return 单证预警列表
     */
    @Override
    public List<TicketAlert> selectList(TicketQuery query) {
        query.setDeptId(SecurityUtils.getHighParentId());
        return alertMapper.selectListByQuery(query);
    }

    /**
     * 单证预警新增
     *
     * @param alert 新增参数
     * @return 结果
     */
    @Override
    public int add(TicketAlert alert) {
        Long highParentId = SecurityUtils.getHighParentId();
        alert.setDeptId(highParentId);
        TicketAlert list = alertMapper.selectOne(new QueryWrapper<TicketAlert>()
                .eq("bill_source", alert.getBillSource())
                .eq("bill_type",alert.getBillType())
                .eq("bill_prefix",alert.getBillPrefix())
                .eq("is_chartering", alert.getIsChartering())
                .eq("dept_id", highParentId)
                .eq("is_del",0));
        if (StringUtils.isNotNull(list)){
            throw new CustomException("当前已存在相同预警");
        }
        return alertMapper.insert(alert);
    }

    /**
     * 单证预警修改
     *
     * @param alert 修改参数
     * @return 结果
     */
    @Override
    public int edit(TicketAlert alert) {
        Long highParentId = SecurityUtils.getHighParentId();
        alert.setDeptId(highParentId);
        TicketAlert list = alertMapper.selectOne(new QueryWrapper<TicketAlert>()
                .eq("bill_source", alert.getBillSource())
                .eq("bill_type",alert.getBillType())
                .eq("bill_prefix",alert.getBillPrefix())
                .eq("is_chartering", alert.getIsChartering())
                .eq("dept_id", highParentId)
                .eq("is_del",0));
        if (list != null && !list.getId().equals(alert.getId())){
            throw new CustomException("当前已存在相同预警");
        }
        return alertMapper.updateById(alert);
    }

    /**
     * 单证预警删除
     *
     * @param id 单证预警id
     * @return 结果
     */
    @Override
    public int removeAlert(Long id) {
        return alertMapper.remove(id);
    }

    /**
     * 查看预警详情
     *
     * @param id 预警id
     * @return 预警详情
     */
    @Override
    public TicketAlert getInfo(Long id) {
        return alertMapper.getInfo(id);
    }

    /**
     * 票证预警
     *
     */
    @Override
    public void alert() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long deptId = loginUser.getDeptId();
        SysDept sysDept = deptMapper.selectDeptById(deptId);
        List<TicketVO> tickets = ticketMapper.selectListByDeptId(deptId);
        if (!CollectionUtils.isEmpty(tickets)){
            for (TicketVO ticket : tickets) {
                TicketAlert alert = alertMapper.selectByBillId(deptId,ticket.getBillSource(),ticket.getIsChartering());
                if (alert != null){
                    Integer integer = numMapper.selectCount(new QueryWrapper<TicketNum>().eq("ticket_id", ticket.getId()).ne("status","NOTGRANT"));
                    //对每个条目根据范围和使用数量，计算出总数量和剩余数量
                    Integer end = ticket.getEndNumber();
                    Integer start = ticket.getStartNumber();

                    Integer quantity = end != null && start != null ? end - start + 1 : null;
                    Integer remainQuantity = quantity != null && integer != null ? quantity - integer : 0;
                    if (alert.getNumberAlert() > remainQuantity){
                        webSocketServer.sendAll(sysDept.getDeptName() + "，" + "国内主单【"+alert.getBillPrefix()+" / "+alert.getBillSource()+"公司】 " +
                                "剩余未使用票存低于预警值"+alert.getNumberAlert()+"");
                    }
                }
            }
        }
    }

}
