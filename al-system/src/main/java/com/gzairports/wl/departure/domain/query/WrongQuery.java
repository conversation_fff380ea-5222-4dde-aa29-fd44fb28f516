package com.gzairports.wl.departure.domain.query;

import lombok.Data;

import java.util.Date;

/**
 * 不正常货运查询参数
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@Data
public class WrongQuery {

    /** 登记时间 */
    private Date startTime;

    /** 登记时间 */
    private Date endTime;

    /** 运单号 */
    private String waybillCode;

    /** 不正常类型 */
    private String wrongType;

    /** 状态 */
    private Integer status;

    /** 所属单位 */
    private Long deptId;

    /** 处理方式 0继续运输 1换单 2退货 */
    private String proMethod;

    /** 进出港类型 ARR-进港 DEP-出港 */
    private String type;

}
