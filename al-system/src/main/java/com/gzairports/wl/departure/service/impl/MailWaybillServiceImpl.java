package com.gzairports.wl.departure.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.*;
import com.gzairports.common.basedata.mapper.*;
import com.gzairports.common.business.arrival.mapper.FlightInfoMapper;
import com.gzairports.common.business.arrival.mapper.FlightLoadWaybillMapper;
import com.gzairports.common.business.departure.domain.*;
import com.gzairports.common.business.departure.domain.vo.LoadInfoVo;
import com.gzairports.common.business.departure.mapper.CostDetailMapper;
import com.gzairports.common.business.departure.mapper.MailWaybillMapper;
import com.gzairports.common.business.departure.mapper.WaybillFeeMapper;
import com.gzairports.common.business.departure.service.impl.WaybillTraceServiceImpl;
import com.gzairports.common.charge.domain.HzChargeIrRelation;
import com.gzairports.common.charge.domain.HzChargeItemRule;
import com.gzairports.common.charge.domain.HzChargeItems;
import com.gzairports.common.charge.domain.HzChargeRule;
import com.gzairports.common.charge.domain.vo.BillRuleVo;
import com.gzairports.common.charge.domain.vo.IrRelationVo;
import com.gzairports.common.charge.mapper.HzChargeIrRelationMapper;
import com.gzairports.common.charge.mapper.HzChargeItemRuleMapper;
import com.gzairports.common.charge.mapper.HzChargeItemsMapper;
import com.gzairports.common.charge.mapper.HzChargeRuleMapper;
import com.gzairports.common.charge.service.BillingRule;
import com.gzairports.common.charge.service.impl.BillingRuleFactory;
import com.gzairports.common.core.domain.entity.SysDept;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.common.log.service.IWaybillLogService;
import com.gzairports.common.pdf.PdfPrintHelper;
import com.gzairports.common.securitySubmit.domain.AllSecurityWaybill;
import com.gzairports.common.securitySubmit.mapper.AllSecurityWaybillMapper;
import com.gzairports.common.system.domain.SysConfig;
import com.gzairports.common.system.mapper.SysConfigMapper;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.utils.BigDecimalRoundUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.ServletUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.hz.business.departure.domain.HzCollectWaybill;
import com.gzairports.hz.business.departure.domain.HzCollectWeight;
import com.gzairports.hz.business.departure.mapper.HzCollectWaybillMapper;
import com.gzairports.hz.business.departure.mapper.HzCollectWeightMapper;
import com.gzairports.wl.charge.domain.ChargeItem;
import com.gzairports.wl.charge.domain.MailPrice;
import com.gzairports.wl.charge.domain.MailPriceItem;
import com.gzairports.wl.charge.mapper.ChargeItemMapper;
import com.gzairports.wl.charge.mapper.MailPriceItemMapper;
import com.gzairports.wl.charge.mapper.MailPriceMapper;
import com.gzairports.wl.departure.domain.query.FareQuery;
import com.gzairports.wl.departure.domain.query.HawbQuery;
import com.gzairports.wl.departure.domain.vo.*;
import com.gzairports.wl.departure.service.IMailWaybillService;
import com.gzairports.wl.ticket.domain.TicketNum;
import com.gzairports.wl.ticket.mapper.TicketMapper;
import com.gzairports.wl.ticket.mapper.TicketNumMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 邮件单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class MailWaybillServiceImpl extends ServiceImpl<MailWaybillMapper, MailWaybill> implements IMailWaybillService {

    @Autowired
    private TicketNumMapper ticketNumMapper;

    @Autowired
    private TicketMapper ticketMapper;

    @Autowired
    private MailWaybillMapper mailWaybillMapper;

    @Autowired
    private WaybillFeeMapper feeMapper;

    @Autowired
    private MailPriceMapper priceMapper;

    @Autowired
    private MailPriceItemMapper itemMapper;

    @Autowired
    private ChargeItemMapper chargeItemMapper;

    @Autowired
    private PostMapper postMapper;

    @Autowired
    private HzChargeItemsMapper itemsMapper;

    @Autowired
    private SysConfigMapper sysConfigMapper;

    @Autowired
    private HzChargeRuleMapper ruleMapper;

    @Autowired
    private HzChargeItemRuleMapper itemRuleMapper;

    @Autowired
    private HzChargeIrRelationMapper relationMapper;

    @Autowired
    private CostDetailMapper costDetailMapper;

    @Autowired
    private CargoCodeMapper cargoCodeMapper;

    @Autowired
    private AirportCodeMapper airportCodeMapper;

    @Autowired
    private FlightInfoMapper flightInfoMapper;

    @Autowired
    private AllSecurityWaybillMapper allSecurityWaybillMapper;

    @Autowired
    private SysConfigMapper configMapper;

    @Autowired
    private SysDeptMapper deptMapper;

    @Autowired
    private WaybillTraceServiceImpl waybillTraceService;

    @Autowired
    private HzCollectWaybillMapper collectWaybillMapper;

    @Autowired
    private HzCollectWeightMapper collectWeightMapper;

    @Autowired
    private BaseAgentMapper baseAgentMapper;

    @Autowired
    private BaseBalanceMapper baseBalanceMapper;

    @Autowired
    private FlightLoadWaybillMapper loadWaybillMapper;

    @Autowired
    private IWaybillLogService waybillLogService;


    /**
     * 根据运单号以及单证控制校验运单号
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    @Override
    public MailWaybill check(String waybillCode) {
        if (waybillCode == null){
            throw new CustomException("运单号不能为空");
        }
        if (waybillCode.length() != 14){
            throw new CustomException("运单号格式错误");
        }
        MailWaybill mawb = mailWaybillMapper.selectOne(new QueryWrapper<MailWaybill>()
                .eq("waybill_code", waybillCode)
                .eq("dept_id", SecurityUtils.getHighParentId())
                .eq("is_del",0)
                .eq("type","DEP"));
            if (mawb != null){
                return getInfo(waybillCode);
            }
        return null;
    }

    /**
     * 校验单号
     * @param waybillCode 运单号
     * @param code 运单前缀
     * @param checkNum 单证校验号
     * @param ticketNum 单证号
     * @param mawb 分单信息
     * @return 结果
     */
    private boolean checkCtrl(String waybillCode, String code, Integer checkNum, Integer ticketNum, MailWaybill mawb) {
        if (mawb != null) {
            return true;
        }
        Long ticketId = ticketMapper.selectCheck(code, waybillCode.substring(4, 7), ticketNum);
        if (ticketId == null) {
            throw new CustomException("无当前单证信息不可保存，请更改运单号");
        }
        TicketNum num = ticketNumMapper.selectOne(new QueryWrapper<TicketNum>().eq("ticket_id", ticketId).eq("num", ticketNum));
        if (num == null) {
            throw new CustomException("当前运单未发放");
        }
        if (!SecurityUtils.getUserId().equals(num.getUseBy())) {
            throw new CustomException("与当前运单领单人不符");
        }
        if (!checkNum.equals(num.getCode())) {
            throw new CustomException("运单校验失败");
        }
        if ("USED".equals(num.getStatus())) {
            return true;
        }
        if ("CANCEL".equals(num.getStatus())) {
            throw new CustomException("当前运单已销号");
        }
        if ("INVALID".equals(num.getStatus())) {
            throw new CustomException("当前运单已作废");
        }
        return false;
    }

    /**
     * 邮件单制单
     *
     * @param waybill 邮件单制单数据
     * @return 返回结果
     */
    @Override
    public String add(MailWaybill waybill) {
        HttpServletResponse response = ServletUtils.getResponse();
        WaybillLog waybillLog = new WaybillLog();
        try {
            try {
                check(waybill.getWaybillCode());
            } catch (CustomException e) {
                throw new CustomException(e.getMessage());
            }
            waybill.setUpdateTime(new Date());
            if (waybill.getId() != null) {
                mailWaybillMapper.updateById(waybill);
                return String.valueOf(waybill.getId());
            }
            MailWaybill mailWaybill = mailWaybillMapper.selectOne(new QueryWrapper<MailWaybill>()
                    .eq("waybill_code", waybill.getWaybillCode()).eq("dept_id", SecurityUtils.getHighParentId()));
            if (mailWaybill != null) {
                throw new CustomException("当前运单号已存在");
            }
            String cargoCode = cargoCodeMapper.selectByName(waybill.getCargoName());
            if (StringUtils.isNotEmpty(cargoCode)) {
                waybill.setCargoCode(cargoCode);
            }

            FlightInfo flight = flightInfoMapper.selectOne(new QueryWrapper<FlightInfo>()
                    .eq("air_ways", waybill.getFlightNo1().substring(0, 2))
                    .eq("flight_no", waybill.getFlightNo1().substring(2))
                    .eq("start_scheme_takeoff_time", waybill.getFlightDate1())
                    .eq("is_offin", "D"));

            //如果托运局或者接收局不存在的话就针对邮局信息做一个新增,存在就不管
            BasePost shipper = postMapper.selectOne(new QueryWrapper<BasePost>()
                    .eq("office_name", waybill.getShipperAbb()));
            if (StringUtils.isNull(shipper)) {
                BasePost shipperNew = new BasePost();
                shipperNew.setOfficeName(waybill.getShipperAbb());
                shipperNew.setAddress(waybill.getShipperAddress());
                shipperNew.setLinkName(waybill.getShipper());
                shipperNew.setPhone(waybill.getShipperPhone());
                shipperNew.setLocation(waybill.getConsignRegion());
                shipperNew.setOfficeType("1");
                shipperNew.setCreateBy(SecurityUtils.getUsername());
                shipperNew.setCreateTime(new Date());
                postMapper.insert(shipperNew);
            }
            BasePost consign = postMapper.selectOne(new QueryWrapper<BasePost>()
                    .eq("office_name", waybill.getConsignAbb()));
            if (StringUtils.isNull(consign)) {
                BasePost consignNew = new BasePost();
                consignNew.setOfficeName(waybill.getConsignAbb());
                consignNew.setAddress(waybill.getConsignAddress());
                consignNew.setLinkName(waybill.getConsign());
                consignNew.setPhone(waybill.getConsignPhone());
                consignNew.setLocation(waybill.getConsignRegion());
                consignNew.setOfficeType("0");
                consignNew.setCreateBy(SecurityUtils.getUsername());
                consignNew.setCreateTime(new Date());
                postMapper.insert(consignNew);
            }
            waybill.setDeptId(SecurityUtils.getHighParentId());
            SysDept sysDept = deptMapper.selectDeptById(SecurityUtils.getHighParentId());
            waybill.setAgentCode(sysDept.getAgentCode());
            waybill.setAgentCompany(sysDept.getDeptName());
            if ("been_sent".equals(waybill.getStatus())) {
                // 生成默认收费项目
                List<HzChargeItems> hzChargeItems = itemsMapper.selectList(new QueryWrapper<HzChargeItems>()
                        .eq("operation_type", "DEP")
                        .eq("is_default", 1).eq("status",1)
                        .le("start_effective_time", waybill.getWriteTime())
                        .ge("end_effective_time", waybill.getWriteTime())
                        .eq("is_del", 0));
                LocalTime startTime;
                LocalTime endTime;
                if (flight == null) {
                    startTime = LocalTime.of(6, 0, 0);
                    endTime = LocalTime.of(6, 0, 1);
                } else {
                    LocalDateTime takeoffTime = flight.getStartSchemeTakeoffTime();
                    LocalDateTime time = takeoffTime.plusSeconds(1);
                    endTime = time.toLocalTime();
                    startTime = takeoffTime.toLocalTime();
                }
                SysConfig sysConfig = sysConfigMapper.selectConfigById(16L);
                Date date = new Date();
                Instant startInstant = date.toInstant();
                long aLong = Long.parseLong(sysConfig.getConfigValue());
                long times = aLong * 60 * 60;
                Instant endInstant = startInstant.plusSeconds(times);
                Date storeEndTime = Date.from(endInstant);
                BigDecimal costSum = new BigDecimal(0);
                BigDecimal weightRate;
                BigDecimal chargeWeight = waybill.getChargeWeight() == null ? new BigDecimal(0) : waybill.getChargeWeight();
                if (waybill.getWeight() == null || waybill.getWeight().compareTo(new BigDecimal(0)) == 0) {
                    weightRate = new BigDecimal(0);
                } else {
                    BigDecimal bigDecimal = chargeWeight.divide(waybill.getWeight(), 5, RoundingMode.DOWN).multiply(waybill.getWeight());
                    weightRate = bigDecimal.setScale(0, RoundingMode.CEILING);
                }
                for (HzChargeItems hzChargeItem : hzChargeItems) {
                    List<HzChargeIrRelation> relations = relationMapper.selectList(new QueryWrapper<HzChargeIrRelation>()
                            .eq("item_id", hzChargeItem.getId())
                            .eq("is_del", 0));
                    List<HzChargeIrRelation> ruleList = new ArrayList<>();
                    for (HzChargeIrRelation hzChargeRule : relations) {
                        if (hzChargeRule.getIsSouth() == 1) {
                            continue;
                        }
                        if (hzChargeRule.getIsExit() == 1) {
                            continue;
                        }
                        if (hzChargeRule.getCrossAir() == 1) {
                            continue;
                        }
                        if (StringUtils.isNotEmpty(hzChargeRule.getNoCharge()) && !hzChargeRule.getNoCharge().contains(SecurityUtils.getHighParentId().toString())) {
                            continue;
                        }
                        if (StringUtils.isNotEmpty(hzChargeRule.getPrefix()) && !hzChargeRule.getPrefix().equals("DN")){
                            continue;
                        }
                        if (StringUtils.isNotEmpty(hzChargeRule.getCategory()) && !hzChargeRule.getCategory().contains(waybill.getCategoryName())) {
                            continue;
                        }
                        ruleList.add(hzChargeRule);
                    }
                    if (!CollectionUtils.isEmpty(ruleList)) {
                        HzChargeIrRelation relation = ruleList.stream().max(Comparator.comparing(HzChargeIrRelation::getPriority)).orElse(null);
                        if (relation != null) {
                            HzChargeRule rule1 = ruleMapper.selectById(relation.getRuleId());
                            List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>().eq("ir_id", relation.getId()));
                            if ("ColdStorageBillingRule.class".equals(rule1.getClassName())) {
                                continue;
                            }
                            CostDetail detail = new CostDetail();
                            detail.setWaybillCode(waybill.getWaybillCode());
                            detail.setIrId(relation.getId());
                            detail.setUnit(1);
                            detail.setSmallItem(1);
                            detail.setLargeItem(1);
                            detail.setSuperLargeItem(1);
                            detail.setStartTime(startTime);
                            detail.setEndTime(endTime);
                            detail.setDaysInStorage(1.0);
                            detail.setStoreStartTime(date);
                            detail.setStoreEndTime(storeEndTime);
                            detail.setDeptId(SecurityUtils.getHighParentId());
                            //将pointTime设置为当前时间
                            detail.setPointTime(startTime);
                            BillingRule rule = BillingRuleFactory.createRule(rule1.getClassName());
                            BillRuleVo vo = rule.calculateFee(itemRules, weightRate, waybill.getQuantity(), detail);
                            BigDecimal totalCharge = BigDecimalRoundUtils.bigDecimalRound(hzChargeItem.getRoundRule(), vo.getTotalCharge());
                            detail.setTotalCharge(totalCharge);
                            detail.setQuantity(vo.getQuantity());
                            detail.setRate(vo.getRate());
                            costSum = costSum.add(totalCharge);
                            costDetailMapper.insert(detail);
                        }
                    }
                }
                waybill.setPayMoney(costSum);
            }

            //去看看这个单是否在安检申报新增表存在 如果存在就和那边数据同步一下
            AllSecurityWaybill allSecurityWaybill = allSecurityWaybillMapper.selectOne(new QueryWrapper<AllSecurityWaybill>()
                    .eq("waybill_code", waybill.getWaybillCode()));
            if (allSecurityWaybill != null) {
                waybill.setSecurityUrl(allSecurityWaybill.getSecurityUrl());
                waybill.setSecuritySubmitWl(allSecurityWaybill.getSecuritySubmitWl());
                waybill.setSecuritySubmit(allSecurityWaybill.getSecuritySubmit());
                waybill.setSecuritySubmitOperator(allSecurityWaybill.getSecuritySubmitOperator());
                waybill.setDeclarationConsistent(allSecurityWaybill.getDeclarationConsistent());
                waybill.setDeliveryIdNo(allSecurityWaybill.getDeliveryIdNo());
                waybill.setDeliveryFilePhoto(allSecurityWaybill.getDeliveryFilePhoto());
                waybill.setDeliveryProfilePhoto(allSecurityWaybill.getDeliveryProfilePhoto());
                waybill.setShipperSignature(allSecurityWaybill.getShipperSignature());
                waybill.setAgentSignature(allSecurityWaybill.getAgentSignature());
            }

            WaybillTrace waybillTrace = new WaybillTrace();
            waybillTrace.setOperTime(new Date());
            waybillTrace.setOperPieces(waybill.getQuantity());
            waybillTrace.setOperWeight(waybill.getWeight());
            waybillTrace.setWaybillCode(waybill.getWaybillCode());
            waybillTrace.setChargeWeight(waybill.getChargeWeight());
            waybillTrace.setNodeName("已发送");
            waybillTraceService.insertWaybillTrace(waybillTrace);
            waybill.setStatus("put_in");
            waybill.setType("DEP");
            waybill.setCollectStatus(3);
            mailWaybillMapper.insert(waybill);

            //运单日志的新增
            waybillLog = waybillLogService.getWaybillLog(
                    waybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                    waybill.getWeight().toString(), waybill.getQuantity().toString(),
                    waybill.getFlightNo1(), waybill, null, 0, null, new Date(),
                    "邮件单保存并发送", "DEP", null);


            HzCollectWaybill collectWaybill = new HzCollectWaybill();
            // 新增收运数据
            collectWaybill.setQuantity(waybill.getQuantity());
            collectWaybill.setWeight(waybill.getWeight());
            collectWaybill.setWaybillId(waybill.getId());
            collectWaybill.setWaybillCode(waybill.getWaybillCode());
            collectWaybill.setCollectTime(new Date());
            collectWaybill.setOperName(SecurityUtils.getUsername());
            collectWaybill.setCollectTime(new Date());
            collectWaybill.setIsReal(1);
            collectWaybill.setStatus("REAL");
            collectWaybillMapper.insert(collectWaybill);
            HzCollectWeight weight = new HzCollectWeight();
            weight.setCollectId(collectWaybill.getId());
            weight.setWeightTime(new Date());
            weight.setDesPort(waybill.getDesPort());
            weight.setTotalWeight(collectWaybill.getWeight());
            weight.setQuantity(collectWaybill.getQuantity());
            weight.setWeight(collectWaybill.getWeight());
            collectWeightMapper.insert(weight);
            // 运单跟踪数据
            WaybillTrace trace = new WaybillTrace();
            trace.setOperTime(new Date());
            trace.setOperPieces(waybill.getQuantity());
            trace.setOperWeight(waybill.getWeight());
            trace.setWaybillCode(waybill.getWaybillCode());
            trace.setNodeName("货站入库");
            trace.setChargeWeight(waybill.getChargeWeight());
            waybillTraceService.insertWaybillTrace(trace);
            return String.valueOf(waybill.getId());
        } catch (Exception e) {
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" +  "," +
                            "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        } finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }

    /**
     * 编辑运单
     *
     * @param waybill 运单详情信息
     * @return 返回结果
     */
    @Override
    public int edit(MailWaybill waybill) {
        HttpServletResponse response = ServletUtils.getResponse();
        WaybillLog waybillLog = new WaybillLog();
        try {
            MailWaybill mailWaybill = mailWaybillMapper.selectById(waybill.getId());
            if (mailWaybill.getPayStatus() > 0 && mailWaybill.getPayStatus() < 14) {
                throw new CustomException("已支付的邮件单不能修改数据,请先取消支付");
            }
            waybill.setDeptId(SecurityUtils.getHighParentId());
            if (StringUtils.isNotEmpty(waybill.getCategoryName())) {
                waybill.setCategoryCode(waybill.getCategoryName());
            }
            String cargoCode = cargoCodeMapper.selectByName(waybill.getCargoName());
            if (StringUtils.isNotEmpty(cargoCode)) {
                waybill.setCargoCode(cargoCode);
            }
            costDetailMapper.delete(new QueryWrapper<CostDetail>().eq("waybill_code", waybill.getWaybillCode()));
            List<HzChargeItems> hzChargeItems = itemsMapper.selectList(new QueryWrapper<HzChargeItems>()
                    .eq("operation_type", "DEP")
                    .eq("is_default", 1).eq("status",1)
                    .le("start_effective_time", waybill.getWriteTime())
                    .ge("end_effective_time", waybill.getWriteTime())
                    .eq("is_del", 0));
            LocalTime startTime;
            LocalTime endTime;
            FlightInfo flight = flightInfoMapper.selectOne(new QueryWrapper<FlightInfo>()
                    .eq("air_ways", waybill.getFlightNo1().substring(0, 2))
                    .eq("flight_no", waybill.getFlightNo1().substring(2))
                    .eq("start_scheme_takeoff_time", waybill.getFlightDate1())
                    .eq("is_offin", "D"));
            if (flight == null) {
                startTime = LocalTime.of(6, 0, 0);
                endTime = LocalTime.of(6, 0, 1);
            } else {
                LocalDateTime takeoffTime = flight.getStartSchemeTakeoffTime();
                LocalDateTime time = takeoffTime.plusSeconds(1);
                endTime = time.toLocalTime();
                startTime = takeoffTime.toLocalTime();
            }
            SysConfig sysConfig = sysConfigMapper.selectConfigById(16L);
            Date date = new Date();
            Instant startInstant = date.toInstant();
            long aLong = Long.parseLong(sysConfig.getConfigValue());
            long times = aLong * 60 * 60;
            Instant endInstant = startInstant.plusSeconds(times);
            Date storeEndTime = Date.from(endInstant);
            BigDecimal costSum = new BigDecimal(0);
            BigDecimal weightRate;
            BigDecimal chargeWeight = waybill.getChargeWeight() == null ? new BigDecimal(0) : waybill.getChargeWeight();
            if (waybill.getWeight() == null || waybill.getWeight().compareTo(new BigDecimal(0)) == 0) {
                weightRate = new BigDecimal(0);
            } else {
                BigDecimal bigDecimal = chargeWeight.divide(waybill.getWeight(), 5, RoundingMode.DOWN).multiply(waybill.getWeight());
                weightRate = bigDecimal.setScale(0, RoundingMode.CEILING);
            }
            for (HzChargeItems hzChargeItem : hzChargeItems) {
                List<HzChargeIrRelation> relations = relationMapper.selectList(new QueryWrapper<HzChargeIrRelation>()
                        .eq("item_id", hzChargeItem.getId())
                        .eq("is_del", 0));
                List<HzChargeIrRelation> ruleList = new ArrayList<>();
                for (HzChargeIrRelation hzChargeRule : relations) {
                    if (hzChargeRule.getIsSouth() == 1) {
                        continue;
                    }
                    if (hzChargeRule.getIsExit() == 1) {
                        continue;
                    }
                    if (hzChargeRule.getCrossAir() == 1) {
                        continue;
                    }
                    if (StringUtils.isNotEmpty(hzChargeRule.getNoCharge()) && !hzChargeRule.getNoCharge().contains(SecurityUtils.getHighParentId().toString())) {
                        continue;
                    }
                    if (StringUtils.isNotEmpty(hzChargeRule.getPrefix()) && !hzChargeRule.getPrefix().equals("DN")){
                        continue;
                    }
                    if (StringUtils.isNotEmpty(hzChargeRule.getCategory()) && !hzChargeRule.getCategory().contains(waybill.getCategoryCode())) {
                        continue;
                    }
                    ruleList.add(hzChargeRule);
                }
                if (!CollectionUtils.isEmpty(ruleList)) {
                    HzChargeIrRelation relation = ruleList.stream().max(Comparator.comparing(HzChargeIrRelation::getPriority)).orElse(null);
                    if (relation != null) {
                        HzChargeRule rule1 = ruleMapper.selectById(relation.getRuleId());
                        List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>().eq("ir_id", relation.getId()));
                        if ("ColdStorageBillingRule.class".equals(rule1.getClassName())) {
                            continue;
                        }
                        CostDetail detail = new CostDetail();
                        detail.setWaybillCode(waybill.getWaybillCode());
                        detail.setIrId(relation.getId());
                        detail.setUnit(1);
                        detail.setSmallItem(1);
                        detail.setLargeItem(1);
                        detail.setSuperLargeItem(1);
                        detail.setStartTime(startTime);
                        detail.setEndTime(endTime);
                        detail.setDaysInStorage(1.0);
                        detail.setStoreStartTime(date);
                        detail.setStoreEndTime(storeEndTime);
                        detail.setDeptId(SecurityUtils.getHighParentId());
                        //将pointTime设置为当前时间
                        detail.setPointTime(startTime);
                        BillingRule rule = BillingRuleFactory.createRule(rule1.getClassName());
                        BillRuleVo vo = rule.calculateFee(itemRules, weightRate, waybill.getQuantity(), detail);
                        BigDecimal totalCharge = BigDecimalRoundUtils.bigDecimalRound(hzChargeItem.getRoundRule(), vo.getTotalCharge());
                        detail.setTotalCharge(totalCharge);
//                        detail.setTotalCharge(vo.getTotalCharge());
                        detail.setQuantity(vo.getQuantity());
                        detail.setRate(vo.getRate());
                        costSum = costSum.add(totalCharge);
                        costDetailMapper.insert(detail);
                    }
                }
            }

            List<HzCollectWaybill> collectWaybills = collectWaybillMapper.selectList(new QueryWrapper<HzCollectWaybill>().eq("waybill_id", waybill.getId()));
            for (HzCollectWaybill collectWaybill : collectWaybills) {
                collectWeightMapper.delete(new QueryWrapper<HzCollectWeight>().eq("collect_id", collectWaybill.getId()));
                collectWaybillMapper.deleteById(collectWaybill.getId());
            }
            HzCollectWaybill collectWaybill = new HzCollectWaybill();
            // 新增收运数据
            collectWaybill.setQuantity(waybill.getQuantity());
            collectWaybill.setWeight(waybill.getWeight());
            collectWaybill.setWaybillId(waybill.getId());
            collectWaybill.setWaybillCode(waybill.getWaybillCode());
            collectWaybill.setCollectTime(new Date());
            collectWaybill.setOperName(SecurityUtils.getUsername());
            collectWaybill.setCollectTime(new Date());
            collectWaybill.setIsReal(1);
            collectWaybill.setStatus("REAL");
            collectWaybillMapper.insert(collectWaybill);
            HzCollectWeight weight = new HzCollectWeight();
            weight.setCollectId(collectWaybill.getId());
            weight.setWeightTime(new Date());
            weight.setDesPort(waybill.getDesPort());
            weight.setTotalWeight(collectWaybill.getWeight());
            weight.setQuantity(collectWaybill.getQuantity());
            weight.setWeight(collectWaybill.getWeight());
            collectWeightMapper.insert(weight);
            // 运单跟踪数据
            WaybillTrace trace = new WaybillTrace();
            trace.setOperTime(new Date());
            trace.setOperPieces(waybill.getQuantity());
            trace.setOperWeight(waybill.getWeight());
            trace.setWaybillCode(waybill.getWaybillCode());
            trace.setNodeName("货站入库");
            trace.setChargeWeight(waybill.getChargeWeight());
            waybillTraceService.insertWaybillTrace(trace);

            waybill.setPayMoney(costSum);
            waybill.setUpdateTime(new Date());
            if (waybill.getPayStatus() == 14) {
                waybill.setPayStatus(0);
            }
            //运单日志的新增
            waybillLog = waybillLogService.getWaybillLog(
                    waybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                    waybill.getWeight().toString(), waybill.getQuantity().toString(),
                    waybill.getFlightNo1(), waybill, null, 0, null, new Date(),
                    "邮件单保存并发送", "DEP", null);

            return mailWaybillMapper.updateById(waybill);
        }catch(Exception e){
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" +  "," +
                            "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        } finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }

    /**
     * 运单作废
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    @Override
    public int invalid(String waybillCode) {

        if (waybillCode == null){
            throw new CustomException("运单号不能为空");
        }
        if (waybillCode.length() != 14){
            throw new CustomException("运单号格式错误");
        }
        MailWaybill waybill = mailWaybillMapper.selectOne(new QueryWrapper<MailWaybill>()
                .eq("waybill_code", waybillCode)
                .eq("dept_id", SecurityUtils.getHighParentId())
                .eq("is_del",0));
        if (waybill == null){
            throw new CustomException("无当前邮件单信息");
        }
        if ("INVALID".equals(waybill.getStatus())){
            throw new CustomException("当前运单已作废");
        }
        if(waybill.getPayStatus() > 0 && waybill.getPayStatus() < 14){
            throw new CustomException("运单已支付,作废失败");
        }
        List<HzCollectWaybill> collectWaybills = collectWaybillMapper.selectList(new QueryWrapper<HzCollectWaybill>()
                .eq("waybill_id", waybill.getId()));
        if (!CollectionUtils.isEmpty(collectWaybills)) {
            throw new CustomException("该运单已收运,作废失败");
        }
        List<LoadInfoVo> infoVos = loadWaybillMapper.selectByLoadInfo(waybill.getId());
        if (!CollectionUtils.isEmpty(infoVos)){
            throw new CustomException("当前运单已配载不能作废");
        }
        HttpServletResponse response = ServletUtils.getResponse();
        //运单日志的新增
        WaybillLog waybillLog = waybillLogService.getWaybillLog(
                waybillCode, 0, SecurityUtils.getNickName(),
                waybill.getWeight().toString(), waybill.getQuantity().toString(), waybill.getFlightNo1(),
                waybillCode, null, 0, null, new Date(),
                "运单作废", "DEP", null);
        String code = waybillCode.substring(0, 4);
        Integer ticketNum = Integer.valueOf(waybillCode.substring(7, 14));
        Long ticketId = ticketMapper.selectCheck(code, waybillCode.substring(4, 7), ticketNum);
        if (ticketId != null) {
            TicketNum num = ticketNumMapper.selectOne(new QueryWrapper<TicketNum>().eq("ticket_id", ticketId).eq("num", ticketNum));
            if (num == null) {
                throw new CustomException("当前运单未发放");
            }
            if ("NOTGRANT".equals(num.getStatus())) {
                throw new CustomException("当前运单未发放");
            }
            if (!SecurityUtils.getUserId().equals(num.getUseBy())) {
                throw new CustomException("与当前运单领单人不符");
            }
            num.setStatus("INVALID");
            ticketNumMapper.updateById(num);
        }
        refund(waybill);
        WaybillTrace waybillTrace = new WaybillTrace();
        waybillTrace.setOperTime(new Date());
        waybillTrace.setOperPieces(waybill.getQuantity());
        waybillTrace.setOperWeight(waybill.getWeight());
        waybillTrace.setWaybillCode(waybill.getWaybillCode());
        waybillTrace.setNodeName("已作废");
        waybillTraceService.insertWaybillTrace(waybillTrace);
        waybillLog.setJsonResult(waybillLogService.getJson(
                "msg:" + "操作成功" +  "," +
                        "code:" + response.getStatus() + "," +
                        "data:" + 1));
        waybillLogService.insertWaybillLog(waybillLog);
        return 1;
    }

    /**
     * 运单取消作废
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    @Override
    public int cancelVoid(String waybillCode) {
        if (waybillCode == null){
            throw new CustomException("运单号不能为空");
        }
        if (waybillCode.length() != 14){
            throw new CustomException("运单号格式错误");
        }
        HttpServletResponse response = ServletUtils.getResponse();
        //运单日志的新增
        WaybillLog waybillLog = new WaybillLog();
        try {
            MailWaybill waybill = mailWaybillMapper.selectOne(new QueryWrapper<MailWaybill>()
                    .eq("waybill_code", waybillCode)
                    .eq("dept_id", SecurityUtils.getHighParentId())
                    .eq("is_del", 0));

            if (waybill == null) {
                throw new CustomException("无当前邮件单信息");
            }
//            if ("been_sent".equals(waybill.getStatus())) {
//                throw new CustomException("当前运单未处于作废状态");
//            }
            if (!"INVALID".equals(waybill.getStatus())){
                throw new CustomException("当前运单未处于作废状态");
            }
            waybillLog = waybillLogService.getWaybillLog(
                    waybillCode, 0, SecurityUtils.getNickName(),
                    waybill.getWeight().toString(), waybill.getQuantity().toString(), waybill.getFlightNo1(),
                    waybillCode, null, 0, null, new Date(),
                    "运单取消作废", "DEP", null);
//            waybill.setIsDel(1);
            waybill.setPayStatus(0);
            waybill.setUpdateTime(new Date());
            waybill.setStatus("staging");
            return mailWaybillMapper.updateById(waybill);
        }catch(Exception e){
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" +  "," +
                            "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        } finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }

    /**
     * 邮件单查询
     * @param query 查询条件
     * @return 结果
     */
    @Override
    public List<MailWaybillVo> queryList(HawbQuery query) {
        query.setDeptId(SecurityUtils.getHighParentId());
        List<MailWaybillVo> mailWaybillVos = mailWaybillMapper.queryList(query);
        for (MailWaybillVo mailWaybillVo : mailWaybillVos) {
             BigDecimal rCostSum = mailWaybillVo.getRCostSum() == null ? new BigDecimal(0) : mailWaybillVo.getRCostSum();
             BigDecimal wCostSum = mailWaybillVo.getWCostSum() == null ? new BigDecimal(0) : mailWaybillVo.getWCostSum();
            mailWaybillVo.setProfit(rCostSum.subtract(wCostSum));
        }
        return mailWaybillVos;
    }

    /**
     * 根据运单code查询运单详情
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    @Override
    public MailWaybill getInfo(String waybillCode) {
        return mailWaybillMapper.selectOne(new QueryWrapper<MailWaybill>()
                .eq("waybill_code",waybillCode)
                .eq("type","DEP")
                .eq("dept_id",SecurityUtils.getHighParentId())
                .eq("is_del",0));
    }

    /**
     * 计算运单运价
     *
     * @param query 新增运单信息
     * @return 返回结果
     */
    @Override
    public FareVo fare(FareQuery query) {
        FareVo vo = new FareVo();
        BigDecimal bigDecimal = new BigDecimal(0);
        if (query.getRatePerKg() != null){
            bigDecimal = query.getRatePerKg().multiply(query.getWeight());
            vo.setCostSum(bigDecimal);
        }else {
            Long deptId = SecurityUtils.getHighParentId();
            vo.setChargeWeight(query.getWeight());
            LocalDate currentDate = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String formattedDate = currentDate.format(formatter);
            String code = query.getWaybillCode().substring(0,4);
            switch (query.getCategoryName()){
                case "08":
                    query.setRateType(0);
                    break;
                case "01":
                    query.setRateType(1);
                    break;
                case "12":
                    query.setRateType(2);
                    break;
                default:
                    query.setRateType(3);
                    break;
            }
            MailPrice price = priceMapper.selectOne(new QueryWrapper<MailPrice>()
                    .eq("applicable_documents",  code)
                    .eq("rate_type",  query.getRateType())
                    .eq("carrier_code", query.getCarrier1())
                    .eq("dept_id", deptId)
                    .le("effective_date", formattedDate)
                    .ge("expiration_date", formattedDate)
                    .eq("is_del", 0));
            if (price != null){
                MailPriceItem item = itemMapper.selectOne(new QueryWrapper<MailPriceItem>()
                        .eq("rate_id", price.getId())
                        .eq("departure_city", query.getSourcePort())
                        .eq("destination_city", query.getDesPort())
                        .eq("is_del",0));
                if (item == null){
                    vo.setCostSum(bigDecimal);
                    return vo;
                }
                FareVo multiply = getInterval(item, query.getWeight());
                // 取舍方式计算
                BigDecimal round = bigDecimalRound(price.getRoundingMethod(), multiply.getCostSum());
                if (round.compareTo(item.getMinimumFreight()) <= 0 ){
                    bigDecimal = bigDecimal.add(item.getMinimumFreight());
                }else {
                    bigDecimal = bigDecimal.add(round);
                }
                vo.setRate(multiply.getRate());
                vo.setWRate(multiply.getWRate());
                vo.setRRate(multiply.getRRate());
                vo.setWCostSum(multiply.getWCostSum());
                vo.setRCostSum(multiply.getRCostSum());
                vo.setChargeWeight(multiply.getChargeWeight());
                vo.setItemId(item.getId());
            }
            // 收费项目默认计费
            List<ChargeItem> chargeItem = chargeItemMapper.selectByDept(deptId,code);
            List<ChargeItem> collect = chargeItem.stream()
                    .filter(e -> StringUtils.isEmpty(e.getCode()) || (StringUtils.isNotEmpty(query.getCarrier1()) && query.getCarrier1().equals(e.getCode())))
                    .collect(Collectors.toList());
            for (ChargeItem item : collect) {
                BigDecimal decimal = getBigDecimal(item, item.getDefaultCostRate(),
                        vo.getChargeWeight() == null ? query.getWeight() : vo.getChargeWeight(),
                        query.getVolume() == null ? new BigDecimal(0) : query.getVolume(),query.getQuantity());
                bigDecimal = bigDecimal.add(decimal);
            }
            vo.setCostSum(bigDecimal);
        }
        return vo;
    }

    /**
     * 打印邮件单接口
     * @param id 邮件单id
     * @param response 返回流
     */
    @Override
    public void printMailData(Long id, HttpServletResponse response) throws Exception {
        MailWaybill waybill = mailWaybillMapper.selectById(id);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if (waybill.getWaybillCode() != null){
            String substring = waybill.getWaybillCode().substring(6);
            waybill.setWaybillCodeAbb(substring);
        }
        BaseAirportCode baseAirportCode = airportCodeMapper.selectByCode(waybill.getDesPort());
        waybill.setDesPortStr(baseAirportCode.getChineseName());
        waybill.setSourcePortStr("贵阳");
        if ("08".equals(waybill.getCargoName())){
            waybill.setPRatePerKg(waybill.getRatePerKg());
            waybill.setPCostSum(waybill.getCostSum());
        }
        if ("01".equals(waybill.getCargoName())){
            waybill.setTRatePerKg(waybill.getRatePerKg());
            waybill.setTCostSum(waybill.getCostSum());
        }
        if (waybill.getWriteTime() != null){
            String format = dateFormat.format(waybill.getWriteTime());
            waybill.setWriteTimeStr(format);
        }else {
            String format = dateFormat.format(new Date());
            waybill.setWriteTimeStr(format);
        }
        if (waybill.getFlightDate1() != null){
            String format = dateFormat.format(waybill.getFlightDate1());
            waybill.setFlightInfo(waybill.getFlightNo1() + "/" +format);
        }
        if (waybill.getFlightDate2() != null){
            String format = dateFormat.format(waybill.getFlightDate2());
            waybill.setFlightInfo2(waybill.getFlightNo2() + "/" +format);
        }
        if (waybill.getFlightDate3() != null){
            String format = dateFormat.format(waybill.getFlightDate3());
            waybill.setFlightInfo3(waybill.getFlightNo3() + "/" +format);
        }
        if(waybill.getWeight()!=null && waybill.getWeight().compareTo(BigDecimal.ZERO) != 0){
            waybill.setWeight(waybill.getWeight().setScale(0, RoundingMode.DOWN));
        }
        if(waybill.getChargeWeight()!=null && waybill.getChargeWeight().compareTo(BigDecimal.ZERO) != 0){
            waybill.setChargeWeight(waybill.getChargeWeight().setScale(0, RoundingMode.DOWN));
        }
        if(waybill.getVolume() != null){
            waybill.setVolumeUnit(waybill.getVolume() + "m³");
        }
        if(StringUtils.isNotEmpty(waybill.getCargoName())){
            waybill.setCargoNameStr(waybill.getCargoName());
        }else{
            if ("12".equals(waybill.getCategoryName())){
                waybill.setCargoNameStr("经邮");
            }
            if ("08".equals(waybill.getCategoryName())){
                waybill.setCargoNameStr("普邮");
            }
            if ("01".equals(waybill.getCategoryName())){
                waybill.setCargoNameStr("特快");
            }
        }
        SysConfig sysConfig = configMapper.checkConfigKeyUnique("mail.print.sourcePort");
        if(sysConfig != null){
            waybill.setConfigSourcePort(sysConfig.getConfigValue());
        }

        ClassPathResource resource = new ClassPathResource("template/mail.pdf");
        if (resource.exists()){
            String path = resource.getPath();
            byte[] bytes = PdfPrintHelper.getPdfDataFromTemplate(waybill,path);
            // 设置响应头
            response.reset();
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "inline; filename=example.pdf");
            // 获取输出流并写入字节数据
            response.getOutputStream().write(bytes);
            response.getOutputStream().flush();
        }
    }

    /**
     * 更换运单号
     * @param editWaybillCode 更换参数
     * @return 结果
     */
    @Override
    public int editWaybillCode(EditWaybillCode editWaybillCode) {
        if(!(editWaybillCode.getWaybillCode().length() == 16 && editWaybillCode.getWaybillCode().endsWith("B"))){
            check(editWaybillCode.getWaybillCode());
        }
        MailWaybill mailWaybill = mailWaybillMapper.selectById(editWaybillCode.getId());
        String code = mailWaybill.getWaybillCode().substring(0, 4);
        Integer ticketNum = Integer.valueOf(mailWaybill.getWaybillCode().substring(6,14));
        Long ticketId = ticketMapper.selectCheck(code, "DN", ticketNum);
        TicketNum num = ticketNumMapper.selectOne(new QueryWrapper<TicketNum>().eq("ticket_id", ticketId).eq("num", ticketNum));
        if (num != null) {
            num.setStatus("NOTUSED");
            ticketNumMapper.updateById(num);
        }
        mailWaybill.setWaybillCode(editWaybillCode.getWaybillCode());
        mailWaybill.setUpdateTime(new Date());
        return mailWaybillMapper.updateById(mailWaybill);
    }

    @Override
    public List<MailWaybillVo> exportList(HawbQuery query) {
        query.setDeptId(SecurityUtils.getHighParentId());
        List<MailWaybillVo> mailWaybillVos = mailWaybillMapper.queryList(query);
        for (MailWaybillVo mailWaybillVo : mailWaybillVos) {
            BigDecimal rCostSum = mailWaybillVo.getRCostSum() == null ? new BigDecimal(0) : mailWaybillVo.getRCostSum();
            BigDecimal wCostSum = mailWaybillVo.getWCostSum() == null ? new BigDecimal(0) : mailWaybillVo.getWCostSum();
            mailWaybillVo.setProfit(rCostSum.subtract(wCostSum));
        }
        if (!CollectionUtils.isEmpty(mailWaybillVos)){
            MailWaybillVo vo = new MailWaybillVo();
            vo.setWaybillCode("合计");
            BigDecimal costSum = mailWaybillVos.stream().map(MailWaybillVo::getCostSum).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setCostSum(costSum);
            BigDecimal rCostSum = mailWaybillVos.stream().map(MailWaybillVo::getRCostSum).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setRCostSum(rCostSum);
            BigDecimal wCostSum = mailWaybillVos.stream().map(MailWaybillVo::getWCostSum).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setWCostSum(wCostSum);
            mailWaybillVos.add(vo);
        }
        return mailWaybillVos;
    }

    @Override
    public int cancelPay(Long waybillId) {
//        List<LoadInfoVo> infoVos = loadWaybillMapper.selectByLoadInfo(waybillId);
//        if (!CollectionUtils.isEmpty(infoVos)){
//            throw new CustomException("当前运单已配载不能取消支付");
//        }
        List<HzCollectWaybill> collectWaybills = collectWaybillMapper.selectList(new QueryWrapper<HzCollectWaybill>()
                .eq("waybill_id", waybillId));
        if (!CollectionUtils.isEmpty(collectWaybills)) {
            throw new CustomException("运单已经收运不能取消支付");
        }
        MailWaybill airWaybill = mailWaybillMapper.selectById(waybillId);
        if ("INVALID".equals(airWaybill.getStatus())){
            throw new CustomException("当前运单已作废");
        }
        BaseAgent agent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", airWaybill.getDeptId()));
        BigDecimal costSum = new BigDecimal(0);
        List<CostDetail> details = costDetailMapper.selectPayOrSettleList(airWaybill.getWaybillCode(), 0, 1, airWaybill.getDeptId());
        if (!CollectionUtils.isEmpty(details)){
            costSum = details.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO,BigDecimal::add);
        }
        for (CostDetail detail : details) {
            detail.setIsSettle(2);
            costDetailMapper.updateById(detail);
        }
        if (agent != null && agent.getSettleMethod() == 1) {
            BigDecimal balance = agent.getBalance() == null ? new BigDecimal(0) : agent.getBalance();
            BigDecimal subtract = balance.add(costSum);
            agent.setBalance(subtract);
            baseAgentMapper.updateBaseAgent(agent);
            BaseBalance baseBalance = new BaseBalance();
            baseBalance.setAgentId(agent.getId());
            baseBalance.setBalance(agent.getBalance());
            baseBalance.setType("增加余额");
            baseBalance.setCreateTime(new Date());
            baseBalance.setCreateBy(SecurityUtils.getNickName());
            // todo 流水号需从银联支付接口获取
            //baseBalance.setSerialNo();
            baseBalance.setTradeMoney(costSum);
            baseBalance.setWaybillCode(airWaybill.getWaybillCode());
            baseBalance.setRemark("取消支付退款");
            baseBalanceMapper.insertBaseBalance(baseBalance);
        }
        airWaybill.setPayStatus(14);
        airWaybill.setRefund(costSum);
        airWaybill.setUpdateTime(new Date());

        //运单日志
        WaybillLog waybillLog = waybillLogService.getWaybillLog(
                airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                airWaybill.getWeight() != null ? airWaybill.getWeight().toString() : null,
                airWaybill.getQuantity() != null ? airWaybill.getQuantity().toString() : null,
                airWaybill.getFlightNo1(), null, "操作成功", 0, null, new Date(),
                "邮件单取消支付", "DEP", null);
        waybillLogService.insertWaybillLog(waybillLog);

        return mailWaybillMapper.updateById(airWaybill);
    }


    private void refund(MailWaybill mawb) {
        BaseAgent agent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", mawb.getDeptId()));
        BigDecimal costSum = new BigDecimal(0);
        BigDecimal weightRate;
        BigDecimal chargeWeight = mawb.getChargeWeight() == null ? new BigDecimal(0) : mawb.getChargeWeight();
        if (mawb.getWeight() == null || mawb.getWeight().compareTo(new BigDecimal(0)) == 0){
            weightRate = new BigDecimal(0);
        }else {
            BigDecimal bigDecimal = chargeWeight.divide(mawb.getWeight(),5, RoundingMode.DOWN).multiply(mawb.getWeight());
            weightRate = bigDecimal.setScale(0, RoundingMode.CEILING);
        }
        List<CostDetail> details = costDetailMapper.selectPayOrSettleList(mawb.getWaybillCode(), 0, 1, mawb.getDeptId());
        if (!CollectionUtils.isEmpty(details)){
            List<CostDetail> collect = details.stream().filter(e -> "处置费".equals(e.getChargeAbb())).collect(Collectors.toList());
            for (CostDetail detail : collect) {
                countCost(detail, mawb.getWeight(), weightRate, mawb.getQuantity());
                costSum = costSum.add(detail.getTotalCharge());
            }
        }
        WaybillFee waybillFee = feeMapper.selectOne(new QueryWrapper<WaybillFee>()
                .eq("waybill_code", mawb.getWaybillCode())
                .eq("dept_id", mawb.getDeptId())
                .eq("type", "DEP"));
        if (agent != null) {
            if (agent.getSettleMethod() == 0) {
                updateStatus(mawb, costSum, waybillFee, mawb.getDeptId(), 9);
            } else if (agent.getSettleMethod() == 1) {
                BigDecimal balance = agent.getBalance() == null ? new BigDecimal(0) : agent.getBalance();
                BigDecimal subtract = balance.add(costSum);
                agent.setBalance(subtract);
                baseAgentMapper.updateBaseAgent(agent);
                BaseBalance baseBalance = new BaseBalance();
                baseBalance.setAgentId(agent.getId());
                baseBalance.setBalance(agent.getBalance());
                baseBalance.setType("增加余额");
                baseBalance.setCreateTime(new Date());
                baseBalance.setCreateBy(SecurityUtils.getNickName());
                // todo 流水号需从银联支付接口获取
                //baseBalance.setSerialNo();
                baseBalance.setTradeMoney(costSum);
                baseBalance.setWaybillCode(mawb.getWaybillCode());
                baseBalance.setRemark("作废退款");
                baseBalanceMapper.insertBaseBalance(baseBalance);
                updateStatus(mawb, costSum, waybillFee, mawb.getDeptId(), 10);
            } else {
                if (agent.getPayMethod() == 0) {
                    updateStatus(mawb, costSum, waybillFee, mawb.getDeptId(), 11);
                } else {
                    updateStatus(mawb, costSum, waybillFee, mawb.getDeptId(), 12);
                }
            }
        } else {
            updateStatus(mawb, costSum, waybillFee, mawb.getDeptId(), 12);
        }
    }

    private void updateStatus(MailWaybill mawb, BigDecimal costSum, WaybillFee waybillFee, Long parentId, Integer payStatus) {
        if (waybillFee != null) {
            waybillFee.setRefund(costSum);
            waybillFee.setSettleMoney(costSum);
            waybillFee.setSettleTime(new Date());
            waybillFee.setRefund(costSum);
            waybillFee.setStatus(2);
            feeMapper.updateById(waybillFee);
        } else {
            WaybillFee fee = new WaybillFee();
            fee.setRefund(costSum);
            fee.setSettleMoney(costSum);
            fee.setDeptId(parentId);
            fee.setSettleTime(new Date());
            fee.setWaybillCode(mawb.getWaybillCode());
            fee.setStatus(2);
            fee.setType("DEP");
            feeMapper.insert(fee);
        }
        mawb.setPayStatus(payStatus);
        mawb.setRefund(costSum);
        mawb.setSettleTime(new Date());
        mawb.setStatus("INVALID");
        mawb.setUpdateTime(new Date());
        mailWaybillMapper.updateById(mawb);
    }

    public BillRuleVo countCost(CostDetail detail, BigDecimal weight, BigDecimal weightRate, Integer quantity) {
        HzChargeIrRelation relation = relationMapper.selectById(detail.getIrId());
        BillRuleVo vo = new BillRuleVo();
        if (relation == null){
            vo.setTotalCharge(new BigDecimal(0));
            return vo;
        }
        HzChargeItems hzChargeItems = itemsMapper.selectById(relation.getItemId());
        HzChargeRule hzChargeRule = ruleMapper.selectById(relation.getRuleId());
        List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>().eq("ir_id", detail.getIrId()));
        if (CollectionUtils.isEmpty(itemRules)){
            vo.setTotalCharge(new BigDecimal(0));
            return vo;
        }
        BillingRule rule = BillingRuleFactory.createRule(hzChargeRule.getClassName());
        BillRuleVo ruleVo = rule.calculateFee(itemRules, weightRate, quantity, detail);
        BigDecimal bigDecimal = BigDecimalRoundUtils.bigDecimalRound(hzChargeItems.getRoundRule(), ruleVo.getTotalCharge());
        detail.setQuantity(ruleVo.getQuantity());
        detail.setFlightId(3L);
        detail.setSettleDepWeight(weight);
        detail.setSettleDepQuantity(quantity);
        detail.setIsSettle(1);
        detail.setType(1);
        detail.setCreateTime(new Date());
        detail.setTotalCharge(bigDecimal);
        detail.setRate(ruleVo.getRate());
        detail.setId(null);
        costDetailMapper.insert(detail);
        return ruleVo;
    }

    /**
     * 获取计算之后价格
     * @param chargeItem 收费项目数据
     * @param defaultCostRate 默认费用/费率
     * @param weight2 重量
     * @param volume2 体积
     * @return 价格
     */
    private BigDecimal getBigDecimal(ChargeItem chargeItem, BigDecimal defaultCostRate, BigDecimal weight2, BigDecimal volume2, Integer quantity2) {
        BigDecimal decimal = new BigDecimal(0);
        Integer defaultBillingMethod = chargeItem.getDefaultBillingMethod();
        switch (defaultBillingMethod) {
            case 0:
                decimal = bigDecimalRound(chargeItem.getRoundRule(), defaultCostRate);
                break;
            case 1:
                BigDecimal weight = defaultCostRate.multiply(weight2);
                decimal = bigDecimalRound(chargeItem.getRoundRule(), weight);
                break;
            case 2:
                BigDecimal volume = defaultCostRate.multiply(volume2);
                decimal = bigDecimalRound(chargeItem.getRoundRule(), volume);
                break;
            case 3:
                BigDecimal quantity = defaultCostRate.multiply(new BigDecimal(quantity2));
                decimal = bigDecimalRound(chargeItem.getRoundRule(), quantity);
                break;
            default:
                break;
        }
        return decimal;
    }

    /**
     * bigDecimal类型四舍五入方法
     *
     * @param roundRule 收费项目id
     * @param defaultCostRate 收费项目id
     * @return 返回四舍五入之后的BigDecimal
     */
    private BigDecimal bigDecimalRound(Integer roundRule, BigDecimal defaultCostRate){
        switch (roundRule){
            case 0:
                // 实际
                return defaultCostRate;
            case 1:
            case 7:
            case 10:
                // 直接舍位到0.5
                // 直接进位到个位
                // 四舍五入至个位
                return defaultCostRate.setScale(0, RoundingMode.HALF_UP);
            case 2:
                // 四舍五入至0.1
                return defaultCostRate.setScale(1, BigDecimal.ROUND_HALF_UP);
            case 3:
                // 四舍五入至0.01
                return defaultCostRate.setScale(2, BigDecimal.ROUND_HALF_UP);
            case 4:
                // 四舍五入至0.001
                return defaultCostRate.setScale(3, BigDecimal.ROUND_HALF_UP);
            case 5:
            case 6:
                // 直接舍位到0.1
                // 直接进位到0.1
                return defaultCostRate.multiply(BigDecimal.TEN).setScale(0,RoundingMode.HALF_UP).divide(BigDecimal.TEN);
            case 8:
                // 直接舍位到个位
                return defaultCostRate.setScale(0, BigDecimal.ROUND_DOWN);
            case 9:
                // 直接进位到0.5
                return defaultCostRate.round(new MathContext(1, RoundingMode.HALF_UP));
            default:
                return defaultCostRate;
        }
    }

    /**
     * 计算计费重量以及价格
     *
     * @param rateItem 计费区间费率
     * @param weight 实际重量
     * @return 自动运价返回参数
     */
    private FareVo getInterval(MailPriceItem rateItem, BigDecimal weight){
        FareVo vo = new FareVo();
        // 初始化最接近的值为null 票面价格
        BigDecimal closestValue = null;
        BigDecimal chargeWeight = null;
        BigDecimal rate = null;
        // 票面
        MailFareVo faceWeight = getFaceWeight(rateItem, weight);
        Map<RateItemVo, BigDecimal> lowestFaceWeight = lowestFaceWeight(rateItem);
        // 结算
        MailFareVo rateWeight = getRateWeight(rateItem, weight);
        Map<RateItemVo, BigDecimal> lowestRateWeight = lowestRateWeight(rateItem);
        // 应收
        MailFareVo receivableWeight = getReceivableWeight(rateItem, weight);
        Map<RateItemVo, BigDecimal> lowestReceivableWeight = lowestReceivableWeight(rateItem);

        for (Map.Entry<RateItemVo, BigDecimal> bigDecimal : lowestFaceWeight.entrySet()) {
            RateItemVo key = bigDecimal.getKey();
            BigDecimal value = bigDecimal.getValue();
            boolean withinRange;
            if (key.getEndData() != null) {
                withinRange = weight.compareTo(key.getBeginData()) >= 0 && weight.compareTo(key.getEndData()) <= 0;
            } else {
                withinRange = weight.compareTo(key.getBeginData()) >= 0;
            }
            boolean beginDataGreaterThanWeight = key.getBeginData().compareTo(weight) >= 0;
            if ((withinRange || beginDataGreaterThanWeight)&& value.compareTo(faceWeight.getMultiply()) <= 0) {
                if (closestValue == null || value.compareTo(closestValue) < 0) {
                    if (weight.compareTo(key.getBeginData()) >= 0 && weight.compareTo(key.getEndData()) <= 0){
                        chargeWeight = weight;
                        closestValue = faceWeight.getMultiply();
                        rate = faceWeight.getRate();
                    }else {
                        chargeWeight = key.getBeginData();
                        closestValue = value;
                        rate = key.getRate();
                    }
                }
            }
        }
        vo.setChargeWeight(chargeWeight);
        vo.setRate(rate);
        MailFareVo wCostSum = getBigDecimal(chargeWeight, rateWeight, lowestRateWeight);
        MailFareVo rCostSum = getBigDecimal(chargeWeight, receivableWeight, lowestReceivableWeight);
        vo.setCostSum(closestValue);
        vo.setWCostSum(wCostSum.getMultiply());
        vo.setWRate(wCostSum.getRate());
        vo.setRCostSum(rCostSum.getMultiply());
        vo.setRRate(rCostSum.getRate());
        return vo;
    }

    private MailFareVo getBigDecimal(BigDecimal weight, MailFareVo receivableWeight, Map<RateItemVo, BigDecimal> lowestReceivableWeight) {
        MailFareVo vo = new MailFareVo();
        BigDecimal multiply = null;
        BigDecimal rate = null;
        for (Map.Entry<RateItemVo, BigDecimal> bigDecimal : lowestReceivableWeight.entrySet()) {
            RateItemVo key = bigDecimal.getKey();
            BigDecimal value = bigDecimal.getValue();
            boolean withinRange;
            if (key.getEndData() != null) {
                withinRange = weight.compareTo(key.getBeginData()) >= 0 && weight.compareTo(key.getEndData()) <= 0;
            } else {
                withinRange = weight.compareTo(key.getBeginData()) >= 0;
            }
            boolean beginDataGreaterThanWeight = key.getBeginData().compareTo(weight) >= 0;
            if ((withinRange || beginDataGreaterThanWeight) && value.compareTo(receivableWeight.getMultiply()) <= 0) {
                if (multiply == null || value.compareTo(multiply) < 0) {
                    if (weight.compareTo(key.getBeginData()) >= 0 && weight.compareTo(key.getEndData()) <= 0) {
                        multiply = receivableWeight.getMultiply();
                        rate = receivableWeight.getRate();
                    } else {
                        multiply = value;
                        rate = key.getRate();
                    }
                }
            }
        }
        vo.setRate(rate);
        vo.setMultiply(multiply);
        return vo;
    }

    /**
     * 计算重量区间最低计费价格
     *
     * @param rateItem 计费区间费率
     * @return 计费区间价格
     */
    private Map<RateItemVo,BigDecimal> lowestRateWeight(MailPriceItem rateItem) {

        Map<RateItemVo,BigDecimal> map = new HashMap<>();

        // (0,5)区间最低价格
        BigDecimal multiply1 = new BigDecimal(1).multiply(rateItem.getRateWeightRange5() == null ? new BigDecimal(0) : rateItem.getRateWeightRange5());
        RateItemVo vo1 = new RateItemVo();
        vo1.setBeginData(new BigDecimal(1));
        vo1.setEndData(new BigDecimal(5));
        map.put(vo1,multiply1);

        // [5,10)区间最低价格
        BigDecimal multiply5 = new BigDecimal(5).multiply(rateItem.getRateWeightRange10() == null ? new BigDecimal(0) : rateItem.getRateWeightRange10());
        RateItemVo vo2 = new RateItemVo();
        vo2.setBeginData(new BigDecimal(5));
        vo2.setEndData(new BigDecimal(10));
        map.put(vo2,multiply5);

        // [10,45)区间最低价格
        BigDecimal multiply10 = new BigDecimal(10).multiply(rateItem.getBaseRateN() == null ? new BigDecimal(0) : rateItem.getBaseRateN());
        RateItemVo vo3 = new RateItemVo();
        vo3.setBeginData(new BigDecimal(10));
        vo3.setEndData(new BigDecimal(45));
        map.put(vo3,multiply10);

        // [45,100)区间最低价格
        BigDecimal multiply45 = new BigDecimal(45).multiply(rateItem.getRateWeightRange45() == null ? new BigDecimal(0) : rateItem.getRateWeightRange45());
        RateItemVo vo4 = new RateItemVo();
        vo4.setBeginData(new BigDecimal(45));
        vo4.setEndData(new BigDecimal(100));
        map.put(vo4,multiply45);

        // [100,300)区间最低价格
        BigDecimal multiply100 = new BigDecimal(100).multiply(rateItem.getRateWeightRange100() == null ? new BigDecimal(0) : rateItem.getRateWeightRange100());
        RateItemVo vo5 = new RateItemVo();
        vo5.setBeginData(new BigDecimal(100));
        vo5.setEndData(new BigDecimal(300));
        map.put(vo5,multiply100);

        // [300,500)区间最低价格
        BigDecimal multiply300 = new BigDecimal(300).multiply(rateItem.getRateWeightRange300() == null ? new BigDecimal(0) : rateItem.getRateWeightRange300());
        RateItemVo vo6 = new RateItemVo();
        vo6.setBeginData(new BigDecimal(300));
        vo6.setEndData(new BigDecimal(500));
        map.put(vo6,multiply300);

        // [500,1000)区间最低价格
        BigDecimal multiply500 = new BigDecimal(500).multiply(rateItem.getRateWeightRange500() == null ? new BigDecimal(0) : rateItem.getRateWeightRange500());
        RateItemVo vo7 = new RateItemVo();
        vo7.setBeginData(new BigDecimal(500));
        vo7.setEndData(new BigDecimal(1000));
        map.put(vo7,multiply500);

        // [1000,∞)区间最低价格
        BigDecimal multiply1000 = new BigDecimal(1000).multiply(rateItem.getRateWeightRange1000() == null ? new BigDecimal(0) : rateItem.getRateWeightRange1000());
        RateItemVo vo8 = new RateItemVo();
        vo8.setBeginData(new BigDecimal(1000));
        vo8.setEndData(new BigDecimal(Integer.MAX_VALUE));
        map.put(vo8,multiply1000);
        return map;
    }

    private boolean isCategoryMatch(HzChargeIrRelation hzChargeRule, String categoryCode) {
        if (hzChargeRule.getCategory() != null && !hzChargeRule.getCategory().isEmpty()) {
            Set<String> categorySet = new HashSet<>(Arrays.asList(hzChargeRule.getCategory().split(",")));
            return categorySet.contains(categoryCode);
        }
        return false;
    }

    private int isCargoCodeMatch(HzChargeIrRelation hzChargeRule, String waybillCargoCode) {
        if (StringUtils.isEmpty(hzChargeRule.getCargoName())) {
            return 1;
        }
        List<IrRelationVo> relationVos = Arrays.stream(hzChargeRule.getCargoName().split(","))
                .map(cargoCodeMapper::selectIrByName)
                .collect(Collectors.toList());

        List<IrRelationVo> cargoNames = Arrays.stream(hzChargeRule.getCategory().split(","))
                .flatMap(category -> cargoCodeMapper.selectListByCategory(Collections.singletonList(category)).stream())
                .collect(Collectors.toList());

        Map<String, List<IrRelationVo>> relationVosMap = relationVos.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(IrRelationVo::getCategoryCode));

        List<IrRelationVo> combinedList = new ArrayList<>();
        Set<String> addedCodes = new HashSet<>();
        for (IrRelationVo vo : cargoNames) {
            String code = vo.getCategoryCode();
            if (relationVosMap.containsKey(code) && !addedCodes.contains(code)) {
                combinedList.addAll(relationVosMap.get(code));
                addedCodes.add(code);
            } else if (!addedCodes.contains(code)) {
                combinedList.add(vo);
            }
        }
        List<String> collect1 = combinedList.stream().map(IrRelationVo::getCode).collect(Collectors.toList());
        if (collect1.stream().anyMatch(cargoCode -> cargoCode.equals(waybillCargoCode))) {
            String category = hzChargeRule.getCategory();
            StringBuilder stringBuffer = new StringBuilder();
            if(category != null){
                String[] split = category.split(",");
                for (String categoryCode:split) {
                    List<BaseCargoCode> cargoCodeList = cargoCodeMapper.selectList(new QueryWrapper<>(new BaseCargoCode())
                            .eq("category_code", categoryCode)
                            .eq("is_del",0));
                    if(cargoCodeList!=null && cargoCodeList.size() > 0){
                        for (BaseCargoCode e:cargoCodeList) {
                            stringBuffer.append(e.getCode());
                        }
                    }
                }
            }else{
                return 1;
            }
            if (stringBuffer.toString().contains(waybillCargoCode)) {
                return 2;
            } else {
                return 1;
            }
        }
        return 0;
    }

    private MailFareVo getFaceWeight(MailPriceItem rateItem, BigDecimal weight){
        MailFareVo vo = new MailFareVo();
        BigDecimal multiply = new BigDecimal(0);
        BigDecimal rate = new BigDecimal(0);
        // (0,5)区间实际重量价格
        if (weight.compareTo(new BigDecimal(0)) > 0 && weight.compareTo(new BigDecimal(5)) < 0){
            multiply = weight.multiply(rateItem.getFaceWeightRange5() == null ? new BigDecimal(0) : rateItem.getFaceWeightRange5());
            rate = rateItem.getFaceWeightRange5();
        }
        // [5,10)区间实际重量价格
        if (weight.compareTo(new BigDecimal(5)) >= 0 && weight.compareTo(new BigDecimal(10)) < 0){
            multiply = weight.multiply(rateItem.getFaceWeightRange10() == null ? new BigDecimal(0) : rateItem.getFaceWeightRange10());
            rate = rateItem.getFaceWeightRange10();
        }
        // [10,45)区间实际重量价格
        if (weight.compareTo(new BigDecimal(10)) >= 0 && weight.compareTo(new BigDecimal(45)) < 0){
            multiply = weight.multiply(rateItem.getFaceWeightRangeN() == null ? new BigDecimal(0) : rateItem.getFaceWeightRangeN());
            rate = rateItem.getFaceWeightRangeN();
        }
        // [45,100)区间实际重量价格
        if (weight.compareTo(new BigDecimal(45)) >= 0 && weight.compareTo(new BigDecimal(100)) < 0){
            multiply = weight.multiply(rateItem.getFaceWeightRange45() == null ? new BigDecimal(0) : rateItem.getFaceWeightRange45());
            rate = rateItem.getFaceWeightRange45();
        }
        // [100,300)区间实际重量价格
        if (weight.compareTo(new BigDecimal(100)) >= 0 && weight.compareTo(new BigDecimal(300)) < 0){
            multiply = weight.multiply(rateItem.getFaceWeightRange100() == null ? new BigDecimal(0) : rateItem.getFaceWeightRange100());
            rate = rateItem.getFaceWeightRange100();
        }
        // [300,500)区间实际重量价格
        if (weight.compareTo(new BigDecimal(300)) >= 0 && weight.compareTo(new BigDecimal(500)) < 0){
            multiply = weight.multiply(rateItem.getFaceWeightRange300() == null ? new BigDecimal(0) : rateItem.getFaceWeightRange300());
            rate = rateItem.getFaceWeightRange300();
        }
        // [500,1000)区间实际重量价格
        if (weight.compareTo(new BigDecimal(500)) >= 0 && weight.compareTo(new BigDecimal(1000)) < 0){
            multiply = weight.multiply(rateItem.getFaceWeightRange500() == null ? new BigDecimal(0) : rateItem.getFaceWeightRange500());
            rate = rateItem.getFaceWeightRange500();
        }
        if (weight.compareTo(new BigDecimal(1000)) >= 0){
            multiply = weight.multiply(rateItem.getFaceWeightRange1000() == null ? new BigDecimal(0) : rateItem.getFaceWeightRange1000());
            rate = rateItem.getFaceWeightRange1000();
        }
        if (rateItem.getFaceWeightRange5() == null
                && rateItem.getFaceWeightRange10() == null
                && weight.compareTo(new BigDecimal(45)) <= 0){
            multiply = weight.multiply(rateItem.getFaceWeightRangeN() == null ? new BigDecimal(0) : rateItem.getFaceWeightRangeN());
            rate = rateItem.getFaceWeightRangeN();
        }
        vo.setMultiply(multiply);
        vo.setRate(rate);
        return vo;
    }

    private MailFareVo getRateWeight(MailPriceItem rateItem, BigDecimal weight){
        MailFareVo vo = new MailFareVo();
        BigDecimal multiply = new BigDecimal(0);
        BigDecimal rate = new BigDecimal(0);
        // (0,5)区间实际重量价格
        if (weight.compareTo(new BigDecimal(0)) > 0 && weight.compareTo(new BigDecimal(5)) < 0){
            multiply = weight.multiply(rateItem.getRateWeightRange5() == null ? new BigDecimal(0) : rateItem.getRateWeightRange5());
            rate = rateItem.getRateWeightRange5();
        }
        // [5,10)区间实际重量价格
        if (weight.compareTo(new BigDecimal(5)) >= 0 && weight.compareTo(new BigDecimal(10)) < 0){
            multiply = weight.multiply(rateItem.getRateWeightRange10() == null ? new BigDecimal(0) : rateItem.getRateWeightRange10());
            rate = rateItem.getRateWeightRange10();
        }
        // [10,45)区间实际重量价格
        if (weight.compareTo(new BigDecimal(10)) >= 0 && weight.compareTo(new BigDecimal(45)) < 0){
            multiply = weight.multiply(rateItem.getBaseRateN() == null ? new BigDecimal(0) : rateItem.getBaseRateN());
            rate = rateItem.getBaseRateN();
        }
        // [45,100)区间实际重量价格
        if (weight.compareTo(new BigDecimal(45)) >= 0 && weight.compareTo(new BigDecimal(100)) < 0){
            multiply = weight.multiply(rateItem.getRateWeightRange45() == null ? new BigDecimal(0) : rateItem.getRateWeightRange45());
            rate = rateItem.getRateWeightRange45();
        }
        // [100,300)区间实际重量价格
        if (weight.compareTo(new BigDecimal(100)) >= 0 && weight.compareTo(new BigDecimal(300)) < 0){
            multiply = weight.multiply(rateItem.getRateWeightRange100() == null ? new BigDecimal(0) : rateItem.getRateWeightRange100());
            rate = rateItem.getRateWeightRange100();
        }
        // [300,500)区间实际重量价格
        if (weight.compareTo(new BigDecimal(300)) >= 0 && weight.compareTo(new BigDecimal(500)) < 0){
            multiply = weight.multiply(rateItem.getRateWeightRange300() == null ? new BigDecimal(0) : rateItem.getRateWeightRange300());
            rate = rateItem.getRateWeightRange300();
        }
        // [500,1000)区间实际重量价格
        if (weight.compareTo(new BigDecimal(500)) >= 0 && weight.compareTo(new BigDecimal(1000)) < 0){
            multiply = weight.multiply(rateItem.getRateWeightRange500() == null ? new BigDecimal(0) : rateItem.getRateWeightRange500());
            rate = rateItem.getRateWeightRange500();
        }
        if (weight.compareTo(new BigDecimal(1000)) >= 0){
            multiply = weight.multiply(rateItem.getRateWeightRange1000() == null ? new BigDecimal(0) : rateItem.getRateWeightRange1000());
            rate = rateItem.getRateWeightRange1000();
        }
        if (rateItem.getRateWeightRange5() == null
                && rateItem.getRateWeightRange10() == null
                && weight.compareTo(new BigDecimal(45)) <= 0){
            multiply = weight.multiply(rateItem.getBaseRateN() == null ? new BigDecimal(0) : rateItem.getBaseRateN());
            rate = rateItem.getBaseRateN();
        }
        vo.setMultiply(multiply);
        vo.setRate(rate);
        return vo;
    }

    private MailFareVo getReceivableWeight(MailPriceItem rateItem, BigDecimal weight){
        MailFareVo vo = new MailFareVo();
        BigDecimal multiply = new BigDecimal(0);
        BigDecimal rate = new BigDecimal(0);
        // (0,5)区间实际重量价格
        if (weight.compareTo(new BigDecimal(0)) > 0 && weight.compareTo(new BigDecimal(5)) < 0){
            multiply = weight.multiply(rateItem.getReceivableWeightRange5() == null ? new BigDecimal(0) : rateItem.getReceivableWeightRange5());
            rate = rateItem.getReceivableWeightRange5();
        }
        // [5,10)区间实际重量价格
        if (weight.compareTo(new BigDecimal(5)) >= 0 && weight.compareTo(new BigDecimal(10)) < 0){
            multiply = weight.multiply(rateItem.getReceivableWeightRange10() == null ? new BigDecimal(0) : rateItem.getReceivableWeightRange10());
            rate = rateItem.getReceivableWeightRange10();
        }
        // [10,45)区间实际重量价格
        if (weight.compareTo(new BigDecimal(10)) >= 0 && weight.compareTo(new BigDecimal(45)) < 0){
            multiply = weight.multiply(rateItem.getBaseReceivableN() == null ? new BigDecimal(0) : rateItem.getBaseReceivableN());
            rate = rateItem.getBaseReceivableN();
        }
        // [45,100)区间实际重量价格
        if (weight.compareTo(new BigDecimal(45)) >= 0 && weight.compareTo(new BigDecimal(100)) < 0){
            multiply = weight.multiply(rateItem.getReceivableWeightRange45() == null ? new BigDecimal(0) : rateItem.getReceivableWeightRange45());
            rate = rateItem.getReceivableWeightRange45();
        }
        // [100,300)区间实际重量价格
        if (weight.compareTo(new BigDecimal(100)) >= 0 && weight.compareTo(new BigDecimal(300)) < 0){
            multiply = weight.multiply(rateItem.getReceivableWeightRange100() == null ? new BigDecimal(0) : rateItem.getReceivableWeightRange100());
            rate = rateItem.getReceivableWeightRange100();
        }
        // [300,500)区间实际重量价格
        if (weight.compareTo(new BigDecimal(300)) >= 0 && weight.compareTo(new BigDecimal(500)) < 0){
            multiply = weight.multiply(rateItem.getReceivableWeightRange300() == null ? new BigDecimal(0) : rateItem.getReceivableWeightRange300());
            rate = rateItem.getReceivableWeightRange300();
        }
        // [500,1000)区间实际重量价格
        if (weight.compareTo(new BigDecimal(500)) >= 0 && weight.compareTo(new BigDecimal(1000)) < 0){
            multiply = weight.multiply(rateItem.getReceivableWeightRange500() == null ? new BigDecimal(0) : rateItem.getReceivableWeightRange500());
            rate = rateItem.getReceivableWeightRange500();
        }
        if (weight.compareTo(new BigDecimal(1000)) >= 0){
            multiply = weight.multiply(rateItem.getReceivableWeightRange1000() == null ? new BigDecimal(0) : rateItem.getReceivableWeightRange1000());
            rate = rateItem.getReceivableWeightRange1000();
        }
        if (rateItem.getReceivableWeightRange5() == null
                && rateItem.getReceivableWeightRange10() == null
                && weight.compareTo(new BigDecimal(45)) <= 0){
            multiply = weight.multiply(rateItem.getBaseReceivableN() == null ? new BigDecimal(0) : rateItem.getBaseReceivableN());
            rate = rateItem.getBaseReceivableN();
        }
        vo.setMultiply(multiply);
        vo.setRate(rate);
        return vo;
    }

    private Map<RateItemVo,BigDecimal> lowestFaceWeight(MailPriceItem rateItem) {

        Map<RateItemVo,BigDecimal> map = new HashMap<>();

        // (0,5)区间最低价格
        BigDecimal multiply1 = new BigDecimal(1).multiply(rateItem.getFaceWeightRange5() == null ? new BigDecimal(0) : rateItem.getFaceWeightRange5());
        RateItemVo vo1 = new RateItemVo();
        vo1.setBeginData(new BigDecimal(1));
        vo1.setEndData(new BigDecimal(5));
        map.put(vo1,multiply1);

        // [5,10)区间最低价格
        BigDecimal multiply5 = new BigDecimal(5).multiply(rateItem.getFaceWeightRange10() == null ? new BigDecimal(0) : rateItem.getFaceWeightRange10());
        RateItemVo vo2 = new RateItemVo();
        vo2.setBeginData(new BigDecimal(5));
        vo2.setEndData(new BigDecimal(10));
        map.put(vo2,multiply5);

        // [10,45)区间最低价格
        BigDecimal multiply10 = new BigDecimal(10).multiply(rateItem.getFaceWeightRangeN() == null ? new BigDecimal(0) : rateItem.getFaceWeightRangeN());
        RateItemVo vo3 = new RateItemVo();
        vo3.setBeginData(new BigDecimal(10));
        vo3.setEndData(new BigDecimal(45));
        map.put(vo3,multiply10);

        // [45,100)区间最低价格
        BigDecimal multiply45 = new BigDecimal(45).multiply(rateItem.getFaceWeightRange45() == null ? new BigDecimal(0) : rateItem.getFaceWeightRange45());
        RateItemVo vo4 = new RateItemVo();
        vo4.setBeginData(new BigDecimal(45));
        vo4.setEndData(new BigDecimal(100));
        map.put(vo4,multiply45);

        // [100,300)区间最低价格
        BigDecimal multiply100 = new BigDecimal(100).multiply(rateItem.getFaceWeightRange100() == null ? new BigDecimal(0) : rateItem.getFaceWeightRange100());
        RateItemVo vo5 = new RateItemVo();
        vo5.setBeginData(new BigDecimal(100));
        vo5.setEndData(new BigDecimal(300));
        map.put(vo5,multiply100);

        // [300,500)区间最低价格
        BigDecimal multiply300 = new BigDecimal(300).multiply(rateItem.getFaceWeightRange300() == null ? new BigDecimal(0) : rateItem.getFaceWeightRange300());
        RateItemVo vo6 = new RateItemVo();
        vo6.setBeginData(new BigDecimal(300));
        vo6.setEndData(new BigDecimal(500));
        map.put(vo6,multiply300);

        // [500,1000)区间最低价格
        BigDecimal multiply500 = new BigDecimal(500).multiply(rateItem.getFaceWeightRange500() == null ? new BigDecimal(0) : rateItem.getFaceWeightRange500());
        RateItemVo vo7 = new RateItemVo();
        vo7.setBeginData(new BigDecimal(500));
        vo7.setEndData(new BigDecimal(1000));
        map.put(vo7,multiply500);

        // [1000,∞)区间最低价格
        BigDecimal multiply1000 = new BigDecimal(1000).multiply(rateItem.getFaceWeightRange1000() == null ? new BigDecimal(0) : rateItem.getFaceWeightRange1000());
        RateItemVo vo8 = new RateItemVo();
        vo8.setBeginData(new BigDecimal(1000));
        vo8.setEndData(new BigDecimal(Integer.MAX_VALUE));
        map.put(vo8,multiply1000);
        return map;
    }

    private Map<RateItemVo,BigDecimal> lowestReceivableWeight(MailPriceItem rateItem) {

        Map<RateItemVo,BigDecimal> map = new HashMap<>();

        // (0,5)区间最低价格
        BigDecimal multiply1 = new BigDecimal(1).multiply(rateItem.getReceivableWeightRange5() == null ? new BigDecimal(0) : rateItem.getReceivableWeightRange5());
        RateItemVo vo1 = new RateItemVo();
        vo1.setBeginData(new BigDecimal(1));
        vo1.setEndData(new BigDecimal(5));
        map.put(vo1,multiply1);

        // [5,10)区间最低价格
        BigDecimal multiply5 = new BigDecimal(5).multiply(rateItem.getReceivableWeightRange10() == null ? new BigDecimal(0) : rateItem.getReceivableWeightRange10());
        RateItemVo vo2 = new RateItemVo();
        vo2.setBeginData(new BigDecimal(5));
        vo2.setEndData(new BigDecimal(10));
        map.put(vo2,multiply5);

        // [10,45)区间最低价格
        BigDecimal multiply10 = new BigDecimal(10).multiply(rateItem.getBaseReceivableN() == null ? new BigDecimal(0) : rateItem.getBaseReceivableN());
        RateItemVo vo3 = new RateItemVo();
        vo3.setBeginData(new BigDecimal(10));
        vo3.setEndData(new BigDecimal(45));
        map.put(vo3,multiply10);

        // [45,100)区间最低价格
        BigDecimal multiply45 = new BigDecimal(45).multiply(rateItem.getReceivableWeightRange45() == null ? new BigDecimal(0) : rateItem.getReceivableWeightRange45());
        RateItemVo vo4 = new RateItemVo();
        vo4.setBeginData(new BigDecimal(45));
        vo4.setEndData(new BigDecimal(100));
        map.put(vo4,multiply45);

        // [100,300)区间最低价格
        BigDecimal multiply100 = new BigDecimal(100).multiply(rateItem.getReceivableWeightRange100() == null ? new BigDecimal(0) : rateItem.getReceivableWeightRange100());
        RateItemVo vo5 = new RateItemVo();
        vo5.setBeginData(new BigDecimal(100));
        vo5.setEndData(new BigDecimal(300));
        map.put(vo5,multiply100);

        // [300,500)区间最低价格
        BigDecimal multiply300 = new BigDecimal(300).multiply(rateItem.getReceivableWeightRange300() == null ? new BigDecimal(0) : rateItem.getReceivableWeightRange300());
        RateItemVo vo6 = new RateItemVo();
        vo6.setBeginData(new BigDecimal(300));
        vo6.setEndData(new BigDecimal(500));
        map.put(vo6,multiply300);

        // [500,1000)区间最低价格
        BigDecimal multiply500 = new BigDecimal(500).multiply(rateItem.getReceivableWeightRange500() == null ? new BigDecimal(0) : rateItem.getReceivableWeightRange500());
        RateItemVo vo7 = new RateItemVo();
        vo7.setBeginData(new BigDecimal(500));
        vo7.setEndData(new BigDecimal(1000));
        map.put(vo7,multiply500);

        // [1000,∞)区间最低价格
        BigDecimal multiply1000 = new BigDecimal(1000).multiply(rateItem.getReceivableWeightRange1000() == null ? new BigDecimal(0) : rateItem.getReceivableWeightRange1000());
        RateItemVo vo8 = new RateItemVo();
        vo8.setBeginData(new BigDecimal(1000));
        vo8.setEndData(new BigDecimal(Integer.MAX_VALUE));
        map.put(vo8,multiply1000);
        return map;
    }
}
