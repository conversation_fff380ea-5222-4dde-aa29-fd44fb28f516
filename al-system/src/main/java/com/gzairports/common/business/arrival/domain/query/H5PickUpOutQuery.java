package com.gzairports.common.business.arrival.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 代理人H5提货出库查询参数
 *
 * <AUTHOR>
 * @date 2024-08-06
 */
@Data
public class H5PickUpOutQuery {

    /** 流水号 */
    private String serialNo;

    /** 办单完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date pickUpTime;

    /** 办单完成时间 */
    private Date startTime;

    /** 办单完成时间 */
    private Date endTime;

    /** 代理人 */
    private Long deptId;
}
