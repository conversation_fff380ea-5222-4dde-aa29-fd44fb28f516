package com.gzairports.wl.charge.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 客户运价与航司运价关联表表
 *
 * <AUTHOR>
 * @date 2024-12-10
 */
@Data
@TableName("wl_sf_custom_air")
public class FreightRateCustomAir {

    /** 主键id */
    @TableId(value = "id")
    private Long id;

    /** 客户运价id */
    private Long customPriceId;

    /** 货品大类 */
    private String cargoCategory;

    /** 货品代码 */
    private String cargoCode;

    /** 货物品名 */
    private String cargoName;

    /** 运价重量区间5 */
    @TableField(value = "rate_weight_range_5")
    private BigDecimal rateWeightRange5;

    /** 运价重量区间10 */
    @TableField(value = "rate_weight_range_10")
    private BigDecimal rateWeightRange10;

    /** 基础运价N */
    private BigDecimal baseRateN;

    /** 运价重量区间45 */
    @TableField(value = "rate_weight_range_45")
    private BigDecimal rateWeightRange45;

    /** 运价重量区间100 */
    @TableField(value = "rate_weight_range_100")
    private BigDecimal rateWeightRange100;

    /** 运价重量区间300 */
    @TableField(value = "rate_weight_range_300")
    private BigDecimal rateWeightRange300;

    /** 运价重量区间500 */
    @TableField(value = "rate_weight_range_500")
    private BigDecimal rateWeightRange500;

    /** 运价重量区间1000 */
    @TableField(value = "rate_weight_range_1000")
    private BigDecimal rateWeightRange1000;

    /** 最低运费 */
    private BigDecimal minimumFreight;

    private Integer isDel;
}
