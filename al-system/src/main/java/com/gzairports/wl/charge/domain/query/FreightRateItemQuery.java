package com.gzairports.wl.charge.domain.query;

import lombok.Data;

import java.util.List;

/**
 * 公布运价条目查询参数
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@Data
public class FreightRateItemQuery {

    /** 运价id */
    private Long id;

    /** 运价名称 */
    private String rateName;

    /** 适用单证类型 */
    private String applicableDocuments;

    /** 始发城市 */
    private String departureCity;

    /** 始发城市 */
    private List<String> sourcePort;

    /** 目的城市 */
    private String destinationCity;

    /** 目的城市 */
    private List<String> desPort;

    /** 最低运费 */
    private String minimumFreight;

    /** 所属单位 */
    private Long deptId;
}
