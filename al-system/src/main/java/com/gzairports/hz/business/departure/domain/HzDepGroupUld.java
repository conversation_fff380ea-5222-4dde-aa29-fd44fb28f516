package com.gzairports.hz.business.departure.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 组货调度板箱表
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
@Data
@TableName("hz_dep_group_uld")
public class HzDepGroupUld {

    /** 组货数据id */
    private Long id;

    /** 航班配载id */
    private Long flightLoadId;

    /** 板车号 */
    private String uld;

    /** 目的站1 */
    private String des1;

    /** 板位 */
    private String cabin;

    /** 板重 */
    @TableField(exist = false)
    private BigDecimal cabinWeight;

    /** 货数 */
    @TableField(exist = false)
    private Integer cargoQuantity;

    /** 货重 */
    @TableField(exist = false)
    private BigDecimal cargoWeight;

    /** 邮数 */
    @TableField(exist = false)
    private Integer mailQuantity;

    /** 邮重 */
    @TableField(exist = false)
    private BigDecimal mailWeight;

    /** 真实板车号 */
    private String realUld;

    /** 特货 */
    private String specialCode;

    /** 复重重量 */
    private BigDecimal repeatWeight;
}
