package com.gzairports.common.core.domain.entity;

import com.gzairports.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

/**
 * 部门表 sys_dept
 * 
 * <AUTHOR>
 */
@Data
public class SysDept extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 部门ID */
    private Long deptId;

    /** 父部门ID */
    private Long parentId;

    /** 祖级列表 */
    private String ancestors;

    /** 部门名称 */
    @NotBlank(message = "部门名称不能为空")
    @Size(min = 0, max = 30, message = "部门名称长度不能超过30个字符")
    private String deptName;

    /** 显示顺序 */
    @NotNull(message = "显示顺序不能为空")
    private Integer orderNum;

    /** 负责人 */
    private String leader;

    /** 联系电话 */
    @Size(min = 0, max = 11, message = "联系电话长度不能超过11个字符")
    private String phone;

    /** 邮箱 */
    @Email(message = "邮箱格式不正确")
    @Size(min = 0, max = 50, message = "邮箱长度不能超过50个字符")
    private String email;

    /** 部门状态:0正常,1停用 */
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 父部门名称 */
    private String parentName;
    
    /** 子部门 */
    private List<SysDept> children = new ArrayList<SysDept>();

    /**  出港简称   */
    private String deptNameAbb;

    /**  进港简称   */
    private String deptNameAbbIn;

    /**  机构编码   */
    private String orgCode;

    /**  代理人识别码   */
    private String agentCode;

    /**  地址   */
    private String address;

    /**  邮编   */
    private String postcode;

    /** 描述信息    */
    private String description;

    /** 二维码地址 */
    private String codeUrl;

    /** 电子印章 */
    private String sealUrl;

    /** 代理人大logo */
    private String logoUrlBig;

    /**代理人小logo*/
    private String logoUrlSmall;

    /** 当前登录人的账号 */
    private String userName;

    /** 安检申报姓名 */
    private String createUser;

    /** 单位类型(1-代理人(默认), 2-航司) */
    @NotNull(message = "单位类型不能为空")
    private Integer deptType;

    /** 可查询航司二字码 */
    private String airlineCodes;

    /** 可查询运单前缀 */
    private String waybillPrefixes;

//    @Override
//    public String toString() {
//        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
//            .append("deptId", getDeptId())
//            .append("parentId", getParentId())
//            .append("ancestors", getAncestors())
//            .append("deptName", getDeptName())
//            .append("orderNum", getOrderNum())
//            .append("leader", getLeader())
//            .append("phone", getPhone())
//            .append("email", getEmail())
//            .append("status", getStatus())
//            .append("delFlag", getDelFlag())
//            .append("createBy", getCreateBy())
//            .append("createTime", getCreateTime())
//            .append("updateBy", getUpdateBy())
//            .append("updateTime", getUpdateTime())
//            .append("codeUrl", getCodeUrl())
//            .append("createUser", getCreateUser())
//            .append("airlineCodes", getAirlineCodes())
//            .append("waybillPrefixes", getWaybillPrefixes())
//            .toString();
//    }
}
