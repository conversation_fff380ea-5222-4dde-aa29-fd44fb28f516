package com.gzairports.common.business.departure.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 运单费用明细表
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@Data
@TableName("wl_waybill_fee")
public class WaybillFee {

    /** 主键id */
    private Long id;

    /** 预支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "预支付时间")
    private Date payTime;

    /** 预支付金额 */
    @Excel(name = "预支付金额")
    private BigDecimal payMoney;

    /** 结算时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结算时间")
    private Date settleTime;

    /** 结算金额 */
    @Excel(name = "结算金额")
    private BigDecimal settleMoney;

    /** 结算退款金额 */
    @Excel(name = "结算退款金额")
    private BigDecimal refund;

    /** 状态（0 预授权 1 已结算 2 已退款） */
    private Integer status;

    @TableField(exist = false)
    @Excel(name = "状态")
    private String strStart;

    /** 运单号 */
    @Excel(name = "运单号")
    private String waybillCode;

    /** 流水号 */
    @Excel(name = "流水号")
    private String serialNo;

    /** 进出港类型 进港 ARR 出港 DEP*/
    private String type;

    /** 所属单位 */
    private Long deptId;
}
