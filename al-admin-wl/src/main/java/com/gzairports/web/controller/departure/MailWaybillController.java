package com.gzairports.web.controller.departure;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.basedata.domain.BaseAgent;
import com.gzairports.common.basedata.domain.BaseCarrier;
import com.gzairports.common.basedata.domain.BaseCityCode;
import com.gzairports.common.basedata.domain.query.CityCodeQuery;
import com.gzairports.common.basedata.mapper.CarrierMapper;
import com.gzairports.common.basedata.service.IAirportCodeService;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.domain.entity.SysDept;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.business.departure.domain.MailWaybill;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.pdf.PdfPrintHelper;
import com.gzairports.common.securitySubmit.domain.AllSecurityWaybill;
import com.gzairports.common.securitySubmit.mapper.AllSecurityWaybillMapper;
import com.gzairports.common.system.domain.SysConfig;
import com.gzairports.common.system.mapper.SysConfigMapper;
import com.gzairports.common.system.service.ISysDeptService;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.common.utils.sign.Base64;
import com.gzairports.common.utils.spring.SpringUtils;
import com.gzairports.oss.domain.SysOss;
import com.gzairports.oss.service.impl.SysOssServiceImpl;
import com.gzairports.wl.departure.domain.query.FareQuery;
import com.gzairports.wl.departure.domain.query.HawbQuery;
import com.gzairports.wl.departure.domain.vo.EditWaybillCode;
import com.gzairports.wl.departure.domain.vo.MailWaybillVo;
import com.gzairports.wl.departure.domain.vo.MawbVo;
import com.gzairports.wl.departure.service.IMailWaybillService;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 邮件单制单Controller
 *
 * <AUTHOR>
 * @date 2024-05-24
 */
@RestController
@RequestMapping("/dep/mail")
public class MailWaybillController extends BaseController {

    @Autowired
    private IMailWaybillService mailWaybillService;

    @Autowired
    private ISysDeptService sysDeptService;

    @Autowired
    private IAirportCodeService airportCodeService;

    @Autowired
    private CarrierMapper carrierMapper;

    @Autowired
    private AllSecurityWaybillMapper allSecurityWaybillMapper;

    @Autowired
    private SysConfigMapper configMapper;

    /**
     * 运单校验
     */
    @PreAuthorize("@ss.hasPermi('dep:mail:check')")
    @GetMapping("/check/{waybillCode}")
    @ApiOperation(value = "运单校验")
    public AjaxResult check(@PathVariable("waybillCode") String waybillCode)
    {
        return AjaxResult.success(mailWaybillService.check(waybillCode));
    }

    /**
     * 自动运价
     */
    @PreAuthorize("@ss.hasPermi('dep:mail:fare')")
    @PostMapping("/fare")
    @ApiOperation(value = "自动运价")
    public AjaxResult fare(@RequestBody FareQuery query)
    {
        return AjaxResult.success(mailWaybillService.fare(query));
    }

    /**
     * 根据运单号查询历史运单
     */
    @PreAuthorize("@ss.hasPermi('dep:mail:getInfo')")
    @GetMapping("/getInfo/{waybillCode}")
    @ApiOperation(value = "运单详情")
    public AjaxResult getInfo(@PathVariable("waybillCode") String waybillCode)
    {
        return AjaxResult.success(mailWaybillService.getInfo(waybillCode));
    }

    /**
     * 运单新增
     */
    @PreAuthorize("@ss.hasPermi('dep:mail:add')")
    @PostMapping("/add")
    @ApiOperation(value = "暂存与保存并发送")
    public AjaxResult add(@RequestBody MailWaybill waybill) throws Exception {
        if (waybill.getChargeWeight() != null && waybill.getWeight() != null){
            if (waybill.getChargeWeight().compareTo(waybill.getWeight()) < 0){
                throw new CustomException("计费重量不能小于实际重量");
            }
        }
        if(waybill.getQuantity() == null){
            waybill.setQuantity(0);
        }
        if(waybill.getWeight() == null){
            waybill.setWeight(BigDecimal.ZERO);
        }
        if(waybill.getChargeWeight() == null){
            waybill.setChargeWeight(BigDecimal.ZERO);
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if (waybill.getWaybillCode() != null){
            String substring = waybill.getWaybillCode().substring(6);
            waybill.setWaybillCodeAbb(substring);
        }
        SysDept dept = sysDeptService.selectDeptById(SecurityUtils.getHighParentId());
        if (dept != null && StringUtils.isNotEmpty(dept.getSealUrl())){
            byte[] bytes = downloadFileFromUrl(dept.getSealUrl());
            String sealImage = Base64.encode(bytes);
            waybill.setSealUrl(sealImage);
        }
        String desPort = airportCodeService.selectChineseName(waybill.getDesPort());
        waybill.setDesPortStr(desPort);
        waybill.setSourcePortStr("贵阳");
        if ("08".equals(waybill.getCategoryName())){
            waybill.setPRatePerKg(waybill.getRatePerKg());
            waybill.setPCostSum(waybill.getCostSum());
        }
        if ("01".equals(waybill.getCategoryName())){
            waybill.setTRatePerKg(waybill.getRatePerKg());
            waybill.setTCostSum(waybill.getCostSum());
        }
        if (waybill.getWriteTime() != null){
            String format = dateFormat.format(waybill.getWriteTime());
            waybill.setWriteTimeStr(format);
        }else {
            waybill.setWriteTime(new Date());
            String format = dateFormat.format(new Date());
            waybill.setWriteTimeStr(format);
        }
        if (waybill.getFlightDate1() != null){
            String format = dateFormat.format(waybill.getFlightDate1());
            waybill.setExecDate(format);
            waybill.setFlightInfo(waybill.getFlightNo1() + "/" +format);
        }
        if (waybill.getFlightDate2() != null){
            String format = dateFormat.format(waybill.getFlightDate2());
            waybill.setFlightInfo2(waybill.getFlightNo2() + "/" +format);
        }
        if (waybill.getFlightDate3() != null){
            String format = dateFormat.format(waybill.getFlightDate3());
            waybill.setFlightInfo3(waybill.getFlightNo3() + "/" +format);
        }

        if(waybill.getWeight()!=null && waybill.getWeight().compareTo(BigDecimal.ZERO) != 0){
            waybill.setWeight(waybill.getWeight().setScale(0, RoundingMode.DOWN));
        }
        if(waybill.getChargeWeight()!=null && waybill.getChargeWeight().compareTo(BigDecimal.ZERO) != 0){
            waybill.setChargeWeight(waybill.getChargeWeight().setScale(0, RoundingMode.DOWN));
        }

        if(waybill.getVolume() != null){
            waybill.setVolumeUnit(waybill.getVolume().setScale(2, RoundingMode.DOWN) + "m³");
        }
        if(StringUtils.isNotEmpty(waybill.getCargoName())){
            waybill.setCargoNameStr(waybill.getCargoName());
        }else{
            if ("12".equals(waybill.getCategoryName())){
                waybill.setCargoNameStr("经邮");
            }
            if ("08".equals(waybill.getCategoryName())){
                waybill.setCargoNameStr("普邮");
            }
            if ("01".equals(waybill.getCategoryName())){
                waybill.setCargoNameStr("特快");
            }
        }

        SysConfig sysConfig = configMapper.checkConfigKeyUnique("mail.print.sourcePort");
        if(sysConfig != null){
            waybill.setConfigSourcePort(sysConfig.getConfigValue());
        }

        String pdfUrl = getPdfUrl(waybill, "electronic/mail.pdf");
        String carrier1 = waybill.getCarrier1();
        BaseCarrier baseCarrier = carrierMapper.selectByCode(carrier1);
        if(baseCarrier!=null){
            waybill.setCarrier1(baseCarrier.getChineseName());
        }
        String securityUrl;
        AllSecurityWaybill allSecurityWaybill = allSecurityWaybillMapper.selectOne(new QueryWrapper<AllSecurityWaybill>().eq("waybill_code", waybill.getWaybillCode()));
        if(allSecurityWaybill != null){
            securityUrl = allSecurityWaybill.getSecurityUrl();
        }else{
            securityUrl = getPdfUrl(waybill, "electronic/mailSecurity.pdf");
        }
        waybill.setCarrier1(carrier1);
        waybill.setSecurityUrl(securityUrl);
        waybill.setPdfUrl(pdfUrl);
        if (dept != null && StringUtils.isNotEmpty(dept.getSealUrl())){
            waybill.setSealUrl(dept.getSealUrl());
        }
        return AjaxResult.success(mailWaybillService.add(waybill));
    }

    /**
     * 编辑运单
     */
    @PreAuthorize("@ss.hasPermi('dep:mail:edit')")
    @PostMapping("/edit")
    @ApiOperation(value = "编辑运单")
    public AjaxResult edit(@RequestBody MailWaybill waybill) throws Exception
    {
        if(waybill.getQuantity() == null){
            waybill.setQuantity(0);
        }
        if(waybill.getWeight() == null){
            waybill.setWeight(BigDecimal.ZERO);
        }
        if(waybill.getChargeWeight() == null){
            waybill.setChargeWeight(BigDecimal.ZERO);
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if (waybill.getWaybillCode() != null){
            String substring = waybill.getWaybillCode().substring(6);
            waybill.setWaybillCodeAbb(substring);
        }
        SysDept dept = sysDeptService.selectDeptById(SecurityUtils.getHighParentId());
        if (dept != null && StringUtils.isNotEmpty(dept.getSealUrl())){
            byte[] bytes = downloadFileFromUrl(dept.getSealUrl());
            String sealImage = Base64.encode(bytes);
            waybill.setSealUrl(sealImage);
        }
        String desPort = airportCodeService.selectChineseName(waybill.getDesPort());
        waybill.setDesPortStr(desPort);
        waybill.setSourcePortStr("贵阳");
        if ("08".equals(waybill.getCategoryName())){
            waybill.setPRatePerKg(waybill.getRatePerKg());
            waybill.setPCostSum(waybill.getCostSum());
        }
        if ("01".equals(waybill.getCategoryName())){
            waybill.setTRatePerKg(waybill.getRatePerKg());
            waybill.setTCostSum(waybill.getCostSum());
        }
        if (waybill.getWriteTime() != null){
            String format = dateFormat.format(waybill.getWriteTime());
            waybill.setWriteTimeStr(format);
        }else {
            String format = dateFormat.format(new Date());
            waybill.setWriteTimeStr(format);
        }
        if (waybill.getFlightDate1() != null){
            String format = dateFormat.format(waybill.getFlightDate1());
            waybill.setExecDate(format);
            waybill.setFlightInfo(waybill.getFlightNo1() + "/" +format);
        }
        if (waybill.getFlightDate2() != null){
            String format = dateFormat.format(waybill.getFlightDate2());
            waybill.setFlightInfo2(waybill.getFlightNo2() + "/" +format);
        }
        if (waybill.getFlightDate3() != null){
            String format = dateFormat.format(waybill.getFlightDate3());
            waybill.setFlightInfo3(waybill.getFlightNo3() + "/" +format);
        }

        if(waybill.getWeight()!=null && waybill.getWeight().compareTo(BigDecimal.ZERO) != 0){
            waybill.setWeight(waybill.getWeight().setScale(0, RoundingMode.DOWN));
        }
        if(waybill.getChargeWeight()!=null && waybill.getChargeWeight().compareTo(BigDecimal.ZERO) != 0){
            waybill.setChargeWeight(waybill.getChargeWeight().setScale(0, RoundingMode.DOWN));
        }

        if(waybill.getVolume() != null){
            waybill.setVolumeUnit(waybill.getVolume().setScale(2, RoundingMode.DOWN) + "m³");
        }
        if(StringUtils.isNotEmpty(waybill.getCargoName())){
            waybill.setCargoNameStr(waybill.getCargoName());
        }else{
            if ("12".equals(waybill.getCategoryName())){
                waybill.setCargoNameStr("经邮");
            }
            if ("08".equals(waybill.getCategoryName())){
                waybill.setCargoNameStr("普邮");
            }
            if ("01".equals(waybill.getCategoryName())){
                waybill.setCargoNameStr("特快");
            }
        }
        SysConfig sysConfig = configMapper.checkConfigKeyUnique("mail.print.sourcePort");
        if(sysConfig != null){
            waybill.setConfigSourcePort(sysConfig.getConfigValue());
        }
        String pdfUrl = getPdfUrl(waybill, "electronic/mail.pdf");
        String carrier1 = waybill.getCarrier1();
        BaseCarrier baseCarrier = carrierMapper.selectByCode(carrier1);
        if(baseCarrier!=null){
            waybill.setCarrier1(baseCarrier.getChineseName());
        }
        String securityUrl;
        AllSecurityWaybill allSecurityWaybill = allSecurityWaybillMapper.selectOne(new QueryWrapper<AllSecurityWaybill>().eq("waybill_code", waybill.getWaybillCode()));
        if(allSecurityWaybill != null){
            securityUrl = allSecurityWaybill.getSecurityUrl();
        }else{
            securityUrl = getPdfUrl(waybill, "electronic/mailSecurity.pdf");
        }
        waybill.setCarrier1(carrier1);
        waybill.setSecurityUrl(securityUrl);
        waybill.setPdfUrl(pdfUrl);
        return toAjax(mailWaybillService.edit(waybill));
    }

    /**
     * 运单作废
     */
    @PreAuthorize("@ss.hasPermi('dep:mail:invalid')")
    @GetMapping("/invalid/{waybillCode}")
    @ApiOperation(value = "运单作废")
    public AjaxResult invalid(@PathVariable("waybillCode") String waybillCode)
    {
        return toAjax(mailWaybillService.invalid(waybillCode));
    }

    @PostMapping("/editWaybillCode")
    @ApiOperation(value = "更改运单号")
    public AjaxResult editWaybillCode(@RequestBody EditWaybillCode editWaybillCode)
    {
        return toAjax(mailWaybillService.editWaybillCode(editWaybillCode));
    }

    /**
     * 运单取消作废
     */
    @PreAuthorize("@ss.hasPermi('dep:mail:cancelVoid')")
    @GetMapping("/cancelVoid/{waybillCode}")
    @ApiOperation(value = "运单取消作废")
    public AjaxResult cancelVoid(@PathVariable("waybillCode") String waybillCode)
    {
        return toAjax(mailWaybillService.cancelVoid(waybillCode));
    }

    /**
     * 邮件单查询
     */
    @PreAuthorize("@ss.hasPermi('dep:mail:query')")
    @GetMapping("/query")
    @ApiOperation(value = "邮件单查询")
    public TableDataInfo query(HawbQuery query)
    {
        startPage();
        List<MailWaybillVo> mailWaybillVo = mailWaybillService.queryList(query);
        return getDataTable(mailWaybillVo);
    }

    /**
     * 导出邮件单
     * */
    @PreAuthorize("@ss.hasPermi('dep:mail:export')")
    @Log(title = "导出邮件单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出邮件单")
    public void export(HttpServletResponse response, HawbQuery query)
    {
        List<MailWaybillVo> list = mailWaybillService.exportList(query);
        String startTime = "";
        String endTime = "";
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (query.getStartTime() != null){
            startTime = simpleDateFormat.format(query.getStartTime());
        }
        if (query.getEndTime() != null){
            endTime = simpleDateFormat.format(query.getEndTime());
        }
        ExcelUtil<MailWaybillVo> util = new ExcelUtil<MailWaybillVo>(MailWaybillVo.class);
        util.exportExcel(response, list, "邮件单", SecurityUtils.getNickName(), null, startTime, endTime);
    }

    /**
     * 邮件单打印
     */
//    @PreAuthorize("@ss.hasPermi('dep:mail:printMailData')")
    @GetMapping(value = "/printMailData/{id}")
    @ApiOperation(value = "邮件单打印")
    public void printMailData(HttpServletResponse response, @PathVariable("id") Long id) throws Exception
    {
        mailWaybillService.printMailData(id,response);
    }

    /**
     * 取消支付
     */
//    @PreAuthorize("@ss.hasPermi('dep:mail:cancelPay')")
    @GetMapping("/cancelPay/{waybillId}")
    @ApiOperation(value = "取消支付")
    public AjaxResult cancelPay(@PathVariable("waybillId") Long waybillId)
    {
        return toAjax(mailWaybillService.cancelPay(waybillId));
    }

    private String getPdfUrl(@RequestBody MailWaybill mailWaybill, String s) throws Exception {
        ClassPathResource securityUrl = new ClassPathResource(s);
        if (securityUrl.exists()) {
            String path = securityUrl.getPath();
            byte[] pdfDataFromTemplate = PdfPrintHelper.getPdfDataFromTemplate(mailWaybill, path);
            // 使用StandardMultipartHttpServletRequest来创建MultipartFile
            DiskFileItem fileItem = new DiskFileItem(mailWaybill.getWaybillCode() + ".pdf", "application/pdf", true, mailWaybill.getWaybillCode() + ".pdf", -1, null);

            // 将 InputStream 写入 DiskFileItem
            fileItem.getOutputStream().write(pdfDataFromTemplate);
            fileItem.getOutputStream().close();

            // 使用 FileItem 构建 MultipartFile
            MultipartFile multipartFile = new CommonsMultipartFile(fileItem);
            SysOss upload = SpringUtils.getBean(SysOssServiceImpl.class).upload(multipartFile);
            return upload.getUrl();
        }
        return null;
    }

    private byte[] downloadFileFromUrl(String urlStr) throws Exception {
        // 创建一个信任所有证书的信任管理器
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }
                    @Override
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                    @Override
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
        };

        // 安装信任所有证书的信任管理器
        SSLContext sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, trustAllCerts, new java.security.SecureRandom());

        URL url = new URL(urlStr);
        HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
        connection.setSSLSocketFactory(sslContext.getSocketFactory());

        // 设置自定义 HostnameVerifier（仅限测试环境）
        connection.setHostnameVerifier((hostname, session) -> true);

        connection.setRequestMethod("GET");
        connection.setConnectTimeout(5000);
        connection.setReadTimeout(5000);


        int responseCode = connection.getResponseCode();
        if (responseCode != 200) {
            throw new RuntimeException("Failed to connect, HTTP error code : " + responseCode);
        }

        try (InputStream inputStream = connection.getInputStream();
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            return baos.toByteArray();
        }
    }
}
