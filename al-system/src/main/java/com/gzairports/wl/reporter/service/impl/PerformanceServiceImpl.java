package com.gzairports.wl.reporter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.basedata.domain.Customer;
import com.gzairports.common.basedata.mapper.CarrierMapper;
import com.gzairports.common.basedata.mapper.CustomerMapper;
import com.gzairports.common.business.reporter.domain.ReportDataItem;
import com.gzairports.common.business.reporter.mapper.ReportDataHawbMapper;
import com.gzairports.common.business.reporter.mapper.ReportDataItemMapper;
import com.gzairports.common.business.reporter.mapper.ReportDataWaybillMapper;
import com.gzairports.common.business.reporter.mapper.ReportDepPayCostMapper;
import com.gzairports.common.core.domain.entity.SysDept;
import com.gzairports.common.core.domain.entity.SysUser;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.system.mapper.SysUserMapper;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.wl.reporter.domain.query.CargoAnalysisQuery;
import com.gzairports.wl.reporter.domain.query.CarrierSumQuery;
import com.gzairports.wl.reporter.domain.vo.*;
import com.gzairports.wl.reporter.service.IPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-03-13
 */
@Service
public class PerformanceServiceImpl implements IPerformanceService {

    @Autowired
    private ReportDataWaybillMapper waybillMapper;

    @Autowired
    private ReportDataHawbMapper hawbMapper;

    @Autowired
    private ReportDepPayCostMapper payCostMapper;

    @Autowired
    private ReportDataItemMapper dataItemMapper;

    @Autowired
    private CarrierMapper carrierMapper;

    @Autowired
    private CustomerMapper customerMapper;

    @Autowired
    private SysDeptMapper deptMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 年货量分析
     * @param year 查询年份
     * @return 结果
     */
    @Override
    public YearCargoQuantityVO yearCargoAnalyse(String year) {
        YearCargoQuantityVO vo = new YearCargoQuantityVO();
        List<WaybillWeightVO> list = waybillMapper.yearCargoAnalyse(year, SecurityUtils.getHighParentId());
        Map<String, Map<String, BigDecimal>> groupedData = list.stream()
                .collect(Collectors.groupingBy(
                        WaybillWeightVO::getMonthData,
                        Collectors.toMap(
                                WaybillWeightVO::getType,
                                WaybillWeightVO::getWeight,
                                (v1, v2) -> v1
                        )
                ));
        BigDecimal lastMonth = BigDecimal.ZERO;
        MonthQuantityVO depVo = new MonthQuantityVO();
        MonthQuantityVO arrVo = new MonthQuantityVO();
        MonthQuantityVO dTotalVo = new MonthQuantityVO();
        MonthQuantityVO monthAccumulateVo = new MonthQuantityVO();
        for (int i = 1; i <= 12; i++) {
            String monthKey = String.format("%02d", i);
            Map<String, BigDecimal> monthData = groupedData.getOrDefault(monthKey, Collections.emptyMap());
            BigDecimal weight = BigDecimal.ZERO;
            for (Map.Entry<String, BigDecimal> map : monthData.entrySet()) {
                weight = weight.add(map.getValue());
            }
            lastMonth = lastMonth.add(weight);
            setMonthValue(depVo, monthKey, monthData.getOrDefault("出港", BigDecimal.ZERO));
            setMonthValue(arrVo, monthKey, monthData.getOrDefault("进港", BigDecimal.ZERO));
            setMonthValue(dTotalVo, monthKey, weight);
            setMonthValue(monthAccumulateVo, monthKey, lastMonth);
        }
        vo.setDDep(depVo);
        vo.setDArr(arrVo);
        vo.setDTotal(dTotalVo);
        vo.setMonthTotal(dTotalVo);
        vo.setMonthAccumulate(monthAccumulateVo);
        return vo;
    }

    /**
     * 年利润分析
     * @param year 查询年份
     * @return 结果
     */
    @Override
    public List<YearProfitVO> yearProfit(String year) {
        List<YearProfitVO> profitList = new ArrayList<>();
        List<CarrierDataVO> shipmentList = carrierMapper.selectIsShipmentList();
        for (CarrierDataVO carrierDataVO : shipmentList) {
            YearProfitVO vo = new YearProfitVO();
            vo.setSettleUnit(carrierDataVO.getChineseName());
            List<ReportWaybillInfoVO> list = waybillMapper.yearProfit(year, SecurityUtils.getHighParentId(),carrierDataVO.getCode());
            if (!CollectionUtils.isEmpty(list)){
                Map<String, List<ReportWaybillInfoVO>> collect = list.stream().collect(Collectors.groupingBy(ReportWaybillInfoVO::getMonthData));
                Map<String, MonthlyDataVO> monthlyDataMap = new LinkedHashMap<>(12);
                for (Map.Entry<String, List<ReportWaybillInfoVO>> entry : collect.entrySet()) {
                    MonthlyDataVO dataVO = new MonthlyDataVO();
                    BigDecimal cargoQuantity = entry.getValue().stream().map(ReportWaybillInfoVO::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                    dataVO.setCargoQuantity(cargoQuantity);
                    // 运费
                    BigDecimal shippingFee = BigDecimal.ZERO;

                    // 杂费
                    BigDecimal sundryFee = BigDecimal.ZERO;
                    for (ReportWaybillInfoVO reportWaybillInfoVO : entry.getValue()) {
                        BigDecimal hawbFee = hawbMapper.selectShippingFee(reportWaybillInfoVO.getWaybillCode());
                        shippingFee = shippingFee.add(hawbFee == null ? BigDecimal.ZERO : hawbFee);
                        BigDecimal waybillFee = dataItemMapper.selectSundryFee(reportWaybillInfoVO.getWaybillCode());
                        BigDecimal payCostFee = payCostMapper.selectSundryFee(reportWaybillInfoVO.getId());
                        sundryFee = sundryFee.add(waybillFee == null ? BigDecimal.ZERO : waybillFee).add(payCostFee == null ? BigDecimal.ZERO : payCostFee);
                    }
                    dataVO.setShippingFee(shippingFee);
                    dataVO.setSundryFee(sundryFee);
                    dataVO.setProfit(shippingFee.subtract(sundryFee));
                    monthlyDataMap.put(entry.getKey(),dataVO);
                }
                vo.setMonthlyDataMap(monthlyDataMap);
            }
            profitList.add(vo);
        }
        return profitList;
    }

    /**
     * 年月货量环比分析
     * @param query 查询参数
     * @return 结果
     */
    @Override
    public CargoVolumeAnalysisVO cargoAnalysis(CargoAnalysisQuery query) {
        CargoVolumeAnalysisVO vo = new CargoVolumeAnalysisVO();
        query.setDeptId(SecurityUtils.getHighParentId());
        BigDecimal baseValve = BigDecimal.ZERO;
        BigDecimal compareValve = BigDecimal.ZERO;
        Map<String, CargoAnalysisVO> analysisMap = new LinkedHashMap<>(12);
        List<WaybillWeightVO> weightList = new ArrayList<>();
        if (!"出港".equals(query.getType())){
            // 进港基值数据
            List<WaybillWeightVO> baseArrWeightVOList = waybillMapper.baseCargoAnalysis(query);
            if (!CollectionUtils.isEmpty(baseArrWeightVOList)){
                BigDecimal baseArrWeight = baseArrWeightVOList.stream().map(WaybillWeightVO::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                baseValve = baseValve.add(baseArrWeight);
                weightList.addAll(baseArrWeightVOList);
            }
            // 进港比较数据
            List<WaybillWeightVO> compareArrWeightVOList = waybillMapper.compareCargoAnalysis(query);
            if (!CollectionUtils.isEmpty(compareArrWeightVOList)){
                BigDecimal compareArrWeight = compareArrWeightVOList.stream().map(WaybillWeightVO::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                compareValve = compareValve.add(compareArrWeight);
                weightList.addAll(compareArrWeightVOList);
            }
        }
        if (!"进港".equals(query.getType())){
            // 出港基值数据
            List<WaybillWeightVO> baseDepWeightVOList = hawbMapper.baseCargoAnalysis(query);
            if (!CollectionUtils.isEmpty(baseDepWeightVOList)){
                BigDecimal baseDepWeight = baseDepWeightVOList.stream().map(WaybillWeightVO::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                baseValve = baseValve.add(baseDepWeight);
                weightList.addAll(baseDepWeightVOList);
            }

            // 出港比较数据
            List<WaybillWeightVO> compareDepWeightVOList = hawbMapper.compareCargoAnalysis(query);
            if (!CollectionUtils.isEmpty(compareDepWeightVOList)){
                BigDecimal compareDepWeight = compareDepWeightVOList.stream().map(WaybillWeightVO::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                compareValve = compareValve.add(compareDepWeight);
                weightList.addAll(compareDepWeightVOList);
            }
        }
        // 基值年份为后期，比较月份为前期
        BigDecimal growthRate;
        if (query.getBaseYear().compareTo(query.getCompareYear()) > 0){
            growthRate = baseValve.subtract(compareValve).divide(compareValve.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ONE : compareValve,4, RoundingMode.HALF_UP);
        }else {
            growthRate = compareValve.subtract(baseValve).divide(baseValve.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ONE : baseValve,4, RoundingMode.HALF_UP);
        }
        vo.setGrowthRate(growthRate);
        if (!CollectionUtils.isEmpty(weightList)){
            Map<String, List<WaybillWeightVO>> weightMap = weightList.stream().collect(Collectors.groupingBy(WaybillWeightVO::getMonthData));
            for (Map.Entry<String, List<WaybillWeightVO>> entry : weightMap.entrySet()) {
                CargoAnalysisVO dataVo = new CargoAnalysisVO();
                BigDecimal depBase = entry.getValue().stream().filter(e -> e.getSign() == 0).map(WaybillWeightVO::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                dataVo.setDepBase(depBase);
                BigDecimal depCompare = entry.getValue().stream().filter(e -> e.getSign() == 1).map(WaybillWeightVO::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                dataVo.setDepCompare(depCompare);
                BigDecimal arrBase = entry.getValue().stream().filter(e -> e.getSign() == 2).map(WaybillWeightVO::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                dataVo.setArrBase(arrBase);
                BigDecimal arrCompare = entry.getValue().stream().filter(e -> e.getSign() == 3).map(WaybillWeightVO::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                dataVo.setArrCompare(arrCompare);
                analysisMap.put(entry.getKey(),dataVo);
            }
        }
        vo.setAnalysisMap(analysisMap);
        return vo;
    }

    /**
     * 月货量分析（表格，利润分布图，客户货量贡献图）
     * @param month 选择的年月
     * @return 结果
     */
    @Override
    public MonthCargoAnalysisVO monthCargoAnalysis(String month) {
        MonthCargoAnalysisVO vo = new MonthCargoAnalysisVO();
        List<ReportWaybillInfoVO> list = waybillMapper.monthCargoAnalysis(month, SecurityUtils.getHighParentId());
        if (CollectionUtils.isEmpty(list)){
            return vo;
        }
        List<ReportWaybillInfoVO> mainWaybillList = list.stream().filter(e -> e.getWaybillCode().contains("AWBA")).collect(Collectors.toList());
        // 主单货量
        if (!CollectionUtils.isEmpty(mainWaybillList)){
            List<Customer> customers = customerMapper.selectList(new QueryWrapper<Customer>().eq("dept_id", SecurityUtils.getHighParentId()).eq("is_del", 0));
            List<CustomerWeightVO> weightList = new ArrayList<>();
            for (Customer customer : customers) {
                List<ReportWaybillInfoVO> collect = mainWaybillList.stream().filter(e -> customer.getName().equals(e.getShipper())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(collect)){
                    CustomerWeightVO customerWeightVO = new CustomerWeightVO();
                    BigDecimal reduce = collect.stream().map(ReportWaybillInfoVO::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                    customerWeightVO.setData(reduce);
                    customerWeightVO.setName(customer.getAbbreviation());
                    weightList.add(customerWeightVO);
                }
            }
            vo.setWeightList(weightList);
            BigDecimal cargoQuantity = mainWaybillList.stream().map(ReportWaybillInfoVO::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setMainWeight(cargoQuantity);
        }
        // 运费
        BigDecimal shippingFee = BigDecimal.ZERO;
        // 杂费
        BigDecimal sundryFee = BigDecimal.ZERO;
        for (ReportWaybillInfoVO reportWaybillInfoVO : mainWaybillList) {
            BigDecimal hawbFee = hawbMapper.selectShippingFee(reportWaybillInfoVO.getWaybillCode());
            shippingFee = shippingFee.add(hawbFee == null ? BigDecimal.ZERO : hawbFee);
            BigDecimal waybillFee = dataItemMapper.selectSundryFee(reportWaybillInfoVO.getWaybillCode());
            BigDecimal payCostFee = payCostMapper.selectSundryFee(reportWaybillInfoVO.getId());
            sundryFee = sundryFee.add(waybillFee == null ? BigDecimal.ZERO : waybillFee).add(payCostFee == null ? BigDecimal.ZERO : payCostFee);
        }
        // 利润
        vo.setMainProfit(shippingFee.subtract(sundryFee));

        // 邮件单利润
        BigDecimal mailProfit = BigDecimal.ZERO;
        List<ReportWaybillInfoVO> mailWaybillList = list.stream().filter(e -> e.getWaybillCode().contains("AWBM")).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(mailWaybillList)){
            BigDecimal mailWeight = mailWaybillList.stream().map(ReportWaybillInfoVO::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setMailWeight(mailWeight);
        }
        for (ReportWaybillInfoVO reportWaybillInfoVO : mailWaybillList) {
            BigDecimal rCostSum = reportWaybillInfoVO.getRCostSum() == null ? new BigDecimal(0) : reportWaybillInfoVO.getRCostSum();
            BigDecimal wCostSum = reportWaybillInfoVO.getWCostSum() == null ? new BigDecimal(0) : reportWaybillInfoVO.getWCostSum();
            mailProfit = mailProfit.add(rCostSum.subtract(wCostSum));
        }
        vo.setMailProfit(mailProfit);
        vo.setTotalProfit(vo.getMainProfit().add(vo.getMailProfit()));
        vo.setTotalWeight(vo.getMainWeight().add(vo.getMailWeight() == null ? BigDecimal.ZERO : vo.getMailWeight()));
        BigDecimal dMainRatio = vo.getMainProfit().divide(vo.getTotalProfit().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ONE : vo.getTotalProfit(), 2, RoundingMode.HALF_UP);
        vo.setDMainRatio(dMainRatio);
        BigDecimal dMailRatio = vo.getMailProfit().divide(vo.getTotalProfit().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ONE : vo.getTotalProfit(), 2, RoundingMode.HALF_UP);
        vo.setDMailRatio(dMailRatio);
        vo.setIMainRatio(BigDecimal.ZERO);
        return vo;
    }


    /**
     * 航线货量利润分布
     * @param month 选择年月
     * @return 结果
     */
    @Override
    public Map<String, AirlineProfitVO> airlineProfit(String month) {
        Map<String, AirlineProfitVO> map = new LinkedHashMap<>();
        List<AirlineWaybillVO> waybills = waybillMapper.airlineProfit(SecurityUtils.getHighParentId(),month);
        if (!CollectionUtils.isEmpty(waybills)){
            Map<String, List<AirlineWaybillVO>> airlineMap = waybills.stream().collect(Collectors.groupingBy(AirlineWaybillVO::getLeg));
            for (Map.Entry<String, List<AirlineWaybillVO>> entry : airlineMap.entrySet()) {
                AirlineProfitVO vo = new AirlineProfitVO();
                BigDecimal cargoWeight = entry.getValue().stream().map(AirlineWaybillVO::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                vo.setCargoWeight(cargoWeight);
                // 运费
                BigDecimal shippingFee = BigDecimal.ZERO;

                // 杂费
                BigDecimal sundryFee = BigDecimal.ZERO;
                for (AirlineWaybillVO waybillVo : entry.getValue()) {
                    BigDecimal hawbFee = hawbMapper.selectShippingFee(waybillVo.getWaybillCode());
                    shippingFee = shippingFee.add(hawbFee == null ? BigDecimal.ZERO : hawbFee);
                    BigDecimal waybillFee = dataItemMapper.selectSundryFee(waybillVo.getWaybillCode());
                    BigDecimal payCostFee = payCostMapper.selectSundryFee(waybillVo.getId());
                    sundryFee = sundryFee.add(waybillFee == null ? BigDecimal.ZERO : waybillFee).add(payCostFee == null ? BigDecimal.ZERO : payCostFee);
                }
                vo.setProfit(shippingFee.subtract(sundryFee));
                map.put(entry.getKey(),vo);
            }
        }
        return map;
    }

    /**
     * 营业点员工绩效
     * @param month 选择年月
     * @return 结果
     */
    @Override
    public StaffPerformanceVO staffPerformance(String month) {
        StaffPerformanceVO vo = new StaffPerformanceVO();
        List<Long> sysDeptIds = deptMapper.selectDeptListById(SecurityUtils.getHighParentId());
        List<String> nikeNameList = sysUserMapper.selecByDeptIds(sysDeptIds);
        BigDecimal totalIncome = hawbMapper.selectIncome(SecurityUtils.getHighParentId(),null, month);
        BigDecimal total = totalIncome == null ? BigDecimal.ONE : totalIncome;
        List<CustomerWeightVO> staffIncomeList = new ArrayList<>();
        Map<String,AirlineProfitVO> profitMap = new LinkedHashMap<>();
        for (String s : nikeNameList) {
            AirlineProfitVO airlineProfitVo = new AirlineProfitVO();
            CustomerWeightVO incomeData = new CustomerWeightVO();
            BigDecimal income = hawbMapper.selectIncome(SecurityUtils.getHighParentId(),s, month);
            BigDecimal bigDecimal = income == null ? BigDecimal.ZERO : income;
            BigDecimal divide = bigDecimal.divide(total, 2, RoundingMode.HALF_UP);
            incomeData.setName(s);
            incomeData.setData(divide);
            staffIncomeList.add(incomeData);
            List<ReportWaybillInfoVO> waybillCount = waybillMapper.selectWaybillCount(SecurityUtils.getHighParentId(),s, month);
            // 邮件单利润
            BigDecimal mailProfit = BigDecimal.ZERO;
            BigDecimal mainProfit = BigDecimal.ZERO;
            if (!CollectionUtils.isEmpty(waybillCount)){
                List<ReportWaybillInfoVO> awba = waybillCount.stream().filter(e -> e.getWaybillCode().contains("AWBA")).collect(Collectors.toList());
                List<ReportWaybillInfoVO> awbm = waybillCount.stream().filter(e -> e.getWaybillCode().contains("AWBM")).collect(Collectors.toList());
                // 运费
                BigDecimal shippingFee = BigDecimal.ZERO;
                // 杂费
                BigDecimal sundryFee = BigDecimal.ZERO;
                for (ReportWaybillInfoVO reportWaybillInfoVO : awba) {
                    BigDecimal hawbFee = hawbMapper.selectShippingFee(reportWaybillInfoVO.getWaybillCode());
                    shippingFee = shippingFee.add(hawbFee == null ? BigDecimal.ZERO : hawbFee);
                    BigDecimal waybillFee = dataItemMapper.selectSundryFee(reportWaybillInfoVO.getWaybillCode());
                    BigDecimal payCostFee = payCostMapper.selectSundryFee(reportWaybillInfoVO.getId());
                    sundryFee = sundryFee.add(waybillFee == null ? BigDecimal.ZERO : waybillFee).add(payCostFee == null ? BigDecimal.ZERO : payCostFee);
                }
                // 利润
                mainProfit = shippingFee.subtract(sundryFee);
                for (ReportWaybillInfoVO reportWaybillInfoVO : awbm) {
                    BigDecimal rCostSum = reportWaybillInfoVO.getRCostSum() == null ? new BigDecimal(0) : reportWaybillInfoVO.getRCostSum();
                    BigDecimal wCostSum = reportWaybillInfoVO.getWCostSum() == null ? new BigDecimal(0) : reportWaybillInfoVO.getWCostSum();
                    mailProfit = mailProfit.add(rCostSum.subtract(wCostSum));
                }
            }
            airlineProfitVo.setCargoWeight(new BigDecimal(waybillCount.size()));
            airlineProfitVo.setProfit(mainProfit.add(mailProfit));
            profitMap.put(s,airlineProfitVo);
        }
        vo.setStaffIncomeList(staffIncomeList);
        List<CustomerWeightVO> billWeightList = new ArrayList<>();
        List<WaybillWeightVO> weightVoList = waybillMapper.selectBillWeightList(SecurityUtils.getHighParentId(),month);
        if (!CollectionUtils.isEmpty(weightVoList)){
            CustomerWeightVO vo1 = new CustomerWeightVO();
            CustomerWeightVO vo2 = new CustomerWeightVO();
            BigDecimal depWeight = weightVoList.stream().filter(e -> "出港".equals(e.getType())).map(WaybillWeightVO::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo1.setName("国内出港");
            vo1.setData(depWeight);
            BigDecimal arrWeight = weightVoList.stream().filter(e -> "进港".equals(e.getType())).map(WaybillWeightVO::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo2.setName("国内进港");
            vo2.setData(arrWeight);
            billWeightList.add(vo1);
            billWeightList.add(vo2);
        }
        vo.setBillWeightList(billWeightList);
        vo.setProfitMap(profitMap);
        return vo;
    }

    /**
     * 每日承运人汇总
     * @param query 查询条件
     * @return 结果
     */
    @Override
    public CarrierDayVO carrierDaySum(CarrierSumQuery query) {
        CarrierDayVO carrierDayVO = new CarrierDayVO();
        List<CarrierDaySumVO> list = new ArrayList<>();
        query.setDeptId(SecurityUtils.getHighParentId());
        List<ReportWaybillInfoVO> waybillInfoVos = waybillMapper.carrierDayList(query);
        if (!CollectionUtils.isEmpty(waybillInfoVos)){
            Map<String, List<ReportWaybillInfoVO>> carrierMap = waybillInfoVos.stream().collect(Collectors.groupingBy(ReportWaybillInfoVO::getCarrier1));
            for (Map.Entry<String, List<ReportWaybillInfoVO>> entry : carrierMap.entrySet()) {
                Map<String,CarrierSumVO> carrierSumVoMap = new LinkedHashMap<>();
                CarrierDaySumVO vo = new CarrierDaySumVO();
                vo.setCarrier(entry.getKey());
                Map<String, List<ReportWaybillInfoVO>> dateData = entry.getValue().stream().collect(Collectors.groupingBy(ReportWaybillInfoVO::getMonthData));
                for (Map.Entry<String, List<ReportWaybillInfoVO>> dateEntry : dateData.entrySet()) {
                    CarrierSumVO sumVo = new CarrierSumVO();
                    BigDecimal weight = dateEntry.getValue().stream().map(ReportWaybillInfoVO::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                    sumVo.setWeight(weight);
                    BigDecimal shippingFee = BigDecimal.ZERO;
                    BigDecimal fuel = BigDecimal.ZERO;
                    for (ReportWaybillInfoVO reportWaybillInfoVO : dateEntry.getValue()) {
                        List<ReportDataItem> dataItems = dataItemMapper.selectMainCarrierFee(reportWaybillInfoVO.getWaybillCode());
                        if (!CollectionUtils.isEmpty(dataItems)){
                            BigDecimal reduce = dataItems.stream()
                                    .filter(e -> "航空运费".equals(e.getChargeName()))
                                    .map(ReportDataItem::getCharging)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            shippingFee = shippingFee.add(reduce);
                            BigDecimal reduce1 = dataItems.stream()
                                    .filter(e -> "燃油附加费".equals(e.getChargeName()))
                                    .map(ReportDataItem::getCharging)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            fuel = fuel.add(reduce1);
                        }
                    }
                    sumVo.setShippingFee(shippingFee);
                    sumVo.setFuel(fuel);
                    carrierSumVoMap.put(dateEntry.getKey(), sumVo);
                }
                vo.setCarrierSumVoMap(carrierSumVoMap);
                list.add(vo);
            }
            Map<String, List<ReportWaybillInfoVO>> dateMap = waybillInfoVos.stream()
                    .sorted(Comparator.comparing(ReportWaybillInfoVO::getMonthData))
                    .collect(Collectors.groupingBy(ReportWaybillInfoVO::getMonthData));
            Map<String,BigDecimal> weightMap = new LinkedHashMap<>();
            Map<String,BigDecimal> shippingMap = new LinkedHashMap<>();
            for (Map.Entry<String, List<ReportWaybillInfoVO>> entry : dateMap.entrySet()) {
                BigDecimal reduce = entry.getValue().stream().map(ReportWaybillInfoVO::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                weightMap.put(entry.getKey(),reduce);
                BigDecimal shippingFee = BigDecimal.ZERO;
                for (ReportWaybillInfoVO reportWaybillInfoVO : entry.getValue()) {
                    List<ReportDataItem> dataItems = dataItemMapper.selectMainCarrierFee(reportWaybillInfoVO.getWaybillCode());
                    if (!CollectionUtils.isEmpty(dataItems)){
                        BigDecimal airFee = dataItems.stream()
                                .filter(e -> "航空运费".equals(e.getChargeName()))
                                .map(ReportDataItem::getCharging)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        shippingFee = shippingFee.add(airFee);

                    }
                }
                shippingMap.put(entry.getKey(),shippingFee);
            }
            carrierDayVO.setWeightMap(weightMap);
            carrierDayVO.setShippingMap(shippingMap);
        }
        carrierDayVO.setList(list);
        return carrierDayVO;
    }

    /**
     * 每日客户汇总
     * @param query 查询条件
     * @return 结果
     */
    @Override
    public CarrierDayVO customDaySum(CarrierSumQuery query) {
        CarrierDayVO carrierDayVO = new CarrierDayVO();
        List<CarrierDaySumVO> list = new ArrayList<>();
        query.setDeptId(SecurityUtils.getHighParentId());
        List<ReportWaybillInfoVO> waybillInfoVos = hawbMapper.selectCustomDaySum(query);
        if (!CollectionUtils.isEmpty(waybillInfoVos)){
            Map<String, List<ReportWaybillInfoVO>> carrierMap = waybillInfoVos.stream().collect(Collectors.groupingBy(ReportWaybillInfoVO::getShipper));
            for (Map.Entry<String, List<ReportWaybillInfoVO>> entry : carrierMap.entrySet()) {
                Map<String,CarrierSumVO> carrierSumVoMap = new LinkedHashMap<>();
                CarrierDaySumVO vo = new CarrierDaySumVO();
                vo.setCarrier(entry.getKey());
                Map<String, List<ReportWaybillInfoVO>> dateData = entry.getValue().stream().collect(Collectors.groupingBy(ReportWaybillInfoVO::getMonthData));
                for (Map.Entry<String, List<ReportWaybillInfoVO>> dateEntry : dateData.entrySet()) {
                    CarrierSumVO sumVo = new CarrierSumVO();
                    BigDecimal weight = dateEntry.getValue().stream().map(ReportWaybillInfoVO::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                    sumVo.setWeight(weight);
                    BigDecimal shippingFee = BigDecimal.ZERO;
                    BigDecimal fuel = BigDecimal.ZERO;
                    for (ReportWaybillInfoVO reportWaybillInfoVO : dateEntry.getValue()) {
                        List<ReportDataItem> dataItems = dataItemMapper.selectMainCarrierFee(reportWaybillInfoVO.getWaybillCode());
                        if (!CollectionUtils.isEmpty(dataItems)){
                            BigDecimal reduce = dataItems.stream()
                                    .filter(e -> "航空运单".equals(e.getFreightType()))
                                    .map(ReportDataItem::getCharging)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            shippingFee = shippingFee.add(reduce);
                            BigDecimal reduce1 = dataItems.stream()
                                    .filter(e -> "燃油附加费".equals(e.getChargeName()))
                                    .map(ReportDataItem::getCharging)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            fuel = fuel.add(reduce1);
                        }
                    }
                    sumVo.setShippingFee(shippingFee);
                    sumVo.setFuel(fuel);
                    carrierSumVoMap.put(dateEntry.getKey(), sumVo);
                }
                vo.setCarrierSumVoMap(carrierSumVoMap);
                list.add(vo);
            }
            Map<String, List<ReportWaybillInfoVO>> dateMap = waybillInfoVos.stream()
                    .sorted(Comparator.comparing(ReportWaybillInfoVO::getMonthData))
                    .collect(Collectors.groupingBy(ReportWaybillInfoVO::getMonthData));
            Map<String,BigDecimal> weightMap = new LinkedHashMap<>();
            Map<String,BigDecimal> shippingMap = new LinkedHashMap<>();
            for (Map.Entry<String, List<ReportWaybillInfoVO>> entry : dateMap.entrySet()) {
                BigDecimal reduce = entry.getValue().stream().map(ReportWaybillInfoVO::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                weightMap.put(entry.getKey(),reduce);
                BigDecimal shippingFee = BigDecimal.ZERO;
                for (ReportWaybillInfoVO reportWaybillInfoVO : entry.getValue()) {
                    List<ReportDataItem> dataItems = dataItemMapper.selectMainCarrierFee(reportWaybillInfoVO.getWaybillCode());
                    if (!CollectionUtils.isEmpty(dataItems)){
                        BigDecimal airFee = dataItems.stream()
                                .filter(e -> "航空运单".equals(e.getChargeName()))
                                .map(ReportDataItem::getCharging)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        shippingFee = shippingFee.add(airFee);

                    }
                }
                shippingMap.put(entry.getKey(),shippingFee);
            }
            carrierDayVO.setWeightMap(weightMap);
            carrierDayVO.setShippingMap(shippingMap);
        }
        carrierDayVO.setList(list);
        return carrierDayVO;
    }

    private void setMonthValue(MonthQuantityVO vo, String month, BigDecimal value) {
        BiConsumer<MonthQuantityVO, BigDecimal> setter = MONTH_SETTER_MAP.get(month);
        if (setter != null) {
            setter.accept(vo, value);
        } else {
            throw new CustomException("未知月份");
        }
    }


    private static final Map<String, BiConsumer<MonthQuantityVO, BigDecimal>> MONTH_SETTER_MAP = new HashMap<>();

    static {
        MONTH_SETTER_MAP.put("01", MonthQuantityVO::setOne);
        MONTH_SETTER_MAP.put("02", MonthQuantityVO::setTwo);
        MONTH_SETTER_MAP.put("03", MonthQuantityVO::setThree);
        MONTH_SETTER_MAP.put("04", MonthQuantityVO::setFour);
        MONTH_SETTER_MAP.put("05", MonthQuantityVO::setFive);
        MONTH_SETTER_MAP.put("06", MonthQuantityVO::setSix);
        MONTH_SETTER_MAP.put("07", MonthQuantityVO::setSeven);
        MONTH_SETTER_MAP.put("08", MonthQuantityVO::setEight);
        MONTH_SETTER_MAP.put("09", MonthQuantityVO::setNine);
        MONTH_SETTER_MAP.put("10", MonthQuantityVO::setTen);
        MONTH_SETTER_MAP.put("11", MonthQuantityVO::setEleven);
        MONTH_SETTER_MAP.put("12", MonthQuantityVO::setTwelve);
    }
}
