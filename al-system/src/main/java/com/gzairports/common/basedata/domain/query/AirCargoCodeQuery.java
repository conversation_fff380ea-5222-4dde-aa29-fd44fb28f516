package com.gzairports.common.basedata.domain.query;

import lombok.Data;

import java.util.List;

/**
 * 航空公司货品代码查询参数
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Data
public class AirCargoCodeQuery {

    /** 主键id集合 */
    private List<Long> ids;

    /** 航空公司货品代码 */
    private String code;

    /** 承运人代码 */
    private String carrierCode;

    /** 适用国内 */
    private Integer domestic;

    /** 适用国际 */
    private Integer international;

    /** 当前登录人 */
    private String userName;
}
