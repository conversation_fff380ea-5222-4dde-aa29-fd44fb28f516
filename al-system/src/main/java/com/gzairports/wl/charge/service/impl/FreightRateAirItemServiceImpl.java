package com.gzairports.wl.charge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.BaseAirportCode;
import com.gzairports.common.basedata.domain.BaseCargoCode;
import com.gzairports.common.basedata.domain.BaseCityCode;
import com.gzairports.common.basedata.mapper.AirportCodeMapper;
import com.gzairports.common.basedata.mapper.CargoCodeMapper;
import com.gzairports.common.basedata.mapper.CityCodeMapper;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.wl.charge.domain.FreightRateAir;
import com.gzairports.wl.charge.domain.FreightRateAirItem;
import com.gzairports.wl.charge.domain.FreightRateCustomItem;
import com.gzairports.wl.charge.domain.query.FareAirItemQuery;
import com.gzairports.wl.charge.domain.query.FreightRateItemQuery;
import com.gzairports.wl.charge.domain.vo.FareAirItemVo;
import com.gzairports.wl.charge.domain.vo.FreightRateAirItemVO;
import com.gzairports.wl.charge.mapper.FreightRateAirItemMapper;
import com.gzairports.wl.charge.mapper.FreightRateAirMapper;
import com.gzairports.wl.charge.service.FreightRateAirItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 航司运价条目service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@Service
public class FreightRateAirItemServiceImpl extends ServiceImpl<FreightRateAirItemMapper, FreightRateAirItem> implements FreightRateAirItemService {

    @Autowired
    private FreightRateAirItemMapper airItemMapper;

    @Autowired
    private FreightRateAirMapper airMapper;

    @Autowired
    private CityCodeMapper cityCodeMapper;

    @Autowired
    private CargoCodeMapper cargoCodeMapper;

    /**
     * 查询运价条目列表
     *
     * @param query 运价条目查询参数
     * @return 结果
     */
    @Override
    public List<FreightRateAirItemVO> selectList(FreightRateItemQuery query) {
//        if(query.getDepartureCity() != null){
//            List<BaseCityCode> sourcePorts = cityCodeMapper.selectCityListByName(query.getDepartureCity());
//            List<String> sources = new ArrayList<>();
//            for (BaseCityCode cityCode : sourcePorts) {
//                sources.add(cityCode.getCode());
//            }
//            query.setSourcePort(sources);
//        }
//        if(query.getDestinationCity() != null){
//            List<BaseCityCode> baseCityCodes = cityCodeMapper.selectCityListByName(query.getDestinationCity());
//            List<String> desPort = new ArrayList<>();
//            for (BaseCityCode baseAirportCode : baseCityCodes) {
//                desPort.add(baseAirportCode.getCode());
//            }
//            query.setDesPort(desPort);
//        }
        List<FreightRateAirItemVO> freightRateAirItemVOS = airItemMapper.selectAirList(query);
        freightRateAirItemVOS.forEach(item -> {
              if((item.getStartTime().before(new Date()) || item.getStartTime().equals(new Date())) &&
                      (item.getEndTime().after(new Date()) || item.getEndTime().equals(new Date()))) {
                item.setIsEffective(1);
            }else{
               item.setIsEffective(0);
            }
        });
        return freightRateAirItemVOS;
    }

    /**
     * 查询运价条目详情
     *
     * @param id 运价条目主键
     * @return 运价条目详情
     */
    @Override
    public FreightRateAirItem selectAirItemById(Long id) {
        FreightRateAirItem freightRateAirItem = airItemMapper.selectById(id);
        FreightRateAir air = airMapper.selectById(freightRateAirItem.getRateId());
//        BaseCargoCode baseCargoCode = cargoCodeMapper.selectById(freightRateAirItem.getCargoCode());
//        if (baseCargoCode != null){
//            freightRateAirItem.setCargoCode(baseCargoCode.getCode());
//        }
        freightRateAirItem.setRateName(air.getRateName());
        return freightRateAirItem;
    }

    /**
     * 新增运价条目
     *
     * @param item 运价条目
     * @return 结果
     */
    @Override
    public int insertAirItem(FreightRateAirItem item) {
        FreightRateAirItem airItem = airItemMapper.selectOne(new QueryWrapper<FreightRateAirItem>()
                .eq("rate_id", item.getId())
                .eq("departure_city", item.getDepartureCity())
                .eq("destination_city", item.getDestinationCity())
                .eq("is_del",0));
        if (airItem != null){
            throw new CustomException("已存在相同运价条目数据");
        }
        if (item.getDepartureCity().equals(item.getDestinationCity())) {
            throw new CustomException("始发城市和目的城市一致");
        }
        item.setDeptId(SecurityUtils.getHighParentId());
        return airItemMapper.insert(item);
    }

    /**
     * 修改运价条目
     *
     * @param item 运价条目
     * @return 结果
     */
    @Override
    public int updateAirItem(FreightRateAirItem item) {
        return airItemMapper.updateById(item);
    }

    /**
     * 删除运价条目
     *
     * @param id 运价条目主键
     * @return 结果
     */
    @Override
    public int removeAirItem(Long id) {
        return airItemMapper.removeAirItem(id);
    }

    @Override
    public List<FareAirItemVo> selectItemList(FareAirItemQuery query) {
        List<FareAirItemVo> list = new ArrayList<>();
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = currentDate.format(formatter);
        FreightRateAir rateAir = airMapper.selectOne(new QueryWrapper<FreightRateAir>()
                .eq("applicable_documents", "AWBA")
                .eq("carrier_code", query.getCarrier1())
                .eq("dept_id", SecurityUtils.getHighParentId())
                .le("effective_date", formattedDate)
                .ge("expiration_date", formattedDate)
                .eq("is_del", 0));
        if (rateAir != null){
            List<FreightRateAirItem> airItem = airItemMapper.selectList(new QueryWrapper<FreightRateAirItem>()
                    .eq("rate_id", rateAir.getId())
                    .eq("departure_city", query.getSourcePort())
                    .eq("destination_city", query.getDesPort())
                    .eq("is_del",0));
            for (FreightRateAirItem rateAirItem : airItem) {
                if (StringUtils.isEmpty(rateAirItem.getCargoCode()) && StringUtils.isEmpty(rateAirItem.getCargoCategory()) && StringUtils.isEmpty(rateAirItem.getSpecialCode())){
                    FareAirItemVo vo1 = new FareAirItemVo();
                    vo1.setId(rateAirItem.getId());
                    vo1.setClauseName(rateAirItem.getClauseName());
                    list.add(vo1);
                }
                if (rateAirItem.getCargoCode() != null && Arrays.asList(rateAirItem.getCargoCode().split(",")).contains(query.getCargoCode())) {
                    FareAirItemVo vo2 = new FareAirItemVo();
                    vo2.setId(rateAirItem.getId());
                    vo2.setClauseName(rateAirItem.getClauseName());
                    list.add(vo2);
                }
                if (rateAirItem.getSpecialCode() != null && rateAirItem.getSpecialCode().equals(query.getSpecialCargoCode1())){
                    FareAirItemVo vo3 = new FareAirItemVo();
                    vo3.setId(rateAirItem.getId());
                    vo3.setClauseName(rateAirItem.getClauseName());
                    list.add(vo3);
                }
            }
        }
        return list;
    }

    @Override
    public void delAll(Long[] ids) {
        if (ids != null){
            Date date = new Date();
            Long id = ids[0];
            FreightRateAirItem rateAirItem = airItemMapper.selectById(id);
            FreightRateAir rateAir = airMapper.selectById(rateAirItem.getRateId());
            if (rateAir.getEffectiveDate().before(date) && rateAir.getExpirationDate().after(date)){
                throw new CustomException("在有效期内的运价不能删除");
            }
            airItemMapper.deleteBatchIds(Arrays.asList(ids));
        }
    }
}
