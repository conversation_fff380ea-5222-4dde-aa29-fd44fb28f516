<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.system.mapper.ATestMapper">
    
    <resultMap type="ATest" id="ATestResult">
        <result property="id"    column="id"    />
        <result property="namceCn"    column="namce_cn"    />
    </resultMap>

    <sql id="selectATestVo">
        select id, namce_cn from a_test
    </sql>

    <select id="selectATestList" parameterType="ATest" resultMap="ATestResult">
        <include refid="selectATestVo"/>
        <where>  
            <if test="namceCn != null  and namceCn != ''"> and namce_cn = #{namceCn}</if>
        </where>
    </select>
    
    <select id="selectATestById" parameterType="Long" resultMap="ATestResult">
        <include refid="selectATestVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertATest" parameterType="ATest" useGeneratedKeys="true" keyProperty="id">
        insert into a_test
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="namceCn != null and namceCn != ''">namce_cn,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="namceCn != null and namceCn != ''">#{namceCn},</if>
         </trim>
    </insert>

    <update id="updateATest" parameterType="ATest">
        update a_test
        <trim prefix="SET" suffixOverrides=",">
            <if test="namceCn != null and namceCn != ''">namce_cn = #{namceCn},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteATestById" parameterType="Long">
        delete from a_test where id = #{id}
    </delete>

    <delete id="deleteATestByIds" parameterType="String">
        delete from a_test where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>