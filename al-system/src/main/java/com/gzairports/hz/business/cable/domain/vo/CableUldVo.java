package com.gzairports.hz.business.cable.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 报文数据收集集装器数据
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@Data
public class CableUldVo {

    /** 主键id */
    private Long id;

    /** 编号 */
    private String code;

    /** 类型 */
    private String type;

    /** 航司 */
    private String airCompany;

    /** 入场航班 */
    private String entranceFlight;

    /** 出场航班 */
    private String exitFlight;

    /** 入场时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date entranceTime;

    /** 出场时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date exitTime;
}
