package com.gzairports.wl.departure.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.core.typehandler.StringListJsonTypeHandler;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 国内分单账单管理表
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@Data
@TableName("wl_dep_bill")
public class Bill {

    /** 主键id */
    private Long id;

    /** 流水号 */
    private String serialNo;

    /** 客户(发货人) */
    private String shipper;

    /** 支付方式 */
    private String payMethod;

    /** 支付状态 */
    private String payStatus;

    /** 支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /** 创建时间 */
    private Date createTime;

    /** 创建人 */
    private String createBy;

    /** 更新时间 */
    private Date updateTime;

    /** 更新人 */
    private String updateBy;

    /** 所属单位 */
    private Long deptId;

    /** 备注 */
    private String remark;

    /** 状态 */
    private String status;

    /** 是否删除 0 否 1 是 */
    private Integer isDel;

    /** 支付凭证 */
    @TableField(value = "pay_voucher",typeHandler = StringListJsonTypeHandler.class)
    private List<String> payVoucher;
}
