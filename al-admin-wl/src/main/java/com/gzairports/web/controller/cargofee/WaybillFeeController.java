package com.gzairports.web.controller.cargofee;

import com.gzairports.common.business.departure.domain.WaybillFee;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.wl.cargofee.domain.query.WaybillFeeQuery;
import com.gzairports.wl.cargofee.domain.vo.WaybillFeeVo;
import com.gzairports.wl.cargofee.service.IWaybillFeeService;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * 运单费用明细Controller
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@RestController
@RequestMapping("/cargofee/waybillFee")
public class WaybillFeeController extends BaseController {

    @Autowired
    private IWaybillFeeService waybillFeeService;

    /**
     * 查询运单费用明细列表
     */
//    @PreAuthorize("@ss.hasPermi('cargofee:waybillFee:list')")
    @PostMapping("/list")
    @ApiOperation(value = "查询运单费用明细列表")
    public AjaxResult list(@RequestBody WaybillFeeQuery query){
        WaybillFeeVo vo = waybillFeeService.selectList(query);
        return AjaxResult.success(vo);
    }

    /**
     * 导出运单费用明细列表
     */
    @PreAuthorize("@ss.hasPermi('cargofee:waybillFee:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出运单费用明细列表")
    public void export(HttpServletResponse response, WaybillFeeQuery query)
    {
        WaybillFeeVo vo = waybillFeeService.selectList(query);
        if (vo != null){
            vo.getList().forEach(item -> {
                String waybillCode = item.getWaybillCode();
                if (StringUtils.isNotEmpty(waybillCode)){
                    if (waybillCode.contains("AWBA")) {
                        item.setWaybillCode(waybillCode.substring(4, 7) + "-" + waybillCode.substring(7));
                    } else {
                        item.setWaybillCode(waybillCode.substring(4, 6) + "-" + waybillCode.substring(6));
                    }
                }
            });
            ExcelUtil<WaybillFee> util = new ExcelUtil<WaybillFee>(WaybillFee.class);
            util.exportExcel(response, vo.getList(), "运单费用明细");
        }
    }
}
