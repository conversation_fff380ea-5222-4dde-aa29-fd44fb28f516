package com.gzairports.web.controller.departure;

import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.wl.departure.domain.query.AwaitTransportWaybillQuery;
import com.gzairports.wl.departure.domain.vo.AwaitTransportWaybillVO;
import com.gzairports.wl.departure.service.AwaitTransportWaybillService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 待运运单 controller
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
@RestController
@RequestMapping("/dep/await")
public class AwaitTransportWaybillController {

    @Autowired
    private AwaitTransportWaybillService service;

    /**
     * 待运运单查询
     */
    @GetMapping("/list")
    @ApiOperation(value = "待运运单查询")
    public AjaxResult list(AwaitTransportWaybillQuery query) {
        AwaitTransportWaybillVO vo = service.selectList(query);
        return AjaxResult.success(vo);
    }

}
