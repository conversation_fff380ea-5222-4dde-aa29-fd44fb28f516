package com.gzairports.wl.reporter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.wl.reporter.domain.ReportField;
import com.gzairports.wl.reporter.domain.ReportSet;
import com.gzairports.wl.reporter.domain.vo.ReportSetVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-19 11:21
 */
public interface IReportSetService extends IService<ReportSet> {

    /**
     * 根据类型查询可选字段
     * @param type 类型
     * @return 可选指端
     */
    List<ReportField> fieldList(String type);

    /**
     * 新增报表设置
     * @param vo 新增参数
     * @return 结果
     */
    int addSet(ReportSetVo vo);

    /**
     * 编辑报表设置
     * @param vo 修改参数
     * @return 结果
     */
    int editSet(ReportSetVo vo);

    /***
     * 查看详情
     * @param id 报表设置id
     * @return 结果
     */
    ReportSetVo getInfo(Long id);

    /**
     * 报表列表查询
     * @param type 查询参数
     * @return 结果
     */
    List<ReportSet> selectList(String type);

    /**
     * 删除报表设置
     * @param id 报表id
     * @return 结果
     */
    int delSet(Long id);
}
