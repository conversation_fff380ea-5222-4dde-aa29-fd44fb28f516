package com.gzairports.wl.departure.domain.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


/**
 * 货站在线缴费详情参数
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
public class OnlinePayVo {

    /** 总单数 */
    private Integer totalOrder;

    /** 未授权支付单数 */
    private Integer unPayOrder;

    /** 已授权支付单数 */
    private Integer payOrder;

    /** 已结算单数 */
    private Integer settleOrder;

    /** 未授权支付费用 */
    private BigDecimal unPay;

    /** 已授权支付费用 */
    private BigDecimal pay;

    /** 已结算支付费用 */
    private BigDecimal settle;

    /** 已结算退还费用 */
    private BigDecimal refund;

    /** 运单详情列表 */
    Page<OnlineMawbVo> onlineMawbVos;

}
