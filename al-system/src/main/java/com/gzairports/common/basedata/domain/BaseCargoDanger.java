package com.gzairports.common.basedata.domain;

import lombok.Data;

import java.util.Date;

/**
 * 危险品管理表
 *
 * <AUTHOR>
 * @date 2024-06-14
 */
@Data
public class BaseCargoDanger {

    /** 主键id */
    private Long id;

    /** 特货代码 */
    private String specialCode;

    /** UN编码 */
    private String unCode;

    /** 项别 */
    private String category;

    /** 类别 */
    private String itemCategory;

    /** 应急代码 */
    private String emergencyCode;

    /** 举例 */
    private String example;

    /** 备注 */
    private String remark;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 是否删除 0 否 1 是 */
    private Integer isDel;
}
