package com.gzairports.wl.charge.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 邮件运价表
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@Data
@TableName("wl_sf_mail_price")
public class MailPrice {

    /** 主键id */
    private Long id;

    /** 运价名称 */
    private String rateName;

    /** 运价类型 */
    private Integer rateType;

    /** 适用单证类型 */
    private String applicableDocuments;

    /** 航司code */
    private String carrierCode;

    /** 是否自动从低 */
    private Integer automaticFromLow;

    /** 取舍方式 */
    private Integer roundingMethod;

    /** 生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date effectiveDate;

    /** 截止日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expirationDate;

    /** 备注 */
    private String remarks;

    /** 所属单位 */
    private Long deptId;

    /** 条目是否逻辑删除 */
    private Integer isDel;

    /** 更新时间 */
    private Date updateTime;

    /** 更新人 */
    private String updateBy;

    /** 创建时间 */
    private Date createTime;

    /** 创建人 */
    private String createBy;
}
