<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gzairports.sms.mapper.log.SmsSendLogMapper">

    <select id="selectSmsSendLog" parameterType="MessageConditionDto"
            resultType="com.gzairports.sms.domain.SmsSendLogPo">
        SELECT * FROM sms_send_log d
        <where>
            <if test="mobile != null and mobile != ''">
                AND d.mobile LIKE CONCAT('%', #{mobile, jdbcType=VARCHAR}, '%')
            </if>
            <if test="startTime != null">
                AND DATE_FORMAT(d.create_time,'%Y%m%d') <![CDATA[>=]]> DATE_FORMAT(#{startTime, jdbcType=DATE},'%Y%m%d')
            </if>
            <if test="endTime != null">
                AND DATE_FORMAT(d.create_time,'%Y%m%d') <![CDATA[<=]]> DATE_FORMAT(#{endTime, jdbcType=DATE},'%Y%m%d')
            </if>
            <if test="depts != null and depts.size == 1">
                AND d.dept_id = #{depts[0], jdbcType=INTEGER}
            </if>
            <if test="depts != null and depts.size > 1">
                AND d.dept_id IN
                <foreach collection="depts" item="item" index="index"
                         open="(" separator="," close=")">
                    #{item, jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
        ORDER BY d.create_time DESC
    </select>
    <select id="selectSmsSendLogById" resultType="com.gzairports.sms.domain.SmsSendLogPo">
        select * from sms_send_log d where d.id = #{smsSendLogId}
    </select>
    <select id="selectSmsSendLogList" parameterType="SmsSendQuery" resultType="com.gzairports.sms.domain.SmsSendLogPo">
        SELECT * FROM sms_send_log d
        <where>
            <if test="mobile != null and mobile != ''">
                AND d.mobile LIKE CONCAT('%', #{mobile, jdbcType=VARCHAR}, '%')
            </if>
            <if test="moduleName != null and moduleName != ''">
                AND d.module_name = #{moduleName, jdbcType=VARCHAR}
            </if>
            <if test="startTime != null">
                AND DATE_FORMAT(d.create_time,'%Y%m%d') <![CDATA[>=]]> DATE_FORMAT(#{startTime, jdbcType=DATE},'%Y%m%d')
            </if>
            <if test="endTime != null">
                AND DATE_FORMAT(d.create_time,'%Y%m%d') <![CDATA[<=]]> DATE_FORMAT(#{endTime, jdbcType=DATE},'%Y%m%d')
            </if>
            <if test="resultCode != null and resultCode == 1">
                AND d.result_code != 0
            </if>
            <if test="resultCode != null and resultCode == 0">
                AND d.result_code = 0
            </if>
            <if test="billId != null and billId != ''">
                AND d.bill_id like '%${billId}%'
            </if>
        </where>
        ORDER BY d.create_time DESC
    </select>
    <select id="getCode" resultType="java.lang.String">
        select DISTINCT(bill_id) from sms_send_log where substring(bill_id,-4) = #{code}
    </select>

    <insert id="insertSmsSendLog" parameterType="com.gzairports.sms.domain.SmsSendLogPo">
        insert into sms_send_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="mobile != null and mobile != ''">mobile,</if>
            <if test="moduleName != null and moduleName != ''">module_name,</if>
            <if test="billId != null and billId != ''">bill_id,</if>
            <if test="msgType != null">msg_type,</if>
            <if test="smsConfigCode != null and smsConfigCode != ''">sms_config_code,</if>
            <if test="smsParameter != null and smsParameter != ''">sms_parameter,</if>
            <if test="content != null and content !=''">content,</if>
            <if test="resultCode != null">result_code,</if>
            <if test="resultMsg != null and resultMsg != ''">result_msg,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="mobile != null and mobile != ''">#{mobile},</if>
            <if test="moduleName != null and moduleName != ''">#{moduleName},</if>
            <if test="billId != null and billId != ''">#{billId},</if>
            <if test="msgType != null">#{msgType},</if>
            <if test="smsConfigCode != null and smsConfigCode != ''">#{smsConfigCode},</if>
            <if test="smsParameter != null and smsParameter != ''">#{smsParameter},</if>
            <if test="content != null and content !=''">#{content},</if>
            <if test="resultCode != null">#{resultCode},</if>
            <if test="resultMsg != null and resultMsg != ''">#{resultMsg},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>


</mapper>