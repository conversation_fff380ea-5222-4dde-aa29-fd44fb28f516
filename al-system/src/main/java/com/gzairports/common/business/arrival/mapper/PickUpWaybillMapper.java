package com.gzairports.common.business.arrival.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.arrival.domain.PickUp;
import com.gzairports.common.business.arrival.domain.PickUpWaybill;
import com.gzairports.common.business.arrival.domain.query.AppPickUpQuery;
import com.gzairports.common.business.arrival.domain.query.PickUpOutQuery;
import com.gzairports.common.business.arrival.domain.vo.*;
import com.gzairports.hz.business.arrival.domain.vo.NodeTallyInfoVo;
import com.gzairports.hz.business.departure.domain.vo.ChargeBillArrSettleExportVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 办理提货运单关联Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-03
 */
@Mapper
public interface PickUpWaybillMapper extends BaseMapper<PickUpWaybill> {

    /**
     * 根据办理提货id查询提货运单
     * @param pickUpId 办理提货id
     * @return 提货运单列表
     */
    List<OutOrderWaybillVo> selectOutOrderById(Long pickUpId);

    /**
     * 提货办单信息
     * @param id 运单id
     * @return 结果
     */
    PickUpOutWaybillVo getInfo(Long id);

    /**
     * 根据理货id查询办单时间
     * @param tallyId 理货id
     * @param waybillCode 运单号
     * @return 办单时间
     */
    Date selectTime(@Param("tallyId") Long tallyId,@Param("waybillCode") String waybillCode);

    /**
     * 备出库查询列表
     * @param query 查询参数
     * @return 备出库列表
     */
    List<PickUpVo> planPickUpOut(PickUpOutQuery query);

    /**
     * 根据理货id集合查询已办单数据
     * @param ids 理货id集合
     * @return 已办单数据
     */
    List<Long> selectTallyIds(@Param("ids") List<Long> ids);

    /**
     * 根据提货id查询数据
     * @param tallyId 提货id
     * @return 数据
     */
    PickUp selectTallyId(Long tallyId);

    /**
     * H5办单详情
     * @param pickUpId 办单id
     * @return 详情
     */
    List<H5OutInfoVo> selectH5Info(Long pickUpId);

    /**
     * app提货出库数据
     * @param query 查询条件
     * @return 提货出库数据
     */
    List<AppPickUpVo> appPickUpList(AppPickUpQuery query);

    /**
     * 根据办单id查询运单数据
     * @param pickUpId 办单id
     * @return 运单数据
     */
    List<ScanWaybillVo> selectListByPickUpId(Long pickUpId);

    /**
     * app提货出库详情
     * @param waybillCode 运单号
     * @param tallyId 理货id
     * @return 详情
     */
    PickUpInfoVo selectPickUpInfo(@Param("waybillCode") String waybillCode,@Param("tallyId") Long tallyId);

    PickUpInfoVo selectPickUpInfoForWaybillCode(String waybillCode);

    /**
     *  app快速交付
     * @param query 查询参数
     * @return 快速交付列表
     */
    List<AppPickUpVo> rapidDeliver(@Param("query") AppPickUpQuery query,@Param("list") List<Long> list);

    /**
     * 根据办单id查询提货运单
     * @param id 办单id
     * @return 办单运单
     */
    List<PickUpWaybill> selectWaybill(Long id);

    /**
     * app快速交付运单号查询
     * @param waybillCode 运单号
     * @return 详情
     */
    PickUpInfoVo waybillCodeQuery(String waybillCode);

    /**
     * 根据后四位或者后八位查询运单
     * @param code 运单号
     * @return 详情
     */
    List<String> getWaybillCodeByFour(@Param("code") String code, @Param("type") String type);

    List<String> selectWaybillCode(Long id);

    Date selectPayTime(@Param("tallyId") Long tallyId,@Param("waybillCode") String waybillCode);

    String selectReportUpTime(@Param("tallyId") Long tallyId,@Param("waybillCode") String waybillCode);

    String selectReportPayTime(@Param("tallyId") Long tallyId,@Param("waybillCode") String waybillCode);

    List<String> selectFlightNo(Long id);

    int updatePickUpWaybillById(PickUpWaybill pickUpWaybill);

    List<ChargeBillArrSettleExportVO> selectListByWaybillCode(@Param("arrWaybillCodeList") List<String> arrWaybillCodeList);

    List<PickUpWaybill> selectBatch(@Param("tallyKeys") List<NodeTallyInfoVo> tallyKeys);

    List<PickUpWaybill> selectPickWaybillList(@Param("waybillCodeList") List<String> waybillCodeList);
}
