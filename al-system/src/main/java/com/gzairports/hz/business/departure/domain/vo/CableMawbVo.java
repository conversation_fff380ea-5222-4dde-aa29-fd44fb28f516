package com.gzairports.hz.business.departure.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 报文数据收集运单数据
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@Data
public class CableMawbVo {

    /** 运单id */
    private Long id;

    /** 运单号 */
    private String waybillCode;

    /** 装机位置 */
    private String loadLocation;

    /** 航班信息 */
    private String flightInfo;

    /** 板箱号 */
    private String uldCode;

    /** 是否危险品 0 否 1 是 */
    private Integer isDanger;

    /** 危险品UN编码 */
    private String dangerCode;

    /** 特货代码 */
    private String specialCode;

    /** 重量 */
    private BigDecimal weight;

    /** 品名 */
    private String cargoName;

    /** 卸机站 */
    private String desPort;

    /** 总件数 */
    private Integer quantity;

    /** 收货人 */
    private String consign;
}
