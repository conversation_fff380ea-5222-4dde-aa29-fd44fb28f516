package com.gzairports.hz.business.departure.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzairports.common.basedata.domain.BaseAgent;
import com.gzairports.common.basedata.domain.BaseBalance;
import com.gzairports.common.basedata.domain.BaseCargoCode;
import com.gzairports.common.basedata.mapper.BaseAgentMapper;
import com.gzairports.common.basedata.mapper.BaseBalanceMapper;
import com.gzairports.common.basedata.mapper.CargoCodeMapper;
import com.gzairports.common.business.arrival.domain.FlightLoadUldWaybill;
import com.gzairports.common.business.arrival.domain.FlightLoadWaybill;
import com.gzairports.common.business.arrival.domain.HzArrTally;
import com.gzairports.common.business.arrival.domain.vo.ArrItemVo;
import com.gzairports.common.business.arrival.domain.vo.ChargeRuleVo;
import com.gzairports.common.business.arrival.mapper.*;
import com.gzairports.common.business.departure.domain.CostDetail;
import com.gzairports.common.business.departure.domain.FlightInfo;
import com.gzairports.common.business.departure.domain.WaybillFee;
import com.gzairports.common.business.departure.domain.WaybillTrace;
import com.gzairports.common.business.departure.domain.vo.*;
import com.gzairports.common.business.departure.mapper.CostDetailMapper;
import com.gzairports.common.business.departure.mapper.PullDownMapper;
import com.gzairports.common.business.departure.mapper.WaybillFeeMapper;
import com.gzairports.common.business.departure.service.impl.WaybillTraceServiceImpl;
import com.gzairports.common.charge.domain.HzChargeIrRelation;
import com.gzairports.common.charge.domain.HzChargeItemRule;
import com.gzairports.common.charge.domain.HzChargeItems;
import com.gzairports.common.charge.domain.HzChargeRule;
import com.gzairports.common.charge.domain.vo.BillRuleVo;
import com.gzairports.common.charge.domain.vo.IrRelationVo;
import com.gzairports.common.charge.mapper.HzChargeIrRelationMapper;
import com.gzairports.common.charge.mapper.HzChargeItemRuleMapper;
import com.gzairports.common.charge.mapper.HzChargeItemsMapper;
import com.gzairports.common.charge.mapper.HzChargeRuleMapper;
import com.gzairports.common.charge.service.BillingRule;
import com.gzairports.common.charge.service.impl.BillingRuleFactory;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.common.log.service.IWaybillLogService;
import com.gzairports.common.system.domain.SysConfig;
import com.gzairports.common.system.mapper.SysConfigMapper;
import com.gzairports.common.utils.BigDecimalRoundUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.hz.business.arrival.domain.query.TallyWaybillKey;
import com.gzairports.hz.business.arrival.domain.vo.BillExportVo;
import com.gzairports.hz.business.departure.domain.*;
import com.gzairports.hz.business.departure.domain.query.BillExportQuery;
import com.gzairports.hz.business.departure.domain.query.ChargeQuery;
import com.gzairports.hz.business.departure.domain.query.WaybillDeptQuery;
import com.gzairports.hz.business.departure.domain.vo.*;
import com.gzairports.hz.business.departure.mapper.*;
import com.gzairports.hz.business.departure.service.IChargeService;
import com.gzairports.wl.departure.domain.vo.OnlineMawbVo;
import com.gzairports.wl.departure.domain.vo.OnlineWaybillVo;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 收费管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Service
public class ChargeServiceImpl implements IChargeService {

    @Autowired
    private AllAirWaybillMapper airWaybillMapper;

    @Autowired
    private CostDetailMapper costDetailMapper;

    @Autowired
    private HzCollectWaybillMapper collectWaybillMapper;

    @Autowired
    private FlightInfoMapper flightInfoMapper;

    @Autowired
    private FlightLoadUldWaybillMapper loadUldWaybillMapper;

    @Autowired
    private FlightLoadWaybillMapper loadWaybillMapper;

    @Autowired
    private FlightLoadUldMapper loadUldMapper;

    @Autowired
    private FlightLoadMapper loadMapper;

    @Autowired
    private HzChargeRuleMapper ruleMapper;

    @Autowired
    private HzChargeItemsMapper chargeItemsMapper;

    @Autowired
    private CargoCodeMapper cargoCodeMapper;

    @Autowired
    private HzChargeIrRelationMapper relationMapper;

    @Autowired
    private HzChargeItemRuleMapper itemRuleMapper;

    @Autowired
    private HzColdRegisterServiceImpl registerService;

    @Autowired
    private HzColdRegisterMapper registerMapper;

    @Autowired
    private WaybillFeeMapper feeMapper;

    @Autowired
    private BaseAgentMapper baseAgentMapper;

    @Autowired
    private BaseBalanceMapper baseBalanceMapper;

    @Autowired
    private WaybillTraceServiceImpl waybillTraceService;

    @Autowired
    private IWaybillLogService waybillLogService;

    @Autowired
    private SysConfigMapper sysConfigMapper;

    @Autowired
    private PickUpWaybillMapper pickUpWaybillMapper;

    @Autowired
    private HzArrItemMapper itemMapper;

    @Autowired
    private PullDownMapper pullDownMapper;

    /**
     * 收费管理列表数据
     * @param query 查询参数
     * @return 列表数据
     */
    @Override
    public ChargeVo selectList(ChargeQuery query) {
        ChargeVo vo = new ChargeVo();
        List<Integer> payStatus = new ArrayList<>();
        if (query.getPayStatus() != null){
            if(query.getPayStatus().contains(0)){
                payStatus.add(1);
                payStatus.add(5);
                payStatus.add(9);
            }
            if(query.getPayStatus().contains(1)){
                payStatus.add(3);
                payStatus.add(4);
                payStatus.add(7);
                payStatus.add(8);
                payStatus.add(11);
                payStatus.add(12);
            }
            if(query.getPayStatus().contains(2)){
                payStatus.add(2);
                payStatus.add(6);
                payStatus.add(10);
            }
        }
        query.setPayStatus(payStatus);
        List<OnlineWaybillVo> totalOrderList = airWaybillMapper.selectTotalOrderList(query);

        // 未支付费用
        BigDecimal unPayCostSum = BigDecimal.ZERO;
        // 未支付单数
        int unPayCount = 0;
        // 已支付费用
        BigDecimal payCostSum = BigDecimal.ZERO;
        // 已支付单数
        int payCount = 0;
        // 未结算费用
        BigDecimal notSettle = BigDecimal.ZERO;
        // 结算费用
        BigDecimal settleCostSum = BigDecimal.ZERO;
        // 已结算单数
        int settleCount = 0;
        // 退款费用
        BigDecimal refundCostSum = BigDecimal.ZERO;
        List<WaybillDeptQuery> keys = new ArrayList<>();
        List<String> waybillCodes = new ArrayList<>();
        List<FlightLoadWaybillVO> flightIdList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(totalOrderList)){
            totalOrderList.stream().filter(e->e.getDeptId() != null && StringUtils.isNotEmpty(e.getWaybillCode()))
                    .forEach(waybill-> keys.add(new WaybillDeptQuery(waybill.getDeptId(), waybill.getWaybillCode())));
            waybillCodes = totalOrderList.stream().map(OnlineWaybillVo::getWaybillCode).distinct().collect(Collectors.toList());
        }
        Map<WaybillDeptQuery,List<CostDetail>> unPayMap = new HashMap<>();
        Map<WaybillDeptQuery,List<CostDetail>> payMap = new HashMap<>();
        Map<WaybillDeptQuery,List<CostDetail>> totalSettleMap = new HashMap<>();
        Map<WaybillDeptQuery,List<CostDetail>> refundMap = new HashMap<>();
        if (!keys.isEmpty()){
            // 未支付
            List<CostDetail> unPayList = costDetailMapper.selectPayOrSettleData(keys,0,0);
            if (!CollectionUtils.isEmpty(unPayList)){
                unPayMap = unPayList.stream()
                        .collect(Collectors.groupingBy(
                                item -> new WaybillDeptQuery(item.getDeptId(), item.getWaybillCode())));
            }
            // 已支付
            List<CostDetail> payList = costDetailMapper.selectPayOrSettleData(keys,0,1);
            if (!CollectionUtils.isEmpty(payList)){
                payMap = payList.stream()
                        .collect(Collectors.groupingBy(
                                item -> new WaybillDeptQuery(item.getDeptId(), item.getWaybillCode())));
            }
            // 结算
            List<CostDetail> totalSettleList = costDetailMapper.selectPayOrSettleData(keys,1,1);
            if (!CollectionUtils.isEmpty(totalSettleList)){
                totalSettleMap = totalSettleList.stream()
                        .collect(Collectors.groupingBy(
                                item -> new WaybillDeptQuery(item.getDeptId(), item.getWaybillCode())));
            }
            // 退款
            List<CostDetail> refundList = costDetailMapper.selectRefundList(keys);
            if (!CollectionUtils.isEmpty(refundList)){
                refundMap = refundList.stream()
                        .collect(Collectors.groupingBy(
                                item -> new WaybillDeptQuery(item.getDeptId(), item.getWaybillCode())));
            }
        }
        BigDecimal settleCharge = BigDecimal.ZERO;
        Map<String,List<CostDetail>> settleMap = new HashMap<>();
        if (query.getFlightLoadStartTime() != null && query.getFlightLoadEndTime() != null){
            BillExportQuery billExportQuery = new BillExportQuery();
            billExportQuery.setPageNum(null);
            billExportQuery.setPageSize(null);
            billExportQuery.setFlightStartTime(query.getFlightLoadStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
            billExportQuery.setFlightEndTime(query.getFlightLoadEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
            flightIdList = flightInfoMapper.selectFlightWaybills(billExportQuery);
//            BaseAgent baseAgent = baseAgentMapper.selectOne(Wrappers.lambdaQuery(BaseAgent.class).eq(BaseAgent::getAgent, query.getAgentCode().get(0)));
            if (!flightIdList.isEmpty()){
                List<ChargeBilExportVO> chargeBilExportVOS = costDetailMapper.selectByFlightWaybills(flightIdList, null);
                List<BaseAgent> baseAgents = Optional.ofNullable(baseAgentMapper.selectListByAgentCode(query.getAgentCode())).orElseGet(ArrayList::new);
                if (!CollectionUtils.isEmpty(baseAgents)){
                    List<Long> deptIdSet = baseAgents.stream().map(BaseAgent::getDeptId).collect(Collectors.toList());
                    if(deptIdSet.size() > 0){
                        chargeBilExportVOS = chargeBilExportVOS.stream()
                                .filter(e -> deptIdSet.contains(e.getDeptId()))
                                .collect(Collectors.toList());
                    }
                }
                //将运单有多种费用进行分组，然后设置对应的费用值
                Map<String, List<ChargeBilExportVO>> waybillCodeToVO = chargeBilExportVOS.stream()
                        .collect(Collectors.groupingBy(ChargeBilExportVO::getWaybillCode));
                List<ChargeBilExportVO> dataList = new ArrayList<>();
                for (List<ChargeBilExportVO> value : waybillCodeToVO.values()) {
                    ChargeBilExportVO chargeBilExportVO = value.get(0);
                    chargeBilExportVO.setSettleCharge(chargeBilExportVO.getTotalCharge());
                    dataList.add(chargeBilExportVO);
                    for(ChargeBilExportVO exportVo: value){
                        if(!(ObjectUtil.equal(exportVo.getFlightId(),chargeBilExportVO.getFlightId())
                                && ObjectUtil.equal(exportVo.getTotalCharge(),chargeBilExportVO.getTotalCharge()))){
                            exportVo.setSettleCharge(exportVo.getTotalCharge());
                            dataList.add(exportVo);
                        }
                    }
                }
                settleCharge = getSumFee(dataList, ChargeBilExportVO::getSettleCharge);
            }
        }else {
            if (!waybillCodes.isEmpty()){
                List<CostDetail> settleList = costDetailMapper.selectSettleByCodeList(waybillCodes);
                if (!CollectionUtils.isEmpty(settleList)){
                    settleMap = settleList.stream().collect(Collectors.groupingBy(CostDetail::getWaybillCode));
                }
            }
        }
        for (OnlineWaybillVo waybillVo : totalOrderList) {
            WaybillDeptQuery key = new WaybillDeptQuery(waybillVo.getDeptId(), waybillVo.getWaybillCode());
            List<CostDetail> unPayList = unPayMap.getOrDefault(key, Collections.emptyList());
            if (!unPayList.isEmpty()){
                unPayCount++;
                BigDecimal costSum = unPayList.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                unPayCostSum = unPayCostSum.add(costSum);
            }
            BigDecimal paySum = BigDecimal.ZERO;
            BigDecimal settleSum = BigDecimal.ZERO;

            List<CostDetail> payList = payMap.getOrDefault(key, Collections.emptyList());
            if (!payList.isEmpty()){
                BigDecimal costSum = payList.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                payCostSum = payCostSum.add(costSum);
                paySum = costSum;
                payCount++;
            }

            List<CostDetail> totalSettleList = totalSettleMap.getOrDefault(key, Collections.emptyList());
            if (!totalSettleList.isEmpty()){
                settleSum = totalSettleList.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            List<CostDetail> settleList = settleMap.getOrDefault(waybillVo.getWaybillCode(), Collections.emptyList());
            if (!settleList.isEmpty()){
                BigDecimal costSum = settleList.stream().filter(e->e.getFlightId() != null && e.getFlightId() >5).map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                if ("INVALID".equals(waybillVo.getStatus())){
                    costSum = new BigDecimal(0);
                }
                settleCostSum = settleCostSum.add(costSum);
                settleCount++;
            }

            BigDecimal newSettleSum = settleSum == null ? new BigDecimal(0) : settleSum;
            BigDecimal newPaySum = paySum == null ? new BigDecimal(0) : paySum;
            BigDecimal subtract = newPaySum.subtract(newSettleSum);
            BigDecimal bigDecimal = subtract.compareTo(new BigDecimal(0)) < 0 ? new BigDecimal(0) : subtract;
            if ("INVALID".equals(waybillVo.getStatus())){
                notSettle = notSettle.add(BigDecimal.ZERO);
            }else {
                notSettle = notSettle.add(bigDecimal);
            }

            List<CostDetail> refundList = refundMap.getOrDefault(key, Collections.emptyList());
            if (!refundList.isEmpty()){
                BigDecimal costSum = refundList.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                refundCostSum = refundCostSum.add(costSum);
            }
        }
        vo.setNotSettle(notSettle);
        vo.setUnPayOrder(unPayCount);
        vo.setUnPay(unPayCostSum);
        vo.setPayOrder(payCount);
        vo.setPay(payCostSum);
//        if (!flightIdList.isEmpty() && !CollectionUtils.isEmpty(query.getAgentCode()) && query.getAgentCode().size() == 1){
        if (!flightIdList.isEmpty()){
            vo.setSettle(settleCharge);
        }else {
            vo.setSettle(settleCostSum);
        }
        vo.setSettleOrder(settleCount);
        vo.setRefund(refundCostSum);
        Page<OnlineMawbVo> pageOne = new Page<OnlineMawbVo>(query.getPageNum(),query.getPageSize());
        Page<ChargeWaybillVo> vos = airWaybillMapper.selectChargeList(query,pageOne);
        if (CollectionUtils.isEmpty(vos.getRecords())){
            return vo;
        }
        setWaybillVo(vos.getRecords(),query);
        for (ChargeWaybillVo chargeWaybillVo : vos.getRecords()) {
            //这里先暂时去掉 不然和账单导出对不上
            query.setFlightLoadStartTime(null);
            query.setFlightLoadEndTime(null);
            List<CostDetail> settleList = costDetailMapper.selectSettleByTime(chargeWaybillVo.getWaybillCode(),query);
            if (!CollectionUtils.isEmpty(settleList)){
                BigDecimal costSum = settleList.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                chargeWaybillVo.setCostSum(costSum);
            }
            List<CostDetail> payList = costDetailMapper.selectPayOrSettleList(chargeWaybillVo.getWaybillCode(),0,1, null);
            if (!CollectionUtils.isEmpty(payList)){
                BigDecimal costSum = payList.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                chargeWaybillVo.setPayMoney(costSum);
            }
        }
        vo.setVos(vos);
        vo.setTotalOrder(totalOrderList.size());
        return vo;
    }

    private void setWaybillVo(List<ChargeWaybillVo> chargeWaybillVos, ChargeQuery query) {
        for (ChargeWaybillVo chargeWaybillVo : chargeWaybillVos) {
            BaseCargoCode baseCargoCode = cargoCodeMapper.selectByCode(chargeWaybillVo.getCargoCode());
            if (baseCargoCode == null){
                chargeWaybillVo.setCodeNameSame("是");
            }else{
                if (Objects.equals(chargeWaybillVo.getCargoName(),baseCargoCode.getChineseName())){
                    chargeWaybillVo.setCodeNameSame("是");
                }else {
                    chargeWaybillVo.setCodeNameSame("否");
                }
            }
            if (chargeWaybillVo.getWaybillCode().contains("DN")){
                //邮件单 品名回显取货品代码
                if(chargeWaybillVo.getCategoryName()!=null){
                    chargeWaybillVo.setCargoName("08".equals(chargeWaybillVo.getCategoryName())?"普邮":
                            "12".equals(chargeWaybillVo.getCategoryName())?"经邮":
                                    "01".equals(chargeWaybillVo.getCategoryName())?"特快":
                                            chargeWaybillVo.getCargoName());
                }
            }
            List<CostDetail> list = costDetailMapper.selectSettleByTime(chargeWaybillVo.getWaybillCode(),query);
            chargeWaybillVo.setCostSum(new BigDecimal(0));
            if (!CollectionUtils.isEmpty(list)) {
                List<CostDetail> collect = list.stream().filter(e -> e.getFlightId() != 1 && e.getFlightId() != 4 && e.getFlightId() != 3L).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(collect)){
                    BigDecimal reduce = list.stream()
                            .map(detail -> detail.getEditCharge() != null ? detail.getEditCharge() : detail.getTotalCharge())
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    chargeWaybillVo.setCostSum(reduce);
                }
            }
            List<Long> flightInfoIdList = new ArrayList<>();
            List<FlightLoadUldWaybill> loadUldWaybillList = loadUldWaybillMapper.selectList(new QueryWrapper<FlightLoadUldWaybill>().eq("waybill_id", chargeWaybillVo.getId()));
            if(loadUldWaybillList.size() > 0){
                for (FlightLoadUldWaybill flightLoadUldWaybill : loadUldWaybillList){
                    FlightLoadUld flightLoadUld = loadUldMapper.selectById(flightLoadUldWaybill.getLoadUldId());
                    if(flightLoadUld!=null){
                        FlightLoad flightLoad = loadMapper.selectById(flightLoadUld.getFlightLoadId());
                        if (flightLoad!=null){
                            flightInfoIdList.add(flightLoad.getFlightId());
                        }
                    }
                }
            }
            List<FlightLoadWaybill> loadWaybillList = loadWaybillMapper.selectList(new QueryWrapper<FlightLoadWaybill>().eq("waybill_id", chargeWaybillVo.getId()));
            if (loadWaybillList.size() > 0){
                for (FlightLoadWaybill flightLoadWaybill:loadWaybillList){
                    FlightLoadUld flightLoadUld = loadUldMapper.selectById(flightLoadWaybill.getFlightLoadId());
                    if(flightLoadUld!=null){
                        FlightLoad flightLoad = loadMapper.selectById(flightLoadUld.getFlightLoadId());
                        if (flightLoad!=null){
                            flightInfoIdList.add(flightLoad.getFlightId());
                        }
                    }
                }
            }
            int depQuantity = 0;
            BigDecimal depWeight = new BigDecimal(0);
            boolean type = false;
            if (!CollectionUtils.isEmpty(flightInfoIdList)) {
                List<Long> collect = flightInfoIdList.stream().distinct().collect(Collectors.toList());
                for (Long id : collect) {
                    FlightInfo flightInfo = flightInfoMapper.selectById(id);
                    if (flightInfo != null) {
                        if ("D".equals(flightInfo.getIsOffin()) && flightInfo.getStartRealTakeoffTime() != null) {
                            type = true;
                        }
                    }
                }
                if (type) {
                    // 航班起飞才会有出港件数和运单
                    List<FlightLoadUldWaybill> loadUldWaybills = loadUldWaybillMapper.selectList(new QueryWrapper<FlightLoadUldWaybill>().eq("waybill_id", chargeWaybillVo.getId()));
                    if (!CollectionUtils.isEmpty(loadUldWaybills)) {
                        int quantity = loadUldWaybills.stream().mapToInt(FlightLoadUldWaybill::getQuantity).sum();
                        depQuantity = depQuantity + quantity;
                        BigDecimal weight = loadUldWaybills.stream().map(FlightLoadUldWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                        depWeight = depWeight.add(weight);
                    }
                    List<FlightLoadWaybill> loadWaybills = loadWaybillMapper.selectList(new QueryWrapper<FlightLoadWaybill>().eq("waybill_id", chargeWaybillVo.getId()));
                    if (!CollectionUtils.isEmpty(loadWaybills)) {
                        int quantity = loadWaybills.stream().mapToInt(FlightLoadWaybill::getQuantity).sum();
                        depQuantity = depQuantity + quantity;
                        BigDecimal weight = loadWaybills.stream().map(FlightLoadWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                        depWeight = depWeight.add(weight);
                    }
                }
            }
            chargeWaybillVo.setDepQuantity(depQuantity);
            chargeWaybillVo.setDepWeight(depWeight);
            chargeWaybillVo.setDepWeightStr(depWeight.toString());
            chargeWaybillVo.setPayStatusStr(PAY_STATUS.get(chargeWaybillVo.getPayStatus()));
        }
    }

    /**
     * 收费明细
     * @param id 运单id
     * @return 明细
     */
    @Override
    public ChargeInfoVo getInfo(Long id) {
        ChargeInfoVo vo = new ChargeInfoVo();
        AirWaybill airWaybill = airWaybillMapper.selectById(id);
        List<CostDetail> details = costDetailMapper.selectPayOrSettleList(airWaybill.getWaybillCode(),0,3, airWaybill.getDeptId());
        if (!CollectionUtils.isEmpty(details)){
            vo.setDetails(details);
            BigDecimal reduce = details.stream().map(detail -> detail.getEditCharge() != null ? detail.getEditCharge() : detail.getTotalCharge()).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setPayTotal(reduce);
            for (CostDetail detail : details) {
                if (detail.getSettleDepWeight() == null){
                    detail.setSettleDepWeight(airWaybill.getWeight());
                }
            }
        }
        List<SettleDetailVo> voList = new ArrayList<>();
        List<CostDetail> settleList = costDetailMapper.selectPayOrSettleList(airWaybill.getWaybillCode(),1,1, airWaybill.getDeptId());
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (!CollectionUtils.isEmpty(settleList)){
            Map<String, List<CostDetail>> collect = settleList.stream().collect(Collectors.groupingBy(e-> {
                Date createTime = e.getCreateTime();
                String timePart = (createTime != null) ? format.format(createTime) : " ";
                return e.getFlightId() + "," + timePart;
            }));
            for (Map.Entry<String, List<CostDetail>> longListEntry : collect.entrySet()) {
                String[] split = longListEntry.getKey().split(",");
                SettleDetailVo settleDetailVo = new SettleDetailVo();
                if ("1".equals(split[0])){
                    settleDetailVo.setFlightNo("退货");
                }else if ("4".equals(split[0])) {
                    settleDetailVo.setFlightNo("退款");
                }else if ("3".equals(split[0])) {
                    settleDetailVo.setFlightNo("作废退款");
                }else if ("1111".equals(split[0])) {
                    settleDetailVo.setFlightNo("3个月未处理自动收取");
                }else {
                    FlightInfo info = flightInfoMapper.selectById(split[0]);
                    if (info != null){
                        settleDetailVo.setFlightNo(info.getAirWays() + info.getFlightNo());
                        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                        settleDetailVo.setFlightDate(dateFormat.format(info.getExecDate()));
                    }
                }
                BigDecimal reduce = longListEntry.getValue().stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                settleDetailVo.setSettleTotal(reduce);
                try {
                    if (!" ".equals(split[1])){
                        settleDetailVo.setCreateTime(format.parse(split[1]));
                    }
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
                settleDetailVo.setSettleList(longListEntry.getValue());
                voList.add(settleDetailVo);
            }
        }
        if (!CollectionUtils.isEmpty(voList)){
            List<SettleDetailVo> collect = voList.stream().sorted(Comparator.comparing(SettleDetailVo::getCreateTime)).collect(Collectors.toList());
            vo.setVoList(collect);
        }
        List<CostDetail> waitSettleList = costDetailMapper.selectPayOrSettleList(airWaybill.getWaybillCode(),1,0,airWaybill.getDeptId());
        vo.setWaitPayList(waitSettleList);
        vo.setCargoCode(airWaybill.getCargoCode());
        vo.setQuantity(airWaybill.getQuantity());
        vo.setWeight(airWaybill.getWeight());
        return vo;
    }

    /**
     * 新增费用
     * @param detail 费用数据
     * @return 结果
     */
    @Override
    public int add(CostDetail detail) {
        HzChargeIrRelation relation = relationMapper.selectById(detail.getIrId());
        HzChargeRule hzChargeRule = ruleMapper.selectById(relation.getRuleId());
        AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                .eq("waybill_code", detail.getWaybillCode())
                .eq("type","DEP")
                .eq("is_del",0));
        if ("ColdStorageBillingRule.class".equals(hzChargeRule.getClassName())){
            HzColdRegister hzColdRegister = new HzColdRegister();
            hzColdRegister.setWaybillCode(detail.getWaybillCode());
            hzColdRegister.setStatus(0);
            hzColdRegister.setWareTime(detail.getStoreStartTime());
            hzColdRegister.setOutTime(detail.getStoreEndTime());
            hzColdRegister.setType("DEP");
            long diff = detail.getStoreEndTime().getTime() - detail.getStoreStartTime().getTime();
            long hour = diff / (60 * 60 * 1000);
            hzColdRegister.setUseTime(new BigDecimal(hour));
            int round = Math.round(hour);
            hzColdRegister.setChargeTime(new BigDecimal(round));
            hzColdRegister.setCargoName(airWaybill.getCargoName());
            String coldStore = airWaybill.getColdStore();
            if(StringUtils.isNull(coldStore)){
                airWaybill.setColdStore(detail.getColdStore());
            }else if (!coldStore.equals(detail.getColdStore())){
                throw new CustomException("该运单冷库为" + coldStore + "请重新选择");
            }
            List<HzChargeItemRule> list = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>().eq("ir_id",relation.getId()));
            hzColdRegister.setColdStore(detail.getColdStore());
            registerService.countSum(airWaybill,detail.getStoreStartTime(),detail.getStoreEndTime(),
                    hzColdRegister,list,relation.getItemId());
            hzColdRegister.setUpdateTime(new Date());
            registerMapper.insert(hzColdRegister);
        }
        detail.setDeptId(airWaybill.getDeptId());
        if (airWaybill.getPayStatus() > 4){
            detail.setType(1);
        }
        return costDetailMapper.insert(detail);
    }


    @Override
    public BillRuleVo countCost(CostDetail detail) {
        HzChargeIrRelation relation = relationMapper.selectById(detail.getIrId());
        HzChargeRule hzChargeRule = ruleMapper.selectById(relation.getRuleId());
        List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>().eq("ir_id", detail.getIrId()));
        if (CollectionUtils.isEmpty(itemRules)){
            BillRuleVo vo = new BillRuleVo();
            vo.setTotalCharge(new BigDecimal(0));
            return vo;
        }
        HzChargeItems hzChargeItems = chargeItemsMapper.selectById(relation.getItemId());
        Date date = new Date();
        Instant startInstant = date.toInstant();
        LocalTime startTime = startInstant.atZone(ZoneId.systemDefault()).toLocalTime();
        detail.setPointTime(startTime);
        AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>().eq("waybill_code", detail.getWaybillCode()));
        BillingRule rule = BillingRuleFactory.createRule(hzChargeRule.getClassName());
        BillRuleVo ruleVo = rule.calculateFee(itemRules, airWaybill.getChargeWeight(), airWaybill.getQuantity(), detail);
        BigDecimal bigDecimal = BigDecimalRoundUtils.bigDecimalRound(hzChargeItems.getRoundRule(), ruleVo.getTotalCharge());
        ruleVo.setTotalCharge(bigDecimal);
        return ruleVo;
    }

    public BillRuleVo countCost1(CostDetail detail,BigDecimal weight, Integer quantity) {
        HzChargeIrRelation relation = relationMapper.selectById(detail.getIrId());
        if (relation == null){
            BillRuleVo vo = new BillRuleVo();
            vo.setTotalCharge(new BigDecimal(0));
            return vo;
        }
        HzChargeItems hzChargeItems = chargeItemsMapper.selectById(relation.getItemId());
        HzChargeRule hzChargeRule = ruleMapper.selectById(relation.getRuleId());
        List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>().eq("ir_id", detail.getIrId()));
        if (CollectionUtils.isEmpty(itemRules)){
            BillRuleVo vo = new BillRuleVo();
            vo.setTotalCharge(new BigDecimal(0));
            return vo;
        }
        BillingRule rule = BillingRuleFactory.createRule(hzChargeRule.getClassName());
        BillRuleVo ruleVo = rule.calculateFee(itemRules, weight, quantity, detail);
        BigDecimal bigDecimal = BigDecimalRoundUtils.bigDecimalRound(hzChargeItems.getRoundRule(), ruleVo.getTotalCharge());
        ruleVo.setTotalCharge(bigDecimal);
        return ruleVo;
    }

    /**
     * 根据收费项目id查询最高优先级规则
     * @param vo 收费参数
     * @return 最该优先级规则
     */
    @Override
    public ChargeRuleVo getHighRule(ItemDetailVo vo) {
        ChargeRuleVo ruleVo = new ChargeRuleVo();
        ruleVo.setIsNoCharge(0);
        List<HzChargeIrRelation> relations = relationMapper.selectList(new QueryWrapper<HzChargeIrRelation>().eq("item_id", vo.getItemId()).eq("is_del",0));
        if (CollectionUtils.isEmpty(relations)){
            return ruleVo;
        }
        ItemWaybillVo waybillVo = airWaybillMapper.selectWaybillItemInfo(vo);
        int maxMatchCount = 0;
        List<HzChargeIrRelation> ruleList = new ArrayList<>();
        for (HzChargeIrRelation hzChargeRule : relations) {
            int matchCount = 0;
            if (StringUtils.isNotEmpty(hzChargeRule.getNoCharge()) && !hzChargeRule.getNoCharge().contains(waybillVo.getDeptId().toString())){
                continue;
            }
            if (StringUtils.isNotEmpty(hzChargeRule.getPrefix()) && !hzChargeRule.getPrefix().contains(vo.getWaybillCode().substring(4,7))){
                continue;
            }
            if (StringUtils.isNotEmpty(hzChargeRule.getCategory()) && !hzChargeRule.getCategory().contains(waybillVo.getCategoryName())){
                continue;
            }
            if (!hzChargeRule.getCrossAir().equals(waybillVo.getCrossAir())){
                continue;
            }
            if (hzChargeRule.getIsSouth() == 1){
                continue;
            }
            if (hzChargeRule.getIsExit() == 1){
                continue;
            }
            int cargoMatchCount = isCargoCodeMatch(hzChargeRule, waybillVo.getCargoName());
            if (cargoMatchCount >= 0) {
                matchCount += cargoMatchCount;
            }

            if (matchCount > 0) {
                if (matchCount > maxMatchCount) {
                    maxMatchCount = matchCount;
                    ruleList.clear();
                    ruleList.add(hzChargeRule);
                } else if (matchCount == maxMatchCount) {
                    ruleList.add(hzChargeRule);
                }
            }
        }
        if (ruleVo.getIsNoCharge() == 1){
            throw new CustomException("该费用项目设置不收此代理人");
        }
        if (CollectionUtils.isEmpty(ruleList)){
            throw new CustomException("该费用项目不适配本运单");
        }
        HzChargeIrRelation relation = ruleList.stream().max(Comparator.comparing(HzChargeIrRelation::getPriority)).orElse(null);
        if (relation == null){
            return ruleVo;
        }
        HzChargeRule rule = ruleMapper.selectById(relation.getRuleId());
        ruleVo.setIrId(relation.getId());
        ruleVo.setClassName(rule.getClassName());
        ruleVo.setRuleName(rule.getRuleName());
        return ruleVo;
    }

    /**
     * 导出收费管理列表
     * @param query 查询条件
     * @return 返回数据
     */
    @Override
    public List<ChargeWaybillVo> importData(ChargeQuery query) {
        Page<ChargeWaybillVo> page = new Page<>(1,-1);
        Page<ChargeWaybillVo> chargeWaybillVos = airWaybillMapper.selectChargeList(query,page);
        if (CollectionUtils.isEmpty(chargeWaybillVos.getRecords())){
            return null;
        }
        setWaybillVo(chargeWaybillVos.getRecords(), query);
        for (ChargeWaybillVo chargeWaybillVo : chargeWaybillVos.getRecords()) {
            List<CostDetail> payList = costDetailMapper.selectPayOrSettleList(chargeWaybillVo.getWaybillCode(),0,1, null);
            if (!CollectionUtils.isEmpty(payList)){
                BigDecimal costSum = payList.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                chargeWaybillVo.setPayMoney(costSum);
            }
        }
        BigDecimal costSum;
        chargeWaybillVos.getRecords().forEach(e->{
            String s = WAYBILL_STATUS.get(e.getStatus());
            e.setStatus(s);
        });
        costSum = chargeWaybillVos.getRecords().stream()
                .map(ChargeWaybillVo::getCostSum)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        ChargeWaybillVo chargeWaybillVo = new ChargeWaybillVo();
        chargeWaybillVo.setDepWeightStr("合计");
        chargeWaybillVo.setCostSum(costSum);
        chargeWaybillVos.getRecords().add(chargeWaybillVo);
        return chargeWaybillVos.getRecords();
    }



    /**
     * 编辑费用明细数据
     * @param detail 更新后的费用明细数据
     * @return 结果
     */
    @Override
    public int editCost(CostDetail detail) {
       return costDetailMapper.updateById(detail);
    }

    /**
     * 删除费用明细
     * @param id 费用明细id
     * @return 结果
     */
    @Override
    public int delCost(Long id) {
        return costDetailMapper.deleteById(id);
    }

    /**
     * 费用明细详情
     * @param id 费用明细id
     * @return 详情
     */
    @Override
    public CostDetail costInfo(Long id) {
        CostDetail costDetail = costDetailMapper.selectCostInfo(id);
        HzChargeIrRelation relation = relationMapper.selectById(costDetail.getIrId());
        costDetail.setChargeItemsId(relation.getItemId());
        return costDetail;
    }

    @Override
    public int handSettle(HandSettleVo vo) {
        if (vo.getDepQuantity() > vo.getQuantity() || vo.getDepWeight().compareTo(vo.getWeight()) > 0){
            throw new CustomException("已出港件数或重量不能大于运单件数和重量");
        }
        List<LoadInfoVo> voList = loadWaybillMapper.selectByLoadInfo(vo.getWaybillId());
        if (CollectionUtils.isEmpty(voList)){
            throw new CustomException("无当前运单配载信息");
        }
        String waybillCode = airWaybillMapper.selectWaybillCode(vo.getWaybillId());
        List<Long> loadIds = voList.stream().map(LoadInfoVo::getLoadId).distinct().collect(Collectors.toList());
        // 已出港航班
        List<Long> longList = loadMapper.selectFlightIds(loadIds);
        List<LoadInfoVo> vos = voList.stream()
                .filter(e -> longList.contains(e.getLoadId()))
                .collect(Collectors.toList());
        BigDecimal allWeight = vo.getDepWeight();
        if (!CollectionUtils.isEmpty(vos)){
            int quantity = vos.stream().mapToInt(LoadInfoVo::getQuantity).sum();
            int i = quantity + vo.getDepQuantity();
            BigDecimal weight = vos.stream().map(LoadInfoVo::getWeight).reduce(BigDecimal.ZERO,BigDecimal::add);
            allWeight = vo.getDepWeight().add(weight);
            if (i > vo.getQuantity() || allWeight.compareTo(vo.getWeight()) > 0){
                throw new CustomException("历史出港件数或重量大于运单件数和重量");
            }
        }
        List<LoadInfoVo> collect = voList.stream().sorted(Comparator.comparing(LoadInfoVo::getCreateTime).reversed()).collect(Collectors.toList());
        LoadInfoVo loadInfoVo = collect.get(0);
        if (loadInfoVo.getWeightTime() == null){
            throw new CustomException("当前运单未复磅完成");
        }
        FlightInfo info = flightInfoMapper.selectFlightNo(loadInfoVo.getLoadId());
        LocalTime startTime = info.getStartSchemeTakeoffTime().toLocalTime();
        LocalTime endTime = startTime.plusSeconds(1);
        Instant weightTime = loadInfoVo.getWeightTime().toInstant();
        Instant now = Instant.now();
        long between = ChronoUnit.HOURS.between(weightTime,now);
        if (between < 2){
            throw new CustomException("已复重出库但未超过2小时不可以手动结算");
        }
        HzCollectWaybill collectWaybill = collectWaybillMapper.selectOne(new LambdaQueryWrapper<HzCollectWaybill>()
                .eq(HzCollectWaybill::getWaybillId, vo.getWaybillId())
                .ne(HzCollectWaybill::getStatus, "REFUSE")
                .orderByAsc(HzCollectWaybill::getCollectTime).last("limit 1"));
        Date storeStartTime = collectWaybill.getCollectTime();
        Date storeEndTime = loadInfoVo.getWeightTime();
        HzColdRegister hzColdRegister = registerMapper.selectOne(new QueryWrapper<HzColdRegister>()
                .eq("waybill_code", waybillCode)
                .eq("type", "DEP")
                .isNotNull("cold_store"));
        if (hzColdRegister != null){
            if (hzColdRegister.getOutTime() == null){
                throw new CustomException("当前运单号" + waybillCode + "无出库时间，请及时处理");
            }else {
                storeEndTime = hzColdRegister.getOutTime();
            }
        }
        AirWaybill airWaybill = airWaybillMapper.selectById(vo.getWaybillId());
        BigDecimal weightRateCeiling;
        BigDecimal weightRateFloor;
        BigDecimal chargeWeight = airWaybill.getChargeWeight() == null ? new BigDecimal(0) : airWaybill.getChargeWeight();
        if (airWaybill.getWeight() == null || airWaybill.getWeight().compareTo(new BigDecimal(0)) == 0){
            weightRateCeiling = new BigDecimal(0);
            weightRateFloor = new BigDecimal(0);
        }else {
            BigDecimal bigDecimal = chargeWeight.divide(airWaybill.getWeight(),5, RoundingMode.DOWN).multiply(vo.getDepWeight());
            weightRateCeiling = bigDecimal.setScale(0, RoundingMode.CEILING);
            weightRateFloor = bigDecimal.setScale(0, RoundingMode.FLOOR);
        }
        List<CostDetail> details = costDetailMapper.selectList(new QueryWrapper<CostDetail>()
                .eq("waybill_code", waybillCode)
                .eq("type",0)
                .eq("service_type",0)
                .eq("is_del",0)
                .eq("dept_id",airWaybill.getDeptId()));
        BigDecimal settleCostSum = costDetailMapper.depIsSettleData(waybillCode, airWaybill.getDeptId());
        settleCostSum = settleCostSum == null ? new BigDecimal(0) : settleCostSum;
        BigDecimal oldCostSum = new BigDecimal(0);
        if (!CollectionUtils.isEmpty(details)){
            oldCostSum = details.stream().filter(e -> e.getIsSettle() == 1).map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        BigDecimal costSum = new BigDecimal(0);
        for (CostDetail detail : details) {
            if (detail.getIsDel() == 1){
                continue;
            }
            detail.setStartTime(startTime);
            detail.setEndTime(endTime);
            storeStartTime = storeStartTime == null ? detail.getStoreStartTime() : storeStartTime;
            storeEndTime = storeEndTime == null ? detail.getStoreEndTime() : storeEndTime;
            detail.setStoreStartTime(storeStartTime);
            detail.setStoreEndTime(storeEndTime);
            long diffInMillis = Math.abs(storeEndTime.getTime() - storeStartTime.getTime());
            double diffInDays = diffInMillis / 86400000.0;
            detail.setDaysInStorage(diffInDays);
            BillRuleVo vo1 = countCost1(detail, weightRateCeiling, vo.getDepQuantity());
            BillRuleVo vo2 = countCost1(detail, weightRateFloor, vo.getDepQuantity());
            if (vo1.getTotalCharge().compareTo(detail.getTotalCharge()) >= 0){
                detail.setTotalCharge(vo1.getTotalCharge());
                detail.setQuantity(vo1.getQuantity());
                detail.setRate(vo1.getRate());
                costSum = costSum.add(vo1.getTotalCharge());
            }else {
                detail.setTotalCharge(vo2.getTotalCharge());
                detail.setQuantity(vo2.getQuantity());
                detail.setRate(vo2.getRate());
                costSum = costSum.add(vo2.getTotalCharge());
            }
            detail.setSettleDepQuantity(vo.getDepQuantity());
            detail.setSettleDepWeight(vo.getDepWeight());
            detail.setType(1);
            detail.setIsSettle(1);
            detail.setIsAuto(1);
            detail.setFlightId(info.getFlightId());
            detail.setDeptId(airWaybill.getDeptId());
            detail.setId(null);
            detail.setPointTime(startTime);
            detail.setCreateTime(new Date());
            costDetailMapper.insert(detail);
        }
        WaybillFee waybillFee = feeMapper.selectOne(new QueryWrapper<WaybillFee>()
                .eq("waybill_code", airWaybill.getWaybillCode())
                .eq("dept_id", airWaybill.getDeptId())
                .eq("type","DEP"));
        WaybillTrace waybillTrace = new WaybillTrace();
        BaseAgent agent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", airWaybill.getDeptId()));
        if (airWaybill.getWeight().compareTo(allWeight) <= 0){
            BigDecimal allCostSum = costSum.add(settleCostSum);
            if (allCostSum.compareTo(oldCostSum) > 0){
                waybillTrace.setReturnMoney(new BigDecimal(0));
                BigDecimal deduct = allCostSum.subtract(oldCostSum);
                setStatus1(airWaybill, costSum, waybillFee, agent, deduct);
            }else if (allCostSum.compareTo(oldCostSum) == 0){
                waybillTrace.setReturnMoney(new BigDecimal(0));
                setStatus(airWaybill, costSum, waybillFee, agent);
            }else {
                BigDecimal add = oldCostSum.subtract(allCostSum);
                waybillTrace.setReturnMoney(add);
                if (agent != null){
                    if (agent.getSettleMethod() == 1) {
                        BigDecimal balance = agent.getBalance() == null ? new BigDecimal(0) : agent.getBalance();
                        BigDecimal subtract = balance.add(add);
                        agent.setBalance(subtract);
                        baseAgentMapper.updateBaseAgent(agent);
                        BaseBalance baseBalance = new BaseBalance();
                        baseBalance.setAgentId(agent.getId());
                        baseBalance.setBalance(agent.getBalance());
                        baseBalance.setType("增加余额");
                        baseBalance.setCreateTime(new Date());
                        baseBalance.setCreateBy("系统");
                        // todo 流水号需从银联支付接口获取
                        //baseBalance.setSerialNo();
                        baseBalance.setTradeMoney(add);
                        baseBalance.setWaybillCode(airWaybill.getWaybillCode());
                        baseBalance.setRemark("运单结算退款");
                        baseBalanceMapper.insertBaseBalance(baseBalance);
                        updateStatus(airWaybill, costSum, waybillFee, 10, add);
                    } else if (agent.getSettleMethod() == 0){
                        updateStatus(airWaybill, costSum, waybillFee, 9, add);
                    }else {
                        if (agent.getPayMethod() == 0){
                            updateStatus(airWaybill, costSum, waybillFee, 11, add);
                        }else {
                            updateStatus(airWaybill, costSum, waybillFee, 12, add);
                        }
                    }
                }else {
                    updateStatus(airWaybill, costSum, waybillFee, 12, add);
                }
            }
        }else {
            waybillTrace.setReturnMoney(new BigDecimal(0));
            if (costSum.compareTo(oldCostSum) > 0) {
                BigDecimal deduct = costSum.subtract(oldCostSum);
                setStatus1(airWaybill, costSum, waybillFee, agent, deduct);
            }else {
                setStatus(airWaybill, costSum, waybillFee, agent);
            }
        }
        // 运单跟踪数据
        waybillTrace.setOperTime(new Date());
        waybillTrace.setOperPieces(vo.getQuantity());
        waybillTrace.setOperWeight(vo.getWeight());
        waybillTrace.setPayMoney(costSum);
        waybillTrace.setWaybillCode(airWaybill.getWaybillCode());
        waybillTrace.setNodeName("已结算");
        waybillTrace.setChargeWeight(airWaybill.getChargeWeight());
        waybillTraceService.insertWaybillTrace(waybillTrace);

        //运单日志
        WaybillLog waybillLog = waybillLogService.getWaybillLog(
                airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                airWaybill.getWeight() != null ? airWaybill.getWeight().toString() : null,
                airWaybill.getQuantity() != null ? airWaybill.getQuantity().toString() : null,
                airWaybill.getFlightNo1(), null, "操作成功", 0, null, new Date(),
                "运单手动结算", "DEP", null);
        waybillLogService.insertWaybillLog(waybillLog);

        return 1;
    }

    @Override
    public List<ChargeImportVo> chargeExport(ChargeQuery query) {
        Page<ChargeImportVo> page = new Page<>(1,-1);
        Page<ChargeImportVo> vos = airWaybillMapper.chargeExportList(query,page);
        Set<String> LongSet = new HashSet<>();
        List<ChargeImportVo> vosList = new ArrayList<>();
        for(ChargeImportVo vo : vos.getRecords()){
            if (LongSet.add(vo.getWaybillCode())) {
                vosList.add(vo);
            }
        }
        for (ChargeImportVo vo : vosList) {
            List<LoadInfoVo> voList = loadWaybillMapper.selectByLoadInfo(vo.getWaybillId());
            if (!CollectionUtils.isEmpty(voList)){
                List<Long> loadIds = voList.stream().map(LoadInfoVo::getLoadId).distinct().collect(Collectors.toList());
                List<Long> longList = loadMapper.selectFlightIds(loadIds);
                List<LoadInfoVo> infoVos = voList.stream()
                        .filter(e -> longList.contains(e.getLoadId()))
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(infoVos)){
                    BigDecimal weight = infoVos.stream().map(LoadInfoVo::getWeight).reduce(BigDecimal.ZERO,BigDecimal::add);
                    vo.setDepWeight(weight);
                }
            }
            List<ArrItemVo> itemVos;
            //这里为啥查的是type为0的数据?只查询预授权支付的数据吗?
//            List<ArrItemVo> itemVos = costDetailMapper.selectItemVo(vo.getWaybillCode());
            if(query.getStartTimeSettle()!=null && query.getEndTimeSettle()!=null){
                itemVos = costDetailMapper.selectItemVoBySettleTime(vo.getWaybillCode(),query);
            }else{
                itemVos = costDetailMapper.selectItemVo(vo.getWaybillCode());
            }
            if (!CollectionUtils.isEmpty(itemVos)){
                BigDecimal totalCost = itemVos.stream()
                        .map(hzArrItem -> hzArrItem.getEditCharge() != null ? hzArrItem.getEditCharge() : hzArrItem.getTotalCharge())
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                Map<String, List<ArrItemVo>> collect = itemVos.stream().filter(e->e.getChargeAbb() != null).collect(Collectors.groupingBy(ArrItemVo::getChargeAbb));
                for (Map.Entry<String, List<ArrItemVo>> stringListEntry : collect.entrySet()) {
                    BigDecimal reduce = stringListEntry.getValue().stream()
                            .map(hzArrItem -> hzArrItem.getEditCharge() != null ? hzArrItem.getEditCharge() : hzArrItem.getTotalCharge())
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    switch (stringListEntry.getKey()){
                        case "处置费":
                            vo.setProcessingFee(reduce);
                            break;
                        case "冷藏费":
                            vo.setRefrigerationFee(reduce);
                            break;
                        case "搬运费":
                            vo.setHandlingFee(reduce);
                            break;
                        case "电报费":
                            vo.setCableCharge(reduce);
                            break;
                        case "检查费":
                            vo.setInspectionFee(reduce);
                            break;
                        default:
                            System.out.println("不展示当前收费项目");
                            break;
                    }
                }
                vo.setSubtotal(totalCost);
            }
        }
        ChargeImportVo importVo = new ChargeImportVo();
        if (!CollectionUtils.isEmpty(vosList)){
            importVo.setWaybillCode("合计");
            int quantity = vosList.stream().filter(e->e.getQuantity() != null).mapToInt(ChargeImportVo::getQuantity).sum();
            importVo.setQuantity(quantity);
            BigDecimal weight = vosList.stream().map(ChargeImportVo::getWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO,BigDecimal::add);
            importVo.setWeight(weight);
            BigDecimal depWeight = vosList.stream().map(ChargeImportVo::getDepWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO,BigDecimal::add);
            importVo.setDepWeight(depWeight);
            BigDecimal processingFee = vosList.stream().map(ChargeImportVo::getProcessingFee).filter(Objects::nonNull).reduce(BigDecimal.ZERO,BigDecimal::add);
            importVo.setProcessingFee(processingFee);
            BigDecimal refrigerationFee = vosList.stream().map(ChargeImportVo::getRefrigerationFee).filter(Objects::nonNull).reduce(BigDecimal.ZERO,BigDecimal::add);
            importVo.setRefrigerationFee(refrigerationFee);
            BigDecimal handlingFee = vosList.stream().map(ChargeImportVo::getHandlingFee).filter(Objects::nonNull).reduce(BigDecimal.ZERO,BigDecimal::add);
            importVo.setHandlingFee(handlingFee);
            BigDecimal cableCharge = vosList.stream().map(ChargeImportVo::getCableCharge).filter(Objects::nonNull).reduce(BigDecimal.ZERO,BigDecimal::add);
            importVo.setCableCharge(cableCharge);
            BigDecimal subtotal = vosList.stream().map(ChargeImportVo::getSubtotal).filter(Objects::nonNull).reduce(BigDecimal.ZERO,BigDecimal::add);
            importVo.setSubtotal(subtotal);
            vosList.add(importVo);
        }
        return vosList;
    }

    @Override
    public int errorDataCost(CountCostVo vo) {
        List<Long> list = airWaybillMapper.selectErrorData(vo);
        if (vo.getType() == 1) {
            for (Long aLong : list) {
                AirWaybill airWaybill = airWaybillMapper.selectById(aLong);
                Integer count = costDetailMapper.selectCount(new QueryWrapper<CostDetail>()
                        .eq("waybill_code", airWaybill.getWaybillCode())
                        .eq("dept_id", airWaybill.getDeptId()));
                if (count > 0){
                    continue;
                }
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String execDate = dateFormat.format(airWaybill.getFlightDate1());
                List<FlightInfo> flightInfoList = flightInfoMapper.selectList(new QueryWrapper<FlightInfo>()
                        .eq("air_ways", airWaybill.getFlightNo1().substring(0, 2))
                        .eq("flight_no", airWaybill.getFlightNo1().substring(2))
                        .eq("exec_date", execDate)
                        .eq("is_offin", "D"));
                FlightInfo flight = null;
                if (flightInfoList.size() == 1) {
                    flight = flightInfoList.get(0);
                } else if (flightInfoList.size() > 1) {
                    List<FlightInfo> collect = flightInfoList.stream().filter(flightInfo -> "W/Z".equals(flightInfo.getTask())).collect(Collectors.toList());
                    if (collect.size() > 0) {
                        flight = collect.get(0);
                    }
                }
                BaseCargoCode baseCargoCode2 = cargoCodeMapper.selectByCode(airWaybill.getCargoCode());
                if (airWaybill.getSwitchBill() != 2) {
                    costDetailMapper.delete(new QueryWrapper<CostDetail>().eq("waybill_code", airWaybill.getWaybillCode()));
                    // 生成默认收费项目
                    List<HzChargeItems> hzChargeItems = chargeItemsMapper.selectList(new QueryWrapper<HzChargeItems>()
                            .eq("operation_type", "DEP")
                            .eq("is_default", 1).eq("status", 1)
                            .le("start_effective_time", airWaybill.getWriteTime())
                            .ge("end_effective_time", airWaybill.getWriteTime())
                            .eq("is_del", 0));
                    SysConfig sysConfig = sysConfigMapper.selectConfigById(16L);
                    LocalTime startTime;
                    LocalTime endTime;
                    if (flight == null) {
                        startTime = LocalTime.of(6, 0, 0);
                        endTime = LocalTime.of(6, 0, 1);
                    } else {
                        LocalDateTime takeoffTime = flight.getStartSchemeTakeoffTime();
                        LocalDateTime time = takeoffTime.plusSeconds(1);
                        endTime = time.toLocalTime();
                        startTime = takeoffTime.toLocalTime();
                    }
                    Date date = new Date();
                    Instant startInstant = date.toInstant();
                    long configValue = Long.parseLong(sysConfig.getConfigValue());
                    long times = configValue * 60 * 60;
                    Instant endInstant = startInstant.plusSeconds(times);
                    Date storeEndTime = Date.from(endInstant);
                    BigDecimal costSum = new BigDecimal(0);
                    BigDecimal weightRate;
                    BigDecimal chargeWeight = airWaybill.getChargeWeight() == null ? new BigDecimal(0) : airWaybill.getChargeWeight();
                    if (airWaybill.getWeight() == null || airWaybill.getWeight().compareTo(new BigDecimal(0)) == 0) {
                        weightRate = new BigDecimal(0);
                    } else {
                        BigDecimal bigDecimal = chargeWeight.divide(airWaybill.getWeight(), 5, RoundingMode.DOWN).multiply(airWaybill.getWeight());
                        weightRate = bigDecimal.setScale(0, RoundingMode.CEILING);
                    }
                    for (HzChargeItems hzChargeItem : hzChargeItems) {
                        List<HzChargeIrRelation> relations = relationMapper.selectList(new QueryWrapper<HzChargeIrRelation>().eq("item_id", hzChargeItem.getId()).eq("is_del", 0));
                        int maxMatchCount = 0;
                        List<HzChargeIrRelation> ruleList = new ArrayList<>();
                        for (HzChargeIrRelation hzChargeRule : relations) {
                            if (hzChargeRule.getIsSouth() == 1) {
                                continue;
                            }
                            if (hzChargeRule.getIsExit() == 1) {
                                continue;
                            }
                            if (StringUtils.isNotEmpty(hzChargeRule.getNoCharge()) && !hzChargeRule.getNoCharge().contains(airWaybill.getDeptId().toString())) {
                                continue;
                            }
                            if (StringUtils.isNotEmpty(hzChargeRule.getPrefix()) && !hzChargeRule.getPrefix().contains(airWaybill.getWaybillCode().substring(4, 7))) {
                                continue;
                            }
                            if (StringUtils.isNotEmpty(hzChargeRule.getCategory()) && !hzChargeRule.getCategory().contains(baseCargoCode2.getCategoryCode())) {
                                continue;
                            }
                            if (!hzChargeRule.getCrossAir().equals(airWaybill.getCrossAir())) {
                                continue;
                            }
                            int matchCount = 0;
                            // 根据判断货品代码
                            int cargoMatchCount = isCargoCodeMatch(hzChargeRule, airWaybill.getCargoCode());

                            if (cargoMatchCount >= 0) {
                                matchCount += cargoMatchCount;
                            }
                            if (matchCount > 0) {
                                if (matchCount > maxMatchCount) {
                                    maxMatchCount = matchCount;
                                    ruleList.clear();
                                    ruleList.add(hzChargeRule);
                                } else if (matchCount == maxMatchCount) {
                                    ruleList.add(hzChargeRule);
                                }
                            }
                        }
                        if (!CollectionUtils.isEmpty(ruleList)) {
                            HzChargeIrRelation relation = ruleList.stream().max(Comparator.comparing(HzChargeIrRelation::getPriority)).orElse(null);
                            if (relation != null) {
                                HzChargeRule rule1 = ruleMapper.selectById(relation.getRuleId());
                                List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>().eq("ir_id", relation.getId()));
                                if ("ColdStorageBillingRule.class".equals(rule1.getClassName()) && StringUtils.isEmpty(airWaybill.getColdStore())) {
                                    continue;
                                }
                                CostDetail detail = new CostDetail();
                                detail.setWaybillCode(airWaybill.getWaybillCode());
                                detail.setIrId(relation.getId());
                                detail.setColdStore(airWaybill.getColdStore());
                                detail.setUnit(1);
                                detail.setSmallItem(1);
                                detail.setLargeItem(1);
                                detail.setSuperLargeItem(1);
                                detail.setStartTime(startTime);
                                detail.setEndTime(endTime);
                                detail.setDaysInStorage(1.0);
                                detail.setStoreStartTime(date);
                                detail.setStoreEndTime(storeEndTime);
                                detail.setPointTime(startTime);
                                BillingRule rule = BillingRuleFactory.createRule(rule1.getClassName());
                                BillRuleVo vo1 = rule.calculateFee(itemRules, weightRate, airWaybill.getQuantity(), detail);
                                BigDecimal totalCharge = BigDecimalRoundUtils.bigDecimalRound(hzChargeItem.getRoundRule(), vo1.getTotalCharge());
                                detail.setTotalCharge(totalCharge);
                                detail.setDeptId(airWaybill.getDeptId());
                                detail.setQuantity(vo1.getQuantity());
                                detail.setRate(vo1.getRate());
                                costSum = costSum.add(totalCharge);
                                costDetailMapper.insert(detail);
                            }
                        }
                    }
                    airWaybill.setPayMoney(costSum);
                    airWaybill.setPayStatus(0);
                    airWaybillMapper.updateById(airWaybill);
                }
            }
        }else if (vo.getType() == 2){
            for (Long aLong : list) {
                AirWaybill airWaybill = airWaybillMapper.selectById(aLong);
                if (airWaybill.getPayStatus() != 0){
                    List<CostDetail> details = costDetailMapper.depAutoSettleTask(airWaybill.getWaybillCode(), airWaybill.getDeptId());
                    for (CostDetail detail : details) {
                        if (detail.getType() == 1){
                            detail.setCreateTime(new Date());
                        }
                        detail.setIsSettle(1);
                        costDetailMapper.updateById(detail);
                    }
                }
            }
        }
        return 1;
    }

    @Override
    public BilExportVO selectBillExportData(BillExportQuery query) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startTime = query.getFlightStartTime();
        LocalDateTime endTime = query.getFlightEndTime();

        query.setAgent(query.getAgentCode().get(0));
        BaseAgent baseAgent = baseAgentMapper.selectOne(Wrappers.lambdaQuery(BaseAgent.class).eq(BaseAgent::getAgent, query.getAgent()));
        if (baseAgent == null) {
            throw new CustomException("代理人不存在");
        }

        //---------------------------------------已结算的列表-----------------------------------------------

        List<ChargeBilExportVO> waybillList = Optional.ofNullable(costDetailMapper.selectExportItemVo(query,1,null)).orElseGet(ArrayList::new);

        //将运单有多种费用进行分组，然后设置对应的费用值
        Map<String, List<ChargeBilExportVO>> waybillCodeToVO = waybillList.stream()
                .collect(Collectors.groupingBy(ChargeBilExportVO::getWaybillCode));
        List<ChargeBilExportVO> dataList = new ArrayList<>();
//        List<ChargeBilExportVO> dataListNotSettle = new ArrayList<>();
        List<ChargeBilExportNotSettleVO> dataListNotSettle = new ArrayList<>();
        //序号
        int idx = 1;
        for (List<ChargeBilExportVO> value : waybillCodeToVO.values()) {
            ChargeBilExportVO chargeBilExportVO = value.get(0);
            chargeBilExportVO.setId((long) idx++);
            //根据费用名称设置对应的值
            value.forEach(val -> setFee(chargeBilExportVO, val.getChargeAbb(), val.getTotalCharge()));
            dataList.add(chargeBilExportVO);
        }

        for(ChargeBilExportVO vo : dataList){
            List<ChargeSettleDetailVo> detailVos = new ArrayList<>();
            ChargeQuery chargeQuery = new ChargeQuery();
            chargeQuery.setFlightLoadStartTime(Date.from(query.getFlightStartTime().atZone(ZoneId.systemDefault()).toInstant()));
            chargeQuery.setFlightLoadEndTime( Date.from(query.getFlightEndTime().atZone(ZoneId.systemDefault()).toInstant()));
            List<CostDetail> settleList = costDetailMapper.selectPayOrSettleListBySettleTime(vo.getWaybillCode(),1,1, baseAgent.getDeptId(),chargeQuery);
            if (!CollectionUtils.isEmpty(settleList)){
                Map<String, List<CostDetail>> collect = settleList.stream().collect(Collectors.groupingBy(e->e.getFlightId() + "," + (e.getCreateTime() == null ? " " : format.format(e.getCreateTime()))));
                for (Map.Entry<String, List<CostDetail>> longListEntry : collect.entrySet()) {
                    String[] split = longListEntry.getKey().split(",");
                    ChargeSettleDetailVo chargeSettleDetailVo = new ChargeSettleDetailVo();
                    if ("1".equals(split[0])){
                        continue;
                    }else {
                        FlightInfo info = flightInfoMapper.selectById(split[0]);
                        if (info != null){
                            chargeSettleDetailVo.setSettleFlightNo(info.getAirWays() + info.getFlightNo());
                            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                            chargeSettleDetailVo.setSettleFlightDate(dateFormat.format(info.getExecDate()));
                        }
                    }
                    BigDecimal reduce = longListEntry.getValue().stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                    chargeSettleDetailVo.setSettleCost(reduce);

                    BigDecimal settleWeight = longListEntry.getValue().stream().map(CostDetail::getSettleDepWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    chargeSettleDetailVo.setSettleDepWeight(settleWeight);

                    BigDecimal quantity = longListEntry.getValue().stream().map(CostDetail::getQuantity).filter(Objects::nonNull).map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add);
                    chargeSettleDetailVo.setQuantity(quantity.toString());

                    int settleQuantity = longListEntry.getValue().stream().filter(e->e.getSettleDepQuantity() != null).mapToInt(CostDetail::getSettleDepQuantity).sum();
                    chargeSettleDetailVo.setSettleDepQuantity(settleQuantity);

                    try {
                        if (" ".equals(split[1])){
                            chargeSettleDetailVo.setSettleTime(null);
                        }else {
                            chargeSettleDetailVo.setSettleTime(format.parse(split[1]));
                        }
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                    detailVos.add(chargeSettleDetailVo);
                }
            }
            if (!CollectionUtils.isEmpty(detailVos)){
                List<ChargeSettleDetailVo> collect = detailVos.stream().filter(e->StringUtils.isNotEmpty(e.getSettleFlightNo())).sorted(Comparator.comparing(ChargeSettleDetailVo::getSettleTime)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(collect)){
                    int sum = collect.stream().mapToInt(ChargeSettleDetailVo::getSettleDepQuantity).sum();
                    vo.setQuantity(sum);
                    BigDecimal reduce1 = collect.stream().map(ChargeSettleDetailVo::getQuantity).map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add);
                    vo.setChargeWeight(reduce1);
                }
            }

            /** 已结算列表的已结算和未结算 */
//            List<CostDetail> settleLists = costDetailMapper.selectList(new QueryWrapper<CostDetail>()
//                    .eq("waybill_code", vo.getWaybillCode())
//                    .eq("is_del", 0)
//                    .eq("type", 1));
//            BigDecimal settleSum = settleLists.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
//            vo.setSettleCharge(settleSum);
//            vo.setNotSettleCharge(vo.getProcessingFee().subtract(settleSum));
        }


        ChargeBilExportVO lastTotal = new ChargeBilExportVO();
        lastTotal.setWaybillCode("合计");
        lastTotal.setQuantity(dataList.stream()
                .map(baseBalance -> Optional.ofNullable(baseBalance.getQuantity()).orElse(0))
                .reduce(0, Integer::sum));

        lastTotal.setChargeWeight(getSumFee(dataList, ChargeBilExportVO::getChargeWeight));

        BigDecimal processingFee = getSumFee(dataList, ChargeBilExportVO::getProcessingFee);
        BigDecimal refrigerationFee = getSumFee(dataList, ChargeBilExportVO::getRefrigerationFee);
        BigDecimal handlingFee = getSumFee(dataList, ChargeBilExportVO::getHandlingFee);
        BigDecimal cableCharge = getSumFee(dataList, ChargeBilExportVO::getCableCharge);
        BigDecimal diffServiceCharge = getSumFee(dataList, ChargeBilExportVO::getDiffServiceCharge);
//        BigDecimal settleCharge = getSumFee(dataList, ChargeBilExportVO::getSettleCharge);
//        BigDecimal notSettleCharge = getSumFee(dataList, ChargeBilExportVO::getNotSettleCharge);

        lastTotal.setId((long)idx);
        lastTotal.setProcessingFee(processingFee);
        lastTotal.setRefrigerationFee(refrigerationFee);
        lastTotal.setHandlingFee(handlingFee);
        lastTotal.setCableCharge(cableCharge);
        lastTotal.setDiffServiceCharge(diffServiceCharge);
//        lastTotal.setSettleCharge(settleCharge);
//        lastTotal.setNotSettleCharge(notSettleCharge);
        lastTotal.setSettleTime("---");
        dataList.add(lastTotal);

        //统计数据
        BilExportVO vo = new BilExportVO();
        vo.setChargeBilExportVOS(dataList);

        //-----------------------------------表头的数据------------------------------------------

        LambdaQueryWrapper<BaseBalance> wrapper = Wrappers.lambdaQuery(BaseBalance.class)
                .eq(BaseBalance::getAgentId, baseAgent.getId())
                .gt(BaseBalance::getCreateTime, startTime)
                .lt(BaseBalance::getCreateTime, endTime)
                .orderByDesc(BaseBalance::getCreateTime).orderByDesc(BaseBalance::getId);
        List<BaseBalance> list = baseBalanceMapper.selectList(wrapper);

        if(!CollectionUtils.isEmpty(list)){
            //充值金额 = 增加余额之和
            Map<String, BigDecimal> typeToSum = list.stream()
                    .collect(Collectors.toMap(
//                        BaseBalance::getType,
                            BaseBalance::getRemark,
                            val -> Optional.ofNullable(val.getTradeMoney()).orElse(new BigDecimal(0)),
                            BigDecimal::add));
            vo.setRechargeAmount(Optional.ofNullable(typeToSum.get("增加余额")).orElse(new BigDecimal(0)));
            //当前余额 = 时间段内最新的余额
            vo.setBalance(CollectionUtil.isEmpty(list) ? new BigDecimal(0) : list.get(0).getBalance());

            //上期余额 等于这个时间段的前一天余额明细的余额
            BaseBalance baseBalance = baseBalanceMapper.selectOne(new QueryWrapper<BaseBalance>()
                    .eq("agent_id", baseAgent.getId())
                    .lt("create_time", list.get(list.size()-1).getCreateTime())
                    .orderByDesc("create_time").orderByDesc("id")
                    .last("limit 1"));
            BigDecimal lastBalance = StringUtils.isNotNull(baseBalance) ? baseBalance.getBalance() : new BigDecimal(0);
            vo.setLastBalance(lastBalance);

            //配载时间段内运单
            List<String> waybillCodeFlightList = dataList.stream().map(ChargeBilExportVO::getWaybillCode).collect(Collectors.toList());

            //配载时间段内运单明细运单集合
            List<String> waybillCodeBanlanceList = list.stream().map(BaseBalance::getWaybillCode).collect(Collectors.toList());

            //非当期支付的本期配载的已结算金额
            BigDecimal settleNotCurrent = BigDecimal.ZERO;

            //非当期支付的本期配载的未结算金额 = 已支付 - 已结算
            BigDecimal unSettleNotCurrent = BigDecimal.ZERO;

            //得到非当期支付的运单
            waybillCodeFlightList.removeAll(waybillCodeBanlanceList);
            for(String waybillCode : waybillCodeFlightList){
                // ----------------- 计算已结算-----------------

                List<CostDetail> settleList = costDetailMapper.selectList(Wrappers.lambdaQuery(CostDetail.class)
                        .eq(CostDetail::getWaybillCode, waybillCode)
                        .eq(CostDetail::getDeptId, baseAgent.getDeptId())
                        .eq(CostDetail::getIsDel, 0)
                        .eq(CostDetail::getType, 1));

                BigDecimal costSum = settleList.stream()
                        .map(CostDetail::getTotalCharge)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                settleNotCurrent = settleNotCurrent.add(costSum);

                // ----------------- 计算未结算-----------------
                List<CostDetail> payList = costDetailMapper.selectList(new QueryWrapper<CostDetail>()
                        .eq("waybill_code", waybillCode)
                        .eq("dept_id", baseAgent.getDeptId())
                        .eq("is_del", 0)
                        .eq("type", 0)
                        .eq("is_settle", 1));
                BigDecimal paySum = payList.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal settleSum = settleList.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal subtract = paySum.subtract(settleSum);
                BigDecimal bigDecimal = subtract.compareTo(new BigDecimal(0)) < 0 ? new BigDecimal(0) : subtract;
                unSettleNotCurrent = unSettleNotCurrent.add(bigDecimal);
            }

            vo.setSettledNotCurrentAmount(settleNotCurrent);
            vo.setUnsettledNotCurrentAmount(unSettleNotCurrent);
        }else{
            vo.setRechargeAmount(BigDecimal.ZERO);
            vo.setBalance(BigDecimal.ZERO);
            vo.setLastBalance(BigDecimal.ZERO);
            vo.setSettledNotCurrentAmount(BigDecimal.ZERO);
            vo.setUnsettledNotCurrentAmount(BigDecimal.ZERO);
        }

        BigDecimal reduceCurrent = BigDecimal.ZERO;

        BigDecimal reduceCurrent2 = BigDecimal.ZERO;

        BigDecimal reduceNotCurrent = BigDecimal.ZERO;

        //计算退款费用
        List<CostDetail> refundList = costDetailMapper.selectList(new QueryWrapper<CostDetail>()
                .eq("dept_id", baseAgent.getDeptId())
                .eq("is_del", 0)
                .gt("create_time", startTime)
                .lt("create_time", endTime)
                .in("flight_id", 1,3,4));

        List<BaseBalance> payCancelCodes = list.stream()
                .filter(e -> e.getRemark().contains("退款") && e.getTradeMoney().compareTo(BigDecimal.ZERO) != 0)
                .collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(payCancelCodes)){
            reduceCurrent = payCancelCodes.stream().map(BaseBalance::getTradeMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        if(!CollectionUtils.isEmpty(refundList)){
            BigDecimal refundSum = refundList.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
            //当期退款
            for(CostDetail costDetail : refundList){
                if(costDetail.getCreateTime() != null){
                    LocalDateTime createTime = costDetail.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                    if(createTime.isAfter(startTime) && createTime.isBefore(endTime)){
                        reduceCurrent2 = reduceCurrent2.add(costDetail.getTotalCharge());
                    }
                }
            }
            //非当期退款
            reduceNotCurrent = refundSum.subtract(reduceCurrent2);
        }

        vo.setRefundCurrentAmount(reduceCurrent.add(reduceCurrent2));
        vo.setRefundNotCurrentAmount(reduceNotCurrent);


        //未来航班金额
        BigDecimal futureFlightCostSum = BigDecimal.ZERO;
        List<ChargeBilExportVO> futureList = costDetailMapper.selectFutureFlightItemVo(query, 0, 1);
        if(!CollectionUtils.isEmpty(futureList)){
            futureFlightCostSum = futureList.stream()
//                    .filter(e -> !ObjectUtil.equal("INVALID",e.getStatus()))
                    .map(ChargeBilExportVO::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        //重新支付的运单也加在未来航班里面 && 退款时间不在所选航班时间/余额明细内
        List<String> payCodes = list.stream()
                .filter(e -> e.getRemark().contains("运单支付") && e.getTradeMoney().compareTo(BigDecimal.ZERO) != 0)
                .map(BaseBalance::getWaybillCode).collect(Collectors.toList());

        List<String> collect1 = payCancelCodes.stream().map(BaseBalance::getWaybillCode).collect(Collectors.toList());

        for(String waybillCode : collect1){
            payCodes.remove(waybillCode);
        }

        for(String code : payCodes){
            List<CostDetail> payCancelList = costDetailMapper.selectList(new QueryWrapper<CostDetail>()
                    .eq("waybill_code",code)
                    .eq("type", 0)
                    .eq("is_settle", 2));
            if(payCancelList.size() > 0){
                BigDecimal refundSum = payCancelList.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                futureFlightCostSum = futureFlightCostSum.add(refundSum);
            }
        }

        if(!CollectionUtils.isEmpty(payCancelCodes)){
            reduceCurrent = payCancelCodes.stream().map(BaseBalance::getTradeMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
            futureFlightCostSum = futureFlightCostSum.add(reduceCurrent);
        }

        vo.setFutureFlightAmount(futureFlightCostSum);

        // 已结算费用
        BigDecimal settleCostSum = BigDecimal.ZERO;

        // 未结算费用
        BigDecimal notSettle = BigDecimal.ZERO;

        for (ChargeBilExportVO exportVO : dataList) {
            if ("INVALID".equals(exportVO.getStatus())) {
                continue;
            }
            String waybillCode = exportVO.getWaybillCode();
            // ----------------- 计算已结算-----------------

            List<CostDetail> settleList = costDetailMapper.selectList(Wrappers.lambdaQuery(CostDetail.class)
                    .eq(CostDetail::getWaybillCode, waybillCode)
                    .eq(CostDetail::getDeptId, baseAgent.getDeptId())
                    .eq(CostDetail::getIsDel, 0)
                    .eq(CostDetail::getType, 1));

            BigDecimal costSum = settleList.stream()
                    .filter(e -> e.getFlightId() != 1 && e.getFlightId() != 4 && e.getFlightId() != 3L)
                    .map(CostDetail::getTotalCharge)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            settleCostSum = settleCostSum.add(costSum);

            // ----------------- 计算未结算-----------------
            List<CostDetail> payList = costDetailMapper.selectList(new QueryWrapper<CostDetail>()
                    .eq("waybill_code", waybillCode)
                    .eq("dept_id", baseAgent.getDeptId())
                    .eq("is_del", 0)
                    .eq("type", 0)
                    .eq("is_settle", 1));
            BigDecimal paySum = payList.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal settleSum = settleList.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal subtract = paySum.subtract(settleSum);
            BigDecimal bigDecimal = subtract.compareTo(new BigDecimal(0)) < 0 ? new BigDecimal(0) : subtract;
            notSettle = notSettle.add(bigDecimal);
        }

        vo.setSettledAmount(settleCostSum);
        vo.setUnsettledAmount(notSettle);

        //进港费用计算
        BigDecimal payPickUpSum = list.stream()
                .filter(e -> ObjectUtil.equal("办单支付", e.getRemark()))
                .map(BaseBalance::getTradeMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal cancelPickUpSum = list.stream()
                .filter(e -> ObjectUtil.equal("办单作废", e.getRemark()) || ObjectUtil.equal("提货超时", e.getRemark()))
                .map(BaseBalance::getTradeMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        vo.setArrAmount(payPickUpSum.subtract(cancelPickUpSum));


        //-----------------------------------未结算的列表数据--------------------------------------
        //(1.8到结束时间制单支付)未结算金额大于0 和支付在这个时间的但是配载不在这个时间的数据 还有拉下的
        BigDecimal notSettleSum = BigDecimal.ZERO;

        LocalDateTime dateTime = LocalDateTime.of(2025, 1, 8, 0, 0, 0);
        query.setWriteStartTime(dateTime);
        query.setWriteEndTime(endTime);

        List<ChargeBilExportNotSettleVO> waybillListNotSettle = Optional.ofNullable(costDetailMapper.selectNotSettleData(query,0,1)).orElseGet(ArrayList::new);
        List<ChargeBilExportNotSettleVO> waybillCodeList = getWaybillCodeList(waybillListNotSettle);
        for(ChargeBilExportNotSettleVO voNotSettle : waybillCodeList){
            BigDecimal[] subs = notSettleResult(voNotSettle.getWaybillCode());
            if(subs[0].compareTo(BigDecimal.ZERO) > 0){
                dataListNotSettle.add(voNotSettle);
                notSettleSum = notSettleSum.add(subs[0]);
            }
        }
        List<ChargeBilExportNotSettleVO> waybillListFlightNotSettle = Optional.ofNullable(costDetailMapper.selectFlightNotSettleData(query,0,1)).orElseGet(ArrayList::new);
        List<ChargeBilExportNotSettleVO> waybillCodeList1 = getWaybillCodeList(waybillListFlightNotSettle);
        for(ChargeBilExportNotSettleVO voNotSettle : waybillCodeList1){
            BigDecimal[] subs = notSettleResult(voNotSettle.getWaybillCode());
            if(subs[0].compareTo(BigDecimal.ZERO) == 0){
                dataListNotSettle.add(voNotSettle);
                notSettleSum = notSettleSum.add(subs[1]);
            }
        }

        //结算后全部拉下 没有退款也没有重新结算的运单
        List<HzDepPullDown> pullWaybillList = Optional.ofNullable(costDetailMapper.selectPullAllNotSettle(query)).orElseGet(ArrayList::new);
        List<String> waybillNotSettlePullList = new ArrayList<>();
        Map<String, List<HzDepPullDown>> pullWaybillMap = pullWaybillList.stream()
                .collect(Collectors.groupingBy(HzDepPullDown::getWaybillCode));
        for (Map.Entry<String, List<HzDepPullDown>> longListEntry : pullWaybillMap.entrySet()) {
            //已结算的运单
            String waybillCode = longListEntry.getKey();
//            List<CostDetail> detailPayList = costDetailMapper.selectList(new QueryWrapper<CostDetail>()
//                    .eq("waybill_code", waybillCode)
//                    .eq("is_del", 0)
//                    .eq("type", 0)
//                    .eq("is_settle", 1));
            List<CostDetail> detailSettleList = costDetailMapper.selectList(new QueryWrapper<CostDetail>()
                    .eq("waybill_code", waybillCode)
                    .eq("is_del", 0)
                    .eq("type", 1));
//            BigDecimal paySum = detailPayList.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
//            BigDecimal settleSum = detailSettleList.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
//            if(paySum.compareTo(settleSum) != 0){
//                continue;
//            }
            if(detailSettleList.size() == 0){
                continue;
            }

            //整单拉下
//            List<HzDepPullDown> pullWaybillList1 = longListEntry.getValue();
//            Integer quantityCount = pullWaybillList1.stream().map(HzDepPullDown::getQuantity).reduce(0, Integer::sum);
//            BigDecimal weightCount = pullWaybillList1.stream().map(HzDepPullDown::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
//            if(Objects.equals(quantityCount, pullWaybillList1.get(0).getWaybillQuantity())
//            && Objects.equals(weightCount, pullWaybillList1.get(0).getWaybillWeight())){
//                waybillNotSettlePullList.add(waybillCode);
//            }
            waybillNotSettlePullList.add(waybillCode);
        }
        if(waybillNotSettlePullList.size() > 0){
            List<ChargeBilExportNotSettleVO> chargeBilExportNotSettleVOS = costDetailMapper.selectPullAllNotSettleData(waybillNotSettlePullList);
            List<ChargeBilExportNotSettleVO> waybillCodeList2 = getWaybillCodeList(chargeBilExportNotSettleVOS);
            for(ChargeBilExportNotSettleVO voNotSettle : waybillCodeList2){
                BigDecimal[] subs = notSettleResult(voNotSettle.getWaybillCode());
                if(subs[0].compareTo(BigDecimal.ZERO) == 0){
                    dataListNotSettle.add(voNotSettle);
                    notSettleSum = notSettleSum.add(subs[1]);
                }
            }

        }



        vo.setNotSettleAmount(notSettleSum);

        int index = 0;
        for(ChargeBilExportNotSettleVO voNotSettle : dataListNotSettle){
            voNotSettle.setId((long)++index);
            List<ChargeSettleDetailVo> detailVos = new ArrayList<>();
            ChargeQuery chargeQuery = new ChargeQuery();
            chargeQuery.setFlightLoadStartTime(Date.from(query.getFlightStartTime().atZone(ZoneId.systemDefault()).toInstant()));
            chargeQuery.setFlightLoadEndTime(Date.from(query.getFlightEndTime().atZone(ZoneId.systemDefault()).toInstant()));
            List<CostDetail> settleList = costDetailMapper.selectPayOrSettleListBySettleTime(voNotSettle.getWaybillCode(),1,1, baseAgent.getDeptId(),chargeQuery);
            if (!CollectionUtils.isEmpty(settleList)){
                Map<String, List<CostDetail>> collect = settleList.stream().collect(Collectors.groupingBy(e->e.getFlightId() + "," + (e.getCreateTime() == null ? " " : format.format(e.getCreateTime()))));
                for (Map.Entry<String, List<CostDetail>> longListEntry : collect.entrySet()) {
                    String[] split = longListEntry.getKey().split(",");
                    ChargeSettleDetailVo chargeSettleDetailVo = new ChargeSettleDetailVo();
                    if ("1".equals(split[0])){
                        continue;
                    }else {
                        FlightInfo info = flightInfoMapper.selectById(split[0]);
                        if (info != null){
                            chargeSettleDetailVo.setSettleFlightNo(info.getAirWays() + info.getFlightNo());
                            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                            chargeSettleDetailVo.setSettleFlightDate(dateFormat.format(info.getExecDate()));
                        }
                    }
                    BigDecimal reduce = longListEntry.getValue().stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                    chargeSettleDetailVo.setSettleCost(reduce);

                    BigDecimal settleWeight = longListEntry.getValue().stream().map(CostDetail::getSettleDepWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    chargeSettleDetailVo.setSettleDepWeight(settleWeight);

                    BigDecimal quantity = longListEntry.getValue().stream().map(CostDetail::getQuantity).filter(Objects::nonNull).map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add);
                    chargeSettleDetailVo.setQuantity(quantity.toString());

                    int settleQuantity = longListEntry.getValue().stream().filter(e->e.getSettleDepQuantity() != null).mapToInt(CostDetail::getSettleDepQuantity).sum();
                    chargeSettleDetailVo.setSettleDepQuantity(settleQuantity);

                    try {
                        if (" ".equals(split[1])){
                            chargeSettleDetailVo.setSettleTime(null);
                        }else {
                            chargeSettleDetailVo.setSettleTime(format.parse(split[1]));
                        }
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                    detailVos.add(chargeSettleDetailVo);
                }
            }
            if (!CollectionUtils.isEmpty(detailVos)){
                List<ChargeSettleDetailVo> collect = detailVos.stream().filter(e->StringUtils.isNotEmpty(e.getSettleFlightNo())).sorted(Comparator.comparing(ChargeSettleDetailVo::getSettleTime)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(collect)){
                    int sum = collect.stream().mapToInt(ChargeSettleDetailVo::getSettleDepQuantity).sum();
                    voNotSettle.setQuantity(sum);
                    BigDecimal reduce1 = collect.stream().map(ChargeSettleDetailVo::getQuantity).map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add);
                    voNotSettle.setChargeWeight(reduce1);
                }
            }

            /** 未结算列表的已结算和未结算 */
            List<CostDetail> settleLists = costDetailMapper.selectList(new QueryWrapper<CostDetail>()
                    .eq("waybill_code", voNotSettle.getWaybillCode())
                    .eq("is_del", 0)
                    .eq("type", 1));
            BigDecimal settleSum = settleLists.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal notSettleSettleSum = BigDecimal.ZERO;
            notSettleSettleSum = notSettleSettleSum.add(voNotSettle.getProcessingFee()).add(voNotSettle.getRefrigerationFee())
                    .add(voNotSettle.getHandlingFee()).add(voNotSettle.getForkliftCharge()).add(voNotSettle.getCableCharge())
                    .add(voNotSettle.getDiffServiceCharge()).subtract(settleSum);
            if(notSettleSettleSum.compareTo(BigDecimal.ZERO) == 0){
                voNotSettle.setSettleCharge(BigDecimal.ZERO);
                voNotSettle.setNotSettleCharge(settleSum);
                voNotSettle.setSettleTime(null);
            }else{
                voNotSettle.setSettleCharge(settleSum);
                voNotSettle.setNotSettleCharge(notSettleSettleSum);
            }
        }

        ChargeBilExportNotSettleVO lastTotalNotSettle = new ChargeBilExportNotSettleVO();
        lastTotalNotSettle.setWaybillCode("合计");
        lastTotalNotSettle.setQuantity(dataListNotSettle.stream()
                .map(baseBalance -> Optional.ofNullable(baseBalance.getQuantity()).orElse(0))
                .reduce(0, Integer::sum));

        lastTotalNotSettle.setChargeWeight(getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getChargeWeight));

        BigDecimal processingFeeNotSettle = getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getProcessingFee);
        BigDecimal refrigerationFeeNotSettle = getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getRefrigerationFee);
        BigDecimal handlingFeeNotSettle = getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getHandlingFee);
        BigDecimal cableChargeNotSettle = getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getCableCharge);
        BigDecimal diffServiceChargeNotSettle = getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getDiffServiceCharge);
        BigDecimal settleChargeNotSettle = getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getSettleCharge);
        BigDecimal notSettleChargeNotSettle = getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getNotSettleCharge);

        lastTotalNotSettle.setId((long) index);
        lastTotalNotSettle.setProcessingFee(processingFeeNotSettle);
        lastTotalNotSettle.setRefrigerationFee(refrigerationFeeNotSettle);
        lastTotalNotSettle.setHandlingFee(handlingFeeNotSettle);
        lastTotalNotSettle.setCableCharge(cableChargeNotSettle);
        lastTotalNotSettle.setDiffServiceCharge(diffServiceChargeNotSettle);
        lastTotalNotSettle.setSettleCharge(settleChargeNotSettle);
        lastTotalNotSettle.setNotSettleCharge(notSettleChargeNotSettle);
        lastTotalNotSettle.setSettleTime("---");
        dataListNotSettle.add(lastTotalNotSettle);

        vo.setChargeBilExportVOSNotSettle(dataListNotSettle);
//        vo.setChargeBilExportVOSNotSettle(new ArrayList<>());



        //-----------------------------------------标题--------------------------------------
        if (startTime == null || endTime == null) {
            vo.setSettlementCycle("---");
            vo.setTitle(query.getAgent() + "费用清单");
        } else {
            vo.setTitle(query.getAgent() + startTime.getMonthValue() + "-" + endTime.getMonthValue() + "月费用清单");
            vo.setSettlementCycle(formatDateTime(startTime) + "-" + formatDateTime(endTime));
        }
//        vo.setRemarks("已结算金额为已支付已承运的运单\n未结算金额为已支付未承运的运单");
        vo.setRemarks("");
        return vo;

    }

    @Override
    public BillExportVoNew selectBillExportDataNew(BillExportQuery query) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startTime = query.getFlightStartTime();
        LocalDateTime endTime = query.getFlightEndTime();

        BaseAgent baseAgent = baseAgentMapper.selectOne(Wrappers.lambdaQuery(BaseAgent.class)
                .eq(BaseAgent::getAgent, query.getAgent()));
        if (baseAgent == null) {
            throw new CustomException("代理人不存在");
        }

        //---------------------------------------已结算的列表-----------------------------------------------

        List<ChargeBilExportVO> waybillList = Optional.ofNullable(costDetailMapper.selectExportItemVo(query,1,null)).orElseGet(ArrayList::new);
        Map<String, List<ChargeBilExportVO>> waybillCodeToVO = waybillList.stream()
                .collect(Collectors.groupingBy(ChargeBilExportVO::getWaybillCode));
        List<ChargeBilExportVO> dataList = new ArrayList<>();
        List<ChargeBilExportNotSettleVO> dataListNotSettle = new ArrayList<>();
        int idx = 1;
        for (List<ChargeBilExportVO> value : waybillCodeToVO.values()) {
            ChargeBilExportVO chargeBilExportVO = value.get(0);
            chargeBilExportVO.setId((long) idx++);
            //根据费用名称设置对应的值
            value.forEach(val -> setFee(chargeBilExportVO, val.getChargeAbb(), val.getTotalCharge()));
            dataList.add(chargeBilExportVO);
        }
        for(ChargeBilExportVO vo : dataList){
            List<ChargeSettleDetailVo> detailVos = new ArrayList<>();
            ChargeQuery chargeQuery = new ChargeQuery();
            chargeQuery.setFlightLoadStartTime(Date.from(query.getFlightStartTime().atZone(ZoneId.systemDefault()).toInstant()));
            chargeQuery.setFlightLoadEndTime( Date.from(query.getFlightEndTime().atZone(ZoneId.systemDefault()).toInstant()));
            List<CostDetail> settleList = costDetailMapper.selectPayOrSettleListBySettleTime(vo.getWaybillCode(),1,1, baseAgent.getDeptId(),chargeQuery);
            if (!CollectionUtils.isEmpty(settleList)){
                Map<String, List<CostDetail>> collect = settleList.stream().collect(Collectors.groupingBy(e->e.getFlightId() + "," + (e.getCreateTime() == null ? " " : format.format(e.getCreateTime()))));
                for (Map.Entry<String, List<CostDetail>> longListEntry : collect.entrySet()) {
                    String[] split = longListEntry.getKey().split(",");
                    ChargeSettleDetailVo chargeSettleDetailVo = new ChargeSettleDetailVo();
                    if ("1".equals(split[0])){
                        continue;
                    }else {
                        FlightInfo info = flightInfoMapper.selectById(split[0]);
                        if (info != null){
                            chargeSettleDetailVo.setSettleFlightNo(info.getAirWays() + info.getFlightNo());
                            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                            chargeSettleDetailVo.setSettleFlightDate(dateFormat.format(info.getExecDate()));
                        }
                    }
                    BigDecimal reduce = longListEntry.getValue().stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                    chargeSettleDetailVo.setSettleCost(reduce);

                    BigDecimal settleWeight = longListEntry.getValue().stream().map(CostDetail::getSettleDepWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    chargeSettleDetailVo.setSettleDepWeight(settleWeight);

                    BigDecimal quantity = longListEntry.getValue().stream().map(CostDetail::getQuantity).filter(Objects::nonNull).map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add);
                    chargeSettleDetailVo.setQuantity(quantity.toString());

                    int settleQuantity = longListEntry.getValue().stream().filter(e->e.getSettleDepQuantity() != null).mapToInt(CostDetail::getSettleDepQuantity).sum();
                    chargeSettleDetailVo.setSettleDepQuantity(settleQuantity);

                    try {
                        if (" ".equals(split[1])){
                            chargeSettleDetailVo.setSettleTime(null);
                        }else {
                            chargeSettleDetailVo.setSettleTime(format.parse(split[1]));
                        }
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                    detailVos.add(chargeSettleDetailVo);
                }
            }
            if (!CollectionUtils.isEmpty(detailVos)){
                List<ChargeSettleDetailVo> collect = detailVos.stream().filter(e->StringUtils.isNotEmpty(e.getSettleFlightNo())).sorted(Comparator.comparing(ChargeSettleDetailVo::getSettleTime)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(collect)){
                    int sum = collect.stream().mapToInt(ChargeSettleDetailVo::getSettleDepQuantity).sum();
                    vo.setQuantity(sum);
                    BigDecimal reduce1 = collect.stream().map(ChargeSettleDetailVo::getQuantity).map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add);
                    vo.setChargeWeight(reduce1);
                }
            }

            /** 已结算列表的已结算和未结算 */
//            List<CostDetail> settleLists = costDetailMapper.selectList(new QueryWrapper<CostDetail>()
//                    .eq("waybill_code", vo.getWaybillCode())
//                    .eq("is_del", 0)
//                    .eq("type", 1));
//            BigDecimal settleSum = settleLists.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
//            vo.setSettleCharge(settleSum);
//            vo.setNotSettleCharge(vo.getProcessingFee().subtract(settleSum));
        }
        ChargeBilExportVO lastTotal = new ChargeBilExportVO();
        lastTotal.setWaybillCode("合计");
        lastTotal.setQuantity(dataList.stream()
                .map(baseBalance -> Optional.ofNullable(baseBalance.getQuantity()).orElse(0))
                .reduce(0, Integer::sum));
        lastTotal.setChargeWeight(getSumFee(dataList, ChargeBilExportVO::getChargeWeight));

        BigDecimal processingFee = getSumFee(dataList, ChargeBilExportVO::getProcessingFee);
        BigDecimal refrigerationFee = getSumFee(dataList, ChargeBilExportVO::getRefrigerationFee);
        BigDecimal handlingFee = getSumFee(dataList, ChargeBilExportVO::getHandlingFee);
        BigDecimal cableCharge = getSumFee(dataList, ChargeBilExportVO::getCableCharge);
        BigDecimal diffServiceCharge = getSumFee(dataList, ChargeBilExportVO::getDiffServiceCharge);

        lastTotal.setId((long)idx);
        lastTotal.setProcessingFee(processingFee);
        lastTotal.setRefrigerationFee(refrigerationFee);
        lastTotal.setHandlingFee(handlingFee);
        lastTotal.setCableCharge(cableCharge);
        lastTotal.setDiffServiceCharge(diffServiceCharge);
        lastTotal.setSettleTime("---");
        dataList.add(lastTotal);

        //统计数据
        BillExportVoNew vo = new BillExportVoNew();
        vo.setChargeBilExportVOS(dataList);

        //-----------------------------------未结算的列表数据--------------------------------------

        //月结代理人 总未结算固定为空 不导出出港未结算明细表
        if(baseAgent.getSettleMethod() == 2 && baseAgent.getPayMethod() == 0){
            vo.setChargeBilExportVOSNotSettle(new ArrayList<>());
        }else {
            //(1.8到结束时间制单支付)未结算金额大于0 和支付在这个时间的但是配载不在这个时间的数据 还有拉下的
            BigDecimal notSettleSum = BigDecimal.ZERO;

            LocalDateTime dateTime = LocalDateTime.of(2025, 1, 8, 0, 0, 0);
            query.setWriteStartTime(dateTime);
            query.setWriteEndTime(endTime);

            List<ChargeBilExportNotSettleVO> waybillListNotSettle = Optional.ofNullable(costDetailMapper.selectNotSettleData(query, 0, 1)).orElseGet(ArrayList::new);
            List<ChargeBilExportNotSettleVO> waybillCodeList = getWaybillCodeList(waybillListNotSettle);
            for (ChargeBilExportNotSettleVO voNotSettle : waybillCodeList) {
                BigDecimal[] subs = notSettleResult(voNotSettle.getWaybillCode());
                if (subs[0].compareTo(BigDecimal.ZERO) > 0) {
                    dataListNotSettle.add(voNotSettle);
                    notSettleSum = notSettleSum.add(subs[0]);
                }
            }
            List<ChargeBilExportNotSettleVO> waybillListFlightNotSettle = Optional.ofNullable(costDetailMapper.selectFlightNotSettleData(query, 0, 1)).orElseGet(ArrayList::new);
            List<ChargeBilExportNotSettleVO> waybillCodeList1 = getWaybillCodeList(waybillListFlightNotSettle);
            for (ChargeBilExportNotSettleVO voNotSettle : waybillCodeList1) {
                BigDecimal[] subs = notSettleResult(voNotSettle.getWaybillCode());
                if (subs[0].compareTo(BigDecimal.ZERO) == 0) {
                    dataListNotSettle.add(voNotSettle);
                    notSettleSum = notSettleSum.add(subs[1]);
                }
            }

            //结算后全部拉下 没有退款也没有重新结算的运单
            List<HzDepPullDown> pullWaybillList = Optional.ofNullable(costDetailMapper.selectPullAllNotSettle(query)).orElseGet(ArrayList::new);
            List<String> waybillNotSettlePullList = new ArrayList<>();
            Map<String, List<HzDepPullDown>> pullWaybillMap = pullWaybillList.stream()
                    .collect(Collectors.groupingBy(HzDepPullDown::getWaybillCode));
            for (Map.Entry<String, List<HzDepPullDown>> longListEntry : pullWaybillMap.entrySet()) {
                //已结算的运单
                String waybillCode = longListEntry.getKey();
                List<CostDetail> detailSettleList = costDetailMapper.selectList(new QueryWrapper<CostDetail>()
                        .eq("waybill_code", waybillCode)
                        .eq("is_del", 0)
                        .eq("type", 1));
                if (detailSettleList.size() == 0) {
                    continue;
                }
                waybillNotSettlePullList.add(waybillCode);
            }
            if (waybillNotSettlePullList.size() > 0) {
                List<ChargeBilExportNotSettleVO> chargeBilExportNotSettleVOS = costDetailMapper.selectPullAllNotSettleData(waybillNotSettlePullList);
                List<ChargeBilExportNotSettleVO> waybillCodeList2 = getWaybillCodeList(chargeBilExportNotSettleVOS);
                for (ChargeBilExportNotSettleVO voNotSettle : waybillCodeList2) {
                    BigDecimal[] subs = notSettleResult(voNotSettle.getWaybillCode());
                    if (subs[0].compareTo(BigDecimal.ZERO) == 0) {
                        dataListNotSettle.add(voNotSettle);
                        notSettleSum = notSettleSum.add(subs[1]);
                    }
                }

            }

            vo.setNotSettleAmount(notSettleSum);

            int index = 0;
            for (ChargeBilExportNotSettleVO voNotSettle : dataListNotSettle) {
                voNotSettle.setId((long) ++index);
                List<ChargeSettleDetailVo> detailVos = new ArrayList<>();
                ChargeQuery chargeQuery = new ChargeQuery();
                chargeQuery.setFlightLoadStartTime(Date.from(query.getFlightStartTime().atZone(ZoneId.systemDefault()).toInstant()));
                chargeQuery.setFlightLoadEndTime(Date.from(query.getFlightEndTime().atZone(ZoneId.systemDefault()).toInstant()));
                List<CostDetail> settleList = costDetailMapper.selectPayOrSettleListBySettleTime(voNotSettle.getWaybillCode(), 1, 1, baseAgent.getDeptId(), chargeQuery);
                if (!CollectionUtils.isEmpty(settleList)) {
                    Map<String, List<CostDetail>> collect = settleList.stream().collect(Collectors.groupingBy(e -> e.getFlightId() + "," + (e.getCreateTime() == null ? " " : format.format(e.getCreateTime()))));
                    for (Map.Entry<String, List<CostDetail>> longListEntry : collect.entrySet()) {
                        String[] split = longListEntry.getKey().split(",");
                        ChargeSettleDetailVo chargeSettleDetailVo = new ChargeSettleDetailVo();
                        if ("1".equals(split[0])) {
                            continue;
                        } else {
                            FlightInfo info = flightInfoMapper.selectById(split[0]);
                            if (info != null) {
                                chargeSettleDetailVo.setSettleFlightNo(info.getAirWays() + info.getFlightNo());
                                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                                chargeSettleDetailVo.setSettleFlightDate(dateFormat.format(info.getExecDate()));
                            }
                        }
                        BigDecimal reduce = longListEntry.getValue().stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                        chargeSettleDetailVo.setSettleCost(reduce);

                        BigDecimal settleWeight = longListEntry.getValue().stream().map(CostDetail::getSettleDepWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                        chargeSettleDetailVo.setSettleDepWeight(settleWeight);

                        BigDecimal quantity = longListEntry.getValue().stream().map(CostDetail::getQuantity).filter(Objects::nonNull).map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add);
                        chargeSettleDetailVo.setQuantity(quantity.toString());

                        int settleQuantity = longListEntry.getValue().stream().filter(e -> e.getSettleDepQuantity() != null).mapToInt(CostDetail::getSettleDepQuantity).sum();
                        chargeSettleDetailVo.setSettleDepQuantity(settleQuantity);

                        try {
                            if (" ".equals(split[1])) {
                                chargeSettleDetailVo.setSettleTime(null);
                            } else {
                                chargeSettleDetailVo.setSettleTime(format.parse(split[1]));
                            }
                        } catch (ParseException e) {
                            throw new RuntimeException(e);
                        }
                        detailVos.add(chargeSettleDetailVo);
                    }
                }
                if (!CollectionUtils.isEmpty(detailVos)) {
                    List<ChargeSettleDetailVo> collect = detailVos.stream().filter(e -> StringUtils.isNotEmpty(e.getSettleFlightNo())).sorted(Comparator.comparing(ChargeSettleDetailVo::getSettleTime)).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(collect)) {
                        int sum = collect.stream().mapToInt(ChargeSettleDetailVo::getSettleDepQuantity).sum();
                        voNotSettle.setQuantity(sum);
                        BigDecimal reduce1 = collect.stream().map(ChargeSettleDetailVo::getQuantity).map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add);
                        voNotSettle.setChargeWeight(reduce1);
                    }
                }

                /** 未结算列表的已结算和未结算 */
                List<CostDetail> settleLists = costDetailMapper.selectList(new QueryWrapper<CostDetail>()
                        .eq("waybill_code", voNotSettle.getWaybillCode())
                        .eq("is_del", 0)
                        .eq("type", 1));
                BigDecimal settleSum = settleLists.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal notSettleSettleSum = BigDecimal.ZERO;
                notSettleSettleSum = notSettleSettleSum.add(voNotSettle.getProcessingFee()).add(voNotSettle.getRefrigerationFee())
                        .add(voNotSettle.getHandlingFee()).add(voNotSettle.getForkliftCharge()).add(voNotSettle.getCableCharge())
                        .add(voNotSettle.getDiffServiceCharge()).subtract(settleSum);
                if (notSettleSettleSum.compareTo(BigDecimal.ZERO) == 0) {
                    voNotSettle.setSettleCharge(BigDecimal.ZERO);
                    voNotSettle.setNotSettleCharge(settleSum);
                    voNotSettle.setSettleTime(null);
                } else {
                    voNotSettle.setSettleCharge(settleSum);
                    voNotSettle.setNotSettleCharge(notSettleSettleSum);
                }
            }

            ChargeBilExportNotSettleVO lastTotalNotSettle = new ChargeBilExportNotSettleVO();
            lastTotalNotSettle.setWaybillCode("合计");
            lastTotalNotSettle.setQuantity(dataListNotSettle.stream()
                    .map(baseBalance -> Optional.ofNullable(baseBalance.getQuantity()).orElse(0))
                    .reduce(0, Integer::sum));

            lastTotalNotSettle.setChargeWeight(getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getChargeWeight));

            BigDecimal processingFeeNotSettle = getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getProcessingFee);
            BigDecimal refrigerationFeeNotSettle = getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getRefrigerationFee);
            BigDecimal handlingFeeNotSettle = getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getHandlingFee);
            BigDecimal cableChargeNotSettle = getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getCableCharge);
            BigDecimal diffServiceChargeNotSettle = getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getDiffServiceCharge);
            BigDecimal settleChargeNotSettle = getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getSettleCharge);
            BigDecimal notSettleChargeNotSettle = getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getNotSettleCharge);

            lastTotalNotSettle.setId((long) index);
            lastTotalNotSettle.setProcessingFee(processingFeeNotSettle);
            lastTotalNotSettle.setRefrigerationFee(refrigerationFeeNotSettle);
            lastTotalNotSettle.setHandlingFee(handlingFeeNotSettle);
            lastTotalNotSettle.setCableCharge(cableChargeNotSettle);
            lastTotalNotSettle.setDiffServiceCharge(diffServiceChargeNotSettle);
            lastTotalNotSettle.setSettleCharge(settleChargeNotSettle);
            lastTotalNotSettle.setNotSettleCharge(notSettleChargeNotSettle);
            lastTotalNotSettle.setSettleTime("---");
            dataListNotSettle.add(lastTotalNotSettle);

            vo.setChargeBilExportVOSNotSettle(dataListNotSettle);
        }

        //-----------------------------------表头的数据------------------------------------------
        LambdaQueryWrapper<BaseBalance> wrapper = Wrappers.lambdaQuery(BaseBalance.class)
                .eq(BaseBalance::getAgentId, baseAgent.getId())
                .gt(BaseBalance::getCreateTime, startTime)
                .lt(BaseBalance::getCreateTime, endTime)
                .orderByDesc(BaseBalance::getCreateTime);
        List<BaseBalance> list = baseBalanceMapper.selectList(wrapper);

        if(!CollectionUtils.isEmpty(list)){
            //充值金额 = 增加余额之和
            Map<String, BigDecimal> typeToSum = list.stream()
                    .collect(Collectors.toMap(
//                        BaseBalance::getType,
                            BaseBalance::getRemark,
                            val -> Optional.ofNullable(val.getTradeMoney()).orElse(new BigDecimal(0)),
                            BigDecimal::add));
            vo.setRechargeAmount(Optional.ofNullable(typeToSum.get("增加余额")).orElse(new BigDecimal(0)));
            //当前余额 = 时间段内最新的余额
            vo.setBalance(CollectionUtil.isEmpty(list) ? new BigDecimal(0) : list.get(0).getBalance());

            //上期余额 等于这个时间段的前一天余额明细的余额
            BaseBalance baseBalance = baseBalanceMapper.selectOne(new QueryWrapper<BaseBalance>()
                    .eq("agent_id", baseAgent.getId())
                    .lt("create_time", list.get(list.size()-1).getCreateTime())
                    .orderByDesc("create_time")
                    .last("limit 1"));
            BigDecimal lastBalance = StringUtils.isNotNull(baseBalance) ? baseBalance.getBalance() : new BigDecimal(0);
            vo.setLastBalance(lastBalance);
            vo.setFinanceBalance(vo.getBalance().add(vo.getNotSettleAmount() != null ? vo.getNotSettleAmount() : BigDecimal.ZERO));

            BigDecimal settleCostSum = BigDecimal.ZERO;

            for (ChargeBilExportVO exportVO : dataList) {
                if ("INVALID".equals(exportVO.getStatus())) {
                    continue;
                }
                String waybillCode = exportVO.getWaybillCode();
                // ----------------- 计算已结算-----------------

                List<CostDetail> settleList = costDetailMapper.selectList(Wrappers.lambdaQuery(CostDetail.class)
                        .eq(CostDetail::getWaybillCode, waybillCode)
                        .eq(CostDetail::getDeptId, baseAgent.getDeptId())
                        .eq(CostDetail::getIsDel, 0)
                        .eq(CostDetail::getType, 1));

                BigDecimal costSum = settleList.stream()
                        .filter(e -> e.getFlightId() != 1 && e.getFlightId() != 4 && e.getFlightId() != 3L)
                        .map(CostDetail::getTotalCharge)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                settleCostSum = settleCostSum.add(costSum);
            }

            vo.setSettledAmount(settleCostSum);

        }else{
            vo.setRechargeAmount(BigDecimal.ZERO);
            vo.setBalance(BigDecimal.ZERO);
            vo.setLastBalance(BigDecimal.ZERO);
            vo.setFinanceBalance(BigDecimal.ZERO);
            vo.setSettledAmount(BigDecimal.ZERO);
        }




          if (startTime == null || endTime == null) {
            vo.setSettlementCycle("---");
        } else {
            vo.setSettlementCycle(formatDateTime(startTime) + "-" + formatDateTime(endTime));
        }
        return vo;
    }

    @Override
    public BillExportVoNewAgent selectBillExportDataNewAgent(BillExportQuery query) {
        BillExportVoNewAgent vo = new BillExportVoNewAgent();
        //---------------------------------------出港已结算的列表-----------------------------------------------

        List<FlightLoadWaybillVO> waybillVoList = flightInfoMapper.selectFlightWaybills(query);
        if (CollectionUtils.isEmpty(waybillVoList)){
            return new BillExportVoNewAgent();
        }
        List<ChargeBilExportVO> waybillList = Optional.ofNullable(costDetailMapper.selectByFlightWaybills(waybillVoList,null)).orElseGet(ArrayList::new);
        List<BaseAgent> baseAgents = Optional.ofNullable(baseAgentMapper.selectListByAgentCode(query.getAgentCode())).orElseGet(ArrayList::new);
        List<Long> agentIds = new ArrayList<>();
        List<Long> deptIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(baseAgents)){
            List<Long> deptIdSet = baseAgents.stream().map(BaseAgent::getDeptId).collect(Collectors.toList());
            waybillList = waybillList.stream()
                    .filter(e -> deptIdSet.contains(e.getDeptId()))
                    .collect(Collectors.toList());
            agentIds = baseAgents.stream().filter(e -> e.getSettleMethod() == 1).map(BaseAgent::getId).collect(Collectors.toList());
            deptIds = baseAgents.stream().filter(e -> e.getSettleMethod() != 1).map(BaseAgent::getDeptId).collect(Collectors.toList());
        }
        //将运单有多种费用进行分组，然后设置对应的费用值
        Map<String, List<ChargeBilExportVO>> waybillCodeToVO = waybillList.stream()
                .collect(Collectors.groupingBy(ChargeBilExportVO::getWaybillCode));
        List<ChargeBillExportVoAgent> dataList = new ArrayList<>();
        //序号
        int idx = 1;
        for (List<ChargeBilExportVO> value : waybillCodeToVO.values()) {
            ChargeBillExportVoAgent voAgent = new ChargeBillExportVoAgent();
            ChargeBilExportVO chargeBilExportVO = value.get(0);
            chargeBilExportVO.setId((long) idx++);
            //根据费用名称设置对应的值
            value.forEach(val -> setFee(chargeBilExportVO, val.getChargeAbb(), val.getTotalCharge()));
            chargeBilExportVO.setSettleCharge(chargeBilExportVO.getTotalCharge());
            BeanUtils.copyProperties(chargeBilExportVO,voAgent);
            voAgent.setId(idx++);
            dataList.add(voAgent);
            for(ChargeBilExportVO exportVo: value){
                if(!(ObjectUtil.equal(exportVo.getFlightId(),chargeBilExportVO.getFlightId())
                        && ObjectUtil.equal(exportVo.getTotalCharge(),chargeBilExportVO.getTotalCharge()))){
                    ChargeBillExportVoAgent agent = new ChargeBillExportVoAgent();
                    exportVo.setId((long) idx++);
                    value.forEach(val -> setFee(exportVo, val.getChargeAbb(), val.getTotalCharge()));
                    exportVo.setSettleCharge(exportVo.getTotalCharge());
                    BeanUtils.copyProperties(exportVo,agent);
                    agent.setId(idx++);
                    dataList.add(agent);
                }
            }

        }
        ChargeBillExportVoAgent lastTotal = new ChargeBillExportVoAgent();
        lastTotal.setWaybillCode("合计");
        lastTotal.setQuantity(dataList.stream()
                .map(baseBalance -> Optional.ofNullable(baseBalance.getQuantity()).orElse(0))
                .reduce(0, Integer::sum));

        lastTotal.setChargeWeight(getSumFee(dataList, ChargeBillExportVoAgent::getChargeWeight));

        BigDecimal processingFee = getSumFee(dataList, ChargeBillExportVoAgent::getProcessingFee);
        BigDecimal refrigerationFee = getSumFee(dataList, ChargeBillExportVoAgent::getRefrigerationFee);
        BigDecimal handlingFee = getSumFee(dataList, ChargeBillExportVoAgent::getHandlingFee);
        BigDecimal cableCharge = getSumFee(dataList, ChargeBillExportVoAgent::getCableCharge);
        BigDecimal diffServiceCharge = getSumFee(dataList, ChargeBillExportVoAgent::getDiffServiceCharge);
        BigDecimal settleCharge = getSumFee(dataList, ChargeBillExportVoAgent::getSettleCharge);

        lastTotal.setId(idx);
        lastTotal.setProcessingFee(processingFee);
        lastTotal.setRefrigerationFee(refrigerationFee);
        lastTotal.setHandlingFee(handlingFee);
        lastTotal.setCableCharge(cableCharge);
        lastTotal.setDiffServiceCharge(diffServiceCharge);
        lastTotal.setSettleCharge(settleCharge);
        lastTotal.setSettleTime("---");
        dataList.add(lastTotal);
        vo.setChargeBilExportVOS(dataList);

        //-----------------------------------进港已结算的数据--------------------------------------
        List<ChargeBillArrSettleExportVO> chargeBillArrSettleExportVOS = new ArrayList<>();
        AtomicInteger index = new AtomicInteger(1);
        if (!agentIds.isEmpty()){
            LambdaQueryWrapper<BaseBalance> wrapper = Wrappers.lambdaQuery(BaseBalance.class)
                    .in(BaseBalance::getAgentId, agentIds)
                    .gt(BaseBalance::getCreateTime, query.getFlightStartTime())
                    .lt(BaseBalance::getCreateTime, query.getFlightEndTime())
                    .orderByDesc(BaseBalance::getCreateTime)
                    .orderByDesc(BaseBalance::getId);
            List<BaseBalance> list = baseBalanceMapper.selectList(wrapper);
            Map<String, BigDecimal> pickUpPayWaybill = list.stream().filter(e -> ObjectUtil.equal("办单支付", e.getRemark()))
                    .collect(Collectors.groupingBy(BaseBalance::getWaybillCode,
                            Collectors.mapping(BaseBalance::getTradeMoney, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
            Map<String, BigDecimal> pickUpCancelWaybill = list.stream().filter(e -> ObjectUtil.equal("办单作废", e.getRemark()) || ObjectUtil.equal("提货超时", e.getRemark()))
                    .collect(Collectors.groupingBy(BaseBalance::getWaybillCode,
                            Collectors.mapping(BaseBalance::getTradeMoney, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

            List<String> collect = list.stream().map(BaseBalance::getWaybillCode).distinct().collect(Collectors.toList());
            List<ChargeBillArrSettleExportVO> arrAmountList = pickUpWaybillMapper.selectListByWaybillCode(collect);
            List<String> arrAmountWaybillList = arrAmountList.stream().map(ChargeBillArrSettleExportVO::getWaybillCode).distinct().collect(Collectors.toList());
            Map<String, List<ArrItemVo>> itemVoMap = arrAmountWaybillList.isEmpty() ?
                    Collections.emptyMap() :
                    itemMapper.selectAllItemVosNew(arrAmountWaybillList,2).stream()
                            .collect(Collectors.groupingBy(ArrItemVo::getWaybillCode));
            Map<String, List<ChargeBillArrSettleExportVO>> arrAmountMap = arrAmountList.stream().collect(Collectors.groupingBy(ChargeBillArrSettleExportVO::getWaybillCode));
            for(List<ChargeBillArrSettleExportVO> bills : arrAmountMap.values()){
                ChargeBillArrSettleExportVO bill = bills.get(0);
                bill.setIdx(index.getAndIncrement());
                List<ArrItemVo> items = itemVoMap.getOrDefault(bill.getWaybillCode(), Collections.emptyList());
                if (!items.isEmpty()) {
                    items.stream()
                            .collect(Collectors.groupingBy(ArrItemVo::getChargeAbb))
                            .forEach((chargeAbb, chargeItems) -> {
                                BigDecimal sum = chargeItems.stream()
                                        .map(item -> Optional.ofNullable(item.getEditCharge()).orElse(item.getTotalCharge()))
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                                switch (chargeAbb) {
                                    case "处置费":
                                        bill.setProcessingFee(sum);
                                        break;
                                    case "仓储费":
                                        bill.setStorageFee(sum);
                                        break;
                                    case "冷藏费":
                                        bill.setRefrigerationFee(sum);
                                        break;
                                    case "搬运费":
                                        bill.setHandlingFee(sum);
                                        break;
                                    case "电报费":
                                        bill.setCableCharge(sum);
                                        break;
                                    default:
                                        break;
                                }
                            });
                }
                BigDecimal payCost = pickUpPayWaybill.get(bill.getWaybillCode()) != null ? pickUpPayWaybill.get(bill.getWaybillCode()) : BigDecimal.ZERO;
                BigDecimal refundCost = pickUpCancelWaybill.get(bill.getWaybillCode()) != null ? pickUpCancelWaybill.get(bill.getWaybillCode()) : BigDecimal.ZERO;

                BigDecimal subtract = payCost.subtract(refundCost);
                if (subtract.compareTo(BigDecimal.ZERO) > 0){
                    bill.setSubtotal(subtract);
                    chargeBillArrSettleExportVOS.add(bill);
                }
            }
        }
        if (!deptIds.isEmpty()){
            List<ChargeBillArrSettleExportVO> arrAmountList = itemMapper.selectListByQuery(query.getFlightStartTime(),query.getFlightEndTime(),deptIds);
            List<String> arrAmountWaybillList = arrAmountList.stream().map(ChargeBillArrSettleExportVO::getWaybillCode).distinct().collect(Collectors.toList());
            Map<String, List<ArrItemVo>> itemVoMap = arrAmountWaybillList.isEmpty() ?
                    Collections.emptyMap() :
                    itemMapper.selectAllItemVosNew(arrAmountWaybillList,1).stream()
                            .collect(Collectors.groupingBy(ArrItemVo::getWaybillCode));
            Map<String, List<ChargeBillArrSettleExportVO>> arrAmountMap = arrAmountList.stream().collect(Collectors.groupingBy(ChargeBillArrSettleExportVO::getWaybillCode));
            for(List<ChargeBillArrSettleExportVO> bills : arrAmountMap.values()){
                ChargeBillArrSettleExportVO bill = bills.get(0);
                bill.setIdx(index.getAndIncrement());
                List<ArrItemVo> items = itemVoMap.getOrDefault(bill.getWaybillCode(), Collections.emptyList());
                if (!items.isEmpty()) {
                    items.stream()
                            .collect(Collectors.groupingBy(ArrItemVo::getChargeAbb))
                            .forEach((chargeAbb, chargeItems) -> {
                                BigDecimal sum = chargeItems.stream()
                                        .map(item -> Optional.ofNullable(item.getEditCharge()).orElse(item.getTotalCharge()))
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                                switch (chargeAbb) {
                                    case "处置费":
                                        bill.setProcessingFee(sum);
                                        break;
                                    case "仓储费":
                                        bill.setStorageFee(sum);
                                        break;
                                    case "冷藏费":
                                        bill.setRefrigerationFee(sum);
                                        break;
                                    case "搬运费":
                                        bill.setHandlingFee(sum);
                                        break;
                                    case "电报费":
                                        bill.setCableCharge(sum);
                                        break;
                                    default:
                                        break;
                                }
                            });
                    BigDecimal subtotal = items.stream().map(item -> Optional.ofNullable(item.getEditCharge()).orElse(item.getTotalCharge())).reduce(BigDecimal.ZERO, BigDecimal::add);
                    bill.setSubtotal(subtotal);
                }
                chargeBillArrSettleExportVOS.add(bill);
            }
        }
        ChargeBillArrSettleExportVO lastTotal1 = new ChargeBillArrSettleExportVO();
        lastTotal1.setWaybillCode("合计");
        lastTotal1.setQuantity(chargeBillArrSettleExportVOS.stream()
                .map(baseBalance -> Optional.ofNullable(baseBalance.getQuantity()).orElse(0))
                .reduce(0, Integer::sum));

        lastTotal1.setChargeWeight(getSumFee(chargeBillArrSettleExportVOS, ChargeBillArrSettleExportVO::getChargeWeight));

        BigDecimal processing = getSumFee(chargeBillArrSettleExportVOS, ChargeBillArrSettleExportVO::getProcessingFee);
        BigDecimal storageFee = getSumFee(chargeBillArrSettleExportVOS, ChargeBillArrSettleExportVO::getStorageFee);
        BigDecimal refrigeration = getSumFee(chargeBillArrSettleExportVOS, ChargeBillArrSettleExportVO::getRefrigerationFee);
        BigDecimal handling = getSumFee(chargeBillArrSettleExportVOS, ChargeBillArrSettleExportVO::getHandlingFee);
        BigDecimal cableChargeFee = getSumFee(chargeBillArrSettleExportVOS, ChargeBillArrSettleExportVO::getCableCharge);
        BigDecimal subtotal = getSumFee(chargeBillArrSettleExportVOS, ChargeBillArrSettleExportVO::getSubtotal);
        lastTotal1.setIdx(index.getAndIncrement());
        lastTotal1.setProcessingFee(processing);
        lastTotal1.setStorageFee(storageFee);
        lastTotal1.setRefrigerationFee(refrigeration);
        lastTotal1.setHandlingFee(handling);
        lastTotal1.setCableCharge(cableChargeFee);
        lastTotal1.setSubtotal(subtotal);
        chargeBillArrSettleExportVOS.add(lastTotal1);
        vo.setArrAmountList(chargeBillArrSettleExportVOS);
        return vo;
    }

    @Override
    public BilExportVO selectBillNewExportData(BillExportQuery query) {
        if (query.getFlightEndTime() == null || query.getFlightStartTime() == null){
            throw new CustomException("请传入航班日期");
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startTime = query.getFlightStartTime();
        LocalDateTime endTime = query.getFlightEndTime();

        BaseAgent baseAgent = baseAgentMapper.selectOne(Wrappers.lambdaQuery(BaseAgent.class).eq(BaseAgent::getAgent, query.getAgentCode().get(0)));
        if (baseAgent == null) {
            throw new CustomException("代理人不存在");
        }
        query.setAgent(query.getAgentCode().get(0));
        //-----------------------------------表头的数据------------------------------------------
        LambdaQueryWrapper<BaseBalance> wrapper = Wrappers.lambdaQuery(BaseBalance.class)
                .eq(BaseBalance::getAgentId, baseAgent.getId())
                .gt(BaseBalance::getCreateTime, startTime)
                .lt(BaseBalance::getCreateTime, endTime)
                .orderByDesc(BaseBalance::getCreateTime)
                .orderByDesc(BaseBalance::getId);
        List<BaseBalance> list = baseBalanceMapper.selectList(wrapper);

//        // 查询当期配载运单
//        List<Long> flightIdList = flightInfoMapper.selectFlightIds(query);
//        //统计数据
        BilExportVO vo = new BilExportVO();
//        // 设置退款/未来航班数据
//        setRefundAmountList(query,baseAgent,startTime,endTime,vo,list);
//        // 设置已结算数据
//        setSettledAmountList(baseAgent.getDeptId(),flightIdList,vo);
//        // 设置非当期已结算和未结算数据 余额数据
//        setSettledNotCurrentAmountList(list,baseAgent,vo,flightIdList,query);
//        // 设置总未结算数据
//        setUnsettledAmountList(query,endTime,format, baseAgent.getDeptId(),vo,list);
        // 设置总未结算数据
//        setUnsettledAmountListNew(query,endTime,format, baseAgent.getId(),vo);
        // 设置进港已结算数据
        setArrAmountListNew(list,vo);

        //-----------------------------------------标题--------------------------------------
        if (startTime == null || endTime == null) {
            vo.setSettlementCycle("---");
            vo.setTitle(query.getAgentCode().get(0) + "费用清单");
        } else {
            vo.setTitle(query.getAgentCode().get(0) + startTime.getMonthValue() + "-" + endTime.getMonthValue() + "月费用清单");
            vo.setSettlementCycle(formatDateTime(startTime) + "-" + formatDateTime(endTime));
        }
        vo.setRemarks("");
        vo.setThisTimeFinanceBalance(vo.getNotSettleAmount().add(vo.getBalance()));
        return vo;
    }

    private void setArrAmountList(List<BaseBalance> list,BilExportVO vo){
        List<String> arrWaybillCodeList = list.stream().filter(e->StringUtils.isNotEmpty(e.getWaybillCode()))
                .collect(Collectors.groupingBy(BaseBalance::getWaybillCode))
                .entrySet().stream()
                .filter(entry -> {
                    Set<String> remarks = entry.getValue().stream()
                            .map(BaseBalance::getRemark)
                            .collect(Collectors.toSet());
                    boolean hasPayment = remarks.contains("办单支付");
                    boolean hasBoth = hasPayment && (remarks.contains("办单作废") || remarks.contains("提货超时"));
                    return hasPayment && !hasBoth;
                })
                .map(Map.Entry::getKey)
                .distinct()
                .collect(Collectors.toList());
        if (!arrWaybillCodeList.isEmpty()){
            List<ChargeBillArrSettleExportVO> arrAmountList = pickUpWaybillMapper.selectListByWaybillCode(arrWaybillCodeList);
            List<TallyWaybillKey> keys = new ArrayList<>();
            arrAmountList.stream()
                    .filter(waybill->waybill.getPickUpId() != null && StringUtils.isNotEmpty(waybill.getWaybillCode()))
                    .forEach(waybill->keys.add(new TallyWaybillKey(waybill.getPickUpId(), waybill.getWaybillCode())));
            Map<TallyWaybillKey, List<ArrItemVo>> itemVoMap = keys.isEmpty() ?
                    Collections.emptyMap() :
                    itemMapper.selectAllItemVos(keys).stream()
                            .collect(Collectors.groupingBy(
                                    item -> new TallyWaybillKey(item.getPickUpId(), item.getWaybillCode())
                            ));
            AtomicInteger idx = new AtomicInteger(1);
            arrAmountList.forEach(bill->{
                bill.setIdx(idx.getAndIncrement());
                TallyWaybillKey key = new TallyWaybillKey(bill.getPickUpId(), bill.getWaybillCode());
                List<ArrItemVo> items = itemVoMap.getOrDefault(key, Collections.emptyList());
                if (!items.isEmpty()) {
                    BigDecimal total = items.stream()
                            .map(item -> Optional.ofNullable(item.getEditCharge()).orElse(item.getTotalCharge()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    bill.setSubtotal(total);
                    items.stream()
                            .collect(Collectors.groupingBy(ArrItemVo::getChargeAbb))
                            .forEach((chargeAbb, chargeItems) -> {
                                BigDecimal sum = chargeItems.stream()
                                        .map(item -> Optional.ofNullable(item.getEditCharge()).orElse(item.getTotalCharge()))
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                                switch (chargeAbb) {
                                    case "处置费":
                                        bill.setProcessingFee(sum);
                                        break;
                                    case "仓储费":
                                        bill.setStorageFee(sum);
                                        break;
                                    case "冷藏费":
                                        bill.setRefrigerationFee(sum);
                                        break;
                                    case "搬运费":
                                        bill.setHandlingFee(sum);
                                        break;
                                    case "电报费":
                                        bill.setCableCharge(sum);
                                        break;
                                    default:
                                        break;
                                }
                            });
                }
            });
            ChargeBillArrSettleExportVO lastTotal = new ChargeBillArrSettleExportVO();
            lastTotal.setWaybillCode("合计");
            lastTotal.setQuantity(arrAmountList.stream()
                    .map(baseBalance -> Optional.ofNullable(baseBalance.getQuantity()).orElse(0))
                    .reduce(0, Integer::sum));

            lastTotal.setChargeWeight(getSumFee(arrAmountList, ChargeBillArrSettleExportVO::getChargeWeight));

            BigDecimal processingFee = getSumFee(arrAmountList, ChargeBillArrSettleExportVO::getProcessingFee);
            BigDecimal storageFee = getSumFee(arrAmountList, ChargeBillArrSettleExportVO::getStorageFee);
            BigDecimal refrigerationFee = getSumFee(arrAmountList, ChargeBillArrSettleExportVO::getRefrigerationFee);
            BigDecimal handlingFee = getSumFee(arrAmountList, ChargeBillArrSettleExportVO::getHandlingFee);
            BigDecimal cableCharge = getSumFee(arrAmountList, ChargeBillArrSettleExportVO::getCableCharge);
            BigDecimal subtotal = getSumFee(arrAmountList, ChargeBillArrSettleExportVO::getSubtotal);

            lastTotal.setProcessingFee(processingFee);
            lastTotal.setStorageFee(storageFee);
            lastTotal.setRefrigerationFee(refrigerationFee);
            lastTotal.setHandlingFee(handlingFee);
            lastTotal.setCableCharge(cableCharge);
            lastTotal.setSubtotal(subtotal);
            arrAmountList.add(lastTotal);
            for (int i = 0; i < arrAmountList.size(); i++) {
                arrAmountList.get(i).setIdx(i + 1);
            }
            vo.setArrAmountList(arrAmountList);
        }
        //进港费用计算
        BigDecimal payPickUpSum = list.stream()
                .filter(e -> ObjectUtil.equal("办单支付", e.getRemark()))
                .map(BaseBalance::getTradeMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal cancelPickUpSum = list.stream()
                .filter(e -> ObjectUtil.equal("办单作废", e.getRemark()) || ObjectUtil.equal("提货超时", e.getRemark()))
                .map(BaseBalance::getTradeMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        vo.setArrAmount(payPickUpSum.subtract(cancelPickUpSum));
    }

    private void setArrAmountListNew(List<BaseBalance> list, BilExportVO vo) {
        Map<String, BigDecimal> pickUpPayWaybill = list.stream().filter(e -> ObjectUtil.equal("办单支付", e.getRemark()))
                .collect(Collectors.groupingBy(BaseBalance::getWaybillCode,
                        Collectors.mapping(BaseBalance::getTradeMoney, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        Map<String, BigDecimal> pickUpCancelWaybill = list.stream().filter(e -> ObjectUtil.equal("办单作废", e.getRemark()) || ObjectUtil.equal("提货超时", e.getRemark()))
                .collect(Collectors.groupingBy(BaseBalance::getWaybillCode,
                        Collectors.mapping(BaseBalance::getTradeMoney, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        List<String> collect = list.stream().map(BaseBalance::getWaybillCode).distinct().collect(Collectors.toList());
        List<ChargeBillArrSettleExportVO> arrAmountList = pickUpWaybillMapper.selectListByWaybillCode(collect);
        List<String> arrAmountWaybillList = arrAmountList.stream().map(ChargeBillArrSettleExportVO::getWaybillCode).distinct().collect(Collectors.toList());
        Map<String, List<ArrItemVo>> itemVoMap = arrAmountWaybillList.isEmpty() ?
                Collections.emptyMap() :
                itemMapper.selectAllItemVosNew(arrAmountWaybillList,2).stream()
                        .collect(Collectors.groupingBy(ArrItemVo::getWaybillCode));
        AtomicInteger idx = new AtomicInteger(1);
        List<ChargeBillArrSettleExportVO> chargeBillArrSettleExportVOS = new ArrayList<>();
        Map<String, List<ChargeBillArrSettleExportVO>> arrAmountMap = arrAmountList.stream().collect(Collectors.groupingBy(ChargeBillArrSettleExportVO::getWaybillCode));
        for(List<ChargeBillArrSettleExportVO> bills : arrAmountMap.values()){
            ChargeBillArrSettleExportVO bill = bills.get(0);
            bill.setIdx(idx.getAndIncrement());
            List<ArrItemVo> items = itemVoMap.getOrDefault(bill.getWaybillCode(), Collections.emptyList());
            if (!items.isEmpty()) {
                items.stream()
                        .collect(Collectors.groupingBy(ArrItemVo::getChargeAbb))
                        .forEach((chargeAbb, chargeItems) -> {
                            BigDecimal sum = chargeItems.stream()
                                    .map(item -> Optional.ofNullable(item.getEditCharge()).orElse(item.getTotalCharge()))
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            switch (chargeAbb) {
                                case "处置费":
                                    bill.setProcessingFee(sum);
                                    break;
                                case "仓储费":
                                    bill.setStorageFee(sum);
                                    break;
                                case "冷藏费":
                                    bill.setRefrigerationFee(sum);
                                    break;
                                case "搬运费":
                                    bill.setHandlingFee(sum);
                                    break;
                                case "电报费":
                                    bill.setCableCharge(sum);
                                    break;
                                default:
                                    break;
                            }
                        });
            }
            BigDecimal payCost = pickUpPayWaybill.get(bill.getWaybillCode()) != null ? pickUpPayWaybill.get(bill.getWaybillCode()) : BigDecimal.ZERO;
            BigDecimal refundCost = pickUpCancelWaybill.get(bill.getWaybillCode()) != null ? pickUpCancelWaybill.get(bill.getWaybillCode()) : BigDecimal.ZERO;

            BigDecimal subtract = payCost.subtract(refundCost);
            if (subtract.compareTo(BigDecimal.ZERO) > 0){
                bill.setSubtotal(subtract);
                chargeBillArrSettleExportVOS.add(bill);
            }
        }


        ChargeBillArrSettleExportVO lastTotal = new ChargeBillArrSettleExportVO();
        lastTotal.setWaybillCode("合计");
        lastTotal.setQuantity(chargeBillArrSettleExportVOS.stream()
                .map(baseBalance -> Optional.ofNullable(baseBalance.getQuantity()).orElse(0))
                .reduce(0, Integer::sum));

        lastTotal.setChargeWeight(getSumFee(chargeBillArrSettleExportVOS, ChargeBillArrSettleExportVO::getChargeWeight));

        BigDecimal processingFee = getSumFee(chargeBillArrSettleExportVOS, ChargeBillArrSettleExportVO::getProcessingFee);
        BigDecimal storageFee = getSumFee(chargeBillArrSettleExportVOS, ChargeBillArrSettleExportVO::getStorageFee);
        BigDecimal refrigerationFee = getSumFee(chargeBillArrSettleExportVOS, ChargeBillArrSettleExportVO::getRefrigerationFee);
        BigDecimal handlingFee = getSumFee(chargeBillArrSettleExportVOS, ChargeBillArrSettleExportVO::getHandlingFee);
        BigDecimal cableCharge = getSumFee(chargeBillArrSettleExportVOS, ChargeBillArrSettleExportVO::getCableCharge);
        BigDecimal subtotal = getSumFee(chargeBillArrSettleExportVOS, ChargeBillArrSettleExportVO::getSubtotal);

        lastTotal.setIdx(idx.getAndIncrement());
        lastTotal.setProcessingFee(processingFee);
        lastTotal.setStorageFee(storageFee);
        lastTotal.setRefrigerationFee(refrigerationFee);
        lastTotal.setHandlingFee(handlingFee);
        lastTotal.setCableCharge(cableCharge);
        lastTotal.setSubtotal(subtotal);
        chargeBillArrSettleExportVOS.add(lastTotal);

        vo.setArrAmountList(chargeBillArrSettleExportVOS);
        //进港费用计算
        BigDecimal payPickUpSum = list.stream()
                .filter(e -> ObjectUtil.equal("办单支付", e.getRemark()))
                .map(BaseBalance::getTradeMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal cancelPickUpSum = list.stream()
                .filter(e -> ObjectUtil.equal("办单作废", e.getRemark()) || ObjectUtil.equal("提货超时", e.getRemark()))
                .map(BaseBalance::getTradeMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        vo.setArrAmount(payPickUpSum.subtract(cancelPickUpSum));
    }

    /***
     * 运单配载在当期航班的数量结算金额
     * @param exportVO 赋值数据
     */
    private void setSettledAmountList(Long deptId, List<Long> flightIdList, BilExportVO exportVO){
        List<ChargeBilExportVO> waybillList = Optional.ofNullable(costDetailMapper.selectByFlightIds(flightIdList,deptId)).orElseGet(ArrayList::new);
        //将运单有多种费用进行分组，然后设置对应的费用值
        Map<String, List<ChargeBilExportVO>> waybillCodeToVO = waybillList.stream()
                .collect(Collectors.groupingBy(ChargeBilExportVO::getWaybillCode));
        List<ChargeBilExportVO> dataList = new ArrayList<>();
        //序号
        int idx = 1;
        for (Map.Entry<String, List<ChargeBilExportVO>> stringListEntry : waybillCodeToVO.entrySet()) {
            ChargeBilExportVO chargeBilExportVO = stringListEntry.getValue().get(0);
            chargeBilExportVO.setId((long) idx++);
            //根据费用名称设置对应的值
            stringListEntry.getValue().forEach(val -> setFee(chargeBilExportVO, val.getChargeAbb(), val.getTotalCharge()));
            chargeBilExportVO.setSettleCharge(chargeBilExportVO.getTotalCharge());
            dataList.add(chargeBilExportVO);
        }
//        for (List<ChargeBilExportVO> value : waybillCodeToVO.values()) {
//
//        }
        ChargeBilExportVO lastTotal = new ChargeBilExportVO();
        lastTotal.setWaybillCode("合计");
        lastTotal.setQuantity(dataList.stream()
                .map(baseBalance -> Optional.ofNullable(baseBalance.getQuantity()).orElse(0))
                .reduce(0, Integer::sum));

        lastTotal.setChargeWeight(getSumFee(dataList, ChargeBilExportVO::getChargeWeight));

        BigDecimal processingFee = getSumFee(dataList, ChargeBilExportVO::getProcessingFee);
        BigDecimal refrigerationFee = getSumFee(dataList, ChargeBilExportVO::getRefrigerationFee);
        BigDecimal handlingFee = getSumFee(dataList, ChargeBilExportVO::getHandlingFee);
        BigDecimal cableCharge = getSumFee(dataList, ChargeBilExportVO::getCableCharge);
        BigDecimal diffServiceCharge = getSumFee(dataList, ChargeBilExportVO::getDiffServiceCharge);
        BigDecimal settleCharge = getSumFee(dataList, ChargeBilExportVO::getSettleCharge);

        lastTotal.setId((long)idx);
        lastTotal.setProcessingFee(processingFee);
        lastTotal.setRefrigerationFee(refrigerationFee);
        lastTotal.setHandlingFee(handlingFee);
        lastTotal.setCableCharge(cableCharge);
        lastTotal.setDiffServiceCharge(diffServiceCharge);
        lastTotal.setSettleCharge(settleCharge);
        lastTotal.setSettleTime("---");
        dataList.add(lastTotal);
        exportVO.setSettledAmount(settleCharge);
        exportVO.setSettledAmountList(dataList);
    }

    private void setUnsettledAmountList(BillExportQuery query, LocalDateTime endTime,
                                        SimpleDateFormat format, Long deptId,BilExportVO vo, List<BaseBalance> balances){
        //-----------------------------------未结算的列表数据--------------------------------------
        //(1.8到结束时间制单支付)未结算金额大于0 和支付在这个时间的但是配载不在这个时间的数据 还有拉下的
        List<ChargeBilExportNotSettleVO> dataListNotSettle = new ArrayList<>();
        BigDecimal notSettleSum = BigDecimal.ZERO;
        LocalDateTime dateTime = LocalDateTime.of(2025, 1, 8, 0, 0, 0);
        query.setWriteStartTime(dateTime);
        query.setWriteEndTime(endTime);

        List<ChargeBilExportNotSettleVO> waybillListNotSettle = Optional.ofNullable(costDetailMapper.selectNotSettleData(query,0,1)).orElseGet(ArrayList::new);
        List<ChargeBilExportNotSettleVO> waybillCodeList = getWaybillCodeList(waybillListNotSettle);
        for(ChargeBilExportNotSettleVO voNotSettle : waybillCodeList){
            BigDecimal[] subs = notSettleResultRefund(voNotSettle.getWaybillCode(), endTime);
            if(subs[0].compareTo(BigDecimal.ZERO) > 0){
                dataListNotSettle.add(voNotSettle);
                notSettleSum = notSettleSum.add(subs[0]);
            }
        }
        List<ChargeBilExportNotSettleVO> waybillListFlightNotSettle = Optional.ofNullable(costDetailMapper.selectFlightNotSettleData(query,0,1)).orElseGet(ArrayList::new);
        List<ChargeBilExportNotSettleVO> waybillCodeList1 = getWaybillCodeList(waybillListFlightNotSettle);
        for(ChargeBilExportNotSettleVO voNotSettle : waybillCodeList1){
            BigDecimal[] subs = notSettleResultRefund(voNotSettle.getWaybillCode(), endTime);
            notSettleSum = notSettleSum.add(subs[2]);
            dataListNotSettle.add(voNotSettle);
//            if(subs[0].compareTo(BigDecimal.ZERO) == 0){
//                dataListNotSettle.add(voNotSettle);
//                notSettleSum = notSettleSum.add(subs[1]);
//            }
        }

        int index = 0;
        for(ChargeBilExportNotSettleVO voNotSettle : dataListNotSettle){
            voNotSettle.setId((long)++index);
            List<ChargeSettleDetailVo> detailVos = new ArrayList<>();
            ChargeQuery chargeQuery = new ChargeQuery();
//            chargeQuery.setFlightLoadStartTime(Date.from(query.getFlightStartTime().atZone(ZoneId.systemDefault()).toInstant()));
//            chargeQuery.setFlightLoadEndTime(Date.from(query.getFlightEndTime().atZone(ZoneId.systemDefault()).toInstant()));
            List<CostDetail> settleList = costDetailMapper.selectPayOrSettleListBySettleTime(voNotSettle.getWaybillCode(),1,1, deptId,chargeQuery);
            if (!CollectionUtils.isEmpty(settleList)){
                Map<String, List<CostDetail>> collect = settleList.stream().collect(Collectors.groupingBy(e->e.getFlightId() + "," + (e.getCreateTime() == null ? " " : format.format(e.getCreateTime()))));
                for (Map.Entry<String, List<CostDetail>> longListEntry : collect.entrySet()) {
                    String[] split = longListEntry.getKey().split(",");
                    ChargeSettleDetailVo chargeSettleDetailVo = new ChargeSettleDetailVo();
                    if ("1".equals(split[0])){
                        continue;
                    }else {
                        FlightInfo info = flightInfoMapper.selectById(split[0]);
                        if (info != null){
                            chargeSettleDetailVo.setSettleFlightNo(info.getAirWays() + info.getFlightNo());
                            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                            chargeSettleDetailVo.setSettleFlightDate(dateFormat.format(info.getExecDate()));
                        }
                    }
                    BigDecimal reduce = longListEntry.getValue().stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                    chargeSettleDetailVo.setSettleCost(reduce);

                    BigDecimal settleWeight = longListEntry.getValue().stream().map(CostDetail::getSettleDepWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    chargeSettleDetailVo.setSettleDepWeight(settleWeight);

                    BigDecimal quantity = longListEntry.getValue().stream().map(CostDetail::getQuantity).filter(Objects::nonNull).map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add);
                    chargeSettleDetailVo.setQuantity(quantity.toString());

                    int settleQuantity = longListEntry.getValue().stream().filter(e->e.getSettleDepQuantity() != null).mapToInt(CostDetail::getSettleDepQuantity).sum();
                    chargeSettleDetailVo.setSettleDepQuantity(settleQuantity);

                    try {
                        if (" ".equals(split[1])){
                            chargeSettleDetailVo.setSettleTime(null);
                        }else {
                            chargeSettleDetailVo.setSettleTime(format.parse(split[1]));
                        }
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                    detailVos.add(chargeSettleDetailVo);
                }
            }
            if (!CollectionUtils.isEmpty(detailVos)){
                List<ChargeSettleDetailVo> collect = detailVos.stream().filter(e->StringUtils.isNotEmpty(e.getSettleFlightNo())).sorted(Comparator.comparing(ChargeSettleDetailVo::getSettleTime)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(collect)){
                    int sum = collect.stream().mapToInt(ChargeSettleDetailVo::getSettleDepQuantity).sum();
                    voNotSettle.setQuantity(sum);
                    BigDecimal reduce1 = collect.stream().map(ChargeSettleDetailVo::getQuantity).map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add);
                    voNotSettle.setChargeWeight(reduce1);
                }
            }

            /** 未结算列表的已结算和未结算 */
            List<CostDetail> settleLists = costDetailMapper.selectList(new QueryWrapper<CostDetail>()
                    .eq("waybill_code", voNotSettle.getWaybillCode())
                    .eq("is_del", 0)
                    .eq("type", 1));
            BigDecimal settleSum = settleLists.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal notSettleSettleSum = BigDecimal.ZERO;
            notSettleSettleSum = notSettleSettleSum.add(voNotSettle.getProcessingFee()).add(voNotSettle.getRefrigerationFee())
                    .add(voNotSettle.getHandlingFee()).add(voNotSettle.getForkliftCharge()).add(voNotSettle.getCableCharge())
                    .add(voNotSettle.getDiffServiceCharge()).subtract(settleSum);
            if(notSettleSettleSum.compareTo(BigDecimal.ZERO) == 0){
                voNotSettle.setSettleCharge(BigDecimal.ZERO);
                voNotSettle.setNotSettleCharge(settleSum);
                voNotSettle.setSettleTime(null);
            }else{
                voNotSettle.setSettleCharge(settleSum);
                voNotSettle.setNotSettleCharge(notSettleSettleSum);
            }
        }


        List<ChargeBilExportNotSettleVO> waybillListFlightRefund = Optional.ofNullable(costDetailMapper.selectFlightRefundData(query)).orElseGet(ArrayList::new);
        if(waybillListFlightRefund.size() > 0){
            notSettleSum = notSettleSum.add(waybillListFlightRefund.stream().map(ChargeBilExportNotSettleVO::getTotalCharge).reduce(BigDecimal.ZERO,BigDecimal::add));
        }
        List<ChargeBilExportNotSettleVO> waybillCodeList2 = getWaybillCodeListNew(waybillListFlightRefund);
        dataListNotSettle.addAll(waybillCodeList2);

        List<BaseBalance> balancesPay = balances.stream()
                .filter(e -> "运单支付".equals(e.getRemark()) && e.getTradeMoney().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
        for(BaseBalance balance : balancesPay){
            BaseBalance baseBalance = baseBalanceMapper.selectOne(new QueryWrapper<BaseBalance>()
                    .eq("waybill_code", balance.getWaybillCode())
                    .in("remark", "取消支付退款","作废退款")
                    .gt("create_time", endTime)
                    .gt("create_time", balance.getCreateTime())
                    .orderByAsc("create_time").last("limit 1"));
            if(baseBalance != null){
                notSettleSum = notSettleSum.add(baseBalance.getTradeMoney());
                List<CostDetail> payCancelList = costDetailMapper.selectDetailListByCode(baseBalance.getWaybillCode(),null);
                if(payCancelList.size() > 0){
                    ChargeBilExportNotSettleVO settleVO = new ChargeBilExportNotSettleVO();
                    settleVO.setWaybillCode(baseBalance.getWaybillCode());
                    settleVO.setNotSettleCharge(baseBalance.getTradeMoney());
                    settleVO.setQuantity(payCancelList.get(0).getSettleDepQuantity());
                    settleVO.setChargeWeight(payCancelList.get(0).getSettleDepWeight());
                    for (CostDetail costDetail : payCancelList) {
                        setFee(settleVO, costDetail.getChargeAbb(), costDetail.getTotalCharge());
                    }
                    dataListNotSettle.add(settleVO);
                }
            }
        }


        //结算后全部拉下 没有退款也没有重新结算的运单
//        List<HzDepPullDown> pullWaybillList = Optional.ofNullable(costDetailMapper.selectPullAllNotSettle(query)).orElseGet(ArrayList::new);
//        List<String> waybillNotSettlePullList = new ArrayList<>();
//        Map<String, List<HzDepPullDown>> pullWaybillMap = pullWaybillList.stream()
//                .collect(Collectors.groupingBy(HzDepPullDown::getWaybillCode));
//        for (Map.Entry<String, List<HzDepPullDown>> longListEntry : pullWaybillMap.entrySet()) {
//            //已结算的运单
//            String waybillCode = longListEntry.getKey();
//            List<CostDetail> detailSettleList = costDetailMapper.selectList(new QueryWrapper<CostDetail>()
//                    .eq("waybill_code", waybillCode)
//                    .eq("is_del", 0)
//                    .eq("type", 1));
//            if(detailSettleList.size() == 0){
//                continue;
//            }
//            waybillNotSettlePullList.add(waybillCode);
//        }
//        if(waybillNotSettlePullList.size() > 0){
//            List<ChargeBilExportNotSettleVO> chargeBilExportNotSettleVOS = costDetailMapper.selectPullAllNotSettleData(waybillNotSettlePullList);
//            List<ChargeBilExportNotSettleVO> waybillCodeList2 = getWaybillCodeList(chargeBilExportNotSettleVOS);
//            for(ChargeBilExportNotSettleVO voNotSettle : waybillCodeList2){
//                BigDecimal[] subs = notSettleResultRefund(voNotSettle.getWaybillCode(),endTime);
//                if(subs[0].compareTo(BigDecimal.ZERO) == 0){
//                    dataListNotSettle.add(voNotSettle);
//                    notSettleSum = notSettleSum.add(subs[1]);
//                }
//            }
//        }
        vo.setNotSettleAmount(notSettleSum);

        ChargeBilExportNotSettleVO lastTotalNotSettle = new ChargeBilExportNotSettleVO();
        lastTotalNotSettle.setWaybillCode("合计");
        lastTotalNotSettle.setQuantity(dataListNotSettle.stream()
                .map(baseBalance -> Optional.ofNullable(baseBalance.getQuantity()).orElse(0))
                .reduce(0, Integer::sum));

        lastTotalNotSettle.setChargeWeight(getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getChargeWeight));

        BigDecimal processingFeeNotSettle = getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getProcessingFee);
        BigDecimal refrigerationFeeNotSettle = getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getRefrigerationFee);
        BigDecimal handlingFeeNotSettle = getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getHandlingFee);
        BigDecimal cableChargeNotSettle = getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getCableCharge);
        BigDecimal diffServiceChargeNotSettle = getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getDiffServiceCharge);
        BigDecimal settleChargeNotSettle = getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getSettleCharge);
        BigDecimal notSettleChargeNotSettle = getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getNotSettleCharge);

        lastTotalNotSettle.setId((long) index);
        lastTotalNotSettle.setProcessingFee(processingFeeNotSettle);
        lastTotalNotSettle.setRefrigerationFee(refrigerationFeeNotSettle);
        lastTotalNotSettle.setHandlingFee(handlingFeeNotSettle);
        lastTotalNotSettle.setCableCharge(cableChargeNotSettle);
        lastTotalNotSettle.setDiffServiceCharge(diffServiceChargeNotSettle);
        lastTotalNotSettle.setSettleCharge(settleChargeNotSettle);
        lastTotalNotSettle.setNotSettleCharge(notSettleChargeNotSettle);
        lastTotalNotSettle.setSettleTime("---");
        dataListNotSettle.add(lastTotalNotSettle);
        vo.setNotSettleAmountList(dataListNotSettle);
    }

    private void setUnsettledAmountListNew(BillExportQuery query, LocalDateTime endTime, SimpleDateFormat format, Long deptId,BilExportVO vo){
        List<ChargeBilExportNotSettleVO> dataListNotSettle = new ArrayList<>();
        BigDecimal notSettleSum = BigDecimal.ZERO;
        LambdaQueryWrapper<BaseBalance> wrapper = Wrappers.lambdaQuery(BaseBalance.class)
                .eq(BaseBalance::getAgentId, deptId)
                .gt(BaseBalance::getCreateTime, "2025-01-08 00:00:00")
                .lt(BaseBalance::getCreateTime, endTime)
                .orderByDesc(BaseBalance::getCreateTime)
                .orderByDesc(BaseBalance::getId);
        List<BaseBalance> list = baseBalanceMapper.selectList(wrapper);
        List<String> waybillCodeList = list.stream()
                .filter(e -> e.getRemark().contains("运单支付") && e.getTradeMoney().compareTo(BigDecimal.ZERO) != 0)
                .map(BaseBalance::getWaybillCode).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(waybillCodeList)){
            BigDecimal costSum = costDetailMapper.selectAllPostSum(waybillCodeList);
            List<String> notLoadWaybillList = airWaybillMapper.selectWaybill(waybillCodeList,endTime);
            if (!CollectionUtils.isEmpty(notLoadWaybillList)){
                BigDecimal settleSum = costDetailMapper.selectAllSettleSum(notLoadWaybillList);
                notSettleSum = costSum.add(settleSum);
            }else {
                notSettleSum = costSum;
            }
        }
        vo.setNotSettleAmountList(dataListNotSettle);
        vo.setNotSettleAmount(notSettleSum);
    }

    private void setSettledNotCurrentAmountList(List<BaseBalance> list,BaseAgent baseAgent,BilExportVO vo,List<Long> flightIdList,BillExportQuery query){
        //-----------------------------------表头的数据------------------------------------------
        List<String> waybillCodeList = baseBalanceMapper.selectNotWaybillCode(query,baseAgent.getId());
        if (!CollectionUtils.isEmpty(waybillCodeList)){
            List<LoadFlightWaybillVO> waybillVoList = loadWaybillMapper.selectLoadFlightWaybill(waybillCodeList);
            // 过滤在当期存在配载的运单
            List<LoadFlightWaybillVO> filteredList = waybillVoList.stream()
                    .filter(Objects::nonNull)
                    .filter(k -> flightIdList.contains(k.getFlightId()))
                    .collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(filteredList)){
                // 非当期已结算
                List<ChargeBilExportNotSettleVO> notCurrentAmountList = costDetailMapper.selectNotCurrentAmountList(filteredList);
                long j = 1;
                for (ChargeBilExportNotSettleVO notSettleVO : notCurrentAmountList) {
                    switch (notSettleVO.getChargeAbb()) {
                        case "处置费":
                            notSettleVO.setProcessingFee(notSettleVO.getTotalCharge());
                            break;
                        case "冷藏费":
                            notSettleVO.setRefrigerationFee(notSettleVO.getTotalCharge());
                            break;
                        case "搬运费":
                            notSettleVO.setHandlingFee(notSettleVO.getTotalCharge());
                            break;
                        case "电报费":
                            notSettleVO.setCableCharge(notSettleVO.getTotalCharge());
                            break;
                        case "叉车费":
                            notSettleVO.setForkliftCharge(notSettleVO.getTotalCharge());
                            break;
                        case "差异化服务费(跨航司)":
                            notSettleVO.setDiffServiceCharge(notSettleVO.getTotalCharge());
                            break;
                    }
                    notSettleVO.setSettleCharge(notSettleVO.getTotalCharge());
                    notSettleVO.setId(j);
                    j++;
                }
                if (!CollectionUtils.isEmpty(notCurrentAmountList)){
                    BigDecimal reduce = notCurrentAmountList.stream().map(ChargeBilExportNotSettleVO::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                    vo.setSettledNotCurrentAmount(reduce);
                }
                vo.setSettledNotCurrentAmountList(notCurrentAmountList);

                List<ChargeBilExportNotSettleVO> notDqAllNotSettle = new ArrayList<>();
                List<ChargeBilExportNotSettleVO> notDqAllNotSettleNew = new ArrayList<>();
                // 非当期未结算
                List<String> notDqSettleCode = filteredList.stream().map(LoadFlightWaybillVO::getWaybillCode).distinct().collect(Collectors.toList());
                List<ChargeBilExportNotSettleVO> notDqPayNoSettleList =  costDetailMapper.selectPayList(notDqSettleCode);
                notDqAllNotSettle.addAll(notDqPayNoSettleList);
                BigDecimal notDqPayCost = notDqPayNoSettleList.stream().map(ChargeBilExportNotSettleVO::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                // 当期及当期之前配载的航班和运单
                query.setFlightStartTime(null);
                List<LoadFlightWaybillVO> notDqList = loadWaybillMapper.selectHistoryLoad(query.getFlightEndTime(),notDqSettleCode);
                // 当期及当期之前配载已结算的
                List<ChargeBilExportNotSettleVO> noDqSettleVos = costDetailMapper.selectNotCurrentAmountList(notDqList);
                notDqAllNotSettle.addAll(noDqSettleVos);
                BigDecimal notDqSettleCost = noDqSettleVos.stream().map(ChargeBilExportNotSettleVO::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                // 当期及当期之前退款的
                List<ChargeBilExportNotSettleVO> notDqRefundList =  costDetailMapper.selectRefundListNew(notDqSettleCode);
                notDqAllNotSettle.addAll(notDqRefundList);
                BigDecimal notDqRefundCost = notDqRefundList.stream().map(ChargeBilExportNotSettleVO::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                vo.setUnsettledNotCurrentAmount(notDqPayCost.subtract(notDqSettleCost).subtract(notDqRefundCost));
                Map<String, List<ChargeBilExportNotSettleVO>> notDqCollect1 = notDqAllNotSettle.stream().collect(Collectors.groupingBy(ChargeBilExportNotSettleVO::getWaybillCode));
                for (ChargeBilExportNotSettleVO notSettleVO : notDqPayNoSettleList) {
                    List<ChargeBilExportNotSettleVO> notSettleVOS = notDqCollect1.get(notSettleVO.getWaybillCode());
                    BigDecimal sumType0 = notSettleVOS.stream()
                            .filter(e -> e.getType() != null && "0".equals(e.getType()))
                            .map(ChargeBilExportNotSettleVO::getTotalCharge)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal sumType1 = notSettleVOS.stream()
                            .filter(e -> e.getType() != null && "1".equals(e.getType()))
                            .map(ChargeBilExportNotSettleVO::getTotalCharge)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (sumType0.compareTo(sumType1) > 0){
                        notSettleVO.setNotSettleCharge(sumType0.subtract(sumType1));
                        notSettleVO.setSettleCharge(sumType1);
                        switch (notSettleVO.getChargeAbb()) {
                            case "处置费":
                                notSettleVO.setProcessingFee(notSettleVO.getTotalCharge());
                                break;
                            case "冷藏费":
                                notSettleVO.setRefrigerationFee(notSettleVO.getTotalCharge());
                                break;
                            case "搬运费":
                                notSettleVO.setHandlingFee(notSettleVO.getTotalCharge());
                                break;
                            case "电报费":
                                notSettleVO.setCableCharge(notSettleVO.getTotalCharge());
                                break;
                            case "叉车费":
                                notSettleVO.setForkliftCharge(notSettleVO.getTotalCharge());
                                break;
                            case "差异化服务费(跨航司)":
                                notSettleVO.setDiffServiceCharge(notSettleVO.getTotalCharge());
                                break;
                        }
                        notDqAllNotSettleNew.add(notSettleVO);
                    }
                }
                vo.setUnsettledNotCurrentAmountList(notDqAllNotSettleNew);
            }
        }
        // 配载数据
        List<ChargeBilExportNotSettleVO> allNotSettle = new ArrayList<>();
        List<ChargeBilExportNotSettleVO> allNotSettleNew = new ArrayList<>();
        // 未结算 (在当期配载的运单)
        List<LoadFlightWaybillVO> waybillVos = loadWaybillMapper.selectNotSettleWaybill(flightIdList,baseAgent.getDeptId());
        List<String> loadWaybillList = waybillVos.stream().map(LoadFlightWaybillVO::getWaybillCode).distinct().collect(Collectors.toList());

        // 预支付金额数据
        List<ChargeBilExportNotSettleVO> payNoSettleList =  costDetailMapper.selectPayList(loadWaybillList);
        allNotSettle.addAll(payNoSettleList);
        BigDecimal payCost = payNoSettleList.stream().map(ChargeBilExportNotSettleVO::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 当期及当期之前配载的航班和运单
        query.setFlightStartTime(null);
        List<LoadFlightWaybillVO> list1 = loadWaybillMapper.selectHistoryLoad(query.getFlightEndTime(),loadWaybillList);
        // 当期及当期之前配载已结算的
        List<ChargeBilExportNotSettleVO> settleVos = costDetailMapper.selectNotCurrentAmountList(list1);
        allNotSettle.addAll(settleVos);
        BigDecimal settleCost = settleVos.stream().map(ChargeBilExportNotSettleVO::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 当期及当期之前退款的
        List<ChargeBilExportNotSettleVO> refundList =  costDetailMapper.selectRefundListNew(loadWaybillList);
        allNotSettle.addAll(refundList);
        BigDecimal refundCost = refundList.stream().map(ChargeBilExportNotSettleVO::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
        vo.setUnsettledAmount(payCost.subtract(settleCost).subtract(refundCost));
        Map<String, List<ChargeBilExportNotSettleVO>> collect1 = allNotSettle.stream().collect(Collectors.groupingBy(ChargeBilExportNotSettleVO::getWaybillCode));
        long i = 1;
        for (ChargeBilExportNotSettleVO notSettleVO : payNoSettleList) {
            List<ChargeBilExportNotSettleVO> notSettleVOS = collect1.get(notSettleVO.getWaybillCode());
            BigDecimal sumType0 = notSettleVOS.stream()
                    .filter(e -> e.getType() != null && "0".equals(e.getType()))
                    .map(ChargeBilExportNotSettleVO::getTotalCharge)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal sumType1 = notSettleVOS.stream()
                    .filter(e -> e.getType() != null && "1".equals(e.getType()))
                    .map(ChargeBilExportNotSettleVO::getTotalCharge)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (sumType0.compareTo(sumType1) > 0){
                notSettleVO.setNotSettleCharge(sumType0.subtract(sumType1));
                notSettleVO.setSettleCharge(sumType1);
                notSettleVO.setId(i);
                allNotSettleNew.add(notSettleVO);
                i++;
            }
        }
        vo.setUnsettledAmountList(allNotSettleNew);
        if(!CollectionUtils.isEmpty(list)) {
            //充值金额 = 增加余额之和
            Map<String, BigDecimal> typeToSum = list.stream()
                    .collect(Collectors.toMap(
                            BaseBalance::getRemark,
                            val -> Optional.ofNullable(val.getTradeMoney()).orElse(new BigDecimal(0)),
                            BigDecimal::add));
            vo.setRechargeAmount(Optional.ofNullable(typeToSum.get("增加余额")).orElse(new BigDecimal(0)));
            //当前余额 = 时间段内最新的余额
            vo.setBalance(CollectionUtil.isEmpty(list) ? new BigDecimal(0) : list.get(0).getBalance());
            //上期余额 等于这个时间段的前一天余额明细的余额
            BaseBalance baseBalance = baseBalanceMapper.selectOne(new QueryWrapper<BaseBalance>()
                    .eq("agent_id", baseAgent.getId())
                    .lt("create_time", list.get(list.size() - 1).getCreateTime())
                    .orderByDesc("create_time").orderByDesc("id")
                    .last("limit 1"));
            BigDecimal lastBalance = StringUtils.isNotNull(baseBalance) ? baseBalance.getBalance() : new BigDecimal(0);
            vo.setLastBalance(lastBalance);

//            //配载时间段内运单
//            List<String> waybillCodeFlightList = vo.getSettledAmountList().stream().map(ChargeBilExportVO::getWaybillCode).collect(Collectors.toList());
//
//            //配载时间段内运单明细运单集合
//            List<String> waybillCodeBanlanceList = list.stream().map(BaseBalance::getWaybillCode).collect(Collectors.toList());
//
//            //非当期支付的本期配载的已结算金额
//            BigDecimal settleNotCurrent = BigDecimal.ZERO;
//
//            //非当期支付的本期配载的未结算金额 = 已支付 - 已结算
//            BigDecimal unSettleNotCurrent = BigDecimal.ZERO;
//
//            //得到非当期支付的运单
//            waybillCodeFlightList.removeAll(waybillCodeBanlanceList);
//            List<CostDetail> settleList = costDetailMapper.selectListByCodeList(waybillCodeFlightList);
//            if (CollectionUtils.isEmpty(settleList)){
//                return;
//            }
//            Map<String, List<CostDetail>> costDetailMap = settleList.stream()
//                    .collect(Collectors.groupingBy(CostDetail::getWaybillCode));
//            // 非当期已结算运单
//            List<String> settledNotCurrentWaybillCodeList = new ArrayList<>();
//            // 非当期未结算运单
//            List<String> unsettledNotCurrentWaybillCodeList = new ArrayList<>();
//            for(String waybillCode : waybillCodeFlightList){
//                // ----------------- 计算已结算-----------------
//                List<CostDetail> detailList = costDetailMap.get(waybillCode);
//                List<CostDetail> collect = new ArrayList<>();
//                if (!CollectionUtils.isEmpty(detailList)){
//                    collect = detailList.stream().filter(e -> e.getType() == 1).collect(Collectors.toList());
//                    if (!CollectionUtils.isEmpty(collect)){
//                        BigDecimal costSum = collect.stream()
//                                .map(CostDetail::getTotalCharge)
//                                .reduce(BigDecimal.ZERO, BigDecimal::add);
//                        settleNotCurrent = settleNotCurrent.add(costSum);
//                    }
//
//                    // ----------------- 计算未结算-----------------
//                    List<CostDetail> payList = detailList.stream().filter(e -> e.getType() == 0 && e.getIsSettle() == 1).collect(Collectors.toList());
//                    if (!CollectionUtils.isEmpty(payList)){
//                        BigDecimal paySum = payList.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
//                        BigDecimal settleSum = BigDecimal.ZERO;
//                        if (!CollectionUtils.isEmpty(collect)){
//                            settleSum = collect.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
//                        }
//                        BigDecimal subtract = paySum.subtract(settleSum);
//                        BigDecimal bigDecimal = subtract.compareTo(new BigDecimal(0)) < 0 ? new BigDecimal(0) : subtract;
//                        if (bigDecimal.compareTo(BigDecimal.ZERO) != 0){
//                            unSettleNotCurrent = unSettleNotCurrent.add(bigDecimal);
//                            unsettledNotCurrentWaybillCodeList.add(waybillCode);
//                        }else {
//                            settledNotCurrentWaybillCodeList.add(waybillCode);
//                        }
//                    }
//                }
//            }
//            // 非当期已结算运单
//            if (!CollectionUtils.isEmpty(settledNotCurrentWaybillCodeList)){
//                List<ChargeBilExportNotSettleVO> settledNotCurrentAmountList = costDetailMapper.selectSettledNotCurrentList(settledNotCurrentWaybillCodeList);
//                if (!CollectionUtils.isEmpty(settledNotCurrentAmountList)) {
//                    List<ChargeBilExportNotSettleVO> collect = settledNotCurrentAmountList.stream().filter(e -> "1".equals(e.getType())).collect(Collectors.toList());
//                    List<ChargeBilExportNotSettleVO> chargeBillNotSettleExportVOS = getChargeBillNotSettleExportLastTotal(collect);
//                    vo.setSettledNotCurrentAmountList(chargeBillNotSettleExportVOS);
//                }
//            }
//
//            // 非当期未结算运单
//            if (!CollectionUtils.isEmpty(unsettledNotCurrentWaybillCodeList)){
//                List<ChargeBilExportNotSettleVO> unsettledNotCurrentAmountList = costDetailMapper.selectSettledNotCurrentList(unsettledNotCurrentWaybillCodeList);
//                if (!CollectionUtils.isEmpty(unsettledNotCurrentAmountList)){
//                    List<ChargeBilExportNotSettleVO> collect = unsettledNotCurrentAmountList.stream().filter(e -> "0".equals(e.getType())).collect(Collectors.toList());
//                    List<ChargeBilExportNotSettleVO> chargeBillNotSettleExportVOS = getChargeBillNotSettleExportLastTotal(collect);
//                    vo.setUnsettledNotCurrentAmountList(chargeBillNotSettleExportVOS);
//                }
//            }
//            vo.setSettledNotCurrentAmount(settleNotCurrent);
//            vo.setUnsettledNotCurrentAmount(unSettleNotCurrent);
//        }else{
//            vo.setRechargeAmount(BigDecimal.ZERO);
//            vo.setBalance(BigDecimal.ZERO);
//            vo.setLastBalance(BigDecimal.ZERO);
//            vo.setSettledNotCurrentAmount(BigDecimal.ZERO);
//            vo.setUnsettledNotCurrentAmount(BigDecimal.ZERO);
//        }
        }
    }

    private void setRefundAmountList(BillExportQuery query, BaseAgent baseAgent,LocalDateTime startTime,LocalDateTime endTime,BilExportVO vo, List<BaseBalance> list){
        // 当期退款运单
        List<String> reduceCurrentWaybillCodeList = new ArrayList<>();
        List<BaseBalance> reduceCurrentBaseBalanceList = new ArrayList<>();
        // 非当期退款运单
        List<String> reduceNotCurrentWaybillCodeList = new ArrayList<>();
        List<BaseBalance> reduceNotCurrentBaseBalanceList = new ArrayList<>();
        List<BaseBalance> payCancelCodes = list.stream()
                .filter(e -> e.getRemark().contains("退款") && e.getTradeMoney().compareTo(BigDecimal.ZERO) != 0)
                .filter(e -> !e.getRemark().equals("取消支付退款"))
                .collect(Collectors.toList());
        for(BaseBalance balance : payCancelCodes){
            BaseBalance baseBalance = baseBalanceMapper.selectOne(new QueryWrapper<BaseBalance>()
                    .eq("waybill_code", balance.getWaybillCode())
                    .eq("remark", "运单支付")
                    .lt("create_time",balance.getCreateTime())
                    .orderByDesc("create_time").last("limit 1"));
            LocalDateTime createTime = baseBalance.getCreateTime().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
            if(createTime.isBefore(startTime) || createTime.isAfter(endTime)){
                reduceNotCurrentBaseBalanceList.add(balance);
                reduceNotCurrentWaybillCodeList.add(baseBalance.getWaybillCode());
            }else{
                reduceCurrentBaseBalanceList.add(balance);
                reduceCurrentWaybillCodeList.add(baseBalance.getWaybillCode());
            }
        }
        vo.setRefundCurrentAmount(reduceCurrentBaseBalanceList.stream().map(BaseBalance::getTradeMoney).reduce(BigDecimal.ZERO,BigDecimal::add));
        vo.setRefundNotCurrentAmount(reduceNotCurrentBaseBalanceList.stream().map(BaseBalance::getTradeMoney).reduce(BigDecimal.ZERO,BigDecimal::add));

        // 当期退款数据
        if (!reduceCurrentWaybillCodeList.isEmpty()) {
            List<ChargeBillRefundExportVO> refundCurrentAmountList = costDetailMapper.selectRefundAmountListNew(reduceCurrentWaybillCodeList);
            List<ChargeBillRefundExportVO> currentAmountList = getChargeBillRefundExportLastTotal(baseAgent, refundCurrentAmountList,reduceCurrentBaseBalanceList);
            vo.setRefundCurrentAmountList(currentAmountList);
        }
        // 非当期退款数据
        if (!reduceNotCurrentWaybillCodeList.isEmpty()){
            List<ChargeBillRefundExportVO> refundNotCurrentAmountList = costDetailMapper.selectRefundAmountListNew(reduceNotCurrentWaybillCodeList);
            List<ChargeBillRefundExportVO> notCurrentAmountList = getChargeBillRefundExportLastTotal(baseAgent, refundNotCurrentAmountList,reduceNotCurrentBaseBalanceList);
            vo.setRefundNotCurrentAmountList(notCurrentAmountList);
        }

        //未来航班金额
        BigDecimal futureFlightCostSum = BigDecimal.ZERO;
        List<ChargeBilExportFutureFlightVO> futureFlightList = new ArrayList<>();
//        List<ChargeBilExportFutureFlightVO> futureList = costDetailMapper.selectFutureFlightItem(query, 0, 1,null);
        List<String> payBalanceWaybillCodeList = list.stream()
                .filter(balance -> "运单支付".equals(balance.getRemark()))
                .filter(balance -> !isCancelPay(balance))
                .map(BaseBalance::getWaybillCode).distinct()
                .collect(Collectors.toList());

        Map<String, List<BaseBalance>> payBalanceWaybillCodeMap = list.stream()
                .filter(balance -> "运单支付".equals(balance.getRemark()))
                .filter(balance -> !isCancelPay(balance))
                .collect(Collectors.groupingBy(BaseBalance::getWaybillCode));

        List<ChargeBilExportFutureFlightVO> futureList = costDetailMapper.selectFutureFlightItem(query, null, null,payBalanceWaybillCodeList);
        AtomicInteger idx = new AtomicInteger(1);
        if(!CollectionUtils.isEmpty(futureList)){
            Map<String, List<ChargeBilExportFutureFlightVO>> voList = futureList.stream()
                    .collect(Collectors.groupingBy(ChargeBilExportFutureFlightVO::getWaybillCode));
            for (List<ChargeBilExportFutureFlightVO> value : voList.values()) {
                ChargeBilExportFutureFlightVO chargeBilExportVO = value.get(0);
                List<BaseBalance> baseBalances = payBalanceWaybillCodeMap.get(chargeBilExportVO.getWaybillCode());
                for(BaseBalance balanceVo : baseBalances){
                    ChargeBilExportFutureFlightVO chargeBilExportFutureFlightVO = new ChargeBilExportFutureFlightVO();
                    BeanUtils.copyProperties(chargeBilExportVO,chargeBilExportFutureFlightVO);
                    chargeBilExportFutureFlightVO.setIdx(idx.getAndIncrement());
                    //根据费用名称设置对应的值
                    value.forEach(val -> setFee(chargeBilExportVO, val.getChargeAbb(), val.getTotalCharge()));
                    chargeBilExportFutureFlightVO.setCountCharge(balanceVo.getTradeMoney());
                    futureFlightCostSum = futureFlightCostSum.add(balanceVo.getTradeMoney());
                    futureFlightList.add(chargeBilExportFutureFlightVO);
                }
            }
        }
        if(futureFlightList.size() > 0){
            ChargeBilExportFutureFlightVO lastTotal = new ChargeBilExportFutureFlightVO();
            lastTotal.setIdx(idx.getAndIncrement());
            lastTotal.setWaybillCode("合计");
            lastTotal.setQuantity(futureFlightList.stream()
                    .map(baseBalance -> Optional.ofNullable(baseBalance.getQuantity()).orElse(0))
                    .reduce(0, Integer::sum));

            lastTotal.setChargeWeight(getSumFee(futureFlightList, ChargeBilExportFutureFlightVO::getChargeWeight));

            BigDecimal processingFee = getSumFee(futureFlightList, ChargeBilExportFutureFlightVO::getProcessingFee);
            BigDecimal refrigerationFee = getSumFee(futureFlightList, ChargeBilExportFutureFlightVO::getRefrigerationFee);
            BigDecimal handlingFee = getSumFee(futureFlightList, ChargeBilExportFutureFlightVO::getHandlingFee);
            BigDecimal cableCharge = getSumFee(futureFlightList, ChargeBilExportFutureFlightVO::getCableCharge);
            BigDecimal diffServiceCharge = getSumFee(futureFlightList, ChargeBilExportFutureFlightVO::getDiffServiceCharge);
            BigDecimal countCharge = getSumFee(futureFlightList, ChargeBilExportFutureFlightVO::getCountCharge);

            lastTotal.setProcessingFee(processingFee);
            lastTotal.setRefrigerationFee(refrigerationFee);
            lastTotal.setHandlingFee(handlingFee);
            lastTotal.setCableCharge(cableCharge);
            lastTotal.setDiffServiceCharge(diffServiceCharge);
            lastTotal.setCountCharge(countCharge);
            futureFlightList.add(lastTotal);
        }
        vo.setFutureFlightAmountList(futureFlightList);
        vo.setFutureFlightAmount(futureFlightCostSum);
    }

    private Boolean isCancelPay(BaseBalance balance){
        Integer cancelPayCount = baseBalanceMapper.selectCount(new QueryWrapper<BaseBalance>()
                .eq("waybill_code", balance.getWaybillCode())
                .eq("remark", "取消支付退款"));
        Integer payCount = baseBalanceMapper.selectCount(new QueryWrapper<BaseBalance>()
                .eq("waybill_code", balance.getWaybillCode())
                .eq("remark", "运单支付"));
        return cancelPayCount >= payCount;
    }

    @NotNull
    private List<ChargeBillRefundExportVO> getChargeBillRefundExportLastTotal(BaseAgent baseAgent, List<ChargeBillRefundExportVO> refundCurrentAmountList,
                                                                              List<BaseBalance> balancesList) {
        Map<String, List<BaseBalance>> balancesMap = balancesList.stream().collect(Collectors.groupingBy(BaseBalance::getWaybillCode));
        List<ChargeBillRefundExportVO> currentAmountList = new ArrayList<>();
        AtomicInteger idx = new AtomicInteger(1);
        if (!CollectionUtils.isEmpty(refundCurrentAmountList)) {
            Map<String, List<ChargeBillRefundExportVO>> collect = refundCurrentAmountList.stream().collect(Collectors.groupingBy(ChargeBillRefundExportVO::getWaybillCode));
            for (List<ChargeBillRefundExportVO> stringListEntry : collect.values()) {
                ChargeBillRefundExportVO chargeBillRefundExportVO = stringListEntry.get(0);
                List<BaseBalance> baseBalances = balancesMap.get(chargeBillRefundExportVO.getWaybillCode());
                for (BaseBalance balance : baseBalances) {
                ChargeBillRefundExportVO chargeBillRefundExportVO1 = new ChargeBillRefundExportVO();
                BeanUtils.copyProperties(chargeBillRefundExportVO,chargeBillRefundExportVO1);
//                chargeBillRefundExportVO1.setSettleCharge(settleCharge);
                chargeBillRefundExportVO1.setAgent(baseAgent.getAgent());
                chargeBillRefundExportVO1.setIdx(idx.getAndIncrement());
                setFee(chargeBillRefundExportVO1, chargeBillRefundExportVO1.getChargeAbb(), chargeBillRefundExportVO1.getTotalCharge());
                chargeBillRefundExportVO1.setRefundCharge(balance.getTradeMoney());
                currentAmountList.add(chargeBillRefundExportVO1);
                }
            }
        }


        ChargeBillRefundExportVO lastTotal = new ChargeBillRefundExportVO();
        lastTotal.setWaybillCode("合计");
        lastTotal.setQuantity(currentAmountList.stream()
                .map(baseBalance -> Optional.ofNullable(baseBalance.getQuantity()).orElse(0))
                .reduce(0, Integer::sum));

        lastTotal.setChargeWeight(getSumFee(currentAmountList, ChargeBillRefundExportVO::getChargeWeight));

        BigDecimal processingFee = getSumFee(currentAmountList, ChargeBillRefundExportVO::getProcessingFee);
        BigDecimal refrigerationFee = getSumFee(currentAmountList, ChargeBillRefundExportVO::getRefrigerationFee);
        BigDecimal handlingFee = getSumFee(currentAmountList, ChargeBillRefundExportVO::getHandlingFee);
        BigDecimal cableCharge = getSumFee(currentAmountList, ChargeBillRefundExportVO::getCableCharge);
        BigDecimal diffServiceCharge = getSumFee(currentAmountList, ChargeBillRefundExportVO::getDiffServiceCharge);
        BigDecimal settleCharge = getSumFee(currentAmountList, ChargeBillRefundExportVO::getSettleCharge);
        BigDecimal refundCharge = getSumFee(currentAmountList, ChargeBillRefundExportVO::getRefundCharge);

        lastTotal.setIdx(idx.getAndIncrement());
        lastTotal.setProcessingFee(processingFee);
        lastTotal.setRefrigerationFee(refrigerationFee);
        lastTotal.setHandlingFee(handlingFee);
        lastTotal.setCableCharge(cableCharge);
        lastTotal.setDiffServiceCharge(diffServiceCharge);
        lastTotal.setSettleCharge(settleCharge);
        lastTotal.setRefundCharge(refundCharge);
        currentAmountList.add(lastTotal);
        return currentAmountList;
    }


    @NotNull
    private List<ChargeBilExportNotSettleVO> getChargeBillNotSettleExportLastTotal(List<ChargeBilExportNotSettleVO> refundCurrentAmountList) {
        List<ChargeBilExportNotSettleVO> currentAmountList = new ArrayList<>();
        long idx = 1L;
        if (!CollectionUtils.isEmpty(refundCurrentAmountList)) {
            Map<String, List<ChargeBilExportNotSettleVO>> collect = refundCurrentAmountList.stream().collect(Collectors.groupingBy(ChargeBilExportNotSettleVO::getWaybillCode));
            for (List<ChargeBilExportNotSettleVO> value : collect.values()) {
                ChargeBilExportNotSettleVO chargeBilExportVO = value.get(0);
                chargeBilExportVO.setId(idx++);
                CostDetailSumVo costDetailSumVo = costDetailMapper.selectPayAndSettleSumByWaybillCode(chargeBilExportVO.getWaybillCode());
                chargeBilExportVO.setSettleCharge(costDetailSumVo.getSettleSum());
                chargeBilExportVO.setNotSettleCharge(costDetailSumVo.getPaySum().subtract(costDetailSumVo.getSettleSum()));
                chargeBilExportVO.setId(idx++);
                setFee(chargeBilExportVO, chargeBilExportVO.getChargeAbb(), chargeBilExportVO.getTotalCharge());
                currentAmountList.add(chargeBilExportVO);
            }

//            for (Map.Entry<String, List<ChargeBilExportNotSettleVO>> stringListEntry : collect.entrySet()) {
////                BigDecimal settleCharge = stringListEntry.getValue().stream().map(ChargeBilExportNotSettleVO::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
//                for (ChargeBilExportNotSettleVO chargeBillRefundExportVO : stringListEntry.getValue()) {
////                    chargeBillRefundExportVO.setSettleCharge(settleCharge);
//                    CostDetailSumVo costDetailSumVo = costDetailMapper.selectPayAndSettleSumByWaybillCode(chargeBillRefundExportVO.getWaybillCode());
//                    chargeBillRefundExportVO.setSettleCharge(costDetailSumVo.getSettleSum());
//                    chargeBillRefundExportVO.setNotSettleCharge(costDetailSumVo.getPaySum().subtract(costDetailSumVo.getSettleSum()));
//                    chargeBillRefundExportVO.setId(idx++);
//                    setFee(chargeBillRefundExportVO, chargeBillRefundExportVO.getChargeAbb(), chargeBillRefundExportVO.getTotalCharge());
//                    currentAmountList.add(chargeBillRefundExportVO);
//                }
//            }
            ChargeBilExportNotSettleVO lastTotal = new ChargeBilExportNotSettleVO();
            lastTotal.setWaybillCode("合计");
            lastTotal.setQuantity(currentAmountList.stream()
                    .map(baseBalance -> Optional.ofNullable(baseBalance.getQuantity()).orElse(0))
                    .reduce(0, Integer::sum));

            lastTotal.setChargeWeight(getSumFee(currentAmountList, ChargeBilExportNotSettleVO::getChargeWeight));

            BigDecimal processingFee = getSumFee(currentAmountList, ChargeBilExportNotSettleVO::getProcessingFee);
            BigDecimal refrigerationFee = getSumFee(currentAmountList, ChargeBilExportNotSettleVO::getRefrigerationFee);
            BigDecimal handlingFee = getSumFee(currentAmountList, ChargeBilExportNotSettleVO::getHandlingFee);
            BigDecimal cableCharge = getSumFee(currentAmountList, ChargeBilExportNotSettleVO::getCableCharge);
            BigDecimal diffServiceCharge = getSumFee(currentAmountList, ChargeBilExportNotSettleVO::getDiffServiceCharge);
            BigDecimal notSettleCharge = getSumFee(currentAmountList, ChargeBilExportNotSettleVO::getNotSettleCharge);
            BigDecimal settleCharge = getSumFee(currentAmountList, ChargeBilExportNotSettleVO::getSettleCharge);


            lastTotal.setId(idx);
            lastTotal.setProcessingFee(processingFee);
            lastTotal.setRefrigerationFee(refrigerationFee);
            lastTotal.setHandlingFee(handlingFee);
            lastTotal.setCableCharge(cableCharge);
            lastTotal.setDiffServiceCharge(diffServiceCharge);
            lastTotal.setNotSettleCharge(notSettleCharge);
            lastTotal.setSettleCharge(settleCharge);
            currentAmountList.add(lastTotal);
        }
        return currentAmountList;
    }

    private List<ChargeBilExportNotSettleVO> getLastTotal(List<ChargeBilExportNotSettleVO> notSettleVoList){
        if(notSettleVoList.size() > 0){
            ChargeBilExportNotSettleVO lastTotal = new ChargeBilExportNotSettleVO();
            lastTotal.setWaybillCode("合计");
            lastTotal.setQuantity(notSettleVoList.stream()
                    .map(baseBalance -> Optional.ofNullable(baseBalance.getQuantity()).orElse(0))
                    .reduce(0, Integer::sum));

            lastTotal.setChargeWeight(getSumFee(notSettleVoList, ChargeBilExportNotSettleVO::getChargeWeight));

            BigDecimal processingFee = getSumFee(notSettleVoList, ChargeBilExportNotSettleVO::getProcessingFee);
            BigDecimal refrigerationFee = getSumFee(notSettleVoList, ChargeBilExportNotSettleVO::getRefrigerationFee);
            BigDecimal handlingFee = getSumFee(notSettleVoList, ChargeBilExportNotSettleVO::getHandlingFee);
            BigDecimal cableCharge = getSumFee(notSettleVoList, ChargeBilExportNotSettleVO::getCableCharge);
            BigDecimal diffServiceCharge = getSumFee(notSettleVoList, ChargeBilExportNotSettleVO::getDiffServiceCharge);
            BigDecimal notSettleCharge = getSumFee(notSettleVoList, ChargeBilExportNotSettleVO::getNotSettleCharge);
            BigDecimal settleCharge = getSumFee(notSettleVoList, ChargeBilExportNotSettleVO::getSettleCharge);

            lastTotal.setProcessingFee(processingFee);
            lastTotal.setRefrigerationFee(refrigerationFee);
            lastTotal.setHandlingFee(handlingFee);
            lastTotal.setCableCharge(cableCharge);
            lastTotal.setDiffServiceCharge(diffServiceCharge);
            lastTotal.setNotSettleCharge(notSettleCharge);
            lastTotal.setSettleCharge(settleCharge);
            notSettleVoList.add(lastTotal);

            for (int i = 0; i < notSettleVoList.size(); i++) {
                notSettleVoList.get(i).setId((long)(i + 1));
            }
        }
        return notSettleVoList;
    }

    private static String formatDateTime(LocalDateTime dateTime) {
        return LocalDateTimeUtil.format(dateTime, "yyyy.MM.dd");
    }

//    private BigDecimal getSumFee(List<ChargeBilExportVO> dataList, Function<ChargeBilExportVO, BigDecimal> getProcessingFee) {
//        return dataList.stream()
//                .map(v -> Optional.ofNullable(getProcessingFee.apply(v)).orElse(BigDecimal.ZERO))
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//    }

    private BigDecimal getSumFeeAgent(List<ChargeBillExportVoAgent> dataList, Function<ChargeBillExportVoAgent, BigDecimal> getProcessingFee) {
        return dataList.stream()
                .map(v -> Optional.ofNullable(getProcessingFee.apply(v)).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BigDecimal getSumFeeNotSettle(List<ChargeBilExportNotSettleVO> dataList, Function<ChargeBilExportNotSettleVO, BigDecimal> getProcessingFee) {
        return dataList.stream()
                .map(v -> Optional.ofNullable(getProcessingFee.apply(v)).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BigDecimal getSumFeeNotSettleAgent(List<ChargeBillExportNotSettleVoAgent> dataList, Function<ChargeBillExportNotSettleVoAgent, BigDecimal> getProcessingFee) {
        return dataList.stream()
                .map(v -> Optional.ofNullable(getProcessingFee.apply(v)).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private <T> BigDecimal getSumFee(List<T> dataList, Function<T, BigDecimal> feeExtractor) {
        return dataList.stream()
                .map(v -> Optional.ofNullable(feeExtractor.apply(v)).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private <T extends FeeSettable> void setFee(T vo, String chargeAbb, BigDecimal totalCharge) {
        switch (chargeAbb) {
            case "处置费":
                vo.setProcessingFee(Optional.ofNullable(vo.getProcessingFee()).orElse(BigDecimal.ZERO).add(totalCharge));
                break;
            case "冷藏费":
                vo.setRefrigerationFee(Optional.ofNullable(vo.getRefrigerationFee()).orElse(BigDecimal.ZERO).add(totalCharge));
                break;
            case "搬运费":
                vo.setHandlingFee(Optional.ofNullable(vo.getHandlingFee()).orElse(BigDecimal.ZERO).add(totalCharge));
                break;
            case "电报费":
                vo.setCableCharge(Optional.ofNullable(vo.getCableCharge()).orElse(BigDecimal.ZERO).add(totalCharge));
                break;
            case "叉车费":
                vo.setForkliftCharge(Optional.ofNullable(vo.getForkliftCharge()).orElse(BigDecimal.ZERO).add(totalCharge));
                break;
            case "差异化服务费(跨航司)":
                vo.setDiffServiceCharge(Optional.ofNullable(vo.getDiffServiceCharge()).orElse(BigDecimal.ZERO).add(totalCharge));
                break;
        }
    }

    @Override
    public List<ChargeSettleWaybillVo> chargeSettleExport(ChargeQuery query) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<ChargeSettleWaybillVo> chargeSettleWaybillVos = new ArrayList<>();
        List<ChargeWaybillVo> chargeWaybillVos = importData(query);
        if(!CollectionUtils.isEmpty(chargeWaybillVos)){
            List<ChargeWaybillVo> voList = chargeWaybillVos.stream().filter(e -> !Objects.equals(e.getSwitchBill(),2)).collect(Collectors.toList());
            for(ChargeWaybillVo chargeWaybillVo : voList){
                if("合计".equals(chargeWaybillVo.getDepWeightStr())){
                    continue;
                }
                ChargeSettleWaybillVo chargeSettleWaybillVo = new ChargeSettleWaybillVo();
                BeanUtils.copyProperties(chargeWaybillVo,chargeSettleWaybillVo);

                List<ArrItemVo> itemVos = costDetailMapper.selectItemVoBySettleTime(chargeWaybillVo.getWaybillCode(),query);
                if (!CollectionUtils.isEmpty(itemVos)){
                    Map<String, List<ArrItemVo>> collect = itemVos.stream().filter(e->e.getChargeAbb() != null).collect(Collectors.groupingBy(ArrItemVo::getChargeAbb));
                    for (Map.Entry<String, List<ArrItemVo>> stringListEntry : collect.entrySet()) {
                        BigDecimal reduce = stringListEntry.getValue().stream()
                                .map(hzArrItem -> hzArrItem.getEditCharge() != null ? hzArrItem.getEditCharge() : hzArrItem.getTotalCharge())
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        switch (stringListEntry.getKey()){
                            case "处置费":
                                BigDecimal costSum = stringListEntry.getValue().stream()
                                        .filter(e-> e.getFlightId() > 5L)
                                        .map(hzArrItem -> hzArrItem.getEditCharge() != null ? hzArrItem.getEditCharge() : hzArrItem.getTotalCharge())
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                                chargeSettleWaybillVo.setProcessingFee(costSum);
                                break;
                            case "冷藏费":
                                chargeSettleWaybillVo.setRefrigerationFee(reduce);
                                break;
                            case "搬运费":
                                chargeSettleWaybillVo.setHandlingFee(reduce);
                                break;
                            case "电报费":
                                BigDecimal cost = stringListEntry.getValue().stream()
                                        .filter(e-> e.getFlightId() != 4L && e.getFlightId() != 3L)
                                        .map(hzArrItem -> hzArrItem.getEditCharge() != null ? hzArrItem.getEditCharge() : hzArrItem.getTotalCharge())
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                                chargeSettleWaybillVo.setCableCharge(cost);
                                break;
                            case "叉车费":
                                chargeSettleWaybillVo.setForkliftCharge(reduce);
                                break;
                            case "差异化服务费(跨航司)":
                                chargeSettleWaybillVo.setDiffServiceCharge(reduce);
                                break;
                            default:
                                System.out.println("不展示当前收费项目");
                                break;
                        }
                    }
                }

                AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                        .eq("waybill_code", chargeWaybillVo.getWaybillCode())
                        .eq("type", "DEP"));
                BaseAgent baseAgent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>()
                        .eq("dept_id", airWaybill.getDeptId()));
                if(baseAgent!=null){
                    switch (baseAgent.getPayMethod()){
                        case 0:
                            chargeSettleWaybillVo.setSettleMethod("预授权支付");
                            break;
                        case 1:
                            chargeSettleWaybillVo.setSettleMethod("余额支付");
                            break;
                        case 2:
                            chargeSettleWaybillVo.setSettleMethod("线下结算");
                            break;
                        default:
                            chargeSettleWaybillVo.setSettleMethod("未知方式");
                            break;
                     }
                }
                BigDecimal paySum = new BigDecimal(0);
                BigDecimal settleSum = new BigDecimal(0);
                List<CostDetail> details = costDetailMapper.selectPayOrSettleList(airWaybill.getWaybillCode(),0,1, airWaybill.getDeptId());
                List<CostDetail> detailList = costDetailMapper.selectPayOrSettleList(airWaybill.getWaybillCode(),1,1, airWaybill.getDeptId());
                if (!CollectionUtils.isEmpty(details)){
                    paySum = details.stream().map(detail -> detail.getEditCharge() != null ? detail.getEditCharge() : detail.getTotalCharge()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    chargeSettleWaybillVo.setPayMoney(paySum);
                }
                if (!CollectionUtils.isEmpty(detailList)){
                    settleSum = detailList.stream().filter(e->e.getFlightId() != 1L && e.getFlightId() != 3L && e.getFlightId() != 4L).map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                }
                List<ChargeSettleDetailVo> detailVos = new ArrayList<>();
                List<CostDetail> settleList = costDetailMapper.selectPayOrSettleListBySettleTime(airWaybill.getWaybillCode(),1,1, airWaybill.getDeptId(),query);
                if (!CollectionUtils.isEmpty(settleList)){
                    Map<String, List<CostDetail>> collect = settleList.stream().collect(Collectors.groupingBy(e->e.getFlightId() + "," + (e.getCreateTime() == null ? " " : format.format(e.getCreateTime()))));
                    for (Map.Entry<String, List<CostDetail>> longListEntry : collect.entrySet()) {
                        String[] split = longListEntry.getKey().split(",");
                        ChargeSettleDetailVo chargeSettleDetailVo = new ChargeSettleDetailVo();
                        if ("1".equals(split[0])){
                            continue;
                        }else {
                            FlightInfo info = flightInfoMapper.selectById(split[0]);
                            if (info != null){
                                chargeSettleDetailVo.setSettleFlightNo(info.getAirWays() + info.getFlightNo());
                                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                                chargeSettleDetailVo.setSettleFlightDate(dateFormat.format(info.getExecDate()));
                            }
                        }
                        BigDecimal reduce = longListEntry.getValue().stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                        chargeSettleDetailVo.setSettleCost(reduce);

                        BigDecimal settleWeight = longListEntry.getValue().stream().map(CostDetail::getSettleDepWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                        chargeSettleDetailVo.setSettleDepWeight(settleWeight);

                        BigDecimal quantity = longListEntry.getValue().stream().map(CostDetail::getQuantity).filter(Objects::nonNull).map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add);
                        chargeSettleDetailVo.setQuantity(quantity.toString());

                        int settleQuantity = longListEntry.getValue().stream().filter(e->e.getSettleDepQuantity() != null).mapToInt(CostDetail::getSettleDepQuantity).sum();
                        chargeSettleDetailVo.setSettleDepQuantity(settleQuantity);

                        try {
                            if (" ".equals(split[1])){
                                chargeSettleDetailVo.setSettleTime(null);
                            }else {
                                chargeSettleDetailVo.setSettleTime(format.parse(split[1]));
                            }
                        } catch (ParseException e) {
                            throw new RuntimeException(e);
                        }
                        detailVos.add(chargeSettleDetailVo);
                    }
                }
                BigDecimal newSettleSum = settleSum == null ? new BigDecimal(0) : settleSum;
                BigDecimal newPaySum = paySum == null ? new BigDecimal(0) : paySum;
                BigDecimal subtract = newPaySum.subtract(newSettleSum);
                BigDecimal bigDecimal = subtract.compareTo(new BigDecimal(0)) < 0 ? new BigDecimal(0) : subtract;
                if ("INVALID".equals(airWaybill.getStatus())){
                    chargeSettleWaybillVo.setUnSettleMoney(new BigDecimal(0));
                }else {
                    chargeSettleWaybillVo.setUnSettleMoney(bigDecimal);
                }
                BigDecimal bigDecimal1 = new BigDecimal(0);
                if (!CollectionUtils.isEmpty(detailVos)){
                    BigDecimal reduce = detailVos.stream().map(ChargeSettleDetailVo::getSettleCost).reduce(BigDecimal.ZERO, BigDecimal::add);
                    bigDecimal1 = reduce == null ? new BigDecimal(0) : reduce;
                    List<ChargeSettleDetailVo> collect = detailVos.stream().filter(e->StringUtils.isNotEmpty(e.getSettleFlightNo())).sorted(Comparator.comparing(ChargeSettleDetailVo::getSettleTime)).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(collect)){
                        int sum = collect.stream().mapToInt(ChargeSettleDetailVo::getSettleDepQuantity).sum();
                        chargeSettleWaybillVo.setSettleQuantitySum(sum);
                        BigDecimal reduce1 = collect.stream().map(ChargeSettleDetailVo::getQuantity).map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add);
                        chargeSettleWaybillVo.setSettleWeightSum(reduce1);
                        chargeSettleWaybillVo.setDetailVos(collect);
                    }
                }
                if ("INVALID".equals(airWaybill.getStatus())){
                    chargeSettleWaybillVo.setSettleSum(new BigDecimal(0));
                }else {
                    chargeSettleWaybillVo.setSettleSum(bigDecimal1);
                }
                chargeSettleWaybillVos.add(chargeSettleWaybillVo);
            }
        }
        return chargeSettleWaybillVos;
    }

    private List<ChargeBilExportNotSettleVO> getWaybillCodeList(List<ChargeBilExportNotSettleVO> list){
        int idx = 1;
        List<ChargeBilExportNotSettleVO> returnList = new ArrayList<>();
        Map<String, List<ChargeBilExportNotSettleVO>> voList = list.stream()
                .collect(Collectors.groupingBy(ChargeBilExportNotSettleVO::getWaybillCode));
        for (List<ChargeBilExportNotSettleVO> value : voList.values()) {
            ChargeBilExportNotSettleVO chargeBilExportVO = value.get(0);
            chargeBilExportVO.setId((long) idx++);
            //根据费用名称设置对应的值
            value.forEach(val -> setFee(chargeBilExportVO, val.getChargeAbb(), val.getTotalCharge()));
            returnList.add(chargeBilExportVO);
        }
        return returnList;
    }

    private List<ChargeBilExportNotSettleVO> getWaybillCodeListNew(List<ChargeBilExportNotSettleVO> list){
        int idx = 1;
        List<ChargeBilExportNotSettleVO> returnList = new ArrayList<>();
        Map<String, List<ChargeBilExportNotSettleVO>> voList = list.stream()
                .collect(Collectors.groupingBy(ChargeBilExportNotSettleVO::getWaybillCode));
        for (List<ChargeBilExportNotSettleVO> value : voList.values()) {
            ChargeBilExportNotSettleVO chargeBilExportVO = value.get(0);
            chargeBilExportVO.setId((long) idx++);
            //根据费用名称设置对应的值
            chargeBilExportVO.setNotSettleCharge(chargeBilExportVO.getTotalCharge());
            value.forEach(val -> setFee(chargeBilExportVO, val.getChargeAbb(), val.getTotalCharge()));
            returnList.add(chargeBilExportVO);
        }
        return returnList;
    }

    private List<ChargeBillExportNotSettleVoAgent> getWaybillCodeListAgent(List<ChargeBillExportNotSettleVoAgent> list){
        int idx = 1;
        List<ChargeBillExportNotSettleVoAgent> returnList = new ArrayList<>();
        Map<String, List<ChargeBillExportNotSettleVoAgent>> voList = list.stream()
                .collect(Collectors.groupingBy(ChargeBillExportNotSettleVoAgent::getWaybillCode));
        for (List<ChargeBillExportNotSettleVoAgent> value : voList.values()) {
            ChargeBillExportNotSettleVoAgent chargeBilExportVO = value.get(0);
            chargeBilExportVO.setId((long) idx++);
            //根据费用名称设置对应的值
            value.forEach(val -> setFee(chargeBilExportVO, val.getChargeAbb(), val.getTotalCharge()));
            returnList.add(chargeBilExportVO);
        }
        return returnList;
    }

    private BigDecimal[] notSettleResult(String waybillCode){
//        List<CostDetail> payList = costDetailMapper.selectList(new QueryWrapper<CostDetail>()
//                .eq("waybill_code", waybillCode)
//                .eq("is_del", 0)
//                .eq("type", 0)
//                .eq("is_settle", 1));
//        List<CostDetail> settleList = costDetailMapper.selectList(new QueryWrapper<CostDetail>()
//                .eq("waybill_code", waybillCode)
//                .eq("is_del", 0)
//                .eq("type", 1));
//        BigDecimal paySum = payList.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
//        BigDecimal settleSum = settleList.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
        CostDetailSumVo costDetailSumVo = costDetailMapper.selectPayAndSettleSumByWaybillCode(waybillCode);
        BigDecimal paySum = costDetailSumVo.getPaySum() == null ? BigDecimal.ZERO : costDetailSumVo.getPaySum();
        BigDecimal settleSum = costDetailSumVo.getSettleSum() == null ? BigDecimal.ZERO : costDetailSumVo.getSettleSum();
        return new BigDecimal[]{paySum.subtract(settleSum),paySum};
    }

    private BigDecimal[] notSettleResultRefund(String waybillCode,LocalDateTime endTime){
        CostDetailSumVo costDetailSumVo = costDetailMapper.selectPayAndSettleSumByWaybillCodeRefund(waybillCode,endTime);
        if (costDetailSumVo == null){
            return new BigDecimal[]{BigDecimal.ZERO,BigDecimal.ZERO};
        }
        BigDecimal paySum = costDetailSumVo.getPaySum() == null ? BigDecimal.ZERO : costDetailSumVo.getPaySum();
        BigDecimal settleSum = costDetailSumVo.getSettleSum() == null ? BigDecimal.ZERO : costDetailSumVo.getSettleSum();
        return new BigDecimal[]{paySum.subtract(settleSum),paySum,settleSum};
    }




    private void setStatus1(AirWaybill mawb, BigDecimal costSum, WaybillFee waybillFee, BaseAgent agent, BigDecimal deduct) {
        BigDecimal refund = new BigDecimal(0);
        if (agent != null) {
            if (agent.getSettleMethod() == 1) {
                BigDecimal balance = agent.getBalance() == null ? new BigDecimal(0) : agent.getBalance();
                BigDecimal subtract = balance.subtract(deduct);
                if (subtract.compareTo(new BigDecimal(0)) < 0) {
                    throw new CustomException("当前代理人余额不足");
                } else {
                    agent.setBalance(subtract);
                    baseAgentMapper.updateBaseAgent(agent);
                    BaseBalance baseBalance = new BaseBalance();
                    baseBalance.setAgentId(agent.getId());
                    baseBalance.setBalance(agent.getBalance());
                    baseBalance.setType("减少余额");
                    baseBalance.setCreateTime(new Date());
                    baseBalance.setCreateBy("系统");
                    // todo 流水号需从银联支付接口获取
                    //baseBalance.setSerialNo();
                    baseBalance.setTradeMoney(deduct);
                    baseBalance.setWaybillCode(mawb.getWaybillCode());
                    baseBalance.setRemark("运单支付");
                    baseBalanceMapper.insertBaseBalance(baseBalance);
                    updateStatus(mawb, costSum, waybillFee, 6, refund);
                }
            } else if (agent.getSettleMethod() == 0) {
                updateStatus(mawb, costSum, waybillFee, 5, refund);
            } else {
                if (agent.getPayMethod() == 0) {
                    updateStatus(mawb, costSum, waybillFee, 7, refund);
                } else {
                    updateStatus(mawb, costSum, waybillFee, 8, refund);
                }
            }
        } else {
            updateStatus(mawb, costSum, waybillFee, 8, refund);
        }
    }

    private void setStatus(AirWaybill mawb, BigDecimal costSum, WaybillFee waybillFee, BaseAgent agent) {
        BigDecimal refund = new BigDecimal(0);
        if (agent != null) {
            if (agent.getSettleMethod() == 1) {
                updateStatus(mawb, costSum, waybillFee, 6, refund);
            } else if (agent.getSettleMethod() == 0) {
                updateStatus(mawb, costSum, waybillFee, 5, refund);
            } else {
                if (agent.getPayMethod() == 0) {
                    updateStatus(mawb, costSum, waybillFee, 7, refund);
                } else {
                    updateStatus(mawb, costSum, waybillFee, 8, refund);
                }
            }
        } else {
            updateStatus(mawb, costSum, waybillFee, 8, refund);
        }
    }

    private void updateStatus(AirWaybill airWaybill, BigDecimal costSum, WaybillFee waybillFee, Integer payStatus, BigDecimal refund) {
        if (waybillFee != null) {
            waybillFee.setSettleTime(new Date());
            waybillFee.setSettleMoney(costSum);
            waybillFee.setRefund(refund);
            waybillFee.setStatus(1);
            feeMapper.updateById(waybillFee);
        } else {
            WaybillFee fee = new WaybillFee();
            fee.setSettleMoney(costSum);
            fee.setSettleTime(new Date());
            fee.setRefund(refund);
            fee.setWaybillCode(airWaybill.getWaybillCode());
            fee.setDeptId(airWaybill.getDeptId());
            fee.setStatus(1);
            fee.setType("DEP");
            feeMapper.insert(fee);
        }
        airWaybill.setPayStatus(payStatus);
        airWaybill.setRefund(refund);
        airWaybill.setStatus("been_settle");
        airWaybill.setSettleTime(new Date());
        airWaybillMapper.updateById(airWaybill);
    }

    private int isCargoCodeMatch(HzChargeIrRelation hzChargeRule, String waybillCargoCode) {
        if (StringUtils.isEmpty(hzChargeRule.getCargoName())) {
            return 1;
        }else if (StringUtils.isEmpty(hzChargeRule.getCategory())){
            if (hzChargeRule.getCargoName().contains(waybillCargoCode)){
                return 1;
            }
            return 0;
        }
        List<IrRelationVo> relationVos = Arrays.stream(hzChargeRule.getCargoName().split(","))
                .map(cargoCodeMapper::selectIrByName)
                .collect(Collectors.toList());

        List<IrRelationVo> cargoNames = Arrays.stream(hzChargeRule.getCategory().split(","))
                .flatMap(category -> cargoCodeMapper.selectListByCategory(Collections.singletonList(category)).stream())
                .collect(Collectors.toList());

        Map<String, List<IrRelationVo>> relationVosMap = relationVos.stream()
                .collect(Collectors.groupingBy(IrRelationVo::getCategoryCode));

        List<IrRelationVo> combinedList = new ArrayList<>();
        Set<String> addedCodes = new HashSet<>();
        for (IrRelationVo vo : cargoNames) {
            String code = vo.getCategoryCode();
            if (relationVosMap.containsKey(code) && !addedCodes.contains(code)) {
                combinedList.addAll(relationVosMap.get(code));
                addedCodes.add(code);
            } else if (!addedCodes.contains(code)) {
                combinedList.add(vo);
            }
        }
        List<String> collect1 = combinedList.stream().map(IrRelationVo::getCode).collect(Collectors.toList());
        if (collect1.stream().anyMatch(cargoCode -> cargoCode.equals(waybillCargoCode))) {
            String category = hzChargeRule.getCategory();
            StringBuilder stringBuffer = new StringBuilder();
            if(category != null){
                String[] split = category.split(",");
                for (String categoryCode:split) {
                    List<BaseCargoCode> cargoCodeList = cargoCodeMapper.selectList(new QueryWrapper<>(new BaseCargoCode())
                            .eq("category_code", categoryCode)
                            .eq("is_del",0));
                    if(cargoCodeList!=null && cargoCodeList.size() > 0){
                        for (BaseCargoCode e:cargoCodeList) {
                            stringBuffer.append(e.getCode());
                        }
                    }
                }
            }else{
                return 1;
            }
            if (stringBuffer.toString().contains(waybillCargoCode)) {
                return 2;
            } else {
                return 1;
            }
        }
        return 0;
    }

    private static final Map<String,String> WAYBILL_STATUS = new HashMap<>();
    static {
        WAYBILL_STATUS.put("staging","暂存");
        WAYBILL_STATUS.put("been_sent","已发送");
        WAYBILL_STATUS.put("pre_pay","预授权支付");
        WAYBILL_STATUS.put("put_in","货站入库");
        WAYBILL_STATUS.put("been_pre","已预配");
        WAYBILL_STATUS.put("been_out","已出库");
        WAYBILL_STATUS.put("been_dep","已出港");
        WAYBILL_STATUS.put("pull_down","临时拉下");
        WAYBILL_STATUS.put("been_return","已退货");
        WAYBILL_STATUS.put("INVALID","已作废");
        WAYBILL_STATUS.put("been_settle","已结算");
    }

    private static final Map<Integer,String> PAY_STATUS = new HashMap<>();
    static {
        PAY_STATUS.put(0,"未支付");
        PAY_STATUS.put(1,"已支付 (预授权支付)");
        PAY_STATUS.put(2,"已支付 (余额支付)");
        PAY_STATUS.put(3,"已支付 (线下结算-现金)");
        PAY_STATUS.put(4,"已支付 (线下结算-月结)");
        PAY_STATUS.put(5,"已结算 (预授权支付)");
        PAY_STATUS.put(6,"已结算 (余额支付)");
        PAY_STATUS.put(7,"已结算 (线下结算_月结)");
        PAY_STATUS.put(8,"已结算 (线下结算_现金)");
        PAY_STATUS.put(9,"已退款 (预授权支付)");
        PAY_STATUS.put(10,"已退款 (余额支付)");
        PAY_STATUS.put(11,"已退款 (线下结算_月结)");
        PAY_STATUS.put(12,"已结算 (线下结算_现金)");
        PAY_STATUS.put(13,"余额不足");
        PAY_STATUS.put(14,"取消支付");
    }

    @Override
    public BillExportVoFinance selectBillFinance(BillExportQuery query){
        if (query.getFlightEndTime() == null || query.getFlightStartTime() == null){
            throw new CustomException("请传入航班日期");
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startTime = query.getFlightStartTime();
        LocalDateTime endTime = query.getFlightEndTime();

        BaseAgent baseAgent = baseAgentMapper.selectOne(Wrappers.lambdaQuery(BaseAgent.class).eq(BaseAgent::getAgent, query.getAgentCode().get(0)));
        if (baseAgent == null) {
            throw new CustomException("代理人不存在");
        }
        BillExportVoFinance finance = new BillExportVoFinance();
        // 查询当期配载运单
//        List<Long> flightIdList = flightInfoMapper.selectFlightIds(query);
        List<FlightLoadWaybillVO> flightLoadWaybillVos = flightInfoMapper.selectFlightWaybills(query);
        //本期出港已结算
        setSettledAmountList(baseAgent.getDeptId(),flightLoadWaybillVos,finance);
        if (startTime == null || endTime == null) {
            finance.setSettlementCycle("---");
            finance.setTitle(query.getAgentCode().get(0) + "费用清单");
        } else {
            finance.setTitle(query.getAgentCode().get(0) + startTime.getMonthValue() + "-" + endTime.getMonthValue() + "月费用清单");
            finance.setSettlementCycle(formatDateTime(startTime) + "-" + formatDateTime(endTime));
        }
        if(baseAgent.getSettleMethod() == 1){
            query.setAgent(query.getAgentCode().get(0));
            //-----------------------------------表头的数据------------------------------------------
            LambdaQueryWrapper<BaseBalance> wrapper = Wrappers.lambdaQuery(BaseBalance.class)
                    .eq(BaseBalance::getAgentId, baseAgent.getId())
                    .gt(BaseBalance::getCreateTime, startTime)
                    .lt(BaseBalance::getCreateTime, endTime)
                    .orderByDesc(BaseBalance::getCreateTime)
                    .orderByDesc(BaseBalance::getId);
            List<BaseBalance> list = baseBalanceMapper.selectList(wrapper);
            //本期进港已结算
            setArrAmountFinanceList(list, finance);
            //总未结算
            setUnsettledAmountFinanceList(query, endTime, baseAgent, finance);
            //余额
            setBalanceFinanceData(list,finance,baseAgent);

            finance.setThisTimeFinanceBalance(finance.getNotSettleAmount().add(finance.getThisBalance()));
            //本期财务余额-（本期充值金额 - 本期出港已结算 - 本期进港已结算）
            finance.setLastTimeFinanceBalance(finance.getThisTimeFinanceBalance()
                    .subtract(finance.getRechargeAmount())
                    .add(finance.getDepAmount())
                    .add(finance.getArrAmount()));
            return finance;
        }
        //月结代理人的账单导出，表头只需要“本期出港已结算”、“本期进港已结算”
        //明细:“出港已结算”明细表、“进港已结算”明细表，
        //总未结算明细表不用显示明细,其他总表字段都显示0
        if (baseAgent.getSettleMethod() == 2) {
            List<ChargeBillArrSettleExportVO> arrExports = costDetailMapper.selectArrBillExport(query, baseAgent.getDeptId());
            List<TallyWaybillKey> keys = new ArrayList<>();
            arrExports.stream().filter(waybill -> waybill.getPickUpId() != null && StringUtils.isNotBlank(waybill.getWaybillCode()))
                    .forEach(waybill ->
                            keys.add(new TallyWaybillKey(waybill.getPickUpId(), waybill.getWaybillCode()))
                    );
            Map<TallyWaybillKey, List<ArrItemVo>> itemVoMap = keys.isEmpty() ?
                    Collections.emptyMap() :
                    itemMapper.selectAllItemVos(keys).stream()
                            .collect(Collectors.groupingBy(
                                    item -> new TallyWaybillKey(item.getPickUpId(), item.getWaybillCode())
                            ));
            AtomicInteger idx = new AtomicInteger(1);
            arrExports.forEach(bill -> {
                bill.setIdx(idx.getAndIncrement());
                TallyWaybillKey key = new TallyWaybillKey(bill.getPickUpId(), bill.getWaybillCode());
                List<ArrItemVo> items = itemVoMap.getOrDefault(key, Collections.emptyList());
                if (!items.isEmpty()) {
                    BigDecimal total = items.stream()
                            .map(item -> Optional.ofNullable(item.getEditCharge()).orElse(item.getTotalCharge()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    bill.setSubtotal(total);
                    items.stream()
                            .collect(Collectors.groupingBy(ArrItemVo::getChargeAbb))
                            .forEach((chargeAbb, chargeItems) -> {
                                BigDecimal sum = chargeItems.stream()
                                        .map(item -> Optional.ofNullable(item.getEditCharge()).orElse(item.getTotalCharge()))
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                                switch (chargeAbb) {
                                    case "处置费":
                                        bill.setProcessingFee(sum);
                                        break;
                                    case "仓储费":
                                        bill.setStorageFee(sum);
                                        break;
                                    case "冷藏费":
                                        bill.setRefrigerationFee(sum);
                                        break;
                                    case "搬运费":
                                        bill.setHandlingFee(sum);
                                        break;
                                    case "电报费":
                                        bill.setCableCharge(sum);
                                        break;
                                    default:
                                        break;
                                }
                            });
                }
            });

            ChargeBillArrSettleExportVO lastTotal = new ChargeBillArrSettleExportVO();
            lastTotal.setWaybillCode("合计");
            lastTotal.setQuantity(arrExports.stream()
                    .map(baseBalance -> Optional.ofNullable(baseBalance.getQuantity()).orElse(0))
                    .reduce(0, Integer::sum));

            lastTotal.setWeight(getSumFee(arrExports, ChargeBillArrSettleExportVO::getWeight));
            lastTotal.setChargeWeight(getSumFee(arrExports, ChargeBillArrSettleExportVO::getChargeWeight));

            BigDecimal processingFee = getSumFee(arrExports, ChargeBillArrSettleExportVO::getProcessingFee);
            BigDecimal storageFee = getSumFee(arrExports, ChargeBillArrSettleExportVO::getStorageFee);
            BigDecimal refrigerationFee = getSumFee(arrExports, ChargeBillArrSettleExportVO::getRefrigerationFee);
            BigDecimal handlingFee = getSumFee(arrExports, ChargeBillArrSettleExportVO::getHandlingFee);
            BigDecimal cableCharge = getSumFee(arrExports, ChargeBillArrSettleExportVO::getCableCharge);
            BigDecimal subtotal = getSumFee(arrExports, ChargeBillArrSettleExportVO::getSubtotal);

            lastTotal.setIdx(idx.getAndIncrement());
            lastTotal.setProcessingFee(processingFee);
            lastTotal.setStorageFee(storageFee);
            lastTotal.setRefrigerationFee(refrigerationFee);
            lastTotal.setHandlingFee(handlingFee);
            lastTotal.setCableCharge(cableCharge);
            lastTotal.setSubtotal(subtotal);
            arrExports.add(lastTotal);

            finance.setArrAmount(subtotal);
            finance.setArrAmountList(arrExports);
            return finance;
        }
        return null;
    }

    private void setArrAmountFinanceList(List<BaseBalance> list, BillExportVoFinance vo) {
        Map<String, BigDecimal> pickUpPayWaybill = list.stream().filter(e -> ObjectUtil.equal("办单支付", e.getRemark()))
                .collect(Collectors.groupingBy(BaseBalance::getWaybillCode,
                        Collectors.mapping(BaseBalance::getTradeMoney, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        Map<String, BigDecimal> pickUpCancelWaybill = list.stream().filter(e -> ObjectUtil.equal("办单作废", e.getRemark()) || ObjectUtil.equal("提货超时", e.getRemark()))
                .collect(Collectors.groupingBy(BaseBalance::getWaybillCode,
                        Collectors.mapping(BaseBalance::getTradeMoney, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        List<String> collect = list.stream().map(BaseBalance::getWaybillCode).distinct().collect(Collectors.toList());
        List<ChargeBillArrSettleExportVO> arrAmountList = pickUpWaybillMapper.selectListByWaybillCode(collect);
        List<String> arrAmountWaybillList = arrAmountList.stream().map(ChargeBillArrSettleExportVO::getWaybillCode).distinct().collect(Collectors.toList());
        Map<String, List<ArrItemVo>> itemVoMap = arrAmountWaybillList.isEmpty() ?
                Collections.emptyMap() :
                itemMapper.selectAllItemVosNew(arrAmountWaybillList,2).stream()
                        .collect(Collectors.groupingBy(ArrItemVo::getWaybillCode));
        AtomicInteger idx = new AtomicInteger(1);
        List<ChargeBillArrSettleExportVO> chargeBillArrSettleExportVOS = new ArrayList<>();
        Map<String, List<ChargeBillArrSettleExportVO>> arrAmountMap = arrAmountList.stream().collect(Collectors.groupingBy(ChargeBillArrSettleExportVO::getWaybillCode));
        for(List<ChargeBillArrSettleExportVO> bills : arrAmountMap.values()){
            ChargeBillArrSettleExportVO bill = bills.get(0);
            bill.setIdx(idx.getAndIncrement());
            List<ArrItemVo> items = itemVoMap.getOrDefault(bill.getWaybillCode(), Collections.emptyList());
            if (!items.isEmpty()) {
                items.stream()
                        .collect(Collectors.groupingBy(ArrItemVo::getChargeAbb))
                        .forEach((chargeAbb, chargeItems) -> {
                            BigDecimal sum = chargeItems.stream()
                                    .map(item -> Optional.ofNullable(item.getEditCharge()).orElse(item.getTotalCharge()))
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            switch (chargeAbb) {
                                case "处置费":
                                    bill.setProcessingFee(sum);
                                    break;
                                case "仓储费":
                                    bill.setStorageFee(sum);
                                    break;
                                case "冷藏费":
                                    bill.setRefrigerationFee(sum);
                                    break;
                                case "搬运费":
                                    bill.setHandlingFee(sum);
                                    break;
                                case "电报费":
                                    bill.setCableCharge(sum);
                                    break;
                                default:
                                    break;
                            }
                        });
            }
            BigDecimal payCost = pickUpPayWaybill.get(bill.getWaybillCode()) != null ? pickUpPayWaybill.get(bill.getWaybillCode()) : BigDecimal.ZERO;
            BigDecimal refundCost = pickUpCancelWaybill.get(bill.getWaybillCode()) != null ? pickUpCancelWaybill.get(bill.getWaybillCode()) : BigDecimal.ZERO;

            BigDecimal subtract = payCost.subtract(refundCost);
            if (subtract.compareTo(BigDecimal.ZERO) > 0){
                bill.setSubtotal(subtract);
                chargeBillArrSettleExportVOS.add(bill);
            }
        }


        ChargeBillArrSettleExportVO lastTotal = new ChargeBillArrSettleExportVO();
        lastTotal.setWaybillCode("合计");
        lastTotal.setQuantity(chargeBillArrSettleExportVOS.stream()
                .map(baseBalance -> Optional.ofNullable(baseBalance.getQuantity()).orElse(0))
                .reduce(0, Integer::sum));

        lastTotal.setChargeWeight(getSumFee(chargeBillArrSettleExportVOS, ChargeBillArrSettleExportVO::getChargeWeight));

        BigDecimal processingFee = getSumFee(chargeBillArrSettleExportVOS, ChargeBillArrSettleExportVO::getProcessingFee);
        BigDecimal storageFee = getSumFee(chargeBillArrSettleExportVOS, ChargeBillArrSettleExportVO::getStorageFee);
        BigDecimal refrigerationFee = getSumFee(chargeBillArrSettleExportVOS, ChargeBillArrSettleExportVO::getRefrigerationFee);
        BigDecimal handlingFee = getSumFee(chargeBillArrSettleExportVOS, ChargeBillArrSettleExportVO::getHandlingFee);
        BigDecimal cableCharge = getSumFee(chargeBillArrSettleExportVOS, ChargeBillArrSettleExportVO::getCableCharge);
        BigDecimal subtotal = getSumFee(chargeBillArrSettleExportVOS, ChargeBillArrSettleExportVO::getSubtotal);

        lastTotal.setIdx(idx.getAndIncrement());
        lastTotal.setProcessingFee(processingFee);
        lastTotal.setStorageFee(storageFee);
        lastTotal.setRefrigerationFee(refrigerationFee);
        lastTotal.setHandlingFee(handlingFee);
        lastTotal.setCableCharge(cableCharge);
        lastTotal.setSubtotal(subtotal);
        chargeBillArrSettleExportVOS.add(lastTotal);

        vo.setArrAmountList(chargeBillArrSettleExportVOS);
        //进港费用计算
        BigDecimal payPickUpSum = list.stream()
                .filter(e -> ObjectUtil.equal("办单支付", e.getRemark()))
                .map(BaseBalance::getTradeMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal cancelPickUpSum = list.stream()
                .filter(e -> ObjectUtil.equal("办单作废", e.getRemark()) || ObjectUtil.equal("提货超时", e.getRemark()))
                .map(BaseBalance::getTradeMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        vo.setArrAmount(payPickUpSum.subtract(cancelPickUpSum));
    }

    private void setUnsettledAmountFinanceList(BillExportQuery query, LocalDateTime endTime, BaseAgent baseAgent,BillExportVoFinance vo){
        List<ChargeBilExportNotSettleVO> dataListNotSettle = new ArrayList<>();
        BigDecimal notSettleSum = BigDecimal.ZERO;
        LocalDateTime startTime = LocalDateTime.of(2025, 1, 7, 0, 0, 0);
        query.setWriteStartTime(startTime);
        query.setWriteEndTime(endTime);
        //1、查询代理人（1.8）到当前截止时间的所有余额明细和结算明细
        //2、将所有余额明细和结算明细，按照运单进行分组，形成Map<运单号，list<余额明细>>余额明细map，Map<运单号，list<结算明细>>结算明细map
        //3、循环余额明细map，计算每个运单的余额明细之和得到运单A明细之和，判断：运单A明细之和>=0说明，全额退款或者取消支付了，就不管了
        // 如果运单A明细之和<0，在求出运单A的结算明细之和，在用运单A预支付金额减去运单A的结算明细之和就得到了运单A总未结算金额，运单A总未结算金额大于0就要在总未结算里面，否则就不出现在总未结算里面
        List<BaseBalance> baseBalances = baseBalanceMapper.selectList(new QueryWrapper<BaseBalance>()
                .eq("agent_id", baseAgent.getId())
                .notLike("remark","办单")
                .gt("create_time", startTime)
                .lt("create_time", endTime));
        Map<String, List<BaseBalance>> balanceMap = baseBalances.stream().filter(e -> StringUtils.isNotEmpty(e.getWaybillCode()))
                .collect(Collectors.groupingBy(BaseBalance::getWaybillCode));
        List<String> waybillCodeList = new ArrayList<>();
        for(List<BaseBalance> waybillBalance : balanceMap.values()){
            String waybillCode = waybillBalance.get(0).getWaybillCode();
            BigDecimal add = waybillBalance.stream().filter(e -> ObjectUtil.equal("增加余额", e.getType()))
                    .map(BaseBalance::getTradeMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal sub = waybillBalance.stream().filter(e -> ObjectUtil.equal("减少余额", e.getType()))
                    .map(BaseBalance::getTradeMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
            if(add.subtract(sub).compareTo(BigDecimal.ZERO) < 0){
                waybillCodeList.add(waybillCode);
            }
        }

        AtomicInteger idx = new AtomicInteger(1);
        for(String waybillCode : waybillCodeList){
            CostDetailSumVo costDetailSumVo = costDetailMapper.selectPayAndSettleSumNew(waybillCode, startTime, endTime);
//            BigDecimal notSettle = costDetailSumVo.getPaySum() != null ? costDetailSumVo.getPaySum() : BigDecimal.ZERO
//                    .subtract(costDetailSumVo.getSettleSum() != null ? costDetailSumVo.getSettleSum() : BigDecimal.ZERO);
            if(costDetailSumVo == null){
                continue;
            }
            BigDecimal notSettle = costDetailSumVo.getPaySum().subtract(costDetailSumVo.getSettleSum());

            BigDecimal notSettleFlight = null;
            CostDetailSumVo costDetailSumVoFlight = costDetailMapper.selectPayAndSettleSumNewByFlightTime(waybillCode, startTime, endTime);
            if(costDetailSumVoFlight != null &&
                    (costDetailSumVoFlight.getPaySum().compareTo(BigDecimal.ZERO) != 0 ||
                    costDetailSumVoFlight.getSettleSum().compareTo(BigDecimal.ZERO) != 0)){
                notSettleFlight = costDetailSumVoFlight.getPaySum().subtract(costDetailSumVoFlight.getSettleSum());
            }
            if(notSettle.compareTo(BigDecimal.ZERO) > 0 && (notSettleFlight == null || notSettleFlight.compareTo(BigDecimal.ZERO) > 0)){
                List<CostDetail> payCancelList = costDetailMapper.selectDetailListByCode(waybillCode,null);
                if(payCancelList.size() > 0){
                    notSettleSum = notSettleSum.add(notSettleFlight == null ? notSettle : notSettleFlight);
                    ChargeBilExportNotSettleVO settleVO = new ChargeBilExportNotSettleVO();
                    settleVO.setIdx(idx.getAndIncrement());
                    settleVO.setWaybillCode(waybillCode);
                    settleVO.setNotSettleCharge(notSettleFlight == null ? notSettle : notSettleFlight);
                    settleVO.setSettleCharge(costDetailSumVo.getSettleSum() != null ? costDetailSumVo.getSettleSum() : BigDecimal.ZERO);
                    settleVO.setQuantity(payCancelList.get(0).getSettleDepQuantity());
                    settleVO.setChargeWeight(payCancelList.get(0).getSettleDepWeight());
                    for (CostDetail costDetail : payCancelList) {
                        setFee(settleVO, costDetail.getChargeAbb(), costDetail.getTotalCharge());
                    }
                    dataListNotSettle.add(settleVO);
                }
            }
        }


        ChargeBilExportNotSettleVO lastTotalNotSettle = new ChargeBilExportNotSettleVO();
        lastTotalNotSettle.setWaybillCode("合计");
        lastTotalNotSettle.setQuantity(dataListNotSettle.stream()
                .map(baseBalance -> Optional.ofNullable(baseBalance.getQuantity()).orElse(0))
                .reduce(0, Integer::sum));

        lastTotalNotSettle.setChargeWeight(getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getChargeWeight));

        BigDecimal processingFeeNotSettle = getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getProcessingFee);
        BigDecimal refrigerationFeeNotSettle = getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getRefrigerationFee);
        BigDecimal handlingFeeNotSettle = getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getHandlingFee);
        BigDecimal cableChargeNotSettle = getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getCableCharge);
        BigDecimal diffServiceChargeNotSettle = getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getDiffServiceCharge);
        BigDecimal settleChargeNotSettle = getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getSettleCharge);
        BigDecimal notSettleChargeNotSettle = getSumFeeNotSettle(dataListNotSettle, ChargeBilExportNotSettleVO::getNotSettleCharge);

        lastTotalNotSettle.setIdx(idx.getAndIncrement());
        lastTotalNotSettle.setProcessingFee(processingFeeNotSettle);
        lastTotalNotSettle.setRefrigerationFee(refrigerationFeeNotSettle);
        lastTotalNotSettle.setHandlingFee(handlingFeeNotSettle);
        lastTotalNotSettle.setCableCharge(cableChargeNotSettle);
        lastTotalNotSettle.setDiffServiceCharge(diffServiceChargeNotSettle);
        lastTotalNotSettle.setSettleCharge(settleChargeNotSettle);
        lastTotalNotSettle.setNotSettleCharge(notSettleChargeNotSettle);
        lastTotalNotSettle.setSettleTime("---");
        dataListNotSettle.add(lastTotalNotSettle);

        vo.setNotSettleAmount(notSettleSum);
        vo.setNotSettleAmountList(dataListNotSettle);
    }
    /***
     * 运单配载在当期航班的数量结算金额
     * @param exportVO 赋值数据
     */
    private void setSettledAmountList(Long deptId, List<FlightLoadWaybillVO> waybillVoList, BillExportVoFinance exportVO){
        if (CollectionUtils.isEmpty(waybillVoList)){
            return;
        }
//        List<ChargeBilExportVO> waybillList = Optional.ofNullable(costDetailMapper.selectByFlightIds(flightIdList,deptId)).orElseGet(ArrayList::new);
        List<ChargeBilExportVO> waybillList = Optional.ofNullable(costDetailMapper.selectByFlightWaybills(waybillVoList,deptId)).orElseGet(ArrayList::new);
        //将运单有多种费用进行分组，然后设置对应的费用值
        Map<String, List<ChargeBilExportVO>> waybillCodeToVO = waybillList.stream()
                .collect(Collectors.groupingBy(ChargeBilExportVO::getWaybillCode));
        List<ChargeBilExportVO> dataList = new ArrayList<>();
        //序号
        int idx = 1;
//        for (Map.Entry<String, List<ChargeBilExportVO>> stringListEntry : waybillCodeToVO.entrySet()) {
//            ChargeBilExportVO chargeBilExportVO = stringListEntry.getValue().get(0);
//            chargeBilExportVO.setId((long) idx++);
//            //根据费用名称设置对应的值
//            stringListEntry.getValue().forEach(val -> setFee(chargeBilExportVO, val.getChargeAbb(), val.getTotalCharge()));
//            chargeBilExportVO.setSettleCharge(chargeBilExportVO.getTotalCharge());
//            dataList.add(chargeBilExportVO);
//        }
        for (List<ChargeBilExportVO> value : waybillCodeToVO.values()) {
            ChargeBilExportVO chargeBilExportVO = value.get(0);
            chargeBilExportVO.setId((long) idx++);
            //根据费用名称设置对应的值
            value.forEach(val -> setFee(chargeBilExportVO, val.getChargeAbb(), val.getTotalCharge()));
            chargeBilExportVO.setSettleCharge(chargeBilExportVO.getTotalCharge());
            dataList.add(chargeBilExportVO);
            for(ChargeBilExportVO exportVo: value){
                if(!(ObjectUtil.equal(exportVo.getFlightId(),chargeBilExportVO.getFlightId())
                        && ObjectUtil.equal(exportVo.getTotalCharge(),chargeBilExportVO.getTotalCharge()))){
                    exportVo.setId((long) idx++);
                    value.forEach(val -> setFee(exportVo, val.getChargeAbb(), val.getTotalCharge()));
                    exportVo.setSettleCharge(exportVo.getTotalCharge());
                    dataList.add(exportVo);
                }
            }
        }
        ChargeBilExportVO lastTotal = new ChargeBilExportVO();
        lastTotal.setWaybillCode("合计");
        lastTotal.setQuantity(dataList.stream()
                .map(baseBalance -> Optional.ofNullable(baseBalance.getQuantity()).orElse(0))
                .reduce(0, Integer::sum));

        lastTotal.setChargeWeight(getSumFee(dataList, ChargeBilExportVO::getChargeWeight));

        BigDecimal processingFee = getSumFee(dataList, ChargeBilExportVO::getProcessingFee);
        BigDecimal refrigerationFee = getSumFee(dataList, ChargeBilExportVO::getRefrigerationFee);
        BigDecimal handlingFee = getSumFee(dataList, ChargeBilExportVO::getHandlingFee);
        BigDecimal cableCharge = getSumFee(dataList, ChargeBilExportVO::getCableCharge);
        BigDecimal diffServiceCharge = getSumFee(dataList, ChargeBilExportVO::getDiffServiceCharge);
        BigDecimal settleCharge = getSumFee(dataList, ChargeBilExportVO::getSettleCharge);

        lastTotal.setId((long)idx);
        lastTotal.setProcessingFee(processingFee);
        lastTotal.setRefrigerationFee(refrigerationFee);
        lastTotal.setHandlingFee(handlingFee);
        lastTotal.setCableCharge(cableCharge);
        lastTotal.setDiffServiceCharge(diffServiceCharge);
        lastTotal.setSettleCharge(settleCharge);
        lastTotal.setSettleTime("---");
        dataList.add(lastTotal);
        exportVO.setDepAmount(settleCharge);
        exportVO.setSettledAmountList(dataList);
    }

    private void setBalanceFinanceData(List<BaseBalance> list, BillExportVoFinance vo, BaseAgent baseAgent) {
            //充值金额 = 增加余额之和
            Map<String, BigDecimal> typeToSum = list.stream()
                    .collect(Collectors.toMap(
                            BaseBalance::getRemark,
                            val -> Optional.ofNullable(val.getTradeMoney()).orElse(new BigDecimal(0)),
                            BigDecimal::add));
            vo.setRechargeAmount(Optional.ofNullable(typeToSum.get("增加余额")).orElse(new BigDecimal(0)));
            //当前余额 = 时间段内最新的余额
            vo.setThisBalance(CollectionUtil.isEmpty(list) ? new BigDecimal(0) : list.get(0).getBalance());
            //上期余额 等于这个时间段的前一天余额明细的余额
//            BaseBalance baseBalance = baseBalanceMapper.selectOne(new QueryWrapper<BaseBalance>()
//                    .eq("agent_id", baseAgent.getId())
//                    .lt("create_time", list.get(list.size() - 1).getCreateTime())
//                    .orderByDesc("create_time").orderByDesc("id")
//                    .last("limit 1"));
//            BigDecimal lastBalance = StringUtils.isNotNull(baseBalance) ? baseBalance.getBalance() : new BigDecimal(0);
//            vo.setLastBalance(lastBalance);
    }
}
