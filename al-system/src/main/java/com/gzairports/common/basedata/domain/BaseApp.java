package com.gzairports.common.basedata.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.gzairports.common.annotation.Excel;
import com.gzairports.common.core.domain.BizEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * app管理对象 base_app
 *
 * <AUTHOR>
 * @date 2024-09-19
 */
@Data
public class BaseApp
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 名称 */
    private String name;

    /** 版本号 */
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal versionNumber;

    /** 版本号中文 */
    @Excel(name = "版本号中文")
    @TableField(exist = false)
    private String versionNumberStr;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 上传时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "上传时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date uploadTime;

    /** 发布时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发布时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date releaseTime;

    /** 描述 */
    @Excel(name = "描述")
    private String remark;

    /** app下载地址 */
    private String appUrl;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("versionNumber", getVersionNumber())
            .append("status", getStatus())
            .append("uploadTime", getUploadTime())
            .append("releaseTime", getReleaseTime())
            .append("remark", getRemark())
            .append("appUrl", getAppUrl())
            .toString();
    }
}
