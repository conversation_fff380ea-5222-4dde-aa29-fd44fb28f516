package com.gzairports.web.controller.basedata;


import com.gzairports.common.basedata.domain.BaseVolume;
import com.gzairports.common.basedata.domain.query.VolumeUnitQuery;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.common.basedata.service.IVolumeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 多体积单位Controller
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@RestController
@RequestMapping("/base/volume")
@Api(tags = "多体积单位数据接口")
public class VolumeController extends BaseController {

    @Autowired
    private IVolumeService volumeService;

    /**
     * 查询多体积单位列表
     */
//    @PreAuthorize("@ss.hasPermi('base:volume:volumeUnitList')")
    @GetMapping("/volumeUnitList")
    @ApiOperation(value = "查询多体积单位列表")
    public TableDataInfo volumeUnitList(VolumeUnitQuery query)
    {
        startPage();
        List<BaseVolume> list = volumeService.selectVolumeUnitList(query);
        return getDataTable(list);
    }

    /**
     * 导出多体积单位
     */
    @PreAuthorize("@ss.hasPermi('base:volume:exportVolumeUnit')")
    @Log(title = "导出多体积单位", businessType = BusinessType.EXPORT)
    @PostMapping("/exportVolumeUnit")
    @ApiOperation(value = "导出多体积单位")
    public void exportVolumeUnit(HttpServletResponse response, VolumeUnitQuery query)
    {
        List<BaseVolume> list = volumeService.selectVolumeUnitList(query);
        ExcelUtil<BaseVolume> util = new ExcelUtil<BaseVolume>(BaseVolume.class);
        util.exportExcel(response, list, "多体积单位");
    }
}
