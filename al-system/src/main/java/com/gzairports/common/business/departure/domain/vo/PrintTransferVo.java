package com.gzairports.common.business.departure.domain.vo;

import com.gzairports.common.pdf.PdfPrintAnnotation;
import com.gzairports.wl.departure.domain.vo.TransferMawbVo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static com.gzairports.common.pdf.PdfPrintAnnotation.PdfFieldType.*;

/**
 * Created by david on 2024/11/29
 * <AUTHOR>
 */
@Data
public class PrintTransferVo {

    /** 标题 */
    @PdfPrintAnnotation(pdfFieldType = TEXT,pdfFieldName = "title")
    private String title;

    private String deptName;

    /** 始发站 */
    @PdfPrintAnnotation(pdfFieldType = TEXT,pdfFieldName = "sourcePort")
    private String sourcePort;

    /** 日期 */
    @PdfPrintAnnotation(pdfFieldType = DATE_YY_MM_DD,pdfFieldName = "date")
    private Date date;

    /** 是否有危险品 */
    @PdfPrintAnnotation(pdfFieldType = TEXT,pdfFieldName = "isDanger")
    private String isDanger;

    /** 电子章 */
    @PdfPrintAnnotation(pdfFieldType = IMAGE,pdfFieldName = "sealUrl")
    private String sealUrl;

    /** 货物交接运单列表 */
    @PdfPrintAnnotation(pdfFieldType = LIST,pdfFieldName = "vos")
    List<TransferMawbVo> vos;

}
