package com.gzairports.common.utils.poi;

import com.gzairports.common.annotation.Excel;
import com.gzairports.common.annotation.Excels;
import com.gzairports.common.core.text.Convert;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.DictUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.file.FileTypeUtils;
import com.gzairports.common.utils.file.ImageUtils;
import lombok.val;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.RegExUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.apache.poi.ss.formula.functions.T;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFDataValidation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by david on 2025/7/14
 */
public class ExcelUtilBillNew<T> {
    private static final Logger log = LoggerFactory.getLogger(ExcelUtil.class);

    public static final String FORMULA_REGEX_STR = "=|-|\\+|@";

    public static final String[] FORMULA_STR = { "=", "-", "+", "@" };

    /**
     * 用于dictType属性数据存储，避免重复查缓存
     */
    public Map<String, String> sysDictMap = new HashMap<String, String>();

    /**
     * Excel sheet最大行数，默认65536
     */
    public static final int sheetSize = 65536;

    /**
     * 工作表名称
     */
    private String sheetName;

    /**
     * 导出类型（EXPORT:导出数据；IMPORT：导入模板）
     */
    private Excel.Type type;

    /**
     * 工作薄对象
     */
    private Workbook wb;

    /**
     * 工作表对象
     */
    private Sheet sheet;

    /**
     * 样式列表
     */
    private Map<String, CellStyle> styles;

    /**
     * 当前行号
     */
    private int rownum;

    /**
     * 标题
     */
    private String title;

    /**
     * 最大高度
     */
    private short maxHeight;

    /**
     * 合并后最后行数
     */
    private int subMergedLastRowNum = 0;

    /**
     * 合并后开始行数
     */
    private int subMergedFirstRowNum = 1;

    /**
     * 对象的子列表方法
     */
    private Method subMethod;

    /**
     * 统计列表
     */
    private Map<Integer, Double> statistics = new HashMap<Integer, Double>();

    /**
     * 数字格式
     */
    private static final DecimalFormat DOUBLE_FORMAT = new DecimalFormat("######0.00");


    /**
     * 需要排除列属性
     */
    public String[] excludeFields;

    /**
     * 实体对象
     */
    public Class<T> clazz;

    public ExcelUtilBillNew(Class<T> clazz) {
        this.clazz = clazz;
    }

    /**
     * 账单导出
     * @param response
     * @param upObj
     * @param sheetName
     */
    public void exportBillExcel(HttpServletResponse response, Object upObj, String sheetName) {
        this.initTwo(sheetName, StringUtils.EMPTY, Excel.Type.EXPORT);
        try {
            Class<?> clazz = upObj.getClass();
            // 创建主工作表样式
            this.styles = createStyles(wb, clazz);

            // 创建主工作表
            createMainSheet(upObj, clazz);

            // 添加说明行
            addExplanationRows(5);

            // 创建其他工作表
            createDetailSheets(upObj, clazz);

            wb.write(response.getOutputStream());
        } catch (IOException e) {
            throw new RuntimeException("导出Excel异常" + e);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException(e);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            IOUtils.closeQuietly(wb);
        }
    }

    public void exportBillExcelMoreAgent(HttpServletResponse response, Object upObj, String sheetName) {
        this.initTwo(sheetName, StringUtils.EMPTY, Excel.Type.EXPORT);
        try {
            Class<?> clazz = upObj.getClass();

            // === 第一步：写入主表数据到已存在的 sheet（出港结算） ===
            Field mainField = clazz.getDeclaredField("chargeBilExportVOS");
            mainField.setAccessible(true);
            List<?> mainDataList = (List<?>) mainField.get(upObj);
            // 此时 this.sheet 已由 initTwo() 创建，直接使用即可
            if (mainDataList != null && !mainDataList.isEmpty()) {
                this.rownum = 0; // 重置行号

                Object firstItem = mainDataList.get(0);
                Class<?> itemClass = firstItem.getClass();

                // 为当前工作表创建样式（根据明细项类型）
                this.styles = createStyles(wb, itemClass);

                // 添加标题（写入表头）
                createCustomTitle(sheetName, itemClass);

                // 写入主表数据
                writeSheet(mainDataList, itemClass);
            }

            createDetailSheet(upObj, clazz, "arrAmountList", "进港已结算");

            wb.write(response.getOutputStream());
        } catch (IOException e) {
            throw new RuntimeException("导出Excel异常" + e);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException(e);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            IOUtils.closeQuietly(wb);
        }
    }

    private void createMainSheet(Object upObj, Class<?> clazz) throws Exception {
        this.styles = createStyles(wb, clazz);

        Set<Integer> columns = new HashSet<>(Arrays.asList(3, 5, 7, 9, 12));

        for (int i = 0; i <= 12; i++) {
            if(columns.contains(i)){
                sheet.setColumnWidth(i,3200);
            }else{
                sheet.setColumnWidth(i,2800);
            }
        }

        int rowIdx = 0;
        // 标题行
        Field titleFd = clazz.getDeclaredField("title");
        titleFd.setAccessible(true);
        String title = (String) titleFd.get(upObj);
        Row titleRow = sheet.createRow(rowIdx++);
        titleRow.setHeightInPoints(30);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellStyle(styles.get("title"));
        titleCell.setCellValue(title);

        // 合并单元格 (0,0) 到 (0,12)
        CellRangeAddress titleRegion = new CellRangeAddress(
                titleRow.getRowNum(), titleRow.getRowNum(), 0, 12);
        sheet.addMergedRegion(titleRegion);
        mergeCellSetBorder(titleRegion);
        // 第一行：结算周期、备注
        Row firstDataRow = sheet.createRow(rowIdx++);
        firstDataRow.setHeight((short) 400);
        // 结算周期
        Field settlementCycleFd = clazz.getDeclaredField("settlementCycle");
        settlementCycleFd.setAccessible(true);
        String settlementCycle = (String) settlementCycleFd.get(upObj);
        Cell settlementCycleLabelCell = firstDataRow.createCell(0);
        settlementCycleLabelCell.setCellValue("结算周期");
        settlementCycleLabelCell.setCellStyle(centerStyle());
        sheet.addMergedRegion(new CellRangeAddress(firstDataRow.getRowNum(), firstDataRow.getRowNum(), 0, 1)); // 合并结算周期的值
        Cell settlementCycleValueCell = firstDataRow.createCell(2); // 值放在第2列
        settlementCycleValueCell.setCellValue(settlementCycle);
        settlementCycleValueCell.setCellStyle(centerStyle());
        sheet.addMergedRegion(new CellRangeAddress(firstDataRow.getRowNum(), firstDataRow.getRowNum(), 2, 6)); // 合并结算周期的值

        // 备注
        Field remarksFd = clazz.getDeclaredField("remarks");
        remarksFd.setAccessible(true);
        String remarks = (String) remarksFd.get(upObj);
        Cell remarksLabelCell = firstDataRow.createCell(7); // 标签放在第7列
        remarksLabelCell.setCellValue("备注");
        remarksLabelCell.setCellStyle(centerStyle());
        Cell remarksValueCell = firstDataRow.createCell(8); // 值放在第8列
        remarksValueCell.setCellValue(remarks);
        remarksValueCell.setCellStyle(centerStyle());
        CellRangeAddress remarkRegion = new CellRangeAddress(firstDataRow.getRowNum(), firstDataRow.getRowNum(), 8, 12);
        mergeCellSetBorder(remarkRegion);
        sheet.addMergedRegion(remarkRegion); // 合并备注的值

        // 第二行：充值金额、上期余额、当期退款、非当期退款、已结算、非当期结算、未结算、非当期未结算、未来航班、当期余额.总未结算,本期财务余额
        Row secondDataRow = sheet.createRow(rowIdx++);
        secondDataRow.setHeight((short) 400);
        int colIdx = 0;
        for (Field declaredField : clazz.getDeclaredFields()) {
            declaredField.setAccessible(true);
            if ("title".equals(declaredField.getName()) || "settlementCycle".equals(declaredField.getName()) || "remarks".equals(declaredField.getName())) {
                continue;
            }

            Excel excelAnno = declaredField.getAnnotation(Excel.class);
            if (excelAnno != null){
                String columnName = excelAnno.name();
                Cell cellUp = secondDataRow.createCell(colIdx);
                cellUp.setCellValue(columnName);
                cellUp.setCellStyle(centerStyle());

                colIdx++;
            }
            Cell cellUp = secondDataRow.createCell(colIdx);
            cellUp.setCellValue("");
            cellUp.setCellStyle(centerStyleLeft());
        }

        // 第三行：填充数据
        Row thirdDataRow = sheet.createRow(rowIdx++);
        thirdDataRow.setHeight((short) 400);
        colIdx = 0;
        for (Field declaredField : clazz.getDeclaredFields()) {
            declaredField.setAccessible(true);
            if ("title".equals(declaredField.getName()) || "settlementCycle".equals(declaredField.getName()) || "remarks".equals(declaredField.getName())) {
                continue;
            }
            Object val = declaredField.get(upObj);
            if (val == null){
                continue;
            }
            Cell cellDown = thirdDataRow.createCell(colIdx);
            if (val instanceof List<?>){
                continue;
            }
            if (val instanceof BigDecimal) {
                cellDown.setCellValue(((BigDecimal) val).doubleValue());
                cellDown.setCellType(CellType.NUMERIC); // 设置单元格类型为数值类型
            } else if (val instanceof Integer) {
                cellDown.setCellValue((Integer) val);
                cellDown.setCellType(CellType.NUMERIC);
            } else if (val instanceof Double) {
                cellDown.setCellValue(((Double) val));
                cellDown.setCellType(CellType.NUMERIC);
            } else {
                // 对于非数值类型的字段，仍可以按原样设置为字符串
                cellDown.setCellValue(val.toString());
            }
            cellDown.setCellStyle(centerStyle());

            colIdx++;
        }
    }

    /**
     * 创建明细工作表
     */
    private void createDetailSheets(Object upObj, Class<?> clazz) throws Exception {
        createDetailSheet(upObj, clazz, "refundCurrentAmountList", "当期退款");
        createDetailSheet(upObj, clazz, "refundNotCurrentAmountList", "非当期退款");
        createDetailSheet(upObj, clazz, "settledAmountList", "已结算");
        createDetailSheet(upObj, clazz, "settledNotCurrentAmountList", "非当期已结算");
        createDetailSheet(upObj, clazz, "unsettledAmountList", "未结算");
        createDetailSheet(upObj, clazz, "unsettledNotCurrentAmountList", "非当期未结算");
        createDetailSheet(upObj, clazz, "futureFlightAmountList", "未来航班");
        createDetailSheet(upObj, clazz, "arrAmountList", "进港已结算");
        createDetailSheet(upObj, clazz, "notSettleAmountList", "总未结算");
    }

    /**
     * 创建明细工作表
     */
    private void createDetailSheet(Object upObj, Class<?> clazz, String fieldName, String sheetName) throws Exception {

        Field field = clazz.getDeclaredField(fieldName);
        field.setAccessible(true);
        List<?> dataList = (List<?>) field.get(upObj);
        this.sheet = wb.createSheet(sheetName);
        if (dataList != null && !dataList.isEmpty()) {
            // 创建新工作表
            this.rownum = 0;

            // 获取明细数据的类
            Object firstItem = dataList.get(0);
            Class<?> itemClass = firstItem.getClass();

            // 为当前工作表创建样式
            this.styles = createStyles(wb, itemClass);

            // 添加标题
            createCustomTitle(sheetName, itemClass);

            // 写入数据
            writeSheet(dataList, itemClass);
        }
    }
    public void initTwo(String sheetName, String title, Excel.Type type) {
        this.sheetName = sheetName;
        this.type = type;
        this.title = title;
        createWorkbook();

    }

    // 添加说明行
    private void addExplanationRows(int startRow) {
        // 第一行：说明：
        Row expRow1 = sheet.createRow(startRow);
        Cell expCell1 = expRow1.createCell(0);
        expCell1.setCellValue("说明：");

        // 第二行和第三行合并
        int beginRow = startRow + 1;
        int endRow = startRow + 2;

        // 创建第二行（合并区域的第一行）
        Row expRow2 = sheet.createRow(beginRow);

        // 合并 A-M 列（0~12），跨两行
        CellRangeAddress mergedRegion = new CellRangeAddress(
                beginRow,
                endRow,
                0,
                12
        );
        sheet.addMergedRegion(mergedRegion);

        // 在合并区域的左上角单元格写入内容
        Cell contentCell = expRow2.createCell(0);
        contentCell.setCellValue("1. 当前余额 = 充值金额+上期余额+当期退款+非当期退款-（已结算-非当前已结算+未结算-非当前未结算+未来航班+进港已结算）\n" +
                "2. 本期财务余额 = 当期余额 + 总未结算");

        // 【可选】设置样式，让文字自动换行
        CellStyle style = sheet.getWorkbook().createCellStyle();
        style.setWrapText(true); // 自动换行
        contentCell.setCellStyle(style);

        // 设置行高（可选）
        sheet.getRow(beginRow).setHeightInPoints(40); // 行高为 40
    }

    /**
     * 获取字段注解信息
     */
    public List<Object[]> getFields(Class<?> clazz)
    {
        List<Object[]> fields = new ArrayList<Object[]>();
        List<Field> tempFields = new ArrayList<>();
        tempFields.addAll(Arrays.asList(clazz.getSuperclass().getDeclaredFields()));
        tempFields.addAll(Arrays.asList(clazz.getDeclaredFields()));
        for (Field field : tempFields)
        {
            if (!ArrayUtils.contains(this.excludeFields, field.getName()))
            {
                // 单注解
                if (field.isAnnotationPresent(Excel.class))
                {
                    Excel attr = field.getAnnotation(Excel.class);
                    if (attr != null && (attr.type() == Excel.Type.ALL || attr.type() == type))
                    {
                        field.setAccessible(true);
                        fields.add(new Object[] { field, attr });
                    }
                }

                // 多注解
                if (field.isAnnotationPresent(Excels.class))
                {
                    Excels attrs = field.getAnnotation(Excels.class);
                    Excel[] excels = attrs.value();
                    for (Excel attr : excels)
                    {
                        if (!ArrayUtils.contains(this.excludeFields, field.getName() + "." + attr.targetAttr())
                                && (attr != null && (attr.type() == Excel.Type.ALL || attr.type() == type)))
                        {
                            field.setAccessible(true);
                            fields.add(new Object[] { field, attr });
                        }
                    }
                }
            }
        }
        return fields;
    }

    /**
     * 创建一个工作簿
     */
    public void createWorkbook()
    {
        this.wb = new SXSSFWorkbook(500);
        this.sheet = wb.createSheet();
        wb.setSheetName(0, sheetName);
    }

    /**
     * 创建表格样式
     *
     * @param wb 工作薄对象
     * @return 样式列表
     */
    private Map<String, CellStyle> createStyles(Workbook wb, Class<?> clazz)
    {
        // 写入各条记录,每条记录对应excel表中的一行
        Map<String, CellStyle> styles = new HashMap<String, CellStyle>();
        CellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font titleFont = wb.createFont();
        titleFont.setFontName("Arial");
        titleFont.setFontHeightInPoints((short) 16);
        titleFont.setBold(true);
        style.setFont(titleFont);
        styles.put("title", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        Font dataFont = wb.createFont();
        dataFont.setFontName("Arial");
        dataFont.setFontHeightInPoints((short) 10);
        style.setFont(dataFont);
        styles.put("data", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font totalFont = wb.createFont();
        totalFont.setFontName("Arial");
        totalFont.setFontHeightInPoints((short) 10);
        style.setFont(totalFont);
        styles.put("total", style);

        styles.putAll(annotationHeaderStyles(wb, styles, clazz));

        styles.putAll(annotationDataStyles(wb, clazz));

        return styles;
    }

    /**
     * 根据Excel注解创建表格头样式
     *
     * @param wb 工作薄对象
     * @return 自定义样式列表
     */
    private Map<String, CellStyle> annotationHeaderStyles(Workbook wb, Map<String, CellStyle> styles, Class<?> clazz)
    {
        Map<String, CellStyle> headerStyles = new HashMap<String, CellStyle>();
        List<Object[]> fields = getFields(clazz);
        for (Object[] os : fields)
        {
            Excel excel = (Excel) os[1];
            String key = StringUtils.format("header_{}_{}", excel.headerColor(), excel.headerBackgroundColor());
            if (!headerStyles.containsKey(key))
            {
                CellStyle style = wb.createCellStyle();
                style.cloneStyleFrom(styles.get("data"));
                style.setAlignment(HorizontalAlignment.CENTER);
                style.setVerticalAlignment(VerticalAlignment.CENTER);
                style.setFillForegroundColor(excel.headerBackgroundColor().index);
                style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                Font headerFont = wb.createFont();
                headerFont.setFontName("Arial");
                headerFont.setFontHeightInPoints((short) 10);
                headerFont.setBold(true);
                headerFont.setColor(excel.headerColor().index);
                style.setFont(headerFont);
                headerStyles.put(key, style);
            }
        }
        return headerStyles;
    }

    /**
     * 根据Excel注解创建表格列样式
     *
     * @param wb 工作薄对象
     * @return 自定义样式列表
     */
    private Map<String, CellStyle> annotationDataStyles(Workbook wb, Class<?> clazz)
    {
        List<Object[]> fields = getFields(clazz);
        Map<String, CellStyle> styles = new HashMap<String, CellStyle>();
        for (Object[] os : fields)
        {
            Excel excel = (Excel) os[1];
            String key = StringUtils.format("data_{}_{}_{}", excel.align(), excel.color(), excel.backgroundColor());
            if (!styles.containsKey(key))
            {
                CellStyle style = wb.createCellStyle();
                style.setAlignment(excel.align());
                style.setVerticalAlignment(VerticalAlignment.CENTER);
                style.setBorderRight(BorderStyle.THIN);
                style.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
                style.setBorderLeft(BorderStyle.THIN);
                style.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
                style.setBorderTop(BorderStyle.THIN);
                style.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
                style.setBorderBottom(BorderStyle.THIN);
                style.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
                style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                style.setFillForegroundColor(excel.backgroundColor().getIndex());
                Font dataFont = wb.createFont();
                dataFont.setFontName("Arial");
                dataFont.setFontHeightInPoints((short) 10);
                dataFont.setColor(excel.color().index);
                style.setFont(dataFont);
                styles.put(key, style);
            }
        }
        return styles;
    }

    /**
     * 根据注解获取最大行高
     */
    public short getRowHeight(Class<?> clazz)
    {
        List<Object[]> fields = getFields(clazz);
        double maxHeight = 0;
        for (Object[] os : fields)
        {
            Excel excel = (Excel) os[1];
            maxHeight = Math.max(maxHeight, excel.height());
        }
        return (short) (maxHeight * 20);
    }

    private void mergeCellSetBorder(CellRangeAddress mergedRegion) {
        // 使用RegionUtil设置边框
        RegionUtil.setBorderTop(BorderStyle.THIN, mergedRegion, sheet);
        RegionUtil.setBorderBottom(BorderStyle.THIN, mergedRegion, sheet);
        RegionUtil.setBorderLeft(BorderStyle.THIN, mergedRegion, sheet);
        RegionUtil.setBorderRight(BorderStyle.THIN, mergedRegion, sheet);
    }

    private void addMergedRegionIfNotOverlapping(CellRangeAddress region, Sheet sheet) {
        if (!isRegionOverlapping(region, sheet)) {
            sheet.addMergedRegion(region);
        } else {
            log.warn("Attempted to add overlapping merged region: {}", region.formatAsString());
        }
    }

    private boolean isRegionOverlapping(CellRangeAddress newRegion, Sheet sheet) {
        for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
            CellRangeAddress existingRegion = sheet.getMergedRegion(i);
            if (existingRegion.intersects(newRegion)) {
                return true;
            }
        }
        return false;
    }

    private CellStyle centerStyle() {
        CellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        return style;
    }

    private CellStyle centerStyleLeft() {
        CellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderLeft(BorderStyle.THIN);
        return style;
    }

    /**
     * 自定义创建标题行
     */
    private void createCustomTitle(String subTitle, Class<?> clazz) {
        Row titleRow = sheet.createRow(rownum++);
        titleRow.setHeightInPoints(25);

        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(subTitle);

        // 确保样式存在
        if (styles != null && styles.containsKey("title")) {
            titleCell.setCellStyle(styles.get("title"));
        }

        // 合并单元格
        int lastColIndex = getFields(clazz).size() - 1;
        sheet.addMergedRegion(new CellRangeAddress(
                titleRow.getRowNum(), titleRow.getRowNum(), 0, lastColIndex
        ));
    }

    public void writeSheet(List<?> dataList, Class<?> clazz)
    {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }
        getRowHeight(clazz);
        List<Object[]> fields = getFields(clazz);
        // 分页写入
        int sheetNo = Math.max(1, (int) Math.ceil(dataList.size() * 1.0 / sheetSize));

        for (int index = 0; index < sheetNo; index++)
        {
            createSheet(sheetNo, index, clazz);

            // 写表头
            Row headerRow = sheet.createRow(rownum);
            int column = 0;
            for (Object[] os : fields) {
                Excel excel = (Excel) os[1];
                this.createHeadCell(excel, headerRow, column++);
            }

            // 填充数据
            if (Excel.Type.EXPORT.equals(type)) {
                fillExcelData(index, dataList, clazz);
                addStatisticsRow();
            }

            rownum++;
        }
    }

    /**
     * 创建工作表
     *
     * @param sheetNo sheet数量
     * @param index 序号
     */
    public void createSheet(int sheetNo, int index, Class<?> clazz)
    {
        // 设置工作表的名称.
        if (sheetNo > 1 && index > 0)
        {
            this.sheet = wb.createSheet();
            this.createTitle(clazz);
            wb.setSheetName(index, sheetName + index);
        }
    }

    /**
     * 创建excel第一行标题
     */
    public void createTitle(Class<?> clazz)
    {
        if (StringUtils.isNotEmpty(title))
        {
            subMergedFirstRowNum++;
            subMergedLastRowNum++;
            int titleLastCol = getFields(clazz).size() - 1;
            Row titleRow = sheet.createRow(rownum == 0 ? rownum++ : 0);
            titleRow.setHeightInPoints(30);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellStyle(styles.get("title"));
            titleCell.setCellValue(title);
            sheet.addMergedRegion(new CellRangeAddress(titleRow.getRowNum(), titleRow.getRowNum(), titleRow.getRowNum(), titleLastCol));
        }
    }

    /**
     * 创建单元格
     */
    public Cell createHeadCell(Excel attr, Row row, int column)
    {
        // 创建列
        Cell cell = row.createCell(column);
        // 写入列信息
        cell.setCellValue(attr.name());
        setDataValidation(attr, row, column);
        cell.setCellStyle(styles.get(StringUtils.format("header_{}_{}", attr.headerColor(), attr.headerBackgroundColor())));
        return cell;
    }

    /**
     * 创建表格样式
     */
    public void setDataValidation(Excel attr, Row row, int column)
    {
        if (attr.name().indexOf("注：") >= 0)
        {
            sheet.setColumnWidth(column, 6000);
        }
        else
        {
            // 设置列宽
            sheet.setColumnWidth(column, (int) ((attr.width() + 0.72) * 256));
        }
        if (StringUtils.isNotEmpty(attr.prompt()) || attr.combo().length > 0)
        {
            if (attr.combo().length > 15 || StringUtils.join(attr.combo()).length() > 255)
            {
                // 如果下拉数大于15或字符串长度大于255，则使用一个新sheet存储，避免生成的模板下拉值获取不到
                setXSSFValidationWithHidden(sheet, attr.combo(), attr.prompt(), 1, 100, column, column);
            }
            else
            {
                // 提示信息或只能选择不能输入的列内容.
                setPromptOrValidation(sheet, attr.combo(), attr.prompt(), 1, 100, column, column);
            }
        }
    }

    public void setXSSFValidationWithHidden(Sheet sheet, String[] textlist, String promptContent, int firstRow, int endRow, int firstCol, int endCol)
    {
        String hideSheetName = "combo_" + firstCol + "_" + endCol;
        Sheet hideSheet = wb.createSheet(hideSheetName); // 用于存储 下拉菜单数据
        for (int i = 0; i < textlist.length; i++)
        {
            hideSheet.createRow(i).createCell(0).setCellValue(textlist[i]);
        }
        // 创建名称，可被其他单元格引用
        Name name = wb.createName();
        name.setNameName(hideSheetName + "_data");
        name.setRefersToFormula(hideSheetName + "!$A$1:$A$" + textlist.length);
        DataValidationHelper helper = sheet.getDataValidationHelper();
        // 加载下拉列表内容
        DataValidationConstraint constraint = helper.createFormulaListConstraint(hideSheetName + "_data");
        // 设置数据有效性加载在哪个单元格上,四个参数分别是：起始行、终止行、起始列、终止列
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);
        // 数据有效性对象
        DataValidation dataValidation = helper.createValidation(constraint, regions);
        if (StringUtils.isNotEmpty(promptContent))
        {
            // 如果设置了提示信息则鼠标放上去提示
            dataValidation.createPromptBox("", promptContent);
            dataValidation.setShowPromptBox(true);
        }
        // 处理Excel兼容性问题
        if (dataValidation instanceof XSSFDataValidation)
        {
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.setShowErrorBox(true);
        }
        else
        {
            dataValidation.setSuppressDropDownArrow(false);
        }

        sheet.addValidationData(dataValidation);
        // 设置hiddenSheet隐藏
        wb.setSheetHidden(wb.getSheetIndex(hideSheet), true);
    }

    public void setPromptOrValidation(Sheet sheet, String[] textlist, String promptContent, int firstRow, int endRow,
                                      int firstCol, int endCol)
    {
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = textlist.length > 0 ? helper.createExplicitListConstraint(textlist) : helper.createCustomConstraint("DD1");
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);
        DataValidation dataValidation = helper.createValidation(constraint, regions);
        if (StringUtils.isNotEmpty(promptContent))
        {
            // 如果设置了提示信息则鼠标放上去提示
            dataValidation.createPromptBox("", promptContent);
            dataValidation.setShowPromptBox(true);
        }
        // 处理Excel兼容性问题
        if (dataValidation instanceof XSSFDataValidation)
        {
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.setShowErrorBox(true);
        }
        else
        {
            dataValidation.setSuppressDropDownArrow(false);
        }
        sheet.addValidationData(dataValidation);
    }

    protected void fillExcelData(int pageIndex, List<?> dataList, Class<?> clazz)
    {
        int start = pageIndex * sheetSize;
        int end = Math.min(start + sheetSize, dataList.size());

        for (int i = start; i < end; i++) {
            Object o = dataList.get(i);
            rownum++;
            Row row = sheet.createRow(rownum);

            int column = 0;
            List<Object[]> fields = getFields(clazz);
            for (Object[] os : fields) {
                Field field = (Field) os[0];
                Excel excel = (Excel) os[1];

                field.setAccessible(true);
                try {
                    Object val = field.get(o);
                    Cell cell = row.createCell(column++);
                    setCellValue(cell, val, excel.align());
                } catch (IllegalAccessException e) {
                    throw new RuntimeException("Excel反射写入失败", e);
                }
            }

        }

    }

    private void setCellValue(Cell cell, Object val, HorizontalAlignment align) {
        if (val instanceof String) {
            cell.setCellValue((String) val);
        } else if (val instanceof Number) {
            cell.setCellValue(((Number) val).doubleValue());
        } else if (val instanceof Boolean) {
            cell.setCellValue((Boolean) val);
        } else if (val == null) {
            cell.setCellValue("");
        } else {
            cell.setCellValue(val.toString());
        }

        CellStyle style = cell.getCellStyle();
        if (style == null) {
            style = cell.getSheet().getWorkbook().createCellStyle();
        }

        // 克隆已有样式后再设置对齐方式
        CellStyle newStyle = cell.getSheet().getWorkbook().createCellStyle();
        newStyle.cloneStyleFrom(style);
        newStyle.setAlignment(align);
        cell.setCellStyle(newStyle);
    }

    public void addStatisticsRow()
    {
        if (statistics.size() > 0)
        {
            Row row = sheet.createRow(sheet.getLastRowNum() + 1);
            Set<Integer> keys = statistics.keySet();
            Cell cell = row.createCell(0);
            cell.setCellStyle(styles.get("total"));
            cell.setCellValue("合计");

            for (Integer key : keys)
            {
                cell = row.createCell(key);
                cell.setCellStyle(styles.get("total"));
                cell.setCellValue(DOUBLE_FORMAT.format(statistics.get(key)));
            }
            statistics.clear();
        }
    }
}
