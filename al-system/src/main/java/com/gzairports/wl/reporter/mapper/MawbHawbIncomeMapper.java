package com.gzairports.wl.reporter.mapper;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzairports.wl.reporter.domain.query.MawbHawbIncomeQuery;
import com.gzairports.wl.reporter.domain.vo.AHIncomeDetailVO;
import com.gzairports.wl.reporter.domain.vo.AHIncomeTotalVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;


@Mapper
public interface MawbHawbIncomeMapper {

    /**
     * 主分单收入明细
     * */
    Page<AHIncomeDetailVO> selectExportDataList(@Param("query") MawbHawbIncomeQuery query, @Param("page") Page<AHIncomeDetailVO> page);
//    List<AHIncomeDetailVO> selectExportDataList(@Param("query") MawbHawbIncomeQuery query);

    /**
     * 主分单收入汇总
     * */
    Page<AHIncomeTotalVO> selectExportDataTotalList(@Param("query") MawbHawbIncomeQuery query, @Param("page") Page<AHIncomeTotalVO> page);

    /**
     * 根据运单号查询总计费金额
     * */
    BigDecimal selectTotalCharging(String waybillCode);

    /**
     * 查询运单号结算总金额
     * */
    BigDecimal selectSettleTotalCharging(String waybillCode);

    /**
     * 查询运单号对应所有分单航空运费
     * */
    BigDecimal selectHawbChargingByWaybillCode(String waybillCode);

    /**
     * 查询这个主单关联了哪些分单
     * */
    List<String> selectHawbWaybillCode(String waybillCode);

}
