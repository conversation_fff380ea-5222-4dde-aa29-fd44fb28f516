package com.gzairports.common.basedata.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.basedata.domain.BaseBillType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 票证管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-17
 */
@Mapper
public interface BillTypeMapper extends BaseMapper<BaseBillType> {

    /**
     * 查询票证来源列表
     *
     * @return 票证来源列表
     */
    List<BaseBillType> selectTypeList();

    /**
     * 通过name得到code
     * @param name 票证name
     * @return 票证code
     */
    String getCodeByName(String name);
}
