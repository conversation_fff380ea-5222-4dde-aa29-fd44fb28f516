package com.gzairports.common.charge.service;



import com.gzairports.common.basedata.domain.BaseCargoCode;
import com.gzairports.common.charge.domain.query.ItemsQuery;
import com.gzairports.common.charge.domain.vo.HzChargeItemsVo;
import com.gzairports.common.charge.domain.vo.HzItemInfoVo;

import java.util.List;


/**
 * 货站收费条目Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-13
 */
public interface IHzChargeItemsService 
{
    /**
     * 查询货站收费条目
     * 
     * @param id 货站收费条目主键
     * @return 货站收费条目
     */
    public HzItemInfoVo selectHzChargeItemsById(Long id);

    /**
     * 查询货站收费条目列表
     * 
     * @param query 货站收费项目查询条件
     * @return 货站收费条目集合
     */
    public List<HzChargeItemsVo> selectHzChargeItemsList(ItemsQuery query);

    /**
     * 新增货站收费条目
     * 
     * @param vo 货站收费条目
     * @return 结果
     */
    public int insertHzChargeItems(HzItemInfoVo vo);

    /**
     * 修改货站收费条目
     * 
     * @param vo 货站收费条目
     * @return 结果
     */
    public int updateHzChargeItems(HzItemInfoVo vo);

    /**
     * 批量删除货站收费条目
     * 
     * @param ids 需要删除的货站收费条目主键集合
     * @return 结果
     */
    public int deleteHzChargeItemsByIds(Long[] ids);

    /**
     * 删除货站收费条目信息
     * 
     * @param id 货站收费条目主键
     * @return 结果
     */
    public int deleteHzChargeItemsById(Long id);

    /**
     * 根据大类得到货品代码,大类可以为空
     * @param categoryCode 查询条件
     * @return 结果
     */
    List<BaseCargoCode> selectCargoCodeList(List<String> categoryCode);
}
