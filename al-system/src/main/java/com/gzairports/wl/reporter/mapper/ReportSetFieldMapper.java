package com.gzairports.wl.reporter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.wl.reporter.domain.ReportSetField;
import com.gzairports.wl.reporter.domain.vo.SelectFieldsVo;
import com.gzairports.wl.reporter.domain.vo.SortFieldVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-19 09:41
 */
@Mapper
public interface ReportSetFieldMapper extends BaseMapper<ReportSetField> {

    /**
     * 查看详情
     * @param id 报表设置id
     * @return 结果
     */
    List<SelectFieldsVo> getInfoBySetId(Long id);

    /**
     * 根据报表设置id查询排序字段
     * @param id 报表设置id
     * @return 排序数据
     */
    List<SortFieldVo> selectSortFields(Long id);

    List<ReportSetField> selectReportDataFields(@Param("reportTitle") String reportTitle);

}
