package com.gzairports.wl.departure.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 国内分单表
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@Data
@TableName("wl_dep_hawb")
public class Hawb {

    /** 主键id */
    private Long id;

    /** 托运业务号 */
    private String consignCode;

    /** 运单号 */
    @Excel(name = "运单号")
    private String waybillCode;

    /** 始发站 */
    @Excel(name = "始发站")
    private String sourcePort;

    /** 目的站 */
    @Excel(name = "目的站")
    private String desPort;

    /** 航班号 */
    @Excel(name = "航班号")
    private String flightNo;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "航班日期", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date flightDate;

    /** 关联主单号 */
    @Excel(name = "关联主单号")
    private String masterWaybillCode;

    /** 承运人1 */
    private String carrier1;

    /** 到达站1 */
    private String des1;

    /** 承运人2 */
    private String carrier2;

    /** 到达站2 */
    private String des2;

    /** 承运人3 */
    private String carrier3;

    /** 到达站3 */
    private String des3;

    /** 收运方式 */
    private String collectMethod;

    /** 发货人简称 */
    private String shipperAbb;

    /** 发货人 */
    @Excel(name = "发货人")
    private String shipper;

    /** 发货人电话 */
    private String shipperPhone;

    /** 发货人地址 */
    private String shipperAddress;

    /** 发货人地区 */
    private String shipperRegion;

    /** 收货人简称 */
    private String consigneeAbb;

    /** 收货人 */
    @Excel(name = "收货人")
    private String consignee;

    /** 收货人电话 */
    private String consigneePhone;

    /** 收货人地址 */
    private String consigneeAddress;

    /** 收货人地区 */
    private String consigneeRegion;

    /** 货品代码 */
    @Excel(name = "货品代码")
    private String cargoCode;

    /** 品名 */
    @Excel(name = "品名")
    private String cargoName;

    /** 特货代码1 */
    @Excel(name = "特货代码1")
    private String specialCargoCode1;

    /** 特货代码2 */
    private String specialCargoCode2;

    /** 特货代码3 */
    private String specialCargoCode3;

    /** 其他特货代码 */
    private String otherSpecialCargoCode;

    /** 包装 */
    private String pack;

    /** 包装code */
    private String packCode;

    /** 件数 */
    @Excel(name = "件数")
    private Integer quantity;

    /** 重量 */
    @Excel(name = "重量")
    private BigDecimal weight;

    /** 计费重量 */
    private BigDecimal chargeWeight;

    /** 体积（M3） */
    private BigDecimal volume;

    /** 尺寸 */
    private String size;

    /** 储运注意事项 */
    private String storageTransportNotes;

    /** 结算注意事项 */
    private String settlementNotes;

    /** 运输保险价值 */
    private BigDecimal transportInsureValue;

    /** 提货方式 */
    private Integer deliveryMethod;

    /** 出港付费方式 */
    private Integer paymentMethod;

    /** 费用总计 */
    private BigDecimal costSum;

    /** 填开时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date writeTime;

    /** 填开地点 */
    private String writeLocation;

    /** 填开人 */
    private String writer;

    /** 备注 */
    private String remark;

    /** 分单账单流水号 */
    private String serialNo;

    /** 所属单位 */
    private Long deptId;

    /** 支付状态 0 未支付 1 已支付 2 已退款 3 生成账单*/
    private Integer payStatus;

    /** 缴费金额 */
    private BigDecimal money;

    /** 支付时间 */
    private Date payTime;

    /** 电子分单pdf地址 */
    private String pdfUrl;

    /** 拼单状态 0 否 1 是 */
    private Integer mergeStatus;

    /** 是否删除 */
    private Integer isDel;

    /** 分运单状态 NORMAL 正常  INVALID 作废*/
    private String status;

    /** 更新时间 */
    private Date updateTime;
}
