package com.gzairports.common.basedata.domain.vo;

import com.gzairports.common.basedata.domain.BaseBalance;
import com.gzairports.common.core.domain.PageQuery;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 代理人余额明细返回数据 base_agent
 *
 * <AUTHOR>
 * @date 2024-06-17
 */
@Data
public class BalanceInfoVo {

    /** 代理人 */
    private String agent;

    /** 充值余额合计 */
    private BigDecimal addBalance;

    /** 减少余额合计 */
    private BigDecimal subtractBalance;

    /** 剩余余额-时间区间内最后一次的余额 */
    private BigDecimal lastBalance;

    /** 账号余额 */
    private BigDecimal balance;

    /** 余额操作明细总条数 */
    private Integer total;

    /** 余额操作明细 */
    private PageQuery<List<BaseBalance>> baseBalances;
}
