package com.gzairports.wl.reporter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.wl.reporter.domain.ReportField;
import com.gzairports.wl.reporter.domain.ReportSet;
import com.gzairports.wl.reporter.domain.ReportSetField;
import com.gzairports.wl.reporter.domain.vo.ReportSetVo;
import com.gzairports.wl.reporter.domain.vo.SelectFieldsVo;
import com.gzairports.wl.reporter.domain.vo.SortFieldVo;
import com.gzairports.wl.reporter.mapper.ReportFieldMapper;
import com.gzairports.wl.reporter.mapper.ReportSetFieldMapper;
import com.gzairports.wl.reporter.mapper.ReportSetMapper;
import com.gzairports.wl.reporter.service.IReportSetService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-02-18 15:29
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ReportSetServiceImpl extends ServiceImpl<ReportSetMapper, ReportSet> implements IReportSetService {

    @Autowired
    private ReportSetMapper reportSetMapper;

    @Autowired
    private ReportFieldMapper reportFieldMapper;

    @Autowired
    private ReportSetFieldMapper reportSetFieldMapper;

    /**
     * 根据类型查询可选字段
     * @param type 类型
     * @return 可选指端
     */
    @Override
    public List<ReportField> fieldList(String type) {
        List<ReportField> fieldList = reportFieldMapper.selectList(new QueryWrapper<ReportField>().eq("type", type));
        if (CollectionUtils.isEmpty(fieldList)){
            return null;
        }
        return fieldList;
    }

    /**
     * 新增报表设置
     * @param vo 新增参数
     * @return 结果
     */
    @Override
    public int addSet(ReportSetVo vo) {
        ReportSet reportSet = new ReportSet();
        BeanUtils.copyProperties(vo, reportSet);
        reportSetMapper.insert(reportSet);
        setField(vo, reportSet);
        return 1;
    }

    /**
     * 编辑报表设置
     * @param vo 修改参数
     * @return 结果
     */
    @Override
    public int editSet(ReportSetVo vo) {
        reportSetFieldMapper.delete(new QueryWrapper<ReportSetField>().eq("set_id",vo.getId()));
        ReportSet reportSet = new ReportSet();
        BeanUtils.copyProperties(vo, reportSet);
        reportSetMapper.updateById(reportSet);
        setField(vo, reportSet);
        return 1;
    }

    /***
     * 查看详情
     * @param id 报表设置id
     * @return 结果
     */
    @Override
    public ReportSetVo getInfo(Long id) {
        ReportSetVo vo = reportSetMapper.getInfoById(id);
        List<ReportField> fields = reportFieldMapper.selectList(new QueryWrapper<ReportField>().eq("type", vo.getReportType()));
        vo.setFields(fields);
        List<SelectFieldsVo> fieldList = reportSetFieldMapper.getInfoBySetId(id);
        vo.setFieldList(fieldList);
        List<SortFieldVo> sortList = reportSetFieldMapper.selectSortFields(id);
        vo.setSortList(sortList);
        return vo;
    }

    /**
     * 报表列表查询
     * @param type 查询参数
     * @return 结果
     */
    @Override
    public List<ReportSet> selectList(String type) {
        if ("0".equals(type)){
            type = null;
        }
        List<ReportSet> reportList = reportSetMapper.getList(type);
        for (ReportSet reportSet : reportList) {
            List<SelectFieldsVo> fieldList = reportSetFieldMapper.getInfoBySetId(reportSet.getId());
            if (!CollectionUtils.isEmpty(fieldList)){
                String columnTitle = fieldList.stream().map(SelectFieldsVo::getFieldNameCn).collect(Collectors.joining(","));
                reportSet.setColumnTitle(columnTitle);
            }
        }
        return reportList;
    }

    /**
     * 删除报表设置
     * @param id 报表id
     * @return 结果
     */
    @Override
    public int delSet(Long id) {
        reportSetFieldMapper.delete(new QueryWrapper<ReportSetField>().eq("set_id",id));
        return reportSetMapper.deleteById(id);
    }

    /***
     * 设置报表字段
     * @param vo 报表参数
     * @param reportSet 报表对象
     */
    private void setField(ReportSetVo vo, ReportSet reportSet) {
        int i = 0;
        for (SelectFieldsVo selectFieldsVo : vo.getFieldList()) {
            ReportSetField setField = new ReportSetField();
            setField.setSetId(reportSet.getId());
            setField.setFieldNameCn(selectFieldsVo.getFieldNameCn());
            setField.setFieldName(selectFieldsVo.getFieldName());
            setField.setFieldIndex(i);
            i++;
            reportSetFieldMapper.insert(setField);
        }
        int j = 0;
        for (SortFieldVo sortFieldVo : vo.getSortList()) {
            ReportSetField setField = reportSetFieldMapper.selectOne(new QueryWrapper<ReportSetField>().eq("set_id", reportSet.getId()).eq("field_name", sortFieldVo.getFieldName()));
            if (setField != null) {
                setField.setSortType(sortFieldVo.getSortType());
                setField.setSortIndex(j);
                j++;
                reportSetFieldMapper.updateById(setField);
            }
        }
    }
}
