package com.gzairports.common.serviceRequest.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.reporter.domain.ReportServiceRequest;
import com.gzairports.common.serviceRequest.domain.ServiceRequest;
import com.gzairports.common.serviceRequest.domain.query.ServiceRequestQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 服务申请Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Mapper
public interface ServiceRequestMapper extends BaseMapper<ServiceRequest> {

    /**
     * 查询服务申请列表
     * @param query 查询条件
     * @return 服务申请列表
     */
    List<ServiceRequest> selectListByQuery(ServiceRequestQuery query);

    List<ReportServiceRequest> selectReportRequestList(@Param("lastSyncTime") Date lastSyncTime,@Param("dateNow") Date dateNow);
}
