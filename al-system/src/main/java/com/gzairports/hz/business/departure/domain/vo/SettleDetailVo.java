package com.gzairports.hz.business.departure.domain.vo;

import com.gzairports.common.business.departure.domain.CostDetail;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created by david on 2024/12/18
 * <AUTHOR>
 */
@Data
public class SettleDetailVo {

    /** 结算合计 */
    private BigDecimal settleTotal;

    private String flightDate;

    private String flightNo;

    private Date createTime;

    /** 结算费用明细 */
    private List<CostDetail> settleList;
}
