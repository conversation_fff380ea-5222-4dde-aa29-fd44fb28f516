package com.gzairports.common.charge.service.impl;

import com.gzairports.common.business.arrival.domain.HzArrItem;
import com.gzairports.common.business.arrival.domain.ItemDetail;
import com.gzairports.common.charge.domain.HzChargeItemRule;
import com.gzairports.common.charge.domain.vo.BillRuleVo;
import com.gzairports.common.charge.service.BillingRule;
import io.swagger.models.auth.In;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.List;

/**
 * 免费保管多少小时后按天计算费率规则
 * <AUTHOR>
 * @date 2024-08-03
 */
public class PostFreeStorageBillingRule implements BillingRule {

    @Override
    public String className() {
        return "PostFreeStorageBillingRule.class";
    }

    @Override
    public BillRuleVo calculateFee(List<HzChargeItemRule> rule, BigDecimal weight, Integer quantity, ItemDetail item) {
        HzChargeItemRule itemRule = rule.get(0);
        BillRuleVo vo = new BillRuleVo();
        vo.setQuantity(itemRule.getFreeTime());

        // 总保管时间（小时）
        double totalHours = item.getDaysInStorage() * 24;
        double freeHours = Double.parseDouble(itemRule.getFreeTime());

        // 未超过免费时间，费用为0
        if (totalHours <= freeHours) {
            vo.setTotalCharge(BigDecimal.ZERO);
            return vo;
        }

        // 计算超出的小时数
        double diffHours = totalHours - freeHours;

        // 计算实际计费天数（不足24小时按1天计）
        double days = Math.ceil(diffHours / 24);

        // 计算费用
        BigDecimal rate = itemRule.getRate();
        BigDecimal minCharge = itemRule.getMinimum() != null ? itemRule.getMinimum() : BigDecimal.ZERO;

        BigDecimal calculatedCharge = rate.multiply(BigDecimal.valueOf(days)).multiply(weight);

        // 取计算费用与最低费用的较大值
        BigDecimal finalCharge = calculatedCharge.max(minCharge);

        vo.setRate(rate);
        vo.setTotalCharge(finalCharge);

        return vo;
    }
}
