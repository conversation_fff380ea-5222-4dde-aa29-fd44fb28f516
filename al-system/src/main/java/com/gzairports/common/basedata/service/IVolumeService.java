package com.gzairports.common.basedata.service;


import com.gzairports.common.basedata.domain.BaseVolume;
import com.gzairports.common.basedata.domain.query.VolumeUnitQuery;

import java.util.List;

/**
 * 多体积单位Service接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
public interface IVolumeService {

    /**
     * 查询体积单位列表
     *
     * @param query 查询参数
     * @return 体积单位列表
     */
    List<BaseVolume> selectVolumeUnitList(VolumeUnitQuery query);

    /**
     * 导入体积单位
     *
     * @param volumes 体积单位数据列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    String importVolume(List<BaseVolume> volumes, boolean updateSupport);

    /**
     * 新增体积单位
     *
     * @param volume 体积单位
     * @return 结果
     */
    int addVolume(BaseVolume volume);

    /**
     * 修改体积单位
     *
     * @param volume 体积单位
     * @return 结果
     */
    int editVolume(BaseVolume volume);

    /**
     * 删除体积单位
     *
     * @param ids 体积单位id集合
     * @return 结果
     */
    int delVolume(Long[] ids);
}
