package com.gzairports.common.serviceRequest.domain.vo;

import com.gzairports.common.core.domain.PageQuery;
import com.gzairports.common.serviceRequest.domain.ServiceRequest;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 服务申请返回参数
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Data
public class ServiceRequestVo {

    /** 总预授权金额 */
    private BigDecimal totalPayMoney;

    /** 总结算支付金额 */
    private BigDecimal totalSettleMoney;

    /** 总结算退款金额 */
    private BigDecimal totalRefund;

    /** 服务申请列表 */
    private PageQuery<List<ServiceRequest>> requestsList;


}
