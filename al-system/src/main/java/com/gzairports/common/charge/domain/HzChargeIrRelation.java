package com.gzairports.common.charge.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 收费项目与规则关联表
 *
 * <AUTHOR>
 * @date 2024-10-23
 */
@Data
@TableName("hz_charge_ir_relation")
public class HzChargeIrRelation {

    /** 主键id */
    private Long id;

    /** 收费项目id */
    private Long itemId;

    /** 规则id */
    private Long ruleId;

    /** 收费项目里的规则名称 */
    private String itemRuleName;

    /** 优先级 */
    private Integer priority;

    /** 货品大类 */
    private String category;

    /** 货物品名 */
    private String cargoName;

    /** 货物品名集合 */
    @TableField(exist = false)
    private List<String> cargoNames;

    /** 代理人 */
    private String noCharge;

    /** 运单前缀 */
    private String prefix;

    /** 跨航司运输 */
    private Integer crossAir;

    /** 转南航 */
    private Integer isSouth;

    /** 退货 */
    private Integer isExit;

    /** 运单前缀多选 */
    @TableField(exist = false)
    private List<String> prefixs;

    /** 不收代理人 */
    @TableField(exist = false)
    private List<String> noCharges;

    /** 是否删除 */
    private Integer isDel;
}
