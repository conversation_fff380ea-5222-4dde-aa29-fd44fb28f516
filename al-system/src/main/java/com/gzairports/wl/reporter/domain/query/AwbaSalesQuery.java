package com.gzairports.wl.reporter.domain.query;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-02-20
 */
@Data
public class AwbaSalesQuery {

    /** 运单类型 AWBM 邮件单 AWBA 主单 */
    @Pattern(regexp = "AWBM|AWBA", message = "运单类型必须是(AWBM-邮件单 AWBA-主单)")
    @NotEmpty(message = "运单类型不能为空")
    private String waybillType;

    /** 主单号 */
    private String masterWaybillCode;

    /** 分单号 */
    private String waybillCode;

    /** 承运人 */
    private String carrier1;

    /** 起点站 */
    private String sourcePort;

    /** 货物编码 */
    private String cargoCode;

    /** 货物品名 */
    private String cargoName;

    /** 目的站 */
    private String desPort;

    /** 发货人简称 */
    private String shipperAbb;

    /** 发货人全称 */
    private String shipper;

    /** 收货人简称 */
    private String consignAbb;

    /** 收货人全称 */
    private String consign;

    /** 制单开始日期 */
    private Date startWriteTime;

    /** 制单结束日期 */
    private Date endWriteTime;

    /** 是否换单 */
    private Boolean isChange;

    /** 是否监管 */
    private Boolean isOversight;

    /** 营业点 */
    private String businessPoint;

    /** 航班号 */
    private String flightNo;

    /** 制单人 */
    private String writer;

    /** 是否作废 */
    private Boolean isInvalid;

    /** 报表名称 */
    private String reportTitle;

    /** 当前页 */
    private Integer pageNum;

    /** 每页数量 */
    private Integer pageSize;
}
