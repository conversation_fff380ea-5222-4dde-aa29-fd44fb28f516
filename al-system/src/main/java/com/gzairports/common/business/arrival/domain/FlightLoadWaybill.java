package com.gzairports.common.business.arrival.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 航班配载散舱表
 *
 * <AUTHOR>
 * @date 2024-07-01
 */
@Data
@TableName("hz_flight_load_waybill")
public class FlightLoadWaybill {

    /** 主键id */
    private Long id;

    /** 航班预配表id */
    private Long flightLoadId;

    @TableField(exist = false)
    private Long flightId;

    /** 航班id */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startRealTakeoffTime;

    /** 运单id */
    private Long waybillId;

    /** 运单装配件数 */
    private Integer quantity;

    /** 运单装配重量 */
    private BigDecimal weight;

    /** 运单号 */
    @TableField(exist = false)
    private String waybillCode;

    /** 运单收运id */
    private Long collectId;

    /** 是否修改 */
    private Integer isEdit;

    @TableField(exist = false)
    private String uld;

    @TableField(exist = false)
    private String cabin;
}
