package com.gzairports.common.log.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.common.log.mapper.WaybillLogMapper;
import com.gzairports.common.log.service.IWaybillLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 运单日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
@Service
public class WaybillLogServiceImpl extends ServiceImpl<WaybillLogMapper, WaybillLog> implements IWaybillLogService {

    @Autowired
    private WaybillLogMapper logMapper;

    @Autowired
    @Qualifier("threadPoolTaskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;

    /**
     * 查询日志列表
     * @param waybillCode 运单号
     * @param type 进出港类型
     * @return 日志列表
     */
    @Override
    public List<WaybillLog> selectList(String waybillCode, String type) {
        QueryWrapper<WaybillLog> waybillLogQueryWrapper = new QueryWrapper<WaybillLog>()
                .eq("type", type)
                .like("waybill_code", waybillCode)
                .orderByDesc("oper_time");
        return logMapper.selectList(waybillLogQueryWrapper);
    }

    /**
     * 请求和返回数据
     * @param id 运单号
     * @return 请求和返回数据
     */
    @Override
    public WaybillLog info(Long id) {
        return logMapper.selectById(id);
    }

    /**
     * 新增运单日志接口-异步操作
     * @param waybillLog 运单日志信息
     */
    @Async
    @Override
    public void insertWaybillLog(WaybillLog waybillLog) {
        taskExecutor.execute(() -> logMapper.insert(waybillLog));
    }

    /**
     *填入数据返回赋好值的运单日志对象
     **/
    @Override
    public WaybillLog getWaybillLog(String waybillCode, Integer operRemark, String operName,
                                    String operWeight, String operPieces, String flightNo,
                                    Object operParam, Object jsonResult, int status,
                                    String error_msg, Date operTime, String remark,
                                    String type, String uld){
        WaybillLog waybillLog = new WaybillLog();
        waybillLog.setWaybillCode(waybillCode);
        waybillLog.setOperRemark(operRemark);
        waybillLog.setOperName(operName);
        waybillLog.setOperWeight(operWeight);
        waybillLog.setOperPieces(operPieces);
        waybillLog.setFlightNo(flightNo);
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        try{
        //字符串转为json
        String operParamJson = objectMapper.writeValueAsString(operParam);
        waybillLog.setOperParam(operParamJson);
        String jsonResultJson = objectMapper.writeValueAsString(jsonResult);
        waybillLog.setJsonResult(jsonResultJson);
        }catch (JsonProcessingException e){
            e.printStackTrace();
        }
        waybillLog.setStatus(status);
        waybillLog.setErrorMsg(error_msg);
        waybillLog.setOperTime(operTime);
        waybillLog.setRemark(remark);
        waybillLog.setType(type);
        waybillLog.setUld(uld);
        return waybillLog;
    }

    @Override
    public String getJson(String s){
        ObjectMapper objectMapper = new ObjectMapper();
        StringBuilder stringBuilder = new StringBuilder();
        try{
            stringBuilder.append(objectMapper.writeValueAsString(s));}
        catch (JsonProcessingException e){
            e.printStackTrace();
        }
        return stringBuilder.toString();
    }

    @Override
    public boolean isExistLog(String waybillCode, String flightNo, String remark) {
        return logMapper.isExistLog(waybillCode,flightNo, remark);
    }
}
