package com.gzairports.common.business.arrival.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * Created by david on 2024/12/6
 * <AUTHOR>
 */
@Data
public class BillOfLadingQuery {

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date queryTime;

    private Date startTime;

    private Date endTime;

    private String waybillCode;

    private Long deptId;

    private Integer type;

    private String serialNo;
}
