package com.gzairports.hz.business.departure.service;

import com.gzairports.hz.business.departure.domain.HzCollectWeight;
import com.gzairports.hz.business.departure.domain.query.WaybillInfoQuery;
import com.gzairports.hz.business.departure.domain.query.WaybillStatusQuery;
import com.gzairports.hz.business.departure.domain.vo.ChargeStatusVo;
import com.gzairports.hz.business.departure.domain.vo.DetailedVo;
import com.gzairports.hz.business.departure.domain.vo.WaybillInfoVo;
import com.gzairports.hz.business.departure.domain.vo.WaybillStatusVo;

import java.util.List;

/**
 * 运单状态Service接口
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
public interface IWaybillStatusService {

    /**
     * 运单状态列表查询
     * @param query 查询条件
     * @return 列表数据
     */
    List<WaybillStatusVo> selectList(WaybillStatusQuery query);

    /**
     * 运单明细查询
     * @param query 查询条件
     * @return 结果
     */
    WaybillInfoVo getInfo(WaybillInfoQuery query);

    /**
     * 过磅记录
     * @param id 收运id
     * @return 记录
     */
    List<HzCollectWeight> weightInfo(Long id);

    /**
     * 运单状态列表查询
     * @param query 查询条件
     * @return 结果
     */
    List<DetailedVo> getStatusList(WaybillInfoQuery query);

    /**
     * 运单明细查询收费
     * @param query 查询条件
     * @return 收费数据
     */
    ChargeStatusVo chargeStatus(WaybillInfoQuery query);

    /**
     * 运单作废
     * @param id 运单号
     * @return 结果
     */
    int waybillCancel(Long id);

    /**
     * 运单删除
     * @param id 运单id
     * @return 结果
     */
    int waybillDel(Long id);
}
