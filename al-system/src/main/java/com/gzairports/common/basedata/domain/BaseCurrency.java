package com.gzairports.common.basedata.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 币种管理
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Data
@TableName("base_currency")
public class BaseCurrency {

    /** 主键id */
    private Long id;

    /** 币种代码 */
    @Excel(name = "币种代码")
    private String currency;

    /** 国家或地区 */
    @Excel(name = "国家或地区")
    private String country;

    /** 币种单位 */
    @Excel(name = "币种单位")
    private String currencyUnit;

    /** 币种符号 */
    @Excel(name = "币种符号")
    private String currencySymbol;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;
}
