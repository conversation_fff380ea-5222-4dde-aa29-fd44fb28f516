package com.gzairports.common.basedata.service.impl;

import com.gzairports.common.basedata.domain.BaseCargoMapping;
import com.gzairports.common.basedata.domain.query.CargoCodeMappingQuery;
import com.gzairports.common.basedata.mapper.CargoMappingMapper;
import com.gzairports.common.basedata.service.ICargoMappingService;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.exception.ServiceException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.bean.BeanValidators;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.Validator;
import java.util.List;

/**
 * 货品代码映射Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Service
public class CargoMappingServiceImpl implements ICargoMappingService {

    private static final Logger log = LoggerFactory.getLogger(WeightServiceImpl.class);

    @Autowired
    private Validator validator;

    @Autowired
    private CargoMappingMapper mappingMapper;

    /**
     * 查询货品代码映射列表
     *
     * @param query 查询参数
     * @return 货品代码映射列表
     */
    @Override
    public List<BaseCargoMapping> selectCargoCodeMappingList(CargoCodeMappingQuery query) {
        return mappingMapper.selectCargoCodeMappingList(query);
    }

    /**
     * 导入货品代码映射
     *
     * @param cargoMappings 货品代码映射列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importCargoMapping(List<BaseCargoMapping> cargoMappings, boolean updateSupport) {
        if (StringUtils.isNull(cargoMappings) || cargoMappings.size() == 0)
        {
            throw new ServiceException("导入货品代码映射数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (BaseCargoMapping mapping : cargoMappings) {
            try {
                // 验证是否存在相同的货品代码映射
                BaseCargoMapping cargoMapping = mappingMapper.selectCargoMappingByCode(mapping.getCargoCode(),mapping.getAirCargoCode(),mapping.getAirCode());
                if (StringUtils.isNull(cargoMapping)) {
                    BeanValidators.validateWithException(validator, mapping);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    mapping.setCreateBy(loginUser.getUsername());
                    mapping.setCreateTime(DateUtils.getNowDate());
                    mappingMapper.insertCargoMapping(mapping);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、货品代码 " + mapping.getCargoCode() + " 导入成功");
                }
                else if (updateSupport) {
                    BeanValidators.validateWithException(validator, mapping);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    mapping.setId(cargoMapping.getId());
                    mapping.setUpdateBy(loginUser.getUsername());
                    mapping.setUpdateTime(DateUtils.getNowDate());
                    mappingMapper.updateCargoMapping(mapping);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、货品代码 " + mapping.getCargoCode() + " 更新成功");
                }
                else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、货品代码 " + mapping.getCargoCode() + " 已存在");
                }
            }catch (Exception e){
                failureNum++;
                String msg = "<br/>" + failureNum + "、货品代码 " + mapping.getCargoCode() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 新增货品代码映射
     *
     * @param cargoMapping 货品代码映射
     * @return 结果
     */
    @Override
    public int addCargoMapping(BaseCargoMapping cargoMapping) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        cargoMapping.setCreateBy(loginUser.getUsername());
        cargoMapping.setCreateTime(DateUtils.getNowDate());
        cargoMapping.setDeptId(loginUser.getDeptId());
        return mappingMapper.insertCargoMapping(cargoMapping);
    }

    /**
     * 修改货品代码映射
     *
     * @param cargoMapping 货品代码映射
     * @return 结果
     */
    @Override
    public int editCargoMapping(BaseCargoMapping cargoMapping) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        cargoMapping.setUpdateBy(loginUser.getUsername());
        cargoMapping.setUpdateTime(DateUtils.getNowDate());
        return mappingMapper.updateCargoMapping(cargoMapping);
    }

    /**
     * 删除货品代码映射
     *
     * @param ids 货品代码映射id集合
     * @return 结果
     */
    @Override
    public int delCargoMapping(Long[] ids) {
        return mappingMapper.delCargoMapping(ids);
    }

    /**
     * 删除货品代码映射
     *
     * @param id 货品代码映射id集合
     * @return 结果
     */
    @Override
    public int delCargoMapping(Long id) {
        BaseCargoMapping cargoMapping = mappingMapper.selectById(id);
        cargoMapping.setIsDel(1);
        return mappingMapper.updateById(cargoMapping);
    }

    /**
     * 根据id获取货品代码映详情
     *
     * @param id 货品代码映射id
     * @return 结果
     */
    @Override
    public BaseCargoMapping getInfo(Long id) {
        return mappingMapper.selectById(id);
    }
}
