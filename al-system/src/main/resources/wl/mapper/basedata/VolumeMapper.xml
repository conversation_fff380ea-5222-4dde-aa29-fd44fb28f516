<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.VolumeMapper">
    <select id="selectVolumeUnitList" resultType="com.gzairports.common.basedata.domain.BaseVolume">
        select id, volume_unit, chinese_name, english_name, conversion_rate from base_volume
        <where>
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="volumeUnit != null  and volumeUnit != ''"> and volume_unit like concat('%',#{volumeUnit},'%')</if>
            <if test="chineseName != null  and chineseName != ''"> and chinese_name like concat('%',#{chineseName},'%')</if>
        </where>
    </select>
</mapper>
