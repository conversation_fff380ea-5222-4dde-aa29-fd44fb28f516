package com.gzairports.hz.business.departure.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.pdf.PdfPrintAnnotation;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import static com.gzairports.common.pdf.PdfPrintAnnotation.PdfFieldType.CHOICE;
import static com.gzairports.common.pdf.PdfPrintAnnotation.PdfFieldType.TEXT;

/**
 * 简易开单数据
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Data
public class EasyBill {

    /** 主键id */
    private Long id;

    /** 运单号 */
    private String waybillCode;

    /** 运单号 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "waybillCode")
    private String waybillCodeAbb;

    /** 交运货站 */
    private String shippingStation;

    /** 交运代理人 */
    private String shippingAgent;

    /** 货品大类名称 */
    private String categoryCode;

    /** 货品代码 */
    private String cargoCode;

    /** 货站货品品名 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "cargoName")
    private String cargoName;

    /** 件数 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "quantity")
    private Integer quantity;

    /** 实际重量 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "weight")
    private BigDecimal weight;

    /** 计费重量 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "chargeWeight")
    private BigDecimal chargeWeight;

    /** 特货代码1 */
    private String specialCargoCode1;

    /** 仓库 */
    private String store;

    /** 库位 */
    private String locator;

    /** 始发站 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "sourcePort")
    private String sourcePort;

    /** 承运人1 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "carrier1")
    private String carrier1;

    /** 到达站1 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "des1")
    private String des1;

    /** 承运人2 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "carrier2")
    private String carrier2;

    /** 到达站2 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "des2")
    private String des2;

    /** 承运人3 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "carrier3")
    private String carrier3;

    /** 到达站3 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "des3")
    private String des3;

    /** 目的站 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "desPort")
    private String desPort;

    /** 发货人简称 */
    private String shipperAbb;

    /** 发货人 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipper")
    private String shipper;

    /** 发货人电话 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipperPhone")
    private String shipperPhone;

    /** 发货代理人 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "agentCompany")
    private String agentCompany;

    /** 代理人识别码 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "agentCode")
    private String agentCode;

    /** 发货人地址 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipperAddress")
    private String shipperAddress;

    /** 收货人简称 */
    private String consignAbb;

    /** 收货人 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "consign")
    private String consign;

    /** 收货人电话 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "consigneePhone")
    private String consignPhone;

    /** 收货人地址 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "consigneeAddress")
    private String consignAddress;

    /** 航班日期1 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "flightDate1")
    private Date flightDate1;

    /** 航班号1 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "flightNo1")
    private String flightNo1;

    /** 航班日期2 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "flightDate2")
    private Date flightDate2;

    /** 航班号2 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "flightNo2")
    private String flightNo2;

    /** 包装 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "pack")
    private String pack;

    /** 尺寸 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "chargeWeight")
    private String size;

    /** 随附运输文件 */
    private String transportFile;

    /** 随附运输文件PDF */
    private String transportFilePdf;

    /** 是否需要冷藏 0 否 1 是 */
    private Integer isCold;

    /** 冷藏 */
    private String coldStore;

    /** 海关监管 */
    private Integer customsSupervision;

    /** 压仓 */
    private Integer pressureChamber;

    /** 交付运单 */
    private Integer deliver;

    /** 交付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date deliverTime;

    /** 简易开单备注 */
    private String remark;

    /**  状态 STAGING 暂存 NORMAL 正常 INVALID 作废 */
    private String status;

    /** 电子分单pdf地址 */
    private String pdfUrl;

    /** 安检申报单地址 */
    private String securityUrl;

    /** 填开时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "chargeWeight")
    private Date writeTime;

    /** 填开地点 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "writeLocation")
    private String writeLocation;

    /** 填开人 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "writer")
    private String writer;

    @PdfPrintAnnotation(pdfFieldType = CHOICE, pdfFieldName = "pthw")
    private String pthw;

    @PdfPrintAnnotation(pdfFieldType = CHOICE, pdfFieldName = "tzhw")
    private String tzhw;

    @PdfPrintAnnotation(pdfFieldType = CHOICE, pdfFieldName = "wxp")
    private String wxp;

    @PdfPrintAnnotation(pdfFieldType = CHOICE, pdfFieldName = "hkkj")
    private String hkkj;

}
