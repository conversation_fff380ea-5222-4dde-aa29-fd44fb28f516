package com.gzairports.common.basedata.service;


import com.gzairports.common.basedata.domain.BaseAirDifferent;
import com.gzairports.common.basedata.domain.query.AirDifferentQuery;

import java.util.List;

/**
 * 航司机型差异Service接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
public interface IAirDifferentService {

    /**
     * 查询航司机型差异列表
     *
     * @param query 查询参数
     * @return 航司机型差异列表
     */
    List<BaseAirDifferent> airDifferentList(AirDifferentQuery query);

    /**
     * 新增航司机型差异
     *
     * @param different 航司机型差异配置
     * @return 结果
     */
    int addAirDifferent(BaseAirDifferent different);

    /**
     * 修改航司机型差异
     *
     * @param different 航司机型差异配置
     * @return 结果
     */
    int editAirDifferent(BaseAirDifferent different);

    /**
     * 删除航司机型差异
     * @param id 航司机型差异id
     * @return 结果
     */
    int removeAirDifferent(Long id);

    /**
     * 查看详情
     * @param id 航司机型差异id
     * @return 结果
     */
    BaseAirDifferent getInfo(Long id);
}
