package com.gzairports.hz.business.departure.rabbitmq;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.basedata.domain.BaseCarrier;
import com.gzairports.common.basedata.mapper.CarrierMapper;
import com.gzairports.common.business.arrival.domain.FlightLoadWaybill;
import com.gzairports.common.business.arrival.mapper.FlightLoadWaybillMapper;
import com.gzairports.common.business.departure.domain.FlightInfo;
import com.gzairports.common.business.departure.domain.WaybillTrace;
import com.gzairports.common.business.departure.service.impl.WaybillTraceServiceImpl;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.common.log.service.IWaybillLogService;
import com.gzairports.common.system.mapper.SysConfigMapper;
import com.gzairports.common.utils.SerialNumberGenerator;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.hz.business.cable.domain.HzCable;
import com.gzairports.hz.business.cable.domain.HzCableAddress;
import com.gzairports.hz.business.cable.domain.vo.*;
import com.gzairports.hz.business.cable.mapper.HzCableAddressMapper;
import com.gzairports.hz.business.cable.mapper.HzCableMapper;
import com.gzairports.hz.business.cable.service.impl.HttpServiceImpl;
import com.gzairports.hz.business.departure.domain.AirWaybill;
import com.gzairports.hz.business.departure.domain.FlightLoad;
import com.gzairports.hz.business.departure.domain.HzDepRepeatWeight;
import com.gzairports.hz.business.departure.mapper.AllAirWaybillMapper;
import com.gzairports.hz.business.departure.mapper.FlightLoadMapper;
import com.gzairports.hz.business.departure.mapper.RepeatWeightMapper;
import com.gzairports.hz.business.departure.service.impl.FlightInfoServiceImpl;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.Marker;
import org.slf4j.MarkerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 *
 * 监听rabbitmq队列获取航班数据
 *
 * Created by david on 2023/9/18
 * <AUTHOR>
 */

@Component
@Transactional(rollbackFor = Exception.class)
public class Consumer implements ChannelAwareMessageListener {

    @Autowired
    private FlightInfoServiceImpl infoService;

    @Autowired
    private FlightLoadMapper flightLoadMapper;

    @Autowired
    private AllAirWaybillMapper airWaybillMapper;

    @Autowired
    private WaybillTraceServiceImpl traceService;

    @Autowired
    private FlightLoadWaybillMapper loadWaybillMapper;

    @Autowired
    private IWaybillLogService waybillLogService;

    @Autowired
    private HzCableMapper hzCableMapper;

    @Autowired
    private HzCableAddressMapper cableAddressMapper;

    @Autowired
    private SysConfigMapper sysConfigMapper;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private HttpServiceImpl httpService;

    @Autowired
    private CarrierMapper carrierMapper;

    private static final Logger logger = LoggerFactory.getLogger("FLIGHT");

    @Value("${hzCable.account}")
    private String account;

    @Value("${hzCable.loginUrl}")
    private String loginUrl;

    @Value("${hzCable.getMsg}")
    private String getMsg;

    @Value("${hzCable.FTPAddress}")
    private String ftpAddress;

    private static final Marker CABLE_ERROR_MARKER  = MarkerFactory.getMarker("CABLE-ERROR");

    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        try {
            //提取消息字节数组到字符串
            byte[] bytes = message.getBody();
            String mes = new String(bytes);
            System.out.println(mes);
            //业务主体
            JSONObject object = JSON.parseObject(mes);
            String dynFlight = object.getString("dynFlight");
            String msgType = object.getString("msgType");
            FlightInfo info =  JSONObject.parseObject(dynFlight, FlightInfo.class);
            if (info != null){
                info.setMsgType(msgType);
                infoService.saveOrUpdate(info);
                String[] split = info.getAirLineShort().split("-");
                List<String> legList = new ArrayList<>();
                if ("D".equals(info.getIsOffin())){
                    for (int i = 1; i < split.length; i++) {
                        legList.add(split[0] + "-" + split[i]);
                    }
                    for (String s : legList) {
                        FlightLoad load = flightLoadMapper.selectOne(new QueryWrapper<FlightLoad>()
                                .eq("flight_id", info.getFlightId()).eq("leg",s));
                        if (load == null){
                            FlightLoad flightLoad = new FlightLoad();
                            flightLoad.setFlightId(info.getFlightId());
                            flightLoad.setLeg(s);
                            flightLoad.setIsOffIn("D");
                            flightLoad.setState("not_pre");
                            flightLoad.setCreateTime(new Date());
                            try {
                                flightLoadMapper.insert(flightLoad);
                            }catch (DuplicateKeyException e){
                                logger.error("数据插入失败，唯一键冲突: flight_id={}, leg={}", flightLoad.getFlightId(), flightLoad.getLeg());
                                throw e;
                            }
                        }else if (info.getStartRealTakeoffTime() != null){
                            String sendAddress = sysConfigMapper.selectValue("dep.sendAddress");
                            String token = getToken();
                            MsgJsonVO msgJsonVO = setMsgVo(info,sendAddress);
                            Date date = new Date();
                            List<FlightLoadWaybill> waybillList = loadWaybillMapper.selectWaybillIdsByFlightLoadId(load.getId());
                            for (FlightLoadWaybill flightLoadWaybill : waybillList) {
                                // 修改运单状态
                                AirWaybill airWaybill = airWaybillMapper.selectById(flightLoadWaybill.getWaybillId());
                                try {
                                    setFSUMsg(info, token, msgJsonVO, flightLoadWaybill, airWaybill);
                                }catch (Exception e){
                                    logger.error(CABLE_ERROR_MARKER, "发送FSU-DEP报文失败：" + e.getMessage());
                                }
                                // 新增运单跟踪数据
                                List<WaybillTrace> waybillTraces = traceService.getBaseMapper().selectList(new QueryWrapper<WaybillTrace>()
                                        .eq("waybill_code", airWaybill.getWaybillCode()).eq("node_index", 8));
                                if (CollectionUtils.isEmpty(waybillTraces)){
                                    WaybillTrace waybillTrace = new WaybillTrace();
                                    waybillTrace.setOperTime(date);
                                    waybillTrace.setOperPieces(flightLoadWaybill.getQuantity());
                                    waybillTrace.setOperWeight(flightLoadWaybill.getWeight());
                                    waybillTrace.setWaybillCode(airWaybill.getWaybillCode());
                                    waybillTrace.setFlightNo(info.getFlightNo());
                                    waybillTrace.setPlanTakeoffTime(info.getStartSchemeTakeoffTime());
                                    waybillTrace.setNodeName("已出港");
                                    traceService.insertWaybillTrace(waybillTrace);
                                }
                                //新增运单日志
                                WaybillLog waybillLog = waybillLogService.getWaybillLog(
                                        airWaybill.getWaybillCode(), 0, "系统",
                                        flightLoadWaybill.getWeight().toString(), flightLoadWaybill.getQuantity().toString(), airWaybill.getFlightNo1(),
                                        airWaybill, null, 0, null, new Date(),
                                        "航班起飞", airWaybill.getType(), null);
                                waybillLogService.insertWaybillLog(waybillLog);
                                if ((airWaybill.getPayStatus() > 4 && airWaybill.getPayStatus() < 9) || "been_dep".equals(airWaybill.getStatus())) {
                                    continue;
                                }
                                airWaybill.setStatus("been_dep");
                                airWaybill.setUpdateTime(date);
                                airWaybillMapper.updateById(airWaybill);
                            }
                            load.setState("been_dep");
                            load.setUpdateTime(date);
                            flightLoadMapper.updateById(load);
                        }
                    }
                }
                if ("A".equals(info.getIsOffin())){
                    String desPort = split[split.length - 1];
                    String leg = split[0] + "-" + desPort;
                    legList.add(leg);
                    for (int i = 1; i < split.length - 1; i++) {
                        legList.add(split[i] + "-" + desPort);
                    }
                    for (String s : legList) {
                        FlightLoad load = flightLoadMapper.selectOne(new QueryWrapper<FlightLoad>()
                                .eq("flight_id", info.getFlightId()).eq("leg",s));
                        if (load == null){
                            FlightLoad flightLoad = new FlightLoad();
                            flightLoad.setFlightId(info.getFlightId());
                            flightLoad.setLeg(s);
                            flightLoad.setIsOffIn("A");
                            flightLoad.setCreateTime(new Date());
                            flightLoadMapper.insert(flightLoad);
                        }
                    }
                }
                logger.info("消息接收：" + mes);
                //若业务处理无异常，则回复通道删除消息
                channel.basicAck(deliveryTag,true);
            }
        }catch (Exception e){
            logger.error("获取失败：" + e);
            channel.basicReject(deliveryTag,true);
        }
    }

    private void setFSUMsg(FlightInfo info, String token, MsgJsonVO msgJsonVO, FlightLoadWaybill flightLoadWaybill, AirWaybill airWaybill) {
        List<HzCableAddress> addressList = cableAddressMapper.selectAddressByAirportCode(Collections.singletonList(airWaybill.getDesPort()));
        StringBuilder sb = new StringBuilder();
        FSUJsonVO fsuJsonVO = new FSUJsonVO();
        fsuJsonVO.setDepAirport(airWaybill.getSourcePort());
        fsuJsonVO.setDesAirport(airWaybill.getDesPort());
        fsuJsonVO.setMawbId(airWaybill.getId().toString());
        if(airWaybill.getWaybillCode().startsWith("AWBA")){
            String substring = airWaybill.getWaybillCode().substring(4);
            StringBuilder stringBuilder = new StringBuilder(substring);
            fsuJsonVO.setMawbNo(stringBuilder.insert(3, "-").toString());
        }else{
            String substring = airWaybill.getWaybillCode().substring(4);
            StringBuilder stringBuilder = new StringBuilder(substring);
            fsuJsonVO.setMawbNo(stringBuilder.insert(2, "-").toString());
        }
        fsuJsonVO.setPieces(flightLoadWaybill.getQuantity().toString());
        if (!airWaybill.getQuantity().equals(flightLoadWaybill.getQuantity())){
            fsuJsonVO.setShipmentDescriptionCode("P");
            fsuJsonVO.setTotalPieces(airWaybill.getQuantity().toString());
        }else {
            fsuJsonVO.setShipmentDescriptionCode("T");
        }
        fsuJsonVO.setTotalWeight(airWaybill.getWeight().toString());
        fsuJsonVO.setWeight(flightLoadWaybill.getWeight().toString());
        fsuJsonVO.setWeightUnit("K");
        List<StatusDetails> statusDetails = new ArrayList<>();
        StatusDetails statusDetail = new StatusDetails();
        statusDetail.setDepartureIndicator("A");
        statusDetail.setDepartureTime(info.getStartRealTakeoffTime().format(DATE_TIME_FORMATTER));
        statusDetail.setMovementAirport(info.getStartStation());
        statusDetail.setMovementArrivalAirport(info.getTerminalStation());
        statusDetail.setMovementCarrier(info.getAirWays());
        if (info.getStartRealTakeoffTime() != null){
            statusDetail.setMovementDate(info.getStartRealTakeoffTime().format(DATE_FORMATTER));
            statusDetail.setMovementTime(info.getStartRealTakeoffTime().format(TIME_FORMATTER));
        }
        statusDetail.setMovementDepartureAirport(info.getStartStation());
        statusDetail.setMovementFlightNo(info.getFlightNo());
        statusDetail.setPieces(flightLoadWaybill.getQuantity().toString());
        statusDetail.setShipmentDescriptionCode(fsuJsonVO.getShipmentDescriptionCode());
        statusDetail.setStatusCode("DEP");
        statusDetail.setWeight(flightLoadWaybill.getWeight().toString());
        statusDetail.setWeightUnit("K");
        statusDetails.add(statusDetail);
        fsuJsonVO.setStatusDetails(statusDetails);
        msgJsonVO.setMsgJson(JSON.toJSONString(fsuJsonVO));
        Map<String, List<HzCableAddress>> addressMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(addressList)){
            addressMap = addressList.stream().collect(Collectors.groupingBy(HzCableAddress::getAirportCode));
        }
        List<HzCableAddress> cableAddresses = addressMap.get(airWaybill.getDesPort());
        setInteractionType(cableAddresses,msgJsonVO);
        restExchange(msgJsonVO,token,sb);
        insertCableAndSendMsg(msgJsonVO,info, sb,token);
    }

    private MsgJsonVO setMsgVo(FlightInfo info,String sendAddress){
        MsgJsonVO vo = new MsgJsonVO();
        vo.setCarrier(info.getAirWays());
        vo.setDepartureStation(info.getStartStation());
        vo.setMsgType("FSU");
        vo.setNextStation(info.getTerminalStation());
        vo.setOperationNode("DEP");
        vo.setOperationStation("KWE");
        if (StringUtils.isNotEmpty(sendAddress)) {
            vo.setOrigin(sendAddress.split(","));
        }else {
            vo.setOrigin(new String[]{"KWEFDCN"});
        }
        vo.setPriority(new String[]{"QD"});
        vo.setSourceId("44162409105767715");
        vo.setUniqueId("44162409105767715");
        vo.setMsgVersion("12");
        BaseCarrier baseCarrier = carrierMapper.selectByCode(info.getAirWays());
        if (baseCarrier != null){
            vo.setWaybillPrefix(baseCarrier.getPrefix());
        }
        return vo;
    }

    public void setInteractionType(List<HzCableAddress> cableAddresses, MsgJsonVO vo) {
        List<String> sendType = new ArrayList<>();
        if (cableAddresses == null || cableAddresses.isEmpty()) {
            sendType.add("FD");
            vo.setSendType("FD");
            vo.setAddress(new String[]{"-"});
            return;
        }
        List<String> sendTypeCn = cableAddresses.stream()
                .map(HzCableAddress::getInteractionTypes)
                .filter(StringUtils::isNotEmpty)
                .map(s -> s.split(","))
                .flatMap(Arrays::stream)
                .map(String::trim)
                .distinct()
                .collect(Collectors.toList());
        for (String type : sendTypeCn) {
            switch (type) {
                case "民航局报文":
                    sendType.add("FD");
                    break;
                case "邮箱报文":
                    sendType.add("MAIL");
                    break;
                case "FTP收发报文":
                    sendType.add("FTP");
                    break;
                case "rabbitmq收发报文":
                    sendType.add("MQ");
                    break;
                default:
                    break;

            }
        }
        if (!sendType.contains("FD")){
            sendType.add("FD");
        }
        vo.setSendType(String.join(",", sendType));
        List<String> emailAddresses = new ArrayList<>();
        List<String> caacAddresses = new ArrayList<>();
        List<String> ftpList = new ArrayList<>();
        List<String> mqQueueList = new ArrayList<>();
        for (HzCableAddress cableAddress : cableAddresses) {
            List<String> interactionTypeList = new ArrayList<>();
            if (StringUtils.isNotEmpty(cableAddress.getInteractionTypes())) {
                interactionTypeList = Arrays.asList(cableAddress.getInteractionTypes().split(","));
            }
            if (!CollectionUtils.isEmpty(interactionTypeList)) {
                for (String type : interactionTypeList) {
                    switch (type) {
                        case "邮箱报文":
                            emailAddresses.add(cableAddress.getEmailAddress());
                            break;
                        case "FTP收发报文":
                            ftpList.add(cableAddress.getFtpList());
                            break;
                        case "民航局报文":
                            caacAddresses.add(cableAddress.getCaacAddress());
                            break;
                        case "rabbitmq收发报文":
                            mqQueueList.add(cableAddress.getMqQueue());
                            break;
                        default:
                            break;
                    }
                }
            }
        }
        if (!emailAddresses.isEmpty()) {
            vo.setReceiveMailAddress(emailAddresses.toArray(new String[0]));
        }
        if (!ftpList.isEmpty()) {
            vo.setReceiveFtpFolder(String.join(",",ftpList));
        }
        if (!mqQueueList.isEmpty()) {
            vo.setReceiveMQQueue(String.join(",",mqQueueList));
        }
        if (!caacAddresses.isEmpty()) {
            vo.setAddress(caacAddresses.toArray(new String[0]));
        }else {
            vo.setAddress(new String[]{"-"});
        }
    }

    private void insertCableAndSendMsg(MsgJsonVO vo, FlightInfo info, StringBuilder builder,String token) {
        if (StringUtils.isEmpty(vo.getSendType())){
            return;
        }
        String[] sendTypes = vo.getSendType().split(",");
        for (String sendType : sendTypes) {
            HzCable cable = new HzCable();
            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("ddHHmm");
            cable.setSerialNo(SerialNumberGenerator.generateSerialNumber());
            cable.setCableNo(sdf.format(date));
            cable.setCableTime(date);
            cable.setCreateBy("系统");
            cable.setIsSend(1);
            cable.setIsAuto(1);
            cable.setType("FSU");
            cable.setVersion("12");
            cable.setPriority("QD");
            cable.setCableAddress(String.join(",", vo.getOrigin()));
            if (StringUtils.isNotEmpty(vo.getAddress())){
                cable.setReceiveAddress(String.join(",", vo.getAddress()));
            }
            cable.setFlightNo(info.getFlightNo());
            cable.setFlightDate(info.getExecDate());
            cable.setContent(builder.toString());
            hzCableMapper.insert(cable);
            ForwardOriginMsgVO msgVO = new ForwardOriginMsgVO();
            msgVO.setOriginMsg(builder.toString());
            msgVO.setMsgType("FSU");
            msgVO.setSendType(sendType);
            msgVO.setReceiveAddress("-");
            if ("FD".equals(sendType)) {
                if (vo.getAddress() != null){
                    msgVO.setReceiveAddress(String.join(",",vo.getAddress()));
                }
            }
            if ("MAIL".equals(sendType)){
                msgVO.setMailAddress(String.join(",",vo.getReceiveMailAddress()));
            }
            if ("FTP".equals(sendType)){
                msgVO.setReceiveFtpAddress(ftpAddress);
                msgVO.setReceiveFtpFolder(vo.getReceiveFtpFolder());
            }
            if ("MQ".equals(sendType)){
                msgVO.setReceiveMQQueue(vo.getReceiveMQQueue());
            }
            msgVO.setSendAddress(String.join(",", vo.getOrigin()));
            msgVO.setPriority("QD");
            httpService.sendCable(msgVO, cable, token);
        }
    }

    private String getToken(){
        System.out.println("*********调用登录接口获取token*********");
        String token = "";
        HttpHeaders headers = setHeaders();
        HttpEntity<?> httpEntity = new HttpEntity<>(headers);
        ResponseEntity<JSONObject> exchange = restTemplate.exchange(loginUrl + account, HttpMethod.GET, httpEntity, JSONObject.class);
        if (exchange.getBody() != null) {
            if (!"0".equals(exchange.getBody().getString("code"))) {
                throw new CustomException(exchange.getBody().getString("message"));
            }
            JSONObject data = exchange.getBody().getJSONObject("data");
            token = data.getString("token");
        }
        return token;
    }

    private HttpHeaders setHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType("application/json;charset=UTF-8"));
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        headers.add("Accept-Charset", "UTF-8");
        return headers;
    }

    private void restExchange(MsgJsonVO vo, String token, StringBuilder sb){
        HttpHeaders header = setHeaders();
        header.add("X-Access-Token", token);
        HttpEntity<?> httpEntity = new HttpEntity<>(vo, header);
        ResponseEntity<JSONObject> exchange = restTemplate.exchange(getMsg, HttpMethod.POST, httpEntity, JSONObject.class);
        if (exchange.getBody() != null) {
            if (!"0".equals(exchange.getBody().getString("code"))) {
                throw new CustomException(exchange.getBody().getString("msg"));
            }
            JSONObject data = exchange.getBody().getJSONObject("data");
            String msgContent = data.getString("msgContent");
            sb.append(msgContent).append("\n");
        }
    }
}
