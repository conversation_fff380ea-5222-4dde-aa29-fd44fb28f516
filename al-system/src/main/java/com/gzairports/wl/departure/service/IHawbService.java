package com.gzairports.wl.departure.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.wl.charge.domain.query.FreightItemQuery;
import com.gzairports.wl.charge.domain.vo.FareAirItemVo;
import com.gzairports.wl.departure.domain.Consign;
import com.gzairports.wl.departure.domain.Hawb;
import com.gzairports.wl.departure.domain.HawbErrorRemark;
import com.gzairports.wl.departure.domain.HawbItem;
import com.gzairports.wl.departure.domain.query.FareQuery;
import com.gzairports.wl.departure.domain.query.HawbQuery;
import com.gzairports.wl.departure.domain.query.ItemQuery;
import com.gzairports.wl.departure.domain.vo.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 分单制单Service接口
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
public interface IHawbService extends IService<Hawb> {

    /**
     * 分单制单
     *
     * @param vo 分单制单数据
     * @return 返回结果
     */
    Map<String,String> add(HawbVo vo);

    /**
     * 根据运单号以及单证控制校验运单号
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    HawbVo check(String waybillCode);

    /**
     * 根据默认收费方式计算运单运价
     *
     * @param query 新增运单信息
     * @return 返回结果
     */
    FareVo fare(FareQuery query);

    /**
     * 添加收费管理
     *
     * @param query 分单收费项目参数
     * @return 返回结果
     */
    FareVo addFees(ItemQuery query);

    /**
     * 运单作废
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    int invalid(String waybillCode);

    /**
     * 根据运单code查询运单详情
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    HawbVo getInfo(String waybillCode);

    /**
     * 编辑运单
     *
     * @param vo 运单详情信息
     * @return 返回结果
     */
    int edit(HawbVo vo);

    /**
     * 修改费用项
     *
     * @param item 分单收费项目
     * @return 返回结果
     */
    FareVo editFee(HawbItem item);

    /**
     * 删除费用项
     *
     * @param item 分单收费项目
     * @return 返回结果
     */
    String delFee(HawbItem item);

    /**
     * 运单取消作废
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    int cancelVoid(String waybillCode);

    /**
     * 新增运单异常备注
     *
     * @param remark 运单异常备注
     * @return 返回结果
     */
    int addRemark(HawbErrorRemark remark);

    /**
     * 根据运单号查询异常备注列表
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    List<HawbErrorRemark> remarkList(String waybillCode);

    /**
     * 国内分单查询
     * @param query 国内分单查询参数
     * @return 结果
     */
    HawbQueryVo queryList(HawbQuery query);

    /**
     * 国内分单销售查询
     * @param query 国内分单销售查询参数
     * @return 结果
     */
    HawbQueryVo saleQuery(HawbQuery query);

    /**
     * 生成账单
     * @param ids 国内分单主键id
     * @return 结果
     */
    HawbQueryVo generateBill(Long[] ids);

    /**
     * 生成账单确认按钮
     * @param vo 生成账单参数
     * @return 结果
     */
    int confirm(HawbQueryVo vo);

    /**
     * 打印分单
     * @param response 返回流
     * @param id 分单id
     */
    void printHawb(HttpServletResponse response, Long id) throws Exception;

    /**
     * 打印托运书
     * @param response 返回流
     * @param consign 托运书
     */
    void printConsign(HttpServletResponse response, Consign consign) throws Exception;

    List<FareAirItemVo> selectItemList(FreightItemQuery query);

    /**
     * 添加运价条目
     * @param query 添加参数
     * @return 结果
     */
    FareVo addItems(ItemQuery query);

    int editWaybillCode(EditWaybillCode editWaybillCode);

    List<HawbQueryVo1> exportList(HawbQuery query);

    int payComp(Long id,String money);

    String isCtrl();
}
