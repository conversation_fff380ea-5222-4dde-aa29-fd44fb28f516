package com.gzairports.web.controller.basedata;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.basedata.domain.BaseAgent;
import com.gzairports.common.basedata.domain.BaseBalance;
import com.gzairports.common.basedata.domain.query.BaseAgentQuery;
import com.gzairports.common.basedata.domain.query.BaseBalanceInfoQuery;
import com.gzairports.common.basedata.mapper.BaseAgentMapper;
import com.gzairports.common.basedata.mapper.BaseBalanceMapper;
import com.gzairports.common.basedata.service.IBaseAgentService;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 代理人配置Controller
 *
 * <AUTHOR>
 * @date 2024-06-17
 */
@RestController
@RequestMapping("/base/agent")
@Api(tags = "代理人配置")
public class BaseAgentController extends BaseController
{
    @Autowired
    private IBaseAgentService baseAgentService;

    @Autowired
    private SysDeptMapper deptMapper;

    @Autowired
    private BaseBalanceMapper baseBalanceMapper;

    @Autowired
    private BaseAgentMapper baseAgentMapper;

    /**
     * 查询代理人配置列表
     */
//    @PreAuthorize("@ss.hasPermi('base:agent:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询代理人配置列表")
    public TableDataInfo list(BaseAgent baseAgent)
    {
        startPage();
        List<BaseAgent> list = baseAgentService.selectBaseAgentList(baseAgent);
        return getDataTable(list);
    }

    /**
     * 导出代理人配置列表
     */
    @PreAuthorize("@ss.hasPermi('base:agent:export')")
    @Log(title = "代理人配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出代理人配置列表")
    public void export(HttpServletResponse response, BaseAgent baseAgent)
    {
        List<BaseAgent> list = baseAgentService.selectExportBaseAgentList(baseAgent);
//        List<BaseAgent> list = baseAgentService.selectBaseAgentList(baseAgent);
        ExcelUtil<BaseAgent> util = new ExcelUtil<BaseAgent>(BaseAgent.class);
        util.exportExcel(response, list, "代理人配置数据");
    }

    /**
     * 获取代理人配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('base:agent:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取代理人配置详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(baseAgentService.selectBaseAgentById(id));
    }

    /**
     * 新增代理人配置
     */
    @PreAuthorize("@ss.hasPermi('base:agent:add')")
    @Log(title = "代理人配置", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ApiOperation(value = "代理人配置")
    public AjaxResult add(@RequestBody BaseAgent baseAgent)
    {
        return toAjax(baseAgentService.insertBaseAgent(baseAgent));
    }

    /**
     * 修改代理人配置
     */
    @PreAuthorize("@ss.hasPermi('base:agent:edit')")
    @Log(title = "修改代理人配置", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ApiOperation(value = "修改代理人配置")
    public AjaxResult edit(@RequestBody BaseAgent baseAgent)
    {
        return toAjax(baseAgentService.updateBaseAgent(baseAgent));
    }

    /**
     * 删除代理人配置
     */
    @PreAuthorize("@ss.hasPermi('base:agent:remove')")
    @Log(title = "代理人配置", businessType = BusinessType.DELETE)
	@GetMapping("/remove/{ids}")
    @ApiOperation(value = "删除代理人配置")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(baseAgentService.deleteBaseAgentByIds(ids));
    }

    /**
     * 增加余额
     */
    @PreAuthorize("@ss.hasPermi('base:agent:addMoney')")
    @PostMapping("/addMoney")
    @ApiOperation(value = "增加余额")
    public AjaxResult addMoney(@RequestBody BaseAgentQuery query)
    {
        return toAjax(baseAgentService.addMoney(query));
    }

    /**
     * 余额明细
     */
    @PreAuthorize("@ss.hasPermi('base:agent:balanceInfo')")
    @GetMapping("/balanceInfo")
    @ApiOperation(value = "余额明细")
    public AjaxResult balanceInfo(@RequestParam Long id,
                                  @RequestParam Integer pageNum,
                                  @RequestParam Integer pageSize,
                                  @RequestParam(required = false) String remark,
                                  @RequestParam(required = false) Date startTime,
                                  @RequestParam(required = false) Date endTime)
    {
        return AjaxResult.success(baseAgentService.balanceInfo(id,pageNum,pageSize,remark,startTime,endTime));
    }

    /**
     * 导出余额明细
     */
    @Log(title = "导出余额明细", businessType = BusinessType.EXPORT)
    @PostMapping("/balanceInfoExport")
    @ApiOperation(value = "导出余额明细")
    public void export(HttpServletResponse response, BaseBalanceInfoQuery query)
    {
        QueryWrapper<BaseBalance> wrapper = new QueryWrapper<BaseBalance>()
                .eq("agent_id", query.getId())
                .gt("create_time", query.getStartTime())
                .lt("create_time", query.getEndTime())
                .orderByDesc("create_time");
        if(query.getRemark()!=null){
            wrapper.eq("remark", query.getRemark());
        }
        List<BaseBalance> baseBalances = baseBalanceMapper.selectList(wrapper);
        BaseAgent baseAgent = baseAgentMapper.selectById(query.getId());
        Map<String, BigDecimal> collect = baseBalances.stream().filter(e->e.getTradeMoney()!=null).collect(Collectors.groupingBy(
                BaseBalance::getType, Collectors.reducing(BigDecimal.ZERO, BaseBalance::getTradeMoney, BigDecimal::add)));
        baseBalances.forEach(e->{
            if ("减少余额".equals(e.getType())){
                BigDecimal negate = e.getTradeMoney().negate();
                e.setTradeMoney(negate);
            }
            //代理人名称
            e.setAgent(baseAgent.getAgent());
        });
        BigDecimal add = collect.get("增加余额");
        BigDecimal reduce = collect.get("减少余额");
        BaseBalance baseBalance = new BaseBalance();
        baseBalance.setWaybillCode("代理人:" + baseAgent.getAgent());
        baseBalance.setRemark("共增加:" + add);
        baseBalance.setType("共减少:" + reduce);
        baseBalances.add(baseBalance);
        ExcelUtil<BaseBalance> util = new ExcelUtil<BaseBalance>(BaseBalance.class);
        util.exportExcel(response, baseBalances, "余额明细");
    }

    /**
     * 根据根据id查询信息
     * */
    @GetMapping("/getAbbByAgent/{id}")
    @ApiOperation(value = "根据根据id查询信息")
    public AjaxResult getAbbByAgent(@PathVariable Long id)
    {
        return AjaxResult.success(deptMapper.getAbbByAgent(id));
    }
}
