package com.gzairports.common.infoquery.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzairports.common.core.domain.entity.SysDept;
import com.gzairports.common.infoquery.domain.query.AirlineWaybillQuery;
import com.gzairports.common.infoquery.domain.vo.AirlineWaybillInfoVO;
import com.gzairports.common.infoquery.mapper.FlightMapper;
import com.gzairports.common.infoquery.service.IAirlineWaybillService;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 航司运单 服务层impl
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Service
public class AirlineWaybillServiceImpl implements IAirlineWaybillService {

    @Resource
    private FlightMapper flightMapper;

    @Resource
    private SysDeptMapper deptMapper;

    @Override
    public IPage<AirlineWaybillInfoVO> selectACWList(AirlineWaybillQuery query) {
        //查询 当前部门即子部门 设置的可查询的 运单前缀 和 航司二字码
        Long deptId = SecurityUtils.getDeptId();
        SysDept sysDept = deptMapper.selectDeptById(deptId);

        if(sysDept.getDeptType().equals(2)){
            //航司类型的部门，添加可查询航司限制
            List<SysDept> allEnableTmpDept = deptMapper.selectEnabledPrefixAndAirline(deptId);
            Set<String> allWaybillPrefixes = allEnableTmpDept.stream()
                    .filter(ObjectUtil::isNotNull)
                    .map(SysDept::getWaybillPrefixes)
                    .filter(StringUtils::isNotBlank)
                    .flatMap(waybillPrefixes -> Arrays.stream(waybillPrefixes.split(",")))
                    .map(String::trim)
                    .collect(Collectors.toSet());
            if(CollectionUtil.isEmpty(allWaybillPrefixes)){
                return new Page<>();
            }
            query.setEnabledWaybillCodePrefixList(allWaybillPrefixes);
        }

        IPage<AirlineWaybillInfoVO> page = new Page<>(query.getPageNum(), query.getPageSize());
        return flightMapper.selectACWList(query, page);
        /*
# left join hz_arr_record_order haro on haro.waybill_code = aaw.waybill_code
# left join hz_flight_load hfl on hfl.id = haro.leg_id
# left join all_flight_info afi on afi.flight_id = hfl.flight_id
         */
    }

}
