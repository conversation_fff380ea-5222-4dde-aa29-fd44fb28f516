package com.gzairports.common.serviceRequest.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.gzairports.common.serviceRequest.domain.ServiceExamine;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 服务申请详情返回参数
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Data
public class ServiceInfoVo {

    /** 主键id */
    private Long id;

    /** 服务编号 */
    private String serviceNo;

    /** 运单号 */
    private String waybillCode;

    /** 代理人 */
    private String agent;

    /** 服务项目 */
    private String serviceItem;

    /** 服务项目ID */
    private Long serviceItemId;

    /** 预算选择时间 */
    private Integer selectTime;

    /** 实际次数/小时 */
    private Integer actualTime;

    /** 支付状态 */
    private Integer payStatus;

    /** 状态 */
    private String status;

    /** 申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date requestTime;

    /** 服务开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /** 预授权支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /** 预授权支付金额 */
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal payMoney;

    /** 结算时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date settleTime;

    /** 服务结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 结束支付金额 */
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal endMoney;

    /** 结束退款金额 */
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal refund;

    /** 拒绝描述 */
    private String remark;

    /** 申请描述 */
    private String applyRemark;

    /** 审核状态 */
    private String examineStatus;

    /** 审核内容 */
    private String content;

    /** 审核列表 */
    private List<ServiceExamine> examines;

    /** 用于区分是完成服务还是拒绝服务 0完成 1拒绝  */
    @TableField(exist = false)
    private Integer type;

    private Integer payMethod;
}
