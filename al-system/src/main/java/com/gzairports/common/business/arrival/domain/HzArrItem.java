package com.gzairports.common.business.arrival.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.Date;

/**
 * 进港运单费用明细表
 *
 * <AUTHOR>
 * @date 2024-06-03
 */
@Data
@TableName(value = "hz_arr_waybill_item")
public class HzArrItem implements ItemDetail{

    /** 主键id */
    private Long id;

    /** 进港运单号 */
    private String waybillCode;

    /** 收费项目id */
    private Long irId;

    /** 收费项目id */
    @TableField(exist = false)
    private Long chargeItemsId;

    /** 收费项目简称 */
    @TableField(exist = false)
    private String chargeAbb;

    /** 收费名称 */
    @TableField(exist = false)
    private String chargeName;

    /** 计费方式 */
    @TableField(exist = false)
    private String ruleName;

    /** 执行类 */
    @TableField(exist = false)
    private String className;

    /** 是否删除 0 否 1 是 */
    private Integer isDel;

    /** 总费用 */
    private BigDecimal totalCharge;

    /** 修改费用 */
    private BigDecimal editCharge;

    /** 描述 */
    private String remark;

    /** 录单id */
    private Long orderId;

    /** 理货id */
    private Long tallyId;

    /** 份数 */
    private Integer unit;

    /** 小件件数 */
    private Integer smallItem;

    /** 大件件数 */
    private Integer largeItem;

    /** 超大件件数 */
    private Integer superLargeItem;

    /** 分时段计费开始时间 */
    @JsonFormat(pattern = "HH:mm")
    private LocalTime startTime;

    /** 分时段计费结束时间 */
    @JsonFormat(pattern = "HH:mm")
    private LocalTime endTime;

    /** 保存天数 */
    private Double daysInStorage;

    /** 冷库类型 */
    private String coldStore;

    /** 冷库开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date storeStartTime;

    /** 冷库结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date storeEndTime;

    /** 时间点 */
    private LocalTime pointTime;

    /** 是否自动 0 自动 1 手动 */
    private Integer isAuto;

    /** 办单id */
    private Long pickUpId;

    /** 收费服务类型 */
    private Integer serviceType;

    /** 缴费状态 0 未支付 1 已支付 2已结算 */
    private Integer isSettle;

    /** 进处费费率 */
    private BigDecimal feeRate;

    /** 部门id */
    private Long deptId;

    /** 支付方式 0现金 1月结 2余额支付 3预授权支付 */
    private Integer payMethod;

    /** 服务申请id */
    private Long serviceId;
}
