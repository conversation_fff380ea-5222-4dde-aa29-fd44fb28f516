package com.gzairports.framework.web.service;

import com.gzairports.common.constant.CacheConstants;
import com.gzairports.common.constant.Constants;
import com.gzairports.common.core.domain.entity.SysDept;
import com.gzairports.common.core.domain.entity.SysRole;
import com.gzairports.common.core.domain.entity.SysUser;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.core.redis.RedisCache;
import com.gzairports.common.enums.UserStatus;
import com.gzairports.common.exception.ServiceException;
import com.gzairports.common.exception.user.LoginRetryLimitException;
import com.gzairports.common.system.service.ISysDeptService;
import com.gzairports.common.system.service.ISysUserService;
import com.gzairports.common.utils.MessageUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.ip.IpUtils;
import com.gzairports.framework.manager.AsyncManager;
import com.gzairports.framework.manager.factory.AsyncFactory;
import com.gzairports.framework.security.context.AuthenticationContextHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 用户验证处理
 *
 * <AUTHOR>
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService
{
    private static final Logger log = LoggerFactory.getLogger(UserDetailsServiceImpl.class);

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysDeptService sysDeptService;

    @Autowired
    private SysPasswordService passwordService;

    @Autowired
    private SysPermissionService permissionService;

    @Value("${system.type}")
    private Integer type;

    @Autowired
    private RedisCache redisCache;

    @Value(value = "${user.ip.maxIpCount}")
    private int maxIpCount;

    @Value(value = "${user.ip.lockTime}")
    private int lockTime;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException
    {
        SysUser user = userService.selectUserByUserName(username);
        validate(user);
        passwordService.validate(user);
        SysDept dept = sysDeptService.selectDeptById(user.getDeptId());
        List<SysDept> list = sysDeptService.selectList();
        Long parentId = getParentId(dept, list);
        user.setHighParentId(parentId);

        return createLoginUser(user);
    }

    public UserDetails createLoginUser(SysUser user)
    {
        return new LoginUser(user.getUserId(), user.getDeptId(), user.getHighParentId(), user, permissionService.getMenuPermission(user));
    }

    /**
     * 从list中获取部门信息
     * @param deptId 部门id
     * @param list 部门集合
     * @return 部门信息
     */
    private SysDept findDeptById(Long deptId, List<SysDept> list) {
        return list.stream()
                .filter(dept -> dept.getDeptId().equals(deptId))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据当前登录人获取父级公司
     */
    public Long getParentId(SysDept dept,List<SysDept> list){
        if (dept.getParentId() == 0) {
            return dept.getDeptId();
        } else {
            // 继续查找父部门
            SysDept parentDept = findDeptById(dept.getParentId(),list);
            return getParentId(parentDept,list);
        }
    }

    public void validate(SysUser user)
    {
        Authentication usernamePasswordAuthenticationToken = AuthenticationContextHolder.getContext();
        String username = usernamePasswordAuthenticationToken.getName();

        if (StringUtils.isNull(user))
        {
            log.info("登录用户：{} 不存在.", username);
            throw new ServiceException(MessageUtils.message("user.not.exists"));
        }
        else if (UserStatus.DELETED.getCode().equals(user.getDelFlag()))
        {
            log.info("登录用户：{} 已被删除.", username);
            throw new ServiceException(MessageUtils.message("user.password.delete"));
        }
        else if (UserStatus.DISABLE.getCode().equals(user.getStatus()))
        {
            log.info("登录用户：{} 已被停用.", username);
            throw new ServiceException(MessageUtils.message("user.blocked"));
        }
        if (CollectionUtils.isEmpty(user.getRoles())){
            if (!type.equals(user.getType())){
                log.info("登录用户：{} 不存在.", username);
                throw new ServiceException(MessageUtils.message("user.not.exists"));
            }
        }else {
            List<SysRole> collect = user.getRoles().stream().filter(e -> e.getRoleId().equals(1L)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)){
                if (!type.equals(user.getType())){
                    log.info("登录用户：{} 不存在.", username);
                    throw new ServiceException(MessageUtils.message("user.not.exists"));
                }
            }
        }
    }
}
