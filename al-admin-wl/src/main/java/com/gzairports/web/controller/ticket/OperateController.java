package com.gzairports.web.controller.ticket;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.wl.ticket.domain.TicketCtrl;
import com.gzairports.wl.ticket.service.OperateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 单证控制管理Controller
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@RestController
@RequestMapping("/operate")
@Api(tags = "单证控制接口")
public class OperateController extends BaseController{

    @Autowired
    private OperateService operateService;

    /**
     * 查询单证控制管理列表
     */
//    @PreAuthorize("@ss.hasPermi('ticket.operate.list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询单证信息列表")
    public AjaxResult list(){
        return AjaxResult.success(operateService.selectList());
    }

    /**
     * 保存单证控制管理
     */
    @PreAuthorize("@ss.hasPermi('ticket.operate.save')")
    @PostMapping("/save")
    @ApiOperation(value = "保存单证控制管理")
    public AjaxResult save(@RequestBody List<TicketCtrl> list){
        return toAjax(operateService.saveCtrl(list));
    }


}
