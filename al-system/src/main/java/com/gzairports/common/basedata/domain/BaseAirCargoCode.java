package com.gzairports.common.basedata.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 航空公司货品代码
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Data
public class BaseAirCargoCode extends Model<BaseAirCargoCode> {

    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private Long id;

    /** 航空公司货品代码 */
    @Excel(name = "航空公司货品代码")
    private String code;

    /** 货站货品代码 */
    @Excel(name = "货站货品代码")
    private String cargoCode;

    /** 特货代码id */
    @TableField(exist = false)
    private List<Long> specialId;

    /** 特货代码code */
/*    @TableField(exist = false)
    private List<String> specialCode;*/

    /** 中文描述 */
    @Excel(name = "中文描述")
    private String chineseDescription;

    /** 英文描述 */
    @Excel(name = "英文描述")
    private String englishDescription;

    /** 承运人代码 */
    @Excel(name = "承运人代码")
    private String carrierCode;

    /** 适用国内 */
    @Excel(name = "适用国内")
    private Integer domestic;

    /** 适用国际 */
    @Excel(name = "适用国际")
    private Integer international;

    /** 是否公用 */
    private Integer isCommon;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 是否普删除 */
    private Integer isDel;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }
}
