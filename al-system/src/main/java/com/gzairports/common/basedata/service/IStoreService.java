package com.gzairports.common.basedata.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.basedata.domain.BaseStore;
import com.gzairports.common.basedata.domain.query.StoreQuery;
import com.gzairports.common.basedata.domain.vo.StoreLocatorVo;

import java.util.List;

/**
 * 仓库管理Service接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
public interface IStoreService  extends IService<BaseStore> {

    /**
     * 查询仓库管理列表
     *
     * @param query 查询参数
     * @return 仓库管理列表
     */
    List<BaseStore> storeList(StoreQuery query);

    /**
     * 查询仓库管理列表
     *
     * @param store 仓库参数
     * @return 结果
     */
    int add(BaseStore store);

    /**
     * 查询仓库管理列表
     *
     * @param store 仓库参数
     * @return 结果
     */
    int edit(BaseStore store);

    /**
     * 查询仓库管理列表
     *
     * @param id 仓库id
     * @return 结果
     */
    int del(Long id);

    /**
     * 详情
     * @param id 仓库id
     * @return 结果
     */
    BaseStore getInfo(Long id);

    /**
     * 查询仓库和库位集合
     * @return 结果
     */
    StoreLocatorVo storeSelect();

}
