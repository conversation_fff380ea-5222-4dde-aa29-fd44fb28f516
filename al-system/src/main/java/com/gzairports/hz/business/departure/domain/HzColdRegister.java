package com.gzairports.hz.business.departure.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 冷藏登记表
 *
 * <AUTHOR>
 * @date 2024-07-12
 */
@Data
@TableName("hz_cold_register")
public class HzColdRegister {

    /** 主键id */
    private Long id;

    /** 运单号 */
    private String waybillCode;

    /** 品名 */
    private String cargoName;

    /** 冷库 */
    private String coldStore;

    /** 状态 0 未入库,1 冷藏中,2 已出库*/
    private Integer status;

    /** 申请时长 */
    private Integer timeLen;

    /** 申请时间 */
    private Date applyTime;

    /** 申请人 */
    private String applyUser;

    /** 入库时间 */
    private Date wareTime;

    /** 出库时间 */
    private Date outTime;

    /** 计费金额 */
    private BigDecimal sumMoney;

    /** 使用时间 */
    private BigDecimal useTime;

    /** 计费时间 */
    private BigDecimal chargeTime;

    /** 进出港类型 */
    private String type;

    /** 支付状态 */
    private Integer payStatus;

//    /** 审核状态 */
//    private Integer examineStatus;

    private Long deptId;

    @TableField(exist = false)
    private Long relationId;

    /** 更新时间 */
    private Date updateTime;
}
