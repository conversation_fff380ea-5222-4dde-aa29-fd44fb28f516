package com.gzairports.wl.charge.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gzairports.common.annotation.Excel;
import com.gzairports.wl.departure.domain.RateItem;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 邮件运价条目表
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@Data
@TableName("wl_sf_mail_price_item")
public class MailPriceItem implements RateItem {

    /** 主键id */
    private Long id;

    /** 所属运价id */
    private Long rateId;

    /** 所属运价名称 */
    @TableField(exist = false)
    private String rateName;

    /** 条款名称 */
    @Excel(name = "条款名称")
    private String clauseName;

    /** 航班号 */
    @Excel(name = "航班号")
    private String flightNo;

    /** 托运局 */
    @Excel(name = "托运局")
    private String consignName;

    /** 承运人 */
    private String carrierCode;

    /** 始发城市 */
    @Excel(name = "始发城市")
    private String departureCity;

    /** 目的城市 */
    @Excel(name = "目的城市")
    private String destinationCity;

    /** 邮件类型 */
    private String mailType;

    /** 结算最低运费 */
    @Excel(name = "结算M")
    private BigDecimal minimumFreight;

    /** 票面最低运费 */
    @Excel(name = "票面M")
    private BigDecimal faceMinimumFreight;

    /** 应收最低运费 */
    @Excel(name = "应收M")
    private BigDecimal receivableMinimumFreight;

    /** 首重重量 */
    @Excel(name = "首重重量")
    private BigDecimal firstWeight;

    /** 运价重量区间5 */
    @TableField(value = "rate_weight_range_5")
    @Excel(name = "结算5")
    private BigDecimal rateWeightRange5;

    /** 运价重量区间10 */
    @TableField(value = "rate_weight_range_10")
    @Excel(name = "结算10")
    private BigDecimal rateWeightRange10;

    /** 运价重量区间N */
    @Excel(name = "结算N")
    private BigDecimal baseRateN;

    /** 运价重量区间45 */
    @TableField(value = "rate_weight_range_45")
    @Excel(name = "结算45")
    private BigDecimal rateWeightRange45;

    /** 运价重量区间100 */
    @TableField(value = "rate_weight_range_100")
    @Excel(name = "结算100")
    private BigDecimal rateWeightRange100;

    /** 运价重量区间300 */
    @TableField(value = "rate_weight_range_300")
    @Excel(name = "结算300")
    private BigDecimal rateWeightRange300;

    /** 运价重量区间500 */
    @TableField(value = "rate_weight_range_500")
    @Excel(name = "结算500")
    private BigDecimal rateWeightRange500;

    /** 运价重量区间1000 */
    @TableField(value = "rate_weight_range_1000")
    @Excel(name = "结算1000")
    private BigDecimal rateWeightRange1000;

    /** 票面重量区间5 */
    @TableField(value = "face_weight_range_5")
    @Excel(name = "票面5")
    private BigDecimal faceWeightRange5;

    /** 票面重量区间10 */
    @TableField(value = "face_weight_range_10")
    @Excel(name = "票面10")
    private BigDecimal faceWeightRange10;

    /** 票面重量区间N */
    @Excel(name = "票面N")
    private BigDecimal faceWeightRangeN;

    /** 票面重量区间45 */
    @TableField(value = "face_weight_range_45")
    @Excel(name = "票面45")
    private BigDecimal faceWeightRange45;

    /** 票面重量区间100 */
    @TableField(value = "face_weight_range_100")
    @Excel(name = "票面100")
    private BigDecimal faceWeightRange100;

    /** 票面重量区间300 */
    @TableField(value = "face_weight_range_300")
    @Excel(name = "票面300")
    private BigDecimal faceWeightRange300;

    /** 票面重量区间500 */
    @TableField(value = "face_weight_range_500")
    @Excel(name = "票面500")
    private BigDecimal faceWeightRange500;

    /** 票面重量区间1000 */
    @TableField(value = "face_weight_range_1000")
    @Excel(name = "票面1000")
    private BigDecimal faceWeightRange1000;

    /** 应收重量区间5 */
    @TableField(value = "receivable_weight_range_5")
    @Excel(name = "应收5")
    private BigDecimal receivableWeightRange5;

    /** 应收重量区间10 */
    @TableField(value = "receivable_weight_range_10")
    @Excel(name = "应收10")
    private BigDecimal receivableWeightRange10;

    /** 应收重量区间N */
    @Excel(name = "应收N")
    private BigDecimal baseReceivableN;

    /** 应收重量区间45 */
    @TableField(value = "receivable_weight_range_45")
    @Excel(name = "应收45")
    private BigDecimal receivableWeightRange45;

    /** 应收重量区间100 */
    @TableField(value = "receivable_weight_range_100")
    @Excel(name = "应收100")
    private BigDecimal receivableWeightRange100;

    /** 应收重量区间300 */
    @TableField(value = "receivable_weight_range_300")
    @Excel(name = "应收300")
    private BigDecimal receivableWeightRange300;

    /** 应收重量区间500 */
    @TableField(value = "receivable_weight_range_500")
    @Excel(name = "应收500")
    private BigDecimal receivableWeightRange500;

    /** 应收重量区间1000 */
    @TableField(value = "receivable_weight_range_1000")
    @Excel(name = "应收1000")
    private BigDecimal receivableWeightRange1000;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    /** 储运注意事项 */
    @Excel(name = "储运注意事项")
    private String storageShippingNotes;

    /** 结算注意事项 */
    @Excel(name = "结算注意事项")
    private String settlementNotes;

    /** 条目是否逻辑删除 */
    private Integer isDel;

    /** 更新时间 */
    private Date updateTime;

    /** 更新人 */
    private String updateBy;

    /** 创建时间 */
    private Date createTime;

    /** 创建人 */
    private String createBy;
}
