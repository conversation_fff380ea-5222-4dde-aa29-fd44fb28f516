package com.gzairports.common.business.arrival.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 挑单查询参数
 *
 * <AUTHOR>
 * @date 2024-06-03
 */
@Data
public class PickUpQuery {
    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date flightDate;
    /** 航班号 */
    private String flightNo;

    /** 收货代理人 */
    private String agent;

    /** 收货人 */
    private String consign;

    private String consignee;

    /** 制单日期 */
    private Date startTime;

    /** 制单日期 */
    private Date endTime;

    /** 所属单位 */
    private Long deptId;

    /**
     * 理货开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tallyStartTime;

    /**
     * 理货结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tallyEndTime;
}
