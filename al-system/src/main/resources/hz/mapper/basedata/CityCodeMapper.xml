<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.CityCodeMapper">
    <insert id="insertCityCode" parameterType="BaseCityCode">
        insert into base_city_code
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="code != null and code != ''">code,</if>
            <if test="chineseName != null and chineseName != ''">chinese_name,</if>
            <if test="englishName != null and englishName != ''">english_name,</if>
            <if test="stateOrProvinceChinese != null and stateOrProvinceChinese != ''">state_or_province_chinese,</if>
            <if test="stateOrProvinceEnglish != null and stateOrProvinceEnglish != ''">state_or_province_english,</if>
            <if test="region != null and region != ''">region,</if>
            <if test="fourCode != null and fourCode != ''">four_code,</if>
            <if test="timeZone != null and timeZone != ''">time_zone,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="code != null and code != ''">#{code},</if>
            <if test="chineseName != null and chineseName != ''">#{chineseName},</if>
            <if test="englishName != null and englishName != ''">#{englishName},</if>
            <if test="stateOrProvinceChinese != null and stateOrProvinceChinese != ''">#{stateOrProvinceChinese},</if>
            <if test="stateOrProvinceEnglish != null and stateOrProvinceEnglish != ''">#{stateOrProvinceEnglish},</if>
            <if test="region != null and region != ''">#{region},</if>
            <if test="fourCode != null and fourCode != ''">#{fourCode},</if>
            <if test="timeZone != null and timeZone != ''">#{timeZone},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>
    <update id="updateCityCode" parameterType="BaseCityCode">
        update base_city_code
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="chineseName != null and chineseName != ''">chinese_name = #{chineseName},</if>

            <if test="englishName != null">english_name = #{englishName},</if>
            <if test="englishName == null">english_name = null,</if>

            <if test="stateOrProvinceChinese != null and stateOrProvinceChinese != ''">state_or_province_chinese = #{stateOrProvinceChinese},</if>

            <if test="stateOrProvinceEnglish != null">state_or_province_english = #{stateOrProvinceEnglish},</if>
            <if test="stateOrProvinceEnglish == null">state_or_province_english = null,</if>

            <if test="region != null and region != ''">region = #{region},</if>

            <if test="fourCode != null">four_code = #{fourCode},</if>
            <if test="fourCode == null">four_code = null,</if>

            <if test="timeZone != null">time_zone = #{timeZone},</if>
            <if test="timeZone == null">time_zone = null,</if>

            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null and createTime != ''">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="delCityCode" parameterType="long">
        update base_city_code set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectCityCodeList" resultType="com.gzairports.common.basedata.domain.BaseCityCode">
        select id, code, chinese_name, english_name, state_or_province_chinese, state_or_province_english, region, four_code, time_zone from base_city_code
        where is_del = 0
        <if test="ids != null  and ids.size() > 0"> and id in
            <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="code != null  and code != ''"> and code like concat('%',#{code},'%')</if>
        <if test="chineseName != null  and chineseName != ''"> and chinese_name like concat('%',#{chineseName},'%')</if>
        <if test="englishName != null  and englishName != ''"> and english_name like concat('%',#{englishName},'%')</if>
        <if test="stateOrProvinceChinese != null  and stateOrProvinceChinese != ''"> and state_or_province_chinese like concat('%',#{stateOrProvinceChinese},'%')</if>
        <if test="region != null  and region != ''"> and region like concat('%',#{region},'%')</if>
    </select>
    <select id="selectCityByCode" resultType="com.gzairports.common.basedata.domain.BaseCityCode">
        select id, code, chinese_name, english_name, state_or_province_chinese, state_or_province_english, region, four_code, time_zone
        from base_city_code where code = #{code} and is_del = 0
    </select>
    <select id="selectCityListByName" resultType="com.gzairports.common.basedata.domain.BaseCityCode">
        select id, code, chinese_name, english_name, state_or_province_chinese, state_or_province_english, region, four_code, time_zone
        from base_city_code where is_del = 0 and chinese_name like '%${cityName}%'
    </select>
</mapper>
