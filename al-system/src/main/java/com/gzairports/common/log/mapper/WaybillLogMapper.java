package com.gzairports.common.log.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.log.domain.WaybillLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 运单日志Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
@Mapper
public interface WaybillLogMapper extends BaseMapper<WaybillLog> {
    boolean isExistLog(@Param("waybillCode") String waybillCode,@Param("flightNo") String flightNo,@Param("remark") String remark);
}
