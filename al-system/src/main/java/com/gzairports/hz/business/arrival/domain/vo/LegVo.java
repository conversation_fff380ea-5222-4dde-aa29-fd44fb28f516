package com.gzairports.hz.business.arrival.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 航班文件航段返回参数
 * <AUTHOR>
 * @date 2024-07-17
 */
@Data
public class LegVo {

    /** 是否航班结束 0 否 1 是 */
    private Integer isComp;

    /** 航班id */
    private Long flightId;

    /** 航段id */
    private Long legId;

    /** 航段 */
    private String leg;

    /** 计降 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date terminalSchemeLandInTime;

    /** 实降 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date terminalAlteratelAndInTime;
}
