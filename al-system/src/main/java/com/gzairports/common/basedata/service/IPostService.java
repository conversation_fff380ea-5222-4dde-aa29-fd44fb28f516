package com.gzairports.common.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.basedata.domain.BasePost;
import com.gzairports.common.basedata.domain.query.BasePostQuery;

import java.util.List;

/**
 * 邮局维护Service接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
public interface IPostService extends IService<BasePost> {

    /**
     * 查询邮局维护列表
     *
     * @param query 查询参数
     * @return 邮局维护列表
     */
    List<BasePost> selectBasePostList(BasePostQuery query);

    /**
     * 新增邮局维护
     *
     * @param basePost 邮局维护
     * @return 结果
     */
    int addBasePost(BasePost basePost);

    /**
     * 修改邮局维护
     *
     * @param basePost 邮局维护
     * @return 结果
     */
    int editBasePost(BasePost basePost);

    /**
     * 删除邮局维护
     *
     * @param id 邮局维护id集合
     * @return 结果
     */
    int delBasePost(Long id);

    /**
     * 根据id获取邮局维护
     *
     * @param id 邮局维护id
     * @return 邮局维护
     */
    BasePost getInfo(Long id);

    /**
     * 根据接收局/托运局名称查询邮局信息
     *
     * @param abb 接收局/托运局名称
     * @return 邮局信息
     */
    BasePost getInfoByCode(String abb,Integer type);

    /**
     * 根据始发港/目的港查询邮局信息
     *
     * @param port 始发港/目的港
     * @return 邮局信息
     */
    BasePost getInfoByPost(String port,Integer type);
}
