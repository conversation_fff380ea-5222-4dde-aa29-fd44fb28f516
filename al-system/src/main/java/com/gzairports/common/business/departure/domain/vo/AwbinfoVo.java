package com.gzairports.common.business.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "AwbinfoVo", description = "运单详细信息")
public class AwbinfoVo {

    /** 运单完整编码 */
    private String billid;

    /** 运单前缀 */
    private String stockpre;

    /** 运单号 不带运单类型 */
    private String stockno;

    /**开单时间*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date crtopetime;

    /** 做废标记 */
    private String delflag;

    /** 换单前单号 */
    private String previousbillid;

    /** 是否国内/国际 */
    private String domint;

    /** 是否海关监管 */
    private String customctl;

    /** 始发站 */
    private String sairportid;

    /** 目的站 */
    private String eairportid;

    /** 货物编码 */
    private String cargono;

    /** 货物名称(品名)*/
    private String cargonm;

    /** 承运人1 */
    private String by1;

    /** 到达站1 */
    private String dest1;

    /** 承运人2 */
    private String by2;

    /** 到达站2 */
    private String dest2;

    /** 承运人3 */
    private String by3;

    /** 到达站3 */
    private String dest3;

    /** 包装 */
    private String pack;

    /** 需冷藏 */
    private String refrigerated;

    /** 特殊处理代码 */
    private String specopeid;

    /** 特殊处理代码扩展 */
    private String specopeidext;

    /** 运费 */
    private BigDecimal carriage;

    /** 尺寸 */
    private String meas;

    /** 数量 */
    private BigDecimal pcs;

    /** 重量 */
    private BigDecimal weight;

    /** 计费重量 */
    private BigDecimal feewt;

    /** 体积 */
    private BigDecimal vol;

    /** 控制营业点（代理人） */
    private String ctrlopedepartment;

    /** 发货人代理人名称 */
    private String shprname;

    /** 发货人电话 */
    private String shprtel;

    /** 发货人地址 */
    private String shpraddress;

    /** 发货代理人简称 */
    private String shpcustomerid;

    /** 收货人 */
    private String cnsnname;

    /** 收货人电话 */
    private String cnsntel;

    /** 收货人地址 */
    private String cnsnaddress;

    /** 收货代理人代码 */
    private String csgcustomerid;

    /** 预配航班号承运人（航司） */
    private String preairline;

    /** 预配航班号 */
    private String preflightno;

    /** 运单类型 */
    private String stocktypeid;

    /** 预配航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date preflightdate;

    /** 预配航班号第二组 */
    private String preflightno1;

    /** 预配航班日期第二组 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date preflightdate1;

    /** 预配航班号承运人第二组 */
    private String preairline1;

    /** 储运注意事项 ，换单前单号 如902-03733590*/
    private String storeremark;

    /** 结算注意事项 */
    private String balanceremark;

    /** 进港是否中转 */
    private String istrans;

    /** 审核完毕 Y N*/
    private String chked;

    /** 发货人代码 */
    private String shpcustomer;

    /** 填开人 */
    private String crtagent;

    /** 填开日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date crtdate;

    /** 收货人身份证号码 */
    private String cnsnidcard;

    /** 是否真实收运标识 Y N*/
    private String isrcs;

    /**-----扩展字段-------------------------------*/

    /**运单状态 检枚举字段 CargoStatus */
    private String status;

    /**运单状态描述 */
    private String statusDesc;

    /** 状态更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date statusTime;

    /**已换运单完整编码*/
    private String nowBillid;

    /** 理货完成件数 */
    private Long lhpCs;

    /** 理货完成重量 */
    private BigDecimal lhpWeight;

}
