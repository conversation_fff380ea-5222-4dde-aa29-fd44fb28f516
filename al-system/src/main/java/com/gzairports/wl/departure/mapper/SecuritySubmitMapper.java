package com.gzairports.wl.departure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.departure.domain.Mawb;
import com.gzairports.hz.business.departure.domain.AirWaybill;
import com.gzairports.wl.departure.domain.query.SecurityQueryWl;
import com.gzairports.wl.departure.domain.vo.SecurityInfoWl;
import com.gzairports.wl.departure.domain.vo.SecurityVoWl;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 物流安检申报Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-14
 */

@Mapper
public interface SecuritySubmitMapper extends BaseMapper<Mawb> {
    /**
     * 安检申报列表
     * */
    List<SecurityVoWl> securitySubmitList(SecurityQueryWl query);

    /**
     * 安检申报详情
     * */
    SecurityInfoWl selectInfoById(Long id);
}
