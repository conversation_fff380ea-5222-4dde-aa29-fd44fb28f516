package com.gzairports.common.basedata.service.impl;


import com.gzairports.common.basedata.domain.BaseAirDifferent;
import com.gzairports.common.basedata.domain.query.AirDifferentQuery;
import com.gzairports.common.basedata.mapper.AirDifferentMapper;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.basedata.service.IAirDifferentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 航司机型差异Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Service
public class AirDifferentServiceImpl implements IAirDifferentService {

    @Autowired
    private AirDifferentMapper differentMapper;

    /**
     * 查询航司机型差异列表
     *
     * @param query 查询参数
     * @return 航司机型差异列表
     */
    @Override
    public List<BaseAirDifferent> airDifferentList(AirDifferentQuery query) {
        return differentMapper.airDifferentList(query);
    }

    /**
     * 新增航司机型差异
     *
     * @param different 航司机型差异配置
     * @return 结果
     */
    @Override
    public int addAirDifferent(BaseAirDifferent different) {
        if (different.getStartTime().after(different.getEndTime())) {
            throw new CustomException("开始时间不能晚于结束时间");
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        different.setCreateBy(loginUser.getUsername());
        different.setCreateTime(DateUtils.getNowDate());
        return differentMapper.addAirDifferent(different);
    }

    /**
     * 修改航司机型差异
     *
     * @param different 航司机型差异配置
     * @return 结果
     */
    @Override
    public int editAirDifferent(BaseAirDifferent different) {
        if (different.getStartTime().after(different.getEndTime())) {
            throw new CustomException("开始时间不能晚于结束时间");
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        different.setUpdateBy(loginUser.getUsername());
        different.setUpdateTime(DateUtils.getNowDate());
        return differentMapper.editAirDifferent(different);
    }

    /**
     * 删除航司机型差异
     * @param id 航司机型差异id
     * @return 结果
     */
    @Override
    public int removeAirDifferent(Long id) {
        return differentMapper.removeAirDifferent(id);
    }

    /**
     * 查看详情
     * @param id 航司机型差异id
     * @return 结果
     */
    @Override
    public BaseAirDifferent getInfo(Long id) {
        return differentMapper.getInfo(id);
    }
}
