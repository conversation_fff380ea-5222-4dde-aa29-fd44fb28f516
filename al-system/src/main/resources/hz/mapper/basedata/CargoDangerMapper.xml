<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.CargoDangerMapper">
    <update id="delDanger" parameterType="long">
        update base_cargo_danger set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <select id="selectDangerList" resultType="com.gzairports.common.basedata.domain.BaseCargoDanger">
        select id, special_code, un_code, category, item_category, emergency_code, example, remark, create_time, update_by,
               update_time, create_by from base_cargo_danger
        <where>
            and is_del = 0
            <if test="specialCode != null  and specialCode != ''"> and special_code like concat('%', #{specialCode}, '%')</if>
            <if test="unCode != null and unCode != ''"> and un_code like concat('%', #{unCode}, '%')</if>
        </where>
    </select>
</mapper>