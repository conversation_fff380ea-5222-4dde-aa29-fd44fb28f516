package com.gzairports.common.basedata.mapper;

import com.gzairports.common.basedata.domain.BaseAirType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 机型管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-14
 */
@Mapper
public interface BaseAirTypeMapper 
{
    /**
     * 查询机型管理
     * 
     * @param id 机型管理主键
     * @return 机型管理
     */
    public BaseAirType selectBaseAirTypeById(Long id);

    /**
     * 查询机型管理列表
     * 
     * @param baseAirType 机型管理
     * @return 机型管理集合
     */
    public List<BaseAirType> selectBaseAirTypeList(BaseAirType baseAirType);

    /**
     * 新增机型管理
     * 
     * @param baseAirType 机型管理
     * @return 结果
     */
    public int insertBaseAirType(BaseAirType baseAirType);

    /**
     * 修改机型管理
     * 
     * @param baseAirType 机型管理
     * @return 结果
     */
    public int updateBaseAirType(BaseAirType baseAirType);

    /**
     * 删除机型管理
     * 
     * @param id 机型管理主键
     * @return 结果
     */
    public int deleteBaseAirTypeById(Long id);

    /**
     * 批量删除机型管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBaseAirTypeByIds(Long[] ids);

    /**
     * 根据type查询机型
     * @param type type
     * @return 结果
     */
    BaseAirType selectByType(String type);
}
