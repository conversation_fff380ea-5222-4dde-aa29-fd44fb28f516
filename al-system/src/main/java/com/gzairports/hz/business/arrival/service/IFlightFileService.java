package com.gzairports.hz.business.arrival.service;

import com.gzairports.common.basedata.domain.BaseCargoCategory;
import com.gzairports.common.business.departure.domain.vo.FlightVo;
import com.gzairports.hz.business.arrival.domain.query.FlightFileQuery;
import com.gzairports.hz.business.arrival.domain.query.TallyManifestQuery;
import com.gzairports.hz.business.arrival.domain.vo.*;

import java.util.List;

/**
 * 航班文件Service接口
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
public interface IFlightFileService {

    /**
     * 进港运单录入
     * @param vo 进港运单数据
     * @return 结果
     */
    int enter(EnterWaybillVo vo);

    /**
     * 根据运单号查询录入信息
     * @param query 查询条件
     * @return 结果
     */
    EnterWaybillVo getWaybillInfo(FlightFileQuery query);

    /**
     * 运单修改保存
     * @param vo 运单修改参数
     * @return 结果
     */
    int edit(EnterWaybillVo vo);

    /**
     * 根据航班日期航班号查询航段
     * @param query 查询参数
     * @return 航段列表
     */
    List<LegVo> getLeg(FlightFileQuery query);

    /**
     * 查询运单列表
     * @param query 查询条件
     * @return 运单列表
     */
    FlightFileVo waybillList(FlightFileQuery query);

    /**
     * 删除
     * @param id 运单id
     * @return 结果
     */
    int del(Long id,Long legId);

    /**
     * 保存航班操作
     * @param query 查询条件
     * @return 结果
     */
    int saveFlightOper(FlightFileQuery query);

    /**
     * 理货舱单
     * @param query 查询参数
     * @return 理货舱单列表
     */
    TallyManifestVo tallyManifest(TallyManifestQuery query);

    /**
     * 生成理货舱单
     * @param query 查询条件
     * @return 结果
     */
    int createTallyManifest(TallyManifestQuery query);

    /**
     * 理货列表查询
     * @param query 查询条件
     * @return 列表
     */
    FlightFileVo tallyList(FlightFileQuery query);

    /**
     * 查询理货运单详情
     * @param query 查询条件
     * @return 理货详情数据
     */
    TallyWaybillVo getTallyInfo(FlightFileQuery query);

    /**
     * 理货保存
     * @param vo 理货保存参数
     * @return 结果
     */
    int tallySave(TallyWaybillVo vo);

    /**
     * app进港理货航班列表
     * @param query 查询条件
     * @return 航班列表
     */
    AppFlightVo appFlightList(FlightFileQuery query);

    /**
     * app进港理货运单列表
     * @param flightId 航班id
     * @return 运单列表
     */
    AppWaybillVo appWaybillList(Long flightId);

    /**
     * app理货完成
     * @param flightId 航班id
     * @return 结果
     */
    int appTallyComp(Long flightId);

    /**
     * 根据代理人代码查询代理人
     * @param agentCode 代理代码
     * @return 代理名称
     */
    String shipperForAgentCode(String agentCode);

    /**
     * 修改理货历史
     * @param vo 理货历史对象
     * @return 结果
     */
    int tallyUpdate(TallyHistoryVo vo);

    /**
     * @author: lan
     * @description: 根据代理人查询代理人代码
     * @param: [shipper] 代理人
     * @date: 2024/10/3
     * @return: java.lang.String
     */
    String agentCodeForShipper(String shipper);

    /**
     * @author: lan
     * @description: 根据航班号得到航司以及对应的运单前缀
     * @date: 2024/10/24
     */
    FlightNoVo getPrefix(FlightFileQuery query);

    /**
     * @author: lan
     * @description: 取消理货历史
     * @date: 2024/10/24
     */
    int tallyCancel(TallyHistoryVo vo);

    /**
     * 批量理货
     * @param tallyIds 理货id集合
     * @return 结果
     */
    int batchTally(List<String> tallyIds);

    /**
     * @author: lan
     * @description: 根据航班号和运单号找大类 在得到默认的库位信息
     * @param: [query]
     * @date: 2024/11/6
     * @return: com.gzairports.common.basedata.domain.BaseCargoCategory
     */
    BaseCargoCategory getLocator(FlightFileQuery query);

    int compFlight(Long flightId);

    int openFlight(Long flightId);

    int examine(Long orderId);

    int cancelExamine(Long orderId);
}
