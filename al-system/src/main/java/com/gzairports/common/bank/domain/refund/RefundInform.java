package com.gzairports.common.bank.domain.refund;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.List;

/**
 * @author: lan
 * @Desc: 退款inform字段
 * @create: 2025-03-18 14:27
 **/

@Data
@XmlRootElement(name = "InFormBody")
@XmlAccessorType(XmlAccessType.FIELD)
public class RefundInform implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 子订单数 10 Y
     * 子订单数，与prodList一一对应
     * */
    @XmlElement(name = "recNum")
    private Integer recNum;

    /** 子订单列表 Y
     * prodList内容包含如下：
     * 该inForm字段以xml格式构造报文，并将该xml以BASE64编码（UTF-8）存放于inForm字段中
     * */
//    @XmlElement(name = "prodList")
    private List<RefundProdList> prodList;

    /** 客户附言 255 N
     * */
    @XmlElement(name = "postscript")
    private String postscript;

}
