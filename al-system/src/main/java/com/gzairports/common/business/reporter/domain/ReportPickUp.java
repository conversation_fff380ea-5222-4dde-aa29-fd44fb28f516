package com.gzairports.common.business.reporter.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-02-19
 */
@Data
@TableName("all_report_pick_up")
public class ReportPickUp {

    /** 主键id */
    private Long id;

    /** 流水号 */
    private String serialNo;

    /** 包含运单号 */
    private String waybillCode;

    /** 提货客户 */
    private String customerName;

    /** 提货人证件类型 */
    private String customerIdType;

    /** 提货人证件号 */
    private String customerIdNo;

    /** 提货人手机号 */
    private String customerPhone;

    /** 经办人 */
    private String handleBy;

    /** 支付状态 */
    private String payStatus;

    /** 支付方式（0 现金 1月结 2余额 3预授权） */
    private String payMethod;

    /** 总费用 */
    private BigDecimal totalCost;

    /** 总计费重量 */
    private BigDecimal totalChargeWeight;

    /** 总件数 */
    private Integer totalQuantity;

    /** 总重量 */
    private BigDecimal totalWeight;

    /** 总票数 */
    private Integer totalCount;

    /** 办单时间  */
    private LocalDateTime pickUpTime;
}
