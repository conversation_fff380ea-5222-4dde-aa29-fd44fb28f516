<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.CityCodeMapper">
    <select id="selectCityCodeList" resultType="com.gzairports.common.basedata.domain.BaseCityCode">
        select id, code, chinese_name, english_name, state_or_province_chinese, state_or_province_english, region, four_code, time_zone from base_city_code
        <where>
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="code != null  and code != ''"> and code like concat('%',#{code},'%')</if>
            <if test="chineseName != null  and chineseName != ''"> and chinese_name like concat('%',#{chineseName},'%')</if>
            <if test="englishName != null  and englishName != ''"> and english_name like concat('%',#{englishName},'%')</if>
            <if test="stateOrProvinceChinese != null  and stateOrProvinceChinese != ''"> and state_or_province_chinese like concat('%',#{stateOrProvinceChinese},'%')</if>
            <if test="region != null  and region != ''"> and region like concat('%',#{region},'%')</if>
        </where>
    </select>
    <select id="selectCityListByName" resultType="com.gzairports.common.basedata.domain.BaseCityCode">
        select id, code, chinese_name, english_name, state_or_province_chinese, state_or_province_english, region, four_code, time_zone
        from base_city_code where is_del = 0 and chinese_name like '%${cityName}%'
    </select>
</mapper>
