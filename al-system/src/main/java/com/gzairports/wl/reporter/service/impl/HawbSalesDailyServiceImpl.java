package com.gzairports.wl.reporter.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzairports.common.basedata.mapper.BaseAgentMapper;
import com.gzairports.common.business.departure.mapper.CostDetailMapper;
import com.gzairports.common.business.reporter.domain.ReportDataHawb;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.poi.ExcelToPdfUtil;
import com.gzairports.common.utils.poi.ReportExcelUtil;
import com.gzairports.hz.business.departure.mapper.AllAirWaybillMapper;
import com.gzairports.wl.reporter.domain.GoodsWeightStatistics;
import com.gzairports.wl.reporter.domain.ReportSetField;
import com.gzairports.wl.reporter.domain.query.GoodsWeightStatisticsQuery;
import com.gzairports.wl.reporter.domain.query.HawbSalesQuery;
import com.gzairports.wl.reporter.domain.query.ReportExportData;
import com.gzairports.wl.reporter.domain.vo.GoodsWeightStatisticsVO;
import com.gzairports.wl.reporter.domain.vo.ReportTotalVo;
import com.gzairports.wl.reporter.mapper.HawbSalesDailyMapper;
import com.gzairports.wl.reporter.mapper.ReportSetFieldMapper;
import com.gzairports.wl.reporter.service.IHawbSalesDailyService;
import com.itextpdf.text.DocumentException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-02-20
 */
@Service
public class HawbSalesDailyServiceImpl implements IHawbSalesDailyService {

    @Autowired
    private ReportSetFieldMapper setFieldMapper;

    @Autowired
    private HawbSalesDailyMapper salesDailyMapper;

    @Autowired
    private BaseAgentMapper baseAgentMapper;

    @Autowired
    private AllAirWaybillMapper allAirWaybillMapper;

    @Autowired
    private CostDetailMapper costDetailMapper;

    @Override
    public ReportExportData queryExportDataList(HawbSalesQuery query) {
        List<ReportSetField> reportDataFields = setFieldMapper.selectReportDataFields(query.getReportTitle());
        if (CollectionUtils.isEmpty(reportDataFields)) {
            return ReportExportData.builder().build();
        }

        //查询统计报表的用户信息
        StringBuilder reportMsg = new StringBuilder();
        String nickName = SecurityUtils.getNickName();
        reportMsg.append("制单人：").append(StrUtil.isEmpty(nickName) ? "   " : nickName).append("  ");
        String now = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        reportMsg.append("统计时间：").append(now);

        //可排序字段
        String sortFieldsStr = reportDataFields.stream()
                .filter(e -> e.getSortType() != 0)
                .map(reportSetField -> reportSetField.getFieldName() + (reportSetField.getSortType() == 1 ? " ASC" : " DESC"))
                .collect(Collectors.joining(","));

        List<String> fieldNames = reportDataFields.stream().map(ReportSetField::getFieldName).distinct().collect(Collectors.toList());
        String fieldNamesStr = String.join(",", fieldNames);

        Page<ReportDataHawb> page = (query.getPageNum() == null || query.getPageSize() == null) ?
                new Page<>(1, -1) :
                new Page<>(query.getPageNum(), query.getPageSize());
        Page<ReportDataHawb> pageData = salesDailyMapper.selectHawbFieldByName(fieldNamesStr, query, page, sortFieldsStr);

        List<Map<String, Object>> columnNameToValueList = filterFields(pageData.getRecords(), fieldNames);
//convertRowMapDataList(pageData.getRecords(), fieldNames);
        if (pageData.getPages() == pageData.getCurrent() || pageData.getSize() == -1) {
            ReportTotalVo vo = salesDailyMapper.selectHawbTotal(query);
            Map<String, Object> totalReport = new LinkedHashMap<>();
            totalReport.put("lastCount", "运单总数：" + vo.getCount());
            totalReport.put("lastQuantity", "总件数：" + (fieldNames.contains("quantity") ? vo.getQuantity() : 0));
            totalReport.put("lastWeight", "总量：" + (fieldNames.contains("weight") ? vo.getWeight() : 0));
            totalReport.put("lastCostSum", "总金额：" + (fieldNames.contains("cost_sum") ? vo.getCostSum() : 0));
            columnNameToValueList.add(totalReport);
        }

        List<String> fieldNameCnList = reportDataFields.stream().map(ReportSetField::getFieldNameCn).distinct().collect(Collectors.toList());
        fieldNameCnList.add(0, "序号");

        ReportExportData build = ReportExportData.builder()
                .reportTitle(query.getReportTitle())
                .reportMsg(reportMsg.toString())
                .fieldNameCnList(fieldNameCnList)
                .columnNameToValueList(columnNameToValueList)
                .build();
        return build;
    }

    /**
     * 分页查询报表数据
     *
     * @param query 查询参数
     * @return 结果
     */
    @Override
    public IPage<ReportDataHawb> pageQuery(HawbSalesQuery query) {
        List<ReportSetField> reportDataFields = setFieldMapper.selectReportDataFields(query.getReportTitle());
        if (CollectionUtils.isEmpty(reportDataFields)) {
            return null;
        }
        String fieldNames = reportDataFields.stream().map(ReportSetField::getFieldName).collect(Collectors.joining(","));
        Page<ReportDataHawb> pageParam = new Page<>(query.getPageNum() != null ? query.getPageNum() : 1, query.getPageSize() != null ? query.getPageSize() : 10);
        return salesDailyMapper.selectHawbPageData(pageParam, fieldNames, query);
    }

    @Override
    public void export(ReportExportData reportExportData, HttpServletResponse response) {
        try {
            ReportExcelUtil.exportToExcel(response.getOutputStream(),reportExportData.getColumnNameToValueList(), reportExportData.getFieldNameCnList(), reportExportData.getReportTitle(), reportExportData.getReportMsg());
            //对文件名进行 URL 编码
            String fileName = reportExportData.getReportTitle() + "-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + encodedFileName + ".xlsx");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

//        ExcelUtil<ReportData> util = new ExcelUtil<>(ReportData.class);
//        util.exportExcel(response, pageData.getRecords(), query.getReportTitle());
    }

    @Override
    public void printPDF(ReportExportData reportExportData , HttpServletResponse response) {

        // 设置响应头
        try(ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            // 将 Workbook 写入 ByteArrayOutputStream

            ReportExcelUtil.exportToExcel(baos, reportExportData.getColumnNameToValueList(), reportExportData.getFieldNameCnList(), reportExportData.getReportTitle(), reportExportData.getReportMsg());
            //对文件名进行 URL 编码
            String fileName = reportExportData.getReportTitle() + "-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "attachment; filename=" + encodedFileName + ".pdf");

            // 从 ByteArrayOutputStream 获取 ByteArrayInputStream
            ByteArrayInputStream inputStream = new ByteArrayInputStream(baos.toByteArray());
            ExcelToPdfUtil.excelToPdf(inputStream, response.getOutputStream(), ".xlsx");
        } catch (IOException | DocumentException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 根据给定的数据库字段名称列表，从ReportData列表中提取相应的字段。
     *
     * @param reportDataList 包含ReportData对象的列表
     * @param dbFieldNames   数据库字段名称列表
     * @return 包含过滤后数据的列表，每项为一个Map，键是数据库字段名，值是字段值
     */
    public static List<Map<String, Object>> filterFields(List<ReportDataHawb> reportDataList, List<String> dbFieldNames) {
        List<Map<String, Object>> filteredDataList = new ArrayList<>();
        for (ReportDataHawb reportData : reportDataList) {
            //需要保证顺序插入
            Map<String, Object> filteredData = new LinkedHashMap<>();
            Class<?> clazz = reportData.getClass();
            for (String dbFieldName : dbFieldNames) {
                if (StringUtils.isEmpty(dbFieldName)) {
                    continue;
                }
                try {
                    String camelCaseFieldName = StringUtils.underscoreToCamelCase(dbFieldName);
                    Field field = clazz.getDeclaredField(camelCaseFieldName);
                    field.setAccessible(true);
                    filteredData.put(dbFieldName, field.get(reportData));
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    throw new CustomException("处理字段 " + dbFieldName + " 时发生错误: " + e.getMessage());
                }
            }
            filteredDataList.add(filteredData);
        }
        return filteredDataList;
    }

    @Override
    public GoodsWeightStatisticsVO selectGoodsWeightStatistics(GoodsWeightStatisticsQuery query) {
        Page<Object> pageParam = new Page<>(query.getPageNum(), query.getPageSize());
        String t = query.getStatisticsType();
        if ("M".equals(t)) {
            query.setStatisticsType("%Y-%m");
        } else {
            //默认按天统计
            query.setStatisticsType("%Y-%m-%d");
        }
        IPage<GoodsWeightStatistics> pageData = "HAWB".equals(query.getWaybillType())?
                salesDailyMapper.selectHAWBGoodsWeightStatistics(pageParam, query):
                salesDailyMapper.selectGoodsWeightStatistics(pageParam, query);
        GoodsWeightStatisticsVO goodsWeightStatisticsVO = new GoodsWeightStatisticsVO();
        List<GoodsWeightStatistics> records = pageData.getRecords();
        goodsWeightStatisticsVO.setStatistics(records);
        goodsWeightStatisticsVO.setTotal(pageData.getTotal());
        //求和
        BigDecimal weightTotal = records.stream()
                .map(GoodsWeightStatistics::getWeight)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        goodsWeightStatisticsVO.setWeightTotal(weightTotal);
        return goodsWeightStatisticsVO;
    }
}
