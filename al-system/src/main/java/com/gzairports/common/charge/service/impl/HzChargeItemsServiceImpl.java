package com.gzairports.common.charge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.basedata.domain.BaseCargoCode;
import com.gzairports.common.basedata.mapper.CargoCodeMapper;
import com.gzairports.common.charge.domain.*;
import com.gzairports.common.charge.domain.vo.HzItemRuleVo;
import com.gzairports.common.charge.domain.vo.TimeSlot;
import com.gzairports.common.charge.mapper.*;
import com.gzairports.common.core.domain.entity.SysDept;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.charge.domain.query.ItemsQuery;
import com.gzairports.common.charge.domain.vo.HzChargeItemsVo;
import com.gzairports.common.charge.domain.vo.HzItemInfoVo;
import com.gzairports.common.charge.service.IHzChargeItemsService;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.utils.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 货站收费条目Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-13
 */
@Service
public class HzChargeItemsServiceImpl implements IHzChargeItemsService
{
    @Autowired
    private HzChargeItemsMapper hzChargeItemsMapper;

    @Autowired
    private CargoCodeMapper cargoCodeMapper;

    @Autowired
    private HzChargeRuleMapper ruleMapper;

    @Autowired
    private HzChargeItemRuleMapper itemRuleMapper;

    @Autowired
    private HzChargeIrRelationMapper relationMapper;

    private static final String RULE_NAME = "TimeSegmentBillingRule.class";

    /**
     * 查询货站收费条目
     * 
     * @param id 货站收费条目主键
     * @return 货站收费条目
     */
    @Override
    public HzItemInfoVo selectHzChargeItemsById(Long id)
    {
        HzItemInfoVo vo = new HzItemInfoVo();
        HzChargeItems items = hzChargeItemsMapper.selectHzChargeItemsById(id);
        if (items.getEndEffectiveTime().before(new Date())){
            items.setStatus(2);
        }
        BeanUtils.copyProperties(items,vo);
        List<HzChargeIrRelation> relations = relationMapper.selectList(new QueryWrapper<HzChargeIrRelation>().eq("item_id",id).eq("is_del",0));
        List<HzItemRuleVo> hzItemRuleVos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(relations)){
            for (HzChargeIrRelation relation : relations) {
                HzItemRuleVo ruleVo = new HzItemRuleVo();
                HzChargeRule rule = ruleMapper.selectById(relation.getRuleId());
                ruleVo.setChargeRuleId(relation.getRuleId());
                ruleVo.setRuleName(rule.getRuleName());
                ruleVo.setClassName(rule.getClassName());
                ruleVo.setItemRuleName(relation.getItemRuleName());
                if (relation.getCargoName() != null){
                    String[] split = relation.getCargoName().split(",");
                    List<String> cargoNames = new ArrayList<>(Arrays.asList(split));
                    ruleVo.setCargoNames(cargoNames);
                }
                if (relation.getPrefix() != null){
                    String[] split = relation.getPrefix().split(",");
                    List<String> prefixList = new ArrayList<>(Arrays.asList(split));
                    ruleVo.setPrefixs(prefixList);
                }
                if (relation.getCategory() != null){
                    String[] split = relation.getCategory().split(",");
                    List<String> categoryList = new ArrayList<>(Arrays.asList(split));
                    ruleVo.setCategoryList(categoryList);
                }
                ruleVo.setPriority(relation.getPriority());
                ruleVo.setCategory(relation.getCategory());
                ruleVo.setId(relation.getId());
                ruleVo.setIsExit(relation.getIsExit());
                ruleVo.setIsSouth(relation.getIsSouth());
                ruleVo.setCrossAir(relation.getCrossAir());
                if (StringUtils.isNotEmpty(relation.getNoCharge())){
                    String[] split = relation.getNoCharge().split(",");
                    List<String> noCharges = new ArrayList<>(Arrays.asList(split));
                    ruleVo.setNoCharges(noCharges);
                }
                List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>().eq("ir_id",relation.getId()).eq("is_del",0));
                ruleVo.setList(itemRules);
                hzItemRuleVos.add(ruleVo);
            }
        }
        vo.setRules(hzItemRuleVos);
        return vo;
    }

    /**
     * 查询货站收费条目列表
     * 
     * @param query 货站收费条目查询条件
     * @return 货站收费条目
     */
    @Override
    public List<HzChargeItemsVo> selectHzChargeItemsList(ItemsQuery query)
    {
        List<HzChargeItemsVo> hzChargeItemsVos = hzChargeItemsMapper.selectHzChargeItemsList(query);
        for (HzChargeItemsVo hzChargeItemsVo : hzChargeItemsVos) {
            if (hzChargeItemsVo.getEndEffectiveTime().before(new Date())){
                hzChargeItemsVo.setStatus(2);
            }
            switch (hzChargeItemsVo.getStatus()){
                case 0:
                    hzChargeItemsVo.setStatusStr("未启用");
                    break;
                case 1:
                    hzChargeItemsVo.setStatusStr("启用");
                    break;
                case 2:
                    hzChargeItemsVo.setStatusStr("已失效");
                    break;
                default:
                    hzChargeItemsVo.setStatusStr("");
                    break;
            }
            List<HzChargeIrRelation> relations = relationMapper.selectList(new QueryWrapper<HzChargeIrRelation>().eq("item_id",hzChargeItemsVo.getId()));
            if (!CollectionUtils.isEmpty(relations)){
                List<Long> chargeRuleIds = relations.stream().map(HzChargeIrRelation::getRuleId).distinct().collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(chargeRuleIds)){
                    List<HzChargeRule> hzChargeRules = ruleMapper.selectBatchIds(chargeRuleIds);
                    if (!CollectionUtils.isEmpty(hzChargeRules)){
                        String ruleName = hzChargeRules.stream().map(HzChargeRule::getRuleName).collect(Collectors.joining(","));
                        hzChargeItemsVo.setRuleName(ruleName);
                    }
                }
            }
            if ("DEP".equals(hzChargeItemsVo.getOperationType())){
                hzChargeItemsVo.setType("出港");
            }else {
                hzChargeItemsVo.setType("进港");
            }

            if (hzChargeItemsVo.getIsDefault() == 0){
                hzChargeItemsVo.setDefaultCharge("否");
            }else {
                hzChargeItemsVo.setDefaultCharge("是");
            }

            if (hzChargeItemsVo.getIsServiceItem() == 0){
                hzChargeItemsVo.setServiceItem("否");
            }else {
                hzChargeItemsVo.setServiceItem("是");
            }

            if (hzChargeItemsVo.getIsEdit() == 0){
                hzChargeItemsVo.setEdit("否");
            }else {
                hzChargeItemsVo.setEdit("是");
            }
        }
        return hzChargeItemsVos;
    }

    /**
     * 新增货站收费条目
     * 
     * @param vo 货站收费条目
     * @return 结果
     */
    @Override
    public int insertHzChargeItems(HzItemInfoVo vo)
    {
        if (vo == null){
            throw new CustomException("货站收费条目不能为空");
        }
        List<HzChargeItems> hzChargeItemsList = hzChargeItemsMapper.selectList(new QueryWrapper<HzChargeItems>()
                .eq("charge_abb", vo.getChargeAbb())
                .eq("operation_type", vo.getOperationType()));

        if (!CollectionUtils.isEmpty(hzChargeItemsList)) {
            for (HzChargeItems hzChargeItems : hzChargeItemsList) {
                if (hzChargeItems.getEndEffectiveTime() != null && hzChargeItems.getStartEffectiveTime() != null) {
                    if (!(hzChargeItems.getEndEffectiveTime().before(vo.getStartEffectiveTime()) || vo.getEndEffectiveTime().before(hzChargeItems.getStartEffectiveTime()))) {
                        throw new CustomException("相同收费项目时间段不能重叠");
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(vo.getRules())){
            boolean b = vo.getRules().stream()
                    .collect(Collectors.groupingBy(HzItemRuleVo::getPriority, Collectors.counting()))
                    .values()
                    .stream()
                    .anyMatch(count -> count > 1);
            if (b){
                throw new CustomException("同一个收费项目里面不能存在相同优先级的收费规则");
            }
        }
        checkTimeSlot(vo);
        HzChargeItems items = new HzChargeItems();
        BeanUtils.copyProperties(vo,items);
        hzChargeItemsMapper.insertHzChargeItems(items);
        for (HzItemRuleVo rule : vo.getRules()) {
            HzChargeIrRelation relation = new HzChargeIrRelation();
            if (!CollectionUtils.isEmpty(rule.getCargoNames())){
                String cargoName = String.join(",", rule.getCargoNames());
                relation.setCargoName(cargoName);
            }
            if (!CollectionUtils.isEmpty(rule.getCategoryList())){
                String category = String.join(",", rule.getCategoryList());
                relation.setCategory(category);
            }
            if (!CollectionUtils.isEmpty(rule.getNoCharges())){
                String noCharge = String.join(",", rule.getNoCharges());
                relation.setNoCharge(noCharge);
            }
            if (!CollectionUtils.isEmpty(rule.getPrefixs())){
                String prefix = String.join(",", rule.getPrefixs());
                relation.setPrefix(prefix);
            }
            relation.setItemId(items.getId());
            relation.setRuleId(rule.getChargeRuleId());
            relation.setPriority(rule.getPriority());
            relation.setItemRuleName(rule.getItemRuleName());
            relation.setIsExit(rule.getIsExit());
            relation.setIsSouth(rule.getIsSouth());
            relation.setCrossAir(rule.getCrossAir());
            relationMapper.insert(relation);
            for (HzChargeItemRule ruleRule : rule.getList()) {
                ruleRule.setIrId(relation.getId());
                itemRuleMapper.insert(ruleRule);
            }
        }
        return 1;
    }

    /**
     * 修改货站收费条目
     * 
     * @param vo 货站收费条目
     * @return 结果
     */
    @Override
    public int updateHzChargeItems(HzItemInfoVo vo)
    {
        if (vo == null){
            throw new CustomException("货站收费条目不能为空");
        }
        List<HzChargeItems> hzChargeItemsList = hzChargeItemsMapper.selectList(new QueryWrapper<HzChargeItems>()
                .eq("charge_abb", vo.getChargeAbb())
                .eq("operation_type", vo.getOperationType()));
        if (!CollectionUtils.isEmpty(hzChargeItemsList)) {
            List<HzChargeItems> collect = hzChargeItemsList.stream().filter(e -> !e.getId().equals(vo.getId())).collect(Collectors.toList());
            for (HzChargeItems hzChargeItems : collect) {
                if (hzChargeItems.getEndEffectiveTime() != null && hzChargeItems.getStartEffectiveTime() != null) {
                    if (!(hzChargeItems.getEndEffectiveTime().before(vo.getStartEffectiveTime()) || vo.getEndEffectiveTime().before(hzChargeItems.getStartEffectiveTime()))) {
                        throw new CustomException("相同收费项目时间段不能重叠");
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(vo.getRules())){
            boolean b = vo.getRules().stream()
                    .collect(Collectors.groupingBy(HzItemRuleVo::getPriority, Collectors.counting()))
                    .values()
                    .stream()
                    .anyMatch(count -> count > 1);
            if (b){
                throw new CustomException("同一个收费项目里面不能存在相同优先级的收费规则");
            }
        }
        checkTimeSlot(vo);
        List<HzChargeIrRelation> relations = relationMapper.selectList(new QueryWrapper<HzChargeIrRelation>().eq("item_id", vo.getId()));
        for (HzChargeIrRelation relation : relations) {
            relation.setIsDel(1);
            relationMapper.updateById(relation);
        }
        if (!CollectionUtils.isEmpty(vo.getRules())){
            for (HzItemRuleVo ruleVo : vo.getRules()) {
                List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>().eq("ir_id", ruleVo.getId()));
                for (HzChargeItemRule itemRule : itemRules) {
                    itemRule.setIsDel(1);
                    itemRuleMapper.updateById(itemRule);
                }
                HzChargeIrRelation relation = new HzChargeIrRelation();
                if (!CollectionUtils.isEmpty(ruleVo.getCargoNames())){
                    String cargoName = String.join(",", ruleVo.getCargoNames());
                    relation.setCargoName(cargoName);
                }
                if (!CollectionUtils.isEmpty(ruleVo.getCategoryList())){
                    String category = String.join(",", ruleVo.getCategoryList());
                    relation.setCategory(category);
                }
                if (!CollectionUtils.isEmpty(ruleVo.getNoCharges())){
                    String noCharge = String.join(",", ruleVo.getNoCharges());
                    relation.setNoCharge(noCharge);
                }
                if (!CollectionUtils.isEmpty(ruleVo.getPrefixs())){
                    String prefix = String.join(",", ruleVo.getPrefixs());
                    relation.setPrefix(prefix);
                }
                relation.setItemId(vo.getId());
                relation.setRuleId(ruleVo.getChargeRuleId());
                relation.setPriority(ruleVo.getPriority());
                relation.setItemRuleName(ruleVo.getItemRuleName());
                relation.setIsExit(ruleVo.getIsExit());
                relation.setIsSouth(ruleVo.getIsSouth());
                relation.setCrossAir(ruleVo.getCrossAir());
                relation.setId(null);
                relationMapper.insert(relation);
                for (HzChargeItemRule ruleRule : ruleVo.getList()) {
                    ruleRule.setIrId(relation.getId());
                    ruleRule.setId(null);
                    itemRuleMapper.insert(ruleRule);
                }
            }
        }
        HzChargeItems items = new HzChargeItems();
        BeanUtils.copyProperties(vo,items);
        items.setIsDefault(vo.getIsDefault());
        items.setIsEdit(vo.getIsEdit());
        return hzChargeItemsMapper.updateHzChargeItems(items);
    }

    /**
     * 如果是分时段计费校验时间段重叠
     * @param vo 新增或修改参数
     */
    private void checkTimeSlot(HzItemInfoVo vo) {
        //时间段不应该重叠+最低收费应该低于最高收费
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        for (HzItemRuleVo ruleVo : vo.getRules()) {
            if (RULE_NAME.equals(ruleVo.getClassName())){
                List<TimeSlot> list = new ArrayList<>();
                for (HzChargeItemRule rule : ruleVo.getList()) {
                    if (rule.getMinimum() != null && rule.getMaximum() !=null && rule.getMinimum().compareTo(rule.getMaximum()) > 0) {
                        throw new CustomException("最低收费不能高于最高收费");
                    }
                    TimeSlot timeSlot = new TimeSlot();
                    LocalTime start = LocalTime.parse(rule.getTimeSlot1(), formatter);
                    timeSlot.setStart(start);
                    LocalTime ent = LocalTime.parse(rule.getTimeSlot2(), formatter);
                    timeSlot.setEnd(ent);
                    list.add(timeSlot);
                    rule.setTimeSlot(rule.getTimeSlot1() + "," + rule.getTimeSlot2());
                }
                if (hasOverlap(list)) {
                    throw new CustomException("时间段不能重叠");
                }
            }
        }
    }

    /**
     * 批量删除货站收费条目
     * 
     * @param ids 需要删除的货站收费条目主键
     * @return 结果
     */
    @Override
    public int deleteHzChargeItemsByIds(Long[] ids)
    {
        return hzChargeItemsMapper.deleteHzChargeItemsByIds(ids);
    }

    /**
     * 删除货站收费条目信息
     * 
     * @param id 货站收费条目主键
     * @return 结果
     */
    @Override
    public int deleteHzChargeItemsById(Long id)
    {
        return hzChargeItemsMapper.deleteHzChargeItemsById(id);
    }

    /**
     * 根据大类得到货品代码,大类可以为空
     * @param categoryCode 查询条件
     * @return 结果
     */
    @Override
    public List<BaseCargoCode> selectCargoCodeList(List<String> categoryCode) {
        return cargoCodeMapper.selectCargoCodeListByCodes(categoryCode);
    }


    /**
     * 分时段计费判断时间重叠
     * @param timeSlots 时间段集合
     * @return 是否重叠
     */
    public boolean hasOverlap(List<TimeSlot> timeSlots) {
        if (timeSlots.size() < 2) {
            return false;
        }
        timeSlots.sort(Comparator.comparing(TimeSlot::getStart));

        for (int i = 0; i < timeSlots.size() - 1; i++) {
            TimeSlot current = timeSlots.get(i);
            TimeSlot next = timeSlots.get(i + 1);

            if (next.getStart().isBefore(current.getEnd())) {
                return true;
            }
        }

        return false;
    }
}
