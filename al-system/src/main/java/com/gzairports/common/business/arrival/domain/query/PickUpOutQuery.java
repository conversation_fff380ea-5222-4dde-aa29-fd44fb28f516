package com.gzairports.common.business.arrival.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 提货出库查询参数
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
@Data
public class PickUpOutQuery {

    /** 运单号 */
    private String waybillCode;

    /** 办单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 办单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 代理人 */
    private String shipper;

    /** 收货人 */
    private String consign;

    /** 收货人证件号 */
    private String consignIdCar;

    /** 流水号 */
    private String serialNo;

    /** 提货码 */
    private String pickUpCode;

    /** 查询时间 */
    private Date queryDate;
}
