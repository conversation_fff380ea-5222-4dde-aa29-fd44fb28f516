package com.gzairports.wl.reporter.domain.vo;

import com.gzairports.wl.reporter.domain.ReportField;
import com.gzairports.wl.reporter.domain.vo.SelectFieldsVo;
import com.gzairports.wl.reporter.domain.vo.SortFieldVo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-18 16:45
 */
@Data
public class ReportSetVo {

    /** 报表设置id */
    private Long id;

    /** 报表单证类型 */
    private String reportType;

    /** 单证来源（结算单位） */
    private String reportSource;

    /** 报表标题 */
    private String reportTitle;

    /** 已选择字段 */
    private List<SelectFieldsVo> fieldList;

    /** 排序字段 */
    private List<SortFieldVo> sortList;

    /** 可选字段 */
    private List<ReportField> fields;
}
