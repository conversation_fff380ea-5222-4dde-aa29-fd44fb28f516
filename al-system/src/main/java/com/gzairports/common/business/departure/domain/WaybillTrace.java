package com.gzairports.common.business.departure.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 运单跟踪表
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Data
@TableName("wl_dep_waybill_trace")
public class WaybillTrace {

    /** 主键id */
    private Long id;

    /** 运单号 */
    private String waybillCode;

    /** 节点名称 */
    private String nodeName;

    /** 节点操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operTime;

    /** 节点操作件数 */
    private Integer operPieces;

    /** 发送时重量 */
    private BigDecimal operWeight;

    /** 计费重量 */
    private BigDecimal chargeWeight;

    /** 金额 */
    private BigDecimal payMoney;

    /** 退回金额 */
    private BigDecimal returnMoney;

    /** 航班号 */
    private String flightNo;

    /** 计划起飞时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planTakeoffTime;

    /** 换单单号 */
    private String switchBill;

    /** 运单类型 0 主单 1 分单 2 邮件单 */
    private Integer type;

    /** 运单跟踪索引值 */
    private Integer nodeIndex;

}
