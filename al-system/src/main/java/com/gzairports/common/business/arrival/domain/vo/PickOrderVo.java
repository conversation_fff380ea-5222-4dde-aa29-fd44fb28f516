package com.gzairports.common.business.arrival.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 挑单数据
 *
 * <AUTHOR>
 * @date 2024-06-03
 */
@Data
public class PickOrderVo {

    /** 运单数据集合 */
    private List<PickUpVo> pickUpVos;

    /** 办理提货id */
    private Long id;

    /** 收货代理人 */
    private Long deptId;

    /** 流水号 */
    private String serialNo;

    /** 总票数 */
    private Integer totalCount;

    /** 总件数 */
    private Integer totalQuantity;

    /** 可提货总件数 */
    private Integer totalCanPickUpQuantity;

    /** 总计费重量 */
    private BigDecimal totalChargeWeight;

    /** 总重量 */
    private BigDecimal totalWeight;

    /** 可提货总重量 */
    private BigDecimal totalCanPickUpWeight;

    /** 总费用 */
    private BigDecimal totalCost;

    /** 提货客户 */
    private String customerName;

    /** 提货人证件类型 */
    private String customerIdType;

    /** 提货人证件号 */
    private String customerIdNo;

    /** 提货人手机号 */
    private String customerPhone;

    /** 收货人 */
    private String consign;

    /** 收货人证件号 */
    private String consignIdNo;

    /** 收货人手机号 */
    private String consignPhone;

    /** 代理人 */
    private String agentCode;

    /** 支付方式（现金 线上） */
    private String payMethod;

    /** 支付状态 0 未支付 1 预授权支付 2 结算 */
    private Integer payStatus;

    /** 结算方式  0 预授权 1 余额 2 线下*/
    private Integer settleMethod;

    /** 经办人 */
    private String handleBy;

    /** 备注 */
    private String remark;

    /** 申请修改描述 */
    private String applyEdit;

    /** 拒绝修改描述 */
    private String refuseEdit;

    /** 提货码 */
    private String pickUpCode;

    /** 是否已支付 0 否 1 是 */
    private Integer isPay;

    /** 是否出库 */
    private Integer isOut;

    /** 类型 */
    private String type;

    private Integer noStatus;

    /** 是否是快速交付代理人 0 否 1 是*/
    private Integer isQuickDelivery;

    /** 结算客户 */
    private String settleUser;

    /** 结算客户简称 */
    private String settleUserAbb;
}
