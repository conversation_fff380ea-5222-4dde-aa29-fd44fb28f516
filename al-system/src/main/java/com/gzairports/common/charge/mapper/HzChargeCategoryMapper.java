package com.gzairports.common.charge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.charge.domain.HzChargeCategory;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 收费货物类别Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-13
 */
@Mapper
public interface HzChargeCategoryMapper extends BaseMapper<HzChargeCategory>
{
    /**
     * 查询收费货物类别
     * 
     * @param id 收费货物类别主键
     * @return 收费货物类别
     */
    public HzChargeCategory selectHzChargeCategoryById(Long id);

    /**
     * 查询收费货物类别列表
     * 
     * @param hzChargeCategory 收费货物类别
     * @return 收费货物类别集合
     */
    public List<HzChargeCategory> selectHzChargeCategoryList(HzChargeCategory hzChargeCategory);

    /**
     * 修改收费货物类别
     * 
     * @param hzChargeCategory 收费货物类别
     * @return 结果
     */
    public int updateHzChargeCategory(HzChargeCategory hzChargeCategory);

    /**
     * 删除收费货物类别
     * 
     * @param id 收费货物类别主键
     * @return 结果
     */
    public int deleteHzChargeCategoryById(Long id);

}
