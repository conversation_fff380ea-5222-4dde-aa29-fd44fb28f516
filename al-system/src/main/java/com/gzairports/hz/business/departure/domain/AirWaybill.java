package com.gzairports.hz.business.departure.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 运单表
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Data
@TableName("all_air_waybill")
public class AirWaybill {

    /** 主键id */
    private Long id;

    /** 运单号 */
    private String waybillCode;

    /** 交运货站 */
    private String shippingStation;

    /** 交运代理人 */
    private String shippingAgent;

    /** 换单(运单类型 0 正常 1换单 2补货 3补重) */
    private Integer switchBill;

    /** 进港中转 */
    private Integer transferBill;

    /** 原单单号 */
    private String originBill;

    /** 是否补货单 */
    private Integer replenishBill;

    /** 可补货重量 */
    private BigDecimal canRestockWeight;

    /** 补货重量 */
    private BigDecimal replenishWeight;

    /** 补货数量 */
    private Integer replenishNum;

    /** 运单出港状态
     * staging: 暂存 been_sent: 已发送 pre_pay: 预授权支付 put_in:货站入库 refuse_collect 拒绝收运
     * been_pre: 已预配 been_out: 已出库 been_dep: 已出港 pull_down: 临时拉下 been_return: 已退货
     * been_voided 已作废 order_change: 已换单 been_settle: 已结算
     * 运单进港状态 record_order 录单 tally_comp 理货完成 comp_order 已办单 out_stock 已出库*/
    private String status;

    /** 始发站 */
    private String sourcePort;

    /** 目的站 */
    private String desPort;

    /** 航班号1 */
    private String flightNo1;

    /** 航班日期1 */
    private Date flightDate1;

    /** 航班号2 */
    private String flightNo2;

    /** 航班日期2 */
    private Date flightDate2;

    /** 航班号3 */
    private String flightNo3;

    /** 航班日期3 */
    private Date flightDate3;

    /** 承运人1 */
    private String carrier1;

    /** 到达站1 */
    private String des1;

    /** 承运人2 */
    private String carrier2;

    /** 到达站2 */
    private String des2;

    /** 承运人3 */
    private String carrier3;

    /** 到达站3 */
    private String des3;

    /** 发货人简称 */
    private String shipperAbb;

    /** 发货人 */
    private String shipper;

    /** 发货人电话 */
    private String shipperPhone;

    /** 发货人地址 */
    private String shipperAddress;

    /** 发货人地区 */
    private String shipperRegion;

    /** 收货人简称 */
    private String consignAbb;

    /** 收货人 */
    private String consign;

    /** 收货人电话 */
    private String consignPhone;

    /** 收货人地址 */
    private String consignAddress;

    /** 收货人地区 */
    private String consignRegion;

    /** 代理人公司 */
    private String agentCompany;

    /** 代理人识别码 */
    private String agentCode;

    /** 城市 */
    private String city;

    /** 账号 */
    private String account;

    /** 结算注意事项 */
    private String settlementNotes;

    /** 储运注意事项 */
    private String storageTransportNotes;

    /** 海关监管 */
    private Integer customsSupervision;

    /** 公务单 */
    private Integer officialForm;

    /** 包量/包仓 */
    private Integer bulkWarehouse;

    /** 特货代码1 */
    private String specialCargoCode1;

    /** 特货代码2 */
    private String specialCargoCode2;

    /** 特货代码3 */
    private String specialCargoCode3;

    /** 其他特货代码 */
    private String otherSpecialCargoCode;

    /** 品名编码 */
    private String cargoCode;

    /** 品名（邮件种类） */
    private String cargoName;

    /** 货品大类名称 */
    private String categoryName;

    /** 包装 */
    private String pack;

    /** 包装code */
    private String packCode;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 计费重量 */
    private BigDecimal chargeWeight;

    /** 体积（M3） */
    private BigDecimal volume;

    /** 尺寸 */
    private String size;

    /** 是否需要冷藏 0 否 1 是 */
    private Integer isCold;

    /** 冷藏库 */
    private String coldStore;

    /** 危险品UN编号 */
    private String dangerCode;

    /** 危险品类型 */
    private String dangerType;

    /** 紧急联系人 */
    private String emergentContact;

    /** 紧急联系人电话 */
    private String contactPhone;

    /** 费用总计 */
    private BigDecimal costSum;

    /** 费率/公斤 */
    private BigDecimal ratePerKg;

    /** 填开时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date writeTime;

    /** 填开地点 */
    private String writeLocation;

    /** 填开人 */
    private String writer;

    /** 备注 */
    private String remark;

    /** 所属单位 */
    private Long deptId;

    /** 缴费状态 0 未授权支付 1 已授权支付 2 已余额支付 3 已支付(线下结算-现金) 4 已支付(线下结算-月结)
     *          5 已结算 (预授权支付) 6 已结算(余额支付) 7 已结算(线下结算-月结) 8 已结算(线下结算-现金)
     *          9 已退款 (预授权支付) 10 已退款(余额支付) 11已退款(线下结算-月结) 12 已退款(线下结算-现金)
     *          13 余额不足 14 取消支付*/
    private Integer payStatus;

    /** 缴费金额 */
    private BigDecimal payMoney;

    /** 已结算退还费用 */
    private BigDecimal refund;

    /** 是否删除 */
    private Integer isDel;

    /** 支付时间 */
    private Date payTime;

    /** 电子分单pdf地址 */
    private String pdfUrl;

    /** 类型 DEP 出港 ARR 进港 */
    private String type;

    /** 拼单状态 0 否 1 是 */
    private Integer mergeStatus;

    /** 制单类型 SIMPLE 简易开单 MAWB 主单制单 */
    private String makeType;

    /** 运单类型  A 主单 M 邮件单 */
    private String waybillType;

    /** 仓库 */
    private String store;

    /** 随附运输文件 */
    private String transportFile;

    /** 随附运输文件PDF */
    private String transportFilePdf;

    /** 交付运单 */
    private String deliver;

    /** 交付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date deliverTime;

    /** 压仓 */
    private Integer pressureChamber;

    /** 是否与申报一致 0 否 1 是  -> 审单结果 0退回 1符合运输 */
    private Integer declarationConsistent;

    /** 是否审核 0 否 1 是 */
    private Integer isExamine;

    /** 最终安检提交状态 0 未提交 1 已提交  -1退回 -2不合格 */
    private Integer securitySubmit;

    /** 物流端安检提交状态 0 未提交 1 物流提交 2货站提交 -1退回 -2不合格 */
    private Integer securitySubmitWl;

    /** 货站选择安检提交时的操作人 */
    private String securitySubmitOperator;

    /** 更新时间 */
    private Date updateTime;

    /** 是否收运导入 */
    private Integer isLoad;

    /** 文件到达 */
    private Integer fileArr;

    /** 优先处理 */
    private Integer prioritize;

    /** 收货人证件号 */
    private String consignIdCar;

    /** 转关 */
    private Integer isTransfer;

    /** 海关转关号 */
    private String transferNo;

    /** 到付 */
    private Integer arrPay;

    /** 是否国内/国际 */
    @Excel(name = "是否国内/国际")
    private String domint;

    /** 录单id */
    @TableField(exist = false)
    private Long orderId;

    /** 安检申报单地址 */
    private String securityUrl;

    /** 结算时间 */
    private Date settleTime;

    /** 安检申报填写 代理人签章 */
    private String agentSignature;

    /** 托运人签章 */
    private String shipperSignature;

    /** 是否跨航司承运 0 否 1 是 */
    private Integer crossAir;

    /** 转南航 */
    private Integer isSouth;

    /** 控制版本号 */
    @TableField("version")
    @Version
    private Integer version;

    private String rateType;

    /** 收运状态 0未收运 1开始收运 2虚拟收运 3收运结束 */
    private Integer collectStatus;

    /** 关联进港运单号 */
    private String arrWaybillCode;
}
