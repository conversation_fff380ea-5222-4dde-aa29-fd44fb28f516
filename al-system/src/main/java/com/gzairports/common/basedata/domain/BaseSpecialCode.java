package com.gzairports.common.basedata.domain;

import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 货物特殊处理代码
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Data
public class BaseSpecialCode {

    /** 主键id */
    private Long id;

    /** 特殊货物处理代码 */
    @Excel(name = "特殊货物处理代码")
    private String code;

    /** 危险货物 */
    @Excel(name = "危险货物",readConverterExp = "0=否,1=是")
    private Integer isDanger;

    /** 中文描述 */
    @Excel(name = "中文描述")
    private String chineseDescription;

    /** 英文描述 */
    @Excel(name = "英文描述")
    private String englishDescription;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;
}
