package com.gzairports.common.business.departure.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 交接单与运单关联表
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
@TableName("wl_dep_transfer_waybill")
public class TransferWaybill {

    /** 主键id */
    private Long id;

    /** 货物出港交接id */
    private Long transferId;

    /** 主单id */
    private Long mawbId;

    /** 入库件数 */
    private Integer storeQuantity;

    /** 入库重量 */
    private BigDecimal storeWeight;

    /** 是否收运入库 0 否 1 是 */
    private Integer isCollect;

    /** 运单制单时的件数 */
    @TableField(exist = false)
    private Integer waybillQuantity;

    /** 运单制单时的重量 */
    @TableField(exist = false)
    private BigDecimal waybillWeight;
}
