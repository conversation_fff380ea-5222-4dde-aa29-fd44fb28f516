package com.gzairports.hz.business.departure.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 代运导入板箱返回数据
 * <AUTHOR>
 * @date 2024-06-28
 */
@Data
public class ForwardImportUldVo {

    /** uld id */
    private Long id;

    /** 重量id */
    private Long weightId;

    /** 运单收运id */
    private Long collectId;

    /** 舱位 */
    private String cabin;

    /** 板箱号 */
    private String uld;

    /** 过磅重量 */
    private BigDecimal weight;

    /** 垫板重量 */
    private BigDecimal plateWeight;

    /** 板箱重量 */
    private BigDecimal boardWeight;

    /** 总件数 */
    private Integer totalQuantity;

    /** 总重量 */
    private BigDecimal totalWeight;

    /** 类型 判断是拉下还是卸下 */
    private Integer type;

    /** 代运导入时相同uld号关联的收运id */
    private List<Long> collectIds;

    /** 板箱下的运单数据 */
    private List<ForwardImportWaybillVo> waybillVos;
}
