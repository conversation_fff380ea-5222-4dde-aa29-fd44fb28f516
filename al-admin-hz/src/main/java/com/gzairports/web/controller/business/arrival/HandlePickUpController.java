package com.gzairports.web.controller.business.arrival;

import com.gzairports.common.business.arrival.domain.ApplyEdit;
import com.gzairports.common.business.arrival.domain.HzArrItem;
import com.gzairports.common.business.arrival.domain.query.*;
import com.gzairports.common.business.arrival.domain.vo.OperDataVo;
import com.gzairports.common.business.arrival.domain.vo.PickUpListVo;
import com.gzairports.common.business.arrival.service.IPickUpService;
import com.gzairports.common.business.departure.domain.vo.ItemDetailVo;
import com.gzairports.common.charge.domain.query.ItemsQuery;
import com.gzairports.common.charge.domain.vo.HzItemsVo;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.common.business.arrival.domain.vo.PickOrderVo;
import com.gzairports.common.business.arrival.domain.vo.PickedUpVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 进港提货办单操作Controller
 *
 * <AUTHOR>
 * @date 2024-06-03
 */
@RestController
@RequestMapping("/arr/handlePickUp")
@Api(tags = "办理提货")
public class HandlePickUpController extends BaseController {

    @Autowired
    private IPickUpService pickUpService;

    /**
     * 挑单
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:one')")
    @PostMapping("/one")
    @ApiOperation(value = "挑单")
    public AjaxResult one(@RequestBody PickUpClickQuery query){
        query.setType("hz");
        return AjaxResult.success(pickUpService.one(query,null));
    }

    /**
     * 批量挑单
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:batch')")
    @PostMapping("/batch")
    @ApiOperation(value = "批量挑单")
    public AjaxResult batch(@RequestBody PickUpQuery query){
        return AjaxResult.success(pickUpService.batch(query,null));
    }

    /**
     * 费用明细
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:cost')")
    @GetMapping("/cost")
    @ApiOperation(value = "费用明细")
    public AjaxResult cost(@NotNull @RequestParam String waybillCode, @NotNull @RequestParam Long tallyId)
    {
        return AjaxResult.success(pickUpService.cost(waybillCode,tallyId));
    }

    /**
     * 费用明细详情
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:costInfo')")
    @GetMapping("/costInfo/{id}")
    @ApiOperation(value = "费用明细详情")
    public AjaxResult costInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(pickUpService.costInfo(id));
    }

    /**
     * 编辑费用明细
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:editCost')")
    @PostMapping("/editCost")
    @ApiOperation(value = "编辑费用明细")
    public AjaxResult editCost(@RequestBody HzArrItem item)
    {
        return toAjax(pickUpService.editCost(item));
    }

    /**
     * 删除费用明细
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:delCost')")
    @GetMapping("/delCost/{id}")
    @ApiOperation(value = "删除费用明细")
    public AjaxResult delCost(@PathVariable("id") Long id)
    {
        return toAjax(pickUpService.delCost(id));
    }

    /**
     * 过滤收费项目
     */
    @PostMapping("/itemList")
    @ApiOperation(value = "过滤收费项目")
    public AjaxResult list(@RequestBody ItemsQuery itemQuery)
    {
        List<HzItemsVo> list = pickUpService.selectHzChargeItemsList(itemQuery);
        return AjaxResult.success(list);
    }
    /**
     * 新增费用明细
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:addCost')")
    @PostMapping("/addCost")
    @ApiOperation(value = "新增费用明细")
    public AjaxResult addCost(@RequestBody HzArrItem item)
    {
        return toAjax(pickUpService.addCost(item));
    }

    /**
     * 计算总费用
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:countCost')")
    @PostMapping("/countCost")
    @ApiOperation(value = "计算总费用")
    public AjaxResult countCost(@RequestBody HzArrItem item)
    {
        return AjaxResult.success(pickUpService.countCost(item));
    }

    /**
     * 保存
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:add')")
    @PostMapping("/add")
    @ApiOperation(value = "保存")
    public AjaxResult add(@RequestBody PickOrderVo vo)
    {
        vo.setType("hz");
        return AjaxResult.success(pickUpService.add(vo));
    }


    /**
     * 修改提货办单数据
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:edit')")
    @PostMapping("/edit")
    @ApiOperation(value = "修改提货办单数据")
    public AjaxResult edit(@RequestBody PickOrderVo vo)
    {
        return AjaxResult.success(pickUpService.edit(vo));
    }

    /**
     * 结算
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:settle')")
    @GetMapping("/settle/{waybillCode}/{totalCost}")
    @ApiOperation(value = "结算")
    public AjaxResult settle(@PathVariable("waybillCode") String waybillCode,@PathVariable("totalCost") String totalCost)
    {
        return AjaxResult.success(pickUpService.hzSettle(waybillCode, totalCost));
    }

    /**
     * 支付
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:balance')")
    @PostMapping("/balance")
    @ApiOperation(value = "支付")
    public AjaxResult balance(@RequestBody PickOrderVo vo)
    {
        vo.setType("hz");
        return AjaxResult.success(pickUpService.balance(vo));
    }

    /**
     * 流水号查询
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:serial')")
    @PostMapping("/serial")
    @ApiOperation(value = "流水号查询")
    public AjaxResult serial(@RequestBody PickUpCodeQuery query){
        return AjaxResult.success(pickUpService.serial(query));
    }

    /**
     * 加入提货列表
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:pickList')")
    @PostMapping("/pickList")
    @ApiOperation(value = "加入提货列表")
    public AjaxResult pickList(@RequestBody PickUpListVo vo){
        vo.setType("hz");
        return AjaxResult.success(pickUpService.pickList(vo));
    }

    /**
     * 查询已提货办单数据
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:pickedUp')")
    @PostMapping("/pickedUp")
    @ApiOperation(value = "查询已提货办单数据")
    public AjaxResult pickedUp(@RequestBody PickedUpQuery query){
        return AjaxResult.success(pickUpService.selectByQuery(query));
    }

    /**
     * 导出已提货办单数据
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出已提货办单数据")
    public void export(HttpServletResponse response, PickedUpQuery query){
        List<PickedUpVo> list = pickUpService.exportList(query);
        ExcelUtil<PickedUpVo> excelUtil = new ExcelUtil<>(PickedUpVo.class);
        excelUtil.exportExcel(response,list,"已提货办单数据");
    }

    /**
     * 查询未提货办单数据
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:notPickedUp')")
    @PostMapping("/notPickedUp")
    @ApiOperation(value = "查询未提货办单数据")
    public AjaxResult notPickedUp(@RequestBody PickedUpQuery query){
        return AjaxResult.success(pickUpService.notPickedUp(query,null));
    }

    /**
     * 查询申请修改列表
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:applyList')")
    @PostMapping("/applyList")
    @ApiOperation(value = "查询申请修改列表")
    public AjaxResult applyList(@RequestBody ApplyEditQuery query){
        return AjaxResult.success(pickUpService.selectApplyList(query));
    }

    /**
     * 修改
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:hzApplyEdit')")
    @GetMapping("/hzApplyEdit/{id}")
    @ApiOperation(value = "修改")
    public AjaxResult hzApplyEditInfo(@PathVariable("id") Long id){
        return AjaxResult.success(pickUpService.hzApplyEdit(id));
    }

    /**
     * 根据流水号查询修改提货办单需要的数据
     */
    @GetMapping("/getEditData/{serialNo}")
    @ApiOperation(value = "修改")
    public AjaxResult getEditDataInfo(@PathVariable("serialNo") Long serialNo){
        return AjaxResult.success(pickUpService.getEditDataInfo(serialNo));
    }

    /**
     * 根据id查询修改操作区数据
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:operData')")
    @GetMapping("/operData/{id}")
    @ApiOperation(value = "根据id查询修改操作区数据")
    public AjaxResult operData(@PathVariable("id") Long id){
        return AjaxResult.success(pickUpService.operData(id));
    }

    /**
     * 修改完成
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:editComp')")
    @PostMapping("/editComp")
    @ApiOperation(value = "修改完成")
    public AjaxResult editComp(@RequestBody OperDataVo vo){
        return toAjax(pickUpService.editComp(vo));
    }

    /**
     * 拒绝修改
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUp:refuseEdit')")
    @PostMapping("/refuseEdit")
    @ApiOperation(value = "拒绝修改")
    public AjaxResult refuseEdit(@RequestBody ApplyEdit applyEdit){
        return toAjax(pickUpService.refuseEdit(applyEdit));
    }

    /**
     * 打印出库单
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:printOutOrder')")
    @GetMapping("/printOutOrder/{pickUpId}")
    @ApiOperation(value = "打印出库单")
    public AjaxResult printOutOrder(@PathVariable("pickUpId") Long pickUpId){
        return AjaxResult.success(pickUpService.printOutOrder(pickUpId));
    }

    /**
     * 自动办单机办单
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:autoOrder')")
    @PostMapping("/autoOrder")
    @ApiOperation(value = "自动办单机办单（办理提货）")
    public AjaxResult autoOrder(@RequestBody AutoOrderQuery query){
        return AjaxResult.success(pickUpService.autoOrder(query));
    }

    /**
     * 根据收费项目id查询优先级最高的收费规则
     */
//    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:getHighRule')")
    @PostMapping("/getHighRule")
    @ApiOperation(value = "根据收费项目id查询优先级最高的收费规则")
    public AjaxResult getHighRule(@RequestBody ItemDetailVo vo){
        return AjaxResult.success(pickUpService.getHighRule(vo));
    }

    /**
     * 查询客户列表
     */
    @PostMapping("/getCustomerList")
    @ApiOperation(value = "查询客户列表")
    public AjaxResult getCustomerList(){
        return AjaxResult.success(pickUpService.getCustomerList());
    }


    /**
     * 取消办理提货
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:cancel')")
    @GetMapping("/cancel/{pickUpId}")
    @ApiOperation(value = "取消办理提货")
    public AjaxResult cancel(@PathVariable("pickUpId") Long pickUpId){
        return AjaxResult.success(pickUpService.cancel(pickUpId));
    }

}
