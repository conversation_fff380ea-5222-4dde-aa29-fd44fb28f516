package com.gzairports.hz.business.departure.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 新增航班配载查询条件
 * <AUTHOR>
 * @date 2024-07-02
 */
@Data
public class AddQuery {

    /** 航班号1 */
    private String flightNo;

    /** 航班日期1 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date flightDate;

    /** 航段 */
    private String leg;

    /** 0 点击搜索查询 1 自动刷新 */
    private Integer type;
}
