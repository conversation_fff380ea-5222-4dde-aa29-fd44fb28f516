<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.departure.mapper.SpecialTraceMapper">
    <select id="selectListByQuery" resultType="com.gzairports.hz.business.departure.domain.vo.SpecialTraceVo">
        select hdst.id, hdst.ware_time, hdst.waybill_code, aaw.flight_no1 as flightNo, aaw.special_cargo_code1 as specialCode, aaw.quantity, aaw.weight,
               aaw.source_port, aaw.des_port, aaw.cargo_name, hdst.ware_location, hdst.ware_user, hdst.ware_remark, hdst.out_time, hdst.out_flight,
               hdst.out_piece, hdst.out_weight, hdst.out_location, hdst.out_user, hdst.out_remark
        from hz_dep_special_trace hdst
            left join all_air_waybill aaw on hdst.waybill_code = aaw.waybill_code
        <where>
            <if test="waybillCode != null and waybillCode != ''">
                and hdst.waybill_code like '%${waybillCode}%'
            </if>
            <if test="specialCode != null and specialCode != ''">
                and aaw.special_cargo_code1 = #{specialCode}
            </if>
            <if test="agentCode != null and agentCode != ''">
                and aaw.agent_code = #{agentCode}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="startTime != null">
                and hdst.ware_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and hdst.ware_time <![CDATA[<=]]> #{endTime}
            </if>
        </where>
    </select>
</mapper>