<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.reporter.mapper.HzCountReportMapper">

    <sql id="conditionFragment">
        <choose>
            <when test="filter.fieldFilterType == 'BETWEEN'">
                ${filter.fieldName} BETWEEN #{filter.lessValue} and #{filter.greaterValue}
            </when>
            <when test="filter.fieldFilterType == 'IN'">
                <if test="filter.selectValue != null and !filter.selectValue.isEmpty()">
                    ${filter.fieldName} IN
                    <foreach collection="filter.selectValue" item="value" index="index" open="(" separator="," close=")">
                        #{value}
                    </foreach>
                </if>
            </when>
            <when test="filter.fieldFilterType == 'LIKE'">
                ${filter.fieldName} LIKE concat('%', #{filter.fieldValue}, '%')
            </when>
            <otherwise>
              ${filter.fieldName} ${filter.fieldFilterType} #{filter.fieldValue}
            </otherwise>
        </choose>
    </sql>

    <select id="generateReport" resultType="java.util.Map">
        SELECT
        <foreach collection="config.displayFields" item="field" separator=",">
            ${field}
        </foreach>
        FROM ${config.mainTable} as main
        <foreach collection="config.joinTables" item="joinTable">
            inner join ${joinTable.tableName} as ${joinTable.alias} ON ${joinTable.joinConditionWithAlias}
        </foreach>
        <where>
            <if test="config.filters != null and !config.filters.isEmpty()">
                <foreach collection="config.filters" item="filter" index="index">
                    <choose>
                        <when test="index == 0">
                            <include refid="conditionFragment"/>
                        </when>
                        <otherwise>
                            ${filter.logic} <include refid="conditionFragment"/>
                        </otherwise>
                    </choose>
                </foreach>
            </if>
        </where>
        <if test="config.sortFields != null and !config.sortFields.isEmpty()">
            ORDER BY
            <foreach collection="config.sortFields" item="sortField" separator=",">
                ${sortField.field} ${sortField.direction}
            </foreach>
        </if>
    </select>
    <select id="pageQuery" resultType="java.lang.Integer">
        SELECT count(1)
        FROM ${config.mainTable} as main
        <foreach collection="config.joinTables" item="joinTable">
            inner join ${joinTable.tableName} as ${joinTable.alias} ON ${joinTable.joinConditionWithAlias}
        </foreach>
        <where>
            <if test="config.filters != null and !config.filters.isEmpty()">
                <foreach collection="config.filters" item="filter" index="index">
                    <choose>
                        <when test="index == 0">
                            <include refid="conditionFragment"/>
                        </when>
                        <otherwise>
                            ${filter.logic} <include refid="conditionFragment"/>
                        </otherwise>
                    </choose>
                </foreach>
            </if>
        </where>
    </select>
    <select id="countReportTotal" resultType="java.lang.Long">
        SELECT count(1)
        FROM ${config.mainTable} as main
        <foreach collection="config.joinTables" item="joinTable">
            inner join ${joinTable.tableName} as ${joinTable.alias} ON ${joinTable.joinConditionWithAlias}
        </foreach>
        <where>
            <if test="config.filters != null and !config.filters.isEmpty()">
                <foreach collection="config.filters" item="filter" index="index">
                    <choose>
                        <when test="index == 0">
                            <include refid="conditionFragment"/>
                        </when>
                        <otherwise>
                            ${filter.logic} <include refid="conditionFragment"/>
                        </otherwise>
                    </choose>
                </foreach>
            </if>
        </where>
        <if test="config.sortFields != null and !config.sortFields.isEmpty()">
            ORDER BY
            <foreach collection="config.sortFields" item="sortField" separator=",">
                ${sortField.field} ${sortField.direction}
            </foreach>
        </if>
    </select>
</mapper>