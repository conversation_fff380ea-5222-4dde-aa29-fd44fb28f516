package com.gzairports.common.charge.service.impl;

import com.gzairports.common.business.arrival.domain.ItemDetail;
import com.gzairports.common.charge.domain.HzChargeItemRule;
import com.gzairports.common.charge.domain.vo.BillRuleVo;
import com.gzairports.common.charge.service.BillingRule;

import java.math.BigDecimal;
import java.util.List;

/**
 * 免费保管多少天后按天计算费率规则
 * <AUTHOR>
 * @date 2024-08-03
 */
public class PostFreeDayBillingRule implements BillingRule {

    @Override
    public String className() {
        return "PostFreeDayBillingRule.class";
    }

    @Override
    public BillRuleVo calculateFee(List<HzChargeItemRule> rule, BigDecimal weight, Integer quantity, ItemDetail item) {
        HzChargeItemRule itemRule = rule.get(0);
        BillRuleVo vo = new BillRuleVo();
        vo.setQuantity(itemRule.getFreeTime());
        // 免费保管多少天
        if (item.getDaysInStorage() <= Double.parseDouble(itemRule.getFreeTime())) {
            vo.setTotalCharge(new BigDecimal(0));
            return vo;
        } else {
            double diff = item.getDaysInStorage() - Double.parseDouble(itemRule.getFreeTime());
            double days;
            if (Math.floor(diff) == diff){
                days = diff;
            }else {
                days = diff + 1;
            }
            BigDecimal multiply1 = itemRule.getRate().multiply(new BigDecimal(days)).multiply(weight);
            BigDecimal decimal = itemRule.getMinimum() == null ? new BigDecimal(0) : itemRule.getMinimum();
            if (multiply1.compareTo(decimal) >= 0){
                vo.setRate(itemRule.getRate());
            }
            BigDecimal max = multiply1.max(decimal);
            vo.setTotalCharge(max);
            return vo;
        }
    }

}
