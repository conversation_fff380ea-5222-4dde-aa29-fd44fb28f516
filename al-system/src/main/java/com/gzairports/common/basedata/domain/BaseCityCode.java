package com.gzairports.common.basedata.domain;

import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 城市代码
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Data
public class BaseCityCode {

    /** 主键id */
    private Long id;

    /** 城市代码 */
    @Excel(name = "城市代码")
    private String code;

    /** 中文名称 */
    @Excel(name = "中文名称")
    private String chineseName;

    /** 英文名称 */
    @Excel(name = "英文名称")
    private String englishName;

    /** 州或省中文 */
    @Excel(name = "州或省中文")
    private String stateOrProvinceChinese;

    /** 州或省英文 */
    @Excel(name = "州或省英文")
    private String stateOrProvinceEnglish;

    /** 国家或地区 */
    @Excel(name = "国家或地区")
    private String region;

    /** 城市四位码 */
    @Excel(name = "城市四位码")
    private String fourCode;

    /** 所在时区 */
    @Excel(name = "所在时区")
    private String timeZone;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;
}
