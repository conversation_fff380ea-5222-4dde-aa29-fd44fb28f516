package com.gzairports.hz.business.departure.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.business.departure.domain.vo.AwbinfoVo;
import com.gzairports.common.business.departure.service.impl.PullWaybillInfoServiceImpl;
import com.gzairports.common.core.domain.entity.SysDept;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.hz.business.departure.domain.AirWaybill;
import com.gzairports.hz.business.departure.domain.PinCheck;
import com.gzairports.hz.business.departure.domain.query.PinCheckQuery;
import com.gzairports.hz.business.departure.domain.vo.PinCheckVo;
import com.gzairports.hz.business.departure.domain.vo.WaybillIdAndCodeVo;
import com.gzairports.hz.business.departure.domain.vo.WaybillInfoVo;
import com.gzairports.hz.business.departure.mapper.AllAirWaybillMapper;
import com.gzairports.hz.business.departure.mapper.PinCheckMapper;
import com.gzairports.hz.business.departure.service.IPinCheckService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.security.AccessControlException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 开箱抽查Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Service
public class PinCheckServiceImpl extends ServiceImpl<PinCheckMapper, PinCheck> implements IPinCheckService {

    @Autowired
    private PinCheckMapper pinCheckMapper;

    @Autowired
    private AllAirWaybillMapper waybillMapper;

    @Autowired
    private PullWaybillInfoServiceImpl infoService;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    /**
     * 开箱抽查查询列表
     * @param query 查询条件
     * @return 结果
     */
    @Override
    public List<PinCheckVo> selectPinCheckList(PinCheckQuery query) {
        List<PinCheckVo> pinCheckVos = pinCheckMapper.selectPinCheckList(query);
        pinCheckVos.forEach(e->{
            e.setDeclaration(e.getDeclarationConsistent() == 0 ? "否" : "是");
        });
        return pinCheckVos;
    }

    /**
     * 查询详情
     * @param id 抽检id
     * @return 结果
     */
    @Override
    public PinCheckVo getInfo(Long id) {
        return pinCheckMapper.getInfo(id);
    }


    /**
     * 修改开箱抽查
     * @param vo 开箱抽查参数
     * @return 结果
     */
    @Override
    public int edit(PinCheckVo vo) {
        if (vo == null){
            throw  new CustomException("无修改信息");
        }
        Date flightDate1 = waybillMapper.selectById(vo.getWaybillId().toString()).getFlightDate1();
        if(flightDate1 != null && flightDate1.after(vo.getInspectTime())){
            throw new CustomException("检查时间不能早于航班时间");
        }
        PinCheck pinCheck = new PinCheck();
        BeanUtils.copyProperties(vo,pinCheck);
        pinCheck.setCheckStatus(0);
        return pinCheckMapper.updateById(pinCheck);
    }

    /**
     * H5端查询开箱检查数据
     * @param query 查询条件
     * @return 结果
     */
    @Override
    public List<PinCheckVo> selectH5List(PinCheckQuery query) {
        return pinCheckMapper.selectH5List(query, SecurityUtils.getUserId());
    }

    /**
     * H5端新增开箱抽查
     * @param pinCheck 新增数据
     * @return 结果
     */
    @Override
    public int addH5PinCheck(PinCheck pinCheck) {
        pinCheck.setUserId(SecurityUtils.getUserId());
        pinCheck.setCheckStatus(0);
        pinCheck.setInspectTime(new Date());
        return pinCheckMapper.insert(pinCheck);
    }

    /**
     * 根据四位查询运单号
     * @param fourCode 运单号四位
     * @return 结果
     */
    @Override
    public List<WaybillIdAndCodeVo> getFourCode(String fourCode) {
        LambdaQueryWrapper<AirWaybill> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.likeLeft(AirWaybill::getWaybillCode,fourCode);
        List<AirWaybill> waybillList = waybillMapper.selectList(queryWrapper);
        List<WaybillIdAndCodeVo> resultList = new ArrayList<>();
        for (AirWaybill airWaybill : waybillList) {
            WaybillIdAndCodeVo codeVo = new WaybillIdAndCodeVo();
            codeVo.setWaybillId(airWaybill.getId());
            codeVo.setWaybillCode(airWaybill.getWaybillCode());
            resultList.add(codeVo);
        }
        if (CollectionUtils.isEmpty(resultList)){
            throw new CustomException("未查询到运单信息，请输入运单八位查询");
        }
        return resultList;
    }

    /**
     * 根据运单id查询运单信息
     * @param waybillId 运单id
     * @return 结果
     */
    @Override
    public PinCheckVo waybillInfo(Long waybillId) {
        AirWaybill airWaybill = waybillMapper.selectById(waybillId);
        PinCheckVo vo = new PinCheckVo();
        vo.setCargoName(airWaybill.getCargoName());
        vo.setWaybillId(airWaybill.getId());
        vo.setWaybillCode(airWaybill.getWaybillCode());
        vo.setQuantity(airWaybill.getQuantity());
        vo.setWeight(airWaybill.getWeight());
        vo.setDesPort(airWaybill.getDesPort());
        return vo;
    }

    /**
     * 新增暂存
     * @param check 暂存数据
     * @return 结果
     */
    @Override
    public boolean addStaging(PinCheck check) {
        check.setUserId(SecurityUtils.getUserId());
        check.setWaybillId(0L);
        check.setCheckStatus(1);
        check.setInspectTime(new Date());
        return this.save(check);
    }

    /**
     * 暂存列表查询
     * @param query 查询参数
     * @return 暂存列表
     */
    @Override
    public List<PinCheckVo> selectStagingList(PinCheckQuery query) {
        Long userId = SecurityUtils.getUserId();
        return pinCheckMapper.selectStagingList(userId,query);
    }

    /**
     * 查询暂存详情
     * @param checkId 检查id
     * @return 结果
     */
    @Override
    public PinCheck stagingDetail(Long checkId) {
        PinCheck check = pinCheckMapper.selectById(checkId);
        if (check == null) {
            throw new CustomException("运单抽检记录id对应的抽检记录不存在");
        }
        //检查用户访问资格（是否是该用户创建的运单）
        if (!Objects.equals(check.getUserId(),SecurityUtils.getUserId())) {
            throw new AccessControlException("当前登录用户不是执行该抽检的用户，安全考虑拒绝访问。");
        }
        return check;
    }

    /**
     * 暂存删除
     * @param checkId 检查id
     * @return 结果
     */
    @Override
    public boolean delStaging(Long checkId) {
        PinCheck check = pinCheckMapper.selectById(checkId);
        if (check == null) {
            throw new RuntimeException("运单抽检记录id对应的抽检记录不存在");
        }
        //检查用户访问资格（是否是该用户创建的运单）
        if (!Objects.equals(check.getUserId(),SecurityUtils.getUserId())) {
            throw new AccessControlException("当前登录用户不是执行该抽检的用户，安全考虑拒绝访问。");
        }
        return this.removeById(checkId);
    }

    /**
     * 暂存修改
     * @param check 修改参数
     * @return 结果
     */
    @Override
    public boolean editStaging(PinCheck check) {
        return this.updateById(check);
    }

    /**
     * 根据运单后八位查询
     * @param code 运单八位code
     * @return 结果
     */
    @Override
    public List<WaybillIdAndCodeVo> getEightCode(String code) {
        return waybillMapper.selectEightCodeList(code);
    }

    @Override
    public PinCheckVo waybillGetInfo(String waybillCode) {
        List<AirWaybill> airWaybills = waybillMapper.selectList(new QueryWrapper<AirWaybill>()
                .eq("waybill_code",waybillCode)
                .eq("type","DEP").eq("is_del",0));
        PinCheckVo vo = new PinCheckVo();
        if (!CollectionUtils.isEmpty(airWaybills)){
            AirWaybill airWaybill = airWaybills.get(0);
            vo.setCargoName(airWaybill.getCargoName());
            vo.setWaybillId(airWaybill.getId());
            vo.setWaybillCode(airWaybill.getWaybillCode());
            vo.setQuantity(airWaybill.getQuantity());
            vo.setWeight(airWaybill.getWeight());
            vo.setDesPort(airWaybill.getDesPort());
            return vo;
        }
        return vo;
    }

    private static final Map<String, String> STATUSMAP = new HashMap<>();
    static {
        STATUSMAP.put("not_in", "been_sent");
        STATUSMAP.put("not_pre", "put_in");
        STATUSMAP.put("al_pre", "been_pre");
        STATUSMAP.put("fz_out", "been_out");
        STATUSMAP.put("pull_down", "pull_down");
        STATUSMAP.put("change_order", "order_change");
        STATUSMAP.put("a_in", "record_order");
        STATUSMAP.put("lh_comp", "tally_comp");
        STATUSMAP.put("ta_tx", "out_stock");
    }
}
