package com.gzairports.hz.business.departure.domain.vo;

import com.gzairports.common.pdf.PdfPrintAnnotation;
import lombok.Data;

import java.math.BigDecimal;

import static com.gzairports.common.pdf.PdfPrintAnnotation.PdfFieldType.TEXT;

/**
 * Created by david on 2024/11/5
 */
@Data
public class DangerNoticeVo {

    /** 卸机站 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "dangerDesPort")
    private String dangerDesPort;

    /** 货运单号 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "dangerWaybill")
    private String dangerWaybill;

    /** UN或ID编号 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "unCode")
    private String dangerCode;

    /** 类或者项 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "dangerType")
    private String dangerType;

    /** 危险品件数 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "dangerQuantity")
    private Integer dangerQuantity;

    /** 危险品重量 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "dangerWeight")
    private BigDecimal dangerWeight;

    /** 危险品uld */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "dangerUld")
    private String dangerUld;

    /** 危险品舱位 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "dangerCabin")
    private String dangerCabin;

    /** 备注 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "remark")
    private String remark;
}
