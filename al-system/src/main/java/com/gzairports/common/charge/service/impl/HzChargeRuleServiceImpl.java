package com.gzairports.common.charge.service.impl;

import com.gzairports.common.charge.domain.HzChargeRule;
import com.gzairports.common.charge.mapper.HzChargeRuleMapper;
import com.gzairports.common.charge.service.IHzChargeRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 计费规则Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-13
 */
@Service
public class HzChargeRuleServiceImpl implements IHzChargeRuleService
{
    @Autowired
    private HzChargeRuleMapper hzChargeRuleMapper;

    /**
     * 查询计费规则
     * 
     * @param id 计费规则主键
     * @return 计费规则
     */
    @Override
    public HzChargeRule selectHzChargeRuleById(Long id)
    {
        return hzChargeRuleMapper.selectHzChargeRuleById(id);
    }

    /**
     * 查询计费规则列表
     * 
     * @param hzChargeRule 计费规则
     * @return 计费规则
     */
    @Override
    public List<HzChargeRule> selectHzChargeRuleList(HzChargeRule hzChargeRule)
    {
        return hzChargeRuleMapper.selectHzChargeRuleList(hzChargeRule);
    }

    /**
     * 新增计费规则
     * 
     * @param hzChargeRule 计费规则
     * @return 结果
     */
    @Override
    public int insertHzChargeRule(HzChargeRule hzChargeRule)
    {
        return hzChargeRuleMapper.insertHzChargeRule(hzChargeRule);
    }

    /**
     * 修改计费规则
     * 
     * @param hzChargeRule 计费规则
     * @return 结果
     */
    @Override
    public int updateHzChargeRule(HzChargeRule hzChargeRule)
    {
        return hzChargeRuleMapper.updateHzChargeRule(hzChargeRule);
    }

    /**
     * 删除计费规则信息
     * 
     * @param id 计费规则主键
     * @return 结果
     */
    @Override
    public int deleteHzChargeRuleById(Long id)
    {
        return hzChargeRuleMapper.deleteHzChargeRuleById(id);
    }
}
