<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.BaseBalanceMapper">
    
    <resultMap type="BaseBalance" id="BaseBalanceResult">
        <result property="id"    column="id"    />
        <result property="agentId"    column="agent_id"    />
        <result property="serialNo"    column="serial_no"    />
        <result property="type"    column="type"    />
        <result property="tradeMoney"    column="trade_money"    />
        <result property="balance"    column="balance"    />
        <result property="voucher"    column="voucher"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="voucherPDF"    column="voucher_PDF"    />
        <result property="addType"    column="add_type"    />
    </resultMap>

    <sql id="selectBaseBalanceVo">
        select id, agent_id, serial_no, type, trade_money, balance, voucher, add_type, create_time, create_by, update_time, update_by from base_balance
    </sql>
        
    <insert id="insertBaseBalance" parameterType="BaseBalance" useGeneratedKeys="true" keyProperty="id">
        insert into base_balance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="agentId != null">agent_id,</if>
            <if test="serialNo != null and serialNo != ''">serial_no,</if>
            <if test="type != null">type,</if>
            <if test="tradeMoney != null">trade_money,</if>
            <if test="balance != null">balance,</if>
            <if test="voucher != null">voucher,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="waybillCode != null">waybill_code,</if>
            <if test="remark != null">remark,</if>
            <if test="voucherPDF != null and voucherPDF != ''">voucher_PDF,</if>
            <if test="addType != null">add_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="agentId != null">#{agentId},</if>
            <if test="serialNo != null and serialNo != ''">#{serialNo},</if>
            <if test="type != null">#{type},</if>
            <if test="tradeMoney != null">#{tradeMoney},</if>
            <if test="balance != null">#{balance},</if>
            <if test="voucher != null">#{voucher},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="waybillCode != null">#{waybillCode},</if>
            <if test="remark != null">#{remark},</if>
            <if test="voucherPDF != null and voucherPDF != ''">#{voucherPDF},</if>
            <if test="addType != null">#{addType},</if>
         </trim>
    </insert>
    <select id="selectNotWaybillCode" resultType="java.lang.String">
        select distinct waybill_code from base_balance where agent_id = #{id} and remark = '运单支付' and (create_time <![CDATA[<]]> #{query.flightStartTime} or create_time <![CDATA[>]]> #{query.flightEndTime})
    </select>
</mapper>