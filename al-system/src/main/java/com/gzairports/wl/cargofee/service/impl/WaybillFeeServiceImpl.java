package com.gzairports.wl.cargofee.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.BaseAgent;
import com.gzairports.common.basedata.mapper.BaseAgentMapper;
import com.gzairports.common.business.departure.domain.WaybillFee;
import com.gzairports.common.business.departure.mapper.WaybillFeeMapper;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.wl.cargofee.domain.query.WaybillFeeQuery;
import com.gzairports.wl.cargofee.domain.vo.WaybillFeeVo;
import com.gzairports.wl.cargofee.service.IWaybillFeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 运单费用明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@Service
public class WaybillFeeServiceImpl extends ServiceImpl<WaybillFeeMapper, WaybillFee> implements IWaybillFeeService {

    @Autowired
    private WaybillFeeMapper feeMapper;

    @Autowired
    private BaseAgentMapper agentMapper;


    /**
     * 查询运单费用明细列表
     * @param query 查询参数
     * @return 运单费用明细列表
     */
    @Override
    public WaybillFeeVo selectList(WaybillFeeQuery query) {
        WaybillFeeVo vo = new WaybillFeeVo();
        query.setDeptId(SecurityUtils.getHighParentId());
        List<String> deptIds = new ArrayList<>();
        deptIds.add(SecurityUtils.getHighParentId().toString());
        BaseAgent agent = agentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", SecurityUtils.getHighParentId()));
        if (agent != null){
            switch (agent.getSettleMethod()){
                case 0:
                    vo.setSettleMethod("预授权支付");
                    break;
                case 1:
                    vo.setSettleMethod("余额支付");
                    break;
                case 2:
                    vo.setSettleMethod("线下结算");
                    break;
                default:
                    vo.setSettleMethod("");
                    break;
            }
            if(StringUtils.isNotEmpty(agent.getDeptIds())){
                deptIds = Arrays.asList(agent.getDeptIds().split(","));
            }
            vo.setBalance(agent.getBalance());
        }
        query.setDeptIds(deptIds);
        List<WaybillFee> waybillFees = feeMapper.selectListByQuery(query);
        if (CollectionUtils.isEmpty(waybillFees)){
            return vo;
        }
        for (WaybillFee waybillFee : waybillFees) {
            switch (waybillFee.getStatus()){
                case 0:
                    waybillFee.setStrStart("预授权");
                    break;
                case 1:
                    waybillFee.setStrStart("已结算");
                    break;
                case 2:
                    waybillFee.setStrStart("结算失败");
                    break;
                default:
                    waybillFee.setStrStart("");
                    break;
            }
        }
        vo.setList(waybillFees);
        BigDecimal totalPay = waybillFees.stream().map(WaybillFee::getPayMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        vo.setTotalPay(totalPay);

        BigDecimal totalSettle = waybillFees.stream().map(WaybillFee::getSettleMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        vo.setTotalSettle(totalSettle);

        BigDecimal totalRefund = waybillFees.stream().map(WaybillFee::getRefund).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        vo.setTotalRefund(totalRefund);
        return vo;
    }
}
