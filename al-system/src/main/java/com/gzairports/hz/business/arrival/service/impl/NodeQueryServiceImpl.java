package com.gzairports.hz.business.arrival.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzairports.common.business.arrival.domain.AllPickUpOut;
import com.gzairports.common.business.arrival.domain.PickUpWaybill;
import com.gzairports.common.business.arrival.mapper.AllPickUpOutMapper;
import com.gzairports.common.business.arrival.mapper.PickUpWaybillMapper;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.hz.business.arrival.domain.HzArrTransfer;
import com.gzairports.hz.business.arrival.domain.query.NodeQuery;
import com.gzairports.hz.business.arrival.domain.vo.NodeFlightInfoVo;
import com.gzairports.hz.business.arrival.domain.vo.NodeTallyInfoVo;
import com.gzairports.hz.business.arrival.domain.vo.NodeVo;
import com.gzairports.hz.business.arrival.domain.vo.NodeWaybillVo;
import com.gzairports.hz.business.arrival.mapper.HzArrRecordOrderMapper;
import com.gzairports.hz.business.arrival.mapper.HzArrTransferMapper;
import com.gzairports.hz.business.arrival.service.INodeQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 运单保障节点查询Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-12
 */
@Service
public class NodeQueryServiceImpl implements INodeQueryService {


    @Autowired
    private HzArrRecordOrderMapper recordOrderMapper;

    @Autowired
    private HzArrTransferMapper transferMapper;

    @Autowired
    private PickUpWaybillMapper pickUpWaybillMapper;

    @Autowired
    private AllPickUpOutMapper pickUpOutMapper;



    /**
     * 运单保障节点查询列表数据
     * @param query 查询条件
     * @return 运单保障节点列表
     */
    @Override
    public NodeVo selectList(NodeQuery query) {
        NodeVo vo = new NodeVo();
        IPage<NodeWaybillVo> pageData = recordOrderMapper.selectNodeList(query, query.build());
        List<NodeWaybillVo> list = pageData.getRecords();
        if (!CollectionUtils.isEmpty(list)) {
            setConsumeDate(list);
            vo.setWaybillVos(list);
            vo.setTotalNumber((int) pageData.getTotal());
            int sum = list.stream().mapToInt(NodeWaybillVo::getQuantity).sum();
            vo.setTotalQuantity(sum);
            BigDecimal reduce = list.stream().map(NodeWaybillVo::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setTotalWeight(reduce);
        }
        return vo;
    }

    /**
     * 导出运单保障节点数据
     * @param query 查询条件
     * @return 结果
     */
    @Override
    public List<NodeWaybillVo> selectListByQuery(NodeQuery query) {
        Page<NodeWaybillVo> page = query.build(1, Integer.MAX_VALUE);
        IPage<NodeWaybillVo> pageData = recordOrderMapper.selectNodeList(query, page);
        List<NodeWaybillVo> list = pageData.getRecords();
        if (!CollectionUtils.isEmpty(list)){
            setConsumeDate(list);
        }
        return list;
    }

    /**
     * 设置耗时数据
     * @param list 运单保障节点列表数据
     */
    private void setConsumeDate(List<NodeWaybillVo> list) {
        List<NodeFlightInfoVo> flightKeys = new ArrayList<>();
        List<NodeTallyInfoVo> tallyKeys = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)){
            list.stream().filter(vo -> vo.getTallyId() != null && StringUtils.isNotBlank(vo.getWaybillCode()))
                    .forEach(nodeWaybill ->
                            tallyKeys.add(new NodeTallyInfoVo(nodeWaybill.getTallyId(), nodeWaybill.getWaybillCode()))
                    );

            list.stream().filter(vo -> vo.getExecDate() != null && StringUtils.isNotBlank(vo.getFlightNo()))
                    .forEach(nodeWaybill ->
                            flightKeys.add(new NodeFlightInfoVo(nodeWaybill.getFlightNo(), nodeWaybill.getExecDate()))
                    );
        }
        Map<String, List<HzArrTransfer>> transferMap = new HashMap<>();
        if (!flightKeys.isEmpty()){
            List<HzArrTransfer> transfers = transferMapper.batchSelect(flightKeys);
            transferMap = transfers.stream()
                    .collect(Collectors.groupingBy(
                            out -> out.getFlightNo() + "_" + out.getExecDate()));
        }
        Map<String, List<PickUpWaybill>> waybillMap = new HashMap<>();
        Map<String, List<AllPickUpOut>> outMap = new HashMap<>();
        if (!tallyKeys.isEmpty()){
            List<PickUpWaybill> pickUpWaybills = pickUpWaybillMapper.selectBatch(tallyKeys);
            waybillMap = pickUpWaybills.stream()
                    .collect(Collectors.groupingBy(
                            out->out.getTallyId() + "_" + out.getWaybillCode()));

            List<AllPickUpOut> outList = pickUpOutMapper.selectBatchByTallyId(tallyKeys);
            outMap = outList.stream()
                    .collect(Collectors.groupingBy(
                            out->out.getTallyId() + "_" + out.getWaybillCode()));
        }
        for (NodeWaybillVo nodeWaybillVo : list) {
            List<HzArrTransfer> transferList = transferMap.getOrDefault(nodeWaybillVo.getFlightNo() + "_" + nodeWaybillVo.getExecDate(), Collections.emptyList());
            if (!transferList.isEmpty()) {
                nodeWaybillVo.setHandoverTime(transferList.get(0).getHandoverTime());
            }
            List<PickUpWaybill> waybillList = waybillMap.getOrDefault(nodeWaybillVo.getTallyId() + "_" + nodeWaybillVo.getWaybillCode(), Collections.emptyList());
            if (!waybillList.isEmpty()) {
                nodeWaybillVo.setPickUpTime(waybillList.get(0).getCreateTime());
            }

            List<AllPickUpOut> outList = outMap.getOrDefault(nodeWaybillVo.getTallyId() + "_" + nodeWaybillVo.getWaybillCode(), Collections.emptyList());
            if (!outList.isEmpty()) {
                nodeWaybillVo.setOutTime(outList.get(0).getOutTime());
            }
            if (nodeWaybillVo.getHandoverTime() == null){
                nodeWaybillVo.setHandoverTime(nodeWaybillVo.getWriteTime());
            }
            LinkedHashMap<String, Date> map = new LinkedHashMap<>();
            map.put("jl", nodeWaybillVo.getTerminalAlteratelAndInTime());
            map.put("jj", nodeWaybillVo.getHandoverTime());
            map.put("lh", nodeWaybillVo.getTallyTime());
            map.put("bd", nodeWaybillVo.getPickUpTime());
            map.put("th", nodeWaybillVo.getOutTime());
            getDiffTimeConsume(map, nodeWaybillVo);
        }
    }

    /**
     * 计算节点耗时方法
     * @param map 节点时间
     * @param nodeWaybillVo 返回数据
     */
    public void getDiffTimeConsume(Map<String,Date> map,NodeWaybillVo nodeWaybillVo) {
        LocalDateTime lastNonNullDateTime = null;

        long totalTime = 0;
        for (Map.Entry<String, Date> stringDateEntry : map.entrySet()) {
            LocalDateTime currentDateTime = null;
            if (stringDateEntry.getValue() != null) {
                currentDateTime = LocalDateTime.ofInstant(stringDateEntry.getValue().toInstant(), ZoneId.systemDefault());
            }
            if (currentDateTime != null) {
                if (lastNonNullDateTime != null) {
                    long minutesBetween = ChronoUnit.MINUTES.between(lastNonNullDateTime, currentDateTime);
                    totalTime = totalTime + minutesBetween;
                    switch (stringDateEntry.getKey()){
                        case "jj":
                            nodeWaybillVo.setSiteConsume(minutesBetween);
                            break;
                        case "lh":
                            nodeWaybillVo.setTallyConsume(minutesBetween);
                            break;
                        case "bd":
                            nodeWaybillVo.setPickUpConsume(minutesBetween);
                            break;
                        case "th":
                            nodeWaybillVo.setOutConsume(minutesBetween);
                            break;
                        default:
                            break;
                    }
                }
                lastNonNullDateTime = currentDateTime;
            }
        }
        nodeWaybillVo.setTotalTime(totalTime);
    }
}
