package com.gzairports.common.business.reporter.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.reporter.domain.ReportDataHawb;
import com.gzairports.wl.reporter.domain.query.CargoAnalysisQuery;
import com.gzairports.wl.reporter.domain.query.CarrierSumQuery;
import com.gzairports.wl.reporter.domain.vo.ReportWaybillInfoVO;
import com.gzairports.wl.reporter.domain.vo.WaybillWeightVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.security.core.parameters.P;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-03
 */
@Mapper
public interface ReportDataHawbMapper extends BaseMapper<ReportDataHawb> {

    /**
     * 查询主单运费
     * @param waybillCode 主单号
     * @return 运费
     */
    BigDecimal selectShippingFee(String waybillCode);

    List<WaybillWeightVO> baseCargoAnalysis(CargoAnalysisQuery query);

    List<WaybillWeightVO> compareCargoAnalysis(CargoAnalysisQuery query);

    BigDecimal selectIncome(@Param("deptId") Long deptId,@Param("writer") String writer,@Param("month") String month);

    /**
     * 每日客户汇总
     * @param query 查询条件
     * @return 结果
     */
    List<ReportWaybillInfoVO> selectCustomDaySum(CarrierSumQuery query);
}
