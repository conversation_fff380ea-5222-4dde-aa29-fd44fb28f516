package com.gzairports.wl.ticket.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 单证信息查询参数
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Data
public class TicketQuery {

    /** 票证来源 */
    private String billSource;

    /** 票证类型 */
    private String billType;

    /** 票证前缀 */
    private String billPrefix;

    /** 状态 */
    private Integer status;

    /** 所属单位 */
    private Long deptId;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 详情选择的单证状态 */
    private String statusDetail;

    /** 票证id */
    private Long ticketId;
}
