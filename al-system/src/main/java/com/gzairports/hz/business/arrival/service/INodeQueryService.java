package com.gzairports.hz.business.arrival.service;

import com.gzairports.hz.business.arrival.domain.query.NodeQuery;
import com.gzairports.hz.business.arrival.domain.vo.NodeVo;
import com.gzairports.hz.business.arrival.domain.vo.NodeWaybillVo;

import java.util.List;

/**
 * 运单保障节点查询Service接口
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
public interface INodeQueryService {

    /**
     * 运单保障节点查询列表数据
     * @param query 查询条件
     * @return 运单保障节点列表
     */
    NodeVo selectList(NodeQuery query);

    /**
     * 导出运单保障节点数据
     * @param query 查询条件
     * @return 结果
     */
    List<NodeWaybillVo> selectListByQuery(NodeQuery query);
}
