package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BaseCurrency;
import com.gzairports.common.basedata.domain.query.CurrencyQuery;
import com.gzairports.common.basedata.service.ICurrencyService;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 币种管理Controller
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@RestController
@RequestMapping("/base/currency")
@Api(tags = "币种管理数据接口")
public class CurrencyController extends BaseController {

    @Autowired
    private ICurrencyService currencyService;

    /**
     * 查询币种管理列表
     */
//    @PreAuthorize("@ss.hasPermi('base:currency:currencyList')")
    @GetMapping("/currencyList")
    @ApiOperation(value = "查询币种管理列表")
    public TableDataInfo specialCodeList(CurrencyQuery query)
    {
        startPage();
        List<BaseCurrency> list = currencyService.selectCurrencyList(query);
        return getDataTable(list);
    }

    /**
     * 导出币种管理
     */
    @PreAuthorize("@ss.hasPermi('base:currency:exportCurrency')")
    @Log(title = "导出币种管理", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCurrency")
    @ApiOperation(value = "导出币种管理")
    public void exportCurrency(HttpServletResponse response, CurrencyQuery query)
    {
        List<BaseCurrency> list = currencyService.selectCurrencyList(query);
        ExcelUtil<BaseCurrency> util = new ExcelUtil<BaseCurrency>(BaseCurrency.class);
        util.exportExcel(response, list, "币种管理");
    }


    /**
     * 导出币种管理下载模板
     */
    @PreAuthorize("@ss.hasPermi('base:currency:exportCurrency')")
    @Log(title = "导出币种管理", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCurrencyNull")
    @ApiOperation(value = "导出币种管理模板")
    public void exportCurrencyNull(HttpServletResponse response)
    {
        List<BaseCurrency> list = new ArrayList<>();
        ExcelUtil<BaseCurrency> util = new ExcelUtil<BaseCurrency>(BaseCurrency.class);
        util.exportExcel(response, list, "币种管理模板");
    }

    /**
     * 导入币种
     */
    @PreAuthorize("@ss.hasPermi('base:currency:importCurrency')")
    @Log(title = "导入币种", businessType = BusinessType.IMPORT)
    @PostMapping("/importCurrency")
    @ApiOperation(value = "导入币种")
    public AjaxResult importCurrency(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<BaseCurrency> util = new ExcelUtil<BaseCurrency>(BaseCurrency.class);
        List<BaseCurrency> currencies = util.importExcel(file.getInputStream());
        String message = currencyService.importCurrency(currencies, updateSupport);
        return success(message);
    }

    /**
     * 新增币种
     */
    @PreAuthorize("@ss.hasPermi('base:currency:addCurrency')")
    @Log(title = "新增币种", businessType = BusinessType.INSERT)
    @PostMapping("/addCurrency")
    @ApiOperation(value = "新增币种")
    public AjaxResult addCurrency(@RequestBody BaseCurrency currency)
    {
        return toAjax(currencyService.addCurrency(currency));
    }

    /**
     * 修改币种
     */
    @PreAuthorize("@ss.hasPermi('base:currency:editCurrency')")
    @Log(title = "修改币种", businessType = BusinessType.UPDATE)
    @PostMapping("/editCurrency")
    @ApiOperation(value = "修改币种")
    public AjaxResult editCurrency(@RequestBody BaseCurrency currency)
    {
        return toAjax(currencyService.editCurrency(currency));
    }

    /**
     * 删除币种
     */
    @PreAuthorize("@ss.hasPermi('base:currency:delCurrency')")
    @Log(title = "删除币种", businessType = BusinessType.UPDATE)
    @GetMapping("/delCurrency/{ids}")
    @ApiOperation(value = "删除币种")
    public AjaxResult delCurrency(@PathVariable("ids") Long[] ids)
    {
        return toAjax(currencyService.delCurrency(ids));
    }
}
