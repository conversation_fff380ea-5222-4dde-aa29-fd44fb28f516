package com.gzairports.hz.business.departure.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 冷藏登记审核表
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@Data
@TableName("hz_cold_examine")
public class HzColdExamine {

    /** 主键id */
    private Long id;

    /** 冷藏id */
    private Long coldId;

    /** 审核状态 0 审核中 1 通过 2 不通过*/
    private Integer status;

    /** 审核内容 */
    private String content;

    /** 创建时间 */
    private Date createTime;

    /** 操作人 */
    private String userName;
}
