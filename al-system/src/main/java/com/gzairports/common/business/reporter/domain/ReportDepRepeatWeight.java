package com.gzairports.common.business.reporter.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 复重统计表(all_report_dep_repeat_weight)
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@TableName("all_report_dep_repeat_weight")
public class ReportDepRepeatWeight {

    /** 复重数据ID */
    private long id;

    /** 航班日期 */
    private LocalDateTime flightDate;

    /** 航班号 */
    private String flightNo;

    /** 文件件数 取货物件数*/
    private Integer fileQuantity;

    /** 文件重量 */
    private BigDecimal fileWeight;

    /** 货物件数 */
    private Integer quantity;

    /** 货物净重 */
    private BigDecimal weight;

    /** 板箱自重 */
    private BigDecimal boardWeight;

    /** 板货总重 */
    private BigDecimal boardCargoWeight;

    /** 板箱车号 */
    private String uld;

    /** 操作人（昵称） */
    private String operName;

    /** 航班配载id */
    @TableField(exist = false)
    private Long flightLoadId;
}
