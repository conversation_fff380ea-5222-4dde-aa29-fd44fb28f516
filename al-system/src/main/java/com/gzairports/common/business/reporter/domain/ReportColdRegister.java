package com.gzairports.common.business.reporter.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-02-19
 */
@Data
@TableName("all_report_cold_register")
public class ReportColdRegister {

    /** 主键id */
    private Long id;

    /** 运单号 */
    private String waybillCode;

    /** 冷库 */
    private String coldStore;

    /** 使用时间 */
    private BigDecimal useTime;

    /** 计费金额 */
    private BigDecimal sumMoney;

    /** 状态 */
    private String status;

    /** 支付状态 */
    private String payStatus;

    /** 入库时间 */
    private String wareTime;

    /** 出库时间 */
    private String outTime;

    /** 进出港类型 */
    private String type;

    /** 部门id */
    private Long deptId;

    /** 代理人 */
    private String agentCode;
}
