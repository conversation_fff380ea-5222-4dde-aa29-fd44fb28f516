package com.gzairports.wl.departure.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 主分单拼单查询参数
 *
 * <AUTHOR>
 * @date 2024-05-23
 */
@Data
public class MergeQuery {

    /** 主单id */
    private Long mawbId;

    /** 运单号 */
    private String waybillCode;

    /** 拼单状态 */
    private Integer mergeStatus;

    /** 承运人（航司） */
    private String carrier;

    /** 发货人 */
    private String shipper;

    /** 收货人 */
    private String consignee;

    /** 制单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 制单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 所属单位id */
    private Long depId;
}
