package com.gzairports.hz.business.abnormal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.hz.business.abnormal.domain.HzNoLabel;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 无标签货物Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-26
 */
@Mapper
public interface HzNoLabelMapper extends BaseMapper<HzNoLabel>
{
    /**
     * 查询无标签货物
     * 
     * @param id 无标签货物ID
     * @return 无标签货物
     */
    HzNoLabel selectHzNoLabelById(Long id);

    /**
     * 查询无标签货物列表
     * 
     * @param hzNoLabel 无标签货物
     * @return 无标签货物集合
     */
    List<HzNoLabel> selectHzNoLabelList(HzNoLabel hzNoLabel);

    /**
     * 新增无标签货物
     * 
     * @param hzNoLabel 无标签货物
     * @return 结果
     */
    int insertHzNoLabel(HzNoLabel hzNoLabel);

    /**
     * 修改无标签货物
     * 
     * @param hzNoLabel 无标签货物
     * @return 结果
     */
    int updateHzNoLabel(HzNoLabel hzNoLabel);
}
