package com.gzairports.common.business.arrival.domain.query;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 流水号，提货码查询参数
 *
 * <AUTHOR>
 * @date 2024-01-13
 */
@Data
public class PickUpCodeQuery {

    /** 流水号 */
    private String serialNo;

    /** 提货码 */
    private String pickUpCode;

    private Date queryDate;

    /** 部门id */
    private Long deptId;

    private List<String> deptIdList;
}
