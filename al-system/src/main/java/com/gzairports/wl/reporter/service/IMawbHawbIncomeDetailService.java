package com.gzairports.wl.reporter.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzairports.wl.reporter.domain.query.MawbHawbIncomeQuery;
import com.gzairports.wl.reporter.domain.query.ReportExportData;
import com.gzairports.wl.reporter.domain.vo.AHIncomeDetailVO;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


public interface IMawbHawbIncomeDetailService {
    Page<AHIncomeDetailVO> queryExportDataList(MawbHawbIncomeQuery query);

    void export(List<AHIncomeDetailVO> data, HttpServletResponse response);

    void printPDF(MawbHawbIncomeQuery query, HttpServletResponse response);
}
