package com.gzairports.hz.business.reporter.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.system.service.ISysRoleService;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.hz.business.reporter.domain.*;
import com.gzairports.hz.business.reporter.domain.query.HzReportSetDto;
import com.gzairports.hz.business.reporter.domain.query.HzReportSetQuery;
import com.gzairports.hz.business.reporter.mapper.*;
import com.gzairports.hz.business.reporter.service.HzReportSetService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: lan
 * @create: 2025-03-05 17:23
 **/

@Service
@Transactional(rollbackFor = Exception.class)
public class HzReportSetServiceImpl extends ServiceImpl<HzReportSetMapper, HzReportSet> implements HzReportSetService {

    @Autowired
    private HzReportSetMapper hzReportSetMapper;

    @Autowired
    private HzReportFieldMapper hzReportFieldMapper;

    @Autowired
    private HzReportSetRelationMapper hzReportSetRelationMapper;

    @Autowired
    private HzReportSetFilterMapper hzReportSetFilterMapper;

    @Autowired
    private HzReportSetDisplayMapper hzReportSetDisplayMapper;

    @Autowired
    private HzReportSetDisplaySubtotalMapper hzReportSetDisplaySubtotalMapper;

    @Autowired
    private HzReportSetCountMapper hzReportSetCountMapper;

    @Autowired
    private ISysRoleService sysRoleService;


    @Override
    public List<HzReportSet> selectHzReportSetList(HzReportSetQuery query) {
//        List<HzReportSet> hzReportSets = hzReportSetMapper.selectHzReportSetList(query);
//        List<HzReportSet> hzReportSetList = new ArrayList<>();
//        for (HzReportSet hzReportSet: hzReportSets) {
//            String reportRole = hzReportSet.getReportRole();
//            List<SysRole> sysRoles = sysRoleService.selectRolesByUserId(SecurityUtils.getUserId());
//            for(SysRole sysRole:sysRoles){
//                if(reportRole.contains(sysRole.getRoleId().toString())){
//                    hzReportSetList.add(hzReportSet);
//                }
//            }
//        }
//        return hzReportSetList;
        return hzReportSetMapper.selectHzReportSetList(query);
    }

    @Override
    public int publishReport(Long id) {
        HzReportSet hzReportSet = hzReportSetMapper.selectById(id);
        hzReportSet.setReportStatus(1);
        return hzReportSetMapper.updateById(hzReportSet);
    }

    @Override
    public int downReport(Long id) {
        HzReportSet hzReportSet = hzReportSetMapper.selectById(id);
        hzReportSet.setReportStatus(0);
        return hzReportSetMapper.updateById(hzReportSet);
    }

    @Override
    public int delReport(Long id) {
        HzReportSet hzReportSet = hzReportSetMapper.selectById(id);
        hzReportSet.setIsDel(1);
        return hzReportSetMapper.updateById(hzReportSet);
    }

    @Override
    public List<HzReportField> selectTableList(Long type) {
        return hzReportFieldMapper.selectReportFieldList(type);
    }

    @Override
    public int insertReportSet(HzReportSetDto dto) {
        HzReportSet hzReportSet = new HzReportSet();
        hzReportSet.setReportTitle(dto.getReportTitle());
        hzReportSet.setReportDomint(dto.getReportDomint());
        hzReportSet.setReportType(dto.getReportType());
        hzReportSet.setReportRole(dto.getReportRole());
        hzReportSet.setIsFrame(dto.getIsFrame());
        hzReportSet.setPageSite(dto.getPageSite());
        hzReportSet.setReportStatus(0);
        hzReportSet.setIsDel(0);
        hzReportSetMapper.insert(hzReportSet);
        //外链
        if ("0".equals(dto.getIsFrame())) {
            return 1;
        }
        HzReportSetRelation hzReportSetRelation = new HzReportSetRelation();
        HzReportSetRelation relation = dto.getHzReportSetRelation();
        if(StringUtils.isNotNull(relation)){
            BeanUtils.copyProperties(relation,hzReportSetRelation);
            hzReportSetRelation.setSetId(hzReportSet.getId());
            hzReportSetRelationMapper.insert(hzReportSetRelation);
        }
        List<HzReportSetFilter> filters = dto.getHzReportSetFilter();
        if(!CollectionUtils.isEmpty(filters)){
            if (relation != null){
//                if (relation.getMasterTable() == 2 || relation.getSlaveTable() == 2 || relation.getSlaveTable2() == 2)
                if (ObjectUtil.equal(2, relation.getMasterTable()) ||
                        ObjectUtil.equal(2, relation.getSlaveTable()) ||
                        ObjectUtil.equal(2, relation.getSlaveTable2()))
                {
                    HzReportSetFilter writeTimeFilter = filters.stream().filter(e -> "write_time".equals(e.getFieldName())).findFirst().orElse(null);
                    LocalDate yesterday = LocalDate.now().minusDays(1);
                    LocalDateTime yesterdayStart = yesterday.atTime(LocalTime.MIN);
                    LocalDateTime yesterdayEnd = yesterday.atTime(LocalTime.MAX);
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    String startTime = yesterdayStart.format(formatter);
                    String endTime = yesterdayEnd.format(formatter);
                    if (writeTimeFilter != null){
                        if ("3".equals(writeTimeFilter.getFieldFilterType()) && writeTimeFilter.getFieldFilterValue() == null){
                            writeTimeFilter.setFieldFilterValue(startTime + "," + endTime);
                        }else if (writeTimeFilter.getFieldFilterValue() == null){
                            writeTimeFilter.setFieldFilterValue(startTime);
                        }
                    }else {
                        HzReportSetFilter setFilter = new HzReportSetFilter();
                        setFilter.setSetId(hzReportSet.getId());
                        setFilter.setFieldNameCn("填开时间");
                        setFilter.setFieldName("write_time");
                        setFilter.setFieldId(93L);
                        setFilter.setFieldFilterType("3");
                        setFilter.setFieldFilterValue(startTime + "," + endTime);
                        setFilter.setFieldType("DateTime");
                        filters.add(setFilter);
                    }
                }
            }
            for(HzReportSetFilter filter : filters){
                HzReportSetFilter hzReportSetFilter = new HzReportSetFilter();
                BeanUtils.copyProperties(filter,hzReportSetFilter);
                hzReportSetFilter.setSetId(hzReportSet.getId());
                if(StringUtils.isEmpty(hzReportSetFilter.getFieldFilterType())){
                    hzReportSetFilter.setFieldFilterType("0");
                }
                if("Enum".equals(hzReportSetFilter.getFieldType())){
                    hzReportSetFilter.setFieldFilterType("5");
                }
                hzReportSetFilterMapper.insert(hzReportSetFilter);
            }
        }

        List<HzReportSetDisplay> displays = dto.getHzReportSetDisplay();
        if(!CollectionUtils.isEmpty(displays)){
            for(HzReportSetDisplay display : displays){
                HzReportSetDisplay hzReportSetDisplay = new HzReportSetDisplay();
                BeanUtils.copyProperties(display,hzReportSetDisplay);
                hzReportSetDisplay.setSetId(hzReportSet.getId());
                hzReportSetDisplayMapper.insert(hzReportSetDisplay);
                List<HzReportSetDisplaySubtotal> subtotals = display.getHzReportSetDisplaySubtotal();
                if(!CollectionUtils.isEmpty(subtotals)){
                    for(HzReportSetDisplaySubtotal subtotal : subtotals){
                        HzReportSetDisplaySubtotal hzReportSetDisplaySubtotal = new HzReportSetDisplaySubtotal();
                        BeanUtils.copyProperties(subtotal,hzReportSetDisplaySubtotal);
                        hzReportSetDisplaySubtotal.setSetDisplayId(hzReportSetDisplay.getId());
                        hzReportSetDisplaySubtotalMapper.insert(hzReportSetDisplaySubtotal);
                    }
                }
            }
        }

        List<HzReportSetCount> counts = dto.getHzReportSetCount();
        if(!CollectionUtils.isEmpty(counts)){
            for(HzReportSetCount count : counts){
                HzReportSetCount hzReportSetCount = new HzReportSetCount();
                BeanUtils.copyProperties(count,hzReportSetCount);
                hzReportSetCount.setSetId(hzReportSet.getId());
                hzReportSetCountMapper.insert(hzReportSetCount);
            }
        }

        return 1;
    }

    @Override
    public HzReportSetDto infoSet(Long id) {
        HzReportSetDto vo = new HzReportSetDto();
        HzReportSet hzReportSet = hzReportSetMapper.selectById(id);
        BeanUtils.copyProperties(hzReportSet,vo);
        //外链
        if ("0".equals(hzReportSet.getIsFrame())) {
            return vo;
        }
        HzReportSetRelation relation = hzReportSetRelationMapper.selectOne(new QueryWrapper<HzReportSetRelation>()
                .eq("set_id", id));
        if(StringUtils.isNotNull(relation)){
            vo.setHzReportSetRelation(relation);
        }
        List<HzReportSetFilter> filters = hzReportSetFilterMapper.selectList(new QueryWrapper<HzReportSetFilter>()
                .eq("set_id", id));
        if(!CollectionUtils.isEmpty(filters)){
            vo.setHzReportSetFilter(filters);
        }
        List<HzReportSetDisplay> displays = hzReportSetDisplayMapper.selectList(new QueryWrapper<HzReportSetDisplay>()
                .eq("set_id", id));
        if(!CollectionUtils.isEmpty(displays)){
            for(HzReportSetDisplay display : displays){
                if(display.getFieldId() != null){
                    HzReportField hzReportField = hzReportFieldMapper.selectById(display.getFieldId());
                    if(hzReportField!=null){
                        display.setFieldType(hzReportField.getFieldType());
                    }
                }
                List<HzReportSetDisplaySubtotal> subtotals = hzReportSetDisplaySubtotalMapper.selectList(new QueryWrapper<HzReportSetDisplaySubtotal>()
                        .eq("set_display_id", display.getId()));
                if(!CollectionUtils.isEmpty(subtotals)){
                    display.setHzReportSetDisplaySubtotal(subtotals);
                }
            }
            vo.setHzReportSetDisplay(displays);
        }
        List<HzReportSetCount> counts = hzReportSetCountMapper.selectList(new QueryWrapper<HzReportSetCount>()
                .eq("set_id", id));
        if(!CollectionUtils.isEmpty(counts)){
            vo.setHzReportSetCount(counts);
        }
        return vo;
    }

    @Override
    public int updateSet(HzReportSetDto dto) {
        HzReportSet hzReportSet = hzReportSetMapper.selectById(dto.getId());
        hzReportSet.setReportTitle(dto.getReportTitle());
        hzReportSet.setReportDomint(dto.getReportDomint());
        hzReportSet.setReportType(dto.getReportType());
        hzReportSet.setReportRole(dto.getReportRole());
        hzReportSet.setIsFrame(dto.getIsFrame());
        hzReportSet.setPageSite(dto.getPageSite());
        hzReportSetMapper.updateById(hzReportSet);
        //外链
        if ("0".equals(dto.getIsFrame())) {
            return 1;
        }
        if(StringUtils.isNotNull(dto.getHzReportSetRelation())){
            hzReportSetRelationMapper.updateById(dto.getHzReportSetRelation());
        }

        List<HzReportSetFilter> filters = hzReportSetFilterMapper.selectList(new QueryWrapper<HzReportSetFilter>()
                .eq("set_id", dto.getId()));
        if(!CollectionUtils.isEmpty(filters)){
            for(HzReportSetFilter filter : filters){
                hzReportSetFilterMapper.deleteById(filter.getId());
            }
        }
        if(!CollectionUtils.isEmpty(dto.getHzReportSetFilter())){
            if (dto.getHzReportSetRelation() != null){
                if (ObjectUtil.equal(2, dto.getHzReportSetRelation().getMasterTable())){
                    HzReportSetFilter writeTimeFilter = dto.getHzReportSetFilter().stream().filter(e -> "write_time".equals(e.getFieldName())).findFirst().orElse(null);
                    LocalDate yesterday = LocalDate.now().minusDays(1);
                    LocalDateTime yesterdayStart = yesterday.atTime(LocalTime.MIN);
                    LocalDateTime yesterdayEnd = yesterday.atTime(LocalTime.MAX);
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    String startTime = yesterdayStart.format(formatter);
                    String endTime = yesterdayEnd.format(formatter);
                    if (writeTimeFilter != null){
                        if ("3".equals(writeTimeFilter.getFieldFilterType()) && writeTimeFilter.getFieldFilterValue() == null){
                            writeTimeFilter.setFieldFilterValue(startTime + "," + endTime);
                        }else if (writeTimeFilter.getFieldFilterValue() == null){
                            writeTimeFilter.setFieldFilterValue(startTime);
                        }
                    }else {
                        HzReportSetFilter setFilter = new HzReportSetFilter();
                        setFilter.setSetId(hzReportSet.getId());
                        setFilter.setFieldNameCn("填开时间");
                        setFilter.setFieldName("write_time");
                        setFilter.setFieldId(93L);
                        setFilter.setFieldFilterType("3");
                        setFilter.setFieldFilterValue(startTime + "," + endTime);
                        setFilter.setFieldType("DateTime");
                        dto.getHzReportSetFilter().add(setFilter);
                    }
                }
            }
            for(HzReportSetFilter filter : dto.getHzReportSetFilter()){
                HzReportSetFilter hzReportSetFilter = new HzReportSetFilter();
                BeanUtils.copyProperties(filter,hzReportSetFilter);
                hzReportSetFilter.setSetId(hzReportSet.getId());
                if("enum".equals(hzReportSetFilter.getFieldType())){
                    hzReportSetFilter.setFieldFilterType("0");
                }
                hzReportSetFilterMapper.insert(hzReportSetFilter);
            }
        }

        List<HzReportSetDisplay> displays = hzReportSetDisplayMapper.selectList(new QueryWrapper<HzReportSetDisplay>()
                .eq("set_id", dto.getId()));
        if(!CollectionUtils.isEmpty(displays)){
            for(HzReportSetDisplay display : displays){
                hzReportSetDisplayMapper.deleteById(display.getId());
                List<HzReportSetDisplaySubtotal> subtotals = hzReportSetDisplaySubtotalMapper.selectList(new QueryWrapper<HzReportSetDisplaySubtotal>()
                        .eq("set_display_id", display.getId()));
                if(!CollectionUtils.isEmpty(subtotals)){
                    for(HzReportSetDisplaySubtotal subtotal : subtotals){
                        hzReportSetDisplaySubtotalMapper.deleteById(subtotal.getId());
                    }
                }
            }
        }
        if(!CollectionUtils.isEmpty(dto.getHzReportSetDisplay())){
            for(HzReportSetDisplay display : dto.getHzReportSetDisplay()){
                HzReportSetDisplay hzReportSetDisplay = new HzReportSetDisplay();
                BeanUtils.copyProperties(display,hzReportSetDisplay);
                hzReportSetDisplay.setSetId(hzReportSet.getId());
                hzReportSetDisplayMapper.insert(hzReportSetDisplay);
                if(!CollectionUtils.isEmpty(display.getHzReportSetDisplaySubtotal())){
                    for(HzReportSetDisplaySubtotal subtotal : display.getHzReportSetDisplaySubtotal()){
                        HzReportSetDisplaySubtotal hzReportSetDisplaySubtotal = new HzReportSetDisplaySubtotal();
                        BeanUtils.copyProperties(subtotal,hzReportSetDisplaySubtotal);
                        hzReportSetDisplaySubtotal.setSetDisplayId(hzReportSetDisplay.getId());
                        hzReportSetDisplaySubtotalMapper.insert(hzReportSetDisplaySubtotal);
                    }
                }
            }
        }
        List<HzReportSetCount> counts = hzReportSetCountMapper.selectList(new QueryWrapper<HzReportSetCount>()
                .eq("set_id", dto.getId()));
        if(!CollectionUtils.isEmpty(counts)){
            for(HzReportSetCount count : counts){
                hzReportSetCountMapper.deleteById(count.getId());
            }
        }
        if(!CollectionUtils.isEmpty(dto.getHzReportSetCount())){
            for(HzReportSetCount count : dto.getHzReportSetCount()){
                HzReportSetCount hzReportSetCount = new HzReportSetCount();
                BeanUtils.copyProperties(count,hzReportSetCount);
                hzReportSetCount.setSetId(hzReportSet.getId());
                hzReportSetCountMapper.insert(hzReportSetCount);
            }
        }
        return 1;
    }

    /**
     * 新增计算公式
     * */
    @Override
    public int addFormula(HzReportFormula dto) {
//        String s = TABLE_MAP.get(dto.getMasterTable());
//        dto.setTableName(s);
//        try{
//            hzReportSetMapper.addFormula(dto);
//        }catch (Exception e){
//            e.printStackTrace();
//            throw new CustomException("英文字段有误,请重新输入");
//        }
        HzReportField hzReportFieldOld = hzReportFieldMapper.selectOne(new QueryWrapper<HzReportField>()
                .eq("field_name", dto.getFieldNameEn())
                .eq("type", dto.getMasterTable()));
        if(hzReportFieldOld != null){
            throw new CustomException("英文字段重复,请重新输入");
        }
        HzReportField hzReportField = new HzReportField();
        hzReportField.setType(dto.getMasterTable());
        hzReportField.setFieldNameCn(dto.getFieldNameCn());
        hzReportField.setFieldName(dto.getFieldNameEn());
        hzReportField.setFieldType("Int");
        hzReportFieldMapper.insert(hzReportField);
        return 1;
    }

    /** 主表 0:分单 1:分单费用数据 2:主单 3:主单收运数据
     4:预支付费用数据 5:配载数据 6:结算费用数据
     7:拉下 8:理货数据 9:提货办单数据 10:进港费用数据
     11:提货出库数据 12:运单保障节点 13:冷藏登记
     14:服务 */
    private static final Map<Integer,String> TABLE_MAP = new HashMap<>();
    static {
        TABLE_MAP.put(0,"all_report_data_hawb");
        TABLE_MAP.put(1,"all_report_data_item");
        TABLE_MAP.put(2,"all_report_data_waybill");
        TABLE_MAP.put(3,"all_report_data_collect");
        TABLE_MAP.put(4,"all_report_dep_pay_cost");
        TABLE_MAP.put(5,"all_report_dep_load");
        TABLE_MAP.put(6,"all_report_dep_settle_cost");
        TABLE_MAP.put(7,"all_report_pull_down");
        TABLE_MAP.put(8,"all_report_tally");
        TABLE_MAP.put(9,"all_report_pick_up");
        TABLE_MAP.put(10,"all_report_arr_cost");
        TABLE_MAP.put(11,"all_report_pick_out");
        TABLE_MAP.put(12,"all_report_node_query");
        TABLE_MAP.put(13,"all_report_cold_register");
        TABLE_MAP.put(14,"all_report_service_request");
        TABLE_MAP.put(15,"all_report_dep_repeat_weight");
    }
}
