package com.gzairports.hz.business.arrival.service;

import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.hz.business.arrival.domain.query.WaybillQuery;
import com.gzairports.hz.business.arrival.domain.vo.*;

import java.util.List;

/**
 * 运单查询Service接口
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
public interface IWaybillQueryService {

    /**
     * 运单查询列表
     * @param query 查询参数
     * @return 运单查询列表
     */
    List<WaybillQueryVo> selectList(WaybillQuery query);

    /**
     * 运单明细查询
     * @param waybillCode 运单号
     * @return 运单明细
     */
    WaybillDetailVo detail(String waybillCode);

    /**
     * 运单明细办单列表查询
     * @param waybillCode 运单号
     * @return 办单列表
     */
    List<BdVo> getBdList(String waybillCode);

    /**
     * 运单明细提货列表查询
     * @param waybillCode 运单号
     * @return 提货列表
     */
    List<ThVo> getThList(String waybillCode);

    /**
     * 运单明细库存列表查询
     * @param waybillCode 运单号
     * @return 库存列表
     */
    List<KcVo> getKcList(String waybillCode);

    /**
     * 运单日志查询列表
     * @param waybillCode 运单号
     * @return 日志列表
     */
    List<WaybillLog> selectLogList(String waybillCode);
}
