<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.cable.mapper.HzCableMapper">
    
    <resultMap type="HzCable" id="HzCableResult">
        <result property="id"    column="id"    />
        <result property="cableNo"    column="cable_no"    />
        <result property="serialNo"    column="serial_no"    />
        <result property="cableAddress"    column="cable_address"    />
        <result property="receiveAddress"    column="receive_address"    />
        <result property="cableTime"    column="cable_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="type"    column="type"    />
        <result property="remark"    column="remark"    />
        <result property="isAuto"    column="is_auto"    />
        <result property="isSend"    column="is_send"    />
        <result property="status"    column="status"    />
        <result property="version"    column="version"    />
        <result property="priority"    column="priority"    />
        <result property="flightNo"    column="flight_no"    />
        <result property="content"    column="content"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectHzCableVo">
        select id, cable_no, serial_no, cable_address, receive_address, cable_time, create_by, type, remark, is_auto, is_send,
               status, version, priority, flight_date, flight_no, content, is_del from hz_cable
    </sql>

    <select id="selectHzCableList" parameterType="HzCable" resultMap="HzCableResult">
        <include refid="selectHzCableVo"/>
        <where>  
            <if test="cableNo != null  and cableNo != ''"> and cable_no like concat('%', #{cableNo}, '%')</if>
            <if test="serialNo != null  and serialNo != ''"> and serial_no like concat('%', #{serialNo}, '%')</if>
            <if test="cableAddress != null  and cableAddress != ''"> and cable_address like concat('%', #{cableAddress}, '%')</if>
            <if test="receiveAddress != null  and receiveAddress != ''"> and receive_address like concat('%', #{receiveAddress}, '%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="isSend != null "> and is_send = #{isSend}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="flightNo != null  and flightNo != ''"> and flight_no like concat('%', #{flightNo}, '%')</if>
            <if test="flightDate != null"> and flight_date = #{flightDate}</if>
            <if test="startTime != null">
                and cable_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and cable_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="content != null  and content != ''"> and content like concat('%', #{content}, '%')</if>
        </where>
    </select>
    
    <select id="selectHzCableById" parameterType="Long" resultMap="HzCableResult">
        <include refid="selectHzCableVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertHzCable" parameterType="HzCable" useGeneratedKeys="true">
        insert into hz_cable
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="cableNo != null">cable_no,</if>
            <if test="serialNo != null">serial_no,</if>
            <if test="cableAddress != null">cable_address,</if>
            <if test="receiveAddress != null and receiveAddress != ''">receive_address,</if>
            <if test="cableTime != null">cable_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="type != null">type,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="isAuto != null">is_auto,</if>
            <if test="isSend != null">is_send,</if>
            <if test="status != null">status,</if>
            <if test="version != null">version,</if>
            <if test="priority != null">priority,</if>
            <if test="flightNo != null">flight_no,</if>
            <if test="flightDate != null">flight_date,</if>
            <if test="content != null">content,</if>
            <if test="isDel != null">is_del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="cableNo != null">#{cableNo},</if>
            <if test="serialNo != null">#{serialNo},</if>
            <if test="cableAddress != null">#{cableAddress},</if>
            <if test="receiveAddress != null and receiveAddress != ''">#{receiveAddress},</if>
            <if test="cableTime != null">#{cableTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="type != null">#{type},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="isAuto != null">#{isAuto},</if>
            <if test="isSend != null">#{isSend},</if>
            <if test="status != null">#{status},</if>
            <if test="version != null">#{version},</if>
            <if test="priority != null">#{priority},</if>
            <if test="flightNo != null">#{flightNo},</if>
            <if test="flightDate != null">#{flightDate},</if>
            <if test="content != null">#{content},</if>
            <if test="isDel != null">#{isDel},</if>
         </trim>
    </insert>

    <update id="updateHzCable" parameterType="HzCable">
        update hz_cable
        <trim prefix="SET" suffixOverrides=",">
            <if test="cableNo != null">cable_no = #{cableNo},</if>
            <if test="serialNo != null">serial_no = #{serialNo},</if>
            <if test="cableAddress != null">cable_address = #{cableAddress},</if>
            <if test="receiveAddress != null and receiveAddress != ''">receive_address = #{receiveAddress},</if>
            <if test="cableTime != null">cable_time = #{cableTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="type != null">type = #{type},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="isAuto != null">is_auto = #{isAuto},</if>
            <if test="isSend != null">is_send = #{isSend},</if>
            <if test="status != null">status = #{status},</if>
            <if test="version != null">version = #{version},</if>
            <if test="priority != null">priority = #{priority},</if>
            <if test="flightNo != null">flight_no = #{flightNo},</if>
            <if test="flightDate != null">flight_date = #{flightDate},</if>
            <if test="content != null">content = #{content},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>