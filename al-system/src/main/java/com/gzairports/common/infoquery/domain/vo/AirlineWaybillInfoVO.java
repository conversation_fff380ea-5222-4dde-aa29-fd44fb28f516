package com.gzairports.common.infoquery.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 航司 VO
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
public class AirlineWaybillInfoVO {

    /** 运单号 */
    private String waybillCode;

    /** 运单出港状态 */
    private String status;

    /** 目的站 */
    private String desPort;

    /** 配载航班号 */
    private String flightNo;

    /** 配载航班日期 */
    private LocalDateTime flightDate;

    /** 发货人 */
    private String shipper;

    /** 提货代码（字段缺失，可补充） */
    private String pickUpCode;

    /** 品名编码 */
    private String cargoCode;

    /** 品名 */
    private String cargoName;

    /** 制单时间 */
    private LocalDateTime writeTime;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 计费重量 */
    private BigDecimal chargeWeight;
}
