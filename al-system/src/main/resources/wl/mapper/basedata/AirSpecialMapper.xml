<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.AirSpecialMapper">
    <insert id="insert">
        insert into base_air_special(air_code_id, special_code_id) values (#{id},#{aLong})
    </insert>
    <select id="selectByCargoId" resultType="java.lang.Long">
        select special_code_id from base_air_special where air_code_id = #{id}
    </select>
</mapper>