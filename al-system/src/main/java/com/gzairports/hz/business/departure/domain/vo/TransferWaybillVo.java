package com.gzairports.hz.business.departure.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 出港货物交接运单参数
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
public class TransferWaybillVo {

    /** 主键id */
    private Long id;

    /** 运单号 */
    private String waybillCode;

    /** 目的站 */
    private String desPort;

    /** 预配航班 */
    private String flight;

    /** 货品代码 */
    private String cargoCode;

    /** 品名 */
    private String cargoName;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 计费重量 */
    private BigDecimal chargeWeight;

    /** 入库件数 */
    private Integer storeQuantity;

    /** 入库重量 */
    private BigDecimal storeWeight;

    /** 状态 */
    private String status;
}
