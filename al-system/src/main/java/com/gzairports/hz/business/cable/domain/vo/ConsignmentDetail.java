package com.gzairports.hz.business.cable.domain.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-04-07
 */
@Data
public class ConsignmentDetail {

    /** 货物运输信息 */
    private String awbType;

    /** 货物运输信息 */
    private String consignmentOnwardMovementInformation;

    /** 货物原产地代码 */
    private String customsOriginCode;

    /** 密度组 */
    private String densityGroup;

    /** 密度标识 */
    private String densityIndicator;

    /** 启运港 */
    private String depAirport;

    /** 目的港 */
    private String desAirport;

    /** 尺寸 */
    private String dimensions;

    /** 货物品名 */
    private String goodsName;

    /** 主运单号 */
    private String mawbNo;

    /** 其他服务信息 */
    private String osi;

    /** 其他海关信息 */
    private String otherCustoms;

    /** 装载件数 */
    private String pieces;

    /** 特殊处理代码 */
    private String[] shc;

    /** 运输描述代码 分割托运 D 多批托运 M 部分托运 P 拆分托运 S 总托运 T*/
    private String shipmentDescriptionCode;

    private String stowageSlac;

    /** 总件数 */
    private String totalPieces;

    private String totalSlac;

    /** 体积 */
    private String volume;

    /** 体积单位 */
    private String volumeUnit;

    /** 重量 */
    private String weight;

    /** 重量单位 */
    private String weightUnit;

    /** 特殊处理代码 */
    private String shcStr;
}
