<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.departure.mapper.HzColdRegisterMapper">
    <select id="getWaybillCodeByFour" resultType="java.lang.String">
        select waybill_code from all_air_waybill
        where waybill_code like concat('%',#{waybillCode})
    </select>
    <select id="selectNewListByQuery"
            resultType="com.gzairports.hz.business.departure.domain.vo.ColdQueryVo">
        select hcr.id, hcr.waybill_code, hcr.cargo_name, ba.agent, hcr.cold_store, hcr.status, hcr.pay_status, hcr.time_len,
               hcr.apply_user, hcr.ware_time, hcr.out_time, hcr.type, hcr.use_time, hcr.charge_time, hcr.sum_money, hcr.apply_time,
               hcr.examine_status
        from hz_cold_register hcr
        left join all_air_waybill aaw on hcr.waybill_code = aaw.waybill_code
        left join base_agent ba on ba.dept_id = aaw.dept_id
        where hcr.type = aaw.type
            <if test="waybillCode != null  and waybillCode != ''"> and hcr.waybill_code like concat('%',#{waybillCode},'%')</if>
            <if test="coldStore != null  and coldStore != ''"> and hcr.cold_store = #{coldStore}</if>
            <if test="status != null"> and hcr.status = #{status}</if>
            <if test="agentCode != null  and agentCode != ''"> and ba.agent like concat('%',#{agentCode},'%')</if>
            <if test="payStatus != null">
                and hcr.pay_status = #{payStatus}
            </if>
            <if test="startTime != null">
                and hcr.apply_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and hcr.apply_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="type != null and type != ''">
                and hcr.type = #{type}
            </if>
            <if test="deptId != null">
                and hcr.dept_id = #{deptId}
            </if>
        order by hcr.apply_time desc
    </select>
    <select id="selectReportColdList"
            resultType="com.gzairports.common.business.reporter.domain.ReportColdRegister">
        select hcr.id, hcr.waybill_code, hcr.cold_store, hcr.use_time, hcr.sum_money, hcr.status, hcr.pay_status, hcr.ware_time, hcr.out_time, hcr.type, hcr.dept_id, sd.dept_name as agentCode
        from hz_cold_register hcr
            left join sys_dept sd on sd.dept_id = hcr.dept_id
        where 1=1
        <if test="lastSyncTime != null">
            and hcr.update_time > #{lastSyncTime}
        </if>
        <if test="dateNow != null">
            and hcr.update_time <![CDATA[<=]]> #{dateNow}
        </if>
    </select>
    <select id="selectWaybillColdStatus"
            resultType="com.gzairports.hz.business.departure.domain.HzColdRegister">
        select * from hz_cold_register where waybill_code = #{waybillCode}
    </select>
</mapper>