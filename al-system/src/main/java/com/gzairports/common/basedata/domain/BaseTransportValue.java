package com.gzairports.common.basedata.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 运输价值维护
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
public class BaseTransportValue {

    /** 主键id */
    private Long id;

    /** 名称 */
    private String name;

    /** 运输价值类型 */
    private Integer transportType;

    /** 货品大类 */
    private String cargoCategory;

    /** 货品代码 */
    private String cargoCode;

    /** 货物品名 */
    private String cargoName;

    /** 保险费率 */
    private BigDecimal rate;

    /** 是否启用 */
    private Integer isEnable;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 是否删除 */
    private Integer isDel;

    /** 所属单位 */
    private Long deptId;
}
