package com.gzairports.framework.config;

import com.gzairports.common.config.FrameworkConfig;
import com.gzairports.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.nio.charset.Charset;

/**
 * <AUTHOR>
 * @Description: 自定义redis key序列化，统一添加项目前缀
 * @date 2021/11/11 10:02
 */
@Slf4j
@Component
public class KeyStringRedisSerializer implements RedisSerializer<String> {

    private final Charset charset;
    private String prefix;
    @Autowired
    private FrameworkConfig frameworkConfig;
    public KeyStringRedisSerializer() {
        this ( Charset.forName ( "UTF8" ) );
    }



    public KeyStringRedisSerializer(Charset charset) {
        Assert.notNull ( charset, "Charset must not be null!" );
        this.charset = charset;
    }

    @Override
    public byte[] serialize(String string) throws SerializationException {
        if(StringUtils.isBlank(string)){
            log.error("未配置framework.redisKeyPrefix，无法添加redis前缀，建议配置");
        }
        prefix = StringUtils.isBlank(frameworkConfig.getRedisKeyPrefix()) ? "defult" : frameworkConfig.getRedisKeyPrefix() + ":";

        return (string == null ? null : (prefix + string).getBytes(charset));
    }

    @Override
    public String deserialize(byte[] bytes) throws SerializationException {
        prefix = StringUtils.isBlank(frameworkConfig.getRedisKeyPrefix()) ? "defult" : frameworkConfig.getRedisKeyPrefix() + ":";

        return (bytes == null ? null : new String(bytes, charset).replaceFirst(prefix, ""));
    }
}
