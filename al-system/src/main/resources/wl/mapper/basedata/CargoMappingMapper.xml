<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.CargoMappingMapper">
    <select id="selectCargoCodeMappingList" resultType="com.gzairports.common.basedata.domain.BaseCargoMapping">
        select id, cargo_code, cargo_name, air_cargo_code, air_cargo_name, air_code from base_cargo_mapping
        <where>
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="cargoCode != null  and cargoCode != ''"> and cargo_code like concat('%',#{cargoCode},'%')</if>
            <if test="airCargoCode != null and airCargoCode != ''"> and air_cargo_code like concat('%',#{airCargoCode},'%')</if>
            <if test="airCode != null and airCode != ''"> and air_code = #{airCode}</if>
            and is_del = 0
        </where>
    </select>
</mapper>
