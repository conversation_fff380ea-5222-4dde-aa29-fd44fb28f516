package com.gzairports.hz.business.departure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzairports.common.business.departure.domain.vo.CountCostVo;
import com.gzairports.common.business.departure.domain.vo.ItemDetailVo;
import com.gzairports.common.business.departure.domain.vo.ItemWaybillVo;
import com.gzairports.hz.business.arrival.domain.query.FlightFileQuery;
import com.gzairports.hz.business.arrival.domain.query.WaybillQuery;
import com.gzairports.hz.business.arrival.domain.vo.EnterWaybillVo;
import com.gzairports.hz.business.arrival.domain.vo.WaybillDetailVo;
import com.gzairports.hz.business.arrival.domain.vo.WaybillQueryVo;
import com.gzairports.hz.business.departure.domain.AirWaybill;
import com.gzairports.hz.business.departure.domain.HzColdRegister;
import com.gzairports.hz.business.departure.domain.query.*;
import com.gzairports.hz.business.departure.domain.vo.*;
import com.gzairports.hz.business.transfer.domain.query.TransferPickQuery;
import com.gzairports.hz.business.transfer.domain.vo.TransferPickVo;
import com.gzairports.wl.departure.domain.vo.OnlineMawbVo;
import com.gzairports.wl.departure.domain.vo.OnlineWaybillVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 运单Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-24
 */
@Mapper
public interface AllAirWaybillMapper extends BaseMapper<AirWaybill> {

    /**
     * 录单时运单状态列表查询
     * @param query 查询条件
     * @return 列表数据
     * todo 慢sql
     */
    List<WaybillStatusVo> selectWaybillStatusListForRecordOrder(WaybillStatusQuery query);

    /**
     * 收运时运单状态列表查询
     * @param query 查询条件
     * @return 列表数据
     */
    List<WaybillStatusVo> selectWaybillStatusListForCollect(WaybillStatusQuery query);


    /**
     * 退货时运单状态列表查询
     * @param query 查询条件
     * @return 列表数据
     */
    List<WaybillStatusVo> selectWaybillStatusListForExitCargo(WaybillStatusQuery query);

    /**
     * 运单明细查询
     * @param query 查询条件
     * @return 结果
     */
    WaybillInfoVo getInfo(WaybillInfoQuery query);

    /**
     * 运单明细查询
     * @param query 查询条件
     * @return 结果
     */
    WaybillInfoVo getStatusInfo(WaybillInfoQuery query);

    /**
     * 根据id集合查询运单详情列表
     * @param mawbId 运单id
     * @return 运单详情列表
     */
    TransferWaybillVo selectByWaybillId(Long mawbId);

    /**
     * 根据id查询运单收运详情
     * @param waybillId 运单id
     * @return 结果
     */
    WaybillInfoVo getInfoById(Long waybillId);

    /**
     * 虚拟收运列表
     * @param query 查询参数
     * @return 列表
     */
    List<VirtualListVo> virtualList(VirtualListQuery query);

    /**
     * 根据条件查询收费管理运单数据
     * @param query 查询条件
     * @return 结果
     */
    Page<ChargeWaybillVo> selectChargeList(@Param("query") ChargeQuery query,@Param("page") Page page);

    /**
     * 根据运单号查询品名
     * @param waybillCode 运单号
     * @param type 进出港类型
     * @return 品名
     */
    HzColdRegister selectCargoName(@Param("waybillCode") String waybillCode, @Param("type") String type,@Param("deptId") Long deptId);

    /**
     * 根据运单号查询录入信息
     * @param query 查询条件
     * @return 结果
     */
    EnterWaybillVo getInfoByCode(FlightFileQuery query);

    /**
     * 运单明细查询
     * @param waybillCode 运单号
     * @return 运单明细
     */
    WaybillDetailVo detail(String waybillCode);

    /**
     *  中转交接挑单
     * @param waybillCode 运单号
     * @return 挑单数据
     */
    TransferPickVo pickOne(String waybillCode);

    /**
     *  中转交接批量挑单
     * @param query 查询参数
     * @return 批量挑单数据
     */
    List<TransferPickVo> batchPick(TransferPickQuery query);

    /**
     * 航班承运时运单状态列表查询
     * @param query 查询条件
     * @return 列表数据
     */
    List<WaybillStatusVo> selectWaybillStatusListForFlightAcceptance(WaybillStatusQuery query);

    /**
     * 根据id集合查询中转交接运单
     * @param waybillIds 运单id集合
     * @return 中转交接运单集合
     */
    List<TransferPickVo> selectByIds(@Param("waybillIds") List<Long> waybillIds);

    /**
     * 根据运单id查询录入信息
     * @param waybillId 运单id
     * @return 结果
     */
    EnterWaybillVo selectInfoById(Long waybillId);

    /**
     * 加入提货列表
     * @param waybillIds 运单id集合
     * @return 中转列表
     */
    List<TransferPickVo> pickList(@Param("ids") Long[] waybillIds);

    /**
     * 运单明细查询收费
     * @param query 查询条件
     * @return 收费数据
     */
    ChargeStatusVo selectChargeInfo(WaybillInfoQuery query);

    /**
     * 根据运单id查询品名
     * @param collect 运单id
     * @return 品名集合
     */
    List<String> selectCargoNames(@Param("collect") List<Long> collect);

    /**
     * 根据运单后八位查询
     * @param code 运单后八位
     * @return 查询结果
     */
    List<WaybillIdAndCodeVo> selectEightCodeList(String code);

    /**
     * 安检申报列表
     * @param query 查询参数
     * @return 列表
     */
    List<SecurityVo> securitySubmitList(SecurityQuery query);

    /**
     * 根据id查询运单数据
     * @param id 查询参数
     * @return 安检申报返回参数
     */
    SecurityVo selectWaybillbyid(String id);

    /**
     * 查询运单状态
     * @param query 查询条件
     * @return 结果
     */
    List<WaybillQueryVo> selectQueryList(WaybillQuery query);

    /**
     * 根据运单号查询部门id
     * @param waybillCode
     * @return
     */
    Long selectDeptIdByCode(String waybillCode);

    /**
     * 查询收费所需运单信息
     * @param vo 查询参数
     * @return 结果
     */
    ItemWaybillVo selectWaybillItemInfo(ItemDetailVo vo);

    Long selectDeptId(@Param("waybillCode") String waybillCode,@Param("type") String type);

    Page<ChargeImportVo> chargeExportList(@Param("query") ChargeQuery query,@Param("page") Page<ChargeImportVo> page);

    PrintCaptainVo selectDangerInfo(Long waybillId);
    PrintCaptainVo selectSpecialInfo(Long waybillId);

    String selectWaybillCode(Long waybillId);

    AirWaybill selectDelInfoById(Long id);

    int updateDelById(@Param("id") Long id, @Param("version") Integer version);

    AirWaybill selectComeInfo(@Param("waybillCode") String waybillCode,@Param("type") String type,@Param("deptId") Long deptId);

    AirWaybill selectComeInfoNew(@Param("waybillCode") String waybillCode,@Param("type") String type,@Param("deptIds") List<Long> deptIds);

    Integer selectPayCount(@Param("page") Page<OnlineMawbVo> page, @Param("query") ChargeQuery query);

    List<OnlineWaybillVo> selectRefund(@Param("page") Page<OnlineMawbVo> page, @Param("query") ChargeQuery query);

    List<OnlineWaybillVo> selectByStatusByNull(@Param("query") ChargeQuery query, @Param("status") Integer status);

    List<OnlineWaybillVo> selectByStatusForPayStatus(@Param("query") ChargeQuery query, @Param("status") Integer status);

    BigDecimal selectWeight(@Param("waybillCode") String waybillCode);

    List<Long> selectErrorData(CountCostVo vo);

    List<OnlineWaybillVo> selectTotalOrderList(@Param("query") ChargeQuery query);

    AirWaybill selectByIdSome(Long waybillId);

    int updateDataById(@Param("id") Long id, @Param("version") Integer version);

    List<String> selectWaybill(@Param("waybillCodeList") List<String> waybillCodeList,@Param("endTime") LocalDateTime endTime);
}
