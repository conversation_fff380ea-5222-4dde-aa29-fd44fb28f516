package com.gzairports.hz.business.departure.domain.query;

import lombok.Data;

import java.util.Date;

/**
 * 开箱检查查询参数
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Data
public class PinCheckQuery {

    /** 运单号 */
    private String waybillCode;

    /** 申报是否与实际一致 0 否 1 是*/
    private Integer declarationConsistent;

    /** 检查时间 */
    private Date startTime;

    /** 检查时间 */
    private Date endTime;

    /** 代理人 */
    private String agent;

    /** 用户姓名 */
    private String userName;
}
