package com.gzairports.common.business.departure.enums;

import com.gzairports.common.business.departure.domain.vo.AwbinfoVo;
import com.gzairports.common.business.departure.domain.vo.LogAwblog;
import com.gzairports.common.business.departure.domain.vo.TImpDlv;
import com.gzairports.common.business.departure.domain.vo.TLogFeelog;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/***
 * 航班状态
 */
@Slf4j
public enum CargoStatus {
    /**
     * 出港运单状态
     */
    not_in("D", 0, "not_in", "未入库", "NEWAWB", "A"),
    not_pre("D", 1, "not_pre", "未预配", "RCS", "A"),
    al_pre("D", 2, "al_pre", "已预配", "MAN", "A"),
    fz_out("D", 3, "fz_out", "复重出库", "DEP", "A"),
    pull_down("D", 4, "pull_down", "临时拉下", "OFLD", "A"),
    change_order("D", 5, "change_order", "已换单", " ", ""),
    quit_oerder("D", 6, "quit_order", "货物退仓", "RCS", "B"),
    /**
     * 航班状态
     */
    delay("D", 7, "delay", "延误", "", ""),
    diversion("D", 8, "diversion", "备降", "", ""),
    reach("D", 9, "reach", "到达", "", ""),

    /**
     * 进港运单状态
     */
    a_in("A", 0, "a_in", "进港入库", "NEWAWB", "A"),
    lh_comp("A", 1, "lh_comp", "理货完成", "RCF", "A"),
    bl_comp("A", 2, "bl_comp", "办理提货", "FEE", "A"),
    ta_tx("A", 3, "ta_tx", "已提货", "DLV", "A");


    CargoStatus(String offin, int level, String code, String desc, String cfpsCode, String cfpsSubCode) {
        this.offin = offin;
        this.code = code;
        this.desc = desc;
        this.cfpsCode = cfpsCode;
        this.cfpsSubCode = cfpsSubCode;
        this.level = level;
    }

    public static void setCargoStatusByLogs(String offin, AwbinfoVo vo, List<LogAwblog> awblogs,
                                            List<TLogFeelog> feelogs, List<TImpDlv> dlvList) {
        CargoStatus reStatus = null;
        LogAwblog reAwblog = new LogAwblog();
        for (LogAwblog awblog : awblogs) {
            //设置航班日期和航班号
            if (StringUtils.isNotBlank(awblog.getFlightno())) {
                String[] split = awblog.getFlightno().split("/");
                vo.setPreairline(split[0].substring(0, 2));
                vo.setPreflightno(split[0].substring(2));
                Date date = null;
                try {
                    date = DateUtils.parseDate(split[1], DateUtils.YYYYMMDD);
                } catch (ParseException e) {
                    log.error("运单{}日志{}错误", vo.getBillid(), awblog.getRecid());
                    log.error(e.getMessage(), e);
                }
                vo.setPreflightdate(date);
            }

            CargoStatus status = getCargoStatus(offin, awblog.getStatusid(), awblog.getSubid());
            if (status == null) {
                continue;
            }
            if (reStatus == null ||
                    ("D".equals(offin) && awblog.getOpetime().after(reAwblog.getOpetime()))
                    || ("A".equals(offin) && (status.level > reStatus.level ||
                    (status.level >= reStatus.level && awblog.getOpetime().after(reAwblog.getOpetime()))))
            ) {
                reStatus = status;
                reAwblog = awblog;
                vo.setStatus(reStatus.code);
                vo.setStatusDesc(reStatus.desc);
                vo.setStatusTime(reAwblog.getOpetime());
                vo.setPcs(new BigDecimal(awblog.getPcs()));
                vo.setWeight(awblog.getWeight());
            }
        }

        TLogFeelog reFeelog = null;
        for (TLogFeelog feelog : feelogs) {
            CargoStatus status = getCargoStatus(offin, feelog.getStatusid(), feelog.getSubid());
            if (status == null) {
                continue;
            }
            if (reStatus == null ||
                    ("D".equals(offin) && feelog.getOpetime().after(reAwblog.getOpetime() == null ? feelog.getOpetime() : reAwblog.getOpetime()))
                    || ("A".equals(offin) && feelog.getOpetime().after(reAwblog.getOpetime() == null ? feelog.getOpetime() : reAwblog.getOpetime()))
            ) {
                reStatus = status;
                reFeelog = feelog;
                vo.setStatus(reStatus.code);
                vo.setStatusDesc(reStatus.desc);
                vo.setStatusTime(reFeelog.getOpetime());
            }
        }
        if (reFeelog != null && dlvList != null) {
            TImpDlv tdlv = null;
            for (TImpDlv dlv : dlvList) {
                if (tdlv == null || dlv.getPickopetime().after(tdlv.getPickopetime())) {
                    tdlv = dlv;
                    vo.setLhpCs(tdlv.getPcs());
                    vo.setLhpWeight(tdlv.getWeight());
                }
            }
        }
    }

    public static CargoStatus getCargoStatus(String offin, String cfpsCode, String cfpsSubCode) {
        for (CargoStatus value : values()) {
            if (value.offin.equals(offin) && value.cfpsCode.equals(cfpsCode) && value.cfpsSubCode.equals(cfpsSubCode)) {
                return value;
            }
        }
        return null;
    }

    public final String offin;
    public final String code;
    public final String desc;

    public final String cfpsCode;
    public final String cfpsSubCode;
    public final int level;
}
