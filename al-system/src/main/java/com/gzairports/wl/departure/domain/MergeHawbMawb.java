package com.gzairports.wl.departure.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 国内主单表
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@Data
@TableName("wl_dep_merge_hawb_mawb")
public class MergeHawbMawb {

    /** 主键id */
    private Long id;

    /** 主单id */
    private Long mawbId;

    /** 分单id */
    private Long hawbId;

    /** 分运单拼单件数 */
    private Integer mergeNum;

    /** 分运单拼单重量 */
    private BigDecimal mergeWeight;

    /** 分运单待拼件数 */
    private Integer remainNum;

    /** 分运单待拼重量 */
    private BigDecimal remainWeight;


}
