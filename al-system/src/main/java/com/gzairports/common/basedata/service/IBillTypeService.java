package com.gzairports.common.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.basedata.domain.BaseBillType;

import java.util.List;

/**
 * 票证管理Service接口
 *
 * <AUTHOR>
 * @date 2024-05-17
 */
public interface IBillTypeService extends IService<BaseBillType> {

    /**
     * 查询票证来源列表
     *
     * @return 票证来源列表
     */
    List<BaseBillType> selectList();

    /**
     * 查询票证管理详情
     *
     * @param id 票证管理id
     * @return 票证管理详情
     */
    BaseBillType getInfo(Long id);

    /**
     * 新增票证管理
     *
     * @param baseBillType 票证管理
     * @return 结果
     */
    int insertType(BaseBillType baseBillType);

    /**
     * 新增票证管理
     *
     * @param baseBillType 票证管理
     * @return 结果
     */
    int updateType(BaseBillType baseBillType);
}
