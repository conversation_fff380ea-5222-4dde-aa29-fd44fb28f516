package com.gzairports.wl.charge.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 客户运价表
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@Data
@TableName("wl_sf_custom_price")
public class FreightRateCustom extends Model<FreightRateCustom> {

    /** 主键id */
    @TableId(value = "id")
    private Long id;

    /** 运价名称 */
    private String rateName;

    /** 运价模式 */
    private Integer rateModel;

    /** 适用单证类型 */
    private String applicableDocuments;

    /** 客户名称 */
    private String customer;

    /** 是否自动从低 */
    private Integer automaticFromLow;

    /** 取舍方式 */
    private Integer roundingMethod;

    /** 生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date effectiveDate;

    /** 截止日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expirationDate;

    /** 备注 */
    private String remarks;

    /** 所属单位 */
    private Long deptId;

    /** 条目是否逻辑删除 */
    private Integer isDel;

    /** 更新时间 */
    private Date updateTime;

    /** 更新人 */
    private String updateBy;

    /** 创建时间 */
    private Date createTime;

    /** 创建人 */
    private String createBy;
}
