package com.gzairports.common.core.typehandler;



import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.gzairports.common.utils.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class StringListJsonTypeHandler extends BaseTypeHandler<List<String>> {
    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int columnIndex, List<String> parameter, JdbcType jdbcType) throws SQLException {
        if (parameter==null){
            preparedStatement.setString(columnIndex, null);
        }
        preparedStatement.setString(columnIndex, JSON.toJSONString(parameter));
    }

    @Override
    public List<String> getNullableResult(ResultSet resultSet, String columnName) throws SQLException {
        ArrayList<String> result = new ArrayList<>();
        JSONArray jsonArray = JSON.parseArray(resultSet.getString(columnName));
        if(StringUtils.isNotNull(jsonArray)){
            for (int i = 0; i < jsonArray.size(); i++) {
                result.add(jsonArray.getString(i));
            }
        }
        return result;
    }

    @Override
    public List<String> getNullableResult(ResultSet resultSet, int columnIndex) throws SQLException {
        ArrayList<String> result = new ArrayList<>();
        JSONArray jsonArray = JSON.parseArray(resultSet.getString(columnIndex));
        for (int i = 0; i < jsonArray.size(); i++) {
            result.add(jsonArray.getString(i));
        }
        return result;
    }

    @Override
    public List<String> getNullableResult(CallableStatement callableStatement, int columnIndex) throws SQLException {
        ArrayList<String> result = new ArrayList<>();
        JSONArray jsonArray = JSON.parseArray(callableStatement.getString(columnIndex));
        for (int i = 0; i < jsonArray.size(); i++) {
            result.add(jsonArray.getString(i));
        }
        return result;
    }
}
