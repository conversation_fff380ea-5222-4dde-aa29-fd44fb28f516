package com.gzairports.common.basedata.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.basedata.domain.BaseCargoDanger;
import com.gzairports.common.basedata.domain.query.CargoDangerQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 危险品管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-14
 */
@Mapper
public interface CargoDangerMapper extends BaseMapper<BaseCargoDanger> {

    /**
     * 查询危险品管理列表
     *
     * @param query 查询参数
     * @return 危险品管理列表
     */
    List<BaseCargoDanger> selectDangerList(CargoDangerQuery query);

    /**
     * 删除危险品管理
     *
     * @param ids 危险品管理id集合
     * @return 结果
     */
    int delDanger(Long[] ids);
}
