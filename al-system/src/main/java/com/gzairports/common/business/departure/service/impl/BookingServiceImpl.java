package com.gzairports.common.business.departure.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.business.departure.domain.FlightInfo;
import com.gzairports.common.utils.SerialNumberGenerator;
import com.gzairports.hz.business.departure.domain.AirWaybill;
import com.gzairports.hz.business.departure.mapper.AllAirWaybillMapper;
import com.gzairports.common.business.arrival.mapper.FlightInfoMapper;
import com.gzairports.common.business.departure.domain.Booking;
import com.gzairports.common.business.departure.domain.BookingRecord;
import com.gzairports.common.business.departure.domain.query.BookingQuery;
import com.gzairports.common.business.departure.domain.vo.BookingVo;
import com.gzairports.common.business.departure.mapper.BookingMapper;
import com.gzairports.common.business.departure.mapper.BookingRecordMapper;
import com.gzairports.common.business.departure.service.IBookingService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 订舱Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@Service
public class BookingServiceImpl extends ServiceImpl<BookingMapper, Booking> implements IBookingService {

    @Autowired
    private BookingMapper bookingMapper;

   @Autowired
   private FlightInfoMapper flightInfoMapper;

    @Autowired
    private BookingRecordMapper recordMapper;

    @Autowired
    private AllAirWaybillMapper airWaybillMapper;

    /**
     * 查询订舱列表
     * @param query 查询参数
     * @return 订舱列表
     */
    @Override
    public List<BookingVo> selectList(BookingQuery query) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
//        query.setDeptId(SecurityUtils.getHighParentId());
        List<BookingVo> vos = bookingMapper.selectListByQuery(query);
        for (BookingVo vo : vos) {
            List<FlightInfo> flights = flightInfoMapper.selectList(new QueryWrapper<FlightInfo>()
                    .eq("air_ways",vo.getFlightNo().substring(0,2))
                    .eq("flight_no", vo.getFlightNo().substring(2))
                    .eq("is_offin","D")
                    .eq("exec_date", vo.getFlightDate()));
            String date = format.format(vo.getFlightDate());
            if (!CollectionUtils.isEmpty(flights)){
                FlightInfo flight = flights.get(0);
                vo.setFlight(vo.getFlightNo() + "/" + date + flight.getStartStation() + "-" + flight.getTerminalStation());
            }else {
                vo.setFlight(vo.getFlightNo() + "/" + date);
            }
        }
        return vos;
    }

    /**
     * 新增订舱数据
     * @param booking 订舱数据
     * @return 结果
     */
    @Override
    public int add(Booking booking) {
        booking.setDeptId(SecurityUtils.getHighParentId());
        booking.setAgentCode(SecurityUtils.getUsername());
        booking.setStatus(0);
        booking.setBookingNo(SerialNumberGenerator.generateSerialNumber());
        return bookingMapper.insert(booking);
    }

    /**
     * 查看详情
     * @param id 订舱id
     * @return 结果
     */
    @Override
    public BookingVo getInfo(Long id) {
        BookingVo vo = new BookingVo();
        Booking booking = bookingMapper.selectById(id);
        AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                .eq("waybill_code", booking.getWaybillCode())
                .eq("type", "DEP"));
        if (airWaybill != null){
            vo.setCargoCode(airWaybill.getCargoCode());
        }
        BeanUtils.copyProperties(booking,vo);
        List<BookingRecord> recordList = recordMapper.selectList(new QueryWrapper<BookingRecord>()
                .eq("booking_id", id).orderByDesc("examine_time"));
        if (!CollectionUtils.isEmpty(recordList)){
            vo.setRecordList(recordList);
            if (vo.getStatus() == null || !vo.getStatus().equals(recordList.get(0).getStatus().toString())){
                vo.setStatus(recordList.get(0).getStatus().toString());
            }
        }
        return vo;
    }

    /**
     * 审核
     * @param record 审核信息
     * @return 结果
     */
    @Override
    public int bookingReview(BookingRecord record) {
        recordMapper.updateStatus(record.getBookingId(),record.getStatus());
        record.setExamineTime(new Date());
        record.setCreateBy(SecurityUtils.getUsername());
        record.setCreateTime(new Date());
        return recordMapper.insert(record);
    }

    /**
     * 批量订舱审核
     * @param record 审核信息
     * @return 结果
     */
    @Override
    public int reviewBatch(BookingRecord record) {
        for (Long bookingId : record.getBookingIds()) {
            BookingRecord bookingRecord = new BookingRecord();
            bookingRecord.setBookingId(bookingId);
            bookingRecord.setExamineTime(new Date());
            bookingRecord.setCreateBy(SecurityUtils.getUsername());
            bookingRecord.setCreateTime(new Date());
            bookingRecord.setStatus(record.getStatus());
            bookingRecord.setExamineRemark(record.getExamineRemark());
            recordMapper.insert(bookingRecord);
        }
        return recordMapper.updateStatusBatch(record.getBookingIds(),record.getStatus());
    }
}
