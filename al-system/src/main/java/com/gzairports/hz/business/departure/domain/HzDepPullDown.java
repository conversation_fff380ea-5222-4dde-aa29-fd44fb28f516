package com.gzairports.hz.business.departure.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 临时拉下表
 *
 * <AUTHOR>
 * @date 2024-07-08
 */
@Data
@TableName("hz_dep_pull_down")
public class HzDepPullDown {

    /** 组货数据id */
    private Long id;

    /** 航班配载id -拉下时的航班 */
    private Long flightId;

    /** 航班配载id -再次预配的航班 */
    private Long flightIdNew;

    /** 航班号 */
    private String flightNo;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date execDate;

    /** 运单号 */
    private String waybillCode;

    /** 配载重量 */
    private BigDecimal loadWeight;

    /** 配载件数 */
    private Integer loadQuantity;

    /** 拉下件数 */
    private Integer quantity;

    /** 拉下重量 */
    private BigDecimal weight;

    /** 板车号 */
    private String uld;

    /** 总重量 */
    private BigDecimal totalWeight;

    /** uld重量 */
    private BigDecimal uldWeight;

    /** 垫板重量 */
    private BigDecimal plateWeight;

    /** 操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operTime;

    /** 操作人 */
    private String operName;

    /** 库位 */
    private String locator;

    /** 仓库 */
    private String store;

    /** 备注 */
    private String remark;

    /** 是否配载 0 否 1 是 */
    private Integer isLoad;

    /** 是否处理 */
    private Integer isHandle;

    /** 运单件数 */
    @TableField(exist = false)
    private Integer waybillQuantity;

    /** 运单重量 */
    @TableField(exist = false)
    private BigDecimal waybillWeight;

}
