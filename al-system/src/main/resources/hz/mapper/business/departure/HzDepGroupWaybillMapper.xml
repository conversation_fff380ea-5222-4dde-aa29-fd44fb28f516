<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.departure.mapper.HzDepGroupWaybillMapper">
    <select id="selectListByFlightLoadId"
            resultType="com.gzairports.hz.business.departure.domain.HzDepGroupWaybill">
        select hdgw.id, hdgw.flight_load_id, hdgw.waybill_code, hdgw.des_port, hdgw.quantity, hdgw.weight, hdgw.type, aaw.status
        from hz_dep_group_waybill hdgw
            left join all_air_waybill aaw on hdgw.waybill_code = aaw.waybill_code
        where hdgw.flight_load_id = #{id}
    </select>
    <select id="selectOneByWaybillCode"
            resultType="com.gzairports.hz.business.departure.domain.HzDepGroupWaybill">
        select hdgw.id, hdgw.flight_load_id, hdgw.waybill_code, hdgw.des_port, hdgw.quantity, hdgw.weight, hdgw.type, aaw.status
        from hz_dep_group_waybill hdgw
                 left join all_air_waybill aaw on hdgw.waybill_code = aaw.waybill_code
        where hdgw.waybill_code = #{waybillCode}
    </select>
</mapper>