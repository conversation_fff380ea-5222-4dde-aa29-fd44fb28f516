package com.gzairports.web.controller.departure;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.wl.departure.domain.query.CancelMergeQuery;
import com.gzairports.wl.departure.domain.query.MergeHawbQuery;
import com.gzairports.wl.departure.domain.query.MergeQuery;
import com.gzairports.wl.departure.domain.vo.MergeHawbVo;
import com.gzairports.wl.departure.domain.vo.MergeMawbVo;
import com.gzairports.wl.departure.service.IMergeHawbMawbService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 主分单拼单Controller
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
@RestController
@RequestMapping("/dep/merge")
public class MergeHawbMawbController extends BaseController {

    @Autowired
    private IMergeHawbMawbService mergeService;

    /**
     * 查询主分单拼单列表
     */
//    @PreAuthorize("@ss.hasPermi('dep:merge:mawbList')")
    @PostMapping("/mawbList")
    @ApiOperation(value = "查询主单列表")
    public AjaxResult mawbList(@RequestBody MergeQuery query){
        List<MergeMawbVo> vo = mergeService.selectMawbList(query);
        return AjaxResult.success(vo);
    }

    /**
     * 根据主单id查询已拼单情况
     */
    @PreAuthorize("@ss.hasPermi('dep:merge:hawbList')")
    @GetMapping("/hawbList/{mawbId}")
    @ApiOperation(value = "查询已拼单列表")
    public AjaxResult hawbList(@PathVariable("mawbId") Long mawbId){
        List<MergeHawbVo> vo = mergeService.selectHawbList(mawbId);
        return AjaxResult.success(vo);
    }

    /**
     * 查询可拼单情况
     */
    @PreAuthorize("@ss.hasPermi('dep:merge:canList')")
    @PostMapping("/canList")
    @ApiOperation(value = "查询可拼单列表")
    public AjaxResult canList(@RequestBody CancelMergeQuery query){
        List<MergeHawbVo> vo = mergeService.selectCanList(query);
        return AjaxResult.success(vo);
    }

    /**
     * 拼单取消
     */
    @PreAuthorize("@ss.hasPermi('dep:merge:cancel')")
    @GetMapping("/cancel/{mawbId}")
    @ApiOperation(value = "拼单取消")
    public AjaxResult cancel(@PathVariable("mawbId") Long mawbId){
        return toAjax(mergeService.cancel(mawbId));
    }

    /**
     * 取消拼单
     */
    @PreAuthorize("@ss.hasPermi('dep:merge:cancelMerge')")
    @PostMapping("/cancelMerge")
    @ApiOperation(value = "取消拼单")
    public AjaxResult cancelMerge(@RequestBody CancelMergeQuery query){
        return toAjax(mergeService.cancelMerge(query));
    }

    /**
     * 拼单
     */
    @PreAuthorize("@ss.hasPermi('dep:merge:merge')")
    @PostMapping("/merge")
    @ApiOperation(value = "拼单")
    public AjaxResult merge(@RequestBody MergeHawbQuery query){
        return toAjax(mergeService.merge(query));
    }


}
