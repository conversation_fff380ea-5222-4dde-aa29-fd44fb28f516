package com.gzairports.hz.business.cargofee.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.BaseAgent;
import com.gzairports.common.basedata.mapper.BaseAgentMapper;

import com.gzairports.common.utils.SecurityUtils;


import com.gzairports.hz.business.cargofee.domain.WaybillFeeHz;
import com.gzairports.hz.business.cargofee.domain.query.WaybillFeeHzQuery;
import com.gzairports.hz.business.cargofee.domain.vo.WaybillFeeHzVo;
import com.gzairports.hz.business.cargofee.mapper.WaybillFeeHzMapper;
import com.gzairports.hz.business.cargofee.service.IWaybillFeeHzService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 运单费用明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@Service
public class WaybillFeeHzServiceImpl extends ServiceImpl<WaybillFeeHzMapper, WaybillFeeHz> implements IWaybillFeeHzService {

    @Autowired
    private WaybillFeeHzMapper feeMapper;

    @Autowired
    private BaseAgentMapper agentMapper;


    /**
     * 查询运单费用明细列表
     * @param query 查询参数
     * @return 运单费用明细列表
     */
    @Override
    public WaybillFeeHzVo selectList(WaybillFeeHzQuery query) {
        WaybillFeeHzVo vo = new WaybillFeeHzVo();
        //货站查询所有,不赋值
//        query.setDeptId(SecurityUtils.getHighParentId());
        BaseAgent agent = agentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", SecurityUtils.getHighParentId()));
        if (agent != null){
            switch (agent.getSettleMethod()){
                case 0:
                    vo.setSettleMethod("预授权支付");
                    break;
                case 1:
                    vo.setSettleMethod("余额支付");
                    break;
                case 2:
                    vo.setSettleMethod("线下结算");
                    break;
                default:
                    vo.setSettleMethod("");
                    break;
            }
            vo.setBalance(agent.getBalance());
        }
        List<WaybillFeeHz> waybillFees = feeMapper.selectListByQuery(query);
        if (CollectionUtils.isEmpty(waybillFees)){
            return vo;
        }
        for (WaybillFeeHz waybillFee : waybillFees) {
            switch (waybillFee.getStatus()){
                case 0:
                    waybillFee.setStrStart("预授权");
                    break;
                case 1:
                    waybillFee.setStrStart("已结算");
                    break;
                case 2:
                    waybillFee.setStrStart("结算失败");
                    break;
                default:
                    waybillFee.setStrStart("");
                    break;
            }
        }
        vo.setList(waybillFees);
        BigDecimal totalPay = waybillFees.stream().map(WaybillFeeHz::getPayMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        vo.setTotalPay(totalPay);

        BigDecimal totalSettle = waybillFees.stream().map(WaybillFeeHz::getSettleMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        vo.setTotalSettle(totalSettle);

        BigDecimal totalRefund = waybillFees.stream().map(WaybillFeeHz::getRefund).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        vo.setTotalRefund(totalRefund);
        return vo;
    }

}
