package com.gzairports.wl.charge.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 收费项目表
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@Data
@TableName("wl_sf_item")
public class ChargeItem {

    /** 主键id */
    private Long id;

    /** 名称 */
    private String name;

    /** 航司code */
    private String code;

    /** 进出港类型 */
    private String operationType;

    /** 适用单证类型 */
    private String applicableDocuments;

    /** 用途 */
    private String purpose;

    /** 结算收款对象 */
    private String settlementPayee;

    /** 简称 */
    private String abbreviation;

    /** 是否默认收费 */
    private Integer defaultChargeable;

    /** 默认计费方式 */
    private Integer defaultBillingMethod;

    /** 默认费用/费率 */
    private BigDecimal defaultCostRate;

    /** 四舍五入规则 */
    private Integer roundRule;

    /** 是否可编辑 */
    private Integer editable;

    /** 备注 */
    private String remarks;

    /** 所属单位 */
    private Long deptId;

    /** 条目是否逻辑删除 */
    @TableLogic
    private Integer isDel;

    /** 更新时间 */
    private Date updateTime;

    /** 更新人 */
    private String updateBy;

    /** 创建时间 */
    private Date createTime;

    /** 创建人 */
    private String createBy;
}
