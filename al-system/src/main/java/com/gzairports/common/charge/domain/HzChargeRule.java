package com.gzairports.common.charge.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

/**
 * 计费规则表
 *
 * <AUTHOR>
 * @date 2024-06-13
 */
@Data
@TableName("hz_charge_rule")
public class HzChargeRule {

    /**
     * 主键id
     */
    @Excel(name = "主键id", prompt = "计费规则编号")
    private Long id;

    /**
     * 规则名称
     */
    @Excel(name = "规则名称")
    private String ruleName;

    /**
     * 描述
     */
    @Excel(name = "描述")
    private String remark;

    /**
     * 执行类
     */
    @Excel(name = "执行类")
    private String className;

    /**
     * 是否删除 0 否 1 是
     */
    private Integer isDel;
}
