package com.gzairports.hz.business.cable.domain.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-07
 */
@Data
public class FSUJsonVO {

    /** 启运港 */
    private String depAirport;

    /** 目的港 */
    private String desAirport;

    private String mawbId;

    /** 主运单号 */
    private String mawbNo;

    /** 装载件数 */
    private String pieces;

    /** 运输描述代码
     分割托运 D
     多批托运 M
     部分托运 P
     拆分托运 S
     总托运 T
     */
    private String shipmentDescriptionCode;

    /** 总件数 */
    private String totalPieces;

    /** 总重量 */
    private String totalWeight;

    /** 重量 */
    private String weight;

    /** 重量单位 */
    private String weightUnit;

    /** 操作时间 */
    private Date operTime;

    /** 状态 */
    private List<StatusDetails> statusDetails;
}
