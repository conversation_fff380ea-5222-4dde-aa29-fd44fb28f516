package com.gzairports.wl.departure.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.gzairports.wl.departure.domain.HawbItem;
import com.gzairports.wl.departure.domain.MawbItem;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 自动运价返回参数
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@Data
public class FareVo {

    /** 分单默认收费项目列表 */
    private List<HawbItem> hawbItems;

    /** 主单默认收费项目列表 */
    private List<MawbItem> mawbItems;

    /** 单个分单收费项目 */
    private HawbItem hawbItem;

    /** 单个主单收费项目 */
    private MawbItem mawbItem;

    /** 票面费率 */
    private BigDecimal rate;

    /** 运价条目id */
    private Long itemId;

    /** 费用总计 */
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal costSum;

    /** 结算费率/公斤 */
    private BigDecimal wRate;

    /** 结算费用总额 */
    private BigDecimal wCostSum;

    /** 应收费率/公斤 */
    private BigDecimal rRate;

    /** 应收费用总额 */
    private BigDecimal rCostSum;

    /** 计费重量 */
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal chargeWeight;

    private String rateType;
}
