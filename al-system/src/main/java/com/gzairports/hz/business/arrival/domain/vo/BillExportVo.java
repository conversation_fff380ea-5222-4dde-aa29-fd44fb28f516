package com.gzairports.hz.business.arrival.domain.vo;

import com.gzairports.common.annotation.Excel;
import com.gzairports.common.business.arrival.domain.vo.OutOrderWaybillVo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
public class BillExportVo {

    /** 办单id */
    private Long id;

    /** 序号 */
    @Excel(name = "序号")
    private Integer idx;

    /** 流水号 */
    @Excel(name = "流水号")
    private String serialNo;

    /** 发票号码 */
    @Excel(name = "发票号码")
    private String invoiceNum;

    /** 总费用 */
    @Excel(name = "总费用", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal totalCost;

    /** 总件数 */
    @Excel(name = "总件数", cellType = Excel.ColumnType.NUMERIC)
    private Integer totalQuantity;

    /** 总重量 */
    @Excel(name = "总重量", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal totalWeight;

    /** 总计费重量 */
    @Excel(name = "总计费重量", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal totalChargeWeight;

    /** 结算方式（0 现金 1月结 2余额 3预授权） */
    @Excel(name = "结算方式"/*, readConverterExp = "0=现金,1=月结,2=余额,3=预授权"*/)
    private String payMethod;

    /** 登记营业点 */
    @Excel(name = "登记营业点",defaultValue = "GNJG")
    private String registerPoint;

    /** 计费人 */
    @Excel(name = "计费人")
    private String handleBy;

    /** 计费时间 */
    @Excel(name = "计费时间",dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date pickUpTime;

    /** 结算客户 */
    @Excel(name = "结算客户")
    private String settleUser;

    /** 结算时间 */
    @Excel(name = "结算时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date settleTime;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 运单对象 */
    @Excel(name = "")
    private List<OutOrderWaybillVo> orderWaybillVos;
}
