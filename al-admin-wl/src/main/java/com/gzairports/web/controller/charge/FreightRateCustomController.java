package com.gzairports.web.controller.charge;

import com.gzairports.wl.charge.domain.FreightRateCustom;
import com.gzairports.wl.charge.domain.FreightRateCustomItem;
import com.gzairports.wl.charge.domain.query.FreightRateCustomQuery;
import com.gzairports.wl.charge.domain.vo.FreightRateCustomVO;
import com.gzairports.wl.charge.domain.vo.FreightRateItemVO;
import com.gzairports.wl.charge.service.FreightRateCustomService;
import com.gzairports.common.annotation.OperLog;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.wl.log.service.WlOperLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 客户运价Controller
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@RestController
@RequestMapping("/freightCustom")
@Api(tags = "客户运价接口")
public class FreightRateCustomController extends BaseController {
    
    @Autowired
    private FreightRateCustomService customService;

    @Autowired
    private WlOperLogService logService;

    /**
     * 查询客户运价列表
     */
//    @PreAuthorize("@ss.hasPermi('charge:freightCustom:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询客户运价列表")
    public TableDataInfo list(FreightRateCustomQuery query){
        startPage();
        List<FreightRateCustomVO> list = customService.selectList(query);
        return getDataTable(list);
    }

    /**
     * 导出客户运价列表
     */
    @PreAuthorize("@ss.hasPermi('charge:freightCustom:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出客户运价")
    public void export(HttpServletResponse response, FreightRateCustomQuery query){
        List<FreightRateCustomVO> list = customService.selectList(query);
        ExcelUtil<FreightRateCustomVO> util = new ExcelUtil<FreightRateCustomVO>(FreightRateCustomVO.class);
        util.exportExcel(response, list, "客户运价");
    }

    /**
     * 根据选中id导出客户运价列表
     */
    @PreAuthorize("@ss.hasPermi('charge:freightCustom:export')")
    @PostMapping("/exportByIds")
    @ApiOperation(value = "根据id导出客户运价")
    public void export(HttpServletResponse response,@RequestParam("ids") List<Long> ids){
        List<FreightRateCustomVO> list = customService.selectListByIds(ids);
        ExcelUtil<FreightRateCustomVO> util = new ExcelUtil<FreightRateCustomVO>(FreightRateCustomVO.class);
        util.exportExcel(response, list, "客户运价");
    }

    /**
     * 获取客户运价详细信息
     */
    @PreAuthorize("@ss.hasPermi('charge:freightCustom:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取客户运价详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(customService.selectRateById(id));
    }

    /**
     * 新增客户运价
     */
    @PreAuthorize("@ss.hasPermi('charge:freightCustom:add')")
    @OperLog(title = "客户运价", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ApiOperation(value = "新增客户运价")
    public AjaxResult add(@RequestBody FreightRateCustomVO vo)
    {
        return success(customService.insertFreightRateCustom(vo));
    }

    /**
     * 修改客户运价
     */
    @PreAuthorize("@ss.hasPermi('charge:freightCustom:edit')")
    @OperLog(title = "客户运价", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ApiOperation(value = "修改客户运价")
    public AjaxResult edit(@RequestBody FreightRateCustomVO vo)
    {
        return toAjax(customService.updateFreightRateCustom(vo));
    }
    /**
     * 导入客户运价条目
     */
//    @PreAuthorize("@ss.hasPermi('charge:freightCustom:importItem')")
    @OperLog(title = "客户运价", businessType = BusinessType.IMPORT)
    @PostMapping("/importItem")
    @ApiOperation(value = "导入客户运价条目")
    public AjaxResult importItem(@RequestParam("file") MultipartFile file, @RequestParam("rateId") Long rateId, @RequestParam("updateSupport") boolean updateSupport) throws Exception
    {
        if (file == null){
            return warn("请上传文件");
        }
        ExcelUtil<FreightRateCustomItem> util = new ExcelUtil<FreightRateCustomItem>(FreightRateCustomItem.class);
        List<FreightRateCustomItem> airportRateItems = util.importRateExcel(file.getInputStream(),1);
        Long message = customService.importItem(airportRateItems,rateId, updateSupport);
        return success(message);
    }

    /**
     * 查看运价条目
     */
    @PreAuthorize("@ss.hasPermi('charge:freightCustom:rateItemList')")
    @GetMapping("/rateItemList/{id}")
    @ApiOperation(value = "查看运价条目")
    public TableDataInfo rateItemList(@PathVariable Long id)
    {
        startPage();
        List<FreightRateItemVO> freightRateItemVOS = customService.selectRateItemList(id);
        return getDataTable(freightRateItemVOS);
    }

    /**
     * 删除客户运价
     */
    @PreAuthorize("@ss.hasPermi('charge:freightCustom:del')")
    @OperLog(title = "客户运价", businessType = BusinessType.DELETE)
    @GetMapping("/del/{id}")
    @ApiOperation(value = "删除客户运价")
    public AjaxResult del(@PathVariable Long id)
    {
        return toAjax(customService.deleteFreightRateCustom(id));
    }

    /**
     * 删除客户与航司运价
     */
    @PreAuthorize("@ss.hasPermi('charge:freightCustom:delCustomAir')")
    @OperLog(title = "客户运价", businessType = BusinessType.DELETE)
    @GetMapping("/delCustomAir/{id}")
    @ApiOperation(value = "删除客户与航司运价")
    public AjaxResult delCustomAir(@PathVariable Long id)
    {
        return toAjax(customService.delCustomAir(id));
    }


    /**
     * 客户运价操作日志查询
     */
    @PreAuthorize("@ss.hasPermi('charge:freightCustom:logList')")
    @GetMapping("/logList/{id}")
    @ApiOperation(value = "客户运价操作日志查询")
    public AjaxResult logList(@PathVariable Long id)
    {
        return success(logService.log("客户运价",id));
    }

    /**
     * 客户运价操作日志详情
     */
    @PreAuthorize("@ss.hasPermi('charge:freightCustom:logInfo')")
    @GetMapping("/logInfo/{operId}")
    @ApiOperation(value = "客户运价操作日志详情")
    public AjaxResult logInfo(@PathVariable Long operId)
    {
        return success(logService.logInfo(operId));
    }
}
