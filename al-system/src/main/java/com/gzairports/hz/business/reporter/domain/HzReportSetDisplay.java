package com.gzairports.hz.business.reporter.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.List;

/**
 * @author: lan
 * @Desc: 货站报表设置显示字段设置表
 * @create: 2025-03-05 17:00
 **/

@Data
@TableName("hz_report_set_display")
public class HzReportSetDisplay {
    /** 主键id */
    private Long id;

    /** 报表设置id */
    private Long setId;

    /** 字段中文 */
    private String fieldNameCn;
    
    /** 字段 */
    private String fieldName;

    /** 字段显示顺序 */
    private Integer fieldIndex;

    /** 是否排序 0否 1是 */
    private Integer fieldSort;

    /** 排序顺序 升序降序 */
    private String fieldSortType;

    /** 相同字段是否合并显示 0否 1是 */
    private Integer fieldSortMerge;

    /** 是否显示小计 0否 1是 */
    private Integer fieldSortSubtotal;

    /** 计算公式 为空不计算 */
    private String designFormulas;

    /** 报表设置主表从表字段id */
    private Long fieldId;

    /** 字段来源 0 主表 1 子表1 2 子表2*/
    private Integer tableField;

    /** 是否只显示小计 */
    private Integer isSubtotal;

    /** 字段类型 */
    @TableField(exist = false)
    private String fieldType;

    /** 小计 */
    @TableField(exist = false)
    private List<HzReportSetDisplaySubtotal> hzReportSetDisplaySubtotal;

}
