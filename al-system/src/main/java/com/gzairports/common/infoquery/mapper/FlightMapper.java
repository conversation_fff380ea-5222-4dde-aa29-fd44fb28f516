package com.gzairports.common.infoquery.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gzairports.common.infoquery.domain.Flight;
import com.gzairports.common.infoquery.domain.query.AirlineWaybillQuery;
import com.gzairports.common.infoquery.domain.query.FlightQuery;
import com.gzairports.common.infoquery.domain.vo.AirlineWaybillInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 航班表 数据层
 *
 * <AUTHOR>
 */
@Mapper
public interface FlightMapper extends BaseMapper<Flight>{

    /**
     * 查询航班列表
     *
     * @param query 航班查询信息
     * @return 航班集合
     */
    List<Flight> selectFlightList(FlightQuery query);

    List<Flight> selectFlightBookList(FlightQuery query);

    IPage<AirlineWaybillInfoVO> selectACWList(@Param("query") AirlineWaybillQuery query, @Param("page") IPage<AirlineWaybillInfoVO> page);

    Flight selectByWaybillId(@Param("waybillId") Long waybillId);
}
