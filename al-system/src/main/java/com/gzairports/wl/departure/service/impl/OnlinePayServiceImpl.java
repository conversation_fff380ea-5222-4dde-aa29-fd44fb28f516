package com.gzairports.wl.departure.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzairports.common.basedata.domain.BaseAgent;
import com.gzairports.common.basedata.domain.BaseBalance;
import com.gzairports.common.basedata.mapper.BaseAgentMapper;
import com.gzairports.common.basedata.mapper.BaseBalanceMapper;
import com.gzairports.common.business.arrival.domain.FlightLoadUldWaybill;
import com.gzairports.common.business.arrival.domain.FlightLoadWaybill;
import com.gzairports.common.business.arrival.mapper.FlightInfoMapper;
import com.gzairports.common.business.arrival.mapper.FlightLoadUldWaybillMapper;
import com.gzairports.common.business.arrival.mapper.FlightLoadWaybillMapper;
import com.gzairports.common.business.departure.domain.*;
import com.gzairports.common.business.departure.mapper.CostDetailMapper;
import com.gzairports.common.business.departure.mapper.MawbMapper;
import com.gzairports.common.business.departure.mapper.WaybillFeeMapper;
import com.gzairports.common.business.departure.service.impl.WaybillTraceServiceImpl;
import com.gzairports.common.core.domain.entity.SysDept;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.common.log.service.IWaybillLogService;
import com.gzairports.common.serviceRequest.domain.ServiceRequest;
import com.gzairports.common.serviceRequest.mapper.ServiceRequestMapper;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.ServletUtils;
import com.gzairports.hz.business.departure.domain.FlightLoad;
import com.gzairports.hz.business.departure.domain.FlightLoadUld;
import com.gzairports.hz.business.departure.domain.HzCollectWaybill;
import com.gzairports.hz.business.departure.domain.HzCollectWeight;
import com.gzairports.hz.business.departure.domain.vo.SettleDetailVo;
import com.gzairports.hz.business.departure.mapper.FlightLoadMapper;
import com.gzairports.hz.business.departure.mapper.FlightLoadUldMapper;
import com.gzairports.hz.business.departure.mapper.HzCollectWaybillMapper;
import com.gzairports.hz.business.departure.mapper.HzCollectWeightMapper;
import com.gzairports.wl.departure.domain.query.OnlinePayQuery;
import com.gzairports.wl.departure.domain.vo.OnlineInfoVo;
import com.gzairports.wl.departure.domain.vo.OnlineMawbVo;
import com.gzairports.wl.departure.domain.vo.OnlinePayVo;
import com.gzairports.wl.departure.service.IOnlinePayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 货站在线缴费Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Service
public class OnlinePayServiceImpl implements IOnlinePayService {

    @Autowired
    private MawbMapper mawbMapper;

    @Autowired
    private WaybillFeeMapper feeMapper;

    @Autowired
    private CostDetailMapper costDetailMapper;

    @Autowired
    private FlightInfoMapper flightInfoMapper;

    @Autowired
    private FlightLoadWaybillMapper loadWaybillMapper;

    @Autowired
    private FlightLoadUldWaybillMapper loadUldWaybillMapper;

    @Autowired
    private FlightLoadUldMapper loadUldMapper;

    @Autowired
    private FlightLoadMapper loadMapper;

    @Autowired
    private BaseAgentMapper baseAgentMapper;

    @Autowired
    private BaseBalanceMapper baseBalanceMapper;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    @Autowired
    private IWaybillLogService waybillLogService;

    @Autowired
    private ServiceRequestMapper serviceRequestMapper;

    @Autowired
    private HzCollectWaybillMapper collectWaybillMapper;

    @Autowired
    private HzCollectWeightMapper collectWeightMapper;

    @Autowired
    private WaybillTraceServiceImpl waybillTraceService;



    /**
     * 根据条件查询货站在线缴费列表
     * @param query 查询条件
     * @return 货站在线缴费列表
     */
    @Override
    public OnlinePayVo selectList(OnlinePayQuery query) {
        OnlinePayVo vo = new OnlinePayVo();
        query.setDeptId(SecurityUtils.getHighParentId());
        Page<OnlineMawbVo> page = new Page<>(1, -1);
        Integer totalOrder = mawbMapper.selectPayCount(page,query);
        List<String> unPayOrder = new ArrayList<>();
        List<String> payOrder  = new ArrayList<>();
        List<String> settleOrder  = new ArrayList<>();
        if (query.getPayStatus() != null){
            if (query.getPayStatus() == 0){
                unPayOrder = mawbMapper.selectByStatus(query, query.getPayStatus());
            }
            if (query.getPayStatus() == 4){
                payOrder = mawbMapper.selectByStatus(query, query.getPayStatus());
            }
            if (query.getPayStatus() == 8){
                settleOrder = mawbMapper.selectByStatus(query, query.getPayStatus());
            }
            if (query.getPayStatus() == 9){
                BigDecimal refund = mawbMapper.selectRefund(page,query);
                vo.setRefund(refund);
            }
        }else {
            unPayOrder = mawbMapper.selectByStatus(query, 0);
            payOrder = mawbMapper.selectByStatus(query, 4);
            settleOrder = mawbMapper.selectByStatus(query, 8);
            BigDecimal refund = mawbMapper.selectRefund(page,query);
            vo.setRefund(refund);
        }
        // 未支付费用
        BigDecimal unPayCostSum = BigDecimal.ZERO;
        for (String s : unPayOrder) {
            List<CostDetail> details = costDetailMapper.selectPayOrSettleList(s,0,0, SecurityUtils.getHighParentId());
            if (!CollectionUtils.isEmpty(details)){
                BigDecimal costSum = details.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                unPayCostSum = unPayCostSum.add(costSum);
            }
        }
        vo.setUnPayOrder(unPayOrder.size());
        vo.setUnPay(unPayCostSum);
        // 已支付
        BigDecimal payCostSum = BigDecimal.ZERO;
        for (String s : payOrder) {
            List<CostDetail> details = costDetailMapper.selectList(new QueryWrapper<CostDetail>()
                    .eq("waybill_code",s)
                    .eq("dept_id",SecurityUtils.getHighParentId())
                    .eq("is_del",0)
                    .eq("type",0)
                    .eq("is_settle",1));
            if (!CollectionUtils.isEmpty(details)){
                BigDecimal costSum = details.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                payCostSum = payCostSum.add(costSum);
            }
        }
        vo.setPayOrder(payOrder.size());
        vo.setPay(payCostSum);
        // 结算
        BigDecimal settleCostSum = BigDecimal.ZERO;
        for (String s : settleOrder) {
            List<CostDetail> details = costDetailMapper.selectList(new QueryWrapper<CostDetail>()
                    .eq("waybill_code",s)
                    .eq("dept_id",SecurityUtils.getHighParentId())
                    .eq("is_del",0).eq("type",1));
            if (!CollectionUtils.isEmpty(details)){
                BigDecimal costSum = details.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                settleCostSum = settleCostSum.add(costSum);
            }
        }
        vo.setSettle(settleCostSum);
        vo.setSettleOrder(settleOrder.size());
        vo.setTotalOrder(totalOrder);

        Page<OnlineMawbVo> pageOne = new Page<OnlineMawbVo>(query.getPageNum(),query.getPageSize());
        Page<OnlineMawbVo> onlineMawbVos = mawbMapper.selectPay(query,pageOne);
        if (CollectionUtils.isEmpty(onlineMawbVos.getRecords())){
            return vo;
        }
        for (OnlineMawbVo onlineMawbVo : onlineMawbVos.getRecords()) {
            List<CostDetail> details = costDetailMapper.selectList(new QueryWrapper<CostDetail>()
                    .eq("waybill_code",onlineMawbVo.getWaybillCode())
                    .eq("dept_id",SecurityUtils.getHighParentId()).eq("is_del",0));
            if (!CollectionUtils.isEmpty(details)){
                if (onlineMawbVo.getPayStatus() == 0){
                    List<CostDetail> collect = details.stream().filter(e -> e.getType() == 0 && e.getIsSettle() == 0).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(collect)){
                        BigDecimal costSum = collect.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                        onlineMawbVo.setCostSum(costSum);
                    }
                }
                if (onlineMawbVo.getPayStatus() > 0 && onlineMawbVo.getPayStatus() <= 4){
                    List<CostDetail> collect = details.stream().filter(e -> e.getType() == 0 && e.getIsSettle() == 1).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(collect)){
                        BigDecimal costSum = collect.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                        onlineMawbVo.setCostSum(costSum);
                    }
                }
                if (onlineMawbVo.getPayStatus() > 4){
                    List<CostDetail> collect = details.stream().filter(e -> e.getType() == 1).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(collect)){
                        BigDecimal costSum = collect.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                        onlineMawbVo.setCostSum(costSum);
                    }
                }
            }else {
                onlineMawbVo.setCostSum(new BigDecimal(0));
            }
        }
        vo.setOnlineMawbVos(onlineMawbVos);
        return vo;
    }

    /**
     * 货站在线缴费详情
     * @param id 运单id
     * @return 货站在线缴费详情
     */
    @Override
    public OnlineInfoVo getInfo(Long id) {
        OnlineInfoVo infoVo = mawbMapper.selectInfo(id);
        if (infoVo == null){
            throw new CustomException("无当前运单信息");
        }
        List<Long> flightInfoIdList = new ArrayList<>();
        List<FlightLoadUldWaybill> loadUldWaybillList = loadUldWaybillMapper.selectList(new QueryWrapper<FlightLoadUldWaybill>().eq("waybill_id", id));
        if(loadUldWaybillList.size() > 0){
            for (FlightLoadUldWaybill flightLoadUldWaybill : loadUldWaybillList){
                FlightLoadUld flightLoadUld = loadUldMapper.selectById(flightLoadUldWaybill.getLoadUldId());
                if(flightLoadUld!=null){
                    FlightLoad flightLoad = loadMapper.selectById(flightLoadUld.getFlightLoadId());
                    if (flightLoad!=null){
                        flightInfoIdList.add(flightLoad.getFlightId());
                    }
                }
            }
        }
        List<FlightLoadWaybill> loadWaybillList = loadWaybillMapper.selectList(new QueryWrapper<FlightLoadWaybill>().eq("waybill_id", id));
        if (loadWaybillList.size() > 0){
            for (FlightLoadWaybill flightLoadWaybill:loadWaybillList){
                FlightLoadUld flightLoadUld = loadUldMapper.selectById(flightLoadWaybill.getFlightLoadId());
                if(flightLoadUld!=null){
                    FlightLoad flightLoad = loadMapper.selectById(flightLoadUld.getFlightLoadId());
                    if (flightLoad!=null){
                        flightInfoIdList.add(flightLoad.getFlightId());
                    }
                }
            }
        }
        int depQuantity = 0;
        BigDecimal depWeight = new BigDecimal(0);
        if (!CollectionUtils.isEmpty(flightInfoIdList)){
            List<Long> collect = flightInfoIdList.stream().distinct().collect(Collectors.toList());
            for(Long flightId :collect){
                FlightInfo flightInfo = flightInfoMapper.selectById(flightId);
                if(flightInfo!=null){
                    if("D".equals(flightInfo.getIsOffin()) && flightInfo.getStartRealTakeoffTime()!=null){
                        // 航班起飞才会有出港件数和运单
                        List<FlightLoadUldWaybill> loadUldWaybills = loadUldWaybillMapper.selectList(new QueryWrapper<FlightLoadUldWaybill>().eq("waybill_id", id));
                        if (!CollectionUtils.isEmpty(loadUldWaybills)) {
                            int quantity = loadUldWaybills.stream().mapToInt(FlightLoadUldWaybill::getQuantity).sum();
                            depQuantity = depQuantity + quantity;
                            BigDecimal weight = loadUldWaybills.stream().map(FlightLoadUldWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                            depWeight = depWeight.add(weight);
                        }
                        List<FlightLoadWaybill> loadWaybills = loadWaybillMapper.selectList(new QueryWrapper<FlightLoadWaybill>().eq("waybill_id", id));
                        if (!CollectionUtils.isEmpty(loadWaybills)) {
                            int quantity = loadWaybills.stream().mapToInt(FlightLoadWaybill::getQuantity).sum();
                            depQuantity = depQuantity + quantity;
                            BigDecimal weight = loadWaybills.stream().map(FlightLoadWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                            depWeight = depWeight.add(weight);
                        }
                    }
                }
            }
        }
        infoVo.setDepQuantity(depQuantity);
        infoVo.setDepWeight(depWeight);
        //将传过来的waybill_id转为waybill_code
        String waybillCode = infoVo.getWaybillCode();
        List<CostDetail> payList = costDetailMapper.selectPayOrSettleList(waybillCode,0,3,SecurityUtils.getHighParentId());
        infoVo.setPayList(payList);

        List<SettleDetailVo> voList = new ArrayList<>();
        List<CostDetail> settleList = costDetailMapper.selectPayOrSettleList(waybillCode,1,1, SecurityUtils.getDeptId());
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (!CollectionUtils.isEmpty(settleList)){
            Map<String, List<CostDetail>> collect = settleList.stream().collect(Collectors.groupingBy(e-> {
                Date createTime = e.getCreateTime();
                String timePart = (createTime != null) ? format.format(createTime) : " ";
                return e.getFlightId() + "," + timePart;
            }));
            for (Map.Entry<String, List<CostDetail>> longListEntry : collect.entrySet()) {
                String[] split = longListEntry.getKey().split(",");
                SettleDetailVo settleDetailVo = new SettleDetailVo();
                if ("1".equals(split[0])){
                    settleDetailVo.setFlightNo("退货");
                }else if ("4".equals(split[0])) {
                    settleDetailVo.setFlightNo("退款");
                }else if ("3".equals(split[0])) {
                    settleDetailVo.setFlightNo("作废退款");
                }else if ("1111".equals(split[0])) {
                    settleDetailVo.setFlightNo("3个月未处理自动收取");
                }else {
                    FlightInfo info = flightInfoMapper.selectById(split[0]);
                    if (info != null){
                        settleDetailVo.setFlightNo(info.getAirWays() + info.getFlightNo());
                        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                        settleDetailVo.setFlightDate(dateFormat.format(info.getExecDate()));
                    }
                }
                BigDecimal reduce = longListEntry.getValue().stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                settleDetailVo.setSettleTotal(reduce);
                try {
                    if (!" ".equals(split[1])){
                        settleDetailVo.setCreateTime(format.parse(split[1]));
                    }
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
                settleDetailVo.setSettleList(longListEntry.getValue());
                voList.add(settleDetailVo);
            }
        }
        if (!CollectionUtils.isEmpty(voList)){
            List<SettleDetailVo> collect = voList.stream().sorted(Comparator.comparing(SettleDetailVo::getCreateTime)).collect(Collectors.toList());
            infoVo.setSettleList(collect);
        }
        List<CostDetail> waitSettleList = costDetailMapper.selectPayOrSettleList(waybillCode,1,0,SecurityUtils.getHighParentId());
        infoVo.setWaitPayList(waitSettleList);
        return infoVo;
    }

    /**
     * 支付
     * @param id 运单id
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized int payment(Long id) {
        Mawb mawb = mawbMapper.selectById(id);
        if (mawb == null){
            throw new CustomException("无当前运单信息");
        }
        Mawb updatedWaybill = new Mawb();
        updatedWaybill.setId(id);
        updatedWaybill.setVersion(mawb.getVersion());
        updatedWaybill.setWaybillCode(mawb.getWaybillCode());
        if("staging".equals(mawb.getStatus())){
            throw new CustomException("运单须保存并发送后才能支付");
        }
        Integer count = costDetailMapper.selectCount(new QueryWrapper<CostDetail>()
                .eq("waybill_code",mawb.getWaybillCode())
                .eq("is_settle",2)
                .eq("dept_id",SecurityUtils.getHighParentId()));
        if (count > 0){
            throw new CustomException("取消支付的运单须保存并发送后才能支付");
        }
        List<CostDetail> costDetailsList = costDetailMapper.selectList(new QueryWrapper<CostDetail>()
                .eq("waybill_code", mawb.getWaybillCode())
                .eq("dept_id", SecurityUtils.getHighParentId()));
        if(costDetailsList.size() == 0){
            throw new CustomException("该运单无支付项目，请核对后再重新支付");
        }
        Mawb mawbTransfer = null;
        //查询原单是否为进港中转
        if(mawb.getOriginBill()!=null){
           mawbTransfer = mawbMapper.selectOne(new QueryWrapper<Mawb>()
                    .eq("waybill_code", mawb.getOriginBill())
                    .eq("type", "DEP")
                    .eq("transfer_bill", 1));
        }
        //进港中转（已在文件交接完成时自动收运） + 换单中转(原单号为中转运单的换单)
        if (mawb.getSwitchBill() == 1 && mawbTransfer != null){
            HzCollectWaybill waybill = new HzCollectWaybill();
            // 新增收运数据
            waybill.setQuantity(mawb.getQuantity());
            waybill.setWeight(mawb.getWeight());
            waybill.setWaybillId(id);
            waybill.setWaybillCode(mawb.getWaybillCode());
            waybill.setCollectTime(new Date());
//            waybill.setOperName(SecurityUtils.getUsername());
            waybill.setOperName("系统");
            waybill.setCollectTime(new Date());
            waybill.setIsReal(1);
            waybill.setStatus("REAL");
            mawb.setCollectStatus(3);
            collectWaybillMapper.insert(waybill);
            HzCollectWeight weight = new HzCollectWeight();
            weight.setCollectId(waybill.getId());
            weight.setWeightTime(new Date());
            weight.setDesPort(mawb.getDesPort());
            weight.setTotalWeight(waybill.getWeight());
            weight.setQuantity(waybill.getQuantity());
            weight.setWeight(waybill.getWeight());
            collectWeightMapper.insert(weight);
            // 运单跟踪数据
            WaybillTrace waybillTrace = new WaybillTrace();
            waybillTrace.setOperTime(new Date());
            waybillTrace.setOperPieces(mawb.getQuantity());
            waybillTrace.setOperWeight(mawb.getWeight());
            waybillTrace.setWaybillCode(mawb.getWaybillCode());
            waybillTrace.setNodeName("货站入库");
            waybillTrace.setChargeWeight(mawb.getChargeWeight());
            waybillTraceService.insertWaybillTrace(waybillTrace);
            updatedWaybill.setStatus("put_in");
        }
        //运单日志的新增
        HttpServletResponse response = ServletUtils.getResponse();
        WaybillLog waybillLog = waybillLogService.getWaybillLog(
                mawb.getWaybillCode(), 0, SecurityUtils.getNickName(),
                mawb.getWeight() != null ? mawb.getWeight().toString() : null,
                mawb.getQuantity() != null ? mawb.getQuantity().toString() : null,
                mawb.getFlightNo1(), id.toString(), null, 0, null, new Date(),
                null, mawb.getType(), null);
        try{
            List<SysDept> list = sysDeptMapper.selectList();
            if (!mawb.getDeptId().equals(SecurityUtils.getHighParentId())){
                throw new CustomException("运单" + mawb.getWaybillCode() + "代理公司有误");
            }
            SysDept dept = findDeptById(SecurityUtils.getHighParentId(), list);
            Long parentId = getParentId(dept, list);
            BaseAgent agent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", parentId));
            BigDecimal costSum = new BigDecimal(0);
            //这里的isSettle要改成1 因为制好单就要看到预授权支付明细 在制单的时候就赋值为1->又改成0了
            List<CostDetail> details = costDetailMapper.selectPayOrSettleList(mawb.getWaybillCode(),0,0, SecurityUtils.getHighParentId());
            if (!CollectionUtils.isEmpty(details)){
                costSum = details.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                for (CostDetail detail : details) {
                    detail.setIsSettle(1);
                    costDetailMapper.updateById(detail);
                }
            }
            waybillLog.setRemark("预授权支付，金额:" + costSum);
            WaybillFee waybillFee = feeMapper.selectOne(new QueryWrapper<WaybillFee>()
                    .eq("waybill_code", mawb.getWaybillCode())
                    .eq("dept_id", mawb.getDeptId())
                    .eq("type","DEP"));
            if (agent != null){
                if (agent.getSettleMethod() == 1) {
                    BigDecimal balance = agent.getBalance() == null ? new BigDecimal(0) : agent.getBalance();
                    BigDecimal subtract = balance.subtract(costSum);
                    if (subtract.compareTo(new BigDecimal(0)) < 0) {
                        throw new CustomException("当前余额不足");
                    } else {
                        agent.setBalance(subtract);
                        baseAgentMapper.updateBaseAgent(agent);
                        BaseBalance baseBalance = new BaseBalance();
                        baseBalance.setAgentId(agent.getId());
                        baseBalance.setBalance(agent.getBalance());
                        baseBalance.setType("减少余额");
                        baseBalance.setCreateTime(new Date());
                        baseBalance.setCreateBy(SecurityUtils.getNickName());
                        // todo 流水号需从银联支付接口获取
                        //baseBalance.setSerialNo();
                        baseBalance.setTradeMoney(costSum);
                        baseBalance.setWaybillCode(mawb.getWaybillCode());
                        baseBalance.setRemark("运单支付");
                        baseBalanceMapper.insertBaseBalance(baseBalance);
                        updateStatus(updatedWaybill, costSum, waybillFee, parentId, 2);
                    }
                } else if (agent.getSettleMethod() == 0){
                    updateStatus(updatedWaybill, costSum, waybillFee, parentId, 1);
                }else {
                    if (agent.getPayMethod() == 0){
                        updateStatus(updatedWaybill, costSum, waybillFee, parentId, 4);
                    }else {
                        updateStatus(updatedWaybill, costSum, waybillFee, parentId, 3);
                    }
                }
            }else {
                updateStatus(updatedWaybill, costSum, waybillFee, parentId, 3);
            }
            WaybillTrace waybillTrace = new WaybillTrace();
            waybillTrace.setOperTime(new Date());
            waybillTrace.setOperPieces(mawb.getQuantity());
            waybillTrace.setOperWeight(mawb.getWeight());
            waybillTrace.setPayMoney(costSum);
            waybillTrace.setWaybillCode(mawb.getWaybillCode());
            waybillTrace.setNodeName("预授权支付");
            waybillTrace.setChargeWeight(mawb.getChargeWeight());
            waybillTraceService.insertWaybillTrace(waybillTrace);
            //同步修改服务申请/审核的预授权支付时间和金额
            ServiceRequest serviceRequest = serviceRequestMapper.selectOne(new QueryWrapper<ServiceRequest>()
                    .eq("waybill_code", mawb.getWaybillCode())
                    .like("service_item", mawb.getColdStore()));
                if (serviceRequest != null) {
                    if ("UNAUDITED".equals(serviceRequest.getStatus())) {
                        throw new CustomException("该运单冷库服务申请未审批");
                    }
                    if ("NOPASS".equals(serviceRequest.getStatus())) {
                        throw new CustomException("该运单冷库服务申请未通过");
                    }
                    serviceRequest.setPayTime(new Date());
                    serviceRequest.setPayMoney(mawb.getPayMoney());
                    serviceRequest.setUpdateTime(new Date());
                    serviceRequestMapper.updateById(serviceRequest);
                }
//                String originBill = mawb.getOriginBill();
//                //支付的时候要检查这个单是否是换单,如果是,要将原来的单作废掉
//                if(StringUtils.isNotNull(originBill) && mawb.getSwitchBill() == 1){
//                    Mawb mawbForSwitchBill = mawbMapper.selectOne(new QueryWrapper<Mawb>()
//                            .eq("waybill_code", originBill)
//                            .eq("switch_bill", 0)
//                            .eq("type", "DEP"));
//                    //将原单状态改为已作废
//                    mawbForSwitchBill.setStatus("INVALID");
//                    mawbMapper.updateById(mawbForSwitchBill);
//
//                    WaybillTrace trace = new WaybillTrace();
//                    trace.setOperTime(new Date());
//                    trace.setOperPieces(mawbForSwitchBill.getQuantity());
//                    trace.setOperWeight(mawbForSwitchBill.getWeight());
//                    trace.setWaybillCode(mawbForSwitchBill.getWaybillCode());
//                    trace.setNodeName("已作废");
//                    waybillTraceService.insertWaybillTrace(trace);
//
//                    List<CostDetail> costDetails = costDetailMapper.selectList(new QueryWrapper<CostDetail>()
//                            .eq("waybill_code", mawb.getWaybillCode())
//                            .eq("is_settle", 1));
//                    BigDecimal totalCharge = costDetails.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    //根据原单的代理人公司去找代理人配置
//                    String agentCompany = mawbForSwitchBill.getAgentCompany();
//                    BaseAgent baseAgent = baseAgentMapper.selectBaseAgentByName(agentCompany);
//                    if(StringUtils.isNotNull(baseAgent)){
//                        //判断是否为余额支付
//                        if (baseAgent.getSettleMethod() == 1){
//                            //将原单已授权支付的费用 退还给代理人
//                            baseAgent.setBalance(baseAgent.getBalance().add(totalCharge));
//                            baseAgentMapper.updateBaseAgent(baseAgent);
//                        }
//                    }
//                }
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作成功" +  "," +
                           "code:" + response.getStatus() + "," +
                           "data:" + 1));
            return 1;
        }catch (Exception e){
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" +  "," +
                            "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        }finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }

    /**
     * 从list中获取部门信息
     * @param deptId 部门id
     * @param list 部门集合
     * @return 部门信息
     */
    private SysDept findDeptById(Long deptId, List<SysDept> list) {
        return list.stream()
                .filter(dept -> dept.getDeptId().equals(deptId))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据当前登录人获取父级公司
     */
    public Long getParentId(SysDept dept,List<SysDept> list){
        if (dept.getParentId() == 0) {
            return dept.getDeptId();
        } else {
            // 继续查找父部门
            SysDept parentDept = findDeptById(dept.getParentId(),list);
            return getParentId(parentDept,list);
        }
    }

    private void updateStatus(Mawb mawb, BigDecimal costSum, WaybillFee waybillFee,Long parentId, int mawbStatus) {
        if (waybillFee != null) {
            waybillFee.setPayTime(new Date());
            waybillFee.setPayMoney(costSum);
            waybillFee.setStatus(0);
            feeMapper.updateById(waybillFee);
        } else {
            WaybillFee fee = new WaybillFee();
            fee.setPayMoney(costSum);
            fee.setDeptId(parentId);
            fee.setPayTime(new Date());
            fee.setWaybillCode(mawb.getWaybillCode());
            fee.setStatus(0);
            fee.setType("DEP");
            feeMapper.insert(fee);
        }
        mawb.setPayStatus(mawbStatus);
        mawb.setPayTime(new Date());
        mawb.setUpdateTime(new Date());
        int i = mawbMapper.updateById(mawb);
        if (i == 0){
            throw new CustomException("支付失败，运单已被其他操作修改，请刷新后重试");
        }
    }
}
