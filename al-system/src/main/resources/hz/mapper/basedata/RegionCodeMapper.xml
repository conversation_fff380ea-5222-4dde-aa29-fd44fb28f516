<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.RegionCodeMapper">
    <insert id="insertRegionCode" parameterType="BaseRegionCode">
        insert into base_region_code
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="code != null and code != ''">code,</if>
            <if test="continent != null and continent != ''">continent,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="englishRemark != null and englishRemark != ''">english_remark,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="code != null and code != ''">#{code},</if>
            <if test="continent != null and continent != ''">#{continent},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="englishRemark != null and englishRemark != ''">#{englishRemark},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateRegionCode" parameterType="baseRegionCode">
        update base_region_code
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="continent != null and continent != ''">continent = #{continent},</if>

            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="remark == null">
                remark = null,
            </if>

            <if test="englishRemark != null">
                english_remark = #{englishRemark},
            </if>
            <if test="englishRemark == null">
                english_remark = null,
            </if>

            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null and createTime != ''">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="delRegionCode" parameterType="long">
        update base_region_code set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectRegionCodeList" resultType="com.gzairports.common.basedata.domain.BaseRegionCode">
        select id, code, continent, remark,english_remark  from base_region_code
        <where>
            and is_del = 0
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="code != null  and code != ''"> and code like concat('%',#{code},'%')</if>
            <if test="continent != null  and continent != ''"> and continent like concat('%',#{continent},'%')</if>
        </where>
    </select>

    <select id="selectRegionByCode" resultType="com.gzairports.common.basedata.domain.BaseRegionCode">
        select id, code, continent, remark, english_remark  from base_region_code where code = #{code} and is_del = 0
    </select>
</mapper>
