package com.gzairports.web.controller.business.arrival;

import com.gzairports.common.basedata.mapper.BaseAgentMapper;
import com.gzairports.common.business.arrival.domain.query.CostDetailQuery;
import com.gzairports.common.business.arrival.domain.query.PickedUpQuery;
import com.gzairports.common.business.arrival.domain.vo.ArrCostDetailListVo;
import com.gzairports.common.business.arrival.domain.vo.ArrCostDetailVo;
import com.gzairports.common.business.arrival.domain.vo.PickedOrderVo;
import com.gzairports.common.business.arrival.service.IPickUpService;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 进港费用明细 Controller
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@RestController
@RequestMapping("/arr/cost")
public class CostDetailController extends BaseController {

    @Autowired
    private IPickUpService pickUpService;

    @Autowired
    private BaseAgentMapper baseAgentMapper;

    /**
     * 费用明细
     */
//    @PreAuthorize("@ss.hasPermi('arr:pickUp:cost')")
    @GetMapping("/cost")
    @ApiOperation(value = "费用明细")
    public AjaxResult cost(@NotNull @RequestParam String waybillCode, @NotNull @RequestParam Long tallyId) {
        return AjaxResult.success(pickUpService.cost(waybillCode, tallyId));
    }


    /**
     * 查询已提货办单数据
     */
//    @PreAuthorize("@ss.hasPermi('arr:pickUp:pickedUp')")
    @PostMapping("/pickedUp")
    @ApiOperation(value = "查询已提货办单数据")
    public AjaxResult pickedUp(@RequestBody PickedUpQuery query) {
        query.setDeptId(SecurityUtils.getHighParentId());
        PickedOrderVo pickedOrderVo = pickUpService.selectByQuery(query);
        return AjaxResult.success(pickedOrderVo);
    }

    /**
     * 费用明细列表查询
     */
//    @PreAuthorize("@ss.hasPermi('arr:pickUp:pickedUp')")
    @PostMapping("/list/detail")
    @ApiOperation(value = "费用明细列表查询")
    public AjaxResult listCost(@RequestBody CostDetailQuery query) {
        query.setDeptId(SecurityUtils.getHighParentId());
        ArrCostDetailVo arrCostDetailVo = pickUpService.selectCostDetail(query);
        return AjaxResult.success(arrCostDetailVo);
    }

    /**
     * 导出进港费用明细
     */
//    @PreAuthorize("@ss.hasPermi('arr:cost:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出进港费用明细")
    public void export(HttpServletResponse response, CostDetailQuery query) {
        query.setDeptId(SecurityUtils.getHighParentId());
        List<ArrCostDetailListVo> list = pickUpService.selectCostDetailExport(query);
        ExcelUtil<ArrCostDetailListVo> excelUtil = new ExcelUtil<>(ArrCostDetailListVo.class);
        excelUtil.exportExcel(response, list, "进港费用明细");
    }

}
