package com.gzairports.common.business.reporter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.reporter.domain.ReportDataItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by david on 2025/2/24
 */
@Mapper
public interface ReportDataItemMapper extends BaseMapper<ReportDataItem> {
    /**
     * 查询分单运价费用条目
     * @param waybillCode 运单号
     * @param deptId 部门id
     * @return 结果
     */
    List<ReportDataItem> selectHawbItemList(@Param("waybillCode") String waybillCode, @Param("deptId") Long deptId);

    /**
     * 查询主单运价费用条目
     * @param reportIds 运单id
     * @return 结果
     */
    List<ReportDataItem> selectMawbItemList(@Param("reportIds") List<Long> reportIds);

    /**
     * 查询主单运价费用条目
     * @param waybillCode 运单号
     * @return 结果
     */
    BigDecimal selectSundryFee(String waybillCode);

    /**
     * 根据运单号查询运价数据
     * @param waybillCode 运单号
     * @return 运价数据
     */
    List<ReportDataItem> selectMainCarrierFee(String waybillCode);

    int insertBatchSomeColumn(List<ReportDataItem> dataItemList);

    void deleteBatch(@Param("waybillCodes") List<String> depWaybillCodes);
}
