package com.gzairports.hz.business.abnormal.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 处理记录表（无标签货物） hz_no_label_record
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
@Data
@TableName("hz_no_label_record")
public class HzNoLabelRecord {

    /** 主键id */
    private Long id;

    /** 无标签货物表id */
    private Long labelId;

    /** 处理记录时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recordTime;

    /** 处理记录描述 */
    private String recordRemark;

    /** 处理人 */
    private String recordBy;
}
