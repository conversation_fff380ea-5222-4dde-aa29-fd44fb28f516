package com.gzairports.common.charge.domain;


import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 货站收费项目表
 *
 * <AUTHOR>
 * @date 2024-06-13
 */
@Data
@TableName("hz_charge_items")
public class HzChargeItems {

    /** 主键id */
    private Long id;

    /** 收费项目简称 */
    private String chargeAbb;

    /** 收费项目名称 */
    private String chargeName;

    /** 进出港类型 0 进港 1 出港 */
    private String operationType;

    /** 是否默认收费 0 否 1 是 */
    private Integer isDefault;

    /** 有效日期开始 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startEffectiveTime;

    /** 有效日期开始 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endEffectiveTime;

    /** 是否可编辑 0 否 1 是 */
    private Integer isEdit;

    /** 备注 */
    private String remark;

    /** 是否删除 0 否 1 是 */
    @TableLogic
    private Integer isDel;

    /** 状态 0 未启用 1 启用 */
    private Integer status;

    /** 是否服务收费项目 0 否 1 是 */
    private Integer isServiceItem;

    /** 四舍五入规则 */
    private Integer roundRule;
}
