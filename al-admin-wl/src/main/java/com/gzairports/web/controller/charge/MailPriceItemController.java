package com.gzairports.web.controller.charge;

import com.gzairports.wl.charge.domain.MailPriceItem;
import com.gzairports.wl.charge.domain.query.PriceItemQuery;
import com.gzairports.wl.charge.domain.vo.MailPriceItemVo;
import com.gzairports.wl.charge.service.IMailPriceItemService;
import com.gzairports.common.annotation.OperLog;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 邮件运价条目Controller
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@RestController
@RequestMapping("/mailPriceItem")
@Api(tags = "邮件运价条目接口")
public class MailPriceItemController extends BaseController {

    @Autowired
    private IMailPriceItemService priceItemService;

    /**
     * 查询邮件运价条目列表
     */
//    @PreAuthorize("@ss.hasPermi('charge:mailPriceItem:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询邮件运价条目列表")
    public TableDataInfo list(PriceItemQuery query){
        startPage();
        List<MailPriceItemVo> list = priceItemService.selectList(query);
        return getDataTable(list);
    }

    /**
     * 导出邮件运价条目
     */
    @PreAuthorize("@ss.hasPermi('charge:mailPriceItem:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出邮件运价条目")
    public void export(HttpServletResponse response, PriceItemQuery query){
        List<MailPriceItemVo> list = priceItemService.selectList(query);
        ExcelUtil<MailPriceItemVo> util = new ExcelUtil<MailPriceItemVo>(MailPriceItemVo.class);
        util.exportExcel(response, list, "邮件运价条目");
    }

    /**
     * 获取运价条目详细信息
     */
    @PreAuthorize("@ss.hasPermi('charge:mailPriceItem:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取运价条目详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(priceItemService.selectAirItemById(id));
    }

    /**
     * 新增运价条目
     */
    @PreAuthorize("@ss.hasPermi('charge:mailPriceItem:add')")
    @OperLog(title = "运价条目", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ApiOperation(value = "新增运价条目")
    public AjaxResult add(@RequestBody MailPriceItem item)
    {
        return toAjax(priceItemService.insertAirItem(item));
    }

    /**
     * 修改运价条目
     */
    @PreAuthorize("@ss.hasPermi('charge:mailPriceItem:edit')")
    @OperLog(title = "运价条目", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ApiOperation(value = "修改运价条目")
    public AjaxResult edit(@RequestBody MailPriceItem item)
    {
        return toAjax(priceItemService.updateAirItem(item));
    }

    /**
     * 删除运价条目
     */
    @PreAuthorize("@ss.hasPermi('charge:mailPriceItem:del')")
    @OperLog(title = "运价条目", businessType = BusinessType.DELETE)
    @GetMapping("/del/{id}")
    @ApiOperation(value = "删除运价条目")
    public AjaxResult del(@PathVariable Long id)
    {
        return toAjax(priceItemService.removeAirItem(id));}

    /**
     * 批量删除邮件运价条目
     */
    @PreAuthorize("@ss.hasPermi('charge:freightCustom:del')")
    @OperLog(title = "运价条目", businessType = BusinessType.DELETE)
    @GetMapping("/delBatch/{ids}")
    @ApiOperation(value = "批量删除邮件运价")
    public AjaxResult delAll(@PathVariable("ids") Long[] ids)
    {
        priceItemService.delAll(ids);
        return AjaxResult.success("操作成功");
    }
}
