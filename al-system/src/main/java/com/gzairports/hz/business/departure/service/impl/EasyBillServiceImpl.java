package com.gzairports.hz.business.departure.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.basedata.mapper.BillTypeMapper;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.hz.business.departure.domain.AirWaybill;
import com.gzairports.hz.business.departure.domain.EasyBill;
import com.gzairports.hz.business.departure.domain.query.EasyBillInfoQuery;
import com.gzairports.hz.business.departure.domain.query.EasyBillQuery;
import com.gzairports.hz.business.departure.domain.vo.EasyBillVo;
import com.gzairports.hz.business.departure.mapper.AllAirWaybillMapper;
import com.gzairports.hz.business.departure.mapper.EasyBillMapper;
import com.gzairports.hz.business.departure.service.IEasyBillService;
import com.gzairports.wl.ticket.domain.TicketCtrl;
import com.gzairports.wl.ticket.domain.TicketNum;
import com.gzairports.wl.ticket.mapper.OperateMapper;
import com.gzairports.wl.ticket.mapper.TicketMapper;
import com.gzairports.wl.ticket.mapper.TicketNumMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 简易开单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-20
 */
@Service
public class EasyBillServiceImpl implements IEasyBillService {

    @Autowired
    private EasyBillMapper easyBillMapper;

    @Autowired
    private AllAirWaybillMapper airWaybillMapper;

    @Autowired
    private OperateMapper operateMapper;

    @Autowired
    private TicketNumMapper ticketNumMapper;

    @Autowired
    private TicketMapper ticketMapper;

    @Autowired
    private BillTypeMapper billTypeMapper;


    /**
     * 简易开单列表查询
     * @param query 查询参数
     * @return 简易开单列表
     */
    @Override
    public List<EasyBillVo> selectEasyBillList(EasyBillQuery query) {
        return easyBillMapper.selectEasyBillList(query);
    }

    /**
     * 新增简易开单
     * @param bill 简易开单数据
     * @return 结果
     */
    @Override
    public int insertEasyBill(EasyBill bill) {
        if (bill.getWaybillCode() == null){
            throw new CustomException("请输入运单号");
        }
        try {
            check(bill.getWaybillCode());
        }catch (CustomException e){
            throw new CustomException(e.getMessage());
        }
        AirWaybill airWaybill = new AirWaybill();
        BeanUtils.copyProperties(bill,airWaybill);
        airWaybill.setMakeType("SIMPLE");
        airWaybill.setDeptId(SecurityUtils.getHighParentId());
        airWaybill.setWriteTime(new Date());
        airWaybill.setWriter(SecurityUtils.getUsername());
        airWaybill.setWriteLocation("贵阳");
        airWaybill.setUpdateTime(new Date());
        airWaybill.setStatus("been_sent");
        airWaybill.setCategoryName(bill.getCategoryCode());
        airWaybill.setCargoCode(bill.getCargoCode());
        String substring = bill.getWaybillCode().substring(0, 4);
        airWaybill.setWaybillType(substring);
        airWaybill.setType("DEP");
        return airWaybillMapper.insert(airWaybill);
    }

    /**
     * 根据运单号以及单证控制校验运单号
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    @Override
    public String check(String waybillCode) {
        if (waybillCode == null){
            throw new CustomException("运单号不能为空");
        }
        if (waybillCode.length() != 15){
            throw new CustomException("运单号格式错误");
        }
        AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                .eq("waybill_code", waybillCode)
                .eq("is_del",0)
                .eq("type","DEP"));

        String message = null;
        String substring = waybillCode.substring(0, 4);
        if ("AWBD".equals(substring) || "AWBA".equals(substring)){
            message = checkWaybill(airWaybill, waybillCode,billTypeMapper.getCodeByName("国内主单"));
        }
        if ("AWBM".equals(substring)){
            message = checkWaybill(airWaybill,waybillCode,billTypeMapper.getCodeByName("邮件单"));
        }
        return message;
    }

    /**
     * 修改简易开单
     * @param bill 简易开单数据
     * @return 结果
     */
    @Override
    public int editEasyBill(EasyBill bill) {
        if (bill.getWaybillCode() == null){
            throw new CustomException("请输入运单号");
        }
        AirWaybill airWaybill = new AirWaybill();
        BeanUtils.copyProperties(bill,airWaybill);
        airWaybill.setStore(bill.getStore() + "/" +bill.getLocator());
        return airWaybillMapper.updateById(airWaybill);
    }

    /**
     * 查看详情
     * @param query 查询条件
     * @return 结果
     */
    @Override
    public EasyBill getInfo(EasyBillInfoQuery query) {
        EasyBill easyBill = new EasyBill();
        AirWaybill airWaybill = airWaybillMapper.selectById(query.getId());
        BeanUtils.copyProperties(airWaybill,easyBill);
        return easyBill;
    }

    /**
     * 删除简易开单
     * @param query 查询条件
     * @return 结果
     */
    @Override
    public int delEasyBill(EasyBillInfoQuery query) {
        AirWaybill airWaybill = airWaybillMapper.selectById(query.getId());
        airWaybill.setIsDel(1);
        return airWaybillMapper.updateById(airWaybill);
    }

    /**
     * 运单作废
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    @Override
    public int invalid(String waybillCode) {
        if (waybillCode == null){
            throw new CustomException("运单号不能为空");
        }
        if (waybillCode.length() != 15){
            throw new CustomException("运单号格式错误");
        }
        AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                .eq("waybill_code", waybillCode)
                .eq("is_del",0)
                .eq("status", "NORMAL"));
        if (airWaybill == null){
            throw new CustomException("无当前运单信息");
        }
        ticketInvalid(waybillCode);
        airWaybill.setStatus("INVALID");
        return airWaybillMapper.updateById(airWaybill);
    }

    private void ticketInvalid(String waybillCode) {
        Integer ticketNum = Integer.valueOf(waybillCode.substring(7, 14));
        Long ticketId = ticketMapper.selectCheck(billTypeMapper.getCodeByName("国内主单"), waybillCode.substring(4, 7), ticketNum);
        if (ticketId != null) {
            TicketNum num = ticketNumMapper.selectOne(new QueryWrapper<TicketNum>().eq("ticket_id", ticketId).eq("num", ticketNum));
            if (num == null) {
                throw new CustomException("当前运单未发放");
            }
            if ("NOTGRANT".equals(num.getStatus())) {
                throw new CustomException("当前运单未发放");
            }
            if (!SecurityUtils.getUserId().equals(num.getUseBy())) {
                throw new CustomException("与当前运单领单人不符");
            }
            num.setStatus("INVALID");
            ticketNumMapper.updateById(num);
        }
    }

    private String checkWaybill(Object o, String waybillCode, String code){
        TicketCtrl ctrl = operateMapper.selectOne(new QueryWrapper<TicketCtrl>().eq("code", code).eq("domint","D"));

        Integer checkNum = Integer.valueOf(waybillCode.substring(waybillCode.length() - 1));
        Integer ticketNum = Integer.valueOf(waybillCode.substring(7,14));


        if (ctrl != null && ctrl.getControlEnabled() == 1 && ctrl.getPrefix().contains(waybillCode.substring(4,7))){
            if (o != null){
                throw new CustomException("当前运单已使用");
            }

            Long ticketId = ticketMapper.selectCheck(code, waybillCode.substring(4,7), ticketNum);
            if (ticketId == null){
                throw new CustomException("无当前单证信息不可保存，请更改运单号");
            }
            TicketNum num = ticketNumMapper.selectOne(new QueryWrapper<TicketNum>().eq("ticket_id", ticketId).eq("num", ticketNum));
            if (num == null){
                throw new CustomException("当前运单未发放");
            }
            if (!SecurityUtils.getUserId().equals(num.getUseBy())){
                throw new CustomException("与当前运单领单人不符");
            }
            if (!checkNum.equals(num.getCode())){
                throw new CustomException("运单校验失败");
            }
            if ("USED".equals(num.getStatus())){
                throw new CustomException("当前运单已使用");
            }
            if ("CANCEL".equals(num.getStatus())){
                throw new CustomException("当前运单已销号");
            }
            if ("INVALID".equals(num.getStatus())){
                throw new CustomException("当前运单已作废");
            }
        }else {
            if (o != null){
                throw new CustomException("当前运单已使用");
            }
            Long ticketId = ticketMapper.selectCheck(code, waybillCode.substring(4,7), ticketNum);
            if (ticketId == null){
                return "当前运单可使用";
            }
            TicketNum num = ticketNumMapper.selectOne(new QueryWrapper<TicketNum>().eq("ticket_id", ticketId).eq("num", ticketNum));
            if (num == null){
                return "当前运单可使用";
            }
            if (!SecurityUtils.getUserId().equals(num.getUseBy())){
                throw new CustomException("与当前运单领单人不符");
            }
            if ("NOTUSED".equals(num.getStatus())){
                return "当前运单可使用";
            }
        }
        return "当前运单可使用";
    }
}
