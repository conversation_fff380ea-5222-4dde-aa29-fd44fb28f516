<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.departure.mapper.EasyBillMapper">
    <select id="selectEasyBillList" resultType="com.gzairports.hz.business.departure.domain.vo.EasyBillVo">
        select id, waybill_code, agent_code, category_name, special_cargo_code1, cargo_name, write_time
        from all_air_waybill
        <where>
            is_del = 0 and make_type = 'SIMPLE'
            <if test="waybillCode != null and waybillCode != ''">
                and waybill_code like '%${waybillCode}%'
            </if>
            <if test="agentCode != null and agentCode != ''">
                and agent_code like '%${agentCode}%'
            </if>
            <if test="startTime != null">
                AND DATE_FORMAT(write_time,'%Y%m%d %H:%i:%s') <![CDATA[>=]]> DATE_FORMAT(#{startTime, jdbcType=TIMESTAMP},'%Y%m%d %H:%i:%s')
            </if>
            <if test="endTime != null">
                AND DATE_FORMAT(write_time,'%Y%m%d %H:%i:%s') <![CDATA[<=]]> DATE_FORMAT(#{endTime, jdbcType=TIMESTAMP},'%Y%m%d %H:%i:%s')
            </if>
            <if test="categoryName != null and categoryName != ''">
                and category_name = #{categoryName}
            </if>
            <if test="specialCode != null and specialCode != ''">
                and special_cargo_code1 like '%${specialCode}%'
            </if>
        </where>
    </select>
</mapper>