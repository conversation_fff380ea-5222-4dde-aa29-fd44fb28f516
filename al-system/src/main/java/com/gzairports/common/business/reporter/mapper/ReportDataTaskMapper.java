package com.gzairports.common.business.reporter.mapper;

import com.gzairports.common.business.reporter.domain.ReportDataHawb;
import com.gzairports.common.business.reporter.domain.ReportDataWaybill;
import lombok.Data;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-19
 */
@Mapper
public interface ReportDataTaskMapper {

    /**
     * 查询最后一次更新时间
     * @return 结果
     */
    Date getLastTime(String tableName);

    /**
     * 查询主单表数据
     * @param lastSyncTime 最后更新时间
     * @return 结果
     */
    List<ReportDataWaybill> selectWaybillDataList(@Param("lastSyncTime") Date lastSyncTime, @Param("offset") Integer offset, @Param("size") Integer size,@Param("dateNow") Date dateNow);

    /**
     * 查询分单单表数据
     * @param lastSyncTime 最后更新时间
     * @return 结果
     */
    List<ReportDataHawb> selectHawbDataList(@Param("lastSyncTime") Date lastSyncTime,@Param("dateNow") Date dateNow);

    /**
     * 更新元数据
     * @param tableName 表名称
     * @param newSyncTime 更新时间
     */
    void updateSyncMetadata(@Param("tableName") String tableName,@Param("newSyncTime") Date newSyncTime);

    /**
     * 批量更新
     * @param dataList 统计数据
     */
    void batchUpsert(@Param("list") List<ReportDataWaybill> dataList);
}
