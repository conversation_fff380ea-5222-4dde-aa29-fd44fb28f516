<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.WeightMapper">
    <select id="selectWeightUnitList" resultType="com.gzairports.common.basedata.domain.BaseWeight">
        select id, weight_unit, chinese_name, english_name, conversion_rate from base_weight
        <where>
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="weightUnit != null  and weightUnit != ''"> and weight_unit like concat('%',#{weightUnit},'%')</if>
            <if test="chineseName != null  and chineseName != ''"> and chinese_name like concat('%',#{chineseName},'%')</if>
        </where>
    </select>
</mapper>
