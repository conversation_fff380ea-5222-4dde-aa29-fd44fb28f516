package com.gzairports.hz.business.departure.domain.vo;

import com.gzairports.hz.business.departure.domain.HzDepGroupUldWaybill;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 板车详情返回数据
 * <AUTHOR>
 * @date 2024-07-04
 */
@Data
public class TruckInfoVo {

    /** 组货板箱id */
    private Long id;

    /** 板车编号 */
    private String uld;

    /** 目的站 */
    private String des1;

    /** 舱位 */
    private String cabin;

    /** 文件重量 */
    private BigDecimal fileWeight;

    /** 件数 */
    private Integer quantity;

    /** 复重重量 */
    private BigDecimal repeatWeight;

    /** 板自重 */
    private BigDecimal deadWeight;

    /** 特货 */
    private String specialCode;

    /** 详情运单列表 */
    private List<HzDepGroupUldWaybill> vos;
}
