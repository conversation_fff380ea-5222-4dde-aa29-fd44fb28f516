package com.gzairports.web.controller.business.arrival;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.hz.business.arrival.domain.query.CostWaybillQuery;
import com.gzairports.hz.business.arrival.domain.vo.ArrChargeExportNewVo;
import com.gzairports.hz.business.arrival.domain.vo.BillExportAWBMVo;
import com.gzairports.hz.business.arrival.domain.vo.BillExportVo;
import com.gzairports.hz.business.arrival.domain.vo.CostWaybillVo;
import com.gzairports.hz.business.arrival.service.ICostWaybillService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: lan
 * @Desc: 进港费用controller
 * @create: 2024-11-27 17:44
 **/


@RestController
@RequestMapping("/arr/costWaybill")
@Api(tags = "进港费用列表")
public class CostWaybillController extends BaseController {

    @Autowired
    private ICostWaybillService costWaybillService;


    /**
     * 查询进港费用列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询进港费用列表")
    public TableDataInfo list(CostWaybillQuery query)
    {
        startPage();
        List<CostWaybillVo> costWaybillVos = costWaybillService.selectList(query);
        return getDataTable(costWaybillVos);
    }

    /**
     * 查询进港费用列表数据
     */
    @GetMapping("/listDetail")
    @ApiOperation(value = "查询进港费用列表数据")
    public AjaxResult listDetail(CostWaybillQuery query)
    {
        List<CostWaybillVo> costWaybillVos = costWaybillService.selectListCount(query);
        Map<String, List<CostWaybillVo>> listMap = costWaybillVos.stream().collect(Collectors.groupingBy(CostWaybillVo::getSerialNo));
        //运单数和总费用
        Integer waybillCount = costWaybillVos.size();
        BigDecimal totalCost = BigDecimal.ZERO;
        Map<String, Object> hashMap = new HashMap<>();
        for(List<CostWaybillVo> value : listMap.values()){
            totalCost = totalCost.add(value.get(0).getTotalCost());
        }
        hashMap.put("waybillCount", waybillCount);
        hashMap.put("totalCost", totalCost);
        return AjaxResult.success(hashMap);
    }



    /**
     * 费用明细
     */
    @GetMapping("/costDetail")
    @ApiOperation(value = "费用明细")
    public AjaxResult cost(@RequestParam String waybillCode, @RequestParam String serial)
    {
        return AjaxResult.success(costWaybillService.cost(waybillCode,serial));
    }

    /**
     * 导出进港费用数据
     */
    @PostMapping("/export")
    @ApiOperation(value = "导出进港费用数据")
    public void export(HttpServletResponse response, CostWaybillQuery query)
    {
        if(StringUtils.isNull(query.getPageNum()) &&  StringUtils.isNull(query.getPageSize())){
            query.setIsCancel("正常");
        }
        List<CostWaybillVo> costWaybillVos = costWaybillService.selectList(query);
        int totalWaybillNum = costWaybillVos.size();
        Map<String, List<CostWaybillVo>> listMap = costWaybillVos.stream().collect(Collectors.groupingBy(CostWaybillVo::getSerialNo));
        BigDecimal totalCost = BigDecimal.ZERO;
        for(List<CostWaybillVo> value : listMap.values()){
            totalCost = totalCost.add(value.get(0).getTotalCost());
        }
        String s = "运单数:" + totalWaybillNum + "总费用" + totalCost;
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        long idx = 1L;
        for(CostWaybillVo c:costWaybillVos){
            c.setIdx(idx++);
            c.setIsPayStr(c.getIsPay() == 0 ? "否" :
                    c.getIsPay() == 1 ? "是" : "已作废");
            if (c.getIsOut() == 0){
                c.setIsOutStr("否");
            }else {
                c.setIsOutStr("是");
            }
            String waybillCode = c.getWaybillCode();
            if(waybillCode.contains("DN")){
                c.setWaybillCode(waybillCode.substring(4,6) + "-" + waybillCode.substring(6));
            }else{
                c.setWaybillCode(waybillCode.substring(4,7) + "-" + waybillCode.substring(7));
            }
            if(c.getHandleTime()!=null){
                c.setHandleTime2(outputFormat.format(c.getHandleTime()));
            }
           if(c.getOutTime()!=null){
               c.setOutTime2(outputFormat.format(c.getOutTime()));
           }
        }
        ExcelUtil<CostWaybillVo> util = new ExcelUtil<CostWaybillVo>(CostWaybillVo.class);
        util.exportExcelArrCost(response, costWaybillVos, "进港费用列表",s);
    }

    /**
     * 费用导出
     */
    @PostMapping("/chargeExport")
    @ApiOperation(value = "费用导出")
    public void chargeExport(HttpServletResponse response, CostWaybillQuery query){
        List<ArrChargeExportNewVo> list = costWaybillService.chargeCostExport(query);
        ExcelUtil<ArrChargeExportNewVo> util = new ExcelUtil<ArrChargeExportNewVo>(ArrChargeExportNewVo.class);
        util.exportExcel(response, list, "费用明细", "贵阳机场进港收费清单", SecurityUtils.getNickName());
    }

    @PostMapping("/billExport")
    @ApiOperation(value = "账单导出")
    public void billExport(HttpServletResponse response, CostWaybillQuery query){
        query.setWaybillType("AWBA");
        List<BillExportVo> list = costWaybillService.billExport(query);
        ExcelUtil<BillExportVo> util = new ExcelUtil<>(BillExportVo.class);
        LocalDate date = LocalDate.now();
        util.exportExcel(response, list, "主单费用清单", "国内进港" + date.getMonthValue() + "-" + date.getDayOfMonth() +"费用清单");
    }

    @PostMapping("/billExportAWBM")
    @ApiOperation(value = "邮件单账单导出")
    public void billExportAWBM(HttpServletResponse response, CostWaybillQuery query){
        query.setWaybillType("AWBM");
        List<BillExportAWBMVo> list = costWaybillService.billExportAWBM(query);
        ExcelUtil<BillExportAWBMVo> util = new ExcelUtil<>(BillExportAWBMVo.class);
        LocalDate date = LocalDate.now();
        util.exportAWBMExcel(response, list, "邮件单费用清单", "国内进港邮件单" + date.getMonthValue() + "-" + date.getDayOfMonth() +"费用清单");
    }
}
