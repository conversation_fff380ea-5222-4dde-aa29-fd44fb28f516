package com.gzairports.common.charge.service;

import com.gzairports.common.business.arrival.domain.ItemDetail;
import com.gzairports.common.charge.domain.HzChargeItemRule;
import com.gzairports.common.charge.domain.vo.BillRuleVo;
import com.gzairports.common.charge.domain.vo.ChargeReturnVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 计费规则的基本行为
 * <AUTHOR>
 * @date 2024-08-03
 */
public interface BillingRule {

    /**
     * 获取类名称
     * @return 类名称
     */
    String className();

    /**
     * 计费规则值
     * @param rule 计费规则参数
     * @param weight 重量
     * @return
     */
    BillRuleVo calculateFee(List<HzChargeItemRule> rule, BigDecimal weight, Integer quantity, ItemDetail detail);
}
