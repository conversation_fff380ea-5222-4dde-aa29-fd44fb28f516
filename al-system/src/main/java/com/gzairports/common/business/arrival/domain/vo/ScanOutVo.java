package com.gzairports.common.business.arrival.domain.vo;

import lombok.Data;

import java.util.List;

/**
 * app扫码出库数据
 *
 * <AUTHOR>
 * @date 2024-08-07
 */
@Data
public class ScanOutVo {

    /** 办单id */
    private Long pickUpId;

    /** 提货码 */
    private String pickUpCode;

    /** 流水号 */
    private String serialNo;

    /** 代理人 */
    private String shipper;

    /** 收货人 */
    private String consign;

    /** 总单数 */
    private String totalCount;

    /** 总件数 */
    private String totalQuantity;

    /** 总重量 */
    private String totalWeight;

    /** 支付状态 0 未支付 1 已支付 */
    private Integer isPay;


    private Integer status;

    /** 提货运单数据 */
    private List<ScanWaybillVo> voList;
}
