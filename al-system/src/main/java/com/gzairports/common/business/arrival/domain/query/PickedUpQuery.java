package com.gzairports.common.business.arrival.domain.query;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 已提货办单查询参数
 *
 * <AUTHOR>
 * @date 2024-06-07
 */
@Data
public class PickedUpQuery {

    /** 流水号 */
    private String serialNo;

    /** 航班号 */
    private String flightNo;

    /** 办单时间(开单时间) */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 办单时间(开单时间) */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 提货人 */
    private String customerName;

    /** 提货人证件号 */
    private String customerIdNo;

    /** 经办人 */
    private String handleBy;

    /** 运单号 */
    private String waybillCode;

    /** 收货代理人 */
    private String agentCompany;

    /** 收货人 */
    private String consignee;

    /** 部门id    */
    private Long deptId;

    /** 关联id */
    private List<String> deptIdList;

    /** 是否支付 */
    private Integer isPay;
}
