<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.departure.mapper.TFilAwbinfoMapper">
    
    <resultMap type="TFilAwbinfo" id="TFilAwbinfoResult">
        <result property="billid"    column="BILLID"    />
        <result property="stocktypeid"    column="STOCKTYPEID"    />
        <result property="stockpre"    column="STOCKPRE"    />
        <result property="stockno"    column="STOCKNO"    />
        <result property="productid"    column="PRODUCTID"    />
        <result property="carrierproductid"    column="CARRIERPRODUCTID"    />
        <result property="delflag"    column="DELFLAG"    />
        <result property="previousbillid"    column="PREVIOUSBILLID"    />
        <result property="domint"    column="DOMINT"    />
        <result property="customctl"    column="CUSTOMCTL"    />
        <result property="specopeid"    column="SPECOPEID"    />
        <result property="specopeidext"    column="SPECOPEIDEXT"    />
        <result property="sairportid"    column="SAIRPORTID"    />
        <result property="scityid"    column="SCITYID"    />
        <result property="eairportid"    column="EAIRPORTID"    />
        <result property="ecityid"    column="ECITYID"    />
        <result property="by1"    column="BY1"    />
        <result property="dest1"    column="DEST1"    />
        <result property="dest1city"    column="DEST1CITY"    />
        <result property="by2"    column="BY2"    />
        <result property="dest2"    column="DEST2"    />
        <result property="dest2city"    column="DEST2CITY"    />
        <result property="by3"    column="BY3"    />
        <result property="dest3"    column="DEST3"    />
        <result property="dest3city"    column="DEST3CITY"    />
        <result property="by4"    column="BY4"    />
        <result property="dest4"    column="DEST4"    />
        <result property="dest4city"    column="DEST4CITY"    />
        <result property="cargono"    column="CARGONO"    />
        <result property="cargonm"    column="CARGONM"    />
        <result property="pack"    column="PACK"    />
        <result property="meas"    column="MEAS"    />
        <result property="pcs"    column="PCS"    />
        <result property="weight"    column="WEIGHT"    />
        <result property="feewt"    column="FEEWT"    />
        <result property="vol"    column="VOL"    />
        <result property="ctrlopedepartment"    column="CTRLOPEDEPARTMENT"    />
        <result property="shprname"    column="SHPRNAME"    />
        <result property="shprtel"    column="SHPRTEL"    />
        <result property="shpraddress"    column="SHPRADDRESS"    />
        <result property="shpcustomerid"    column="SHPCUSTOMERID"    />
        <result property="cnsnname"    column="CNSNNAME"    />
        <result property="cnsntel"    column="CNSNTEL"    />
        <result property="cnsnaddress"    column="CNSNADDRESS"    />
        <result property="csgcustomerid"    column="CSGCUSTOMERID"    />
        <result property="preairline"    column="PREAIRLINE"    />
        <result property="preflightno"    column="PREFLIGHTNO"    />
        <result property="preflightdate"    column="PREFLIGHTDATE"    />
        <result property="collected"    column="COLLECTED"    />
        <result property="exchagerate"    column="EXCHAGERATE"    />
        <result property="comat"    column="COMAT"    />
        <result property="refrigerated"    column="REFRIGERATED"    />
        <result property="forknum"    column="FORKNUM"    />
        <result property="whshold"    column="WHSHOLD"    />
        <result property="expcusttransit"    column="EXPCUSTTRANSIT"    />
        <result property="impcusttransit"    column="IMPCUSTTRANSIT"    />
        <result property="shorttrans"    column="SHORTTRANS"    />
        <result property="shorttransbup"    column="SHORTTRANSBUP"    />
        <result property="cargoowner"    column="CARGOOWNER"    />
        <result property="chargetime"    column="CHARGETIME"    />
        <result property="isinstruction"    column="ISINSTRUCTION"    />
        <result property="notify"    column="NOTIFY"    />
        <result property="shippervalue"    column="SHIPPERVALUE"    />
        <result property="trafficvalue"    column="TRAFFICVALUE"    />
        <result property="customvalue"    column="CUSTOMVALUE"    />
        <result property="insurevalue"    column="INSUREVALUE"    />
        <result property="fileattached"    column="FILEATTACHED"    />
        <result property="ratetype"    column="RATETYPE"    />
        <result property="processingmethod"    column="PROCESSINGMETHOD"    />
        <result property="handlingcircs"    column="HANDLINGCIRCS"    />
        <result property="reservedtonnage"    column="RESERVEDTONNAGE"    />
        <result property="carriage"    column="CARRIAGE"    />
        <result property="rate"    column="RATE"    />
        <result property="preflightno1"    column="PREFLIGHTNO1"    />
        <result property="preflightdate1"    column="PREFLIGHTDATE1"    />
        <result property="crtoper"    column="CRTOPER"    />
        <result property="preairline1"    column="PREAIRLINE1"    />
        <result property="wtunit"    column="WTUNIT"    />
        <result property="originalwt"    column="ORIGINALWT"    />
        <result property="originalfeewt"    column="ORIGINALFEEWT"    />
        <result property="volunit"    column="VOLUNIT"    />
        <result property="currencyid"    column="CURRENCYID"    />
        <result property="originalvol"    column="ORIGINALVOL"    />
        <result property="crtopetime"    column="CRTOPETIME"    />
        <result property="dlvpriority"    column="DLVPRIORITY"    />
        <result property="chked"    column="CHKED"    />
        <result property="labelnum"    column="LABELNUM"    />
        <result property="shpcustomer"    column="SHPCUSTOMER"    />
        <result property="crtagent"    column="CRTAGENT"    />
        <result property="crtdate"    column="CRTDATE"    />
        <result property="extraweight"    column="EXTRAWEIGHT"    />
        <result property="storeremark"    column="STOREREMARK"    />
        <result property="balanceremark"    column="BALANCEREMARK"    />
        <result property="filechked"    column="FILECHKED"    />
        <result property="iscarbalance"    column="ISCARBALANCE"    />
        <result property="cnsnidcard"    column="CNSNIDCARD"    />
        <result property="ciqcheck"    column="CIQCHECK"    />
        <result property="tmpbillno"    column="TMPBILLNO"    />
        <result property="subbillid"    column="SUBBILLID"    />
        <result property="istrans"    column="ISTRANS"    />
        <result property="splittag"    column="SPLITTAG"    />
        <result property="customstrans"    column="CUSTOMSTRANS"    />
        <result property="endoper"    column="ENDOPER"    />
        <result property="endopetime"    column="ENDOPETIME"    />
        <result property="isrcs"    column="ISRCS"    />
        <result property="transrcs"    column="TRANSRCS"    />
        <result property="istranscarrier"    column="ISTRANSCARRIER"    />
    </resultMap>

    <sql id="selectTFilAwbinfoVo">
        select BILLID, STOCKTYPEID, STOCKPRE, STOCKNO, PRODUCTID, CARRIERPRODUCTID, DELFLAG, PREVIOUSBILLID, DOMINT, CUSTOMCTL, SPECOPEID, SPECOPEIDEXT, SAIRPORTID, SCITYID, EAIRPORTID, ECITYID,
               BY1, DEST1, DEST1CITY, BY2, DEST2, DEST2CITY, BY3, DEST3, DEST3CITY, BY4, DEST4, DEST4CITY, CARGONO, CARGONM, PACK, MEAS, PCS, WEIGHT, FEEWT, VOL, CTRLOPEDEPARTMENT, SHPRNAME,
               SHPRTEL, SHPRADDRESS, SHPCUSTOMERID, CNSNNAME, CNSNTEL, CNSNADDRESS, CSGCUSTOMERID, PREAIRLINE, PREFLIGHTNO, PREFLIGHTDATE, COLLECTED, EXCHAGERATE, COMAT, REFRIGERATED, FORKNUM,
               WHSHOLD, EXPCUSTTRANSIT, IMPCUSTTRANSIT, SHORTTRANS, SHORTTRANSBUP, CARGOOWNER, CHARGETIME, ISINSTRUCTION, NOTIFY, SHIPPERVALUE, TRAFFICVALUE, CUSTOMVALUE, INSUREVALUE, FILEATTACHED,
               RATETYPE, PROCESSINGMETHOD, HANDLINGCIRCS, RESERVEDTONNAGE, CARRIAGE, RATE, PREFLIGHTNO1, PREFLIGHTDATE1, CRTOPER, PREAIRLINE1, WTUNIT, ORIGINALWT, ORIGINALFEEWT, VOLUNIT, CURRENCYID,
               ORIGINALVOL, CRTOPETIME, DLVPRIORITY, CHKED, LABELNUM, SHPCUSTOMER, CRTAGENT, CRTDATE, EXTRAWEIGHT, STOREREMARK, BALANCEREMARK, FILECHKED, ISCARBALANCE, CNSNIDCARD, CIQCHECK, TMPBILLNO,
               SUBBILLID, ISTRANS, SPLITTAG, CUSTOMSTRANS, ENDOPER, ENDOPETIME, ISRCS, TRANSRCS, ISTRANSCARRIER from KWECFPS.t_fil_awbinfo
    </sql>


    <select id="selectTFilAwbinfoStockno" parameterType="String" resultMap="TFilAwbinfoResult">
        <include refid="selectTFilAwbinfoVo"/>
        where STOCKNO = #{stockno}
    </select>
    <select id="selectTFilAwbinfoPageList"
            resultType="com.gzairports.common.business.departure.domain.vo.TFilAwbinfo">
        SELECT *
        FROM (
             SELECT t.*, ROWNUM rnum
             FROM (
                SELECT BILLID, STOCKTYPEID, STOCKPRE, STOCKNO, PRODUCTID, CARRIERPRODUCTID, DELFLAG, PREVIOUSBILLID, DOMINT, CUSTOMCTL, SPECOPEID, SPECOPEIDEXT, SAIRPORTID, SCITYID, EAIRPORTID, ECITYID,
                        BY1, DEST1, DEST1CITY, BY2, DEST2, DEST2CITY, BY3, DEST3, DEST3CITY, BY4, DEST4, DEST4CITY, CARGONO, CARGONM, PACK, MEAS, PCS, WEIGHT, FEEWT, VOL, CTRLOPEDEPARTMENT, SHPRNAME,
                        SHPRTEL, SHPRADDRESS, SHPCUSTOMERID, CNSNNAME, CNSNTEL, CNSNADDRESS, CSGCUSTOMERID, PREAIRLINE, PREFLIGHTNO, PREFLIGHTDATE, COLLECTED, EXCHAGERATE, COMAT, REFRIGERATED, FORKNUM,
                        WHSHOLD, EXPCUSTTRANSIT, IMPCUSTTRANSIT, SHORTTRANS, SHORTTRANSBUP, CARGOOWNER, CHARGETIME, ISINSTRUCTION, NOTIFY, SHIPPERVALUE, TRAFFICVALUE, CUSTOMVALUE, INSUREVALUE, FILEATTACHED,
                        RATETYPE, PROCESSINGMETHOD, HANDLINGCIRCS, RESERVEDTONNAGE, CARRIAGE, RATE, PREFLIGHTNO1, PREFLIGHTDATE1, CRTOPER, PREAIRLINE1, WTUNIT, ORIGINALWT, ORIGINALFEEWT, VOLUNIT, CURRENCYID,
                        ORIGINALVOL, CRTOPETIME, DLVPRIORITY, CHKED, LABELNUM, SHPCUSTOMER, CRTAGENT, CRTDATE, EXTRAWEIGHT, STOREREMARK, BALANCEREMARK, FILECHKED, ISCARBALANCE, CNSNIDCARD, CIQCHECK, TMPBILLNO,
                        SUBBILLID, ISTRANS, SPLITTAG, CUSTOMSTRANS, ENDOPER, ENDOPETIME, ISRCS, TRANSRCS, ISTRANSCARRIER
                FROM KWECFPS.t_fil_awbinfo WHERE CRTOPETIME &gt;= TO_DATE('2024-04-12 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
                ORDER BY CRTOPETIME
        ) t
        WHERE ROWNUM &lt;= #{endRow}
        )
        WHERE rnum &gt; #{startRow}
    </select>
</mapper>