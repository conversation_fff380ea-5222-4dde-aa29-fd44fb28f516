package com.gzairports.hz.business.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 收费管理运单返回参数
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
public class ChargeWaybillVo {

    /** 主键id */
    private Long id;

    /** 开单日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开单日期", dateFormat = "yyyy-MM-dd")
    private Date writeDate;

    /** 开单时间 */
    @JsonFormat(pattern = "HH:mm:ss")
    @Excel(name = "开单时间", dateFormat = "HH:mm:ss")
    private Date writeTime;

    /** 运单类型 */
    private Integer switchBill;

    /** 运单号 */
    @Excel(name = "运单号")
    private String waybillCode;

    /** 航班号 */
    private String flightNo1;

    /** 航班日期 */
    private Date execDate;

    /** 代理人 */
    @Excel(name = "代理人")
    private String agentCode;

    /** 货品大类 */
    @Excel(name = "货品大类")
    private String categoryName;

    /** 货品代码 */
    @Excel(name = "货品代码")
    private String cargoCode;

    /** 品名 */
    @Excel(name = "品名")
    private String cargoName;

    /** 特货代码1 */
    @Excel(name = "特货代码")
    private String specialCargoCode1;

    /** 件数 */
    @Excel(name = "件数",cellType = Excel.ColumnType.NUMERIC)
    private Integer quantity;

    /** 重量 */
    @Excel(name = "重量",cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal weight;

    /** 计费重量 */
    @Excel(name = "计费重量",cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal chargeWeight;

    /** 运单状态 */
    @Excel(name = "运单状态")
    private String status;

    /** 缴费状态 0 未授权支付 1 已授权支付 2 已结算 */
    private Integer payStatus;

    /** 缴费状态 */
    @Excel(name = "缴费状态")
    private String payStatusStr;

    /** 已出港件数 */
    @Excel(name = "已出港件数")
    private Integer depQuantity = 0;

    /** 已出港重量 */
    private BigDecimal depWeight;

    /** 已出港重量 */
    @Excel(name = "已出港重量",cellType = Excel.ColumnType.NUMERIC)
    private String depWeightStr = "0";

    /** 结算费用 */
    @Excel(name = "结算费用",cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal costSum;

    /** 目的站 */
    @Excel(name = "目的站")
    private String desPort;

    /** 承运人 */
    @Excel(name = "承运人")
    private String carrier1;

    /** 支付时间 */
    @Excel(name = "预授权支付时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /** 支付结算时间 */
    @Excel(name = "支付结算时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date settleTime;

    /** 预支付费用 */
    @Excel(name = "预支付费用")
    private BigDecimal payMoney;

    /** 已结算退还费用 */
    private BigDecimal refund;

    private Long deptId;

    /** 名码一致 */
    @Excel(name = "名码一致")
    private String codeNameSame;

}
