package com.gzairports.hz.business.arrival.service;

import com.gzairports.hz.business.arrival.domain.query.ArrInventoryQuery;
import com.gzairports.hz.business.arrival.domain.vo.ArrInventoryVo;
import com.gzairports.hz.business.arrival.domain.vo.InventoryListVo;

import java.util.List;

/**
 * 库存管理Service接口
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
public interface IInventoryService {

    /**
     * 库存管理列表查询
     * @param query 查询参数
     * @return 库存列表
     */
    ArrInventoryVo selectList(ArrInventoryQuery query);

    /**
     * 导出库存列表
     * @param query 查询参数
     * @return 结果
     */
    List<InventoryListVo> selectListByQuery(ArrInventoryQuery query);
}
