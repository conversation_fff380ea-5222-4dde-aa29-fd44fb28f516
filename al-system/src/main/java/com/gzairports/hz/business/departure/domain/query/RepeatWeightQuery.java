package com.gzairports.hz.business.departure.domain.query;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: lan
 * @create: 2025-03-17 16:06
 **/

@Data
public class RepeatWeightQuery {
    /** 组货id * */
    private Long groupUldId;
    /** 配载id * */
    private Long flightLoadId;
    /** 板箱号 */
    private String uld;
    /** 仓位 */
    private String cabin;
    /** 重量 */
    private BigDecimal weight;
}
