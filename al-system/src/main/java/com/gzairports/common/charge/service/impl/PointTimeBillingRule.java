package com.gzairports.common.charge.service.impl;

import cn.hutool.core.lang.func.VoidFunc0;
import com.gzairports.common.business.arrival.domain.ItemDetail;
import com.gzairports.common.charge.domain.HzChargeItemRule;
import com.gzairports.common.charge.domain.vo.BillRuleVo;
import com.gzairports.common.charge.service.BillingRule;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 时间点计费规则
 * <AUTHOR>
 * @date 2024-10-08
 */
public class PointTimeBillingRule implements BillingRule {
    @Override
    public String className() {
        return "PointTimeBillingRule.class";
    }

    @Override
    public BillRuleVo calculateFee(List<HzChargeItemRule> rule, BigDecimal weight, Integer quantity, ItemDetail detail) {
        BillRuleVo vo = new BillRuleVo();
        if (detail.getPointTime() == null){
            return vo;
        }
        vo.setQuantity(weight.toString());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        HzChargeItemRule itemRule = rule.get(0);
        if (detail.getPointTime().isAfter(LocalTime.parse(itemRule.getPointTime(),formatter))){
            BigDecimal multiply = itemRule.getAfterRate().multiply(weight);
            BigDecimal decimal = itemRule.getAfterMin() == null ? new BigDecimal(0) : itemRule.getAfterMin();
            if (multiply.compareTo(decimal) >= 0){
                vo.setRate(itemRule.getAfterRate());
            }
            BigDecimal max = multiply.max(decimal);
            vo.setTotalCharge(max);
            return vo;
        }else {
            BigDecimal multiply = itemRule.getBeforeRate().multiply(weight);
            BigDecimal decimal = itemRule.getBeforeMin() == null ? new BigDecimal(0) : itemRule.getBeforeMin();
            if (multiply.compareTo(decimal) >= 0){
                vo.setRate(itemRule.getBeforeRate());
            }
            BigDecimal max = multiply.max(decimal);
            vo.setTotalCharge(max);
            return vo;
        }
    }
}
