<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.wl.reporter.mapper.MawbHawbIncomeMapper">

    <select id="selectExportDataList" resultType="com.gzairports.wl.reporter.domain.vo.AHIncomeDetailVO">
        select DISTINCT ardw.id, ardw.waybill_code as awbaWaybillCode, ardw.write_time,/*ardh.waybill_code as hawbWaybillCode,*/
               ardw.source_port,ardw.des_port,ardw.quantity,ardw.weight,ardw.charge_weight,ardw.special_cargo_code1,
               ardw.cargo_name
        from all_report_data_waybill ardw
        left join all_report_data_hawb ardh on ardw.waybill_code = ardh.master_waybill_code
        where ardh.waybill_code IS NOT NULL
        <if test="query.masterWaybillCode != null and query.masterWaybillCode != ''">
            and ardw.waybill_code = #{query.masterWaybillCode}
        </if>
        <if test="query.waybillCode != null and query.waybillCode != ''">
            and ardh.waybill_code = #{query.waybillCode}
        </if>
        <if test="query.carrier != null and query.carrier != ''">
            and ardw.carrier1 = #{query.carrier}
        </if>
        <if test="query.sourcePort != null and query.sourcePort != ''">
            and ardw.source_port = #{query.sourcePort}
        </if>
        <if test="query.desPort != null and query.desPort != ''">
            and ardw.des_port = #{query.desPort}
        </if>
        <if test="query.shipper != null and query.shipper != ''">
            and ardw.shipper = #{query.shipper}
        </if>
        <if test="query.consign != null and query.consign != ''">
            and ardw.consign = #{query.consign}
        </if>
        <if test="query.startWriteTime != null">
            and ardw.write_time <![CDATA[>=]]> #{query.startWriteTime}
        </if>
        <if test="query.endWriteTime != null">
            and ardw.write_time <![CDATA[<=]]> #{query.endWriteTime}
        </if>
        <if test="query.switchBill != null">
            <if test="query.switchBill == 1">
                and ardw.origin_bill is not null
            </if>
            <if test="query.switchBill == 0">
                and ardw.origin_bill is null
            </if>
        </if>
        <if test="query.customsSupervision != null">
            and ardw.customs_supervision = #{query.customsSupervision}
        </if>
        <if test="query.deptId != null">
            and ardw.dept_id = #{query.deptId}
        </if>

    </select>
    <select id="selectExportDataTotalList" resultType="com.gzairports.wl.reporter.domain.vo.AHIncomeTotalVO">
        select ardw.waybill_code as masterWaybillCode,ardw.write_time,ardh.waybill_code as waybillCode,ardw.flight_no1 as flightNo,
               ardw.source_port,ardw.des_port,ardw.quantity,ardw.weight,ardw.charge_weight,ardw.special_cargo_code1,
               ardw.cargo_name,ardh.quantity as HawbQuantity,ardh.weight as HawbWeight,ardh.charge_weight as HawbChargeWeight
        from all_report_data_waybill ardw
                 left join all_report_data_hawb ardh on ardw.waybill_code = ardh.master_waybill_code
        where ardh.waybill_code IS NOT NULL
        <if test="query.masterWaybillCode != null and query.masterWaybillCode != ''">
            and ardw.waybill_code = #{query.masterWaybillCode}
        </if>
        <if test="query.waybillCode != null and query.waybillCode != ''">
            and ardh.waybill_code = #{query.waybillCode}
        </if>
        <if test="query.carrier != null and query.carrier != ''">
            and ardw.carrier1 = #{query.carrier}
        </if>
        <if test="query.sourcePort != null and query.sourcePort != ''">
            and ardw.source_port = #{query.sourcePort}
        </if>
        <if test="query.desPort != null and query.desPort != ''">
            and ardw.des_port = #{query.desPort}
        </if>
        <if test="query.shipper != null and query.shipper != ''">
            and ardw.shipper = #{query.shipper}
        </if>
        <if test="query.consign != null and query.consign != ''">
            and ardw.consign = #{query.consign}
        </if>
        <if test="query.startWriteTime != null">
            and ardw.write_time <![CDATA[>=]]> #{query.startWriteTime}
        </if>
        <if test="query.endWriteTime != null">
            and ardw.write_time <![CDATA[<=]]> #{query.endWriteTime}
        </if>
        <if test="query.switchBill != null">
            <if test="query.switchBill == 1">
                and ardw.origin_bill is not null
            </if>
            <if test="query.switchBill == 0">
                and ardw.origin_bill is null
            </if>
        </if>
        <if test="query.customsSupervision != null">
            and ardw.customs_supervision = #{query.customsSupervision}
        </if>
        <if test="query.deptId != null">
            and ardw.dept_id = #{query.deptId}
        </if>
        <if test="query.hawbShipper != null and query.hawbShipper != ''">
            and ardh.shipper = #{query.hawbShipper}
        </if>
    </select>
    <select id="selectTotalCharging" resultType="java.math.BigDecimal">
        select sum(charging)
        from all_report_data_item
        where waybill_code = #{waybillCode}
    </select>
    <select id="selectSettleTotalCharging" resultType="java.math.BigDecimal">
        select sum(total_charge)
        from all_report_dep_settle_cost
        where waybill_code= #{waybillCode}
    </select>
    <select id="selectHawbChargingByWaybillCode" resultType="java.math.BigDecimal">
        select sum(charging)
        from all_report_data_item
        where waybill_code in(
            select waybill_code
            from all_report_data_hawb
            where master_waybill_code = #{waybillCode})
    </select>
    <select id="selectHawbWaybillCode" resultType="java.lang.String">
        select waybill_code
        from all_report_data_hawb
        where master_waybill_code = #{waybillCode}
    </select>
</mapper>