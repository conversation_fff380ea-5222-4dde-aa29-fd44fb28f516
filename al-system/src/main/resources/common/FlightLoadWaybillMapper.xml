<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.arrival.mapper.FlightLoadWaybillMapper">
    <select id="selectListById" resultType="java.lang.Long">
        select waybill_id from hz_flight_load_waybill where flight_load_id = #{bizId}
    </select>
    <select id="selectLoadWaybillList"
            resultType="com.gzairports.hz.business.departure.domain.vo.ForwardImportWaybillVo">
        select hflw.id, aaw.cargo_name, aaw.waybill_code, aaw.source_port, aaw.des1, hflw.quantity, hflw.weight,
               aaw.quantity as waybillQuantity, aaw.weight as waybillWeight,
               aaw.storage_transport_notes,sd.dept_name_abb
        from hz_flight_load_waybill hflw
                 left join all_air_waybill aaw on aaw.id = hflw.waybill_id
                 left join sys_dept sd on sd.dept_id = aaw.dept_id
        where hflw.flight_load_id = #{flightLoadId}
    </select>
    <select id="selectListByFlightLoadId"
            resultType="com.gzairports.hz.business.departure.domain.vo.FormalWaybillVo">
        select hflw.id, aaw.cargo_name, aaw.waybill_code, aaw.source_port,  aaw.des_port, aaw.quantity as waybillQuantity, hflw.quantity,
               aaw.weight as waybillWeight, hflw.weight, aaw.des1, aaw.special_cargo_code1, aaw.storage_transport_notes as remark,
               aaw.origin_bill, aaw.volume, hflw.is_edit, 1 as looseCabin
        from hz_flight_load_waybill hflw
                 left join all_air_waybill aaw on aaw.id = hflw.waybill_id
        where hflw.flight_load_id = #{id}
          <if test="type != null">
            and hflw.is_edit = #{type}
        </if>
    </select>
    <select id="selectListByFlightLoadIdAndWaybillId"
            resultType="com.gzairports.hz.business.departure.domain.vo.FormalWaybillVo">
        select hflw.id, aaw.cargo_name, aaw.waybill_code, aaw.category_name, aaw.source_port,  aaw.des_port, aaw.quantity as allQuantity, hflw.quantity,
        aaw.weight as allWeight, hflw.weight, aaw.des1, aaw.special_cargo_code1, aaw.storage_transport_notes as remark,
        aaw.origin_bill, aaw.volume, hflw.is_edit, 1 as looseCabin
        from hz_flight_load_waybill hflw
        left join all_air_waybill aaw on aaw.id = hflw.waybill_id
        where hflw.flight_load_id = #{id}
        <if test="waybillIds != null and waybillIds.size() > 0">
        and hflw.id in
        <foreach collection="waybillIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        </if>
    </select>
    <select id="selectListByIds" resultType="com.gzairports.common.business.arrival.domain.FlightLoadWaybill">
        select hzlw.id, hzlw.flight_load_id, hzlw.waybill_id, hzlw.quantity, hzlw.weight, aaw.waybill_code
        from hz_flight_load_waybill hzlw
            left join all_air_waybill aaw on aaw.id = hzlw.waybill_id
        where hzlw.id in
        <foreach collection="flightLoadWaybillIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectWaybillNum" resultType="java.lang.Integer">
        select count(DISTINCT hflw.waybill_id)
        from hz_flight_load_waybill hflw
        where hflw.flight_load_id = #{id}
    </select>
    <select id="selectWaybillIdsByFlightLoadId" resultType="com.gzairports.common.business.arrival.domain.FlightLoadWaybill">
        select waybill_id, SUM(t.quantity) as quantity, SUM(t.weight) as weight
        from ((select waybill_id, quantity, weight from hz_flight_load_waybill where flight_load_id = #{id})
        union all
        (select hfluw.waybill_id, hfluw.quantity, hfluw.weight
         from hz_flight_load_uld_waybill hfluw
                  left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
         where hflu.flight_load_id = #{id})) as t  group by waybill_id
    </select>
    <select id="selectWaybillIdsByLoadId" resultType="com.gzairports.common.business.arrival.domain.FlightLoadWaybill">
        select waybill_id, quantity, weight, uld, cabin
        from ((select waybill_id, quantity, weight, 'BLK' as uld, '' as cabin from hz_flight_load_waybill where flight_load_id = #{id})
              union all
              (select hfluw.waybill_id, hfluw.quantity, hfluw.weight, hflu.uld, hflu.cabin
               from hz_flight_load_uld_waybill hfluw
                        left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
               where hflu.flight_load_id = #{id})) as t
    </select>
    <select id="selectLoadInfoList"
            resultType="com.gzairports.common.business.arrival.domain.FlightLoadWaybill">
        (select waybill_id, quantity, weight from hz_flight_load_waybill where flight_load_id = #{legId} and waybill_id = #{waybillId})
        union all
        (select hfluw.waybill_id, hfluw.quantity, hfluw.weight
         from hz_flight_load_uld_waybill hfluw
                  left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
         where hflu.flight_load_id = #{legId} and hfluw.waybill_id = #{waybillId})
    </select>
    <select id="selectLoadWaybill" resultType="com.gzairports.hz.business.departure.domain.vo.CableMawbVo">
        (
        select aaw.id, aaw.waybill_code, concat_ws('/',CONCAT(afi.air_ways,afi.flight_no),afi.exec_date) as flightInfo, aaw.danger_code, aaw.consign,
               aaw.special_cargo_code1 as specialCode, aaw.weight, aaw.cargo_name, aaw.des_port, aaw.quantity, 'BLK' as uldCode, '' as loadLocation
        from hz_flight_load_waybill hflw
            left join hz_flight_load hfl on hfl.id = hflw.flight_load_id
            left join all_flight_info afi on afi.flight_id = hfl.flight_id
            left join all_air_waybill aaw on hflw.waybill_id = aaw.id
        <where>
            aaw.is_del = 0 and aaw.type = 'DEP'
            <if test="type == 'FSH'"> and aaw.special_cargo_code1 is not null </if>
            <if test="flightNo != null  and flightNo != ''"> and CONCAT(afi.air_ways,afi.flight_no) like '%${flightNo}%'</if>
            <if test="flightDate != null"> and afi.exec_date = #{flightDate}</if>
            <if test="waybillCode != null  and waybillCode != ''"> and aaw.waybill_code = #{waybillCode}</if>
            <if test="specialCode != null  and specialCode != ''"> and aaw.special_cargo_code1 = #{specialCode}</if>
        </where>
        )
        union all
        (
        select aaw.id, aaw.waybill_code, concat_ws('/',CONCAT(afi.air_ways,afi.flight_no),afi.exec_date) as flightInfo, aaw.danger_code, aaw.consign,
               aaw.special_cargo_code1 as specialCode, aaw.weight, aaw.cargo_name, aaw.des_port, aaw.quantity, hflu.uld as uldCode, hflu.cabin as loadLocation
        from hz_flight_load_uld_waybill hfluw
            left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
            left join hz_flight_load hfl on hfl.id = hflu.flight_load_id
            left join all_flight_info afi on afi.flight_id = hfl.flight_id
            left join all_air_waybill aaw on hfluw.waybill_id = aaw.id
        <where>
            aaw.is_del = 0 and aaw.type = 'DEP'
            <if test="type == 'FSH'"> and aaw.special_cargo_code1 is not null </if>
            <if test="flightNo != null  and flightNo != ''"> and CONCAT(afi.air_ways,afi.flight_no) like '%${flightNo}%'</if>
            <if test="flightDate != null"> and afi.exec_date = #{flightDate}</if>
            <if test="waybillCode != null  and waybillCode != ''"> and aaw.waybill_code = #{waybillCode}</if>
            <if test="specialCode != null  and specialCode != ''"> and aaw.special_cargo_code1 = #{specialCode}</if>
        </where>
        )
    </select>
    <select id="isHaveDate" resultType="java.lang.Long">
        (select id from hz_flight_load_waybill where waybill_id = #{waybillId} and flight_load_id = #{flightLoadId})
        UNION ALL
        (select hfluw.id from hz_flight_load_uld_waybill hfluw
            left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
        where waybill_id = #{waybillId} and hflu.flight_load_id = #{flightLoadId})
    </select>
    <select id="selectByLoadInfo" resultType="com.gzairports.common.business.departure.domain.vo.LoadInfoVo">
        (
            select hflw.flight_load_id as loadId, hflw.quantity, hflw.weight,hfl.create_time, hfl.weight_time, hfl.flight_id
            from hz_flight_load_waybill hflw
                left join hz_flight_load hfl on hfl.id = hflw.flight_load_id
            where hflw.waybill_id = #{waybillId}
        )
        UNION ALL
        (
            select hflu.flight_load_id as loadId, hfluw.quantity, hfluw.weight,hfl.create_time, hfl.weight_time, hfl.flight_id
            from hz_flight_load_uld_waybill hfluw
                left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
                left join hz_flight_load hfl on hfl.id = hflu.flight_load_id
             where hfluw.waybill_id = #{waybillId}
        )
    </select>
    <select id="selectWaybillIdsByWaybillId"
            resultType="com.gzairports.common.business.arrival.domain.FlightLoadWaybill">
        select SUM(t.weight) as weight
        from ((select waybill_id, weight from hz_flight_load_waybill where waybill_id = #{waybillId})
              union all
              (select hfluw.waybill_id, hfluw.weight
               from hz_flight_load_uld_waybill hfluw
                        left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
               where hfluw.waybill_id = #{waybillId})) as t  group by waybill_id
    </select>
    <select id="selectWaybillByWaybillId" resultType="java.math.BigDecimal">
        select SUM(t.weight)
        from ((select weight from hz_flight_load_waybill where waybill_id = #{id})
              union all
              (select  weight
               from hz_flight_load_uld_waybill where waybill_id = #{id})) as t
    </select>
    <select id="selectSettleWeight"
            resultType="com.gzairports.common.business.arrival.domain.FlightLoadWaybill">
        SELECT waybill_id, SUM(quantity) AS quantity, SUM(weight) AS weight
        FROM (
        SELECT hflw.waybill_id, hflw.quantity, hflw.weight
        FROM hz_flight_load_waybill hflw
        INNER JOIN hz_flight_load hfl ON hfl.id = hflw.flight_load_id
        WHERE hflw.waybill_id = #{waybillId}
        <if test="flightIds != null and flightIds.size() > 0">
            AND hfl.flight_id IN
            <foreach collection="flightIds" item="flightId" open="(" separator="," close=")">
                #{flightId}
            </foreach>
        </if>

        UNION ALL

        SELECT hfluw.waybill_id, hfluw.quantity, hfluw.weight
        FROM hz_flight_load_uld_waybill hfluw
        INNER JOIN hz_flight_load_uld hflu ON hflu.id = hfluw.load_uld_id
        INNER JOIN hz_flight_load hfl ON hfl.id = hflu.flight_load_id
        WHERE hfluw.waybill_id = #{waybillId}
        <if test="flightIds != null and flightIds.size() > 0">
            AND hfl.flight_id IN
            <foreach collection="flightIds" item="flightId" open="(" separator="," close=")">
                #{flightId}
            </foreach>
        </if>
        ) AS t
        GROUP BY waybill_id;
    </select>
    <select id="selectFlightIds" resultType="java.lang.Long">
        (
            select hfl.flight_id
            from hz_flight_load hfl
                     left join hz_flight_load_waybill hflw on hfl.id = hflw.flight_load_id
            where hflw.waybill_id in
                <foreach collection="waybillIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
        )
        UNION
        (
            select hfl.flight_id
            from hz_flight_load hfl
                     left join hz_flight_load_uld hflu on hfl.id = hflu.flight_load_id
                     left join hz_flight_load_uld_waybill hfluw on hflu.id = hfluw.load_uld_id
            where hfluw.waybill_id in
                <foreach collection="waybillIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
        )
    </select>
    <select id="selectReportLoadList"
            resultType="com.gzairports.common.business.reporter.domain.ReportDepLoad">
        (
            select aaw.id as reportId, aaw.waybill_code, aaw.agent_company, aaw.des_port, aaw.carrier1, aaw.cargo_code, aaw.cargo_name,
                   hflw.quantity as typeQuantity, hflw.weight as typeWeight, hfl.flight_id, 'BLK' as uld, afi.exec_date as flightDate,
                   CONCAT(afi.air_ways,afi.flight_no) as flightNo, hfl.state as flightStatus, afi.start_real_takeoff_time,
                   hfl.load_time, hfl.load_user, hfl.id as flight_load_id
            from hz_flight_load_waybill hflw
                left join hz_flight_load hfl on hfl.id = hflw.flight_load_id
                left join all_flight_info afi on afi.flight_id = hfl.flight_id
                left join all_air_waybill aaw on aaw.id = hflw.waybill_id
            where 1=1
            <if test="ids != null and ids.size() > 0">
                and hflw.waybill_id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        )
        UNION ALL
        (
            select aaw.id as reportId, aaw.waybill_code, aaw.agent_company, aaw.des_port, aaw.carrier1, aaw.cargo_code, aaw.cargo_name,
                   hfluw.quantity as typeQuantity, hfluw.weight as typeWeight, hfl.flight_id, hflu.uld, afi.exec_date as flightDate,
                   CONCAT(afi.air_ways,afi.flight_no) as flightNo, hfl.state as flightStatus,afi.start_real_takeoff_time,
                    hfl.load_time, hfl.load_user, hfl.id as flight_load_id
            from hz_flight_load_uld_waybill hfluw
                     left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
                     left join hz_flight_load hfl on hfl.id = hflu.flight_load_id
                     left join all_flight_info afi on afi.flight_id = hfl.flight_id
                     left join all_air_waybill aaw on aaw.id = hfluw.waybill_id
            where 1=1
            <if test="ids != null and ids.size() > 0">
                and hfluw.waybill_id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        )
    </select>
    <select id="selectDetailList" resultType="com.gzairports.hz.business.cable.domain.vo.ConsignmentDetail">
        SELECT aaw.waybill_type as awbType, aaw.source_port as depAirport, aaw.des_port as desAirport, aaw.size as dimensions, aaw.cargo_name as goodsName,
               aaw.waybill_code as mawbNo, aaw.quantity as totalPieces, aaw.volume, aaw.weight,
               <choose>
                    <when test="uldNo == 'BLK'">
                        hflw.quantity as pieces,
                    </when>
                    <otherwise>
                        hfluw.quantity as pieces,
                    </otherwise>
                </choose>
               CONCAT_WS(',',aaw.special_cargo_code1, aaw.special_cargo_code2, aaw.special_cargo_code3, aaw.other_special_cargo_code) as osi
        FROM
        <choose>
            <when test="uldNo == 'BLK'">
                hz_flight_load_waybill hflw
                left join hz_flight_load hfl on hfl.id = hflw.flight_load_id
                left join all_air_waybill aaw on aaw.id = hflw.waybill_id
                where 1=1
                <if test="waybillIdList != null and waybillIdList.size() > 0">
                    and hflw.waybill_id in
                    <foreach collection="waybillIdList" item="waybillId" open="(" separator="," close=")">
                        #{waybillId}
                    </foreach>
                </if>
            </when>
            <otherwise>
                hz_flight_load_uld_waybill hfluw
                left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
                left join hz_flight_load hfl on hfl.id = hflu.flight_load_id
                left join all_air_waybill aaw on aaw.id = hfluw.waybill_id
                where hflu.uld = #{uldNo}
                <if test="waybillIdList != null and waybillIdList.size() > 0">
                    and hfluw.waybill_id in
                    <foreach collection="waybillIdList" item="waybillId" open="(" separator="," close=")">
                        #{waybillId}
                    </foreach>
                </if>
            </otherwise>
        </choose>
    </select>
    <select id="selectFWBDataList" resultType="com.gzairports.hz.business.cable.domain.vo.FWBJsonVO">
        (
            select sd.agent_code as agentIataCode, sd.dept_name as agentName, aaw.rate_per_kg as chargeOrRate, aaw.quantity as chargePieces, aaw.charge_weight,
                   aaw.cargo_code as commodityItemNo, aaw.consign_address as consigneeAddr, aaw.consign_region as consigneeCity, aaw.consign as consigneeName,
                   aaw.consign_phone as consigneeTel, aaw.source_port as depAirport, aaw.des_port as desAirport, aaw.des_port as destination, aaw.size as dimensions,
                   aaw.write_time as executeDate, aaw.write_location as executePlace, fi.exec_date as flightDate, CONCAT(afi.air_ways,afi.flight_no) as flightNo,
                   aaw.cargo_name as goodsDescnc, aaw.cargo_code as goodsDesc, aaw.waybill_code as mawbNo, hflw.quantity as pieces, aaw.carrier2 as secondCarrier,
                   CONCAT_WS(',',aaw.special_cargo_code1, aaw.special_cargo_code2, aaw.special_cargo_code3, aaw.other_special_cargo_code) as osi, aaw.des2 as secondDestination,
                   aaw.quantity as slac, aaw.carrier3 as thirdCarrier, aaw.des3 as thirdDestination, aaw.cost_sum as totalChargeamount, aaw.pay_money as totalPrepaid, aaw.volume,
                   aaw.weight
            from hz_flight_load_waybill hflw
                     left join hz_flight_load hfl on hfl.id = hflw.flight_load_id
                     left join all_air_waybill aaw on aaw.id = hflw.waybill_id
                     left join sys_dept sd on sd.dept_id = aaw.dept_id
            where hflw.waybill_id = #{waybillId}
        )
        UNION ALL
        (
            select sd.agent_code as agentIataCode, sd.dept_name as agentName, aaw.rate_per_kg as chargeOrRate, aaw.quantity as chargePieces, aaw.charge_weight,
                   aaw.cargo_code as commodityItemNo, aaw.consign_address as consigneeAddr, aaw.consign_region as consigneeCity, aaw.consign as consigneeName,
                   aaw.consign_phone as consigneeTel, aaw.source_port as depAirport, aaw.des_port as desAirport, aaw.des_port as destination, aaw.size as dimensions,
                   aaw.write_time as executeDate, aaw.write_location as executePlace, fi.exec_date as flightDate, CONCAT(afi.air_ways,afi.flight_no) as flightNo,
                   aaw.cargo_name as goodsDescnc, aaw.cargo_code as goodsDesc, aaw.waybill_code as mawbNo, hfluw.quantity as pieces, aaw.carrier2 as secondCarrier,
                   CONCAT_WS(',',aaw.special_cargo_code1, aaw.special_cargo_code2, aaw.special_cargo_code3, aaw.other_special_cargo_code) as osi, aaw.des2 as secondDestination,
                   aaw.quantity as slac, aaw.carrier3 as thirdCarrier, aaw.des3 as thirdDestination, aaw.cost_sum as totalChargeamount, aaw.pay_money as totalPrepaid, aaw.volume,
                   aaw.weight
            from hz_flight_load_uld_waybill hfluw
                     left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
                     left join hz_flight_load hfl on hfl.id = hflu.flight_load_id
                     left join all_air_waybill aaw on aaw.id = hfluw.waybill_id
                     left join sys_dept sd on sd.dept_id = aaw.dept_id
            where hfluw.waybill_id = #{waybillId}
        )
    </select>
    <select id="selectFHLDataList" resultType="com.gzairports.hz.business.cable.domain.vo.FHLJsonVO">
        (
            select sd.agent_code as agentIataCode, sd.dept_name as agentName, aaw.rate_per_kg as chargeOrRate, aaw.quantity as chargePieces, aaw.charge_weight,
                   aaw.cargo_code as commodityItemNo, aaw.consign_address as consigneeAddr, aaw.consign_region as consigneeCity, aaw.consign as consigneeName,
                   aaw.consign_phone as consigneeTel, aaw.source_port as depAirport, aaw.des_port as desAirport, aaw.des_port as destination, aaw.size as dimensions,
                   aaw.write_time as executeDate, aaw.write_location as executePlace, fi.exec_date as flightDate, CONCAT(afi.air_ways,afi.flight_no) as flightNo,
                   aaw.cargo_name as goodsDescnc, aaw.cargo_code as goodsDesc, aaw.waybill_code as mawbNo, hflw.quantity as pieces, aaw.carrier2 as secondCarrier,
                   CONCAT_WS(',',aaw.special_cargo_code1, aaw.special_cargo_code2, aaw.special_cargo_code3, aaw.other_special_cargo_code) as osi, aaw.des2 as secondDestination,
                   aaw.quantity as slac, aaw.carrier3 as thirdCarrier, aaw.des3 as thirdDestination, aaw.cost_sum as totalChargeamount, aaw.pay_money as totalPrepaid, aaw.volume,
                   aaw.weight
            from hz_flight_load_waybill hflw
                     left join hz_flight_load hfl on hfl.id = hflw.flight_load_id
                     left join all_air_waybill aaw on aaw.id = hflw.waybill_id
                     left join sys_dept sd on sd.dept_id = aaw.dept_id
            where hflw.waybill_id = #{waybillId}
        )
        UNION ALL
        (
            select sd.agent_code as agentIataCode, sd.dept_name as agentName, aaw.rate_per_kg as chargeOrRate, aaw.quantity as chargePieces, aaw.charge_weight,
                   aaw.cargo_code as commodityItemNo, aaw.consign_address as consigneeAddr, aaw.consign_region as consigneeCity, aaw.consign as consigneeName,
                   aaw.consign_phone as consigneeTel, aaw.source_port as depAirport, aaw.des_port as desAirport, aaw.des_port as destination, aaw.size as dimensions,
                   aaw.write_time as executeDate, aaw.write_location as executePlace, fi.exec_date as flightDate, CONCAT(afi.air_ways,afi.flight_no) as flightNo,
                   aaw.cargo_name as goodsDescnc, aaw.cargo_code as goodsDesc, aaw.waybill_code as mawbNo, hfluw.quantity as pieces, aaw.carrier2 as secondCarrier,
                   CONCAT_WS(',',aaw.special_cargo_code1, aaw.special_cargo_code2, aaw.special_cargo_code3, aaw.other_special_cargo_code) as osi, aaw.des2 as secondDestination,
                   aaw.quantity as slac, aaw.carrier3 as thirdCarrier, aaw.des3 as thirdDestination, aaw.cost_sum as totalChargeamount, aaw.pay_money as totalPrepaid, aaw.volume,
                   aaw.weight
            from hz_flight_load_uld_waybill hfluw
                     left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
                     left join hz_flight_load hfl on hfl.id = hflu.flight_load_id
                     left join all_air_waybill aaw on aaw.id = hfluw.waybill_id
                     left join sys_dept sd on sd.dept_id = aaw.dept_id
            where hfluw.waybill_id = #{waybillId}
        )
    </select>
    <select id="selectDetailListForLegId"
            resultType="com.gzairports.hz.business.cable.domain.vo.ConsignmentDetail">
        SELECT aaw.waybill_type as awbType, aaw.source_port as depAirport, aaw.des_port as desAirport, aaw.size as dimensions, aaw.cargo_name as goodsName,
        aaw.waybill_code as mawbNo, aaw.quantity as totalPieces, aaw.volume, aaw.weight, CONCAT_WS(' ', aaw.consign, aaw.consign_phone) as osi,
        <choose>
            <when test="uldNo == 'BLK'">
                hflw.quantity as pieces,
            </when>
            <otherwise>
                hfluw.quantity as pieces,
            </otherwise>
        </choose>
        CONCAT_WS(',',aaw.special_cargo_code1, aaw.special_cargo_code2, aaw.special_cargo_code3, aaw.other_special_cargo_code) as shcStr
        FROM
        <choose>
            <when test="uldNo == 'BLK'">
                hz_flight_load_waybill hflw
                left join hz_flight_load hfl on hfl.id = hflw.flight_load_id
                left join all_air_waybill aaw on aaw.id = hflw.waybill_id
                where 1=1
                <if test="legIds != null and legIds.size() > 0">
                    and hflw.flight_load_id in
                    <foreach collection="legIds" item="legId" open="(" separator="," close=")">
                        #{legId}
                    </foreach>
                </if>
            </when>
            <otherwise>
                hz_flight_load_uld_waybill hfluw
                left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
                left join hz_flight_load hfl on hfl.id = hflu.flight_load_id
                left join all_air_waybill aaw on aaw.id = hfluw.waybill_id
                where hflu.uld = #{uldNo}
                <if test="legIds != null and legIds.size() > 0">
                    and hflu.flight_load_id in
                    <foreach collection="legIds" item="legId" open="(" separator="," close=")">
                        #{legId}
                    </foreach>
                </if>
            </otherwise>
        </choose>
    </select>
    <select id="selectLoadFlightWaybill"
            resultType="com.gzairports.hz.business.departure.domain.vo.LoadFlightWaybillVO">
        WITH target_waybills AS (
            SELECT id, waybill_code
            FROM all_air_waybill
            WHERE waybill_code IN
        <foreach collection="waybillCodeList" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        )
        SELECT tw.waybill_code, hfl1.flight_id
        FROM target_waybills tw
                 INNER JOIN hz_flight_load_waybill hflw ON tw.id = hflw.waybill_id
                 LEFT JOIN hz_flight_load hfl1 ON hfl1.id = hflw.flight_load_id
        UNION
        SELECT tw.waybill_code, hfl2.flight_id
        FROM target_waybills tw
                 INNER JOIN hz_flight_load_uld_waybill hfluw ON tw.id = hfluw.waybill_id
                 LEFT JOIN hz_flight_load_ULD hflu ON hflu.id = hfluw.load_uld_id
                 LEFT JOIN hz_flight_load hfl2 ON hfl2.id = hflu.flight_load_id;
    </select>
    <select id="selectNotSettleWaybill"
            resultType="com.gzairports.hz.business.departure.domain.vo.LoadFlightWaybillVO">
        SELECT aaw.waybill_code, hfl1.flight_id
        FROM hz_flight_load_waybill hflw
        INNER JOIN hz_flight_load hfl1 ON hfl1.id = hflw.flight_load_id
        INNER JOIN all_air_waybill aaw ON aaw.id = hflw.waybill_id
        WHERE aaw.dept_id = #{deptId} and hfl1.flight_id IN
        <foreach collection="flightIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        UNION
        SELECT aaw.waybill_code, hfl2.flight_id
        FROM hz_flight_load_uld_waybill hfluw
        INNER JOIN hz_flight_load_uld hflu ON hflu.id = hfluw.load_uld_id
        INNER JOIN hz_flight_load hfl2 ON hfl2.id = hflu.flight_load_id
        INNER JOIN all_air_waybill aaw ON aaw.id = hfluw.waybill_id
        WHERE aaw.dept_id = #{deptId} and hfl2.flight_id IN
        <foreach collection="flightIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectHistoryLoad"
            resultType="com.gzairports.hz.business.departure.domain.vo.LoadFlightWaybillVO">
        WITH target_waybills AS (
        SELECT id, waybill_code
        FROM all_air_waybill
        WHERE waybill_code IN
        <foreach collection="loadWaybillList" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        )
        SELECT aaw.waybill_code, hfl1.flight_id
        FROM hz_flight_load_waybill hflw
        INNER JOIN hz_flight_load hfl1 ON hfl1.id = hflw.flight_load_id
        INNER JOIN target_waybills aaw ON aaw.id = hflw.waybill_id
        INNER JOIN all_flight_info afi ON afi.flight_id = hfl1.flight_id
        WHERE afi.start_scheme_takeoff_time <![CDATA[<=]]> #{endTime}

        UNION ALL

        SELECT aaw.waybill_code, hfl2.flight_id
        FROM hz_flight_load_uld_waybill hfluw
        INNER JOIN hz_flight_load_uld hflu ON hflu.id = hfluw.load_uld_id
        INNER JOIN hz_flight_load hfl2 ON hfl2.id = hflu.flight_load_id
        INNER JOIN target_waybills aaw ON aaw.id = hfluw.waybill_id
        INNER JOIN all_flight_info afi ON afi.flight_id = hfl2.flight_id
        WHERE afi.start_scheme_takeoff_time <![CDATA[<=]]> #{endTime}
    </select>
</mapper>