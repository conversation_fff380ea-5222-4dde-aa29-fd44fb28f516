<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.wl.charge.mapper.FreightRateCustomItemMapper">
    <update id="removeCustomItem">
        update wl_sf_custom_price_item set is_del = 1 where id = #{id}
    </update>
    <select id="selectRateItemList" resultType="com.gzairports.wl.charge.domain.vo.FreightRateItemVO">
        select wfri.id, wcfr.rate_name, wfri.applicable_documents, wfri.departure_city, wfri.destination_city,
               wfri.airline, wfri.flight_number, wfri.rate_category, wfri.cargo_category, wfri.transit_city,
               wfri.special_cargo_code, wfri.product_code, wfri.minimum_freight, wfri.remarks
        from wl_sf_custom_price_item wfri
            left join wl_sf_custom_price wcfr on wcfr.id = wfri.rate_id
                  where wfri.is_del = 0 and wfri.rate_id = #{id}
    </select>
    <select id="selectCustomItemList" resultType="com.gzairports.wl.charge.domain.vo.FreightRateItemVO">
        select wfri.id,wcfr.rate_name, wfri.applicable_documents, wfri.departure_city, wfri.destination_city,
               wfri.airline, wfri.flight_number, wfri.rate_category, wfri.cargo_category, wfri.transit_city,
               wfri.special_cargo_code, wfri.minimum_freight, wfri.remarks, wfri.product_code,
                wcfr.effective_date as startTime,wcfr.expiration_date as endTime,wfri.clause_name
        from wl_sf_custom_price_item wfri
        left join wl_sf_custom_price wcfr on wcfr.id = wfri.rate_id
        <where>
            and wfri.is_del = 0 and wfri.rate_id = #{id}
            <if test="rateName != null  and rateName != ''"> and wcfr.rate_name like concat('%',#{rateName},'%')</if>
            <if test="applicableDocuments != null  and applicableDocuments != ''"> and wfri.applicable_documents = #{applicableDocuments}</if>
            <if test="sourcePort != null  and sourcePort.size() > 0">
                and wfri.departure_city in
                <foreach collection="sourcePort" item="sourceCity" index="index" open="(" close=")" separator=",">
                    #{sourceCity}
                </foreach>
            </if>
<!--            <if test="desPort != null  and desPort.size() > 0">-->
<!--                and wfri.destination_city in-->
<!--                <foreach collection="desPort" item="desCity" index="index" open="(" close=")" separator=",">-->
<!--                    #{desCity}-->
<!--                </foreach>-->
<!--            </if>-->
            <if test="destinationCity != null  and destinationCity != ''"> and wfri.destination_city = #{destinationCity}</if>
            <if test="minimumFreight != null and minimumFreight != ''"> and wfri.minimum_freight = #{minimumFreight}</if>
        </where>
    </select>
</mapper>