package com.gzairports.common.basedata.service;


import com.gzairports.common.basedata.domain.BaseAbnormalType;
import com.gzairports.common.basedata.domain.query.AbnormalTypeQuery;

import java.util.List;

/**
 * 不正常类型Service接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
public interface IAbnormalTypeService {

    /**
     * 查询不正常类型列表
     *
     * @param query 查询参数
     * @return 不正常类型列表
     */
    List<BaseAbnormalType> selectAbnormalTypeList(AbnormalTypeQuery query);

    /**
     * 导入不正常类型
     *
     * @param abnormalTypes 不正常类型列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    String importAbnormalType(List<BaseAbnormalType> abnormalTypes, boolean updateSupport);

    /**
     * 新增不正常类型
     *
     * @param abnormalType 不正常类型
     * @return 结果
     */
    int addAbnormalType(BaseAbnormalType abnormalType);

    /**
     * 修改不正常类型
     *
     * @param abnormalType 不正常类型
     * @return 结果
     */
    int editAbnormalType(BaseAbnormalType abnormalType);

    /**
     * 删除不正常类型
     *
     * @param ids 不正常类型id集合
     * @return 结果
     */
    int delAbnormalType(Long[] ids);
}
