package com.gzairports.hz.business.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.models.auth.In;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 航班配载列表返回数据
 * @date 2024-07-03
 * <AUTHOR>
 */
@Data
public class FlightLoadVo {

    /** 航班配载id */
    private Long id;

    /** 航班id */
    private Long flightId;

    /** 航班号 */
    private String flightNo;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date execDate;

    /** 计飞 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startSchemeTakeoffTime;

    /** 状态（未配载 已配载 未组货 已组货 未复重 未交接 已完成 无货） */
    private String state;

    /** 配载时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String loadTime;

    /** 航段 */
    private String legs;

    /** 舱位1H件数 */
    private Integer h1Quantity;

    /** 舱位1H重量 */
    private BigDecimal h1Weight;

    /** 舱位2H件数 */
    private Integer h2Quantity;

    /** 舱位2H重量 */
    private BigDecimal h2Weight;

    /** 舱位3H件数 */
    private Integer h3Quantity;

    /** 舱位3H重量 */
    private BigDecimal h3Weight;

    /** 舱位4H件数 */
    private Integer h4Quantity;

    /** 舱位4H重量 */
    private BigDecimal h4Weight;

    /** 舱位5H件数 */
    private Integer h5Quantity;

    /** 舱位5H重量 */
    private BigDecimal h5Weight;

    /** 舱位1H */
    private String h1;

    /** 舱位2H */
    private String h2;

    /** 舱位3H */
    private String h3;

    /** 舱位4H */
    private String h4;

    /** 舱位5H */
    private String h5;

    /** 状态颜色 未配载白色 white，已配载绿色 green、计飞前100-120分钟未配载的黄色 yellow，计飞前100分钟以下未配载的红色 read */
    private String statusColor;

    /** 是否开放 */
    private Integer isOpen;

    /** 是否可创建 */
    private Integer isCreate;
}
