package com.gzairports.hz.business.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 复重返回数据
 * <AUTHOR>
 * @date 2024-07-06
 */
@Data
public class RepeatWeightVo {

    /** 航班配载id */
    private Long id;

    /** 航班号 */
    @Excel(name = "航班号")
    private String flightNo;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "航班日期")
    private Date execDate;

    /** 机型 */
    @Excel(name = "机型")
    private String craftType;

    /** 航段 */
    @Excel(name = "航段")
    private String leg;

    /** 状态（未配载 已配载 未组货 已组货 未复重 未交接 已完成 无货） */
    private String state;

    @Excel(name = "状态")
    private String status;

    /** 计飞 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "计飞")
    private LocalDateTime startSchemeTakeoffTime;

    /** 预飞 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "预飞")
    private LocalDateTime startAlterateTakeoffTime;

    /** 实飞 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "实飞")
    private LocalDateTime startRealTakeoffTime;

    /** 正式舱单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "正式舱单时间")
    private Date loadTime;

    /** 组货完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "组货完成时间")
    private Date groupTime;

    /** 复磅完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "复磅完成时间")
    private Date weightTime;

    /** 机号 */
    private String craftNo;
}
