package com.gzairports.hz.business.arrival.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.business.arrival.domain.AllPickUpOut;
import com.gzairports.common.business.arrival.domain.HzArrTally;
import com.gzairports.common.business.arrival.mapper.AllPickUpOutMapper;
import com.gzairports.common.business.arrival.mapper.HzArrTallyMapper;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.hz.business.arrival.domain.query.ArrInventoryQuery;
import com.gzairports.hz.business.arrival.domain.vo.ArrInventoryVo;
import com.gzairports.hz.business.arrival.domain.vo.InventoryListVo;
import com.gzairports.hz.business.arrival.mapper.HzArrRecordOrderMapper;
import com.gzairports.hz.business.arrival.service.IInventoryService;
import com.gzairports.hz.business.departure.domain.vo.ChargeWaybillVo;
import com.gzairports.hz.business.departure.mapper.AllAirWaybillMapper;
import com.gzairports.hz.business.transfer.domain.HzTransferHandover;
import com.gzairports.hz.business.transfer.domain.HzTransferHandoverWaybill;
import com.gzairports.hz.business.transfer.mapper.HzTransferHandoverMapper;
import com.gzairports.hz.business.transfer.mapper.HzTransferHandoverWaybillMapper;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 库存管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-12
 */
@Service
public class InventoryServiceImpl implements IInventoryService {

    @Autowired
    private HzArrTallyMapper tallyMapper;

    @Autowired
    private AllPickUpOutMapper pickUpOutMapper;

    @Autowired
    private HzTransferHandoverMapper hzTransferHandoverMapper;

    @Autowired
    private HzTransferHandoverWaybillMapper handoverWaybillMapper;

    /**
     * 库存管理列表查询
     * @param query 查询参数
     * @return 库存列表
     */
    @Override
    public ArrInventoryVo selectList(ArrInventoryQuery query) {
        ArrInventoryVo vo = new ArrInventoryVo();
        List<InventoryListVo> voList = getVoList(query);
        if (!CollectionUtils.isEmpty(voList)){
            int sum = voList.stream().mapToInt(InventoryListVo::getTallyQuantity).sum();
            vo.setTotalQuantity(sum);
            BigDecimal reduce = voList.stream().map(InventoryListVo::getTallyWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setTotalWeight(reduce);
            vo.setTotalNumber(voList.size());
            vo.setVoList(voList);
        }
        return vo;
    }

    /**
     * 导出库存列表
     * @param query 查询参数
     * @return 结果
     */
    @Override
    public List<InventoryListVo> selectListByQuery(ArrInventoryQuery query) {
        return getVoList(query);
    }

    @NotNull
    private List<InventoryListVo> getVoList(ArrInventoryQuery query) {
        List<InventoryListVo> list = tallyMapper.selectInventoryList(query);
        List<InventoryListVo> voList = new ArrayList<>();
        Set<String> waybillCodeSet = new HashSet<>();
        for (InventoryListVo inventoryListVo : list) {
            if(!waybillCodeSet.add(inventoryListVo.getWaybillCode())){
                continue;
            }
            //库存值是 = 运单总理货件数/重量 - 出库件数/重量
            int inventoryQuantity;
            BigDecimal inventoryWeight;
            List<HzArrTally> hzArrTallyList = tallyMapper.selectList(new QueryWrapper<HzArrTally>()
                    .eq("waybill_code", inventoryListVo.getWaybillCode()));
            List<AllPickUpOut> pickUpOuts = pickUpOutMapper.selectList(new QueryWrapper<AllPickUpOut>()
                    .eq("waybill_code", inventoryListVo.getWaybillCode())
                    .eq("is_cancel", 0));
            int tallyQuantity = hzArrTallyList.stream().mapToInt(HzArrTally::getPieces).sum();
            BigDecimal tallyWeight = hzArrTallyList.stream().map(HzArrTally::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            inventoryQuantity = tallyQuantity - pickUpOuts.stream().mapToInt(AllPickUpOut::getPieces).sum();
            inventoryWeight = tallyWeight.subtract(pickUpOuts.stream().map(AllPickUpOut::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add));
            inventoryListVo.setInventoryQuantity(inventoryQuantity);
            inventoryListVo.setInventoryWeight(inventoryWeight);
            inventoryListVo.setTallyQuantity(tallyQuantity);
            inventoryListVo.setTallyWeight(tallyWeight);

            boolean isTransfer = true;
            HzTransferHandoverWaybill handoverWaybill = handoverWaybillMapper.selectOne(new QueryWrapper<HzTransferHandoverWaybill>().eq("waybill_id", inventoryListVo.getWaybillId()));
            if (handoverWaybill != null){
                HzTransferHandover hzTransferHandover = hzTransferHandoverMapper.selectById(handoverWaybill.getTransferId());
                if (hzTransferHandover != null) {
                    if (hzTransferHandover.getFileStatus() == 2 || hzTransferHandover.getCargoStatus() == 2){
                        isTransfer = false;
                    }
                }
            }
            List<AllPickUpOut> pickUpOut = pickUpOutMapper.selectList(new QueryWrapper<AllPickUpOut>().eq("pick_up_id", inventoryListVo.getPickUpId()));
            if (isTransfer && (inventoryListVo.getPickUpId() == null || CollectionUtils.isEmpty(pickUpOut))) {
                voList.add(inventoryListVo);
            }
        }
        return voList;
    }
}
