<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.reporter.mapper.ReportDataHawbMapper">
    <select id="selectShippingFee" resultType="java.math.BigDecimal">
        select sum(di.charging) from all_report_data_item di
             left join all_report_data_hawb dh on di.waybill_code = dh.waybill_code
             left join all_report_data_waybill dw on dh.master_waybill_code = dw.waybill_code
        where dw.waybill_code = #{waybillCode} and di.is_del = 0
    </select>
    <select id="baseCargoAnalysis" resultType="com.gzairports.wl.reporter.domain.vo.WaybillWeightVO">
        select  DATE_FORMAT(write_time, '%m') as monthData, sum(weight) as weight, 0 as sign
        from all_report_data_hawb
        where DATE_FORMAT(write_time, '%Y')  = #{baseYear} and dept_id = #{deptId}
        <if test="baseMonth != null and baseMonth.size() > 0">
            and DATE_FORMAT(write_time, '%m') in
            <foreach collection="baseMonth" item="month" open="(" separator="," close=")">
                #{month}
            </foreach>
        </if>
        GROUP BY DATE_FORMAT(write_time, '%m')
    </select>
    <select id="compareCargoAnalysis" resultType="com.gzairports.wl.reporter.domain.vo.WaybillWeightVO">
        select  DATE_FORMAT(write_time, '%m') as monthData, sum(weight) as weight, 1 as sign
        from all_report_data_hawb
        where DATE_FORMAT(write_time, '%Y')  = #{compareYear} and dept_id = #{deptId}
        <if test="baseMonth != null and baseMonth.size() > 0">
            and DATE_FORMAT(write_time, '%m') in
            <foreach collection="baseMonth" item="month" open="(" separator="," close=")">
                #{month}
            </foreach>
        </if>
        GROUP BY DATE_FORMAT(write_time, '%m')
    </select>
    <select id="selectIncome" resultType="java.math.BigDecimal">
        select sum(charging)
        from all_report_data_item ai
            left join all_report_data_hawb ah on ah.waybill_code = ai.waybill_code
        where ah.dept_id = #{deptId} and DATE_FORMAT(ah.write_time, '%Y-%m')  = #{month}
        <if test="writer != null and writer != ''">
            and ah.writer = #{writer}
        </if>
    </select>
    <select id="selectCustomDaySum" resultType="com.gzairports.wl.reporter.domain.vo.ReportWaybillInfoVO">
        select id, waybill_code, weight, shipper, DATE_FORMAT(write_time, '%Y-%m-%d') as monthData
        from all_report_data_hawb
        where  dept_id = #{deptId}
        <if test="startTime != null">
            and write_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            and write_time <![CDATA[<=]]> #{endTime}
        </if>
    </select>
</mapper>