package com.gzairports.wl.coldRegister.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.BaseAgent;
import com.gzairports.common.basedata.domain.BaseBalance;
import com.gzairports.common.basedata.domain.BaseCargoCode;
import com.gzairports.common.basedata.mapper.BaseAgentMapper;
import com.gzairports.common.basedata.mapper.BaseBalanceMapper;
import com.gzairports.common.basedata.mapper.CargoCodeMapper;
import com.gzairports.common.business.arrival.domain.HzArrItem;
import com.gzairports.common.business.arrival.domain.HzArrTally;
import com.gzairports.common.business.arrival.mapper.HzArrItemMapper;
import com.gzairports.common.business.arrival.mapper.HzArrTallyMapper;
import com.gzairports.common.business.departure.domain.CostDetail;
import com.gzairports.common.business.departure.domain.Mawb;
import com.gzairports.common.business.departure.mapper.CostDetailMapper;
import com.gzairports.common.business.departure.mapper.MawbMapper;
import com.gzairports.common.charge.domain.HzChargeIrRelation;
import com.gzairports.common.charge.domain.HzChargeItemRule;
import com.gzairports.common.charge.domain.HzChargeItems;
import com.gzairports.common.charge.domain.vo.BillRuleVo;
import com.gzairports.common.charge.domain.vo.IrRelationVo;
import com.gzairports.common.charge.mapper.HzChargeIrRelationMapper;
import com.gzairports.common.charge.mapper.HzChargeItemRuleMapper;
import com.gzairports.common.charge.mapper.HzChargeItemsMapper;
import com.gzairports.common.charge.service.BillingRule;
import com.gzairports.common.charge.service.impl.BillingRuleFactory;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.common.log.service.IWaybillLogService;
import com.gzairports.common.utils.BigDecimalRoundUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.ServletUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.hz.business.arrival.domain.HzArrRecordOrder;
import com.gzairports.hz.business.departure.domain.HzColdRegister;
import com.gzairports.hz.business.departure.domain.HzCollectWaybill;
import com.gzairports.hz.business.departure.domain.query.HzColdRegisterQuery;
import com.gzairports.hz.business.departure.domain.vo.ColdQueryVo;
import com.gzairports.hz.business.departure.domain.vo.ColdRegisterVo;
import com.gzairports.hz.business.departure.domain.vo.HzColdRegisterVo;
import com.gzairports.hz.business.departure.domain.vo.TotalChargeVo;
import com.gzairports.hz.business.departure.mapper.HzCollectWaybillMapper;
import com.gzairports.wl.coldRegister.mapper.WlColdRegisterMapper;
import com.gzairports.wl.coldRegister.service.IWlColdRegisterService;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by david on 2025/1/3
 */
@Service
public class WlColdRegisterServiceImpl extends ServiceImpl<WlColdRegisterMapper, HzColdRegister> implements IWlColdRegisterService {

    @Autowired
    private WlColdRegisterMapper registerMapper;

    @Autowired
    private MawbMapper mawbMapper;

    @Autowired
    private CostDetailMapper costDetailMapper;

    @Autowired
    private HzArrTallyMapper tallyMapper;

    @Autowired
    private HzArrItemMapper itemMapper;

    @Autowired
    private HzCollectWaybillMapper collectWaybillMapper;

    @Autowired
    private HzChargeItemsMapper chargeItemsMapper;

    @Autowired
    private HzChargeItemRuleMapper itemRuleMapper;

    @Autowired
    private CargoCodeMapper cargoCodeMapper;

    @Autowired
    private HzChargeIrRelationMapper relationMapper;

    @Autowired
    private BaseAgentMapper baseAgentMapper;

    @Autowired
    private BaseBalanceMapper baseBalanceMapper;

    @Autowired
    private IWaybillLogService waybillLogService;

    @Override
    public HzColdRegisterVo selectList(HzColdRegisterQuery query) {
        HzColdRegisterVo vo = new HzColdRegisterVo();
        List<ColdQueryVo> voList = registerMapper.selectNewListByQuery(query);
        if (!CollectionUtils.isEmpty(voList)){
            List<ColdQueryVo> coldRegisters = voList.stream().filter(e -> e.getStatus().equals(0)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(coldRegisters)){
                vo.setColdNum(coldRegisters.size());
            }
            List<ColdQueryVo> out = voList.stream().filter(e -> e.getStatus().equals(2)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(out)){
                vo.setOutNum(out.size());
                BigDecimal reduce = out.stream().map(ColdQueryVo::getSumMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (reduce != null){
                    vo.setOutMoney(reduce);
                }
            }
            vo.setList(voList);
        }
        return vo;
    }

    @Override
    public List<ColdQueryVo> selectListByQuery(HzColdRegisterQuery query) {
        return registerMapper.selectNewListByQuery(query);
    }

    @Override
    public int add(HzColdRegister register) {
        HttpServletResponse response = ServletUtils.getResponse();
        WaybillLog waybillLog = new WaybillLog();
        try{
        //2.13 冷藏登记入库时间取 支付提交时的时间
        register.setWareTime(new Date());
        Integer count = registerMapper.selectCount(new QueryWrapper<HzColdRegister>()
                .eq("waybill_code", register.getWaybillCode())
                .eq("type", register.getType()));
        if (count != 0){
            throw new CustomException("当前运单存在冷藏登记数据");
        }
        BaseAgent agent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", register.getDeptId()));
        if (agent != null) {
            if (agent.getSettleMethod() == 1) {
                BigDecimal balance = agent.getBalance() == null ? new BigDecimal(0) : agent.getBalance();
                BigDecimal subtract = balance.subtract(register.getSumMoney());
                if (subtract.compareTo(new BigDecimal(0)) < 0) {
                    throw new CustomException("当前代理人余额不足");
                } else {
                    agent.setBalance(subtract);
                    baseAgentMapper.updateBaseAgent(agent);
                    BaseBalance baseBalance = new BaseBalance();
                    baseBalance.setAgentId(agent.getId());
                    baseBalance.setBalance(agent.getBalance());
                    baseBalance.setType("减少余额");
                    baseBalance.setCreateTime(new Date());
                    baseBalance.setCreateBy("系统");
                    // todo 流水号需从银联支付接口获取
                    //baseBalance.setSerialNo();
                    baseBalance.setTradeMoney(register.getSumMoney());
                    baseBalance.setWaybillCode(register.getWaybillCode());
                    baseBalance.setRemark("冷藏申请付款");
                    baseBalanceMapper.insertBaseBalance(baseBalance);
                    register.setPayStatus(2);
                }
            } else if (agent.getSettleMethod() == 0) {
                register.setPayStatus(1);
            } else {
                if (agent.getPayMethod() == 0) {
                    register.setPayStatus(3);
                } else {
                    register.setPayStatus(4);
                }
            }
        } else {
            register.setPayStatus(4);
        }
        Mawb airWaybill = mawbMapper.selectOne(new QueryWrapper<Mawb>()
                    .eq("waybill_code", register.getWaybillCode())
                    .eq("type", register.getType())
                    .eq("is_del", 0)
                    .eq("dept_id",register.getDeptId()));
        if (airWaybill == null){
            throw new CustomException("无当前运单信息");
        }
        if ("ARR".equals(register.getType())){
            String port = airWaybill.getSourcePort() + "-KWE";
            HzArrRecordOrder orderId = tallyMapper.selectId(port,airWaybill.getWaybillCode());
            if (orderId == null){
                throw new CustomException("无当前运单信息");
            }
            HzArrItem hzArrItem = new HzArrItem();
            hzArrItem.setWaybillCode(register.getWaybillCode());
            hzArrItem.setIrId(register.getRelationId());
            hzArrItem.setTotalCharge(register.getSumMoney());
            hzArrItem.setStoreStartTime(register.getWareTime());
            hzArrItem.setStoreEndTime(register.getOutTime());
            hzArrItem.setColdStore(register.getColdStore());
            hzArrItem.setOrderId(orderId.getId());
            hzArrItem.setServiceType(2);
            hzArrItem.setIsSettle(1);
            HzArrTally hzArrTally = tallyMapper.selectOne(new QueryWrapper<HzArrTally>()
                    .eq("waybill_code", airWaybill.getWaybillCode())
                    .eq("record_order_id", orderId.getId()));
            if (hzArrTally != null){
                hzArrItem.setTallyId(hzArrTally.getId());
            }
            itemMapper.insert(hzArrItem);
        }
        //出港的冷藏登记也需要
        if ("DEP".equals(register.getType())){
            CostDetail detail = new CostDetail();
            detail.setWaybillCode(register.getWaybillCode());
            detail.setColdStore(register.getColdStore());
            detail.setIrId(register.getRelationId());
            detail.setTotalCharge(register.getSumMoney());
            detail.setStoreStartTime(register.getWareTime());
            detail.setStoreEndTime(register.getOutTime());
            detail.setDeptId(airWaybill.getDeptId());
            detail.setServiceType(2);
            detail.setQuantity(register.getTimeLen().toString());
            detail.setSettleDepWeight(new BigDecimal(register.getTimeLen()));
            detail.setType(0);
            detail.setIsSettle(1);
            costDetailMapper.insert(detail);
        }
        register.setStatus(0);
        register.setApplyTime(new Date());
        register.setApplyUser(SecurityUtils.getNickName());
        if (register.getDeptId() == null){
            register.setDeptId(airWaybill.getDeptId());
        }
        BigDecimal payMoney = airWaybill.getPayMoney() == null ? new BigDecimal(0) : airWaybill.getPayMoney();
        BigDecimal sumMoney = register.getSumMoney() == null ? new BigDecimal(0) : register.getSumMoney();
        airWaybill.setPayMoney(payMoney.add(sumMoney));
        mawbMapper.updateById(airWaybill);
        //运单日志的新增
        waybillLog = waybillLogService.getWaybillLog(
                airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                null, null, null,
                register, null, 0, null, new Date(),
                "冷藏登记 新增,申请时长"+register.getTimeLen()+",支付金额:"+register.getSumMoney(),
                register.getType(), null);
            register.setUpdateTime(new Date());
        return registerMapper.insert(register);
        }catch (Exception e){
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" +  "," +
                            "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        }finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }

    @Override
    public HzColdRegister selectCargoName(String waybillCode, String type, Long deptId) {
        HzColdRegister hzColdRegister = mawbMapper.selectCargoName(waybillCode, type, deptId);
        if (hzColdRegister == null){
            throw new CustomException("该运单不存在");
        }
        if ("DEP".equals(type)){
            HzCollectWaybill collectWaybill = collectWaybillMapper.selectOne(new QueryWrapper<HzCollectWaybill>()
                    .eq("waybill_id", hzColdRegister.getId())
                    .orderByDesc("collect_time")
                    .last("limit 1"));
            if (collectWaybill == null){
                throw new CustomException("当前运单未收运入库");
            }
            hzColdRegister.setWareTime(collectWaybill.getCollectTime());
        }else {
            HzArrTally hzArrTally = tallyMapper.selectOne(new QueryWrapper<HzArrTally>()
                    .eq("waybill_code", waybillCode)
                    .orderByDesc("tally_time")
                    .last("limit 1"));
            if (hzArrTally == null){
                throw new CustomException("当前运单未理货");
            }
            hzColdRegister.setWareTime(hzArrTally.getTallyTime());
        }
        hzColdRegister.setId(null);
        return hzColdRegister;
    }

    @Override
    public TotalChargeVo countSum(ColdRegisterVo vo) {
        TotalChargeVo totalChargeVo = new TotalChargeVo();
        Mawb airWaybill = mawbMapper.selectOne(new QueryWrapper<Mawb>()
                .eq("waybill_code", vo.getWaybillCode())
                .eq("type", vo.getType())
                .eq("is_del", 0));
        // airWaybill NPE
        Date date = new Date();
        if ("ARR".equals(vo.getType())){
            String port = airWaybill.getSourcePort() + "-KWE";
            HzArrRecordOrder orderId = tallyMapper.selectId(port,airWaybill.getWaybillCode());
            if (orderId == null){
                throw new CustomException("无当前航班下运单的库单信息");
            }
        }
        List<HzChargeItems> hzChargeItems = chargeItemsMapper.selectList(new QueryWrapper<HzChargeItems>()
                .eq("operation_type",vo.getType())
                .le("start_effective_time",date)
                .ge("end_effective_time",date)
                .eq("is_del",0));
        BigDecimal costSum = new BigDecimal(0);
        for (HzChargeItems hzChargeItem : hzChargeItems) {
            HzChargeIrRelation relation = getHzChargeIrRelation(airWaybill, hzChargeItem);
            if (relation == null) continue;
            totalChargeVo.setRelationId(relation.getId());
            List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>().eq("ir_id",relation.getId()));
            if (CollectionUtils.isEmpty(itemRules)){
                totalChargeVo.setCostSum("0");
                return totalChargeVo;
            }
            long millisInDay = vo.getTimeLen() * 60 * 60 * 1000L;
            Date plusHours = new Date(vo.getStoreStartTime().getTime() + millisInDay);
            HzArrItem item = new HzArrItem();
            item.setColdStore(vo.getColdStore());
            item.setStoreStartTime(vo.getStoreStartTime());
            item.setStoreEndTime(plusHours);
            BillingRule rule = BillingRuleFactory.createRule("ColdStorageBillingRule.class");
            BillRuleVo vo1 = rule.calculateFee(itemRules, airWaybill.getChargeWeight(), 1, item);
            BigDecimal bigDecimal = BigDecimalRoundUtils.bigDecimalRound(hzChargeItem.getRoundRule(), vo1.getTotalCharge());
            costSum = costSum.add(bigDecimal);
        }
        totalChargeVo.setCostSum(costSum.toString());
        return totalChargeVo;
    }

    @Override
    public ColdQueryVo getInfo(Long id) {
        ColdQueryVo vo = new ColdQueryVo();
        HzColdRegister hzColdRegister = registerMapper.selectById(id);
        BeanUtils.copyProperties(hzColdRegister,vo);
        return vo;
    }

    @Override
    public List<String> getWaybillCodeByFour(String waybillCode) {
        return registerMapper.getWaybillCodeByFour(waybillCode);
    }

    @Nullable
    private HzChargeIrRelation getHzChargeIrRelation(Mawb airWaybill, HzChargeItems hzChargeItems) {
        List<HzChargeIrRelation> relations = relationMapper.selectColdCharge(hzChargeItems.getId());
        List<HzChargeIrRelation> ruleList = getHzChargeIrRelations(airWaybill, relations);
        if (CollectionUtils.isEmpty(ruleList)) {
            return null;
        }
        return ruleList.stream().max(Comparator.comparing(HzChargeIrRelation::getPriority)).orElse(null);
    }

    @NotNull
    private List<HzChargeIrRelation> getHzChargeIrRelations(Mawb airWaybill, List<HzChargeIrRelation> relations) {
        int maxMatchCount = 0;
        List<HzChargeIrRelation> ruleList = new ArrayList<>();
        for (HzChargeIrRelation hzChargeRule : relations) {
            if (hzChargeRule.getIsSouth() == 1){
                continue;
            }
            if (hzChargeRule.getIsExit() == 1){
                continue;
            }
            if (!hzChargeRule.getCrossAir().equals(airWaybill.getCrossAir())){
                continue;
            }
            if (StringUtils.isNotEmpty(hzChargeRule.getNoCharge()) && !hzChargeRule.getNoCharge().contains(airWaybill.getDeptId().toString())){
                continue;
            }
            if (StringUtils.isNotEmpty(hzChargeRule.getPrefix()) && !hzChargeRule.getPrefix().contains(airWaybill.getWaybillCode().substring(4,7))){
                continue;
            }
            if (StringUtils.isNotEmpty(hzChargeRule.getCategory()) && !hzChargeRule.getCategory().contains(airWaybill.getCategoryName())){
                continue;
            }
            int matchCount = 0;
            int cargoMatchCount = isCargoCodeMatch(hzChargeRule, airWaybill.getCargoName());
            if (cargoMatchCount >= 0) {
                matchCount += cargoMatchCount;
            }

            if (matchCount > 0) {
                if (matchCount > maxMatchCount) {
                    maxMatchCount = matchCount;
                    ruleList.clear();
                    ruleList.add(hzChargeRule);
                } else if (matchCount == maxMatchCount) {
                    ruleList.add(hzChargeRule);
                }
            }
        }
        return ruleList;
    }
    private int isCargoCodeMatch(HzChargeIrRelation hzChargeRule, String waybillCargoCode) {
        if (StringUtils.isEmpty(hzChargeRule.getCargoName())) {
            return 1;
        }else if (StringUtils.isEmpty(hzChargeRule.getCategory())){
            if (hzChargeRule.getCargoName().contains(waybillCargoCode)){
                return 1;
            }
            return 0;
        }
        List<IrRelationVo> relationVos = Arrays.stream(hzChargeRule.getCargoName().split(","))
                .map(cargoCodeMapper::selectIrByName)
                .collect(Collectors.toList());

        List<IrRelationVo> cargoNames = Arrays.stream(hzChargeRule.getCategory().split(","))
                .flatMap(category -> cargoCodeMapper.selectListByCategory(Collections.singletonList(category)).stream())
                .collect(Collectors.toList());

        Map<String, List<IrRelationVo>> relationVosMap = relationVos.stream()
                .collect(Collectors.groupingBy(IrRelationVo::getCategoryCode));

        List<IrRelationVo> combinedList = new ArrayList<>();
        Set<String> addedCodes = new HashSet<>();
        for (IrRelationVo vo : cargoNames) {
            String code = vo.getCategoryCode();
            if (relationVosMap.containsKey(code) && !addedCodes.contains(code)) {
                combinedList.addAll(relationVosMap.get(code));
                addedCodes.add(code);
            } else if (!addedCodes.contains(code)) {
                combinedList.add(vo);
            }
        }
        List<String> collect1 = combinedList.stream().map(IrRelationVo::getCode).collect(Collectors.toList());
        if (collect1.stream().anyMatch(cargoCode -> cargoCode.equals(waybillCargoCode))) {
            String category = hzChargeRule.getCategory();
            StringBuilder stringBuffer = new StringBuilder();
            if(category != null){
                String[] split = category.split(",");
                for (String categoryCode:split) {
                    List<BaseCargoCode> cargoCodeList = cargoCodeMapper.selectList(new QueryWrapper<>(new BaseCargoCode())
                            .eq("category_code", categoryCode)
                            .eq("is_del",0));
                    if(cargoCodeList!=null && cargoCodeList.size() > 0){
                        for (BaseCargoCode e:cargoCodeList) {
                            stringBuffer.append(e.getCode());
                        }
                    }
                }
            }else{
                return 1;
            }
            if (stringBuffer.toString().contains(waybillCargoCode)) {
                return 2;
            } else {
                return 1;
            }
        }
        return 0;
    }
}
