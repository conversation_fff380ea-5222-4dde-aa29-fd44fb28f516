package com.gzairports.common.charge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.departure.domain.vo.IdsVo;
import com.gzairports.common.charge.domain.HzChargeItemRule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 收费项目计费规则表Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@Mapper
public interface HzChargeItemRuleMapper extends BaseMapper<HzChargeItemRule> {

    /**
     * 根据收费项目id查询规则id
     * @param id 收费项目id
     * @return 规则id集合
     */
    List<Long> selectRuleIdsByItemId(Long id);

    /**
     * 根据收费项目id查询冷藏规则id
     * @param id 收费项目id
     * @return 冷藏规则id集合
     */
    List<Long> selectColdRuleIdsByItemId(Long id);

    /**
     * 根据项目id查询冷藏规则
     * @param itemIds 项目id
     * @return 冷藏规则
     */
    List<IdsVo> selectColdRuleByIds(@Param("itemIds") List<Long> itemIds);
}
