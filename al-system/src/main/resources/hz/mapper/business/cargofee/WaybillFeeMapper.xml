<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.cargofee.mapper.WaybillFeeHzMapper">

    <select id="selectListByQuery" resultType="com.gzairports.hz.business.cargofee.domain.WaybillFeeHz">
        select wwf.id, wwf.pay_time, wwf.pay_money, wwf.settle_time, wwf.settle_money, wwf.refund, wwf.status, wwf.waybill_code, wwf.serial_no,
               sd.dept_name as agent
        from wl_waybill_fee wwf
        left join sys_dept sd on sd.dept_id = wwf.dept_id
        <where>
            <!--<if test="deptId != null">
                and wwf.dept_id = #{deptId}
            </if>-->
            <if test="startPayTime != null">
                and wwf.pay_time <![CDATA[>=]]> #{startPayTime}
            </if>
            <if test="endPayTime != null">
                and wwf.pay_time <![CDATA[<=]]> #{endPayTime}
            </if>
            <if test="startSettleTime != null">
                and wwf.settle_time <![CDATA[>=]]> #{startSettleTime}
            </if>
            <if test="endSettleTime != null">
                and wwf.settle_time <![CDATA[<=]]> #{endSettleTime}
            </if>
            <if test="waybillCode != null and waybillCode != ''">
                and wwf.waybill_code like '%${waybillCode}%'
            </if>
            <if test="serialNo != null and serialNo != ''">
                and wwf.serial_no like '%${serialNo}%'
            </if>
            <if test="status != null">
                and wwf.status = #{status}
            </if>
            <if test="agent != null and agent != ''">
                and wwf.dept_id = #{agent}
            </if>
        </where>
    </select>
</mapper>