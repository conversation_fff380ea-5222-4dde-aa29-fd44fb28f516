package com.gzairports.common.infoquery.service;

import com.gzairports.hz.business.departure.domain.query.FlightLoadQuery;
import com.gzairports.hz.business.departure.domain.vo.FlightLoadVo;
import com.gzairports.hz.business.departure.domain.vo.FormalManifestVo;

import java.util.List;

/**
 * 舱单查询 服务层
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
public interface IManifestService {

    List<FlightLoadVo> selectList(FlightLoadQuery query, String[] split);

    FormalManifestVo infoFormalManifest(Long id);
}
