package com.gzairports.web.controller.reporter;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.domain.entity.SysRole;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.system.service.ISysRoleService;
import com.gzairports.hz.business.reporter.domain.HzReportFormula;
import com.gzairports.hz.business.reporter.domain.HzReportSet;
import com.gzairports.hz.business.reporter.domain.query.HzReportSetDto;
import com.gzairports.hz.business.reporter.domain.query.HzReportSetQuery;
import com.gzairports.hz.business.reporter.service.HzReportSetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @author: lan
 * @create: 2025-03-05 17:21
 **/

@RestController
@RequestMapping("/hz/reportSet")
@Api(tags = "货站报表设置")
public class HzReportSetController extends BaseController {
    @Autowired
    private HzReportSetService hzReportSetService;

    @Autowired
    private ISysRoleService roleService;

    /**
     * 查询报表设置列表
     * */
    @GetMapping("/list")
    @ApiOperation(value = "查询报表设置列表")
    public TableDataInfo list(HzReportSetQuery query){
        startPage();
        List<HzReportSet> list = hzReportSetService.selectHzReportSetList(query);
        return getDataTable(list);
    }

    /**
     * 发布
     * */
    @GetMapping("/publish/{id}")
    @ApiOperation(value = "报表发布")
    public AjaxResult publishReport(@PathVariable Long id){
      return AjaxResult.success("操作成功",hzReportSetService.publishReport(id));
    }

    /**
     * 下架
     * */
    @GetMapping("/down/{id}")
    @ApiOperation(value = "报表下架")
    public AjaxResult downReport(@PathVariable Long id){
        return AjaxResult.success("操作成功",hzReportSetService.downReport(id));
    }

    /**
     * 逻辑删除
     * */
    @GetMapping("/del/{id}")
    @ApiOperation(value = "报表删除")
    public AjaxResult delReport(@PathVariable Long id){
        return AjaxResult.success("操作成功",hzReportSetService.delReport(id));
    }

    /** 查询货站所有角色 */
    @GetMapping("/roleList")
    @ApiOperation(value = "查询货站所有角色")
    public AjaxResult roleList(){
        return AjaxResult.success("操作成功",roleService.selectRoleList(new SysRole()));
    }

    /** 查询货站报表设置表对应的字段 */
    @GetMapping("/fieldList/{type}")
    @ApiOperation(value = "查询货站报表设置表对应的字段")
    public AjaxResult fieldList(@PathVariable Long type){
        return AjaxResult.success("操作成功",hzReportSetService.selectTableList(type));
    }

    /** 新增报表 */
    @PostMapping("/addSet")
    @ApiOperation(value = "新增报表设置")
    public AjaxResult addSet(@RequestBody HzReportSetDto dto){
        return AjaxResult.success("操作成功",hzReportSetService.insertReportSet(dto));
    }

    /** 查询报表详情 */
    @GetMapping("/infoSet/{id}")
    @ApiOperation(value = "查询报表详情")
    public AjaxResult infoSet(@PathVariable Long id){
        return AjaxResult.success("操作成功",hzReportSetService.infoSet(id));
    }

    /** 更新报表 */
    @PostMapping("/updateSet")
    @ApiOperation(value = "更新报表设置")
    public AjaxResult updateSet(@RequestBody HzReportSetDto dto){
        return AjaxResult.success("操作成功",hzReportSetService.updateSet(dto));
    }

    /** 新增计算字段 */
    @PostMapping("/addFormula")
    @ApiOperation(value = "新增计算字段")
    public AjaxResult addFormula(@RequestBody HzReportFormula dto){
        return AjaxResult.success("操作成功",hzReportSetService.addFormula(dto));
    }

}
