package com.gzairports.common.infoquery.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.business.departure.domain.FlightInfo;
import com.gzairports.common.infoquery.domain.Flight;
import com.gzairports.common.infoquery.domain.query.FlightQuery;

import java.util.List;

/**
 * 航班 服务层
 *
 * <AUTHOR>
 */
public interface IFlightService {

    /**
     * 查询航班列表
     *
     * @param query 航班查询信息
     * @return 航班集合
     */
    List<Flight> selectFlightList(FlightQuery query);

    /**
     * 新增航班
     * @param flight 航班参数
     * @return 结果
     */
    int add(Flight flight);


    /**
     * 修改航班数据
     * */
    int update(Flight flight);


    /**
     * 查询航班信息
     *
     * @param query 航班查询参数
     * @return 航班数据
     */
    Flight getCraft(FlightQuery query);


    List<Flight> selectFlightBookList(FlightQuery query);
}
