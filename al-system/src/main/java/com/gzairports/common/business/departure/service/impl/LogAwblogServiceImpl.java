package com.gzairports.common.business.departure.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.annotation.DataSource;
import com.gzairports.common.business.departure.domain.vo.LogAwblog;
import com.gzairports.common.business.departure.mapper.LogAwblogMapper;
import com.gzairports.common.business.departure.service.ILogAwblogService;
import com.gzairports.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 运单日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-09-25
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class LogAwblogServiceImpl extends ServiceImpl<LogAwblogMapper, LogAwblog> implements ILogAwblogService
{
    @Autowired
    private LogAwblogMapper logAwblogMapper;


    @Override
    public Map<String,List<LogAwblog>> listByStocks(Collection<String> stocknoSet) {
        if(stocknoSet==null ||stocknoSet.isEmpty()){
            return new HashMap<>();
        }
        Map<String,List<LogAwblog>> rMap = new HashMap<>();
        LambdaQueryWrapper<LogAwblog> query = Wrappers.lambdaQuery(LogAwblog.class).in(LogAwblog::getStockno, stocknoSet);
        List<LogAwblog> awblogs = logAwblogMapper.selectList(query);
        for (LogAwblog awblog : awblogs) {
            List<LogAwblog> list = rMap.computeIfAbsent(awblog.getStockno(), k -> new ArrayList<>());
            list.add(awblog);
        }
        return rMap;
    }

    @Override
    public Map<String,List<LogAwblog>>  listByTime(Date startTime, Date endTime) {
        Map<String,List<LogAwblog>> rMap = new HashMap<>();
        LambdaQueryWrapper<LogAwblog> query = Wrappers.lambdaQuery(LogAwblog.class).ge(LogAwblog::getOpetime, startTime).le(LogAwblog::getOpetime, endTime);
        List<LogAwblog> awblogs = logAwblogMapper.selectList(query);
        for (LogAwblog awblog : awblogs) {
            List<LogAwblog> list = rMap.get(awblog.getStockno());
            if(list == null){
                list = new ArrayList<>();
                rMap.put(awblog.getStockno(),list);
            }
            list.add(awblog);
        }
        return rMap;
    }
}
