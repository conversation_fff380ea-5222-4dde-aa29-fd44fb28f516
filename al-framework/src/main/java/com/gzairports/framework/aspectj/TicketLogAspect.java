package com.gzairports.framework.aspectj;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.gzairports.common.annotation.WlTicketLog;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.enums.BusinessStatus;
import com.gzairports.common.enums.HttpMethod;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.ServletUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.framework.manager.AsyncManager;
import com.gzairports.framework.manager.factory.AsyncFactory;
import com.gzairports.wl.ticket.domain.TicketGrant;
import com.gzairports.wl.ticket.domain.TicketLog;
import com.gzairports.wl.ticket.mapper.TicketGrantMapper;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.Map;

/**
 * 单证日志记录处理
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class TicketLogAspect {

    @Autowired
    private TicketGrantMapper grantMapper;

    private static final Logger log = LoggerFactory.getLogger(TicketLogAspect.class);
    
    /**
     * 处理完请求后执行
     *
     * @param joinPoint 切点
     */
    @AfterReturning(pointcut = "@annotation(controllerLog)", returning = "jsonResult")
    public void doAfterReturning(JoinPoint joinPoint, WlTicketLog controllerLog, Object jsonResult)
    {
        handleLog(joinPoint, controllerLog, null, jsonResult);
    }

    /**
     * 拦截异常操作
     *
     * @param joinPoint 切点
     * @param e 异常
     */
    @AfterThrowing(value = "@annotation(controllerLog)", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, WlTicketLog controllerLog, Exception e)
    {
        handleLog(joinPoint, controllerLog, e, null);
    }

    protected void handleLog(final JoinPoint joinPoint, WlTicketLog controllerLog, final Exception e, Object jsonResult)
    {
        try
        {
            // 获取当前的用户
            LoginUser loginUser = SecurityUtils.getLoginUser();

            // *========数据库日志=========*//
            TicketLog ticketLog = new TicketLog();
            ticketLog.setStatus(BusinessStatus.SUCCESS.ordinal());
            if (loginUser != null)
            {
                ticketLog.setOperName(loginUser.getUsername());
            }

            if (e != null)
            {
                ticketLog.setStatus(BusinessStatus.FAIL.ordinal());
                ticketLog.setErrorMsg(StringUtils.substring(e.getMessage(), 0, 2000));
            }
            // 设置请求方式
            ticketLog.setRequestMethod(ServletUtils.getRequest().getMethod());
            // 处理设置注解上的参数
            getControllerMethodDescription(joinPoint, controllerLog, ticketLog, jsonResult);
            // 保存数据库
            AsyncManager.me().execute(AsyncFactory.recordTicket(ticketLog));
        }
        catch (Exception exp)
        {
            // 记录本地异常日志
            log.error("异常信息:{}", exp.getMessage());
            exp.printStackTrace();
        }
        finally
        {
            System.out.println("");
        }
    }

    /**
     * 获取注解中对方法的描述信息 用于Controller层注解
     *
     * @param log 日志
     * @param ticketLog 操作日志
     * @throws Exception
     */
    public void getControllerMethodDescription(JoinPoint joinPoint, WlTicketLog log, TicketLog ticketLog, Object jsonResult) throws Exception
    {
        // 设置action动作
        ticketLog.setBusinessType(log.businessType().ordinal());
        String substring = StringUtils.substring(ServletUtils.getRequest().getRequestURI(), 0, 255);
        int index = substring.lastIndexOf("/");
        String id = substring.substring(index + 1);
        if (ticketLog.getBusinessType() == 3){
            ticketLog.setTicketId(Long.valueOf(id));
        }
        if (ticketLog.getBusinessType() == 13){
            TicketGrant record = grantMapper.selectById(Long.valueOf(id));
            if (record != null){
                ticketLog.setTicketId(record.getTicketId());
            }
        }
        // 获取参数的信息，传入到数据库中。
        setRequestValue(joinPoint, ticketLog);
        // 是否需要保存response，参数和值
        if (StringUtils.isNotNull(jsonResult))
        {
            String s = JSON.toJSONString(jsonResult);
            ticketLog.setJsonResult(StringUtils.substring(s, 0, 2000));
            JSONObject jsonObject = JSON.parseObject(s);
            if (!StringUtils.isEmpty(jsonObject.getString("data"))){
                ticketLog.setTicketId(Long.valueOf(jsonObject.getString("data")));
            }
        }
    }

    /**
     * 获取请求的参数，放到log中
     *
     * @param ticketLog 操作日志
     * @throws Exception 异常
     */
    private void setRequestValue(JoinPoint joinPoint, TicketLog ticketLog) throws Exception
    {
        Map<?, ?> paramsMap = ServletUtils.getParamMap(ServletUtils.getRequest());
        String requestMethod = ticketLog.getRequestMethod();
        if (StringUtils.isEmpty(paramsMap)
                && (HttpMethod.PUT.name().equals(requestMethod) || HttpMethod.POST.name().equals(requestMethod)))
        {
            String params = argsArrayToString(joinPoint.getArgs());
            ticketLog.setOperParam(StringUtils.substring(params, 0, 2000));
            JSONObject jsonObject = JSON.parseObject(params);
            String id = jsonObject.getString("id");
            if (!StringUtils.isEmpty(id)){
                ticketLog.setTicketId(Long.valueOf(id));
            }
        }
        else
        {
            ticketLog.setOperParam(StringUtils.substring(JSON.toJSONString(paramsMap), 0, 2000));
        }
    }

    /**
     * 参数拼装
     */
    private String argsArrayToString(Object[] paramsArray)
    {
        String params = "";
        if (paramsArray != null && paramsArray.length > 0)
        {
            for (Object o : paramsArray)
            {
                if (StringUtils.isNotNull(o) && !isFilterObject(o))
                {
                    try
                    {
                        String jsonObj = JSON.toJSONString(o);
                        params += jsonObj.toString() + " ";
                    }
                    catch (Exception e)
                    {
                    }
                }
            }
        }
        return params.trim();
    }

    /**
     * 判断是否需要过滤的对象。
     *
     * @param o 对象信息。
     * @return 如果是需要过滤的对象，则返回true；否则返回false。
     */
    @SuppressWarnings("rawtypes")
    public boolean isFilterObject(final Object o)
    {
        Class<?> clazz = o.getClass();
        if (clazz.isArray())
        {
            return clazz.getComponentType().isAssignableFrom(MultipartFile.class);
        }
        else if (Collection.class.isAssignableFrom(clazz))
        {
            Collection collection = (Collection) o;
            for (Object value : collection)
            {
                return value instanceof MultipartFile;
            }
        }
        else if (Map.class.isAssignableFrom(clazz))
        {
            Map map = (Map) o;
            for (Object value : map.entrySet())
            {
                Map.Entry entry = (Map.Entry) value;
                return entry.getValue() instanceof MultipartFile;
            }
        }
        return o instanceof MultipartFile || o instanceof HttpServletRequest || o instanceof HttpServletResponse
                || o instanceof BindingResult;
    }
}
