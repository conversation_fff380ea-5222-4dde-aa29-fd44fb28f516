package com.gzairports.hz.business.departure.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.*;
import com.gzairports.common.basedata.mapper.*;
import com.gzairports.common.business.arrival.domain.FlightLoadWaybill;
import com.gzairports.common.business.arrival.mapper.FlightInfoMapper;
import com.gzairports.common.business.arrival.mapper.FlightLoadWaybillMapper;
import com.gzairports.common.business.departure.domain.CostDetail;
import com.gzairports.common.business.departure.domain.FlightInfo;
import com.gzairports.common.business.departure.mapper.CostDetailMapper;
import com.gzairports.common.charge.domain.HzChargeIrRelation;
import com.gzairports.common.charge.domain.HzChargeItemRule;
import com.gzairports.common.charge.domain.HzChargeItems;
import com.gzairports.common.charge.domain.vo.BillRuleVo;
import com.gzairports.common.charge.mapper.HzChargeIrRelationMapper;
import com.gzairports.common.charge.mapper.HzChargeItemRuleMapper;
import com.gzairports.common.charge.mapper.HzChargeItemsMapper;
import com.gzairports.common.charge.service.BillingRule;
import com.gzairports.common.charge.service.impl.BillingRuleFactory;
import com.gzairports.common.config.AddressMsgVO;
import com.gzairports.common.config.FFMRedisOperator;
import com.gzairports.common.config.FFMScheduler;
import com.gzairports.common.enums.FFMStatus;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.common.log.service.IWaybillLogService;
import com.gzairports.common.pdf.PdfPrintHelper;
import com.gzairports.common.system.mapper.SysConfigMapper;
import com.gzairports.common.utils.*;
import com.gzairports.hz.business.arrival.domain.vo.TallyManifestVo;
import com.gzairports.hz.business.cable.domain.HzCable;
import com.gzairports.hz.business.cable.domain.HzCableAddress;
import com.gzairports.hz.business.cable.domain.vo.*;
import com.gzairports.hz.business.cable.mapper.HzCableAddressMapper;
import com.gzairports.hz.business.cable.mapper.HzCableMapper;
import com.gzairports.hz.business.cable.service.impl.HttpServiceImpl;
import com.gzairports.hz.business.departure.domain.*;
import com.gzairports.hz.business.departure.domain.query.GroupCargoQuery;
import com.gzairports.hz.business.departure.domain.vo.*;
import com.gzairports.hz.business.departure.mapper.*;
import com.gzairports.hz.business.departure.service.IRepeatWeightService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Marker;
import org.slf4j.MarkerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 复重Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-06
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RepeatWeightServiceImpl extends ServiceImpl<RepeatWeightMapper, HzDepRepeatWeight> implements IRepeatWeightService {

    @Autowired
    private RepeatWeightMapper repeatWeightMapper;

    @Autowired
    private HzDepGroupUldMapper hzDepGroupUldMapper;

    @Autowired
    private FlightLoadMapper flightLoadMapper;

    @Autowired
    private SysConfigMapper configMapper;

    @Autowired
    private UldMapper uldMapper;

    @Autowired
    private HzDepGroupUldWaybillMapper groupUldWaybillMapper;

    @Autowired
    private AllAirWaybillMapper airWaybillMapper;

    @Autowired
    private SpecialTraceMapper specialTraceMapper;

    @Autowired
    private HzColdRegisterMapper coldRegisterMapper;

    @Autowired
    private CostDetailMapper costDetailMapper;

    @Autowired
    private TruckMapper truckMapper;

    @Autowired
    private IWaybillLogService waybillLogService;

    @Autowired
    private FlightInfoMapper flightInfoMapper;

    @Autowired
    private HzDepGroupUldMapper groupUldMapper;

    @Autowired
    private HzDepGroupWaybillMapper groupWaybillMapper;

    @Autowired
    private HzCollectWeightMapper collectWeightMapper;

    @Autowired
    private HzCollectWaybillMapper collectWaybillMapper;

    @Autowired
    private FlightLoadUldMapper flightLoadUldMapper;

    @Autowired
    private FlightLoadWaybillMapper loadWaybillMapper;

    @Autowired
    private HzChargeItemRuleMapper itemRuleMapper;

    @Autowired
    private HzChargeIrRelationMapper relationMapper;

    @Autowired
    private HzChargeItemsMapper chargeItemsMapper;

    @Autowired
    private BaseAgentMapper baseAgentMapper;

    @Autowired
    private BaseBalanceMapper baseBalanceMapper;

    @Autowired
    private HzColdRegisterMapper registerMapper;

    @Autowired
    private HzCableMapper hzCableMapper;

    @Autowired
    private HzCableAddressMapper cableAddressMapper;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private HttpServiceImpl httpService;

    @Autowired
    private CarrierMapper carrierMapper;

    @Value("${hzCable.account}")
    private String account;

    @Value("${hzCable.loginUrl}")
    private String loginUrl;

    @Value("${hzCable.getMsg}")
    private String getMsg;

    @Value("${hzCable.FTPAddress}")
    private String ftpAddress;

    private static final Marker CABLE_ERROR_MARKER  = MarkerFactory.getMarker("CABLE-ERROR");

    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMdd");
    private static final SimpleDateFormat FORMAT = new SimpleDateFormat("yyyy-MM-dd");

    private static final Pattern LETTERS_PATTERN = Pattern.compile("([A-Za-z]+)(\\d+)");

    private final FFMRedisOperator ffmRedisOperator;
    private final FFMScheduler ffmScheduler;

    /**
     * 查询复重列表
     * @param query 查询条件
     * @return 结果
     */
    @Override
    public List<RepeatWeightVo> selectList(GroupCargoQuery query) {
        List<RepeatWeightVo> repeatWeightVos = repeatWeightMapper.selectListByQuery(query);
        for (RepeatWeightVo repeatWeightVo:repeatWeightVos) {
            String status = FLIGHT_STATUS.get(repeatWeightVo.getState());
            repeatWeightVo.setStatus(status);
        }
        return repeatWeightVos;
    }

    /**
     * 复磅
     * @param id 航班配载id
     * @return 复磅列表
     */
    @Override
    public List<HzDepRepeatWeight> weight(Long id) {
        List<Long> list = flightLoadMapper.selectLegIdByVo(id);
        return repeatWeightMapper.selectList(new QueryWrapper<HzDepRepeatWeight>()
                .in("flight_load_id", list));
    }

    /**
     * 板箱复重详情
     * @param id 板箱复重id
     * @return 详情
     */
    @Override
    public WeightInfoVo getInfo(Long id) {
        WeightInfoVo vo = repeatWeightMapper.getInfo(id);
        List<HzDepRepeatWeight> repeatWeights = repeatWeightMapper.selectList(new QueryWrapper<HzDepRepeatWeight>()
                .eq("flight_load_id", vo.getFlightLoadId()));
        if (!CollectionUtils.isEmpty(repeatWeights)){
            BigDecimal totalRealityWeight = repeatWeights.stream()
                    .map(HzDepRepeatWeight::getWeight)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setTotalRealityWeight(totalRealityWeight);
            BigDecimal totalFileWeight = repeatWeights.stream().map(HzDepRepeatWeight::getFileWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setTotalFileWeight(totalFileWeight);
            Integer totalQuantity = repeatWeights.stream().map(HzDepRepeatWeight::getQuantity).filter(Objects::nonNull).reduce(0, Integer::sum);
            vo.setQuantity(totalQuantity);
        }
        if (vo.getWeight() != null && vo.getFileWeight() != null && vo.getFileWeight().compareTo(BigDecimal.ZERO) != 0){
            BigDecimal abs = vo.getWeight().subtract(vo.getFileWeight()).abs();
            BigDecimal errorRate = abs.divide(vo.getFileWeight(), 2, RoundingMode.DOWN).multiply(new BigDecimal(100));
            vo.setErrorRate(errorRate.toString());
        }
        return vo;
    }

    /**
     * 编辑
     * @param repeatWeight 编辑参数
     * @return 结果
     */
    @Override
    public int edit(HzDepRepeatWeight repeatWeight) {
        HzDepGroupUld groupUld = hzDepGroupUldMapper.selectById(repeatWeight.getGroupUldId());
        if (groupUld != null){
            groupUld.setRepeatWeight(repeatWeight.getWeight());
            hzDepGroupUldMapper.updateById(groupUld);
        }
        HzDepRepeatWeight hzDepRepeatWeight = repeatWeightMapper.selectById(repeatWeight.getId());
        hzDepRepeatWeight.setWeight(repeatWeight.getWeight());
        hzDepRepeatWeight.setBoardWeight(repeatWeight.getBoardWeight());
        hzDepRepeatWeight.setBoardCargoWeight(repeatWeight.getBoardCargoWeight());
        hzDepRepeatWeight.setPlateWeight(repeatWeight.getPlateWeight());
        return repeatWeightMapper.updateById(hzDepRepeatWeight);
    }

    /**
     * 复磅完成
     * @param id 航班配载id
     * @return 结果
     */
    @Override
    public int finishWeight(Long id) {
        //1.22 前端传的是flight_id 而不是flight_load_id
        List<FlightLoad> flightLoadList = flightLoadMapper.selectList(new QueryWrapper<FlightLoad>().eq("flight_id", id));
        List<Long> flightLoadIdList = flightLoadList.stream().map(FlightLoad::getId).collect(Collectors.toList());
        List<HzDepRepeatWeight> repeatWeights = repeatWeightMapper.selectList(new QueryWrapper<HzDepRepeatWeight>()
                .in("flight_load_id", flightLoadIdList));
        ArrayList<WaybillLog> waybillLogs = new ArrayList<>();
        HttpServletResponse response = ServletUtils.getResponse();
        try{
        repeatSteps(flightLoadIdList, repeatWeights);
        FlightInfo flightInfo = flightInfoMapper.selectById(id);
            List<HzDepGroupUld> groupUlds = groupUldMapper.selectList(new QueryWrapper<HzDepGroupUld>()
                    .in("flight_load_id", flightLoadIdList));
            for (HzDepGroupUld groupUld:groupUlds) {
                List<HzDepGroupUldWaybill> groupUldWaybills = groupUldWaybillMapper.selectList(new QueryWrapper<HzDepGroupUldWaybill>()
                        .eq("group_uld_id", groupUld.getId()));
                //运单日志
                for (HzDepGroupUldWaybill groupUldWaybill : groupUldWaybills) {
                    WaybillLog waybillLog = waybillLogService.getWaybillLog(
                            groupUldWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                            groupUldWaybill.getWeight().toString(), groupUldWaybill.getQuantity().toString(), flightInfo.getAirWays() + flightInfo.getFlightNo(),
                            id.toString(), null, 0, null, new Date(),
                            "复重，ULD号:" + groupUld.getUld(), "A".equals(flightInfo.getIsOffin()) ? "ARR" : "DEP", groupUld.getUld());
                    waybillLogs.add(waybillLog);
                }
            }
            List<HzDepGroupWaybill> groupWaybills = groupWaybillMapper.selectList(new QueryWrapper<HzDepGroupWaybill>()
                    .eq("flight_load_id", id));
            if (!CollectionUtils.isEmpty(groupWaybills)) {
                for (HzDepGroupWaybill groupWaybill : groupWaybills) {
                    WaybillLog waybillLog = waybillLogService.getWaybillLog(
                            groupWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                            groupWaybill.getWeight().toString(), groupWaybill.getQuantity().toString(), flightInfo.getAirWays() + flightInfo.getFlightNo(),
                            id.toString(), null, 0, null, new Date(),
                            "复重，ULD号:" + null, "A".equals(flightInfo.getIsOffin()) ? "ARR" : "DEP", null);
                    waybillLogs.add(waybillLog);
                }
            }
            flightLoadList.forEach(e->{
                e.setState("been_weight");
                e.setWeightTime(new Date());
                flightLoadMapper.updateById(e);
            });
            int isComp = flightInfoMapper.selectIsComp(id);
            if (isComp == 0) {
                String token = getToken();
                String sendAddress = configMapper.selectValue("dep.sendAddress");
                if (StringUtils.isNotNull(sendAddress)){
                    List<Long> list = flightLoadMapper.selectLegIdByVo(id);
                    List<AddressMsgVO> msgVoList = new ArrayList<>();
                    for (Long aLong : list) {
                        List<FlightLoadWaybill> waybillList = loadWaybillMapper.selectWaybillIdsByFlightLoadId(aLong);
                        for (FlightLoadWaybill loadWaybill : waybillList) {
                            AddressMsgVO addressMsgVo = new AddressMsgVO();
                            StringBuilder sb = new StringBuilder();
                            MsgJsonVO msgJsonVO = setMsgVo(flightInfo, sendAddress);
                            AirWaybill airWaybill = airWaybillMapper.selectById(loadWaybill.getWaybillId());
                            setMANMsg(flightInfo, msgJsonVO, loadWaybill.getQuantity(), loadWaybill.getWeight().toString(), airWaybill);
                            List<HzCableAddress> addressList = cableAddressMapper.selectAddressByAirportCode(Collections.singletonList(airWaybill.getDesPort()));
                            Map<String, List<HzCableAddress>> addressMap = new HashMap<>();
                            if (!CollectionUtils.isEmpty(addressList)){
                                addressMap = addressList.stream().collect(Collectors.groupingBy(HzCableAddress::getAirportCode));
                            }
                            List<HzCableAddress> cableAddresses = addressMap.get(airWaybill.getDesPort());
                            try {
                                setInteractionType(cableAddresses,msgJsonVO);
                                restExchange(msgJsonVO, token, sb);
                            }catch (Exception e){
                                log.error(CABLE_ERROR_MARKER,"运单"+airWaybill.getWaybillCode()+"发送FSU-MAN报文失败" + e.getMessage());
                            }
                            addressMsgVo.setAddress(msgJsonVO.getAddress());
                            addressMsgVo.setReceiveMailAddress(msgJsonVO.getReceiveMailAddress());
                            addressMsgVo.setReceiveFtpAddress(ftpAddress);
                            addressMsgVo.setReceiveFtpFolder(msgJsonVO.getReceiveFtpFolder());
                            addressMsgVo.setReceiveMQQueue(msgJsonVO.getReceiveMQQueue());
                            addressMsgVo.setMsg(sb.toString());
                            addressMsgVo.setMsgType("FSU");
                            addressMsgVo.setSendType(msgJsonVO.getSendType());
                            msgVoList.add(addressMsgVo);
                        }
                    }
                    List<AddressMsgVO> originMsg = setFFMMsg(id,sendAddress,token);
                    msgVoList.addAll(originMsg);
                    ffmRedisOperator.updateData(id, FFMStatus.REWEIGH, msgVoList);
                    // 2. 点击时才设置定时任务
                    ffmScheduler.scheduleOrReset(id, () -> {
                        FlightInfoVO info = flightInfoMapper.selectInfoVo(id);
                        List<AddressMsgVO> lastDataList = ffmRedisOperator.getMsgDataList(id);
                        if (lastDataList != null && info.getIsComp() == 0) {
                            try {
                                for (AddressMsgVO msgVo : lastDataList) {
                                    insertAndSendMsg(sendAddress, info, msgVo);
                                }
                                log.info("✅ 90分钟后自动发送FFM电报: {}", id);
                            } catch (Exception e) {
                                log.error("❌ FFM电报发送失败: {}", id, e);
                            } finally {
                                ffmRedisOperator.deleteData(id);
                                ffmScheduler.cancel(id.toString());
                            }
                        }
                    }, Duration.ofMinutes(2));
                }
            }
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作成功" + "," +
                                "code:" + response.getStatus() + "," +
                                "data:" + 1));
            }
        return 1;
        }catch (Exception e){
            for (WaybillLog waybillLog:waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作失败" +  "," +
                                "code:" + response.getStatus()));
                waybillLog.setErrorMsg(e.getMessage());
                waybillLog.setStatus(1);
            }
            throw new CustomException(e.getMessage());
        }finally {
            for (WaybillLog waybillLog:waybillLogs) {
                waybillLogService.insertWaybillLog(waybillLog);
            }
        }
    }

    private void insertAndSendMsg(String sendAddress, FlightInfoVO info, AddressMsgVO msgVo) {
        if (StringUtils.isEmpty(msgVo.getSendType())){
            return;
        }
        List<String> sendTypes = Arrays.asList(msgVo.getSendType().split(","));
        if (!sendTypes.contains("FD")){
            sendTypes.add("FD");
        }
        for (String sendType : sendTypes) {
            HzCable cable = new HzCable();
            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("ddHHmm");
            cable.setSerialNo(SerialNumberGenerator.generateSerialNumber());
            cable.setCableNo(sdf.format(date));
            cable.setCableTime(date);
            cable.setCreateBy("系统");
            cable.setIsSend(1);
            cable.setIsAuto(1);
            cable.setType("FSU");
            cable.setVersion("12");
            cable.setPriority("QD");
            cable.setCableAddress(sendAddress);
            if (msgVo.getAddress() != null){
                cable.setReceiveAddress(String.join(",", msgVo.getAddress()));
            }
            cable.setFlightNo(info.getFlightNo());
            cable.setFlightDate(info.getExecDate());
            cable.setContent(msgVo.getMsg());
            hzCableMapper.insert(cable);
            ForwardOriginMsgVO msgVO = new ForwardOriginMsgVO();
            msgVO.setOriginMsg(msgVo.getMsg());
            msgVO.setMsgType("FSU");
            msgVO.setSendType(sendType);
            msgVO.setReceiveAddress("-");
            if ("FD".equals(sendType)) {
                if (msgVo.getAddress() != null){
                    msgVO.setReceiveAddress(String.join(",",msgVo.getAddress()));
                }
            }
            if ("MAIL".equals(sendType)){
                msgVO.setMailAddress(String.join(",",msgVo.getReceiveMailAddress()));
            }
            if ("FTP".equals(sendType)){
                msgVO.setReceiveFtpAddress(ftpAddress);
                msgVO.setReceiveFtpFolder(msgVo.getReceiveFtpFolder());
            }
            if ("MQ".equals(sendType)){
                msgVO.setReceiveMQQueue(msgVo.getReceiveMQQueue());
            }
            msgVO.setSendAddress(sendAddress);
            msgVO.setPriority("QD");
            httpService.sendCable(msgVO, cable, getToken());
        }
    }

    /**
     * 新增板车
     * @param weight 新增数据
     * @return 结果
     */
    @Override
    public List<HzDepRepeatWeight> addCar(HzDepRepeatWeight weight) {
        BaseFlatbedTruck flatbedTruck = truckMapper.selectOne(new QueryWrapper<BaseFlatbedTruck>().eq("code", weight.getUld()));
        if (flatbedTruck == null){
            BaseFlatbedTruck truck = new BaseFlatbedTruck();
            truck.setCode(weight.getUld());
            truck.setType("板车");
            truck.setStatus("正常");
            truck.setCreateBy(SecurityUtils.getUsername());
            truck.setCreateTime(new Date());
        }
        repeatWeightMapper.insert(weight);
        return repeatWeightMapper.selectList(new QueryWrapper<HzDepRepeatWeight>().eq("flight_load_id",weight.getFlightLoadId()));
    }

    /**
     * 打印挂牌
     * @param id 复重id
     * @param response 输出流
     */
    @Override
    public void printInstallSigns(Long id, HttpServletResponse response) throws Exception{
        PrintRepeatVo vo = getPrintRepeatVo(id);
        ClassPathResource resource = new ClassPathResource("template/installSigns.pdf");
        if (resource.exists()){
            String path = resource.getPath();
            byte[] bytes = PdfPrintHelper.getPdfDataFromTemplate(vo,path);
            // 设置响应头
            response.reset();
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "inline; filename=example.pdf");
            // 获取输出流并写入字节数据
            response.getOutputStream().write(bytes);
            response.getOutputStream().flush();
        }
    }

    /**
     * app打印挂牌
     * @param id 复磅id
     */
    @Override
    public PrintRepeatVo printAppInstallSigns(Long id) {
        return getPrintRepeatVo(id);
    }

    @NotNull
    private PrintRepeatVo getPrintRepeatVo(Long id) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat dateFormat = new SimpleDateFormat("MMdd");
        HzDepRepeatWeight repeatWeight = repeatWeightMapper.selectById(id);
        TallyManifestVo tallyManifestVo = flightInfoMapper.selectByLegId(repeatWeight.getFlightLoadId());
        PrintRepeatVo vo = new PrintRepeatVo();
        vo.setCabin(repeatWeight.getCabin());
        vo.setDes1(repeatWeight.getDes1());
        vo.setUld(repeatWeight.getUld());
        vo.setExecDate(format.format(tallyManifestVo.getExecDate()));
        vo.setFlightNo(tallyManifestVo.getFlightNo());
        vo.setCraftNo(tallyManifestVo.getCraftNo());
        if (repeatWeight.getGroupUldId() != null) {
            List<HzDepGroupUldWaybill> waybills = groupUldWaybillMapper.selectList(new QueryWrapper<HzDepGroupUldWaybill>().eq("group_uld_id", repeatWeight.getGroupUldId()));
            if (!CollectionUtils.isEmpty(waybills)) {
                HzDepGroupUldWaybill groupUldWaybill = waybills.get(0);
                if (groupUldWaybill.getWaybillCode().contains("AWBA")) {
                    vo.setCargoWeight(repeatWeight.getBoardCargoWeight());
                    vo.setCargoQuantity(repeatWeight.getQuantity());
                } else {
                    vo.setMailWeight(repeatWeight.getBoardCargoWeight());
                    vo.setMailQuantity(repeatWeight.getQuantity());
                }
            }
        } else {
            vo.setCargoWeight(repeatWeight.getBoardCargoWeight());
            vo.setCargoQuantity(repeatWeight.getQuantity());
        }
        String execDate = dateFormat.format(tallyManifestVo.getExecDate());
        BigDecimal weight = vo.getCargoWeight() == null ? vo.getMailWeight() : vo.getCargoWeight();
        int quantity = vo.getCargoQuantity() == null ? vo.getMailQuantity() : vo.getCargoQuantity();
        weight = weight == null ? new BigDecimal(0) : weight;
        StringBuilder builder = new StringBuilder();
        builder.append(execDate).append("|")
                .append(vo.getFlightNo()).append("|")
                .append(vo.getCraftNo()).append("|")
                .append(vo.getUld()).append("|")
                .append(vo.getDes1()).append("|")
                .append(quantity).append("|")
                .append(weight).append("|");
        try {
            String qrCodeBase64 = QRCodeGenerator.generateQRCodeBase64(builder.toString());
            vo.setQrCode(qrCodeBase64);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return vo;
    }

    /**
     * 复重出库中间步骤
//     * @param id 配载id
     * @param flightLoadIdList 配载id集合
     * @param repeatWeights 复重数据
     */
    private void repeatSteps(List<Long> flightLoadIdList, List<HzDepRepeatWeight> repeatWeights) {
        List<HzDepGroupUld> groupUlds = hzDepGroupUldMapper.selectList(new QueryWrapper<HzDepGroupUld>().in("flight_load_id", flightLoadIdList));
        if (!CollectionUtils.isEmpty(groupUlds)){
            // 修改组货复重重量
            for (HzDepGroupUld groupUld : groupUlds) {
                HzDepRepeatWeight repeatWeight = repeatWeights.stream()
                        .filter(e -> flightLoadIdList.contains(e.getFlightLoadId()) && e.getUld().equals(groupUld.getUld())).findFirst().orElse(null);
                if (repeatWeight != null){
                    groupUld.setRepeatWeight(repeatWeight.getWeight());
                    hzDepGroupUldMapper.updateById(groupUld);
                }
            }
            List<Long> uldIds = groupUlds.stream().map(HzDepGroupUld::getId).collect(Collectors.toList());
            List<HzDepGroupUldWaybill> groupUldWaybills = groupUldWaybillMapper.selectList(new QueryWrapper<HzDepGroupUldWaybill>().in("group_uld_id", uldIds));
            if (!CollectionUtils.isEmpty(groupUldWaybills)){
                String flightNo = flightLoadMapper.selectNoById(flightLoadIdList.get(0));
                for (HzDepGroupUldWaybill groupUldWaybill : groupUldWaybills) {
                    AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                            .eq("waybill_code", groupUldWaybill.getWaybillCode())
                            .eq("type","DEP"));
                    airWaybill.setStatus("been_out");
                    airWaybill.setUpdateTime(new Date());
                    airWaybillMapper.updateById(airWaybill);

                    HzDepSpecialTrace specialTrace = specialTraceMapper.selectOne(new QueryWrapper<HzDepSpecialTrace>().eq("waybill_code", airWaybill.getWaybillCode()));
                    if (specialTrace != null){
                        specialTrace.setOutFlight(flightNo);
                        specialTrace.setOutLocation(specialTrace.getWareLocation());
                        specialTrace.setOutWeight(groupUldWaybill.getWeight());
                        specialTrace.setOutPiece(groupUldWaybill.getQuantity());
                        specialTrace.setOutTime(new Date());
                        specialTrace.setOutUser("系统");
                        specialTraceMapper.updateById(specialTrace);
                    }
                    BigDecimal allWeight = loadWaybillMapper.selectWaybillByWaybillId(airWaybill.getId());
                    BigDecimal weight = allWeight == null ? new BigDecimal(0) : allWeight;
                    if (airWaybill.getWeight().compareTo(weight) <= 0){
                        HzColdRegister hzColdRegister = coldRegisterMapper.selectOne(new QueryWrapper<HzColdRegister>()
                                .eq("waybill_code", airWaybill.getWaybillCode())
                                .eq("type", "DEP")
                                .ne("status",2));
                        if (hzColdRegister != null && hzColdRegister.getOutTime() == null){
                            Date date = new Date();
                            long out = date.getTime();
                            long ware = hzColdRegister.getWareTime().getTime();
                            long timeDiff = (out - ware) / (1000 * 60 * 60);
                            BigDecimal time = new BigDecimal(timeDiff);
                            BigDecimal bigDecimal = time.setScale(0, RoundingMode.UP);
                            hzColdRegister.setOutTime(date);
                            hzColdRegister.setChargeTime(bigDecimal);
                            hzColdRegister.setUseTime(time);
                            hzColdRegister.setStatus(2);
                            List<CostDetail> details = costDetailMapper.selectList(new QueryWrapper<CostDetail>()
                                    .eq("waybill_code", hzColdRegister.getWaybillCode())
                                    .eq("dept_id", hzColdRegister.getDeptId())
                                    .eq("service_type", 2)
                                    .eq("type",0));
                            if (!CollectionUtils.isEmpty(details)) {
                                BigDecimal costSum = new BigDecimal(0);
                                BigDecimal oldSum = details.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                                for (CostDetail detail : details) {
                                    HzChargeIrRelation relation = relationMapper.selectById(detail.getIrId());
                                    if (relation == null) {
                                        continue;
                                    }
                                    HzChargeItems hzChargeItems = chargeItemsMapper.selectById(relation.getItemId());
                                    detail.setStoreEndTime(date);
                                    detail.setIsSettle(1);
                                    detail.setQuantity(bigDecimal.toString());
                                    detail.setType(1);
                                    detail.setCreateTime(new Date());
                                    detail.setFlightId(2L);
                                    detail.setSettleDepWeight(groupUldWaybill.getWeight());
                                    detail.setSettleDepQuantity(groupUldWaybill.getQuantity());
                                    detail.setIsAuto(0);
                                    detail.setId(null);
                                    List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>().eq("ir_id",relation.getId()));
                                    BillingRule rule = BillingRuleFactory.createRule("ColdStorageBillingRule.class");
                                    BillRuleVo vo1 = rule.calculateFee(itemRules, new BigDecimal(0), 1, detail);
                                    BigDecimal bigDecimal1 = BigDecimalRoundUtils.bigDecimalRound(hzChargeItems.getRoundRule(), vo1.getTotalCharge());
                                    costSum = costSum.add(bigDecimal1);
                                    detail.setTotalCharge(bigDecimal1);
                                    costDetailMapper.insert(detail);
                                }
                                hzColdRegister.setSumMoney(costSum);
                                BaseAgent agent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", hzColdRegister.getDeptId()));
                                if (costSum.compareTo(oldSum) > 0){
                                    BigDecimal deduct = costSum.subtract(oldSum);
                                    if (agent != null) {
                                        if (agent.getSettleMethod() == 1) {
                                            BigDecimal balance = agent.getBalance() == null ? new BigDecimal(0) : agent.getBalance();
                                            BigDecimal subtract = balance.subtract(deduct);
                                            if (subtract.compareTo(new BigDecimal(0)) < 0) {
                                                throw new CustomException("当前代理人余额不足");
                                            } else {
                                                agent.setBalance(subtract);
                                                baseAgentMapper.updateBaseAgent(agent);
                                                BaseBalance baseBalance = new BaseBalance();
                                                baseBalance.setAgentId(agent.getId());
                                                baseBalance.setBalance(agent.getBalance());
                                                baseBalance.setType("减少余额");
                                                baseBalance.setCreateTime(new Date());
                                                baseBalance.setCreateBy("系统");
                                                // todo 流水号需从银联支付接口获取
                                                //baseBalance.setSerialNo();
                                                baseBalance.setTradeMoney(deduct);
                                                baseBalance.setWaybillCode(hzColdRegister.getWaybillCode());
                                                baseBalance.setRemark("冷藏申请支付");
                                                baseBalanceMapper.insertBaseBalance(baseBalance);
                                                hzColdRegister.setPayStatus(6);
                                            }
                                        } else if (agent.getSettleMethod() == 0) {
                                            hzColdRegister.setPayStatus(5);
                                        } else {
                                            if (agent.getPayMethod() == 0) {
                                                hzColdRegister.setPayStatus(7);
                                            } else {
                                                hzColdRegister.setPayStatus(8);
                                            }
                                        }
                                    } else {
                                        hzColdRegister.setPayStatus(8);
                                    }
                                }else if (costSum.compareTo(oldSum) == 0){
                                    if (agent != null) {
                                        if (agent.getSettleMethod() == 1) {
                                            hzColdRegister.setPayStatus(6);
                                        } else if (agent.getSettleMethod() == 0) {
                                            hzColdRegister.setPayStatus(5);
                                        } else {
                                            if (agent.getPayMethod() == 0) {
                                                hzColdRegister.setPayStatus(7);
                                            } else {
                                                hzColdRegister.setPayStatus(8);
                                            }
                                        }
                                    } else {
                                        hzColdRegister.setPayStatus(8);
                                    }
                                }else {
                                    BigDecimal add = oldSum.subtract(costSum);
                                    if (agent != null){
                                        if (agent.getSettleMethod() == 1) {
                                            BigDecimal balance = agent.getBalance() == null ? new BigDecimal(0) : agent.getBalance();
                                            BigDecimal subtract = balance.add(add);
                                            agent.setBalance(subtract);
                                            baseAgentMapper.updateBaseAgent(agent);
                                            BaseBalance baseBalance = new BaseBalance();
                                            baseBalance.setAgentId(agent.getId());
                                            baseBalance.setBalance(agent.getBalance());
                                            baseBalance.setType("增加余额");
                                            baseBalance.setCreateTime(new Date());
                                            baseBalance.setCreateBy("系统");
                                            // todo 流水号需从银联支付接口获取
                                            //baseBalance.setSerialNo();
                                            baseBalance.setTradeMoney(add);
                                            baseBalance.setWaybillCode(hzColdRegister.getWaybillCode());
                                            baseBalance.setRemark("冷藏申请退款");
                                            baseBalanceMapper.insertBaseBalance(baseBalance);
                                            hzColdRegister.setPayStatus(10);
                                        } else if (agent.getSettleMethod() == 0){
                                            hzColdRegister.setPayStatus(9);
                                        }else {
                                            if (agent.getPayMethod() == 0){
                                                hzColdRegister.setPayStatus(11);
                                            }else {
                                                hzColdRegister.setPayStatus(12);
                                            }
                                        }
                                    }else {
                                        hzColdRegister.setPayStatus(12);
                                    }
                                }
                            }
                            hzColdRegister.setUpdateTime(new Date());
                            coldRegisterMapper.updateById(hzColdRegister);
                        }
                    }
                }
            }
        }
    }

    private static final Map<String,String> FLIGHT_STATUS = new HashMap<>();
    static {
        FLIGHT_STATUS.put("not_pre","未配载");
        FLIGHT_STATUS.put("been_pre","未指派");
        FLIGHT_STATUS.put("not_group","未组货");
        FLIGHT_STATUS.put("been_group","未复重");
        FLIGHT_STATUS.put("not_weight","未复重");
        FLIGHT_STATUS.put("been_weight","未交接");
        FLIGHT_STATUS.put("not_handed","未交接");
        FLIGHT_STATUS.put("been_handed","已完成");
        FLIGHT_STATUS.put("out_of_stock","无货");
    }

    /**
     * @author: lan
     * @description: 根据groupUldId去查询运单对应数据
     * @param: [id]
     * @date: 2024/11/7
     */
    @Override
    public List<HzDepRepeatWaybillsVo> getWaybills(Long id) {
        List<HzDepRepeatWaybillsVo> waybills = repeatWeightMapper.getWaybills(id);
        List<HzDepRepeatWaybillsVo> waybillsVos = new ArrayList<>();
        if(waybills.size() > 0){
            for (HzDepRepeatWaybillsVo vo:waybills) {
                FlightLoadUld flightLoadUld = flightLoadUldMapper.selectById(vo.getLoadUldId());
                if(flightLoadUld!=null && StringUtils.isNotEmpty(flightLoadUld.getUld())){
                    if(flightLoadUld.getUld().equals(vo.getUld())){
                        waybillsVos.add(vo);
                    }
                }
            }
            waybillsVos.forEach(e->{
                HzCollectWaybill collectWaybill = collectWaybillMapper.selectById(e.getCollectId());
                if (collectWaybill != null){
//                    if ("REAL".equals(collectWaybill.getStatus())){
//                        e.setIsReal(1);
//                    }
//                    if ("VIRTUAL".equals(collectWaybill.getStatus())){
//                        e.setIsReal(0);
//                    }
                    if (collectWaybill.getUld() != null){
                        HzCollectWeight collectWeight = collectWeightMapper.selectOne(new QueryWrapper<HzCollectWeight>()
                                .eq("collect_id", collectWaybill.getId())
                                .eq("uld", collectWaybill.getUld()));
                        if(collectWeight!=null){
                            e.setWeighWeight(collectWeight.getWeight());
                        }
                    }
                }
                HzColdRegister register = registerMapper.selectWaybillColdStatus(e.getWaybillCode());
                if (register == null){
                    e.setIsCold("否");
                }else {
                    if (register.getStatus() == 0 || register.getStatus() == 2){
                        e.setIsCold("否");
                    }else {
                        e.setIsCold("是");
                    }
                }
                AirWaybill airWaybill = airWaybillMapper.selectById(e.getId());
                if(airWaybill.getCollectStatus() != null){
                    e.setIsReal(airWaybill.getCollectStatus());
                }
            });
        }
        return waybillsVos;
    }

    public void setInteractionType(List<HzCableAddress> cableAddresses, MsgJsonVO vo) {
        List<String> sendType = new ArrayList<>();
        if (cableAddresses == null || cableAddresses.isEmpty()) {
            sendType.add("FD");
            vo.setSendType("FD");
            vo.setAddress(new String[]{"-"});
            return;
        }
        List<String> sendTypeCn = cableAddresses.stream()
                .map(HzCableAddress::getInteractionTypes)
                .filter(StringUtils::isNotEmpty)
                .map(s -> s.split(","))
                .flatMap(Arrays::stream)
                .map(String::trim)
                .distinct()
                .collect(Collectors.toList());
        for (String type : sendTypeCn) {
            switch (type) {
                case "民航局报文":
                    sendType.add("FD");
                    break;
                case "邮箱报文":
                    sendType.add("MAIL");
                    break;
                case "FTP收发报文":
                    sendType.add("FTP");
                    break;
                case "rabbitmq收发报文":
                    sendType.add("MQ");
                    break;
                default:
                    break;
            }
        }
        if (!sendType.contains("FD")){
            sendType.add("FD");
        }
        vo.setSendType(String.join(",", sendType));
        List<String> emailAddresses = new ArrayList<>();
        List<String> ftpList = new ArrayList<>();
        List<String> mqQueueList = new ArrayList<>();
        List<String> caacAddresses = new ArrayList<>();
        for (HzCableAddress cableAddress : cableAddresses) {
            List<String> interactionTypeList = new ArrayList<>();
            if (StringUtils.isNotEmpty(cableAddress.getInteractionTypes())) {
                interactionTypeList = Arrays.asList(cableAddress.getInteractionTypes().split(","));
            }
            if (!CollectionUtils.isEmpty(interactionTypeList)) {
                for (String type : interactionTypeList) {
                    switch (type) {
                        case "邮箱报文":
                            emailAddresses.add(cableAddress.getEmailAddress());
                            break;
                        case "FTP收发报文":
                            ftpList.add(cableAddress.getFtpList());
                            break;
                        case "民航局报文":
                            caacAddresses.add(cableAddress.getCaacAddress());
                            break;
                        case "rabbitmq收发报文":
                            mqQueueList.add(cableAddress.getMqQueue());
                            break;
                        default:
                            break;
                    }
                }
            }
        }
        if (!emailAddresses.isEmpty()) {
            vo.setReceiveMailAddress(emailAddresses.toArray(new String[0]));
        }
        if (!ftpList.isEmpty()) {
            vo.setReceiveFtpFolder(String.join(",",ftpList));
        }
        if (!mqQueueList.isEmpty()) {
            vo.setReceiveMQQueue(String.join(",",mqQueueList));
        }
        if (!caacAddresses.isEmpty()) {
            vo.setAddress(caacAddresses.toArray(new String[0]));
        }else {
            vo.setAddress(new String[]{"-"});
        }
    }

    private List<AddressMsgVO> setFFMMsg(Long flightId,String sendAddress,String token){
        List<AddressMsgVO> msgVoList = new ArrayList<>();
        List<MsgFlightInfoVO> flightInfoVoList = flightInfoMapper.selectFlightInfoById(flightId, null);
        if (CollectionUtils.isEmpty(flightInfoVoList)) {
            return msgVoList;
        }
        MsgJsonVO vo = new MsgJsonVO();
        vo.setMsgType("FFM");
        vo.setOrigin(sendAddress.split(","));
        vo.setPriority(new String[]{"QD"});
        vo.setSourceId("1727215332084514819");
        vo.setUniqueId("1727215332084514819");
        List<String> desPortList = flightInfoVoList.stream().map(MsgFlightInfoVO::getNextStation).distinct().collect(Collectors.toList());
        List<HzCableAddress> desPortAddressList = cableAddressMapper.selectAddressByAirportCode(desPortList);
        List<String> carrierList = flightInfoVoList.stream().map(MsgFlightInfoVO::getCarrier).distinct().collect(Collectors.toList());
        List<HzCableAddress> carrierAddressList= cableAddressMapper.selectAddressByCarrier(carrierList);
        List<MsgFlightInfoVO> collect = flightInfoVoList.stream().distinct().collect(Collectors.toList());
        for (MsgFlightInfoVO infoVo : collect) {
            vo.setMsgVersion("5");
            vo.setCarrier(infoVo.getCarrier());
            vo.setFlightDate(infoVo.getFlightDate());
            vo.setDepartureStation(infoVo.getDepartureStation());
            vo.setFlightNo(infoVo.getFlightNo());
            vo.setNextStation(infoVo.getNextStation());
            vo.setFlightType(infoVo.getFlightType());
            FFMJsonVO ffmJsonVo = new FFMJsonVO();
            ffmJsonVo.setCarrier(infoVo.getCarrier());
            ffmJsonVo.setAircraftRegistration(infoVo.getAircraftRegistration());
            ffmJsonVo.setEtd(infoVo.getEtd());
            ffmJsonVo.setFlightNo(infoVo.getFlightNo());
            ffmJsonVo.setOriAirport(infoVo.getDepartureStation());
            PointOfUnloading unloading = new PointOfUnloading();
            unloading.setEta(infoVo.getEta());
            unloading.setArrAirport(infoVo.getNextStation());
            String date = infoVo.getStartSchemeTakeoffTime().toLocalDate().toString();
            unloading.setScheduledDepartureDate(date);
            String time = infoVo.getStartSchemeTakeoffTime().toLocalTime().format(TIME_FORMATTER);
            unloading.setScheduledDepartureTime(time);
            List<Long> legIds = flightLoadMapper.selectIdByFlightId(flightId);
            List<UldVO> uldVoList = uldMapper.selectUldByFlightId(infoVo.getFlightId(),null);
            List<ConsignmentDetail> bulk = new ArrayList<>();
            for (UldVO uldVO : uldVoList) {
                String uldNo = uldVO.getUldNo();
                List<ConsignmentDetail> detailList = loadWaybillMapper.selectDetailListForLegId(legIds, uldNo);
                if (!CollectionUtils.isEmpty(detailList)) {
                    for (ConsignmentDetail detail : detailList) {
                        if (StringUtils.isEmpty(detail.getGoodsName())){
                            detail.setGoodsName("-");
                        }
                        String mawbNo = detail.getMawbNo();
                        if(mawbNo.startsWith("AWBA")){
                            String substring = mawbNo.substring(4);
                            StringBuilder stringBuilder = new StringBuilder(substring);
                            detail.setMawbNo(stringBuilder.insert(3, "-").toString());
                        }else{
                            String substring = mawbNo.substring(4);
                            StringBuilder stringBuilder = new StringBuilder(substring);
                            detail.setMawbNo(stringBuilder.insert(2, "-").toString());
                        }
                        detail.setShipmentDescriptionCode("T");
                        detail.setTotalPieces(null);
                        if (StringUtils.isNotEmpty(detail.getShcStr())) {
                            detail.setShc(detail.getShcStr().split(","));
                        }
                        if (StringUtils.isNotEmpty(detail.getVolume())) {
                            detail.setVolumeUnit("MC");
                        }
                        if (StringUtils.isNotEmpty(detail.getWeight())) {
                            detail.setWeightUnit("KG");
                        }
                    }
                    if ("BLK".equals(uldNo)){
                        bulk.addAll(detailList);
                    }else {
                        Matcher matcher = LETTERS_PATTERN.matcher(uldNo);
                        if (matcher.find()) {
                            String letters = matcher.group(1);
                            uldVO.setUldType(letters);
                            String numbers = matcher.group(2);
                            uldVO.setUldNum(numbers);
                        }
                        uldVO.setUldOwner(infoVo.getCarrier());
                        uldVO.setUldNo(uldNo + infoVo.getCarrier());
                        uldVO.setConsignmentDetail(detailList);
                    }
                }
            }
            if (!bulk.isEmpty()){
                unloading.setBulk(bulk);
            }
            if (!uldVoList.isEmpty()){
                unloading.setUld(uldVoList);
            }
            ffmJsonVo.setPointOfUnloading(Collections.singletonList(unloading));
            vo.setMsgJson(JSON.toJSONString(ffmJsonVo));
            List<List<HzCableAddress>> targets = new ArrayList<>();
            Map<String, List<HzCableAddress>> desPortAddressMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(desPortAddressList)){
                desPortAddressMap = desPortAddressList.stream().collect(Collectors.groupingBy(HzCableAddress::getAirportCode));
            }
            List<HzCableAddress> desPortAddresses = desPortAddressMap.get(infoVo.getNextStation());
            targets.add(desPortAddresses);

            Map<String, List<HzCableAddress>> carrierAddressMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(carrierAddressList)){
                carrierAddressMap = carrierAddressList.stream().collect(Collectors.groupingBy(HzCableAddress::getAirlinesCode));
            }
            List<HzCableAddress> carrierAddresses = carrierAddressMap.get(infoVo.getCarrier());
            targets.add(carrierAddresses);
            for (List<HzCableAddress> target : targets) {
                StringBuilder sb = new StringBuilder();
                AddressMsgVO msgVo = new AddressMsgVO();
                try {
                    setInteractionType(target,vo);
                    if (vo.getAddress() != null){
                        restExchange(vo,token,sb);
                        msgVo.setMsg(sb.toString());
                        msgVo.setMsgType("FFM");
                        msgVo.setSendType(vo.getSendType());
                        msgVo.setAddress(vo.getAddress());
                        msgVo.setReceiveMailAddress(vo.getReceiveMailAddress());
                        msgVo.setReceiveFtpAddress(ftpAddress);
                        msgVo.setReceiveFtpFolder(vo.getReceiveFtpFolder());
                        msgVo.setReceiveMQQueue(vo.getReceiveMQQueue());
                        msgVoList.add(msgVo);
                    }
                }catch (Exception e){
                    log.error(CABLE_ERROR_MARKER,"发送FFM报文失败" + e.getMessage());
                }
            }
        }
        return msgVoList;
    }

    private void setMANMsg(FlightInfo info,MsgJsonVO msgJsonVO, Integer quantity, String weight, AirWaybill airWaybill){
        FSUJsonVO fsuJsonVO = new FSUJsonVO();
        fsuJsonVO.setDepAirport(airWaybill.getSourcePort());
        fsuJsonVO.setDesAirport(airWaybill.getDesPort());
        fsuJsonVO.setMawbId(airWaybill.getId().toString());
        if(airWaybill.getWaybillCode().startsWith("AWBA")){
            String substring = airWaybill.getWaybillCode().substring(4);
            StringBuilder stringBuilder = new StringBuilder(substring);
            fsuJsonVO.setMawbNo(stringBuilder.insert(3, "-").toString());
        }else{
            String substring = airWaybill.getWaybillCode().substring(4);
            StringBuilder stringBuilder = new StringBuilder(substring);
            fsuJsonVO.setMawbNo(stringBuilder.insert(2, "-").toString());
        }
        fsuJsonVO.setPieces(quantity.toString());
        if (!airWaybill.getQuantity().equals(quantity)){
            fsuJsonVO.setShipmentDescriptionCode("P");
            fsuJsonVO.setTotalPieces(airWaybill.getQuantity().toString());
        }else {
            fsuJsonVO.setShipmentDescriptionCode("T");
        }
        fsuJsonVO.setWeight(weight);
        fsuJsonVO.setWeightUnit("K");
        List<StatusDetails> statusDetails = new ArrayList<>();
        StatusDetails statusDetail = new StatusDetails();
        statusDetail.setStatusCode(msgJsonVO.getOperationNode());
        statusDetail.setMovementCarrier(info.getAirWays());
        statusDetail.setMovementFlightNo(info.getFlightNo());
        statusDetail.setMovementDate(LocalDateTime.now().format(DATE_FORMATTER));
        statusDetail.setMovementTime(LocalDateTime.now().format(TIME_FORMATTER));
        statusDetail.setMovementAirport(info.getStartStation());
        statusDetail.setMovementArrivalAirport(info.getTerminalStation());
        statusDetail.setMovementDepartureAirport(info.getStartStation());
        statusDetail.setShipmentDescriptionCode(fsuJsonVO.getShipmentDescriptionCode());
        statusDetail.setPieces(quantity.toString());
        statusDetail.setWeight(weight);
        statusDetail.setWeightUnit("K");
        statusDetail.setReportingDate(DATE_FORMAT.format(new Date()));
        statusDetails.add(statusDetail);
        fsuJsonVO.setStatusDetails(statusDetails);
        fsuJsonVO.setTotalWeight(airWaybill.getWeight().toString());
        msgJsonVO.setMsgJson(JSON.toJSONString(fsuJsonVO));
    }


    private MsgJsonVO setMsgVo(FlightInfo info,String sendAddress){
        MsgJsonVO vo = new MsgJsonVO();
        vo.setCarrier(info.getAirWays());
        vo.setDepartureStation(info.getStartStation());
        vo.setMsgType("FSU");
        vo.setNextStation(info.getTerminalStation());
        vo.setOperationNode("MAN");
        vo.setOperationStation("KWE");
        if (StringUtils.isNotEmpty(sendAddress)) {
            vo.setOrigin(sendAddress.split(","));
        }else {
            vo.setOrigin(new String[]{"KWEFDCN"});
        }
        vo.setPriority(new String[]{"QD"});
        vo.setSourceId("44162409105767715");
        vo.setUniqueId("44162409105767715");
        vo.setMsgVersion("12");
        BaseCarrier baseCarrier = carrierMapper.selectByCode(info.getAirWays());
        if (baseCarrier != null){
            vo.setWaybillPrefix(baseCarrier.getPrefix());
        }
        return vo;
    }

    private String getToken(){
        System.out.println("*********调用登录接口获取token*********");
        String token = "";
        HttpHeaders headers = setHeaders();
        HttpEntity<?> httpEntity = new HttpEntity<>(headers);
        ResponseEntity<JSONObject> exchange = restTemplate.exchange(loginUrl + account, HttpMethod.GET, httpEntity, JSONObject.class);
        if (exchange.getBody() != null) {
            if (!"0".equals(exchange.getBody().getString("code"))) {
                throw new CustomException(exchange.getBody().getString("message"));
            }
            JSONObject data = exchange.getBody().getJSONObject("data");
            token = data.getString("token");
            System.out.println(token);
        }
        return token;
    }

    private HttpHeaders setHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType("application/json;charset=UTF-8"));
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        headers.add("Accept-Charset", "UTF-8");
        return headers;
    }

    private void restExchange(MsgJsonVO vo, String token, StringBuilder sb){
        HttpHeaders header = setHeaders();
        header.add("X-Access-Token", token);
        HttpEntity<?> httpEntity = new HttpEntity<>(vo, header);
        System.out.println("参数：" + JSON.toJSONString(vo));
        ResponseEntity<JSONObject> exchange = restTemplate.exchange(getMsg, HttpMethod.POST, httpEntity, JSONObject.class);
        if (exchange.getBody() != null) {
            if (!"0".equals(exchange.getBody().getString("code"))) {
                throw new CustomException(exchange.getBody().getString("msg"));
            }
            JSONObject data = exchange.getBody().getJSONObject("data");
            String msgContent = data.getString("msgContent");
            sb.append(msgContent).append("\n");
        }
    }

}
