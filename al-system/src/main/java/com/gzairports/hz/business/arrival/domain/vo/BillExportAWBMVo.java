package com.gzairports.hz.business.arrival.domain.vo;

import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
public class BillExportAWBMVo {

    /** 办单id */
    private Long id;

    /** 序号 */
    @Excel(name = "序号")
    private Integer idx;

    /** 流水号 */
    @Excel(name = "流水号", width = 20)
    private String serialNo;

    /** 运单对象 */
    @Excel(name = "")
    private List<OutOrderWaybillAWBMVo> orderWaybillVos;
}
