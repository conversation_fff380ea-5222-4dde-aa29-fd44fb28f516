<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.monitor.mapper.FinanceBalanceMapper">

    <select id="selectFinanceForList"
            resultType="com.gzairports.hz.business.monitor.domain.vo.FinanceBalanceVO">
        select hm.id, hm.monitor_time, hm.monitor_before, hm.before_balance, hm.monitor_yesterday, hm.yesterday_balance,
               hm.difference, hm.status, bg.agent, group_concat(distinct su.nick_name separator ',') as smsUser
        from hz_finance_balance_monitor hm
            left join base_agent bg on hm.agent_id = bg.id
            left join hz_fbm_sms_agent_user hu on hu.agent_id = hm.agent_id
            left join sys_user su on su.user_id = hu.user_id where 1=1
        <if test="startMonitorTime != null">
            and hm.monitor_time <![CDATA[>=]]> #{startMonitorTime}
        </if>
        <if test="endMonitorTime != null">
            and hm.monitor_time <![CDATA[<=]]> #{endMonitorTime}
        </if>
        <if test="status != null">
            and hm.status = #{status}
        </if>
        <if test="agentIds != null and agentIds.size() > 0">
            and hm.agent_id in
            <foreach collection="agentIds" item="agentId" open="(" separator="," close=")">
                #{agentId}
            </foreach>
        </if>
        group by hm.id, hm.monitor_time, hm.monitor_before, hm.before_balance, hm.monitor_yesterday, hm.yesterday_balance,
                 hm.difference, hm.status, bg.agent
    </select>
</mapper>