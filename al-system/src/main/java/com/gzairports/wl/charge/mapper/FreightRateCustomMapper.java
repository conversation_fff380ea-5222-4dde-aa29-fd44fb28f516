package com.gzairports.wl.charge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.wl.charge.domain.FreightRateCustom;
import com.gzairports.wl.charge.domain.query.FreightRateCustomQuery;
import com.gzairports.wl.charge.domain.vo.FreightRateCustomVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户运价Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@Mapper
public interface FreightRateCustomMapper extends BaseMapper<FreightRateCustom> {

    /**
     * 删除客户运价
     *
     * @param id 客户运价主键
     * @return 结果
     */
    int removeRateCustom(Long id);

    /**
     * 查询客户运价列表
     *
     * @param query 查询参数
     * @return 客户运价列表
     */
    List<FreightRateCustomVO> selectRateCustomList(FreightRateCustomQuery query);

    /**
     * 根据id查询客户运价列表
     *
     * @param ids 查询参数
     * @return 客户运价列表
     */
    List<FreightRateCustomVO> selectRateCustomListByIds(@Param("ids") List<Long> ids);
}
