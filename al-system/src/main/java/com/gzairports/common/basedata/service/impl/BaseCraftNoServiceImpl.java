package com.gzairports.common.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.basedata.domain.BaseCraftNo;
import com.gzairports.common.basedata.domain.BaseCraftNoMail;
import com.gzairports.common.basedata.domain.query.BaseCraftNoQuery;
import com.gzairports.common.basedata.mapper.BaseCraftNoMapper;
import com.gzairports.common.basedata.service.IBaseCraftNoService;
import com.gzairports.common.business.arrival.mapper.FlightInfoMapper;
import com.gzairports.common.business.departure.domain.FlightInfo;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.message.domain.vo.SocketMessageVo;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.hz.websocket.HzWebSocketServer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: lan
 * @Desc: 基础数据机号管理
 * @create: 2024-12-09 13:48
 **/
@Service
public class BaseCraftNoServiceImpl implements IBaseCraftNoService {

    @Autowired
    private BaseCraftNoMapper baseCraftNoMapper;

    @Autowired
    private FlightInfoMapper flightInfoMapper;

    @Autowired
    private HzWebSocketServer webSocketServer;



    @Override
    public List<BaseCraftNo> selectCraftNoList(BaseCraftNo baseCraftNo) {
        return baseCraftNoMapper.selectCraftNoList(baseCraftNo);
    }

    @Override
    public List<BaseCraftNoMail> selectCraftNoMailList(BaseCraftNo baseCraftNo) {
        return baseCraftNoMapper.selectCraftNoMailList(baseCraftNo);
    }

    @Override
    public int insertCraft(BaseCraftNo baseCraftNo) {
        BaseCraftNo baseCraftNoOld = baseCraftNoMapper.selectOne(new QueryWrapper<>(baseCraftNo)
                .eq("craft_no", baseCraftNo.getCraftNo())
                .eq("type", baseCraftNo.getType()));
        if(baseCraftNoOld != null && baseCraftNoOld.getStartTime().before(new Date()) && baseCraftNoOld.getEndTime().after(new Date())){
            if(baseCraftNo.getType() == 1){
                throw new CustomException("该机号已设置生效中的副邮箱配置");
            }
            if(baseCraftNo.getType() == 2){
                throw new CustomException("该机号已设置生效中的特殊供氧配置");
            }
            if(baseCraftNo.getType() == 3){
                throw new CustomException("该机号已设置生效中的特殊舱位配置");
            }
        }
        return baseCraftNoMapper.insert(baseCraftNo);
    }

    @Override
    public BaseCraftNo getInfoCraftNo(Long id) {
        return baseCraftNoMapper.selectById(id);
    }

    @Override
    public int delete(Long id) {
        return baseCraftNoMapper.deleteById(id);
    }

    @Override
    public int update(BaseCraftNo baseCraftNo) {
        return baseCraftNoMapper.updateById(baseCraftNo);
    }

    @Override
    public void selectCraftNoConfig(BaseCraftNoQuery quary) {
        FlightInfo flightInfo = flightInfoMapper.selectOne(new QueryWrapper<FlightInfo>()
                .eq("air_ways", quary.getFlightNo().substring(0, 2))
                .eq("flight_no", quary.getFlightNo().substring(2))
                .eq("exec_date", quary.getFlightDate())
                .eq("is_offin","D"));
        if(flightInfo != null){
            String craftNo = flightInfo.getCraftNo();
            BaseCraftNo baseCraftNoMail = baseCraftNoMapper.selectCraftNoInfo(craftNo, new Date(), 1);
            if(baseCraftNoMail != null){
                SocketMessageVo vo = new SocketMessageVo();
                vo.setMessage("请注意该航班" + quary.getFlightNo() + "有副邮箱");
                webSocketServer.sendMessageToDept(SecurityUtils.getUserId(), vo);
            }
            BaseCraftNo baseCraftNoOxygen = baseCraftNoMapper.selectCraftNoInfo(craftNo, new Date(), 2);
            if(baseCraftNoOxygen != null){
                if(StringUtils.isNotEmpty(baseCraftNoOxygen.getCabins())){
                    SocketMessageVo vo = new SocketMessageVo();
                    vo.setMessage("请注意该航班" + quary.getFlightNo() + "有供氧舱位,舱位为" + baseCraftNoOxygen.getCabins());
                    webSocketServer.sendMessageToDept(SecurityUtils.getUserId(), vo);
                }
            }
            BaseCraftNo baseCraftNoCabins = baseCraftNoMapper.selectCraftNoInfo(craftNo, new Date(), 3);
            if(baseCraftNoCabins != null){
                if(StringUtils.isNotEmpty(baseCraftNoCabins.getCabins())){
                    SocketMessageVo vo = new SocketMessageVo();
                    vo.setMessage("请注意该航班" + quary.getFlightNo() + "舱位分布为" + baseCraftNoOxygen.getCabins());
                    webSocketServer.sendMessageToDept(SecurityUtils.getUserId(), vo);
                }
            }
        }
    }
}
