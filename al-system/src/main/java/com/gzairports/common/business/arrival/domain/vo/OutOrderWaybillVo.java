package com.gzairports.common.business.arrival.domain.vo;

import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 出库单运单数据
 *
 * <AUTHOR>
 * @date 2024-07-19
 */
@Data
public class OutOrderWaybillVo {

    /** 理货id */
    private Long tallyId;

    /** 办单id */
    private Long pickUpId;

    /** 运单类型 */
    private String waybillType;

    /** 运单号 */
    @Excel(name = "运单号")
    private String waybillCode;

    /** 件数 */
    @Excel(name = "件数", cellType = Excel.ColumnType.NUMERIC)
    private Integer quantity;

    /** 重量 */
    @Excel(name = "重量", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal weight;

    /** 品名 */
    @Excel(name = "品名")
    private String cargoName;

    /** 件数 */
    private String quantityStr;

    /** 可提货件数 */
    private Integer canPickUpQuantity;

    /** 计费重量 */
    @Excel(name = "计费重量", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal chargeWeight;

    /** 计费重量 */
    private String chargeWeightStr;

    /** 始发站 */
    private String sourcePort;

    /** 进港航班号 */
    private String flightNo;

    /** 航班日期 */
    private String flightDate;

    /** 处理费 */
    @Excel(name = "进处费", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal processingFee = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);

    /** 保管费 */
    @Excel(name = "保管费", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal storageFee = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);

    /** 冷藏费 */
    @Excel(name = "冷藏费", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal refrigerationFee = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);

    /** 搬运费 */
    @Excel(name = "搬运费", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal handlingFee = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);

    /** 电报费 */
    @Excel(name = "电报费", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal cableCharge = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);

    /** 小计 */
    @Excel(name = "费用合计", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal subtotal = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);

    /** 备注 */
    private String remark;

    /** 运单制单件数 */
    private Integer waybillQuantity;

    /** 运单制单重量 */
    private BigDecimal waybillWeight;
}
