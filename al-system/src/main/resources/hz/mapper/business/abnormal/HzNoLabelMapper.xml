<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.abnormal.mapper.HzNoLabelMapper">
    
    <resultMap type="HzNoLabel" id="HzNoLabelResult">
        <result property="id"    column="id"    />
        <result property="flightNo"    column="flight_no"    />
        <result property="sourcePort"    column="source_port"    />
        <result property="desPort"    column="des_port"    />
        <result property="pieces"    column="pieces"    />
        <result property="username"    column="username"    />
        <result property="remark"    column="remark"    />
        <result property="registerTime"    column="register_time"    />
        <result property="closeCase"    column="close_case"    />
        <result property="imgUrl"    column="img_url"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectHzNoLabelVo">
        select id, flight_no, source_port, des_port, pieces, username, remark, register_time, close_case, img_url, create_by, create_time from hz_no_label
    </sql>

    <select id="selectHzNoLabelList" parameterType="HzNoLabel" resultMap="HzNoLabelResult">
        <include refid="selectHzNoLabelVo"/>
        <where>  
            <if test="flightNo != null  and flightNo != ''"> and flight_no = #{flightNo}</if>
            <if test="registerTime != null "> and register_time = #{registerTime}</if>
            <if test="closeCase != null "> and close_case = #{closeCase}</if>
        </where>
    </select>
    
    <select id="selectHzNoLabelById" parameterType="Long" resultMap="HzNoLabelResult">
        <include refid="selectHzNoLabelVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertHzNoLabel" parameterType="HzNoLabel" useGeneratedKeys="true" keyProperty="id">
        insert into hz_no_label
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="flightNo != null">flight_no,</if>
            <if test="sourcePort != null">source_port,</if>
            <if test="desPort != null">des_port,</if>
            <if test="pieces != null">pieces,</if>
            <if test="username != null">username,</if>
            <if test="remark != null">remark,</if>
            <if test="registerTime != null">register_time,</if>
            <if test="closeCase != null">close_case,</if>
            <if test="imgUrl != null">img_url,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="flightNo != null">#{flightNo},</if>
            <if test="sourcePort != null">#{sourcePort},</if>
            <if test="desPort != null">#{desPort},</if>
            <if test="pieces != null">#{pieces},</if>
            <if test="username != null">#{username},</if>
            <if test="remark != null">#{remark},</if>
            <if test="registerTime != null">#{registerTime},</if>
            <if test="closeCase != null">#{closeCase},</if>
            <if test="imgUrl != null">#{imgUrl},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateHzNoLabel" parameterType="HzNoLabel">
        update hz_no_label
        <trim prefix="SET" suffixOverrides=",">
            <if test="flightNo != null">flight_no = #{flightNo},</if>
            <if test="sourcePort != null">source_port = #{sourcePort},</if>
            <if test="desPort != null">des_port = #{desPort},</if>
            <if test="pieces != null">pieces = #{pieces},</if>
            <if test="username != null">username = #{username},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="registerTime != null">register_time = #{registerTime},</if>
            <if test="closeCase != null">close_case = #{closeCase},</if>
            <if test="imgUrl != null">img_url = #{imgUrl},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>