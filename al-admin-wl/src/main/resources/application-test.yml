# 数据源配置
spring:
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: false
  rabbitmq:
    publisher-confirm-type: correlated
    publisher-returns: true
    username: air_cargo
    password: airCargo@2023
    host: *************
    port: 5672
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      stat-view-servlet:
        enabled: false
      # 主库数据源
      master:
        url: *****************************************************************************************************************************************************************************
        username: root
        password: Jskfb@2021
        # （生产）主库数据源
#      master:
#        url: ****************************************************************************************************************************************************************************
#        username: root
#        password: Jskfb@2024..
      slave:
        # 从数据源开关/默认关闭
        enabled: true
#        url: *******************************************
        url: *******************************************
        username: cfps_jk
        password: Cfps_jk_8523
        driver-class-name: oracle.jdbc.OracleDriver
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置连接超时时间
      connectTimeout: 30000
      # 配置网络超时时间
      socketTimeout: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 30000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 60000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 28000000
      # 配置检测连接是否有效
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: false
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: admin
        login-password: Jskfb@2021
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  # redis 配置
  redis:
    # 地址
#    host: **********
#    host: *************
    host: 127.0.0.1
#     端口，默认为6379
    port: 6379
    # 数据库索引
    database: 2
    # 密码
#    password: Jskfb@2019
#    password: jskfb@2024!
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms


sms:
  address: http://***********:10002/kgkj_sms/api/sms/send
  appId: WLGS20230921
  appSecret: XghOTV8KIaofALyk6IyWIQ==
  taskFailureNoticeConfigCode: kgkj_system_warning_notice
  ruKuConfigCode: wlgs_ruku_paiban
  hangBanyanwuConfigCode: wlgs_hangban_yanwu
  hangBanbeijiangConfigCode: wlgs_hangban_beijiang
  liHuoConfigCode: wlgs_lihuo_wancheng
  weiTiHuoConfigCode: wlgs_weitihuo

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: false
  # 请求前缀
  pathMapping: /dev-api

cargo:
  url: http://*************:8892/cfpsApi/openApi/loadAWBInfo
  customerUrl: http://*************:8892/cfpsApi/openApi/loadCustomer

mq:
  queueName: dynflightLoad

image:
  fileNames:
    - png
    - jpg
    - jpeg
    - pdf
    - apk
    - bmp


wechat:
  qrCode:
    appId: wxdc4716d9e03b5ea3
    appSecret: 8aa5cdb9eca60610be982ae0ec630619

agent:
  qrCode: https://hkgwl.gcac-kj.com:8086/pages/consignment/identity?name=

ui:
  agentPath: /pages/consignment/identity


hzCable:
  account: 9ZYtmgDRwfHu8J9aSXFg9A==
  loginUrl: http://*************:7060/outerLogin/getToken?account=
  getMsg: http://*************:7060/outerSystem/generateOriginMsg
  sendMsg: http://*************:7060/bizForwardMsg/forwardOriginMsg
  FTPAddress: *************

#人脸比对
comparison:
  appId: 1869213201895727106
  appKey: d5c143149ccd4ef0a3bab345537810e8
  publicKey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJCR_sAKhlYtztlZy-5dmIdItDW2K-qIy6auuS75lZN22tTuxXTLaZt_6jegl-xYXDlCeORwlh6goxWjB1kE5ykCAwEAAQ

#一级商户
firstMerchant:
  #客户号
  clientNo: '**********'
  #账号
  accountNo: '*********'
  #商户号
  merchantNo: '****************'
  #商户名
  merchantName: '何峰磊企业'
  #微信子商户号
  vxMerchantNo: '*********'
  #支付分账产品签约编号
  contractCode: '01202502260920090002'

secondMerchant:
  clientNo: '**********'
  accountNo: '*********'
  merchantNo: '****************'
  merchantName: '毕帅帅企业'
  contractCode: '01202502260943230004'

secondMerchant2:
  clientNo: '**********'
  accountNo: '*********'
  merchantNo: '****************'
  merchantName: '元查文企业'
  contractCode: '01202502271632220001'

#民生接口地址
bankUrl:
  #网关支付
  unityPayReqUrl: https://wxpay.cmbc.com.cn/mobilePlatform/appserver/lcbpPay.do
  #支付结果查询
  paymentResultSelectUrl: https://wxpay.cmbc.com.cn/mobilePlatform/appserver/paymentResultSelectNew.do
  #退款
  cancelTransUrl: https://wxpay.cmbc.com.cn/mobilePlatform/appserver/cancelTrans.do
  #确认支付
  confirmPayUrl: https://wxpay.cmbc.com.cn/securedTransaction/confirmPayment.do

#测试环境证书
merchant:
  #银行公钥
  bankPublicKey: cust/cmbcTest.cer
  #商户公钥
  publicKey: cust/cust0001.cer
  #商户私钥
  privateKey: cust/cust0001.sm2
  #商户私钥密码
  pwd: 123abc