package com.gzairports.wl.charge.domain.query;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 公布运价查询参数
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@Data
public class FreightRateQuery {

    /** id集合 */
    private List<Long> ids;

    /** 运价名称 */
    private String rateName;

    /** 适用单证类型 */
    private String applicableDocuments;

    /** 生效日期 */
    private Date effectiveDate;

    /** 截止日期 */
    private Date expirationDate;

    /** 所属单位 */
    private Long deptId;
}
