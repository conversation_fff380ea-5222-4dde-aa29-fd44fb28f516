package com.gzairports.common.business.reporter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.reporter.domain.ReportDepPayCost;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-03
 */
@Mapper
public interface ReportDepPayCostMapper extends BaseMapper<ReportDepPayCost> {

    /**
     * 主单预授权费用
     * @param id id
     * @return 结果
     */
    BigDecimal selectSundryFee(Long id);

    void deleteBatch(@Param("reportIds") List<Long> reportIds);

    int insertBatchSomeColumn(List<ReportDepPayCost> payList);
}
