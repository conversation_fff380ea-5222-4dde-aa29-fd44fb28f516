package com.gzairports.common.business.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 货检推送数据
 *
 * <AUTHOR>
 * @date 2024-11-04
 */
@Data
public class CargoSecurityVo {

    /** 承运航空公司 */
    private String airWays;

    /** 航班号 */
    private String flightNo;

    /** 日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date execDate;

    /** 航空货运单号 */
    private String waybillCode;

    /** 航空货运销售代理人名称 */
    private String agentName;

    /** 航空货物托运人名称 */
    private String shipper;

    /** 收运员安全检查结论 */
    private String checkInference;

    /** 收运员 */
    private String collectName;

    /** 货物列表 */
    private List<CargoInfoVo> cargoInfoVos;

    /** 代理人经办人员身份证信息 */
    private IdentityInfoVo identityInfoVo;

    /** 代理人经办人员头像 */
    private List<String> icons;

    /** 电子安检申报单地址 */
    private List<String> securityPdfUrls;

    /** 相关随附文件 */
    private List<String> transportFilePdfs;
}
