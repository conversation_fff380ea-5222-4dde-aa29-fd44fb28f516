package com.gzairports.common.business.departure.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.business.departure.domain.Booking;
import com.gzairports.common.business.departure.domain.BookingRecord;
import com.gzairports.common.business.departure.domain.query.BookingQuery;
import com.gzairports.common.business.departure.domain.vo.BookingVo;

import java.util.List;

/**
 * 订舱Service接口
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
public interface IBookingService extends IService<Booking> {

    /**
     * 查询订舱列表
     * @param query 查询参数
     * @return 订舱列表
     */
    List<BookingVo> selectList(BookingQuery query);

    /**
     * 新增订舱数据
     * @param booking 订舱数据
     * @return 结果
     */
    int add(Booking booking);

    /**
     * 查看详情
     * @param id 订舱id
     * @return 结果
     */
    BookingVo getInfo(Long id);

    /**
     * 审核
     * @param record 审核信息
     * @return 结果
     */
    int bookingReview(BookingRecord record);

    /**
     * 批量订舱审核
     * @param record 审核信息
     * @return 结果
     */
    int reviewBatch(BookingRecord record);
}
