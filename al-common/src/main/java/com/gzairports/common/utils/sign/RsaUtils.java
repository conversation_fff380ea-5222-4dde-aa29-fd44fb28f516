package com.gzairports.common.utils.sign;

import com.gzairports.common.utils.StringUtils;
import org.apache.commons.codec.binary.Base64;
import javax.crypto.Cipher;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

/**
 * Created by david on 2023/10/20
 */
public class RsaUtils {

    /**
     * Rsa 私钥
     */
    public static String privateKey = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAIc0rClV" +
            "+dqnkwTVNL6iJSe1i1Kbb86uWIYSMv646JuU29neTmR3TM+v6Ywmh6HXn3bX4cezI" +
            "+tMS7N+lEFVpTMlBkMylWKw/bZfmCiExuHKrJAFbeDt50OikW0pgSH88/dvS25lu" +
            "SlPb20IRO7r16fNMZsIkgV9WPVSQGGBtnBDAgMBAAECgYBk5S+cYSZkvyx9LMWBhOqJ" +
            "KXbgnFHhQEWbNzWK1F3wzDML8FM7sbNGQRXdmlsYsRm0zAEWz138lyDev5TO9KSj2XYL" +
            "Wopj0xw/cvUFW/xN3n5aTqUp4f2hNRpYBKb8hpB3aEDCcCGFEWRoALApf9jvd03cmqum" +
            "QzhkOWgqCoyAQQJBAO9RpfgG2ewFkA0N14BPpteDd/H2gpSxQMTb6JXL4OY+Xn33dtgJ79qWKsp9DobSjoiC04selc2C9" +
            "+jrfFrjjlMCQQCQoUCrLy4cJRR6EatZh1UbnKBeF7Bjc8hp1v34lSkQhwVN36bv38CzlQDt" +
            "+WeaPgsk7v85fAQaKrABFtEEEvhRAkEAjuDpPL4iZwYfLZwA+CsVyf9EoiHxbgKxT5aXMq" +
            "+pEu1TjtMqkerE13TmEUQ+YwHcdOXelGVrPGofxmCpOr423wJABP" +
            "+dtYze4hgh+goMSv7TYS3rmPTmorlIrufARUwabDjOZR" +
            "+arGWmSk6XBqJ9Ayarye38oib4GVNqW89HEAX70QJBAKGm2d" +
            "+9u6Y6mQgsiaqs2lexWJzKmhwDcQujEXLgS0JLEawxCgcX75SfHyhVc1RrrYUGYJJ6c2wfUP3dgzIJR/s=";

    /**
     * 私钥解密
     *
     * @param text 待解密的文本
     * @return 解密后的文本
     */
    public static String decryptByPrivateKey(String text) throws Exception
    {
        return decryptByPrivateKey(privateKey, text);
    }

    /**
     * 公钥解密
     *
     * @param publicKeyString 公钥
     * @param text 待解密的信息
     * @return 解密后的文本
     */
    public static String decryptByPublicKey(String publicKeyString, String text) throws Exception
    {
        X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(Base64.decodeBase64(publicKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(x509EncodedKeySpec);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, publicKey);
        byte[] result = cipher.doFinal(Base64.decodeBase64(text));
        return new String(result);
    }

    /**
     * 私钥加密
     *
     * @param privateKeyString 私钥
     * @param text 待加密的信息
     * @return 加密后的文本
     */
    public static String encryptByPrivateKey(String privateKeyString, String text) throws Exception
    {
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, privateKey);
        byte[] result = cipher.doFinal(text.getBytes());
        return Base64.encodeBase64String(result);
    }

    /**
     * 私钥解密
     *
     * @param privateKeyString 私钥
     * @param text 待解密的文本
     * @return 解密后的文本
     */
    public static String decryptByPrivateKey(String privateKeyString, String text) throws Exception
    {
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec5 = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec5);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        if (StringUtils.isEmpty(text)){
            return null;
        }
        byte[] result = cipher.doFinal(Base64.decodeBase64(text));
        return new String(result);
    }

    /**
     * 公钥加密
     *
     * @param publicKeyString 公钥
     * @param text 待加密的文本
     * @return 加密后的文本
     */
    public static String encryptByPublicKey(String publicKeyString, String text) throws Exception
    {
        X509EncodedKeySpec x509EncodedKeySpec2 = new X509EncodedKeySpec(Base64.decodeBase64(publicKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(x509EncodedKeySpec2);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        byte[] result = cipher.doFinal(text.getBytes());
        return Base64.encodeBase64String(result);
    }

    /**
     * 构建RSA密钥对
     *
     * @return 生成后的公私钥信息
     */
    public static RsaKeyPair generateKeyPair() throws NoSuchAlgorithmException
    {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
        keyPairGenerator.initialize(1024);
        KeyPair keyPair = keyPairGenerator.generateKeyPair();
        RSAPublicKey rsaPublicKey = (RSAPublicKey) keyPair.getPublic();
        RSAPrivateKey rsaPrivateKey = (RSAPrivateKey) keyPair.getPrivate();
        String publicKeyString = Base64.encodeBase64String(rsaPublicKey.getEncoded());
        String privateKeyString = Base64.encodeBase64String(rsaPrivateKey.getEncoded());
        return new RsaKeyPair(publicKeyString, privateKeyString);
    }

    /**
     * RSA密钥对对象
     */
    public static class RsaKeyPair
    {
        private final String publicKey;
        private final String privateKey;

        public RsaKeyPair(String publicKey, String privateKey)
        {
            this.publicKey = publicKey;
            this.privateKey = privateKey;
        }

        public String getPublicKey()
        {
            return publicKey;
        }

        public String getPrivateKey()
        {
            return privateKey;
        }
    }

    public static void main(String[] args) throws NoSuchAlgorithmException {
        RsaKeyPair rsaKeyPair = generateKeyPair();
        String privateKey = rsaKeyPair.getPrivateKey();
        String publicKey = rsaKeyPair.getPublicKey();
        System.out.println("私钥：" + privateKey);
        System.out.println("公钥：" + publicKey);
    }
}
