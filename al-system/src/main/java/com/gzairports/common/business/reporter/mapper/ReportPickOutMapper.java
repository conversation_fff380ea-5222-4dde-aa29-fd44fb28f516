package com.gzairports.common.business.reporter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.reporter.domain.ReportPickOut;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * Created by david on 2025/3/4
 */
@Mapper
public interface ReportPickOutMapper extends BaseMapper<ReportPickOut> {
    void saveOrUpdateBatch(List<ReportPickOut> pickOutList);
}
