package com.gzairports.wl.departure.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.gzairports.common.annotation.Excel;
import com.gzairports.common.pdf.PdfPrintAnnotation;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import static com.gzairports.common.pdf.PdfPrintAnnotation.PdfFieldType.*;

/**
 * 托运管理
 *
 * <AUTHOR>
 * @date 2024-05-08
 */
@Data
@TableName("wl_dep_consign")
public class Consign {

    /** 主键id */
    private Long id;

    /** 业务号 */
    @Excel(name = "业务号")
    private String code;

    /** 目的站 */
    private String desPort;

    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "desPort")
    private String desPortStr;

    /** 关联主单号 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "waybillCode")
    private String masterWaybillCode;

    /** 托运单位 */
    @Excel(name = "托运单位")
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipperCompany")
    private String shipperCompany;

    /** 托运人 */
    @Excel(name = "托运人")
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipperName")
    private String shipperName;

    /** 托运人电话 */
    @Excel(name = "托运人电话")
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipperPhone")
    private String shipperPhone;

    /** 托运人地址 */
    @Excel(name = "托运人地址")
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipperAddress")
    private String shipperAddress;

    /** 托运人地区 */
    private String shipperRegion;

    /** 托运人身份证号 */
    @Excel(name = "托运人身份证号")
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipperIdCard")
    private String shipperIdCard;

    /** 托运人签名 */
    @Excel(name = "托运人签名")
    private String shipperSign;

    /** 托运人签名图片Base64 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = IMAGE, pdfFieldName = "shipperSign")
    private String signImage;

    /** 收货人单位 */
    @Excel(name = "收货人单位")
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "consigneeCompany")
    private String consigneeCompany;

    /** 收货人 */
    @Excel(name = "收货人")
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "consigneeName")
    private String consigneeName;

    /** 收货人电话 */
    @Excel(name = "收货人电话")
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "consigneePhone")
    private String consigneePhone;

    /** 收货人地址 */
    @Excel(name = "收货人地址")
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "consigneeAddress")
    private String consigneeAddress;

    /** 收货人地区 */
    private String consigneeRegion;

    /** 运单号 */
    @Excel(name = "运单号")
    private String waybillCode;

    /** 货物名称 */
    @Excel(name = "货物名称")
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "cargoName")
    private String cargoName;

    /** 货品代码 */
    @TableField(exist = false)
    private String cargoCode;

    /** 包装 */
    @Excel(name = "包装")
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "pack")
    private String pack;

    /** 件数 */
    @Excel(name = "件数")
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "quantity")
    private Integer quantity;

    /** 实际重量 */
    @Excel(name = "实际重量")
    private BigDecimal actualWeight;

    /** 实际重量 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "actualWeight")
    private String actualWeightStr;

    /** 计费重量 */
    @Excel(name = "计费重量")
    private BigDecimal chargeWeight;

    /** 计费重量 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "chargeWeight")
    private String chargeWeightStr;

    /** 计费尺寸 */
    @Excel(name = "计费尺寸")
    private String chargeSize;

    /** 是否保险 */
    private Integer insurance;

    @Excel(name = "是否保险")
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "insure")
    private String insure;

    /** 保险价值 */
    @Excel(name = "保险价值")
    private BigDecimal insuranceValue;

    /** 保险价值 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "insuranceValue")
    private String insuranceValueStr;

    /** 是否危险品 */
    private Integer isDanger;

    @Excel(name = "是否危险品")
    @PdfPrintAnnotation(pdfFieldType = CHOICE, pdfFieldName = "danger")
    @TableField(exist = false)
    private String danger;

    /** 是否贵重物品 */
    private Integer isValuable;

    @Excel(name = "是否贵重物品")
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = CHOICE, pdfFieldName = "valuable")
    private String valuable;

    /** 经办人 */
    private String agent;

    /** 电子托运书pdf地址 */
    private String pdfUrl;

    /** 托运时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date consignTime;

    /** 所属单位 */
    private Long deptId;

    /** 是否可编辑 */
    private Integer isEdit;

    /** 是否删除 */
    private Integer isDel;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
