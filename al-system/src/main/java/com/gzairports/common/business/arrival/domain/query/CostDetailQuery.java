package com.gzairports.common.business.arrival.domain.query;

import com.gzairports.wl.departure.domain.query.BasePageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class CostDetailQuery extends BasePageQuery {

    /** 进出港类型 */
    private String type = "ARR";

    /** 部门ID */
    private Long deptId;

    /** 运单号 */
    private String waybillCode;

    /** 结算方式 */
    private Integer settleType;

    /** 收货人 */
    private String consign;

    /** 流水号 */
    private String serialNo;

    /** 办单经办人 */
    private String handleBy;

    /** 办单时间(开单时间) */
    private LocalDateTime startTime;

    /** 办单时间(开单时间) */
    private LocalDateTime endTime;

    /** 代理人 */
    private List<String> deptIdList;

}
