package com.gzairports.wl.charge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.wl.charge.domain.ChargeItem;
import com.gzairports.wl.charge.domain.query.ChargeItemQuery;
import com.gzairports.wl.charge.domain.vo.ChargeItemVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 收费项目Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@Mapper
public interface ChargeItemMapper extends BaseMapper<ChargeItem> {

    /**
     * 查询收费项目列表
     *
     * @param query 查询参数
     * @return 收费项目列表
     */
    List<ChargeItemVO> selectListByQuery(ChargeItemQuery query);

    /**
     * 查询收费项目
     *
     * @param id 收费项目主键
     * @return 收费项目
     */
    ChargeItemVO selectChargeById(Long id);

    /**
     * 新增收费项目
     *
     * @param item 收费项目
     * @return 结果
     */
    void insertChargeItem(ChargeItem item);

    /**
     * 修改收费项目
     *
     * @param item 收费项目
     * @return 结果
     */
    int updateChargeItem(ChargeItem item);

    /**
     * 删除收费项目
     *
     * @param id 收费项目主键
     * @return 结果
     */
    int removeChargeItem(Long id);

    /**
     * 根据单位查询默认收费项目
     *
     * @param deptId 单位id
     * @return 结果
     */
    List<ChargeItem> selectByDept(@Param("deptId") Long deptId,@Param("type") String type);
}
