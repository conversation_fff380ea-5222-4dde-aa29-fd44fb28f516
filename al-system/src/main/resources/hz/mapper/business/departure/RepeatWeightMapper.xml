<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.departure.mapper.RepeatWeightMapper">
    <select id="selectListByQuery" resultType="com.gzairports.hz.business.departure.domain.vo.RepeatWeightVo">
        WITH RankedFlights AS (
        SELECT afi.flight_id as id,
            afi.exec_date,
            CONCAT(afi.air_ways, afi.flight_no) AS flight_no,
            afi.craft_type,
            afi.is_offin,
            afi.air_line_short as leg,
            afi.start_scheme_takeoff_time,
            afi.start_alterate_takeoff_time,
            afi.start_real_takeoff_time,
            hfl.load_time,
            hfl.group_time,
            hfl.weight_time,
            hfl.state,
            afi.craft_no,
            ROW_NUMBER() OVER (PARTITION BY afi.flight_id ORDER BY hfl.weight_time DESC) AS rn
        FROM all_flight_info afi
        LEFT JOIN hz_flight_load hfl ON afi.flight_id = hfl.flight_id
        )
        SELECT id,
            exec_date,
            flight_no,
            craft_type,
            leg,
            is_offin,
            start_scheme_takeoff_time,
            start_alterate_takeoff_time,
            start_real_takeoff_time,
            load_time,
            group_time,
            weight_time,
            state,
            craft_no
        FROM RankedFlights
        <where>
            rn = 1 and is_offin = 'D'
            <if test="execDate != null">
                AND exec_date = #{execDate}
            </if>
            <if test="flightNo != null and flightNo != ''">
                AND flight_no like CONCAT('%',#{flightNo},'%')
            </if>
            <if test="state != null and state != ''">
                <choose>
                    <when test="state == 'not_pre'">
                        AND (load_time is null and weight_time is null)
                    </when>
                    <when test="state == 'been_group'">
                        AND (load_time is not null and weight_time is null)
                    </when>
                    <otherwise>
                        AND (load_time is not null and weight_time is not null)
                    </otherwise>
                </choose>
            </if>
            <if test="startTime != null">
                and start_scheme_takeoff_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and start_scheme_takeoff_time <![CDATA[<=]]> #{endTime}
            </if>
        </where>
        order by start_scheme_takeoff_time asc
    </select>
<!--    <if test="state != null and state != ''">
        <choose>
            <when test="state == 'been_group'">
                AND (state = "been_group" or state = "been_pre"
                or state = "not_group" or state = "been_dep")
            </when>
            <when test="state == 'been_handed'">
                AND (state = "not_handed" or state = "been_weight"
                or state = "been_handed" or state = "been_dep")
            </when>
            <otherwise>
                AND (state = #{state} or state = 'been_dep')
            </otherwise>
        </choose>
    </if>-->
    <select id="getInfo" resultType="com.gzairports.hz.business.departure.domain.vo.WeightInfoVo">
        select id, flight_load_id, group_uld_id, cabin, uld, des1, weight, board_cargo_weight, plate_weight, board_weight, file_weight
        from hz_dep_repeat_weight where id = #{id}
    </select>
    <select id="getWaybills" resultType="com.gzairports.hz.business.departure.domain.vo.HzDepRepeatWaybillsVo">
        select aaw.id,aaw.waybill_code,aaw.cargo_name,aaw.special_cargo_code1,aaw.des_port,hdguw.quantity,hdguw.weight,
               hfluw.collect_id,hdgu.uld,hfluw.load_uld_id
        from hz_dep_group_uld hdgu
                 left join hz_dep_group_uld_waybill hdguw on hdgu.id = hdguw.group_uld_id
                 left join all_air_waybill aaw on hdguw.waybill_code = aaw.waybill_code
                 left join hz_flight_load_uld_waybill hfluw on hfluw.waybill_id = aaw.id
        where hdgu.id = #{id}
    </select>
    <select id="selectDataByQuery" resultType="com.gzairports.hz.business.departure.domain.HzDepRepeatWeight">
        select id, flight_load_id, group_uld_id, cabin, uld, des1,quantity, weight,
               board_cargo_weight, plate_weight, board_weight, file_weight, reality_weight, create_time
        from hz_dep_repeat_weight
        <where>
            <if test="flightLoadId != null">
                and flight_load_id = #{flightLoadId}
            </if>
            <if test="groupUldId != null">
                and group_uld_id = #{groupUldId}
            </if>
            <if test="cabin != null and cabin != ''">
                and cabin = #{cabin}
            </if>
            <if test="uld != null and uld != ''">
                and uld = #{uld}
            </if>
            <if test="weight != null">
                and weight = #{weight}
            </if>
        </where>
    </select>

    <select id="selectRepeatWeightList"
            resultType="com.gzairports.common.business.reporter.domain.ReportDepRepeatWeight">
        select hdrw.file_weight,
               hdrw.quantity,
               hdrw.weight,
               hdrw.board_weight,
               hdrw.board_cargo_weight,
               hdrw.uld,
               hdrw.flight_load_id,
               aaw.flight_date1 as flight_date,
               aaw.flight_no1 as flight_no,
               aaw.waybill_code
        from hz_dep_repeat_weight hdrw
                 left join hz_dep_group_uld hdgu on hdgu.id = hdrw.group_uld_id
                 left join hz_dep_group_uld_waybill hdguw on hdguw.group_uld_id = hdgu.id
                 left join all_air_waybill aaw on aaw.waybill_code = hdguw.waybill_code
        where aaw.flight_date1 between #{lastSyncTime} and #{dateNow}
    </select>

    <select id="selectWaitSyncData" resultType="com.gzairports.common.business.reporter.domain.ReportDepRepeatWeight">
        select hdrw.id,
        hdrw.file_weight,
        hdrw.quantity as file_quantity,
        hdrw.quantity,
        hdrw.weight,
        hdrw.board_weight,
        hdrw.board_cargo_weight,
        hdrw.uld,
        hdrw.flight_load_id,
        hdrw.oper_name,
        aaw.flight_date1 as flight_date,
        aaw.flight_no1 as flight_no,
        aaw.waybill_code
        from hz_dep_repeat_weight hdrw
        left join hz_dep_group_uld hdgu on hdgu.id = hdrw.group_uld_id
        left join hz_dep_group_uld_waybill hdguw on hdguw.group_uld_id = hdgu.id
        left join all_air_waybill aaw on aaw.waybill_code = hdguw.waybill_code
        where hdrw.flight_load_id in
        <foreach collection="flightLoadIdList" item="flightLoadId" open="(" separator="," close=")">
            #{flightLoadId}
        </foreach>
    </select>

</mapper>