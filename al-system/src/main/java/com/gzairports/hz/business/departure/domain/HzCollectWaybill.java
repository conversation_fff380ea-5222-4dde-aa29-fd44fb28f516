package com.gzairports.hz.business.departure.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 运单收运数据
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Data
@TableName("hz_collect_waybill")
public class HzCollectWaybill {

    /** 主键id */
    private Long id;

    /** 运单id */
    private Long waybillId;

    /** 运单号 */
    private String waybillCode;

    /** uld号 */
    private String uld;

    /** 操作人 */
    private String operName;

    /** 是否真实收运 */
    private Integer isReal;

    /** 收运时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date collectTime;

    /** 收运件数 */
    private Integer quantity;

    /** 收运重量 */
    private BigDecimal weight;

    /** 收运状态 REAL 真实收运 VIRTUAL 虚拟收运 CANCEL 取消收运 */
    private String status;

    /** 备注 */
    private String remark;

    /** 原因 */
    private String reason;
}
