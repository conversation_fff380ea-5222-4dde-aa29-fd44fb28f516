package com.gzairports.hz.business.reporter.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @author: lan
 * @Desc: 货站报表设置显示字段设置表
 * @create: 2025-03-05 17:00
 **/

@Data
@TableName("hz_report_set_relation")
public class HzReportSetRelation {
    /** 主键id */
    private Long id;

    /** 报表设置id */
    private Long setId;

    /** 关联字段中文 */
    private String relationFieldNameCn;
    private String relationFieldNameCn2;

    /** 关联字段 */
    private String relationFieldName;

    private String relationFieldName2;

    /** 主表 0:分单 1:分单费用数据 2:主单 3:主单收运数据
     4:预支付费用数据 5:配载数据 6:结算费用数据
     7:拉下 8:理货数据 9:提货办单数据 10:进港费用数据
     11:提货出库数据 12:运单保障节点 13:冷藏登记
     14:服务 */
    private Integer masterTable;

    /** 从表 */
    private Integer slaveTable;
    private Integer slaveTable2;

    /** 报表设置主表从表字段id */
    private Long fieldId;
    private Long fieldId2;
}
