package com.gzairports.common.business.arrival.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 散客运单查询列表
 *
 * <AUTHOR>
 * @date 2024-07-22
 */
@Data
public class RetailVo {

    /** 理货id */
    private Long tallyId;

    /** 日期 */
    private Date execDate;

    /** 航班号 */
    private String flightNo;

    /** 运单号 */
    private String waybillCode;

    /** 可提货件数 */
    private Integer canPickUpQuantity;

    /** 可提货重量 */
    private BigDecimal canPickUpWeight;

    /** 状态 */
    private String status;

    /** 操作时间 */
    private Date tallyTime;
}
