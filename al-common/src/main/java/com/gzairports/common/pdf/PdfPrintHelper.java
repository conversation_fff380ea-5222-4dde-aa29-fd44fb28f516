package com.gzairports.common.pdf;

import com.itextpdf.text.Font;
import com.itextpdf.text.Image;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.*;
import org.springframework.core.io.ClassPathResource;

import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * pdf模板数据生成
 * <AUTHOR>
 * @date 2024-08-19
 */
public class PdfPrintHelper {

    public static byte[] getPdfDataFromTemplate(Object bean, String templateFullPath) throws Exception {

        ByteArrayOutputStream os = new ByteArrayOutputStream();
        PdfReader reader = new PdfReader(templateFullPath);
        PdfStamper ps = new PdfStamper(reader, os);
        AcroFields form = ps.getAcroFields();

        BaseFont bf = null;
        if ("com.gzairports.wl.departure.domain.vo.MawbVo".equals(bean.getClass().getName())
                || "com.gzairports.common.business.departure.domain.Mawb".equals(bean.getClass().getName())
        || "com.gzairports.hz.business.departure.domain.vo.CaptainNotice".equals(bean.getClass().getName())){
            ClassPathResource resource = new ClassPathResource("simhei.ttf");
            if (resource.exists()) {
                String path = resource.getPath();
                bf = BaseFont.createFont(path, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                Field[] fields = bean.getClass().getDeclaredFields();
                for (Field field : fields) {
                    field.setAccessible(true);
                    PdfPrintAnnotation printAnnotation = field.getAnnotation(PdfPrintAnnotation.class);
                    if ( null == printAnnotation){
                        continue;
                    }
                    String fieldName = printAnnotation.pdfFieldName();
                    form.setFieldProperty(fieldName, "textfont", bf, null);
                }
            }
        }else {
            ClassPathResource resource = new ClassPathResource("simsun.ttc");
            if (resource.exists()) {
                String path = resource.getPath();
                bf = BaseFont.createFont(path + ",0", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            }
        }
        form.addSubstitutionFont(bf);
        putDataToForm(bean, form,ps);
        ps.setFormFlattening(true);
        ps.close();
        reader.close();
        return os.toByteArray();
    }

    private static void putDataToForm(Object bean, AcroFields form,PdfStamper ps) throws Exception {
        Field[] fields = bean.getClass().getDeclaredFields();
        for(Field field:fields){
            field.setAccessible(true);
            PdfPrintAnnotation printAnnotation = field.getAnnotation(PdfPrintAnnotation.class);
            if ( null == printAnnotation){
                continue;
            }
            Object fieldObject = field.get(bean);
            String fieldStringValue;
            PdfPrintAnnotation.PdfFieldType fieldType = printAnnotation.pdfFieldType();
            String fieldName = printAnnotation.pdfFieldName();
            if(null == fieldObject || "".equals(fieldName.trim())){
                continue;
            }

            switch (fieldType){
                case TEXT:
                    fieldStringValue = getBeanFieldStringValue(fieldObject);
                    if( null != form.getField(fieldName) ){
                        form.setField(fieldName,fieldStringValue);
                    }
                    break;
                case CHOICE:
                    fieldStringValue = getBeanFieldStringValue(fieldObject);
                    if(null != form.getField(fieldName)){
                        form.setField(fieldName, fieldStringValue, true);
                    }
                    break;
                case CHOICE_MUTI:
                    fieldStringValue = getBeanFieldStringValue(fieldObject);
                    String[] strArray = fieldStringValue.split(",");
                    for(String string : strArray){
                        if(null != form.getField(fieldName + "_" + string)){
                            form.setField(fieldName + "_" + string, "Yes", true);
                        }
                    }
                    break;
                case DATE_YY_MM_DD:
                    fieldStringValue = getBeanFieldStringValue(fieldObject);
                    if(null != form.getField(fieldName +"_year")){
                        form.setField(fieldName +"_year", fieldStringValue.substring(0,4));
                    }
                    if(null != form.getField(fieldName +"_month")){
                        form.setField(fieldName +"_month", fieldStringValue.substring(5,7));
                    }
                    if(null != form.getField(fieldName +"_day")){
                        form.setField(fieldName +"_day", fieldStringValue.substring(8,10));
                    }

                case DATE_YY_MM_DD_HH:
                    fieldStringValue = getBeanFieldStringValue(fieldObject);
                    if(null != form.getField(fieldName +"_year")){
                        form.setField(fieldName +"_year", fieldStringValue.substring(0,4));
                    }
                    if(null != form.getField(fieldName +"_month")){
                        form.setField(fieldName +"_month", fieldStringValue.substring(5,7));
                    }
                    if(null != form.getField(fieldName +"_day")){
                        form.setField(fieldName +"_day", fieldStringValue.substring(8,10));
                    }
                    if(null != form.getField(fieldName +"_hour")){
                        form.setField(fieldName +"_hour", fieldStringValue.substring(11,13));
                    }
                    break;
                case BOOLEAN:
                    fieldStringValue = getBeanFieldStringValue(fieldObject);
                    if(null != form.getField( fieldName ) && "1".equals(fieldStringValue)){
                        form.setField(fieldName , "Yes", true);
                    }
                    break;
                case IMAGE:
                    fieldStringValue = getBeanFieldStringValue(fieldObject);
                    if(null != form.getField( fieldName ) && !"".equals(fieldStringValue) ){
                        int pageNo = form.getFieldPositions(fieldName).get(0).page;
                        Rectangle signRect = form.getFieldPositions(fieldName).get(0).position;
                        float x = signRect.getLeft();
                        float y = signRect.getBottom();
                        Image image = Image.getInstance(Base64.getDecoder().decode(fieldStringValue));
                        // 获取操作的页面
                        PdfContentByte under = ps.getOverContent(pageNo);
                        // 根据域的大小缩放图片
                        image.scaleToFit(signRect.getWidth(), signRect.getHeight());
                        // 添加图片
                        image.setAbsolutePosition(x, y);
                        under.addImage(image);
                    }
                    break;
                case LIST:
                    List<Object> objectList = (List) fieldObject;
                    for(int i = 0; i< objectList.size(); i++){
                        Object obj = objectList.get(i);
                        Field[] objFieldArray = obj.getClass().getDeclaredFields();
                        for(Field objField:objFieldArray) {
                            objField.setAccessible(true);
                            PdfPrintAnnotation objAnnotation = objField.getAnnotation(PdfPrintAnnotation.class);
                            if (null == objAnnotation) {
                                continue;
                            }
                            String objFieldName = objAnnotation.pdfFieldName();
                            objFieldName = fieldName + "_" + (i+1) + "_" + objFieldName;
                            fieldStringValue = getBeanFieldStringValue(objField.get(obj));
                            if( null != form.getField(objFieldName) ){
                                form.setField(objFieldName,fieldStringValue);
                            }
                        }
                    }
                    break;
                default:
                    fieldStringValue = getBeanFieldStringValue(fieldObject);
                    form.setField(fieldName,fieldStringValue);
                    break;
            }
        }
    }

    private static  String getBeanFieldStringValue(Object obj){
        if (obj == null) {
            return null;
        }
        String value = null;
        //简单的查检列类型
        switch (obj.getClass().getSimpleName()) {
            case "String":
                value = (String) obj;
                break;
            case "Date":
                Date date = (Date) obj;
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                value = sdf.format(date);

                break;
            case "Boolean":
            case "Float":
            case "Integer":
                value = String.valueOf(obj);
                break;
            default :
                value = obj.toString();
                break;
        }
        return value == null ? null : value.trim();
    }

}
