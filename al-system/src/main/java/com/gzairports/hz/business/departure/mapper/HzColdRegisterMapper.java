package com.gzairports.hz.business.departure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.reporter.domain.ReportColdRegister;
import com.gzairports.hz.business.departure.domain.HzColdRegister;
import com.gzairports.hz.business.departure.domain.query.HzColdRegisterQuery;
import com.gzairports.hz.business.departure.domain.vo.ColdQueryVo;
import com.gzairports.hz.business.departure.domain.vo.ColdRegisterVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.security.core.parameters.P;

import java.util.Date;
import java.util.List;

/**
 * 冷藏登记Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-12
 */
@Mapper
public interface HzColdRegisterMapper extends BaseMapper<HzColdRegister> {

    /**
     * 根据运单号后四位去查询
     * @param waybillCode 后四位运单号
     * @return 冷藏登记列表
     */
    List<String> getWaybillCodeByFour(String waybillCode);

    /**
     * 查询冷藏登记列表
     * @param query 查询参数
     * @return 冷藏登记列表
     */
    List<ColdQueryVo> selectNewListByQuery(HzColdRegisterQuery query);

    List<ReportColdRegister> selectReportColdList(@Param("lastSyncTime") Date lastSyncTime,@Param("dateNow") Date dateNow);

    HzColdRegister selectWaybillColdStatus(String waybillCode);
}
