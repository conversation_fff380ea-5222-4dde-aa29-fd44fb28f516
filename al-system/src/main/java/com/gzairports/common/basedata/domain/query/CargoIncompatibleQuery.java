package com.gzairports.common.basedata.domain.query;

import lombok.Data;

import java.util.List;

/**
 * 货物不兼容性查询参数
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Data
public class CargoIncompatibleQuery {

    /** 主键id集合 */
    private List<Long> ids;

    /** 航空公司 */
    private String airCompany;

    /** 特货代码1 */
    private String specialCargoOne;

    /** 特货代码2 */
    private String specialCargoTwo;

}
