package com.gzairports.wl.charge.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 运价条目查询参数
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@Data
public class PriceItemQuery {

    /** 运价id */
    private Long id;

    /** 运价名称 */
    private String rateName;

    /** 运价类型 */
    private Integer rateType;

    /** 适用单证类型 */
    private String applicableDocuments;

    /** 生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date effectiveDate;

    /** 截止日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expirationDate;

    /** 目的地 */
    private String destinationCity;
}
