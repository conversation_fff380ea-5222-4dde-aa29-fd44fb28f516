package com.gzairports.common.business.departure.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.business.departure.domain.vo.PageVO;
import com.gzairports.common.business.departure.domain.vo.TFilAwbinfo;

import java.util.Collection;
import java.util.Date;
import java.util.List;


/**
 * 航空运单Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-24
 */
public interface ITFilAwbinfoService  extends IService<TFilAwbinfo>
{

    /***
     * 根据创单时间与订单编号查询
     * @param startTime 创单开始时间
     * @param endTime 创单结束时间
     * @param billid 订单编号（完整编号或者后4位）
     * @return
     */
    List<TFilAwbinfo> selectTFilAwbinfoDateBillId(Date startTime,Date endTime, String billid);


    List<TFilAwbinfo> listByScokNo(Collection<String> strings);

    List<TFilAwbinfo> listByScokNo(String scokNo);

    /**
     * 分页查询运单数据
     * @param pageVO 分页信息
     * @return 结果
     */
    List<TFilAwbinfo> selectTFilAwbinfoPageList(PageVO pageVO);
}
