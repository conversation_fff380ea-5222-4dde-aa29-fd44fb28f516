package com.gzairports.web.controller.ticket;

import com.gzairports.common.annotation.WlTicketLog;
import com.gzairports.common.basedata.domain.BaseBillSource;
import com.gzairports.common.basedata.mapper.BillSourceMapper;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.wl.ticket.domain.Ticket;
import com.gzairports.wl.ticket.domain.TicketNum;
import com.gzairports.wl.ticket.domain.query.TicketNumQuery;
import com.gzairports.wl.ticket.domain.query.TicketQuery;
import com.gzairports.wl.ticket.domain.vo.TicketSourceVo;
import com.gzairports.wl.ticket.domain.vo.TicketVO;
import com.gzairports.wl.ticket.service.TicketService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 单证信息Controller
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@RestController
@RequestMapping("/ticket")
@Api(tags = "单证信息接口")
public class TicketController extends BaseController {

    @Autowired
    private TicketService ticketService;

//    @Autowired
//    private BillSourceMapper billSourceMapper;

    /**
     * 查询单证信息列表
     */
//    @PreAuthorize("@ss.hasPermi('ticket.info.select')")
    @GetMapping("/list")
    @ApiOperation(value = "查询单证信息列表")
    public TableDataInfo list(TicketQuery query){
        startPage();
        List<TicketVO> poList = ticketService.selectList(query);
        return getDataTable(poList);
    }

    /**
     * 根据票证类型查询票证来源
     */
    @GetMapping("/getTicketSource/{type}")
    @ApiOperation(value = "根据票证类型查询票证来源")
    public AjaxResult getTicketSource(@PathVariable("type") String type){
        List<TicketSourceVo> poList = ticketService.getTicketSource(type);
        return AjaxResult.success(poList);
    }

    /**
     * 根据票证类型查询票证来源
     */
    @GetMapping("/getSource/{type}")
    @ApiOperation(value = "根据票证类型查询票证来源")
    public AjaxResult getSource(@PathVariable("type") String type){
        List<TicketSourceVo> poList = ticketService.getSource(type);
        return AjaxResult.success(poList);
    }

    /**
     * 导出单证信息
     */
    @PreAuthorize("@ss.hasPermi('ticket.info.export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出单证信息")
    public void export(HttpServletResponse response,TicketQuery query){
        //要通过传进来的ticketSource得到base_bill_type中的name再作为ticketSource去查询
//        String ticketSource = query.getTicketSource();
//        BaseBillSource baseBillSource = billSourceMapper.selectById(ticketSource);
//        query.setTicketSource(baseBillSource.getName());
        List<TicketVO> poList = ticketService.selectList(query);
        ExcelUtil<TicketVO> util = new ExcelUtil<TicketVO>(TicketVO.class);
        util.exportExcel(response, poList, "单证信息");
    }


    @PostMapping("/exportDetails")
    @ApiOperation(value = "导出单证详情信息")
    public void exportDetails(HttpServletResponse response,TicketQuery query){
        //要通过传进来的ticketSource得到base_bill_type中的name再作为ticketSource去查询
//        String ticketSource = query.getTicketSource();
//        BaseBillSource baseBillSource = billSourceMapper.selectById(ticketSource);
//        query.setTicketSource(baseBillSource.getName());
        List<TicketNum> poList = ticketService.selectDetailList(query);
        ExcelUtil<TicketNum> util = new ExcelUtil<TicketNum>(TicketNum.class);
        util.exportExcel(response, poList, "单证详情");
    }

    /**
     * 单证入库
     */
    @PreAuthorize("@ss.hasPermi('ticket.info.add')")
    @WlTicketLog(businessType = BusinessType.HOUSE)
    @GetMapping("/add")
    @ApiOperation(value = "单证入库")
    public AjaxResult add(Ticket ticket){
        return AjaxResult.success(ticketService.add(ticket));
    }

    /**
     * 单证修改
     */
    @PreAuthorize("@ss.hasPermi('ticket.info.edit')")
    @WlTicketLog(businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ApiOperation(value = "单证修改")
    public AjaxResult edit(@RequestBody Ticket ticket){
        return toAjax(ticketService.edit(ticket));
    }

    /**
     * 单证删除
     */
    @PreAuthorize("@ss.hasPermi('ticket.info.remove')")
    @WlTicketLog(businessType = BusinessType.DELETE)
    @GetMapping("/remove/{id}")
    @ApiOperation(value = "单证删除")
    public AjaxResult remove(@PathVariable("id") Long id){
        return toAjax(ticketService.removeByTicketId(id));
    }

    /**
     * 根据id查询单证信息
     */
    @PreAuthorize("@ss.hasPermi('ticket.info.getInfo')")
    @GetMapping("/{id}")
    @ApiOperation(value = "根据id查询单证使用信息")
    public AjaxResult getInfo(@PathVariable("id") Long id){
        return AjaxResult.success(ticketService.getInfo(id));
    }

    /**
     * 单证销号
     */
    @PreAuthorize("@ss.hasPermi('ticket.info.cancel')")
    @WlTicketLog(businessType = BusinessType.CANCEL)
    @PostMapping("/cancel")
    @ApiOperation(value = "单证销号")
    public AjaxResult cancel(@RequestBody TicketNumQuery query){
        return toAjax(ticketService.cancel(query));
    }

    /**
     * 单证发放
     */
    @PreAuthorize("@ss.hasPermi('ticket.info.grant')")
    @WlTicketLog(businessType = BusinessType.PROVIDE)
    @PostMapping("/grant")
    @ApiOperation(value = "单证发放")
    public AjaxResult grant(@RequestBody TicketNumQuery query){
        return toAjax(ticketService.grant(query));
    }


    /**
     * 取消发放
     */
    @PreAuthorize("@ss.hasPermi('ticket.info.cancelGrant')")
    @WlTicketLog(businessType = BusinessType.CANCELGRANT)
    @GetMapping("/cancelGrant/{recordId}")
    @ApiOperation(value = "取消发放")
    public AjaxResult cancelGrant(@PathVariable("recordId") Long recordId){
        return toAjax(ticketService.cancelGrant(recordId));
    }

    /**
     * 重新发放
     */
    @PreAuthorize("@ss.hasPermi('ticket.info.reissue')")
    @WlTicketLog(businessType = BusinessType.CANCELGRANT)
    @GetMapping("/reissue/{recordId}")
    @ApiOperation(value = "重新发放")
    public AjaxResult reissue(@PathVariable("recordId") Long recordId){
        return toAjax(ticketService.reissue(recordId));
    }

    /**
     * 取消入库
     */
    @PreAuthorize("@ss.hasPermi('ticket.info.warehouse')")
    @WlTicketLog(businessType = BusinessType.WAREHOUSE)
    @PostMapping("/warehouse")
    @ApiOperation(value = "取消入库")
    public AjaxResult warehouse(@RequestBody TicketNumQuery query){
        return toAjax(ticketService.warehouse(query));
    }

    /**
     * 查看单证详情-发放记录
     */
    @PreAuthorize("@ss.hasPermi('ticket.info.detail')")
    @GetMapping("/detail/{ticketId}")
    @ApiOperation(value = "查看单证使用详情-发放记录")
    public AjaxResult detail(@PathVariable("ticketId") Long ticketId){
        return AjaxResult.success(ticketService.detail(ticketId));
    }

    /**
     * 详情-使用明细
     */
    @PreAuthorize("@ss.hasPermi('ticket.info.usage')")
    @GetMapping("/usage/{recordId}")
    @ApiOperation(value = "查看单证使用详情")
    public AjaxResult usage(@PathVariable("recordId") Long recordId){
        return AjaxResult.success(ticketService.usage(recordId));
    }

    /**
     * 查看操作日志
     */
    @PreAuthorize("@ss.hasPermi('ticket.info.log')")
    @GetMapping("/log/{id}")
    @ApiOperation(value = "查看操作日志")
    public AjaxResult log(@PathVariable("id") Long id){
        return AjaxResult.success(ticketService.log(id));
    }

}
