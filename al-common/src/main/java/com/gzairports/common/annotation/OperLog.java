package com.gzairports.common.annotation;

import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.enums.OperatorType;

import java.lang.annotation.*;

/**
 * 物流日志注解
 *
 * <AUTHOR>
 *
 */
@Target({ ElementType.PARAMETER, ElementType.METHOD })
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperLog {

    /**
     * 模块
     */
    public String title() default "";

    /**
     * 功能
     */
    public BusinessType businessType() default BusinessType.OTHER;

    /**
     * 操作人类别
     */
    public OperatorType operatorType() default OperatorType.MANAGE;
}
