package com.gzairports.wl.charge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.wl.charge.domain.MailPriceItem;
import com.gzairports.wl.charge.domain.query.PriceItemQuery;
import com.gzairports.wl.charge.domain.vo.MailPriceItemVo;

import java.util.List;

/**
 * 邮件运价条目Service接口
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
public interface IMailPriceItemService extends IService<MailPriceItem> {
    /**
     * 查询运价条目列表
     *
     * @param query 运价条目查询参数
     * @return 结果
     */
    List<MailPriceItemVo> selectList(PriceItemQuery query);

    /**
     * 查询运价条目详情
     *
     * @param id 运价条目主键
     * @return 运价条目详情
     */
    MailPriceItem selectAirItemById(Long id);

    /**
     * 新增运价条目
     *
     * @param item 运价条目
     * @return 结果
     */
    int insertAirItem(MailPriceItem item);

    /**
     * 修改运价条目
     *
     * @param item 运价条目
     * @return 结果
     */
    int updateAirItem(MailPriceItem item);

    /**
     * 删除运价条目
     *
     * @param id 运价条目主键
     * @return 结果
     */
    int removeAirItem(Long id);

    void delAll(Long[] ids);
}
