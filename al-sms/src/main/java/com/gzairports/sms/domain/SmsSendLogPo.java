package com.gzairports.sms.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author: Ring
 * @Date: 2020/2/26
 * @Description:
 */
@Data
@TableName("sms_send_log")
public class SmsSendLogPo {

    /**模板短信**/
    public static final Integer TEMPLATE_MSG = 0;

    /***普通短信**/
    public static final Integer NORMAL_MSG = 1;

    /***主键id**/
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /***电话号码**/
    private String mobile;

    /***运单完整编码**/
    private String billId;

    /***模块名称**/
    private String moduleName;

    /**
     * 消息类型:
     * 0表示发送模板短信
     * 1表示发送普通短信
     */
    private Integer msgType;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息参数
     */
    private String smsParameter;

    /**
     * 返回代码
     */
    private Integer resultCode;

    /**
     * 模板id
     */
    private String smsConfigCode;

    /**
     * 返回消息内容
     */
    private String resultMsg;


    /**
     * 消息创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 消息更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

}
