<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.CarrierMapper">
    <select id="selectCarrierList" resultType="com.gzairports.common.basedata.domain.BaseCarrier">
        select id, code, chinese_name, english_name, abbreviation, prefix, logo_url, is_shipment from base_carrier
        <where>
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="code != null  and code != ''"> and code like concat('%',#{code},'%')</if>
            <if test="chineseName != null and chineseName != ''"> and chinese_name like concat('%',#{chineseName},'%')</if>
            <if test="englishName != null and englishName != ''"> and english_name like concat('%',#{englishName},'%')</if>
            <if test="abbreviation != null and abbreviation != ''"> and abbreviation like concat('%',#{abbreviation},'%')</if>
            <if test="prefix != null"> and prefix like concat('%',#{prefix},'%')</if>
            <if test="isShipment != null"> and is_shipment = #{isShipment}</if>
        </where>
    </select>
    <select id="selectLogoByCode" resultType="java.lang.String">
        select logo_url from base_carrier where code = #{carrier1}
    </select>
    <select id="getTicketSource" resultType="com.gzairports.wl.ticket.domain.vo.TicketSourceVo">
        select prefix as ticketPrefix, chinese_name as airWays from base_carrier where is_shipment = 1 and is_del = 0
    </select>
    <select id="selectIsShipmentList" resultType="com.gzairports.wl.reporter.domain.vo.CarrierDataVO">
        select code, chinese_name from base_carrier where is_shipment = 1 and is_del = 0
    </select>
</mapper>
