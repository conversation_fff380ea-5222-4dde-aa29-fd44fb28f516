package com.gzairports.common.business.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.business.departure.domain.BookingRecord;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订舱返回参数
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@Data
public class BookingVo {

    /** 主键id */
    private Long id;

    /** 航班日期 */
    @Excel(name = "航班日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date flightDate;

    /** 航班号 */
    @Excel(name = "航班号")
    private String flightNo;

    /** 订舱航班 */
    @Excel(name = "订舱航班")
    private String flight;

    /** 运单号 */
    @Excel(name = "运单号")
    private String waybillCode;

    /** 计划起飞 */
    @Excel(name = "计划起飞")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planTakeoffTime;

    /** 机型 */
    @Excel(name = "机型")
    private String craftType;

    /** 机号 */
    @Excel(name = "机号")
    private String craftNo;

    /** 代理人代码 */
    @Excel(name = "代理人代码")
    private String agentCode;

    /** 订舱件数 */
    @Excel(name = "订舱件数")
    private Integer quantity;

    /** 货品代码 */
    private String cargoCode;

    /** 货物品名 */
    @Excel(name = "货物品名")
    private String cargoName;

    /** 特货代码 */
    @Excel(name = "特货代码")
    private String specialCode;

    /** 订舱重量 */
    @Excel(name = "订舱重量")
    private BigDecimal weight;

    /** 计费重量 */
    @Excel(name = "计费重量")
    private BigDecimal chargeWeight;

    /** 订舱体积 */
    @Excel(name = "订舱体积")
    private BigDecimal volume;

    /** 订舱号 */
    @Excel(name = "订舱号")
    private String bookingNo;

    /** 保障等级 */
    @Excel(name = "保障等级")
    private String ensureLevel;

    /** 舱位等级 */
    @Excel(name = "舱位等级")
    private String berthLevel;

    /** 飞机号限制 */
    @Excel(name = "飞机号限制")
    private String flightLimit;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 记录审核列表 */
    private List<BookingRecord> recordList;
}
