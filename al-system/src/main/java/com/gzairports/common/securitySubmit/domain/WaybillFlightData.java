package com.gzairports.common.securitySubmit.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 2.货站完成航班/定时任务自动关闭航班
 * @date 2025-06-17
 **/

@Data
public class WaybillFlightData {
    /** 类型
     * 1.货站推送收运数据
     * 2.货站完成航班/定时任务自动关闭航班
     * 3.物流平台选择换单，将原单号、新单号推送货检系统
     * 4.代理人不正常货邮选择换单
     * 5.货运系统完成退单
     * 6.代理人重复提交品名清单附件
     **/
    private Integer type;

    /** 航班号 */
    private String flightNo;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date flightDate;

    /** 配载列表 */
    private List<WaybillFlightLoadInfoData> flightLoadList;
}
