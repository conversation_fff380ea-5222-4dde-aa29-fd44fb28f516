package com.gzairports.common.business.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * Created by david on 2025/1/23
 * <AUTHOR>
 */
@Data
public class CountCostVo {
    private String waybillCode;

    /** 入库时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 入库时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 发货代理人 */
    private List<String> agentCode;

    /** 错误类型 */
    private Integer type;
}
