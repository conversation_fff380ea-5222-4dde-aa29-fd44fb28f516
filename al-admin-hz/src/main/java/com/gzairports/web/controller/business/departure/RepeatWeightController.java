package com.gzairports.web.controller.business.departure;

import com.gzairports.common.pdf.PdfPrintHelper;
import com.gzairports.common.utils.spring.SpringUtils;
import com.gzairports.hz.business.departure.domain.HzDepRepeatWeight;
import com.gzairports.hz.business.departure.domain.query.GroupCargoQuery;
import com.gzairports.hz.business.departure.domain.vo.PrintRepeatVo;
import com.gzairports.hz.business.departure.domain.vo.RepeatWeightVo;
import com.gzairports.hz.business.departure.service.IRepeatWeightService;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.oss.domain.SysOss;
import com.gzairports.oss.service.impl.SysOssServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 复重Controller
 *
 * <AUTHOR>
 * @date 2024-07-03
 */
@RestController
@RequestMapping("/dep/repeatWeight")
@Api(tags = "复重")
public class RepeatWeightController extends BaseController {

    @Autowired
    private IRepeatWeightService repeatWeightService;

    /**
     * 查询复重列表
     */
//    @PreAuthorize("@ss.hasPermi('dep:repeatWeight:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询复重列表")
    public TableDataInfo list(GroupCargoQuery query)
    {
        startPage();
        List<RepeatWeightVo> list = repeatWeightService.selectList(query);
        return getDataTable(list);
    }

    /**
     * 导出复重列表
     */
    @PreAuthorize("@ss.hasPermi('dep:repeatWeight:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出复重列表")
    public void export(HttpServletResponse response, GroupCargoQuery query)
    {
        List<RepeatWeightVo> list = repeatWeightService.selectList(query);
        ExcelUtil<RepeatWeightVo> util = new ExcelUtil<RepeatWeightVo>(RepeatWeightVo.class);
        util.exportExcel(response, list, "复重列表");
    }

    /**
     * 复磅
     */
    @PreAuthorize("@ss.hasPermi('dep:repeatWeight:weight')")
    @GetMapping("/weight/{id}")
    @ApiOperation(value = "复磅")
    public AjaxResult weight(@PathVariable("id") Long id)
    {
        List<HzDepRepeatWeight> list = repeatWeightService.weight(id);
        return AjaxResult.success(list);
    }

    /**
     * 详情
     */
    @PreAuthorize("@ss.hasPermi('dep:repeatWeight:genInfo')")
    @GetMapping("/genInfo/{id}")
    @ApiOperation(value = "详情")
    public AjaxResult genInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(repeatWeightService.getInfo(id));
    }

    /**
     * 编辑
     */
    @PreAuthorize("@ss.hasPermi('dep:repeatWeight:edit')")
    @PostMapping("/edit")
    @ApiOperation(value = "编辑")
    public AjaxResult edit(@RequestBody HzDepRepeatWeight repeatWeight)
    {
        return toAjax(repeatWeightService.edit(repeatWeight));
    }

    /**
     * 复磅完成
     */
    @PreAuthorize("@ss.hasPermi('dep:repeatWeight:finishWeight')")
    @GetMapping("/finishWeight/{id}")
    @ApiOperation(value = "复磅完成")
    public AjaxResult finishWeight(@PathVariable("id") Long id)
    {
        return toAjax(repeatWeightService.finishWeight(id));
    }

    /**
     * 新增板车
     */
    @PreAuthorize("@ss.hasPermi('dep:repeatWeight:addCar')")
    @PostMapping("/addCar")
    @ApiOperation(value = "新增板车")
    public AjaxResult addCar(@RequestBody HzDepRepeatWeight weight)
    {
        return AjaxResult.success(repeatWeightService.addCar(weight));
    }

    /**
     * 装机指示牌打印
     */
//    @PreAuthorize("@ss.hasPermi('dep:repeatWeight:printInstallSigns')")
    @GetMapping(value = "/printInstallSigns/{id}")
    @ApiOperation(value = "装机指示牌打印")
    public void printInstallSigns(HttpServletResponse response, @PathVariable("id") Long id) throws Exception
    {
        repeatWeightService.printInstallSigns(id,response);
    }

    /**
     * app装机指示牌打印
     */
//    @PreAuthorize("@ss.hasPermi('dep:repeatWeight:printInstallSigns')")
    @GetMapping(value = "/printAppInstallSigns/{id}")
    @ApiOperation(value = "app装机指示牌打印")
    public AjaxResult printAppInstallSigns(@PathVariable("id") Long id) throws Exception
    {
        PrintRepeatVo printRepeatVo = repeatWeightService.printAppInstallSigns(id);
        return success(getPdfUrl(printRepeatVo));
    }


    /**
     * 查询板车上面的运单信息
     */
    @GetMapping(value = "/getWaybills/{groupUldId}")
    @ApiOperation(value = "查询板车上面的运单信息")
    public AjaxResult getWaybills(@PathVariable("groupUldId") Long id)
    {
        return success(repeatWeightService.getWaybills(id));
    }

    private String getPdfUrl(@RequestBody PrintRepeatVo vo) throws Exception {
        ClassPathResource securityUrl = new ClassPathResource("template/installSigns.pdf");
        if (securityUrl.exists()) {
            String path = securityUrl.getPath();
            byte[] pdfDataFromTemplate = PdfPrintHelper.getPdfDataFromTemplate(vo, path);
            // 使用StandardMultipartHttpServletRequest来创建MultipartFile
            DiskFileItem fileItem = new DiskFileItem(vo.getUld() + ".pdf", "application/pdf", true, vo.getUld() + ".pdf", -1, null);

            // 将 InputStream 写入 DiskFileItem
            fileItem.getOutputStream().write(pdfDataFromTemplate);
            fileItem.getOutputStream().close();

            // 使用 FileItem 构建 MultipartFile
            MultipartFile multipartFile = new CommonsMultipartFile(fileItem);
            SysOss upload = SpringUtils.getBean(SysOssServiceImpl.class).upload(multipartFile);
            return upload.getUrl();
        }
        return null;
    }

}
