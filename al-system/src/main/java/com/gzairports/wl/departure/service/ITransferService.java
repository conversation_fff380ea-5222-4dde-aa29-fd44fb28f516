package com.gzairports.wl.departure.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.business.departure.domain.Transfer;
import com.gzairports.wl.departure.domain.query.TransFerWaybillsVo;
import com.gzairports.wl.departure.domain.query.TransferInfoQuery;
import com.gzairports.wl.departure.domain.query.TransferQuery;
import com.gzairports.wl.departure.domain.vo.TransferMawbVo;
import com.gzairports.wl.departure.domain.vo.TransferVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 货站出港货物交接Service接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface ITransferService extends IService<Transfer> {

    /**
     * 根据条件查询货物交接列表
     * @param query 查询条件
     * @return 货物交接列表
     */
    List<TransferVo> selectList(TransferQuery query);

    /**
     * 新增出港货物交接单
     * @param transfer 出港货物交接单数据
     * @return 结果
     */
    int add(Transfer transfer);

    /**
     * 查询出港货物交接单详情
     * @param id 出港货物交接单id
     * @return 出港货物交接单详情
     */
    TransferVo getInfo(Long id);

    /**
     * 删除出港货物交接单
     * @param id 出港货物交接单id
     * @return 结果
     */
    int del(Long id);

    /**
     * 编辑出港货物交接单
     * @param transfer 出港货物交接单数据
     * @return 结果
     */
    int edit(Transfer transfer);

    /**
     * 出港货物交接增加运单
     * @param query 出港货物交接数据
     * @return 结果
     */
    List<TransferMawbVo> addWaybill(TransferInfoQuery query);

    /**
     * 出港货物交接删除运单
     * @param query 出港货物交接数据
     * @return 结果
     */
    int delWaybill(TransferInfoQuery query);

    /**
     * 获取交接单头部信息
     * @return 交接单头部信息
     */
    Transfer getTransfer();

    /**
     * 获取可交接运单
     * @return 交接运单列表
     */
    List<TransferMawbVo> getWaybill(TransFerWaybillsVo vo);

    /**
     * 打印货物交接单数据
     * @param id 交接单id
     */
    void printTransfer(HttpServletResponse response, Long id) throws Exception;

    /**
     * 获取特货交接单需要回显的主单信息
     * @param id 主单id
     * */
    TransferVo getTransferByMawbId(Long id);
}
