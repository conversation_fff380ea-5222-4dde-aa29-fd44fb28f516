package com.gzairports.wl.departure.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 托运管理查询参数
 *
 * <AUTHOR>
 * @date 2024-05-08
 */
@Data
public class ConsignQuery {

    /** 业务号 */
    private String code;

    /** 托运人身份证号 */
    private String shipperIdCard;

    /** 运单号 */
    private String waybillCode;

    /** 时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;

    /** 所所属单位 */
    private Long deptId;
}
