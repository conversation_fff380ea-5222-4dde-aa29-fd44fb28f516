package com.gzairports.common.business.departure.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.annotation.DataSource;
import com.gzairports.common.business.departure.domain.vo.TLogFeelog;
import com.gzairports.common.business.departure.mapper.LogFeelogMapper;
import com.gzairports.common.business.departure.service.ILogFeelogService;
import com.gzairports.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 运单日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-25
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class LogFeelogServiceImpl extends ServiceImpl<LogFeelogMapper, TLogFeelog> implements ILogFeelogService {
    @Autowired
    private LogFeelogMapper freelogMapper;



    @Override
    public Map<String, List<TLogFeelog>> listByStocks(Collection<String> stocknoSet) {
        if (stocknoSet == null || stocknoSet.isEmpty()) {
            return new HashMap<>();
        }
        Map<String, List<TLogFeelog>> rMap = new HashMap<>();
        LambdaQueryWrapper<TLogFeelog> query = Wrappers.lambdaQuery(TLogFeelog.class).in(TLogFeelog::getStockno, stocknoSet);
        List<TLogFeelog> awblogs = freelogMapper.selectList(query);
        for (TLogFeelog awblog : awblogs) {
            List<TLogFeelog> list = rMap.get(awblog.getStockno());
            if (list == null) {
                list = new ArrayList<>();
                rMap.put(awblog.getStockno(), list);
            }
            list.add(awblog);
        }
        return rMap;
    }

    @Override
    public Map<String, List<TLogFeelog>> listByTime(Date startTime, Date endTime) {
        Map<String, List<TLogFeelog>> rMap = new HashMap<>();
        LambdaQueryWrapper<TLogFeelog> query = Wrappers.lambdaQuery(TLogFeelog.class).ge(TLogFeelog::getOpetime, startTime).le(TLogFeelog::getOpetime, endTime);
        List<TLogFeelog> awblogs = freelogMapper.selectList(query);
        for (TLogFeelog awblog : awblogs) {
            List<TLogFeelog> list = rMap.get(awblog.getStockno());
            if (list == null) {
                list = new ArrayList<>();
                rMap.put(awblog.getStockno(), list);
            }
            list.add(awblog);
        }
        return rMap;
    }
}
