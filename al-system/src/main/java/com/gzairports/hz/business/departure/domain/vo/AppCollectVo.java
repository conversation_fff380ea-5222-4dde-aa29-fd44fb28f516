package com.gzairports.hz.business.departure.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by david on 2024/12/7
 * <AUTHOR>
 */
@Data
public class AppCollectVo {

    /** 总共 */
    private Integer totalCount;

    /** 总共的运单总件数 */
    private Integer totalQuantity;

    /** 总共的运单总重量 */
    private BigDecimal totalWeight;

    /** 已处理 */
    private Integer handledCount;

    /** 已处理的运单总件数 */
    private Integer handledQuantity;

    /** 已处理的运单总重量 */
    private BigDecimal handledWeight;

    /** 已提交 */
    private Integer submitCount;

    /** 已提交的运单总件数 */
    private Integer submitQuantity;

    /** 已提交的运单总重量 */
    private BigDecimal submitWeight;

    /** 总共数据 */
    private List<HzTransferVo> totalList;

    /** 已处理数据 */
    private List<HzTransferVo> handleList;

    /** 已提交数据 */
    private List<HzTransferVo> submitList;
}
