package com.gzairports.common.business.reporter.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-02-19
 */
@Data
@TableName("all_report_data_item")
public class ReportDataItem {

    /** 主键id */
    private Long id;

    /** 报表id */
    private Long reportId;

    /** 运单号 */
    private String waybillCode;

    /** 收费项目名称 */
    private String chargeName;

    /** 运价类型 */
    private String freightType;

    /** 费率 */
    private BigDecimal rate;

    /** 计费金额 */
    private BigDecimal charging;

    /** 所属单位 */
    private Long deptId;

    /** 是否删除 0 否 1 是 */
    private Long isDel;

    /** 费用类型2 0 收费规则 1 航司运价 */
    private String rateType;
}
