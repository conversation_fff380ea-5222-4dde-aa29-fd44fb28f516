package com.gzairports.web.controller.infoquery;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.infoquery.domain.query.AirlineWaybillQuery;
import com.gzairports.common.infoquery.domain.vo.AirlineWaybillInfoVO;
import com.gzairports.common.infoquery.service.IAirlineWaybillService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 航司运单Controller
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@RestController
@RequestMapping("/info/airline")
public class AirlineWaybillController extends BaseController {

    @Autowired
    private IAirlineWaybillService airCompanyWaybillService;

    /**
     * 获取航班列表
     */
//    @PreAuthorize("@ss.hasPermi('info:airCompany:list')")
    @GetMapping("/waybill/list")
    public TableDataInfo list(AirlineWaybillQuery query) {
        return buildDataFromPage(airCompanyWaybillService.selectACWList(query));
    }
}
