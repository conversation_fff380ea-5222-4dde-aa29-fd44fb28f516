<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.BaseAgentMapper">
    
    <resultMap type="BaseAgent" id="BaseAgentResult">
        <result property="id"    column="id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="agent"    column="agent"    />
        <result property="agentAbb"    column="agent_abb"    />
        <result property="agentAbbIn"    column="agent_abb_in"    />
        <result property="unionPayAccount"    column="union_pay_account"    />
        <result property="settleMethod"    column="settle_method"    />
        <result property="balance"    column="balance"    />
        <result property="balanceAlert"    column="balance_alert"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="isDel"    column="is_del"    />
        <result property="sealUrl"    column="seal_url"    />
        <result property="deptIds"    column="dept_ids"    />
        <result property="payCostSum" column="pay_cost_sum"/>
        <result property="settleCostSum" column="settle_cost_sum"/>
        <result property="rechargeCostSum" column="recharge_cost_sum"/>
    </resultMap>

    <sql id="selectBaseAgentVo">
        select id, dept_id, agent, agent_abb,agent_abb_in, union_pay_account, settle_method, balance, balance_alert, remark, create_time, create_by,
               update_time, update_by, is_del, seal_url, dept_ids, pay_method from base_agent
    </sql>

    <select id="selectBaseAgentList" parameterType="BaseAgent" resultMap="BaseAgentResult">
        <include refid="selectBaseAgentVo"/>
        <where>
            <if test="agent != null  and agent != ''">and agent like concat('%', #{agent}, '%')</if>
            <if test="deptId != null">and dept_id = #{deptId}</if>
            <if test="settleMethod != null">and settle_method = #{settleMethod}</if>
            <if test="isHadBalance != null and isHadBalance == 1">and balance > 0</if>
            <if test="isHadBalance != null and isHadBalance == 0">and (balance = 0 or balance is null)</if>
        </where>
    </select>

    <select id="selectExportBaseAgentList" parameterType="BaseAgent" resultMap="BaseAgentResult">
        SELECT
        ba.*,
        COALESCE(wdcd.pay_sum, 0) AS pay_cost_sum, -- 预付款总金额（默认值0）
        COALESCE(wdcd.settle_sum, 0) AS settle_cost_sum, -- 结算总金额（默认值0）
        COALESCE(bb.recharge_sum, 0) AS recharge_cost_sum -- 已充值总金额（默认值0）
        FROM base_agent ba
        LEFT JOIN
        (SELECT
        dept_id,
        SUM(IF(type = 0, total_charge, 0)) AS pay_sum,
        SUM(IF(type = 1, total_charge, 0)) AS settle_sum
        FROM wl_dep_cost_detail
        GROUP BY dept_id
        ) wdcd
        ON ba.dept_id = wdcd.dept_id
        LEFT JOIN (
        SELECT
        agent_id,
        SUM(balance) AS recharge_sum
        FROM base_balance
        WHERE add_type = 1
        GROUP BY agent_id
        ) bb
        ON ba.id = bb.agent_id
        <where>
            <if test="agent != null  and agent != ''">ba.agent like concat('%', #{agent}, '%')</if>
            <if test="settleMethod != null">and settle_method = #{settleMethod}</if>
            <if test="isHadBalance != null and isHadBalance == 1">and balance > 0</if>
            <if test="isHadBalance != null and isHadBalance == 0">and (balance = 0 or balance is null)</if>
        </where>
    </select>

    <select id="selectBaseAgentById" parameterType="Long" resultMap="BaseAgentResult">
        <include refid="selectBaseAgentVo"/>
        where id = #{id}
    </select>
    <select id="selectBaseAgentByName" resultType="com.gzairports.common.basedata.domain.BaseAgent">
        <include refid="selectBaseAgentVo"/>
        where agent = #{agentCompany}
    </select>
    <select id="selectNameById" resultType="java.lang.String">
        select
    </select>
    <select id="selectByDeptId" resultType="com.gzairports.common.basedata.domain.BaseAgent">
        <include refid="selectBaseAgentVo"/>
        where dept_id = #{deptId}
    </select>
    <select id="selectListByAgentCode" resultType="com.gzairports.common.basedata.domain.BaseAgent">
        select id, dept_id, settle_method from base_agent where is_del is null
       <if test="agentCodes != null and agentCodes.size() > 0">
           and agent in
           <foreach collection="agentCodes" item="agentCode" open="(" separator="," close=")">
               #{agentCode}
           </foreach>
       </if>
    </select>

    <insert id="insertBaseAgent" parameterType="BaseAgent" useGeneratedKeys="true" keyProperty="id">
        insert into base_agent
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="agent != null and agent != ''">agent,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="agentAbb != null and agentAbb != ''">agent_abb,</if>
            <if test="agentAbbIn != null and agentAbbIn != ''">agent_abb_in,</if>
            <if test="unionPayAccount != null">union_pay_account,</if>
            <if test="settleMethod != null">settle_method,</if>
            <if test="payMethod != null">pay_method,</if>
            <if test="balance != null">balance,</if>
            <if test="balanceAlert != null">balance_alert,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDel != null">is_del,</if>
            <if test="sealUrl != null and sealUrl != ''">seal_url,</if>
            <if test="deptIds != null and deptIds != ''">dept_ids,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="agent != null and agent != ''">#{agent},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="agentAbb != null and agentAbb != ''">#{agentAbb},</if>
            <if test="agentAbbIn != null and agentAbbIn != ''">#{agentAbbIn},</if>
            <if test="unionPayAccount != null">#{unionPayAccount},</if>
            <if test="settleMethod != null">#{settleMethod},</if>
            <if test="payMethod != null">#{payMethod},</if>
            <if test="balance != null">#{balance},</if>
            <if test="balanceAlert != null">#{balanceAlert},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="sealUrl != null and sealUrl != ''">#{sealUrl},</if>
            <if test="deptIds != null and deptIds != ''">#{deptIds},</if>
         </trim>
    </insert>

    <update id="updateBaseAgent" parameterType="BaseAgent">
        update base_agent
        <trim prefix="SET" suffixOverrides=",">
            <if test="agent != null">agent = #{agent},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="agentAbb != null">agent_abb = #{agentAbb},</if>
            <if test="agentAbbIn != null">agent_abb_in = #{agentAbbIn},</if>
            <if test="unionPayAccount != null">union_pay_account = #{unionPayAccount},</if>
            <if test="settleMethod != null">settle_method = #{settleMethod},</if>
            <if test="payMethod != null">pay_method = #{payMethod},</if>
            <if test="balance != null">balance = #{balance},</if>
            <if test="balanceAlert != null">balance_alert = #{balanceAlert},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="sealUrl != null and sealUrl != ''">seal_url = #{sealUrl},</if>
            <if test="deptIds != null and deptIds != ''">dept_ids = #{deptIds},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBaseAgentById" parameterType="Long">
        delete from base_agent where id = #{id}
    </delete>

    <delete id="deleteBaseAgentByIds" parameterType="String">
        delete from base_agent where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>