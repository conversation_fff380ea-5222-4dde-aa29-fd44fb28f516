package com.gzairports.common.securitySubmit.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.pdf.PdfPrintAnnotation;
import com.gzairports.hz.business.departure.domain.vo.DetailedVo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static com.gzairports.common.pdf.PdfPrintAnnotation.PdfFieldType.*;

/**
 * @author: lan
 * @Desc: 安检申报运单详情
 * @create: 2024-11-15 15:55
 **/

@Data
public class SecurityWaybillInfo {
    /** 主键id */
    private Long id;

    /** 运单号 */
    private String waybillCode;

    /** 特货代码1 */
    private String specialCargoCode1;

    /** 大类 */
    private String categoryCode;

    /** 货品代码 */
    private String cargoCode;

    /** 安检提交 */
    private Integer securitySubmit;

    /** 货品大类 */
    private String categoryName;

    /** 状态 */
    private String status;

    /** 安检申报单 */
    private String securityUrl;

    /** 随附文件 */
    private String transportFile;

    /** 随附文件pdf */
    private String transportFilePdf;

    /** 危险品UN编号 */
    private String dangerCode;

    /** 运单号 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "waybillCode")
    private String waybillCodeAbb;

    /** 始发站 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "sourcePort")
    private String sourcePort;

    /** 目的站 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "desPort")
    private String desPort;

    /** 航班号 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "flightNo1")
    private String flightNo1;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date flightDate1;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "flightDate1")
    private String flightDate1Str;

    /** 承运人1/承运航空公司 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "carrier1")
    private String carrier1;

    /** 发货人 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipper")
    private String shipper;

    /** 发货人 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipperStr")
    private String shipperStr;

    /** 代理人公司 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "agentCompany")
    private String agentCompany;

    /** 品名 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "cargoName")
    private String cargoName;


    /** 件数 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "quantity")
    private Integer quantity;

    /** 重量 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "weight")
    private BigDecimal weight;


    /** 填开时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "writeTime")
    private Date writeTime;

    @PdfPrintAnnotation(pdfFieldType = CHOICE, pdfFieldName = "pthw")
    private String pthw;

    @PdfPrintAnnotation(pdfFieldType = CHOICE, pdfFieldName = "tzhw")
    private String tzhw;

    @PdfPrintAnnotation(pdfFieldType = CHOICE, pdfFieldName = "wxp")
    private String wxp;

    @PdfPrintAnnotation(pdfFieldType = CHOICE, pdfFieldName = "hkkj")
    private String hkkj;

    /** 航空货运销售代理人签章  */
    @PdfPrintAnnotation(pdfFieldType = IMAGE, pdfFieldName = "sealUrl")
    private String sealUrl;

    /** 航空货物托运人签章 */
    @PdfPrintAnnotation(pdfFieldType = IMAGE, pdfFieldName = "sealUrl2")
    private String sealUrl2;

    /** 收运人安全检查结论 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "checkConclusionCollect")
    private String checkConclusionCollect;

    /** 收运人签名 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "sealUrlCollect")
    private String sealUrlCollect;

    /** 托运人或航空货运销售代理人信用情况 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "creditSituation")
    private String creditSituation;

    /** 安全检查开始时间 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "startTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 安全检查结束时间 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "endTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 安全检查理论 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "checkConclusion")
    private String checkConclusion;

    /** 安全检查通道 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "checkPass")
    private String checkPass;

    /** 货邮快件安检设备操作员 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "deviceOper")
    private String deviceOper;

    /** 开箱检查员 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "openCheck")
    private String openCheck;

    /** 安检勤务调度员 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "dispatcher")
    private String dispatcher;

    /** 单据审核员 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "auditor")
    private String auditor;

    /** 已检洪武抽检员 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "samplingInspector")
    private String samplingInspector;

    /** 已检货物抽检情况 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "samplingSituation")
    private String samplingSituation;

    /** 代理人签章 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "agentSignature")
    private String agentSignature;

    /** 托运人签章 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipperSignature")
    private String shipperSignature;

    /** 航空邮件托运人名称 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipperAbb")
    private String shipperAbb;

    /** 航空邮件邮包汇总路单号 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "sumCode")
    private String sumCode;

    /** 储运注意事项 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "storageTransportNotes")
    private String storageTransportNotes;

}
