package com.gzairports.wl.departure.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 分单异常备注表
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@Data
@TableName("wl_dep_hawb_error_remark")
public class HawbErrorRemark {

    /** 主键id */
    private Long id;

    /** 分单id */
    private Long hawbId;

    /** 分单运单号 */
    private String waybillCode;

    /** 备注人 */
    private String userName;

    /** 备注时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date remarkTime;

    /** 备注内容 */
    private String remark;

    /** 是否删除 0 否 1 是 */
    private Integer isDel;
}
