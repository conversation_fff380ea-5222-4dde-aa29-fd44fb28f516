package com.gzairports.web.controller.cargofee;

import com.gzairports.wl.cargofee.domain.CustomPay;
import com.gzairports.wl.cargofee.domain.query.PayRecordQuery;
import com.gzairports.wl.cargofee.service.ICustomPayService;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 客户支付流水Controller
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@RestController
@RequestMapping("/cargofee/customPay")
public class CustomPayController extends BaseController {

    @Autowired
    private ICustomPayService customPayService;

    /**
     * 查询客户支付流水列表
     */
//    @PreAuthorize("@ss.hasPermi('cargofee:customPay:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询客户支付流水列表")
    public TableDataInfo list(PayRecordQuery query){
        startPage();
        List<CustomPay> list = customPayService.selectList(query);
        return getDataTable(list);
    }

    /**
     * 导出客户支付流水列表
     */
    @PreAuthorize("@ss.hasPermi('cargofee:customPay:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出客户支付流水列表")
    public void export(HttpServletResponse response, PayRecordQuery query)
    {
        List<CustomPay> list = customPayService.selectList(query);
        ExcelUtil<CustomPay> util = new ExcelUtil<CustomPay>(CustomPay.class);
        util.exportExcel(response, list, "客户支付流水");
    }
}
