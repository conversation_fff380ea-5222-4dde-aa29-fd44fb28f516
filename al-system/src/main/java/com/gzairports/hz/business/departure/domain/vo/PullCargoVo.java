package com.gzairports.hz.business.departure.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 拉货报文运单数据
 *
 * <AUTHOR>
 * @date 2024-10-14
 */
@Data
public class PullCargoVo {

    /** 主键id */
    private Long id;

    /** 运单号 */
    private String waybillCode;

    /** 航班日期 */
    private Date execDate;

    /** 航班号 */
    private String flightNo;

    /** 航班信息 */
    private String flightInfo;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 目的地 */
    private String desPort;

    /** 操作类型 */
    private String type;
}
