package com.gzairports.web.controller.basedata;

import com.gzairports.common.annotation.Log;
import com.gzairports.common.basedata.domain.BaseAirCargoCode;
import com.gzairports.common.basedata.service.IAirCargoCodeService;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.common.basedata.domain.BaseAirCargoCode;
import com.gzairports.common.basedata.domain.query.AirCargoCodeQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 航空公司货品代码Controller
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@RestController
@RequestMapping("/base/airCargoCode")
@Api(tags = "航空公司货品代码数据接口")
public class AirCargoCodeController extends BaseController {

    @Autowired
    private IAirCargoCodeService airCargoCodeService;

    /**
     * 查询航空公司货品代码列表
     */
//    @PreAuthorize("@ss.hasPermi('base:airCargoCode:airCargoCodeList')")
    @GetMapping("/airCargoCodeList")
    @ApiOperation(value = "查询航空公司货品代码列表")
    public TableDataInfo airCargoCodeList(AirCargoCodeQuery query)
    {
        startPage();
        List<BaseAirCargoCode> list = airCargoCodeService.selectAirCargoCodeList(query);
        return getDataTable(list);
    }

    /**
     * 导出航空公司货品代码
     */
    @PreAuthorize("@ss.hasPermi('base:airCargoCode:exportAirCargoCode')")
    @Log(title = "导出航空公司货品代码", businessType = BusinessType.EXPORT)
    @PostMapping("/exportAirCargoCode")
    @ApiOperation(value = "导出航空公司货品代码")
    public void exportAirCargoCode(HttpServletResponse response, AirCargoCodeQuery query)
    {
        List<BaseAirCargoCode> list = airCargoCodeService.selectAirCargoCodeList(query);
        ExcelUtil<BaseAirCargoCode> util = new ExcelUtil<BaseAirCargoCode>(BaseAirCargoCode.class);
        util.exportExcel(response, list, "航空公司货品代码");
    }

    /**
     * 新增航空公司货品代码
     */
    @PreAuthorize("@ss.hasPermi('base:airCargoCode:addAirCargoCode')")
    @Log(title = "新增航空公司货品代码", businessType = BusinessType.INSERT)
    @PostMapping("/addAirCargoCode")
    @ApiOperation(value = "新增航空公司货品代码")
    public AjaxResult addAirCargoCode(@RequestBody BaseAirCargoCode airCargoCode)
    {
        return toAjax(airCargoCodeService.addAirCargoCode(airCargoCode));
    }

    /**
     * 航空公司货品代码详情
     */
    @PreAuthorize("@ss.hasPermi('base:airCargoCode:getInfo')")
    @GetMapping("/{id}")
    @ApiOperation(value = "航空公司货品详情")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(airCargoCodeService.getInfo(id));
    }

    /**
     * 修改航空公司货品代码
     */
    @PreAuthorize("@ss.hasPermi('base:airCargoCode:editAirCargoCode')")
    @Log(title = "修改航空公司货品代码", businessType = BusinessType.UPDATE)
    @PostMapping("/editAirCargoCode")
    @ApiOperation(value = "修改航空公司货品代码")
    public AjaxResult editAirCargoCode(@RequestBody BaseAirCargoCode airCargoCode)
    {
        return toAjax(airCargoCodeService.editAirCargoCode(airCargoCode));
    }

    /**
     * 删除航空公司货品代码
     */
    @PreAuthorize("@ss.hasPermi('base:airCargoCode:delAirCargoCode')")
    @Log(title = "删除航空公司货品代码", businessType = BusinessType.UPDATE)
    @GetMapping("/delAirCargoCode/{id}")
    @ApiOperation(value = "删除航空公司货品代码")
    public AjaxResult delAirCargoCode(@PathVariable("id") Long id)
    {
        return toAjax(airCargoCodeService.delAirCargoCode(id));
    }

}
