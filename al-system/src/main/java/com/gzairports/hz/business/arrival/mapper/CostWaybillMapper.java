package com.gzairports.hz.business.arrival.mapper;

import com.gzairports.hz.business.arrival.domain.query.CostWaybillQuery;
import com.gzairports.hz.business.arrival.domain.vo.ArrChargeExportNewVo;
import com.gzairports.hz.business.arrival.domain.vo.ArrChargeImportVo;
import com.gzairports.hz.business.arrival.domain.vo.CostWaybillVo;
import com.gzairports.hz.business.departure.domain.query.ChargeQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CostWaybillMapper {

    List<CostWaybillVo> selectList(@Param("query") CostWaybillQuery query, @Param("offset") Integer offset);

    List<ArrChargeImportVo> chargeExportList(ChargeQuery query);

    List<ArrChargeExportNewVo> chargeCostExportList(@Param("query") CostWaybillQuery query);

    List<CostWaybillVo> selectListCount(@Param("query") CostWaybillQuery query);
}
