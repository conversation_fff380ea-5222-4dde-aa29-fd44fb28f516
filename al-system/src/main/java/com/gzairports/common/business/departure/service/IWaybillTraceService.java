package com.gzairports.common.business.departure.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.business.arrival.domain.query.AutoOrderQuery;
import com.gzairports.common.business.departure.domain.WaybillTrace;
import com.gzairports.wl.departure.domain.vo.NewWaybillTraceVO;
import com.gzairports.wl.departure.domain.vo.WaybillTraceVo;

import java.util.List;


/**
 * 运单跟踪Service接口
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
public interface IWaybillTraceService extends IService<WaybillTrace> {


    /**
     * 查询运单跟踪数据
     * @param type 运单类型
     * @param waybillCode 运单号
     * @return 运单跟踪详情
     */
    WaybillTraceVo selectTraceInfo(Integer type, String waybillCode);

    List<WaybillTraceVo> selectAllStatusInfo(Integer type, String waybillCode);
    /**
     * H5运单跟踪数据
     * @param query 查询条件
     * @return H5运单跟踪数据
     */
    List<WaybillTrace> h5WaybillTrace(AutoOrderQuery query);

    /**
     * 根据运单后八位查询运单集合
     * @param code 运单后八位
     */
    List<String> selectInfoWaybillCode(String code);

    /***
     * 新运单查询接口
     * @param waybillCode 运单号，逗号隔开
     * @return 新运单数据
     */
    List<NewWaybillTraceVO> selectWaybillStatusInfo(String waybillCode);
}
