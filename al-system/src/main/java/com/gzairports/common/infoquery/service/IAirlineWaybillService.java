package com.gzairports.common.infoquery.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gzairports.common.infoquery.domain.query.AirlineWaybillQuery;
import com.gzairports.common.infoquery.domain.vo.AirlineWaybillInfoVO;

/**
 * 航司运单 服务层
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
public interface IAirlineWaybillService {


    IPage<AirlineWaybillInfoVO> selectACWList(AirlineWaybillQuery query);
}
