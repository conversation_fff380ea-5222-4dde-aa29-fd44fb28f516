package com.gzairports.common.basedata.service.impl;


import com.gzairports.common.basedata.domain.BaseAirportCode;
import com.gzairports.common.basedata.domain.query.AirportCodeQuery;
import com.gzairports.common.basedata.mapper.AirportCodeMapper;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.exception.ServiceException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.bean.BeanValidators;
import com.gzairports.common.basedata.service.IAirportCodeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.Validator;
import java.util.List;

/**
 * 机场代码Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Service
public class AirportCodeServiceImpl implements IAirportCodeService {

    @Autowired
    private AirportCodeMapper airportCodeMapper;

    private static final Logger log = LoggerFactory.getLogger(AirportCodeServiceImpl.class);

    @Autowired
    private Validator validator;

    /**
     * 查询机场代码列表
     *
     * @param query 查询参数
     * @return 机场代码列表
     */
    @Override
    public List<BaseAirportCode> selectAirportCodeList(AirportCodeQuery query) {
        return airportCodeMapper.selectAirportCodeList(query);
    }

    /**
     * 新增机场代码
     *
     * @param code 机场代码
     * @return 结果
     */
    @Override
    public int addAirportCode(BaseAirportCode code) {
        BaseAirportCode airportCode = airportCodeMapper.selectByCode(code.getCode());
        if (airportCode != null){
            throw new CustomException("已存在相同数据");
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        code.setCreateBy(loginUser.getUsername());
        code.setCreateTime(DateUtils.getNowDate());
        return airportCodeMapper.insertAirportCode(code);
    }

    /**
     * 修改机场代码
     *
     * @param code 机场代码
     * @return 结果
     */
    @Override
    public int editAirportCode(BaseAirportCode code) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        code.setUpdateBy(loginUser.getUsername());
        code.setUpdateTime(DateUtils.getNowDate());
        return airportCodeMapper.updateAirportCode(code);
    }

    /**
     * 删除机场代码
     *
     * @param ids 机场代码id集合
     * @return 结果
     */
    @Override
    public int delAirportCode(Long[] ids) {
        return airportCodeMapper.delAirportCode(ids);
    }

    /**
     * 导入机场代码
     *
     * @param airportCodes 机场代码列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importAirportCode(List<BaseAirportCode> airportCodes, boolean updateSupport) {
        if (StringUtils.isNull(airportCodes) || airportCodes.size() == 0)
        {
            throw new ServiceException("导入机场代码数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (BaseAirportCode airportCode : airportCodes) {
            try {
                // 验证是否存在相同的货站货品代码
                BaseAirportCode code = airportCodeMapper.selectByCode(airportCode.getCode());
                if (StringUtils.isNull(code)) {
                    BeanValidators.validateWithException(validator, airportCode);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    airportCode.setCreateBy(loginUser.getUsername());
                    airportCode.setCreateTime(DateUtils.getNowDate());
                    airportCodeMapper.insertAirportCode(airportCode);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、货站货品代码 " + airportCode.getCode() + " 导入成功");
                }
                else if (updateSupport) {
                    BeanValidators.validateWithException(validator, airportCode);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    airportCode.setId(code.getId());
                    airportCode.setUpdateBy(loginUser.getUsername());
                    airportCode.setUpdateTime(DateUtils.getNowDate());
                    airportCodeMapper.updateAirportCode(airportCode);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、货站货品代码 " + airportCode.getCode() + " 更新成功");
                }
                else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、货站货品代码 " + airportCode.getCode() + " 已存在");
                }
            }catch (Exception e){
                failureNum++;
                String msg = "<br/>" + failureNum + "、货站货品代码 " + airportCode.getCode() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    @Override
    public String selectChineseName(String desPort) {
        BaseAirportCode code = airportCodeMapper.selectByCode(desPort);
        if(code != null){
            return code.getChineseName();
        }else{
            return "";
        }
    }
}
