package com.gzairports.wl.reporter.domain.query;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-02-20
 */
@Data
public class HawbSalesQuery {

    /** 分单号 */
    private String waybillCode;

    /** 主单号 */
    private String masterWaybillCode;

    /** 承运人 */
    private String carrier1;

    /** 起点站 */
    private String sourcePort;

    /** 货物编码 */
    private String cargoCode;

    /** 货物品名 */
    private String cargoName;

    /** 承运人 */
    private String carrier;

    /** 目的站 */
    private String desPort;

    /** 发货人简称 */
    private String shipperAbb;

    /** 发货人全称 */
    private String shipper;

    /** 收货人简称 */
    private String consignAbb;

    /** 收货人全称 */
    private String consign;

    /** 制单开始日期 */
    private Date startWriteTime;

    /** 制单结束日期 */
    private Date endWriteTime;

    /** 是否换单 */
    private Boolean isChange;

    /** 是否监管 */
    private Boolean isOversight;

    /** 营业点 */
    private String businessPoint;

    /** 航班号 */
    private String flightNo;

    /** 是否作废 */
    private Boolean isInvalid;

    /** 制单人 */
    private String writer;

    /** 运价类型 */
    private String tariffType;

    /** 报表名称 */
    @NotEmpty(message = "报表名称不能为空")
    private String reportTitle;

    /** 当前页 */
    private Integer pageNum;

    /** 每页数量 */
    private Integer pageSize;
}
