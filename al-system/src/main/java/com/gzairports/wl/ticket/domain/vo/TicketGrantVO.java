package com.gzairports.wl.ticket.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 单证发放记录返回数据
 *
 * <AUTHOR>
 * @date 2024-03-20
 */
@Data
public class TicketGrantVO {

    /** 主键id */
    private Long id;

    /** 票证id */
    private Long ticketId;

    /** 单证id */
    private String grantNum;

    /** 数量 */
    private Integer total;

    /** 领单部门 */
    private String deptName;

    /** 领单人 */
    private String userName;

    /** 状态 */
    private Integer status;

    /** 领单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date grantTime;
}
