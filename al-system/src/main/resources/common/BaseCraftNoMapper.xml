<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.BaseCraftNoMapper">
    <select id="selectCraftNoList" resultType="com.gzairports.common.basedata.domain.BaseCraftNo">
        select id, craft_no, craft_type, air_ways, des_port, start_time, end_time, cabins
        from base_craft_no_settings
        where type=#{type}
        <if test="airWays != null and airWays != ''">
            and air_ways = #{airWays}
        </if>
        <if test="desPort != null and desPort != ''">
            and des_port = #{desPort}
        </if>
        <if test="craftNo != null and craftNo != ''">
            and craft_no = #{craftNo}
        </if>
        <if test="craftType != null and craftType != ''">
            and craft_type = #{craftType}
        </if>
    </select>
    <select id="selectCraftNoMailList" resultType="com.gzairports.common.basedata.domain.BaseCraftNoMail">
        select id, craft_no, craft_type, air_ways, des_port, start_time, end_time, cabins
        from base_craft_no_settings
        where type=#{type}
        <if test="airWays != null and airWays != ''">
            and air_ways = #{airWays}
        </if>
        <if test="desPort != null and desPort != ''">
            and des_port = #{desPort}
        </if>
        <if test="craftNo != null and craftNo != ''">
            and craft_no = #{craftNo}
        </if>
        <if test="craftType != null and craftType != ''">
            and craft_type = #{craftType}
        </if>
    </select>
    <select id="selectCraftNoInfo" resultType="com.gzairports.common.basedata.domain.BaseCraftNo">
        select id, craft_no, craft_type, air_ways, des_port, start_time, end_time, cabins
        from base_craft_no_settings
        where type=#{type} and craft_no=#{craftNo} and start_time &lt;= #{date} and end_time &gt;= #{date}
    </select>
</mapper>