package com.gzairports.hz.business.reporter.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @author: lan
 * @Desc: 货站报表设置过滤字段设置表表
 * @create: 2025-03-05 17:00
 **/

@Data
@TableName("hz_report_set_filter")
public class HzReportSetFilter {
    /** 主键id */
    private Long id;

    /** 报表设置id */
    private Long setId;

    /** 字段中文 */
    private String fieldNameCn;

    /** 字段类型 文本 varchar  数字 int  时间 datetime  枚举 enum */
    private String fieldType;

    /** 枚举值 */
    private String fieldEnumValue;

    /** 字段 */
    private String fieldName;

    /** 过滤设置 0等于 1大于 2小于 3大于小于 4模糊 */
    private String fieldFilterType;

    /** 是否固定过滤 0否 1是  ->在查询时就不可更改*/
    private String fieldFilterFix;

    /** 固定过滤值 */
    private String fieldFilterValue;

    /** 报表设置主表从表字段id */
    private Long fieldId;

}
