package com.gzairports.common.basedata.service.impl;


import com.gzairports.common.basedata.domain.BaseVolume;
import com.gzairports.common.basedata.domain.query.VolumeUnitQuery;
import com.gzairports.common.basedata.mapper.VolumeMapper;
import com.gzairports.common.basedata.service.IVolumeService;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.exception.ServiceException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.bean.BeanValidators;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.Validator;
import java.util.List;

/**
 * 多体积单位Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Service
public class VolumeServiceImpl implements IVolumeService {
    
    private static final Logger log = LoggerFactory.getLogger(WeightServiceImpl.class);

    @Autowired
    private Validator validator;
    
    @Autowired
    private VolumeMapper volumeMapper;

    /**
     * 查询体积单位列表
     *
     * @param query 查询参数
     * @return 体积单位列表
     */
    @Override
    public List<BaseVolume> selectVolumeUnitList(VolumeUnitQuery query) {
        return volumeMapper.selectVolumeUnitList(query);
    }

    /**
     * 导入体积单位
     *
     * @param volumes 体积单位数据列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importVolume(List<BaseVolume> volumes, boolean updateSupport) {
        if (StringUtils.isNull(volumes) || volumes.size() == 0)
        {
            throw new ServiceException("导入体积单位数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (BaseVolume volume : volumes) {
            try {
                // 验证是否存在相同的体积单位
                BaseVolume baseVolume = volumeMapper.selectVolumeByVolumeUnit(volume.getVolumeUnit());
                if (StringUtils.isNull(baseVolume)) {
                    BeanValidators.validateWithException(validator, volume);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    volume.setCreateBy(loginUser.getUsername());
                    volume.setCreateTime(DateUtils.getNowDate());
                    volumeMapper.insertVolume(volume);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、体积单位 " + volume.getVolumeUnit() + " 导入成功");
                }
                else if (updateSupport) {
                    BeanValidators.validateWithException(validator, volume);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    volume.setId(baseVolume.getId());
                    volume.setUpdateBy(loginUser.getUsername());
                    volume.setUpdateTime(DateUtils.getNowDate());
                    volumeMapper.updateVolume(volume);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、体积单位 " + volume.getVolumeUnit() + " 更新成功");
                }
                else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、体积单位 " + volume.getVolumeUnit() + " 已存在");
                }
            }catch (Exception e){
                failureNum++;
                String msg = "<br/>" + failureNum + "、体积单位 " + volume.getVolumeUnit() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 新增体积单位
     *
     * @param volume 体积单位
     * @return 结果
     */
    @Override
    public int addVolume(BaseVolume volume) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        volume.setCreateBy(loginUser.getUsername());
        volume.setCreateTime(DateUtils.getNowDate());
        return volumeMapper.insertVolume(volume);
    }

    /**
     * 修改体积单位
     *
     * @param volume 体积单位
     * @return 结果
     */
    @Override
    public int editVolume(BaseVolume volume) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        volume.setUpdateBy(loginUser.getUsername());
        volume.setUpdateTime(DateUtils.getNowDate());
        return volumeMapper.updateVolume(volume);
    }

    /**
     * 删除体积单位
     *
     * @param ids 体积单位id集合
     * @return 结果
     */
    @Override
    public int delVolume(Long[] ids) {
        return volumeMapper.delVolume(ids);
    }
}
