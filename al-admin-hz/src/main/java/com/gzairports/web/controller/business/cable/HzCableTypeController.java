package com.gzairports.web.controller.business.cable;


import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.hz.business.cable.domain.HzCableType;
import com.gzairports.hz.business.cable.service.IHzCableTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 电报类型Controller
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
@RestController
@RequestMapping("/bus/cableType")
@Api(tags = "电报类型")
public class HzCableTypeController extends BaseController
{
    @Autowired
    private IHzCableTypeService hzCableTypeService;

    /**
     * 查询电报类型列表
     */
//    @PreAuthorize("@ss.hasPermi('bus:cableType:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询电报类型列表")
    public TableDataInfo list(HzCableType hzCableType)
    {
        startPage();
        List<HzCableType> list = hzCableTypeService.selectHzCableTypeList(hzCableType);
        return getDataTable(list);
    }

    /**
     * 获取电报类型详细信息
     */
    @PreAuthorize("@ss.hasPermi('bus:cableType:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取电报类型详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(hzCableTypeService.selectHzCableTypeById(id));
    }

    /**
     * 新增电报类型
     */
    @PreAuthorize("@ss.hasPermi('bus:cableType:add')")
    @Log(title = "电报类型", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ApiOperation(value = "新增电报类型")
    public AjaxResult add(@RequestBody HzCableType hzCableType)
    {
        return toAjax(hzCableTypeService.insertHzCableType(hzCableType));
    }

    /**
     * 修改电报类型
     */
    @PreAuthorize("@ss.hasPermi('bus:cableType:edit')")
    @Log(title = "电报类型", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ApiOperation(value = "修改电报类型")
    public AjaxResult edit(@RequestBody HzCableType hzCableType)
    {
        return toAjax(hzCableTypeService.updateHzCableType(hzCableType));
    }

    /**
     * 删除电报类型
     */
    @PreAuthorize("@ss.hasPermi('bus:cableType:remove')")
    @Log(title = "电报类型", businessType = BusinessType.DELETE)
	@GetMapping("/remove/{id}")
    @ApiOperation(value = "删除电报类型")
    public AjaxResult remove(@PathVariable("id") Long id)
    {
        return toAjax(hzCableTypeService.deleteHzCableTypeById(id));
    }
}
