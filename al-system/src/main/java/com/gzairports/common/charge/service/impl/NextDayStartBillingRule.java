package com.gzairports.common.charge.service.impl;

import com.gzairports.common.business.arrival.domain.HzArrItem;
import com.gzairports.common.business.arrival.domain.ItemDetail;
import com.gzairports.common.charge.domain.HzChargeItemRule;
import com.gzairports.common.charge.domain.vo.BillRuleVo;
import com.gzairports.common.charge.service.BillingRule;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.List;

/**
 * 次日起按天计算费率规则
 * <AUTHOR>
 * @date 2024-06-13
 */
public class NextDayStartBillingRule implements BillingRule {

    @Override
    public String className() {
        return "NextDayStartBillingRule.class";
    }

    @Override
    public BillRuleVo calculateFee(List<HzChargeItemRule> rule, BigDecimal weight, Integer quantity, ItemDetail item) { HzChargeItemRule itemRule = rule.get(0);
        BillRuleVo vo = new BillRuleVo();
        vo.setQuantity(item.getDaysInStorage().toString());
        // 次日起不满一天的
        if (item.getDaysInStorage() <= 1) {
            vo.setTotalCharge(new BigDecimal(0));
            return vo;
        } else {
            BigDecimal multiply1 = itemRule.getRate().multiply(BigDecimal.valueOf(item.getDaysInStorage()).subtract(new BigDecimal(1))).multiply(weight);
            BigDecimal decimal = itemRule.getMinimum() == null ? new BigDecimal(0) : itemRule.getMinimum();
            if (multiply1.compareTo(decimal) >= 0){
                vo.setRate(itemRule.getRate());
            }
            BigDecimal max = multiply1.max(decimal);
            vo.setTotalCharge(max);
            return vo;
        }
    }
}
