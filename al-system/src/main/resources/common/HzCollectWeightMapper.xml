<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.departure.mapper.HzCollectWeightMapper">
    <select id="selectCollectList" resultType="com.gzairports.hz.business.departure.domain.vo.ForwardImportUldVo">
        select hw.id as weightId, hw.collect_id, hw.uld, hw.quantity as totalQuantity,
               hw.weight, hw.board_weight, hw.plate_weight, aaw.quantity as waybillQuantity, aaw.weight as waybillWeight
        from hz_collect_weight hw
        left join hz_collect_waybill hcw on hcw.id = hw.collect_id
        left join all_air_waybill aaw on aaw.id = hcw.waybill_id
        <where>
            hw.is_load = 0 and (hw.uld != '' or hw.uld is not null)
             and aaw.status != 'INVALID' and aaw.type = 'DEP'
            <if test="query.des1 != null and query.des1 != ''">
                and aaw.des1 = #{query.des1}
            </if>
            <if test="query.carrierList != null and query.carrierList.size() > 0">
                and hw.air_ways in
                <foreach collection="query.carrierList" item="code" index="index" open="(" close=")" separator=",">
                    #{code}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectListByIds" resultType="com.gzairports.hz.business.departure.domain.vo.ForwardImportUldVo">
        select uld, sum(quantity) as totalQuantity, sum(weight) as totalWeight from hz_collect_weight where uld in
        <foreach collection="collectUld" item="uld" open="(" separator="," close=")">
            #{uld}
        </foreach>
        group by uld
    </select>
    <select id="selectListByWaybillId"
            resultType="com.gzairports.hz.business.departure.domain.HzCollectWeight">
        select hcwe.store, hcwe.locator
        from hz_collect_weight hcwe
            left join hz_collect_waybill hcwa on hcwa.id = hcwe.collect_id
        where hcwa.waybill_id = #{id}
    </select>
    <select id="selectCollectWaybillList-back"
            resultType="com.gzairports.hz.business.departure.domain.vo.ForwardImportWaybillVo">
        (
        select hcw.id as collectId, hw.id as weightId, aaw.id as waybillId, aaw.cargo_name, aaw.special_cargo_code1, aaw.waybill_code, aaw.source_port,
               aaw.des1, hw.quantity, hw.total_weight as weight
        from hz_collect_weight hw
            left join hz_collect_waybill hcw on hcw.id = hw.collect_id
            left join all_air_waybill aaw on aaw.id = hcw.waybill_id
        <where>
            hw.is_load = 0 and (hw.uld is null or hw.uld = '')
            <if test="waybillCode != null  and waybillCode != ''"> and aaw.waybill_code = #{waybillCode}</if>
            <if test="flightNo1 != null  and flightNo1 != ''"> and aaw.flight_no1 like concat('%',#{flightNo1},'%')</if>
            <if test="flightDate1 != null"> and aaw.flight_date1 = #{flightDate1}</if>
            <if test="sourcePort != null  and sourcePort != ''"> and aaw.source_port = #{sourcePort}</if>
            <if test="startStation != null  and startStation != ''"> and aaw.source_port = #{startStation}</if>
            <if test="carrier1 != null  and carrier1 != ''"> and aaw.carrier1 = #{carrier1}</if>
            <if test="des1 != null  and des1 != ''"> and aaw.des1 = #{des1}</if>
            <if test="specialCargoCode1 != null  and specialCargoCode1 != ''"> and aaw.special_cargo_code1 = #{specialCargoCode1}</if>
            <if test="shipper != null  and shipper != ''"> and aaw.agent_company = #{shipper}</if>
            <if test="startTime != null">
                and aaw.write_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and aaw.write_time <![CDATA[<=]]> #{endTime}
            </if>
        </where>
        )
        union all
        (
        select hthw.id as collectId, hthw.id as weightId, aaw.id as waybillId, aaw.cargo_name, aaw.special_cargo_code1, aaw.waybill_code, aaw.source_port,
        aaw.des1, aaw.quantity, aaw.weight
        from hz_transfer_handover_waybill hthw
        left join all_air_waybill aaw on aaw.id = hthw.waybill_id
        left join hz_transfer_handover hth on hth.id = hthw.transfer_id
        <where>
            hthw.is_load = 0 and (hth.file_status = 2 or hth.cargo_status = 2)
            <if test="waybillCode != null  and waybillCode != ''"> and aaw.waybill_code = #{waybillCode}</if>
            <if test="flightNo1 != null  and flightNo1 != ''"> and aaw.flight_no1 like concat('%',#{flightNo1},'%')</if>
            <if test="flightDate1 != null"> and aaw.flight_date1 = #{flightDate1}</if>
            <if test="sourcePort != null  and sourcePort != ''"> and aaw.source_port = #{sourcePort}</if>
            <if test="startStation != null  and startStation != ''"> and aaw.source_port = #{startStation}</if>
            <if test="carrier1 != null  and carrier1 != ''"> and aaw.carrier1 = #{carrier1}</if>
            <if test="des1 != null  and des1 != ''"> and aaw.des1 = #{des1}</if>
            <if test="specialCargoCode1 != null  and specialCargoCode1 != ''"> and aaw.special_cargo_code1 = #{specialCargoCode1}</if>
            <if test="shipper != null  and shipper != ''"> and aaw.shipper = #{shipper}</if>
            <if test="startTime != null">
                and aaw.write_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and aaw.write_time <![CDATA[<=]]> #{endTime}
            </if>
        </where>
        )
    </select>
    <select id="selectCollectWaybillList"
            resultType="com.gzairports.hz.business.departure.domain.vo.ForwardImportWaybillVo">
        select hcw.id as collectId, hw.id as weightId, aaw.id as waybillId, aaw.cargo_name, aaw.special_cargo_code1,
               aaw.waybill_code, aaw.des1, hw.quantity, hw.total_weight as weight, aaw.write_time, aaw.quantity as waybillQuantity,
               aaw.weight as waybillWeight, aaw.storage_transport_notes,sd.dept_name_abb
        from hz_collect_weight hw
        left join hz_collect_waybill hcw on hcw.id = hw.collect_id
        left join all_air_waybill aaw on aaw.id = hcw.waybill_id
        left join sys_dept sd on sd.dept_id = aaw.dept_id
        <where>
            hw.is_load = 0 and (hw.uld is null or hw.uld = '') and aaw.status != 'INVALID'
            <if test="query.waybillCode != null  and query.waybillCode != ''"> and aaw.waybill_code = #{query.waybillCode}</if>
            <if test="query.flightNo1 != null  and query.flightNo1 != ''"> and aaw.flight_no1 like concat('%',#{query.flightNo1},'%')</if>
            <if test="query.flightDate1 != null"> and aaw.flight_date1 = #{query.flightDate1}</if>
            <if test="query.sourcePort != null  and query.sourcePort != ''"> and aaw.source_port = #{query.sourcePort}</if>
            <if test="query.startStation != null  and query.startStation != ''"> and aaw.source_port = #{query.startStation}</if>
            <if test="query.carrierList != null and query.carrierList.size() > 0">
                and aaw.carrier1 in
                <foreach collection="query.carrierList" item="code" index="index" open="(" close=")" separator=",">
                    #{code}
                </foreach>
            </if>
            <if test="query.des1 != null  and query.des1 != ''"> and aaw.des1 = #{query.des1}</if>
            <if test="query.specialCargoCode1 != null  and query.specialCargoCode1 != ''"> and aaw.special_cargo_code1 = #{query.specialCargoCode1}</if>
            <if test="query.shipper != null  and query.shipper != ''"> and aaw.agent_company = #{query.shipper}</if>
            <if test="query.startTime != null">
                and aaw.write_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and aaw.write_time <![CDATA[<=]]> #{query.endTime}
            </if>
        </where>
    </select>
    <select id="selectCollectIds" resultType="java.lang.Long">
        SELECT collect_id FROM hz_collect_weight WHERE uld = #{uld} GROUP BY collect_id
    </select>
    <select id="selectImportDataByCode" resultType="java.lang.Long">
        select hw.id
        from hz_collect_weight hw
        left join hz_collect_waybill hcw on hcw.id = hw.collect_id
        where hw.is_load = 0 and hcw.waybill_id = #{waybillId} and (hw.uld is null or hw.uld = '')
    </select>
</mapper>
