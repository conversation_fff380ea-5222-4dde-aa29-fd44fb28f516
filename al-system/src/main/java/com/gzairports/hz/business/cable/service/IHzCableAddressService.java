package com.gzairports.hz.business.cable.service;


import com.gzairports.hz.business.cable.domain.HzCableAddress;
import com.gzairports.hz.business.cable.domain.query.TypeAndAddressQuery;

import java.util.List;


/**
 * 电报地址管理Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
public interface IHzCableAddressService 
{
    /**
     * 查询电报地址管理
     * 
     * @param id 电报地址管理主键
     * @return 电报地址管理
     */
    public HzCableAddress selectHzCableAddressById(Long id);

    /**
     * 查询电报地址管理列表
     * 
     * @param hzCableAddress 电报地址管理
     * @return 电报地址管理集合
     */
    public List<HzCableAddress> selectHzCableAddressList(HzCableAddress hzCableAddress);

    /**
     * 新增电报地址管理
     * 
     * @param hzCableAddress 电报地址管理
     * @return 结果
     */
    public int insertHzCableAddress(HzCableAddress hzCableAddress);

    /**
     * 修改电报地址管理
     * 
     * @param hzCableAddress 电报地址管理
     * @return 结果
     */
    public int updateHzCableAddress(HzCableAddress hzCableAddress);

    /**
     * 删除电报地址管理信息
     * 
     * @param id 电报地址管理主键
     * @return 结果
     */
    public int deleteHzCableAddressById(Long id);

    /**
     * 导入航司运价条目
     * @param cableAddresses 文件数据
     * @param updateSupport 导入所需数据
     * @return 结果
     */
    String importCableAddress(List<HzCableAddress> cableAddresses, boolean updateSupport);

    /**
     * 新版查询电报地址列表
     * @return 电报地址列表
     */
    List<TypeAndAddressQuery> newSelectHzCableAddressList();
}
