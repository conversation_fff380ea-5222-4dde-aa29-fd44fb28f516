package com.gzairports.web.controller.business.arrival;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.hz.business.arrival.domain.query.ArrInventoryQuery;
import com.gzairports.hz.business.arrival.domain.query.NodeQuery;
import com.gzairports.hz.business.arrival.domain.vo.InventoryListVo;
import com.gzairports.hz.business.arrival.domain.vo.NodeWaybillVo;
import com.gzairports.hz.business.arrival.service.INodeQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 运单保障节点查询Controller
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
@RestController
@RequestMapping("/arr/nodeQuery")
@Api(tags = "运单保障节点查询")
public class NodeQueryController extends BaseController {

    @Autowired
    private INodeQueryService nodeQueryService;

    /**
     * 运单保障节点查询列表
     */
//    @PreAuthorize("@ss.hasPermi('arr:nodeQuery:list')")
    @GetMapping("/list")
    @ApiOperation(value = "运单保障节点查询列表")
    public AjaxResult list(NodeQuery query)
    {
        return AjaxResult.success(nodeQueryService.selectList(query));
    }

    /**
     * 导出运单保障节点列表
     */
    @PreAuthorize("@ss.hasPermi('arr:nodeQuery:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出运单保障节点列表")
    public void export(HttpServletResponse response, NodeQuery query)
    {
        List<NodeWaybillVo> list = nodeQueryService.selectListByQuery(query);
        ExcelUtil<NodeWaybillVo> util = new ExcelUtil<NodeWaybillVo>(NodeWaybillVo.class);
        util.exportExcel(response, list, "运单保障节点数据");
    }
}
