package com.gzairports.web.rabbitmq;

import com.alibaba.fastjson2.JSONObject;
import com.aliyun.oss.ServiceException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.basedata.domain.AllSecurityUrl;
import com.gzairports.common.basedata.domain.BaseCarrier;
import com.gzairports.common.basedata.mapper.AirportCodeMapper;
import com.gzairports.common.basedata.mapper.CarrierMapper;
import com.gzairports.common.basedata.service.IAirportCodeService;
import com.gzairports.common.basedata.service.IBaseAgentService;
import com.gzairports.common.business.departure.domain.Mawb;
import com.gzairports.common.business.departure.mapper.MawbMapper;
import com.gzairports.common.core.domain.entity.SysDept;
import com.gzairports.common.pdf.PdfPrintHelper;
import com.gzairports.common.rabbitmq.SecurityProducer;
import com.gzairports.common.securitySubmit.domain.AllSecurityWaybill;
import com.gzairports.common.securitySubmit.domain.SampLingVo;
import com.gzairports.common.securitySubmit.domain.SecuritySubmitBack;
import com.gzairports.common.securitySubmit.domain.SecurityWaybillInfo;
import com.gzairports.common.securitySubmit.mapper.AllSecurityUrlMapper;
import com.gzairports.common.securitySubmit.mapper.AllSecurityWaybillMapper;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.sign.Base64;
import com.gzairports.common.utils.spring.SpringUtils;
import com.gzairports.framework.web.service.TokenService;
import com.gzairports.hz.business.departure.rabbitmq.Consumer;
import com.gzairports.oss.config.ImageFileName;
import com.gzairports.oss.domain.SysOss;
import com.gzairports.oss.entity.UploadResult;
import com.gzairports.oss.factory.OssFactory;
import com.gzairports.oss.mapper.SysOssMapper;
import com.gzairports.oss.service.IOssStrategy;
import com.gzairports.oss.service.impl.SysOssServiceImpl;
import com.rabbitmq.client.Channel;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @author: lan
 * @Desc:
 * @create: 2024-11-18 13:56
 **/
@Component
public class SecuritySubmitConsumer {

    @Autowired
    private MawbMapper mawbMapper;
    @Autowired
    private AllSecurityUrlMapper securityUrlMapper;
    @Autowired
    private SysDeptMapper sysDeptMapper;
    @Autowired
    private IAirportCodeService airportCodeService;
    @Autowired
    private CarrierMapper carrierMapper;
    @Autowired
    private AllSecurityWaybillMapper allSecurityWaybillMapper;

    private static final Logger logger = LoggerFactory.getLogger(SecuritySubmitConsumer.class);



    @RabbitListener(queues = "securityDataBack")
    public void customer(Message message, Channel channel) throws IOException {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        try {
            //提取消息字节数组到字符串
            byte[] bytes = message.getBody();
            String mes = new String(bytes);
            SecuritySubmitBack back = JSONObject.parseObject(mes, SecuritySubmitBack.class);
            logger.info("安检接收到的消息:" + JSONObject.toJSONString(back));
            String waybillCode;
            if(back.getWaybillCode().contains("DN")){
                waybillCode = "AWBM" + back.getWaybillCode();
            }else {
                waybillCode = "AWBA" + back.getWaybillCode();
            }
            Mawb mawb = mawbMapper.selectOne(new QueryWrapper<Mawb>()
                    .eq("waybill_code",waybillCode)
                    .eq("type","DEP"));
            AllSecurityWaybill allSecurityWaybill = allSecurityWaybillMapper.selectOne(new QueryWrapper<AllSecurityWaybill>()
                    .eq("waybill_code", waybillCode));

            //判断是审单结果还是安检结果
            if(back.getMessageType().equals("审单结果")){
                if(!back.getAuditConclusion().equals("放行")){
                    //不用货站退回 打回到物流重新提交
                    if(mawb == null){
                        allSecurityWaybill.setSecuritySubmit(-1);
                        allSecurityWaybill.setSecuritySubmitWl(-1);
                        allSecurityWaybillMapper.updateById(allSecurityWaybill);
                    }else{
                        mawb.setSecuritySubmit(-1);
                        mawb.setSecuritySubmitWl(-1);
                        mawbMapper.updateById(mawb);
                        if(allSecurityWaybill!=null){
                            allSecurityWaybill.setSecuritySubmit(-1);
                            allSecurityWaybill.setSecuritySubmitWl(-1);
                            allSecurityWaybillMapper.updateById(allSecurityWaybill);
                        }
                    }

                }
                return;
            }

            if(mawb == null){
                SysDept sysDept = sysDeptMapper.selectDeptById(allSecurityWaybill.getDeptId());
                SecurityWaybillInfo info = new SecurityWaybillInfo();
                BeanUtils.copyProperties(back,info);
                info.setCheckConclusionCollect(allSecurityWaybill.getDeclarationConsistent() == 1 ? "符合运输" : "退回");
                info.setSealUrlCollect(allSecurityWaybill.getSecuritySubmitOperator());
                info.setShipperAbb(allSecurityWaybill.getShipper());
                info.setFlightNo1(allSecurityWaybill.getFlightNo1());
                info.setQuantity(allSecurityWaybill.getQuantity());
                info.setWeight(allSecurityWaybill.getWeight());
                List<SampLingVo> samplingList = back.getSamplingList();
                if(samplingList!=null && samplingList.size()>0){
                    String samplingInspector = samplingList.stream()
                            .map(SampLingVo::getSamplingInspector)
                            .collect(Collectors.joining(", "));
                    String samplingSituation = samplingList.stream()
                            .map(SampLingVo::getSamplingSituation)
                            .collect(Collectors.joining(", "));
                    info.setSamplingInspector(samplingInspector);
                    info.setSamplingSituation(samplingSituation);
                }
                if(sysDept.getSealUrl()!=null){
                    byte[] bytes2 = downloadFileFromUrl(sysDept.getSealUrl());
                    String logoImage = Base64.encode(bytes2);
                    info.setSealUrl(logoImage);
                    info.setSealUrl2(logoImage);
                }
                info.setCheckConclusionCollect(allSecurityWaybill.getDeclarationConsistent() == 1 ? "符合运输" : "退回");
                info.setSealUrlCollect(allSecurityWaybill.getSecuritySubmitOperator());

                if(StringUtils.isNotEmpty(allSecurityWaybill.getShipperSignature())){
                    info.setShipperSignature(allSecurityWaybill.getShipperSignature());
                }
                if(StringUtils.isNotEmpty(allSecurityWaybill.getAgentSignature())){
                    info.setAgentSignature(allSecurityWaybill.getAgentSignature());
                }
                if (allSecurityWaybill.getCargoType() != null){
                    if(allSecurityWaybill.getCargoType() == 0){
                        info.setPthw("YES");
                    }else if(allSecurityWaybill.getCargoType() == 1){
                        info.setTzhw("YES");
                    }else{
                        info.setWxp("YES");
                    }
                }
                String desPortChinese = airportCodeService.selectChineseName(info.getDesPort());
                if(StringUtils.isNotEmpty(desPortChinese)){
                    info.setDesPort(desPortChinese);
                }
                String securityUrl;
                if(allSecurityWaybill.getWaybillCode().contains("AWBA")) {
                    String substring = info.getWaybillCode().substring(4);
                    String waybillCodeAbb = substring.substring(0, 3) + "-" + substring.substring(3);
                    info.setWaybillCodeAbb(waybillCodeAbb);
                    securityUrl = getPdfUrl(info, "electronic/security.pdf");
                    allSecurityWaybill.setSecurityUrl(securityUrl);
                }else{
                    String substring = info.getWaybillCode().substring(4);
                    String waybillCodeAbb = substring.substring(0, 2) + "-" + substring.substring(2);
                    info.setWaybillCodeAbb(waybillCodeAbb);
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    if (allSecurityWaybill.getFlightDate1() != null){
                        info.setFlightDate1Str(simpleDateFormat.format(allSecurityWaybill.getFlightDate1()));
                    }
                    securityUrl = getPdfUrl(info, "electronic/mailSecurity.pdf");
                    allSecurityWaybill.setSecurityUrl(securityUrl);
                }
                if(back.getCheckConclusion().equals("正常") || back.getCheckConclusion().equals("放行")){
                    allSecurityWaybill.setSecuritySubmit(1);
                    allSecurityWaybill.setSecuritySubmitWl(3);
                }else{
                    allSecurityWaybill.setSecuritySubmit(-2);
                    allSecurityWaybill.setSecuritySubmitWl(-2);
                }
                allSecurityWaybillMapper.updateById(allSecurityWaybill);

                //往历史表新增
                AllSecurityUrl allSecurityUrl = new AllSecurityUrl();
                allSecurityUrl.setWaybillId(allSecurityWaybill.getId());
                allSecurityUrl.setWaybillCode(allSecurityWaybill.getWaybillCode());
                allSecurityUrl.setSecurityUrl(allSecurityWaybill.getSecurityUrl());
                allSecurityUrl.setCreateBy("第三方货检系统");
                allSecurityUrl.setCreateTime(new Date());
                allSecurityUrl.setExecDate(back.getExecDate());
                securityUrlMapper.insert(allSecurityUrl);

            }else{
                SysDept sysDept = sysDeptMapper.selectDeptById(mawb.getDeptId());
                SecurityWaybillInfo vo = mawbMapper.getInfo(waybillCode);
                //这里要区分是主单还是邮件单
                if(mawb.getWaybillCode().contains("AWBA")){
                    //拼接pdf
                    BeanUtils.copyProperties(back,vo);
                    List<SampLingVo> samplingList = back.getSamplingList();
                    if(samplingList!=null && samplingList.size()>0){
                        String samplingInspector = samplingList.stream()
                                .map(SampLingVo::getSamplingInspector)
                                .collect(Collectors.joining(", "));
                        String samplingSituation = samplingList.stream()
                                .map(SampLingVo::getSamplingSituation)
                                .collect(Collectors.joining(", "));
                        vo.setSamplingInspector(samplingInspector);
                        vo.setSamplingSituation(samplingSituation);
                    }
                    if(sysDept.getSealUrl()!=null){
                        byte[] bytes2 = downloadFileFromUrl(sysDept.getSealUrl());
                        String logoImage = Base64.encode(bytes2);
                        vo.setSealUrl(logoImage);
                        vo.setSealUrl2(logoImage);
                    }
                    vo.setCheckConclusionCollect(mawb.getDeclarationConsistent() == 1 ? "符合运输" : "退回");
                    vo.setSealUrlCollect(mawb.getSecuritySubmitOperator());

                    if(StringUtils.isNotEmpty(mawb.getShipperSignature())){
                        vo.setShipperSignature(mawb.getShipperSignature());
                    }
                    if(StringUtils.isNotEmpty(mawb.getAgentSignature())){
                        vo.setAgentSignature(mawb.getAgentSignature());
                    }

                    SecurityWaybillInfo securityWaybillInfo = generateSecurity(vo);
                    mawb.setSecurityUrl(securityWaybillInfo.getSecurityUrl());
                }else{
                    SecurityWaybillInfo mailVo = new SecurityWaybillInfo();
                    BeanUtils.copyProperties(back,mailVo);
                    mailVo.setCheckConclusionCollect(mawb.getDeclarationConsistent() == 1 ? "符合运输" : "退回");
                    mailVo.setSealUrlCollect(mawb.getSecuritySubmitOperator());
                    mailVo.setShipperAbb(mawb.getShipperAbb());
                    mailVo.setFlightNo1(mawb.getFlightNo1());
                    List<SampLingVo> samplingList = back.getSamplingList();
                    if(samplingList!=null && samplingList.size()>0){
                        String samplingInspector = samplingList.stream()
                                .map(SampLingVo::getSamplingInspector)
                                .collect(Collectors.joining(", "));
                        String samplingSituation = samplingList.stream()
                                .map(SampLingVo::getSamplingSituation)
                                .collect(Collectors.joining(", "));
                        mailVo.setSamplingInspector(samplingInspector);
                        mailVo.setSamplingSituation(samplingSituation);
                    }
                    if(sysDept!=null){
                        if(StringUtils.isNotEmpty(sysDept.getSealUrl())){
                            byte[] bytes3 = downloadFileFromUrl(sysDept.getSealUrl());
                            String logoImage = Base64.encode(bytes3);
                            mailVo.setSealUrl(logoImage);
                        }
                    }
                    if(StringUtils.isNotEmpty(mawb.getShipperSignature())){
                        mailVo.setShipperSignature(mawb.getShipperSignature());
                    }
                    if(StringUtils.isNotEmpty(mawb.getAgentSignature())){
                        mailVo.setAgentSignature(mawb.getAgentSignature());
                    }
//                String substring = mailVo.getWaybillCode().substring(4);
//                String waybillCodeAbb = substring.substring(0, 2) + "-" + substring.substring(2);
                    mailVo.setWaybillCodeAbb(mailVo.getWaybillCode());
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    if (mawb.getFlightDate1() != null){
                        mailVo.setFlightDate1Str(simpleDateFormat.format(mawb.getFlightDate1()));
                    }
                    //将目的站从英文转化为中文
                    String desPortChinese = airportCodeService.selectChineseName(mawb.getDesPort());
                    if(StringUtils.isNotEmpty(desPortChinese)){
                        mailVo.setDesPort(desPortChinese);
                    }
                    String carrier1 = mawb.getCarrier1();
                    BaseCarrier baseCarrier = carrierMapper.selectByCode(carrier1);
                    if(baseCarrier!=null){
                        mailVo.setCarrier1(baseCarrier.getChineseName());
                    }
                    String securityUrl = getPdfUrl(mailVo, "electronic/mailSecurity.pdf");
                    mawb.setSecurityUrl(securityUrl);
                }

                if(back.getCheckConclusion().equals("正常") || back.getCheckConclusion().equals("放行")){
                    mawb.setSecuritySubmit(1);
                    mawb.setSecuritySubmitWl(3);
                }else{
                    mawb.setSecuritySubmit(-2);
                    mawb.setSecuritySubmitWl(-2);
                }
                mawbMapper.updateById(mawb);
                if(allSecurityWaybill!=null){
                    if(back.getCheckConclusion().equals("正常") || back.getCheckConclusion().equals("放行")){
                        allSecurityWaybill.setSecuritySubmit(1);
                        allSecurityWaybill.setSecuritySubmitWl(3);
                    }else{
                        allSecurityWaybill.setSecuritySubmit(-2);
                        allSecurityWaybill.setSecuritySubmitWl(-2);
                    }
                    allSecurityWaybillMapper.updateById(allSecurityWaybill);
                }

                //往历史表新增
                AllSecurityUrl allSecurityUrl = new AllSecurityUrl();
                allSecurityUrl.setWaybillId(mawb.getId());
                allSecurityUrl.setWaybillCode(mawb.getWaybillCode());
                allSecurityUrl.setSecurityUrl(mawb.getSecurityUrl());
                allSecurityUrl.setCreateBy("第三方货检系统");
                allSecurityUrl.setCreateTime(new Date());
                allSecurityUrl.setExecDate(back.getExecDate());
                securityUrlMapper.insert(allSecurityUrl);
            }


            channel.basicAck(deliveryTag,true);
        }catch (Exception e){
            logger.error("获取失败：" + e);
            channel.basicReject(deliveryTag,false);
        }
    }


    private SecurityWaybillInfo generateSecurity(SecurityWaybillInfo vo) throws Exception {
//        String substring = vo.getWaybillCode().substring(4);
        String substring = vo.getWaybillCode();
        String waybillCodeAbb = substring.substring(0, 3) + "-" + substring.substring(3);
        vo.setWaybillCodeAbb(waybillCodeAbb);
        //将目的站从英文转化为中文
        String desPortChinese = airportCodeService.selectChineseName(vo.getDesPort());
        if(StringUtils.isNotEmpty(desPortChinese)){
            vo.setDesPort(desPortChinese);
        }
        if (StringUtils.isNotEmpty(vo.getDangerCode())){
            vo.setWxp("YES");
        }else if (StringUtils.isNotEmpty(vo.getSpecialCargoCode1())){
            vo.setTzhw("YES");
        }else {
            vo.setPthw("YES");
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if (vo.getFlightDate1() != null){
            vo.setFlightDate1Str(simpleDateFormat.format(vo.getFlightDate1()));
        }
        String securityUrl = getPdfUrl(vo, "electronic/security.pdf");
        vo.setSecurityUrl(securityUrl);
        return vo;
    }

    private String getPdfUrl(@RequestBody SecurityWaybillInfo vo, String s) throws Exception {
        ClassPathResource securityUrl = new ClassPathResource(s);
        if (securityUrl.exists()) {
            String path = securityUrl.getPath();
            byte[] pdfDataFromTemplate = PdfPrintHelper.getPdfDataFromTemplate(vo, path);
            // 使用StandardMultipartHttpServletRequest来创建MultipartFile
            DiskFileItem fileItem = new DiskFileItem(vo.getWaybillCode() + ".pdf", "application/pdf", true, vo.getWaybillCode() + ".pdf", -1, null);

            // 将 InputStream 写入 DiskFileItem
            fileItem.getOutputStream().write(pdfDataFromTemplate);
            fileItem.getOutputStream().close();

            // 使用 FileItem 构建 MultipartFile
            MultipartFile multipartFile = new CommonsMultipartFile(fileItem);
            SysOss upload = SpringUtils.getBean(SysOssServiceImpl.class).uploadByNoHttp(multipartFile);
            return upload.getUrl();
        }
        return null;
    }

    private byte[] downloadFileFromUrl(String urlStr) throws Exception {
        URL url = new URL(urlStr);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(5000);
        connection.setReadTimeout(5000);

        if (connection.getResponseCode() != 200) {
            throw new RuntimeException("Failed to connect, HTTP error code : " + connection.getResponseCode());
        }

        try (InputStream inputStream = connection.getInputStream();
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            return baos.toByteArray();
        }
    }


}
