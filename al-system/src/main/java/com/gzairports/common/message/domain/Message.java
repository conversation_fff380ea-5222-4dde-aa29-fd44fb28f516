package com.gzairports.common.message.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 消息管理表
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@Data
@TableName("all_message")
public class Message {

    /** 主键id */
    private Long id;

    /** 标题 */
    private String title;

    /** 处理人 */
    private String handleName;

    /** 是否已处理 0 否 1 是 */
    private Integer isHandle;

    /** 消息内容 */
    private String content;

    /** 消息接收岗位 */
    private Long postId;

    /** 消息产生时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date handleTime;
}
