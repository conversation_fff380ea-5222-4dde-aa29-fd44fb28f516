package com.gzairports.common.basedata.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.BaseDutySchedule;
import com.gzairports.common.basedata.mapper.BaseDutyScheduleMapper;
import com.gzairports.common.basedata.service.IBaseDutyScheduleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 值班人员信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-01
 */
@Service
public class BaseDutyScheduleServiceImpl extends ServiceImpl<BaseDutyScheduleMapper, BaseDutySchedule> implements IBaseDutyScheduleService
{
    @Autowired
    private BaseDutyScheduleMapper baseDutyScheduleMapper;

    /**
     * 查询值班人员信息
     * 
     * @param id 值班人员信息ID
     * @return 值班人员信息
     */
    @Override
    public BaseDutySchedule selectHzDutyScheduleById(Long id)
    {
        return baseDutyScheduleMapper.selectHzDutyScheduleById(id);
    }

    /**
     * 查询值班人员信息列表
     * 
     * @param baseDutySchedule 值班人员信息
     * @return 值班人员信息
     */
    @Override
    public List<BaseDutySchedule> selectHzDutyScheduleList(BaseDutySchedule baseDutySchedule)
    {
        return baseDutyScheduleMapper.selectHzDutyScheduleList(baseDutySchedule);
    }

    /**
     * 新增值班人员信息
     * 
     * @param baseDutySchedule 值班人员信息
     * @return 结果
     */
    @Override
    public int insertHzDutySchedule(BaseDutySchedule baseDutySchedule)
    {
        baseDutySchedule.setCreateTime(LocalDateTime.now());
        return baseDutyScheduleMapper.insertHzDutySchedule(baseDutySchedule);
    }

    /**
     * 修改值班人员信息
     * 
     * @param baseDutySchedule 值班人员信息
     * @return 结果
     */
    @Override
    public int updateHzDutySchedule(BaseDutySchedule baseDutySchedule)
    {
        baseDutySchedule.setUpdateTime(LocalDateTime.now());
        return getBaseMapper().updateHzDutySchedule(baseDutySchedule);
    }

    /**
     * 批量删除值班人员信息
     * 
     * @param ids 需要删除的值班人员信息ID
     * @return 结果
     */
    @Override
    public int deleteHzDutyScheduleByIds(Long[] ids)
    {
        return baseDutyScheduleMapper.deleteHzDutyScheduleByIds(ids);
    }

    /**
     * 删除值班人员信息信息
     * 
     * @param id 值班人员信息ID
     * @return 结果
     */
    @Override
    public int deleteHzDutyScheduleById(Long id)
    {
        return baseDutyScheduleMapper.deleteHzDutyScheduleById(id);
    }
}
