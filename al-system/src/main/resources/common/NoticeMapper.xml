<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.infoquery.mapper.NoticeMapper">
    
    <resultMap type="SysNotice" id="SysNoticeResult">
        <result property="noticeId"       column="notice_id"       />
        <result property="noticeTitle"    column="notice_title"    />
        <result property="noticeType"     column="notice_type"     />
        <result property="noticeContent"  column="notice_content"  />
        <result property="status"         column="status"          />
        <result property="createBy"       column="create_by"       />
        <result property="createTime"     column="create_time"     />
        <result property="updateBy"       column="update_by"       />
        <result property="updateTime"     column="update_time"     />
        <result property="remark"         column="remark"          />
        <result property="viewScope"      column="view_scope"      />
        <result property="carrier"        column="carrier"         />
    </resultMap>
    
    <sql id="selectNoticeVo">
        select notice_id, notice_title, notice_type, cast(notice_content as char) as notice_content, status, create_by, create_time, update_by,
               update_time, remark, view_scope, carrier, file_type
		from sys_notice
    </sql>
    
    <select id="selectNoticeById" parameterType="Long" resultMap="SysNoticeResult">
        <include refid="selectNoticeVo"/>
        where notice_id = #{noticeId}
    </select>
    
    <select id="selectNoticeList" parameterType="SysNotice" resultMap="SysNoticeResult">
        <include refid="selectNoticeVo"/>
        <where>
			<if test="noticeTitle != null and noticeTitle != ''">
				AND notice_title like concat('%', #{noticeTitle}, '%')
			</if>
			<if test="noticeType != null and noticeType != ''">
				AND notice_type = #{noticeType}
			</if>
            <if test="viewScope != null and viewScope != ''">
                AND view_scope = #{viewScope}
            </if>
            <if test="carrier != null and carrier != ''">
                AND carrier = #{carrier}
            </if>
			<if test="createBy != null and createBy != ''">
				AND create_by like concat('%', #{createBy}, '%')
			</if>
            <if test="startTime != null">
                and create_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and create_time <![CDATA[<=]]> #{endTime}
            </if>
		</where>
    </select>
    
    <insert id="insertNotice" parameterType="SysNotice">
        insert into sys_notice (
			<if test="noticeTitle != null and noticeTitle != '' ">notice_title, </if>
			<if test="noticeType != null and noticeType != '' ">notice_type, </if>
			<if test="noticeContent != null and noticeContent != '' ">notice_content, </if>
			<if test="status != null and status != '' ">status, </if>
			<if test="remark != null and remark != ''">remark,</if>
			<if test="viewScope != null and viewScope != ''">view_scope,</if>
			<if test="carrier != null and carrier != ''">carrier,</if>
			<if test="fileType != null and fileType != ''">file_type,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			create_time
 		)values(
			<if test="noticeTitle != null and noticeTitle != ''">#{noticeTitle}, </if>
			<if test="noticeType != null and noticeType != ''">#{noticeType}, </if>
			<if test="noticeContent != null and noticeContent != ''">#{noticeContent}, </if>
			<if test="status != null and status != ''">#{status}, </if>
			<if test="remark != null and remark != ''">#{remark},</if>
			<if test="viewScope != null and viewScope != ''">#{viewScope},</if>
			<if test="carrier != null and carrier != ''">#{carrier},</if>
			<if test="fileType != null and fileType != ''">#{fileType},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			sysdate()
		)
    </insert>
	 
    <update id="updateNotice" parameterType="SysNotice">
        update sys_notice 
        <set>
            <if test="noticeTitle != null and noticeTitle != ''">notice_title = #{noticeTitle}, </if>
            <if test="noticeType != null and noticeType != ''">notice_type = #{noticeType}, </if>
            <if test="noticeContent != null">notice_content = #{noticeContent}, </if>
            <if test="status != null and status != ''">status = #{status}, </if>
            <if test="viewScope != null and viewScope != ''">view_scope = #{viewScope}, </if>
            <if test="carrier != null and carrier != ''">carrier = #{carrier}, </if>
            <if test="fileType != null and fileType != ''">file_type = #{fileType}, </if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			update_time = sysdate()
        </set>
        where notice_id = #{noticeId}
    </update>
	
    <delete id="deleteNoticeById" parameterType="Long">
        delete from sys_notice where notice_id = #{noticeId}
    </delete>
    
    <delete id="deleteNoticeByIds" parameterType="Long">
        delete from sys_notice where notice_id in 
        <foreach item="noticeId" collection="array" open="(" separator="," close=")">
            #{noticeId}
        </foreach>
    </delete>
    
</mapper>