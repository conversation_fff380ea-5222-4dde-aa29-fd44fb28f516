package com.gzairports.web.controller.business.departure;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.hz.business.departure.domain.HzColdRegister;
import com.gzairports.hz.business.departure.domain.query.HzColdRegisterQuery;
import com.gzairports.hz.business.departure.domain.query.InventoryQuery;
import com.gzairports.hz.business.departure.domain.vo.HzColdRegisterVo;
import com.gzairports.hz.business.departure.domain.vo.InventoryRequestVo;
import com.gzairports.hz.business.departure.domain.vo.InventoryVo;
import com.gzairports.hz.business.departure.service.IHzCollectWaybillService;
import com.gzairports.hz.business.departure.service.IRepeatWeightService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 消息管理Controller
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@RestController
@RequestMapping("/dep/inventory")
@Api(tags = "库存管理")
public class InventoryController extends BaseController {

    @Autowired
    private IHzCollectWaybillService hzCollectWaybillService;

    /**
     * 查询库存列表
     */
//    @PreAuthorize("@ss.hasPermi('dep:inventory:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询库存列表")
    public AjaxResult list(InventoryQuery query){
        InventoryRequestVo vo = hzCollectWaybillService.selectList(query);
        return AjaxResult.success(vo);
    }

    /**
     * 导出库存列表
     */
    @PreAuthorize("@ss.hasPermi('dep:inventory:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出库存列表")
    public void export(HttpServletResponse response, InventoryQuery query)
    {
        List<InventoryVo> list = hzCollectWaybillService.selectListByQuery(query);
        ExcelUtil<InventoryVo> util = new ExcelUtil<InventoryVo>(InventoryVo.class);
        util.exportExcel(response, list, "库存列表");
    }

}
