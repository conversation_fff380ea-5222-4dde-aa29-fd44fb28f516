package com.gzairports.common.business.departure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.business.departure.domain.CostDetail;
import com.gzairports.common.business.departure.mapper.CostDetailMapper;
import com.gzairports.common.business.departure.service.ICostDetailService;
import org.springframework.stereotype.Service;

/**
 * 出港费用明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Service
public class CostDetailServiceImpl extends ServiceImpl<CostDetailMapper, CostDetail> implements ICostDetailService {
}
