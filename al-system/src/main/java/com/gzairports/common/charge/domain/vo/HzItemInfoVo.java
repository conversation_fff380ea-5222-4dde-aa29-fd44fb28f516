package com.gzairports.common.charge.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.charge.domain.HzChargeItemRule;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 货站收费项目详情返回数据
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@Data
public class HzItemInfoVo {

    /** 主键id */
    private Long id;

    /** 收费项目简称 */
    private String chargeAbb;

    /** 收费项目名称 */
    private String chargeName;

    /** 进出港类型 ARR 进港 DEP 出港 */
    private String operationType;

    /** 是否默认收费 0 否 1 是 */
    private Integer isDefault;

    /** 是否可编辑 0 否 1 是 */
    private Integer isEdit;

    /** 有效日期开始 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startEffectiveTime;

    /** 有效日期开始 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endEffectiveTime;

    /** 备注 */
    private String remark;

    /** 状态 */
    private Integer status;

    /** 是否服务项目收费 0 否 1 是 */
    private Integer isServiceItem;

    /** 四舍五入规则 */
    private Integer roundRule;

    /** 计费规则列表 */
    private List<HzItemRuleVo> rules;
}
