package com.gzairports.hz.business.departure.service;

import java.math.BigDecimal;

public interface Formal {

    String getCabin();

    String getWaybillCode();

    String getOriginBill();

    Integer getQuantity();

    BigDecimal getWeight();

    BigDecimal getVolume();

    String getCargoName();

    String getSourcePort();

    String getDesPort();

    String getSpecialCargoCode1();

    String getPriority();

    String getRemark();
}
