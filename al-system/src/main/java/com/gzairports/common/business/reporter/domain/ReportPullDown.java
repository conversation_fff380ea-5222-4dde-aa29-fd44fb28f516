package com.gzairports.common.business.reporter.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-02-19
 */
@Data
@TableName("all_report_pull_down")
public class ReportPullDown {

    /** 主键id */
    private Long id;

    /** 报表id */
    private Long reportId;

    /** 运单号 */
    private String waybillCode;

    /** 航班id */
    @TableField(exist = false)
    private String flightId;

    /** 航班日期 */
    private String flightDate;

    /** 航班号 */
    private String flightNo;

    /** 拉下时间 */
    private String operTime;

    /** 拉下件数 */
    private Integer quantity;

    /** 拉下重量（总重 - ULD重量 - 垫板重量） */
    private BigDecimal weight;

    /** 配载重量 */
    private String loadWeight;

    /** 配载件数 */
    private String loadQuantity;

    /** 航班id-再次预配的 */
    private String flightIdNew;

    /** 代理人 */
    private String agentCompany;

    /** 拉下原因 */
    private String downReason;

    /** 操作人（昵称） */
    private String operName;

    /** 品名编码 */
    private String cargoCode;

    /** 品名 */
    private String cargoName;
}
