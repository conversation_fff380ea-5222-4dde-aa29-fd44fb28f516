package com.gzairports.wl.cargofee.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.BaseAgent;
import com.gzairports.common.basedata.mapper.BaseAgentMapper;
import com.gzairports.common.core.domain.PageQuery;
import com.gzairports.hz.business.departure.domain.vo.SecurityVo;
import com.gzairports.wl.cargofee.domain.PayRecord;
import com.gzairports.wl.cargofee.domain.query.PayRecordQuery;
import com.gzairports.wl.cargofee.mapper.PayRecordMapper;
import com.gzairports.wl.cargofee.service.IPayRecordService;
import com.gzairports.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 支付流水记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@Service
public class PayRecordService extends ServiceImpl<PayRecordMapper, PayRecord> implements IPayRecordService {

    @Autowired
    private PayRecordMapper payRecordMapper;


    /**
     * 查询支付流水记录
     * @param query 查询条件
     * @return 结果
     */
    @Override
    public List<PayRecord> selectList(PayRecordQuery query) {
//        query.setDeptId(SecurityUtils.getHighParentId());
//        List<PayRecord> payRecords = payRecordMapper.selectListByQuery(query);
        List<PayRecord> payRecords = payRecordMapper.selectListByQueryForBalance(query);
        return payRecords;

//        for (PayRecord payRecord : payRecords) {
//            String s = payRecord.getStatus() == 0 ? "失败" : "成功";
//            payRecord.setStrStart(s);
//            switch (payRecord.getType()){
//                case 0:
//                    payRecord.setStrType("预授权");
//                    break;
//                case 1:
//                    payRecord.setStrType("结算支付");
//                    break;
//                case 2:
//                    payRecord.setStrType("结算退款");
//                    break;
//                case 3:
//                    payRecord.setStrType("充值");
//                    break;
//                default:
//                    payRecord.setStrType("");
//
//            }
//        }
    }
}
