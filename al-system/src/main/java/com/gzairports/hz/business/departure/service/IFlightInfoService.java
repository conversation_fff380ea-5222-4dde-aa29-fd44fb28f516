package com.gzairports.hz.business.departure.service;




import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.business.departure.domain.FlightInfo;
import com.gzairports.hz.business.departure.domain.query.FlightInfoQuery;
import com.gzairports.hz.business.departure.domain.vo.FlightInfoVO;

import java.util.Date;
import java.util.List;

/**
 * 航班信息Service接口
 * 
 * <AUTHOR>
 * @date 2023-09-18
 */
public interface IFlightInfoService extends IService<FlightInfo>
{
    /**
     * 查询入库排班详情
     * 
     * @param flightId 航班信息ID
     * @return 入库排班详情
     */
    FlightInfoVO selectBusinessFlightInfoById(Long flightId);

    /**
     * 查询航班信息列表
     * 
     * @param query 航班信息
     * @return 航班信息集合
     */
    List<FlightInfoVO> selectBusinessFlightInfoList(FlightInfoQuery query);

    /**
     * 修改航班信息
     * 
     * @param businessFlightInfo 航班信息
     * @return 结果
     */
    int updateBusinessFlightInfo(FlightInfo businessFlightInfo);

    /**
     * 生产排班
     *
     * @param date 航班日期
     * @return 结果
     */
    String generate(String date);

    /**
     * 排班显示
     * @return 当天未起飞航班列表
     */
    List<FlightInfoVO> show();

    /**
     * 入库排班短信发送接口
     * @param flightId 航班主键id
     * @return 结果
     */
    String sendSms(Long flightId);

    /**
     * 入库排班未通知短信发送接口
     * @param query 未通知短信发送参数
     * @return 结果
     */
    String noSendSms(FlightInfoQuery query);

    /**
     * 修改宽体机维护列表
     * @return 结果
     */
    int updateWideBody(String[] strings);

    /**
     * 获取宽体机维护列表
     * @return 结果
     */
    String[] getWideBody();

    /**
     * 根据航班号获取最新航班日期
     * @return 结果
     */
    String getDateByNo(String flightNo);
}
