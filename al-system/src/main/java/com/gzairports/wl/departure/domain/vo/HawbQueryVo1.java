package com.gzairports.wl.departure.domain.vo;

import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by david on 2024/11/26
 * <AUTHOR>
 */
@Data
public class HawbQueryVo1 {

    private Long id;

    /** 运单号 */
    @Excel(name = "运单号")
    private String waybillCode;

    /** 关联主单号 */
    @Excel(name = "关联主单号")
    private String masterWaybillCode;

    /** 目的站 */
    @Excel(name = "目的站")
    private String desPort;

    /** 发货人 */
    @Excel(name = "发货人")
    private String shipper;

    /** 品名 */
    @Excel(name = "品名")
    private String cargoName;

    /** 件数 */
    @Excel(name = "件数")
    private Integer quantity;

    /** 重量 */
    @Excel(name = "重量")
    private BigDecimal weight;

    /** 计费重量 */
    @Excel(name = "计费重量")
    private BigDecimal chargeWeight;

    /** 航空费率 */
    @Excel(name = "航空费率")
    private BigDecimal airRate;

    /** 航空费用 */
    @Excel(name = "航空费用")
    private BigDecimal airCost;

    /** 燃油费 */
    @Excel(name = "燃油费")
    private BigDecimal fuelCost;

    /** 保险费 */
    @Excel(name = "保险费")
    private BigDecimal premium;

    /** 包装费 */
    @Excel(name = "包装费")
    private BigDecimal packCost;

    /** 其他费用 */
    @Excel(name = "其他费用")
    private BigDecimal otherCost;

    /** 合计 */
    @Excel(name = "合计")
    private BigDecimal totalCost;

    /** 制单时间 */
    @Excel(name = "制单时间",dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date writeTime;

    /** 作废备注 */
    @Excel(name = "作废备注")
    private String remark;

    /** 收货人 */
    @Excel(name = "收货人")
    private String consignee;

}
