package com.gzairports.common.basedata.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 航司机型差异
 *
 * <AUTHOR>
 * @date 2024-02-19
 */
@Data
@TableName("base_air_different")
public class BaseAirDifferent {

    /** 主键id */
    private Long id;

    /** 航司 */
    @Excel(name = "航司")
    private String airCompany;

    /** 机号 */
    @Excel(name = "机号")
    private String flightNumber;

    /** 机型 */
    @Excel(name = "机型")
    private String flightType;

    /** 目的站 */
    @Excel(name = "目的站")
    private String desPort;

    /** 不可配载货品 */
    @Excel(name = "不可配载货品")
    private String noLoad;

    /** 生效开始生效时间 */
    @Excel(name = "生效开始生效时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 生效结算生效时间 */
    @Excel(name = "生效结算生效时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;
}
