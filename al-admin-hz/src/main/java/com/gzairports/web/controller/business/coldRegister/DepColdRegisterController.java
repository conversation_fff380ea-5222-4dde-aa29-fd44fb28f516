package com.gzairports.web.controller.business.coldRegister;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.hz.business.departure.domain.HzColdRegister;
import com.gzairports.hz.business.departure.domain.query.HzColdRegisterQuery;
import com.gzairports.hz.business.departure.domain.vo.ColdQueryVo;
import com.gzairports.hz.business.departure.domain.vo.ColdRegisterVo;
import com.gzairports.hz.business.departure.domain.vo.HzColdRegisterVo;
import com.gzairports.hz.business.departure.service.IHzColdRegisterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 冷藏登记Controller
 *
 * <AUTHOR>
 * @date 2024-07-12
 */
@RestController
@RequestMapping("/dep/coldRegister")
@Api(tags = "冷藏登记")
public class DepColdRegisterController extends BaseController {

    @Autowired
    private IHzColdRegisterService registerService;

    /**
     * 查询冷藏登记列表
     */
//    @PreAuthorize("@ss.hasPermi('dep:coldRegister:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询冷藏登记列表")
    public AjaxResult list(HzColdRegisterQuery query){
        HzColdRegisterVo vo = registerService.selectList(query);
        return AjaxResult.success(vo);
    }

    /**
     * 导出冷藏登记列表
     */
    @PreAuthorize("@ss.hasPermi('dep:coldRegister:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出冷藏登记列表")
    public void export(HttpServletResponse response, HzColdRegisterQuery query)
    {
        List<ColdQueryVo> list = registerService.selectListByQuery(query);
        ExcelUtil<ColdQueryVo> util = new ExcelUtil<ColdQueryVo>(ColdQueryVo.class);
        util.exportExcel(response, list, "冷藏登记列表");
    }

    /**
     * 新增冷藏登记
     */
    @PreAuthorize("@ss.hasPermi('dep:coldRegister:add')")
    @PostMapping("/add")
    @ApiOperation(value = "新增冷藏登记")
    public AjaxResult add(@RequestBody HzColdRegister register){
        return toAjax(registerService.add(register));
    }

    /**
     * 根据运单号查询品名入库时间
     */
//    @PreAuthorize("@ss.hasPermi('dep:coldRegister:selectCargoName')")
    @GetMapping("/selectCargoName/{waybillCode}/{type}")
    @ApiOperation(value = "根据运单号查询品名")
    public AjaxResult selectCargoName(@PathVariable("waybillCode") String waybillCode,@PathVariable("type") String type){
        return AjaxResult.success("操作成功",registerService.selectCargoName(waybillCode,type,null));
    }

    /**
     * 根据计费时间计算计费金额
     */
//    @PreAuthorize("@ss.hasPermi('dep:coldRegister:countSum')")
    @PostMapping("/countSum")
    @ApiOperation(value = "根据计费时间计算计费金额")
    public AjaxResult countSum(@RequestBody ColdRegisterVo vo){
        return AjaxResult.success("操作成功",registerService.countSum(vo));
    }

    /**
     * 查看详情
     */
    @PreAuthorize("@ss.hasPermi('dep:coldRegister:getInfo')")
    @GetMapping("/getInfo/{id}")
    @ApiOperation(value = "查看详情")
    public AjaxResult getInfo(@PathVariable("id") Long id){
        return AjaxResult.success(registerService.getInfo(id));
    }

    /**
     * 入库
     */
    @GetMapping("/ware/{id}")
    @ApiOperation(value = "入库")
    public AjaxResult ware(@PathVariable("id") Long id){
        return toAjax(registerService.ware(id));
    }

    /**
     * 出库
     */
    @PostMapping("/out")
    @ApiOperation(value = "出库")
    public AjaxResult out(@RequestBody ColdQueryVo vo){
        return toAjax(registerService.out(vo));
    }


    /**
     * 根据运单后四位查询运单号
     */
    @PreAuthorize("@ss.hasPermi('dep:coldRegister:edit')")
    @GetMapping("/getWaybillCodeByFour")
    @ApiOperation(value = "根据运单后四位查询运单号")
    public AjaxResult getWaybillCodeByFour(@RequestParam("waybillCodeLastFour") String waybillCode){
        return AjaxResult.success(registerService.getWaybillCodeByFour(waybillCode));
    }
}
