package com.gzairports.sms.service;

import com.gzairports.sms.domain.HzAgentUser;
import com.gzairports.sms.domain.SmsSendLogPo;
import com.gzairports.sms.domain.dto.HzAgentUserDto;
import com.gzairports.sms.domain.dto.SmsReceiveUserDto;
import com.gzairports.sms.domain.dto.SmsSendParameter;
import com.gzairports.sms.domain.dto.SmsSendQuery;
import com.gzairports.sms.domain.vo.SmsReceiveUserVo;

import java.util.List;

/**
 * <AUTHOR>
 * @Description: 短信接口
 * @date 2022/6/1 16:49
 */
public interface ISmsSendService {
    /**
     * 通过模板的方式发送短信
     * @param param 消息发送参数
     */
    void sendSms(SmsSendParameter param);

    /**
     * 记录发送日志
     * @param smsSendLogPo
     * @return
     */
    int insertSmsSendLog(SmsSendLogPo smsSendLogPo);

    /**
     * 查询短信记录列表
     * @param query 查询参数
     * @return
     */
    List<SmsSendLogPo> selectSmsSendLogList(SmsSendQuery query);

    /**
     * 获取消息记录详情信息
     * @param smsSendLogId 消息发送记录id
     * @return
     */
    SmsSendLogPo selectSmsSendLogById(Long smsSendLogId);

    /**
     * 重新发送消息
     * @param po 消息对象
     * @return
     */
    String resend(SmsSendLogPo po);

    /**
     * 配置代理人需发送短信的用户
     * @param agentUserDtoList 代理人发送用户配置
     */
    void configAgentSendSmsUser(List<HzAgentUserDto> agentUserDtoList);

    /**
     * 查询发送短信配置用户信息
     * @param smsReceiveUserDto dto
     * @return vos
     */
    List<SmsReceiveUserVo> listSmsReceiveUser(SmsReceiveUserDto smsReceiveUserDto);

    /**
     * 查询财务余额监控代理人配置接收短信的用户
     * @param agentIds 代理人IDS
     * @return
     */
    List<HzAgentUser> selectFBMSmsSendAgentUser(List<Long> agentIds);

}
