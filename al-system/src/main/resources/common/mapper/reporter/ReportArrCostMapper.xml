<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.reporter.mapper.ReportArrCostMapper">
    <delete id="deleteBatch">
        delete from all_report_tally
        <where>
            <if test="arrReportIds != null and arrReportIds.size() > 0">
                and report_id in
                <foreach item="id" collection="arrReportIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </delete>
</mapper>