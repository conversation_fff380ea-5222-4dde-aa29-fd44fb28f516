package com.gzairports.web.controller.basedata;


import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.common.basedata.domain.BaseCarrier;
import com.gzairports.common.basedata.domain.query.CarrierQuery;
import com.gzairports.common.basedata.service.ICarrierService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 承运人管理Controller
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@RestController
@RequestMapping("/base/carrier")
@Api(tags = "承运人管理数据接口")
public class CarrierController extends BaseController {

    @Autowired
    private ICarrierService carrierService;
    /**
     * 查询承运人管理列表
     */
//    @PreAuthorize("@ss.hasPermi('base:carrier:carrierList')")
    @GetMapping("/carrierList")
    @ApiOperation(value = "查询承运人管理列表")
    public TableDataInfo carrierList(CarrierQuery query)
    {
        startPage();
        List<BaseCarrier> list = carrierService.selectCarrierList(query);
        return getDataTable(list);
    }

    /**
     * 导出承运人
     */
    @PreAuthorize("@ss.hasPermi('base:carrier:exportCarrier')")
    @Log(title = "导出承运人", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCarrier")
    @ApiOperation(value = "导出承运人")
    public void exportCarrier(HttpServletResponse response, CarrierQuery query)
    {
        List<BaseCarrier> list = carrierService.selectCarrierList(query);
        ExcelUtil<BaseCarrier> util = new ExcelUtil<BaseCarrier>(BaseCarrier.class);
        util.exportExcel(response, list, "承运人管理");
    }
}
