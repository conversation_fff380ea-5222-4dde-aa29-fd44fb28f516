package com.gzairports.common.basedata.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.basedata.domain.BaseStore;
import com.gzairports.common.basedata.domain.query.StoreQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 仓库管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Mapper
public interface StoreMapper extends BaseMapper<BaseStore> {

    /**
     * 查询仓库管理列表
     *
     * @param query 查询参数
     * @return 仓库管理列表
     */
    List<BaseStore> storeList(StoreQuery query);
}
