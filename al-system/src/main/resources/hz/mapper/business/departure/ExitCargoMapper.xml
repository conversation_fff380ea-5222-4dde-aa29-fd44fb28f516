<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.departure.mapper.ExitCargoMapper">
    <select id="selectExitCargoList" resultType="com.gzairports.hz.business.departure.domain.vo.DetailedVo">
        select start_time as createTime, exit_store_time as operTime, exit_quantity as quantity, exit_weight as weight, oper_name,
               exit_transfer_remark as remark
        from hz_dep_exit_cargo
        <where>
            <if test="waybillCode != null and waybillCode != ''">
                waybill_code = #{waybillCode}
            </if>
        </where>
    </select>
    <select id="selectListByQuery" resultType="com.gzairports.hz.business.departure.domain.vo.ExitCargoVo">
        select hdec.id, aaw.id as waybillId, hdec.waybill_code, hdec.status as exitStatus, aaw.flight_no1, aaw.flight_date1 as execDate,
               aaw.write_time, aaw.status as waybillStatus, aaw.agent_company, hdec.remark,
               hdec.exit_quantity, hdec.exit_weight
        from hz_dep_exit_cargo hdec
            left join all_air_waybill aaw on hdec.waybill_code = aaw.waybill_code
        <where>
            aaw.type = 'DEP'
            <if test="agentCode != null">
                AND aaw.agent_company = #{agentCode}
            </if>
            <if test="exitStatus != null and exitStatus != ''">
                AND aaw.status = #{exitStatus}
            </if>
            <if test="waybillCode != null and waybillCode != ''">
                AND hdec.waybill_code = #{waybillCode}
            </if>
            <if test="startWriteTime != null">
                and aaw.write_time <![CDATA[>=]]> #{startWriteTime}
            </if>
            <if test="endWriteTime != null">
                and aaw.write_time <![CDATA[<=]]> #{endWriteTime}
            </if>
        </where>
    </select>
    <select id="selectByExitId" resultType="com.gzairports.hz.business.departure.domain.vo.ExitCargoVo">
        select hdec.id, aaw.id as waybillId, hdec.waybill_code, hdec.status as exitStatus, aaw.flight_no1, aaw.flight_date1 as execDate,
               aaw.write_time, aaw.status, aaw.agent_code, hdec.exit_quantity, hdec.exit_weight, hdec.exit_transfer_remark, hdec.receive_url,hdec.remark
        from hz_dep_exit_cargo hdec left join all_air_waybill aaw on hdec.waybill_code = aaw.waybill_code where hdec.id = #{id}
    </select>
</mapper>