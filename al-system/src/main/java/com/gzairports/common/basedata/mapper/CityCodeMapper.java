package com.gzairports.common.basedata.mapper;


import com.gzairports.common.basedata.domain.BaseCityCode;
import com.gzairports.common.basedata.domain.query.CityCodeQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 城市代码Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Mapper
public interface CityCodeMapper {

    /**
     * 查询城市代码列表
     *
     * @param query 查询参数
     * @return 城市代码列表
     */
    List<BaseCityCode> selectCityCodeList(CityCodeQuery query);

    /**
     * 根据城市代码查询城市
     *
     * @param code 城市代码
     * @return 结果
     */
    BaseCityCode selectCityByCode(String code);

    /**
     * 新增城市代码
     *
     * @param baseCityCode 城市代码
     * @return 结果
     */
    int insertCityCode(BaseCityCode baseCityCode);

    /**
     * 修改城市代码
     *
     * @param baseCityCode 城市代码
     * @return 结果
     */
    int updateCityCode(BaseCityCode baseCityCode);

    /**
     * 删除城市代码
     *
     * @param ids 城市代码id集合
     * @return 结果
     */
    int delCityCode(Long[] ids);

    /**
     * 根据城市名称查询城市代码
     * @param cityName 名称
     * @return 城市信息
     */
    List<BaseCityCode> selectCityListByName(String cityName);
}
