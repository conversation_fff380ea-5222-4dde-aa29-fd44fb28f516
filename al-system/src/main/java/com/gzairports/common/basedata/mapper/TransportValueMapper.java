package com.gzairports.common.basedata.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.basedata.domain.BaseTransportValue;
import com.gzairports.common.basedata.domain.query.BaseTransportValueQuery;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;
import java.util.List;

/**
 * 运输价值维护Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Mapper
public interface TransportValueMapper extends BaseMapper<BaseTransportValue> {

    /**
     * 查询运输价值维护列表
     *
     * @param query 查询参数
     * @return 运输价值维护列表
     */
    List<BaseTransportValue> selectListByQuery(BaseTransportValueQuery query);

    /**
     * 根据货品代码和名称查询费率
     * @param deptId 部门id
     * @return 费率
     */
    List<BaseTransportValue> selectOneByQuery(Long deptId);
}
