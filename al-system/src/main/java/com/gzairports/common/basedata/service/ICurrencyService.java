package com.gzairports.common.basedata.service;

import com.gzairports.common.basedata.domain.BaseCurrency;
import com.gzairports.common.basedata.domain.query.CurrencyQuery;

import java.util.List;

/**
 * 币种管理Service接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
public interface ICurrencyService {

    /**
     * 查询币种列表
     *
     * @param query 查询参数
     * @return 币种列表
     */
    List<BaseCurrency> selectCurrencyList(CurrencyQuery query);

    /**
     * 导入币种
     *
     * @param currencies 币种列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    String importCurrency(List<BaseCurrency> currencies, boolean updateSupport);

    /**
     * 新增币种
     *
     * @param currency 币种
     * @return 结果
     */
    int addCurrency(BaseCurrency currency);

    /**
     * 修改币种
     *
     * @param currency 币种
     * @return 结果
     */
    int editCurrency(BaseCurrency currency);

    /**
     * 删除币种
     *
     * @param ids 币种id集合
     * @return 结果
     */
    int delCurrency(Long[] ids);
}
