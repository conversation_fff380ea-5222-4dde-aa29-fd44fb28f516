package com.gzairports.hz.business.departure.domain.query;

import lombok.Data;

import java.util.Date;
import java.util.Set;

/**
 * 航班配载查询条件
 * <AUTHOR>
 * @date 2024-06-28
 */
@Data
public class FlightLoadQuery {

    /** 航班日期 */
    private Date startExecDate;

    /** 航班日期 */
    private Date stopExecDate;

    /** 航班号 */
    private String flightNo;

    /** 状态 */
    private String state;

    /** 是否配载 */
    private Integer isPre;

    /** 可查询的航司二字码 */
    private Set<String> enabledAirlineCodeList;
}
