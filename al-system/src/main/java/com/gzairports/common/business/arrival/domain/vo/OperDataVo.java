package com.gzairports.common.business.arrival.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 运单修改操作区数据
 *
 * <AUTHOR>
 * @date 2024-08-05
 */
@Data
public class OperDataVo {

    /** 主键id */
    private Long id;

    /** 理货id */
    private Long tallyId;

    /** 申请修改id */
    private Long editId;

    /** 运单号 */
    private String waybillCode;

    /** 提货次数 */
    private Integer num;

    /** 理货件数 */
    private Integer tallyQuantity;

    /** 理货重量 */
    private BigDecimal tallyWeight;

    /** 提货件数 */
    private Integer pickedUpQuantity;

    /** 提货重量 */
    private BigDecimal pickedUpWeight;
}
