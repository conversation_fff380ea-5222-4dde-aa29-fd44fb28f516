package com.gzairports.wl.reporter.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-02-20
 */
@Data
public class CarrierSumQuery {

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /** 运单类型 0 主单 1 邮件单 */
    private Integer waybillType;

    /** 所属单位 */
    private Long deptId;
}
