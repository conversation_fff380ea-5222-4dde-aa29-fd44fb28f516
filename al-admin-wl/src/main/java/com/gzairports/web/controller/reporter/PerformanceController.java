package com.gzairports.web.controller.reporter;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.wl.reporter.domain.query.CargoAnalysisQuery;
import com.gzairports.wl.reporter.domain.query.CarrierSumQuery;
import com.gzairports.wl.reporter.service.IPerformanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2025-03-13
 **/
@RestController
@RequestMapping("/wl/performance")
@Api(tags = "经营业绩分析")
public class PerformanceController extends BaseController {

    @Autowired
    private IPerformanceService performanceService;

    /**
     * 年货量分析
     */
    @GetMapping("/yearCargo/{year}")
    @ApiOperation(value = "年货量分析")
    public AjaxResult yearCargo(@PathVariable("year") String year){
        return AjaxResult.success(performanceService.yearCargoAnalyse(year));
    }

    /**
     * 年利润分析
     */
    @GetMapping("/yearProfit/{year}")
    @ApiOperation(value = "年利润分析")
    public AjaxResult yearProfit(@PathVariable("year") String year){
        return AjaxResult.success(performanceService.yearProfit(year));
    }

    /**
     * 年月货量环比分析
     */
    @PostMapping("/cargoAnalysis")
    @ApiOperation(value = "年月货量环比分析")
    public AjaxResult cargoAnalysis(@RequestBody CargoAnalysisQuery query){
        return AjaxResult.success(performanceService.cargoAnalysis(query));
    }

    /**
     * 月货量分析
     */
    @GetMapping("/monthCargoAnalysisTable/{month}")
    @ApiOperation(value = "月货量分析")
    public AjaxResult monthCargoAnalysis(@PathVariable("month") String month){
        return AjaxResult.success(performanceService.monthCargoAnalysis(month));
    }

    /**
     * 航线货量利润
     */
    @GetMapping("/airlineProfit/{month}")
    @ApiOperation(value = "航线货量利润")
    public AjaxResult airlineProfit(@PathVariable("month") String month){
        return AjaxResult.success(performanceService.airlineProfit(month));
    }

    /**
     * 营业点员工绩效
     */
    @GetMapping("/staffPerformance/{month}")
    @ApiOperation(value = "营业点员工绩效")
    public AjaxResult staffPerformance(@PathVariable("month") String month){
        return AjaxResult.success(performanceService.staffPerformance(month));
    }

    /**
     * 每日承运人汇总
     */
    @PostMapping("/carrierDaySum")
    @ApiOperation(value = "每日承运人汇总")
    public AjaxResult carrierDaySum(@RequestBody CarrierSumQuery query){
        return AjaxResult.success(performanceService.carrierDaySum(query));
    }

    /**
     * 每日客户汇总
     */
    @PostMapping("/customDaySum")
    @ApiOperation(value = "每日客户汇总")
    public AjaxResult customDaySum(@RequestBody CarrierSumQuery query){
        return AjaxResult.success(performanceService.customDaySum(query));
    }
}
