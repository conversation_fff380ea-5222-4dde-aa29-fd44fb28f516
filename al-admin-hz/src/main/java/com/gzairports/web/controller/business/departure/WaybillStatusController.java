package com.gzairports.web.controller.business.departure;

import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.common.log.service.IWaybillLogService;
import com.gzairports.hz.business.departure.domain.HzCollectWeight;
import com.gzairports.hz.business.departure.domain.query.WaybillInfoQuery;
import com.gzairports.hz.business.departure.domain.query.WaybillStatusQuery;
import com.gzairports.hz.business.departure.domain.vo.ChargeStatusVo;
import com.gzairports.hz.business.departure.domain.vo.DetailedVo;
import com.gzairports.hz.business.departure.domain.vo.WaybillInfoVo;
import com.gzairports.hz.business.departure.domain.vo.WaybillStatusVo;
import com.gzairports.hz.business.departure.service.IWaybillStatusService;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 运单状态Controller
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@RestController
@RequestMapping("/dep/waybillStatus")
@Api(tags = "运单状态")
public class WaybillStatusController extends BaseController {

    @Autowired
    private IWaybillStatusService statusService;

    @Autowired
    private IWaybillLogService logService;

    /**
     * 运单状态列表查询
     */
//    @PreAuthorize("@ss.hasPermi('dep:waybillStatus:list')")
    @GetMapping("/list")
    @ApiOperation(value = "运单状态列表查询")
    public TableDataInfo list(WaybillStatusQuery query){
        startPage();
        List<WaybillStatusVo> list = statusService.selectList(query);
        return getDataTable(list);
    }

    /**
     * 运单作废
     */
    @PreAuthorize("@ss.hasPermi('dep:waybillStatus:waybillCancel')")
    @GetMapping("/waybillCancel/{id}")
    @ApiOperation(value = "运单作废")
    public AjaxResult waybillCancel(@PathVariable("id") Long id)
    {
        return toAjax(statusService.waybillCancel(id));
    }

    /**
     * 运单删除
     */
    @PreAuthorize("@ss.hasPermi('dep:waybillStatus:waybillDel')")
    @GetMapping("/waybillDel/{id}")
    @ApiOperation(value = "运单删除")
    public AjaxResult waybillDel(@PathVariable("id") Long id)
    {
        return toAjax(statusService.waybillDel(id));
    }

    /**
     * 运单明细查询
     */
    @PreAuthorize("@ss.hasPermi('dep:waybillStatus:getInfo')")
    @PostMapping("/getInfo")
    @ApiOperation(value = "运单明细查询")
    public AjaxResult getInfo(@RequestBody WaybillInfoQuery query){
        WaybillInfoVo vo = statusService.getInfo(query);
        return AjaxResult.success(vo);
    }

    /**
     * 过磅记录
     */
    @PreAuthorize("@ss.hasPermi('dep:waybillStatus:weightInfo')")
    @GetMapping("/weightInfo/{id}")
    @ApiOperation(value = "过磅记录")
    public AjaxResult weightInfo(@PathVariable("id") Long id){
        List<HzCollectWeight> weightInfo = statusService.weightInfo(id);
        return AjaxResult.success(weightInfo);
    }


    /**
     * 运单状态数据查询
     */
    @PreAuthorize("@ss.hasPermi('dep:waybillStatus:status')")
    @PostMapping("/status")
    @ApiOperation(value = "运单状态数据查询")
    public AjaxResult status(@RequestBody WaybillInfoQuery query){
        List<DetailedVo> detailedVos = statusService.getStatusList(query);
        return AjaxResult.success(detailedVos);
    }

    /**
     * 运单状态收费数据查询
     */
    @PreAuthorize("@ss.hasPermi('dep:waybillStatus:chargeStatus')")
    @PostMapping("/chargeStatus")
    @ApiOperation(value = "运单状态收费数据查询")
    public AjaxResult chargeStatus(@RequestBody WaybillInfoQuery query){
        ChargeStatusVo vo = statusService.chargeStatus(query);
        return AjaxResult.success(vo);
    }

    /**
     * 查询日志列表
     */
    @PreAuthorize("@ss.hasPermi('dep:waybillStatus:logList')")
    @GetMapping("/listForLog")
    @ApiOperation(value = "查询日志列表")
    public TableDataInfo list(String waybillCode){
        startPage();
        List<WaybillLog> list = logService.selectList(waybillCode,"DEP");
        return getDataTable(list);
    }

    /**
     * 请求和返回数据
     */
    @PreAuthorize("@ss.hasPermi('dep:waybillStatus:logInfo')")
    @GetMapping("/info/{id}")
    @ApiOperation(value = "请求和返回数据")
    public AjaxResult info(@PathVariable("id") Long id){
        WaybillLog log = logService.info(id);
        return AjaxResult.success(log);
    }


}
