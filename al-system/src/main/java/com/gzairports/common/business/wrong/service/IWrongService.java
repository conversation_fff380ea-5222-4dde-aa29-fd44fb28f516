package com.gzairports.common.business.wrong.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.business.wrong.domain.Wrong;
import com.gzairports.wl.departure.domain.query.WrongQuery;

import java.util.List;

/**
 * 不正常流程Service接口
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
public interface IWrongService extends IService<Wrong> {

    /**
     * 查询不正常货运列表
     * @param query 查询参数
     * @return 不正常货邮列表
     */
    List<Wrong> selectList(WrongQuery query);

    /**
     * 新增不正常货邮
     * @param wrong 新增数据
     * @return 结果
     */
    int add(Wrong wrong);

    /**
     * 获取不正常货邮详情
     * @param id 不正常货邮id
     * @return 不正常货邮详情
     */
    Wrong getInfo(Long id);

    /**
     * 选择处理方式
     * @param wrong 处理方式参数
     * @return 结果
     */
    int method(Wrong wrong);

    /**
     * 处理
     * @param wrong 处理数据
     * @return 结果
     */
    int handle(Wrong wrong);

    /**
     * 修改
     * @param wrong 修改数据
     * @return 结果
     */
    int update(Wrong wrong);

    /**
     * 根据运单号查询代理人等数据
     * @return 结果
     */
    Wrong selectAgent(String waybillCode);

    List<String> getWaybillCodeByFour(String waybillCode,Long deptId);

    /**
     * 货站修改
     * @param wrong 修改数据
     * @return 结果
     */
    int hzUpdate(Wrong wrong);

    int isExitPull(String waybillCode);


    int isExitSettle(String waybillCode);
}
