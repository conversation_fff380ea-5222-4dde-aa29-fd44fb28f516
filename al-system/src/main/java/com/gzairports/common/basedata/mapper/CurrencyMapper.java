package com.gzairports.common.basedata.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.basedata.domain.BaseCurrency;
import com.gzairports.common.basedata.domain.query.CurrencyQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 币种管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Mapper
public interface CurrencyMapper extends BaseMapper<BaseCurrency> {

    /**
     * 查询币种管理列表
     *
     * @param query 查询参数
     * @return 币种管理列表
     */
    List<BaseCurrency> selectCurrencyList(CurrencyQuery query);


    /**
     * 根据币种代码查询币种
     *
     * @param currency 币种代码
     * @return 结果
     */
    BaseCurrency selectCurrencyByCode(String currency);

    /**
     * 新增币种
     *
     * @param currency 币种
     * @return 结果
     */
    int insertCurrency(BaseCurrency currency);

    /**
     * 修改币种
     *
     * @param currency 币种
     * @return 结果
     */
    int updateCurrency(BaseCurrency currency);

    /**
     * 删除币种
     *
     * @param ids 币种id集合
     * @return 结果
     */
    int delCurrency(Long[] ids);
}
