package com.gzairports.common.business.departure.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.annotation.DataSource;
import com.gzairports.common.business.departure.domain.vo.TImpDlv;
import com.gzairports.common.business.departure.mapper.ImlFlvMapper;
import com.gzairports.common.business.departure.service.IImlDlvService;
import com.gzairports.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 运单日志Service接口
 *
 * <AUTHOR>
 * @date 2023-09-25
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class ImlDlvServiceImpl extends ServiceImpl<ImlFlvMapper, TImpDlv> implements IImlDlvService {
    @Autowired
    private ImlFlvMapper imlFlvMapper;

    @Override
    public Map<String, List<TImpDlv>> listByTime(Date startTime, Date endTime) {
        Map<String, List<TImpDlv>> rMap = new HashMap<>();
        LambdaQueryWrapper<TImpDlv> query = Wrappers.lambdaQuery(TImpDlv.class).ge(TImpDlv::getPickopetime, startTime).le(TImpDlv::getPickopetime, endTime);
        List<TImpDlv> awblogs = imlFlvMapper.selectList(query);
        for (TImpDlv awblog : awblogs) {
            List<TImpDlv> list = rMap.get(awblog.getBillid());
            if (list == null) {
                list = new ArrayList<>();
                rMap.put(awblog.getBillid(), list);
            }
            list.add(awblog);
        }
        return rMap;
    }

    @Override
    public Map<String, List<TImpDlv>> listByBillid(Set<String> stocknoSet) {
        Map<String, List<TImpDlv>> rMap = new HashMap<>();
        if(null == stocknoSet ||stocknoSet.isEmpty()){
            return rMap;
        }
        LambdaQueryWrapper<TImpDlv> query = Wrappers.lambdaQuery(TImpDlv.class).in(TImpDlv::getBillid,stocknoSet);
        List<TImpDlv> awblogs = imlFlvMapper.selectList(query);
        for (TImpDlv awblog : awblogs) {
            List<TImpDlv> list = rMap.get(awblog.getBillid());
            if (list == null) {
                list = new ArrayList<>();
                rMap.put(awblog.getBillid(), list);
            }
            list.add(awblog);
        }
        return rMap;
    }
}
