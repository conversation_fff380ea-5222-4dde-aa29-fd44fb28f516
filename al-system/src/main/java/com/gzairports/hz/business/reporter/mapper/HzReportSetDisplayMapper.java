package com.gzairports.hz.business.reporter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.hz.business.reporter.domain.HzReportSet;
import com.gzairports.hz.business.reporter.domain.HzReportSetDisplay;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: lan
 * @create: 2025-03-05 17:25
 **/

@Mapper
public interface HzReportSetDisplayMapper extends BaseMapper<HzReportSetDisplay> {

    /**
     * 根据报表设置id查询字段
     * @param setId 报表设置字段
     * @return 展示字段
     */
    List<HzReportSetDisplay> selectFieldList(Long setId);
}
