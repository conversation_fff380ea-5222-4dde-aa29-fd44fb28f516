package com.gzairports.wl.charge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.*;
import com.gzairports.common.basedata.mapper.*;
import com.gzairports.common.basedata.service.impl.WeightServiceImpl;
import com.gzairports.common.exception.ServiceException;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.bean.BeanValidators;
import com.gzairports.wl.charge.domain.FreightRateAir;
import com.gzairports.wl.charge.domain.FreightRateAirItem;
import com.gzairports.wl.charge.domain.FreightRateCustom;
import com.gzairports.wl.charge.domain.query.FreightRateAirQuery;
import com.gzairports.wl.charge.domain.vo.FreightRateAirItemVO;
import com.gzairports.wl.charge.domain.vo.FreightRateAirVO;
import com.gzairports.wl.charge.domain.vo.RateItemImportVo;
import com.gzairports.wl.charge.mapper.FreightRateAirItemMapper;
import com.gzairports.wl.charge.mapper.FreightRateAirMapper;
import com.gzairports.wl.charge.service.FreightRateAirService;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.Validator;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 航司运价service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@Service
public class FreightRateAirServiceImpl extends ServiceImpl<FreightRateAirMapper, FreightRateAir> implements FreightRateAirService {

    private static final Logger log = LoggerFactory.getLogger(FreightRateAirServiceImpl.class);

    @Autowired
    private FreightRateAirMapper airMapper;

    @Autowired
    private FreightRateAirItemMapper airItemMapper;

    @Autowired
    private BillTypeMapper billTypeMapper;

    @Autowired
    private Validator validator;

    @Autowired
    private CargoCodeMapper cargoCodeMapper;

    @Autowired
    private CargoCategoryMapper cargoCategoryMapper;

    @Autowired
    private AirportCodeMapper airportCodeMapper;


    /**
     * 查询航司运价列表
     *
     * @param query 查询参数
     * @return 航司运价列表
     */
    @Override
    public List<FreightRateAirVO> selectList(FreightRateAirQuery query) {
        //查询票证类型并存入集合,仅查询一次
        if (DOCUMENTMAP.isEmpty()) {
            selectTypeList();
        }
        query.setDeptId(SecurityUtils.getHighParentId());
        List<FreightRateAirVO> freightRateAirVOS = airMapper.selectListByQuery(query);
        for (FreightRateAirVO freightRateAirVO : freightRateAirVOS) {
            freightRateAirVO.setApplicable(DOCUMENTMAP.get(freightRateAirVO.getApplicableDocuments()));
            freightRateAirVO.setAutomatic(freightRateAirVO.getAutomaticFromLow() == 0 ? "否":"是");
//            freightRateAirVO.setType(freightRateAirVO.getRateType() == 0 ? "普通运价":"特货运价");
            freightRateAirVO.setRounding(ROUNDRULE.get(freightRateAirVO.getRoundingMethod()));
        }

        return freightRateAirVOS;
    }

    /**
     * 查询航司运价想
     *
     * @param id 航司运价主键
     * @return 航司运价
     */
    @Override
    public FreightRateAirVO selectRateAirById(Long id) {
        FreightRateAirVO vo = new FreightRateAirVO();
        FreightRateAir freightRateAir = airMapper.selectById(id);
        BeanUtils.copyProperties(freightRateAir,vo);
        return vo;
    }

    /**
     * 新增航司运价
     *
     * @param air 航司运价
     * @return 结果
     */
    @Override
    public Long insertFreightRateAir(FreightRateAir air) {
        //航司运价：航司+适用单证类型+生效时间
        List<FreightRateAir> list = airMapper.selectList(new QueryWrapper<FreightRateAir>()
                .eq("carrier_code", air.getCarrierCode())
                .eq("dept_id",SecurityUtils.getHighParentId())
                .eq("is_del",0));
        for (FreightRateAir rateAir : list) {
//            if (rateAir.getEffectiveDate().before(air.getEffectiveDate()) || rateAir.getExpirationDate().after(air.getExpirationDate()))
            if((rateAir.getEffectiveDate().before(air.getEffectiveDate()) &&
                    rateAir.getExpirationDate().after(air.getEffectiveDate()))
                    || (rateAir.getEffectiveDate().before(air.getExpirationDate()) &&
                    rateAir.getExpirationDate().after(air.getExpirationDate())))
            {
                throw new CustomException("相同航司的运价生效和截止日期不能重叠");
            }
        }
        //生效时间不能晚于截止时间
        if (air.getEffectiveDate().after(air.getExpirationDate())){
            throw new CustomException("生效时间不能晚于截止时间");
        }

        air.setCreateBy(SecurityUtils.getUsername());
        air.setCreateTime(DateUtils.getNowDate());
        air.setDeptId(SecurityUtils.getHighParentId());
        airMapper.insert(air);
        return air.getId();
    }

    /**
     * 修改航司运价
     *
     * @param air 航司运价
     * @return 结果
     */
    @Override
    public int updateFreightRateAir(FreightRateAir air) {
        if (air.getEffectiveDate().after(air.getExpirationDate())){
            throw new CustomException("生效时间不能晚于截止时间");
        }
        return airMapper.updateById(air);
    }

    /**
     * 查看运价条目
     *
     * @param id 航司运价id
     * @return 航司运价条目集合
     */
    @Override
    public List<FreightRateAirItemVO> selectRateItemList(Long id) {
        return airItemMapper.selectRateItemList(id);
    }

    /**
     * 删除航司运价
     *
     * @param id 航司运价主键
     * @return 结果
     */
    @Override
    public int deleteFreightRateAir(Long id) {
        FreightRateAir rateAir = airMapper.selectById(id);
        Date date = new Date();
        if (rateAir.getEffectiveDate().before(date) && rateAir.getExpirationDate().after(date)){
            throw new CustomException("在有效期内的运价不能删除");
        }
        return airMapper.removeRateAir(id);
    }

    /**
     * 导入航司运价
     *
     * @param freightRateAirVOS 导入
     * @return 结果
     */
    @Override
    public String importAirlines(List<FreightRateAirVO> freightRateAirVOS, boolean updateSupport) {
        //查询票证类型并存入集合,仅查询一次
        if (DOCUMENTMAP.isEmpty()) {
            selectTypeList();
        }
        if (StringUtils.isNull(freightRateAirVOS) || freightRateAirVOS.size() == 0)
        {
            throw new ServiceException("导入航司运价数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        FreightRateAir freightRateAir = new FreightRateAir();
        for (FreightRateAirVO freightRateAirVO : freightRateAirVOS) {
            try {
                BeanUtils.copyProperties(freightRateAirVO,freightRateAir);
                freightRateAir.setRateType("普通运价".equals(freightRateAirVO.getType()) ? 0 : 1);
                freightRateAir.setAutomaticFromLow("否".equals(freightRateAirVO.getAutomatic()) ? 0 : 1);
                //传进来的是显示的值 要存的是数据库的值(map中通过值找键)
                freightRateAir.setApplicableDocuments(getKey(DOCUMENTMAP, freightRateAirVO.getApplicable()));
                freightRateAir.setRoundingMethod(getKey(ROUNDRULE, freightRateAirVO.getRounding()));
                // 验证是否存在相同的航司运价
                //航司运价：航司+适用单证类型+生效时间
                FreightRateAir freightRateAirNew= airMapper.selectOne(new QueryWrapper<FreightRateAir>()
                        .eq("carrier_code", freightRateAir.getCarrierCode())
                        .eq("applicable_documents", freightRateAir.getApplicableDocuments())
                        .eq("effective_date", freightRateAir.getEffectiveDate())
                        .eq("expiration_date", freightRateAir.getExpirationDate())
                        .eq("dept_id",SecurityUtils.getHighParentId())
                        .eq("is_del",0));
                if (StringUtils.isNull(freightRateAirNew)) {
                    BeanValidators.validateWithException(validator, freightRateAir);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    freightRateAir.setCreateBy(loginUser.getUsername());
                    freightRateAir.setCreateTime(DateUtils.getNowDate());
                    airMapper.insert(freightRateAir);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、代码 " + freightRateAir.getRateName() + " 导入成功");
                }
                else if (updateSupport) {
                    BeanValidators.validateWithException(validator, freightRateAir);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    freightRateAir.setId(freightRateAirNew.getId());
                    freightRateAir.setUpdateBy(loginUser.getUsername());
                    freightRateAir.setUpdateTime(DateUtils.getNowDate());
                    airMapper.updateById(freightRateAir);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、代码 " + freightRateAir.getRateName() + " 更新成功");
                }
                else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、代码 " + freightRateAir.getRateName() + " 已存在");
                }
            }catch (Exception e){
                failureNum++;
                String msg = "<br/>" + failureNum + "、代码 " + freightRateAir.getRateName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 导入航司运价条目
     * @param list 文件数据
     * @param rateId 导入所需数据
     * @param updateSupport 导入所需数据
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long importItem(List<FreightRateAirItem> list, Long rateId, boolean updateSupport) {
        if (StringUtils.isNull(list) || list.size() == 0)
        {
            throw new ServiceException("导入航司运价条目数据不能为空！");
        }
        for (int i = 0; i < list.size(); i++) {
            int j = i + 1;
            if (list.get(i).getDepartureCity() == null){
                throw new ServiceException("第" + j + "条数据导入失败，始发城市为空");
            }
            if (list.get(i).getDestinationCity() == null){
                throw new ServiceException("第" + j + "条数据导入失败，目的城市为空");
            }
            if (list.get(i).getMinimumFreight() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算M为空");
            }
            if (list.get(i).getRateWeightRange5() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算5为空");
            }
            if (list.get(i).getRateWeightRange10() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算10为空");
            }
            if (list.get(i).getBaseRateN() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算N为空");
            }
            if (list.get(i).getRateWeightRange45() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算45为空");
            }
            if (list.get(i).getRateWeightRange100() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算100为空");
            }
            if (list.get(i).getRateWeightRange300() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算300为空");
            }
            if (list.get(i).getRateWeightRange500() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算500为空");
            }
            if (list.get(i).getRateWeightRange1000() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算1000为空");
            }
//            List<BaseAirportCode> desPort = airportCodeMapper.selectCityByName(list.get(i).getDestinationCity());
//            for (BaseAirportCode baseAirportCode : desPort) {

            BaseAirportCode desPort = airportCodeMapper.selectOne(new QueryWrapper<BaseAirportCode>()
                    .eq("chinese_name", list.get(i).getDestinationCity()));
            if (desPort == null){
                throw new ServiceException("第" + j + "条数据导入失败、目的城市 " + list.get(i).getDestinationCity() + " 基础数据机场代码里未维护");
            }

                FreightRateAirItem rateAirItem1 = airItemMapper.selectOne(new QueryWrapper<FreightRateAirItem>()
                        .eq("clause_name", list.get(i).getClauseName())
                        .eq("rate_id",rateId));
                if (rateAirItem1 != null){
                    throw new ServiceException("第" + j + "条数据导入失败、条目名称 " + list.get(i).getClauseName() + " 已存在");
                }
                FreightRateAirItem airItem = new FreightRateAirItem();
                BeanUtils.copyProperties(list.get(i),airItem);
                LoginUser loginUser = SecurityUtils.getLoginUser();
                airItem.setRateId(rateId);
                airItem.setDepartureCity("KWE");
                airItem.setDestinationCity(desPort.getCode());
                airItem.setCreateBy(loginUser.getUsername());
                airItem.setCreateTime(DateUtils.getNowDate());
                airItem.setDeptId(SecurityUtils.getHighParentId());
                if(list.get(i).getCargoCode() != null && !"".equals(list.get(i).getCargoCode())){
                    String[] split = list.get(i).getCargoCode().split(";");
                    List<String> cargoCategoryList = new ArrayList<>();
                    List<String> productCodeList = new ArrayList<>();
                    for(String s:split){
                        BaseCargoCode cargoCode = cargoCodeMapper.selectCodeByName(s);
                        if(cargoCode != null){
                            //表格里面的货品代码填的是具体货品
                            BaseCargoCategory code = cargoCategoryMapper.selectOne(new QueryWrapper<BaseCargoCategory>()
                                    .eq("code", cargoCode.getCategoryCode()));
                            cargoCategoryList.add(code.getCode());
                            productCodeList.add(cargoCode.getCode());
                        }else{
                            List<BaseCargoCategory> code = cargoCategoryMapper.selectList(new QueryWrapper<BaseCargoCategory>()
                                    .eq("chinese_name", s));
                            if(code != null && code.size() > 0){
                                //表格里面填的是大类
                                List<String> cargoCategoryCollect = code.stream().map(BaseCargoCategory::getCode).collect(Collectors.toList());
                                cargoCategoryList.addAll(cargoCategoryCollect);
                            }else{
                                throw new ServiceException("导入失败，第"+ j +"条数据识别失败，货物种类 "+ s +" 不存在，请修改后重新导入");
                            }
                        }
                    }
                    List<String> collect = cargoCategoryList.stream().distinct().collect(Collectors.toList());
                    List<String> collect2 = productCodeList.stream().distinct().collect(Collectors.toList());
                    airItem.setCargoCategory(String.join(",",collect));
                    airItem.setCargoCode(String.join(",",collect2));
                }
                airItemMapper.insert(airItem);


//        }
//                if (StringUtils.isEmpty(list.get(i).getCargoCode())){
//                    FreightRateAirItem airItem = new FreightRateAirItem();
//                    BeanUtils.copyProperties(list.get(i),airItem);
//                    LoginUser loginUser = SecurityUtils.getLoginUser();
//                    airItem.setRateId(rateId);
//                    airItem.setDepartureCity("KWE");
//                    airItem.setDestinationCity(baseAirportCode.getCode());
//                    airItem.setCreateBy(loginUser.getUsername());
//                    airItem.setCreateTime(DateUtils.getNowDate());
//                    airItem.setDeptId(SecurityUtils.getHighParentId());
//                    airItemMapper.insert(airItem);
//                }else {
//                    String[] split = list.get(i).getCargoCode().split(";");
//                    for (String s : split) {
//                        List<BaseCargoCategory> categories = cargoCategoryMapper.selectList(new QueryWrapper<BaseCargoCategory>().eq("chinese_name", s));
//                        if (!CollectionUtils.isEmpty(categories)){
//                            for (BaseCargoCategory category : categories) {
//                                FreightRateAirItem airItem = new FreightRateAirItem();
//                                BeanUtils.copyProperties(list.get(i),airItem);
//                                LoginUser loginUser = SecurityUtils.getLoginUser();
//                                airItem.setRateId(rateId);
//                                airItem.setDepartureCity("KWE");
//                                airItem.setCargoCategory(category.getCode());
//                                airItem.setCargoCode(null);
//                                airItem.setDestinationCity(baseAirportCode.getCode());
//                                airItem.setCreateBy(loginUser.getUsername());
//                                airItem.setCreateTime(DateUtils.getNowDate());
//                                airItem.setDeptId(SecurityUtils.getHighParentId());
//                                airItemMapper.insert(airItem);
//                            }
//                        }else {
//                            List<String> codes = new ArrayList<>();
//                            List<String> categoryCodes = new ArrayList<>();
//                            BaseCargoCode cargoCode = cargoCodeMapper.selectCodeByName(s);
//                            if (cargoCode == null){
//                                throw new ServiceException("导入失败，第"+ j +"条数据识别失败，货物种类"+ s +" 不存在，请修改后重新导入");
//                            }else {
//                                codes.add(cargoCode.getCode());
//                                categoryCodes.add(cargoCode.getCategoryCode());
//                            }
//                            FreightRateAirItem airItem = new FreightRateAirItem();
//                            BeanUtils.copyProperties(list.get(i),airItem);
//                            LoginUser loginUser = SecurityUtils.getLoginUser();
//                            airItem.setRateId(rateId);
//                            airItem.setDepartureCity("KWE");
//                            airItem.setCargoCode(String.join(",",codes));
//                            airItem.setCargoCategory(String.join(",",categoryCodes));
//                            airItem.setDestinationCity(baseAirportCode.getCode());
//                            airItem.setCreateBy(loginUser.getUsername());
//                            airItem.setCreateTime(DateUtils.getNowDate());
//                            airItem.setDeptId(SecurityUtils.getHighParentId());
//                            airItemMapper.insert(airItem);
//                        }
//                    }
//                }

        }
        return rateId;
    }

    @Override
    public int deleteAll(Long[] ids) {
        if (ids == null){
            return 1;
        }
        Date date = new Date();
        for (Long id : ids) {
            FreightRateAir rateAir = airMapper.selectById(id);
            if (rateAir.getEffectiveDate().before(date) && rateAir.getExpirationDate().after(date)){
                throw new CustomException("在有效期内的运价不能删除");
            }
        }
        return airMapper.deleteBatchIds(Arrays.asList(ids));
    }

    /**
     * 查询所有票证类型
     *
     * @return 结果
     */
    public void selectTypeList() {
        List<BaseBillType> baseBillTypes = billTypeMapper.selectTypeList();
        for (BaseBillType baseBillType:baseBillTypes) {
            String code = baseBillType.getCode();
            String name = baseBillType.getName();
            DOCUMENTMAP.put(code,name);
        }
    }

    private static final Map<String,String> DOCUMENTMAP = new HashMap<>();

    private static final Map<Integer,String> ROUNDRULE = new HashMap<>();
    static {
        ROUNDRULE.put(0,"实际");
        ROUNDRULE.put(1,"四舍五入至个位");
        ROUNDRULE.put(2,"四舍五入至0.1");
        ROUNDRULE.put(3,"四舍五入至0.01");
        ROUNDRULE.put(4,"四舍五入至0.001");
        ROUNDRULE.put(5,"直接进位到0.1");
        ROUNDRULE.put(6,"直接舍位到0.1");
        ROUNDRULE.put(7,"直接进位到个位");
        ROUNDRULE.put(8,"直接舍位到个位");
        ROUNDRULE.put(9,"直接进位到0.5");
        ROUNDRULE.put(10,"直接舍位到0.5");
    }

    /** 通过值找键 */
    public <K, V> K getKey(Map<K, V> map, V value) {
        for (Map.Entry<K, V> entry : map.entrySet()) {
            if (entry.getValue().equals(value)) {
                return entry.getKey();
            }
        }
        return null;
    }
}
