package com.gzairports.common.bankUtils;

import org.apache.http.*;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLContexts;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.KeyManagementException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;


public class HttpClient{
    private String url;
    private HttpPost httpPost;

    public HttpClient(String url) {
        this.url = url;
        httpPost = new HttpPost(url);
    }

    protected static int maxTotal =200;
    protected static int maxPerRoute=100;
    protected static int socketTimeOut=35000;
    protected static int connTimeOut=30000;
    protected static String characterEncoding = "UTF-8";


    public String postFormRequest(Map<String, String> formParams) throws HttpException {
        CloseableHttpClient httpClient = null;
        try {
            httpClient = getSslHttpClient();
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(socketTimeOut)
                    .setConnectTimeout(connTimeOut).build();
            HttpPost httpPost = new HttpPost(url);
            httpPost.setConfig(requestConfig);
            List<NameValuePair> fPs = new ArrayList<NameValuePair>();
            Iterator<Entry<String, String>> iterator = formParams.entrySet().iterator();
            Entry<String, String> entry = null;
            while (iterator.hasNext()) {
                entry = (Entry<String, String>) iterator.next();
                fPs.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
            }
            UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(fPs, characterEncoding);
            httpPost.setEntity(urlEncodedFormEntity);
            HttpResponse httpResponse = httpClient.execute(httpPost);
            HttpEntity httpEntity = httpResponse.getEntity();
            if (httpEntity != null) {
                return EntityUtils.toString(httpEntity, characterEncoding);
            }


        } catch (Exception e) {
            closeHttpClient(httpClient);
            e.printStackTrace();
        } finally {
            closeHttpClient(httpClient);
        }
        return null;
    }

//    public String postFormRequest(Map<String, String> formParams) throws HttpException {
//        CloseableHttpClient httpClient = null;
//        try {
//           System.out.println("开始创建SSL HttpClient...");
//            httpClient = getSslHttpClient();
//           System.out.println("SSL HttpClient 创建成功。");
//
//            RequestConfig requestConfig = RequestConfig.custom()
//                    .setSocketTimeout(socketTimeOut)
//                    .setConnectTimeout(connTimeOut).build();
//           System.out.println("设置请求配置：socket超时=" + socketTimeOut + ", 连接超时=" + connTimeOut);
//
//            HttpPost httpPost = new HttpPost(url);
//            httpPost.setConfig(requestConfig);
//           System.out.println("准备发送POST请求到：" + url);
//
//            List<NameValuePair> fPs = new ArrayList<NameValuePair>();
//            Iterator<Entry<String, String>> iterator = formParams.entrySet().iterator();
//            Entry<String, String> entry = null;
//            while (iterator.hasNext()) {
//                entry = iterator.next();
//                fPs.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
//                System.out.println("添加表单参数：" + entry.getKey() + "=" + entry.getValue());
//            }
//
//            UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(fPs, characterEncoding);
//            httpPost.setEntity(urlEncodedFormEntity);
//           System.out.println("表单实体设置完成，准备执行请求...");
//
//            HttpResponse httpResponse = httpClient.execute(httpPost);
//           System.out.println("请求已执行，状态码为：" + httpResponse.getStatusLine().getStatusCode());
//
//            HttpEntity httpEntity = httpResponse.getEntity();
//            if (httpEntity != null) {
//                String responseString = EntityUtils.toString(httpEntity, characterEncoding);
//               System.out.println("响应内容：" + responseString);
//                return responseString;
//            }
//        } catch (Exception e) {
//            System.out.println("请求过程中发生异常: " + e.getMessage());
//            e.printStackTrace();
//        } finally {
//            closeHttpClient(httpClient);
//           System.out.println("HttpClient已关闭");
//        }
//        return null;
//    }
    public CloseableHttpResponse getCloseableHttpResponse(CloseableHttpClient client) throws HttpException {
        CloseableHttpResponse response = null;
        try {
            response = client.execute(httpPost);
            response.getEntity().getContentLength();
            responseCheck(response, httpPost);
           
        } catch (Exception e) {
        	e.printStackTrace();
        }
        return response;
    }

    public String doPost(String reqStr) throws HttpException {
        CloseableHttpClient httpClient = getHttpClient(reqStr);
        CloseableHttpResponse response = getCloseableHttpResponse(httpClient);
        String resp = null;
        try {
             HttpEntity httpEntity = response.getEntity();
             if (httpEntity != null) {
                 resp = EntityUtils.toString(httpEntity, characterEncoding);
             } 
        } catch (ParseException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
            try {
                if (response != null)
                    response.close();
                if (httpClient != null)
                    httpClient.close();
            } catch (IOException e) {
            	e.printStackTrace(); 
            }
        }
        return resp;
    }
    
    public CloseableHttpClient getHttpClient(String reqStr) throws HttpException {
        CloseableHttpClient httpClient = getHttpClient();
        StringEntity se = null;
        se = new StringEntity(reqStr, characterEncoding);
        se.setContentType("application/json");
        httpPost.setEntity(se);
        RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(socketTimeOut)
                .setConnectTimeout(connTimeOut).build();// 设置请求和传输超时时间
        httpPost.setConfig(requestConfig);
        return httpClient;
    }
    
    private CloseableHttpClient getHttpClient() {
        PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
        cm.setMaxTotal(maxTotal);
        cm.setDefaultMaxPerRoute(maxPerRoute);
        HttpClientBuilder httpBulder = HttpClients.custom();
        httpBulder.setConnectionManager(cm);
        CloseableHttpClient httpClient = httpBulder.build();
        return httpClient;
    }

    private static CloseableHttpClient getSslHttpClient() {
        SSLContext sslContext = SSLContexts.createDefault();
        try {
            sslContext.init(null, new TrustManager[] { new X509TrustManager() {
                @Override
                public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                }

                @Override
                public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                }

                @Override
                public X509Certificate[] getAcceptedIssuers() {
                    return null;
                }
            } }, null);
        } catch (KeyManagementException e) {
        }
        SSLConnectionSocketFactory sSLConnectionSocketFactory = new SSLConnectionSocketFactory(sslContext,
                SSLConnectionSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER);

        return HttpClients.custom().setSSLSocketFactory(sSLConnectionSocketFactory).build();
    }

    private void responseCheck(HttpResponse response, HttpPost httppost) throws Exception {
        int statusCode = response.getStatusLine().getStatusCode();
        if (statusCode != 200) {
            httppost.abort();
            throw new Exception("statusCode:" + statusCode);
        }
    }
    
    private void closeHttpClient(CloseableHttpClient client) {
        if (client != null) {
            try {
                client.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
