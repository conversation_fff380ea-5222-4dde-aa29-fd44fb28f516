package com.gzairports.wl.ticket.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 单证管理日志返回对象
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@Data
public class TicketOprLogVO {

    /** 日志主键 */
    private Long operId;

    /** 操作人员 */
    private String operName;

    /** 操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operTime;

    /** 业务类型（0其它 1新增 2修改 3删除） */
    private Integer businessType;

    /** 操作状态（0正常 1异常） */
    private Integer status;

    /** 请求参数 */
    private String operParam;

    /** 返回参数 */
    private String jsonResult;
}
