package com.gzairports.hz.business.departure.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.basedata.domain.AllSecurityUrl;
import com.gzairports.common.basedata.domain.BaseCargoCode;
import com.gzairports.common.basedata.domain.BaseCargoUldForSave;
import com.gzairports.common.core.domain.PageQuery;
import com.gzairports.hz.business.departure.domain.HzCollectWaybill;
import com.gzairports.hz.business.departure.domain.query.*;
import com.gzairports.hz.business.departure.domain.vo.*;

import java.util.List;

/**
 * 运单收运Service接口
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
public interface IHzCollectWaybillService extends IService<HzCollectWaybill> {

    /**
     * 查询运单收运列表
     * @param query 查询参数
     * @return 结果
     */
    List<HzTransferVo> selectCollectWaybillList(HzTransferQuery query);

    /**
     * 运单收运
     * @param id 货物交接id
     * @return 结果
     */
    HzTransferVo collect(Long id);

    /**
     * 运单收运操作
     * @param waybillId 运单id
     * @return 结果
     */
    WaybillInfoVo collectWaybill(Long waybillId);

    /**
     * 新增运单收运
     * @param vo 新增参数
     * @return 结果
     */
    String addCollectWaybill(CollectWaybillVo vo);

    /**
     * 虚拟收运
     * @param waybill 收运数据
     * @return 结果
     */
    String virtualCollect(HzCollectWaybill waybill);


    /**
     * 批量虚拟收运
     * @param ids 运单id集合
     * @return 结果
     */
    int virtualCollectBatch(Long[] ids);

    /**
     * 拒绝收运
     * @param waybill 收运数据
     * @return 结果
     */
    int refuseCollect(HzCollectWaybill waybill);

    /**
     * 安检提交
     * @param query 安检数据
     * @return 结果
     */
    int securitySubmit(SecuritySubmitQuery query);

    /**
     * 根据运单号查询运单详情
     * @param waybillCode 运单号
     * @return 运单详情
     */
    WaybillInfoVo getInfo(String waybillCode);
    WaybillInfoVo getInfoApp(String waybillCode);

    /**
     * 运单保存
     * @param vo 运单修改参数
     * @return 结果
     */
    int saveWaybill(WaybillInfoVo vo);

    /**
     * 查看随附文件
     * @param waybillId 运单id
     * @return 文件
     */
    String viewTransportFile(Long waybillId);

    /**
     * 虚拟收运列表
     * @param query 查询参数
     * @return 列表
     */
    List<VirtualListVo> virtualList(VirtualListQuery query);

    /**
     * 查询库存列表
     * @param query 查询条件
     * @return 结果
     */
    InventoryRequestVo selectList(InventoryQuery query);

    /**
     * 导出库存列表
     * @param query 查询条件
     * @return 结果
     */
    List<InventoryVo> selectListByQuery(InventoryQuery query);

    /**
     * 取消收运
     * @param vo 查询条件
     * @return 结果
     */
    String cancelWaybill(WaybillInfoVo vo);

    /**
     * 查询特货代码列表
     * @return 结果
     */
    List<String> selectSpecialCargoCode();

    /**
     * 根据特货代码得到中文描述
     * @param specialCargoCode 查询条件
     * @return 结果
     */
    String selectChineseDescriptionBySpecialCargoCode(String specialCargoCode);

    /**
     * 根据特货代码得到大类,货品代码可以为空
     * @param cargoCode 查询条件
     * @return 结果
     */
    List<String> selectCategoryCodeList(String cargoCode);

    /**
     * 根据大类得到货品代码,大类可以为空
     * @param categoryCode 查询条件
     * @return 结果
     */
    List<BaseCargoCode> selectCargoCodeList(String categoryCode);

    String selectCargoNameByCategoryCode(String cargoCode);

    /**
     * 查看修改详情
     * @param id 收运id
     * @return 详情
     */
    CollectWaybillVo editInfo(Long id);

    /**
     * 修改
     * @param vo 修改数据
     * @return 结果
     */
    String edit(CollectWaybillVo vo);

    /**
     * 保存uld
     * @param uld uld数据
     * @return 结果
     */
    int saveUld(BaseCargoUldForSave uld);

    /**
     * 查询当前uld使用状态
     * @param uld uld数据
     * @return 是否
     */
    boolean selectStatus(BaseCargoUldForSave uld);

    /**
     * 修改文件url
     * @param vo 数据
     */
    void saveWaybillUrl(WaybillInfoVo vo);

    /**
     * 安检申报列表
     * @param query 查询参数
     * @return 列表
     */
    PageQuery<List<SecurityVo>> securitySubmitList(SecurityQuery query);

    /**
     * 保存生成的安检申报单
     * */
    int saveWaybillForSecurity(WaybillInfoVo query);

    /**
     * 根据id查询运单数据
     * @param id 查询参数
     * @return 安检申报返回参数
     */
    SecurityVo selectWaybillbyid(String id);

    /**
     * 将虚拟收运的记录转化为真实收运
     * @param id 查询参数
     * @return
     */
    int realCollect(Long id);

    /**
     * 查询该航班是否为宽体机
     * @param  query 航班号  航班时间
     * @return
     * */
    Boolean getCraftType(AddQuery query);

    /**
     * 根据货品代码查询信息
     * */
    BaseCargoCode selectCargoInfo(String cargoCode);

    /**
     * 查询历史
     * */
    List<AllSecurityUrl> getHistoryList(Long id);

    int appRealCollect(Long id);

    AppCollectVo selectCollectWaybillAppList(HzTransferQuery query);

    int appUpdateStorageTransportNotes(WaybillInfoVo vo);

    int cancelPay(Long waybillId);
}
