package com.gzairports.sms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.utils.bean.BeanUtils;
import com.gzairports.common.utils.sign.Md5Utils;
import com.gzairports.sms.domain.HzAgentUser;
import com.gzairports.sms.domain.SmsSendLogPo;
import com.gzairports.sms.domain.dto.*;
import com.gzairports.sms.domain.vo.SmsReceiveUserVo;
import com.gzairports.sms.mapper.sms.SmsSendMapper;
import com.gzairports.sms.service.ISmsSendService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Description: 贵州机场集团发短信实现类
 * @date 2022/6/1 17:04
 */
@Slf4j
@Component
public class GzaSmsSendServiceImpl implements ISmsSendService {

    /**
     * 短信请求url
     */
    @Value("${sms.address}")
    private String SMS_SEND_ADDRESS;

    /**
     * appid
     */
    @Value("${sms.appId}")
    private String SMS_APP_ID;

    /***
     * appSercret
     */
    @Value("${sms.appSecret}")
    private String SMS_APP_SECRET;

    @Autowired
    private SmsSendMapper smsSendMapper;

    @Autowired
    private SmsSendLogService smsSendLogService;

    /**
     * 通过模板的方式发送短信
     * @param param 消息发送参数
     */
    @Override
    public void sendSms(SmsSendParameter param){
        long timestamp = System.currentTimeMillis();
        String authCode = Md5Utils.hash(SMS_APP_ID+SMS_APP_SECRET+timestamp);
        SmsSendBo smsSendBo = new SmsSendBo(param.getMobile(), param.getSmsParameter());
        String url = SMS_SEND_ADDRESS+"?appId="+SMS_APP_ID+"&timestamp="+timestamp+"&authCode="+authCode+"&configCode="+param.getConfigCode();
        HttpResponse response = HttpRequest.post(url).body(JSON.toJSONString(smsSendBo)).execute();
        String resultBody = response.body();
        log.info("发起http请求，url：{}，方法：POST，参数：{}，返回信息：{}",url,JSON.toJSONString(smsSendBo),resultBody);
        SmsSendLogPo smsSendLogPo = new SmsSendLogPo();
        smsSendLogPo.setSmsParameter(param.getSmsParameter());
        smsSendLogPo.setContent(param.getContent());
        smsSendLogPo.setMobile(param.getMobile());
        if (param.getBillId() != null){
            smsSendLogPo.setBillId(param.getBillId());
        }
        smsSendLogPo.setModuleName(param.getModuleName());
        smsSendLogPo.setMsgType(SmsSendLogPo.TEMPLATE_MSG);
        JSONObject resultObject = JSON.parseObject(resultBody);

        smsSendLogPo.setResultCode(resultObject.getInteger("code"));
        smsSendLogPo.setResultMsg(resultBody);
        smsSendLogPo.setSmsConfigCode(param.getConfigCode());
        smsSendLogPo.setCreateTime(new Date());
        smsSendLogService.insertSmsSendLog(smsSendLogPo);
    }

    /**
     * 记录发送日志
     *
     * @param smsSendLogPo 消息发送日志对象
     * @return
     */
    @Override
    public int insertSmsSendLog(SmsSendLogPo smsSendLogPo) {
        //todo 没有记录发送日志
        //return smsSendLogMapper.insert(smsSendLogPo);
        return 0;
    }

    /**
     * 查询短信发送历史记录列表
     *
     * @param query 短信发送历史记录
     * @return 短信发送历史记录列表
     */
    @Override
    public List<SmsSendLogPo> selectSmsSendLogList(SmsSendQuery query) {
        return smsSendLogService.selectSmsSendLogList(query);
    }

    /**
     * 查询短信发送历史记录
     *
     * @param smsSendLogId 短信发送历史记录ID
     * @return 短信发送历史记录
     */
    @Override
    public SmsSendLogPo selectSmsSendLogById(Long smsSendLogId) {
        return smsSendLogService.selectSmsSendLogById(smsSendLogId);
    }

    /**
     * 重新发送消息
     * @param po 消息发送对象
     * @return 接口消息
     */
    @Override
    public String resend(SmsSendLogPo po) {
        long timestamp = System.currentTimeMillis();
        String authCode = Md5Utils.hash(SMS_APP_ID+SMS_APP_SECRET+timestamp);
        SmsSendBo smsSendBo = new SmsSendBo(po.getMobile(),po.getSmsParameter());
        String url = SMS_SEND_ADDRESS+"?appId="+SMS_APP_ID+"&timestamp="+timestamp+"&authCode="+authCode+"&configCode="+po.getSmsConfigCode();
        HttpResponse response = HttpRequest.post(url).body(JSON.toJSONString(smsSendBo)).execute();
        String resultBody = response.body();
        log.info("发起http请求，url：{}，方法：POST，参数：{}，返回信息：{}",url,JSON.toJSONString(smsSendBo),resultBody);
        po.setMsgType(SmsSendLogPo.TEMPLATE_MSG);
        JSONObject resultObject = JSON.parseObject(resultBody);

        po.setResultCode(resultObject.getInteger("code"));
        po.setResultMsg(resultBody);
        po.setUpdateTime(new Date());
        smsSendLogService.saveOrUpdate(po);
        return resultBody;
    }

    @Override
    public void configAgentSendSmsUser(List<HzAgentUserDto> agentUserDtoList) {
        if (CollUtil.isEmpty(agentUserDtoList)) {
            return;
        }
        List<Long> agentIds = agentUserDtoList.stream()
                .map(HzAgentUserDto::getAgentId)
                .distinct()
                .collect(Collectors.toList());
        //删除旧配置
        smsSendMapper.deleteAgentSendSmsConfig(agentIds);

        List<HzAgentUser> agentUserList = BeanUtils.copyBeanList(agentUserDtoList, HzAgentUser.class);
        int isSuccess = smsSendMapper.agentSendSmsConfig(agentUserList);
        if (!SqlHelper.retBool(isSuccess)) {
            throw new CustomException("代理人配置接收短信用户失败");
        }
    }

    @Override
    public List<SmsReceiveUserVo> listSmsReceiveUser(SmsReceiveUserDto smsReceiveUserDto) {
        if (CollUtil.isEmpty(smsReceiveUserDto.getDeptIds())) {
            return CollUtil.newArrayList();
        }
        List<SmsReceiveUserVo> smsReceiveUserVoList = smsSendMapper.selectSmsReceiveUser(smsReceiveUserDto);
        if (CollUtil.isEmpty(smsReceiveUserVoList)) {
            return CollUtil.newArrayList();
        }
        //标记已配置用户
        List<Long> agentIds = smsReceiveUserVoList.stream()
                .map(vo -> {
                    if (vo.getAgentId() == null) {
                        //子部门没有代理人ID，取顶级部门对应代理人ID
                        vo.setAgentId(smsSendMapper.selectAgentIdByDeptId(vo.getTopDeptId()));
                    }
                    return vo.getAgentId();
                })
                .distinct()
                .collect(Collectors.toList());
        List<HzAgentUser> hzAgentUserList = selectFBMSmsSendAgentUser(agentIds);
        Map<Long, Set<Long>> agentToUserIds = hzAgentUserList.stream()
                .collect(Collectors.groupingBy(
                        HzAgentUser::getAgentId,
                        Collectors.mapping(HzAgentUser::getUserId, Collectors.toSet())
                ));
        for (SmsReceiveUserVo vo : smsReceiveUserVoList) {
            Set<Long> userIds = agentToUserIds.get(vo.getAgentId());
            vo.setIsConfig(userIds != null && userIds.contains(vo.getUserId()));
        }
        return smsReceiveUserVoList;
    }

    @Override
    public List<HzAgentUser> selectFBMSmsSendAgentUser(List<Long> agentIds) {
        List<HzAgentUser> hzAgentUserList = smsSendMapper.selectByAgentIds(agentIds);
        return hzAgentUserList;
    }


    /**
     * todo 定时任务获取财务余额监控数据时，发送短信
     */
    public void sendFinanceMonitorSMS() {
        String agent = "贵州航空港物流产业发展有限公司";
        String content = StrUtil.format("【{}】\n" +
                        "贵州汇通城达物流有限公司2025年8月10日上期财务余额（5000.00元）与2025年8月09日本期财务余额（4000.00元）不一致，请及时核对",
                agent);
        SmsSendParameter param = new SmsSendParameter();
        String phone = "query.getPhone()";
        param.setMobile(phone);
        param.setSmsParameter(JSON.toJSONString(content));
        param.setModuleName("财务监控");
        this.sendSms(param);
    }


    private static final Map<String,String> FLIGHT_STATUS  = new LinkedHashMap<>();
    static {
        FLIGHT_STATUS.put("ALT","备降");
        FLIGHT_STATUS.put("ARR","到达");
        FLIGHT_STATUS.put("BAK","滑回");
        FLIGHT_STATUS.put("BOR","登机");
        FLIGHT_STATUS.put("CAN","取消");
        FLIGHT_STATUS.put("CKI","正在值机");
        FLIGHT_STATUS.put("CKO","值机截止");
        FLIGHT_STATUS.put("DEP","起飞");
        FLIGHT_STATUS.put("DLY","延误");
        FLIGHT_STATUS.put("LBD","登机催促");
        FLIGHT_STATUS.put("NST","到下站");
        FLIGHT_STATUS.put("ONR","前方起飞");
        FLIGHT_STATUS.put("POK","登机结束");
        FLIGHT_STATUS.put("RTN","返航");
        FLIGHT_STATUS.put("TBR","过站登机");
        FLIGHT_STATUS.put("TST","测试状态");
        FLIGHT_STATUS.put("BOT","推出");
    }

}
