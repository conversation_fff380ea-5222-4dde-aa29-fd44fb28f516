<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.arrival.mapper.HzArrTallyMapper">

    <select id="notPickedUp" resultType="com.gzairports.common.business.arrival.domain.vo.NotPickedUpVo">
        select hat.id as tallyId, hat.waybill_code, am.cargo_name, am.special_cargo_code1, am.quantity,
               am.weight, am.consign as consignee, am.agent_company, hat.pieces as canQuantity, hat.weight as canWeight,
               ho.order_time as storeTime, am.source_port
        from hz_arr_tally hat
        left join hz_arr_record_order ho on ho.id = hat.record_order_id
        left join all_air_waybill am on am.waybill_code = hat.waybill_code
        <where>
            am.type = 'ARR' and am.transfer_bill = '0' and hat.status = 'lh_comp'
            <if test="value == null or value == '' or value == 'true'">
                and ho.is_examine = 1
            </if>
            <if test="query.consignee != null and query.consignee != ''">
                and am.consign like '%${query.consignee}%'
            </if>
            <if test="query.deptId != null and deptIdList == null">
                and am.dept_id = #{query.deptId}
            </if>
            <if test="deptIdList != null">
                AND am.dept_id IN
                <foreach item="deptId" index="index" collection="deptIdList"
                         open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="query.startTime != null">
                and ho.order_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and ho.order_time <![CDATA[<=]]> #{query.endTime}
            </if>
            <if test="query.agentCompany != null and query.agentCompany != ''">
                and am.agent_company like '%${query.agentCompany}%'
            </if>
            <if test="query.waybillCode != null and query.waybillCode != ''">
                and am.waybill_code like '%${query.waybillCode}%'
            </if>

            <if test="query.flightNo != null and query.flightNo != ''">
                and am.flight_no1 = #{query.flightNo}
            </if>
        </where>
    </select>

    <select id="selectNotPickedUp" resultType="com.gzairports.common.business.arrival.domain.vo.NotPickedUpVo">
        select sum(hat.pieces) as canQuantity, sum(hat.weight) as canWeight
        from hz_arr_tally hat
        left join all_air_waybill am on am.waybill_code = hat.waybill_code
        where hat.status = 'lh_comp' and am.type = 'ARR' and am.transfer_bill = '0'
        and am.waybill_code =#{waybillCode}
    </select>

    <select id="selectTallyData" resultType="com.gzairports.common.business.arrival.domain.vo.NotPickedUpVo">
        select sum(hat.pieces) as canQuantity, sum(hat.weight) as canWeight
        from hz_arr_tally hat
                 left join all_air_waybill am on am.waybill_code = hat.waybill_code
        where am.type = 'ARR' and am.transfer_bill = '0'
          and am.waybill_code =#{waybillCode}
    </select>

    <select id="waybillList" resultType="com.gzairports.hz.business.arrival.domain.vo.FlightFileWaybillVo">
        select aaw.id, aaw.waybill_code, aaw.file_arr, aaw.replenish_bill, aaw.quantity, aaw.weight, aaw.consign, aaw.special_cargo_code1, aaw.cargo_name,
        hat.pieces as tallyQuantity, hat.weight as tallyWeight, hat.cabin_pieces as cabinQuantity, hat.cabin_weight as cabinWeight, hat.leg_id
        from all_air_waybill aaw
        left join hz_arr_tally hat on aaw.waybill_code = hat.waybill_code
        where hat.leg_id = #{legId} and aaw.type = 'ARR'
        <if test="waybillIds != null and waybillIds.size() > 0">
            and aaw.id in
            <foreach collection="waybillIds" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="status != null and status !=''">
            and hat.status = #{status}
        </if>
    </select>
    <select id="selectTallyHistoryList"
            resultType="com.gzairports.hz.business.arrival.domain.vo.TallyHistoryVo">
        select hat.id, CONCAT(afi.air_ways,afi.flight_no) AS flight_no, hat.tally_time, aaw.quantity, aaw.weight,
               hat.pieces as tallyQuantity, ho.cabin_pieces, ho.cabin_weight,
               hat.weight as tallyWeight, hat.store, hat.locator, hat.abnormal, hat.uld, hat.username
        from hz_arr_tally hat
                 left join all_air_waybill aaw on aaw.waybill_code = hat.waybill_code
                 left join hz_flight_load hfl on hfl.id = hat.leg_id
                 left join all_flight_info afi on hfl.flight_id = afi.flight_id
                 left join hz_arr_record_order ho on ho.id = hat.record_order_id
        where hat.waybill_code = #{waybillCode} and aaw.type = 'ARR'
    </select>
    <select id="selectWaybillCount" resultType="java.lang.Integer">
        select count(1) from hz_arr_tally hat left join hz_flight_load hfl on hfl.id = hat.leg_id where hfl.flight_id = #{flightId}
    </select>
    <select id="batchPickUp" resultType="com.gzairports.common.business.arrival.domain.vo.PickUpVo">
        select hat.id as tallyId, hat.waybill_code, aaw.quantity, aaw.weight, aaw.consign, aaw.cargo_name,
               hat.pieces as canPickUpQuantity, hat.weight as canPickUpWeight, aaw.agent_company as agentCode
        from  hz_arr_tally hat
            left join hz_arr_record_order ho on ho.id = hat.record_order_id
            left join all_air_waybill aaw on aaw.waybill_code = hat.waybill_code
            left join hz_flight_load hfl on hfl.id = hat.leg_id
            left join all_flight_info afi on hfl.flight_id = afi.flight_id
            left join sys_dept sd on sd.dept_id = aaw.dept_id
        where hat.status = 'lh_comp' and aaw.type = 'ARR' and aaw.transfer_bill = '0'
        <if test="value == null or value == '' or value == 'true'">
            and ho.is_examine = 1
        </if>
        <if test="query.flightDate != null"> and afi.exec_date = #{query.flightDate} </if>
        <if test="query.flightNo != null and query.flightNo != ''">
            and CONCAT(afi.air_ways,afi.flight_no) like CONCAT('%',#{query.flightNo},'%')
        </if>
        <if test="query.agent != null and query.agent != ''">
            and aaw.agent_company = #{query.agent}
        </if>
        <if test="query.consignee != null and query.consignee != ''">
            and aaw.consign like concat('%',#{query.consignee},'%')
        </if>
        <if test="query.startTime != null">
            and aaw.write_time <![CDATA[>=]]> #{query.startTime}
        </if>
        <if test="query.endTime != null">
            and aaw.write_time <![CDATA[<=]]> #{query.endTime}
        </if>
        <if test="query.deptId != null and query.deptId != '' and deptIdList == null">
            and aaw.dept_id = #{query.deptId}
        </if>
        <if test="deptIdList != null">
            AND aaw.dept_id IN
            <foreach item="deptId" index="index" collection="deptIdList"
                     open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>

        <if test="query.tallyStartTime != null and query.tallyEndTime != null">
            and hat.tally_time between #{query.tallyStartTime} and #{query.tallyEndTime}
        </if>
    </select>
    <select id="selectPickUpVoList" resultType="com.gzairports.common.business.arrival.domain.vo.PickUpVo">
        select hat.id as tallyId, hat.waybill_code, aaw.quantity, aaw.weight, aaw.charge_weight, aaw.consign, aaw.special_cargo_code1, aaw.cargo_name,
               hat.pieces as canPickUpQuantity, hat.weight as canPickUpWeight, hat.record_order_id as orderId, aaw.agent_company as agentCode, aaw.remark
        from  hz_arr_tally hat
            left join all_air_waybill aaw on aaw.waybill_code = hat.waybill_code
        where hat.status = 'lh_comp' and aaw.type = 'ARR' and hat.id in
            <foreach collection="tallyIds" item="tallyId" index="index" open="(" close=")" separator=",">
                #{tallyId}
            </foreach>
    </select>
    <select id="selectPickUpVo" resultType="com.gzairports.common.business.arrival.domain.vo.PickUpVo">
        select hat.id as tallyId, hat.record_order_id as orderId, hat.waybill_code, aaw.quantity, aaw.weight, aaw.charge_weight, aaw.consign, aaw.special_cargo_code1, aaw.cargo_name,
               hat.weight as canPickUpWeight, hat.pieces as canPickUpQuantity, hat.status, hat.record_order_id as orderId, aaw.agent_company as agentCode
        from  hz_arr_tally hat
            left join hz_arr_record_order haro on haro.id = hat.record_order_id
            left join all_air_waybill aaw on aaw.waybill_code = hat.waybill_code
        where aaw.type = 'ARR' and hat.status = 'lh_comp'
          and hat.waybill_code = #{query.waybillCode} and aaw.transfer_bill = '0'
          <if test="value == null or value == '' or value == 'true'">
              and haro.is_examine = 1
          </if>
            <if test="query.deptId != null and deptIdList == null">
                and aaw.dept_id = #{query.deptId}
            </if>
            <if test="deptIdList != null">
                AND aaw.dept_id IN
                <foreach item="deptId" index="index" collection="deptIdList"
                         open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
        order by hat.tally_time desc limit 1
    </select>
    <select id="autoOrder" resultType="com.gzairports.common.business.arrival.domain.vo.AutoOrderVo">
        select hat.waybill_code, aaw.cargo_name, sum(hat.weight) as tallyWeight, sum(hat.pieces) as tallyPieces
        from  hz_arr_tally hat
            left join all_air_waybill aaw on aaw.waybill_code = hat.waybill_code
        where aaw.type = 'ARR' and hat.status = 'lh_comp' and hat.waybill_code = #{waybillCode}
          and aaw.consign_phone = #{phone} and aaw.consign_id_car = #{customerIdNo} and aaw.consign = #{customerName}
        group by hat.waybill_code
    </select>
    <select id="selectQueryList" resultType="com.gzairports.hz.business.arrival.domain.vo.WaybillQueryVo">
        select  aaw.id as waybillId, aaw.waybill_code, aaw.replenish_bill, aaw.file_arr, aaw.prioritize, aaw.transfer_bill, aaw.source_port,
                aaw.carrier1, aaw.des1, aaw.carrier2, aaw.des2, aaw.carrier3, aaw.des3, aaw.des_port, aaw.quantity, aaw.weight, aaw.charge_weight,
                hat.cabin_pieces as cabinQuantity, hat.cabin_weight, aaw.shipper, aaw.consign, aaw.consign_id_car, aaw.consign_phone, aaw.cargo_name,
                aaw.special_cargo_code1, aaw.cold_store,
                aaw.customs_supervision, aaw.is_transfer, aaw.transfer_no, aaw.arr_pay, aaw.cost_sum, aaw.remark
        from hz_arr_tally hat
                left join all_air_waybill aaw on aaw.waybill_code = hat.waybill_code
                left join hz_flight_load hfl on hfl.id = hat.leg_id
                left join all_flight_info afi on hfl.flight_id = afi.flight_id
        where aaw.is_del = 0
        <if test="waybillCode != null and waybillCode !=''">
            and aaw.waybill_code = #{waybillCode}
        </if>
        <if test="domint != null and domint != ''">
            and aaw.domint = #{domint}
        </if>
        <if test="type == 'ARR'">
            and aaw.type = #{type}
        </if>
        <if test="type == 'TRANSFER'">
            and aaw.transfer_bill = 1
        </if>
        <if test="sourcePort != null and sourcePort !=''">
            and aaw.source_port = #{sourcePort}
        </if>
        <if test="desPort != null and desPort !=''">
            and aaw.des_port = #{desPort}
        </if>
        <if test="shipper != null and shipper !=''">
            and aaw.shipper = #{shipper}
        </if>
        <if test="consign != null and consign !=''">
            and aaw.consign = #{consign}
        </if>
        <if test="agentCode != null and agentCode !=''">
            and aaw.agent_code = #{agentCode}
        </if>
        <if test="specialCode != null and specialCode !=''">
            and aaw.special_cargo_code = #{specialCode}
        </if>
        <if test="carrier != null and carrier !=''">
            and aaw.carrier = #{carrier}
        </if>
        <if test="flightNo != null and flightNo !=''">
            and CONCAT(afi.air_ways,afi.flight_no) = #{flightNo}
        </if>
        <if test="cargoCode != null and cargoCode !=''">
            and aaw.cargo_code = #{cargoCode}
        </if>
        <if test="startTime != null">
            and hat.tally_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            and hat.tally_time <![CDATA[<=]]> #{endTime}
        </if>
    </select>
    <select id="selectLhVos" resultType="com.gzairports.hz.business.arrival.domain.vo.LhVo">
        select aaw.quantity, aaw.weight, hat.pieces as lhQuantity, hat.weight as lhWeight, hat.username, hat.store, hat.locator, hat.uld, hat.tally_time, hat.abnormal
        from hz_arr_tally hat
        left join all_air_waybill aaw on aaw.waybill_code = hat.waybill_code
        where aaw.type = 'ARR' and aaw.is_del = 0 and hat.waybill_code = #{waybillCode}
    </select>
    <select id="selectKcList" resultType="com.gzairports.hz.business.arrival.domain.vo.KcVo">
        select aaw.id as waybillId, hat.id as tallyId, hat.pick_up_id, hat.store, hat.locator, hat.uld, hat.pieces as quantity, hat.weight, hat.abnormal
        from hz_arr_tally hat
            left join all_air_waybill aaw on aaw.waybill_code = hat.waybill_code
        where aaw.type = 'ARR' and hat.waybill_code = #{waybillCode} and hat.status in ('lh_comp','save','settle')
    </select>
    <select id="selectInventoryList" resultType="com.gzairports.hz.business.arrival.domain.vo.InventoryListVo">
        select aaw.id as waybillId, hat.id as tallyId, hat.waybill_code, ho.order_time as writeTime, aaw.consign, aaw.quantity, aaw.weight,
               hat.pieces as tallyQuantity,hat.weight as tallyWeight, hat.pick_up_id,hat.store, hat.locator
        from hz_arr_tally hat
            left join all_air_waybill aaw on aaw.waybill_code = hat.waybill_code
            left join hz_arr_record_order ho on ho.id = hat.record_order_id
            left join hz_flight_load hfl on hfl.id = hat.leg_id
            left join all_flight_info afi on hfl.flight_id = afi.flight_id
        where aaw.is_del = 0 and aaw.type = 'ARR' and hat.status in ('lh_comp','save','settle')
        <if test="startTime != null">
            and ho.order_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            and ho.order_time <![CDATA[<=]]> #{endTime}
        </if>
        <if test="shipper != null and shipper != ''">
            and aaw.shipper = #{shipper}
        </if>
        <if test="flightNo != null and flightNo !=''">
            and CONCAT(afi.air_ways,afi.flight_no) = #{flightNo}
        </if>
        <if test="waybillCode != null and waybillCode !=''">
            and aaw.waybill_code like concat('%',#{waybillCode},'%')
        </if>
        <if test="store != null and store !=''">
            and hat.store = #{store}
        </if>
        <if test="locator != null and locator !=''">
            and hat.locator = #{locator}
        </if>
        order by right (hat.waybill_code,1) ASC
    </select>
    <select id="selectRetailList" resultType="com.gzairports.common.business.arrival.domain.vo.RetailVo">
        select hat.id as tallyId, hat.waybill_code, CONCAT(afi.air_ways, afi.flight_no) AS flightNo, afi.exec_date,
               hat.pieces as canPickUpQuantity, hat.weight as canPickUpWeight
        from hz_arr_tally hat
            left join all_air_waybill aaw on aaw.waybill_code = hat.waybill_code
            left join hz_arr_record_order ho on ho.id = hat.record_order_id
            left join hz_flight_load hfl on hfl.id = hat.leg_id
            left join all_flight_info afi on hfl.flight_id = afi.flight_id
        WHERE aaw.is_del = 0 AND aaw.type = 'ARR'
            and aaw.consign_phone = #{phone}
            and aaw.consign_id_car = #{customerIdNo}
        <if test="execDate != null">
            and afi.exec_date = #{execDate}
        </if>
        <if test="flightNo != null and flightNo != ''">
            and CONCAT(afi.air_ways,afi.flight_no) = #{flightNo}
        </if>
    </select>
    <select id="selectOrderData" resultType="com.gzairports.common.business.arrival.domain.vo.OrderProVo">
        select hat.waybill_code, aaw.quantity, aaw.weight, hat.pieces as canPickQuantity, hat.weight as canPickWeight, aaw.cargo_name,
               aaw.special_cargo_code1, aaw.consign
        from hz_arr_tally hat
            left join all_air_waybill aaw on aaw.waybill_code = hat.waybill_code
        where hat.id = #{tallyId}
    </select>
    <select id="selectTimeById" resultType="java.util.Date">
        select order_time from hz_arr_record_order ho left join hz_arr_tally ht on ht.record_order_id = ho.id where ht.id = #{tallyId}
    </select>
    <select id="notBillList" resultType="com.gzairports.common.business.arrival.domain.vo.NotBillListVo">
        select hat.id as tallyId, ho.order_time, aaw.waybill_code, aaw.quantity, aaw.weight,  hat.weight as canBill
        from hz_arr_tally hat
        left join hz_arr_record_order ho on ho.id = hat.record_order_id
        left join all_air_waybill aaw on aaw.waybill_code = hat.waybill_code
        <where>
            hat.status = 'lh_comp' and aaw.type = 'ARR' and aaw.transfer_bill = '0'
            <if test="value == null or value == '' or value == 'true'">
                and ho.is_examine = 1
            </if>
            <if test="query.deptId != null and deptIdList == null">
                and aaw.dept_id = #{query.deptId}
            </if>
            <if test="deptIdList != null">
                AND aaw.dept_id IN
                <foreach item="deptId" index="index" collection="deptIdList"
                         open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="query.startTime != null">
                and ho.order_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and ho.order_time <![CDATA[<=]]> #{query.endTime}
            </if>
            <if test="query.waybillCode != null and query.waybillCode != ''">
                and aaw.waybill_code like '%${query.waybillCode}%'
            </if>
        </where>
    </select>
    <select id="selectId" resultType="com.gzairports.hz.business.arrival.domain.HzArrRecordOrder">
        select haro.id, haro.order_time
        from hz_arr_record_order haro
                 left join hz_flight_load hfl on hfl.id = haro.leg_id
        where hfl.leg = #{desPort} and haro.waybill_code = #{waybillCode} order by haro.order_time desc limit 1
    </select>
    <select id="selectWaybillInfo" resultType="com.gzairports.hz.business.arrival.domain.vo.LhVo">
        select sum(weight) as weight, sum(pieces) as quantity from hz_arr_tally where waybill_code = #{waybillCode}
    </select>
    <select id="selectReportTallyList" resultType="com.gzairports.common.business.reporter.domain.ReportTally">
        select aaw.id as reportId, hat.waybill_code, hat.username, hat.cabin_pieces, hat.cabin_weight, hat.pieces, hat.weight, hat.store,
               hat.tally_time, CONCAT(afi.air_ways, afi.flight_no) AS flightNo, aaw.flight_date1 as flight_date
        from hz_arr_tally hat
            left join all_air_waybill aaw on aaw.waybill_code = hat.waybill_code
            left join hz_flight_load hfl on hat.leg_id = hfl.id
            left join all_flight_info afi on afi.flight_id = hfl.flight_id where aaw.type = 'ARR'
        <if test="waybillCodes != null and waybillCodes.size() > 0">
            and hat.waybill_code in
            <foreach item="code" index="index" collection="waybillCodes" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
    </select>
    <select id="selectTallyInfo" resultType="com.gzairports.hz.business.arrival.domain.vo.LhVo">
        select sum(hat.weight) as weight, sum(hat.pieces) as quantity
        from hz_arr_tally hat
                 left join hz_arr_record_order haro on haro.id = hat.record_order_id
                 left join hz_flight_load hfl on hfl.id = haro.leg_id
                 left join all_flight_info afi on hfl.flight_id = afi.flight_id
        where hat.waybill_code = #{waybillCode} and afi.flight_id = #{flightId}
    </select>
    <select id="selectTallyList" resultType="com.gzairports.common.business.arrival.domain.vo.NotPickedUpVo">
        select hat.pieces as canQuantity, am.waybill_code, hat.weight as canWeight, DATE_FORMAT(hat.tally_time, '%Y-%m-%d') as tallyTime1
        from hz_arr_tally hat
                 left join all_air_waybill am on am.waybill_code = hat.waybill_code
        where am.type = 'ARR' and am.transfer_bill = '0'
        and am.waybill_code in
        <foreach item="code" index="index" collection="waybillCodeList" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>

    <update id="updateTally">
        update hz_arr_tally
        <set>
            <if test="tallyQuantity != null and tallyQuantity != ''">pieces = #{tallyQuantity},</if>
            <if test="tallyWeight != null and tallyWeight != ''">weight = #{tallyWeight},</if>
            <if test="store != null and store != ''">store = #{store},</if>
            <if test="locator != null and locator != ''">locator = #{locator},</if>
            <if test="tallyTime != null">tally_time = #{tallyTime},</if>
        </set>
            where id = #{id}
    </update>
</mapper>