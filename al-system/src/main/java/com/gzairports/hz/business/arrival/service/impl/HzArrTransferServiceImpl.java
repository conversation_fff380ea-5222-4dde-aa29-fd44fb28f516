package com.gzairports.hz.business.arrival.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.hz.business.arrival.domain.HzArrTransfer;
import com.gzairports.hz.business.arrival.domain.query.HzArrTransferQuery;
import com.gzairports.hz.business.arrival.mapper.HzArrTransferMapper;
import com.gzairports.hz.business.arrival.service.IHzArrTransferService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 进港交接Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-12
 */
@Service
public class HzArrTransferServiceImpl extends ServiceImpl<HzArrTransferMapper, HzArrTransfer> implements IHzArrTransferService {

    @Autowired
    private HzArrTransferMapper transferMapper;

    /**
     * 进港交接列表查询
     * @param query 查询参数
     * @return 交接列表
     */
    @Override
    public List<HzArrTransfer> selectList(HzArrTransferQuery query) {
        return transferMapper.selectListByQuery(query);
    }

    /**
     * 根据id查询详情
     * @param id 进港交接id
     * @return 进港交接详情
     */
    @Override
    public HzArrTransfer getInfo(Long id) {
        return transferMapper.selectById(id);
    }

    /**
     * 新增进港交接
     * @param transfer 新增参数
     * @return 结果
     */
    @Override
    public int add(HzArrTransfer transfer) {
        transfer.setSorter(SecurityUtils.getUsername());
        transfer.setHandoverTime(new Date());
        return transferMapper.insert(transfer);
    }
}
