package com.gzairports.wl.ticket.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.wl.ticket.domain.TicketNum;
import com.gzairports.wl.ticket.domain.query.TicketNumQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 票号使用信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-20
 */
@Mapper
public interface TicketNumMapper extends BaseMapper<TicketNum> {

    /**
     * 根据单证id查询未使用票号
     *
     * @param id 单证id
     * @param status 票号使用状态
     * @return 未使用票号集合
     */
    List<Integer> selectByTicketId(@Param("id") Long id,@Param("status") String status);

    /**
     * 单证票号销号
     *
     * @param query 销号票号信息
     * @return 结果
     */
    int cancelByQuery(TicketNumQuery query);

    /**
     * 单证票号发放
     *
     * @param query 发放票号数量
     * @param id 发放记录id
     * @return 结果
     */
    int grant(@Param("query") TicketNumQuery query,@Param("id") Long id);

    /**
     * 根据发放记录id查询票号
     *
     * @param recordId 发放记录id
     */
    void updateStatus(Long recordId);

    /**
     * 批量新增票号数据
     *
     * @param list 批量数据
     */
    int insertBatchSomeColumn(List<TicketNum> list);

    /**
     * 详情-使用明细
     *
     * @param recordId 发放记录id
     * @return 票号使用明细列表
     */
    List<TicketNum> selectListById(Long recordId);

    /**
     * 重新发放
     * @param recordId 发放记录id
     * @return 结果
     */
    void reissue(Long recordId);
}
