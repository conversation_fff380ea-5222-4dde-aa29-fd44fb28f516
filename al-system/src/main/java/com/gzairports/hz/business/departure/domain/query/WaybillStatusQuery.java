package com.gzairports.hz.business.departure.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 运单状态查询参数
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Data
public class WaybillStatusQuery {

    /** 运单号 */
    private String waybillCode;

    /** 始发站 */
    private String sourcePort;

    /** 目的站 */
    private String desPort;

    /** 航班号 */
    private String flightNo1;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date flightDate1;

    /** 承运人1 */
    private String carrier1;

    /** 到达站1 */
    private String des1;

    /** 发货人 */
    private String shipper;

    /** 收货人 */
    private String consign;

    /** 特货代码1 */
    private String specialCargoCode1;

    /** 品名编码 */
    private String cargoCode;

    /** 类型 DEP 出港 ARR 进港 */
    private String type;

    /** 运单状态*/
    private String waybillStatus;

    /** 主单表的运单状态 */
    private String status;

    /** 时间段设置时间 */
    private Date startTime;

    /** 时间段设置时间 */
    private Date endTime;

    /** 国际国内判断条件:国际 I /国内 D */
    private String domint;
}
