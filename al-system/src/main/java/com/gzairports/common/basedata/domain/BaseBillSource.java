package com.gzairports.common.basedata.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 票证来源
 *
 * <AUTHOR>
 * @date 2024-05-17
 */
@Data
@TableName("base_bill_source")
public class BaseBillSource {

    /** 主键id */
    private Long id;

    /** 票证管理id */
    private Long billTypeId;

    @TableField(exist = false)
    private String code;

    @TableField(exist = false)
    private String typeName;

    /** 名称 */
    @Excel(name = "名称")
    private String name;

    /** 前缀 */
    @Excel(name = "前缀")
    private String prefix;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 是否删除 0 否 1 是 */
    private Integer isDel;

    /** 所属单位 */
    private Long deptId;
}
