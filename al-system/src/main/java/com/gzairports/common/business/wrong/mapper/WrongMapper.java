package com.gzairports.common.business.wrong.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.wrong.domain.Wrong;
import com.gzairports.wl.departure.domain.query.WrongQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 不正常货邮Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Mapper
public interface WrongMapper extends BaseMapper<Wrong> {

    /**
     * 查询不正常货运列表->代理人必须为自己最父级部门
     * @param query 查询参数
     * @return 不正常货邮列表
     */
    List<Wrong> selectListByQuery(@Param("query") WrongQuery query,@Param("agent") String agent);

    /**
     * 查询不正常货运列表->货站登录方条件查询所有
     * @param query 查询参数
     * @return 不正常货邮列表
     */
    List<Wrong> selectListAllByQuery(@Param("query") WrongQuery query);

    /**
     * 根据运单号后四位或者八位查询运单号
     * @param waybillCode 运单号
     * @return 运单号集合
     */
    List<String> getWaybillCodeByFour(@Param("waybillCode") String waybillCode,
                                      @Param("length") int length,
                                      @Param("deptId") Long deptId);


    /**
     * 根据运单号查询代理人等数据返回给不正常货邮
     * @return 收费数据
     */
    List<Wrong> selectAgent(String waybillCode);

    Integer isOperateWrong(String waybillCode);
}
