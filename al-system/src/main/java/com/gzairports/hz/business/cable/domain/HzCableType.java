package com.gzairports.hz.business.cable.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 电报类型对象 hz_cable_type
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
@Data
@TableName("hz_cable_type")
public class HzCableType
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 电报名称 */
    private String cableName;

    /** 电报编码 */
    private String cableCode;

    /** 内容前缀 */
    private String prefix;

    /** 是否删除 0 否 1 是 */
    private Integer isDel;

    /** 备注 */
    private String remark;

    /** 是否可删除 */
    @TableField(exist = false)
    private Integer isAbleDel;

    /** 电报模板 */
    private String template;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("cableName", getCableName())
            .append("cableCode", getCableCode())
            .append("prefix", getPrefix())
            .append("remark", getRemark())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .append("isDel", getIsDel())
            .toString();
    }
}
