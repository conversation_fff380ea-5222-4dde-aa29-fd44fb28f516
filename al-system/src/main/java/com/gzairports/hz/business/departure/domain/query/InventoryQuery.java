package com.gzairports.hz.business.departure.domain.query;

import lombok.Data;

import java.util.Date;

/**
 * 库存管理查询参数
 * <AUTHOR>
 * @date 2024-07-15
 */
@Data
public class InventoryQuery {

    /** 入库时间 */
    private Date startTime;

    /** 入库时间 */
    private Date endTime;

    /** 发货代理人 */
    private String agentCode;

    /** 仓库 */
    private String store;

    /** 库位 */
    private String locator;

    /** 运单号 */
    private String waybillCode;
}
