package com.gzairports.web.controller.departure;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzairports.common.basedata.domain.Customer;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.wl.departure.domain.query.OnlinePayQuery;
import com.gzairports.wl.departure.domain.vo.OnlineMawbVo;
import com.gzairports.wl.departure.domain.vo.OnlinePayVo;
import com.gzairports.wl.departure.service.IOnlinePayService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 货站在线缴费Controller
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@RestController
@RequestMapping("/dep/onlinePay")
public class OnlinePayController extends BaseController {

    @Autowired
    private IOnlinePayService onlinePayService;

    /**
     * 货站在线缴费
     */
//    @PreAuthorize("@ss.hasPermi('dep:onlinePay:list')")
    @GetMapping("/list")
    @ApiOperation(value = "货站在线缴费")
    public AjaxResult list(OnlinePayQuery query){
        OnlinePayVo vo = onlinePayService.selectList(query);
        return AjaxResult.success(vo);
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出货站在线缴费")
    public void export(HttpServletResponse response, OnlinePayQuery query)
    {
        query.setPageSize(Integer.MAX_VALUE);
        OnlinePayVo vo = onlinePayService.selectList(query);
        List<OnlineMawbVo> records = vo.getOnlineMawbVos().getRecords();
        if(records.size() > 0){
            OnlineMawbVo onlineMawbVo = new OnlineMawbVo();
            onlineMawbVo.setWaybillCode("总单数:" + vo.getTotalOrder());
            onlineMawbVo.setSourcePort("未支付单数总计:" + vo.getUnPayOrder());
            onlineMawbVo.setDesPort("已支付单数总计:" + vo.getPayOrder());
            onlineMawbVo.setFlightNo1("已结算单数总计:" + vo.getSettleOrder());
            onlineMawbVo.setShipper("已支付费用总计:" + vo.getPay());
            onlineMawbVo.setSpecialCargoCode1("已结算费用总计:" + vo.getSettle());
            onlineMawbVo.setCargoCode("已退还费用总计:" + vo.getRefund());
            onlineMawbVo.setCargoName("未支付费用总计:" + vo.getUnPay());
            records.add(onlineMawbVo);
        }
        ExcelUtil<OnlineMawbVo> util = new ExcelUtil<OnlineMawbVo>(OnlineMawbVo.class);
        util.exportExcel(response, records, "货站在线缴费数据");
    }

    /**
     * 货站在线缴费详细信息
     */
    @PreAuthorize("@ss.hasPermi('dep:onlinePay:getInfo')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "货站在线缴费详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(onlinePayService.getInfo(id));
    }


    /**
     * 预授权支付
     */
    @PreAuthorize("@ss.hasPermi('dep:onlinePay:payment')")
    @GetMapping(value = "/payment/{id}")
    @ApiOperation(value = "预授权支付")
    public AjaxResult payment(@PathVariable("id") Long id)
    {
        return toAjax(onlinePayService.payment(id));
    }
}
