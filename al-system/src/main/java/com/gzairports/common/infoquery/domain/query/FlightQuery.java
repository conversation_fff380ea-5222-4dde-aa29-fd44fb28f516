package com.gzairports.common.infoquery.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * Created by david on 2024/5/8
 * <AUTHOR>
 */

@Data
public class FlightQuery {

    /** 日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date flightDate;

    /** 航班号 */
    private String flightNo;

    /** 计飞 */
    private Date planTakeoffTime;

    /** 航司 */
    private String air;

    /** 进(ARR)出(DEP)港 */
    private String type;

    /** 目的地 */
    private String desPort;
}
