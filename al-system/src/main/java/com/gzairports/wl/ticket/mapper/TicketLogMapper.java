package com.gzairports.wl.ticket.mapper;

import com.gzairports.wl.ticket.domain.TicketLog;
import com.gzairports.wl.ticket.domain.vo.TicketOprLogVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 单证日志Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@Mapper
public interface TicketLogMapper {

    /**
     * 查询单证使用log列表
     *
     * @param id 查询参数
     * @return 单证log详情
     */
    List<TicketOprLogVO> selectList(Long id);

    /**
     * 新增操作日志
     *
     * @param ticketLog 操作日志对象
     */
    void insertTicketLog(TicketLog ticketLog);
}
