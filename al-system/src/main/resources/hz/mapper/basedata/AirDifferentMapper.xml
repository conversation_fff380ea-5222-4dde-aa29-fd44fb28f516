<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.AirDifferentMapper">
    <insert id="addAirDifferent" parameterType="BaseAirDifferent">
        insert into base_air_different
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="airCompany != null and airCompany != ''">air_company,</if>
            <if test="flightType != null and flightType != ''">flight_type,</if>
            <if test="flightNumber != null and flightNumber != ''">flight_number,</if>
            <if test="desPort != null and desPort != ''">des_port,</if>
            <if test="noLoad != null">no_load,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="airCompany != null and airCompany != ''">#{airCompany},</if>
            <if test="flightType != null and flightType != ''">#{flightType},</if>
            <if test="flightNumber != null and flightNumber != ''">#{flightNumber},</if>
            <if test="desPort != null and desPort != ''">#{desPort},</if>
            <if test="noLoad != null">#{noLoad},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="editAirDifferent" parameterType="baseAirDifferent">
        update base_air_different
        <trim prefix="SET" suffixOverrides=",">
            <if test="desPort != null and desPort != ''">des_port = #{desPort},</if>
            <if test="flightType != null and flightType != ''">flight_type = #{flightType},</if>
            <if test="flightNumber != null and flightNumber != ''">flight_number = #{flightNumber},</if>
            <if test="airCompany != null and airCompany != ''">air_company = #{airCompany},</if>
            <if test="noLoad != null">no_load = #{noLoad},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="removeAirDifferent">
        update base_air_different set is_del = 1 where id = #{id}
    </update>

    <select id="airDifferentList" resultType="com.gzairports.common.basedata.domain.BaseAirDifferent">
        select id, air_company, flight_number, flight_type, des_port, no_load, start_time, end_time  from base_air_different
        <where>
         and is_del = 0
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="airCompany != null  and airCompany != ''"> and air_company like CONCAT('%',#{airCompany},'%')</if>
            <if test="flightType != null and flightType != ''"> and flight_type like CONCAT('%',#{flightType},'%')</if>
            <if test="flightNumber != null and flightNumber != ''"> and flight_number like CONCAT('%',#{flightNumber},'%')</if>
            <if test="desPort != null and desPort != ''"> and des_port like CONCAT('%',#{desPort},'%')</if>
            <if test="noLoad != null and noLoad != ''"> and no_load = #{noLoad}</if>
            <if test="startTime != null and startTime != ''">
                AND DATE_FORMAT(start_time,'%Y%m%d') <![CDATA[>=]]> DATE_FORMAT(#{startTime, jdbcType=DATE},'%Y%m%d')
            </if>
            <if test="endTime != null and endTime != ''">
                AND DATE_FORMAT(end_time,'%Y%m%d') <![CDATA[<=]]> DATE_FORMAT(#{endTime, jdbcType=DATE},'%Y%m%d')
            </if>
        </where>
    </select>
    <select id="getInfo" resultType="com.gzairports.common.basedata.domain.BaseAirDifferent">
        select id, air_company, flight_number, flight_type, des_port, no_load, start_time, end_time  from base_air_different where id = #{id}
    </select>
</mapper>
