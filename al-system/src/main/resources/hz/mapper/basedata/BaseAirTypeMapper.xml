<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.BaseAirTypeMapper">
    
    <resultMap type="BaseAirType" id="BaseAirTypeResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="airType"    column="air_type"    />
        <result property="cabin"    column="cabin"    />
        <result property="number"    column="number"    />
        <result property="cabinLimit"    column="cabin_limit"    />
        <result property="support"    column="support"    />
        <result property="beforeHatchSize"    column="before_hatch_size"    />
        <result property="afterHatchSize"    column="after_hatch_size"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectBaseAirTypeVo">
        select id, type, air_type, cabin, number, cabin_limit, support, before_hatch_size, after_hatch_size, create_time, update_by, update_time, create_by, is_del from base_air_type
    </sql>

    <select id="selectBaseAirTypeList" parameterType="BaseAirType" resultMap="BaseAirTypeResult">
        <include refid="selectBaseAirTypeVo"/>
        <where>
            is_del = 0
            <if test="type != null  and type != ''"> and type like '%${type}%'</if>
            <if test="airType != null  and airType != ''"> and air_type like '%${airType}%'</if>
        </where>
    </select>
    
    <select id="selectBaseAirTypeById" parameterType="Long" resultMap="BaseAirTypeResult">
        <include refid="selectBaseAirTypeVo"/>
        where id = #{id}
    </select>
    <select id="selectByType" resultType="com.gzairports.common.basedata.domain.BaseAirType">
        select id, type, air_type, cabin, number, cabin_limit, support, before_hatch_size, after_hatch_size, create_time, update_by, update_time,
               create_by, is_del
        from base_air_type where type = #{type} and is_del = 0
    </select>

    <insert id="insertBaseAirType" parameterType="BaseAirType" useGeneratedKeys="true" keyProperty="id">
        insert into base_air_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null">type,</if>
            <if test="airType != null">air_type,</if>
            <if test="cabin != null">cabin,</if>
            <if test="number != null">number,</if>
            <if test="cabinLimit != null">cabin_limit,</if>
            <if test="support != null">support,</if>
            <if test="beforeHatchSize != null">before_hatch_size,</if>
            <if test="afterHatchSize != null">after_hatch_size,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="isDel != null">is_del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null">#{type},</if>
            <if test="airType != null">#{airType},</if>
            <if test="cabin != null">#{cabin},</if>
            <if test="number != null">#{number},</if>
            <if test="cabinLimit != null">#{cabinLimit},</if>
            <if test="support != null">#{support},</if>
            <if test="beforeHatchSize != null">#{beforeHatchSize},</if>
            <if test="afterHatchSize != null">#{afterHatchSize},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="isDel != null">#{isDel},</if>
         </trim>
    </insert>

    <update id="updateBaseAirType" parameterType="BaseAirType">
        update base_air_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="airType != null">air_type = #{airType},</if>
            <if test="cabin != null">cabin = #{cabin},</if>
            <if test="number != null">number = #{number},</if>
            <if test="cabinLimit != null">cabin_limit = #{cabinLimit},</if>
            <if test="support != null">support = #{support},</if>
            <if test="beforeHatchSize != null">before_hatch_size = #{beforeHatchSize},</if>
            <if test="afterHatchSize != null">after_hatch_size = #{afterHatchSize},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteBaseAirTypeById" parameterType="Long">
        update base_air_type set is_del = 1 where id = #{id}
    </update>

    <delete id="deleteBaseAirTypeByIds" parameterType="String">
        delete from base_air_type where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>