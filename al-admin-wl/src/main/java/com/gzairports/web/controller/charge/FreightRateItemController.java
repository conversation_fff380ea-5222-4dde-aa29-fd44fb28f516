package com.gzairports.web.controller.charge;

import com.gzairports.wl.charge.domain.FreightRateItem;
import com.gzairports.wl.charge.domain.query.FreightRateItemQuery;
import com.gzairports.wl.charge.domain.vo.FreightRateItemVO;
import com.gzairports.wl.charge.service.FreightRateItemService;
import com.gzairports.common.annotation.OperLog;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 公布运价条目Controller
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@RestController
@RequestMapping("/freightItem")
@Api(tags = "公布运价条目接口")
public class FreightRateItemController extends BaseController {

    @Autowired
    private FreightRateItemService rateItemService;


    /**
     * 查询公布运价条目列表
     */
//    @PreAuthorize("@ss.hasPermi('charge:freightItem:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询公布运价条目列表")
    public TableDataInfo list(FreightRateItemQuery query){
        startPage();
        List<FreightRateItemVO> list = rateItemService.selectList(query);
        return getDataTable(list);
    }

    /**
     * 获取运价条目详细信息
     */
    @PreAuthorize("@ss.hasPermi('charge:freightItem:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取运价条目详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rateItemService.selectRateItemById(id));
    }

    /**
     * 新增运价条目
     */
    @PreAuthorize("@ss.hasPermi('charge:freightItem:add')")
    @OperLog(title = "运价条目", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ApiOperation(value = "新增运价条目")
    public AjaxResult add(@RequestBody FreightRateItem item)
    {
        return toAjax(rateItemService.insertRateItem(item));
    }

    /**
     * 修改运价条目
     */
    @PreAuthorize("@ss.hasPermi('charge:freightItem:edit')")
    @OperLog(title = "运价条目", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ApiOperation(value = "修改运价条目")
    public AjaxResult edit(@RequestBody FreightRateItem item)
    {
        return toAjax(rateItemService.updateRateItem(item));
    }

    /**
     * 删除运价条目
     */
    @PreAuthorize("@ss.hasPermi('charge:freightItem:del')")
    @OperLog(title = "运价条目", businessType = BusinessType.DELETE)
    @GetMapping("/del/{id}")
    @ApiOperation(value = "删除运价条目")
    public AjaxResult del(@PathVariable Long id)
    {
        return toAjax(rateItemService.removeRateItem(id));
    }

    /**
     * 批量删除公布运价条目
     */
    @PreAuthorize("@ss.hasPermi('charge:freightCustom:del')")
    @OperLog(title = "运价条目", businessType = BusinessType.DELETE)
    @GetMapping("/delBatch/{ids}")
    @ApiOperation(value = "批量删除公布运价")
    public AjaxResult delAll(@PathVariable("ids") Long[] ids)
    {
        rateItemService.delAll(ids);
        return AjaxResult.success("操作成功");
    }

}
