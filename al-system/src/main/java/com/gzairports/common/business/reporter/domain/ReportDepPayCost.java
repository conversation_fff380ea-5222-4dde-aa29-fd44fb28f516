package com.gzairports.common.business.reporter.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-02-19
 */
@Data
@TableName("all_report_dep_pay_cost")
public class ReportDepPayCost {

    /** 主键id */
    private Long id;

    /** 报表id */
    private Long reportId;

    /** 运单号 */
    private String waybillCode;

    /** 费用名称 */
    private String rateName;

    /** 费率 */
    private BigDecimal rate;

    /** 计费重量 */
    private String payQuantity;

    /** 实际数量 */
    private BigDecimal settleDepWeight;

    /** 总费用 */
    private BigDecimal totalCharge;

    /** 是否支付 0 未支付 1 已支付 2 取消支付*/
    private String isSettle;

    /** 部门id */
    private Long deptId;

    /** 收费服务类型 0 默认收费 1 服务申请收费 2 冷藏登记收费*/
    private Integer serviceType;

    /** 服务申请id */
    private Long serviceRequestId;

    /** 创建时间 */
    private String createTime;

    /** 结算已出港件数 */
    private Integer settleDepQuantity;
}
