package com.gzairports.common.charge.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.charge.domain.HzChargeItemRule;
import com.gzairports.common.charge.mapper.HzChargeItemRuleMapper;
import com.gzairports.common.charge.service.IHzChargeItemRuleService;
import com.gzairports.common.charge.service.IHzChargeRuleService;
import org.springframework.stereotype.Service;

/**
 * Created by david on 2024/12/16
 */
@Service
public class HzChargeItemRuleServiceImpl extends ServiceImpl<HzChargeItemRuleMapper, HzChargeItemRule> implements IHzChargeItemRuleService {
}
