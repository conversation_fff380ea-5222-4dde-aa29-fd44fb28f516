package com.gzairports.hz.business.reporter.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.hz.business.reporter.domain.HzReportField;
import com.gzairports.hz.business.reporter.domain.HzReportFormula;
import com.gzairports.hz.business.reporter.domain.HzReportSet;
import com.gzairports.hz.business.reporter.domain.query.HzReportSetDto;
import com.gzairports.hz.business.reporter.domain.query.HzReportSetQuery;

import java.util.List;

public interface HzReportSetService extends IService<HzReportSet> {
    List<HzReportSet> selectHzReportSetList(HzReportSetQuery query);

    int publishReport(Long id);

    int downReport(Long id);

    int delReport(Long id);

    List<HzReportField> selectTableList(Long type);

    int insertReportSet(HzReportSetDto dto);

    HzReportSetDto infoSet(Long id);

    int updateSet(HzReportSetDto dto);

    int addFormula(HzReportFormula dto);
}
