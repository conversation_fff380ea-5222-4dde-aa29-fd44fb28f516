package com.gzairports.hz.business.departure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.gzairports.hz.business.departure.domain.FlightLoadUld;
import com.gzairports.hz.business.departure.domain.vo.FormalUldVo;
import com.gzairports.hz.business.departure.domain.vo.ForwardImportUldVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 航班配载板箱Mapper接口
 *
 * <AUTHOR>
 * @date 2023-09-18
 */
@Mapper
public interface FlightLoadUldMapper extends BaseMapper<FlightLoadUld> {

    /**
     * 根据配载id查询板箱配载列表
     * @param flightLoadId 航班配载id
     * @return 板箱配载列表
     */
    List<ForwardImportUldVo> selectLoadUldList(Long flightLoadId);

    /**
     * 根据配载id查询板箱配载列表
     * @param id 航班配载id
     * @return 板箱配载列表
     */
    List<FormalUldVo> selectListByFlightLoadId(@Param("id") Long id,@Param("type") Integer type);

    /**
     * 根据配载id和板箱id查询板箱配载列表
     * */
    List<FormalUldVo> selectListByFlightLoadIdAndUldIds(@Param("id") Long id,@Param("uldIds") List<Long> uldIds);


    /**
     * 根据配载id查询板箱配载列表,并进行去重得到总票数
     * @param id 航班配载id
     * @return 总票数 waybillNum
     */
    Integer selectWaybillNum(Long id);

    /**
     * 根据航段id查询板箱上的运单id
     * @param id 配载id
     * @return 板箱运单id集合
     */
    List<Long> selectWaybillIdsByFlightLoadId(Long id);
}
