package com.gzairports.common.business.departure.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.business.departure.domain.vo.LogAwblog;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 运单日志Service接口
 * 
 * <AUTHOR>
 * @date 2023-09-25
 */
public interface ILogAwblogService extends IService<LogAwblog>
{
    /***
     * 更加运单号查询
     * @param stocknoSet
     * @return
     */
    Map<String,List<LogAwblog>> listByStocks(Collection<String> stocknoSet);

    Map<String, List<LogAwblog>> listByTime(Date startDate, Date endDate);
}
