package com.gzairports.hz.business.cargofee.service;

import com.baomidou.mybatisplus.extension.service.IService;

import com.gzairports.hz.business.cargofee.domain.WaybillFeeHz;
import com.gzairports.hz.business.cargofee.domain.query.WaybillFeeHzQuery;
import com.gzairports.hz.business.cargofee.domain.vo.WaybillFeeHzVo;


/**
 * 运单费用明细Service接口
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
public interface IWaybillFeeHzService extends IService<WaybillFeeHz> {

    /**
     * 查询运单费用明细列表
     * @param query 查询参数
     * @return 运单费用明细列表
     */
    WaybillFeeHzVo selectList(WaybillFeeHzQuery query);
}
