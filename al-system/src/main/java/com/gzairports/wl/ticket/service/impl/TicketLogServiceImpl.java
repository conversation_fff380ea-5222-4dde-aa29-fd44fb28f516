package com.gzairports.wl.ticket.service.impl;

import com.gzairports.wl.ticket.domain.TicketLog;
import com.gzairports.wl.ticket.mapper.TicketLogMapper;
import com.gzairports.wl.ticket.service.TicketLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 单证管理日志 服务层处理
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@Service
public class TicketLogServiceImpl implements TicketLogService {

    @Autowired
    private TicketLogMapper logMapper;

    /**
     * 新增操作日志
     *
     * @param ticketLog 操作日志对象
     */
    @Override
    public void insertTicketLog(TicketLog ticketLog) {
        logMapper.insertTicketLog(ticketLog);
    }
}
