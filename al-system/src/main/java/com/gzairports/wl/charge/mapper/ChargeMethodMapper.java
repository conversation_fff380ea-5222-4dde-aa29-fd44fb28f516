package com.gzairports.wl.charge.mapper;

import com.gzairports.wl.charge.domain.ChargeMethod;
import com.gzairports.wl.charge.domain.query.ChargeMethodQuery;
import com.gzairports.wl.charge.domain.vo.ChargeMethodVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 客户结算方式Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@Mapper
public interface ChargeMethodMapper {

    /**
     * 查询客户结算方式列表
     *
     * @param query 查询参数
     * @return 客户结算方式列表
     */
    List<ChargeMethodVO> selectList(ChargeMethodQuery query);

    /**
     * 新增客户结算方式
     *
     * @param method 客户结算方式
     * @return 结果
     */
    int insertChargeMethod(ChargeMethod method);

    /**
     * 修改客户结算方式
     *
     * @param method 客户结算方式
     * @return 结果
     */
    int updateChargeMethod(ChargeMethod method);

    /**
     * 查询客户结算方式
     *
     * @param id 客户结算方式主键
     * @return 客户结算方式
     */
    ChargeMethodVO selectChargeById(Long id);

    /**
     * 删除客户结算方式
     *
     * @param id 客户结算方式主键
     * @return 结果
     */
    int removeChargeMethod(Long id);
}
