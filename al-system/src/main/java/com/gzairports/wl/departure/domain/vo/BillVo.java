package com.gzairports.wl.departure.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import com.gzairports.common.core.typehandler.StringListJsonTypeHandler;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 国内分单账单管理列表返回参数
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@Data
public class BillVo {

    /** 主键id */
    private Long id;

    /** 流水号 */
    @Excel(name = "账号流水号")
    private String serialNo;

    /** 客户(发货人) */
    @Excel(name = "客户")
    private String shipper;

    /** 运单数 */
    @Excel(name = "运单数")
    private Integer totalOrder;

    /** 总件数 */
    @Excel(name = "总件数")
    private Integer totalQuantity;

    /** 总重量 */
    @Excel(name = "总重量")
    private BigDecimal totalWeight;

    /** 计费总重量 */
    @Excel(name = "计费总重量")
    private BigDecimal totalChargeWeight;

    /** 总金额 */
    @Excel(name = "总金额")
    private BigDecimal totalMoney;

    /** 支付方式 */
    @Excel(name = "支付方式")
    private String payMethod;

    /** 支付状态 */
    @Excel(name = "支付状态")
    private String payStatus;

    /** 备注 */
    private String remark;

    /** 状态 */
    private String status;

    /** 生成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "生成时间")
    private Date createTime;

    /** 支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付时间")
    private Date payTime;

    /** 支付凭证 */
    @TableField(value = "pay_voucher",typeHandler = StringListJsonTypeHandler.class)
    private List<String> payVoucher;

    /** 运单列表 */
    private List<HawbBillVo> billVos;
}
