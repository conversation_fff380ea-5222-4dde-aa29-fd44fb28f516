package com.gzairports.common.business.wrong.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.BaseAgent;
import com.gzairports.common.basedata.domain.BaseBalance;
import com.gzairports.common.basedata.domain.BaseCargoCode;
import com.gzairports.common.basedata.mapper.BaseAgentMapper;
import com.gzairports.common.basedata.mapper.BaseBalanceMapper;
import com.gzairports.common.basedata.mapper.CargoCodeMapper;
import com.gzairports.common.business.arrival.domain.FlightLoadWaybill;
import com.gzairports.common.business.arrival.domain.HzArrTally;
import com.gzairports.common.business.arrival.mapper.FlightInfoMapper;
import com.gzairports.common.business.arrival.mapper.FlightLoadWaybillMapper;
import com.gzairports.common.business.arrival.mapper.HzArrTallyMapper;
import com.gzairports.common.business.arrival.mapper.PickUpWaybillMapper;
import com.gzairports.common.business.departure.domain.*;
import com.gzairports.common.business.departure.domain.vo.LoadInfoVo;
import com.gzairports.common.business.departure.mapper.CostDetailMapper;
import com.gzairports.common.business.departure.mapper.MawbMapper;
import com.gzairports.common.business.departure.mapper.PullDownMapper;
import com.gzairports.common.business.departure.mapper.WaybillFeeMapper;
import com.gzairports.common.business.departure.service.impl.WaybillTraceServiceImpl;
import com.gzairports.common.business.wrong.domain.Wrong;
import com.gzairports.common.business.wrong.domain.WrongRecord;
import com.gzairports.common.business.wrong.mapper.WrongMapper;
import com.gzairports.common.business.wrong.mapper.WrongRecordMapper;
import com.gzairports.common.business.wrong.service.IWrongService;
import com.gzairports.common.charge.domain.HzChargeIrRelation;
import com.gzairports.common.charge.domain.HzChargeItemRule;
import com.gzairports.common.charge.domain.HzChargeItems;
import com.gzairports.common.charge.domain.HzChargeRule;
import com.gzairports.common.charge.domain.vo.BillRuleVo;
import com.gzairports.common.charge.domain.vo.IrRelationVo;
import com.gzairports.common.charge.mapper.HzChargeIrRelationMapper;
import com.gzairports.common.charge.mapper.HzChargeItemRuleMapper;
import com.gzairports.common.charge.mapper.HzChargeItemsMapper;
import com.gzairports.common.charge.mapper.HzChargeRuleMapper;
import com.gzairports.common.charge.service.BillingRule;
import com.gzairports.common.charge.service.impl.BillingRuleFactory;
import com.gzairports.common.core.domain.entity.SysDept;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.common.log.service.IWaybillLogService;
import com.gzairports.common.message.domain.Message;
import com.gzairports.common.message.domain.vo.SocketMessageVo;
import com.gzairports.common.message.mapper.MessageMapper;
import com.gzairports.common.rabbitmq.SecurityProducer;
import com.gzairports.common.securitySubmit.domain.WaybillExitData;
import com.gzairports.common.system.domain.SysConfig;
import com.gzairports.common.system.mapper.SysConfigMapper;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.utils.BigDecimalRoundUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.ServletUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.hz.business.departure.domain.*;
import com.gzairports.hz.business.departure.mapper.*;
import com.gzairports.hz.business.departure.rabbitmq.WaybillMessageProducer;
import com.gzairports.wl.departure.domain.query.WrongQuery;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 不正常货邮Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Service
public class WrongServiceImpl extends ServiceImpl<WrongMapper, Wrong> implements IWrongService {

    @Autowired
    private WrongMapper wrongMapper;

    @Autowired
    private WrongRecordMapper wrongRecordMapper;

    @Autowired
    private ExitCargoMapper exitCargoMapper;

    @Autowired
    private SysDeptMapper deptMapper;

    @Autowired
    private AllAirWaybillMapper airWaybillMapper;

    @Autowired
    private CostDetailMapper costDetailMapper;

    @Autowired
    private BaseAgentMapper baseAgentMapper;

    @Autowired
    private BaseBalanceMapper baseBalanceMapper;

    @Autowired
    private MawbMapper mawbMapper;

    @Autowired
    private IWaybillLogService waybillLogService;

    @Autowired
    private FlightLoadWaybillMapper loadWaybillMapper;

    @Autowired
    private FlightInfoMapper infoMapper;

    @Autowired
    private WaybillMessageProducer waybillMessageProducer;

    @Autowired
    private MessageMapper messageMapper;

    @Autowired
    private HzChargeRuleMapper ruleMapper;

    @Autowired
    private HzChargeIrRelationMapper relationMapper;

    @Autowired
    private HzChargeItemRuleMapper itemRuleMapper;

    @Autowired
    private WaybillFeeMapper feeMapper;

    @Autowired
    private HzChargeItemsMapper itemsMapper;

    @Autowired
    private SysConfigMapper sysConfigMapper;

    @Autowired
    private CargoCodeMapper cargoCodeMapper;

    @Autowired
    private WaybillTraceServiceImpl traceService;

    @Autowired
    private PullDownMapper pullDownMapper;

    @Autowired
    private FlightLoadMapper flightLoadMapper;

    @Autowired
    private HzDisBoardMapper disBoardMapper;

    @Autowired
    private PickUpWaybillMapper pickUpWaybillMapper;

    @Autowired
    private HzArrTallyMapper hzArrTallyMapper;

    @Autowired
    private SecurityProducer securityProducer;

    @Autowired
    private HzCollectWaybillMapper collectWaybillMapper;

    /**
     * 查询不正常货运列表
     * @param query 查询参数
     * @return 不正常货邮列表
     */
    @Override
    public List<Wrong> selectList(WrongQuery query) {
        List<Wrong> wrongs = wrongMapper.selectListAllByQuery(query);
        for (Wrong wrong:wrongs) {
            List<WrongRecord> recordList = wrongRecordMapper.selectList(new QueryWrapper<WrongRecord>()
                    .eq("wrong_id", wrong.getId()));
            if (!CollectionUtils.isEmpty(recordList)){
                wrong.setRecordList(recordList);
            }
            Wrong pullData = getPullData(wrong);
            BeanUtils.copyProperties(pullData, wrong);
            if ("ARR".equals(wrong.getType())){
                String s = pickUpWaybillMapper.selectReportPayTime(wrong.getTallyId(), wrong.getWaybillCode());
                if (StringUtils.isNotEmpty(s)){
                    wrong.setIsOrder(1);
                }else {
                    wrong.setIsOrder(0);
                }
            }
        }
        return wrongs;
    }

    /**
     * 查询当前登录人的最父级部门
     * @return 最父级部门信息
     */
    public SysDept getParentDeptByDeptId() {
        Long deptId = SecurityUtils.getHighParentId();
        return deptMapper.selectDeptById(deptId);
    }

    /**
     * 新增不正常货邮
     * @param wrong 新增数据
     * @return 结果
     */
    @Override
    public int add(Wrong wrong) {
        HttpServletResponse response = ServletUtils.getResponse();
        //运单日志的新增
        WaybillLog waybillLog = waybillLogService.getWaybillLog(
                wrong.getWaybillCode(), 1, SecurityUtils.getNickName(),
                null, null, null,
                wrong, null, 0, null, new Date(),
                "标记不正常,不正常类型:" + wrong.getWrongType(), wrong.getType(), null);
        try {
            if (!CollectionUtils.isEmpty(wrong.getRecordList())) {
                for (WrongRecord wrongRecord : wrong.getRecordList()) {
                    wrongRecordMapper.insert(wrongRecord);
                }
            }
            AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                    .eq("waybill_code", wrong.getWaybillCode())
                    .eq("type", wrong.getType()));
            if("2".equals(wrong.getProMethod())){
                wrong.setStatus(3);
                setExitDate(wrong, response, waybillLog, airWaybill);
            }
            Long deptId = airWaybill.getDeptId();
            wrong.setDeptId(deptId);
            SysDept parentDept = getParentDeptByDeptId();
            wrong.setCreateBy(parentDept.getDeptName());
            int insert = wrongMapper.insert(wrong);

            sendMessage(wrong.getWaybillCode(),"新增不正常货物",wrong.getAgent(),deptId);
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作成功" +  "," +
                            "code:" + response.getStatus() + "," +
                            "data:" + insert));
            return insert;
        } catch (Exception e) {
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" +  "," +
                            "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        } finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }

    @Async
    public void sendMessage(String waybillCode, String s, String agentCompany,Long deptId) {
        String message = "    运单"+waybillCode+"不正常货邮"+s+"  \n" +
                "运单："+ waybillCode +"不正常，不支持类型"+ s + "请及时处理。";
        SocketMessageVo vo = new SocketMessageVo();
        vo.setMessage(message);
        vo.setType(6);
        vo.setDeptId(deptId);

        Message mes = new Message();
        mes.setContent(message);
        mes.setCreateTime(new Date());
        mes.setIsHandle(0);
        mes.setPostId(SecurityUtils.getDeptId());
        mes.setTitle("不正常货邮提醒");
        messageMapper.insert(mes);
        waybillMessageProducer.send(vo);
    }

    @Async
    public void sendMessageForHandle(String waybillCode, String s, String type, String proMethod) {
        String message = "    运单"+waybillCode+"不正常货邮"+s+"  \n" +
                "运单："+ waybillCode +"不正常，不支持类型"+ s + "请及时处理。"
                +"代理人选择处理方式：" +
                (proMethod.equals("0")?"继续运输":
                        proMethod.equals("1")?"换单":
                                proMethod.equals("2")?"退货":"其他")
                + "请及时处理";
        SocketMessageVo vo = new SocketMessageVo();
        vo.setMessage(message);
        if(type.equals("DEP")){
            vo.setType(7);
        } else if (type.equals("ARR")) {
            vo.setType(8);
        }
        Message mes = new Message();
        mes.setContent(message);
        mes.setCreateTime(new Date());
        mes.setIsHandle(0);
        mes.setPostId(SecurityUtils.getDeptId());
        mes.setTitle("不正常货邮处理");
        messageMapper.insert(mes);
        waybillMessageProducer.send(vo);
    }

    /**
     * 获取不正常货邮详情
     * @param id 不正常货邮id
     * @return 不正常货邮详情
     */
    @Override
    public Wrong getInfo(Long id) {
        Wrong wrong = wrongMapper.selectById(id);
        List<WrongRecord> recordList = wrongRecordMapper.selectList(new QueryWrapper<WrongRecord>()
                .eq("wrong_id", wrong.getId()));
        if (!CollectionUtils.isEmpty(recordList)){
            wrong.setRecordList(recordList);
        }
        return getPullData(wrong);
    }

    /**
     * 不正常货邮与拉下数据
     * */
    private Wrong getPullData(Wrong wrong){
        String waybillCode = wrong.getWaybillCode();
        AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                .eq("waybill_code", waybillCode)
                .eq("type", wrong.getType()));
        if(airWaybill == null){
            return wrong;
        }
        //收货人
        if (airWaybill != null){
            if (airWaybill.getPayStatus() == 0) {
                wrong.setPayStatus(0);
            } else {
                wrong.setPayStatus(1);
            }
        }
        wrong.setConsign(Optional.ofNullable(airWaybill).orElseGet(AirWaybill::new).getConsign());
        if (StringUtils.isNotNull(wrong.getPullId())){
            HzDepPullDown hzDepPullDown = pullDownMapper.selectById(wrong.getPullId());
            if(hzDepPullDown != null) {
                wrong.setPullTime(hzDepPullDown.getOperTime());
                if (StringUtils.isNull(wrong.getPullQuantity()) && StringUtils.isNull(wrong.getPullWeight())) {
                    wrong.setPullQuantity(hzDepPullDown.getQuantity());
                    wrong.setPullWeight(hzDepPullDown.getWeight());
                }
                if(airWaybill != null && hzDepPullDown.getFlightIdNew() != null){
                    List<FlightLoad> flightLoadList = flightLoadMapper.selectList(new QueryWrapper<FlightLoad>()
                            .eq("flight_id", hzDepPullDown.getFlightIdNew()));
                    List<FlightLoad> beenDep = flightLoadList.stream().filter(e -> ("been_dep,been_weight").contains(e.getState())).collect(Collectors.toList());
                    if(CollectionUtils.isEmpty(beenDep)){
                        return wrong;
                    }
                    List<LoadInfoVo> infoVos = loadWaybillMapper.selectByLoadInfo(airWaybill.getId());
                    List<LoadInfoVo> collect = infoVos.stream().filter(e -> e.getFlightId().equals(hzDepPullDown.getFlightIdNew())).collect(Collectors.toList());
                    wrong.setPullDepQuantity(collect.stream().mapToInt(LoadInfoVo::getQuantity).sum());
                    wrong.setPullDepWeight(collect.stream().map(LoadInfoVo::getWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
                    wrong.setPullDepTime(beenDep.get(0).getUpdateTime());
                }
            }
        }

        //查询运单的理货数据
        LambdaQueryWrapper<HzArrTally> tallyLqw = Wrappers.<HzArrTally>lambdaQuery()
                .eq(HzArrTally::getWaybillCode, waybillCode);
        List<HzArrTally> tallyList = hzArrTallyMapper.selectList(tallyLqw);
        if (CollectionUtil.isNotEmpty(tallyList)) {
            HzArrTally hzArrTally = tallyList.get(0);
            wrong.setCabinWeight(hzArrTally.getCabinWeight());
            wrong.setCabinPieces(hzArrTally.getCabinPieces());
            int tallyPiecesSum = tallyList.stream().mapToInt(HzArrTally::getPieces).sum();
            wrong.setActualPieces(tallyPiecesSum);
            BigDecimal reduce = tallyList.stream().map(HzArrTally::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            wrong.setActualWeight(reduce);
            wrong.setNoArrivePieces(airWaybill.getQuantity() - tallyPiecesSum);
            wrong.setLocator(hzArrTally.getLocator());
            wrong.setStore(hzArrTally.getStore());
        }
        return wrong;
    }

    /**
     * 选择处理方式
     * @param wrong 处理方式参数
     * @return 结果
     */
    @Override
    public int method(Wrong wrong) {
        //代理人处理之后默认为货站处理 12.16
        wrong.setStatus(2);
        //处理方式继续运输的不正常货邮才可以修改为换单或者退货 换单或者退货的不可修改
        Wrong oldWrong = wrongMapper.selectById(wrong.getId());
        if(oldWrong.getProMethod() != null &&
                !"0".equals(oldWrong.getProMethod()) &&
                !wrong.getProMethod().equals(oldWrong.getProMethod())){
            throw new CustomException("不可再修改处理方式");
        }
        HzDepPullDown hzDepPullDown = new HzDepPullDown();
        if(wrong.getPullId()!=null){
            hzDepPullDown = pullDownMapper.selectById(wrong.getPullId());
            if(hzDepPullDown.getIsLoad()==1 && !"0".equals(wrong.getProMethod())){
                throw new CustomException("拉下货物已经配载");
            }

            //代理人选择换单
//            if("1".equals(wrong.getProMethod())){
//                WaybillChangeWrongData waybillChangeWrongData = new WaybillChangeWrongData();
//                waybillChangeWrongData.setType(4);
//                waybillChangeWrongData.setWaybillCodeNew(wrong.getWaybillCodeNew().substring(4));
//                waybillChangeWrongData.setWaybillCode(wrong.getWaybillCode().substring(4));
//                waybillChangeWrongData.setQuantity(wrong.getChangeQuantity());
//                waybillChangeWrongData.setWeight(wrong.getChangeWeight());
//                securityProducer.sendOtherWaybill(waybillChangeWrongData,wrong.getWaybillCode(),"代理人不正常货邮选择换单");
//            }
        }
        if ("2".equals(wrong.getProMethod())){
            HttpServletResponse response = ServletUtils.getResponse();
            WaybillLog waybillLog = waybillLogService.getWaybillLog(
                    wrong.getWaybillCode(), 1, SecurityUtils.getNickName(),
                    null, null, null,
                    wrong, null, 0, null, new Date(),
                    "标记不正常,不正常类型:" + wrong.getWrongType(), wrong.getType(), null);
            wrong.setStatus(3);
            AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                    .eq("waybill_code", wrong.getWaybillCode())
                    .eq("type", wrong.getType()));
            setExitDate(wrong, response, waybillLog, airWaybill);

            //货运系统选择退货 推送数据
            WaybillExitData waybillExitData = new WaybillExitData();
            waybillExitData.setType(5);
            waybillExitData.setWaybillCode(wrong.getWaybillCode().substring(4));
            waybillExitData.setQuantity(hzDepPullDown.getQuantity());
            waybillExitData.setWeight(hzDepPullDown.getWeight());
            if(Objects.equals(hzDepPullDown.getQuantity(),airWaybill.getQuantity()) && Objects.equals(hzDepPullDown.getWeight(),airWaybill.getWeight())){
                waybillExitData.setIsAllReturn(1);
            }else{
                waybillExitData.setIsAllReturn(0);
            }
            securityProducer.sendOtherWaybill(waybillExitData, wrong.getWaybillCode(), "货运系统完成退单");
        }


        //运单日志的新增
        HttpServletResponse response = ServletUtils.getResponse();
        WaybillLog waybillLog = waybillLogService.getWaybillLog(
                wrong.getWaybillCode(), 2, SecurityUtils.getNickName(),
                null, null, null,
                wrong, null, 0, null, new Date(),
                "不正常选择处理方式,处理方式:" +
                        (("0".equals(wrong.getProMethod())) ? "继续运输"
                        : ("1".equals(wrong.getProMethod())) ? "换单"
                        : ("2".equals(wrong.getProMethod())) ? "退货"
                        : "其他"),
                wrong.getType(), null);
        try {
            //往不正常货邮的处理记录表新增处理记录
            WrongRecord wrongRecord = new WrongRecord();
            wrongRecord.setWrongId(wrong.getId());
            wrongRecord.setRecordTime(new Date());
            wrongRecord.setRecordRemark(
                    ("0".equals(wrong.getProMethod())) ? "继续运输"
                            : ("1".equals(wrong.getProMethod())) ? "换单"
                            : ("2".equals(wrong.getProMethod())) ? "退货"
                            : "其他"
            );
            wrongRecord.setUsername(SecurityUtils.getUsername());
            wrongRecordMapper.insert(wrongRecord);
            int i = wrongMapper.updateById(wrong);
            if(wrong.getProMethod()==null){
                wrong.setProMethod("其他");
            }
            sendMessageForHandle(wrong.getWaybillCode(),wrong.getWrongType(),wrong.getType(),wrong.getProMethod());
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作成功" +  "," +
                            "code:" + response.getStatus() + "," +
                            "data:" + i));
            return i;
        } catch (Exception e) {
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" +  "," +
                            "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        } finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }

    private void setExitDate(Wrong wrong, HttpServletResponse response, WaybillLog waybillLog, AirWaybill airWaybill) {
        List<HzCollectWaybill> collectWaybills = collectWaybillMapper.selectList(new QueryWrapper<HzCollectWaybill>()
                .eq("waybill_id", airWaybill.getId()));
        if(CollectionUtils.isEmpty(collectWaybills)){
            throw new CustomException("该运单无收运记录,不可申请退货");
        }
        List<Wrong> wrongs = wrongMapper.selectList(new QueryWrapper<Wrong>()
                .eq("waybill_code", airWaybill.getWaybillCode())
                .eq("pro_method", "2")
                .in("status", "0,1,2"));
        if ("INVALID".equals(airWaybill.getStatus())){
            throw new CustomException("该运单已作废");
        }
        if (wrongs.size() > 0) {
            throw new CustomException("该运单已存在退货记录,请勿重复申请");
        }
        Integer count = pullDownMapper.selectIsSettle(airWaybill.getWaybillCode());
        if (count != 0) {
            throw new CustomException("该运单未结算，请在明天12点后，操作退货。");
        }
        if (wrong.getIsReturnCargo() != null && wrong.getIsReturnCargo() != 0) {
            String waybillCode = wrong.getWaybillCode();
            BigDecimal chargeWeight = airWaybill.getChargeWeight() == null ? new BigDecimal(0) : airWaybill.getChargeWeight();
            BigDecimal actualWeight = airWaybill.getWeight() == null ? new BigDecimal(0) : airWaybill.getWeight();
            if (airWaybill.getSwitchBill() == 2) {
                waybillCode = airWaybill.getOriginBill();
                Mawb mawb = mawbMapper.selectWeight(airWaybill.getOriginBill(), airWaybill.getDeptId());
                if (mawb != null) {
                    chargeWeight = mawb.getChargeWeight() == null ? new BigDecimal(0) : mawb.getChargeWeight();
                    actualWeight = mawb.getWeight() == null ? new BigDecimal(0) : mawb.getWeight();
                }
            }
            // 预授权支付费用
            List<CostDetail> detailList = costDetailMapper.depAutoSettleTask(waybillCode, wrong.getDeptId());
            BigDecimal oldCostSum = new BigDecimal(0);
            if (!CollectionUtils.isEmpty(detailList)) {
                oldCostSum = detailList.stream().filter(e -> (e.getIsSettle() == 1 && "处置费".equals(e.getChargeAbb()))).map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            // 获取运单之前所有已结算费用和
            BigDecimal settleCostSum = new BigDecimal(0);
            List<CostDetail> settleList = costDetailMapper.selectIsSettle(waybillCode, wrong.getDeptId());
            if (!CollectionUtils.isEmpty(settleList)) {
                settleCostSum = settleList.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            // 运单之前所有的结算重量之和
            List<Long> list = costDetailMapper.selectFlights(waybillCode, wrong.getDeptId());
            BigDecimal allLoadWeight = new BigDecimal(0);
            if (!CollectionUtils.isEmpty(list)) {
                List<FlightLoadWaybill> loadWeights = loadWaybillMapper.selectSettleWeight(airWaybill.getId(), list);
                if (!CollectionUtils.isEmpty(loadWeights)) {
                    allLoadWeight = loadWeights.stream().map(FlightLoadWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                }
            } else {
                allLoadWeight = new BigDecimal(0);
            }
            int quantity = 0;
            BigDecimal weight = new BigDecimal(0);
            HzDepPullDown hzDepPullDown = pullDownMapper.selectById(wrong.getPullId());
            if (hzDepPullDown != null) {
                quantity = hzDepPullDown.getQuantity();
                weight = hzDepPullDown.getWeight();
            }
            List<HzDisBoard> disBoardList = disBoardMapper.selectList(new QueryWrapper<HzDisBoard>()
                    .eq("waybill_id", airWaybill.getId())
                    .eq("is_handle", 0).eq("is_load", 0));
            if (!CollectionUtils.isEmpty(disBoardList)) {
                quantity += disBoardList.stream().mapToInt(HzDisBoard::getQuantity).sum();
                weight = weight.add(disBoardList.stream().map(HzDisBoard::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            List<LoadInfoVo> infoVos = loadWaybillMapper.selectByLoadInfo(airWaybill.getId());
            for (LoadInfoVo infoVo : infoVos) {
                FlightInfo info = infoMapper.selectFlightNo(infoVo.getLoadId());
                if (info.getStartRealTakeoffTime() == null) {
                    throw new CustomException("该运单在配载中不可申请退货");
                }
            }
            BigDecimal weightRate;
            if (actualWeight == null || actualWeight.compareTo(new BigDecimal(0)) == 0) {
                weightRate = new BigDecimal(0);
            } else {
                BigDecimal bigDecimal = chargeWeight.divide(actualWeight, 5, RoundingMode.DOWN).multiply(weight);
                weightRate = bigDecimal.setScale(0, RoundingMode.FLOOR);
            }
            // 出港处置费
            List<CostDetail> handleSum = detailList.stream().filter(e -> "处置费".equals(e.getChargeAbb()) && e.getIsDel() == 0).collect(Collectors.toList());
            BaseCargoCode baseCargoCode = cargoCodeMapper.selectByCode(airWaybill.getCargoCode());
            List<HzChargeItems> hzChargeItems = itemsMapper.selectList(new QueryWrapper<HzChargeItems>()
                    .eq("operation_type", "DEP")
                    .eq("is_default", 1).eq("status", 1)
                    .le("start_effective_time", new Date())
                    .ge("end_effective_time", new Date())
                    .eq("is_del", 0));

            SysConfig sysConfig = sysConfigMapper.selectConfigById(16L);
            LocalTime startTime;
            LocalTime endTime;
            if (airWaybill.getFlightDate1() == null) {
                startTime = LocalTime.of(6, 0, 0);
                endTime = LocalTime.of(6, 0, 1);
            } else {
                Instant instant = airWaybill.getFlightDate1().toInstant();
                LocalDateTime takeoffTime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
                LocalDateTime time = takeoffTime.plusSeconds(1);
                endTime = time.toLocalTime();
                startTime = takeoffTime.toLocalTime();
            }
            Date date = new Date();
            Instant startInstant = date.toInstant();
            long aLong = Long.parseLong(sysConfig.getConfigValue());
            long times = aLong * 60 * 60;
            Instant endInstant = startInstant.plusSeconds(times);
            Date storeEndTime = Date.from(endInstant);
            BigDecimal exitSum = new BigDecimal(0);
            for (HzChargeItems hzChargeItem : hzChargeItems) {
                List<HzChargeIrRelation> relations = relationMapper.selectList(new QueryWrapper<HzChargeIrRelation>().eq("item_id", hzChargeItem.getId()).eq("is_del", 0));
                int maxMatchCount = 0;
                List<HzChargeIrRelation> ruleList = new ArrayList<>();
                for (HzChargeIrRelation hzChargeRule : relations) {
                    if (hzChargeRule.getIsExit() != 1) {
                        continue;
                    }
                    if (!hzChargeRule.getIsSouth().equals(airWaybill.getIsSouth())) {
                        continue;
                    }
                    if (StringUtils.isNotEmpty(hzChargeRule.getNoCharge()) && !hzChargeRule.getNoCharge().contains(airWaybill.getDeptId().toString())) {
                        continue;
                    }
                    if (StringUtils.isNotEmpty(hzChargeRule.getPrefix()) && !hzChargeRule.getPrefix().contains(airWaybill.getWaybillCode().substring(4, 7))) {
                        continue;
                    }
                    if (StringUtils.isNotEmpty(hzChargeRule.getCategory()) && !hzChargeRule.getCategory().contains(baseCargoCode.getCategoryCode())) {
                        continue;
                    }
                    if (!hzChargeRule.getCrossAir().equals(airWaybill.getCrossAir())) {
                        continue;
                    }
                    int matchCount = 0;
                    // 根据判断货品代码
                    int cargoMatchCount = isCargoCodeMatch(hzChargeRule, airWaybill.getCargoCode());
                    if (cargoMatchCount >= 0) {
                        matchCount += cargoMatchCount;
                    }
                    if (matchCount > 0) {
                        if (matchCount > maxMatchCount) {
                            maxMatchCount = matchCount;
                            ruleList.clear();
                            ruleList.add(hzChargeRule);
                        } else if (matchCount == maxMatchCount) {
                            ruleList.add(hzChargeRule);
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(ruleList)) {
                    HzChargeIrRelation relation = ruleList.stream().max(Comparator.comparing(HzChargeIrRelation::getPriority)).orElse(null);
                    if (relation != null) {
                        HzChargeRule rule1 = ruleMapper.selectById(relation.getRuleId());
                        List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>().eq("ir_id", relation.getId()));
                        if ("ColdStorageBillingRule.class".equals(rule1.getClassName()) && StringUtils.isEmpty(airWaybill.getColdStore())) {
                            continue;
                        }
                        CostDetail detail = new CostDetail();
                        detail.setWaybillCode(airWaybill.getWaybillCode());
                        detail.setIrId(relation.getId());
                        detail.setColdStore(airWaybill.getColdStore());
                        detail.setUnit(1);
                        detail.setType(1);
                        detail.setIsSettle(1);
                        detail.setSmallItem(1);
                        detail.setLargeItem(1);
                        detail.setSuperLargeItem(1);
                        detail.setFlightId(1L);
                        detail.setSettleDepQuantity(quantity);
                        detail.setSettleDepWeight(weight);
                        detail.setStartTime(startTime);
                        detail.setEndTime(endTime);
                        detail.setDaysInStorage(1.0);
                        detail.setStoreStartTime(date);
                        detail.setStoreEndTime(storeEndTime);
                        detail.setPointTime(startTime);
                        detail.setCreateTime(new Date());
                        BillingRule rule = BillingRuleFactory.createRule(rule1.getClassName());
                        BillRuleVo vo1 = rule.calculateFee(itemRules, weightRate, quantity, detail);
                        BigDecimal totalCharge = BigDecimalRoundUtils.bigDecimalRound(hzChargeItem.getRoundRule(), vo1.getTotalCharge());
                        detail.setTotalCharge(totalCharge);
                        detail.setDeptId(airWaybill.getDeptId());
                        detail.setQuantity(vo1.getQuantity());
                        detail.setRate(vo1.getRate());
                        exitSum = exitSum.add(totalCharge);
                        costDetailMapper.insert(detail);
                    }
                }
            }
            BigDecimal costSum = new BigDecimal(0);
            // 剩余费用
            BigDecimal remain = oldCostSum.subtract(settleCostSum);
            if (airWaybill.getWeight().compareTo(allLoadWeight.add(weight)) <= 0) {
                costSum = remain.subtract(exitSum);
                costSum = costSum.compareTo(new BigDecimal(0)) < 0 ? new BigDecimal(0) : costSum;
                for (CostDetail detail : handleSum) {
                    BillRuleVo ruleVo = countCost(detail, weightRate, quantity);
                    detail.setQuantity(ruleVo.getQuantity());
                    detail.setFlightId(1L);
                    detail.setSettleDepQuantity(quantity);
                    detail.setSettleDepWeight(weight);
                    detail.setIsSettle(1);
                    detail.setType(1);
                    detail.setCreateTime(new Date());
                    detail.setTotalCharge(costSum);
                    detail.setRate(ruleVo.getRate());
                    detail.setId(null);
                    costDetailMapper.insert(detail);
                }
            } else {
                for (CostDetail detail : handleSum) {
                    BillRuleVo ruleVo = countCost(detail, weightRate, quantity);
                    if (ruleVo.getTotalCharge().compareTo(remain) > 0) {
                        BigDecimal subtract = remain.subtract(exitSum);
                        subtract = subtract.compareTo(new BigDecimal(0)) < 0 ? new BigDecimal(0) : subtract;
                        detail.setTotalCharge(subtract);
                        costSum = costSum.add(remain);
                    } else {
                        BigDecimal subtract = ruleVo.getTotalCharge().subtract(exitSum);
                        subtract = subtract.compareTo(new BigDecimal(0)) < 0 ? new BigDecimal(0) : subtract;
                        detail.setTotalCharge(subtract);
                        costSum = costSum.add(ruleVo.getTotalCharge());
                    }
                    detail.setQuantity(ruleVo.getQuantity());
                    detail.setFlightId(1L);
                    detail.setSettleDepQuantity(quantity);
                    detail.setSettleDepWeight(weight);
                    detail.setIsSettle(1);
                    detail.setType(1);
                    detail.setCreateTime(new Date());
                    detail.setRate(ruleVo.getRate());
                    detail.setId(null);
                    costDetailMapper.insert(detail);
                }
                costSum = costSum.subtract(exitSum);
            }
            WaybillFee waybillFee = feeMapper.selectOne(new QueryWrapper<WaybillFee>()
                    .eq("waybill_code", airWaybill.getWaybillCode())
                    .eq("dept_id", airWaybill.getDeptId())
                    .eq("type", "DEP"));
            BaseAgent agent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", airWaybill.getDeptId()));
            if (agent != null) {
                if (agent.getSettleMethod() == 1) {
                    BigDecimal balance = agent.getBalance() == null ? new BigDecimal(0) : agent.getBalance();
                    BigDecimal subtract = balance.add(costSum);
                    agent.setBalance(subtract);
                    baseAgentMapper.updateBaseAgent(agent);
                    BaseBalance baseBalance = new BaseBalance();
                    baseBalance.setAgentId(agent.getId());
                    baseBalance.setBalance(agent.getBalance());
                    baseBalance.setType("增加余额");
                    baseBalance.setCreateTime(new Date());
                    baseBalance.setCreateBy("系统");
                    // todo 流水号需从银联支付接口获取
                    //baseBalance.setSerialNo();
                    baseBalance.setTradeMoney(costSum);
                    baseBalance.setWaybillCode(airWaybill.getWaybillCode());
                    if (wrong.getIsReturnCargo() == 1) {
                        baseBalance.setRemark("运单退货退款");
                    } else {
                        baseBalance.setRemark("不正常货邮退款");
                    }
                    baseBalanceMapper.insertBaseBalance(baseBalance);
                    updateStatus(airWaybill, costSum, waybillFee, 10, costSum);
                } else if (agent.getSettleMethod() == 0) {
                    updateStatus(airWaybill, costSum, waybillFee, 9, costSum);
                } else {
                    if (agent.getPayMethod() == 0) {
                        updateStatus(airWaybill, costSum, waybillFee, 11, costSum);
                    } else {
                        updateStatus(airWaybill, costSum, waybillFee, 12, costSum);
                    }
                }
            } else {
                updateStatus(airWaybill, costSum, waybillFee, 12, costSum);
            }

            //选择退货退款才会新增退货 否则仅退款
            if (wrong.getIsReturnCargo() == 1) {
                WaybillTrace waybillTrace = new WaybillTrace();
                waybillTrace.setOperTime(new Date());
                waybillTrace.setOperPieces(quantity);
                waybillTrace.setOperWeight(weight);
                waybillTrace.setWaybillCode(wrong.getWaybillCode());
                waybillTrace.setNodeName("已退货");
                traceService.insertWaybillTrace(waybillTrace);

                HzDepExitCargo hzDepExitCargo = new HzDepExitCargo();
                hzDepExitCargo.setWaybillCode(wrong.getWaybillCode());
                hzDepExitCargo.setExitQuantity(quantity);
                hzDepExitCargo.setExitWeight(weight);
                hzDepExitCargo.setStartTime(new Date());
                hzDepExitCargo.setStatus("agree_return");
                hzDepExitCargo.setOperName(SecurityUtils.getUsername());
                //退货管理的运单日志
                WaybillLog waybillLogForExitCargo = waybillLogService.getWaybillLog(
                        wrong.getWaybillCode(), 1, SecurityUtils.getNickName(),
                        hzDepExitCargo.getExitWeight().toString(), hzDepExitCargo.getExitQuantity().toString(), airWaybill.getFlightNo1(),
                        hzDepExitCargo, 1, 0, null, new Date(),
                        "退货,件数:" + hzDepExitCargo.getExitQuantity() + ",重量:" + hzDepExitCargo.getExitWeight() + "金额:" + costSum,
                        airWaybill.getType(), null);
                try {
                    exitCargoMapper.insert(hzDepExitCargo);
                } catch (Exception e) {
                    waybillLog.setJsonResult(waybillLogService.getJson(
                            "msg:" + "操作失败" + "," +
                                    "code:" + response.getStatus()));
                    waybillLog.setErrorMsg(e.getMessage());
                    waybillLog.setStatus(1);
                    throw new CustomException(e.getMessage());
                } finally {
                    waybillLogService.insertWaybillLog(waybillLogForExitCargo);
                }
            }

        }
        if (wrong.getPullId() != null) {
            HzDepPullDown hzDepPullDown = pullDownMapper.selectById(wrong.getPullId());
            hzDepPullDown.setIsHandle(1);
            hzDepPullDown.setIsLoad(1);
            pullDownMapper.updateById(hzDepPullDown);
        }
        if (wrong.getDisBoardIds() != null) {
            String disBoardIds = wrong.getDisBoardIds();
            List<HzDisBoard> disBoardList = disBoardMapper.selectBatchIds(Arrays.asList(disBoardIds.split(",")));
            for (HzDisBoard hzDisBoard : disBoardList) {
                hzDisBoard.setIsHandle(1);
                hzDisBoard.setIsLoad(1);
                disBoardMapper.updateById(hzDisBoard);
            }
        }
    }

    /**
     * 处理
     * @param wrong 处理数据
     * @return 结果
     */
    @Override
    public int handle(Wrong wrong) {
        HttpServletResponse response = ServletUtils.getResponse();
        //运单日志的新增
        WaybillLog waybillLog = waybillLogService.getWaybillLog(
                wrong.getWaybillCode(), 2, SecurityUtils.getNickName(),
                null, null, null,
                wrong, null, 0, null, new Date(),
                "不正常选择处理方式,处理方式:" +
                        (("0".equals(wrong.getProMethod())) ? "继续运输"
                                : ("1".equals(wrong.getProMethod())) ? "换单"
                                : ("2".equals(wrong.getProMethod())) ? "退货"
                                : "其他"),
                wrong.getType(), null);
        try {
            //货站处理之后,将状态改为处理结束
            wrong.setStatus(3);
            Integer isPickUp = wrong.getIsPickUp();
            if(Objects.equals(isPickUp,1)){
                //设置为可办理提货的时间
                LambdaUpdateWrapper<HzArrTally> setIsPickUpTime = Wrappers.<HzArrTally>lambdaUpdate()
                        .eq(HzArrTally::getId, wrong.getTallyId())
                        .isNull(HzArrTally::getIsPickUpTime)
                        .set(HzArrTally::getIsPickUpTime, LocalDateTime.now());
                hzArrTallyMapper.update(null, setIsPickUpTime);
            }
            wrongMapper.updateById(wrong);
            if(wrong.getProMethod()==null){
                wrong.setProMethod("其他");
            }
            sendMessageForHandle(wrong.getWaybillCode(),wrong.getWrongType(),wrong.getType(),wrong.getProMethod());
            if (wrong.getPullId() != null){
                HzDepPullDown hzDepPullDown = pullDownMapper.selectById(wrong.getPullId());
                hzDepPullDown.setIsHandle(1);
//                hzDepPullDown.setIsLoad(1);
                pullDownMapper.updateById(hzDepPullDown);
            }
            if(wrong.getDisBoardIds() != null){
                String disBoardIds = wrong.getDisBoardIds();
                List<HzDisBoard> disBoardList = disBoardMapper.selectBatchIds(Arrays.asList(disBoardIds.split(",")));
                for (HzDisBoard hzDisBoard : disBoardList) {
                    hzDisBoard.setIsHandle(1);
                    hzDisBoard.setIsLoad(1);
                    disBoardMapper.updateById(hzDisBoard);
                }
            }
            WrongRecord record = new WrongRecord();
            if ("ARR".equals(wrong.getType())){
                record.setRecordRemark("是否可办理提货：" + (wrong.getIsPickUp() == 0 ? "否" : "是"));
            }else {
                record.setRecordRemark(wrong.getMethodRemark());
            }
            record.setRecordTime(new Date());
            record.setWrongId(wrong.getId());
            record.setUsername(SecurityUtils.getUsername());
            record.setCreateTime(new Date());
            int insert = wrongRecordMapper.insert(record);
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作成功" +  "," +
                            "code:" + response.getStatus() + "," +
                            "data:" + insert));
            return insert;
        } catch (Exception e) {
            throw new CustomException(e.getMessage());
        } finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }

    /**
     * 修改
     * @param wrong 处理数据
     * @return 结果
     */
    @Override
    public int update(Wrong wrong) {
        String createBy = wrongMapper.selectById(wrong.getId()).getCreateBy();
        SysDept parentDept = getParentDeptByDeptId();
        if(StringUtils.isNotNull(createBy)){
            if (!createBy.equals(parentDept.getDeptName())){
                throw new CustomException("没有修改权限");
            }
        }
        return wrongMapper.updateById(wrong);
    }

    /**
     * 根据运单号查询代理人 进出港类型等数据
     * @return 结果
     */
    @Override
    public Wrong selectAgent(String waybillCode) {
        List<Wrong> wrongs = wrongMapper.selectAgent(waybillCode);
        if (!CollectionUtils.isEmpty(wrongs)){
            Wrong wrong = wrongs.get(0);
            AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                    .eq("waybill_code", waybillCode)
                    .eq("type", "DEP"));
            Wrong wrongOld = pullDownMapper.selectOldWrong(airWaybill);
            if (wrongOld == null){
                return null;
            }
            wrong.setPullQuantity(wrongOld.getPullQuantity()==null?0:wrongOld.getPullQuantity());
            wrong.setPullWeight(wrongOld.getPullWeight()==null?BigDecimal.ZERO:wrongOld.getPullWeight());
            //将pullId赋值传过去给新的不正常货邮数据
            Wrong wrongPull = wrongMapper.selectOne(new QueryWrapper<Wrong>()
                    .eq("waybill_code", waybillCode)
                    .orderByDesc("register_time")
                    .last("limit 1"));
            if(wrongPull!=null && wrongPull.getPullId()!=null){
                wrong.setPullId(wrongPull.getPullId());
            }
            List<HzDisBoard> disBoardList = disBoardMapper.selectList(new QueryWrapper<HzDisBoard>()
                    .eq("waybill_id", airWaybill.getId())
                    .eq("is_load", 0));
            if(!CollectionUtils.isEmpty(disBoardList)){
                String disBoardListIds = disBoardList.stream().map(HzDisBoard::getId).map(String::valueOf)
                        .collect(Collectors.joining(","));
                wrong.setDisBoardIds(disBoardListIds);
            }
            return wrong;
        }
        return null;
    }

    /**
     * 根据后四位或者后八位查询运单号
     * */
    @Override
    public List<String> getWaybillCodeByFour(String waybillCode,Long deptId) {
        int length = waybillCode.length();
        return wrongMapper.getWaybillCodeByFour(waybillCode,length,deptId);
    }

    /**
     * 货站修改
     * @param wrong 修改数据
     * @return 结果
     */
    @Override
    public int hzUpdate(Wrong wrong) {
        //货站处理完之后 默认处理结束
        if(wrong.getStatus() == 3){
            wrong.setIsOver(1);
        }
        return wrongMapper.updateById(wrong);
    }

    /**
     * 是否可以退货 物流不用判断航班是否结算 货站需要判断
     * @param waybillCode 运单号
     * @return 结果
     */
    @Override
    public int isExitSettle(String waybillCode) {
        List<Long> waybillIds = new ArrayList<>();
        Mawb mawb = mawbMapper.selectIdByCode(waybillCode);
        waybillIds.add(mawb.getId());
        if (StringUtils.isNotEmpty(mawb.getOriginBill())){
            Mawb originBill = mawbMapper.selectIdByCode(mawb.getOriginBill());
            waybillIds.add(originBill.getId());
        }
        List<Long> flightIds = loadWaybillMapper.selectFlightIds(waybillIds);
        if (!CollectionUtils.isEmpty(flightIds)){
            List<FlightInfo> infos = infoMapper.selectList(new QueryWrapper<FlightInfo>().in("flight_id", flightIds));
            List<FlightInfo> noSettle = infos.stream().filter(e -> e.getIsSettle() == 0).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(noSettle)){
                throw new CustomException("存在未结算航班");
            }
        }
        return 1;
    }

    @Override
    public int isExitPull(String waybillCode) {
        List<HzDepPullDown> pullDowns = pullDownMapper.selectList(new QueryWrapper<HzDepPullDown>()
                .eq("waybill_code", waybillCode)
                .eq("is_handle", 0));
        AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                .eq("waybill_code", waybillCode)
                .last("limit 1"));
        if (airWaybill == null){
            throw new CustomException("无当前运单信息");
        }
        List<HzDisBoard> disBoardList = disBoardMapper.selectList(new QueryWrapper<HzDisBoard>()
                .eq("waybill_id", airWaybill.getId())
                .eq("is_handle", 0));
        if (CollectionUtils.isEmpty(pullDowns) && CollectionUtils.isEmpty(disBoardList)){
            throw new CustomException("当前运单无拉下未处理数据");
        }
        Integer count = pullDownMapper.selectIsSettle(airWaybill.getWaybillCode());
        if (count != 0){
            throw new CustomException("该运单未结算，请在明天12点后，操作退货。");
        }
        return 1;
    }

    public BillRuleVo countCost(CostDetail detail, BigDecimal weightRate, Integer quantity) {
        HzChargeIrRelation relation = relationMapper.selectById(detail.getIrId());
        BillRuleVo vo = new BillRuleVo();
        if (relation == null){
            vo.setTotalCharge(new BigDecimal(0));
            return vo;
        }
        HzChargeItems hzChargeItem = itemsMapper.selectById(relation.getItemId());
        HzChargeRule hzChargeRule = ruleMapper.selectById(relation.getRuleId());
        List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>().eq("ir_id", detail.getIrId()));
        if (CollectionUtils.isEmpty(itemRules)){
            vo.setTotalCharge(new BigDecimal(0));
            return vo;
        }
        BillingRule rule = BillingRuleFactory.createRule(hzChargeRule.getClassName());
        BillRuleVo ruleVo = rule.calculateFee(itemRules, weightRate, quantity, detail);
        BigDecimal totalCharge = BigDecimalRoundUtils.bigDecimalRound(hzChargeItem.getRoundRule(), ruleVo.getTotalCharge());
        ruleVo.setTotalCharge(totalCharge);
        return ruleVo;
    }

    private void updateStatus(AirWaybill airWaybill, BigDecimal costSum, WaybillFee waybillFee, Integer payStatus, BigDecimal refund) {
        if (waybillFee != null) {
            waybillFee.setRefund(costSum);
            waybillFee.setRefund(refund);
            waybillFee.setStatus(1);
            feeMapper.updateById(waybillFee);
        } else {
            WaybillFee fee = new WaybillFee();
            fee.setSettleMoney(costSum);
            fee.setRefund(refund);
            fee.setSettleTime(new Date());
            fee.setWaybillCode(airWaybill.getWaybillCode());
            fee.setDeptId(airWaybill.getDeptId());
            fee.setStatus(1);
            fee.setType("DEP");
            feeMapper.insert(fee);
        }
        airWaybill.setRefund(refund);
        airWaybill.setPayStatus(payStatus);
        airWaybill.setSettleTime(new Date());
        airWaybillMapper.updateById(airWaybill);
    }

    private int isCargoCodeMatch(HzChargeIrRelation hzChargeRule, String waybillCargoCode) {
        if (StringUtils.isEmpty(hzChargeRule.getCargoName())) {
            return 1;
        }else if (StringUtils.isEmpty(hzChargeRule.getCategory())){
            if (hzChargeRule.getCargoName().contains(waybillCargoCode)){
                return 1;
            }
            return 0;
        }
        List<IrRelationVo> relationVos = Arrays.stream(hzChargeRule.getCargoName().split(","))
                .map(cargoCodeMapper::selectIrByName)
                .collect(Collectors.toList());

        List<IrRelationVo> cargoNames = Arrays.stream(hzChargeRule.getCategory().split(","))
                .flatMap(category -> cargoCodeMapper.selectListByCategory(Collections.singletonList(category)).stream())
                .collect(Collectors.toList());

        Map<String, List<IrRelationVo>> relationVosMap = relationVos.stream()
                .collect(Collectors.groupingBy(IrRelationVo::getCategoryCode));

        List<IrRelationVo> combinedList = new ArrayList<>();
        Set<String> addedCodes = new HashSet<>();
        for (IrRelationVo vo : cargoNames) {
            String code = vo.getCategoryCode();
            if (relationVosMap.containsKey(code) && !addedCodes.contains(code)) {
                combinedList.addAll(relationVosMap.get(code));
                addedCodes.add(code);
            } else if (!addedCodes.contains(code)) {
                combinedList.add(vo);
            }
        }
        List<String> collect1 = combinedList.stream().map(IrRelationVo::getCode).collect(Collectors.toList());
        if (collect1.stream().anyMatch(cargoCode -> cargoCode.equals(waybillCargoCode))) {
            String category = hzChargeRule.getCategory();
            StringBuilder stringBuffer = new StringBuilder();
            if(category != null){
                String[] split = category.split(",");
                for (String categoryCode:split) {
                    List<BaseCargoCode> cargoCodeList = cargoCodeMapper.selectList(new QueryWrapper<>(new BaseCargoCode())
                            .eq("category_code", categoryCode)
                            .eq("is_del",0));
                    if(cargoCodeList!=null && cargoCodeList.size() > 0){
                        for (BaseCargoCode e:cargoCodeList) {
                            stringBuffer.append(e.getCode());
                        }
                    }
                }
            }else{
                return 1;
            }
            if (stringBuffer.toString().contains(waybillCargoCode)) {
                return 2;
            } else {
                return 1;
            }
        }
        return 0;
    }
}
