package com.gzairports.wl.charge.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.BaseBillType;
import com.gzairports.common.basedata.mapper.BillTypeMapper;
import com.gzairports.wl.charge.domain.ChargeItem;
import com.gzairports.wl.charge.domain.query.ChargeItemQuery;
import com.gzairports.wl.charge.domain.vo.ChargeItemVO;
import com.gzairports.wl.charge.mapper.ChargeItemMapper;
import com.gzairports.wl.charge.service.ChargeItemService;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 收费项目service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@Service
public class ChargeItemServiceImpl extends ServiceImpl<ChargeItemMapper,ChargeItem> implements ChargeItemService {
    
    @Autowired
    private ChargeItemMapper itemMapper;

    @Autowired
    private BillTypeMapper billTypeMapper;

    /**
     * 查询收费项目列表
     *
     * @param query 查询参数
     * @return 收费项目列表
     */
    @Override
    public List<ChargeItemVO> selectList(ChargeItemQuery query) {
        //查询票证类型并存入集合,仅查询一次
        if (DOCUMENTMAP.isEmpty()) {
            selectTypeList();
        }
        query.setDeptId(SecurityUtils.getHighParentId());
        List<ChargeItemVO> list = itemMapper.selectListByQuery(query);
        for (ChargeItemVO chargeItemVO : list) {
            chargeItemVO.setIsEdit(chargeItemVO.getEditable() == 0 ? "否" : "是");
            chargeItemVO.setIsChargeable(chargeItemVO.getDefaultChargeable() == 0 ? "否" : "是");
            chargeItemVO.setAppDocuments(DOCUMENTMAP.get(chargeItemVO.getApplicableDocuments()));
            chargeItemVO.setBillingMethod(BILLMETHOD.get(chargeItemVO.getDefaultBillingMethod()));
            chargeItemVO.setRule(ROUNDRULE.get(chargeItemVO.getRoundRule()));
            chargeItemVO.setOperationType("DEP".equals(chargeItemVO.getOperationType()) ? "出港" : "进港");
        }
        return list;
    }

    /**
     * 查询收费项目
     *
     * @param id 收费项目主键
     * @return 收费项目
     */
    @Override
    public ChargeItemVO selectChargeById(Long id) {
        return itemMapper.selectChargeById(id);
    }

    /**
     * 新增收费项目
     *
     * @param item 收费项目
     * @return 结果
     */
    @Override
    public Long insertChargeItem(ChargeItem item) {
        item.setCreateBy(SecurityUtils.getUsername());
        item.setCreateTime(DateUtils.getNowDate());
        item.setDeptId(SecurityUtils.getHighParentId());
        itemMapper.insertChargeItem(item);
        return item.getId();
    }

    /**
     * 修改收费项目
     *
     * @param item 收费项目
     * @return 结果
     */
    @Override
    public int updateChargeItem(ChargeItem item) {
        item.setUpdateBy(SecurityUtils.getUsername());
        item.setUpdateTime(DateUtils.getNowDate());
        return itemMapper.updateById(item);
    }

    /**
     * 删除收费项目
     *
     * @param id 收费项目主键
     * @return 结果
     */
    @Override
    public int deleteChargeItem(Long id) {
        return itemMapper.removeChargeItem(id);
    }


    /**
     * 查询所有票证类型
     *
     * @return 结果
     */
    public void selectTypeList() {
        List<BaseBillType> baseBillTypes = billTypeMapper.selectTypeList();
        for (BaseBillType baseBillType:baseBillTypes) {
            String code = baseBillType.getCode();
            String name = baseBillType.getName();
            DOCUMENTMAP.put(code,name);
        }
    }

    private static final Map<String,String> DOCUMENTMAP = new HashMap<>();
 /*   static {
        DOCUMENTMAP.put("guonei_fendan","国内分单");
        DOCUMENTMAP.put("guoji_fendan","国际分单");
        DOCUMENTMAP.put("guonei_zhudan","国内主单");
        DOCUMENTMAP.put("guoji_zhudan","国际主单");
        DOCUMENTMAP.put("youjiandan","邮件单");
    }*/

    private static final Map<Integer,String> BILLMETHOD = new HashMap<>();
    static {
        BILLMETHOD.put(0,"按单收费");
        BILLMETHOD.put(1,"按重量计费");
        BILLMETHOD.put(2,"按体积计费");
    }

    private static final Map<Integer,String> ROUNDRULE = new HashMap<>();
    static {
        ROUNDRULE.put(0,"实际");
        ROUNDRULE.put(1,"四舍五入至个位");
        ROUNDRULE.put(2,"四舍五入至0.1");
        ROUNDRULE.put(3,"四舍五入至0.01");
        ROUNDRULE.put(4,"四舍五入至0.001");
        ROUNDRULE.put(5,"直接进位到0.1");
        ROUNDRULE.put(6,"直接舍位到0.1");
        ROUNDRULE.put(7,"直接进位到个位");
        ROUNDRULE.put(8,"直接舍位到个位");
        ROUNDRULE.put(9,"直接进位到0.5");
        ROUNDRULE.put(10,"直接舍位到0.5");
    }
}
