<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.cleanup.mapper.HzCleanupMapper">
    
    <resultMap type="HzCleanup" id="HzCleanupResult">
        <result property="id"    column="id"    />
        <result property="waybillCode"    column="waybill_code"    />
        <result property="flightNo"    column="flight_no"    />
        <result property="desPort"    column="des_port"    />
        <result property="systemQuantity"    column="system_quantity"    />
        <result property="actualQuantity"    column="actual_quantity"    />
        <result property="weight"    column="weight"    />
        <result property="type"    column="type"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectHzCleanupVo">
        select id, waybill_code, flight_no, des_port, system_quantity, actual_quantity, weight, type, create_by, update_by, create_time, update_time, is_del from hz_cleanup
    </sql>

    <select id="selectHzCleanupList" parameterType="HzCleanup" resultMap="HzCleanupResult">
        <include refid="selectHzCleanupVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="waybillCode != null  and waybillCode != ''"> and waybill_code = #{waybillCode}</if>
            <if test="flightNo != null  and flightNo != ''"> and flight_no = #{flightNo}</if>
            <if test="desPort != null  and desPort != ''"> and des_port = #{desPort}</if>
            <if test="systemQuantity != null "> and system_quantity = #{systemQuantity}</if>
            <if test="actualQuantity != null "> and actual_quantity = #{actualQuantity}</if>
            <if test="weight != null "> and weight = #{weight}</if>
            <if test="writeTimeStart != null and writeTimeEnd != null">
                and write_time between #{writeTimeStart} and #{writeTimeEnd}
             </if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
        </where>
    </select>
    
    <select id="selectHzCleanupById" parameterType="Long" resultMap="HzCleanupResult">
        <include refid="selectHzCleanupVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertHzCleanup" parameterType="HzCleanup" useGeneratedKeys="true" keyProperty="id">
        insert into hz_cleanup
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="waybillCode != null">waybill_code,</if>
            <if test="flightNo != null">flight_no,</if>
            <if test="desPort != null">des_port,</if>
            <if test="systemQuantity != null">system_quantity,</if>
            <if test="actualQuantity != null">actual_quantity,</if>
            <if test="weight != null">weight,</if>
            <if test="type != null">type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDel != null">is_del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="waybillCode != null">#{waybillCode},</if>
            <if test="flightNo != null">#{flightNo},</if>
            <if test="desPort != null">#{desPort},</if>
            <if test="systemQuantity != null">#{systemQuantity},</if>
            <if test="actualQuantity != null">#{actualQuantity},</if>
            <if test="weight != null">#{weight},</if>
            <if test="type != null">#{type},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDel != null">#{isDel},</if>
         </trim>
    </insert>

    <update id="updateHzCleanup" parameterType="HzCleanup">
        update hz_cleanup
        <trim prefix="SET" suffixOverrides=",">
            <if test="waybillCode != null">waybill_code = #{waybillCode},</if>
            <if test="flightNo != null">flight_no = #{flightNo},</if>
            <if test="desPort != null">des_port = #{desPort},</if>
            <if test="systemQuantity != null">system_quantity = #{systemQuantity},</if>
            <if test="actualQuantity != null">actual_quantity = #{actualQuantity},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="type != null">type = #{type},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHzCleanupById" parameterType="Long">
        delete from hz_cleanup where id = #{id}
    </delete>

    <delete id="deleteHzCleanupByIds" parameterType="String">
        delete from hz_cleanup where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>