package com.gzairports.common.bank.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.List;

/**
 * @author: lan
 * @Desc: 银行订单号与运单对应关系
 * @create: 2025-03-19 11:26
 **/

@Data
@TableName("hz_bank_order_waybill")
public class HzBankOrderWaybill {

    /** 主键id */
    private Long id;

    /** 商户订单号 */
    private String orderNo;

    /** 运单id type为0就是主单id type为1/2就是分单id type为3是进港主单办单id type为4是服务申请表id type为5是冷藏表id */
    private Long waybillId;

    /** 运单号 */
    private String waybillCode;

    /** 运单类型 0出港主单/邮件单 1分单 2分单账单 3进港主单  4服务申请 5冷藏 */
    private Integer type;

    @TableField(exist = false)
    private List<HzBankOrderSerial> orderSerials;
}
