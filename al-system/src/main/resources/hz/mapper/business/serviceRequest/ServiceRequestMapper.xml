<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.serviceRequest.mapper.ServiceRequestMapper">

    <select id="selectListByQuery" resultType="com.gzairports.common.serviceRequest.domain.ServiceRequest">
        select asr.id, asr.service_no, asr.waybill_code, asr.agent, asr.service_item, asr.service_item_id, asr.select_time, asr.status,
               asr.pay_status, asr.request_time, asr.begin_time, asr.pay_time, asr.pay_money, asr.settle_time, asr.end_time, asr.end_money,
               asr.refund, asr.actual_time, asr.remark, asi.type
        from all_service_request asr left join all_service_item asi on asi.id = asr.service_item_id
        <where>
            <if test="deptId != null and deptId == 1">
              and asr.dept_id is null
            </if>
            <if test="deptId == null">
                and asr.status != 'STAGING'
            </if>
            <if test="serviceNo != null and serviceNo != ''">
                and asr.service_no like '%${serviceNo}%'
            </if>
            <if test="agent != null and agent != ''">
                and asr.agent = #{agent}
            </if>
            <if test="startTime != null">
                and asr.request_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and asr.request_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="serviceItem != null and serviceItem != ''">
                and asr.service_item like '%${serviceItem}%'
            </if>
            <if test="waybillCode != null and waybillCode != ''">
                and asr.waybill_code like '%${waybillCode}%'
            </if>
            <if test="status != null and status != ''">
                and asr.status = #{status}
            </if>
        </where>
        order by asr.request_time desc
    </select>
    <select id="selectReportRequestList"
            resultType="com.gzairports.common.business.reporter.domain.ReportServiceRequest">
        select id, waybill_code, agent, service_item, select_time, actual_time, status, pay_status, pay_money
        from all_service_request where 1=1
        <if test="lastSyncTime != null">
            and update_time > #{lastSyncTime}
        </if>
        <if test="dateNow != null">
            and update_time <![CDATA[<=]]> #{dateNow}
        </if>
    </select>
</mapper>