<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.VolumeMapper">
    <insert id="insertVolume" parameterType="BaseVolume">
        insert into base_volume
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="volumeUnit != null and volumeUnit != ''">volume_unit,</if>
            <if test="chineseName != null and chineseName != ''">chinese_name,</if>
            <if test="englishName != null and englishName != ''">english_name,</if>
            <if test="conversionRate != null">conversion_rate,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="volumeUnit != null and volumeUnit != ''">#{volumeUnit},</if>
            <if test="chineseName != null and chineseName != ''">#{chineseName},</if>
            <if test="englishName != null and englishName != ''">#{englishName},</if>
            <if test="conversionRate != null">#{conversionRate},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>
    <update id="updateVolume" parameterType="BaseVolume">
        update base_volume
        <trim prefix="SET" suffixOverrides=",">
            <if test="volumeUnit != null and volumeUnit != ''">volume_unit = #{volumeUnit},</if>
            <if test="chineseName != null and chineseName != ''">chinese_name = #{chineseName},</if>
            <if test="englishName != null and englishName != ''">english_name = #{englishName},</if>
            <if test="conversionRate != null">conversion_rate = #{conversionRate},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null and createTime != ''">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="delVolume" parameterType="Long">
        update base_volume set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <select id="selectVolumeUnitList" resultType="com.gzairports.common.basedata.domain.BaseVolume">
        select id, volume_unit, chinese_name, english_name, conversion_rate from base_volume
        <where>
            and is_del = 0
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="volumeUnit != null  and volumeUnit != ''"> and volume_unit like concat('%',#{volumeUnit},'%')</if>
            <if test="chineseName != null  and chineseName != ''"> and chinese_name like concat('%',#{chineseName},'%')</if>
        </where>
    </select>
    <select id="selectVolumeByVolumeUnit" resultType="com.gzairports.common.basedata.domain.BaseVolume">
        select id, volume_unit, chinese_name, english_name, conversion_rate
        from base_volume where volume_unit = #{volumeUnit} and is_del = 0
    </select>
</mapper>
