package com.gzairports.common.bank.domain.payment;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * @author: lan
 * @Desc: 网关支付子订单列表
 * @create: 2025-03-18 11:35
 **/

@Data
@XmlRootElement(name = "prodList")
@XmlAccessorType(XmlAccessType.FIELD)
public class PaymentProdList implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 子商户流水号 50 Y
     * 该笔交易的子流水号且必须保证唯一
     */
    @XmlElement(name = "inateMeroChannelSerialNo")
    private String inateMeroChannelSerialNo;

    /** 商品金额 20 Y
     * 子订单金额，以分为单位
     */
    @XmlElement(name = "prodAmt")
    private Long prodAmt;

    /** 商品编号 20 Y
     */
    @XmlElement(name = "prodId")
    private String prodId;

    /** 商品名称 64 Y
     */
    @XmlElement(name = "prodName")
    private String prodName;

    /** 商品数量 10 Y
     */
    @XmlElement(name = "prodCount")
    private Integer prodCount;

    /** 二级商户签约编码 32 N
     */
    @XmlElement(name = "agtContractId")
    private String agtContractId;

    /** 是否使用延迟结算功能 N
     * 0-不使用
     * 1-使用（所有的子订单信息在下单接口上送，发起确认支付后，T+1结算给确认支付指定的子商户）
     * 2-事后分账（实际产生的子订单信息在确认支付接口上送，发起确认支付后，T+1结算给确认支付指定的子商户）
     * 注：支付分账产品，子订单可按字典自由输入；非支付分账产品，如果存在多条子订单，所有子订单中该标识需保持一致；
     */
    @XmlElement(name = "guarantePayment")
    private Integer guarantePayment;

    /** 核心收款标识 N
     * 1-当前list商户为主要收款商户
     */
    @XmlElement(name = "mainPayeeFlag")
    private Boolean mainPayeeFlag;


}
