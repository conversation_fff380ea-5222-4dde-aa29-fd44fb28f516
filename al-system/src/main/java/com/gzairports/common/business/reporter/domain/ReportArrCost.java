package com.gzairports.common.business.reporter.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-02-19
 */
@Data
@TableName("all_report_arr_cost")
public class ReportArrCost {

    /** 主键id */
    private Long id;

    /** 报表id */
    private Long reportId;

    /** 运单号 */
    private String waybillCode;

    /** 流水号 */
    private String serialNo;

    /** 费用名称 */
    private String rateName;

    /** 费率 */
    private BigDecimal rate;

    /** 数量 */
    private String arrQuantity;

    /** 总费用 */
    private BigDecimal totalCharge;

    /** 支付状态 */
    private String isSettle;

    /** 部门id */
    private Long deptId;

    /** 办单时间 */
    private String pickUpTime;
}
