package com.gzairports.hz.business.departure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.reporter.domain.ReportDepRepeatWeight;
import com.gzairports.hz.business.departure.domain.HzDepRepeatWeight;
import com.gzairports.hz.business.departure.domain.query.GroupCargoQuery;
import com.gzairports.hz.business.departure.domain.query.RepeatWeightQuery;
import com.gzairports.hz.business.departure.domain.vo.HzDepRepeatWaybillsVo;
import com.gzairports.hz.business.departure.domain.vo.RepeatWeightVo;
import com.gzairports.hz.business.departure.domain.vo.WeightInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 复重Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-06
 */
@Mapper
public interface RepeatWeightMapper extends BaseMapper<HzDepRepeatWeight> {

    /**
     * 查询复重列表
     * @param query 查询条件
     * @return 结果
     */
    List<RepeatWeightVo> selectListByQuery(GroupCargoQuery query);

    /**
     * 板箱复重详情
     * @param id 板箱复重id
     * @return 详情
     */
    WeightInfoVo getInfo(Long id);


    /**
     * @author: lan
     * @description: 根据groupUldId去查询运单对应数据
     * @param: [id]
     * @date: 2024/11/7
     */
    List<HzDepRepeatWaybillsVo> getWaybills(Long id);

    /**
     * 根据组货id 配载id 板箱号 仓位 重量 查询复重数据
     * */
    HzDepRepeatWeight selectDataByQuery(RepeatWeightQuery query);

    /**
     * 查询货站出港复重表数据
     * @param lastSyncTime 最后更新时间
     * @param dateNow 当前时间
     * @return 结果
     */
    List<ReportDepRepeatWeight> selectRepeatWeightList(@Param("lastSyncTime") Date lastSyncTime, @Param("dateNow") Date dateNow);

    /**
     * 查询航班下的待复重数据
     * @param flightLoadIdList 航班配载id
     * @return 结果
     */
    List<ReportDepRepeatWeight> selectWaitSyncData(@Param("flightLoadIdList") List<Long> flightLoadIdList);

}
