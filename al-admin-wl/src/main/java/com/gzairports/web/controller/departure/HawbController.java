package com.gzairports.web.controller.departure;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.basedata.domain.BaseAirportCode;
import com.gzairports.common.basedata.mapper.AirportCodeMapper;
import com.gzairports.common.basedata.service.IAirportCodeService;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.domain.entity.SysDept;
import com.gzairports.common.pdf.PdfPrintHelper;
import com.gzairports.common.system.service.ISysDeptService;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.SerialNumberGenerator;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.common.utils.sign.Base64;
import com.gzairports.common.utils.spring.SpringUtils;
import com.gzairports.oss.domain.SysOss;
import com.gzairports.oss.service.impl.SysOssServiceImpl;
import com.gzairports.wl.charge.domain.query.FareAirItemQuery;
import com.gzairports.wl.charge.domain.query.FreightItemQuery;
import com.gzairports.wl.charge.domain.vo.FareAirItemVo;
import com.gzairports.wl.departure.domain.*;
import com.gzairports.wl.departure.domain.query.FareQuery;
import com.gzairports.wl.departure.domain.query.HawbQuery;
import com.gzairports.wl.departure.domain.query.ItemQuery;
import com.gzairports.wl.departure.domain.vo.EditWaybillCode;
import com.gzairports.wl.departure.domain.vo.HawbQueryVo;
import com.gzairports.wl.departure.domain.vo.HawbQueryVo1;
import com.gzairports.wl.departure.domain.vo.HawbVo;
import com.gzairports.wl.departure.service.IConsignService;
import com.gzairports.wl.departure.service.IHawbService;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 分单制单Controller
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@RestController
@RequestMapping("/dep/hawb")
public class HawbController extends BaseController {

    @Autowired
    private IHawbService hawbService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private IAirportCodeService airportCodeService;

    @Autowired
    private IConsignService consignService;

    /**
     * 运单新增
     */
    @PreAuthorize("@ss.hasPermi('dep:hawb:add')")
    @PostMapping("/add")
    @ApiOperation(value = "运单新增(保存)")
    public AjaxResult add(@RequestBody HawbVo vo) throws Exception {
        if (StringUtils.isEmpty(vo.getWriter())){
            vo.setWriter(SecurityUtils.getUsername());
        }
        String desPort = airportCodeService.selectChineseName(vo.getDesPort());
        vo.setDesPortStr(desPort);
        vo.setSourcePortStr("贵阳");
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if (vo.getWriteTime() == null){
            vo.setWriteTime(new Date());
        }
        String format1 = simpleDateFormat.format(vo.getWriteTime());
        vo.setWriteTimeStr(format1);
        if (vo.getFlightDate() != null){
            vo.setFlightDateStr(simpleDateFormat.format(vo.getFlightDate()));
        }
        if (!StringUtils.isEmpty(vo.getMasterWaybillCode())){
            String substring = vo.getMasterWaybillCode().substring(4);
            String waybillCodeAbb = substring.substring(0, 3) + "-" + substring.substring(3);
            vo.setMasterAbb(waybillCodeAbb);
        }
        if (vo.getTransportInsureValue() == null){
            vo.setInsurance("无");
        }else {
            vo.setInsurance(vo.getTransportInsureValue().toString());
        }
        if (vo.getPaymentMethod().equals(0)){
            vo.setPaymentMethodStr("现付");
        }else {
            vo.setPaymentMethodStr("周期结算");
        }
        vo.setDeclaredValue("无");
        vo.setGroundCost(new BigDecimal(0).setScale(2, RoundingMode.DOWN));
//        vo.setCargoInfo((vo.getSize() == null ? "" : vo.getSize()) + " " + (vo.getCargoName() == null ? "" : vo.getCargoName()) + " " + (vo.getPack() == null ? "" : vo.getPack()) + " " + (vo.getVolume() == null ? "" : vo.getVolume().toString()));
        vo.setSizeVolume((vo.getSize() == null ? "" : vo.getSize()) + " " + (vo.getVolume() == null ? "" : vo.getVolume()+"m³"));
        vo.setChargeWeight(vo.getChargeWeight().setScale(0, RoundingMode.DOWN));
        if (!CollectionUtils.isEmpty(vo.getItems())){
            List<HawbItem> airRates = vo.getItems().stream().filter(e -> "航空运费".equals(e.getFreightType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(airRates)){
                HawbItem item = airRates.get(0);
                vo.setAirCost(item.getCharging().setScale(2, RoundingMode.DOWN));
                vo.setAirRate(item.getRate());
                vo.setCostType(item.getCostType());
            }
            List<HawbItem> otherRates = vo.getItems().stream().filter(e -> "收费项目".equals(e.getFreightType())).collect(Collectors.toList());
            otherRates.addAll(vo.getItems().stream()
                    .filter(e -> "保险费".equals(e.getFreightType()))
                    .collect(Collectors.toList()));
            StringBuilder builder = new StringBuilder();
            for (HawbItem otherRate : otherRates) {
                builder.append(otherRate.getCostType()).append(": ").append(otherRate.getCharging().setScale(2, RoundingMode.DOWN)).append(" ");
            }
            vo.setOtherInfo(builder.toString());
            if (!CollectionUtils.isEmpty(otherRates)){
                BigDecimal reduce = otherRates.stream().map(HawbItem::getCharging).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                vo.setOtherCost(reduce.setScale(2, RoundingMode.DOWN));
            }
        }
        if (vo.getAirCost() == null){
            vo.setAirRate(new BigDecimal(0).setScale(2, RoundingMode.DOWN));
        }
        if (vo.getOtherCost() == null){
            vo.setOtherCost(new BigDecimal(0).setScale(2, RoundingMode.DOWN));
        }
        BigDecimal airCost = vo.getAirCost() == null ? new BigDecimal(0) : vo.getAirCost();
        BigDecimal otherCost = vo.getOtherCost() == null ? new BigDecimal(0) : vo.getOtherCost();
        vo.setTotalCost(airCost.add(otherCost).setScale(2, RoundingMode.DOWN));
        SysDept dept = deptService.selectDeptById(SecurityUtils.getHighParentId());
        if (dept != null && StringUtils.isNotEmpty(dept.getLogoUrlBig())){
            byte[] bytes = downloadFileFromUrl(dept.getLogoUrlBig());
            String airLogo = Base64.encode(bytes);
            vo.setAirLogo(airLogo);
        }
        vo.setPrePay("YES");
        ClassPathResource resource = new ClassPathResource("electronic/hawb.pdf");
        if (resource.exists()){
            String path = resource.getPath();
            byte[] pdfDataFromTemplate = PdfPrintHelper.getPdfDataFromTemplate(vo, path);
            // 使用StandardMultipartHttpServletRequest来创建MultipartFile
            DiskFileItem fileItem = new DiskFileItem(vo.getWaybillCode() + ".pdf", "application/pdf", true, vo.getWaybillCode() + ".pdf", -1, null);

            // 将 InputStream 写入 DiskFileItem
            fileItem.getOutputStream().write(pdfDataFromTemplate);
            fileItem.getOutputStream().close();

            // 使用 FileItem 构建 MultipartFile
            MultipartFile multipartFile = new CommonsMultipartFile(fileItem);
            SysOss upload = SpringUtils.getBean(SysOssServiceImpl.class).upload(multipartFile);
            vo.setPdfUrl(upload.getUrl());
        }
        return AjaxResult.success(hawbService.add(vo));
    }

    /**
     * 运单校验
     */
    @PreAuthorize("@ss.hasPermi('dep:hawb:check')")
    @GetMapping("/check/{waybillCode}")
    @ApiOperation(value = "运单校验")
    public AjaxResult check(@PathVariable("waybillCode") String waybillCode)
    {
        return AjaxResult.success(hawbService.check(waybillCode));
    }

    /**
     * 自动运价
     */
    @PreAuthorize("@ss.hasPermi('dep:hawb:fare')")
    @PostMapping("/fare")
    @ApiOperation(value = "自动运价")
    public AjaxResult fare(@RequestBody FareQuery query)
    {
        return AjaxResult.success(hawbService.fare(query));
    }

    /**
     * 添加费用项
     */
    @PreAuthorize("@ss.hasPermi('dep:hawb:addFees')")
    @PostMapping("/addFees")
    @ApiOperation(value = "添加费用项")
    public AjaxResult addFees(@RequestBody ItemQuery query)
    {
        return AjaxResult.success(hawbService.addFees(query));
    }

    /**
     * 修改费用项
     */
    @PreAuthorize("@ss.hasPermi('dep:hawb:editFee')")
    @PostMapping("/editFee")
    @ApiOperation(value = "修改费用项")
    public AjaxResult editFee(@RequestBody HawbItem item)
    {
        return AjaxResult.success(hawbService.editFee(item));
    }

    /**
     * 删除费用项
     */
    @PreAuthorize("@ss.hasPermi('dep:hawb:delFee')")
    @PostMapping("/delFee")
    @ApiOperation(value = "删除费用项")
    public AjaxResult delFee(@RequestBody HawbItem item)
    {
        return AjaxResult.success(hawbService.delFee(item));
    }

    /**
     * 运单作废
     */
    @PreAuthorize("@ss.hasPermi('dep:hawb:invalid')")
    @GetMapping("/invalid/{waybillCode}")
    @ApiOperation(value = "运单作废")
    public AjaxResult invalid(@PathVariable("waybillCode") String waybillCode)
    {
        return toAjax(hawbService.invalid(waybillCode));
    }

    /**
     * 运单取消作废
     */
    @PreAuthorize("@ss.hasPermi('dep:hawb:cancelVoid')")
    @GetMapping("/cancelVoid/{waybillCode}")
    @ApiOperation(value = "运单取消作废")
    public AjaxResult cancelVoid(@PathVariable("waybillCode") String waybillCode)
    {
        return toAjax(hawbService.cancelVoid(waybillCode));
    }

    /**
     * 根据运单号查询历史运单
     */
    @PreAuthorize("@ss.hasPermi('dep:hawb:getInfo')")
    @GetMapping("/getInfo/{waybillCode}")
    @ApiOperation(value = "运单详情")
    public AjaxResult getInfo(@PathVariable("waybillCode") String waybillCode)
    {
        return AjaxResult.success(hawbService.getInfo(waybillCode));
    }

    /**
     * 编辑运单
     */
    @PreAuthorize("@ss.hasPermi('dep:hawb:edit')")
    @PostMapping("/edit")
    @ApiOperation(value = "编辑运单")
    public AjaxResult edit(@RequestBody HawbVo vo) throws Exception {
        if (StringUtils.isEmpty(vo.getWriter())){
            vo.setWriter(SecurityUtils.getUsername());
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (vo.getWriteTime() == null){
            vo.setWriteTime(new Date());
        }
        String desPort = airportCodeService.selectChineseName(vo.getDesPort());
        vo.setDesPortStr(desPort);
        vo.setSourcePortStr("贵阳");
        String format1 = format.format(vo.getWriteTime());
        vo.setWriteTimeStr(format1);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if (vo.getFlightDate() != null){
            vo.setFlightDateStr(simpleDateFormat.format(vo.getFlightDate()));
        }
        if (!StringUtils.isEmpty(vo.getMasterWaybillCode())){
            String substring = vo.getMasterWaybillCode().substring(4);
            String waybillCodeAbb = substring.substring(0, 3) + "-" + substring.substring(3);
            vo.setMasterAbb(waybillCodeAbb);
        }
        if (vo.getTransportInsureValue() == null){
            vo.setInsurance("无");
        }else {
            vo.setInsurance(vo.getTransportInsureValue().toString());
        }
        if (vo.getPaymentMethod().equals(0)){
            vo.setPaymentMethodStr("现付");
        }else {
            vo.setPaymentMethodStr("周期结算");
        }
        vo.setDeclaredValue("无");
        vo.setGroundCost(new BigDecimal(0));
        vo.setChargeWeight(vo.getChargeWeight().setScale(0, RoundingMode.DOWN));
        if (!CollectionUtils.isEmpty(vo.getItems())){
            List<HawbItem> airRates = vo.getItems().stream().filter(e -> "航空运费".equals(e.getFreightType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(airRates)){
                HawbItem item = airRates.get(0);
                vo.setAirCost(item.getCharging().setScale(2, RoundingMode.DOWN));
                vo.setAirRate(item.getRate());
                vo.setCostType(item.getCostType());
            }
            List<HawbItem> otherRates = vo.getItems().stream().filter(e -> "收费项目".equals(e.getFreightType())).collect(Collectors.toList());
            otherRates.addAll(vo.getItems().stream()
                    .filter(e -> "保险费".equals(e.getFreightType()))
                    .collect(Collectors.toList()));
            StringBuilder builder = new StringBuilder();
            for (HawbItem otherRate : otherRates) {
                builder.append(otherRate.getCostType()).append(": ").append(otherRate.getCharging().toString()).append(" ");
            }
            vo.setOtherInfo(builder.toString());
            if (!CollectionUtils.isEmpty(otherRates)){
                BigDecimal reduce = otherRates.stream().map(HawbItem::getCharging).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                vo.setOtherCost(reduce);
            }
        }
        if (vo.getAirCost() == null){
            vo.setAirRate(new BigDecimal(0));
        }
        if (vo.getOtherCost() == null){
            vo.setOtherCost(new BigDecimal(0));
        }
        BigDecimal airCost = vo.getAirCost() == null ? new BigDecimal(0) : vo.getAirCost();
        BigDecimal otherCost = vo.getOtherCost() == null ? new BigDecimal(0) : vo.getOtherCost();
        vo.setTotalCost(airCost.add(otherCost));
        SysDept dept = deptService.selectDeptById(SecurityUtils.getHighParentId());
        if (dept != null && StringUtils.isNotEmpty(dept.getLogoUrlBig())){
            byte[] bytes = downloadFileFromUrl(dept.getLogoUrlBig());
            String airLogo = Base64.encode(bytes);
            vo.setAirLogo(airLogo);
        }
        vo.setPrePay("YES");
        ClassPathResource resource = new ClassPathResource("electronic/hawb.pdf");
        if (resource.exists()){
            String path = resource.getPath();
            byte[] pdfDataFromTemplate = PdfPrintHelper.getPdfDataFromTemplate(vo, path);
            // 使用StandardMultipartHttpServletRequest来创建MultipartFile
            DiskFileItem fileItem = new DiskFileItem(vo.getWaybillCode() + ".pdf", "application/pdf", true, vo.getWaybillCode() + ".pdf", -1, null);

            // 将 InputStream 写入 DiskFileItem
            fileItem.getOutputStream().write(pdfDataFromTemplate);
            fileItem.getOutputStream().close();

            // 使用 FileItem 构建 MultipartFile
            MultipartFile multipartFile = new CommonsMultipartFile(fileItem);
            SysOss upload = SpringUtils.getBean(SysOssServiceImpl.class).upload(multipartFile);
            vo.setPdfUrl(upload.getUrl());
        }
        return toAjax(hawbService.edit(vo));
    }


    /**
     * 增加备注
     */
    @PreAuthorize("@ss.hasPermi('dep:hawb:addRemark')")
    @PostMapping("/addRemark")
    @ApiOperation(value = "增加备注")
    public AjaxResult addRemark(@RequestBody HawbErrorRemark remark)
    {
        return toAjax(hawbService.addRemark(remark));
    }

    /**
     * 查询备注
     */
    @PreAuthorize("@ss.hasPermi('dep:hawb:remarkList')")
    @GetMapping("/remarkList/{waybillCode}")
    @ApiOperation(value = "增加备注")
    public AjaxResult remarkList(@PathVariable("waybillCode") String waybillCode)
    {
        return AjaxResult.success(hawbService.remarkList(waybillCode));
    }

    /**
     * 国内分单查询
     */
    @PreAuthorize("@ss.hasPermi('dep:hawb:query')")
    @PostMapping("/query")
    @ApiOperation(value = "国内分单查询")
    public AjaxResult query(@RequestBody HawbQuery query)
    {
        return AjaxResult.success(hawbService.queryList(query));
    }


    /**
     * 国内分单销售查询
     */
    @PreAuthorize("@ss.hasPermi('dep:hawb:saleQuery')")
    @PostMapping("/saleQuery")
    @ApiOperation(value = "国内分单销售查询")
    public AjaxResult saleQuery(@RequestBody HawbQuery query)
    {
        return AjaxResult.success(hawbService.saleQuery(query));
    }

    /**
     * 生成账单
     */
    @PreAuthorize("@ss.hasPermi('dep:hawb:generateBill')")
    @PostMapping("/generateBill")
    @ApiOperation(value = "生成账单")
    public AjaxResult generateBill(@RequestBody Long[] ids)
    {
        return AjaxResult.success(hawbService.generateBill(ids));
    }

    /**
     * 生成账单确认按钮
     */
    @PreAuthorize("@ss.hasPermi('dep:hawb:confirm')")
    @PostMapping("/confirm")
    @ApiOperation(value = "生成账单确认按钮")
    public AjaxResult confirm(@RequestBody HawbQueryVo vo)
    {
        return toAjax(hawbService.confirm(vo));
    }

    /**
     * 导出查询的国内分单
     */
    @PreAuthorize("@ss.hasPermi('dep:hawb:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出国内分单")
    public void export(HttpServletResponse response, HawbQuery query)
    {
        List<HawbQueryVo1> hawbs = hawbService.exportList(query);
        String startTime = "";
        String endTime = "";
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (query.getStartTime() != null){
            startTime = simpleDateFormat.format(query.getStartTime());
        }
        if (query.getEndTime() != null){
            endTime = simpleDateFormat.format(query.getEndTime());
        }
        ExcelUtil<HawbQueryVo1> util = new ExcelUtil<HawbQueryVo1>(HawbQueryVo1.class);
        util.exportExcel(response, hawbs, "国内分单",SecurityUtils.getNickName(), null, startTime, endTime);
    }

    /**
     * 打印国内分单
     */
    @PreAuthorize("@ss.hasPermi('dep:hawb:printHawb')")
    @GetMapping(value = "/printHawb/{id}")
    @ApiOperation(value = "打印国内分单")
    public void printHawb(HttpServletResponse response, @PathVariable("id") Long id) throws Exception
    {
        hawbService.printHawb(response,id);
    }

    /**
     * 打印托运书
     */
//    @PreAuthorize("@ss.hasPermi('dep:hawb:printConsign')")
    @GetMapping(value = "/printConsign/{id}")
    @ApiOperation(value = "打印托运书")
    public void printConsign(HttpServletResponse response, @PathVariable("id") Long id) throws Exception
    {
        Hawb hawb = hawbService.getById(id);
        Consign consign = new Consign();
        if (hawb.getConsignCode() != null){
            consign = consignService.getOne(new QueryWrapper<Consign>().eq("code",hawb.getConsignCode()));
            String baseAirportCode = airportCodeService.selectChineseName(consign.getDesPort());
            consign.setDesPortStr(baseAirportCode);
            if(StringUtils.isNotEmpty(consign.getWaybillCode())){
                String substring = consign.getWaybillCode().substring(4);
                String waybillCodeAbb = substring.substring(0, 3) + "-" + substring.substring(3);
                consign.setMasterWaybillCode(waybillCodeAbb);
            }else{
                if (StringUtils.isNotEmpty(hawb.getMasterWaybillCode())){
                    String substring = hawb.getMasterWaybillCode().substring(4);
                    String waybillCodeAbb = substring.substring(0, 3) + "-" + substring.substring(3);
                    consign.setMasterWaybillCode(waybillCodeAbb);
                    //托运管理的运单号应该是分单关联的主单号 不然就为空
                    consign.setWaybillCode(hawb.getMasterWaybillCode());
                }else{
                    consign.setWaybillCode(null);
                }
            }
//            consign.setInsure(consign.getInsurance() == 0 ? "否":"是");
//            consign.setDanger(consign.getIsDanger() == 0 ? "NO":"YES");
//            consign.setValuable(consign.getIsValuable() == 0 ? "NO":"YES");
            if(consign.getInsurance() == 1){
                consign.setInsure("是");
            }
            if(consign.getIsDanger() == 1){
                consign.setDanger("YES");
            }
            if(consign.getIsValuable() == 1){
                consign.setValuable("YES");
            }
            BigDecimal actualWeight = consign.getActualWeight();
            consign.setActualWeightStr(actualWeight.toString());
            if (consign.getChargeWeight() != null){
                consign.setChargeWeightStr(consign.getChargeWeight().toString());
            }
            if (consign.getInsuranceValue() != null){
                consign.setInsuranceValueStr(consign.getInsuranceValue().toString());
            }
            if (StringUtils.isNotEmpty(consign.getShipperSign())) {
                byte[] bytes = downloadFileFromUrl(consign.getShipperSign());
                String signImage = Base64.encode(bytes);
                consign.setSignImage(signImage);
            }
        }else {
            BeanUtils.copyProperties(hawb,consign);
            if (StringUtils.isNotEmpty(hawb.getMasterWaybillCode())){
                String substring = hawb.getMasterWaybillCode().substring(4);
                String waybillCodeAbb = substring.substring(0, 3) + "-" + substring.substring(3);
                consign.setMasterWaybillCode(waybillCodeAbb);
                //托运管理的运单号应该是分单关联的主单号 不然就为空
                consign.setWaybillCode(hawb.getMasterWaybillCode());
            }else{
                consign.setWaybillCode(null);
            }
            consign.setCode(hawb.getConsignCode());
            consign.setIsDanger(0);
            consign.setIsValuable(0);
            consign.setActualWeight(hawb.getWeight());
            if (hawb.getTransportInsureValue() != null){
                consign.setInsurance(1);
                consign.setInsuranceValue(hawb.getTransportInsureValue());
            }else {
                consign.setInsurance(0);
            }
            consign.setShipperName(hawb.getShipper());
            consign.setConsigneeName(hawb.getConsignee());
            consign.setCreateTime(new Date());
            consign.setConsignTime(new Date());
            consign.setId(null);
            if (consign.getCode() == null){
                String s = SerialNumberGenerator.generateSerialNumber();
                String substring = s.substring(8);
                consign.setCode(substring);
                hawb.setConsignCode(substring);
                hawbService.updateById(hawb);
            }
            String baseAirportCode = airportCodeService.selectChineseName(consign.getDesPort());
            consign.setDesPortStr(baseAirportCode);
            if(consign.getInsurance() == 1){
                consign.setInsure("是");
            }
            if(consign.getIsDanger() == 1){
                consign.setDanger("YES");
            }
            if(consign.getIsValuable() == 1){
                consign.setValuable("YES");
            }
            BigDecimal actualWeight = consign.getActualWeight();
            consign.setActualWeightStr(actualWeight.toString());
            if (consign.getChargeWeight() != null){
                consign.setChargeWeightStr(consign.getChargeWeight().toString());
            }
            if (consign.getInsuranceValue() != null){
                consign.setInsuranceValueStr(consign.getInsuranceValue().toString());
            }
            if (StringUtils.isNotEmpty(consign.getShipperSign())) {
                byte[] bytes = downloadFileFromUrl(consign.getShipperSign());
                String signImage = Base64.encode(bytes);
                consign.setSignImage(signImage);
            }
            ClassPathResource resource = new ClassPathResource("electronic/consign.pdf");
            if (resource.exists()) {
                String path = resource.getPath();
                byte[] pdfDataFromTemplate = PdfPrintHelper.getPdfDataFromTemplate(consign, path);
                DiskFileItem fileItem = new DiskFileItem("consign.pdf", "application/pdf", true, "consign.pdf", -1, null);
                fileItem.getOutputStream().write(pdfDataFromTemplate);
                fileItem.getOutputStream().close();
                MultipartFile multipartFile = new CommonsMultipartFile(fileItem);
                SysOss upload = SpringUtils.getBean(SysOssServiceImpl.class).upload(multipartFile);
                consign.setPdfUrl(upload.getUrl());
            }
        }
        consignService.saveOrUpdate(consign);
        hawbService.printConsign(response,consign);
    }

    @GetMapping("/itemList")
    @ApiOperation(value = "查询运价条目列表")
    public AjaxResult itemList(FreightItemQuery query){
        List<FareAirItemVo> list = hawbService.selectItemList(query);
        return AjaxResult.success(list);
    }

    @PostMapping("/editWaybillCode")
    @ApiOperation(value = "更改运单号")
    public AjaxResult editWaybillCode(@RequestBody EditWaybillCode editWaybillCode)
    {
        return toAjax(hawbService.editWaybillCode(editWaybillCode));
    }

    /**
     * 添加运价条目
     */
//    @PreAuthorize("@ss.hasPermi('dep:hawb:addItems')")
    @PostMapping("/addItems")
    @ApiOperation(value = "添加运价条目")
    public AjaxResult addItems(@RequestBody ItemQuery query)
    {
        return AjaxResult.success(hawbService.addItems(query));
    }

    private byte[] downloadFileFromUrl(String urlStr) throws Exception {
        URL url = new URL(urlStr);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(5000);
        connection.setReadTimeout(5000);

        if (connection.getResponseCode() != 200) {
            throw new RuntimeException("Failed to connect, HTTP error code : " + connection.getResponseCode());
        }

        try (InputStream inputStream = connection.getInputStream();
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            return baos.toByteArray();
        }
    }

    @GetMapping("/payComp/{id}/{money}")
    @ApiOperation(value = "支付完成接口")
    public AjaxResult payComp(@PathVariable("id") Long id, @PathVariable("money") String money)
    {
        return toAjax(hawbService.payComp(id, money));
    }

    @GetMapping("/isCtrl")
    @ApiOperation(value = "查询是否启用单证控制")
    public AjaxResult isCtrl()
    {
        return AjaxResult.success(hawbService.isCtrl());
    }


    @GetMapping("/seeConsign/{id}")
    @ApiOperation(value = "查看电子托运书")
    public AjaxResult seeConsign(@PathVariable("id") Long id) throws Exception {
        Hawb hawb = hawbService.getById(id);
        Consign consign = new Consign();
        if (StringUtils.isNotEmpty(hawb.getConsignCode())){
            consign = consignService.getOne(new QueryWrapper<Consign>().eq("code",hawb.getConsignCode()));
            if(StringUtils.isNotEmpty(consign.getWaybillCode())){
                String substring = consign.getWaybillCode().substring(4);
                String waybillCodeAbb = substring.substring(0, 3) + "-" + substring.substring(3);
                consign.setMasterWaybillCode(waybillCodeAbb);
            }else{
                if (StringUtils.isNotEmpty(hawb.getMasterWaybillCode())){
                    String substring = hawb.getMasterWaybillCode().substring(4);
                    String waybillCodeAbb = substring.substring(0, 3) + "-" + substring.substring(3);
                    consign.setMasterWaybillCode(waybillCodeAbb);
                    //托运管理的运单号应该是分单关联的主单号 不然就为空
                    consign.setWaybillCode(hawb.getMasterWaybillCode());
                }else{
                    consign.setWaybillCode(null);
                }
            }
            if(StringUtils.isNotEmpty(hawb.getPack())){
                consign.setPack(hawb.getPack());
            }
            if (consign.getInsuranceValue() != null) {
                consign.setInsure(hawb.getTransportInsureValue() == null ? "否" : "是");
                consign.setInsuranceValueStr(hawb.getTransportInsureValue().toString());
            }
            consign.setShipperName(hawb.getShipper());
            consign.setConsigneeName(hawb.getConsignee());
            consign.setCreateTime(new Date());
            consign.setConsignTime(new Date());
            consign.setId(null);
            String s = SerialNumberGenerator.generateSerialNumber();
            String substring = s.substring(8);
            consign.setCode(substring);
            hawb.setConsignCode(substring);
            hawbService.updateById(hawb);
            String baseAirportCode = airportCodeService.selectChineseName(consign.getDesPort());
            consign.setDesPortStr(baseAirportCode);
            if(consign.getInsurance() == 1){
                consign.setInsure("是");
            }
            if(consign.getIsDanger() == 1){
                consign.setDanger("YES");
            }
            if(consign.getIsValuable() == 1){
                consign.setValuable("YES");
            }
            BigDecimal actualWeight = consign.getActualWeight();
            consign.setActualWeightStr(actualWeight.toString());
            if (consign.getChargeWeight() != null) {
                consign.setChargeWeightStr(consign.getChargeWeight().toString());
            }
            if (consign.getInsuranceValue() != null) {
                consign.setInsuranceValueStr(consign.getInsuranceValue().toString());
            }
            if (StringUtils.isNotEmpty(consign.getShipperSign())) {
                byte[] bytes = downloadFileFromUrl(consign.getShipperSign());
                String signImage = Base64.encode(bytes);
                consign.setSignImage(signImage);
            }
            ClassPathResource resource = new ClassPathResource("electronic/consign.pdf");
            if (resource.exists()) {
                String path = resource.getPath();
                byte[] pdfDataFromTemplate = PdfPrintHelper.getPdfDataFromTemplate(consign, path);
                DiskFileItem fileItem = new DiskFileItem("consign.pdf", "application/pdf", true, "consign.pdf", -1, null);
                fileItem.getOutputStream().write(pdfDataFromTemplate);
                fileItem.getOutputStream().close();
                MultipartFile multipartFile = new CommonsMultipartFile(fileItem);
                SysOss upload = SpringUtils.getBean(SysOssServiceImpl.class).upload(multipartFile);
                consign.setPdfUrl(upload.getUrl());
            }
        }else {
            BeanUtils.copyProperties(hawb, consign);
            if (StringUtils.isNotEmpty(hawb.getMasterWaybillCode())) {
                String substring = hawb.getMasterWaybillCode().substring(4);
                String waybillCodeAbb = substring.substring(0, 3) + "-" + substring.substring(3);
                consign.setMasterWaybillCode(waybillCodeAbb);
                consign.setWaybillCode(hawb.getMasterWaybillCode());
            }else{
                consign.setWaybillCode(null);
            }
            consign.setIsDanger(0);
            consign.setIsValuable(0);
            consign.setActualWeight(hawb.getWeight());
            if (hawb.getTransportInsureValue() != null) {
                consign.setInsurance(1);
                consign.setInsuranceValue(hawb.getTransportInsureValue());
            } else {
                consign.setInsurance(0);
            }
            consign.setShipperName(hawb.getShipper());
            consign.setConsigneeName(hawb.getConsignee());
            consign.setCreateTime(new Date());
            consign.setConsignTime(new Date());
            consign.setId(null);
            String s = SerialNumberGenerator.generateSerialNumber();
            String substring = s.substring(8);
            consign.setCode(substring);
            hawb.setConsignCode(substring);
            hawbService.updateById(hawb);
            String baseAirportCode = airportCodeService.selectChineseName(consign.getDesPort());
            consign.setDesPortStr(baseAirportCode);
//            consign.setInsure(consign.getInsurance() == 0 ? "否" : "是");
//            consign.setDanger(consign.getIsDanger() == 0 ? "NO" : "YES");
//            consign.setValuable(consign.getIsValuable() == 0 ? "NO" : "YES");
            if(consign.getInsurance() == 1){
                consign.setInsure("是");
            }
            if(consign.getIsDanger() == 1){
                consign.setDanger("YES");
            }
            if(consign.getIsValuable() == 1){
                consign.setValuable("YES");
            }
            BigDecimal actualWeight = consign.getActualWeight();
            consign.setActualWeightStr(actualWeight.toString());
            if (consign.getChargeWeight() != null) {
                consign.setChargeWeightStr(consign.getChargeWeight().toString());
            }
            if (consign.getInsuranceValue() != null) {
                consign.setInsuranceValueStr(consign.getInsuranceValue().toString());
            }
            if (StringUtils.isNotEmpty(consign.getShipperSign())) {
                byte[] bytes = downloadFileFromUrl(consign.getShipperSign());
                String signImage = Base64.encode(bytes);
                consign.setSignImage(signImage);
            }
            ClassPathResource resource = new ClassPathResource("electronic/consign.pdf");
            if (resource.exists()) {
                String path = resource.getPath();
                byte[] pdfDataFromTemplate = PdfPrintHelper.getPdfDataFromTemplate(consign, path);
                DiskFileItem fileItem = new DiskFileItem("consign.pdf", "application/pdf", true, "consign.pdf", -1, null);
                fileItem.getOutputStream().write(pdfDataFromTemplate);
                fileItem.getOutputStream().close();
                MultipartFile multipartFile = new CommonsMultipartFile(fileItem);
                SysOss upload = SpringUtils.getBean(SysOssServiceImpl.class).upload(multipartFile);
                consign.setPdfUrl(upload.getUrl());
            }
        }
        consignService.saveOrUpdate(consign);
        return AjaxResult.success(consign.getPdfUrl());
    }
}
