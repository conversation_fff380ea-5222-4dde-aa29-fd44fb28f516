package com.gzairports.common.basedata.service;


import com.gzairports.common.basedata.domain.BaseCityCode;
import com.gzairports.common.basedata.domain.query.CityCodeQuery;

import java.util.List;

/**
 * 城市代码Service接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
public interface ICityCodeService {

    /**
     * 查询城市代码列表
     *
     * @param query 查询参数
     * @return 城市代码列表
     */
    List<BaseCityCode> selectCityCodeList(CityCodeQuery query);

    /**
     * 导入城市代码
     *
     * @param cityCodes 城市代码数据列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    String importCityCode(List<BaseCityCode> cityCodes, boolean updateSupport);

    /**
     * 新增城市代码
     *
     * @param cityCode 城市代码
     * @return 结果
     */
    int addCityCode(BaseCityCode cityCode);

    /**
     * 修改城市代码
     *
     * @param cityCode 城市代码
     * @return 结果
     */
    int editCityCode(BaseCityCode cityCode);

    /**
     * 删除城市代码
     *
     * @param ids 城市代码id集合
     * @return 结果
     */
    int delCityCode(Long[] ids);
}
