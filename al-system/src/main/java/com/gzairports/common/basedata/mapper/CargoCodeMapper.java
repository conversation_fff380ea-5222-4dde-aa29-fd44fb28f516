package com.gzairports.common.basedata.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.basedata.domain.BaseCargoCode;
import com.gzairports.common.basedata.domain.query.CargoCodeQuery;
import com.gzairports.common.charge.domain.vo.IrRelationVo;
import com.gzairports.hz.business.departure.domain.vo.CollectWaybillVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 货站货品代码Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Mapper
public interface CargoCodeMapper extends BaseMapper<BaseCargoCode> {

    /**
     * 查询货站货品代码列表
     *
     * @param query 查询参数
     * @return 货站货品代码列表
     */
    List<BaseCargoCode> selectCargoCodeList(CargoCodeQuery query);

    /**
     * 根据货站货品代码查询货站货品代码
     *
     * @param code 货站货品代码
     * @return 结果
     */
    BaseCargoCode selectCargoCodeByCode(String code);

    /**
     * 新增货站货品代码
     *
     * @param cargoCode 货站货品代码
     * @return 结果
     */
    int insertCargoCode(BaseCargoCode cargoCode);

    /**
     * 修改货站货品代码
     *
     * @param cargoCode 货站货品代码
     * @return 结果
     */
    int updateCargoCode(BaseCargoCode cargoCode);

    /**
     * 删除货站货品代码
     *
     * @param ids 货站货品代码id集合
     * @return 结果
     */
    int delCargoCode(Long[] ids);

    /**
     * 根据id查询货站货品代码
     *
     * @param cargoId 货站货品代码id
     * @return 货站货品代码
     */
    BaseCargoCode selectById(Long cargoId);

    /**
     * 根据code查询货站货品代码
     *
     * @param code 货站货品代码code
     * @return 货站货品代码
     */
    BaseCargoCode selectByCode(String code);

    /**
     * 根据cargoCode查询货站货品代码
     *
     * @param cargoCode 货品代码
     * @return 大类
     */
    List<String> selectCategoryCodeList(String cargoCode);


    List<BaseCargoCode> selectCargoCodeListByCategoryCode(String categoryCode);


    String selectCargoNameByCategoryCode(String cargoCode);

    /**
     * 根据大类得到货品代码,大类可以为空
     * @param categoryCode 查询条件
     * @return 结果
     */
    List<BaseCargoCode> selectCargoCodeListByCodes(@Param("categoryCode") List<String> categoryCode);

    /**
     * 查询大类下面的所有小类
     * @param list 大类
     * @return 小类
     */
    List<IrRelationVo> selectListByCategory(@Param("list") List<String> list);

    IrRelationVo selectIrByName(String ruleCaroName);

    /**
     * 根据货品代码查询信息
     * */
    BaseCargoCode selectCargoInfo(String cargoCode);

    /**
     * 根据名称查询货品代码
     * @param s 名称
     * @return 结果
     */
    String selectByName(String s);

    /**
     * 根据名称查询货品代码
     * @param s 名称
     * @return 结果
     */
    BaseCargoCode selectCodeByName(String s);
}
