package com.gzairports.wl.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 国内分单账单详情运单列表返回参数
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@Data
public class HawbBillVo {

    /** 运单号 */
    private String waybillCode;

    /** 品名 */
    private String cargoName;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 计费重量 */
    private BigDecimal chargeWeight;

    /** 费用 */
    private BigDecimal costSum;

    /** 制单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date writeTime;
}
