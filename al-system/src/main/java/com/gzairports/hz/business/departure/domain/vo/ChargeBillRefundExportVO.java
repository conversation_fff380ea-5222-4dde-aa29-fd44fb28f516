package com.gzairports.hz.business.departure.domain.vo;

import com.gzairports.common.annotation.Excel;
import io.swagger.models.auth.In;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-07-11
 */
@Data
public class ChargeBillRefundExportVO implements FeeSettable{

    private Long id;

    @Excel(name = "序号", needMerge = true, width = 10)
    private Integer idx;

    /** 航班id */
    private Long flightId;

    private String isSettle;

    private BigDecimal totalCharge;

    private String chargeAbb;

    @Excel(name = "运单号", needMerge = true, width = 18)
    private String waybillCode;

    @Excel(name = "代理人", needMerge = true, width = 30)
    private String agent;

    @Excel(name = "件数", needMerge = true, cellType = Excel.ColumnType.NUMERIC, width = 10)
    private Integer quantity;

    @Excel(name = "计费重量", needMerge = true, cellType = Excel.ColumnType.NUMERIC, width = 12)
    private BigDecimal chargeWeight;

    @Excel(name = "处置费", needMerge = true, cellType = Excel.ColumnType.NUMERIC, width = 8)
    private BigDecimal processingFee = new BigDecimal(0);

    @Excel(name = "冷藏费", needMerge = true, cellType = Excel.ColumnType.NUMERIC, width = 12)
    private BigDecimal refrigerationFee = new BigDecimal(0);

    @Excel(name = "搬运费", needMerge = true, cellType = Excel.ColumnType.NUMERIC, width = 8)
    private BigDecimal handlingFee = new BigDecimal(0);

    @Excel(name = "电报费", needMerge = true, cellType = Excel.ColumnType.NUMERIC, width = 12)
    private BigDecimal cableCharge = new BigDecimal(0);

    @Excel(name = "叉车费", needMerge = true, cellType = Excel.ColumnType.NUMERIC, width = 10)
    private BigDecimal forkliftCharge = new BigDecimal(0);

    @Excel(name = "差异化服务费(跨航司)", needMerge = true, cellType = Excel.ColumnType.NUMERIC, width = 20)
    private BigDecimal diffServiceCharge = new BigDecimal(0);

    @Excel(name = "已结算", needMerge = true, cellType = Excel.ColumnType.NUMERIC, width = 8)
    private BigDecimal settleCharge = new BigDecimal(0);

    @Excel(name = "已退款", needMerge = true, cellType = Excel.ColumnType.NUMERIC, width = 8)
    private BigDecimal refundCharge = new BigDecimal(0);

    @Excel(name = "退款时间", needMerge = true, width = 20)
    private String refundTime;
}
