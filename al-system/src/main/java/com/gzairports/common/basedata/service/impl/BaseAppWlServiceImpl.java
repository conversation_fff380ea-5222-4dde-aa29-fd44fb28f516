package com.gzairports.common.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.BaseApp;
import com.gzairports.common.basedata.mapper.BaseAppMapper;
import com.gzairports.common.basedata.mapper.BaseAppWlMapper;
import com.gzairports.common.basedata.service.IBaseAppService;
import com.gzairports.common.basedata.service.IBaseAppWlService;
import com.gzairports.common.constant.CacheConstants;
import com.gzairports.common.core.redis.RedisCache;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * app管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-19
 */
@Service
public class BaseAppWlServiceImpl extends ServiceImpl<BaseAppWlMapper, BaseApp> implements IBaseAppWlService
{
    @Autowired
    private BaseAppWlMapper baseAppMapper;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询app管理
     * 
     * @param id app管理ID
     * @return app管理
     */
    @Override
    public BaseApp selectBaseAppById(Long id)
    {
        BaseApp baseApp = baseAppMapper.selectBaseAppByIdWl(id);
        BigDecimal versionNumber = baseApp.getVersionNumber();
        baseApp.setVersionNumberStr("V"+ versionNumber.toString());
        return baseApp;
    }

    /**
     * 查询app管理列表
     * 
     * @param baseApp app管理
     * @return app管理
     */
    @Override
    public List<BaseApp> selectBaseAppList(BaseApp baseApp)
    {
        List<BaseApp> baseApps = baseAppMapper.selectBaseAppListWl(baseApp);
        for (BaseApp app : baseApps) {
            BigDecimal versionNumber = app.getVersionNumber();
            app.setVersionNumberStr("V"+ versionNumber.toString());
        }
        return baseApps;
    }

    /**
     * 新增app管理
     * 
     * @param baseApp app管理
     * @return 结果
     */
    @Override
    public int insertBaseApp(BaseApp baseApp)
    {
        String verifyKey = CacheConstants.APP_VER_CTRL_KEY_WL;
        String captcha = redisCache.getCacheObject(verifyKey);
        if (StringUtils.isEmpty(captcha)){
//            baseApp.setVersionNumber(new BigDecimal("1.6"));
            baseApp.setVersionNumber(new BigDecimal("1.0"));
        }else {
            baseApp.setVersionNumber(new BigDecimal(captcha).add(new BigDecimal("0.1")));
        }
        redisCache.setCacheObject(verifyKey,baseApp.getVersionNumber().toString());
        baseApp.setUploadTime(new Date());
        baseApp.setStatus("staging");
        return baseAppMapper.insertBaseAppWl(baseApp);
    }

    /**
     * 修改app管理
     * 
     * @param baseApp app管理
     * @return 结果
     */
    @Override
    public int updateBaseApp(BaseApp baseApp)
    {
        return getBaseMapper().updateBaseAppWl(baseApp);
    }

    /**
     * 批量删除app管理
     * 
     * @param ids 需要删除的app管理ID
     * @return 结果
     */
    @Override
    public int deleteBaseAppByIds(Long[] ids)
    {
        return baseAppMapper.deleteBaseAppByIdsWl(ids);
    }

    /**
     * 删除app管理信息
     * 
     * @param id app管理ID
     * @return 结果
     */
    @Override
    public int deleteBaseAppById(Long id)
    {
        return baseAppMapper.deleteBaseAppByIdWl(id);
    }

    /**
     * 发布app
     *
     * @param id app管理ID
     * @return 结果
     */
    @Override
    public int release(Long id) {
        BaseApp baseApp = baseAppMapper.selectBaseAppByIdWl(id);
        baseApp.setStatus("release");
        baseApp.setReleaseTime(new Date());
        return baseAppMapper.updateBaseAppWl(baseApp);
    }

    /**
     * app下载
     * @return 下载地址
     */
    @Override
    public String download() {
        return baseAppMapper.selectOneDownloadWl();
    }

    /**
     * app版本号对比
     * @param version 版本号参数
     * @return 对比结果
     */
    @Override
    public String compare(String version) {
//        BaseApp baseApp = baseAppMapper.selectOne(new QueryWrapper<BaseApp>().eq("status", "release").orderByDesc("release_time").last("limit 1"));
        BaseApp baseApp = baseAppMapper.selectCompareVersionWl();
        BigDecimal decimal = new BigDecimal(version);
        if (baseApp == null){
            throw new CustomException("无APP版本信息");
        }
        if (baseApp.getVersionNumber().compareTo(decimal) != 0){
            return "当前版本过低，请更新";
        }
        return null;
    }
}
