package com.gzairports.wl.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lan
 * @Desc: 安检申报
 * @create: 2024-11-15 10:22
 **/
@Data
public class SecurityInfoWl {
    /** 主键id（运单id） */
    private Long id;

    /** 运单号 */
    private String waybillCode;

    /** 代理人 */
    private String agent;

    /** 托运人 */
    private String shipper;

    /** 品名 */
    private String cargoName;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 航班号 */
    private String flightNo1;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date flightDate1;

    /** 危险品编码 */
    private String dangerCode;

    /** 特货代码 */
    private String specialCargoCode1;

    /** 货物类型 0普货 1特货 2危险品 */
    private Integer cargoType;

    /** 是否特货 0否 1是  */
    private Integer isSpecial;

    /** 安检申报状态 0 未提交 1 已提交 */
    private String securitySubmit;

    /** 安检申报pdf地址 */
    private String securityUrl;

    /** 始发站 */
    private String sourcePort;

    /** 目的站 */
    private String desPort;

    /** 是否与申报一致(审单结果) 0 否(退回) 1 是(符合运输) */
    private Integer declarationConsistent;

    /** 是否审核 0 否 1 是  */
    private Integer isExamine;

    /** 代理人签章 */
    private String agentSignature;

    /** 托运人签章 */
    private String shipperSignature;

    /** 交货人信息 身份证 */
    private String deliveryIdNo;

    /** 交货人信息 头像拍照 */
    private String deliveryProfilePhoto;

    /** 交货人信息 随附文件拍照 */
    private String deliveryFilePhoto;

    /** 品名清单 */
    private String deliveryCargoNames;

    /** 品名清单pdf */
    private String deliveryCargoNamesPdf;

}
