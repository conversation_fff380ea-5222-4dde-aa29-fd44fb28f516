package com.gzairports.wl.ticket.domain.vo;

import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 单证信息列表返回VO数据
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Data
public class TicketVO {

    /** 主键id */
    private Long id;

    /** 部门id */
    private Long deptId;

    /** 票证来源 */
    @Excel(name = "票证来源")
    private String billSource;

    /** 票证来源id */
    private Long billSourceId;

    /** 票证类型 */
    @Excel(name = "票证类型")
    private String billType;

    /** 票证前缀 */
    @Excel(name = "票证前缀")
    private String billPrefix;

    /** 开始编号 */
    @Excel(name = "开始编号")
    private Integer startNumber;

    /** 开始编号检查号 */
    private String startCode;

    /** 结束编号 */
    @Excel(name = "结束编号")
    private Integer endNumber;

    /** 结束编号检查号 */
    private String endCode;

    /** 数量 */
    @Excel(name = "数量")
    private Integer quantity;

    /** 已使用数量 */
    @Excel(name = "已使用数量")
    private Integer usedQuantity;

    /** 剩余数量 */
    @Excel(name = "剩余数量")
    private Integer remainQuantity;

    /** 是否：包舱单（包租整个货舱的运单） */
    private Integer isChartering;

    @Excel(name = "是否包舱单")
    private String charter;

    /** 所属单位 */
    @Excel(name = "所属单位")
    private String department;

    /** 部门 */
    @Excel(name = "部门")
    private String deptName;

    /** 记录人 */
    @Excel(name = "记录人")
    private String recorder;

    /** 记录时间 */
    @Excel(name = "记录时间")
    private Date recordTime;

    /** 状态 */
    private Integer status;

    @Excel(name = "状态")
    private String statusStr;
}
