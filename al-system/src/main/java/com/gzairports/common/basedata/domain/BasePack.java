package com.gzairports.common.basedata.domain;

import lombok.Data;

import java.util.Date;

/**
 * 包装管理
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
public class BasePack {

    /** 主键id */
    private Long id;

    /** 包装 */
    private String pack;

    /** 包装代码 */
    private String packCode;

    /** 适用国内 */
    private Integer domestic;

    /** 适用国际 */
    private Integer international;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 是否删除 */
    private Integer isDel;

    /** 是否公用 */
    private Long isCommon;
}
