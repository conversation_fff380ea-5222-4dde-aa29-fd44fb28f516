package com.gzairports.common.business.departure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.departure.domain.MailWaybill;
import com.gzairports.wl.departure.domain.query.HawbQuery;
import com.gzairports.wl.departure.domain.vo.MailWaybillVo;
import com.gzairports.wl.departure.domain.vo.NewWaybillTraceVO;
import com.gzairports.wl.departure.domain.vo.WaybillTraceVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 邮件单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@Mapper
public interface MailWaybillMapper extends BaseMapper<MailWaybill> {

    /**
     * 根据运单号查询邮件单信息
     * @param waybillCode 运单号
     * @param deptId 部门id
     * @return 结果
     */
    WaybillTraceVo selectOneByCode(@Param("waybillCode") String waybillCode, @Param("deptId") Long deptId);

    /**
     * 根据运单号查询进港和出港的邮件单信息
     * @param waybillCode 运单号
     * @param deptId 部门id
     * @return 结果
     */
    List<WaybillTraceVo> selectAllByCode(@Param("waybillCode") String waybillCode, @Param("deptId") Long deptId);

    /**
     * 邮件单查询
     * @param query 查询条件
     * @return 结果
     */
    List<MailWaybillVo> queryList(HawbQuery query);

}
