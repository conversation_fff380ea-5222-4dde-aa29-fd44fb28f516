package com.gzairports.wl.ticket.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 单证管理日志表
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@Data
@TableName("wl_ticket_log")
public class TicketLog {

    private static final long serialVersionUID = 1L;

    /** 日志主键 */
    private Long operId;

    /** 单证id */
    private Long ticketId;

    /** 请求方式 */
    private String requestMethod;

    /** 业务类型（0其它 1新增 2修改 3删除） */
    private Integer businessType;

    /** 操作人员 */
    private String operName;

    /** 请求参数 */
    private String operParam;

    /** 返回参数 */
    private String jsonResult;

    /** 操作状态（0正常 1异常） */
    private Integer status;

    /** 错误消息 */
    private String errorMsg;

    /** 操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operTime;
}
