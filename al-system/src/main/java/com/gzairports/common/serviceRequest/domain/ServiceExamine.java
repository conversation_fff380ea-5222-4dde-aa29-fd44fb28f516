package com.gzairports.common.serviceRequest.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 服务申请审核表
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Data
@TableName("all_service_examine")
public class ServiceExamine {

    /** 主键id */
    private Long id;

    /** 服务申请表id */
    private Long serviceId;

    /** 审核状态（PASS 通过 NOPASS 不通过） */
    private String status;

    /** 审核内容 */
    private String content;

    /** 操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 操作人员 */
    private String userName;

    /** 类型 */
    private String type;
}
