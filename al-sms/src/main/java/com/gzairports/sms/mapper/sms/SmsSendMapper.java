package com.gzairports.sms.mapper.sms;


import com.gzairports.sms.domain.HzAgentUser;
import com.gzairports.sms.domain.dto.SmsReceiveUserDto;
import com.gzairports.sms.domain.vo.SmsReceiveUserVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 短信发送Mapper
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Mapper
public interface SmsSendMapper {

    int agentSendSmsConfig(@Param("agentUserList") List<HzAgentUser> agentUserList);

    List<SmsReceiveUserVo> selectSmsReceiveUser(SmsReceiveUserDto smsReceiveUserDto);

    void deleteAgentSendSmsConfig(@Param("agentIds") List<Long> agentIds);

    @Select("select * from hz_fbm_sms_agent_user where agent_id = #{agentId} and user_id = #{userId}")
    HzAgentUser selectByAgentIdAndUserId(@Param("agentId") Long agentId, @Param("userId") Long userId);

    List<HzAgentUser> selectByAgentIds(@Param("agentIds") List<Long> agentIds);

    @Select("select id from base_agent where dept_id = #{topDeptId}")
    Long selectAgentIdByDeptId(Long topDeptId);

}
