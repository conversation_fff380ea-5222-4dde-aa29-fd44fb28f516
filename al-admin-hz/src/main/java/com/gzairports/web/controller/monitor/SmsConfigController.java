package com.gzairports.web.controller.monitor;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.R;
import com.gzairports.sms.domain.dto.HzAgentUserDto;
import com.gzairports.sms.domain.dto.SmsReceiveUserDto;
import com.gzairports.sms.domain.vo.SmsReceiveUserVo;
import com.gzairports.sms.service.ISmsSendService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 财务余额监控短信配置Controller
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Valid
@RestController
@RequestMapping("/sms/monitor")
@Api(value = "财务余额监控短信配置", tags = {"财务余额监控短信配置"})
public class SmsConfigController extends BaseController {

    @Autowired
    private ISmsSendService smsSendService;

    /**
     * 查询短信接收人列表
     */
//    @PreAuthorize("@ss.hasPermi('sms:record:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询短信接收人列表")
    public R<List<SmsReceiveUserVo>> list(SmsReceiveUserDto dto) {
        List<SmsReceiveUserVo> list = smsSendService.listSmsReceiveUser(dto);
        return R.ok(list);
    }

    /**
     * 配置代理人需发送短信的用户 HzAgentUser
     */
//    @PreAuthorize("@ss.hasPermi('sms:record:list')")
    @PostMapping("/config")
    @ApiOperation(value = "配置代理人需发送短信的用户")
    public R<Void> configAgentSendSmsUser(@RequestBody List<HzAgentUserDto> agentUserDtoList) {
        smsSendService.configAgentSendSmsUser(agentUserDtoList);
        return R.ok();
    }

}
