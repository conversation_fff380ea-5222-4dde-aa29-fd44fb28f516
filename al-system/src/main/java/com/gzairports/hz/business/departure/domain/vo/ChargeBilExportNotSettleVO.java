package com.gzairports.hz.business.departure.domain.vo;


import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ChargeBilExportNotSettleVO implements FeeSettable{
    /**
     * 主键id
     */
    private Long id;

    @Excel(name = "序号", needMerge = true, width = 10)
    private Integer idx;

    @Excel(name = "运单号", needMerge = true, width = 18)
    private String waybillCode;

    @Excel(name = "件数", needMerge = true, cellType = Excel.ColumnType.NUMERIC, width = 10)
    private Integer quantity;

    @Excel(name = "计费重量", needMerge = true, cellType = Excel.ColumnType.NUMERIC, width = 12)
    private BigDecimal chargeWeight;

    @Excel(name = "处置费", needMerge = true, cellType = Excel.ColumnType.NUMERIC, width = 8)
    private BigDecimal processingFee = new BigDecimal(0);

    @Excel(name = "冷藏费", needMerge = true, cellType = Excel.ColumnType.NUMERIC, width = 12)
    private BigDecimal refrigerationFee = new BigDecimal(0);

    @Excel(name = "搬运费", needMerge = true, cellType = Excel.ColumnType.NUMERIC, width = 8)
    private BigDecimal handlingFee = new BigDecimal(0);

    @Excel(name = "电报费", needMerge = true, cellType = Excel.ColumnType.NUMERIC, width = 12)
    private BigDecimal cableCharge = new BigDecimal(0);

    @Excel(name = "叉车费", needMerge = true, cellType = Excel.ColumnType.NUMERIC, width = 10)
    private BigDecimal forkliftCharge = new BigDecimal(0);

    @Excel(name = "差异化服务费(跨航司)", needMerge = true, cellType = Excel.ColumnType.NUMERIC, width = 20)
    private BigDecimal diffServiceCharge = new BigDecimal(0);

    @Excel(name = "未结算", needMerge = true, cellType = Excel.ColumnType.NUMERIC, width = 10)
    private BigDecimal notSettleCharge = new BigDecimal(0);

    @Excel(name = "已结算", needMerge = true, cellType = Excel.ColumnType.NUMERIC, width = 10)
    private BigDecimal settleCharge = new BigDecimal(0);

    @Excel(name = "结算时间", needMerge = true, width = 20)
    private String settleTime;

    /**
     * 收费项目名称
     */
    private String chargeAbb;

    /**
     * 费用值
     */
    private BigDecimal totalCharge;

    /**
     * 运单状态
     */
    private String status;

    /**
     * 类型:0 预授权支付明细， 1 已结算支付明细',
     */
    private String type;


    private String isSettle;
}