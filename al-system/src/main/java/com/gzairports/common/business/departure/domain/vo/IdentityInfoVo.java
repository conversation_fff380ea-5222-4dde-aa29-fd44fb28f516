package com.gzairports.common.business.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 代理人经办人员身份证信息
 *
 * <AUTHOR>
 * @date 2024-11-04
 */
@Data
public class IdentityInfoVo {

    /** 姓名 */
    private String username;

    /** 民族 */
    private String nation;

    /** 出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthday;

    /** 身份证号码 */
    private String idNumber;

    /** 身份证头像 */
    private String idUrl;

    /** 性别: 男,女 */
    private String sex;

    /** 地址 */
    private String address;
}
