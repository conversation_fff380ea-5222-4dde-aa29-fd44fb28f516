package com.gzairports.wl.charge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.BaseAirportCode;
import com.gzairports.common.basedata.domain.BaseCargoCode;
import com.gzairports.common.basedata.domain.BaseCityCode;
import com.gzairports.common.basedata.mapper.AirportCodeMapper;
import com.gzairports.common.basedata.mapper.CityCodeMapper;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.wl.charge.domain.FreightRateAir;
import com.gzairports.wl.charge.domain.FreightRateAirItem;
import com.gzairports.wl.charge.domain.FreightRateCustom;
import com.gzairports.wl.charge.domain.FreightRateCustomItem;
import com.gzairports.wl.charge.domain.query.FreightRateItemQuery;
import com.gzairports.wl.charge.domain.vo.FreightRateItemVO;
import com.gzairports.wl.charge.mapper.FreightRateCustomItemMapper;
import com.gzairports.wl.charge.mapper.FreightRateCustomMapper;
import com.gzairports.wl.charge.service.FreightRateCustomItemService;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import org.mybatis.spring.MyBatisSystemException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 客户运价条目service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@Service
public class FreightRateCustomItemServiceImpl extends ServiceImpl<FreightRateCustomItemMapper, FreightRateCustomItem>
        implements FreightRateCustomItemService {

    @Autowired
    private FreightRateCustomItemMapper customItemMapper;

    @Autowired
    private FreightRateCustomMapper customMapper;

    @Autowired
    private CityCodeMapper cityCodeMapper;

    /**
     * 查询运价条目详情
     *
     * @param id 运价条目主键
     * @return 运价条目详情
     */
    @Override
    public FreightRateCustomItem selectCustomItemById(Long id) {
        FreightRateCustomItem freightRateCustomItem = customItemMapper.selectById(id);
        FreightRateCustom freightRateCustom = customMapper.selectById(freightRateCustomItem.getRateId());
        freightRateCustomItem.setRateName(freightRateCustom.getRateName());
        return freightRateCustomItem;
    }

    /**
     * 新增运价条目
     *
     * @param item 运价条目
     * @return 结果
     */
    @Override
    public int insertCustomItem(FreightRateCustomItem item) {
        FreightRateCustomItem freightRateCustomItem = customItemMapper.selectOne(new QueryWrapper<FreightRateCustomItem>()
                .eq("rate_id",item.getRateId())
                .eq("applicable_documents",item.getApplicableDocuments())
                .eq("departure_city", item.getDepartureCity())
                .eq("destination_city", item.getDestinationCity())
                .eq("is_del",0));
        if (freightRateCustomItem != null){
            throw new CustomException("已存在相同运价条目数据");
        }
        item.setCreateBy(SecurityUtils.getUsername());
        item.setCreateTime(DateUtils.getNowDate());
        item.setDeptId(SecurityUtils.getHighParentId());
        return customItemMapper.insert(item);
    }

    /**
     * 修改运价条目
     *
     * @param item 运价条目
     * @return 结果
     */
    @Override
    public int updateCustomItem(FreightRateCustomItem item) {
        item.setUpdateBy(SecurityUtils.getUsername());
        item.setUpdateTime(DateUtils.getNowDate());
        return customItemMapper.updateById(item);
    }

    /**
     * 删除运价条目
     *
     * @param id 运价条目主键
     * @return 结果
     */
    @Override
    public int removeCustomItem(Long id) {
        return customItemMapper.removeCustomItem(id);
    }

    /**
     * 批量删除客户运价条目
     *
     * @param ids 运价条目主键
     * @return 结果
     */
    @Override
    public void deleteAllFreightRateCustomItem(Long[] ids) {
        if (ids != null){
            Date date = new Date();
            Long id = ids[0];
            FreightRateCustomItem customItem = customItemMapper.selectById(id);
            FreightRateCustom custom = customMapper.selectById(customItem.getRateId());
            if (custom.getEffectiveDate().before(date) && custom.getExpirationDate().after(date)){
                throw new CustomException("在有效期内的运价不能删除");
            }
            customItemMapper.deleteBatchIds(Arrays.asList(ids));
        }
    }

    /**
     * 查询运价条目列表
     *
     * @param query 运价条目查询参数
     * @return 结果
     */
    @Override
    public List<FreightRateItemVO> selectList(FreightRateItemQuery query) {
        //由于前端传递的是城市名称但是需要的是城市code,所以需要在base_airport_code查一下得到code(机场代码)
//        if (query.getDepartureCity() != null) {
//            try {
//                List<BaseCityCode> baseAirportCodes = cityCodeMapper.selectCityListByName(query.getDepartureCity());
//                if (CollectionUtils.isEmpty(baseAirportCodes)) {
//                    throw new CustomException("暂无该城市对应信息");
//                }
//                List<String> sourcePorts = new ArrayList<>();
//                for (BaseCityCode baseAirportCode : baseAirportCodes) {
//                    sourcePorts.add(baseAirportCode.getCode());
//                }
//                query.setSourcePort(sourcePorts);
//            } catch (MyBatisSystemException e) {
//                throw new CustomException("请输入更精准的城市名");
//            }
//        }
//        if (query.getDestinationCity() != null) {
//            try {
//                List<BaseCityCode> baseAirportCodes = cityCodeMapper.selectCityListByName(query.getDestinationCity());
//                if (CollectionUtils.isEmpty(baseAirportCodes)) {
//                    throw new CustomException("暂无该城市对应信息");
//                }
//                List<String> desPort = new ArrayList<>();
//                for (BaseCityCode baseAirportCode : baseAirportCodes) {
//                    desPort.add(baseAirportCode.getCode());
//                }
//                query.setDesPort(desPort);
//            } catch (MyBatisSystemException e) {
//                throw new CustomException("请输入更精准的城市名");
//            }
//        }
        List<FreightRateItemVO> freightRateItemVOS = customItemMapper.selectCustomItemList(query);
        freightRateItemVOS.forEach(item -> {
            if((item.getStartTime().before(new Date()) || item.getStartTime().equals(new Date())) &&
                    (item.getEndTime().after(new Date()) || item.getEndTime().equals(new Date()))){
                item.setIsEffective(1);
            }else{
               item.setIsEffective(0);
            }
        });
        return freightRateItemVOS;
    }
}
