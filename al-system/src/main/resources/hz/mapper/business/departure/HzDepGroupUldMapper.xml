<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.departure.mapper.HzDepGroupUldMapper">
    <select id="selectTruckInfoById" resultType="com.gzairports.hz.business.departure.domain.vo.TruckInfoVo">
        select hdgu.id, hdgu.uld, hdgu.des1, hdgu.cabin, hdgu.repeat_weight, bcu.dead_weight from hz_dep_group_uld hdgu
            left join base_cargo_uld bcu on hdgu.uld = CONCAT(bcu.type,bcu.code)
            where hdgu.id = #{id}
    </select>
</mapper>