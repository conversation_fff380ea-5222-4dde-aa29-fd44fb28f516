package com.gzairports.common.serviceProcessing.service;


import com.gzairports.common.serviceProcessing.domain.ServiceProcessing;
import com.gzairports.common.serviceProcessing.domain.query.ServiceProcessingQuery;
import com.gzairports.common.serviceProcessing.domain.vo.ServiceProcessingPayVo;
import com.gzairports.common.serviceProcessing.domain.vo.ServiceProcessingVo;

/**
 * 自助办单Service接口
 *
 * <AUTHOR>
 * @date 2024-11-04
 */
public interface IServiceProcessingService {
    ServiceProcessingVo pickUpOne(ServiceProcessing serviceProcessing);

    ServiceProcessingPayVo pay(ServiceProcessingQuery vo);

    ServiceProcessingPayVo printOutOrder(Long pickUpId);

    String comparison(ServiceProcessingQuery vo);
}
