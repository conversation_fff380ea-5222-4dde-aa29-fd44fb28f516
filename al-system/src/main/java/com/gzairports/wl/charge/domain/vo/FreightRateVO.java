package com.gzairports.wl.charge.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 公布运价返回数据
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@Data
public class FreightRateVO {

    /** 主键id */
    private Long id;

    /** 运价名称 */
    @Excel(name = "运价名称")
    private String rateName;

    /** 适用单证类型 */
    private String applicableDocuments;

    @Excel(name = "适用单证类型")
    private String applicable;

    /** 货品代码 */
    @Excel(name = "货品代码")
    private String cargoCode;

    /** 是否可编辑 */
    private Integer editable;

    @Excel(name = "是否可编辑")
    private String edit;

    /** 是否自动从低 */
    private Integer automaticFromLow;

    @Excel(name = "是否自动从低")
    private String automatic;

    /** 取舍方式 */
    private Integer roundingMethod;

    @Excel(name = "取舍方式")
    private String rounding;

    /** 生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生效日期", dateFormat = "yyyy-MM-dd")
    private Date effectiveDate;

    /** 截止日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "截止日期", dateFormat = "yyyy-MM-dd")
    private Date expirationDate;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;
}
