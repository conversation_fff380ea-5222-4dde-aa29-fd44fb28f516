package com.gzairports.hz.business.departure.domain.query;

import lombok.Data;

import java.util.Date;

/**
 * 退货管理查询参数
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
public class ExitCargoQuery {

    /** 开单时间 */
    private Date startWriteTime;

    /** 开单时间 */
    private Date endWriteTime;

    /** 发货代理人 */
    private String agentCode;

    /** 退仓状态 */
    private String exitStatus;

    /** 运单号 */
    private String waybillCode;
}
