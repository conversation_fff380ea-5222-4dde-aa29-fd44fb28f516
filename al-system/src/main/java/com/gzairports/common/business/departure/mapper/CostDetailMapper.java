package com.gzairports.common.business.departure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.arrival.domain.vo.ArrItemVo;
import com.gzairports.common.business.departure.domain.CostDetail;
import com.gzairports.common.business.departure.domain.vo.CostDetailSumVo;
import com.gzairports.common.business.reporter.domain.ReportDepPayCost;
import com.gzairports.common.business.reporter.domain.ReportDepSettleCost;
import com.gzairports.common.business.reporter.domain.vo.ReportBillInfoVo;
import com.gzairports.hz.business.departure.domain.HzDepPullDown;
import com.gzairports.hz.business.departure.domain.query.BillExportQuery;
import com.gzairports.hz.business.departure.domain.query.ChargeQuery;
import com.gzairports.hz.business.departure.domain.query.WaybillDeptQuery;
import com.gzairports.hz.business.departure.domain.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 出港费用明细Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Mapper
public interface CostDetailMapper extends BaseMapper<CostDetail> {

    /**
     * 根据运单id和类型查询费用明细
     * @param code 运单code
     * @param i 0 预授权支付明细， 1 已结算支付明细
     * @param isSettle 是否支付
     * @return 费用明细
     */
    List<CostDetail> selectPayOrSettleList(@Param("code") String code,@Param("i") int i, @Param("isSettle") int isSettle,@Param("deptId") Long deptId);

    /**
     * 根据运单id和类型查询费用明细
     * @param code 运单code
     * @param i 0 预授权支付明细， 1 已结算支付明细
     * @param isSettle 是否支付
     * @return 费用明细
     */
    List<CostDetail> selectPayOrSettleByWaybills(@Param("codes") List<String> code,@Param("i") int i, @Param("isSettle") int isSettle,@Param("deptId") Long deptId);


    /**
     * 同上  加了一个时间过滤
     * */
    List<CostDetail> selectPayOrSettleListBySettleTime(@Param("code") String code,@Param("i") int i,
                                           @Param("isSettle") int isSettle, @Param("deptId") Long deptId,
                                           @Param("query") ChargeQuery query);

    List<CostDetail> selectPayOrSettleListBySettleTimeAgent(@Param("code") String code,@Param("i") int i,@Param("deptId") Long deptId,
                                           @Param("isSettle") int isSettle, @Param("query") ChargeQuery query);

    /**
     * 根据运单号查询收费项目
     * @param waybillCode 运单号
     * @return 收费项目
     */
    List<CostDetail> selectListByCode(@Param("type") Integer type, @Param("isSettle") Integer isSettle, @Param("waybillCode") String waybillCode,@Param("deptId") Long deptId);

    List<CostDetail> depAutoSettleTask(@Param("waybillCode") String waybillCode,@Param("deptId") Long deptId);

    BigDecimal depIsSettleData(@Param("waybillCode") String waybillCode, @Param("deptId") Long deptId);

    CostDetail selectCostInfo(Long id);

    List<ArrItemVo> selectItemVo(String waybillCode);

    List<ArrItemVo> selectItemVoBySettleTime(@Param("waybillCode") String waybillCode,
                                             @Param("query") ChargeQuery query);

    List<CostDetail> selectItemList(@Param("waybillCode") String waybillCode,
                                    @Param("deptId") Long deptId,
                                    @Param("serviceRequestId") Long serviceRequestId);

    Long selectFlightInfo(@Param("waybillCode") String waybillCode, @Param("deptId") Long deptId);

    List<CostDetail> selectSettleByFlightId(@Param("waybillCode") String waybillCode,@Param("deptId") Long deptId,@Param("flightId") Long flightId);

    void delWaitSettleTask(@Param("waybillCode") String waybillCode, @Param("deptId") Long deptId);

    List<Long> selectFlights(@Param("waybillCode") String waybillCode, @Param("deptId") Long deptId);

    List<CostDetail> selectIsSettle(@Param("waybillCode") String waybillCode, @Param("deptId") Long deptId);

    List<CostDetail> selectSettleCount(@Param("waybillCode") String waybillCode, @Param("deptId") Long deptId);

    CostDetail selectSettleInfo(@Param("waybillCode") String waybillCode,@Param("deptId") Long deptId,@Param("flightId") Long flightId);

    List<CostDetail> selectCarryList(@Param("waybillCode") String waybillCode,@Param("deptId") Long deptId,@Param("flightId") Long flightId);

    List<ReportDepPayCost> selectReportList(@Param("keys") List<ReportBillInfoVo> keys);

    List<ReportDepSettleCost> selectSettleList(@Param("keys") List<ReportBillInfoVo> keys);

    List<ChargeBilExportVO> selectExportItemVo(@Param("query") BillExportQuery query,
                                               @Param("type") Integer type,
                                               @Param("isSettle") Integer isSettle);


    List<ChargeBillExportVoAgent> selectExportItemVoAgent(@Param("query") BillExportQuery query,
                                                          @Param("type") Integer type,
                                                          @Param("isSettle") Integer isSettle);

    List<ChargeBilExportVO> selectFutureFlightItemVo(@Param("query") BillExportQuery query,
                                               @Param("type") Integer type,
                                               @Param("isSettle") Integer isSettle);

    /**
     * 结算费用导出查询 1.8到截止时间的制单支付运单支付明细
     * */
    List<ChargeBilExportNotSettleVO> selectNotSettleData(@Param("query") BillExportQuery query,
                                                         @Param("type") Integer type,
                                                         @Param("isSettle") Integer isSettle);


    List<ChargeBillExportNotSettleVoAgent> selectNotSettleDataAgent(@Param("query") BillExportQuery query,
                                                               @Param("type") Integer type,
                                                               @Param("isSettle") Integer isSettle);

    /**
     * 结算费用导出查询 1.8到截止时间的制单支付运单 且配载时间在截止时间之后的 支付明细
     * */
    List<ChargeBilExportNotSettleVO> selectFlightNotSettleData(@Param("query") BillExportQuery query,
                                                     @Param("type") Integer type,
                                                     @Param("isSettle") Integer isSettle);

    List<ChargeBillExportNotSettleVoAgent> selectFlightNotSettleDataAgent(@Param("query") BillExportQuery query,
                                                     @Param("type") Integer type,
                                                     @Param("isSettle") Integer isSettle);

    List<ChargeBilExportVO> selectChargeItems(@Param("waybillCodes") List<String> waybillCodes);

    List<CostDetail> selectSettleByTime(@Param("waybillCode") String waybillCode,@Param("query") ChargeQuery query);

    List<HzDepPullDown> selectPullAllNotSettle(BillExportQuery query);
    List<HzDepPullDown> selectPullAllNotSettleAgent(BillExportQuery query);

    List<ChargeBilExportNotSettleVO> selectPullAllNotSettleData(@Param("waybillCodes")List<String> waybillNotSettlePullList);

    List<ChargeBillExportNotSettleVoAgent> selectPullAllNotSettleDataAgent(@Param("waybillCodes")List<String> waybillNotSettlePullList);

    CostDetailSumVo selectPayAndSettleSumByWaybillCode(String waybillCode);

    CostDetailSumVo selectPayAndSettleSumByWaybillCodeRefund(@Param("waybillCode") String waybillCode,@Param("endTime") LocalDateTime endTime);

    List<ChargeBillRefundExportVO> selectRefundAmountList(@Param("reduceCurrentWaybillCodeList") List<String> reduceCurrentWaybillCodeList);

    List<ChargeBillRefundExportVO> selectRefundAmountListNew(@Param("reduceCurrentWaybillCodeList") List<String> reduceCurrentWaybillCodeList);

    List<CostDetail> selectListByCodeList(@Param("waybillCodeFlightList") List<String> waybillCodeFlightList);

    List<CostDetail> selectDetailListByCode(@Param("waybillCode") String waybillCode,@Param("isSettle") Integer isSettle);

    List<ChargeBilExportNotSettleVO> selectSettledNotCurrentList(@Param("waybillCodeList") List<String> settledNotCurrentWaybillCodeList);


    List<ChargeBilExportFutureFlightVO> selectFutureFlightItem(@Param("query") BillExportQuery query,
                                                               @Param("type") Integer type,
                                                               @Param("isSettle") Integer isSettle,
                                                               @Param("waybillCodeList") List<String> waybillCodeList);
    /** 支付但未结算运单 */
    List<ChargeBilExportNotSettleVO> selectPayWaybillNotSettle(@Param("query") BillExportQuery query);

    List<CostDetail> selectPayOrSettleListBySettle(@Param("keys") List<WaybillDeptQuery> keys);

    List<CostDetail> selectListByWaybillcodeList(@Param("codeList") List<String> codeList);

    List<CostDetailSumVo> selectPayAndSettleSumByCodeList(@Param("collect") List<String> collect);

    List<CostDetail> selectPayOrSettleData(@Param("keys") List<WaybillDeptQuery> keys,@Param("type") int type,@Param("isSettle") int isSettle);

    List<CostDetail> selectSettleByCodeList(@Param("waybillCodes") List<String> waybillCodes);

    List<CostDetail> selectRefundList(@Param("keys") List<WaybillDeptQuery> keys);

    List<ChargeBilExportNotSettleVO> selectNotSettleDataNew(@Param("query") BillExportQuery query,
                                  @Param("type") Integer type,
                                  @Param("isSettle") Integer isSettle);

    BigDecimal selectAllPostSum(@Param("waybillCodeList") List<String> waybillCodeList);

    BigDecimal selectAllSettleSum(@Param("notLoadWaybillList") List<String> notLoadWaybillList);

    List<ChargeBilExportNotSettleVO> selectFlightRefundData(BillExportQuery query);

    List<ChargeBilExportNotSettleVO> selectCancelPayData(BillExportQuery query);

    CostDetailSumVo selectPayAndSettleSumNew(@Param("waybillCode") String waybillCode,@Param("startTime") LocalDateTime startTime,@Param("endTime") LocalDateTime endTime);

    CostDetailSumVo selectPayAndSettleSumNewByFlightTime(@Param("waybillCode") String waybillCode,@Param("startTime") LocalDateTime startTime,@Param("endTime") LocalDateTime endTime);

    List<ChargeBilExportVO> selectByFlightIds(@Param("flightIdList") List<Long> flightIdList,@Param("deptId") Long deptId);

    List<ChargeBilExportNotSettleVO> selectNotCurrentAmountList(@Param("filteredList") List<LoadFlightWaybillVO> filteredList);

    List<ChargeBilExportNotSettleVO> selectPayList(@Param("loadWaybillList") List<String> loadWaybillList);

    List<ChargeBilExportNotSettleVO> selectRefundListNew(@Param("loadWaybillList") List<String> loadWaybillList);

    List<ChargeBilExportVO> selectByFlightWaybills(@Param("waybillVoList") List<FlightLoadWaybillVO> waybillVoList, @Param("deptId") Long deptId);

    List<ChargeBillArrSettleExportVO> selectArrBillExport(@Param("query") BillExportQuery query,@Param("deptId") Long deptId);
}
