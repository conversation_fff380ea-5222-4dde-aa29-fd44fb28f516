package com.gzairports.common.business.reporter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.reporter.domain.ReportDataWaybill;
import com.gzairports.wl.reporter.domain.query.CargoAnalysisQuery;
import com.gzairports.wl.reporter.domain.query.CarrierSumQuery;
import com.gzairports.wl.reporter.domain.vo.AirlineWaybillVO;
import com.gzairports.wl.reporter.domain.vo.ReportWaybillInfoVO;
import com.gzairports.wl.reporter.domain.vo.WaybillWeightVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-03
 */
@Mapper
public interface ReportDataWaybillMapper extends BaseMapper<ReportDataWaybill> {

    List<WaybillWeightVO> yearCargoAnalyse(@Param("year") String year,@Param("deptId") Long deptId);

    List<ReportWaybillInfoVO> yearProfit(@Param("year") String year, @Param("deptId") Long deptId, @Param("code") String code);

    List<WaybillWeightVO> baseCargoAnalysis(CargoAnalysisQuery query);

    List<WaybillWeightVO> compareCargoAnalysis(CargoAnalysisQuery query);

    List<ReportWaybillInfoVO> monthCargoAnalysis(@Param("month") String month,@Param("deptId") Long highParentId);

    List<AirlineWaybillVO> airlineProfit(@Param("deptId") Long deptId,@Param("month") String month);

    List<WaybillWeightVO> selectBillWeightList(@Param("deptId") Long highParentId,@Param("month") String month);

    List<ReportWaybillInfoVO> selectWaybillCount(@Param("deptId") Long highParentId,@Param("writer") String s,@Param("month") String month);

    List<ReportWaybillInfoVO> carrierDayList(CarrierSumQuery query);
}
