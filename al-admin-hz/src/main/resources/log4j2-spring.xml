<?xml version="1.0" encoding="UTF-8"?>
<!--Configuration后面的status，这个用于设置log4j2自身内部的信息输出，可以不设置，当设置成trace时，你会看到log4j2内部各种详细输出-->
<!--monitorInterval：Log4j能够自动检测修改配置 文件和重新配置本身，设置间隔秒数-->
<configuration monitorInterval="5">
    <!--日志级别以及优先级排序: OFF > FATAL > ERROR > WARN > INFO > DEBUG > TRACE > ALL -->

    <!--变量配置-->
    <Properties>
        <!-- 格式化输出：%date表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度 %msg：日志消息，%n是换行符-->
        <!-- %logger{36} 表示 Logger 名字最长36个字符 -->
<!--        <property name="LOG_PATTERN" value="%date{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n" />-->
        <property name="LOG_PATTERN" value="[%d{DEFAULT}{GMT+8}] [%p] [%thread] - %l - %m%n" />

        <!-- 定义日志存储的路径 -->
        <property name="FILE_PATH" value="logs/" />
        <property name="FILE_NAME" value="info.log" />
        <property name="MQ_FILE_NAME" value="mq.log" />
        <property name="REPORT_NAME" value="report.log" />
        <property name="Flight_FILE_NAME" value="flight.log" />
        <property name="BANK_FILE_NAME" value="bank.log" />
    </Properties>

    <appenders>

        <console name="Console" target="SYSTEM_OUT">
            <!--输出日志的格式-->
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <!--控制台只输出level及其以上级别的信息（onMatch），其他的直接拒绝（onMismatch）-->
            <ThresholdFilter level="debug" onMatch="ACCEPT" onMismatch="DENY"/>
        </console>

        <!--文件会打印出所有信息，这个log每次运行程序会自动清空，由append属性决定，适合临时测试用-->
      <!--  <File name="Filelog" fileName="${FILE_PATH}/test.log" append="false">
            <PatternLayout pattern="${LOG_PATTERN}"/>
        </File>-->

        <!-- 这个会打印出所有的info及以下级别的信息，每次大小超过size，则这size大小的日志会自动存入按年份-月份建立的文件夹下面并进行压缩，作为存档-->
        <RollingFile name="RollingFileInfo" fileName="${FILE_PATH}/info.log" filePattern="${FILE_PATH}/info-%d{yyyy-MM-dd}_%i.log.gz">
            <!--控制台只输出level及以上级别的信息（onMatch），其他的直接拒绝（onMismatch）-->
<!--
            <ThresholdFilter level="WARN" onMatch="DENY" onMismatch="NEUTRAL"/>
-->
            <ThresholdFilter level="ERROR" onMatch="DENY" onMismatch="NEUTRAL"/>
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <!--interval属性用来指定多久滚动一次，默认是1 hour-->
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <!-- DefaultRolloverStrategy属性如不设置，则默认为最多同一文件夹下7个文件开始覆盖-->
            <DefaultRolloverStrategy>
                <Delete basePath="${FILE_PATH}" maxDepth="2">
                    <IfFileName glob="info-*.log.gz"/>
                    <IfLastModified age="15d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>

        <!-- 这个会打印出所有的error及以下级别的信息，每次大小超过size，则这size大小的日志会自动存入按年份-月份建立的文件夹下面并进行压缩，作为存档-->
        <RollingFile name="RollingFileError" fileName="${FILE_PATH}/error.log" filePattern="${FILE_PATH}/error-%d{yyyy-MM-dd}_%i.log.gz">
            <!--控制台只输出level及以上级别的信息（onMatch），其他的直接拒绝（onMismatch）-->
            <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <!--interval属性用来指定多久滚动一次，默认是1 hour-->
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <!-- DefaultRolloverStrategy属性如不设置，则默认为最多同一文件夹下7个文件开始覆盖-->
            <DefaultRolloverStrategy>
                <Delete basePath="${FILE_PATH}" maxDepth="2">
                    <IfFileName glob="error-*.log.gz"/>
                    <IfLastModified age="15d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>


        <RollingFile name="RollingFileMQ" fileName="${FILE_PATH}/${MQ_FILE_NAME}" filePattern="${FILE_PATH}/mq-%d{yyyy-MM-dd}_%i.log.gz">
            <ThresholdFilter level="ERROR" onMatch="DENY" onMismatch="NEUTRAL"/>
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy>
                <Delete basePath="${FILE_PATH}" maxDepth="2">
                    <IfFileName glob="mq-*.log.gz"/>
                    <IfLastModified age="15d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>

        <RollingFile name="RollingFileReport" fileName="${FILE_PATH}/${REPORT_NAME}" filePattern="${FILE_PATH}/report-%d{yyyy-MM-dd}_%i.log.gz">
            <ThresholdFilter level="ERROR" onMatch="DENY" onMismatch="NEUTRAL"/>
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy>
                <Delete basePath="${FILE_PATH}" maxDepth="2">
                    <IfFileName glob="report-*.log.gz"/>
                    <IfLastModified age="15d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>

        <RollingFile name="RollingFileFlight" fileName="${FILE_PATH}/${Flight_FILE_NAME}" filePattern="${FILE_PATH}/flight-%d{yyyy-MM-dd}_%i.log.gz">
            <MarkerFilter marker = "FLIGHT" onMatch = "ACCEPT" onMismatch = "DENY" />
            <ThresholdFilter level="ERROR" onMatch="DENY" onMismatch="NEUTRAL"/>
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy>
                <Delete basePath="${FILE_PATH}" maxDepth="2">
                    <IfFileName glob="flight-*.log.gz"/>
                    <IfLastModified age="15d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>

        <RollingFile name="RollingFileCableInfo" fileName="${FILE_PATH}/cable-info.log" filePattern="${FILE_PATH}/cable-info-%d{yyyy-MM-dd}_%i.log.gz">
            <MarkerFilter marker="CABLE-INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy>
                <Delete basePath="${FILE_PATH}" maxDepth="2">
                    <IfFileName glob="cable-info-*.log.gz"/>
                    <IfLastModified age="15d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>

        <RollingFile name="RollingFileCableError" fileName="${FILE_PATH}/cable-error.log" filePattern="${FILE_PATH}/cable-error-%d{yyyy-MM-dd}_%i.log.gz">
            <MarkerFilter marker="CABLE-ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy>
                <Delete basePath="${FILE_PATH}" maxDepth="2">
                    <IfFileName glob="cable-error-*.log.gz"/>
                    <IfLastModified age="15d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>


        <RollingFile name="RollingFileBANK" fileName="${FILE_PATH}/${BANK_FILE_NAME}" filePattern="${FILE_PATH}/bank-%d{yyyy-MM-dd}_%i.log.gz">
            <ThresholdFilter level="ERROR" onMatch="DENY" onMismatch="NEUTRAL"/>
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy>
                <Delete basePath="${FILE_PATH}" maxDepth="2">
                    <IfFileName glob="bank-*.log.gz"/>
                    <IfLastModified age="15d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>

    </appenders>

    <!--Logger节点用来单独指定日志的形式，比如要为指定包下的class指定不同的日志级别等。-->
    <!--然后定义loggers，只有定义了logger并引入的appender，appender才会生效-->
    <loggers>

        <!--过滤掉spring和mybatis的一些无用的DEBUG信息-->
      <!--  <logger name="com.gzairports.common.system.mapper" level="TRACE" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFileInfo"/>
            <AppenderRef ref="RollingFileWarn"/>
            <AppenderRef ref="RollingFileError"/>
        </logger>-->
        <!--监控系统信息-->
        <!--若是additivity设为false，则 子Logger 只会在自己的appender里输出，而不会在 父Logger 的appender里输出。-->
        <Logger name="org.springframework" level="INFO" additivity="false">

            <AppenderRef ref="Console"/>

        </Logger>
        <Logger name="org.mybatis" level="INFO" additivity="false">

<!--            <AppenderRef ref="Console"/>-->
        </Logger>
        <Logger name="com.gzairports.rabbitmq.Consumer" level="INFO" additivity="false">
            <AppenderRef ref="RollingFileInfo"/>
        </Logger>

        <Logger name="com.gzairports.common.rabbitmq" level="INFO" additivity="false">
            <AppenderRef ref="RollingFileMQ"/>
        </Logger>

        <Logger name="com.gzairports.quartz.task" level="DEBUG" additivity="false">
            <AppenderRef ref="RollingFileReport"/>
        </Logger>

        <Logger name="com.gzairports.hz.business.departure.rabbitmq" level="DEBUG" additivity="false">
            <AppenderRef ref="RollingFileFlight"/>
        </Logger>

        <Logger name="com.gzairports.common.bankUtils.BankUtil" level="INFO" additivity="false">
            <AppenderRef ref="RollingFileBANK"/>
        </Logger>

        <Logger name="com.gzairports.web.controller.bank.AsyncNotifyController" level="INFO" additivity="false">
            <AppenderRef ref="RollingFileBANK"/>
        </Logger>


        ///////
        <Logger name="com.gzairports.hz.business.arrival.service.impl.FlightFileServiceImpl" level="INFO" additivity="false">
            <AppenderRef ref="RollingFileCableInfo"/>
            <AppenderRef ref="RollingFileCableError"/>
        </Logger>
        <Logger name="com.gzairports.hz.business.cable.service.impl.HzCableServiceImpl" level="INFO" additivity="false">
            <AppenderRef ref="RollingFileCableInfo"/>
            <AppenderRef ref="RollingFileCableError"/>
        </Logger>
        <Logger name="com.gzairports.quartz.task.TwelveSettleTask" level="INFO" additivity="false">
            <AppenderRef ref="RollingFileCableInfo"/>
            <AppenderRef ref="RollingFileCableError"/>
        </Logger>
        <Logger name="com.gzairports.rabbitmq.Consumer" level="INFO" additivity="false">
            <AppenderRef ref="RollingFileCableInfo"/>
            <AppenderRef ref="RollingFileCableError"/>
        </Logger>
        <Logger name="com.gzairports.common.business.arrival.service.impl.AllPickUpOutServiceImpl" level="INFO" additivity="false">
            <AppenderRef ref="RollingFileCableInfo"/>
            <AppenderRef ref="RollingFileCableError"/>
        </Logger>
        <Logger name="com.gzairports.hz.business.departure.service.impl.FlightLoadServiceImpl" level="INFO" additivity="false">
            <AppenderRef ref="RollingFileCableInfo"/>
            <AppenderRef ref="RollingFileCableError"/>
        </Logger>
        <Logger name="com.gzairports.hz.business.departure.service.impl.HzCollectWaybillServiceImpl" level="INFO" additivity="false">
            <AppenderRef ref="RollingFileCableInfo"/>
            <AppenderRef ref="RollingFileCableError"/>
        </Logger>
        <Logger name="com.gzairports.hz.business.departure.service.impl.RepeatWeightServiceImpl" level="INFO" additivity="false">
            <AppenderRef ref="RollingFileCableInfo"/>
            <AppenderRef ref="RollingFileCableError"/>
        </Logger>
        <Logger name="com.gzairports.hz.business.transfer.service.impl.HzTransferHandoverServiceImpl" level="INFO" additivity="false">
            <AppenderRef ref="RollingFileCableInfo"/>
            <AppenderRef ref="RollingFileCableError"/>
        </Logger>



        <root level="all">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFileInfo"/>
<!--
            <AppenderRef ref="RollingFileWarn"/>
-->
            <AppenderRef ref="RollingFileError"/>
        </root>
    </loggers>

</configuration>