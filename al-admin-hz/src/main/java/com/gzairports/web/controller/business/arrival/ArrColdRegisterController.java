package com.gzairports.web.controller.business.arrival;

import com.gzairports.common.business.arrival.domain.HzArrItem;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.hz.business.departure.domain.HzColdRegister;
import com.gzairports.hz.business.departure.domain.query.HzColdRegisterQuery;
import com.gzairports.hz.business.departure.domain.vo.ColdQueryVo;
import com.gzairports.hz.business.departure.domain.vo.ColdRegisterVo;
import com.gzairports.hz.business.departure.domain.vo.HzColdRegisterVo;
import com.gzairports.hz.business.departure.service.IHzColdRegisterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 冷藏登记Controller
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
//@RestController
@RequestMapping("/arr/coldRegister")
@Api(tags = "冷藏登记")
public class ArrColdRegisterController extends BaseController {

    @Autowired
    private IHzColdRegisterService registerService;

    /**
     * 查询冷藏登记列表
     */
//    @PreAuthorize("@ss.hasPermi('arr:coldRegister:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询冷藏登记列表")
    public AjaxResult list(HzColdRegisterQuery query){
        query.setType("ARR");
        HzColdRegisterVo vo = registerService.selectList(query);
        return AjaxResult.success(vo);
    }

    /**
     * 导出冷藏登记列表
     */
    @PreAuthorize("@ss.hasPermi('arr:coldRegister:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出冷藏登记列表")
    public void export(HttpServletResponse response, HzColdRegisterQuery query)
    {
        query.setType("ARR");
        List<ColdQueryVo> list = registerService.selectListByQuery(query);
        ExcelUtil<ColdQueryVo> util = new ExcelUtil<ColdQueryVo>(ColdQueryVo.class);
        util.exportExcel(response, list, "冷藏登记列表");
    }

    /**
     * 新增冷藏登记
     */
    @PreAuthorize("@ss.hasPermi('arr:coldRegister:add')")
    @PostMapping("/add")
    @ApiOperation(value = "新增冷藏登记")
    public AjaxResult add(@RequestBody HzColdRegister register){
        register.setType("ARR");
        register.setStatus(0);
        return toAjax(registerService.add(register));
    }

    /**
     * 根据运单号查询品名
     */
    @PreAuthorize("@ss.hasPermi('arr:coldRegister:selectCargoName')")
    @GetMapping("/selectCargoName/{waybillCode}")
    @ApiOperation(value = "根据运单号查询品名")
    public AjaxResult selectCargoName(@PathVariable("waybillCode") String waybillCode){
        return AjaxResult.success("操作成功",registerService.selectCargoName(waybillCode,"ARR",null));
    }

    /**
     * 根据计费时间计算计费金额
     */
    @PreAuthorize("@ss.hasPermi('arr:coldRegister:countSum')")
    @PostMapping("/countSum")
    @ApiOperation(value = "根据计费时间计算计费金额")
    public AjaxResult countSum(@RequestBody ColdRegisterVo vo){
        vo.setType("ARR");
        return AjaxResult.success("操作成功",registerService.countSum(vo));
    }

    /**
     * 查询冷库计费项目
     */
    @PreAuthorize("@ss.hasPermi('arr:coldRegister:selectColdItem')")
    @GetMapping("/selectColdItem")
    @ApiOperation(value = "查询冷库计费项目")
    public AjaxResult selectColdItem(){
        return AjaxResult.success(registerService.selectColdItem());
    }

    /**
     * 查看详情
     */
    @PreAuthorize("@ss.hasPermi('arr:coldRegister:getInfo')")
    @GetMapping("/getInfo/{id}")
    @ApiOperation(value = "查看详情")
    public AjaxResult getInfo(@PathVariable Long id){
        return AjaxResult.success(registerService.getInfo(id));
    }


//    /**
//     * 出库
//     */
//    @GetMapping("/out/{id}")
//    @ApiOperation(value = "出库")
//    public AjaxResult out(@PathVariable("id") Long id){
//        return toAjax(registerService.out(id));
//    }
}
