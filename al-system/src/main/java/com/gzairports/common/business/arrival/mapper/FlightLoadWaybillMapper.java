package com.gzairports.common.business.arrival.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.gzairports.common.business.arrival.domain.FlightLoadWaybill;
import com.gzairports.common.business.departure.domain.CostDetail;
import com.gzairports.common.business.departure.domain.vo.LoadInfoVo;
import com.gzairports.common.business.reporter.domain.ReportDepLoad;
import com.gzairports.hz.business.cable.domain.vo.*;
import com.gzairports.hz.business.departure.domain.query.BillExportQuery;
import com.gzairports.hz.business.departure.domain.query.CableMawbQuery;
import com.gzairports.hz.business.departure.domain.vo.CableMawbVo;
import com.gzairports.hz.business.departure.domain.vo.FormalWaybillVo;
import com.gzairports.hz.business.departure.domain.vo.ForwardImportWaybillVo;
import com.gzairports.hz.business.departure.domain.vo.LoadFlightWaybillVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 航班预配散舱Mapper接口
 *
 * <AUTHOR>
 * @date 2023-09-18
 */
@Mapper
public interface FlightLoadWaybillMapper extends BaseMapper<FlightLoadWaybill> {

    /**
     * 根据配载id查询散舱运单
     * @param bizId 配载id
     * @return 散舱运单
     */
    List<Long> selectListById(Long bizId);

    /**
     * 航班配载散舱运单数据
     * @param flightLoadId 航班配载id
     * @return 散舱运单数据
     */
    List<ForwardImportWaybillVo> selectLoadWaybillList(Long flightLoadId);

    /**
     * 根据航班配载id查询散舱运单
     * @param id 航班配载id
     * @return 结果
     */
    List<FormalWaybillVo> selectListByFlightLoadId(@Param("id") Long id,@Param("type") Integer type);


    /**
     * 根据航班配载id和散舱id查询散舱运单
     * */
    List<FormalWaybillVo> selectListByFlightLoadIdAndWaybillId(@Param("id") Long id,@Param("waybillIds") List<Long> waybillIds);

    /**
     * 根据散舱配载id集合查询配载数据
     * @param flightLoadWaybillIds 散舱配载id集合
     * @return 配载数据集合
     */
    List<FlightLoadWaybill> selectListByIds(@Param("flightLoadWaybillIds") List<Long> flightLoadWaybillIds);


    Integer selectWaybillNum(Long id);

    /**
     * 根据航段id查询板箱上的运单id
     * @param id 配载id
     * @return 板箱运单id集合
     */
    List<FlightLoadWaybill> selectWaybillIdsByFlightLoadId(Long id);
    List<FlightLoadWaybill> selectWaybillIdsByLoadId(Long id);

    /**
     * 根据配载航班查询配载信息
     * @param legId 航段id
     * @param waybillId 运单id
     * @return 配载信息
     */
    List<FlightLoadWaybill> selectLoadInfoList(@Param("legId") Long legId,@Param("waybillId") Long waybillId);

    /**
     * FFM舱单控制报运单数据查询
     * @param query 查询条件
     * @return 运单数据
     */
    List<CableMawbVo> selectLoadWaybill(CableMawbQuery query);

    /**
     * 根据运单id查询运单是否还有数据
     * @param waybillId 运单id
     * @return 结果
     */
    List<Long> isHaveDate(@Param("waybillId") Long waybillId,@Param("flightLoadId") Long flightLoadId);

    List<LoadInfoVo> selectByLoadInfo(Long waybillId);

    List<FlightLoadWaybill> selectWaybillIdsByWaybillId(Long waybillId);

    BigDecimal selectWaybillByWaybillId(Long id);

    List<FlightLoadWaybill> selectSettleWeight(@Param("waybillId") Long waybillId,@Param("flightIds") List<Long> flightIds);

    List<Long> selectFlightIds(@Param("waybillIds") List<Long> waybillIds);

    List<ReportDepLoad> selectReportLoadList(@Param("ids") List<Long> ids);

    List<ConsignmentDetail> selectDetailList(@Param("waybillIdList") List<Long> waybillIdList,@Param("uldNo") String uldNo);

    FWBJsonVO selectFWBDataList(Long flightId);

    FHLJsonVO selectFHLDataList(Long flightId);

    List<ConsignmentDetail> selectDetailListForLegId(@Param("legIds") List<Long> legIds,@Param("uldNo") String uldNo);

    List<LoadFlightWaybillVO> selectLoadFlightWaybill(@Param("waybillCodeList") List<String> waybillCodeList);

    List<LoadFlightWaybillVO> selectNotSettleWaybill(@Param("flightIdList") List<Long> flightIdList,@Param("deptId") Long deptId);

    List<LoadFlightWaybillVO> selectHistoryLoad(@Param("endTime") LocalDateTime endTime,@Param("loadWaybillList") List<String> loadWaybillList);
}
