package com.gzairports.web.controller.basedata;


import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.common.basedata.domain.BaseCargoMapping;
import com.gzairports.common.basedata.domain.query.CargoCodeMappingQuery;
import com.gzairports.common.basedata.service.ICargoMappingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 货品代码映射Controller
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@RestController
@RequestMapping("/base/cargoMapping")
@Api(tags = "货品代码映射数据接口")
public class CargoMappingController extends BaseController {

    @Autowired
    private ICargoMappingService cargoMappingService;

    /**
     * 查询货品代码映射列表
     */
//    @PreAuthorize("@ss.hasPermi('base:cargoMapping:cargoCodeMappingList')")
    @GetMapping("/cargoCodeMappingList")
    @ApiOperation(value = "查询货品代码映射列表")
    public TableDataInfo cargoCodeMappingList(CargoCodeMappingQuery query)
    {
        startPage();
        query.setDeptId(SecurityUtils.getHighParentId());
        List<BaseCargoMapping> list = cargoMappingService.selectCargoCodeMappingList(query);
        return getDataTable(list);
    }

    /**
     * 导出货品代码映射
     */
    @PreAuthorize("@ss.hasPermi('base:cargoMapping:exportCargoCodeMapping')")
    @Log(title = "导出货品代码映射", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCargoCodeMapping")
    @ApiOperation(value = "导出货品代码映射")
    public void exportCargoCodeMapping(HttpServletResponse response, CargoCodeMappingQuery query)
    {
        List<BaseCargoMapping> list = cargoMappingService.selectCargoCodeMappingList(query);
        ExcelUtil<BaseCargoMapping> util = new ExcelUtil<BaseCargoMapping>(BaseCargoMapping.class);
        util.exportExcel(response, list, "货品代码映射");
    }

    /**
     * 新增货品代码映射
     */
    @PreAuthorize("@ss.hasPermi('base:cargoMapping:addCargoMapping')")
    @Log(title = "新增货品代码映射", businessType = BusinessType.INSERT)
    @PostMapping("/addCargoMapping")
    @ApiOperation(value = "新增货品代码映射")
    public AjaxResult addCargoMapping(@RequestBody BaseCargoMapping cargoMapping)
    {
        cargoMapping.setIsCommon(SecurityUtils.getHighParentId());
        return toAjax(cargoMappingService.addCargoMapping(cargoMapping));
    }

    /**
     * 货品代码映射详情
     */
    @PreAuthorize("@ss.hasPermi('base:cargoMapping:getInfo')")
    @GetMapping("/{id}")
    @ApiOperation(value = "货品代码映射详情")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(cargoMappingService.getInfo(id));
    }

    /**
     * 修改货品代码映射
     */
    @PreAuthorize("@ss.hasPermi('base:cargoMapping:editCargoMapping')")
    @Log(title = "修改货品代码映射", businessType = BusinessType.UPDATE)
    @PostMapping("/editCargoMapping")
    @ApiOperation(value = "修改货品代码映射")
    public AjaxResult editCargoMapping(@RequestBody BaseCargoMapping cargoMapping)
    {
        return toAjax(cargoMappingService.editCargoMapping(cargoMapping));
    }

    /**
     * 删除货品代码映射
     */
    @PreAuthorize("@ss.hasPermi('base:cargoMapping:delCargoMapping')")
    @Log(title = "删除货品代码映射", businessType = BusinessType.UPDATE)
    @GetMapping("/delCargoMapping/{id}")
    @ApiOperation(value = "删除货品代码映射")
    public AjaxResult delCargoMapping(@PathVariable("id") Long id)
    {
        return toAjax(cargoMappingService.delCargoMapping(id));
    }

}
