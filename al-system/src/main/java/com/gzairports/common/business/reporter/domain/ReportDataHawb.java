package com.gzairports.common.business.reporter.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-02-19
 */
@Data
@TableName("all_report_data_hawb")
public class ReportDataHawb {

    /** 主键id */
    private Long id;

    /** 运单号 */
    private String waybillCode;

    /** 运单前缀 */
    private String waybillPrefix;

    /** 运单类型 */
    private String waybillType;

    /** 状态 */
    private String status;

    /** 关联主单号 */
    private String masterWaybillCode;

    /** 始发站 */
    private String sourcePort;

    /** 承运人1 */
    private String carrier1;

    /** 承运人2 */
    private String carrier2;

    /** 到达站2 */
    private String des2;

    /** 承运人3 */
    private String carrier3;

    /** 到达站3 */
    private String des3;

    /** 目的站 */
    private String desPort;

    /** 航班 */
    private String flightNo;

    /** 航班日期 */
    private String flightDate;

    /** 收运方式 */
    private String collectMethod;

    /** 发货人 */
    private String shipper;

    /** 收货人 */
    private String consign;

    /** 品名编码 */
    private String cargoCode;

    /** 品名 */
    private String cargoName;

    /** 件数 */
    private String quantity;

    /** 重量 */
    private String weight;

    /** 计费重量 */
    private String chargeWeight;

    /** 包装 */
    private String pack;

    /** 尺寸 */
    private String size;

    /** 体积 */
    private String volume;

    /** 提货方式 */
    private String deliveryMethod;

    /** 储运注意事项 */
    private String storageTransportNotes;

    /** 结算注意事项 */
    private String settlementNotes;

    /** 运输保险价值 */
    private String transportInsureValue;

    /** 特货代码1 */
    private String specialCargoCode1;

    /** 特货代码2 */
    private String specialCargoCode2;

    /** 特货代码3 */
    private String specialCargoCode3;

    /** 其他特货代码 */
    private String otherSpecialCargoCode;

    /** 出港付费方式 */
    private String paymentMethod;

    /** 支付状态 */
    private String payStatus;

    /** 支付时间 */
    private String payTime;

    /** 总金额 */
    private String costSum;

    /** 填开时间 */
    private String writeTime;

    /** 填开地点 */
    private String writeLocation;

    /** 填开人 */
    private String writer;

    /** 单位id */
    private Long deptId;
}
