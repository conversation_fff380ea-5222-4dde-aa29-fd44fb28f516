<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.TruckMapper">
    <insert id="addFlatbedTruck" parameterType="BaseFlatbedTruck">
        insert into base_flatbed_truck
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="code != null and code != ''">code,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="deadWeight != null and deadWeight != ''">dead_weight,</if>
            <if test="loadWeight != null">load_weight,</if>
            <if test="loadSize != null">load_size,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="imageUrl != null and imageUrl != ''">image_url,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="code != null and code != ''">#{code},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="deadWeight != null and deadWeight != ''">#{deadWeight},</if>
            <if test="loadWeight != null">#{loadWeight},</if>
            <if test="loadSize != null">#{loadSize},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="imageUrl != null and imageUrl != ''">#{imageUrl},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="editFlatbedTruck" parameterType="BaseFlatbedTruck" >
        update base_flatbed_truck
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="deadWeight != null and deadWeight != ''">dead_weight = #{deadWeight},</if>
            <if test="loadWeight != null">load_weight = #{loadWeight},</if>
            <if test="loadSize != null">load_size = #{loadSize},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="imageUrl != null and imageUrl != ''">image_url = #{imageUrl},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="removeFlatbedTruckByIds">
        update base_flatbed_truck set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="flatbedTruckList" resultType="com.gzairports.common.basedata.domain.BaseFlatbedTruck">
        select id, code, type, dead_weight, load_weight, load_size, status, image_url, remark from base_flatbed_truck
        <where>
            and is_del = 0
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="code != null  and code != ''"> and code like concat('%',#{code},'%')</if>
            <if test="type != null and type != ''"> and type = #{type}</if>
        </where>
    </select>
    <select id="selectById" resultType="com.gzairports.common.basedata.domain.BaseFlatbedTruck">
        select id, code, type, dead_weight, load_weight, load_size, status, image_url, remark from base_flatbed_truck
        where is_del = 0 and id = #{id}
    </select>
    <select id="selectByCode" resultType="com.gzairports.common.basedata.domain.BaseFlatbedTruck">
        select id, code, type, dead_weight, load_weight, load_size, status, image_url, remark from base_flatbed_truck where code = #{code}  and is_del = 0
    </select>
</mapper>