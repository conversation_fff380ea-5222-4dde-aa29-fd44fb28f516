package com.gzairports.hz.business.departure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.hz.business.departure.domain.HzDepGroupWaybill;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 组货调度运单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
@Mapper
public interface HzDepGroupWaybillMapper extends BaseMapper<HzDepGroupWaybill> {

    /**
     * 加货运单查询
     * @param id 运单组货id
     * @return 结果
     */
    List<HzDepGroupWaybill> selectListByFlightLoadId(Long id);

    /**
     * 根据运单号手动添加
     * @param waybillCode 运单号
     * @return 结果
     */
    HzDepGroupWaybill selectOneByWaybillCode(String waybillCode);
}
