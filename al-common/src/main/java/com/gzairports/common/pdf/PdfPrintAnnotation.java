package com.gzairports.common.pdf;

import java.lang.annotation.*;


/**
 * 生成pdf注解
 * <AUTHOR>
 * @date 2024-08-19
 */
@Documented
@Inherited
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.TYPE,ElementType.LOCAL_VARIABLE})
public @interface PdfPrintAnnotation {

    enum PdfFieldType {
        /**
         * 文本类型
         */
        TEXT,

        /**
         * 选择框
         */
        CHOICE,

        /**
         * 选择框
         */
        CHOICE_MUTI,

        /**
         * 时间，不带小时
         */
        DATE_YY_MM_DD,

        /**
         * 时间，带小时
         */
        DATE_YY_MM_DD_HH,

        /**
         * 布尔类型
         */
        BOOLEAN,

        /**
         * 图片
         */
        IMAGE,

        /**
         * 集合
         */
        LIST
    }

    PdfFieldType pdfFieldType() default PdfFieldType.TEXT;

    String pdfFieldName() ;

}
