package com.gzairports.hz.business.cable.domain.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-07
 */
@Data
public class UldVO {

    /** 板箱信息 */
    private List<ConsignmentDetail> consignmentDetail;

    /** 货物运输信息 */
    private String consignmentOnwardMovementInformation;

    /** 位置类型 */
    private String locationType;

    /** 集装器号，用于标识特定的集装设备 */
    private String uldNo;

    /** 集装器号 */
    private String uldNum;

    /** 板箱信息 */
    private String uldOwner;

    /** 集装器备注 */
    private String uldRemark;

    /** 集装器类型 */
    private String uldType;
}
