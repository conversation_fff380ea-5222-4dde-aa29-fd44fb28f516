package com.gzairports.wl.charge.domain.query;

import lombok.Data;

import java.util.List;

/**
 * 收费项目查询参数
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@Data
public class ChargeItemQuery {

    /** 查询的id集合 */
    private List<Long> ids;

    /** 名称 */
    private String name;

    /** 进出港类型 */
    private String operationType;

    /** 适用单证类型 */
    private String applicableDocuments;

    /** 简称 */
    private String abbreviation;

    /** 所属单位 */
    private Long deptId;

    /** 航司 */
    private String code;
}
