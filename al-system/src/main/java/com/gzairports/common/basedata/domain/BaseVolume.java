package com.gzairports.common.basedata.domain;

import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.util.Date;


/**
 * 多体积单位
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Data
public class BaseVolume {

    /** 主键id */
    private Long id;

    /** 体积单位 */
    @Excel(name = "体积单位")
    private String volumeUnit;

    /** 中文名称 */
    @Excel(name = "中文名称")
    private String chineseName;

    /** 英文名称 */
    @Excel(name = "英文名称")
    private String englishName;

    /** 与立方转换率 */
    @Excel(name = "与立方转换率")
    private String conversionRate;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;
}
