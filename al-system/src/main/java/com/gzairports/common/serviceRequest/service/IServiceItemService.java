package com.gzairports.common.serviceRequest.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.serviceRequest.domain.ServiceItem;

import java.util.List;

/**
 * 服务项目管理Service接口
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
public interface IServiceItemService extends IService<ServiceItem> {

    /**
     * 查询服务项目列表
     * @return 结果
     */
    List<ServiceItem> selectItemList();

    /**
     * 新增服务项目
     * @param serviceItem 服务项目参数
     * @return 结果
     */
    int insertItem(ServiceItem serviceItem);

    /**
     * 删除服务项目
     * @param id 服务项目id
     * @return 结果
     */
    int deleteItem(Long id);
}
