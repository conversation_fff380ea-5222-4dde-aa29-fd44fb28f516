package com.gzairports.hz.business.transfer.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 中转交接挑单运单数据
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
@Data
public class TransferPickVo {

    /** 录单id */
    private Long orderId;

    /** 运单id */
    private Long waybillId;

    /** 交接单号 */
    private String handoverNo;

    /** 运单号 */
    private String waybillCode;

    /** 航班号 */
    private String flightNo;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 计费重量 */
    private BigDecimal chargeWeight;

    /** 理货件数 */
    private Integer tallyQuantity;

    /** 理货重量 */
    private BigDecimal tallyWeight;

    /** 本地代理人 */
    private String shipper;

    /** 特货代码 */
    private String specialCargoCode1;

    /** 品名 */
    private String cargoName;

    /** 备注 */
    private String remark;

    /** 是否中转单 */
    private Integer transferBill;

    /** 理货时填的仓库 */
    private String store;

    /** 理货时填的库位 */
    private String locator;
}
