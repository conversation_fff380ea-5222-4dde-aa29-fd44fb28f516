package com.gzairports.wl.ticket.domain.vo;

import lombok.Data;

import java.util.List;

/**
 * 单证详细信息列表返回VO数据
 *
 * <AUTHOR>
 * @date 2024-03-20
 */
@Data
public class TicketInfoVO {

    /** 主键id */
    private Long id;

    /** 票证来源id */
    private Long billSourceId;

    /** 票证来源 */
    private String billSource;

    /** 票证类型 */
    private String  billType;

    /** 票证前缀 */
    private String billPrefix;

    /** 开始编号 */
    private Integer startNumber;

    /** 开始编号检查号 */
    private String startCode;

    /** 结束编号检查号 */
    private String endCode;

    /** 是否：包舱单（包租整个货舱的运单） */
    private Integer isChartering;

    /** 部门 */
    private Long deptId;

    /** 数量 */
    private Integer number;

    /** 结束编号 */
    private Integer endNumber;

    /** 未发放号段 */
    private List<String> unissued;

    /** 发放号段 */
    private List<String> issue;

    /** 销号号段 */
    private List<String> cancelNumber;

    /** 已使用号段 */
    private List<String> used;

    /** 备注 */
    private String remark;

}
