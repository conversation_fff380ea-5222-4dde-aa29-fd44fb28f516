package com.gzairports.hz.business.reporter.service;

import com.gzairports.hz.business.reporter.domain.HzReportSet;
import com.gzairports.hz.business.reporter.domain.HzReportSetFilter;
import com.gzairports.hz.business.reporter.domain.query.HzReportQuery;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-06
 */
public interface CountReportService {

    /**
     * 统计报表信息
     * @return 结果
     */
    List<HzReportSet> selectReportTitle();

    /**
     * 获取查询字段
     * @param setId 报表设置id
     * @return 查询字段
     */
    List<HzReportSetFilter> getFilter(Long setId);

    /**
     * 获取表格流
     * @param query 查询参数
     * @param response 返回流
     */
    void getReportExcel(HzReportQuery query, HttpServletResponse response) throws IOException;

    /**
     * 分页查询数据
     * @param query 查询参数
     * @return 结果
     */
    Integer pageQuery(HzReportQuery query);

    /**
     * 根据报表ID查询报表数据
     * @param id 报表ID
     * @return 结果
     */
    HzReportSet selectById(Long id);


}
