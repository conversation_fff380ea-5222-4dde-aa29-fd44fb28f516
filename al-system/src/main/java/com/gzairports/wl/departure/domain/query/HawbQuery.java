package com.gzairports.wl.departure.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 国内分单查询参数
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Data
public class HawbQuery {

    /** 制单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 制单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 运单号 */
    private String waybillCode;

    /** 发货人 */
    private String shipper;

    /** 航班号 */
    private String flightNo;

    /** 特货代码选项 */
    private Integer option;

    /** 特货代码 */
    private String specialCargoCode;

    /** 货品代码 */
    private String cargoCode;

    private String cargoName;

    /** 支付状态 0 未支付 2 生成账单 1 已支付 3 已退款*/
    private Integer payStatus;

    /** 航司 */
    private String carrierCode;

    /** 目的站 */
    private String desPort;

    /** 所属单位 */
    private Long deptId;

    /** 状态 */
    private String status;

    /** 航班时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTakeoffTime;

    /** 航班时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTakeoffTime;

    private Integer pageNum = 1;

    private Integer pageSize = 10;
}
