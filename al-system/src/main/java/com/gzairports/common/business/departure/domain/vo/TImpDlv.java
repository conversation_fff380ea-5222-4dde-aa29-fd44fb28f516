package com.gzairports.common.business.departure.domain.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 运单日志对象 t_log_awblog
 *
 * <AUTHOR>
 * @date 2023-09-25
 */
@Data
@TableName(schema = "KWECFPS", value = "T_IMP_DLV")
public class TImpDlv {
    private static final long serialVersionUID = 1L;

    /** 记录ID */
    private Long recid;
    /** 运单完整编码 */
    private String billid;
    /**办理提货时间*/
    private Date pickopetime;
    /**提货件数*/
    private Long pcs;
    /**提货重量*/
    private BigDecimal weight;

}
