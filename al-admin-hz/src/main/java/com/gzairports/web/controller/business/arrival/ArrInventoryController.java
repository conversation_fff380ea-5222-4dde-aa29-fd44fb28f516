package com.gzairports.web.controller.business.arrival;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.hz.business.arrival.domain.HzArrQuickDelivery;
import com.gzairports.hz.business.arrival.domain.query.ArrInventoryQuery;
import com.gzairports.hz.business.arrival.domain.query.QuickDeliveryQuery;
import com.gzairports.hz.business.arrival.domain.vo.ArrInventoryVo;
import com.gzairports.hz.business.arrival.domain.vo.InventoryListVo;
import com.gzairports.hz.business.arrival.service.IInventoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 库存管理Controller
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
@RestController
@RequestMapping("/arr/inventory")
@Api(tags = "库存管理")
public class ArrInventoryController extends BaseController {

    @Autowired
    private IInventoryService iInventoryService;

    /**
     * 查询库存管理列表
     */
//    @PreAuthorize("@ss.hasPermi('arr:inventory:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询库存管理列表")
    public AjaxResult list(ArrInventoryQuery query)
    {
        return AjaxResult.success(iInventoryService.selectList(query));
    }

    /**
     * 导出库存管理列表
     */
    @PreAuthorize("@ss.hasPermi('arr:inventory:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出库存管理列表")
    public void export(HttpServletResponse response, ArrInventoryQuery query)
    {
        List<InventoryListVo> list = iInventoryService.selectListByQuery(query);
        ExcelUtil<InventoryListVo> util = new ExcelUtil<InventoryListVo>(InventoryListVo.class);
        util.exportExcel(response, list, "库存管理","清库计划", SecurityUtils.getNickName());
    }
}
