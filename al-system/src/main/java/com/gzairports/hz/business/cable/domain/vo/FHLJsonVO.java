package com.gzairports.hz.business.cable.domain.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-04-07
 */
@Data
public class FHLJsonVO {

    /** 收货人地址 */
    private String consigneeAddr;

    /** 收货人所在城市 */
    private String consigneeCity;

    /** 收货人所在国家 */
    private String consigneeCountry;

    /** 收货人名称 */
    private String consigneeName;

    /** 收货人所在州 / 省 */
    private String consigneeStateProvince;

    /** 收货人电话号码 */
    private String consigneeTel;

    /** 收货人邮政编码 */
    private String consigneeZipCode;

    /** 货币代码 */
    private String currency;

    /** 运输声明价值 */
    private String declaredValueCarriage;

    /** 海关申报价值 */
    private String declaredValueCustoms;

    /** 启运港 */
    private String depAirport;

    /** 目的港 */
    private String desAirport;

    /** 协调商品代码 */
    private String harmonisedCommoditycode;

    /** 分运单号 */
    private String hawbNo;

    /**  */
    private HouseWaybillDetails houseWaybillSummaryDetails;

    /** 信息标识符 */
    private String insuranceAmount;

    /** 主运单号 */
    private String mawbNo;

    /** 其他费用支付方式 */
    private String other;

    /** 件数 */
    private String pieces;

    /** 托运人地址 */
    private String shipperAddr;

    /** 托运人所在城市 */
    private String shipperCity;

    /** 托运人所在国家代码 */
    private String shipperCountry;

    /** 托运人名称 */
    private String shipperName;

    /** 托运人所在州 / 省 */
    private String shipperStateProvince;

    /** 托运人电话号码 */
    private String shipperTel;

    /** 托运人邮政编码 */
    private String shipperZipCode;

    /** 重量 */
    private String weight;

    /** 重量单位 */
    private String weightUnit;

    /** 运费支付方式（重量相关价值） */
    private String wtVal;
}
