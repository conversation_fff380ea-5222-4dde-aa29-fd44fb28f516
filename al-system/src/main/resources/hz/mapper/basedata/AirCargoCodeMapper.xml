<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.AirCargoCodeMapper">
    <insert id="insertAirCargoCode" parameterType="baseAirCargoCode">
        insert into base_air_cargo_code
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="code != null and code != ''">code,</if>
            <if test="cargoCode != null and cargoCode != ''">cargo_code,</if>
            <if test="chineseDescription != null and chineseDescription != ''">chinese_description,</if>
            <if test="englishDescription != null and englishDescription != ''">english_description,</if>
            <if test="carrierCode != null and carrierCode != ''">carrier_code,</if>
            <if test="domestic != null">domestic,</if>
            <if test="international != null">international,</if>
            <if test="isCommon != null">is_common,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="code != null and code != ''">#{code},</if>
            <if test="cargoCode != null and cargoCode != ''">#{cargoCode},</if>
            <if test="chineseDescription != null and chineseDescription != ''">#{chineseDescription},</if>
            <if test="englishDescription != null and englishDescription != ''">#{englishDescription},</if>
            <if test="carrierCode != null and carrierCode != ''">#{carrierCode},</if>
            <if test="domestic != null">#{domestic},</if>
            <if test="international != null">#{international},</if>
            <if test="isCommon != null">#{isCommon},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>
    <update id="updateAirCargoCode" parameterType="baseAirCargoCode">
        update base_air_cargo_code
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="cargoCode != null and cargoCode != ''">cargo_code = #{cargoCode},</if>
            <if test="chineseDescription != null and chineseDescription != ''">chinese_description = #{chineseDescription},</if>
            <if test="englishDescription != null and englishDescription != ''">english_description = #{englishDescription},</if>
            <if test="carrierCode != null and carrierCode != ''">carrier_code = #{carrierCode},</if>
            <if test="domestic != null">domestic = #{domestic},</if>
            <if test="international != null">international = #{international},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="delAirCargoCode" parameterType="long">
        update base_air_cargo_code set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <select id="selectAirCargoCodeList" resultType="com.gzairports.common.basedata.domain.BaseAirCargoCode">
        select id, code, cargo_code, chinese_description, english_description, carrier_code, domestic, international from base_air_cargo_code
        <where>
        and is_del = 0
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="code != null  and code != ''"> and code like concat('%',#{code},'%')</if>
            <if test="carrierCode != null  and carrierCode != ''"> and carrier_code like concat('%',#{carrierCode},'%')</if>
            <if test="domestic != null"> and domestic = #{domestic}</if>
            <if test="international != null"> and international = #{international}</if>
            and is_del = 0
        </where>
    </select>
    <select id="selectAirCargoCodeByCode" resultType="com.gzairports.common.basedata.domain.BaseAirCargoCode">
        select id, code, cargo_code, chinese_description, english_description, carrier_code, domestic, international
        from base_air_cargo_code where code = #{code} and is_del = 0
    </select>
</mapper>