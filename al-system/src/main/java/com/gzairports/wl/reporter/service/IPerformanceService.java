package com.gzairports.wl.reporter.service;

import com.gzairports.wl.reporter.domain.query.CargoAnalysisQuery;
import com.gzairports.wl.reporter.domain.query.CarrierSumQuery;
import com.gzairports.wl.reporter.domain.vo.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface IPerformanceService {

    /**
     * 年货量分析
     * @param year 查询年份
     * @return 结果
     */
    YearCargoQuantityVO yearCargoAnalyse(String year);

    /**
     * 年利润分析
     * @param year 查询年份
     * @return 结果
     */
    List<YearProfitVO> yearProfit(String year);

    /**
     * 年月货量环比分析
     * @param query 查询参数
     * @return 结果
     */
    CargoVolumeAnalysisVO cargoAnalysis(CargoAnalysisQuery query);

    /**
     * 月货量分析（表格，利润分布图，客户货量贡献图）
     * @param month 选择的年月
     * @return 结果
     */
    MonthCargoAnalysisVO monthCargoAnalysis(String month);

    /**
     * 航线货量利润分布
     * @param month 选择年月
     * @return 结果
     */
    Map<String, AirlineProfitVO> airlineProfit(String month);

    /**
     * 营业点员工绩效
     * @param month 选择年月
     * @return 结果
     */
    StaffPerformanceVO staffPerformance(String month);

    /**
     * 每日承运人汇总
     * @param query 查询条件
     * @return 结果
     */
    CarrierDayVO carrierDaySum(CarrierSumQuery query);

    /**
     * 每日客户汇总
     * @param query 查询条件
     * @return 结果
     */
    CarrierDayVO customDaySum(CarrierSumQuery query);
}
