package com.gzairports.common.basedata.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.basedata.domain.BaseCargoCategory;
import com.gzairports.common.basedata.domain.query.CargoCategoryQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 货品大类Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-14
 */
@Mapper
public interface CargoCategoryMapper extends BaseMapper<BaseCargoCategory> {

    /**
     * 查询货品大类列表
     *
     * @param query 查询参数
     * @return 货品大类列表
     */
    List<BaseCargoCategory> selectCargoCategoryList(CargoCategoryQuery query);

    /**
     * 删除货品大类数据
     * @param ids 货品大类id集合
     * @return 结果
     */
    int delCargoCategory(Long[] ids);

    String selectByCode(String categoryName);

    List<BaseCargoCategory> selectBatchByCodes(@Param("categoryCodes") List<String> categoryCodes);
}
