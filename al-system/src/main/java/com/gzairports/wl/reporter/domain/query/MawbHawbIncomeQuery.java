package com.gzairports.wl.reporter.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @author: lan
 * @Desc: 主分单收入明细报表查询参数
 * @create: 2025-03-12 10:11
 **/

@Data
public class MawbHawbIncomeQuery {
    /** 主单号 */
    private String masterWaybillCode;

    /** 分单号 */
    private String waybillCode;

    /** 承运人 */
    private String carrier;

    /** 起点站 */
    private String sourcePort;

    /** 目的站 */
    private String desPort;

    /** 发货人简称 */
    private String shipperAbb;

    /** 发货人全称 */
    private String shipper;

    /** 收货人简称 */
    private String consignAbb;

    /** 收货人全称 */
    private String consign;

    /** 制单开始日期 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startWriteTime;

    /** 制单结束日期 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endWriteTime;

    /** 是否换单 换单(运单类型 0 正常 1换单 2补货 3补重) */
    private Integer switchBill;

    /** 是否监管 海关监管 */
    private Integer customsSupervision;

    /** 营业点 */
    private Integer deptId;

    /** 当前页 */
    private Integer pageNum;

    /** 每页数量 */
    private Integer pageSize;

    /** 分单发货人简称 */
    private String hawbShipperAbb;

    /** 分单发货人全称 */
    private String hawbShipper;

    /** 利润 1负 0正 */
    private Integer profit;

}
