package com.gzairports.hz.business.arrival.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 航班文件参数
 * <AUTHOR>
 * @date 2024-07-16
 */
@Data
public class FlightFileVo {

    /** 航班操作 */
    private String flightOper;

    /** 主单文件到达总数 */
    private Integer totalAWBA;

    /** 邮件文件到达总数 */
    private Integer totalAWBM;

    /** 文件总数 */
    private Integer totalFile;

    /** 文件总重量 */
    private BigDecimal totalFileWeight;

    /** 文件总件数 */
    private Integer totalFileQuantity;

    /** 舱单总件数 */
    private Integer totalCabin;

    /** 舱单总重量 */
    private BigDecimal totalCabinWeight;

    /** 理货总数 */
    private Integer totalTally;

    /** 理货总重量 */
    private BigDecimal totalTallyWeight;

    /** 运单数据列表 */
    private List<FlightFileWaybillVo> waybillVos;
}
