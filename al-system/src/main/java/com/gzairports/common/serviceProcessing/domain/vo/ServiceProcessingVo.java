package com.gzairports.common.serviceProcessing.domain.vo;

import com.gzairports.common.business.arrival.domain.HzArrItem;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: lan
 * @Desc: 自助办单办理提货返回参数
 * @create: 2024-11-04 14:58
 **/
@Data
public class ServiceProcessingVo {
    /**
     * 运单号
     * */
    private String waybillCode;
    /**
     * 品名
     * */
    private String cargoName;
    /**
     * 提货次数
     * */
    private Integer pickUpCount;
    /**
     * 提货件数
     * */
    private Integer pickUpPiece;
    /**
     * 提货重量
     * */
    private BigDecimal pickUpWeight;
    /**
     * 费用明细
     * */
    private List<HzArrItem> items;
    /**
     *费用合计
     * */
    private BigDecimal totalCost;
    /***
     * 理货id
     */
    private Long tallyId;

}
