package com.gzairports.common.basedata.service.impl;


import ch.qos.logback.core.joran.spi.ElementSelector;
import com.gzairports.common.basedata.domain.BaseFlatbedTruck;
import com.gzairports.common.basedata.domain.query.FlatbedTruckQuery;
import com.gzairports.common.basedata.mapper.TruckMapper;
import com.gzairports.common.basedata.service.ITruckService;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.exception.ServiceException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.bean.BeanValidators;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.Validator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 板车管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Service
public class TruckServiceImpl implements ITruckService {

    @Autowired
    private TruckMapper truckMapper;

    @Autowired
    private Validator validator;

    private static final Logger log = LoggerFactory.getLogger(CityCodeServiceImpl.class);

    /**
     * 查询板车管理列表
     *
     * @param query 查询参数
     * @return 板车管理列表
     */
    @Override
    public List<BaseFlatbedTruck> flatbedTruckList(FlatbedTruckQuery query) {
        return truckMapper.flatbedTruckList(query);
    }

    /**
     * 新增板车
     *
     * @param truck 客户
     * @return 结果
     */
    @Override
    public int addFlatbedTruck(BaseFlatbedTruck truck) {
        BaseFlatbedTruck flatbedTruck = truckMapper.selectByCode(truck.getCode());
        if (flatbedTruck != null){
            throw new CustomException("已存在相同数据");
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        truck.setCreateBy(loginUser.getUsername());
        truck.setCreateTime(DateUtils.getNowDate());
        return truckMapper.addFlatbedTruck(truck);
    }

    /**
     * 修改板车
     *
     * @param truck 板车
     * @return 结果
     */
    @Override
    public int editFlatbedTruck(BaseFlatbedTruck truck) {
        BaseFlatbedTruck flatbedTruck = truckMapper.selectByCode(truck.getCode());
        if (flatbedTruck != null && !flatbedTruck.getId().equals(truck.getId())){
            throw new CustomException("已存在相同数据");
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        truck.setUpdateBy(loginUser.getUsername());
        truck.setUpdateTime(DateUtils.getNowDate());
        return truckMapper.editFlatbedTruck(truck);
    }

    /**
     * 删除板车
     *
     * @param ids 板车id集合
     * @return 结果
     */
    @Override
    public int removeFlatbedTruckByIds(Long[] ids) {
        return truckMapper.removeFlatbedTruckByIds(ids);
    }

    /**
     * 详情
     * @param id 板车id
     * @return 结果
     */
    @Override
    public BaseFlatbedTruck getInfo(Long id) {
        return truckMapper.selectById(id);
    }

    /**
     * 导入板车对数据的校验(自重载重)
     * @param input 数据
     * @return 结果
     * */
    public static boolean validateString(String input) {
        // 正则表达式：匹配形如 "1*1*1" 格式的数据
        String regex = "^\\d+\\*\\d+\\*\\d+$";
        // 创建 Pattern 对象
        Pattern pattern = Pattern.compile(regex);
        // 创建 matcher 对象
        Matcher matcher = pattern.matcher(input);
        // 检查输入字符串是否与正则表达式匹配
        return matcher.matches();
    }

    /**
     * 导入板车对数据的校验(尺寸)
     * @param input 数据
     * @return 结果
     * */
    public static boolean validateString2(String input) {
        // 正则表达式：匹配小数部分为两位的浮点数
        String regex = "^\\d+(?:\\.\\d{2})?$";
        // 创建 Pattern 对象
        Pattern pattern = Pattern.compile(regex);
        // 创建 matcher 对象
        Matcher matcher = pattern.matcher(input);
        // 检查输入字符串是否与正则表达式匹配
        return matcher.matches();
    }



    /**
     * 导入板车
     * @param trucks 板车数据列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importTrucks(List<BaseFlatbedTruck> trucks, boolean updateSupport) {
        if (StringUtils.isNull(trucks) || trucks.size() == 0)
        {
            throw new ServiceException("导入板车数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (BaseFlatbedTruck truck : trucks) {
            try {
                //对自重,载重,最大装载尺寸进行格式校验
                String deadWeight = truck.getDeadWeight();
                String loadWeight = truck.getLoadWeight();
                String loadSize = truck.getLoadSize();

                // 验证是否存在相同的城市代码
                BaseFlatbedTruck flatbedTruck = truckMapper.selectByCode(truck.getCode());
                //数据格式校验
                if(!(validateString2(deadWeight) && validateString2(loadWeight) && validateString(loadSize))){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、代码 " + truck.getCode() + " 格式错误");
                }
                else if (StringUtils.isNull(flatbedTruck)) {
                    BeanValidators.validateWithException(validator, truck);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    truck.setCreateBy(loginUser.getUsername());
                    truck.setCreateTime(DateUtils.getNowDate());
                    truckMapper.addFlatbedTruck(truck);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、代码 " + truck.getCode() + " 导入成功");
                }
                else if (updateSupport) {
                    BeanValidators.validateWithException(validator, truck);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    truck.setId(flatbedTruck.getId());
                    truck.setUpdateBy(loginUser.getUsername());
                    truck.setUpdateTime(DateUtils.getNowDate());
                    truckMapper.editFlatbedTruck(truck);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、代码 " + truck.getCode() + " 更新成功");
                }
                else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、代码 " + truck.getCode() + " 已存在");
                }
            }catch (Exception e){
                failureNum++;
                String msg = "<br/>" + failureNum + "、代码 " + truck.getCode() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
