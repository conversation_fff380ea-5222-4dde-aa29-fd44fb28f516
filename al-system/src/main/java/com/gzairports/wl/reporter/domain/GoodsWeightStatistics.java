package com.gzairports.wl.reporter.domain;

import com.gzairports.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GoodsWeightStatistics {
    /**
     * 制单日期
     */
    @Excel(name = "制单日期", width = 32)
    private String writeDate;

    /**
     * 货量
     */
    @Excel(name = "货量", width = 32)
    private BigDecimal weight;
}
