package com.gzairports.common.business.arrival.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 申请修改列表查询参数
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@Data
public class ApplyEditQuery {

    /** 流水号 */
    private String serialNo;

    /** 办单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 办单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 状态 APPLY 申请 EDIT 已修改 REFUSE 已拒绝*/
    private String applyStatus;

    /** 提货人 */
    private String customerName;

    /** 提货人证件号 */
    private String customerIdNo;

    /** 经办人 */
    private String handleBy;

}
