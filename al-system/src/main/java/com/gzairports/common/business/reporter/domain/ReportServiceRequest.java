package com.gzairports.common.business.reporter.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-02-19
 */
@Data
@TableName("all_report_service_request")
public class ReportServiceRequest {

    /** 主键id */
    private Long id;

    /** 运单号 */
    private String waybillCode;

    /** 代理人 货站操作人（昵称）*/
    private String agent;

    /** 服务项目 */
    private String serviceItem;

    /** 预算选择时间 申请次数/小时 */
    private Integer selectTime;

    /** 实际次数/小时 */
    private Integer actualTime;

    /** 状态: 暂存:STAGING 申请:UNAUDITED 完成服务:SUCCESS 拒绝服务:REFUSE */
    private String status;

    /** 支付状态 */
    private String payStatus;

    /** 预授权支付金额 */
    private BigDecimal payMoney;
}
