package com.gzairports.hz.business.departure.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.hz.business.departure.domain.HzDepExitCargo;
import com.gzairports.hz.business.departure.domain.query.ExitCargoQuery;
import com.gzairports.hz.business.departure.domain.query.WaybillInfoQuery;
import com.gzairports.hz.business.departure.domain.vo.DetailedVo;
import com.gzairports.hz.business.departure.domain.vo.ExitCargoVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 退货管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-26
 */
@Mapper
public interface ExitCargoMapper extends BaseMapper<HzDepExitCargo> {

    /**
     * 根据条件查询拉下数据
     * @param query 查询条件
     * @return 结果
     */
    List<DetailedVo> selectExitCargoList(WaybillInfoQuery query);

    /**
     * 查询退货管理列表
     * @param query 查询参数
     * @return 退货列表
     */
    List<ExitCargoVo> selectListByQuery(ExitCargoQuery query);

    /**
     * 根据id查询退货详情
     * @param id 查询id
     * @return 详情
     */
    ExitCargoVo selectByExitId(Long id);
}
