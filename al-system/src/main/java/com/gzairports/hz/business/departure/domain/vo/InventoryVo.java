package com.gzairports.hz.business.departure.domain.vo;

import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 库存管理返回数据
 * <AUTHOR>
 * @date 2024-07-15
 */
@Data
public class InventoryVo {

    /** 收运id */
    private Long id;

    /** 运单id */
    private Long waybillId;

    /** 运单号 */
    @Excel(name = "运单号")
    private String waybillCode;

    /** 开单时间 */
    @Excel(name = "开单时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date writeTime;

    /** 发货代理人 */
    @Excel(name = "发货代理人")
    private String agentCode;

    /** 件数 */
    @Excel(name = "件数")
    private Integer quantity;

    /** 重量 */
    @Excel(name = "重量")
    private BigDecimal weight;

    /** 入库件数 */
    @Excel(name = "入库件数")
    private Integer collectQuantity;

    /** 入库重量 */
    @Excel(name = "入库重量")
    private BigDecimal collectWeight;

    /** 库存件数 */
    @Excel(name = "库存件数")
    private Integer inventoryQuantity;

    /** 库存重量 */
    @Excel(name = "库存重量")
    private BigDecimal inventoryWeight;

    /** 仓库 */
    @Excel(name = "仓库")
    private String store;

    /** 库位 */
    @Excel(name = "库位")
    private String locator;

    /** uld */
    private String uld;


}
