<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.reporter.mapper.ReportDataRepeatWeightMapper">

    <insert id="insertBatchOrUpdate">
        insert into all_report_dep_repeat_weight (id, quantity, weight, board_cargo_weight, board_weight,
        file_weight, file_quantity, uld, flight_no, flight_date, oper_name)
        values
        <foreach collection="repeatWeightList" item="repeatWeight" separator=",">
            (#{repeatWeight.id},#{repeatWeight.quantity},#{repeatWeight.weight},#{repeatWeight.boardCargoWeight},
            #{repeatWeight.boardWeight},#{repeatWeight.fileWeight},#{repeatWeight.fileQuantity},
             #{repeatWeight.uld},#{repeatWeight.flightNo}, #{repeatWeight.flightDate}, #{repeatWeight.operName})
        </foreach>
        ON DUPLICATE KEY UPDATE
        quantity = VALUES(quantity),
        weight = VALUES(weight),
        board_cargo_weight = VALUES(board_cargo_weight),
        board_weight = VALUES(board_weight),
        file_weight = VALUES(file_weight),
        file_quantity = VALUES(file_quantity),
        uld = VALUES(uld),
        oper_name = VALUES(oper_name),
        flight_no = VALUES(flight_no),
        flight_date = VALUES(flight_date);
    </insert>
</mapper>