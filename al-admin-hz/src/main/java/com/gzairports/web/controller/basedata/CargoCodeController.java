package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BaseCargoCode;
import com.gzairports.common.basedata.domain.query.CargoCodeQuery;
import com.gzairports.common.basedata.service.ICargoCodeService;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.domain.TreeSelect;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 货站货品代码Controller
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@RestController
@RequestMapping("/base/cargoCode")
@Api(tags = "货站货品代码数据接口")
public class CargoCodeController extends BaseController {

    @Autowired
    private ICargoCodeService cargoCodeService;

    /**
     * 查询货站货品代码列表
     */
//    @PreAuthorize("@ss.hasPermi('base:cargoCode:cargoCodeList')")
    @GetMapping("/cargoCodeList")
    @ApiOperation(value = "查询货站货品代码列表")
    public TableDataInfo cargoCodeList(CargoCodeQuery query)
    {
        startPage();
        List<BaseCargoCode> list = cargoCodeService.selectCargoCodeList(query);
        return getDataTable(list);
    }

    /**
     * 查询货站货品代码下拉树结构
     */
    @GetMapping("/cargoCodeListTree")
    @ApiOperation(value = "查询货站货品代码下拉树结构")
    public AjaxResult cargoCodeListTree()
    {
        return AjaxResult.success(cargoCodeService.selectCargoCodeListTree());
    }

    /**
     * 导出货站货品代码
     */
    @PreAuthorize("@ss.hasPermi('base:cargoCode:exportCargoCode')")
    @Log(title = "导出货站货品代码", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCargoCode")
    @ApiOperation(value = "导出货站货品代码")
    public void exportCargoCode(HttpServletResponse response, CargoCodeQuery query)
    {
        List<BaseCargoCode> list = cargoCodeService.selectCargoCodeList(query);
        ExcelUtil<BaseCargoCode> util = new ExcelUtil<BaseCargoCode>(BaseCargoCode.class);
        util.exportExcel(response, list, "货站货品代码");
    }

    /**
     * 导出货站货品代码下载模板
     */
    @PreAuthorize("@ss.hasPermi('base:cargoCode:exportCargoCode')")
    @Log(title = "导出货站货品代码", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCargoCodeNull")
    @ApiOperation(value = "导出货站货品代码模板")
    public void exportCargoCodeNull(HttpServletResponse response)
    {
        List<BaseCargoCode> list = new ArrayList<>();
        ExcelUtil<BaseCargoCode> util = new ExcelUtil<BaseCargoCode>(BaseCargoCode.class);
        util.exportExcel(response, list, "货站货品代码模板");
    }

    /**
     * 导入货站货品代码
     */
    @PreAuthorize("@ss.hasPermi('base:cargoCode:importCargoCode')")
    @Log(title = "导入货站货品代码", businessType = BusinessType.IMPORT)
    @PostMapping("/importCargoCode")
    @ApiOperation(value = "导入货站货品代码")
    public AjaxResult importCargoCode(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<BaseCargoCode> util = new ExcelUtil<BaseCargoCode>(BaseCargoCode.class);
        List<BaseCargoCode> cargoCodes = util.importExcel(file.getInputStream());
        String message = cargoCodeService.importCargoCode(cargoCodes, updateSupport);
        return success(message);
    }

    /**
     * 新增货站货品代码
     */
    @PreAuthorize("@ss.hasPermi('base:cargoCode:addCargoCode')")
    @Log(title = "新增货站货品代码", businessType = BusinessType.INSERT)
    @PostMapping("/addCargoCode")
    @ApiOperation(value = "新增货站货品代码")
    public AjaxResult addCargoCode(@RequestBody BaseCargoCode cargoCode)
    {
        return toAjax(cargoCodeService.addCargoCode(cargoCode));
    }

    /**
     * 修改货站货品代码
     */
    @PreAuthorize("@ss.hasPermi('base:cargoCode:editCargoCode')")
    @Log(title = "修改货站货品代码", businessType = BusinessType.UPDATE)
    @PostMapping("/editCargoCode")
    @ApiOperation(value = "修改货站货品代码")
    public AjaxResult editCargoCode(@RequestBody BaseCargoCode cargoCode)
    {
        return toAjax(cargoCodeService.editCargoCode(cargoCode));
    }

    /**
     * 删除货站货品代码
     */
    @PreAuthorize("@ss.hasPermi('base:cargoCode:delCargoCode')")
    @Log(title = "删除货站货品代码", businessType = BusinessType.UPDATE)
    @GetMapping("/delCargoCode/{ids}")
    @ApiOperation(value = "删除货站货品代码")
    public AjaxResult delCargoCode(@PathVariable("ids") Long[] ids)
    {
        return toAjax(cargoCodeService.delCargoCode(ids));
    }
}
