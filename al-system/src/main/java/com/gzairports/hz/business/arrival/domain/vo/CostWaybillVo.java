package com.gzairports.hz.business.arrival.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lan
 * @Desc: 进港运单费用返回参数
 * @create: 2024-11-27 17:52
 **/
@Data
public class CostWaybillVo {
    /** 序号 */
    @Excel(name = "序号", scale = 0)
    private Long idx;

    /** 办单流水号 */
    @Excel(name = "流水号", scale = 1)
    private String serialNo;

    /** 运单号 */
    @Excel(name = "运单号", scale = 2)
    private String waybillCode;

    /** 代理人 */
    private String agentCompany;

    /** 结算客户*/
    @Excel(name = "结算客户")
    private String settleUser;

    /** 收货人 */
    @Excel(name = "收货人")
    private String consign;

    /** 结算客户 */
//    @Excel(name = "结算客户")
    private String customer;

    /** 计费方式 */
    @Excel(name = "支付方式", scale = 4)
    private String billingMethod;

    /** 代理人 */
    private String deptId;

    /** 总费用 */
    @Excel(name = "总费用", scale = 5)
    private BigDecimal totalCost;

    /** 运单件数 */
    @Excel(name = "运单件数", scale = 6)
    private Integer quantity;

    /** 运单重量 */
    @Excel(name = "运单重量", scale = 7)
    private BigDecimal weight;

    /** 办单件数 */
    @Excel(name = "办单件数", scale = 8)
    private Integer handleQuantity;

    /** 办单重量 */
    @Excel(name = "办单重量", scale = 9)
    private BigDecimal handleWeight;

    /** 提货件数 */
    @Excel(name = "提货件数", scale = 10)
    private Integer tallyQuantity;

    /** 提货重量 */
    @Excel(name = "提货重量", scale = 11)
    private BigDecimal tallyWeight;

    /** 货品代码 */
    @Excel(name = "货品代码", scale = 12)
    private String cargoCode;

    /** 品名 */
    @Excel(name = "品名", scale = 13)
    private String cargoName;

    /** 经办人 */
    @Excel(name = "经办人", scale = 14)
    private String handleBy;

    /** 办单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date handleTime;

    @Excel(name = "办单时间", scale = 15)
    private String handleTime2;

    /** 是否支付 0未支付 1已支付 2已作废 */
    private Integer isPay;

    /** 是否出库 0未出库 1已出库 */
    private Integer isOut;

    /** 是否支付 0未支付 1已支付 2已作废 */
    @Excel(name = "是否支付", scale = 16)
    private String isPayStr;

    /** 是否出库 0未出库 1已出库 */
    @Excel(name = "是否出库", scale = 17)
    private String isOutStr;

    /** 出库时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date outTime;

    @Excel(name = "出库时间", scale = 18)
    private String outTime2;

    /** 出库人 */
    @Excel(name = "出库人", scale = 19)
    private String outBy;

    /** 办单id */
    private Long id;
}
