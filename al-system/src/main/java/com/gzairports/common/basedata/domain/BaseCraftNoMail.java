package com.gzairports.common.basedata.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * @author: lan
 * @Desc: 基础数据机型机号
 * @create: 2024-12-09 13:48
 **/
@Data
@TableName("base_craft_no_settings")
public class BaseCraftNoMail {
    /** 主键id */
    private Long id;

    /** 机号 */
    @Excel(name = "机号")
    private String craftNo;

    /** 机型 */
    private String craftType;

    /** 航司 */
    private String airWays;

    /** 目的站 */
    private String desPort;

    /** 生效开始时间 */
    @Excel(name = "生效开始时间",dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 生效截止时间 */
    @Excel(name = "生效截止时间",dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 类型 1:副邮箱机号 2:特殊供氧机号 3:特殊舱位机号 */
    private Integer type;
}
