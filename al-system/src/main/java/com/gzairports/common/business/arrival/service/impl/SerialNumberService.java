package com.gzairports.common.business.arrival.service.impl;

import com.gzairports.common.business.arrival.domain.vo.SerialNumberRecord;
import com.gzairports.common.business.arrival.mapper.SerialNumberMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * Created by david on 2025/6/5
 */
@Service
public class SerialNumberService {

    @Autowired
    private SerialNumberMapper serialNumberMapper;

    public String generateSerialNumber(String prefix) {
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        int retry = 3;
        while (retry-- > 0) {
            SerialNumberRecord record = serialNumberMapper.selectByDateAndPrefix(dateStr, prefix);
            if (record == null) {
                serialNumberMapper.insertNewRecord(dateStr, prefix);
                return dateStr + prefix + String.format("%05d", 1);
            }
            int newSequence = record.getLastSequence() + 1;
            int rowsAffected = serialNumberMapper.incrementSequence(dateStr, prefix, record.getUpdateTime());
            if (rowsAffected == 1) {
                return dateStr + prefix + String.format("%05d", newSequence);
            }
            try {
                Thread.sleep(50);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        throw new RuntimeException("生成流水号失败，请稍后再试");
    }
}
