package com.gzairports.framework.aspectj;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.gzairports.common.core.domain.WebLog;
import com.gzairports.common.enums.BusinessStatus;
import com.gzairports.common.enums.HttpMethod;
import com.gzairports.common.utils.ServletUtils;
import com.gzairports.common.utils.ip.IpUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.HandlerMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 统一日志处理切面
 * zwh
 */
@Aspect
@Component
@Order(1)
@Slf4j
public class WebLogAspect {

    private static SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 切点配置：
     * 1. execution(public * com.gzairports.*.controller.*.*(..)):  com.gzairports包下所有下级子包中的controller包中的所有类中的所有public方法。
     * 2. execution(public * com.gzairports.*.*.controller.*.*(..))： com.gzairports下的两级子包下的controller包下的所有类中的public方法
     * 3. execution(public * com.gzairports.*.controller.*.*.*(..))： com.zgairports下的一级子包下的controller包的所有两级子包下的public方法
     */
    @Pointcut("execution(public * com.gzairports.*.controller.*.*.*(..))")
    public void webLog() {
    }

    @Before("webLog()")
    public void doBefore(JoinPoint joinPoint) throws Throwable {
        //log.info("请求参数：{}", JSONUtil.parse(joinPoint.getArgs()));
        //log.info("获取到的参数信息：{}",JSONUtil.parse(getParameter(((MethodSignature)joinPoint.getSignature()).getMethod(),joinPoint.getArgs())));
    }

    @AfterReturning(value = "webLog()", returning = "ret")
    public void doAfterReturning(JoinPoint joinPoint, Object ret) throws Throwable {
        //  handleApiLog(joinPoint,null,ret);
    }

    /**
     * 异常通知
     *
     * @param joinPoint
     * @param e
     * @throws Throwable
     */
    @AfterThrowing(value = "webLog()", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, Exception e) throws Throwable {
        WebLog webLog = new WebLog();
        handleCommonInfo(joinPoint, webLog);
        if (e != null) {
            webLog.setStatus(BusinessStatus.FAIL.ordinal());
            webLog.setErrorMsg(com.gzairports.common.utils.StringUtils.substring(e.getMessage(), 0, 2000));
        }
        //推送到logstash
        log.info("{}", JSONUtil.toJsonStr(webLog));
    }
    /**
     * 环绕通知
     * @param joinPoint
     * @return
     * @throws Throwable
     */
    @Around("webLog()")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        WebLog webLog = new WebLog();

        long startTime = System.currentTimeMillis();
        handleCommonInfo(joinPoint,webLog);

        //记录请求信息(通过Logstash传入Elasticsearch)
        Object result = joinPoint.proceed();
        long endTime = System.currentTimeMillis();
        //保留一百个字符的返回结果
        if(null!=result && JSON.toJSONString(result).length()>100){
            webLog.setResult(JSON.toJSONString(result).substring(0,100));
        }else{
            webLog.setResult(result);
        }
        webLog.setSpendTime((int) (endTime - startTime));
        webLog.setStartTime(dateTimeFormat.format(startTime));
//        log.info("{}", JSONUtil.toJsonStr(webLog));
        return result;
    }
    /**
     * 处理通用信息
     *
     * @param joinPoint
     * @param webLog
     */
    private void handleCommonInfo(JoinPoint joinPoint, WebLog webLog) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();
        if (method.isAnnotationPresent(ApiOperation.class)) {
            ApiOperation log = method.getAnnotation(ApiOperation.class);
            webLog.setDescription(log.value());
        }
        String urlStr = request.getRequestURL().toString();
        webLog.setBasePath(StrUtil.removeSuffix(urlStr, URLUtil.url(urlStr).getPath()));
        webLog.setIp(IpUtils.getIpAddr(ServletUtils.getRequest()));
        webLog.setMethod(request.getMethod());
        webLog.setParameter(getParameter(method, joinPoint.getArgs(), request));
        webLog.setUri(request.getRequestURI());
        webLog.setUrl(request.getRequestURL().toString());
    }

    /**
     * log信息推送到logstash
     * @param webLog
     */
    private void logToLogstash(WebLog webLog){
        Map<String, Object> logMap = new HashMap<>();
        logMap.put("url", webLog.getUrl());
        logMap.put("method", webLog.getMethod());
        logMap.put("parameter", webLog.getParameter());
        logMap.put("spendTime", webLog.getSpendTime());
        logMap.put("description", webLog.getDescription());
        //log.info(Markers.appendEntries(logMap), JSONUtil.parse(webLog).toString());
    }


    /**
     * 获取请求的参数
     *
     * @param joinPoint 操作日志
     * @throws Exception 异常
     */
    private void setRequestValue(WebLog webLog, JoinPoint joinPoint, String requestMethod) throws Exception {
        if (HttpMethod.PUT.name().equals(requestMethod) || HttpMethod.POST.name().equals(requestMethod)) {
            String params = argsArrayToString(joinPoint.getArgs());
            webLog.setParameter(com.gzairports.common.utils.StringUtils.substring(params, 0, 2000));
        } else {
            Map<?, ?> paramsMap = (Map<?, ?>) ServletUtils.getRequest().getAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE);
            webLog.setParameter(com.gzairports.common.utils.StringUtils.substring(paramsMap.toString(), 0, 2000));
        }
    }

    /**
     * 参数拼装
     */
    private String argsArrayToString(Object[] paramsArray) {
        String params = "";
        if (paramsArray != null && paramsArray.length > 0) {
            for (int i = 0; i < paramsArray.length; i++) {
                if (com.gzairports.common.utils.StringUtils.isNotNull(paramsArray[i]) && !isFilterObject(paramsArray[i])) {
                    Object jsonObj = JSON.toJSON(paramsArray[i]);
                    params += jsonObj.toString() + " ";
                }
            }
        }
        return params.trim();
    }

    /**
     * 判断是否需要过滤的对象。
     *
     * @param o 对象信息。
     * @return 如果是需要过滤的对象，则返回true；否则返回false。
     */
    @SuppressWarnings("rawtypes")
    private boolean isFilterObject(final Object o) {
        Class<?> clazz = o.getClass();
        if (clazz.isArray()) {
            return clazz.getComponentType().isAssignableFrom(MultipartFile.class);
        } else if (Collection.class.isAssignableFrom(clazz)) {
            Collection collection = (Collection) o;
            for (Iterator iter = collection.iterator(); iter.hasNext(); ) {
                return iter.next() instanceof MultipartFile;
            }
        } else if (Map.class.isAssignableFrom(clazz)) {
            Map map = (Map) o;
            for (Iterator iter = map.entrySet().iterator(); iter.hasNext(); ) {
                Map.Entry entry = (Map.Entry) iter.next();
                return entry.getValue() instanceof MultipartFile;
            }
        }
        return o instanceof MultipartFile || o instanceof HttpServletRequest || o instanceof HttpServletResponse
                || o instanceof BindingResult;
    }

    /**
     * 根据方法和传入的参数获取请求参数
     */
    private Object getParameter(Method method, Object[] args, HttpServletRequest request) {
        List<Object> argList = new ArrayList<>();
        Parameter[] parameters = method.getParameters();
        for (int i = 0; i < parameters.length; i++) {
            //将RequestBody注解修饰的参数作为请求参数
            Parameter parameter = parameters[i];
            RequestBody requestBody = parameters[i].getAnnotation(RequestBody.class);
            if (requestBody != null) {
                argList.add(args[i]);
            }
            //将RequestParam注解修饰的参数作为请求参数
            RequestParam requestParam = parameters[i].getAnnotation(RequestParam.class);
            if (requestParam != null) {
                Map<String, Object> map = new HashMap<>();
                String key = parameters[i].getName();
                if (!StringUtils.isEmpty(requestParam.value())) {
                    key = requestParam.value();
                }
                map.put(key, args[i]);
                argList.add(map);
            }
            //将get请求携带的参数也写进去
            String queryString = request.getQueryString();
            if (!StringUtils.isEmpty(queryString)) {
                String[] split = queryString.split("&");
                if (StringUtils.isEmpty(split)) {
                    continue;
                }
                for (String param : split) {
                    if(StringUtils.isEmpty(param)){
                        continue;
                    }
                    String[] paramKeyValue = param.split("=");
                    Map<String, Object> map = new HashMap<>();

                    if(StringUtils.isEmpty(paramKeyValue)){
                        continue;
                    }
                    //长度小于2，则代表参数没有值
                    if(paramKeyValue.length<2){
                        map.put(paramKeyValue[0], "");
                    }else{
                        map.put(paramKeyValue[0], paramKeyValue[1]);
                    }

                    argList.add(map);
                }
            }


        }
        if (argList.size() == 0) {
            return null;
        } else if (argList.size() == 1) {
            return argList.get(0);
        } else {
            return argList;
        }
    }
}
