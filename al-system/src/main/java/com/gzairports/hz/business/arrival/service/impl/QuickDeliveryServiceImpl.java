package com.gzairports.hz.business.arrival.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.hz.business.arrival.domain.HzArrQuickDelivery;
import com.gzairports.hz.business.arrival.domain.query.QuickDeliveryQuery;
import com.gzairports.hz.business.arrival.mapper.QuickDeliveryMapper;
import com.gzairports.hz.business.arrival.service.IQuickDeliveryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 快速交付管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
@Service
public class QuickDeliveryServiceImpl extends ServiceImpl<QuickDeliveryMapper, HzArrQuickDelivery> implements IQuickDeliveryService {

    @Autowired
    private QuickDeliveryMapper quickDeliveryMapper;

    /**
     * 查询快速交付列表
     * @param query 查询条件
     * @return 结果
     */
    @Override
    public List<HzArrQuickDelivery> selectList(QuickDeliveryQuery query) {
        List<HzArrQuickDelivery> hzArrQuickDeliveries = quickDeliveryMapper.selectListByQuery(query);
        for (HzArrQuickDelivery hzArrQuickDelivery : hzArrQuickDeliveries) {
            if (hzArrQuickDelivery.getEndTime().before(new Date())){
                hzArrQuickDelivery.setStatus(2);
            }
        }
        this.saveOrUpdateBatch(hzArrQuickDeliveries);
        return hzArrQuickDeliveries;
    }

    /**
     * 查看交付详情
     * @param id 交付id
     * @return 详情
     */
    @Override
    public HzArrQuickDelivery getInfo(Long id) {
        return quickDeliveryMapper.selectById(id);
    }

    /**
     * 新增交付数据
     * @param delivery 新增数据
     * @return 结果
     */
    @Override
    public int add(HzArrQuickDelivery delivery) {
        return quickDeliveryMapper.insert(delivery);
    }

    /**
     * 修改交付数据
     * @param delivery 修改数据
     * @return 结果
     */
    @Override
    public int edit(HzArrQuickDelivery delivery) {
        return quickDeliveryMapper.updateById(delivery);
    }
}
