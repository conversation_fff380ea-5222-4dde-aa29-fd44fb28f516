package com.gzairports.wl.charge.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 客户运价查询参数
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@Data
public class FreightRateCustomQuery {

    /** id集合 */
    private List<Long> ids;

    /** 运价名称 */
    private String rateName;

    /** 客户名称 */
    private String customer;

    /** 所属单位 */
    private Long deptId;

    /** 适用单证类型 */
    private String applicableDocuments;

    /** 生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date effectiveDate;

    /** 截止日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expirationDate;
}
