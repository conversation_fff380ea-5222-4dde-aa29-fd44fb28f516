# 项目相关配置
framework:
  # 名称
  name: demo
  # 版本
  version: 1.0.0
  # 版权年份
  copyrightYear: 2021
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/zwh/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: C:/Users/<USER>/Pictures
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math
  #redis key前缀
  redisKeyPrefix: hz_master

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 18086
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    max-threads: 800
    # Tomcat启动初始化的线程数，默认值25
    min-spare-threads: 30

#log4j2日志配置
logging:
  config: classpath:log4j2-spring.xml
  level:
    com.gzairport: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 1440
  ip:
    # 登录ip错误次数
    maxIpCount: 8
    lockTime: 1440

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles: 
    active: ${profiles.active}
  # 文件上传
  servlet:
     multipart:
       # 单个文件大小
       max-file-size:  50MB
       # 设置总上传的文件大小
       max-request-size:  100MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  mvc:
    format:
      date-time: yyyy-MM-dd HH:mm:ss
  jackson:
    # 日期格式化
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      # 格式化输出
      indent_output: false
      # 忽略无法转换的对象
      fail_on_empty_beans: false
    deserialization:
      # 允许对象忽略json中不存在的属性
      fail_on_unknown_properties: false


# token配置
token:
    # 令牌自定义标识
    header: Authorization
    # 令牌密钥
    secret: abcdefghijklmnopqrstuvwxyz
    # 令牌有效期（默认12个小时）
    expireTime: 720
    # 是否允许账户多端登录
    soloLogin: false
  

# MyBatis Plus配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.gzairports.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:/hz/mapper/**/*Mapper.xml,classpath*:/mapper/**/*Mapper.xml,classpath*:/common/**/*Mapper.xml,classpath*:/wl/mapper/infoquery/*Mapper.xml,classpath*:/wl/mapper/ticket/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml
# PageHelper分页插件
pagehelper: 
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

# 防止XSS攻击
xss: 
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

system:
  type: 1
