package com.gzairports.web.controller.cargofee;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.basedata.domain.BaseAgent;
import com.gzairports.common.basedata.mapper.BaseAgentMapper;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.wl.cargofee.domain.PayRecord;
import com.gzairports.wl.cargofee.domain.query.PayRecordQuery;
import com.gzairports.wl.cargofee.service.IPayRecordService;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 支付流水记录Controller
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@RestController
@RequestMapping("/cargofee/payRecord")
public class PayRecordController extends BaseController {

    @Autowired
    private IPayRecordService payRecordService;

    @Autowired
    private BaseAgentMapper agentMapper;

    /**
     * 查询支付流水记录列表
     */
//    @PreAuthorize("@ss.hasPermi('cargofee:payRecord:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询支付流水记录列表")
    public TableDataInfo list(PayRecordQuery query){
        BaseAgent baseAgent = agentMapper.selectOne(new QueryWrapper<BaseAgent>()
                .eq("dept_id", SecurityUtils.getHighParentId()));
        query.setDeptId(baseAgent.getId());
        startPage();
        List<PayRecord> list = payRecordService.selectList(query);
        return getDataTable(list);
    }

    /**
     * 导出支付流水记录列表
     */
    @PreAuthorize("@ss.hasPermi('cargofee:payRecord:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出支付流水记录列表")
    public void export(HttpServletResponse response, PayRecordQuery query)
    {
        BaseAgent baseAgent = agentMapper.selectOne(new QueryWrapper<BaseAgent>()
                .eq("dept_id", SecurityUtils.getHighParentId()));
        query.setDeptId(baseAgent.getId());
        List<PayRecord> list = payRecordService.selectList(query);
        ExcelUtil<PayRecord> util = new ExcelUtil<PayRecord>(PayRecord.class);
        util.exportExcel(response, list, "支付流水记录");
    }
}
