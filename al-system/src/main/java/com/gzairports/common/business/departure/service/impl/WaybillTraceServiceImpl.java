package com.gzairports.common.business.departure.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.BaseAirportCode;
import com.gzairports.common.basedata.mapper.AirportCodeMapper;
import com.gzairports.common.business.arrival.domain.FlightLoadUldWaybill;
import com.gzairports.common.business.arrival.domain.FlightLoadWaybill;
import com.gzairports.common.business.arrival.domain.query.AutoOrderQuery;
import com.gzairports.common.business.arrival.mapper.FlightInfoMapper;
import com.gzairports.common.business.arrival.mapper.FlightLoadUldWaybillMapper;
import com.gzairports.common.business.arrival.mapper.FlightLoadWaybillMapper;
import com.gzairports.common.business.departure.domain.Mawb;
import com.gzairports.common.business.departure.domain.WaybillTrace;
import com.gzairports.common.business.departure.domain.vo.CarrierFlightVo;
import com.gzairports.common.business.departure.mapper.MailWaybillMapper;
import com.gzairports.common.business.departure.mapper.MawbMapper;
import com.gzairports.common.business.departure.mapper.WaybillTraceMapper;
import com.gzairports.common.business.departure.service.IWaybillTraceService;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.infoquery.mapper.FlightMapper;
import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.common.log.mapper.WaybillLogMapper;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.hz.business.departure.domain.FlightLoad;
import com.gzairports.hz.business.departure.domain.FlightLoadUld;
import com.gzairports.hz.business.departure.mapper.FlightLoadMapper;
import com.gzairports.hz.business.departure.mapper.FlightLoadUldMapper;
import com.gzairports.wl.departure.domain.Hawb;
import com.gzairports.wl.departure.domain.MergeHawbMawb;
import com.gzairports.wl.departure.domain.vo.MawbQueryVo;
import com.gzairports.wl.departure.domain.vo.NewWaybillTraceVO;
import com.gzairports.wl.departure.domain.vo.WaybillTraceVo;
import com.gzairports.wl.departure.mapper.HawbMapper;
import com.gzairports.wl.departure.mapper.MergeHawbMawbMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 运单跟踪Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Service
public class WaybillTraceServiceImpl extends ServiceImpl<WaybillTraceMapper, WaybillTrace> implements IWaybillTraceService {

    @Autowired
    private WaybillTraceMapper waybillTraceMapper;

    @Autowired
    private MawbMapper mawbMapper;

    @Autowired
    private HawbMapper hawbMapper;

    @Autowired
    private MergeHawbMawbMapper mergeMapper;

    @Autowired
    private MailWaybillMapper mailWaybillMapper;

    @Autowired
    private WaybillTraceMapper traceMapper;

    @Autowired
    private FlightLoadMapper flightLoadMapper;

    @Autowired
    private FlightLoadUldMapper flightLoadUldMapper;

    @Autowired
    private FlightLoadUldWaybillMapper flightLoadUldWaybillMapper;

    @Autowired
    private FlightLoadWaybillMapper flightLoadWaybillMapper;

    @Autowired
    private WaybillLogMapper logMapper;

    @Autowired
    private AirportCodeMapper airportCodeMapper;

    /**
     * 查询运单跟踪数据
     * @param type 运单类型
     * @param waybillCode 运单号
     * @return 运单跟踪详情
     */
    @Override
    public WaybillTraceVo selectTraceInfo(Integer type, String waybillCode) {
        switch (type){
            case 1:
                waybillCode = "AWBF" + waybillCode;
                WaybillTraceVo vo = hawbMapper.selectOneByCode(waybillCode);
                if (vo == null){
                    throw new CustomException("无当前分单信息");
                }

                //查询航班信息
                queryFlightData(vo);

                List<MergeHawbMawb> mergeHawb = mergeMapper.selectList(new QueryWrapper<MergeHawbMawb>().eq("hawb_id", vo.getId()));
                if (!CollectionUtils.isEmpty(mergeHawb)){
                    MergeHawbMawb mergeHawbMawb = mergeHawb.get(0);
                    String masterWaybillCode = mawbMapper.selectCodeById(mergeHawbMawb.getMawbId(),null);
                    if (masterWaybillCode.isEmpty()){
                        return vo;
                    }
                    vo.setMasterWaybillCode(masterWaybillCode);
                    setCarrierFlight(vo, mergeHawbMawb.getMawbId());
                    List<WaybillTrace> traceList = traceMapper.selectList(new QueryWrapper<WaybillTrace>()
                            .eq("waybill_code", masterWaybillCode)
                            .eq("type",0));
                    if (traceList != null){
                        traceList = traceList.stream().sorted(Comparator.comparing(WaybillTrace::getNodeIndex)).collect(Collectors.toList());
                    }
                    vo.setTraces(traceList);
                }
                return vo;
            case 0:
                waybillCode = "AWBA" + waybillCode;
                WaybillTraceVo traceVo = mawbMapper.selectCodeByCode(waybillCode);
                if (traceVo == null){
                    throw new CustomException("无当前主单信息");
                }
                //查询航班信息
                queryFlightData(traceVo);
                //查询航班信息
//                BaseAirportCode baseAirportCode0 = airportCodeMapper.selectByCode(traceVo.getDesPort());
//                traceVo.setDesPortName(baseAirportCode0.getChineseName());
//                Flight flight0 = flightMapper.selectByWaybillId(traceVo.getId());
//                traceVo.setFlightArrivalDate(Optional.ofNullable(flight0).map(Flight::getPlanLandingTime).orElse(null));

                /*Integer total = 0;
                BigDecimal weight = new BigDecimal(0);*/
                Integer total = traceVo.getTotalQuantity();
                BigDecimal weight = traceVo.getTotalWeight();
                StringBuilder builder = new StringBuilder();
                List<MergeHawbMawb> mergeMawb = mergeMapper.selectList(new QueryWrapper<MergeHawbMawb>()
                        .eq("mawb_id", traceVo.getId()));
                if (!CollectionUtils.isEmpty(mergeMawb)){
                    for (MergeHawbMawb mergeHawbMawb : mergeMawb) {
                        total += mergeHawbMawb.getMergeNum();
                        weight = weight.add(mergeHawbMawb.getMergeWeight());
                        String slaveWaybillCode = hawbMapper.selectOneById(mergeHawbMawb.getHawbId(),null);
                        if (StringUtils.isNotEmpty(slaveWaybillCode)){
                            builder.append(slaveWaybillCode).append(",");
                        }
                    }
                    String result = builder.toString();
                    if (!result.isEmpty()) {
                        result = result.substring(0, result.length() - 1);
                        traceVo.setSlaveWaybillCode(result);
                    }
                    traceVo.setTotalQuantity(total);
                    traceVo.setTotalWeight(weight);
                }
                setCarrierFlight(traceVo, traceVo.getId());
                List<WaybillTrace> traceList = traceMapper.selectList(new QueryWrapper<WaybillTrace>()
                        .eq("waybill_code", waybillCode)
                        .eq("type",0));
                if (traceList != null){
                    traceList = traceList.stream().sorted(Comparator.comparing(WaybillTrace::getNodeIndex)).collect(Collectors.toList());
                }
                traceVo.setTraces(traceList);
                List<WaybillLog> logs = logMapper.selectList(new QueryWrapper<WaybillLog>().eq("waybill_code",waybillCode).eq("type","DEP").orderByDesc("oper_time"));
                traceVo.setLogs(logs);
                return traceVo;
            case 2:
                waybillCode = "AWBM" + waybillCode;
                WaybillTraceVo waybillTraceVo = mailWaybillMapper.selectOneByCode(waybillCode);
                if (waybillTraceVo == null){
                    throw new CustomException("无当前邮件单信息");
                }
                //查询航班信息
                queryFlightData(waybillTraceVo);
                setCarrierFlight(waybillTraceVo, waybillTraceVo.getId());
                List<WaybillTrace> traces = traceMapper.selectList(new QueryWrapper<WaybillTrace>()
                        .eq("waybill_code", waybillCode)
                        .eq("type",2));
                if (traces != null){
                    traces = traces.stream().sorted(Comparator.comparing(WaybillTrace::getNodeIndex)).collect(Collectors.toList());
                }
                waybillTraceVo.setTraces(traces);
                List<WaybillLog> logList = logMapper.selectList(new QueryWrapper<WaybillLog>().eq("waybill_code",waybillCode).eq("type","DEP").orderByDesc("oper_time"));
                waybillTraceVo.setLogs(logList);
                return waybillTraceVo;
            default:
                return null;
        }
    }

    private void queryFlightData(WaybillTraceVo waybillTraceVo) {
        BaseAirportCode baseAirportCode2 = airportCodeMapper.selectByCode(waybillTraceVo.getDesPort());
        waybillTraceVo.setDesPortName(baseAirportCode2.getChineseName());

        MawbQueryVo mawbQueryVo = mawbMapper.queryFlightInfo(waybillTraceVo.getFlightDate(), waybillTraceVo.getFlightNo());
        if(ObjectUtil.isNotNull(mawbQueryVo)){
            waybillTraceVo.setFlightArrivalDate(mawbQueryVo.getTerminalSchemeLandInTime());
        }
    }

    /**
     * 查询进港出港运单跟踪数据
     *
     * @param type        运单类型
     * @param waybillCode 运单号
     * @return 运单跟踪详情
     */
    @Override
    public List<WaybillTraceVo> selectAllStatusInfo(Integer type, String waybillCode) {
//        String businessType = getBusinessType(type, waybillCode);
        //进港数据不做限制
        Long deptId = SecurityUtils.getHighParentId();
        switch (type) {
            case 0:
                waybillCode = "AWBA" + waybillCode;
                List<WaybillTraceVo> traceVoList = mawbMapper.selectAllCodeByCode(waybillCode, deptId);
                if (CollectionUtils.isEmpty(traceVoList)) {
                    throw new CustomException("无当前主单信息");
                }
                for (WaybillTraceVo traceVo : traceVoList) {
                    /*Integer total = 0;
                    BigDecimal weight = new BigDecimal(0);*/
                    Integer total = traceVo.getTotalQuantity();
                    BigDecimal weight = traceVo.getTotalWeight();
                    StringBuilder builder = new StringBuilder();
                    List<MergeHawbMawb> mergeMawb = mergeMapper.selectList(new QueryWrapper<MergeHawbMawb>()
                            .eq("mawb_id", traceVo.getId()));
                    if (!CollectionUtils.isEmpty(mergeMawb)) {
                        for (MergeHawbMawb mergeHawbMawb : mergeMawb) {
                            total += mergeHawbMawb.getMergeNum();
                            weight = weight.add(mergeHawbMawb.getMergeWeight());
                            String slaveWaybillCode = hawbMapper.selectOneById(mergeHawbMawb.getHawbId(), deptId);
                            if (StringUtils.isNotEmpty(slaveWaybillCode)) {
                                builder.append(slaveWaybillCode).append(",");
                            }
                        }
                        String result = builder.toString();
                        if (!result.isEmpty()) {
                            result = result.substring(0, result.length() - 1);
                            traceVo.setSlaveWaybillCode(result);
                        }
                        traceVo.setTotalQuantity(total);
                        traceVo.setTotalWeight(weight);
                    }
                    setCarrierFlight(traceVo, traceVo.getId());
                    List<WaybillTrace> traceList = traceMapper.selectList(new QueryWrapper<WaybillTrace>()
                            .eq("waybill_code", waybillCode)
                            .eq("type", 0));
                    if (traceList != null) {
                        traceList = traceList.stream().sorted(Comparator.comparing(WaybillTrace::getNodeIndex)).collect(Collectors.toList());
                    }
                    traceVo.setTraces(traceList);
                }
                return traceVoList;
            case 2:
                waybillCode = "AWBM" + waybillCode;
                List<WaybillTraceVo> waybillTraceVoList = mailWaybillMapper.selectAllByCode(waybillCode, deptId);
                if (CollectionUtils.isEmpty(waybillTraceVoList)) {
                    throw new CustomException("无当前邮件单信息");
                }
                for (WaybillTraceVo waybillTraceVo : waybillTraceVoList) {
                    setCarrierFlight(waybillTraceVo, waybillTraceVo.getId());
                    List<WaybillTrace> traces = traceMapper.selectList(new QueryWrapper<WaybillTrace>()
                            .eq("waybill_code", waybillCode)
                            .eq("type", 2));
                    if (traces != null) {
                        traces = traces.stream().sorted(Comparator.comparing(WaybillTrace::getNodeIndex)).collect(Collectors.toList());
                    }
                    waybillTraceVo.setTraces(traces);
                }
                return waybillTraceVoList;
            default:
                return null;
        }
    }

    private String getBusinessType(Integer type, String waybillCode) {
        //判断是进港还是出港
        HashMap<Integer, String> typeMap = new HashMap<>();
        typeMap.put(1, "AWBF");
        typeMap.put(0, "AWBA");
        typeMap.put(2, "AWBM");
        if (!typeMap.containsKey(type)) {
            return null;
        }
        String actualWaybillCode = typeMap.get(type) + waybillCode;
        LambdaQueryWrapper<Mawb> lqw = Wrappers.lambdaQuery(Mawb.class)
                .select(Mawb::getType)
                .eq(Mawb::getWaybillCode, actualWaybillCode)
                .eq(Mawb::getIsDel, 0);
        return Optional.ofNullable(mawbMapper.selectOne(lqw))
                .orElse(new Mawb()).getType();
    }

    /**
     * H5运单跟踪数据
     * @param query 查询条件
     * @return H5运单跟踪数据
     */
    @Override
    public List<WaybillTrace> h5WaybillTrace(AutoOrderQuery query) {
        Hawb hawb = hawbMapper.selectOne(new QueryWrapper<Hawb>().eq("waybill_code", query.getWaybillCode()));
        if (hawb == null){
            throw new CustomException("无当前分单信息");
        }
        List<MergeHawbMawb> mergeHawb = mergeMapper.selectList(new QueryWrapper<MergeHawbMawb>().eq("hawb_id", hawb.getId()));
        if (!CollectionUtils.isEmpty(mergeHawb)){
            MergeHawbMawb mergeHawbMawb = mergeHawb.get(0);
            String masterWaybillCode = mawbMapper.selectCodeById(mergeHawbMawb.getMawbId(),null);
            if (masterWaybillCode.isEmpty()){
                return null;
            }
            List<WaybillTrace> traceList = traceMapper.selectList(new QueryWrapper<WaybillTrace>()
                    .eq("waybill_code", masterWaybillCode)
                    .eq("type",0));
            if (traceList != null){
                traceList = traceList.stream().sorted(Comparator.comparing(WaybillTrace::getNodeIndex)).collect(Collectors.toList());
            }
           return traceList;
        }
        return null;
    }

    @Override
    public List<String> selectInfoWaybillCode(String code) {
        List<Mawb> mawbs = mawbMapper.selectList(new QueryWrapper<Mawb>()
                .likeLeft("waybill_code", code));
        return mawbs.stream().map(Mawb::getWaybillCode).collect(Collectors.toList());
    }

    @Override
    public List<NewWaybillTraceVO> selectWaybillStatusInfo(String waybillCode) {
        if (StringUtils.isEmpty(waybillCode)){
            return null;
        }
        List<String> list = Arrays.asList(waybillCode.split(","));
        List<NewWaybillTraceVO> traceList = mawbMapper.selectNewTraceList(list);
        for (NewWaybillTraceVO newWaybillTraceVO : traceList) {
            BaseAirportCode desCode = airportCodeMapper.selectByCode(newWaybillTraceVO.getDesPort());
            BaseAirportCode sourceCode = airportCodeMapper.selectByCode(newWaybillTraceVO.getSourcePort());
            String source = Optional.ofNullable(sourceCode)
                    .map(code -> code.getCode() + "（" + code.getChineseName() + "）")
                    .orElse(newWaybillTraceVO.getSourcePort());

            String dest = Optional.ofNullable(desCode)
                    .map(code -> code.getCode() + "（" + code.getChineseName() + "）")
                    .orElse(newWaybillTraceVO.getDesPort());
            newWaybillTraceVO.setDepAndDes(source + " / " + dest);
        }
        return traceList;
    }

    private void setCarrierFlight(WaybillTraceVo waybillTraceVo, Long id) {
        List<Long> ids = traceMapper.selectFlight(id);
        if (!CollectionUtils.isEmpty(ids)) {
            List<Long> collect = ids.stream().distinct().collect(Collectors.toList());
            List<CarrierFlightVo> flightNo = traceMapper.selectFlightNo(collect);
            if (!CollectionUtils.isEmpty(flightNo)) {
                for (CarrierFlightVo vo : flightNo) {
                    Integer quantity = 0;
                    BigDecimal weight = new BigDecimal(0);
//                    FlightLoad flightLoad = flightLoadMapper.selectOne(new QueryWrapper<FlightLoad>()
//                            .eq("flight_id", vo.getFlightId()));
//                    List<FlightLoadUld> FlightLoadUldList = flightLoadUldMapper.selectList(new QueryWrapper<FlightLoadUld>()
//                            .eq("flight_load_id", flightLoad.getId()));
                    List<FlightLoad> flightLoadList = flightLoadMapper.selectList(new QueryWrapper<FlightLoad>()
                            .eq("flight_id", vo.getFlightId()));
                    List<Long> flightIdList = flightLoadList.stream().map(FlightLoad::getId).collect(Collectors.toList());
                    List<FlightLoadUld> FlightLoadUldList = flightLoadUldMapper.selectList(new QueryWrapper<FlightLoadUld>()
                            .in("flight_load_id", flightIdList));
                    if(FlightLoadUldList.size() > 0){
                        for (FlightLoadUld flightLoadUld:FlightLoadUldList) {
                            List<FlightLoadUldWaybill> FlightLoadUldWaybillList = flightLoadUldWaybillMapper.selectList(new QueryWrapper<FlightLoadUldWaybill>()
                                    .eq("load_uld_id", flightLoadUld.getId())
                                    .eq("waybill_id", id));
                            if(FlightLoadUldWaybillList.size() > 0){
                                quantity = FlightLoadUldWaybillList.stream().map(FlightLoadUldWaybill::getQuantity).reduce(0, Integer::sum);
                                weight = FlightLoadUldWaybillList.stream().map(FlightLoadUldWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                        }
                    }
                    List<FlightLoadWaybill> flightLoadWaybills = flightLoadWaybillMapper.selectList(new QueryWrapper<FlightLoadWaybill>()
                            .in("flight_load_id", flightIdList)
                            .eq("waybill_id", id));
                    if(flightLoadWaybills.size() > 0){
                        Integer reduce = flightLoadWaybills.stream().map(FlightLoadWaybill::getQuantity).reduce(0, Integer::sum);
                        BigDecimal reduce1 = flightLoadWaybills.stream().map(FlightLoadWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                        quantity += reduce;
                        weight = weight.add(reduce1);
                    }
                    vo.setQuantity(quantity);
                    vo.setWeight(weight);
                }
                waybillTraceVo.setVoList(flightNo);
            }

        }
    }


    /**
     * 新增运单跟踪数据
     * @param waybillTrace 运单跟踪数据
     */
    @Async
    @Transactional(rollbackFor = Exception.class)
    public synchronized void insertWaybillTrace(WaybillTrace waybillTrace) {
        if (waybillTrace.getWaybillCode().contains("AWBA")){
            waybillTrace.setType(0);
        } else if (waybillTrace.getWaybillCode().contains("AWBM")){
            waybillTrace.setType(2);
        }else {
            waybillTrace.setType(1);
        }
        waybillTrace.setNodeIndex(NODE_INDEX.get(waybillTrace.getNodeName()));
        waybillTraceMapper.insert(waybillTrace);
    }

    private static final HashMap<String,Integer> NODE_INDEX = new HashMap<>();
    static {
        NODE_INDEX.put("已发送",0);
        NODE_INDEX.put("预授权支付",1);
        NODE_INDEX.put("货站入库",2);
        NODE_INDEX.put("已预配",3);
        NODE_INDEX.put("临时拉下",4);
        NODE_INDEX.put("已退货",5);
        NODE_INDEX.put("已作废",6);
        NODE_INDEX.put("已换单",7);
        NODE_INDEX.put("已出港",8);
        NODE_INDEX.put("已结算",9);
    }
}
