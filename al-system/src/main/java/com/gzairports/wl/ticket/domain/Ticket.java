package com.gzairports.wl.ticket.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 单证信息表
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Data
@TableName("wl_ticket")
public class Ticket {

    /** 主键id */
    private Long id;

    /** 票证来源 */
    private Long billSourceId;

    /** 票证来源 */
    private String billSource;

    /** 开始编号 */
    private Integer startNumber;

    /** 开始编号检查号 */
    private Integer startCode;

    /** 结束编号 */
    private Integer endNumber;

    /** 结束编号检查号 */
    private Integer endCode;

    /** 是否：包舱单（包租整个货舱的运单） */
    private Integer isChartering;

    /** 部门 */
    private Long deptId;

    /** 记录人 */
    private String recorder;

    /** 记录时间 */
    private Date recordTime;

    /** 状态 */
    private Integer status;

    /** 备注 */
    private String remark;

    /** 条目是否逻辑删除 */
    private Integer isDel;

    /** 票证类型 */
    private String billType;

    /** 票证前缀 */
    private String billPrefix;

    @TableField(exist = false)
    private Integer startNumberNew;

    @TableField(exist = false)
    private Integer endNumberNew;
}
