package com.gzairports.hz.business.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: lan
 * @Desc: 出港费用管理导出
 * @create: 2025-01-17 14:52
 **/

@Data
public class ChargeSettleWaybillVo {
    /** 主键id */
    private Long id;

    /** 开单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开单时间", dateFormat = "yyyy-MM-dd HH:mm:ss", needMerge = true)
    private Date writeTime;

    /** 运单号 */
    @Excel(name = "运单号", needMerge = true)
    private String waybillCode;

    /** 代理人 */
    @Excel(name = "代理人", needMerge = true)
    private String agentCode;

    /** 结算方式 */
    @Excel(name = "结算方式", needMerge = true)
    private String settleMethod;

    /** 货品大类 */
    @Excel(name = "货品大类", needMerge = true)
    private String categoryName;

    /** 货品代码 */
    @Excel(name = "货品代码", needMerge = true)
    private String cargoCode;

    /** 品名 */
    @Excel(name = "品名", needMerge = true)
    private String cargoName;

    /** 名码一致 */
    @Excel(name = "名码一致", needMerge = true)
    private String codeNameSame;

    /** 特货代码1 */
    @Excel(name = "特货代码", needMerge = true)
    private String specialCargoCode1;

    /** 件数 */
    @Excel(name = "件数", needMerge = true,cellType = Excel.ColumnType.NUMERIC)
    private Integer quantity;

    /** 重量 */
    @Excel(name = "重量", needMerge = true,cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal weight;

    /** 计费重量 */
    @Excel(name = "计费重量", needMerge = true,cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal chargeWeight;

    /** 预支付费用 */
    @Excel(name = "预支付费用", needMerge = true,cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal payMoney;

    /** 支付时间 */
    @Excel(name = "预授权支付时间", dateFormat = "yyyy-MM-dd HH:mm:ss", needMerge = true)
    private Date payTime;

    /** 运单状态 */
    @Excel(name = "运单状态", needMerge = true)
    private String status;

    /** 支付状态 */
    @Excel(name = "支付状态", needMerge = true)
    private String payStatusStr;

    /** 未结算费用 */
    @Excel(name = "未结算费用", needMerge = true,cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal unSettleMoney;

    /** 结算费用合计 */
    @Excel(name = "结算费用合计", needMerge = true,cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal settleSum;

    @Excel(name = "处置费", needMerge = true, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal processingFee = new BigDecimal(0);

    /** 冷藏费 */
    @Excel(name = "冷藏费", needMerge = true, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal refrigerationFee = new BigDecimal(0);

    /** 搬运费 */
    @Excel(name = "搬运费", needMerge = true, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal handlingFee = new BigDecimal(0);

    /** 电报费 */
    @Excel(name = "电报费", needMerge = true, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal cableCharge = new BigDecimal(0);

    /** 叉车费 */
    @Excel(name = "叉车费", needMerge = true, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal forkliftCharge = new BigDecimal(0);

    /** 差异化服务费(跨航司) */
    @Excel(name = "差异化服务费(跨航司)", needMerge = true, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal diffServiceCharge = new BigDecimal(0);

    /** 结算件数合计 */
    @Excel(name = "结算件数合计", needMerge = true, cellType = Excel.ColumnType.NUMERIC)
    private Integer settleQuantitySum;

    /** 结算重量合计 */
    @Excel(name = "结算重量合计", needMerge = true, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal settleWeightSum;

    /** 结算费用明细 */
//    @Excels({
//            @Excel(name = "结算时间", targetAttr = "settleTime", type = Excel.Type.EXPORT),
//            @Excel(name = "结算航班", targetAttr = "settleFlightNo", type = Excel.Type.EXPORT),
//            @Excel(name = "结算已出港件数", targetAttr = "settleDepQuantity", type = Excel.Type.EXPORT),
//            @Excel(name = "结算已出港重量", targetAttr = "settleDepWeight", type = Excel.Type.EXPORT),
//            @Excel(name = "结算出港费用", targetAttr = "settleCost", type = Excel.Type.EXPORT),
//    })
    @Excel(name = "结算费用明细")
    private List<ChargeSettleDetailVo> detailVos;

}
