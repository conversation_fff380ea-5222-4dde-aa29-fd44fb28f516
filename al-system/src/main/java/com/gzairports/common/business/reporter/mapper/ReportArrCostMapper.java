package com.gzairports.common.business.reporter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.reporter.domain.ReportArrCost;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-03
 */
@Mapper
public interface ReportArrCostMapper extends BaseMapper<ReportArrCost> {
    void deleteBatch(@Param("arrReportIds") List<Long> arrReportIds);

    int insertBatchSomeColumn(List<ReportArrCost> arrCostList);
}
