package com.gzairports.wl.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 货站在线缴费运单参数
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
public class OnlineMawbVo {

    /** 主键id */
    private Long id;

    /** 运单号 */
    @Excel(name = "运单号")
    private String waybillCode;

    /** 始发站 */
    @Excel(name = "始发站")
    private String sourcePort;

    /** 目的站 */
    @Excel(name = "目的站")
    private String desPort;

    /** 航班号 */
    @Excel(name = "航班号")
    private String flightNo1;

    /** 航班日期 */
    @Excel(name = "航班日期", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date flightDate1;

    /** 发货人 */
    @Excel(name = "发货人")
    private String shipper;

    /** 特货代码1 */
    @Excel(name = "特货代码1")
    private String specialCargoCode1;

    /** 品名编码 */
    @Excel(name = "品名编码")
    private String cargoCode;

    /** 品名 */
    @Excel(name = "品名")
    private String cargoName;

    /** 填开时间 */
    @Excel(name = "填开时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date writeTime;

    /** 件数 */
    @Excel(name = "件数")
    private Integer quantity;

    /** 重量 */
    @Excel(name = "重量")
    private BigDecimal weight;

    /** 计费重量 */
    @Excel(name = "计费重量")
    private BigDecimal chargeWeight;

    /** 费用总计 */
    @Excel(name = "费用总计")
    private BigDecimal costSum;

    /** 缴费状态 0 未授权支付 1 已授权支付 2 已结算 */
    private Integer payStatus;

    /** 缴费金额 */
    @Excel(name = "缴费金额")
    private BigDecimal payMoney;

    /** 已结算退还费用 */
    @Excel(name = "已结算退还费用")
    private BigDecimal refund;

    private String type;
}
