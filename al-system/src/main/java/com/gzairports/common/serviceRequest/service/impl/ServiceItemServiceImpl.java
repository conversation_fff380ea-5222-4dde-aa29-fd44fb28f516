package com.gzairports.common.serviceRequest.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.serviceRequest.mapper.ServiceItemMapper;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.serviceRequest.domain.ServiceItem;
import com.gzairports.common.serviceRequest.service.IServiceItemService;
import com.gzairports.common.charge.domain.HzChargeItems;
import com.gzairports.common.charge.mapper.HzChargeItemsMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 服务项目管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@Service
public class ServiceItemServiceImpl extends ServiceImpl<ServiceItemMapper, ServiceItem> implements IServiceItemService {

    @Autowired
    private ServiceItemMapper itemMapper;

    @Autowired
    private HzChargeItemsMapper chargeItemsMapper;

    /**
     * 查询服务项目列表
     * @return 结果
     */
    @Override
    public List<ServiceItem> selectItemList() {
        List<ServiceItem> items = itemMapper.selectList(new QueryWrapper<ServiceItem>().eq("is_del", 0));
        for (ServiceItem item : items) {
            HzChargeItems chargeItems = chargeItemsMapper.selectById(item.getChargeItemId());
            item.setChargeItem(chargeItems.getChargeName());
        }
        return items;
    }

    /**
     * 新增服务项目
     * @param serviceItem 服务项目参数
     * @return 结果
     */
    @Override
    public int insertItem(ServiceItem serviceItem) {
        ServiceItem item = itemMapper.selectOne(new QueryWrapper<ServiceItem>()
                .eq("service_name", serviceItem.getServiceName())
                .eq("charge_item_id", serviceItem.getChargeItemId())
                .eq("is_del",0));
        if (item != null){
            throw new CustomException("已存在相同数据");
        }
        serviceItem.setCreateTime(new Date());
        serviceItem.setCreateBy(SecurityUtils.getUsername());
        return itemMapper.insert(serviceItem);
    }

    /**
     * 删除服务项目
     * @param id 服务项目id
     * @return 结果
     */
    @Override
    public int deleteItem(Long id) {
        ServiceItem item = itemMapper.selectById(id);
        item.setIsDel(1);
        return itemMapper.updateById(item);
    }
}
