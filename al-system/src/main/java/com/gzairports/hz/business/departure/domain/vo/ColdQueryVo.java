package com.gzairports.hz.business.departure.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.gzairports.common.annotation.Excel;
import com.gzairports.hz.business.departure.domain.HzColdExamine;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 * 冷藏登记列表数据
 *
 * <AUTHOR>
 */
@Data
public class ColdQueryVo {

    /** 主键id */
    private Long id;

    /** 运单号 */
    @Excel(name = "运单号")
    private String waybillCode;

    /** 品名 */
    @Excel(name = "品名")
    private String cargoName;

    /** 代理人/收货人 */
    private String agent;

    /** 冷库 */
    @Excel(name = "冷库")
    private String coldStore;

    /** 进出港类型 */
    private String type;

    /** 状态 */
    @Excel(name = "状态",readConverterExp = "0=未入库,1=冷藏中,2=已出库")
    private Integer status;

    /** 支付状态 */
    private Integer payStatus;

//    /** 审核状态 */
//    private Integer examineStatus;

    /** 申请时长 */
    private Integer timeLen;

    /** 申请时间 */
    private Date applyTime;

    /** 申请人 */
    private String applyUser;

//    /** 审核时间 */
//    private Date createTime;
//
//    /** 审核人 */
//    private String userName;

    /** 入库时间 */
    @Excel(name = "入库时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date wareTime;

    /** 出库时间 */
    @Excel(name = "出库时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date outTime;

    /** 使用时间（小时） */
    @Excel(name = "使用时间（小时）")
    private BigDecimal useTime;

    /** 计费时间（小时） */
    @Excel(name = "计费时间（小时）")
    private BigDecimal chargeTime;

    /** 计费金额 */
    @Excel(name = "计费金额")
    private BigDecimal sumMoney;

//    /** 单个审核内容 */
//    private String content;
//
//    /** 审核内容 */
//    @TableField(exist = false)
//    private List<HzColdExamine> examines;
}
