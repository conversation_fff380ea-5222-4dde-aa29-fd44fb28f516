package com.gzairports.common.bankUtils;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import java.io.StringReader;
import java.io.StringWriter;

/**
 * @author: lan
 * @Desc: 将对象转化为xml的方法
 * @create: 2025-03-19 11:11
 **/

public class XmlUtil {
    public static <T> String convertToXml(T object) throws JAXBException {
        JAXBContext context = JAXBContext.newInstance(object.getClass());
        Marshaller marshaller = context.createMarshaller();
        marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, Boolean.TRUE);

        StringWriter sw = new StringWriter();
        marshaller.marshal(object, sw);

        return sw.toString();
    }

    public static <T> T convertXmlToObject(String xmlString, Class<T> clazz) throws JAXBException {
        // 创建JAXB上下文
        JAXBContext jaxbContext = JAXBContext.newInstance(clazz);
        // 创建解组器
        Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();
        // 使用StringReader读取XML字符串
        StringReader reader = new StringReader(xmlString);
        // 解组并返回结果
        return clazz.cast(unmarshaller.unmarshal(reader));
    }
}
