package com.gzairports.hz.business.departure.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 组货调度板箱表
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
@Data
@TableName("hz_dep_group_waybill")
public class HzDepGroupWaybill {

    /** 主键id */
    private Long id;

    /** 航班配载id */
    private Long flightLoadId;

    /** 运单号 */
    private String waybillCode;

    /** 目的站 */
    private String desPort;

    /** 类型 */
    private String type;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 是否入库 0 否 1 是 */
    @TableField(exist = false)
    private String status;
}
