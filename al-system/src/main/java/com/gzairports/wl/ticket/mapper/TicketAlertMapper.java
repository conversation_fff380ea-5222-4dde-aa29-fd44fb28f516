package com.gzairports.wl.ticket.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.wl.ticket.domain.TicketAlert;
import com.gzairports.wl.ticket.domain.query.TicketQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 单证预警管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Mapper
public interface TicketAlertMapper extends BaseMapper<TicketAlert> {

    /**
     * 查询单证预警列表
     *
     * @param query 查询参数
     * @return 单证预警列表
     */
    List<TicketAlert> selectListByQuery(TicketQuery query);

    /**
     * 单证预警删除
     *
     * @param id 单证预警id
     * @return 结果
     */
    int remove(Long id);

    /**
     * 查看预警详情
     *
     * @param id 预警id
     * @return 预警详情
     */
    TicketAlert getInfo(Long id);

    /**
     * 根据条件查询预警值
     *
     * @param deptId 单位id
     * @param billSource 票证来源
     * @param isChartering 是否包舱单
     * @return 预警详情
     */
    TicketAlert selectByBillId(@Param("deptId") Long deptId,@Param("billSource") String billSource,@Param("isChartering") Integer isChartering);
}
