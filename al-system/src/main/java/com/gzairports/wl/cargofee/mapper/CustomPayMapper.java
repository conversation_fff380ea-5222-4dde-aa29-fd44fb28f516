package com.gzairports.wl.cargofee.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.wl.cargofee.domain.CustomPay;
import com.gzairports.wl.cargofee.domain.query.PayRecordQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 客户支付流水Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@Mapper
public interface CustomPayMapper extends BaseMapper<CustomPay> {

    /**
     * 查询客户支付流水数据
     * @param query 查询参数
     * @return 客户支付流水数据
     */
    List<CustomPay> selectListByQuery(PayRecordQuery query);
}
