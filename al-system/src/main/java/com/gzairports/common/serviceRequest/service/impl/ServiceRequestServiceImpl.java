package com.gzairports.common.serviceRequest.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.gzairports.common.basedata.domain.BaseAgent;
import com.gzairports.common.basedata.domain.BaseBalance;
import com.gzairports.common.basedata.domain.BaseCargoCode;
import com.gzairports.common.basedata.mapper.BaseAgentMapper;
import com.gzairports.common.basedata.mapper.BaseBalanceMapper;
import com.gzairports.common.basedata.mapper.CargoCodeMapper;
import com.gzairports.common.business.arrival.domain.HzArrItem;
import com.gzairports.common.business.arrival.domain.HzArrTally;
import com.gzairports.common.business.arrival.domain.ItemDetail;
import com.gzairports.common.business.arrival.mapper.HzArrItemMapper;
import com.gzairports.common.business.arrival.mapper.HzArrTallyMapper;
import com.gzairports.common.business.departure.domain.CostDetail;
import com.gzairports.common.business.departure.domain.Mawb;
import com.gzairports.common.business.departure.mapper.CostDetailMapper;
import com.gzairports.common.business.departure.mapper.MawbMapper;
import com.gzairports.common.charge.domain.HzChargeIrRelation;
import com.gzairports.common.charge.domain.HzChargeItemRule;
import com.gzairports.common.charge.domain.HzChargeItems;
import com.gzairports.common.charge.domain.HzChargeRule;
import com.gzairports.common.charge.domain.vo.BillRuleVo;
import com.gzairports.common.charge.domain.vo.IrRelationVo;
import com.gzairports.common.charge.mapper.HzChargeIrRelationMapper;
import com.gzairports.common.charge.mapper.HzChargeItemRuleMapper;
import com.gzairports.common.charge.mapper.HzChargeItemsMapper;
import com.gzairports.common.charge.mapper.HzChargeRuleMapper;
import com.gzairports.common.charge.service.BillingRule;
import com.gzairports.common.charge.service.impl.BillingRuleFactory;
import com.gzairports.common.core.domain.PageQuery;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.common.log.service.IWaybillLogService;
import com.gzairports.common.message.domain.Message;
import com.gzairports.common.message.domain.vo.SocketMessageVo;
import com.gzairports.common.message.mapper.MessageMapper;
import com.gzairports.common.serviceRequest.domain.ServiceExamine;
import com.gzairports.common.serviceRequest.domain.ServiceItem;
import com.gzairports.common.serviceRequest.domain.ServiceRequest;
import com.gzairports.common.serviceRequest.domain.bo.ServiceRequestBo;
import com.gzairports.common.serviceRequest.domain.query.ServiceRequestQuery;
import com.gzairports.common.serviceRequest.domain.vo.ServiceInfoVo;
import com.gzairports.common.serviceRequest.domain.vo.ServiceRequestVo;
import com.gzairports.common.serviceRequest.mapper.ServiceExamineMapper;
import com.gzairports.common.serviceRequest.mapper.ServiceItemMapper;
import com.gzairports.common.serviceRequest.mapper.ServiceRequestMapper;
import com.gzairports.common.serviceRequest.service.IServiceRequestService;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.utils.*;
import com.gzairports.hz.business.arrival.domain.HzArrRecordOrder;
import com.gzairports.hz.business.arrival.mapper.HzArrRecordOrderMapper;
import com.gzairports.hz.business.departure.domain.AirWaybill;
import com.gzairports.hz.business.departure.domain.HzColdRegister;
import com.gzairports.hz.business.departure.domain.HzDepExitCargo;
import com.gzairports.hz.business.departure.mapper.AllAirWaybillMapper;
import com.gzairports.hz.business.departure.mapper.ExitCargoMapper;
import com.gzairports.hz.business.departure.rabbitmq.WaybillMessageProducer;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 服务申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Service
public class ServiceRequestServiceImpl extends ServiceImpl<ServiceRequestMapper,ServiceRequest> implements IServiceRequestService {

    @Autowired
    private ServiceRequestMapper requestMapper;

    @Autowired
    private ServiceExamineMapper examineMapper;

    @Autowired
    private AllAirWaybillMapper airWaybillMapper;

    @Autowired
    private MawbMapper mawbMapper;

    @Autowired
    private MessageMapper messageMapper;

    @Autowired
    private WaybillMessageProducer waybillMessageProducer;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    @Autowired
    private ServiceItemMapper itemMapper;

    @Autowired
    private HzArrItemMapper hzArrItemMapper;

    @Autowired
    private HzChargeItemsMapper chargeItemsMapper;

    @Autowired
    private ExitCargoMapper exitCargoMapper;

    @Autowired
    private HzChargeItemRuleMapper itemRuleMapper;

    @Autowired
    private HzArrRecordOrderMapper recordOrderMapper;

    @Autowired
    private HzChargeRuleMapper ruleMapper;

    @Autowired
    private HzChargeIrRelationMapper relationMapper;

    @Autowired
    private CostDetailMapper costDetailMapper;

    @Autowired
    private CargoCodeMapper cargoCodeMapper;

    @Autowired
    private BaseAgentMapper baseAgentMapper;

    @Autowired
    private BaseBalanceMapper baseBalanceMapper;

    @Autowired
    private HzArrTallyMapper hzArrTallyMapper;

    @Autowired
    private IWaybillLogService waybillLogService;

    /**
     * 查询服务申请列表
     * @param query 查询条件
     * @return 服务申请列表
     */
    @Override
    public ServiceRequestVo selectListByQuery(ServiceRequestQuery query) {
        query.setDeptId(SecurityUtils.getHighParentId());
        ServiceRequestVo vo = new ServiceRequestVo();
        List<ServiceRequest> list = requestMapper.selectListByQuery(query);
        if (CollectionUtils.isEmpty(list)){
            return vo;
        }
        int total = list.size();
        int pageNum = query.getStartRow();
        int pageSize = query.getEndRow();
        int fromIndex = (pageNum - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, total);
        List<ServiceRequest> pagedServiceRequestVos = list.subList(fromIndex, toIndex);
        vo.setRequestsList(new PageQuery<List<ServiceRequest>>(
                pagedServiceRequestVos,
                pageNum,
                pageSize,
                total
        ));
        BigDecimal totalPayMoney = new BigDecimal(0);
        BigDecimal totalSettleMoney = new BigDecimal(0);
        BigDecimal totalRefund = new BigDecimal(0);
        for (ServiceRequest request : list) {
            if (request.getPayMoney() != null){
                totalPayMoney = totalPayMoney.add(request.getPayMoney());
            }
            if (request.getEndMoney() != null){
                totalSettleMoney = totalSettleMoney.add(
                        request.getPayMoney()==null ? BigDecimal.ZERO:request.getPayMoney()
                );
            }
            if (request.getRefund() != null){
                totalRefund = totalRefund.add(request.getRefund());
            }
        }
        vo.setTotalPayMoney(totalPayMoney);
        vo.setTotalSettleMoney(totalSettleMoney);
        vo.setTotalRefund(totalRefund);
        return vo;
    }

    /**
     * 新增服务申请
     * @param request 新增参数
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int add(ServiceRequest request) {
        HttpServletResponse response = ServletUtils.getResponse();
        WaybillLog waybillLog = new WaybillLog();
        try{
        ServiceItem serviceItem = itemMapper.selectById(request.getServiceItemId());
        Long deptId = request.getDeptId();
        List<Long> deptIds = getDeptIds(deptId);
//        Mawb mawb = mawbMapper.selectComeInfo(request.getWaybillCode(),serviceItem.getType(), request.getDeptId());
        Mawb mawb = mawbMapper.selectComeInfoNew(request.getWaybillCode(),serviceItem.getType(), deptIds);
        if (mawb == null){
            throw new CustomException("无当前运单信息");
        }
        if (request.getDeptId() != null){
            if(!Objects.equals(mawb.getDeptId(), request.getDeptId())){
                throw new CustomException("代理人不一致,新增失败");
            }
        }
        request.setAgent(mawb.getAgentCompany());
        request.setServiceNo(SerialNumberGenerator.generateSerialNumber());
        request.setRequestTime(new Date());
        request.setServiceItem(serviceItem.getServiceName());
        request.setUpdateTime(new Date());
        requestMapper.insert(request);
        sendMessage(mawb.getWaybillCode(),request.getServiceItem());
        ServiceExamine examine = new ServiceExamine();
        examine.setServiceId(request.getId());
        examine.setCreateTime(new Date());
        if ("STAGING".equals(request.getStatus())){
            examine.setType("暂存");
        }
        if ("UNAUDITED".equals(request.getStatus())){
            examine.setType("申请");
        }
        examine.setUserName(SecurityUtils.getUsername());
        //运单日志的新增
        waybillLog = waybillLogService.getWaybillLog(
                request.getWaybillCode(), 0, SecurityUtils.getNickName(),
                null, null, null,
                request, null, 0, null, new Date(),
                "服务申请,服务项目:"+request.getServiceItem()+",申请次数/小时:"+request.getSelectTime(),
                serviceItem.getType(), null);
        return examineMapper.insert(examine);
        }catch (Exception e){
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" +  "," +
                            "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        }finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }

    @Async
    public void sendMessage(String waybillCode,String serviceItem) {
        String message = "    运单"+waybillCode+"服务申请  \n" +
                "运单"+ waybillCode +"申请"+ serviceItem +"，请及时处理";
        SocketMessageVo vo = new SocketMessageVo();
        vo.setMessage(message);
        vo.setType(2);
        Message mes = new Message();
        mes.setContent(message);
        mes.setCreateTime(new Date());
        mes.setIsHandle(0);
        mes.setPostId(SecurityUtils.getDeptId());
        mes.setTitle("服务申请提醒");
        messageMapper.insert(mes);
        waybillMessageProducer.send(vo);
    }

    /**
     * 物流详情
     * @param id 服务申请id
     * @return 详情
     */
    @Override
    public ServiceInfoVo wlGetInfo(Long id) {
        ServiceInfoVo vo = new ServiceInfoVo();
        ServiceRequest request = requestMapper.selectById(id);
        BeanUtils.copyProperties(request,vo);
        List<ServiceExamine> examines = examineMapper.selectList(new QueryWrapper<ServiceExamine>().eq("service_id", id));
        if (!CollectionUtils.isEmpty(examines)){
            vo.setExamines(examines);
        }
        return vo;
    }

    /**
     * 货站详情
     * @param id 服务申请id
     * @return 详情
     */
    @Override
    public ServiceInfoVo hzGetInfo(Long id) {
        ServiceInfoVo vo = new ServiceInfoVo();
        ServiceRequest request = requestMapper.selectById(id);
        BeanUtils.copyProperties(request,vo);
        List<ServiceExamine> examines = examineMapper.selectList(new QueryWrapper<ServiceExamine>().eq("service_id", id));
        if (!CollectionUtils.isEmpty(examines)){
//            examines.forEach(e->{
//                if(e.getStatus()==null||e.getStatus().equals("")){
//                    e.setStatus("待审核");
//                }else{
//                e.setStatus("PASS".equals(e.getStatus()) ? "通过" : "不通过");
//                }
//            });
            vo.setExamines(examines);
            List<ServiceExamine> collect = examines.stream().sorted(Comparator.comparing(ServiceExamine::getCreateTime).reversed()).collect(Collectors.toList());
            ServiceExamine serviceExamine = collect.get(0);
            vo.setExamineStatus(serviceExamine.getStatus());
            vo.setContent(serviceExamine.getContent());
        }
        return vo;
    }

    /**
     * 提交
     * @param id 服务id
     * @return 结果
     */
    @Override
    public int commit(Long id) {
        ServiceRequest request = requestMapper.selectById(id);
        request.setStatus("UNAUDITED");
        request.setUpdateTime(new Date());
        ServiceExamine examine = new ServiceExamine();
        examine.setServiceId(request.getId());
        examine.setCreateTime(new Date());
        examine.setType("申请");
        examine.setUserName(SecurityUtils.getUsername());
        examineMapper.insert(examine);
        return requestMapper.updateById(request);
    }

    /**
     * 提交审核
     * @param vo 审核参数
     * @return 结果
     */
    @Override
    public int examine(ServiceInfoVo vo) {
        Date date = new Date();
        if ("PASS".equals(vo.getExamineStatus()) && vo.getServiceItem().contains("冷藏")){
            HzColdRegister hzColdRegister = new HzColdRegister();
            hzColdRegister.setStatus(0);
        }
        ServiceRequest request = requestMapper.selectById(vo.getId());
        request.setStatus(vo.getExamineStatus());
        request.setUpdateTime(new Date());
        requestMapper.updateById(request);
        sendMessageForExamine(vo.getWaybillCode(),vo.getExamineStatus(),request.getServiceItem());
        ServiceExamine examine = new ServiceExamine();
        examine.setContent(vo.getContent());
        examine.setStatus(vo.getExamineStatus());
        examine.setCreateTime(date);
        examine.setServiceId(vo.getId());
        examine.setUserName(SecurityUtils.getUsername());
        examine.setType("审核");
        return examineMapper.insert(examine);
    }

    @Override
    public List<ServiceItem> itemList() {
        List<ServiceItem> items = itemMapper.selectList(new QueryWrapper<ServiceItem>().eq("is_del", 0));
        for (ServiceItem item : items) {
            HzChargeItems chargeItems = chargeItemsMapper.selectById(item.getChargeItemId());
            item.setChargeItem(chargeItems.getChargeName());
        }
        return items;
    }

    /**
     * 货站端选择完成服务/拒绝服务
     * @param vo 审核参数
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int handleService(ServiceInfoVo vo) {
        HttpServletResponse response = ServletUtils.getResponse();
        WaybillLog waybillLog = new WaybillLog();
        try{
        ServiceRequest serviceRequest = requestMapper.selectById(vo.getId());
        ServiceExamine examine = new ServiceExamine();
        examine.setContent(vo.getContent());
        examine.setCreateTime(new Date());
        examine.setServiceId(vo.getId());
        examine.setUserName(SecurityUtils.getUsername());
        ServiceItem serviceItem = itemMapper.selectById(serviceRequest.getServiceItemId());
        if (serviceItem == null){
            throw new CustomException("无当前服务项目");
        }
        Long deptId = serviceRequest.getDeptId();
        List<Long> deptIds = getDeptIds(deptId);
//        AirWaybill mawb = airWaybillMapper.selectComeInfo(serviceRequest.getWaybillCode(),serviceItem.getType(), serviceRequest.getDeptId());
        AirWaybill mawb = airWaybillMapper.selectComeInfoNew(serviceRequest.getWaybillCode(),serviceItem.getType(), deptIds);
        BigDecimal costSum = new BigDecimal(0);
        BigDecimal oldCostSum = new BigDecimal(0);
        BigDecimal weightRate;
        BigDecimal chargeWeight = mawb.getChargeWeight() == null ? new BigDecimal(0) : mawb.getChargeWeight();
        if (mawb.getWeight() == null || mawb.getWeight().compareTo(new BigDecimal(0)) == 0){
            weightRate = new BigDecimal(0);
        }else {
            BigDecimal bigDecimal = chargeWeight.divide(mawb.getWeight(),5, RoundingMode.DOWN).multiply(mawb.getWeight());
            weightRate = bigDecimal.setScale(0, RoundingMode.CEILING);
        }
        BaseAgent agent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", serviceRequest.getDeptId()));
        if(vo.getType() == 0){
            if (vo.getActualTime() == null){
                throw new CustomException("无实际使用次数/小时");
            }
            if ("DEP".equals(serviceItem.getType())){
                if(serviceRequest.getPayStatus() == 0){
                    pay(vo.getId());
                }
                List<CostDetail> details = costDetailMapper.selectItemList(serviceRequest.getWaybillCode(),serviceRequest.getDeptId(),serviceRequest.getId());
                for (CostDetail detail : details) {
                    oldCostSum = oldCostSum.add(detail.getTotalCharge());
                    if (detail.getIsDel() == 1){
                        continue;
                    }
                    detail.setDaysInStorage(vo.getActualTime().doubleValue());
                    detail.setUnit(vo.getActualTime());
                    BillRuleVo vo1 = countCost(detail, weightRate, mawb.getQuantity());
                    detail.setTotalCharge(vo1.getTotalCharge());
                    detail.setQuantity(vo1.getQuantity());
                    detail.setRate(vo1.getRate());
                    costSum = costSum.add(vo1.getTotalCharge());
                    detail.setType(1);
                    detail.setIsSettle(1);
                    detail.setIsAuto(1);
                    detail.setFlightId(0L);
                    detail.setSettleDepQuantity(vo.getActualTime());
                    detail.setSettleDepWeight(new BigDecimal(vo.getActualTime()));
                    detail.setCreateTime(new Date());
                    detail.setDeptId(serviceRequest.getDeptId());
                    detail.setId(null);
                    detail.setServiceRequestId(vo.getId());
                    costDetailMapper.insert(detail);
                }
                setStatus(serviceRequest, mawb, costSum, oldCostSum, agent);
            }else {
                List<HzArrItem> items = hzArrItemMapper.selectItemList(serviceRequest.getWaybillCode(),serviceRequest.getId(),1);
                for (HzArrItem item : items) {
                    oldCostSum = oldCostSum.add(item.getTotalCharge());
                    if (item.getIsDel() == 1){
                        continue;
                    }
                    item.setDaysInStorage(vo.getActualTime().doubleValue());
                    item.setUnit(vo.getActualTime());
                    BillRuleVo vo1 = countCost(item, weightRate, mawb.getQuantity());
                    item.setTotalCharge(vo1.getTotalCharge());
                    costSum = costSum.add(vo1.getTotalCharge());
                    item.setIsSettle(1);
                    item.setIsAuto(1);
                    item.setDeptId(serviceRequest.getDeptId());
                    item.setServiceType(1);
                    item.setServiceId(vo.getId());
                    hzArrItemMapper.updateById(item);
                }
                setStatus(serviceRequest, mawb, costSum, oldCostSum, agent);
            }
            serviceRequest.setEndMoney(costSum);
            serviceRequest.setSettleTime(new Date());
            serviceRequest.setStatus("SUCCESS");
            serviceRequest.setActualTime(vo.getActualTime());
            examine.setType("完成服务");
            examine.setStatus("完成服务");
        }else{
            //拒绝服务
            if ("DEP".equals(serviceItem.getType())){
                List<CostDetail> details = costDetailMapper.selectItemList(serviceRequest.getWaybillCode(), serviceRequest.getDeptId(),serviceRequest.getId());
                if (!CollectionUtils.isEmpty(details)){
                    List<CostDetail> collect = details.stream().filter(e -> e.getIsSettle() == 1).collect(Collectors.toList());
                    for (CostDetail detail : collect) {
                        detail.setDaysInStorage(serviceRequest.getSelectTime().doubleValue());
                        detail.setType(1);
                        detail.setIsSettle(1);
                        detail.setIsAuto(1);
                        detail.setFlightId(4L);
                        detail.setSettleDepQuantity(mawb.getQuantity());
                        detail.setSettleDepWeight(new BigDecimal(vo.getSelectTime()));
                        detail.setCreateTime(new Date());
                        detail.setServiceRequestId(vo.getId());
                        costDetailMapper.updateById(detail);
                    }
                    costSum = getBigDecimal(serviceRequest, mawb, costSum, agent, CollectionUtils.isEmpty(collect), collect.stream().map(CostDetail::getTotalCharge));
                }
            }else {
                List<HzArrItem> items = hzArrItemMapper.selectItemList(serviceRequest.getWaybillCode(), serviceRequest.getId(),1);
                for (HzArrItem detail : items) {
                    detail.setDaysInStorage(serviceRequest.getSelectTime().doubleValue());
                    detail.setServiceType(1);
                    detail.setIsSettle(1);
                    detail.setIsAuto(1);
                    detail.setTotalCharge(BigDecimal.ZERO);
                    detail.setServiceId(vo.getId());
                    hzArrItemMapper.updateById(detail);
                }
                costSum = getBigDecimal(serviceRequest, mawb, costSum, agent, CollectionUtils.isEmpty(items), items.stream().map(HzArrItem::getTotalCharge));
            }
            //拒绝服务解释支付金额应该为0
            serviceRequest.setEndMoney(BigDecimal.ZERO);
            serviceRequest.setRefund(costSum);
            serviceRequest.setRemark(vo.getRemark());
            serviceRequest.setStatus("REFUSE");
            examine.setType("拒绝服务");
            examine.setStatus("拒绝服务");
        }
        serviceRequest.setBeginTime(vo.getBeginTime());
        serviceRequest.setEndTime(new Date());
        examineMapper.insert(examine);
        //运单日志的新增
        waybillLog = waybillLogService.getWaybillLog(
                mawb.getWaybillCode(), 0, SecurityUtils.getNickName(),
                null, null, null,
                vo, null, 0, null, new Date(),
                vo.getType()==0?"完成服务":"拒绝服务" +
                        ",服务项目"+serviceRequest.getServiceItem()+",实际次数小时:"+serviceRequest.getActualTime()+
                        ",金额:" + costSum,
                serviceItem.getType(), null);
        serviceRequest.setUpdateTime(new Date());
        return requestMapper.updateById(serviceRequest);
        }catch (Exception e){
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" +  "," +
                            "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        }finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }

    private BigDecimal getBigDecimal(ServiceRequest serviceRequest, AirWaybill mawb, BigDecimal costSum, BaseAgent agent, boolean empty, Stream<BigDecimal> bigDecimalStream) {
        if (!empty) {
            costSum = bigDecimalStream.reduce(BigDecimal.ZERO, BigDecimal::add);
            if (agent != null) {
                if (agent.getSettleMethod() == 0) {
                    serviceRequest.setPayStatus(9);
                } else if (agent.getSettleMethod() == 1) {
                    BigDecimal balance = agent.getBalance() == null ? new BigDecimal(0) : agent.getBalance();
                    BigDecimal subtract = balance.add(costSum);
                    agent.setBalance(subtract);
                    baseAgentMapper.updateBaseAgent(agent);
                    BaseBalance baseBalance = new BaseBalance();
                    baseBalance.setAgentId(agent.getId());
                    baseBalance.setBalance(agent.getBalance());
                    baseBalance.setType("增加余额");
                    baseBalance.setCreateTime(new Date());
                    baseBalance.setCreateBy(SecurityUtils.getNickName());
                    // todo 流水号需从银联支付接口获取
                    //baseBalance.setSerialNo();
                    baseBalance.setTradeMoney(costSum);
                    baseBalance.setWaybillCode(mawb.getWaybillCode());
                    baseBalance.setRemark("服务审核退款");
                    baseBalanceMapper.insertBaseBalance(baseBalance);
                    serviceRequest.setPayStatus(10);
                } else {
                    if (agent.getPayMethod() == 0) {
                        serviceRequest.setPayStatus(11);
                    } else {
                        serviceRequest.setPayStatus(12);
                    }
                }
            } else {
                serviceRequest.setPayStatus(12);
            }
        }
        return costSum;
    }

    private void setStatus(ServiceRequest serviceRequest, AirWaybill mawb, BigDecimal costSum, BigDecimal oldCostSum, BaseAgent agent) {
        if (costSum.compareTo(oldCostSum) > 0) {
            BigDecimal deduct = costSum.subtract(oldCostSum);
            BigDecimal refund = new BigDecimal(0);
            if (agent != null) {
                if (agent.getSettleMethod() == 1) {
                    BigDecimal balance = agent.getBalance() == null ? new BigDecimal(0) : agent.getBalance();
                    BigDecimal subtract = balance.subtract(deduct);
                    if (subtract.compareTo(new BigDecimal(0)) < 0) {
                        throw new CustomException("当前代理人余额不足");
                    } else {
                        agent.setBalance(subtract);
                        baseAgentMapper.updateBaseAgent(agent);
                        BaseBalance baseBalance = new BaseBalance();
                        baseBalance.setAgentId(agent.getId());
                        baseBalance.setBalance(agent.getBalance());
                        baseBalance.setType("减少余额");
                        baseBalance.setCreateTime(new Date());
                        baseBalance.setCreateBy("系统");
                        // todo 流水号需从银联支付接口获取
                        //baseBalance.setSerialNo();
                        baseBalance.setTradeMoney(deduct);
                        baseBalance.setWaybillCode(mawb.getWaybillCode());
                        baseBalance.setRemark("服务审核付款");
                        baseBalanceMapper.insertBaseBalance(baseBalance);
                        serviceRequest.setPayStatus(6);
                        serviceRequest.setRefund(refund);
                    }
                } else if (agent.getSettleMethod() == 0) {
                    serviceRequest.setPayStatus(5);
                    serviceRequest.setRefund(refund);
                } else {
                    if (agent.getPayMethod() == 0) {
                        serviceRequest.setPayStatus(7);
                        serviceRequest.setRefund(refund);
                    } else {
                        serviceRequest.setPayStatus(8);
                        serviceRequest.setRefund(refund);
                    }
                }
            } else {
                serviceRequest.setPayStatus(8);
                serviceRequest.setRefund(refund);
            }
        } else if (costSum.compareTo(oldCostSum) == 0) {
            BigDecimal refund = new BigDecimal(0);
            if (agent != null) {
                if (agent.getSettleMethod() == 1) {
                    serviceRequest.setPayStatus(6);
                    serviceRequest.setRefund(refund);
                } else if (agent.getSettleMethod() == 0) {
                    serviceRequest.setPayStatus(5);
                    serviceRequest.setRefund(refund);
                } else {
                    if (agent.getPayMethod() == 0) {
                        serviceRequest.setPayStatus(7);
                        serviceRequest.setRefund(refund);
                    } else {
                        serviceRequest.setPayStatus(8);
                        serviceRequest.setRefund(refund);
                    }
                }
            } else {
                serviceRequest.setPayStatus(8);
                serviceRequest.setRefund(refund);
            }
        } else {
            BigDecimal add = oldCostSum.subtract(costSum);
            if (agent != null) {
                if (agent.getSettleMethod() == 1) {
                    BigDecimal balance = agent.getBalance() == null ? new BigDecimal(0) : agent.getBalance();
                    BigDecimal subtract = balance.add(add);
                    agent.setBalance(subtract);
                    baseAgentMapper.updateBaseAgent(agent);
                    BaseBalance baseBalance = new BaseBalance();
                    baseBalance.setAgentId(agent.getId());
                    baseBalance.setBalance(agent.getBalance());
                    baseBalance.setType("增加余额");
                    baseBalance.setCreateTime(new Date());
                    baseBalance.setCreateBy("系统");
                    // todo 流水号需从银联支付接口获取
                    //baseBalance.setSerialNo();
                    baseBalance.setTradeMoney(add);
                    baseBalance.setWaybillCode(mawb.getWaybillCode());
                    baseBalance.setRemark("服务审核退款");
                    baseBalanceMapper.insertBaseBalance(baseBalance);
                    serviceRequest.setPayStatus(10);
                    serviceRequest.setRefund(add);
                } else if (agent.getSettleMethod() == 0) {
                    serviceRequest.setPayStatus(9);
                    serviceRequest.setRefund(add);
                } else {
                    if (agent.getPayMethod() == 0) {
                        serviceRequest.setPayStatus(11);
                        serviceRequest.setRefund(add);
                    } else {
                        serviceRequest.setPayStatus(12);
                        serviceRequest.setRefund(add);
                    }
                }
            } else {
                serviceRequest.setPayStatus(12);
                serviceRequest.setRefund(add);
            }
        }
    }

    /**
     * 物流端的预授权支付
     * @param id 服务申请id
     * @return 结果
     */
    @Override
    public int pay(Long id) {
        HttpServletResponse response = ServletUtils.getResponse();
        WaybillLog waybillLog = new WaybillLog();
        try{
            Long deptId = SecurityUtils.getHighParentId();
            ServiceRequest serviceRequest = requestMapper.selectById(id);
            ServiceItem serviceItem = itemMapper.selectById(serviceRequest.getServiceItemId());
            if (serviceItem == null){
                throw new CustomException("无当前服务项目");
            }
            List<Long> deptIds = getDeptIds(deptId);
//            Mawb mawb = mawbMapper.selectComeInfo(serviceRequest.getWaybillCode(),serviceItem.getType(), deptId);
            Mawb mawb = mawbMapper.selectComeInfoNew(serviceRequest.getWaybillCode(),serviceItem.getType(), deptIds);
            BigDecimal costSum = countSum(serviceRequest, serviceItem, mawb);
            BaseAgent agent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", deptId));
            if (agent != null){
                if (agent.getSettleMethod() == 1) {
                    BigDecimal balance = agent.getBalance() == null ? new BigDecimal(0) : agent.getBalance();
                    BigDecimal subtract = balance.subtract(costSum);
                    if (subtract.compareTo(new BigDecimal(0)) < 0) {
                        throw new CustomException("当前余额不足");
                    } else {
                        agent.setBalance(subtract);
                        baseAgentMapper.updateBaseAgent(agent);
                        BaseBalance baseBalance = new BaseBalance();
                        baseBalance.setAgentId(agent.getId());
                        baseBalance.setBalance(agent.getBalance());
                        baseBalance.setType("减少余额");
                        baseBalance.setCreateTime(new Date());
                        baseBalance.setCreateBy(SecurityUtils.getNickName());
                        // todo 流水号需从银联支付接口获取
                        //baseBalance.setSerialNo();
                        baseBalance.setTradeMoney(costSum);
                        baseBalance.setWaybillCode(mawb.getWaybillCode());
                        baseBalance.setRemark("服务申请付款");
                        baseBalanceMapper.insertBaseBalance(baseBalance);
                        serviceRequest.setPayStatus(2);
                        serviceRequest.setPayMethod(2);

                    }
                } else if (agent.getSettleMethod() == 0){
                    serviceRequest.setPayStatus(1);
                    serviceRequest.setPayMethod(3);
                }else {
                    if (agent.getPayMethod() == 0){
                        serviceRequest.setPayStatus(4);
                        serviceRequest.setPayMethod(1);
                    }else {
                        serviceRequest.setPayStatus(3);
                        serviceRequest.setPayMethod(0);
                    }
                }
            }else {
                serviceRequest.setPayStatus(3);
                serviceRequest.setPayMethod(0);
            }
            BigDecimal add = (mawb.getPayMoney() == null ? BigDecimal.ZERO : mawb.getPayMoney())
                    .add(costSum);
            mawbMapper.updatePayMoney(mawb.getId(),add);
            serviceRequest.setPayMoney(costSum);
            serviceRequest.setPayTime(new Date());
            //运单日志的新增
            waybillLog = waybillLogService.getWaybillLog(
                    mawb.getWaybillCode(), 0, SecurityUtils.getNickName(),
                    null, null, null,
                    id, null, 0, null, new Date(),
                    "服务申请,支付"+costSum,
                    serviceItem.getType(), null);
            serviceRequest.setUpdateTime(new Date());
            return requestMapper.updateById(serviceRequest);
        }catch (Exception e){
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" +  "," +
                            "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        }finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int payAndAdd(ServiceRequest request) {
        HttpServletResponse response = ServletUtils.getResponse();
        WaybillLog waybillLog = new WaybillLog();
        try {
            ServiceItem serviceItem = itemMapper.selectById(request.getServiceItemId());
            Long deptId = request.getDeptId();
            List<Long> deptIds = getDeptIds(deptId);
//            Mawb mawb = mawbMapper.selectComeInfo(request.getWaybillCode(), serviceItem.getType(), request.getDeptId());
            Mawb mawb = mawbMapper.selectComeInfoNew(request.getWaybillCode(), serviceItem.getType(), deptIds);
            if (mawb == null) {
                throw new CustomException("无当前运单信息");
            }
            request.setAgent(mawb.getAgentCompany());
            request.setServiceNo(SerialNumberGenerator.generateSerialNumber());
            request.setDeptId(deptId);
            request.setRequestTime(new Date());
            request.setServiceItem(serviceItem.getServiceName());
            request.setUpdateTime(new Date());
            requestMapper.insert(request);
            sendMessage(mawb.getWaybillCode(), request.getServiceItem());
            ServiceExamine examine = new ServiceExamine();
            examine.setServiceId(request.getId());
            examine.setCreateTime(new Date());
            if ("STAGING".equals(request.getStatus())) {
                examine.setType("暂存");
            }
            if ("UNAUDITED".equals(request.getStatus())) {
                examine.setType("申请");
            }
            examine.setUserName(SecurityUtils.getUsername());
            examineMapper.insert(examine);
            //运单日志的新增
            waybillLog = waybillLogService.getWaybillLog(
                    mawb.getWaybillCode(), 0, SecurityUtils.getNickName(),
                    null, null, null,
                    request, null, 0, null, new Date(),
                    "服务申请,服务项目:"+request.getServiceItem()+",申请次数/小时:"+request.getSelectTime(),
                    serviceItem.getType(), null);
            if ("hz".equals(request.getSysType())){
                return hzPay(request.getId());
            }
            return pay(request.getId());
        }catch (Exception e){
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" +  "," +
                            "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        }finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }

    private List<Long> getDeptIds(Long deptId){
        BaseAgent agent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", deptId));
        List<Long> deptIds = new ArrayList<>();
        deptIds.add(deptId);
        if(agent.getDeptIds() != null){
            deptIds.addAll(Arrays.stream(agent.getDeptIds().split(",")).map(Long::parseLong).collect(Collectors.toList()));
        }
        return deptIds;
    }

    @Override
    public int hzPay(Long id) {
        HttpServletResponse response = ServletUtils.getResponse();
        WaybillLog waybillLog = new WaybillLog();
        try {
            ServiceRequest serviceRequest = requestMapper.selectById(id);
            ServiceItem serviceItem = itemMapper.selectById(serviceRequest.getServiceItemId());
            if (serviceItem == null){
                throw new CustomException("无当前服务项目");
            }
            Mawb mawb = mawbMapper.selectComeInfo(serviceRequest.getWaybillCode(),serviceItem.getType(), null);
            BigDecimal costSum = countSum(serviceRequest,serviceItem,mawb);
            if (serviceRequest.getPayMethod() == 0){
                serviceRequest.setPayStatus(3);
            }else {
                serviceRequest.setPayStatus(15);
            }
            BigDecimal add = (mawb.getPayMoney() == null ? BigDecimal.ZERO : mawb.getPayMoney())
                    .add(costSum);
            mawbMapper.updatePayMoney(mawb.getId(),add);
            serviceRequest.setPayMoney(costSum);
            serviceRequest.setPayTime(new Date());
            //运单日志的新增
            waybillLog = waybillLogService.getWaybillLog(
                    mawb.getWaybillCode(), 0, SecurityUtils.getNickName(),
                    null, null, null,
                    id, null, 0, null, new Date(),
                    "服务申请,支付"+costSum,
                    serviceItem.getType(), null);
            serviceRequest.setUpdateTime(new Date());
            return requestMapper.updateById(serviceRequest);
        }catch (Exception e){
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" +  "," +
                            "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        }finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }


    @Override
    public void edit(ServiceRequestBo bo) {
        HttpServletResponse response = ServletUtils.getResponse();
        Long deptId = SecurityUtils.getHighParentId();

        WaybillLog waybillLog = new WaybillLog();
        ServiceRequest oldServiceRequest = requestMapper.selectById(bo.getId());

        if (oldServiceRequest.getDeptId() != null && !Objects.equals(oldServiceRequest.getDeptId(), deptId)) {
            throw new CustomException("代理人不一致, 修改失败");
        }

        if ("SUCCESS".equals(oldServiceRequest.getStatus())) {
            throw new CustomException("已完成服务, 不可修改");
        }

        ServiceItem serviceItem = itemMapper.selectById(bo.getServiceItemId());
        //查询服务项目
        List<Long> deptIds = getDeptIds(deptId);
//        Mawb mawb = mawbMapper.selectComeInfo(oldServiceRequest.getWaybillCode(),serviceItem.getType(), deptId);
        Mawb mawb = mawbMapper.selectComeInfoNew(oldServiceRequest.getWaybillCode(),serviceItem.getType(), deptIds);
        if (mawb == null){
            throw new CustomException("无当前运单信息");
        }
        try {
            ServiceRequest serviceRequest = new ServiceRequest();
            BeanUtils.copyProperties(oldServiceRequest, serviceRequest);
            serviceRequest.setSelectTime(bo.getSelectTime());
            serviceRequest.setApplyRemark(bo.getApplyRemark());
            serviceRequest.setUpdateTime(new Date());
            serviceRequest.setServiceItemId(bo.getServiceItemId());
            serviceRequest.setServiceItem(serviceItem.getServiceName());

            if (SqlHelper.retBool(requestMapper.updateById(serviceRequest))) {
                // 运单日志的新增
                waybillLog = waybillLogService.getWaybillLog(
                        oldServiceRequest.getWaybillCode(), 0, SecurityUtils.getNickName(),
                        null, null, null,
                        bo, null, 0, null, new Date(),
                        "服务申请,服务项目:" + oldServiceRequest.getServiceItem() + ",申请次数/小时:" + bo.getSelectTime(),
                        serviceItem.getType(), null);
            }
            if ("DEP".equals(serviceItem.getType())){
                List<CostDetail> oldCostList = costDetailMapper.selectList(new QueryWrapper<CostDetail>()
                        .eq("type",0)
                        .eq("service_request_id",oldServiceRequest.getId())
                        .eq("total_charge",oldServiceRequest.getPayMoney()));
                // 已支付并且修改了 selectTime
                if (!oldServiceRequest.getPayStatus().equals(0) &&
                        !oldServiceRequest.getSelectTime().equals(bo.getSelectTime())) {
                    //退款
                    serviceRequestRefund(oldServiceRequest, serviceItem, oldCostList,null);

                    //支付
                    if ("hz".equals(oldServiceRequest.getSysType())) {
                        hzPay(oldServiceRequest.getId());
                    } else {
                        pay(oldServiceRequest.getId());
                    }
                }
            }else {
                List<HzArrItem> oldItemList = hzArrItemMapper.selectList(new QueryWrapper<HzArrItem>()
                        .eq("service_id",oldServiceRequest.getId())
                        .eq("is_del",0)
                        .eq("total_charge",oldServiceRequest.getPayMoney()));
                if (!oldServiceRequest.getPayStatus().equals(0) &&
                        !oldServiceRequest.getSelectTime().equals(bo.getSelectTime())) {
                    //退款
                    serviceRequestRefund(oldServiceRequest, serviceItem, null, oldItemList);

                    //支付
                    if ("hz".equals(oldServiceRequest.getSysType())) {
                        hzPay(oldServiceRequest.getId());
                    } else {
                        pay(oldServiceRequest.getId());
                    }
                }
            }
        } catch (Exception e) {
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" + "," +
                            "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        } finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }

    /**
     * 服务申请退款
     *
     * @param oldServiceRequest 修改前的服务申请
     * @param serviceItem
     * @return
     */
    private void serviceRequestRefund(ServiceRequest oldServiceRequest, ServiceItem serviceItem, List<CostDetail> list, List<HzArrItem> itemList) {
        WaybillLog waybillLog = new WaybillLog();
        HttpServletResponse response = ServletUtils.getResponse();
        try {
            if (list != null){
                for (CostDetail detail : list) {
                    detail.setId(null);
                    detail.setType(1);
                    detail.setIsSettle(1);
                    detail.setFlightId(4L);
                    detail.setCreateTime(new Date());
                    costDetailMapper.insert(detail);
                }
            }
            if (itemList != null){
                for (HzArrItem item : itemList) {
                    item.setIsDel(1);
                    hzArrItemMapper.updateById(item);
                }
            }
            Long deptId = oldServiceRequest.getDeptId();
            BigDecimal refundMoney = oldServiceRequest.getPayMoney();
            LambdaQueryWrapper<BaseAgent> lqw = Wrappers.<BaseAgent>lambdaQuery()
                    .eq(BaseAgent::getDeptId, deptId);
            BaseAgent agent = baseAgentMapper.selectOne(lqw);
            if (agent == null || agent.getSettleMethod() != 1) {
                throw new RuntimeException("代理人不匹配");
            }
            BigDecimal balance = agent.getBalance() == null ? new BigDecimal(0) : agent.getBalance();
            BigDecimal added = balance.add(refundMoney);
            agent.setBalance(added);
            baseAgentMapper.updateBaseAgent(agent);
            BaseBalance baseBalance = new BaseBalance();
            baseBalance.setAgentId(agent.getId());
            baseBalance.setBalance(agent.getBalance());
            baseBalance.setType("增加余额");
            baseBalance.setCreateTime(new Date());
            baseBalance.setCreateBy(SecurityUtils.getNickName());
            // 流水号需从银联支付接口获取
            //baseBalance.setSerialNo();
            baseBalance.setTradeMoney(refundMoney);
            baseBalance.setWaybillCode(oldServiceRequest.getWaybillCode());
            baseBalance.setRemark("服务申请退款");
            baseBalanceMapper.insertBaseBalance(baseBalance);

            //运单日志的新增
            waybillLog = waybillLogService.getWaybillLog(
                    oldServiceRequest.getWaybillCode(), 0, SecurityUtils.getNickName(),
                    null, null, null,
                    oldServiceRequest.getId(), null, 0, null, new Date(),
                    "服务申请,退款" + oldServiceRequest.getPayMoney(),
                    serviceItem.getType(), null);
        } catch (Exception e) {
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" + "," +
                            "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        } finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }


    private synchronized BigDecimal countSum(ServiceRequest serviceRequest, ServiceItem serviceItem, Mawb mawb) {
        BigDecimal costSum = BigDecimal.ZERO;
        Long deptId = serviceRequest.getDeptId();
        HzChargeItems hzChargeItem = chargeItemsMapper.selectById(serviceItem.getChargeItemId());
        Integer count = exitCargoMapper.selectCount(new QueryWrapper<HzDepExitCargo>().eq("waybill_code", serviceRequest.getWaybillCode()));
        if (count > 0){
            mawb.setIsExit(1);
        }else {
            mawb.setIsExit(0);
        }
        BaseCargoCode baseCargoCode2 = cargoCodeMapper.selectByCode(mawb.getCargoCode());
        BigDecimal weightRate;
        BigDecimal chargeWeight = mawb.getChargeWeight() == null ? new BigDecimal(0) : mawb.getChargeWeight();
        if (mawb.getWeight() == null || mawb.getWeight().compareTo(new BigDecimal(0)) == 0){
            weightRate = new BigDecimal(0);
        }else {
            BigDecimal bigDecimal = chargeWeight.divide(mawb.getWeight(),5, RoundingMode.DOWN).multiply(mawb.getWeight());
            weightRate = bigDecimal.setScale(0, RoundingMode.CEILING);
        }
        List<HzChargeIrRelation> relations = relationMapper.selectList(new QueryWrapper<HzChargeIrRelation>().eq("item_id", hzChargeItem.getId()).eq("is_del",0));
        int maxMatchCount = 0;
        List<HzChargeIrRelation> ruleList = new ArrayList<>();
        for (HzChargeIrRelation hzChargeRule : relations) {
            if (!hzChargeRule.getIsSouth().equals(mawb.getIsSouth())){
                continue;
            }
            if (!hzChargeRule.getIsExit().equals(mawb.getIsExit())){
                continue;
            }
            if (StringUtils.isNotEmpty(hzChargeRule.getNoCharge()) && !hzChargeRule.getNoCharge().contains(deptId.toString())){
                continue;
            }
            if (StringUtils.isNotEmpty(hzChargeRule.getPrefix()) && !hzChargeRule.getPrefix().contains(serviceRequest.getWaybillCode().substring(4,7))){
                continue;
            }
            if (StringUtils.isNotEmpty(hzChargeRule.getCategory()) && !hzChargeRule.getCategory().contains(baseCargoCode2.getCategoryCode())){
                continue;
            }
            if (!hzChargeRule.getCrossAir().equals(mawb.getCrossAir())){
                continue;
            }
            int matchCount = 0;
            // 根据判断货品代码
            int cargoMatchCount = isCargoCodeMatch(hzChargeRule, mawb.getCargoCode());
            if (cargoMatchCount >= 0) {
                matchCount += cargoMatchCount;
            }
            if (matchCount > 0) {
                if (matchCount > maxMatchCount) {
                    maxMatchCount = matchCount;
                    ruleList.clear();
                    ruleList.add(hzChargeRule);
                } else if (matchCount == maxMatchCount) {
                    ruleList.add(hzChargeRule);
                }
            }
        }
        if ("DEP".equals(serviceItem.getType())){
            if (!CollectionUtils.isEmpty(ruleList)){
                HzChargeIrRelation relation = ruleList.stream().max(Comparator.comparing(HzChargeIrRelation::getPriority)).orElse(null);
                if (relation != null) {
                    HzChargeRule rule1 = ruleMapper.selectById(relation.getRuleId());
                    List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>().eq("ir_id", relation.getId()));
                    if (!CollectionUtils.isEmpty(itemRules)) {
                        if (!"ColdStorageBillingRule.class".equals(rule1.getClassName())) {
                            CostDetail detail = new CostDetail();
                            detail.setWaybillCode(mawb.getWaybillCode());
                            detail.setIrId(relation.getId());
                            detail.setUnit(serviceRequest.getSelectTime());
                            detail.setDaysInStorage(serviceRequest.getSelectTime().doubleValue());
                            BillingRule rule = BillingRuleFactory.createRule(rule1.getClassName());
                            BillRuleVo vo1 = rule.calculateFee(itemRules, weightRate, mawb.getQuantity(), detail);
                            BigDecimal totalCharge = BigDecimalRoundUtils.bigDecimalRound(hzChargeItem.getRoundRule(), vo1.getTotalCharge());
                            detail.setTotalCharge(totalCharge);
                            detail.setDeptId(deptId);
                            detail.setQuantity(vo1.getQuantity());
                            detail.setSettleDepWeight(new BigDecimal(serviceRequest.getSelectTime()));
                            detail.setRate(vo1.getRate());
                            detail.setIsSettle(1);
                            detail.setType(0);
                            detail.setServiceType(1);
                            detail.setServiceRequestId(serviceRequest.getId());
                            costSum = costSum.add(totalCharge);
                            costDetailMapper.insert(detail);
                        }
                    }
                }
            }
        }else {
            HzArrRecordOrder recordOrder = recordOrderMapper.selectOne(new QueryWrapper<HzArrRecordOrder>()
                    .eq("waybill_code", mawb.getWaybillCode())
                    .orderByDesc("order_time")
                    .last("limit 1"));
            HzArrTally hzArrTally = hzArrTallyMapper.selectOne(new QueryWrapper<HzArrTally>()
                    .eq("waybill_code", mawb.getWaybillCode())
                    .eq("status", "lh_comp")
                    .orderByDesc("tally_time").last("limit 1"));
            if (recordOrder == null){
                throw new CustomException("无当前录单信息");
            }
            BaseAgent agent = baseAgentMapper.selectByDeptId(deptId.toString());
            int payMethod;
            if (agent != null){
                if (agent.getSettleMethod() == 1) {
                    payMethod = 2;
                } else if (agent.getSettleMethod() == 0){
                    payMethod = 3;
                }else {
                    if (agent.getPayMethod() == 0){
                        payMethod = 1;
                    }else {
                        payMethod = 0;
                    }
                }
            }else {
                payMethod = 0;
            }
            if (!CollectionUtils.isEmpty(ruleList)) {
                HzChargeIrRelation relation = ruleList.stream().max(Comparator.comparing(HzChargeIrRelation::getPriority)).orElse(null);
                if (relation != null) {
                    HzChargeRule rule1 = ruleMapper.selectById(relation.getRuleId());
                    List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>()
                            .eq("ir_id", relation.getId()));
                    if (!CollectionUtils.isEmpty(itemRules)) {
                        if (!"ColdStorageBillingRule.class".equals(rule1.getClassName())) {
                            HzArrItem item = new HzArrItem();
                            item.setWaybillCode(mawb.getWaybillCode());
                            item.setIrId(relation.getId());
                            item.setUnit(serviceRequest.getSelectTime());
                            item.setSmallItem(1);
                            item.setLargeItem(1);
                            item.setSuperLargeItem(1);
                            item.setDaysInStorage(1.0);
                            item.setServiceType(1);
                            item.setServiceId(serviceRequest.getId());
                            item.setPayMethod(payMethod);
                            if (hzArrTally != null){
                                item.setTallyId(hzArrTally.getId());
                            }
                            item.setOrderId(recordOrder.getId());
                            item.setPointTime(LocalTime.now().withSecond(0).withNano(0));
                            BillingRule rule = BillingRuleFactory.createRule(rule1.getClassName());
                            BillRuleVo vo1 = rule.calculateFee(itemRules, weightRate, mawb.getQuantity(), item);
                            BigDecimal totalCharge = BigDecimalRoundUtils.bigDecimalRound(hzChargeItem.getRoundRule(), vo1.getTotalCharge());
                            item.setTotalCharge(totalCharge);
                            costSum = costSum.add(totalCharge);
                            hzArrItemMapper.insert(item);
                        }
                    }
                }
            }
        }
        return costSum;
    }

    @Async
    public void sendMessageForExamine(String waybillCode,String examineStatus,String serviceItem) {
        String message = "    运单"+waybillCode+"服务审核  \n" +
                "运单"+ waybillCode +"申请的"+ serviceItem +"审核" +
                ("PASS".equals(examineStatus)?"通过":"未通过")
                + "，请及时处理";
        SocketMessageVo vo = new SocketMessageVo();
        vo.setMessage(message);
        vo.setType(3);
//        vo.setDeptId(sysDeptMapper.selectDeptIdByDeptName());
        AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                .eq("waybill_code", waybillCode)
                .last("limit 1"));
        vo.setDeptId(sysDeptMapper.selectDeptIdByDeptName(airWaybill.getAgentCompany()));

        Message mes = new Message();
        mes.setContent(message);
        mes.setCreateTime(new Date());
        mes.setIsHandle(0);
        mes.setPostId(SecurityUtils.getDeptId());
        mes.setTitle("服务审核提醒");
        messageMapper.insert(mes);
        waybillMessageProducer.send(vo);
    }

    private int isCargoCodeMatch(HzChargeIrRelation hzChargeRule, String waybillCargoCode) {
        if (StringUtils.isEmpty(hzChargeRule.getCargoName())) {
            return 1;
        }else if (StringUtils.isEmpty(hzChargeRule.getCategory())){
            if (hzChargeRule.getCargoName().contains(waybillCargoCode)){
                return 1;
            }
            return 0;
        }
        List<IrRelationVo> relationVos = Arrays.stream(hzChargeRule.getCargoName().split(","))
                .map(cargoCodeMapper::selectIrByName)
                .collect(Collectors.toList());

        List<IrRelationVo> cargoNames = Arrays.stream(hzChargeRule.getCategory().split(","))
                .flatMap(category -> cargoCodeMapper.selectListByCategory(Collections.singletonList(category)).stream())
                .collect(Collectors.toList());

        Map<String, List<IrRelationVo>> relationVosMap = relationVos.stream()
                .collect(Collectors.groupingBy(IrRelationVo::getCategoryCode));

        List<IrRelationVo> combinedList = new ArrayList<>();
        Set<String> addedCodes = new HashSet<>();
        for (IrRelationVo vo : cargoNames) {
            String code = vo.getCategoryCode();
            if (relationVosMap.containsKey(code) && !addedCodes.contains(code)) {
                combinedList.addAll(relationVosMap.get(code));
                addedCodes.add(code);
            } else if (!addedCodes.contains(code)) {
                combinedList.add(vo);
            }
        }
        List<String> collect1 = combinedList.stream().map(IrRelationVo::getCode).collect(Collectors.toList());
        if (collect1.stream().anyMatch(cargoCode -> cargoCode.equals(waybillCargoCode))) {
            String category = hzChargeRule.getCategory();
            StringBuilder stringBuffer = new StringBuilder();
            if(category != null){
                String[] split = category.split(",");
                for (String categoryCode:split) {
                    List<BaseCargoCode> cargoCodeList = cargoCodeMapper.selectList(new QueryWrapper<>(new BaseCargoCode())
                            .eq("category_code", categoryCode)
                            .eq("is_del",0));
                    if(cargoCodeList!=null && cargoCodeList.size() > 0){
                        for (BaseCargoCode e:cargoCodeList) {
                            stringBuffer.append(e.getCode());
                        }
                    }
                }
            }else{
                return 1;
            }
            if (stringBuffer.toString().contains(waybillCargoCode)) {
                return 2;
            } else {
                return 1;
            }
        }
        return 0;
    }

    public BillRuleVo countCost(ItemDetail detail, BigDecimal weight, Integer quantity) {
        HzChargeIrRelation relation = relationMapper.selectById(detail.getIrId());
        if (relation == null){
            BillRuleVo vo = new BillRuleVo();
            vo.setTotalCharge(new BigDecimal(0));
            return vo;
        }
        HzChargeItems hzChargeItems = chargeItemsMapper.selectById(relation.getItemId());
        HzChargeRule hzChargeRule = ruleMapper.selectById(relation.getRuleId());
        List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>().eq("ir_id", detail.getIrId()));
        if (CollectionUtils.isEmpty(itemRules)){
            BillRuleVo vo = new BillRuleVo();
            vo.setTotalCharge(new BigDecimal(0));
            return vo;
        }
        BillingRule rule = BillingRuleFactory.createRule(hzChargeRule.getClassName());
        BillRuleVo ruleVo = rule.calculateFee(itemRules, weight, quantity, detail);
        BigDecimal totalCharge = BigDecimalRoundUtils.bigDecimalRound(hzChargeItems.getRoundRule(), ruleVo.getTotalCharge());
        ruleVo.setTotalCharge(totalCharge);
        return ruleVo;
    }
}
