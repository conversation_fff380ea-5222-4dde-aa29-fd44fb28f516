package com.gzairports.common.basedata.service;


import com.gzairports.common.basedata.domain.BaseCarrier;
import com.gzairports.common.basedata.domain.query.CarrierQuery;

import java.util.List;

/**
 * 承运人管理Service接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
public interface ICarrierService {

    /**
     * 查询承运人列表
     *
     * @param query 查询参数
     * @return 承运人列表
     */
    List<BaseCarrier> selectCarrierList(CarrierQuery query);

    /**
     * 新增承运人
     *
     * @param carrier 承运人
     * @return 结果
     */
    int addCarrier(BaseCarrier carrier);

    /**
     * 修改承运人
     *
     * @param carrier 承运人
     * @return 结果
     */
    int editCarrier(BaseCarrier carrier);

    /**
     * 删除承运人
     *
     * @param ids 承运人id集合
     * @return 结果
     */
    int delCarrier(Long[] ids);

    /**
     * 导入承运人
     * @param carriers 承运人列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    String importCarrier(List<BaseCarrier> carriers, boolean updateSupport);

    /**
     * 根据航司查询logo
     * @param carrier1 航司
     * @return logo地址
     */
    String selectLogoByCode(String carrier1);
}
