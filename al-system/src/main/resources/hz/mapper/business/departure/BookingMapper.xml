<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.departure.mapper.BookingMapper">

    <select id="selectListByQuery" resultType="com.gzairports.common.business.departure.domain.vo.BookingVo">
        select id, flight_date, flight_no, waybill_code, plan_takeoff_time, craft_type, craft_no,
               agent_code, quantity, cargo_name, special_code, weight, charge_weight, volume, booking_no,
               ensure_level, berth_level, flight_limit, status, remark from wl_dep_booking
        <where>
            <if test="startTime != null">
                and flight_date <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and flight_date <![CDATA[<=]]> #{endTime}
            </if>
            <if test="flightNo != null and flightNo != ''">
                and flight_no like '%${flightNo}%'
            </if>
            <if test="waybillCode != null and waybillCode != ''">
                and waybill_code like '%${waybillCode}%'
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="deptId != null">
                and dept_id = #{deptId}
            </if>
        </where>
    </select>
</mapper>