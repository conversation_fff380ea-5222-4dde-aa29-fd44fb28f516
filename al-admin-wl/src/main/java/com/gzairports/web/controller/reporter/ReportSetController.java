package com.gzairports.web.controller.reporter;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.wl.reporter.domain.vo.ReportSetVo;
import com.gzairports.wl.reporter.service.IReportSetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2025-02-18 15:07
 **/
@RestController
@RequestMapping("/wl/reportSet")
@Api(tags = "报表设置")
public class ReportSetController extends BaseController {

    @Autowired
    private IReportSetService reportService;

    /**
     * 报表列表查询
     */
    @GetMapping("/list/{type}")
    @ApiOperation(value = "报表列表查询")
    public AjaxResult list(@PathVariable("type") String type){
        return AjaxResult.success(reportService.selectList(type));
    }

    /**
     * 可选字段查询
     */
    @GetMapping("/fieldList/{type}")
    @ApiOperation(value = "可选字段查询")
    public AjaxResult fieldList(@PathVariable("type") String type){
        return AjaxResult.success(reportService.fieldList(type));
    }

    /**
     * 新增报表设置
     */
    @PostMapping("/addSet")
    @ApiOperation(value = "新增报表设置")
    public AjaxResult addSet(@RequestBody ReportSetVo vo){
        return AjaxResult.success(reportService.addSet(vo));
    }

    /**
     * 编辑报表设置
     */
    @PostMapping("/editSet")
    @ApiOperation(value = "编辑报表设置")
    public AjaxResult editSet(@RequestBody ReportSetVo vo){
        return AjaxResult.success(reportService.editSet(vo));
    }

    /**
     * 删除报表设置
     */
    @GetMapping("/delSet/{id}")
    @ApiOperation(value = "删除报表设置")
    public AjaxResult delSet(@PathVariable("id") Long id){
        return toAjax(reportService.delSet(id));
    }

    /**
     * 查看详情
     */
    @GetMapping("/getInfo/{id}")
    @ApiOperation(value = "查看详情")
    public AjaxResult getInfo(@PathVariable("id") Long id){
        return AjaxResult.success(reportService.getInfo(id));
    }
}
