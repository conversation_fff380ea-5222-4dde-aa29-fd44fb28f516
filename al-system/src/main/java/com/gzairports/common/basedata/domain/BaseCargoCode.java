package com.gzairports.common.basedata.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.util.Date;


/**
 * 货站货品代码
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Data
@TableName("base_cargo_code")
public class BaseCargoCode {

    /** 主键id */
    private Long id;

    /** 货品代码 */
    @Excel(name = "货品代码")
    private String code;

    /** 货品大类 */
    @Excel(name = "货品大类")
    private String categoryCode;

    /** 中文名称 */
    @Excel(name = "中文名称")
    private String chineseName;

    /** 英文名称 */
    @Excel(name = "英文名称")
    private String englishName;

    /** 是否特货 */
    @Excel(name = "是否特货")
    private Integer isSpecial;

    /** 适用进港 */
    @Excel(name = "适用进港")
    private Integer isArrival;

    /** 适用出港 */
    @Excel(name = "适用出港")
    private Integer isOut;

    /** 适用国内 */
    @Excel(name = "适用国内")
    private Integer domestic;

    /** 适用国际 */
    @Excel(name = "适用国际")
    private Integer international;

    /** 特货代码1 */
    @Excel(name = "特货代码1")
    private String specialCargoCode1;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;
}
