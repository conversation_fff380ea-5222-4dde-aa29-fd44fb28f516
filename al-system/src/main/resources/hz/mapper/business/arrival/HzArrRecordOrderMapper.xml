<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.arrival.mapper.HzArrRecordOrderMapper">
    <update id="updateExamine">
        update hz_arr_record_order set is_examine = 1 where id = #{orderId}
    </update>
    <delete id="cancelExamine">
        update hz_arr_record_order set is_examine = 0 where id = #{orderId}
    </delete>
    <select id="waybillList" resultType="com.gzairports.hz.business.arrival.domain.vo.FlightFileWaybillVo">
        select haro.id as orderId, aaw.id, aaw.waybill_code, aaw.file_arr, aaw.replenish_bill, aaw.quantity, aaw.weight,
               aaw.consign, aaw.special_cargo_code1, aaw.cargo_name, haro.cabin_pieces as cabinQuantity, haro.cabin_weight as cabinWeight,
               haro.leg_id, aaw.remark, aaw.transfer_bill
        from hz_arr_record_order haro
        left join all_air_waybill aaw on aaw.waybill_code = haro.waybill_code
        where haro.leg_id = #{legId} and aaw.type = 'ARR' and aaw.is_del = 0
        <if test="waybillIds != null and waybillIds.size() > 0">
            and aaw.id in
            <foreach collection="waybillIds" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="status != null and status !=''">
            and haro.status = #{status}
        </if>
    </select>

    <select id="getTallyInfo" resultType="com.gzairports.hz.business.arrival.domain.vo.TallyWaybillVo">
        select haro.id as tallyId, haro.waybill_code, CONCAT(afi.air_ways,afi.flight_no) as flightNo, afi.flight_id, aaw.quantity, aaw.weight, aaw.special_cargo_code1,
               haro.cabin_pieces as tallyQuantity, haro.cabin_weight as tallyWeight, haro.cabin_pieces as cabinQuantity, afi.exec_date,
               haro.cabin_weight, aaw.shipper as deptName
        from hz_arr_record_order haro
                 left join all_air_waybill aaw on aaw.waybill_code = haro.waybill_code
                 left join hz_flight_load hfl on hfl.id = haro.leg_id
                 left join all_flight_info afi on hfl.flight_id = afi.flight_id
        where aaw.type = 'ARR' and haro.status = 'cd' and haro.leg_id = #{legId} and haro.waybill_code = #{waybillCode}
    </select>
    <select id="selectQueryList" resultType="com.gzairports.hz.business.arrival.domain.vo.WaybillQueryVo">
        select  aaw.id as waybillId, aaw.waybill_code, aaw.replenish_bill, aaw.file_arr, aaw.prioritize, aaw.transfer_bill, aaw.source_port,
                aaw.carrier1, aaw.des1, aaw.carrier2, aaw.des2, aaw.carrier3, aaw.des3, aaw.des_port, aaw.quantity, aaw.weight, aaw.charge_weight,
                haro.cabin_pieces as cabinQuantity, haro.cabin_weight, aaw.shipper, aaw.consign, aaw.consign_id_car, aaw.consign_phone, aaw.cargo_name,
                aaw.special_cargo_code1, aaw.cold_store,
                aaw.customs_supervision, aaw.is_transfer, aaw.transfer_no, aaw.arr_pay, aaw.cost_sum, aaw.remark
        from hz_arr_record_order haro
            left join all_air_waybill aaw on aaw.waybill_code = haro.waybill_code
            left join hz_flight_load hfl on hfl.id = haro.leg_id
            left join all_flight_info afi on hfl.flight_id = afi.flight_id
        where aaw.is_del = 0
        <if test="waybillCode != null and waybillCode !=''">
            and aaw.waybill_code = #{waybillCode}
        </if>
        <if test="domint != null and domint != ''">
            and aaw.domint = #{domint}
        </if>
        <if test="type == 'ARR'">
            and aaw.type = #{type}
        </if>
        <if test="type == 'TRANSFER'">
            and aaw.transfer_bill = 1
        </if>
        <if test="sourcePort != null and sourcePort !=''">
            and aaw.source_port = #{sourcePort}
        </if>
        <if test="desPort != null and desPort !=''">
            and aaw.des_port = #{desPort}
        </if>
        <if test="shipper != null and shipper !=''">
            and aaw.shipper = #{shipper}
        </if>
        <if test="consign != null and consign !=''">
            and aaw.consign = #{consign}
        </if>
        <if test="agentCode != null and agentCode !=''">
            and aaw.agent_code = #{agentCode}
        </if>
        <if test="specialCode != null and specialCode !=''">
            and aaw.special_cargo_code = #{specialCode}
        </if>
        <if test="carrier != null and carrier !=''">
            and aaw.carrier = #{carrier}
        </if>
        <if test="flightNo != null and flightNo !=''">
            and CONCAT(afi.air_ways,afi.flight_no) = #{flightNo}
        </if>
        <if test="cargoCode != null and cargoCode !=''">
            and aaw.cargo_code = #{cargoCode}
        </if>
        <if test="startTime != null">
            and haro.order_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            and haro.order_time <![CDATA[<=]]> #{endTime}
        </if>
    </select>
    <select id="selectArrFlights" resultType="com.gzairports.hz.business.arrival.domain.vo.ArrFlightVo">
        select haro.order_time, CONCAT(afi.air_ways,afi.flight_no) as flightNo, afi.exec_date
        from hz_arr_record_order haro
            left join hz_flight_load hfl on hfl.id = haro.leg_id
            left join all_flight_info afi on hfl.flight_id = afi.flight_id
        where haro.waybill_code = #{waybillCode}
    </select>
    <select id="selectNodeList" resultType="com.gzairports.hz.business.arrival.domain.vo.NodeWaybillVo">
        select hat.id as tallyId, haro.waybill_code, CONCAT(afi.air_ways,afi.flight_no) as flightNo, afi.exec_date, haro.order_time, aaw.consign, aaw.quantity,
               aaw.weight, afi.terminal_alteratel_and_in_time, hat.tally_time, aaw.write_time
        from hz_arr_record_order haro
            left join all_air_waybill aaw on aaw.waybill_code = haro.waybill_code
            left join hz_arr_tally hat on haro.id = hat.record_order_id
            left join hz_flight_load hfl on hfl.id = haro.leg_id
            left join all_flight_info afi on hfl.flight_id = afi.flight_id
        where aaw.is_del = 0 and aaw.type = 'ARR'
        <if test="query.consign != null and query.consign !=''">
            and aaw.consign = #{query.consign}
        </if>
        <if test="query.waybillCode != null and query.waybillCode !=''">
            and haro.waybill_code like '%${query.waybillCode}%'
        </if>
        <if test="query.flightNo != null and query.flightNo !=''">
            and CONCAT(afi.air_ways,afi.flight_no) like '%${query.flightNo}%'
        </if>
        <if test="query.startTime != null">
            and haro.order_time <![CDATA[>=]]> #{query.startTime}
        </if>
        <if test="query.endTime != null">
            and haro.order_time <![CDATA[<=]]> #{query.endTime}
        </if>
        order by haro.order_time
    </select>
    <select id="selectListByCode" resultType="java.lang.String">
        select CONCAT(afi.air_ways,afi.flight_no) as flightNo
        from hz_arr_record_order haro
           left join hz_flight_load hfl on hfl.id = haro.leg_id
           left join all_flight_info afi on hfl.flight_id = afi.flight_id
        where haro.waybill_code = #{waybillCode}
    </select>
    <select id="selectWaybillList" resultType="com.gzairports.hz.business.arrival.domain.vo.AppWaybillListVo">
        select haro.waybill_code, haro.cabin_pieces as pieces, haro.cabin_weight as tallyWeight, haro.cabin_pieces, haro.cabin_weight,
               aaw.quantity, aaw.weight, haro.leg_id
        from hz_arr_record_order haro
                 left join hz_flight_load hfl on hfl.id = haro.leg_id
                 left join all_air_waybill aaw on haro.waybill_code = aaw.waybill_code
        where haro.status = 'cd' and haro.leg_id in
        <foreach collection="legIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="getWaybillInfo" resultType="com.gzairports.hz.business.arrival.domain.vo.EnterWaybillVo">
        select waybill_code, source_port, carrier1, des1, carrier2, des2, carrier3, des3, des_port,  quantity, weight, charge_weight, cabin_pieces as cabinQuantity,
               cabin_weight, shipper, consign, consign_id_car, consign_phone, cargo_code, cargo_name, category_name, special_cargo_code1, replenish_bill, file_arr, prioritize,
               transfer_bill, flight_no1, flight_date1, cold_store, customs_supervision, is_transfer, transfer_no, arr_pay, cost_sum, remark
        from hz_arr_record_order haro left join all_air_waybill aaw on aaw.waybill_code = haro.waybill_code where aaw.is_del = 0 and aaw.type = 'ARR' and haro.id = #{orderId}
    </select>
    <select id="selectId" resultType="com.gzairports.hz.business.arrival.domain.HzArrRecordOrder">
        select haro.id, haro.order_time
        from hz_arr_record_order haro
            left join hz_flight_load hfl on hfl.id = haro.leg_id
        where hfl.leg = #{desPort} and haro.waybill_code = #{waybillCode} order by haro.order_time desc limit 1
    </select>
    <select id="selectFlightList" resultType="java.lang.String">

    </select>
    <select id="selectFlightById" resultType="com.gzairports.common.business.departure.domain.vo.FlightVo">
        select CONCAT(afi.air_ways,afi.flight_no) as flightNo, afi.exec_date, haro.order_time
        from hz_arr_record_order haro
              left join hz_flight_load hfl on hfl.id = haro.leg_id
              left join all_flight_info afi on hfl.flight_id = afi.flight_id where haro.id = #{orderId}
    </select>
    <select id="selectReportNodeList"
            resultType="com.gzairports.common.business.reporter.domain.ReportNodeQuery">
        select haro.id,  hat.id as tallyId, aaw.waybill_code, CONCAT(afi.air_ways,afi.flight_no) as flightNo, afi.exec_date, afi.terminal_alteratel_and_in_time,
               hat.tally_time, haro.order_time, aaw.write_time as handoverTime
        from hz_arr_record_order haro
                 left join all_air_waybill aaw on aaw.waybill_code = haro.waybill_code
                 left join hz_arr_tally hat on haro.id = hat.record_order_id
                 left join hz_flight_load hfl on hfl.id = haro.leg_id
                 left join all_flight_info afi on hfl.flight_id = afi.flight_id
        where aaw.is_del = 0 and aaw.type = 'ARR'
        <if test="lastSyncTime != null">
            and aaw.update_time > #{lastSyncTime}
        </if>
        <if test="dateNow != null">
            and aaw.update_time <![CDATA[<=]]> #{dateNow}
        </if>
    </select>
    <select id="selectByTallyId" resultType="com.gzairports.hz.business.arrival.domain.HzArrRecordOrder">
        select cabin_pieces, cabin_weight
        from hz_arr_record_order where id  = #{tallyId}
    </select>
    <select id="selectDetailList" resultType="com.gzairports.hz.business.cable.domain.vo.ConsignmentDetail">
        select aaw.waybill_type as awbType, aaw.source_port as depAirport, aaw.des_port as desAirport, aaw.size as dimensions, aaw.cargo_name as goodsName,
                aaw.waybill_code as mawbNo, aaw.quantity as totalPieces, aaw.volume, aaw.weight, haro.cabin_pieces as pieces, haro.cabin_weight as pieces,
                CONCAT_WS(',',aaw.special_cargo_code1, aaw.special_cargo_code2, aaw.special_cargo_code3, aaw.other_special_cargo_code) as shcStr,
                CONCAT_WS(' ',aaw.consign, aaw.consign_phone) as osi
        from hz_arr_record_order haro
                 left join all_air_waybill aaw on aaw.waybill_code = haro.waybill_code
        where aaw.is_del = 0 and aaw.type = 'ARR'
          <if test="legIds != null and legIds.size() > 0">
              and haro.leg_id in
              <foreach collection="legIds" item="id" index="index" open="(" close=")" separator=",">
                  #{id}
              </foreach>
          </if>
    </select>
    <select id="selectCountByLegId" resultType="java.lang.Integer">
        select count(1) from hz_arr_record_order where waybill_code = #{waybillCode} and leg_id = #{legId}
    </select>
    <select id="waybillListQuery"
            resultType="com.gzairports.hz.business.arrival.domain.vo.FlightFileWaybillVo">
        select haro.id as orderId, aaw.id, aaw.waybill_code, aaw.file_arr, aaw.replenish_bill, aaw.quantity, aaw.weight,
        aaw.consign, aaw.special_cargo_code1, aaw.cargo_name, haro.cabin_pieces as cabinQuantity, haro.cabin_weight as cabinWeight,
        haro.leg_id, aaw.remark, aaw.transfer_bill, haro.is_examine
        from hz_arr_record_order haro
        left join all_air_waybill aaw on aaw.waybill_code = haro.waybill_code
        where aaw.type = 'ARR' and aaw.is_del = 0
        <if test="legId != null">
            and haro.leg_id = #{legId}
        </if>
        <if test="isExamine != null">
            and haro.is_examine = #{isExamine}
        </if>
    </select>
</mapper>