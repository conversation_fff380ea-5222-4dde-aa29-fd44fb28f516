package com.gzairports.hz.business.arrival.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.hz.business.arrival.domain.HzArrQuickDelivery;
import com.gzairports.hz.business.arrival.domain.query.QuickDeliveryQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 快速交付管理Mapper接口
 *
 * <AUTHOR>
 * @date 2023-07-24
 */
@Mapper
public interface QuickDeliveryMapper extends BaseMapper<HzArrQuickDelivery> {

    /**
     * 查询快速交付列表
     * @param query 查询条件
     * @return 结果
     */
    List<HzArrQuickDelivery> selectListByQuery(QuickDeliveryQuery query);

    /**
     * 查询有效期内的快速交付列表
     * @param
     * @return 结果
     */
    List<HzArrQuickDelivery> selectListValidity();
}
