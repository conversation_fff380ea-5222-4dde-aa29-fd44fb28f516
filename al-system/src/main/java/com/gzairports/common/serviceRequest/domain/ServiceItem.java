package com.gzairports.common.serviceRequest.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 服务项目管理表
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@Data
@TableName("all_service_item")
public class ServiceItem {

    /** 主键id */
    private Long id;

    /** 对应收费项目id */
    private Long chargeItemId;

    /** 对应收费项目名称 */
    @TableField(exist = false)
    private String chargeItem;

    /** 服务名称 */
    private String serviceName;

    /** 进出港类型 */
    private String type;

    /** 操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 操作人员 */
    private String createBy;

    /** 是否删除 0 否 1 是 */
    private Integer isDel;
}
