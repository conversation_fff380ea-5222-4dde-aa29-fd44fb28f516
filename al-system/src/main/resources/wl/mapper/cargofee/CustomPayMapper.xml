<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.wl.cargofee.mapper.CustomPayMapper">
    <select id="selectListByQuery" resultType="com.gzairports.wl.cargofee.domain.CustomPay">
        select id, money, payment_time, union_pay, type, status, waybill_code, serial_no, remark from wl_custom_pay
        <where>
            and dept_id = #{deptId}
            <if test="startTime != null">
                and payment_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and payment_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="serialNo != null and serialNo != ''">
                and serial_no like '%${serialNo}%'
            </if>
            <if test="waybillCode != null and waybillCode != ''">
                and waybill_code like '%${waybillCode}%'
            </if>
        </where>
    </select>
</mapper>