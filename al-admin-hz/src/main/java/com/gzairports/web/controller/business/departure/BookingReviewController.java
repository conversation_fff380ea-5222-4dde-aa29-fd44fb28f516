package com.gzairports.web.controller.business.departure;

import com.gzairports.common.business.departure.domain.BookingRecord;
import com.gzairports.common.business.departure.domain.query.BookingQuery;
import com.gzairports.common.business.departure.domain.vo.BookingVo;
import com.gzairports.common.business.departure.service.IBookingService;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 订舱审核Controller
 *
 * <AUTHOR>
 * @date 2024-07-12
 */
@RestController
@RequestMapping("/dep/bookingReview")
@Api(tags = "订舱审核")
public class BookingReviewController extends BaseController {

    @Autowired
    private IBookingService bookingService;

    /**
     * 查询订舱列表
     */
//    @PreAuthorize("@ss.hasPermi('dep:bookingReview:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询订舱列表")
    public TableDataInfo list(BookingQuery query){
        startPage();
        List<BookingVo> list = bookingService.selectList(query);
        return getDataTable(list);
    }

    /**
     * 导出订舱列表
     */
    @PreAuthorize("@ss.hasPermi('dep:bookingReview:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出订舱列表")
    public void export(HttpServletResponse response, BookingQuery query)
    {
        List<BookingVo> list = bookingService.selectList(query);
        for (BookingVo bookingVo:list) {
            String status = bookingVo.getStatus();
            if ("0".equals(status)) {
                bookingVo.setStatus("提交");
            } else if ("1".equals(status)) {
                bookingVo.setStatus("审核通过");
            } else if ("2".equals(status)) {
                bookingVo.setStatus("审核失败");
            }
        }
        ExcelUtil<BookingVo> util = new ExcelUtil<BookingVo>(BookingVo.class);
        util.exportExcel(response, list, "订舱");
    }

    /**
     * 审核
     */
    @PreAuthorize("@ss.hasPermi('dep:bookingReview:review')")
    @PostMapping("/review")
    @ApiOperation(value = "审核")
    public AjaxResult review(@RequestBody BookingRecord record)
    {
        return toAjax(bookingService.bookingReview(record));
    }

    /**
     * 批量审核
     */
    @PreAuthorize("@ss.hasPermi('dep:bookingReview:reviewBatch')")
    @PostMapping("/reviewBatch")
    @ApiOperation(value = "批量审核")
    public AjaxResult reviewBatch(@RequestBody BookingRecord record)
    {
        return toAjax(bookingService.reviewBatch(record));
    }

    /**
     * 审核详情
     */
    @PreAuthorize("@ss.hasPermi('dep:bookingReview:getInfo')")
    @GetMapping("/getInfo/{id}")
    @ApiOperation(value = "审核详情")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(bookingService.getInfo(id));
    }
}
