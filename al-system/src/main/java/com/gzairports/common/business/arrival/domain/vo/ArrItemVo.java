package com.gzairports.common.business.arrival.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 收费项目
 * <AUTHOR>
 * @date 2024-09-11
 */
@Data
public class ArrItemVo {

    /** 航班id */
    private Long flightId;

    private Long pickUpId;

    private String waybillCode;

    /** 费用名称 */
    private String chargeName;

    /** 收费项目简称 */
    private String chargeAbb;

    /** 总费用 */
    private BigDecimal totalCharge;

    /** 总费用 */
    private BigDecimal editCharge;
}
