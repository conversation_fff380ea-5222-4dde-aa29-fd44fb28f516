package com.gzairports.common.basedata.service.impl;

import com.gzairports.common.basedata.domain.BaseCityCode;
import com.gzairports.common.basedata.domain.query.CityCodeQuery;
import com.gzairports.common.basedata.mapper.CityCodeMapper;
import com.gzairports.common.basedata.service.ICityCodeService;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.exception.ServiceException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.bean.BeanValidators;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.Validator;
import java.util.List;

/**
 * 城市代码Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Service
public class CityCodeServiceImpl implements ICityCodeService {

    private static final Logger log = LoggerFactory.getLogger(CityCodeServiceImpl.class);

    @Autowired
    private CityCodeMapper cityCodeMapper;

    @Autowired
    private Validator validator;

    /**
     * 查询城市代码列表
     *
     * @param query 查询参数
     * @return 城市代码列表
     */
    @Override
    public List<BaseCityCode> selectCityCodeList(CityCodeQuery query) {
        return cityCodeMapper.selectCityCodeList(query);
    }

    /**
     * 导入城市代码
     *
     * @param cityCodes 城市代码数据列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importCityCode(List<BaseCityCode> cityCodes, boolean updateSupport) {
        if (StringUtils.isNull(cityCodes) || cityCodes.size() == 0)
        {
            throw new ServiceException("导入城市代码数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (BaseCityCode baseCityCode : cityCodes) {
            try {
                // 验证是否存在相同的城市代码
                BaseCityCode cityCode = cityCodeMapper.selectCityByCode(baseCityCode.getCode());
                if (StringUtils.isNull(cityCode)) {
                    BeanValidators.validateWithException(validator, baseCityCode);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    baseCityCode.setCreateBy(loginUser.getUsername());
                    baseCityCode.setCreateTime(DateUtils.getNowDate());
                    cityCodeMapper.insertCityCode(baseCityCode);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、代码 " + baseCityCode.getCode() + " 导入成功");
                }
                else if (updateSupport) {
                    BeanValidators.validateWithException(validator, baseCityCode);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    baseCityCode.setId(cityCode.getId());
                    baseCityCode.setUpdateBy(loginUser.getUsername());
                    baseCityCode.setUpdateTime(DateUtils.getNowDate());
                    cityCodeMapper.updateCityCode(baseCityCode);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、代码 " + baseCityCode.getCode() + " 更新成功");
                }
                else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、代码 " + baseCityCode.getCode() + " 已存在");
                }
            }catch (Exception e){
                failureNum++;
                String msg = "<br/>" + failureNum + "、代码 " + baseCityCode.getCode() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 新增城市代码
     *
     * @param cityCode 城市代码
     * @return 结果
     */
    @Override
    public int addCityCode(BaseCityCode cityCode) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        cityCode.setCreateBy(loginUser.getUsername());
        cityCode.setCreateTime(DateUtils.getNowDate());
        return cityCodeMapper.insertCityCode(cityCode);
    }

    /**
     * 修改城市代码
     *
     * @param cityCode 城市代码
     * @return 结果
     */
    @Override
    public int editCityCode(BaseCityCode cityCode) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        cityCode.setUpdateBy(loginUser.getUsername());
        cityCode.setUpdateTime(DateUtils.getNowDate());
        return cityCodeMapper.updateCityCode(cityCode);
    }

    /**
     * 删除城市代码
     *
     * @param ids 城市代码id集合
     * @return 结果
     */
    @Override
    public int delCityCode(Long[] ids) {
        return cityCodeMapper.delCityCode(ids);
    }
}
