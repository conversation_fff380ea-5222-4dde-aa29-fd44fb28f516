package com.gzairports.wl.departure.service;

import com.gzairports.common.basedata.domain.AllSecurityUrl;
import com.gzairports.common.core.domain.PageQuery;
import com.gzairports.common.securitySubmit.domain.AllSecurityWaybill;
import com.gzairports.wl.departure.domain.query.SecurityQueryWl;
import com.gzairports.wl.departure.domain.vo.SecurityInfoWl;
import com.gzairports.wl.departure.domain.vo.SecurityVoWl;

import java.util.List;

/**
 * @author: lan
 * @Desc: 物流安检提交Service接口
 * @create: 2024-11-14 11:56
 **/

public interface ISecuritySubmitService {
    /**
     * 安检申报列表
     * */
    PageQuery<List<SecurityVoWl>> securitySubmitList(SecurityQueryWl query);

    /**
     * 安检申报详情
     * */
    SecurityInfoWl getInfo(Long id);

    /**
     * 物流端提交安检数据
     * */
    int submit(SecurityInfoWl securityInfoWl);

    /**
     * 查询历史
     * */
    List<AllSecurityUrl> getHistoryList(Long id);

    /**
     * 物流端新增安检申报
     * */
    int addSecurity(AllSecurityWaybill allSecurityWaybill);

    /**
     * 代理人物流端重复提交品名清单附件
     * */
    int submitRepeat(SecurityInfoWl securityInfoWl);
}
