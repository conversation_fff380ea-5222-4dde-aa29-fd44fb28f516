package com.gzairports.web.controller.sms;

import com.gzairports.sms.domain.SmsSendLogPo;
import com.gzairports.sms.domain.dto.SmsSendQuery;
import com.gzairports.sms.service.ISmsSendService;
import com.gzairports.sms.service.impl.SmsSendLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;

import java.util.List;

/**
 * 短信发送历史记录Controller
 * 
 * <AUTHOR>
 * @date 2023-09-19
 */
@RestController
@RequestMapping("/sms/record")
@Api(value = "短信通知",tags = {"短信通知"})
public class BusinessSmsRecordController extends BaseController
{
    @Autowired
    private ISmsSendService smsSendService;

    @Autowired
    private SmsSendLogService smsSendLogService;

    /**
     * 查询短信发送历史记录列表
     */
    @PreAuthorize("@ss.hasPermi('sms:record:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询短信记录列表")
    public TableDataInfo list(SmsSendQuery query)
    {
        startPage();
        List<SmsSendLogPo> list = smsSendService.selectSmsSendLogList(query);
        return getDataTable(list);
    }

    /**
     * 获取短信发送历史记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('sms:record:query')")
    @GetMapping(value = "/{smsSendLogId}")
    @ApiOperation(value = "获取短信记录详细信息")
    public AjaxResult getInfo(@PathVariable("smsRecordId") Long smsSendLogId)
    {
        return AjaxResult.success(smsSendService.selectSmsSendLogById(smsSendLogId));
    }

    /**
     * 重新发送短信
     */
    @PreAuthorize("@ss.hasPermi('sms:record:resend')")
    @PostMapping(value = "/resend")
    @ApiOperation(value = "重新发送短信")
    public AjaxResult resend(@RequestBody SmsSendLogPo po)
    {
        return AjaxResult.success(smsSendService.resend(po));
    }

    /**
     * 根据四位运单号查询运单
     */
    @PreAuthorize("@ss.hasPermi('sms:record:list')")
    @GetMapping("/getCode/{code}")
    @ApiOperation(value = "根据四位运单号查询运单")
    public AjaxResult getCode(@PathVariable String code)
    {
        return AjaxResult.success(smsSendLogService.getCode(code));
    }

}
