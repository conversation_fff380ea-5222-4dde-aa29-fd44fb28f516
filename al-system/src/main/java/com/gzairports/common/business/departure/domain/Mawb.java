package com.gzairports.common.business.departure.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import com.gzairports.common.pdf.PdfPrintAnnotation;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import static com.gzairports.common.pdf.PdfPrintAnnotation.PdfFieldType.*;

/**
 * 国内主单表
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@Data
@TableName("all_air_waybill")
public class Mawb {

    /** 主键id */
    private Long id;

    /** 运单号 */
    @Excel(name = "运单号")
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "waybillCode")
    private String waybillCode;

    /** 交运货站 */
    private String shippingStation;

    /** 交运代理人 */
    private String shippingAgent;

    /** 换单 */
    private Integer switchBill;

    /** 进港中转 */
    private Integer transferBill;

    /** 原单单号 */
    private String originBill;

    /** 是否补货单 */
    private Integer replenishBill;

    /** 可补货重量 */
    private BigDecimal canRestockWeight;

    /** 补货重量 */
    private BigDecimal replenishWeight;

    /** 补货数量 */
    private Integer replenishNum;

    /** 状态 STAGING 暂存 NORMAL 正常 INVALID 作废*/
    private String status;

    /** 始发站 */
    @Excel(name = "始发站")
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "sourcePort")
    private String sourcePort;

    /** 目的站 */
    @Excel(name = "目的站")
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "desPort")
    private String desPort;

    /** 航班号 */
    @Excel(name = "航班号1")
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "flightNo1")
    private String flightNo1;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "航班日期1")
    private Date flightDate1;

    /** 航班日期 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "flightDate1")
    private String flightDate1Str;

    /** 航班号 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "flightNo2")
    private String flightNo2;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date flightDate2;

    /** 航班日期 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "flightDate2")
    private String flightDate2Str;

    /** 承运人1 */
    @Excel(name = "承运人1")
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "carrier1")
    private String carrier1;

    /** 到达站1 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "des1")
    private String des1;

    /** 承运人2 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "carrier2")
    private String carrier2;

    /** 到达站2 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "des2")
    private String des2;

    /** 承运人3 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "carrier3")
    private String carrier3;

    /** 到达站3 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "des3")
    private String des3;

    /** 发货人简称 */
    private String shipperAbb;

    /** 发货人 */
    @Excel(name = "发货人")
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipper")
    private String shipper;

    /** 发货人电话 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipperPhone")
    private String shipperPhone;

    /** 发货人地址 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipperAddress")
    private String shipperAddress;

    /** 发货人地区 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipperRegion")
    private String shipperRegion;

    /** 收货人简称 */
    private String consignAbb;

    /** 收货人 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "consign")
    private String consign;

    /** 收货人电话 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "consignPhone")
    private String consignPhone;

    /** 收货人地址 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "consignAddress")
    private String consignAddress;

    /** 收货人地区 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "consignRegion")
    private String consignRegion;

    /** 代理人公司 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "agentCompany")
    private String agentCompany;

    /** 代理人识别码 */
    private String agentCode;

    /** 城市 */
    private String city;

    /** 账号 */
    private String account;

    /** 结算注意事项 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "settlementNotes")
    private String settlementNotes;

    /** 储运注意事项 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "storageTransportNotes")
    private String storageTransportNotes;

    /** 海关监管 */
    private Integer customsSupervision;

    /** 公务单 */
    private Integer officialForm;

    /** 包量/包仓 */
    private Integer bulkWarehouse;

    /** 特货代码1 */
    @Excel(name = "特货代码1")
    private String specialCargoCode1;

    /** 特货代码2 */
    private String specialCargoCode2;

    /** 特货代码3 */
    private String specialCargoCode3;

    /** 其他特货代码 */
    private String otherSpecialCargoCode;

    /** 品名编码 12.4打印不需要品名编码 */
    @Excel(name = "品名编码")
//    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "cargoCode")
    private String cargoCode;

    /** 货品大类 */
    private String categoryName;

    /** 品名 */
    @Excel(name = "品名")
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "cargoName")
    private String cargoName;

    /** 包装 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "pack")
    private String pack;

    /** 包装code */
    private String packCode;

    /** 件数 */
    @Excel(name = "件数")
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "quantity")
    private Integer quantity;

    /** 重量 */
    @Excel(name = "重量")
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "weight")
    private BigDecimal weight;

    /** 计费重量 */
    @Excel(name = "计费重量")
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "chargeWeight")
    private BigDecimal chargeWeight;

    /** 体积（M3） */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "volume")
    private BigDecimal volume;

    /** 尺寸 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "size")
    private String size;

    /** 长宽高 体积 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "sizeVolume")
    private String sizeVolume;

    /** 是否需要冷藏 0 否 1 是 */
    private String isCold;

    /** 冷藏库 */
    private String coldStore;

    /** 危险品UN编号 */
    private String dangerCode;

    /** 危险品类型 */
    private String dangerType;

    /** 紧急联系人 */
    private String emergentContact;

    /** 紧急联系人电话 */
    private String contactPhone;

    /** 费用总计 */
    private BigDecimal costSum;

    /** 填开时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date writeTime;

    /** 填开时间打印 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "writeTime")
    private String writeTimeStr;

    /** 填开地点 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "writeLocation")
    private String writeLocation;

    /** 填开人 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "writer")
    private String writer;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 所属单位 */
    private Long deptId;

    /** 缴费状态 0 未授权支付 1 已授权支付 2 已结算 3 结算失败*/
    private Integer payStatus;
    
    /** 支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /** 支付结算时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date settleTime;

    /** 电子分单pdf地址 */
    private String pdfUrl;

    /** 安检申报单地址 */
    private String securityUrl;

    /** 是否与申报一致 0 否 1 是  -> 审单结果 0退回 1符合运输 */
    private Integer declarationConsistent;

    /** 最终安检提交状态 0 未通过 1 通过  -1退回 -2不合格 */
    private Integer securitySubmit;

    /** 物流端安检提交状态 0 未提交 1 物流提交 2货站提交 3合格 -1退回 -2不合格 */
    private Integer securitySubmitWl;

    /** 货站选择安检提交时的操作人 */
    private String securitySubmitOperator;

    /** 交货人信息 身份证 */
    private String deliveryIdNo;

    /** 交货人信息 头像拍照 */
    private String deliveryProfilePhoto;

    /** 交货人信息 随附文件拍照 */
    private String deliveryFilePhoto;

    /** 品名清单附件 */
    private String deliveryCargoNames;

    /** 品名清单附件pdf */
    private String deliveryCargoNamesPdf;

    /** 是否删除 */
    private Integer isDel;

    /** 类型 DEP 出港 ARR 进港 */
    private String type;

    /** 拼单状态 0 否 1 是 */
    private Integer mergeStatus;

    /** 运单类型  AWBA 主单 AWBM 邮件单*/
    private String waybillType;

    /** 支付金额 */
    private BigDecimal payMoney;

    /** 更新时间 */
    private Date updateTime;

    /** 国际国内 */
    private String domint;

    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = CHOICE, pdfFieldName = "pthw")
    private String pthw;

    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = CHOICE, pdfFieldName = "tzhw")
    private String tzhw;

    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = CHOICE, pdfFieldName = "wxp")
    private String wxp;

    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = CHOICE, pdfFieldName = "hkkj")
    private String hkkj;

//    @TableField(exist = false)
//    @PdfPrintAnnotation(pdfFieldType = IMAGE, pdfFieldName = "airLogo")
//    private String airLogo;

    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = IMAGE, pdfFieldName = "sealUrl")
    private String sealUrl;

    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "insurance")
    private String insurance;

    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "declaredValue")
    private String declaredValue;

    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "otherInfo")
    private String otherInfo;

    /** 航空费用 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "airCost")
    private BigDecimal airCost;

    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "airRate")
    private BigDecimal airRate;

    /** 其他费用 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "otherCost")
    private BigDecimal otherCost;

    /** 总额 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "totalCost")
    private BigDecimal totalCost;

    /** 地面运费 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "groundCost")
    private BigDecimal groundCost;

    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "rateType")
    private String rateType;

    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "cargoInfo")
    private String cargoInfo;

    /** 付款方式 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "payMethod")
    private String payMethod;

    /** 托运人或其代理人签字、盖章 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "agentSign")
    private String agentSign;

    /** 是否打印金额 0不打印 1打印 */
    private Integer printAmount;

    /** 安检申报填写 代理人签章 */
    private String agentSignature;

    /** 托运人签章 */
    private String shipperSignature;

    /** 是否告知 */
    private Integer isNotify;

    private BigDecimal refund;

    private Integer crossAir;

    private Integer isSouth;

    @TableField(exist = false)
    private Integer isExit;

    /** 控制版本号 */
    @TableField("version")
    @Version
    private Integer version;

    @TableField(exist = false)
    private String serialNo;


    /** 收运状态 0未收运 1开始收运 2虚拟收运 3收运结束 */
    private Integer collectStatus;
}
