package com.gzairports.common.business.reporter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.reporter.domain.ReportNodeQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by david on 2025/3/4
 */
@Mapper
public interface ReportNodeQueryMapper extends BaseMapper<ReportNodeQuery> {
    void saveOrUpdateBatch(@Param("list") List<ReportNodeQuery> list);
}
