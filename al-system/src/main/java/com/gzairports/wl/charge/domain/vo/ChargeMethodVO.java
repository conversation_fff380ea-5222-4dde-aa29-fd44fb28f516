package com.gzairports.wl.charge.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 客户收费方式返回数据
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@Data
public class ChargeMethodVO {

    /** 主键id */
    private Long id;

    /** 客户名称 */
    @Excel(name = "客户名称")
    private String customerName;

    /** 收费方式 */
    @Excel(name = "收费方式")
    private String paymentMethod;

    /** 结算周期 */
    @Excel(name = "结算周期")
    private Integer cycle;

    /** 是否启用 0 否 1 是 */
    private Integer enabled;

    /** 是否启用 */
    @Excel(name = "是否启用")
    private String isEnable;

    /** 生效时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "生效时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date effectiveTime;

    /** 所属单位 */
    @Excel(name = "所属单位")
    private String dept;
}
