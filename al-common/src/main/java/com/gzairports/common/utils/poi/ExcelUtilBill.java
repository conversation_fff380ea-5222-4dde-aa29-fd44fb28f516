package com.gzairports.common.utils.poi;

import com.gzairports.common.annotation.Excel;
import com.gzairports.common.annotation.Excels;
import com.gzairports.common.core.text.Convert;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.DictUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.file.FileTypeUtils;
import com.gzairports.common.utils.file.ImageUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.RegExUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFDataValidation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: lan
 * @Desc:
 * @create: 2025-05-09
 **/

public class ExcelUtilBill<T,U> {

    private static final Logger log = LoggerFactory.getLogger(ExcelUtil.class);

    public static final String FORMULA_REGEX_STR = "=|-|\\+|@";

    public static final String[] FORMULA_STR = { "=", "-", "+", "@" };

    /**
     * 用于dictType属性数据存储，避免重复查缓存
     */
    public Map<String, String> sysDictMap = new HashMap<String, String>();

    /**
     * Excel sheet最大行数，默认65536
     */
    public static final int sheetSize = 65536;

    /**
     * 工作表名称
     */
    private String sheetName;

    /**
     * 导出类型（EXPORT:导出数据；IMPORT：导入模板）
     */
    private Excel.Type type;

    /**
     * 工作薄对象
     */
    private Workbook wb;

    /**
     * 工作表对象
     */
    private Sheet sheet;

    /**
     * 样式列表
     */
    private Map<String, CellStyle> styles;


    /**
     * 导入导出数据列表
     */
    private List<T> list;

    /**
     * 导入导出数据列表
     */
    private List<U> list2;

    /**
     * 注解列表
     */
    private List<Object[]> fields;

    /**
     * 注解列表
     */
    private List<Object[]> fields2;

    /**
     * 当前行号
     */
    private int rownum;

    /**
     * 标题
     */
    private String title;

    /**
     * 最大高度
     */
    private short maxHeight;

    /**
     * 合并后最后行数
     */
    private int subMergedLastRowNum = 0;

    /**
     * 合并后开始行数
     */
    private int subMergedFirstRowNum = 1;

    /**
     * 对象的子列表方法
     */
    private Method subMethod;

    private Method subMethod2;

    /**
     * 对象的子列表属性
     */
    private List<Field> subFields;

    private List<Field> subFields2;

    /**
     * 统计列表
     */
    private Map<Integer, Double> statistics = new HashMap<Integer, Double>();

    /**
     * 数字格式
     */
    private static final DecimalFormat DOUBLE_FORMAT = new DecimalFormat("######0.00");

    /**
     * 实体对象
     */
    public Class<T> clazz;

    public Class<U> clazz2;

    /**
     * 需要排除列属性
     */
    public String[] excludeFields;

    public ExcelUtilBill(Class<T> clazz,Class<U> clazz2)
    {
        this.clazz = clazz;
        this.clazz2 = clazz2;
    }

    /**
     * 账单导出
     * @param response
     * @param list
     * @param upObj
     * @param sheetName
     */
    public <T,U> void exportBillExcel(HttpServletResponse response, List<T> list, List<U> list2, Object upObj, String sheetName) {
        this.initTwo(list, list2, sheetName, StringUtils.EMPTY, Excel.Type.EXPORT);

        try {
            Class<?> clazz = upObj.getClass();
            int rowIdx = 0;
            // 标题行
            Field titleFd = clazz.getDeclaredField("title");
            titleFd.setAccessible(true);
            String title = (String) titleFd.get(upObj);
            Row titleRow = sheet.createRow(rowIdx++);
            titleRow.setHeightInPoints(30);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellStyle(styles.get("title"));
            titleCell.setCellValue(title);
            CellRangeAddress mergedRegion = new CellRangeAddress(titleRow.getRowNum(), titleRow.getRowNum(), 0, 11);
            mergeCellSetBorder(mergedRegion);
            addMergedRegionIfNotOverlapping(mergedRegion, sheet);
//            sheet.addMergedRegion(mergedRegion);

            // 第一行：结算周期、备注
            Row firstDataRow = sheet.createRow(rowIdx++);
            firstDataRow.setHeight((short) 400);
            // 结算周期
            Field settlementCycleFd = clazz.getDeclaredField("settlementCycle");
            settlementCycleFd.setAccessible(true);
            String settlementCycle = (String) settlementCycleFd.get(upObj);
            Cell settlementCycleLabelCell = firstDataRow.createCell(0);
            settlementCycleLabelCell.setCellValue("结算周期");
            settlementCycleLabelCell.setCellStyle(centerStyle());
            sheet.addMergedRegion(new CellRangeAddress(firstDataRow.getRowNum(), firstDataRow.getRowNum(), 0, 1)); // 合并结算周期的值
            Cell settlementCycleValueCell = firstDataRow.createCell(2); // 值放在第2列
            settlementCycleValueCell.setCellValue(settlementCycle);
            settlementCycleValueCell.setCellStyle(centerStyle());
            sheet.addMergedRegion(new CellRangeAddress(firstDataRow.getRowNum(), firstDataRow.getRowNum(), 2, 6)); // 合并结算周期的值

            // 备注
            Field remarksFd = clazz.getDeclaredField("remarks");
            remarksFd.setAccessible(true);
            String remarks = (String) remarksFd.get(upObj);
            Cell remarksLabelCell = firstDataRow.createCell(7); // 标签放在第7列
            remarksLabelCell.setCellValue("备注");
            remarksLabelCell.setCellStyle(centerStyle());
            Cell remarksValueCell = firstDataRow.createCell(8); // 值放在第8列
            remarksValueCell.setCellValue(remarks);
            remarksValueCell.setCellStyle(centerStyle());
            CellRangeAddress remarkRegion = new CellRangeAddress(firstDataRow.getRowNum(), firstDataRow.getRowNum(), 8, 11);
            mergeCellSetBorder(remarkRegion);
            sheet.addMergedRegion(remarkRegion); // 合并备注的值

            // 第二行：充值金额、上期余额、当期退款、非当期退款、已结算、非当期结算、未结算、非当期未结算、未来航班、当期余额 总未结算
            Row secondDataRow = sheet.createRow(rowIdx++);
            secondDataRow.setHeight((short) 400);
            int colIdx = 0;
            for (Field declaredField : clazz.getDeclaredFields()) {
                declaredField.setAccessible(true);
                if ("title".equals(declaredField.getName()) || "chargeBilExportVOS".equals(declaredField.getName()) || "chargeBilExportVOSNotSettle".equals(declaredField.getName())
                        || "settlementCycle".equals(declaredField.getName()) || "remarks".equals(declaredField.getName())) {
                    continue;
                }
                Excel excelAnno = declaredField.getAnnotation(Excel.class);
                if (excelAnno == null){
                    continue;
                }
                String columnName = excelAnno.name();
                Cell cellUp = secondDataRow.createCell(colIdx);
                cellUp.setCellValue(columnName);
                cellUp.setCellStyle(centerStyle());

                colIdx++;
            }

            // 第三行：填充数据
            Row thirdDataRow = sheet.createRow(rowIdx++);
            thirdDataRow.setHeight((short) 400);
            colIdx = 0;
            for (Field declaredField : clazz.getDeclaredFields()) {
                declaredField.setAccessible(true);
                if ("title".equals(declaredField.getName()) || "chargeBilExportVOS".equals(declaredField.getName()) || "chargeBilExportVOSNotSettle".equals(declaredField.getName())
                        || "settlementCycle".equals(declaredField.getName()) || "remarks".equals(declaredField.getName())) {
                    continue;
                }
                Object val = declaredField.get(upObj);
                if (val == null){
                    continue;
                }
                Cell cellDown = thirdDataRow.createCell(colIdx);
                if (val instanceof BigDecimal) {
                    cellDown.setCellValue(((BigDecimal) val).doubleValue());
                    cellDown.setCellType(CellType.NUMERIC); // 设置单元格类型为数值类型
                } else if (val instanceof Integer) {
                    cellDown.setCellValue(((Integer) val).intValue());
                    cellDown.setCellType(CellType.NUMERIC);
                } else if (val instanceof Double) {
                    cellDown.setCellValue(((Double) val));
                    cellDown.setCellType(CellType.NUMERIC);
                } else {
                    // 对于非数值类型的字段，仍可以按原样设置为字符串
                    cellDown.setCellValue(val.toString());
                }
                cellDown.setCellStyle(centerStyle());

                colIdx++;
            }
            this.rownum = rowIdx + 1;
//            writeSheet();

            // 第一部分数据
            if (!list.isEmpty()) {
                createCustomTitle("已结算");
                createSubHead();
                writeSheet(list);
                rownum += 3;
            }

            // 第二组数据
            if (!list2.isEmpty()) {
                createCustomTitle("未结算");
                createSubHead();
                writeSheet2(list2);
            }

            wb.write(response.getOutputStream());
        } catch (IOException e) {
            throw new RuntimeException("导出Excel异常" + e);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException(e);
        } finally {
            IOUtils.closeQuietly(wb);
        }
    }

    public <T,U> void initTwo(List<T> list,List<U> list2, String sheetName, String title, Excel.Type type) {
        if (list == null)
        {
            list = new ArrayList<T>();
        }
        if (list2 == null)
        {
            list2 = new ArrayList<U>();
        }
        this.sheetName = sheetName;
        this.type = type;
        this.title = title;
        createExcelField();
        createWorkbook();

    }

    /**
     * 得到所有定义字段
     */
    private void createExcelField()
    {
        this.fields = getFields();
        this.fields2 = getFields2();
        this.fields = this.fields.stream().sorted(Comparator.comparing(objects -> ((Excel) objects[1]).sort())).collect(Collectors.toList());
        this.fields2 = this.fields2.stream().sorted(Comparator.comparing(objects -> ((Excel) objects[1]).sort())).collect(Collectors.toList());
        this.maxHeight = getRowHeight();
    }

    /**
     * 获取字段注解信息
     */
    public List<Object[]> getFields()
    {
        List<Object[]> fields = new ArrayList<Object[]>();
        List<Field> tempFields = new ArrayList<>();
        tempFields.addAll(Arrays.asList(clazz.getSuperclass().getDeclaredFields()));
        tempFields.addAll(Arrays.asList(clazz.getDeclaredFields()));
        for (Field field : tempFields)
        {
            if (!ArrayUtils.contains(this.excludeFields, field.getName()))
            {
                // 单注解
                if (field.isAnnotationPresent(Excel.class))
                {
                    Excel attr = field.getAnnotation(Excel.class);
                    if (attr != null && (attr.type() == Excel.Type.ALL || attr.type() == type))
                    {
                        field.setAccessible(true);
                        fields.add(new Object[] { field, attr });
                    }
                    if (Collection.class.isAssignableFrom(field.getType()))
                    {
                        subMethod = getSubMethod(field.getName(), clazz);
                        ParameterizedType pt = (ParameterizedType) field.getGenericType();
                        Class<?> subClass = (Class<?>) pt.getActualTypeArguments()[0];
                        this.subFields = FieldUtils.getFieldsListWithAnnotation(subClass, Excel.class);
                    }
                }

                // 多注解
                if (field.isAnnotationPresent(Excels.class))
                {
                    Excels attrs = field.getAnnotation(Excels.class);
                    Excel[] excels = attrs.value();
                    for (Excel attr : excels)
                    {
                        if (!ArrayUtils.contains(this.excludeFields, field.getName() + "." + attr.targetAttr())
                                && (attr != null && (attr.type() == Excel.Type.ALL || attr.type() == type)))
                        {
                            field.setAccessible(true);
                            fields.add(new Object[] { field, attr });
                        }
                    }
                }
            }
        }
        return fields;
    }

    public List<Object[]> getFields2()
    {
        List<Object[]> fields = new ArrayList<Object[]>();
        List<Field> tempFields = new ArrayList<>();
        tempFields.addAll(Arrays.asList(clazz2.getSuperclass().getDeclaredFields()));
        tempFields.addAll(Arrays.asList(clazz2.getDeclaredFields()));
        for (Field field : tempFields)
        {
            if (!ArrayUtils.contains(this.excludeFields, field.getName()))
            {
                // 单注解
                if (field.isAnnotationPresent(Excel.class))
                {
                    Excel attr = field.getAnnotation(Excel.class);
                    if (attr != null && (attr.type() == Excel.Type.ALL || attr.type() == type))
                    {
                        field.setAccessible(true);
                        fields.add(new Object[] { field, attr });
                    }
                    if (Collection.class.isAssignableFrom(field.getType()))
                    {
                        subMethod2 = getSubMethod(field.getName(), clazz2);
                        ParameterizedType pt = (ParameterizedType) field.getGenericType();
                        Class<?> subClass = (Class<?>) pt.getActualTypeArguments()[0];
                        this.subFields2 = FieldUtils.getFieldsListWithAnnotation(subClass, Excel.class);
                    }
                }

                // 多注解
                if (field.isAnnotationPresent(Excels.class))
                {
                    Excels attrs = field.getAnnotation(Excels.class);
                    Excel[] excels = attrs.value();
                    for (Excel attr : excels)
                    {
                        if (!ArrayUtils.contains(this.excludeFields, field.getName() + "." + attr.targetAttr())
                                && (attr != null && (attr.type() == Excel.Type.ALL || attr.type() == type)))
                        {
                            field.setAccessible(true);
                            fields.add(new Object[] { field, attr });
                        }
                    }
                }
            }
        }
        return fields;
    }

    /**
     * 获取对象的子列表方法
     *
     * @param name 名称
     * @param pojoClass 类对象
     * @return 子列表方法
     */
    public Method getSubMethod(String name, Class<?> pojoClass)
    {
        StringBuffer getMethodName = new StringBuffer("get");
        getMethodName.append(name.substring(0, 1).toUpperCase());
        getMethodName.append(name.substring(1));
        Method method = null;
        try
        {
            method = pojoClass.getMethod(getMethodName.toString(), new Class[] {});
        }
        catch (Exception e)
        {
            log.error("获取对象异常{}", e.getMessage());
        }
        return method;
    }

    /**
     * 创建一个工作簿
     */
    public void createWorkbook()
    {
        this.wb = new SXSSFWorkbook(500);
        this.sheet = wb.createSheet();
        wb.setSheetName(0, sheetName);
        this.styles = createStyles(wb);
    }

    /**
     * 创建表格样式
     *
     * @param wb 工作薄对象
     * @return 样式列表
     */
    private Map<String, CellStyle> createStyles(Workbook wb)
    {
        // 写入各条记录,每条记录对应excel表中的一行
        Map<String, CellStyle> styles = new HashMap<String, CellStyle>();
        CellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font titleFont = wb.createFont();
        titleFont.setFontName("Arial");
        titleFont.setFontHeightInPoints((short) 16);
        titleFont.setBold(true);
        style.setFont(titleFont);
        styles.put("title", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font printByFont = wb.createFont();
        printByFont.setFontHeightInPoints((short) 12);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setFont(printByFont);
        styles.put("printBy", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font clearedByFont = wb.createFont();
        clearedByFont.setFontHeightInPoints((short) 12);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setFont(clearedByFont);
        styles.put("clearedBy", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font twoByFont = wb.createFont();
        twoByFont.setFontHeightInPoints((short) 12);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setFont(twoByFont);
        styles.put("two", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        Font dataFont = wb.createFont();
        dataFont.setFontName("Arial");
        dataFont.setFontHeightInPoints((short) 10);
        style.setFont(dataFont);
        styles.put("data", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font totalFont = wb.createFont();
        totalFont.setFontName("Arial");
        totalFont.setFontHeightInPoints((short) 10);
        style.setFont(totalFont);
        styles.put("total", style);

        styles.putAll(annotationHeaderStyles(wb, styles));

        styles.putAll(annotationDataStyles(wb));

        return styles;
    }

    /**
     * 根据Excel注解创建表格头样式
     *
     * @param wb 工作薄对象
     * @return 自定义样式列表
     */
    private Map<String, CellStyle> annotationHeaderStyles(Workbook wb, Map<String, CellStyle> styles)
    {
        Map<String, CellStyle> headerStyles = new HashMap<String, CellStyle>();
        for (Object[] os : fields)
        {
            Excel excel = (Excel) os[1];
            String key = StringUtils.format("header_{}_{}", excel.headerColor(), excel.headerBackgroundColor());
            if (!headerStyles.containsKey(key))
            {
                CellStyle style = wb.createCellStyle();
                style.cloneStyleFrom(styles.get("data"));
                style.setAlignment(HorizontalAlignment.CENTER);
                style.setVerticalAlignment(VerticalAlignment.CENTER);
                style.setFillForegroundColor(excel.headerBackgroundColor().index);
                style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                Font headerFont = wb.createFont();
                headerFont.setFontName("Arial");
                headerFont.setFontHeightInPoints((short) 10);
                headerFont.setBold(true);
                headerFont.setColor(excel.headerColor().index);
                style.setFont(headerFont);
                headerStyles.put(key, style);
            }
        }
        return headerStyles;
    }

    /**
     * 根据Excel注解创建表格列样式
     *
     * @param wb 工作薄对象
     * @return 自定义样式列表
     */
    private Map<String, CellStyle> annotationDataStyles(Workbook wb)
    {
        Map<String, CellStyle> styles = new HashMap<String, CellStyle>();
        for (Object[] os : fields)
        {
            Excel excel = (Excel) os[1];
            String key = StringUtils.format("data_{}_{}_{}", excel.align(), excel.color(), excel.backgroundColor());
            if (!styles.containsKey(key))
            {
                CellStyle style = wb.createCellStyle();
                style.setAlignment(excel.align());
                style.setVerticalAlignment(VerticalAlignment.CENTER);
                style.setBorderRight(BorderStyle.THIN);
                style.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
                style.setBorderLeft(BorderStyle.THIN);
                style.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
                style.setBorderTop(BorderStyle.THIN);
                style.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
                style.setBorderBottom(BorderStyle.THIN);
                style.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
                style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                style.setFillForegroundColor(excel.backgroundColor().getIndex());
                Font dataFont = wb.createFont();
                dataFont.setFontName("Arial");
                dataFont.setFontHeightInPoints((short) 10);
                dataFont.setColor(excel.color().index);
                style.setFont(dataFont);
                styles.put(key, style);
            }
        }
        return styles;
    }

    /**
     * 根据注解获取最大行高
     */
    public short getRowHeight()
    {
        double maxHeight = 0;
        for (Object[] os : this.fields)
        {
            Excel excel = (Excel) os[1];
            maxHeight = Math.max(maxHeight, excel.height());
        }
        return (short) (maxHeight * 20);
    }

    private void mergeCellSetBorder(CellRangeAddress mergedRegion) {
        // 使用RegionUtil设置边框
        RegionUtil.setBorderTop(BorderStyle.THIN, mergedRegion, sheet);
        RegionUtil.setBorderBottom(BorderStyle.THIN, mergedRegion, sheet);
        RegionUtil.setBorderLeft(BorderStyle.THIN, mergedRegion, sheet);
        RegionUtil.setBorderRight(BorderStyle.THIN, mergedRegion, sheet);
    }

    private void addMergedRegionIfNotOverlapping(CellRangeAddress region, Sheet sheet) {
        if (!isRegionOverlapping(region, sheet)) {
            sheet.addMergedRegion(region);
        } else {
            log.warn("Attempted to add overlapping merged region: {}", region.formatAsString());
        }
    }

    private boolean isRegionOverlapping(CellRangeAddress newRegion, Sheet sheet) {
        for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
            CellRangeAddress existingRegion = sheet.getMergedRegion(i);
            if (existingRegion.intersects(newRegion)) {
                return true;
            }
        }
        return false;
    }

    private CellStyle centerStyle() {
        CellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        return style;
    }

    /**
     * 自定义创建标题行（不会重复合并）
     */
    private void createCustomTitle(String subTitle) {
        if (StringUtils.isNotEmpty(subTitle)) {
            int currentRow = rownum;
            boolean isMerged = false;

            // 检查当前行是否已经被合并
            for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
                CellRangeAddress region = sheet.getMergedRegion(i);
                if (region.getFirstRow() == currentRow && region.getLastRow() == currentRow) {
                    isMerged = true;
                    break;
                }
            }

            if (!isMerged) {
                Row titleRow = sheet.createRow(rownum++);
                titleRow.setHeightInPoints(25);

                Cell titleCell = titleRow.createCell(0);
                titleCell.setCellValue(subTitle);
                titleCell.setCellStyle(styles.get("title"));

                int lastColIndex = this.fields.size() - 1;
                sheet.addMergedRegion(new CellRangeAddress(
                        titleRow.getRowNum(), titleRow.getRowNum(), 0, lastColIndex));
            } else {
                rownum++; // 跳过该行，避免写入内容到已合并但为空的行
            }
        }
    }


    /**
     * 创建对象的子列表名称
     */
    public void createSubHead() {
        if (isSubList()) {
            subMergedFirstRowNum++;
            subMergedLastRowNum++;
            Row subRow = sheet.createRow(rownum);
            int excelNum = 0;
            for (Object[] objects : fields2) {
                Excel attr = (Excel) objects[1];
                Cell headCell1 = subRow.createCell(excelNum);
                headCell1.setCellValue(attr.name());
                excelNum++;
            }
            int headFirstRow = excelNum - 1;
            int headLastRow = headFirstRow + subFields2.size() - 1;
            if (headLastRow > headFirstRow) {
                sheet.addMergedRegion(new CellRangeAddress(rownum, rownum, headFirstRow, headLastRow));
            }
            rownum++;
        }
    }

    /**
     * 是否有对象的子列表
     */
    public boolean isSubList()
    {
        return StringUtils.isNotNull(subFields) && subFields.size() > 0;
    }

    public <T> void writeSheet(List<T> dataList)
    {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        // 分页写入
        int sheetNo = Math.max(1, (int) Math.ceil(dataList.size() * 1.0 / sheetSize));

        for (int index = 0; index < sheetNo; index++)
        {
            createSheet(sheetNo, index);

            // 写表头
            Row headerRow = sheet.createRow(rownum);
            int column = 0;

            for (Object[] os : fields) {
                Field field = (Field) os[0];
                Excel excel = (Excel) os[1];

                if (Collection.class.isAssignableFrom(field.getType())) {
                    for (Field subField : subFields) {
                        Excel subExcel = subField.getAnnotation(Excel.class);
                        this.createHeadCell(subExcel, headerRow, column++);
                    }
                } else {
                    this.createHeadCell(excel, headerRow, column++);
                }
            }

            if (column >= 11) {
                CellRangeAddress region = new CellRangeAddress(
                        rownum, rownum,
                        10, 11
                );
                sheet.addMergedRegion(region);

                // 可选：设置合并后的标题内容
//                Cell mergedCell = headerRow.createCell(10); // 第11列位置
//                mergedCell.setCellValue("你的合并标题"); // 比如 "备注信息"
            }

            // 填充数据
            if (Excel.Type.EXPORT.equals(type)) {
                fillExcelData(index, headerRow, dataList);
                addStatisticsRow();
            }

            rownum++; // 表头后移一行
        }
    }

    public <U> void writeSheet2(List<U> dataList)
    {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        // 分页写入
        int sheetNo = Math.max(1, (int) Math.ceil(dataList.size() * 1.0 / sheetSize));

        for (int index = 0; index < sheetNo; index++)
        {
            createSheet(sheetNo, index);

            // 写表头
            Row headerRow = sheet.createRow(rownum);
            int column = 0;

            for (Object[] os : fields2) {
                Field field = (Field) os[0];
                Excel excel = (Excel) os[1];

                if (Collection.class.isAssignableFrom(field.getType())) {
                    for (Field subField : subFields2) {
                        Excel subExcel = subField.getAnnotation(Excel.class);
                        this.createHeadCell(subExcel, headerRow, column++);
                    }
                } else {
                    this.createHeadCell(excel, headerRow, column++);
                }
            }

            // 填充数据
            if (Excel.Type.EXPORT.equals(type)) {
                fillExcelData2(index, headerRow, dataList);
                addStatisticsRow();
            }

            rownum++; // 表头后移一行
        }
    }

    /**
     * 创建工作表
     *
     * @param sheetNo sheet数量
     * @param index 序号
     */
    public void createSheet(int sheetNo, int index)
    {
        // 设置工作表的名称.
        if (sheetNo > 1 && index > 0)
        {
            this.sheet = wb.createSheet();
            this.createTitle();
            wb.setSheetName(index, sheetName + index);
        }
    }

    /**
     * 创建excel第一行标题
     */
    public void createTitle()
    {
        if (StringUtils.isNotEmpty(title))
        {
            subMergedFirstRowNum++;
            subMergedLastRowNum++;
            int titleLastCol = this.fields.size() - 1;
            if (isSubList())
            {
                titleLastCol = titleLastCol + subFields.size() - 1;
            }
            Row titleRow = sheet.createRow(rownum == 0 ? rownum++ : 0);
            titleRow.setHeightInPoints(30);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellStyle(styles.get("title"));
            titleCell.setCellValue(title);
            sheet.addMergedRegion(new CellRangeAddress(titleRow.getRowNum(), titleRow.getRowNum(), titleRow.getRowNum(), titleLastCol));
        }
    }

    /**
     * 创建单元格
     */
    public Cell createHeadCell(Excel attr, Row row, int column)
    {
        // 创建列
        Cell cell = row.createCell(column);
        // 写入列信息
        cell.setCellValue(attr.name());
        setDataValidation(attr, row, column);
        cell.setCellStyle(styles.get(StringUtils.format("header_{}_{}", attr.headerColor(), attr.headerBackgroundColor())));
        if (isSubList())
        {
            // 填充默认样式，防止合并单元格样式失效
            sheet.setDefaultColumnStyle(column, styles.get(StringUtils.format("data_{}_{}_{}", attr.align(), attr.color(), attr.backgroundColor())));
            if (attr.needMerge())
            {
                sheet.addMergedRegion(new CellRangeAddress(rownum - 1, rownum, column, column));
            }
        }
        return cell;
    }

    /**
     * 创建表格样式
     */
    public void setDataValidation(Excel attr, Row row, int column)
    {
        if (attr.name().indexOf("注：") >= 0)
        {
            sheet.setColumnWidth(column, 6000);
        }
        else
        {
            // 设置列宽
            sheet.setColumnWidth(column, (int) ((attr.width() + 0.72) * 256));
        }
        if (StringUtils.isNotEmpty(attr.prompt()) || attr.combo().length > 0)
        {
            if (attr.combo().length > 15 || StringUtils.join(attr.combo()).length() > 255)
            {
                // 如果下拉数大于15或字符串长度大于255，则使用一个新sheet存储，避免生成的模板下拉值获取不到
                setXSSFValidationWithHidden(sheet, attr.combo(), attr.prompt(), 1, 100, column, column);
            }
            else
            {
                // 提示信息或只能选择不能输入的列内容.
                setPromptOrValidation(sheet, attr.combo(), attr.prompt(), 1, 100, column, column);
            }
        }
    }

    public void setXSSFValidationWithHidden(Sheet sheet, String[] textlist, String promptContent, int firstRow, int endRow, int firstCol, int endCol)
    {
        String hideSheetName = "combo_" + firstCol + "_" + endCol;
        Sheet hideSheet = wb.createSheet(hideSheetName); // 用于存储 下拉菜单数据
        for (int i = 0; i < textlist.length; i++)
        {
            hideSheet.createRow(i).createCell(0).setCellValue(textlist[i]);
        }
        // 创建名称，可被其他单元格引用
        Name name = wb.createName();
        name.setNameName(hideSheetName + "_data");
        name.setRefersToFormula(hideSheetName + "!$A$1:$A$" + textlist.length);
        DataValidationHelper helper = sheet.getDataValidationHelper();
        // 加载下拉列表内容
        DataValidationConstraint constraint = helper.createFormulaListConstraint(hideSheetName + "_data");
        // 设置数据有效性加载在哪个单元格上,四个参数分别是：起始行、终止行、起始列、终止列
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);
        // 数据有效性对象
        DataValidation dataValidation = helper.createValidation(constraint, regions);
        if (StringUtils.isNotEmpty(promptContent))
        {
            // 如果设置了提示信息则鼠标放上去提示
            dataValidation.createPromptBox("", promptContent);
            dataValidation.setShowPromptBox(true);
        }
        // 处理Excel兼容性问题
        if (dataValidation instanceof XSSFDataValidation)
        {
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.setShowErrorBox(true);
        }
        else
        {
            dataValidation.setSuppressDropDownArrow(false);
        }

        sheet.addValidationData(dataValidation);
        // 设置hiddenSheet隐藏
        wb.setSheetHidden(wb.getSheetIndex(hideSheet), true);
    }

    public void setPromptOrValidation(Sheet sheet, String[] textlist, String promptContent, int firstRow, int endRow,
                                      int firstCol, int endCol)
    {
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = textlist.length > 0 ? helper.createExplicitListConstraint(textlist) : helper.createCustomConstraint("DD1");
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);
        DataValidation dataValidation = helper.createValidation(constraint, regions);
        if (StringUtils.isNotEmpty(promptContent))
        {
            // 如果设置了提示信息则鼠标放上去提示
            dataValidation.createPromptBox("", promptContent);
            dataValidation.setShowPromptBox(true);
        }
        // 处理Excel兼容性问题
        if (dataValidation instanceof XSSFDataValidation)
        {
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.setShowErrorBox(true);
        }
        else
        {
            dataValidation.setSuppressDropDownArrow(false);
        }
        sheet.addValidationData(dataValidation);
    }

    protected <T> void fillExcelData(int pageIndex, Row headerRow, List<T> dataList)
    {
        int start = pageIndex * sheetSize;
        int end = Math.min(start + sheetSize, dataList.size());

        for (int i = start; i < end; i++) {
            T t = dataList.get(i);
            rownum++;
            Row row = sheet.createRow(rownum);

            int column = 0;
            for (Object[] os : fields) {
                Field field = (Field) os[0];
                Excel excel = (Excel) os[1];

                field.setAccessible(true);
                try {
                    Object val = field.get(t);
                    Cell cell = row.createCell(column++);
                    setCellValue(cell, val, excel.align());
                } catch (IllegalAccessException e) {
                    throw new RuntimeException("Excel反射写入失败", e);
                }
            }

            //合并第十一列十二列 ->结算时间
            if (column >= 11) {
                sheet.addMergedRegion(new CellRangeAddress(
                        rownum, rownum,
                        10, 11
                ));
            }
        }

    }

    protected <U> void fillExcelData2(int pageIndex, Row headerRow, List<U> dataList)
    {
        int start = pageIndex * sheetSize;
        int end = Math.min(start + sheetSize, dataList.size());

        for (int i = start; i < end; i++) {
            U t = dataList.get(i);
            rownum++;
            Row row = sheet.createRow(rownum);

            int column = 0;
            for (Object[] os : fields2) {
                Field field = (Field) os[0];
                Excel excel = (Excel) os[1];

                field.setAccessible(true);
                try {
                    Object val = field.get(t);
                    Cell cell = row.createCell(column++);
                    setCellValue(cell, val, excel.align());
                } catch (IllegalAccessException e) {
                    throw new RuntimeException("Excel反射写入失败", e);
                }
            }
        }
    }

    private void setCellValue(Cell cell, Object val, HorizontalAlignment align) {
        if (val instanceof String) {
            cell.setCellValue((String) val);
        } else if (val instanceof Number) {
            cell.setCellValue(((Number) val).doubleValue());
        } else if (val instanceof Boolean) {
            cell.setCellValue((Boolean) val);
        } else if (val == null) {
            cell.setCellValue("");
        } else {
            cell.setCellValue(val.toString());
        }

        CellStyle style = cell.getCellStyle();
        if (style == null) {
            style = cell.getSheet().getWorkbook().createCellStyle();
        }

        // 克隆已有样式后再设置对齐方式
        CellStyle newStyle = cell.getSheet().getWorkbook().createCellStyle();
        newStyle.cloneStyleFrom(style);
        newStyle.setAlignment(align);
        cell.setCellStyle(newStyle);
    }


    /**
     * 是否有对象的子列表，集合不为空
     */
    public boolean isSubListValue(T vo)
    {
        return StringUtils.isNotNull(subFields) && subFields.size() > 0 && StringUtils.isNotNull(getListCellValue(vo)) && getListCellValue(vo).size() > 0;
    }

    /**
     * 获取集合的值
     */
    public Collection<?> getListCellValue(Object obj)
    {
        Object value;
        try
        {
            value = subMethod.invoke(obj, new Object[] {});
        }
        catch (Exception e)
        {
            return new ArrayList<Object>();
        }
        return (Collection<?>) value;
    }

    public Cell addCell(Excel attr, Row row, T vo, Field field, int column)
    {
        Cell cell = null;
        try
        {
            // 设置行高
            row.setHeight(maxHeight);
            // 根据Excel中设置情况决定是否导出,有些情况需要保持为空,希望用户填写这一列.
            if (attr.isExport())
            {
                // 创建cell
                cell = row.createCell(column);
                if (isSubListValue(vo) && getListCellValue(vo).size() > 1 && attr.needMerge())
                {
                    CellRangeAddress cellAddress = new CellRangeAddress(subMergedFirstRowNum, subMergedLastRowNum, column, column);
                    sheet.addMergedRegion(cellAddress);
                }
                cell.setCellStyle(styles.get(StringUtils.format("data_{}_{}_{}", attr.align(), attr.color(), attr.backgroundColor())));

                // 用于读取对象中的属性
                Object value = getTargetValue(vo, field, attr);
                String dateFormat = attr.dateFormat();
                String readConverterExp = attr.readConverterExp();
                String separator = attr.separator();
                String dictType = attr.dictType();
                if (StringUtils.isNotEmpty(dateFormat) && StringUtils.isNotNull(value))
                {
                    cell.setCellValue(parseDateToStr(dateFormat, value));
                }
                else if (StringUtils.isNotEmpty(readConverterExp) && StringUtils.isNotNull(value))
                {
                    cell.setCellValue(convertByExp(Convert.toStr(value), readConverterExp, separator));
                }
                else if (StringUtils.isNotEmpty(dictType) && StringUtils.isNotNull(value))
                {
                    if (!sysDictMap.containsKey(dictType + value))
                    {
                        String lable = convertDictByExp(Convert.toStr(value), dictType, separator);
                        sysDictMap.put(dictType + value, lable);
                    }
                    cell.setCellValue(sysDictMap.get(dictType + value));
                }
                else if (value instanceof BigDecimal && -1 != attr.scale())
                {
                    cell.setCellValue((((BigDecimal) value).setScale(attr.scale(), attr.roundingMode())).doubleValue());
                }
                else if (!attr.handler().equals(ExcelHandlerAdapter.class))
                {
                    cell.setCellValue(dataFormatHandlerAdapter(value, attr, cell));
                }
                else
                {
                    // 设置列类型
                    setCellVo(value, attr, cell);
                }
                addStatisticsData(column, Convert.toStr(value), attr);
            }
        }
        catch (Exception e)
        {
            log.error("导出Excel失败{}", e);
        }
        return cell;
    }

    private Object getTargetValue(T vo, Field field, Excel excel) throws Exception
    {
        Object o = field.get(vo);
        if (StringUtils.isNotEmpty(excel.targetAttr()))
        {
            String target = excel.targetAttr();
            if (target.contains("."))
            {
                String[] targets = target.split("[.]");
                for (String name : targets)
                {
                    o = getValue(o, name);
                }
            }
            else
            {
                o = getValue(o, target);
            }
        }
        return o;
    }

    public String parseDateToStr(String dateFormat, Object val)
    {
        if (val == null)
        {
            return "";
        }
        String str;
        if (val instanceof Date)
        {
            str = DateUtils.parseDateToStr(dateFormat, (Date) val);
        }
        else if (val instanceof LocalDateTime)
        {
            str = DateUtils.parseDateToStr(dateFormat, DateUtils.toDate((LocalDateTime) val));
        }
        else if (val instanceof LocalDate)
        {
            str = DateUtils.parseDateToStr(dateFormat, DateUtils.toDate((LocalDate) val));
        }
        else
        {
            str = val.toString();
        }
        return str;
    }

    private Object getValue(Object o, String name) throws Exception
    {
        if (StringUtils.isNotNull(o) && StringUtils.isNotEmpty(name))
        {
            Class<?> clazz = o.getClass();
            Field field = clazz.getDeclaredField(name);
            field.setAccessible(true);
            o = field.get(o);
        }
        return o;
    }

    public static String convertByExp(String propertyValue, String converterExp, String separator)
    {
        StringBuilder propertyString = new StringBuilder();
        String[] convertSource = converterExp.split(",");
        for (String item : convertSource)
        {
            String[] itemArray = item.split("=");
            if (StringUtils.containsAny(propertyValue, separator))
            {
                for (String value : propertyValue.split(separator))
                {
                    if (itemArray[0].equals(value))
                    {
                        propertyString.append(itemArray[1] + separator);
                        break;
                    }
                }
            }
            else
            {
                if (itemArray[0].equals(propertyValue))
                {
                    return itemArray[1];
                }
            }
        }
        return StringUtils.stripEnd(propertyString.toString(), separator);
    }

    public static String convertDictByExp(String dictValue, String dictType, String separator)
    {
        return DictUtils.getDictLabel(dictType, dictValue, separator);
    }

    public String dataFormatHandlerAdapter(Object value, Excel excel, Cell cell)
    {
        try
        {
            Object instance = excel.handler().newInstance();
            Method formatMethod = excel.handler().getMethod("format", new Class[] { Object.class, String[].class, Cell.class, Workbook.class });
            value = formatMethod.invoke(instance, value, excel.args(), cell, this.wb);
        }
        catch (Exception e)
        {
            log.error("不能格式化数据 " + excel.handler(), e.getMessage());
        }
        return Convert.toStr(value);
    }

    public void setCellVo(Object value, Excel attr, Cell cell)
    {
        if (Excel.ColumnType.STRING == attr.cellType())
        {
            String cellValue = Convert.toStr(value);
            // 对于任何以表达式触发字符 =-+@开头的单元格，直接使用tab字符作为前缀，防止CSV注入。
            if (StringUtils.startsWithAny(cellValue, FORMULA_STR))
            {
                cellValue = RegExUtils.replaceFirst(cellValue, FORMULA_REGEX_STR, "\t$0");
            }
            if (value instanceof Collection && StringUtils.equals("[]", cellValue))
            {
                cellValue = StringUtils.EMPTY;
            }
            cell.setCellValue(StringUtils.isNull(cellValue) ? attr.defaultValue() : cellValue + attr.suffix());
        }
        else if (Excel.ColumnType.NUMERIC == attr.cellType())
        {
            if (StringUtils.isNotNull(value))
            {
                cell.setCellValue(StringUtils.contains(Convert.toStr(value), ".") ? Convert.toDouble(value) : Convert.toInt(value));
            }
        }
        else if (Excel.ColumnType.IMAGE == attr.cellType())
        {
            ClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0, (short) cell.getColumnIndex(), cell.getRow().getRowNum(), (short) (cell.getColumnIndex() + 1), cell.getRow().getRowNum() + 1);
            String imagePath = Convert.toStr(value);
            if (StringUtils.isNotEmpty(imagePath))
            {
                byte[] data = ImageUtils.getImage(imagePath);
                getDrawingPatriarch(cell.getSheet()).createPicture(anchor,
                        cell.getSheet().getWorkbook().addPicture(data, getImageType(data)));
            }
        }
    }

    public static Drawing<?> getDrawingPatriarch(Sheet sheet)
    {
        if (sheet.getDrawingPatriarch() == null)
        {
            sheet.createDrawingPatriarch();
        }
        return sheet.getDrawingPatriarch();
    }

    public int getImageType(byte[] value)
    {
        String type = FileTypeUtils.getFileExtendName(value);
        if ("JPG".equalsIgnoreCase(type))
        {
            return Workbook.PICTURE_TYPE_JPEG;
        }
        else if ("PNG".equalsIgnoreCase(type))
        {
            return Workbook.PICTURE_TYPE_PNG;
        }
        return Workbook.PICTURE_TYPE_JPEG;
    }

    private void addStatisticsData(Integer index, String text, Excel entity)
    {
        if (entity != null && entity.isStatistics())
        {
            Double temp = 0D;
            if (!statistics.containsKey(index))
            {
                statistics.put(index, temp);
            }
            try
            {
                temp = Double.valueOf(text);
            }
            catch (NumberFormatException e)
            {
            }
            statistics.put(index, statistics.get(index) + temp);
        }
    }

    public void addStatisticsRow()
    {
        if (statistics.size() > 0)
        {
            Row row = sheet.createRow(sheet.getLastRowNum() + 1);
            Set<Integer> keys = statistics.keySet();
            Cell cell = row.createCell(0);
            cell.setCellStyle(styles.get("total"));
            cell.setCellValue("合计");

            for (Integer key : keys)
            {
                cell = row.createCell(key);
                cell.setCellStyle(styles.get("total"));
                cell.setCellValue(DOUBLE_FORMAT.format(statistics.get(key)));
            }
            statistics.clear();
        }
    }


}
