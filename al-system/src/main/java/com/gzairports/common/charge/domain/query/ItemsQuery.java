package com.gzairports.common.charge.domain.query;

import lombok.Data;

import java.util.Date;

/**
 * 货站收费项目查询参数
 *
 * <AUTHOR>
 * @date 2024-06-13
 */
@Data
public class ItemsQuery {

    /** 收费项目简称 */
    private String chargeAbb;

    /** 收费项目名称 */
    private String chargeName;

    /** 进出港类型 */
    private String type;

    /** 进出港类型 */
    private String operationType;

    /** 状态 */
    private Integer status;

    /** 运单号 */
    private String waybillCode;

    /** 理货id */
    private String tallyId;

    /** 有效时间 */
    private Date effectiveTime;
}
