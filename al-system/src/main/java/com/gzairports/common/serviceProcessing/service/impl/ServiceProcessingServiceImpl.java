package com.gzairports.common.serviceProcessing.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.business.arrival.domain.HzArrItem;
import com.gzairports.common.business.arrival.domain.HzArrTally;
import com.gzairports.common.business.arrival.domain.PickUp;
import com.gzairports.common.business.arrival.domain.PickUpWaybill;
import com.gzairports.common.business.arrival.mapper.HzArrItemMapper;
import com.gzairports.common.business.arrival.mapper.HzArrTallyMapper;
import com.gzairports.common.business.arrival.mapper.PickUpMapper;
import com.gzairports.common.business.arrival.mapper.PickUpWaybillMapper;
import com.gzairports.common.business.arrival.service.impl.PickUpWaybillServiceImpl;
import com.gzairports.common.charge.domain.HzChargeIrRelation;
import com.gzairports.common.charge.domain.HzChargeItems;
import com.gzairports.common.charge.domain.HzChargeRule;
import com.gzairports.common.charge.mapper.HzChargeIrRelationMapper;
import com.gzairports.common.charge.mapper.HzChargeItemsMapper;
import com.gzairports.common.charge.mapper.HzChargeRuleMapper;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.serviceProcessing.domain.ServiceProcessing;
import com.gzairports.common.serviceProcessing.domain.query.ServiceProcessingQuery;
import com.gzairports.common.serviceProcessing.domain.vo.ServiceProcessingPayVo;
import com.gzairports.common.serviceProcessing.domain.vo.ServiceProcessingVo;
import com.gzairports.common.serviceProcessing.service.IServiceProcessingService;
import com.gzairports.common.utils.*;
import com.gzairports.common.utils.http.HttpUtils;
import com.gzairports.common.utils.sign.Base64;
import com.gzairports.hz.business.arrival.domain.HzArrRecordOrder;
import com.gzairports.hz.business.arrival.mapper.HzArrRecordOrderMapper;
import com.gzairports.hz.business.departure.domain.AirWaybill;
import com.gzairports.hz.business.departure.mapper.AllAirWaybillMapper;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.NoSuchAlgorithmException;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

/**
 * @author: lan
 * @Desc: 自助办单业务层
 * @create: 2024-11-04 14:29
 **/

@Service
public class ServiceProcessingServiceImpl implements IServiceProcessingService {
    @Autowired
    private AllAirWaybillMapper airWaybillMapper;

    @Autowired
    private HzArrRecordOrderMapper hzArrRecordOrderMapper;

    @Autowired
    private HzArrTallyMapper hzArrTallyMapper;

    @Autowired
    private HzArrItemMapper itemMapper;

    @Autowired
    private PickUpWaybillMapper pickUpWaybillMapper;

    @Autowired
    private PickUpMapper pickUpMapper;

    @Autowired
    private PickUpWaybillServiceImpl waybillService;

    @Autowired
    private HzChargeIrRelationMapper chargeIrRelationMapper;

    @Autowired
    private HzChargeItemsMapper hzChargeItemsMapper;

    @Autowired
    private HzChargeRuleMapper hzChargeRuleMapper;

    @Value(value = "${comparison.appId}")
    private String appId;

    @Value(value = "${comparison.appKey}")
    private String appKey;

    @Value(value = "${comparison.publicKey}")
    private String publicKey;

    private static final ThreadLocalRandom RANDOM = ThreadLocalRandom.current();

    /**
     * @author: lan
     * @description: 自助办单提货办单
     * @param: [serviceProcessing]
     * @date: 2024/11/4
     * @return: com.gzairports.common.serviceProcessing.domain.vo.ServiceProcessingVo
     */
    @Override
    public ServiceProcessingVo pickUpOne(ServiceProcessing serviceProcessing) {
        String waybillCode = serviceProcessing.getWaybillCode();
        if (waybillCode.length() != 11) {
            throw new CustomException("请输入正确的运单号格式,例如:11122222222");
        }
        AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                .likeLeft("waybill_code", waybillCode)
                .eq("consign", serviceProcessing.getName())
                .eq("consign_phone", serviceProcessing.getPhoneNumber())
                .eq("consign_id_car", serviceProcessing.getIdCard())
                .eq("type", "ARR"));
        if (airWaybill == null) {
            throw new CustomException("无当前运单信息,请核对");
        }
        HzArrRecordOrder order = hzArrRecordOrderMapper.selectOne(new QueryWrapper<HzArrRecordOrder>()
                .eq("waybill_code", airWaybill.getWaybillCode()));
        List<HzArrTally> hzArrTallyList = hzArrTallyMapper.selectList(new QueryWrapper<HzArrTally>()
                .eq("record_order_id", order.getId()));
        List<HzArrItem> item = itemMapper.selectList(new QueryWrapper<HzArrItem>()
                .eq("waybill_code", order.getWaybillCode())
                .eq("order_id", order.getId()));
        ServiceProcessingVo serviceProcessingVo = new ServiceProcessingVo();
        //未理货 就返回两个字段 其他为空
        if ("record_order".equals(airWaybill.getStatus())) {
            serviceProcessingVo.setWaybillCode(waybillCode);
            serviceProcessingVo.setCargoName(airWaybill.getCargoName());
            serviceProcessingVo.setPickUpCount(0);
            serviceProcessingVo.setPickUpPiece(0);
            serviceProcessingVo.setPickUpWeight(BigDecimal.ZERO);
            return serviceProcessingVo;
        }

        //已理货 无论是否保存或者结算也返回数据
        serviceProcessingVo.setWaybillCode(waybillCode);
        serviceProcessingVo.setCargoName(airWaybill.getCargoName());
        serviceProcessingVo.setPickUpCount(hzArrTallyList.size());
        int sum = hzArrTallyList.stream().map(HzArrTally::getPieces).mapToInt(Integer::intValue).sum();
        serviceProcessingVo.setPickUpPiece(sum);
        BigDecimal reduce = hzArrTallyList.stream().map(HzArrTally::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
        serviceProcessingVo.setPickUpWeight(reduce);
        item.forEach(e -> {
            HzChargeIrRelation hzChargeIrRelation = chargeIrRelationMapper.selectById(e.getIrId());
            HzChargeItems hzChargeItems = hzChargeItemsMapper.selectById(hzChargeIrRelation.getItemId());
            HzChargeRule hzChargeRule = hzChargeRuleMapper.selectById(hzChargeIrRelation.getRuleId());
            e.setChargeName(hzChargeItems.getChargeName());
            e.setRuleName(hzChargeRule.getRuleName());
        });
        serviceProcessingVo.setItems(item);
        BigDecimal totalCost = item.stream().map(HzArrItem::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
        serviceProcessingVo.setTotalCost(totalCost);
        //费用那边的理货历史id都是同一条理货历史的
        if(item.size() > 0){
            serviceProcessingVo.setTallyId(item.get(0).getTallyId());
        }else{
            serviceProcessingVo.setTallyId(hzArrTallyList.get(0).getId());
        }

        return serviceProcessingVo;
    }

    /**
     * @author: lan
     * @description: 自助办单支付
     * @param: [serviceProcessingQuery]
     * @date: 2024/11/5
     * @return: com.gzairports.common.serviceProcessing.domain.vo.ServiceProcessingPayVo
     */
    @Override
    public ServiceProcessingPayVo pay(ServiceProcessingQuery serviceProcessingQuery) {
        String waybillCode = serviceProcessingQuery.getWaybillCode();
        HzArrTally hzArrTally = hzArrTallyMapper.selectById(serviceProcessingQuery.getTallyId());
        if (StringUtils.isNull(hzArrTally)) {
            throw new CustomException("抱歉，当前运单没有提货数据暂不可办单");
        }
        HzArrRecordOrder order = hzArrRecordOrderMapper.selectOne(new QueryWrapper<HzArrRecordOrder>()
                .eq("id", hzArrTally.getRecordOrderId()));
        List<HzArrTally> hzArrTallyList = hzArrTallyMapper.selectList(new QueryWrapper<HzArrTally>()
                .eq("record_order_id", order.getId()));
        List<HzArrItem> item = itemMapper.selectList(new QueryWrapper<HzArrItem>()
                .eq("waybill_code", order.getWaybillCode())
                .eq("order_id", order.getId()));

        List<PickUpWaybill> pickUpWaybills = pickUpWaybillMapper.selectList(new QueryWrapper<PickUpWaybill>()
                .eq("waybill_code", "AWBA" + waybillCode));
        //还未保存 先去保存
        if (CollectionUtils.isEmpty(pickUpWaybills)) {
            BigDecimal totalCost = item.stream().map(HzArrItem::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
            PickUp up = new PickUp();
            BigDecimal totalWeight = hzArrTallyList.stream().map(HzArrTally::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            up.setTotalWeight(totalWeight);
            Integer totalQuantity = hzArrTallyList.stream().map(HzArrTally::getPieces).reduce(0, Integer::sum);
            up.setTotalQuantity(totalQuantity);
            up.setSerialNo(SerialNumberGenerator.generateSerialNumber());
            up.setHandleBy("自助办单");
            up.setTotalCost(totalCost);
            up.setTotalChargeWeight(totalWeight);
            up.setTotalCount(1);
            up.setPickUpTime(new Date());
            up.setUpdateTime(new Date());
            pickUpMapper.insert(up);

            hzArrTallyList.forEach(e -> {
                e.setStatus("save");
                hzArrTallyMapper.updateById(e);
            });

            PickUpWaybill waybill = new PickUpWaybill();
            waybill.setPickUpId(up.getId());
            if(item.size() > 0){
                waybill.setTallyId(item.get(0).getTallyId());
            }else{
                waybill.setTallyId(hzArrTallyList.get(0).getId());
            }
            waybill.setWaybillCode("AWBA" + waybillCode);
            waybill.setCanPickUpWeight(totalWeight);
            waybill.setCanPickUpQuantity(totalQuantity);
            waybill.setCostSum(totalCost);
            waybill.setCreateBy(SecurityUtils.getUsername());
            waybill.setCreateTime(new Date());
            waybillService.save(waybill);
        }

        PickUpWaybill pickUpWaybill = pickUpWaybillMapper.selectOne(new QueryWrapper<PickUpWaybill>()
                .eq("waybill_code", order.getWaybillCode()));
        PickUp pickUp = pickUpMapper.selectById(pickUpWaybill.getPickUpId());
        ServiceProcessingPayVo vo = new ServiceProcessingPayVo();
        vo.setPickUpId(pickUp.getId());
        //已结算
        if (pickUp.getIsPay() == 1) {
            vo.setCode("该运单已结算");
            return vo;
        }
        //未结算 生成二维码返回 并修改提货信息为已结算
        //todo 这里如何判断是否已支付 先假定会支付
        try {
            String s = QRCodeGenerator.generateQRCodeBase64(pickUp.getId().toString());
            vo.setCode(s);
        } catch (Exception e) {
            e.printStackTrace();
        }
        String code = generateUniqueCode();
        Date date = new Date();
        pickUp.setIsPay(1);
        pickUp.setPayTime(date);
        pickUp.setPickUpCode(code);
        pickUp.setOutTime(date);
        pickUp.setUpdateTime(new Date());
        pickUpMapper.updateById(pickUp);
        return vo;
    }

    private String generateUniqueCode(){
        String code;
        int attempts = 0;
        do {
            if (attempts++ > 50){
                throw new CustomException("生成提货码失败，请稍后再试");
            }
            code = String.format("%06d", RANDOM.nextInt(1_000_000));
        }while (isCodeUsed(code));
        return code;
    }

    private boolean isCodeUsed(String code) {
        return pickUpMapper.existsByCodeAndTime(code, new Date());
    }

    @Override
    public ServiceProcessingPayVo printOutOrder(Long pickUpId) {
        PickUp pickUp = pickUpMapper.selectById(pickUpId);
        ServiceProcessingPayVo vo = new ServiceProcessingPayVo();
        vo.setPickUpCode(pickUp.getPickUpCode());
        try {
            String s = QRCodeGenerator.generateQRCodeBase64(pickUp.getPickUpCode());
            vo.setCode(s);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return vo;
    }

    private static final  String url ="https://172.15.4.71:18443/faceAdmin/api/open";

    @Override
    public String comparison(ServiceProcessingQuery vo) {
        Map<String, String> header = new HashMap<>();
        header.put("appId", appId);

        JSONObject json = new JSONObject();
        json.put("appKey", appKey);
        json.put("msg_id", "F0001");

        try {
            if (StringUtils.isNotEmpty(vo.getFaceUrl())) {
                byte[] bytes = downloadFileFromUrl(vo.getFaceUrl());
                String faceUrl = Base64.encode(bytes);
//                json.put("face", faceUrl);
                json.put("face", "data:image/png;base64," + faceUrl);
            }
            if (StringUtils.isNotEmpty(vo.getIdCardUrl())) {
                byte[] bytes = downloadFileFromUrl(vo.getIdCardUrl());
                String idCardUrl = Base64.encode(bytes);
//                json.put("idCard", idCardUrl);
                json.put("idCard", "data:image/png;base64," + idCardUrl);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        RSAPublicKey publicKey1 = null;
        try {
            publicKey1 = RSAUtils.getPublicKey(publicKey);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        } catch (InvalidKeySpecException e) {
            throw new RuntimeException(e);
        }

        RequestBody body = RequestBody.create(JSONS, JSON.toJSONString(header, String.valueOf(false)));
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();
        executeCall(request);


        String decrypt = RSAUtils.publicEncrypt(json.toJSONString(), publicKey1);
        String res = HttpUtils.sendSSLPost(url, decrypt, header);
        JSONObject jsonObject = JSON.parseObject(res);
        String data = jsonObject.getString("data");
        System.out.println("--------------------------------------------" +
                "人脸返回结果11111111111" + data +
                "-----------------------------------------------------");
        String resObj = RSAUtils.publicDecrypt(data, publicKey1);
        System.out.println("--------------------------------------------" +
                "人脸返回结果222222222222" + resObj +
                "-----------------------------------------------------");
        return resObj;
    }

    private void executeCall(Request request){

        OK_HTTP_CLIENT.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                log.info("通讯异常 url:" + request.url(), e);
            }

            @Override
            public void onResponse(Call call, Response response) {
                response.close();
                System.out.println(LocalDateTime.now() + "发送成功，发送目的为" + request.url());
            }
        });
    }

    public static final MediaType JSONS = MediaType.parse("application/json; charset=utf-8");

    private static final Logger log = LoggerFactory.getLogger(ServiceProcessingServiceImpl.class);

    /**
     * 不要每次都New一个OKHttpClient，而是把OkHttpClient写成一个静态的final变量，或者使用单例动态创建。其时OkHttpClient本身是一个线程池，每次都new OkHttpClient 就相当于new一个线程池出来，所以会OOM
     */
    private static final OkHttpClient OK_HTTP_CLIENT = new OkHttpClient().newBuilder()
            .connectTimeout(60, TimeUnit.SECONDS)
            .readTimeout(5 * 60, TimeUnit.SECONDS)
            .writeTimeout(5 * 60, TimeUnit.SECONDS)
            .build();

    private byte[] downloadFileFromUrl(String urlStr) throws Exception {
        URL url = new URL(urlStr);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(5000);
        connection.setReadTimeout(5000);

        if (connection.getResponseCode() != 200) {
            throw new RuntimeException("Failed to connect, HTTP error code : " + connection.getResponseCode());
        }

        try (InputStream inputStream = connection.getInputStream();
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            return baos.toByteArray();
        }
    }
}
