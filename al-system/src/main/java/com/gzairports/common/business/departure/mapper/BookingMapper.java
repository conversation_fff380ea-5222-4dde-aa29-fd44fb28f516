package com.gzairports.common.business.departure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.departure.domain.Booking;
import com.gzairports.common.business.departure.domain.query.BookingQuery;
import com.gzairports.common.business.departure.domain.vo.BookingVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 订舱Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@Mapper
public interface BookingMapper extends BaseMapper<Booking> {

    /**
     * 查询订舱列表
     * @param query 查询参数
     * @return 订舱列表
     */
    List<BookingVo> selectListByQuery(BookingQuery query);
}
