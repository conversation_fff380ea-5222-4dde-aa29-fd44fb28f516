package com.gzairports.common.bankUtils;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;

/**
 * @author: lan
 * @Desc:
 * @create: 2025-03-20 17:06
 **/

@Data
public class KeyPath {
    private String bankPublicKey;

    private String publicKey;

    private String privateKey;

    private String pwd;

    public KeyPath(String bankPublicKey, String publicKey, String privateKey, String pwd) {
        this.bankPublicKey = bankPublicKey;
        this.publicKey = publicKey;
        this.privateKey = privateKey;
        this.pwd = pwd;
    }

    public KeyPath() {
    }
}
