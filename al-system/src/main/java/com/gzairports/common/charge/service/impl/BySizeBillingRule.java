package com.gzairports.common.charge.service.impl;

import com.gzairports.common.business.arrival.domain.HzArrItem;
import com.gzairports.common.business.arrival.domain.ItemDetail;
import com.gzairports.common.charge.domain.HzChargeItemRule;
import com.gzairports.common.charge.domain.vo.BillRuleVo;
import com.gzairports.common.charge.service.BillingRule;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.List;

/**
 * 按件大小计费规则
 * <AUTHOR>
 * @date 2024-08-03
 */
public class BySizeBillingRule implements BillingRule {

    @Override
    public String className() {
        return "BySizeBillingRule.class";
    }

    @Override
    public BillRuleVo calculateFee(List<HzChargeItemRule> rule, BigDecimal weight, Integer quantity, ItemDetail item) {
        BillRuleVo vo = new BillRuleVo();
        HzChargeItemRule itemRule = rule.get(0);
        if (item.getSmallItem() == null && item.getLargeItem() == null && item.getSuperLargeItem() == null){
            return vo;
        }
        BigDecimal small = itemRule.getSmallPrice() == null ? new BigDecimal(0) : itemRule.getSmallPrice();
        BigDecimal large = itemRule.getLargePrice() == null ? new BigDecimal(0) : itemRule.getLargePrice();
        BigDecimal superLarge = itemRule.getExtraLargePrice() == null ? new BigDecimal(0) : itemRule.getExtraLargePrice();
        BigDecimal smallPrice = small.multiply(new BigDecimal(item.getSmallItem()));
        BigDecimal largePrice = large.multiply(new BigDecimal(item.getLargeItem()));
        BigDecimal superLargePrice = superLarge.multiply(new BigDecimal(item.getSuperLargeItem()));
        BigDecimal totalCharge = smallPrice.add(largePrice).add(superLargePrice);
        vo.setTotalCharge(totalCharge);
        int i = item.getSmallItem() + item.getLargeItem() + item.getSuperLargeItem();
        vo.setQuantity(String.valueOf(i));
        return vo;
    }
}
