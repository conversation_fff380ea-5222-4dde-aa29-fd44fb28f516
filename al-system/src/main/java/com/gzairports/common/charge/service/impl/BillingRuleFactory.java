package com.gzairports.common.charge.service.impl;

import com.gzairports.common.charge.service.BillingRule;

import java.util.HashMap;
import java.util.Map;

/**
 * BillingRuleFactory类创建实例化计费规则
 * <AUTHOR>
 * @date 2024-08-05
 */
public class BillingRuleFactory {
    private static final Map<String, BillingRule> RULES = new HashMap<>();
    static {
        RULES.put("TimeSegmentBillingRule.class", new TimeSegmentBillingRule());
        RULES.put("FlatRateBillingRule.class", new FlatRateBillingRule());
        RULES.put("PostFreeStorageBillingRule.class", new PostFreeStorageBillingRule());
        RULES.put("NextDayStartBillingRule.class", new NextDayStartBillingRule());
        RULES.put("PerUnitBillingRule.class", new PerUnitBillingRule());
        RULES.put("BySizeBillingRule.class", new BySizeBillingRule());
        RULES.put("WeightRangeBillingRule.class", new WeightRangeBillingRule());
        RULES.put("ColdStorageBillingRule.class", new ColdStorageBillingRule());
        RULES.put("PointTimeBillingRule.class", new PointTimeBillingRule());
        RULES.put("PostFreeDayBillingRule.class", new PostFreeDayBillingRule());
    }

    public static BillingRule createRule(String className) {
        return RULES.get(className);
    }
}
