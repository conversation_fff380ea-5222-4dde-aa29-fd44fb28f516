package com.gzairports.wl.ticket.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 单证控制管理表
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Data
@TableName(value = "wl_ticket_ctrl")
public class TicketCtrl  {

    /** 主键id */
    private Long id;

    /** 票证类型 */
    private String code;

    /** 票证类型中文 */
    @TableField(exist = false)
    private String name;

    /** 是否启用控制 */
    private Integer controlEnabled;

    /** 适用的运单前缀 */
    private String prefix;

    /** 所属单位 */
    private Long deptId;

    /** 启用单证控制的部门id集合 */
    private String deptIds;

    /** 国际国内 */
    private String domint;

}
