package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BaseVolume;
import com.gzairports.common.basedata.domain.query.VolumeUnitQuery;
import com.gzairports.common.basedata.service.IVolumeService;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 多体积单位Controller
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@RestController
@RequestMapping("/base/volume")
@Api(tags = "多体积单位数据接口")
public class VolumeController extends BaseController {

    @Autowired
    private IVolumeService volumeService;

    /**
     * 查询多体积单位列表
     */
//    @PreAuthorize("@ss.hasPermi('base:volume:volumeUnitList')")
    @GetMapping("/volumeUnitList")
    @ApiOperation(value = "查询多体积单位列表")
    public TableDataInfo volumeUnitList(VolumeUnitQuery query)
    {
        startPage();
        List<BaseVolume> list = volumeService.selectVolumeUnitList(query);
        return getDataTable(list);
    }

    /**
     * 导出多体积单位
     */
    @PreAuthorize("@ss.hasPermi('base:volume:exportVolumeUnit')")
    @Log(title = "导出多体积单位", businessType = BusinessType.EXPORT)
    @PostMapping("/exportVolumeUnit")
    @ApiOperation(value = "导出多体积单位")
    public void exportVolumeUnit(HttpServletResponse response, VolumeUnitQuery query)
    {
        List<BaseVolume> list = volumeService.selectVolumeUnitList(query);
        ExcelUtil<BaseVolume> util = new ExcelUtil<BaseVolume>(BaseVolume.class);
        util.exportExcel(response, list, "多体积单位");
    }

    /**
     * 导出多体积单位下载模板
     */
    @PreAuthorize("@ss.hasPermi('base:volume:exportVolumeUnit')")
    @Log(title = "导出多体积单位下载模板", businessType = BusinessType.EXPORT)
    @PostMapping("/exportVolumeUnitNull")
    @ApiOperation(value = "导出多体积单位下载模板")
    public void exportVolumeUnitNull(HttpServletResponse response)
    {
        List<BaseVolume> list = new ArrayList<>();
        ExcelUtil<BaseVolume> util = new ExcelUtil<BaseVolume>(BaseVolume.class);
        util.exportExcel(response, list, "多体积单位模板");
    }

    /**
     * 导入体积单位
     */
    @PreAuthorize("@ss.hasPermi('base:volume:importVolume')")
    @Log(title = "导入体积单位", businessType = BusinessType.IMPORT)
    @PostMapping("/importVolume")
    @ApiOperation(value = "导入体积单位")
    public AjaxResult importVolume(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<BaseVolume> util = new ExcelUtil<BaseVolume>(BaseVolume.class);
        List<BaseVolume> volumes = util.importExcel(file.getInputStream());
        String message = volumeService.importVolume(volumes, updateSupport);
        return success(message);
    }

    /**
     * 新增体积单位
     */
    @PreAuthorize("@ss.hasPermi('base:volume:addVolume')")
    @Log(title = "新增体积单位", businessType = BusinessType.INSERT)
    @PostMapping("/addVolume")
    @ApiOperation(value = "新增体积单位")
    public AjaxResult addVolume(@RequestBody BaseVolume volume)
    {
        return toAjax(volumeService.addVolume(volume));
    }

    /**
     * 修改体积单位
     */
    @PreAuthorize("@ss.hasPermi('base:volume:editVolume')")
    @Log(title = "修改体积单位", businessType = BusinessType.UPDATE)
    @PostMapping("/editVolume")
    @ApiOperation(value = "修改体积单位")
    public AjaxResult editVolume(@RequestBody BaseVolume volume)
    {
        return toAjax(volumeService.editVolume(volume));
    }

    /**
     * 删除体积单位
     */
    @PreAuthorize("@ss.hasPermi('base:volume:delVolume')")
    @Log(title = "删除体积单位", businessType = BusinessType.UPDATE)
    @GetMapping("/delVolume/{ids}")
    @ApiOperation(value = "删除体积单位")
    public AjaxResult delVolume(@PathVariable("ids") Long[] ids)
    {
        return toAjax(volumeService.delVolume(ids));
    }
}
