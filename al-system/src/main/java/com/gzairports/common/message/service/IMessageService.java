package com.gzairports.common.message.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.message.domain.Message;
import com.gzairports.common.message.domain.query.MessageQuery;

import java.util.List;

/**
 * 消息管理Service接口
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
public interface IMessageService extends IService<Message> {

    /**
     * 获取消息详情
     * @param id 消息id
     * @return 详情
     */
    Message getInfo(Long id);

    /**
     * 查询消息列表
     * @param query 查询参数
     * @return 列表
     */
    List<Message> selectList(MessageQuery query);
}
