package com.gzairports.hz.business.reporter.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @author: lan
 * @Desc: 货站报表设置表
 * @create: 2025-03-05 17:00
 **/

@Data
@TableName("hz_report_set")
public class HzReportSet {
    /** 报表设置id */
    private Long id;

    /** 报表标题 */
    private String reportTitle;

    /** 报表国内国际类型 I国内D国际 */
    private String reportDomint;

    /** 报表进出港类型 */
    private String reportType;

    /** 报表可查看角色 */
    private String reportRole;

    /** 报表状态 0未发布 1已发布 */
    private Integer reportStatus;

    /** 是否为外链（0是 1否） */
    private String isFrame;

    /**页面地址 */
    private String pageSite;

    /** 删除状态 */
    private Integer isDel;

}
