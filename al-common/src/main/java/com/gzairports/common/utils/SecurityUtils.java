package com.gzairports.common.utils;

import cn.hutool.core.util.StrUtil;
import com.gzairports.common.constant.HttpStatus;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.exception.ServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.util.regex.Pattern;

/**
 * 安全服务工具类
 * 
 * <AUTHOR>
 */
public class SecurityUtils
{
    /**
     * 用户ID
     **/
    public static Long getUserId()
    {
        try
        {
            return getLoginUser().getUserId();
        }
        catch (Exception e)
        {
            throw new ServiceException("获取用户ID异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取部门ID
     **/
    public static Long getDeptId()
    {
        try
        {
            return getLoginUser().getDeptId();
        }
        catch (Exception e)
        {
            throw new ServiceException("获取部门ID异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 獲取最高父id
     **/
    public static Long getHighParentId()
    {
        try
        {
            return getLoginUser().getHighParentId();
        }
        catch (Exception e)
        {
            throw new ServiceException("获取最高父id异常", HttpStatus.UNAUTHORIZED);
        }
    }
    
    /**
     * 获取用户账户
     **/
    public static String getUsername()
    {
        try
        {
            return getLoginUser().getUsername();
        }
        catch (Exception e)
        {
            throw new ServiceException("获取用户账户异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取用户昵称
     **/
    public static String getNickName()
    {
        try
        {
            return getLoginUser().getUser().getNickName();
        }
        catch (Exception e)
        {
            throw new ServiceException("获取用户账户异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取用户
     **/
    public static LoginUser getLoginUser()
    {
        try
        {
            return (LoginUser) getAuthentication().getPrincipal();
        }
        catch (Exception e)
        {
            throw new ServiceException("获取用户信息异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取Authentication
     */
    public static Authentication getAuthentication()
    {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    /**
     * 生成BCryptPasswordEncoder密码
     *
     * @param password 密码
     * @return 加密字符串
     */
    public static String encryptPassword(String password)
    {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.encode(password);
    }

    /**
     * 判断密码是否相同
     *
     * @param rawPassword 真实密码
     * @param encodedPassword 加密后字符
     * @return 结果
     */
    public static boolean matchesPassword(String rawPassword, String encodedPassword)
    {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    /**
     * 是否为管理员
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public static boolean isAdmin(Long userId)
    {
        return userId != null && 1L == userId;
    }

    /**
     * 是否为管理员
     *
     * @param userName 用户姓名
     * @return 结果
     */
    public static boolean isWlAdmin(String userName)
    {
        return ("admin".equals(userName) || "subadmin".equals(userName));
    }

    // 定义密码正则规则
//    private static final String PASSWORD_REGEX =
//            "^(?=.*[A-Za-z])(?=.*\\\\d)(?=.*[@$!%*#?&])[A-Za-z\\\\d@$!%*#?&]+$"; // @$!%*#?&

    //包含字母
    private static final Pattern LETTER_PATTERN = Pattern.compile(".*[A-Za-z].*");

    //包含数字
    private static final Pattern DIGIT_PATTERN = Pattern.compile(".*\\d.*");

    //包含特殊字符
    private static final Pattern SPECIAL_CHAR_PATTERN = Pattern.compile(".*[@$!%*#?&].*");

    /**
     * 校验密码是否符合规则,不符合则抛出对应的异常
     *
     * @param password 待校验的密码
     */
    public static void checkPwd(String password) {
        if (StrUtil.isBlank(password) || password.length() < 8) {
            throw new CustomException("密码必须至少8位");
        }
        if (!DIGIT_PATTERN.matcher(password).matches()) {
            throw new CustomException("密码必须包含至少一个数字");
        }

        if (!LETTER_PATTERN.matcher(password).matches()) {
            throw new CustomException("密码必须包含至少一个字母");
        }

        if (!SPECIAL_CHAR_PATTERN.matcher(password).matches()) {
            throw new CustomException("密码必须包含至少一个特殊字符（@$!%*#?&）");
        }

    }
}
