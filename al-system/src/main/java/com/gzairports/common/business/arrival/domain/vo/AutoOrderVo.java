package com.gzairports.common.business.arrival.domain.vo;

import com.gzairports.common.business.arrival.domain.HzArrItem;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 自动办单机办单（办理提货）数据
 *
 * <AUTHOR>
 * @date 2024-07-22
 */
@Data
public class AutoOrderVo {

    /** 运单号 */
    private String waybillCode;

    /** 品名 */
    private String cargoName;

    /** 提货次数 */
    private Integer tallyNum;

    /** 提货件数 */
    private Integer tallyPieces;

    /** 提货重量 */
    private BigDecimal tallyWeight;

    /** 费用明细 */
    private List<HzArrItem> items;

    /** 合计 */
    private BigDecimal totalCostSum;

}
