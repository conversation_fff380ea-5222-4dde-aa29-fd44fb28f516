package com.gzairports.web.controller.common;

import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;

import com.gzairports.common.business.arrival.domain.query.AutoOrderQuery;
import com.gzairports.common.business.arrival.service.IPickUpService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.FastByteArrayOutputStream;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.google.code.kaptcha.Producer;
import com.gzairports.common.config.RuoYiConfig;
import com.gzairports.common.constant.CacheConstants;
import com.gzairports.common.constant.Constants;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.redis.RedisCache;
import com.gzairports.common.utils.sign.Base64;
import com.gzairports.common.utils.uuid.IdUtils;
import com.gzairports.common.system.service.ISysConfigService;

/**
 * 验证码操作处理
 * 
 * <AUTHOR>
 */
@RestController
public class CaptchaController
{
    @Resource(name = "captchaProducer")
    private Producer captchaProducer;

    @Resource(name = "captchaProducerMath")
    private Producer captchaProducerMath;

    @Autowired
    private RedisCache redisCache;
    
    @Autowired
    private ISysConfigService configService;

    @Autowired
    private IPickUpService pickUpService;

    /**
     * 生成验证码
     */
    @GetMapping("/captchaImage")
    public AjaxResult getCode(HttpServletResponse response) throws IOException
    {
        AjaxResult ajax = AjaxResult.success();
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        ajax.put("captchaEnabled", captchaEnabled);
        if (!captchaEnabled)
        {
            return ajax;
        }

        // 保存验证码信息
        String uuid = IdUtils.simpleUUID();
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuid;

        String capStr = null, code = null;
        BufferedImage image = null;

        // 生成验证码
        String captchaType = RuoYiConfig.getCaptchaType();
        if ("math".equals(captchaType))
        {
            String capText = captchaProducerMath.createText();
            capStr = capText.substring(0, capText.lastIndexOf("@"));
            code = capText.substring(capText.lastIndexOf("@") + 1);
            image = captchaProducerMath.createImage(capStr);
        }
        else if ("char".equals(captchaType))
        {
            capStr = code = captchaProducer.createText();
            image = captchaProducer.createImage(capStr);
        }

        redisCache.setCacheObject(verifyKey, code, Constants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);
        // 转换流信息写出
        FastByteArrayOutputStream os = new FastByteArrayOutputStream();
        try
        {
            ImageIO.write(image, "jpg", os);
        }
        catch (IOException e)
        {
            return AjaxResult.error(e.getMessage());
        }

        ajax.put("uuid", uuid);
        ajax.put("img", Base64.encode(os.toByteArray()));
        return ajax;
    }

    /**
     * 获取验证码
     */
    @PostMapping("/getCode")
    @ApiOperation(value = "散客H5提货办单（获取验证码）")
    public AjaxResult getCode(@RequestBody AutoOrderQuery query){
        return AjaxResult.success(pickUpService.sendCode(query));
    }


    /**
     * 验证验证码
     */
    @PostMapping("/verifyCode")
    @ApiOperation(value = "验证验证码")
    public AjaxResult verifyCode(@RequestBody AutoOrderQuery query){
        return AjaxResult.success(pickUpService.verifyCode(query));
    }
}
