package com.gzairports.common.basedata.service;


import com.gzairports.common.basedata.domain.BaseCargoCategory;
import com.gzairports.common.basedata.domain.BaseCargoCode;
import com.gzairports.common.basedata.domain.query.CargoCodeQuery;
import com.gzairports.common.core.domain.TreeSelect;

import java.util.List;

/**
 * 货站货品代码Service接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
public interface ICargoCodeService  {

    /**
     * 查询货站货品代码列表
     *
     * @param query 查询参数
     * @return 货站货品代码列表
     */
    List<BaseCargoCode> selectCargoCodeList(CargoCodeQuery query);

    /**
     * 导入货站货品代码
     *
     * @param cargoCodes 货站货品代码列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    String importCargoCode(List<BaseCargoCode> cargoCodes, boolean updateSupport);

    /**
     * 新增货站货品代码
     *
     * @param cargoCode 货站货品代码
     * @return 结果
     */
    int addCargoCode(BaseCargoCode cargoCode);

    /**
     * 修改货站货品代码
     *
     * @param cargoCode 货站货品代码
     * @return 结果
     */
    int editCargoCode(BaseCargoCode cargoCode);

    /**
     * 删除货站货品代码
     *
     * @param ids 货站货品代码id集合
     * @return 结果
     */
    int delCargoCode(Long[] ids);

    /**
     * 根据代码查询货品信息
     *
     * @param code 货站货品代码code
     * @return 货站货品代码
     */
    BaseCargoCode selectByCode(String code);

    List<BaseCargoCategory> selectCargoCodeListTree();


    /**
     * 根据多个货物类别查询货品代码
     * */
    List<BaseCargoCode> selectCargoCodeBatch(String[] codes);

    /**
     * 根据多个货品代码查询货品信息
     * */
    List<BaseCargoCode> selectCodeBatch(String[] codes);
}
