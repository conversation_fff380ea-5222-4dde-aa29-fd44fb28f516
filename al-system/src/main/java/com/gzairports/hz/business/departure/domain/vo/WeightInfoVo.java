package com.gzairports.hz.business.departure.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 复重板箱详情返回数据
 * <AUTHOR>
 * @date 2024-07-08
 */
@Data
public class WeightInfoVo {

    /** 主键id */
    private Long id;

    /** 航班配载id */
    private Long flightLoadId;

    /** 组货板箱id */
    private Long groupUldId;

    /** 板箱号 */
    private String uld;

    /** 舱位 */
    private String cabin;

    /** 目的站 */
    private String des1;

    /** 货物件数 */
    private Integer quantity;

    /** 货物净重（复磅重量） */
    private BigDecimal weight;

    /** 板货总量 */
    private BigDecimal boardCargoWeight;

    /** 垫板重量 */
    private BigDecimal plateWeight;

    /** 板箱自重 */
    private BigDecimal boardWeight;

    /** 文件重量 */
    private BigDecimal fileWeight;

    /** 误差率 */
    private String errorRate;

    /** 总实际重量 */
    private BigDecimal totalRealityWeight;

    /** 总文件重量 */
    private BigDecimal totalFileWeight;
}
