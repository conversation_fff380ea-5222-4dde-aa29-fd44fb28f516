package com.gzairports.common.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gzairports.common.enums.FFMStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by david on 2025/6/11
 */
@Component
@Slf4j
public class FFMRedisOperator {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private static final Charset CHARSET = StandardCharsets.UTF_8;

    @Resource(name = "redisTemplate")
    private RedisTemplate<Object, Object> redisTemplate;

    private static final String KEY_PREFIX = "flight:ffm:";

    private String buildKey(Long flightId) {
        return KEY_PREFIX + flightId;
    }

    private byte[] toBytes(String str) {
        return str.getBytes(CHARSET);
    }

    public void updateData(Long flightId, FFMStatus status, List<AddressMsgVO> msgDataVo) throws JsonProcessingException {
        String key = buildKey(flightId);
        Map<byte[], byte[]> hash = new HashMap<>();
        hash.put("last_update".getBytes(), String.valueOf(System.currentTimeMillis()).getBytes());
        hash.put("status".getBytes(), status.name().getBytes());

        byte[] msgDataBytes = OBJECT_MAPPER.writeValueAsBytes(msgDataVo);
        hash.put("msg_data".getBytes(), msgDataBytes);

        redisTemplate.executePipelined((RedisCallback<Void>) connection -> {
            connection.hMSet(key.getBytes(), hash);
            connection.expire(key.getBytes(), (status == FFMStatus.CLOSED ? 1 : 3) * 60);
            return null;
        });
    }

    public List<AddressMsgVO> getMsgDataList(Long flightId) {
        String key = buildKey(flightId);
        byte[] dataBytes = redisTemplate.execute((RedisConnection connection) ->
                connection.hGet(toBytes(key), toBytes("msg_data")));

        if (dataBytes == null) {
            return Collections.emptyList();
        }

        try {
            return OBJECT_MAPPER.readValue(dataBytes, new TypeReference<List<AddressMsgVO>>() {});
        } catch (Exception e) {
            log.error("反序列化 MAN_DATA 出错", e);
            return Collections.emptyList();
        }
    }

    public void deleteData(Long flightId) {
        String key = buildKey(flightId);
        redisTemplate.delete(key);
    }
}
