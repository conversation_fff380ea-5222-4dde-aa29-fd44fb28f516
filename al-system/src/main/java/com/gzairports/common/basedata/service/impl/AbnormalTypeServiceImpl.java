package com.gzairports.common.basedata.service.impl;


import com.gzairports.common.basedata.domain.BaseAbnormalType;
import com.gzairports.common.basedata.domain.query.AbnormalTypeQuery;
import com.gzairports.common.basedata.mapper.AbnormalTypeMapper;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.exception.ServiceException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.bean.BeanValidators;
import com.gzairports.common.basedata.service.IAbnormalTypeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.Validator;
import java.util.List;

/**
 * 不正常类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Service
public class AbnormalTypeServiceImpl implements IAbnormalTypeService {

    private static final Logger log = LoggerFactory.getLogger(WeightServiceImpl.class);

    @Autowired
    private Validator validator;

    @Autowired
    private AbnormalTypeMapper abnormalTypeMapper;

    /**
     * 查询不正常类型列表
     *
     * @param query 查询参数
     * @return 不正常类型列表
     */
    @Override
    public List<BaseAbnormalType> selectAbnormalTypeList(AbnormalTypeQuery query) {
        return abnormalTypeMapper.selectAbnormalTypeList(query);
    }

    /**
     * 导入不正常类型
     *
     * @param abnormalTypes 不正常类型列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importAbnormalType(List<BaseAbnormalType> abnormalTypes, boolean updateSupport) {
        if (StringUtils.isNull(abnormalTypes) || abnormalTypes.size() == 0)
        {
            throw new ServiceException("导入货品代码映射数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (BaseAbnormalType type : abnormalTypes) {
            try {
                // 验证是否存在相同的不正常类型
                BaseAbnormalType byType = abnormalTypeMapper.selectAbnormalTypeByType(type.getAbnormalType());
                if (StringUtils.isNull(byType)) {
                    BeanValidators.validateWithException(validator, type);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    type.setCreateBy(loginUser.getUsername());
                    type.setCreateTime(DateUtils.getNowDate());
                    abnormalTypeMapper.insertAbnormalType(type);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、不正常类型 " + type.getAbnormalType() + " 导入成功");
                }
                else if (updateSupport) {
                    BeanValidators.validateWithException(validator, type);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    type.setId(byType.getId());
                    type.setUpdateBy(loginUser.getUsername());
                    type.setUpdateTime(DateUtils.getNowDate());
                    abnormalTypeMapper.updateAbnormalType(type);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、不正常类型 " + type.getAbnormalType() + " 更新成功");
                }
                else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、不正常类型 " + type.getAbnormalType() + " 已存在");
                }
            }catch (Exception e){
                failureNum++;
                String msg = "<br/>" + failureNum + "、不正常类型 " + type.getAbnormalType() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 新增不正常类型
     *
     * @param abnormalType 不正常类型
     * @return 结果
     */
    @Override
    public int addAbnormalType(BaseAbnormalType abnormalType) {
        //不正常类型abnormal_type不可重复
        if (StringUtils.isNotNull(abnormalTypeMapper.selectAbnormalTypeByType(abnormalType.getAbnormalType()))){
            throw new CustomException("不正常类型重复");
        }

        LoginUser loginUser = SecurityUtils.getLoginUser();
        abnormalType.setCreateBy(loginUser.getUsername());
        abnormalType.setCreateTime(DateUtils.getNowDate());
        return abnormalTypeMapper.insertAbnormalType(abnormalType);
    }

    /**
     * 修改不正常类型
     *
     * @param abnormalType 不正常类型
     * @return 结果
     */
    @Override
    public int editAbnormalType(BaseAbnormalType abnormalType) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        abnormalType.setUpdateBy(loginUser.getUsername());
        abnormalType.setUpdateTime(DateUtils.getNowDate());
        return abnormalTypeMapper.updateAbnormalType(abnormalType);
    }

    /**
     * 删除不正常类型
     *
     * @param ids 不正常类型id集合
     * @return 结果
     */
    @Override
    public int delAbnormalType(Long[] ids) {
        return abnormalTypeMapper.delAbnormalType(ids);
    }
}
