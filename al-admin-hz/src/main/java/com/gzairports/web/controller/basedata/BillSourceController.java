package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BaseBillSource;
import com.gzairports.common.basedata.service.IBillSourceService;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 票证来源Controller
 *
 * <AUTHOR>
 * @date 2024-05-17
 */
@RestController
@RequestMapping("/base/billSource")
@Api(tags = "票证来源数据接口")
public class BillSourceController extends BaseController {

    @Autowired
    private IBillSourceService billSourceService;

    /**
     * 查询票证来源列表
     */
//    @PreAuthorize("@ss.hasPermi('base:billSource:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询票证来源列表")
    public TableDataInfo list()
    {
        startPage();
        List<BaseBillSource> list = billSourceService.selectList();
        return getDataTable(list);
    }

    /**
     * 票证来源详情
     */
    @PreAuthorize("@ss.hasPermi('base:billSource:getInfo')")
    @GetMapping("/{id}")
    @ApiOperation(value = "票证来源详情")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(billSourceService.getInfo(id));
    }

    /**
     * 新增票证来源
     */
    @PreAuthorize("@ss.hasPermi('base:billSource:add')")
    @PostMapping
    @ApiOperation(value = "新增票证来源")
    public AjaxResult add(@RequestBody BaseBillSource baseBillSource)
    {
        return toAjax(billSourceService.insertSource(baseBillSource));
    }

    /**
     * 修改票证来源
     */
    @PreAuthorize("@ss.hasPermi('base:billSource:edit')")
    @PutMapping
    @ApiOperation(value = "修改票证来源")
    public AjaxResult edit(@RequestBody BaseBillSource baseBillSource)
    {
        return toAjax(billSourceService.updateSource(baseBillSource));
    }

    /**
     * 根据id查询票证类型和前缀
     */
    @PreAuthorize("@ss.hasPermi('base:billSource:byId')")
    @GetMapping("/byId/{id}")
    @ApiOperation(value = "根据id查询票证类型和前缀")
    public AjaxResult byId(@PathVariable("id") Long id)
    {
        return AjaxResult.success(billSourceService.byId(id));
    }

}
