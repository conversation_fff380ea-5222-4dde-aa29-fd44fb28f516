package com.gzairports.common.basedata.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.basedata.domain.BaseFlatbedTruck;
import com.gzairports.common.basedata.domain.query.FlatbedTruckQuery;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;
import java.util.List;

/**
 * 板车管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Mapper
public interface TruckMapper extends BaseMapper<BaseFlatbedTruck> {

    /**
     * 查询板车管理列表
     *
     * @param query 查询参数
     * @return 板车管理列表
     */
    List<BaseFlatbedTruck> flatbedTruckList(FlatbedTruckQuery query);

    /**
     * 新增板车
     *
     * @param truck 板车
     * @return 结果
     */
    int addFlatbedTruck(BaseFlatbedTruck truck);

    /**
     * 修改板车
     *
     * @param truck 板车
     * @return 结果
     */
    int editFlatbedTruck(BaseFlatbedTruck truck);

    /**
     * 删除板车
     *
     * @param ids 板车id集合
     * @return 结果
     */
    int removeFlatbedTruckByIds(Long[] ids);

    /**
     * 详情
     * @param id 板车id
     * @return 结果
     */
    BaseFlatbedTruck selectById(Long id);

    /**
     * 根据code查询板车
     * @param code 板车code
     * @return 结果
     */
    BaseFlatbedTruck selectByCode(String code);
}
