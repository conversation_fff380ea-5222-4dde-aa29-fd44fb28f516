package com.gzairports.hz.business.arrival.service;

import com.gzairports.common.business.arrival.domain.vo.HzArrItemVo;
import com.gzairports.hz.business.arrival.domain.query.CostWaybillQuery;
import com.gzairports.hz.business.arrival.domain.vo.ArrChargeExportNewVo;
import com.gzairports.hz.business.arrival.domain.vo.BillExportAWBMVo;
import com.gzairports.hz.business.arrival.domain.vo.BillExportVo;
import com.gzairports.hz.business.arrival.domain.vo.CostWaybillVo;

import java.util.List;

/**
 * 进港费用管理Service接口
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
public interface ICostWaybillService {

    List<CostWaybillVo> selectList(CostWaybillQuery query);

    /**
     * 费用明细数据
     * @param waybillCode 进港运单号
     * @return 结果
     */
    HzArrItemVo cost(String waybillCode,String serial);

    /**
     * <AUTHOR>
     * @description 进港费用导出 ->新模板
     * @date 2025/6/23
     */
    List<ArrChargeExportNewVo> chargeCostExport(CostWaybillQuery query);

    List<CostWaybillVo> selectListCount(CostWaybillQuery query);

    List<BillExportVo> billExport(CostWaybillQuery query);

    List<BillExportAWBMVo> billExportAWBM(CostWaybillQuery query);
}
