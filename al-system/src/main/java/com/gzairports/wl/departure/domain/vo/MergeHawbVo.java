package com.gzairports.wl.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 主分单拼单分单展示参数
 *
 * <AUTHOR>
 * @date 2024-05-24
 */
@Data
public class MergeHawbVo {

    /** 主键id */
    private Long id;

    /** 分单号 */
    private String waybillCode;

    /** 承运人 */
    private String carrier;

    /** 目的站 */
    private String desPort;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 计费重量 */
    private BigDecimal chargeWeight;

    /** 拼单件数 */
    private Integer mergeCount;

    /** 拼单重量 */
    private BigDecimal mergeWeight;

    /** 待拼单件数 */
    private Integer toMergeCount;

    /** 待拼单重量 */
    private BigDecimal toMergeWeight;

    /** 应收货主费用 */
    private BigDecimal costSum;

    /** 发货人 */
    private String shipper;

    /** 收货人 */
    private String consignee;

    /** 异地合作公司 */
    private String offsiteGtz;

    /** 预配航班 */
    private String flight1;

    /** 制单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date writeTime;

    /** 所属单位 */
    private String dept;
}
