<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.wl.ticket.mapper.TicketMapper">
    <update id="warehouse">
        update wl_ticket set status = 1, remark = #{remark} where id = #{id}
    </update>
    <update id="removeByTicketId">
        update wl_ticket set is_del = 1 where id = #{id}
    </update>
    <delete id="editRemark" parameterType="ticketNumQuery">
        update wl_ticket set remark = #{remark}, status = #{status} where id = #{id}
    </delete>
    <select id="selectListByQuery" resultType="com.gzairports.wl.ticket.domain.vo.TicketVO">
        select wlt.id,
               wlt.dept_id,
               wlt.bill_source,
               wlt.bill_type,
               wlt.bill_prefix,
               wlt.start_number,
               wlt.start_code,
               wlt.end_code,
               wlt.end_number,
               wlt.is_chartering,
               sd.dept_name,
               wlt.recorder,
               wlt.record_time,
               wlt.status
        from wl_ticket wlt
        left join sys_dept sd on sd.dept_id = wlt.dept_id
        <where>
            wlt.is_del = 0 and  wlt.dept_id = #{deptId}
            <if test="billSource != null and billSource != ''"> and wlt.bill_source = #{billSource}</if>
            <if test="billType != null and billType != ''">and wlt.bill_type = #{billType}</if>
            <if test="billPrefix != null and billPrefix != ''">and wlt.bill_prefix = #{billPrefix}</if>
            <if test="status != null">and wlt.status = #{status}</if>
            <if test="startTime != null">
                AND wlt.record_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND wlt.record_time &lt;= #{endTime}
            </if>
        </where>
       order by wlt.record_time desc
    </select>
    <select id="getInfo" resultType="com.gzairports.wl.ticket.domain.vo.TicketInfoVO">
        select wlt.id,
               wlt.bill_source,
               wlt.bill_type,
               wlt.bill_prefix,
               wlt.dept_id,
               wlt.start_number,
               wlt.end_number,
               wlt.start_code,
               wlt.end_code,
               wlt.is_chartering,
               wlt.remark
        from wl_ticket wlt
--                  left join base_bill_source bbs on bbs.id = wlt.bill_source_id
--                  left  join base_bill_type bbt on bbt.id = bbs.bill_type_id
        where wlt.id = #{id}
    </select>
    <select id="selectDetail" resultType="com.gzairports.wl.ticket.domain.vo.TicketDetailVO">
        select wlt.id,
               sd.parent_id as deptId,
               wlt.bill_source,
               wlt.bill_type,
               wlt.bill_prefix,
               wlt.start_number,
               wlt.end_number,
               wlt.start_code,
               wlt.end_code,
               wlt.is_chartering,
               sd.dept_name,
               wlt.recorder,
               wlt.record_time,
               wlt.status
        from wl_ticket wlt
--                  left join base_bill_source bbs on bbs.id = wlt.bill_source_id
--                  left  join base_bill_type bbt on bbt.id = bbs.bill_type_id
                 left join sys_dept sd on sd.dept_id = wlt.dept_id
        where wlt.id = #{id} and wlt.is_del = 0
    </select>
    <select id="selectCheck" resultType="java.lang.Long">
        select wlt.id
        from wl_ticket wlt
--             left join base_bill_source bbs on bbs.id = wlt.bill_source_id
--         left  join base_bill_type bbt on bbt.id = bbs.bill_type_id
        where
          wlt.bill_type = #{ticketType} and wlt.bill_prefix = #{prefix}
          and wlt.start_number <![CDATA[<=]]> #{ticketNum}
          AND wlt.end_number <![CDATA[>=]]> #{ticketNum}
          and wlt.dept_id = #{deptId}
          and wlt.is_del = 0
    </select>

    <select id="selectCheckHawb" resultType="java.lang.Long">
        select wlt.id
        from wl_ticket wlt
        where
            wlt.bill_type = 'HAWB'
          and wlt.start_number <![CDATA[<=]]> #{ticketNum}
          AND wlt.end_number <![CDATA[>=]]> #{ticketNum}
          and wlt.is_del = 0
    </select>
    <select id="selectListByDeptId" resultType="com.gzairports.wl.ticket.domain.vo.TicketVO">
        select wlt.id,
               wlt.bill_source_id,
               sd.dept_id as deptId,
               wlt.bill_source,
               wlt.bill_type,
               wlt.bill_prefix,
               wlt.start_number,
               wlt.end_number,
               wlt.is_chartering,
               sd.dept_name,
               wlt.recorder,
               wlt.record_time,
               wlt.status
        from wl_ticket wlt
--                  left join base_bill_source bbs on bbs.id = wlt.bill_source_id
--                  left  join base_bill_type bbt on bbt.id = bbs.bill_type_id
                 left join sys_dept sd on sd.dept_id = wlt.dept_id
        where wlt.dept_id = #{deptId} and wlt.is_del = 0
    </select>
    <select id="selectRepeatTicket" resultType="com.gzairports.wl.ticket.domain.Ticket">
        select wlt.*
        from wl_ticket wlt
        where wlt.is_del = 0 and wlt.bill_prefix = #{billPrefix}
          and wlt.bill_type = #{billType}
            and (
              CONCAT(#{startNumber},#{startCode})
                BETWEEN CONCAT(wlt.start_number,wlt.start_code) and CONCAT (wlt.end_number,wlt.end_code)
           or
            CONCAT(#{endNumber},#{endCode})
               BETWEEN CONCAT(wlt.start_number,wlt.start_code) and CONCAT (wlt.end_number,wlt.end_code))
    </select>
    <select id="selectHawbCheck" resultType="java.lang.Long">
        select wlt.id
        from wl_ticket wlt
        where
            bill_type = #{code}
          and wlt.start_number <![CDATA[<=]]> #{ticketNum}
          AND wlt.end_number <![CDATA[>=]]> #{ticketNum}
          and wlt.is_del = 0 limit 1
    </select>


</mapper>
