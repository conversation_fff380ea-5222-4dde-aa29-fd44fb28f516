<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.AbnormalTypeMapper">
    <select id="selectAbnormalTypeList" resultType="com.gzairports.common.basedata.domain.BaseAbnormalType">
        select id, abnormal_type, chinese_description, english_description, remark from base_abnormal_type
        <where>
            and is_del = 0
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="abnormalType != null  and abnormalType != ''"> and abnormal_type like concat('%',#{abnormalType},'%')</if>
            <if test="chineseDescription != null and chineseDescription != ''"> and chinese_description like concat('%',#{chineseDescription},'%')</if>
            <if test="englishDescription != null and englishDescription != ''"> and english_description like concat('%',#{englishDescription},'%')</if>
        </where>
    </select>
</mapper>