package com.gzairports.web.controller.departure;

import com.gzairports.common.annotation.OperLog;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.business.departure.domain.Transfer;
import com.gzairports.wl.departure.domain.query.TransFerWaybillsVo;
import com.gzairports.wl.departure.domain.query.TransferInfoQuery;
import com.gzairports.wl.departure.domain.query.TransferQuery;
import com.gzairports.wl.departure.domain.vo.TransferMawbVo;
import com.gzairports.wl.departure.domain.vo.TransferVo;
import com.gzairports.wl.departure.service.ITransferService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 货站出港货物交接Controller
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@RestController
@RequestMapping("/dep/transfer")
public class TransferController extends BaseController {

    @Autowired
    private ITransferService transferService;

    /**
     * 查询出港货物交接列表
     */
//    @PreAuthorize("@ss.hasPermi('dep:transfer:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询出港货物交接列表")
    public TableDataInfo list(TransferQuery query){
        startPage();
        List<TransferVo> list = transferService.selectList(query);
        return getDataTable(list);
    }

    /**
     * 新增出港货物交接单
     */
    @PreAuthorize("@ss.hasPermi('dep:transfer:add')")
    @OperLog(title = "出港货物交接", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ApiOperation(value = "新增出港货物交接单")
    public AjaxResult add(@RequestBody Transfer transfer)
    {
        return toAjax(transferService.add(transfer));
    }

    /**
     * 出港货物交接详情
     */
    @PreAuthorize("@ss.hasPermi('dep:transfer:getInfo')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "出港货物交接详情")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(transferService.getInfo(id));
    }

    /**
     * 出港货物交接增加运单
     */
    @PreAuthorize("@ss.hasPermi('dep:transfer:addWaybill')")
    @PostMapping("/addWaybill")
    @ApiOperation(value = "增加运单")
    public AjaxResult addWaybill(@RequestBody TransferInfoQuery query)
    {
        return success(transferService.addWaybill(query));
    }

    /**
     * 编辑出港货物交接单
     */
    @PreAuthorize("@ss.hasPermi('dep:transfer:edit')")
    @OperLog(title = "出港货物交接", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ApiOperation(value = "编辑出港货物交接单")
    public AjaxResult edit(@RequestBody Transfer transfer)
    {
        return toAjax(transferService.edit(transfer));
    }

    /**
     * 删除出港货物交接单
     */
    @PreAuthorize("@ss.hasPermi('dep:transfer:del')")
    @OperLog(title = "出港货物交接", businessType = BusinessType.DELETE)
    @GetMapping("/del/{id}")
    @ApiOperation(value = "删除出港货物交接单")
    public AjaxResult edit(@PathVariable("id") Long id)
    {
        return toAjax(transferService.del(id));
    }

    /**
     * 出港货物交接删除运单
     */
    @PreAuthorize("@ss.hasPermi('dep:transfer:delWaybill')")
    @PostMapping("/delWaybill")
    @ApiOperation(value = "删除运单")
    public AjaxResult delWaybill(@RequestBody TransferInfoQuery query)
    {
        return toAjax(transferService.delWaybill(query));
    }

    /**
     * 获取交接单头部信息
     */
    @PreAuthorize("@ss.hasPermi('dep:transfer:getTransfer')")
    @GetMapping("/getTransfer")
    @ApiOperation(value = "获取交接单头部信息")
    public AjaxResult getTransfer()
    {
        return AjaxResult.success(transferService.getTransfer());
    }

    /**
     * 根据主单id获取主单信息 用于特货交接单回显
     * */
    @GetMapping("/getTransferByMawbId/{id}")
    @ApiOperation(value = "获取特货交接单需要回显的主单信息")
    public AjaxResult getTransferByMawbId(@PathVariable Long id)
    {
        return AjaxResult.success(transferService.getTransferByMawbId(id));
    }

    /**
     * 获取交接单运单信息
     */
    @PreAuthorize("@ss.hasPermi('dep:transfer:getWaybill')")
    @GetMapping("/getWaybill")
    @ApiOperation(value = "获取交接单运单信息")
    public TableDataInfo getWaybill(TransFerWaybillsVo vo)
    {
        startPage();
        List<TransferMawbVo> waybill = transferService.getWaybill(vo);
        return getDataTable(waybill);
    }

    /**
     * 打印交接单
     */
//    @PreAuthorize("@ss.hasPermi('dep:transfer:printTransfer')")
    @GetMapping(value = "/printTransfer/{id}")
    @ApiOperation(value = "打印交接单")
    public void printTransfer(HttpServletResponse response, @PathVariable("id") Long id) throws Exception
    {
        transferService.printTransfer(response,id);
    }
}
