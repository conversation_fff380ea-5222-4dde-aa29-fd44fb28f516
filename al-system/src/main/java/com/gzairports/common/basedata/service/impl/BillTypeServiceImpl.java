package com.gzairports.common.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.BaseBillType;
import com.gzairports.common.basedata.mapper.BillTypeMapper;
import com.gzairports.common.basedata.service.IBillTypeService;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.wl.ticket.domain.TicketCtrl;
import com.gzairports.wl.ticket.mapper.OperateMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 票证管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-17
 */
@Service
public class BillTypeServiceImpl extends ServiceImpl<BillTypeMapper, BaseBillType> implements IBillTypeService {

    @Autowired
    private BillTypeMapper typeMapper;

    @Autowired
    private OperateMapper operateMapper;

    /**
     * 查询票证来源列表
     *
     * @return 票证来源列表
     */
    @Override
    public List<BaseBillType> selectList() {
        return typeMapper.selectTypeList();
    }

    /**
     * 查询票证管理详情
     *
     * @param id 票证管理id
     * @return 票证管理详情
     */
    @Override
    public BaseBillType getInfo(Long id) {
        return typeMapper.selectById(id);
    }

    /**
     * 新增票证管理
     *
     * @param baseBillType 票证管理
     * @return 结果
     */
    @Override
    public int insertType(BaseBillType baseBillType) {
        Long deptId = SecurityUtils.getHighParentId();

        List<BaseBillType> baseBillTypes = typeMapper.selectList(new QueryWrapper<BaseBillType>()
                .eq("code", baseBillType.getCode())
                .eq("domint",baseBillType.getDomint())
                .eq("is_del", 0));

        if (!CollectionUtils.isEmpty(baseBillTypes)){
            throw new CustomException("票证类型编码不能重复");
        }

        List<BaseBillType> list = typeMapper.selectList(new QueryWrapper<BaseBillType>()
                .eq("name", baseBillType.getName())
                .eq("domint",baseBillType.getDomint())
                .eq("is_del", 0));

        if (!CollectionUtils.isEmpty(list)){
            throw new CustomException("票证类型名称不能重复");
        }
        TicketCtrl ctrl = operateMapper.selectOne(new QueryWrapper<TicketCtrl>()
                .eq("code", baseBillType.getCode())
                .eq("domint",baseBillType.getDomint())
                .eq("dept_id",deptId));
        if (ctrl == null){
            TicketCtrl ticketCtrl = new TicketCtrl();
            ticketCtrl.setCode(baseBillType.getCode());
            ticketCtrl.setDeptId(deptId);
            ticketCtrl.setDomint(baseBillType.getDomint());
            operateMapper.insert(ticketCtrl);
        }
        baseBillType.setDeptId(deptId);
        return typeMapper.insert(baseBillType);
    }

    /**
     * 修改票证管理
     *
     * @param baseBillType 票证管理
     * @return 结果
     */
    @Override
    public int updateType(BaseBillType baseBillType) {
        Long deptId = SecurityUtils.getHighParentId();
        BaseBillType billType = typeMapper.selectById(baseBillType.getId());
        if (!baseBillType.getCode().equals(billType.getCode())){
            TicketCtrl ctrl = operateMapper.selectOne(new QueryWrapper<TicketCtrl>()
                    .eq("code", billType.getCode())
                    .eq("domint",billType.getDomint())
                    .eq("dept_id",deptId));
            if (ctrl != null){
                ctrl.setCode(baseBillType.getCode());
                ctrl.setDomint(baseBillType.getDomint());
                operateMapper.updateById(ctrl);
            }
        }
        return typeMapper.updateById(baseBillType);
    }
}
