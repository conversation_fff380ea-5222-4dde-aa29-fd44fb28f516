package com.gzairports.common.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.BasePack;
import com.gzairports.common.basedata.domain.query.BasePackQuery;
import com.gzairports.common.basedata.mapper.PackMapper;
import com.gzairports.common.basedata.service.IPackService;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.exception.ServiceException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 包装管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Service
public class PackServiceImpl extends ServiceImpl<PackMapper, BasePack> implements IPackService {

    @Autowired
    private PackMapper packMapper;

    /**
     * 查询包装管理列表
     *
     * @param query 查询参数
     * @return 包装管理列表
     */
    @Override
    public List<BasePack> selectBasePackList(BasePackQuery query) {
        QueryWrapper<BasePack> wrapper = new QueryWrapper<BasePack>();
        wrapper.eq("is_del",0);
        if (StringUtils.isNotEmpty(query.getPack())){
            wrapper.like("pack",query.getPack());
        }
        if (StringUtils.isNotEmpty(query.getPackCode())){
            wrapper.like("pack_code",query.getPackCode());
        }
        if (query.getIsCommon() != null){
//            wrapper.eq("is_common",0).or().eq("is_common",query.getIsCommon());
            wrapper.apply("(is_common = 0 OR is_common = {0})", query.getIsCommon());
        }
        return packMapper.selectList(wrapper);
    }

    /**
     * 新增包装管理
     *
     * @param basePack 包装管理
     * @return 结果
     */
    @Override
    public int addBasePack(BasePack basePack) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        basePack.setCreateBy(loginUser.getUsername());
        basePack.setCreateTime(DateUtils.getNowDate());
        return packMapper.insert(basePack);
    }

    /**
     * 修改包装管理
     *
     * @param basePack 包装管理
     * @return 结果
     */
    @Override
    public int editBasePack(BasePack basePack) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        basePack.setUpdateBy(loginUser.getUsername());
        basePack.setUpdateTime(DateUtils.getNowDate());
        return packMapper.updateById(basePack);
    }

    /**
     * 删除包装管理
     *
     * @param id 包装管理id集合
     * @return 结果
     */
    @Override
    public int delBasePack(Long id) {
        BasePack basePack = packMapper.selectById(id);
        if (basePack == null){
            throw new ServiceException("未查询到当前包装数据");
        }
        basePack.setIsDel(1);
        return packMapper.updateById(basePack);
    }

    /**
     * 根据id获取包装管理
     *
     * @param id 包装管理id
     * @return 包装管理
     */
    @Override
    public BasePack getInfo(Long id) {
        return packMapper.selectById(id);
    }
}
