<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.BillSourceMapper">
    <select id="selectBillList" resultType="com.gzairports.common.basedata.domain.BaseBillSource">
        select bbs.id, bbt.name as typeName, bbt.code, bbs.name, bbs.prefix, bbs.remark
        from base_bill_source bbs left join base_bill_type bbt on bbt.id = bbs.bill_type_id
        where bbs.is_del = 0
    </select>
</mapper>