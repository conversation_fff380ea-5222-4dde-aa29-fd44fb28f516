package com.gzairports.wl.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.business.departure.domain.WaybillTrace;
import com.gzairports.common.business.departure.domain.vo.CarrierFlightVo;
import com.gzairports.common.log.domain.WaybillLog;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 运单跟踪返回参数
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Data
public class WaybillTraceVo {

    /** 主键id */
    private Long id;

    /** 主运单号 */
    private String masterWaybillCode;

    /**数据类型 ARR-进港 DEP-出港 */
    private String businessType;

    /**运单状态 */
    private String status;

    /** 分运单号 */
    private String slaveWaybillCode;

    /** 始发站 */
    private String sourcePort;

    /** 目的站 */
    private String desPort;

    /** 目的站名称 */
    private String desPortName;

    /** 发货人 */
    private String shipper;

    /** 发货人电话 */
    private String shipperPhone;

    /** 收货人 */
    private String consign;

    /** 收货人电话 */
    private String consignPhone;

    /** 制单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date writeTime;

    /** 制单人 */
    private String writer;

    /** 航班号 */
    private String flightNo;

    /** 航班日期 */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date flightDate;

    /** 航班落地时间 */
    private LocalDateTime flightArrivalDate;

    /** 品名 */
    private String cargoName;

    /** 承运航班 */
    private List<CarrierFlightVo> voList;

    /** 总承运件数 */
    private Integer totalQuantity;

    /** 总承运重量 */
    private BigDecimal totalWeight;

    /** 运单跟踪数据 */
    private List<WaybillTrace> traces;

    /** 运单日志数据 */
    private List<WaybillLog> logs;
}
