<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.transfer.mapper.HzTransferHandoverMapper">
    
    <resultMap type="HzTransferHandover" id="HzTransferHandoverResult">
        <result property="id"    column="id"    />
        <result property="handoverNo"    column="handover_no"    />
        <result property="votes"    column="votes"    />
        <result property="totalPrices"    column="total_prices"    />
        <result property="totalWeight"    column="total_weight"    />
        <result property="fileStatus"    column="file_status"    />
        <result property="fileBy"    column="file_by"    />
        <result property="fileOutTime"    column="file_out_time"    />
        <result property="fileInTime"    column="file_in_time"    />
        <result property="cargoStatus"    column="cargo_status"    />
        <result property="cargoBy"    column="cargo_by"    />
        <result property="cargoOutTime"    column="cargo_out_time"    />
        <result property="cargoInTime"    column="cargo_in_time"    />
        <result property="outStore"    column="out_store"    />
        <result property="outLocator"    column="out_locator"    />
        <result property="inStore"    column="in_store"    />
        <result property="inLocator"    column="in_locator"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectHzTransferHandoverVo">
        select id, handover_no, votes, total_prices, total_weight, file_status, file_by, file_out_time, file_in_time, cargo_status, cargo_by, cargo_out_time, cargo_in_time, out_store, out_locator, in_store, in_locator, out_name, remark, create_time from hz_transfer_handover
    </sql>

    <select id="selectHzTransferHandoverList" parameterType="HzTransferHandover" resultMap="HzTransferHandoverResult">
        <include refid="selectHzTransferHandoverVo"/>
        <where>  
            <if test="handoverNo != null  and handoverNo != ''"> and handover_no like concat('%',#{handoverNo},'%')</if>
            <if test="fileStatus != null "> and file_status = #{fileStatus}</if>
            <if test="cargoStatus != null "> and cargo_status = #{cargoStatus}</if>
            <if test="cargoOutTime != null">
                and create_time <![CDATA[>=]]> #{cargoOutTime}
            </if>
            <if test="cargoInTime != null">
                and create_time <![CDATA[<=]]> #{cargoInTime}
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectHzTransferHandoverById" parameterType="Long" resultType="com.gzairports.hz.business.transfer.domain.vo.TransferHandoverInfoVo">
        <include refid="selectHzTransferHandoverVo"/>
        where id = #{id}
    </select>
    <select id="selectOneByNo"
            resultType="com.gzairports.hz.business.transfer.domain.vo.TransferHandoverInfoVo">
        <include refid="selectHzTransferHandoverVo"/>
        where handover_no = #{handoverNo}
    </select>
    <select id="selectFileAndCargoInfoById"
            resultType="com.gzairports.hz.business.transfer.domain.vo.FileAndCargoTransferVo">
        select id, handover_no, votes, total_prices, total_weight, file_status, file_by, file_out_time, file_in_time, cargo_status,
               cargo_by, cargo_out_time, cargo_in_time, out_store, out_locator, in_store, in_locator, out_name, remark as transferRemark
        from hz_transfer_handover
        where id = #{id}
    </select>
    <select id="getInfoByQuery"
            resultType="com.gzairports.hz.business.transfer.domain.vo.FileAndCargoTransferVo">
        select id, handover_no, votes, total_prices, total_weight, file_status, file_by, file_out_time, file_in_time, cargo_status,
               cargo_by, cargo_out_time, cargo_in_time, out_store, out_locator, in_store, in_locator, out_name, remark as transferRemark
        from hz_transfer_handover
        <where>
            <if test="handoverNo != null  and handoverNo != ''"> and handover_no = #{handoverNo}</if>
            <if test="startTime != null">
                and create_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and create_time <![CDATA[<=]]> #{endTime}
            </if>
        </where>
    </select>

    <insert id="insertHzTransferHandover" parameterType="HzTransferHandover" useGeneratedKeys="true" keyProperty="id">
        insert into hz_transfer_handover
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="handoverNo != null">handover_no,</if>
            <if test="votes != null">votes,</if>
            <if test="totalPrices != null">total_prices,</if>
            <if test="totalWeight != null">total_weight,</if>
            <if test="fileStatus != null">file_status,</if>
            <if test="fileBy != null">file_by,</if>
            <if test="fileOutTime != null">file_out_time,</if>
            <if test="fileInTime != null">file_in_time,</if>
            <if test="cargoStatus != null">cargo_status,</if>
            <if test="cargoBy != null">cargo_by,</if>
            <if test="cargoOutTime != null">cargo_out_time,</if>
            <if test="cargoInTime != null">cargo_in_time,</if>
            <if test="outStore != null">out_store,</if>
            <if test="outLocator != null">out_locator,</if>
            <if test="inStore != null">in_store,</if>
            <if test="inLocator != null">in_locator,</if>
            <if test="outName != null">out_name,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="handoverNo != null">#{handoverNo},</if>
            <if test="votes != null">#{votes},</if>
            <if test="totalPrices != null">#{totalPrices},</if>
            <if test="totalWeight != null">#{totalWeight},</if>
            <if test="fileStatus != null">#{fileStatus},</if>
            <if test="fileBy != null">#{fileBy},</if>
            <if test="fileOutTime != null">#{fileOutTime},</if>
            <if test="fileInTime != null">#{fileInTime},</if>
            <if test="cargoStatus != null">#{cargoStatus},</if>
            <if test="cargoBy != null">#{cargoBy},</if>
            <if test="cargoOutTime != null">#{cargoOutTime},</if>
            <if test="cargoInTime != null">#{cargoInTime},</if>
            <if test="outStore != null">#{outStore},</if>
            <if test="outLocator != null">#{outLocator},</if>
            <if test="inStore != null">#{inStore},</if>
            <if test="inLocator != null">#{inLocator},</if>
            <if test="outName != null">#{outName},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateHzTransferHandover" parameterType="HzTransferHandover">
        update hz_transfer_handover
        <trim prefix="SET" suffixOverrides=",">
            <if test="handoverNo != null">handover_no = #{handoverNo},</if>
            <if test="votes != null">votes = #{votes},</if>
            <if test="totalPrices != null">total_prices = #{totalPrices},</if>
            <if test="totalWeight != null">total_weight = #{totalWeight},</if>
            <if test="fileStatus != null">file_status = #{fileStatus},</if>
            <if test="fileBy != null">file_by = #{fileBy},</if>
            <if test="fileOutTime != null">file_out_time = #{fileOutTime},</if>
            <if test="fileInTime != null">file_in_time = #{fileInTime},</if>
            <if test="cargoStatus != null">cargo_status = #{cargoStatus},</if>
            <if test="cargoBy != null">cargo_by = #{cargoBy},</if>
            <if test="cargoOutTime != null">cargo_out_time = #{cargoOutTime},</if>
            <if test="cargoInTime != null">cargo_in_time = #{cargoInTime},</if>
            <if test="outStore != null">out_store = #{outStore},</if>
            <if test="outLocator != null">out_locator = #{outLocator},</if>
            <if test="inStore != null">in_store = #{inStore},</if>
            <if test="inLocator != null">in_locator = #{inLocator},</if>
            <if test="outName != null">out_name = #{outName},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>