<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.departure.mapper.WaybillTraceMapper">

    <select id="selectFlight" resultType="java.lang.Long">
        (select hfl.flight_id
         from hz_flight_load_waybill lw
            left join hz_flight_load hfl on hfl.id = lw.flight_load_id  where waybill_id = #{waybillId})
        union all
        (select hfl.flight_id
        from hz_flight_load_uld_waybill lum
            left join hz_flight_load_uld lu on lu.id = lum.load_uld_id
            left join hz_flight_load hfl on hfl.id = lu.flight_load_id
        where lum.waybill_id = #{waybillId})
    </select>
    <select id="selectFlightNo"
            resultType="com.gzairports.common.business.departure.domain.vo.CarrierFlightVo">
        select flight_id ,CONCAT(air_ways,flight_no) AS flight_no, exec_date from all_flight_info where flight_id in
        <foreach collection="collect" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>
