package com.gzairports.common.business.departure.domain.vo;


import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 航空运单对象 t_fil_awbinfo
 *
 * <AUTHOR>
 * @date 2023-03-24
 */
@Data
@TableName(schema = "KWECFPS", value = "T_FIL_AWBINFO")
public class TFilAwbinfo
{
    private static final long serialVersionUID = 1L;

    /** 运单完整编码 */
    private String billid;

    /** 运单类型 */
    private String stocktypeid;

    /** 运单前缀 */
    private String stockpre;

    /** 运单号 */
    private String stockno;

    private String productid;

    private String carrierproductid;

    /** 做废标记 */
    private String delflag;

    /** 换单前单号 */
    private String previousbillid;

    /** 是否国内/国际 */
    private String domint;

    /** 是否海关监管 */
    private String customctl;

    /** 特殊处理代码 */
    @Excel(name = "特殊处理代码")
    private String specopeid;

    /** 特殊处理代码扩展 */
    private String specopeidext;

    private String sairportid;

    private String scityid;

    private String eairportid;

    private String ecityid;

    private String by1;

    private String dest1;

    private String dest1city;

    private String by2;

    private String dest2;

    private String dest2city;

    private String by3;

    private String dest3;

    private String dest3city;

    private String by4;

    private String dest4;

    private String dest4city;

    private String cargono;

    private String cargonm;

    private String pack;

    /** 尺寸 */
    private String meas;

    /** 数量 */
    private BigDecimal pcs;

    /** 重量 */
    private BigDecimal weight;

    private BigDecimal feewt;

    /** 体积 */
    private BigDecimal vol;

    /** 控制营业点 */
    private String ctrlopedepartment;

    /** 发货人代理人 */
    private String shprname;

    /** 发货人电话 */
    private String shprtel;

    /** 发货人地址 */
    private String shpraddress;

    /** 发货代理人代码 */
    private String shpcustomerid;

    /** 收货人 */
    private String cnsnname;

    /** 收货人电话 */
    private String cnsntel;

    /** 收货人地址 */
    private String cnsnaddress;

    /** 收货代理人代码 */
    private String csgcustomerid;

    /** 预配航班号承运人 */
    private String preairline;

    /** 预配航班号 */
    private String preflightno;

    /** 预配航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date preflightdate;

    /** 需到付 */
    private String collected;

    /** 汇率 */
    private BigDecimal exchagerate;

    /** COMAT标记 */
    private String comat;

    /** 需冷藏 */
    private String refrigerated;

    /** 使用叉车数 */
    private BigDecimal forknum;

    /** 压仓标识 */
    private String whshold;

    /** 出口转关货标识 */
    private String expcusttransit;

    /** 进口转关货标识 */
    private String impcusttransit;

    /** 短途转驳标识 */
    private String shorttrans;

    /** 短途转驳代理装卸标识 */
    private String shorttransbup;

    /** 大货主 */
    private String cargoowner;

    /** 计费开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date chargetime;

    /** 是否机场自己销售运单 */
    private String isinstruction;

    /** 另请通知 */
    private String notify;

    /** 托运人声明的价值 */
    private BigDecimal shippervalue;

    /** 供运输用 */
    private BigDecimal trafficvalue;

    /** 供海关用 */
    @Excel(name = "供海关用")
    private BigDecimal customvalue;

    /** 保险金额 */
    private BigDecimal insurevalue;

    /** 随附文件名称组合成的字符串，以;分隔 */
    private String fileattached;

    /** 运价类别 */
    private String ratetype;

    /** 在货物不能交予收货人时托运人指示的处理办法 */
    private String processingmethod;

    /** 处理情况 */
    private String handlingcircs;

    /** 已预留吨位 */
    private String reservedtonnage;

    /** 运费 */
    private BigDecimal carriage;

    /** 费率 */
    private BigDecimal rate;

    /** 预配航班号第二组 */
    private String preflightno1;

    /** 预配航班日期第二组 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date preflightdate1;

    private String crtoper;

    /** 预配航班号承运人第二组 */
    private String preairline1;

    /** 重量单位 */
    private String wtunit;

    /** 原始重量数值 */
    private BigDecimal originalwt;

    /** 原始计费重量数值 */
    private BigDecimal originalfeewt;

    /** 体积单位 */
    private String volunit;

    /** 货币种类 */
    private String currencyid;

    /** 原始体积数值 */
    private BigDecimal originalvol;

    /** $column.columnComment */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date crtopetime;

    /** 提货优先处理标记 Y优先 N不优先 */
    private String dlvpriority;

    /** 审核完毕 */
    private String chked;

    /** 合计打印标签数 */
    private Long labelnum;

    /** 发货人代码 */
    private String shpcustomer;

    /** 填开代理人 */
    private String crtagent;

    /** 填开日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date crtdate;

    /** 超限重量 */
    private BigDecimal extraweight;

    /** 储运注意事项 ，换单前单号 如902-03733590*/
    private String storeremark;

    /** 结算注意事项 */
    private String balanceremark;

    /** 文件审核完毕 */
    private String filechked;

    /** 是否整车复磅.Y=是;N=否 */
    private String iscarbalance;

    /** 收货人身份证号码 */
    private String cnsnidcard;

    /** 是否商检放行 */
    private String ciqcheck;

    /** 临时单号 */
    private String tmpbillno;

    /** 后4位运单号 */
    private String subbillid;

    /** 进港是否中转 */
    private String istrans;

    /** 拆分提货标志位 */
    private String splittag;

    /** 转关标识 */
    private String customstrans;

    private String endoper;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endopetime;

    /** 是否真实收运标识 */
    private String isrcs;

    /** 转为真实收运的时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date transrcs;

    /** 跨航司承运 */
    private String istranscarrier;

}
