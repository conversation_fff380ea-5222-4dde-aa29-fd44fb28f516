<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.departure.mapper.WaybillFeeMapper">

    <select id="selectListByQuery" resultType="com.gzairports.common.business.departure.domain.WaybillFee">
        select id, pay_time, pay_money, settle_time, settle_money, refund, status, waybill_code, serial_no from wl_waybill_fee
        <where>
            1 = 1
            <if test="startPayTime != null">
                and pay_time <![CDATA[>=]]> #{startPayTime}
            </if>
            <if test="endPayTime != null">
                and pay_time <![CDATA[<=]]> #{endPayTime}
            </if>
            <if test="startSettleTime != null">
                and settle_time <![CDATA[>=]]> #{startSettleTime}
            </if>
            <if test="endSettleTime != null">
                and settle_time <![CDATA[<=]]> #{endSettleTime}
            </if>
            <if test="waybillCode != null and waybillCode != ''">
                and waybill_code like '%${waybillCode}%'
            </if>
            <if test="serialNo != null and serialNo != ''">
                and serial_no like '%${serialNo}%'
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="deptIds != null and deptIds.size() > 0">
                and dept_id in
                <foreach collection="deptIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
</mapper>