<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.CurrencyMapper">
    <insert id="insertCurrency" parameterType="baseCurrency">
        insert into base_currency
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="currency != null and currency != ''">currency,</if>
            <if test="country != null and country != ''">country,</if>
            <if test="currencyUnit != null and currencyUnit != ''">currency_unit,</if>
            <if test="currencySymbol != null and currencySymbol != ''">currency_symbol,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="currency != null and currency != ''">#{currency},</if>
            <if test="country != null and country != ''">#{country},</if>
            <if test="currencyUnit != null and currencyUnit != ''">#{currencyUnit},</if>
            <if test="currencySymbol != null and currencySymbol != ''">#{currencySymbol},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>
    <update id="updateCurrency" parameterType="baseCurrency">
        update base_currency
        <trim prefix="SET" suffixOverrides=",">
            <if test="currency != null and currency != ''">currency = #{currency},</if>
            <if test="country != null and country != ''">country = #{country},</if>
            <if test="currencyUnit != null and currencyUnit != ''">currency_unit = #{currencyUnit},</if>
            <if test="currencySymbol != null and currencySymbol != ''">currency_symbol = #{currencySymbol},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null and createTime != ''">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="delCurrency" parameterType="long">
        update base_currency set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <select id="selectCurrencyList" resultType="com.gzairports.common.basedata.domain.BaseCurrency">
        select id, currency, country, currency_unit, currency_symbol from base_currency
        <where>
        and is_del = 0
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="currency != null  and currency != ''"> and currency like concat('%',#{currency},'%')</if>
            <if test="country != null and country != ''"> and country like concat('%',#{country},'%')</if>
        </where>
    </select>
    <select id="selectCurrencyByCode" resultType="com.gzairports.common.basedata.domain.BaseCurrency">
        select id, currency, country, currency_unit, currency_symbol
        from base_currency
        where currency = #{currency} and is_del = 0
    </select>
</mapper>
