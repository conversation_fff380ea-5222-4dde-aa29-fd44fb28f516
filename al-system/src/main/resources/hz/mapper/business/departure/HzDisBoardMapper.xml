<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.departure.mapper.HzDisBoardMapper">
    <update id="updateLoad">
        update hz_dep_dis_board set is_load = 1, is_handle = 1
                                where id = #{collectId}
    </update>
    <select id="selectImportDate"
            resultType="com.gzairports.hz.business.departure.domain.vo.ForwardImportWaybillVo">
        select hddb.id as collectId, hddb.id as weightId, aaw.id as waybillId, aaw.cargo_name, aaw.special_cargo_code1, aaw.waybill_code,
               aaw.des1, hddb.quantity, hddb.weight, @num := 2 as type, aaw.write_time, aaw.quantity as waybillQuantity, aaw.weight as waybillWeight,
               aaw.storage_transport_notes,sd.dept_name_abb
        from hz_dep_dis_board hddb
        left join all_air_waybill aaw on hddb.waybill_id = aaw.id
        left join sys_dept sd on sd.dept_id = aaw.dept_id
        <where>
            hddb.is_load = 0 and hddb.is_handle = 0 and (hddb.uld is null or hddb.uld = '') and aaw.status != 'INVALID'
            <if test="query.waybillCode != null  and query.waybillCode != ''"> and aaw.waybill_code = #{query.waybillCode}</if>
            <if test="query.flightNo1 != null  and query.flightNo1 != ''"> and aaw.flight_no1 like concat('%',#{query.flightNo1},'%')</if>
            <if test="query.flightDate1 != null"> and aaw.flight_date1 = #{query.flightDate1}</if>
            <if test="query.sourcePort != null  and query.sourcePort != ''"> and aaw.source_port = #{query.sourcePort}</if>
            <if test="query.startStation != null  and query.startStation != ''"> and aaw.source_port = #{query.startStation}</if>
            <if test="query.carrierList != null and query.carrierList.size() > 0">
                and aaw.carrier1 in
                <foreach collection="query.carrierList" item="code" index="index" open="(" close=")" separator=",">
                    #{code}
                </foreach>
            </if>
            <if test="query.des1 != null  and query.des1 != ''"> and aaw.des1 = #{query.des1}</if>
            <if test="query.specialCargoCode1 != null  and query.specialCargoCode1 != ''"> and aaw.special_cargo_code1 = #{query.specialCargoCode1}</if>
            <if test="query.shipper != null  and query.shipper != ''"> and aaw.shipper = #{query.shipper}</if>
            <if test="query.startTime != null">
                and aaw.write_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and aaw.write_time <![CDATA[<=]]> #{query.endTime}
            </if>
        </where>
    </select>
    <select id="selectImportUldData"
            resultType="com.gzairports.hz.business.departure.domain.vo.ForwardImportUldVo">
        select hddb.id as weightId, hddb.id as collectId, hddb.uld, hddb.quantity as totalQuantity, hddb.weight as totalWeight, @num := 2 as type,
               aaw.quantity as waybillQuantity, aaw.weight as waybillWeight
        from hz_dep_dis_board hddb
        left join all_air_waybill aaw on hddb.waybill_id = aaw.id
        <where>
            hddb.is_load = 0 and (hddb.uld != '' or hddb.uld is not null) and aaw.status != 'INVALID'
            <if test="query.des1 != null and query.des1 != ''">
                and aaw.des1 = #{query.des1}
            </if>
            <if test="query.carrierList != null and query.carrierList.size() > 0">
                and aaw.carrier1 in
                <foreach collection="query.carrierList" item="code" index="index" open="(" close=")" separator=",">
                    #{code}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectListByCollectIds"
            resultType="com.gzairports.hz.business.departure.domain.vo.ForwardImportWaybillVo">
        select hddb.id as collectId, hddb.id as weightId, aaw.id as waybillId, aaw.cargo_name, aaw.special_cargo_code1, aaw.waybill_code,
               aaw.source_port, aaw.des1, hddb.quantity, hddb.weight, @num := 2 as type, aaw.flight_date1 as flightDate,
               aaw.write_time,aaw.quantity as waybillQuantity, aaw.weight as waybillWeight
        from hz_dep_dis_board hddb
        left join all_air_waybill aaw on hddb.waybill_id = aaw.id
        <where>
            hddb.id in
            <foreach collection="collect2" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>
    <select id="selectByCollectId"
            resultType="com.gzairports.hz.business.departure.domain.vo.ForwardImportWaybillVo">
        select hddb.id as collectId, hddb.id as weightId, aaw.id as waybillId, aaw.cargo_name, aaw.special_cargo_code1, aaw.waybill_code, aaw.source_port,
               aaw.des1, hddb.quantity, hddb.weight, @num := 2 as type
        from hz_dep_dis_board hddb
            left join all_air_waybill aaw on hddb.waybill_id = aaw.id where hddb.id = #{collectId}
    </select>
    <select id="selectImportDataByCode" resultType="java.lang.Long">
        select hddb.id
        from hz_dep_dis_board hddb
        left join all_air_waybill aaw on hddb.waybill_id = aaw.id
        where hddb.is_load = 0 and hddb.waybill_id = #{waybillId} and (hddb.uld is null or hddb.uld = '')
    </select>
    <select id="selectImportUldDataByCode" resultType="java.lang.Long">
        select hddb.id
        from hz_dep_dis_board hddb
                 left join all_air_waybill aaw on hddb.waybill_id = aaw.id
        where hddb.is_load = 0 and hddb.waybill_id = #{waybillId} and hddb.uld = #{uld}
    </select>
</mapper>
