package com.gzairports.hz.business.arrival.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * app航班列表数据
 * <AUTHOR>
 * @date 2024-10-10
 */
@Data
public class AppFlightListVo {

    /** 航班id */
    private Long flightId;

    /** 航班日期 */
    private Date execDate;

    /** 实际降落时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date terminalSchemeLandInTime;

    /** 航班号 */
    private String flightNo;

    /** 生成时间 */
    private Date cabinTime;

    /** 理货次数 */
    private Integer tallyNum;

    /** 运单数 */
    private Integer waybillNum;

    /** 理货状态 */
    private String tallyStatus;
}
