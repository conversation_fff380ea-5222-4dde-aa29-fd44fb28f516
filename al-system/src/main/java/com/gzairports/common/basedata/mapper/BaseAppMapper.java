package com.gzairports.common.basedata.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.basedata.domain.BaseApp;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * app管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-19
 */
@Mapper
public interface BaseAppMapper extends BaseMapper<BaseApp>
{
    /**
     * 查询app管理
     * 
     * @param id app管理ID
     * @return app管理
     */
    BaseApp selectBaseAppById(Long id);

    /**
     * 查询app管理列表
     * 
     * @param baseApp app管理
     * @return app管理集合
     */
    List<BaseApp> selectBaseAppList(BaseApp baseApp);

    /**
     * 新增app管理
     * 
     * @param baseApp app管理
     * @return 结果
     */
    int insertBaseApp(BaseApp baseApp);

    /**
     * 修改app管理
     * 
     * @param baseApp app管理
     * @return 结果
     */
    int updateBaseApp(BaseApp baseApp);

    /**
     * 删除app管理
     * 
     * @param id app管理ID
     * @return 结果
     */
    int deleteBaseAppById(Long id);

    /**
     * 批量删除app管理
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteBaseAppByIds(Long[] ids);

    /**
     * app下载
     * @return 下载地址
     */
    String selectOneDownload();
}
