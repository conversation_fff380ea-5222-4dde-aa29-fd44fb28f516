package com.gzairports.common.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.BasePost;
import com.gzairports.common.basedata.domain.query.BasePostQuery;
import com.gzairports.common.basedata.mapper.PostMapper;
import com.gzairports.common.basedata.service.IPostService;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.exception.ServiceException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 邮局维护Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Service
public class PostServiceImpl extends ServiceImpl<PostMapper, BasePost> implements IPostService {

    @Autowired
    private PostMapper postMapper;

    /**
     * 查询邮局维护列表
     *
     * @param query 查询参数
     * @return 邮局维护列表
     */
    @Override
    public List<BasePost> selectBasePostList(BasePostQuery query) {
        QueryWrapper<BasePost> wrapper = new QueryWrapper<BasePost>();
        wrapper.eq("is_del",0);
        if (StringUtils.isNotEmpty(query.getOfficeName())){
            wrapper.like("office_name",query.getOfficeName());
        }
        if (StringUtils.isNotEmpty(query.getOfficeType())){
            wrapper.eq("office_type",query.getOfficeType());
        }
        if (query.getIsCommon() != null){
//            wrapper.eq("is_common",0).or().eq("is_common",query.getIsCommon());
            wrapper.apply("(is_common = 0 OR is_common = {0})", query.getIsCommon());
        }
        return postMapper.selectList(wrapper);
    }

    /**
     * 新增邮局维护
     *
     * @param basePost 邮局维护
     * @return 结果
     */
    @Override
    public int addBasePost(BasePost basePost) {
        BasePost basePostForOld= postMapper.selectOne(new QueryWrapper<BasePost>()
                .eq("office_name", basePost.getOfficeName())
                .eq("air_code", basePost.getAirCode()));
        if (StringUtils.isNotNull(basePostForOld)){
            throw new ServiceException("邮局名称和机场编码重复");
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        basePost.setCreateBy(loginUser.getUsername());
        basePost.setCreateTime(DateUtils.getNowDate());
        return postMapper.insert(basePost);
    }

    /**
     * 修改邮局维护
     *
     * @param basePost 邮局维护
     * @return 结果
     */
    @Override
    public int editBasePost(BasePost basePost) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        basePost.setUpdateBy(loginUser.getUsername());
        basePost.setUpdateTime(DateUtils.getNowDate());
        return postMapper.updateById(basePost);
    }

    /**
     * 删除邮局维护
     *
     * @param id 邮局维护id集合
     * @return 结果
     */
    @Override
    public int delBasePost(Long id) {
        BasePost basePost = postMapper.selectById(id);
        if (basePost == null){
            throw new ServiceException("未查询到当前邮局维护数据");
        }
        basePost.setIsDel(1);
        return postMapper.updateById(basePost);
    }

    /**
     * 根据id获取邮局维护
     *
     * @param id 邮局维护id
     * @return 邮局维护
     */
    @Override
    public BasePost getInfo(Long id) {
        return postMapper.selectById(id);
    }

    /**
     * 根据接收局/托运局名称查询邮局信息
     *
     * @param abb 根据接收局/托运局名称查询邮局信息
     * @return 邮局信息
     */
    @Override
    public BasePost getInfoByCode(String abb, Integer type) {
        return postMapper.getInfoByCode(abb,type);
    }

    @Override
    public BasePost getInfoByPost(String port, Integer type) {
        return postMapper.getInfoByPost(port,type);
    }
}
