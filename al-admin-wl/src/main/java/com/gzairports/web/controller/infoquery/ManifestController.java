package com.gzairports.web.controller.infoquery;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.infoquery.service.IManifestService;
import com.gzairports.common.system.domain.SysConfig;
import com.gzairports.common.system.mapper.SysConfigMapper;
import com.gzairports.hz.business.departure.domain.query.FlightLoadQuery;
import com.gzairports.hz.business.departure.domain.vo.FlightLoadVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 舱单查询 Controller
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@RestController
@RequestMapping("/info/manifest")
public class ManifestController extends BaseController {

    @Autowired
    private IManifestService manifestService;

    @Autowired
    private SysConfigMapper sysConfigMapper;
    /**
     * 获取航班列表
     */
//    @PreAuthorize("@ss.hasPermi('info:manifest:list')")
    @GetMapping("/list")
    public TableDataInfo list(FlightLoadQuery query) {
        String[] split = getConfigNoViewAirLine();
        startPage();
        List<FlightLoadVo> list = manifestService.selectList(query,split);
        return getDataTable(list);
    }

    /**
     * 正式舱单
     */
//    @PreAuthorize("@ss.hasPermi('info:manifest:formalManifest')")
    @GetMapping("/formalManifest/{id}")
    @ApiOperation(value = "正式舱单")
    public AjaxResult infoFormalManifest(@PathVariable("id") Long id)
    {
        return AjaxResult.success(manifestService.infoFormalManifest(id));
    }

    /**
     * 新增查询
     */
//    @PreAuthorize("@ss.hasPermi('dep:flightLoad:addQuery')")
//    @PostMapping("/addQuery")
//    @ApiOperation(value = "新增查询")
//    public AjaxResult addQuery(@RequestBody AddQuery query)
//    {
//        ForwardImportVo vo = flightLoadService.addQuery(query);
//        return AjaxResult.success(vo);
//    }


    /**
     * 获取参数配置查找不显示航司
     * @return 不显示航司
     */
    private String[] getConfigNoViewAirLine() {
        SysConfig sysConfig = sysConfigMapper.checkConfigKeyUnique("carrier.notDisplayed");
        String[] split = null;
        if (sysConfig != null) {
            String configValue = sysConfig.getConfigValue();
            if (configValue != null && !"".equals(configValue)) {
                split = configValue.split("[,;]");
            }
        }
        return split;
    }
}
