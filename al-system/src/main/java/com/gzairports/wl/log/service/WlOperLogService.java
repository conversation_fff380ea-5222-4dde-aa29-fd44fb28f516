package com.gzairports.wl.log.service;




import com.gzairports.wl.log.domain.WlOperLog;

import java.util.List;

/**
 * 物流操作日志 服务层
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
public interface WlOperLogService {

    /**
     * 新增操作日志
     *
     * @param operLog 操作日志对象
     */
    void insertOperLog(WlOperLog operLog);

    /**
     *  查询物流操作日志
     * @param title 模块标题
     * @param id 查询日志id
     * @return 操作日志
     */
    List<WlOperLog> log(String title, Long id);

    /**
     *  查询物流操作日志详情
     * @param logId 操作日志id
     * @return 操作日志
     */
    WlOperLog logInfo(Long logId);
}
