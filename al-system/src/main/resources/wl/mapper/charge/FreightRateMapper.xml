<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.wl.charge.mapper.FreightRateMapper">
    <insert id="insertFreightRate" parameterType="freightRate" useGeneratedKeys="true" keyProperty="id">
        insert into wl_sf_report_price
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="rateName != null and rateName != ''">rate_name,</if>
            <if test="applicableDocuments != null">applicable_documents,</if>
            <if test="cargoCode != null and cargoCode != ''">cargo_code,</if>
            <if test="editable != null">editable,</if>
            <if test="automaticFromLow != null">automatic_from_low,</if>
            <if test="roundingMethod != null">rounding_method,</if>
            <if test="effectiveDate != null">effective_date,</if>
            <if test="expirationDate != null">expiration_date,</if>
            <if test="remarks != null and remarks != ''">remarks,</if>
            <if test="deptId != null ">dept_id,</if>
            <if test="isDel != null">is_del,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="rateName != null and rateName != ''">#{rateName},</if>
            <if test="applicableDocuments != null">#{applicableDocuments},</if>
            <if test="cargoCode != null and cargoCode != ''">#{cargoCode},</if>
            <if test="editable != null">#{editable},</if>
            <if test="automaticFromLow != null">#{automaticFromLow},</if>
            <if test="roundingMethod != null">#{roundingMethod},</if>
            <if test="effectiveDate != null">#{effectiveDate},</if>
            <if test="expirationDate != null">#{expirationDate},</if>
            <if test="remarks != null and remarks != ''">#{remarks},</if>
            <if test="deptId != null ">#{deptId},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
        </trim>
    </insert>
    <update id="updateFreightRate" parameterType="freightRate">
        update wl_sf_report_price
        <trim prefix="SET" suffixOverrides=",">
            <if test="rateName != null and rateName != ''">rate_name = #{rateName},</if>
            <if test="applicableDocuments != null">applicable_documents = #{applicableDocuments},</if>
            <if test="cargoCode != null and cargoCode != ''">cargo_code = #{cargoCode},</if>
            <if test="editable != null">editable = #{editable},</if>
            <if test="automaticFromLow != null">automatic_from_low = #{automaticFromLow},</if>
            <if test="roundingMethod != null">rounding_method = #{roundingMethod},</if>
            <if test="effectiveDate != null">effective_date = #{effectiveDate},</if>
            <if test="expirationDate != null">expiration_date = #{expirationDate},</if>
            <if test="remarks != null and remarks != ''">remarks = #{remarks},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="removeFreightRate">
        update wl_sf_report_price set is_del = 1 where id = #{id}
    </update>
    <select id="selectListByQuery" resultType="com.gzairports.wl.charge.domain.vo.FreightRateVO">
        select id, rate_name, cargo_code, applicable_documents, editable, automatic_from_low, rounding_method,
               effective_date, expiration_date, remarks
        from wl_sf_report_price
        <where>
            is_del = 0 and dept_id = #{deptId}
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="rateName != null  and rateName != ''"> and rate_name like concat('%',#{rateName},'%')</if>
            <if test="applicableDocuments != null and applicableDocuments != ''"> and applicable_documents = #{applicableDocuments}</if>
            <if test="effectiveDate != null">
                AND effective_date <![CDATA[<=]]> #{effectiveDate} and expiration_date <![CDATA[>=]]> #{effectiveDate}
            </if>
            <if test="expirationDate != null">
                AND expiration_date <![CDATA[>=]]> #{expirationDate} and effective_date <![CDATA[<=]]> #{expirationDate}
            </if>
        </where>
    </select>
    <select id="selectRateById" resultType="com.gzairports.wl.charge.domain.vo.FreightRateVO">
        select id, rate_name, cargo_code, applicable_documents, editable, automatic_from_low, rounding_method,
               effective_date, expiration_date, remarks
        from wl_sf_report_price
        where id = #{id}
    </select>
    <select id="selectByCode" resultType="com.gzairports.wl.charge.domain.FreightRate">
        select id, rate_name, cargo_code, applicable_documents, editable, automatic_from_low, rounding_method,
            effective_date, expiration_date, remarks
        from wl_sf_report_price where dept_id = #{deptId}
                                and applicable_documents = #{billTypeCode}
                                and effective_date <![CDATA[<=]]> CURDATE()
                                and expiration_date <![CDATA[>=]]> CURDATE()
                                and is_del = 0
    </select>
</mapper>