package com.gzairports.common.business.departure.domain.vo;

import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Created by david on 2024/11/26
 * <AUTHOR>
 */
@Data
public class HandSettleVo {

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 已出港件数 */
    private Integer depQuantity;

    /** 已出港重量 */
    private BigDecimal depWeight;

    /** 运单id */
    private Long waybillId;
}
