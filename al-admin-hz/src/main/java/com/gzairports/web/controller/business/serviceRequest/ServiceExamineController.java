package com.gzairports.web.controller.business.serviceRequest;

import com.gzairports.common.basedata.domain.BaseAirportCode;
import com.gzairports.common.basedata.domain.query.AirportCodeQuery;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.serviceRequest.domain.ServiceRequest;
import com.gzairports.common.serviceRequest.domain.query.ServiceRequestQuery;
import com.gzairports.common.serviceRequest.domain.vo.ServiceInfoVo;
import com.gzairports.common.serviceRequest.mapper.ServiceRequestMapper;
import com.gzairports.common.serviceRequest.service.IServiceRequestService;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 服务审核Controller
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@RestController
@RequestMapping("/bus/serviceExamine")
@Api(tags = "服务审核")
public class ServiceExamineController extends BaseController {

    @Autowired
    private IServiceRequestService requestService;

    @Autowired
    private ServiceRequestMapper requestMapper;

    /**
     * 查询服务申请列表
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询服务申请列表")
    public AjaxResult list(@RequestBody ServiceRequestQuery query){
        return AjaxResult.success(requestService.selectListByQuery(query));
    }

    /**
     * 详情
     */
    @PreAuthorize("@ss.hasPermi('bus:serviceExamine:getInfo')")
    @GetMapping("/getInfo/{id}")
    @ApiOperation(value = "详情")
    public AjaxResult getInfo(@PathVariable("id") Long id){
        return AjaxResult.success(requestService.hzGetInfo(id));
    }

    /**
     * 审核
     */
    @PreAuthorize("@ss.hasPermi('bus:serviceExamine:examine')")
    @PostMapping("/examine")
    @ApiOperation(value = "审核")
    public AjaxResult examine(@RequestBody ServiceInfoVo vo){
        return toAjax(requestService.examine(vo));
    }


    /**
     * 完成服务/拒绝服务 type:0/1
     */
    @PostMapping("/handleServiceExamine")
    @ApiOperation(value = "完成服务/拒绝服务")
    public AjaxResult handleServiceExamine(@RequestBody ServiceInfoVo vo){
        return toAjax(requestService.handleService(vo));
    }

    /**
     * 货站 服务审核 列表导出
     * */
    @PostMapping("/export")
    @ApiOperation(value = "导出服务审核数据")
    public void exportAirportCode(HttpServletResponse response, ServiceRequestQuery query)
    {
        Map<String, String> StatusHashMap = new HashMap<>();
        StatusHashMap.put("STAGING","暂存");
        StatusHashMap.put("UNAUDITED","未审核");
        StatusHashMap.put("SUCCESS","完成服务");
        StatusHashMap.put("REFUSE","拒绝服务");
        Map<Integer, String> PayStatusHashMap = new HashMap<>();
        PayStatusHashMap.put(0,"未支付");
        PayStatusHashMap.put(1,"已支付");
        PayStatusHashMap.put(2,"已结算");
        PayStatusHashMap.put(3,"已退款");
        List<ServiceRequest> serviceRequests = requestMapper.selectListByQuery(query);
        serviceRequests.forEach(e->{
            e.setStatus(StatusHashMap.get(e.getStatus()));
            e.setPayStatusStr(PayStatusHashMap.get(e.getPayStatus()));
        });
        ExcelUtil<ServiceRequest> util = new ExcelUtil<ServiceRequest>(ServiceRequest.class);
        util.exportExcel(response, serviceRequests, "服务审核");
    }



}
