<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.reporter.mapper.ReportColdRegisterMapper">
    <insert id="saveOrUpdateBatch">
        insert into all_report_cold_register
            (
                id, waybill_code, cold_store, use_time, sum_money, status, pay_status, ware_time, out_time, type, dept_id, agent_code
            )
        values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.waybillCode}, #{item.coldStore}, #{item.useTime}, #{item.sumMoney}, #{item.status}, #{item.payStatus},
                #{item.wareTime}, #{item.outTime}, #{item.type}, #{item.deptId}, #{item.agentCode}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
                waybill_code = VALUES(waybill_code),
                cold_store = VALUES(cold_store),
                use_time = VALUES(use_time),
                sum_money = VALUES(sum_money),
                status = VALUES(status),
                pay_status = VALUES(pay_status),
                ware_time = VALUES(ware_time),
                out_time = VALUES(out_time),
                type = VALUES(type),
                dept_id = VALUES(dept_id),
                agent_code = VALUES(agent_code)
    </insert>
</mapper>