package com.gzairports.hz.business.departure.domain.vo;

import com.gzairports.hz.business.departure.domain.HzColdRegister;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 冷藏登记返回数据
 * <AUTHOR>
 * @date 2024-07-12
 */
@Data
public class HzColdRegisterVo {

    /** 冷藏中运单数 */
    private Integer coldNum;

    /** 已出库运单数 */
    private Integer outNum;

    /** 已出库总金额 */
    private BigDecimal outMoney;

    /** 新冷藏运单列表 */
    private List<ColdQueryVo> list;
}
