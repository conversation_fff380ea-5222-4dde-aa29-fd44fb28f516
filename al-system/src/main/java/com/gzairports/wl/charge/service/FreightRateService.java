package com.gzairports.wl.charge.service;

import com.gzairports.wl.charge.domain.FreightRate;
import com.gzairports.wl.charge.domain.FreightRateItem;
import com.gzairports.wl.charge.domain.query.FreightRateQuery;
import com.gzairports.wl.charge.domain.vo.FreightRateItemVO;
import com.gzairports.wl.charge.domain.vo.FreightRateVO;

import java.util.List;

/**
 * 公布运价Service接口
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
public interface FreightRateService {

    /**
     * 查询公布运价列表
     *
     * @param query 查询参数
     * @return 公布运价列表
     */
    List<FreightRateVO> selectList(FreightRateQuery query);

    /**
     * 查询公布运价
     *
     * @param id 公布运价主键
     * @return 公布运价
     */
    FreightRateVO selectRateById(Long id);

    /**
     * 新增公布运价
     *
     * @param rate 公布运价
     * @return 结果
     */
    Long insertFreightRate(FreightRate rate);

    /**
     * 修改公布运价
     *
     * @param rate 公布运价
     * @return 结果
     */
    int updateFreightRate(FreightRate rate);

    /**
     * 删除公布运价
     *
     * @param id 公布运价主键
     * @return 结果
     */
    int deleteFreightRate(Long id);

    /**
     * 查看运价条目
     *
     * @param id 公布运价id
     * @return 公布运价条目集合
     */
    List<FreightRateItemVO> selectRateItemList(Long id);

    Long importItem(List<FreightRateItem> rateItems, Long rateId, boolean updateSupport);
}
