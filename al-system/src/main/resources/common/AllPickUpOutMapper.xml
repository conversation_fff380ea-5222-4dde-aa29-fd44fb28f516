<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.arrival.mapper.AllPickUpOutMapper">
    <resultMap id="billExportVoMap" type="com.gzairports.hz.business.arrival.domain.vo.BillExportVo">
        <id column="id" property="id"/>
        <result column="serial_no" property="serialNo"/>
        <result column="total_cost" property="totalCost"/>
        <result column="total_quantity" property="totalQuantity"/>
        <result column="total_weight" property="totalWeight"/>
        <result column="total_charge_weight" property="totalChargeWeight"/>
        <!--        <result column="pay_method" property="payMethod"/>-->
        <!--        <result column="handle_by" property="handleBy"/>-->
        <!--        <result column="pick_up_time" property="pickUpTime"/>-->
        <result column="remark" property="remark"/>
        <collection property="orderWaybillVos" javaType="java.util.ArrayList"
                    select="selectPickUpWaybill" column="{pick_up_id=id,waybill_type=waybillType}"
                    ofType="com.gzairports.common.business.arrival.domain.vo.OutOrderWaybillVo" />
    </resultMap>

    <select id="selectQueryList" resultType="com.gzairports.hz.business.arrival.domain.vo.WaybillQueryVo">
        select aaw.id as waybillId, aaw.waybill_code, aaw.replenish_bill, aaw.file_arr, aaw.prioritize, aaw.transfer_bill, aaw.source_port,
               aaw.carrier1, aaw.des1, aaw.carrier2, aaw.des2, aaw.carrier3, aaw.des3, aaw.des_port, aaw.quantity, aaw.weight, aaw.charge_weight,
               ao.pieces as cabinQuantity, ao.weight as cabinWeight, aaw.shipper, aaw.consign, aaw.consign_id_car, aaw.consign_phone,
               aaw.cargo_name, aaw.special_cargo_code1, aaw.cold_store, aaw.customs_supervision, aaw.is_transfer, aaw.transfer_no, aaw.arr_pay,
               aaw.cost_sum, aaw.remark
        from all_pick_up_out ao
            left join hz_arr_tally hat on hat.id = ao.tally_id
            left join all_air_waybill aaw on aaw.waybill_code = ao.waybill_code
            left join hz_flight_load hfl on hfl.id = hat.leg_id
            left join all_flight_info afi on hfl.flight_id = afi.flight_id
        where aaw.is_del = 0 and ao.is_cancel = 0
        <if test="waybillCode != null and waybillCode !=''">
            and aaw.waybill_code = #{waybillCode}
        </if>
        <if test="domint != null and domint != ''">
            and aaw.domint = #{domint}
        </if>
        <if test="type == 'ARR'">
            and aaw.type = #{type}
        </if>
        <if test="type == 'TRANSFER'">
            and aaw.transfer_bill = 1
        </if>
        <if test="sourcePort != null and sourcePort !=''">
            and aaw.source_port = #{sourcePort}
        </if>
        <if test="desPort != null and desPort !=''">
            and aaw.des_port = #{desPort}
        </if>
        <if test="shipper != null and shipper !=''">
            and aaw.shipper = #{shipper}
        </if>
        <if test="consign != null and consign !=''">
            and aaw.consign = #{consign}
        </if>
        <if test="agentCode != null and agentCode !=''">
            and aaw.agent_code = #{agentCode}
        </if>
        <if test="specialCode != null and specialCode !=''">
            and aaw.special_cargo_code = #{specialCode}
        </if>
        <if test="carrier != null and carrier !=''">
            and aaw.carrier = #{carrier}
        </if>
        <if test="flightNo != null and flightNo !=''">
            and CONCAT(afi.air_ways,afi.flight_no) = #{flightNo}
        </if>
        <if test="cargoCode != null and cargoCode !=''">
            and aaw.cargo_code = #{cargoCode}
        </if>
        <if test="startTime != null">
            and ao.out_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            and ao.out_time <![CDATA[<=]]> #{endTime}
        </if>
    </select>
    <select id="selectThList" resultType="com.gzairports.hz.business.arrival.domain.vo.ThVo">
        select apuo.out_time, apuw.can_pick_up_quantity as orderQuantity, apuw.can_pick_up_weight as orderWeight, apuo.pieces,
               apuo.weight, apuo.pick_up_code, apuo.remark, /*aipu.customer_name*/aipu.handle_by as 'customerName'
        from all_pick_up_out apuo
            left join all_pick_up_waybill apuw on apuo.tally_id = apuw.tally_id
            left join all_in_pick_up aipu on aipu.id = apuw.pick_up_id where apuo.waybill_code = #{waybillCode} and apuo.is_cancel = 0
    </select>
    <select id="selectTallyIdtList" resultType="java.lang.Long">
        select pick_up_id from all_pick_up_out where waybill_code = #{waybillCode} and is_cancel = 0
    </select>
    <select id="selectTime" resultType="java.util.Date">
        select out_time from all_pick_up_out where tally_id = #{tallyId} and waybill_code = #{waybillCode} and is_cancel = 0
    </select>
    <select id="pickedUp" resultType="com.gzairports.common.business.arrival.domain.vo.PickUpVo">
        select apuo.waybill_code, aipu.serial_no, aaw.cargo_name, aaw.special_cargo_code1, aaw.shipper, aaw.quantity, aaw.weight,
               apuo.pieces as orderQuantity, apuo.weight as orderWeight, aipu.customer_name, aipu.customer_id_no, haro.order_time,
               hat.tally_time, aipu.pick_up_time,aaw.consign
        from all_pick_up_out apuo
            left join all_in_pick_up aipu on aipu.id = apuo.pick_up_id
            left join all_air_waybill aaw on aaw.waybill_code = apuo.waybill_code
            left join hz_arr_tally hat on hat.id = apuo.tally_id
            left join hz_arr_record_order haro on haro.id = hat.record_order_id
        where aaw.is_del = 0 and aaw.type = 'ARR' and apuo.is_cancel = 0 and apuo.out_time is not null
        <if test="waybillCode != null and waybillCode != ''">
            and apuo.waybill_code like '%${waybillCode}%'
        </if>
        <if test="startTime != null">
            and aipu.pick_up_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            and aipu.pick_up_time <![CDATA[<=]]> #{endTime}
        </if>
        <if test="serialNo != null and serialNo != ''">
            and aipu.serial_no like '%${serialNo}%'
        </if>
        <if test="shipper != null and shipper != ''">
            and aaw.shipper like '%${shipper}%'
        </if>
    </select>
    <select id="getInfo" resultType="com.gzairports.common.business.arrival.domain.vo.PickUpInfoVo">
        select weight as canPickUpWeight, pieces as canPickUpQuantity,remark
        from all_pick_up_out
        where out_time is null and waybill_code = #{waybillCode} and is_cancel = 0
        order by create_time
        desc limit 1
    </select>
    <select id="selectTransferSouthByPickUpId"
            resultType="com.gzairports.hz.business.arrival.domain.HzArrRecordOrder">
        select id,waybill_code,is_south
        from hz_arr_record_order
        where id in (
            select record_order_id
            from all_pick_up_waybill apuw
                     left join hz_arr_tally hat on hat.id = apuw.tally_id
            where apuw.pick_up_id = #{pickUpId})
    </select>
    <update id="updateTransferSouth">
        update hz_arr_record_order
        set is_south = 1
        where id = #{id}
    </update>
    <select id="selectOutTime" resultType="com.gzairports.common.business.arrival.domain.AllPickUpOut">
        select id,out_time,waybill_code
        from all_pick_up_out
        where out_time is not null and waybill_code = #{waybillCode} and is_cancel = 0
        <if test="pickUpId != null and pickUpId != ''">
            and pick_up_id = #{pickUpId}
        </if>
        <if test="tallyId != null and tallyId != ''">
            and tally_id = #{tallyId}
        </if>
        limit 1
    </select>
    <select id="selectReportPuckOutList"
            resultType="com.gzairports.common.business.reporter.domain.ReportPickOut">
        select id, pick_up_id as reportPickUpId, pieces, weight, pick_up_code, out_time, waybill_code
        from all_pick_up_out
        <where>
            is_cancel = 0
            <if test="lastSyncTime != null">
                and update_time > #{lastSyncTime}
            </if>
            <if test="dateNow != null">
                and update_time <![CDATA[<=]]> #{dateNow}
            </if>
        </where>
    </select>
    <select id="selectReportOutTime" resultType="java.lang.String">
        select out_time from all_pick_up_out where tally_id = #{tallyId} and waybill_code = #{waybillCode} and is_cancel = 0 order by out_time desc limit 1
    </select>
    <select id="billExport" resultMap="billExportVoMap">
        with RankedData as (
        select aipu.id, aipu.serial_no, aipu.total_cost, aipu.total_quantity, aipu.total_weight, aipu.total_charge_weight,
        case
        when aipu.pay_method = '现金' or aipu.pay_method = '0' then '现金支付'
        when aipu.pay_method = '线上' or aipu.pay_method = '1' then '月结支付'
        when aipu.pay_method = '2' then '余额支付'
        when aipu.pay_method = '3' then '预授权支付'
        when aipu.pay_method is null then '空'
        end as payMethod,
        aipu.handle_by, aipu.pick_up_time, aipu.pay_time as settleTime, aipu.settle_user, aipu.remark,
        aaw.waybill_type as waybillType,
        ROW_NUMBER() OVER (partition by aaw.waybill_code, aipu.serial_no order by aipu.pick_up_time desc) as rn
        from all_in_pick_up aipu
        left join all_pick_up_waybill apuw on apuw.pick_up_id = aipu.id
        left join all_air_waybill aaw on aaw.waybill_code = apuw.waybill_code
        where aaw.type = 'ARR' and aaw.waybill_type = #{query.waybillType}
        <if test="query.waybillCode != null and query.waybillCode != ''">
            and aaw.waybill_code like concat('%',#{query.waybillCode},'%')
        </if>
        <if test="query.startTime != null">
            and aipu.pick_up_time <![CDATA[>=]]> #{query.startTime}
        </if>
        <if test="query.endTime != null">
            and aipu.pick_up_time <![CDATA[<=]]> #{query.endTime}
        </if>
        <if test="query.settleUserArr != null and query.settleUserArr.length > 0">
            and aipu.settle_user in
            <foreach collection="query.settleUserArr" item="settleUser" open="(" separator="," close=")">
                #{settleUser}
            </foreach>
        </if>
        <if test="query.agent != null and query.agent != ''">
            and aaw.agent_company = #{query.agent}
        </if>
        <if test="query.serialNo != null and query.serialNo != ''">
            and aipu.serial_no = #{query.serialNo}
        </if>
        <if test="query.payMethod != null and query.payMethod != ''">
            and (
            <choose>
                <when test="query.payMethod == '现金支付'">
                    (aipu.pay_method = '现金' or aipu.pay_method = '0')
                </when>
                <when test="query.payMethod == '月结支付'">
                    (aipu.pay_method = '线上' or aipu.pay_method = '1')
                </when>
                <when test="query.payMethod == '余额支付'">
                    aipu.pay_method = '2'
                </when>
                <when test="query.payMethod == '预授权支付'">
                    aipu.pay_method = '3'
                </when>
                <when test='query.payMethod == "空"'>
                    aipu.pay_method is null
                </when>
            </choose>
            )
        </if>
        <if test="query.isCancel != null and query.isCancel != ''">
            and (
            <choose>
                <when test="query.isCancel == '正常'">
                    (aipu.is_pay = '0' or aipu.is_pay = '1')
                </when>
                <when test="query.isCancel == '作废'">
                    (aipu.is_pay = '2')
                </when>
            </choose>
            )
        </if>
        )
        select distinct *
        from RankedData
        where rn = 1
        order by pick_up_time desc
    </select>
    <select id="selectPickUpWaybill" resultType="com.gzairports.common.business.arrival.domain.vo.OutOrderWaybillVo">
        select apuw.tally_id, apuw.pick_up_id, apuw.waybill_code, aaw.cargo_name, apuw.can_pick_up_weight as charge_weight, apuw.can_pick_up_weight as weight,
               apuw.can_pick_up_quantity as quantity
        from all_pick_up_waybill apuw
                 left join all_air_waybill aaw on aaw.waybill_code = apuw.waybill_code
        where apuw.pick_up_id = #{pick_up_id} and aaw.type = 'ARR' and aaw.waybill_type = #{waybill_type}
    </select>
    <select id="selectByKey" resultType="com.gzairports.common.business.arrival.domain.AllPickUpOut">
        select pieces, weight, out_time, pick_up_id, waybill_code, update_time
        from all_pick_up_out
        where (pick_up_id, waybill_code) in
        <foreach collection="pickUpWaybillPairs" item="key" open="(" separator="," close=")">
            (#{key.pickUpId}, #{key.waybillCode})
        </foreach>
    </select>
    <select id="selectBatch" resultType="com.gzairports.common.business.arrival.domain.AllPickUpOut">
        select *
        from all_pick_up_out
        where is_cancel = 0 and (pick_up_id, waybill_code) in
        <foreach collection="pickUpWaybillPairs" item="key" open="(" separator="," close=")">
            (#{key.pickUpId}, #{key.waybillCode})
        </foreach>
    </select>
    <update id="updateAllPickUpOutById">
        update all_pick_up_out
        <trim prefix="SET" suffixOverrides=",">
            <if test="pieces != null">pieces = #{pieces},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isCancel != null">is_cancel = #{isCancel},</if>
        </trim>
        where id = #{id}
    </update>

    <resultMap id="billExportAWBMMap" type="com.gzairports.hz.business.arrival.domain.vo.BillExportAWBMVo">
        <id column="id" property="id"/>
        <result column="serial_no" property="serialNo"/>
        <collection property="orderWaybillVos" javaType="java.util.ArrayList"
                    ofType="com.gzairports.hz.business.arrival.domain.vo.OutOrderWaybillAWBMVo">
            <result column="waybill_code" property="waybillCode"/>
            <result column="specialCode" property="specialCode"/>
            <result column="cargo_code" property="cargoCode"/>
            <result column="cargo_name" property="cargoName"/>
            <result column="quantity" property="quantity"/>
            <result column="weight" property="weight"/>
            <result column="volume" property="volume"/>
            <result column="consign" property="consign"/>
            <result column="dept_id" property="deptId"/>
            <result column="writer" property="writer"/>
            <result column="write_time" property="writeTime"/>
            <result column="domesticOrInternational" property="domesticOrInternational"/>
            <result column="customsSupervision" property="customsSupervision"/>
            <result column="reviewComplete" property="reviewComplete"/>
            <result column="flight" property="flight"/>
            <result column="voyage" property="voyage"/>
        </collection>
    </resultMap>

    <select id="billExportAWBM" resultMap="billExportAWBMMap">
        select aipu.id,
        aipu.serial_no,

        aaw.waybill_code,
        aaw.special_cargo_code1 as specialCode,
        aaw.cargo_code,
        aaw.cargo_name,
        aaw.quantity,
        aaw.weight,
        aaw.dept_id,
        aaw.consign,
        aaw.write_time,
        aaw.writer,
        'D' as domesticOrInternational,
        'N' as customsSupervision,
        'Y' as reviewComplete,

        CONCAT(aaw.flight_no1, '/', DATE_FORMAT(aaw.flight_date1, '%y-%m-%d')) as flight,
        CONCAT(aaw.source_port, '/', aaw.des_port) as voyage

        from all_in_pick_up aipu
        left join all_pick_up_waybill apuw on apuw.pick_up_id = aipu.id
        left join all_air_waybill aaw on aaw.waybill_code = apuw.waybill_code
        where aaw.type = 'ARR'
        and aaw.waybill_type = #{query.waybillType}
        <if test="query.waybillCode != null and query.waybillCode != ''">
            and aaw.waybill_code like concat('%',#{query.waybillCode},'%')
        </if>
        <if test="query.startTime != null">
            and aipu.pick_up_time <![CDATA[>=]]> #{query.startTime}
        </if>
        <if test="query.endTime != null">
            and aipu.pick_up_time <![CDATA[<=]]> #{query.endTime}
        </if>
        <if test="query.settleUserArr != null and query.settleUserArr.length > 0">
            and aipu.settle_user in
            <foreach collection="query.settleUserArr" item="settleUser" open="(" separator="," close=")">
                #{settleUser}
            </foreach>
        </if>
        <if test="query.agent != null and query.agent != ''">
            and aaw.agent_company = #{query.agent}
        </if>
        <if test="query.serialNo != null and query.serialNo != ''">
            and aipu.serial_no = #{query.serialNo}
        </if>
        <if test="query.payMethod != null and query.payMethod != ''">
            and (
            <choose>
                <when test="query.payMethod == '现金支付'">
                    (aipu.pay_method = '现金' or aipu.pay_method = '0')
                </when>
                <when test="query.payMethod == '月结支付'">
                    (aipu.pay_method = '线上' or aipu.pay_method = '1')
                </when>
                <when test="query.payMethod == '余额支付'">
                    aipu.pay_method = '2'
                </when>
                <when test="query.payMethod == '预授权支付'">
                    aipu.pay_method = '3'
                </when>
                <when test='query.payMethod == "空"'>
                    aipu.pay_method is null
                </when>
            </choose>
            )
        </if>
        <if test="query.isCancel != null and query.isCancel != ''">
            and (
            <choose>
                <when test="query.isCancel == '正常'">
                    (aipu.is_pay = '0' or aipu.is_pay = '1')
                </when>
                <when test="query.isCancel == '作废'">
                    (aipu.is_pay = '2')
                </when>
            </choose>
            )
        </if>
        # order by pick_up_time desc;
    </select>

    <select id="selectBillCostDetail" resultType="com.gzairports.common.charge.domain.vo.HzChargeItemsVo">
        select hawi.waybill_code,
        hci.charge_abb,
        hawi.total_charge,
        hawi.pick_up_id,
        hawi.edit_charge
        from hz_arr_waybill_item hawi
        left join hz_charge_ir_relation hcir on hcir.id = hawi.ir_id
        left join hz_charge_items hci on hci.id = hcir.item_id
        where hawi.waybill_code in
        <foreach collection="waybillCodes" item="wc" open="(" separator="," close=")">
            #{wc.waybillCode}
        </foreach>
    </select>
    <select id="selectTallyList" resultType="com.gzairports.common.business.arrival.domain.HzArrTally">
<!--        select *-->
<!--        from hz_arr_tally hat-->
<!--        <where>-->
<!--            aaw.-->
<!--        </where>-->
<!--        ;-->

    </select>
    <select id="selectBatchByTallyId" resultType="com.gzairports.common.business.arrival.domain.AllPickUpOut">
        select out_time,waybill_code, tally_id from all_pick_up_out
        where is_cancel = 0
          and (tally_id, waybill_code) in
        <foreach collection="tallyKeys" item="key" open="(" separator="," close=")">
            (#{key.tallyId}, #{key.waybillCode})
        </foreach>
    </select>
    <select id="selectBatchById" resultType="com.gzairports.common.business.arrival.domain.AllPickUpOut">
        SELECT id, out_time
        FROM all_pick_up_out
        WHERE 1=1
        AND id IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

</mapper>