package com.gzairports.common.business.arrival.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 提货结算返回数据
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
@Data
public class PickUpOutVo {

    /** 运单数 */
    private Integer num;

    /** 提货总件数 */
    private Integer totalQuantity;

    /** 提货总重量 */
    private BigDecimal totalWeight;

    /** 费用合计 */
    private BigDecimal totalCost;

    /** 支付方式 */
    private String payMethod;

    /** 已经支付 */
    private BigDecimal paid;

    /** 需补支付 */
    private BigDecimal premium;

    /** 提货时间 */
    private Date outTime;

    /** 二维码 */
    private String img;

    /** 代理人 */
    private Long deptId;
}
