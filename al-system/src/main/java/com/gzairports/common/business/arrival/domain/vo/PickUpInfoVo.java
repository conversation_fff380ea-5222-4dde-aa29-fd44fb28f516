package com.gzairports.common.business.arrival.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * app提货出库详情数据
 *
 * <AUTHOR>
 * @date 2024-08-07
 */
@Data
public class PickUpInfoVo {

    /** 办单id */
    private Long pickUpId;

    /** 次数 */
    private Integer num;

    /** 运单号 */
    private String waybillCode;

    /** 运单件数 */
    private Integer quantity;

    /** 运单重量 */
    private BigDecimal weight;

    /** 办单件数 */
    private Integer orderQuantity;

    /** 办单重量 */
    private BigDecimal orderWeight;

    /** 是否已支付 0 否 1 是 */
    private Integer isPay;

    /** 品名 */
    private String cargoName;

    /** 特货代码 */
    private String specialCaroCode1;

    /** 代理人 */
    private String shipper;

    /** 收货人 */
    private String consign;

    /** 提货码 */
    private String pickUpCode;

    /** 提货重量 */
    private BigDecimal canPickUpWeight;

    /** 提货件数 */
    private Integer canPickUpQuantity;

    /** 出库备注 */
    private String remark;

    /** 提货出库状态 */
    private String status;

    /** 是否交付  0 未交付 1 已交付*/
    private Integer deliverStatus;
}
