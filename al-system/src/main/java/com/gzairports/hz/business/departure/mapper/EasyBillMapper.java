package com.gzairports.hz.business.departure.mapper;

import com.gzairports.hz.business.departure.domain.query.EasyBillQuery;
import com.gzairports.hz.business.departure.domain.vo.EasyBillVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 简易开单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Mapper
public interface EasyBillMapper {

    /**
     * 简易开单列表查询
     * @param query 查询参数
     * @return 简易开单列表
     */
    List<EasyBillVo> selectEasyBillList(EasyBillQuery query);
}
