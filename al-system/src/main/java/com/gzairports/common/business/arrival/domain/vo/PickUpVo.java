package com.gzairports.common.business.arrival.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 运单数据
 *
 * <AUTHOR>
 * @date 2024-06-03
 */
@Data
public class PickUpVo {

    /** 主键id */
    private Long id;

    /** 流水号 */
    private String serialNo;

    /** 提货码 */
    private String pickUpCode;

    /** 理货id */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long tallyId;

    /** 理货id */
//    private List<Long> tallyIds;

    /** 录单id */
    private Long orderId;

    /** 航班号 */
    private String flightNo;

    /** 航班日期 */
    private String flightDate;

    /** 运单号 */
    private String waybillCode;

    /** 收货人 */
    private String consign;

    /** 备注 */
    private String remark;

    /** 发货人 */
    private String shipper;

    /** 代理人 */
    private String agentCode;

    /** 收货人证件号 */
    private String consignIdCar;

    /** 办单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;

    /** 理货完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date tallyTime;

    /** 货品代码 */
    private String cargoCode;

    /** 品名 */
    private String cargoName;

    /** 特货代码 */
    private String specialCargoCode1;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 计费重量 */
    private BigDecimal chargeWeight;

    /** 进处费费率 */
    private BigDecimal processingFeeRate;

    /** 费用总计 */
    private BigDecimal costSum;

    /** 可提货件数 */
    private Integer canPickUpQuantity;

    /** 可提货重量 */
    private BigDecimal canPickUpWeight;

    /** 办单件数 */
    private Integer orderQuantity;

    /** 办单重量 */
    private BigDecimal orderWeight;

    /** 已提货件数 */
    private Integer pickedUpQuantity;

    /** 已提货重量 */
    private BigDecimal pickedUpWeight;

    /** 运单理货状态 */
    private String status;

    /** 提货客户 */
    private String customerName;

    /** 提货人证件号 */
    private String customerIdNo;

    /** 提货办单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date pickUpTime;

    /** 费用明细 */
    private HzArrItemVo itemVo;
}
