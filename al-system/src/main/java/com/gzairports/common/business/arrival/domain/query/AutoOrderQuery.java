package com.gzairports.common.business.arrival.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 自动办单机办单查询参数
 *
 * <AUTHOR>
 * @date 2024-07-22
 */
@Data
public class AutoOrderQuery {

    /** 提货运单号 */
    private String waybillCode;

    /** 手机号 */
    private String phone;

    /** 提货客户 */
    private String customerName;

    /** 提货人证件号 */
    private String customerIdNo;

    /** 验证码 */
    private String code;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date execDate;

    /** 航班号 */
    private String flightNo;

    /** 运单状态 （未办单 未提货 已提货） */
    private String status;
}
