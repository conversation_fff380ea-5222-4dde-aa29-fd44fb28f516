package com.gzairports.common.business.reporter.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-02-19
 */
@Data
@TableName("all_report_node_query")
public class ReportNodeQuery {

    /** 主键id */
    private Long id;

    /** 运单号 */
    private String waybillCode;

    /** 航班号 */
    private String flightNo;

    @TableField(exist = false)
    private Date execDate;

    @TableField(exist = false)
    private Long tallyId;

    /** 降落时间 */
    private String terminalAlteratelAndInTime;

    /** 理货完成时间 */
    private String tallyTime;

    /** 理货耗时（分） */
    private Long tallyConsume;

    /** 录单时间 */
    private String orderTime;

    /** 站坪交接时间 */
    private String handoverTime;

    /** 站坪耗时（分） */
    private Long siteConsume;

    /** 提货办单时间 */
    private String pickUpTime;

    /** 支付时间 */
    private String payTime;

    /** 办单耗时（分） */
    private Long pickUpConsume;

    /** 出库时间 */
    private String outTime;

    /** 交付耗时 */
    private Long outConsume;
}
