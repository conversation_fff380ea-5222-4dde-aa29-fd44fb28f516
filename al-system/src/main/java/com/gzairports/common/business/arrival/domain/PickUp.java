package com.gzairports.common.business.arrival.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 办理提货表
 *
 * <AUTHOR>
 * @date 2024-06-03
 */
@Data
@TableName(value = "all_in_pick_up")
public class PickUp {

    /** 主键id */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 提货客户 */
    private String customerName;

    /** 流水号 */
    private String serialNo;

    /** 总票数 */
    private Integer totalCount;

    /** 总件数 */
    private Integer totalQuantity;

    /** 总重量 */
    private BigDecimal totalWeight;

    /** 总计费重量 */
    private BigDecimal totalChargeWeight;

    /** 总费用 */
    private BigDecimal totalCost;

    /** 提货人证件类型 */
    private String customerIdType;

    /** 提货人证件号 */
    private String customerIdNo;

    /** 办单完成时间 */
    private Date pickUpTime;

    /** 支付方式（0 现金 1月结 2余额 3预授权） */
    private String payMethod;

    /** 经办人 */
    private String handleBy;

    /** 备注 */
    private String remark;

    /** 提货码 */
    private String pickUpCode;

    /** 是否已支付 0 否 1 是 */
    private Integer isPay;

    /** 支付时间 */
    private Date payTime;

    /** 缴费状态 0 未授权支付 1 已授权支付 2 已余额支付 3 已支付(线下结算-现金) 4 已支付(线下结算-月结)
     *          5 已结算 (预授权支付) 6 已结算(余额支付) 7 已结算(线下结算-月结) 8 已结算(线下结算-现金)
     *          9 已退款 (预授权支付) 10 已退款(余额支付) 11已退款(线下结算-月结) 12 已退款(线下结算-现金)
     *          13 余额不足 14 取消支付*/
    private Integer payStatus;

    /** 申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private Date applyTime;

    /** 申请状态 */
    @TableField(exist = false)
    private String applyStatus;

    /** 提货时间 */
    private Date outTime;

    /** 代理人 */
    private Long deptId;

    /** 提货人手机号 */
    private String customerPhone;

    /** 结算客户 */
    private String settleUser;

    /** 结算客户简称 */
    private String settleUserAbb;

    /** 收货人 */
    private String consign;

    /** 收货人证件号 */
    private String consignIdNo;

    /** 收货人手机号 */
    private String consignPhone;

    /** 更新时间 */
    private Date updateTime;
}
