package com.gzairports.hz.business.departure.domain.query;

import lombok.Data;

import java.util.Date;

/**
 * 简易开单查询参数
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Data
public class EasyBillQuery {

    /** 开单时间 */
    private Date startTime;

    /** 开单时间 */
    private Date endTime;

    /** 运单号 */
    private String waybillCode;

    /** 发货代理人 */
    private String agentCode;

    /** 货品大类 */
    private String categoryName;

    /** 特货代码 */
    private String specialCode;
}
