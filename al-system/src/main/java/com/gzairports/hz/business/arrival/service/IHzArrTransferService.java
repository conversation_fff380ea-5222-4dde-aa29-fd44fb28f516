package com.gzairports.hz.business.arrival.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.hz.business.arrival.domain.HzArrTransfer;
import com.gzairports.hz.business.arrival.domain.query.HzArrTransferQuery;

import java.util.List;

/**
 * 进港交接Service接口
 *
 * <AUTHOR>
 * @date 2024-07-12
 */
public interface IHzArrTransferService extends IService<HzArrTransfer> {

    /**
     * 进港交接列表查询
     * @param query 查询参数
     * @return 交接列表
     */
    List<HzArrTransfer> selectList(HzArrTransferQuery query);

    /**
     * 根据id查询详情
     * @param id 进港交接id
     * @return 进港交接详情
     */
    HzArrTransfer getInfo(Long id);

    /**
     * 新增进港交接
     * @param transfer 新增参数
     * @return 结果
     */
    int add(HzArrTransfer transfer);
}
