<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.arrival.mapper.FlightLoadUldWaybillMapper">
    <select id="selectListById" resultType="com.gzairports.hz.business.departure.domain.vo.LoadUldVo">
        select lum.waybill_id as mawbId, lu.uld, lu.cabin from hz_flight_load_uld_waybill lum
            left join hz_flight_load_uld lu on lu.id = lum.load_uld_id
        where lu.flight_load_id = #{bizId}
    </select>
    <select id="selectLoadWaybillListByUldId"
            resultType="com.gzairports.hz.business.departure.domain.vo.ForwardImportWaybillVo">
        select hflw.id, hflw.waybill_id, aaw.cargo_name, aaw.waybill_code, aaw.source_port, aaw.des1, hflw.quantity, hflw.weight,
               aaw.weight as waybillWeight,aaw.quantity as waybillQuantity
        from hz_flight_load_uld_waybill hflw
                 left join all_air_waybill aaw on aaw.id = hflw.waybill_id
        where hflw.load_uld_id = #{id} and aaw.type = 'DEP'
    </select>
    <select id="selectListByLoadUldId"
            resultType="com.gzairports.hz.business.departure.domain.vo.FormalWaybillVo">
        select hflw.id, aaw.cargo_name, aaw.waybill_code, aaw.source_port, aaw.quantity as waybillQuantity, hflw.quantity,
               aaw.weight as waybillWeight, hflw.weight, aaw.des_port, aaw.special_cargo_code1, aaw.storage_transport_notes as remark,
               aaw.origin_bill, aaw.volume, hflw.is_edit
        from hz_flight_load_uld_waybill hflw
                 left join all_air_waybill aaw on aaw.id = hflw.waybill_id
        where hflw.load_uld_id = #{id}
         <if test="type != null">
            and is_edit = #{type}
        </if>
    </select>
    <select id="selectListByLoadUldIdAndUldWaybillId"
            resultType="com.gzairports.hz.business.departure.domain.vo.FormalWaybillVo">
        select hflw.id, aaw.cargo_name, aaw.category_name, aaw.waybill_code, aaw.source_port, aaw.quantity as waybillQuantity, hflw.quantity,
        aaw.weight as waybillWeight, hflw.weight, aaw.des_port, aaw.special_cargo_code1, aaw.storage_transport_notes as remark,
        aaw.origin_bill, aaw.volume, hflw.is_edit
        from hz_flight_load_uld_waybill hflw
        left join all_air_waybill aaw on aaw.id = hflw.waybill_id
        where hflw.load_uld_id = #{id}
        <if test="uldWaybillIds != null and uldWaybillIds.size() > 0">
        and hflw.id in
        <foreach collection="uldWaybillIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        </if>
    </select>
    <select id="selectLoadList" resultType="com.gzairports.common.business.arrival.domain.FlightLoadWaybill">
        (
            select hu.flight_load_id, afi.start_real_takeoff_time, hw.waybill_id, hw.quantity, hw.weight,
                   hw.collect_id
            from hz_flight_load_uld_waybill hw
                left join hz_flight_load_uld hu on hu.id = hw.load_uld_id
                left join hz_flight_load hfl on hfl.id = hu.flight_load_id
                left join all_flight_info afi on afi.flight_id = hfl.flight_id
            where hw.collect_id = #{collectId}
        )
        union all
        (
            select hw.flight_load_id,  afi.start_real_takeoff_time, hw.waybill_id, hw.quantity, hw.weight,
                   hw.collect_id
            from hz_flight_load_waybill hw
                left join hz_flight_load hfl on hfl.id = hw.flight_load_id
                left join all_flight_info afi on afi.flight_id = hfl.flight_id
            where collect_id = #{collectId}
        )
    </select>
    <select id="selectUldInfo" resultType="com.gzairports.hz.business.departure.domain.FlightLoadUld">
        select uld, cabin
        from hz_flight_load_uld hflu
                 left join hz_flight_load_uld_waybill hfluw on hflu.id = hfluw.load_uld_id
        where hflu.flight_load_id = #{id} and hfluw.waybill_id = #{id1}
    </select>
    <select id="selectListByIds"
            resultType="com.gzairports.common.business.arrival.domain.FlightLoadUldWaybill">
        select hzlw.id, hzlw.load_uld_id,hzlw.collect_id, hzlw.waybill_id, hzlw.quantity, hzlw.weight, aaw.waybill_code
        from hz_flight_load_uld_waybill hzlw
            left join all_air_waybill aaw on aaw.id = hzlw.waybill_id
        where hzlw.id in
        <foreach collection="flightLoadWaybillIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectFlightInfoById" resultType="com.gzairports.hz.business.cable.domain.vo.MsgFlightInfoVO">
        (
            select afi.flight_id, afi.air_ways as carrier, afi.exec_date as flightDate, afi.flight_no, afi.start_station as departureStation,
                   afi.terminal_station as nextStation, afi.craft_no as aircraftRegistration, afi.terminal_reall_and_in_time as eta,
                   afi.start_scheme_takeoff_time as etd, afi.start_scheme_takeoff_time
            from hz_flight_load_waybill hflw
                     left join hz_flight_load hfl on hfl.id = hflw.flight_load_id
                     left join all_flight_info afi on afi.flight_id = hfl.flight_id
            where 1=1
            <if test="waybillIdList != null and waybillIdList.size() > 0">
            and hflw.waybill_id in
                <foreach collection="waybillIdList" item="waybillId" open="(" separator="," close=")">
                  #{waybillId}
                </foreach>
            </if>
        )
        UNION ALL
        (
            select afi.flight_id, afi.air_ways as carrier, afi.exec_date as flightDate, afi.flight_no, afi.start_station as departureStation,
                   afi.terminal_station as nextStation, afi.craft_no as aircraftRegistration, afi.terminal_reall_and_in_time as eta,
                   afi.start_scheme_takeoff_time as etd, afi.start_scheme_takeoff_time
        from hz_flight_load_uld_waybill hfluw
                     left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
                     left join hz_flight_load hfl on hfl.id = hflu.flight_load_id
                     left join all_flight_info afi on afi.flight_id = hfl.flight_id
            where 1=1
            <if test="waybillIdList != null and waybillIdList.size() > 0">
                and hfluw.waybill_id in
                <foreach collection="waybillIdList" item="waybillId" open="(" separator="," close=")">
                    #{waybillId}
                </foreach>
            </if>
        )
    </select>

</mapper>