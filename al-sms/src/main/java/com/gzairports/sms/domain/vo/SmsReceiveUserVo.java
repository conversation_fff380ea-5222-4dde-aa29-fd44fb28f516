package com.gzairports.sms.domain.vo;

import lombok.Data;

import java.util.List;

/**
 * 短信接收人配置列表返回VO
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
public class SmsReceiveUserVo {

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 顶级部门ID
     */
    private Long topDeptId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 代理人ID
     */
    private Long agentId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 角色名称
     */
    private List<String> roleName;

    /**
     * 姓名-用户昵称
     */
    private String username;


    /**
     * 手机号码
     */
    private String phonenumber;

    /**
     * 对应代理人是否已配置
     */
    private Boolean isConfig;
}
