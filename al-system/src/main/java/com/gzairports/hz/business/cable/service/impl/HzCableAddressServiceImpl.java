package com.gzairports.hz.business.cable.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.basedata.service.impl.WeightServiceImpl;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.exception.ServiceException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.bean.BeanValidators;
import com.gzairports.hz.business.cable.domain.HzCableAddress;
import com.gzairports.hz.business.cable.domain.query.TypeAndAddressQuery;
import com.gzairports.hz.business.cable.mapper.HzCableAddressMapper;
import com.gzairports.hz.business.cable.service.IHzCableAddressService;
import com.gzairports.wl.charge.domain.FreightRateAir;
import com.gzairports.wl.charge.domain.vo.FreightRateAirVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.Validator;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;


/**
 * 电报地址管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
@Service
public class HzCableAddressServiceImpl implements IHzCableAddressService
{

    private static final Logger log = LoggerFactory.getLogger(HzCableAddressServiceImpl.class);

    @Autowired
    private HzCableAddressMapper hzCableAddressMapper;

    @Autowired
    private Validator validator;

    /**
     * 查询电报地址管理
     * 
     * @param id 电报地址管理主键
     * @return 电报地址管理
     */
    @Override
    public HzCableAddress selectHzCableAddressById(Long id)
    {
        HzCableAddress hzCableAddress = hzCableAddressMapper.selectHzCableAddressById(id);
        if (StringUtils.isNotEmpty(hzCableAddress.getInteractionTypes())){
            List<String> interactionTypeList = Arrays.asList(hzCableAddress.getInteractionTypes().split(","));
            hzCableAddress.setInteractionTypeList(interactionTypeList);
        }
        return hzCableAddress;
    }

    /**
     * 查询电报地址管理列表
     * 
     * @param hzCableAddress 电报地址管理
     * @return 电报地址管理
     */
    @Override
    public List<HzCableAddress> selectHzCableAddressList(HzCableAddress hzCableAddress)
    {
        List<HzCableAddress> cableAddresses = hzCableAddressMapper.selectHzCableAddressList(hzCableAddress);
        for (HzCableAddress cableAddress : cableAddresses) {
            if (StringUtils.isNotEmpty(cableAddress.getInteractionTypes())){
                List<String> interactionTypeList = Arrays.asList(cableAddress.getInteractionTypes().split(","));
                cableAddress.setInteractionTypeList(interactionTypeList);
            }
        }
        return cableAddresses;
    }

    /**
     * 新增电报地址管理
     * 
     * @param hzCableAddress 电报地址管理
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertHzCableAddress(HzCableAddress hzCableAddress)
    {
        if (StringUtils.isEmpty(hzCableAddress.getAirlinesCode()) && StringUtils.isEmpty(hzCableAddress.getAirportCode())){
            throw new CustomException("航司二字码和机场三字码二选一必填");
        }
        if (StringUtils.isEmpty(hzCableAddress.getCableAddress()) && StringUtils.isEmpty(hzCableAddress.getCaacAddress())){
            throw new CustomException("电报地址和民航局地址二选一必填");
        }
        if (CollectionUtils.isEmpty(hzCableAddress.getInteractionTypeList())){
            throw new CustomException("交互方式必填");
        }

        int count1 = hzCableAddressMapper.selectCount(new QueryWrapper<HzCableAddress>()
                .eq("cable_address", hzCableAddress.getCableAddress())
                .eq("is_del", 0));
        if (count1 > 0){
            throw new CustomException("电报地址存在重复");
        }

        if (StringUtils.isNotEmpty(hzCableAddress.getAirlinesCode())){
            Integer count = hzCableAddressMapper.selectCount(new QueryWrapper<HzCableAddress>()
                    .eq("airlines_code", hzCableAddress.getAirlinesCode())
                    .eq("is_del", 0));
            if (count > 0){
                throw new CustomException("航司二字码存在重复");
            }
        }
        if (StringUtils.isNotEmpty(hzCableAddress.getAirportCode())){
            Integer count = hzCableAddressMapper.selectCount(new QueryWrapper<HzCableAddress>()
                    .eq("airport_code", hzCableAddress.getAirportCode())
                    .eq("is_del", 0).ne("id",hzCableAddress.getId()));
            if (count > 0){
                throw new CustomException("机场三字码存在重复");
            }
        }
        if (StringUtils.isNotEmpty(hzCableAddress.getCaacAddress())){
            Integer count = hzCableAddressMapper.selectCount(new QueryWrapper<HzCableAddress>()
                    .eq("mq_queue", hzCableAddress.getMqQueue())
                    .eq("is_del", 0));
            if (count > 0){
                throw new CustomException("rabbitmq发报队列存在重复");
            }
        }
        if (StringUtils.isNotEmpty(hzCableAddress.getAirportCode())){
            Integer count = hzCableAddressMapper.selectCount(new QueryWrapper<HzCableAddress>()
                    .eq("ftp_list", hzCableAddress.getFtpList())
                    .eq("is_del", 0));
            if (count > 0){
                throw new CustomException("FTP发报目录存在重复");
            }
        }
        String interactionType = String.join(",", hzCableAddress.getInteractionTypeList());
        hzCableAddress.setInteractionTypes(interactionType);
        validateInteractionFields(hzCableAddress);
        hzCableAddress.setCreateTime(DateUtils.getNowDate());
        hzCableAddress.setCreateBy(SecurityUtils.getUsername());
        return hzCableAddressMapper.insertHzCableAddress(hzCableAddress);
    }

    /**
     * 修改电报地址管理
     * 
     * @param hzCableAddress 电报地址管理
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateHzCableAddress(HzCableAddress hzCableAddress)
    {
        if (StringUtils.isEmpty(hzCableAddress.getAirlinesCode()) && StringUtils.isEmpty(hzCableAddress.getAirportCode())){
            throw new CustomException("航司二字码和机场三字码二选一必填");
        }
        if (StringUtils.isEmpty(hzCableAddress.getCableAddress()) && StringUtils.isEmpty(hzCableAddress.getCaacAddress())){
            throw new CustomException("电报地址和民航局地址二选一必填");
        }
        if (CollectionUtils.isEmpty(hzCableAddress.getInteractionTypeList())){
            throw new CustomException("交互方式必填");
        }

        int count1 = hzCableAddressMapper.selectCount(new QueryWrapper<HzCableAddress>()
                .eq("cable_address", hzCableAddress.getCableAddress())
                .eq("is_del", 0).ne("id",hzCableAddress.getId()));
        if (count1 > 0){
            throw new CustomException("电报地址存在重复");
        }

        if (StringUtils.isNotEmpty(hzCableAddress.getAirlinesCode())){
            Integer count = hzCableAddressMapper.selectCount(new QueryWrapper<HzCableAddress>()
                    .eq("airlines_code", hzCableAddress.getAirlinesCode())
                    .eq("is_del", 0).ne("id",hzCableAddress.getId()));
            if (count > 0){
                throw new CustomException("航司二字码存在重复");
            }
        }
        if (StringUtils.isNotEmpty(hzCableAddress.getAirportCode())){
            Integer count = hzCableAddressMapper.selectCount(new QueryWrapper<HzCableAddress>()
                    .eq("airport_code", hzCableAddress.getAirportCode())
                    .eq("is_del", 0).ne("id",hzCableAddress.getId()));
            if (count > 0){
                throw new CustomException("机场三字码存在重复");
            }
        }
        if (StringUtils.isNotEmpty(hzCableAddress.getCaacAddress())){
            Integer count = hzCableAddressMapper.selectCount(new QueryWrapper<HzCableAddress>()
                    .eq("mq_queue", hzCableAddress.getMqQueue())
                    .eq("is_del", 0).ne("id",hzCableAddress.getId()));
            if (count > 0){
                throw new CustomException("rabbitmq发报队列存在重复");
            }
        }
        if (StringUtils.isNotEmpty(hzCableAddress.getAirportCode())){
            Integer count = hzCableAddressMapper.selectCount(new QueryWrapper<HzCableAddress>()
                    .eq("ftp_list", hzCableAddress.getFtpList())
                    .eq("is_del", 0).ne("id",hzCableAddress.getId()));
            if (count > 0){
                throw new CustomException("FTP发报目录存在重复");
            }
        }
        hzCableAddress.setUpdateTime(DateUtils.getNowDate());
        hzCableAddress.setUpdateBy(SecurityUtils.getUsername());
        if (!CollectionUtils.isEmpty(hzCableAddress.getInteractionTypeList())){
            String interactionType = String.join(",", hzCableAddress.getInteractionTypeList());
            hzCableAddress.setInteractionTypes(interactionType);
        }
        validateInteractionFields(hzCableAddress);
        return hzCableAddressMapper.updateHzCableAddress(hzCableAddress);
    }


    /**
     * 删除电报地址管理信息
     * 
     * @param id 电报地址管理主键
     * @return 结果
     */
    @Override
    public int deleteHzCableAddressById(Long id)
    {
        HzCableAddress hzCableAddress = hzCableAddressMapper.selectHzCableAddressById(id);
        hzCableAddress.setIsDel(1);
        return hzCableAddressMapper.updateHzCableAddress(hzCableAddress);
    }

    /**
     * 导入航司运价条目
     * @param cableAddresses 文件数据
     * @param updateSupport 导入所需数据
     * @return 结果
     */
    @Override
    public String importCableAddress(List<HzCableAddress> cableAddresses, boolean updateSupport) {
        if (StringUtils.isNull(cableAddresses) || cableAddresses.size() == 0)
        {
            throw new ServiceException("导入航司运价数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (HzCableAddress address : cableAddresses) {
            try {
                HzCableAddress hzCableAddress = hzCableAddressMapper.selectOne(new QueryWrapper<HzCableAddress>()
                        .eq("cable_address", address.getCableAddress())
                        .eq("airport_code", address.getAirportCode())
                        .eq("airlines_code", address.getAirlinesCode())
                        .eq("is_del", 0));
                if (StringUtils.isNull(hzCableAddress)) {
                    BeanValidators.validateWithException(validator, address);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    address.setCreateBy(loginUser.getUsername());
                    address.setCreateTime(DateUtils.getNowDate());
                    hzCableAddressMapper.insert(address);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、地址 " + address.getCableAddress() + " 导入成功");
                }
                else if (updateSupport) {
                    BeanValidators.validateWithException(validator, address);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    address.setId(hzCableAddress.getId());
                    address.setUpdateBy(loginUser.getUsername());
                    address.setUpdateTime(DateUtils.getNowDate());
                    hzCableAddressMapper.updateById(address);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、地址 " + address.getCableAddress() + " 更新成功");
                }
                else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、地址 " + address.getCableAddress() + " 已存在");
                }
            }catch (Exception e){
                failureNum++;
                String msg = "<br/>" + failureNum + "、地址 " + address.getCableAddress() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 新版查询电报地址列表
     * @return 电报地址列表
     */
    @Override
    public List<TypeAndAddressQuery> newSelectHzCableAddressList() {
        return hzCableAddressMapper.newSelectHzCableAddressList();
    }

    public static void validateInteractionFields(HzCableAddress address) {
        List<String> interactionTypeList = new ArrayList<>();
        if (StringUtils.isNotEmpty(address.getInteractionTypes())) {
            interactionTypeList = Arrays.asList(address.getInteractionTypes().split(","));
        }
        if (!CollectionUtils.isEmpty(interactionTypeList)) {
            for (String type : interactionTypeList) {
                switch (type) {
                    case "空管报文":
                        if (StringUtils.isEmpty(address.getCableAddress())) {
                            throw new CustomException("请填写【空管报文】对应的电报地址");
                        }
                        break;
                    case "民航局报文":
                        if (StringUtils.isEmpty(address.getCaacAddress())) {
                            throw new CustomException("请填写【民航局报文】对应的民航局地址");
                        }
                        break;
                    case "邮箱报文":
                        if (StringUtils.isEmpty(address.getEmailAddress())) {
                            throw new CustomException("请填写【邮箱报文】对应的邮箱地址");
                        }
                        break;
                    case "FTP收发报文":
                        if (StringUtils.isEmpty(address.getFtpList())) {
                            throw new CustomException("请填写【FTP收发报文】对应的FTP发报目录");
                        }
                        break;
                    case "rabbitmq收发报文":
                        if (StringUtils.isEmpty(address.getMqQueue())) {
                            throw new CustomException("请填写【rabbitmq收发报文】对应的RabbitMQ队列");
                        }
                        break;
                    default:
                        break;
                }
            }
        }
    }
}
