package com.gzairports.common.basedata.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.basedata.domain.BaseAirportCode;
import com.gzairports.common.basedata.domain.BaseCityCode;
import com.gzairports.common.basedata.domain.query.AirportCodeQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 机场代码Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Mapper
public interface AirportCodeMapper extends BaseMapper<BaseAirportCode> {

    /**
     * 查询机场代码列表
     *
     * @param query 查询参数
     * @return 机场代码列表
     */
    List<BaseAirportCode> selectAirportCodeList(AirportCodeQuery query);

    /**
     * 新增机场代码
     *
     * @param code 机场代码
     * @return 结果
     */
    int insertAirportCode(BaseAirportCode code);

    /**
     * 修改机场代码
     *
     * @param code 机场代码
     * @return 结果
     */
    int updateAirportCode(BaseAirportCode code);

    /**
     * 删除机场代码
     *
     * @param ids 机场代码id集合
     * @return 结果
     */
    int delAirportCode(Long[] ids);

    /**
     * 根据code查询机场代码
     * @param code 机场code
     * @return 结果
     */
    BaseAirportCode selectByCode(String code);

    /**
     * 根据城市名称查询城市代码
     * @param departureCity 名称
     * @return 城市信息
     */
    List<BaseAirportCode> selectCityByName(String departureCity);
}
