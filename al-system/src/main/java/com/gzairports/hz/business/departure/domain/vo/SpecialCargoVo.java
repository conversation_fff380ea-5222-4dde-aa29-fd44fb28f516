package com.gzairports.hz.business.departure.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 特种货物报文运单数据
 *
 * <AUTHOR>
 * @date 2024-10-14
 */
@Data
public class SpecialCargoVo {

    /** 主键id */
    private Long id;

    /** 运单号 */
    private String waybillCode;

    /** 特货代码 */
    private String specialCargoCode1;

    /** 危险品UN编码 */
    private String dangerCode;

    /** 紧急联系人 */
    private String emergentContact;

    /** 联系电话 */
    private String contactPhone;

    /** 品名 */
    private String cargoName;

    /** 航班日期 */
    private Date execDate;

    /** 航班号 */
    private String flightNo;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 体积 */
    private BigDecimal volume;

    /** 始发地 */
    private String sourcePort;

    /** 目的地 */
    private String desPort;
}
