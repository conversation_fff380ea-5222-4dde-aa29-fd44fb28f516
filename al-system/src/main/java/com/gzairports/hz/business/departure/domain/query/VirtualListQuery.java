package com.gzairports.hz.business.departure.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 虚拟收运列表查询参数
 *
 * <AUTHOR>
 * @date 2024-06-26
 */
@Data
public class VirtualListQuery {

    /** 航班日期 */
    private Date flightDate1;

    /** 航班号 */
    private String flightNo1;

    /** 代理人 */
    private String agent;

    /** 到达站1 */
    private String des1;

    /**开始时间*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**结束时间*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 运单号 */
    private String waybillCode;
}
