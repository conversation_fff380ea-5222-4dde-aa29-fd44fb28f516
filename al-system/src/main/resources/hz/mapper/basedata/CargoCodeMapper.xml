<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.CargoCodeMapper">
    <insert id="insertCargoCode" parameterType="BaseCargoCode">
        insert into base_cargo_code
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="code != null and code != ''">code,</if>
            <if test="categoryCode != null and categoryCode != ''">category_code,</if>
            <if test="chineseName != null and chineseName != ''">chinese_name,</if>
            <if test="englishName != null and englishName != ''">english_name,</if>
            <if test="isSpecial != null">is_special,</if>
            <if test="isArrival != null">is_arrival,</if>
            <if test="isOut != null">is_out,</if>
            <if test="domestic != null">domestic,</if>
            <if test="international != null">international,</if>
            <if test="specialCargoCode1 != null">special_cargo_code1,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="code != null and code != ''">#{code},</if>
            <if test="categoryCode != null and categoryCode != ''">#{categoryCode},</if>
            <if test="chineseName != null and chineseName != ''">#{chineseName},</if>
            <if test="englishName != null and englishName != ''">#{englishName},</if>
            <if test="isSpecial != null">#{isSpecial},</if>
            <if test="isArrival != null">#{isArrival},</if>
            <if test="isOut != null">#{isOut},</if>
            <if test="domestic != null">#{domestic},</if>
            <if test="international != null">#{international},</if>
            <if test="specialCargoCode1 != null">#{specialCargoCode1},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>
    <update id="updateCargoCode" parameterType="BaseCargoCode">
        update base_cargo_code
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="categoryCode != null and categoryCode != ''">category_code = #{categoryCode},</if>
            <if test="chineseName != null and chineseName != ''">chinese_name = #{chineseName},</if>
            <if test="englishName != null and englishName != ''">english_name = #{englishName},</if>
            <if test="isSpecial == 1 and specialCargoCode1 != null">is_special = #{isSpecial},special_cargo_code1 = #{specialCargoCode1},</if>
            <if test="isSpecial == 0">is_special = #{isSpecial},special_cargo_code1 = null,</if>
            <if test="isArrival != null">is_arrival = #{isArrival},</if>
            <if test="isOut != null">is_out = #{isOut},</if>
            <if test="domestic != null">domestic = #{domestic},</if>
            <if test="international != null">international = #{international},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null and createTime != ''">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="delCargoCode" parameterType="Long">
        update base_cargo_code set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <select id="selectCargoCodeList" resultType="com.gzairports.common.basedata.domain.BaseCargoCode">
        select id, code, category_code, chinese_name, english_name, is_special, is_arrival, is_out, domestic, international, special_cargo_code1 from base_cargo_code
        <where>
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="code != null  and code != ''"> and code like concat('%',#{code},'%')</if>
            <if test="isSpecial != null"> and is_special = #{isSpecial}</if>
            <if test="isArrival != null"> and is_arrival = #{isArrival}</if>
            <if test="isOut != null"> and is_out = #{isOut}</if>
            <if test="domestic != null"> and domestic = #{domestic}</if>
            <if test="international != null"> and international = #{international}</if>
            <if test="categoryCode != null"> and category_code = #{categoryCode}</if>
            and is_del = 0
        </where>
    </select>
    <select id="selectByCode" resultType="com.gzairports.common.basedata.domain.BaseCargoCode">
        select id, code, chinese_name, english_name, category_code, special_cargo_code1
        from base_cargo_code
        where code = #{code} and is_del = 0
    </select>
    <select id="selectCargoCodeByCode" resultType="com.gzairports.common.basedata.domain.BaseCargoCode">
        select id, code, category_code, chinese_name, english_name, is_special, is_arrival, is_out, domestic, international, special_cargo_code1
        from base_cargo_code where code = #{code} and is_del = 0
    </select>
    <select id="selectCategoryCodeList" resultType="java.lang.String">
        select distinct category_code from base_cargo_code
        <where>
            is_del = 0
            <if test="cargoCode != null  and cargoCode != ''"> and code = #{cargoCode}</if>
        </where>
    </select>
    <select id="selectCargoCodeListByCategoryCode" resultType="com.gzairports.common.basedata.domain.BaseCargoCode">
        select code,chinese_name
        from base_cargo_code
        <where>
            is_del = 0
            <if test="categoryCode != null  and categoryCode != ''"> and category_code = #{categoryCode}</if>
        </where>
    </select>
    <select id="selectCargoNameByCategoryCode" resultType="java.lang.String">
        select chinese_name from base_cargo_code
        where  is_del = 0 and code = #{cargoCode}
        limit 1
    </select>
    <select id="selectCargoCodeListByCodes" resultType="com.gzairports.common.basedata.domain.BaseCargoCode">
        select code,chinese_name
        from base_cargo_code
        <where>
            is_del = 0
            <if test="categoryCode != null  and categoryCode.size() > 0">
                and category_code in
                <foreach collection="categoryCode" item="code" open="(" separator="," close=")">
                  #{code}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectListByCategory" resultType="com.gzairports.common.charge.domain.vo.IrRelationVo">
        select category_code, code, chinese_name from base_cargo_code where category_code in
        <if test="list != null and list.size() > 0">
            <foreach collection="list" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
    </select>
    <select id="selectIrByName" resultType="com.gzairports.common.charge.domain.vo.IrRelationVo">
        select category_code, code, chinese_name from base_cargo_code where LEFT(code,2) = category_code AND chinese_name = #{ruleCaroName} and is_del = 0
    </select>
    <select id="selectCargoInfo" resultType="com.gzairports.common.basedata.domain.BaseCargoCode">
        select id, category_code, code, chinese_name, english_name, is_special, is_arrival, is_out, domestic, international, special_cargo_code1
        from base_cargo_code where code = #{cargoCode}
        limit 1
    </select>
</mapper>
