package com.gzairports.web.controller.departure;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.common.business.departure.domain.Booking;
import com.gzairports.common.business.departure.domain.query.BookingQuery;
import com.gzairports.common.business.departure.domain.vo.BookingVo;
import com.gzairports.common.business.departure.service.IBookingService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 订舱Controller
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@RestController
@RequestMapping("/dep/booking")
public class BookingController extends BaseController {

    @Autowired
    private IBookingService bookingService;

    /**
     * 查询订舱列表
     */
//    @PreAuthorize("@ss.hasPermi('dep:booking:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询订舱列表")
    public TableDataInfo list(BookingQuery query){
        startPage();
        List<BookingVo> list = bookingService.selectList(query);
        return getDataTable(list);
    }

    /**
     * 导出订舱列表
     */
    @PreAuthorize("@ss.hasPermi('dep:booking:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出订舱列表")
    public void export(HttpServletResponse response, BookingQuery query)
    {
        List<BookingVo> list = bookingService.selectList(query);
        ExcelUtil<BookingVo> util = new ExcelUtil<BookingVo>(BookingVo.class);
        util.exportExcel(response, list, "订舱");
    }

    /**
     * 新增订舱
     */
    @PreAuthorize("@ss.hasPermi('dep:booking:add')")
    @PostMapping("/add")
    @ApiOperation(value = "新增订舱")
    public AjaxResult add(@RequestBody Booking booking)
    {
        return toAjax(bookingService.add(booking));
    }

    /**
     * 查看详情
     */
    @PreAuthorize("@ss.hasPermi('dep:booking:getInfo')")
    @GetMapping("/getInfo/{id}")
    @ApiOperation(value = "查看详情")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(bookingService.getInfo(id));
    }
}
