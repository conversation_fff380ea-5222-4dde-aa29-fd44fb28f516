package com.gzairports.wl.charge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.wl.charge.domain.FreightRateItem;
import com.gzairports.wl.charge.domain.query.FreightRateItemQuery;
import com.gzairports.wl.charge.domain.vo.FreightRateItemVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 公布运价条目Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@Mapper
public interface FreightRateItemMapper extends BaseMapper<FreightRateItem> {

    /**
     * 查看公布运价条目
     *
     * @param id 公布运价id
     * @return 公布运价条目集合
     */
    List<FreightRateItemVO> selectRateItemList(Long id);

    /**
     * 删除运价条目
     *
     * @param id 运价条目主键
     * @return 结果
     */
    int removeRateItem(Long id);

    /**
     * 查询运价条目列表
     *
     * @param query 运价条目查询参数
     * @return 结果
     */
    List<FreightRateItemVO> selectItemList(FreightRateItemQuery query);
}
