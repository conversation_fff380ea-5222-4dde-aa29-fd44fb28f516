package com.gzairports.hz.business.departure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.gzairports.hz.business.arrival.domain.query.FlightFileQuery;
import com.gzairports.hz.business.arrival.domain.vo.LegVo;
import com.gzairports.hz.business.departure.domain.FlightLoad;
import com.gzairports.hz.business.departure.domain.query.*;
import com.gzairports.hz.business.departure.domain.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 航班配载Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-26
 */
@Mapper
public interface FlightLoadMapper extends BaseMapper<FlightLoad> {

    /**
     * 查询航班配载列表
     * @param query 查询条件
     * @return 配载列表
     */
    List<FlightLoadVo> selectLoadList(@Param("query") FlightLoadQuery query,
                                      @Param("noDisplayedList") String[] split);

    /**
     * 根据条件查询航班配载id
     * @param query 查询条件
     * @return 结果
     */
    ForwardImportVo addQuery(AddQuery query);

    /**
     * 航段查询
     * @param query 查询条件
     * @return 航段集合
     */
    List<String> legQuery(AddQuery query);

    /**
     * 根据id查询正式舱单数据
     * @param id 航班配载id
     * @return 正式舱单数据
     */
    FormalManifestVo selectForMalManifestById(Long id);

    /**
     * 组货调度列表查询
     * @param query 查询参数
     * @return 组货调度列表
     */
    List<GroupCargoVo> selectGroupList(GroupCargoQuery query);

    /**
     * 根据id查询组货调度详情
     * @param id 配载id
     * @return 组货详情
     */
    GroupCargoVo selectByGroupById(Long id);

    /**
     * 根据配载id查询航班号
     * @param id 配载id
     * @return 航班号
     */
    String selectNoById(Long id);

    /**
     * 根据航班信息查询航段
     * @param query 查询条件
     * @return 结果
     */
    Long selectLegId(FlightFileQuery query);

    /**
     * 航段查询
     * @param query 查询条件
     * @return 航段集合
     */
    List<LegVo> getLeg(FlightFileQuery query);

    /**
     * 根据航班信息查询航班id
     * @param query 航班信息
     * @return 航班id
     */
    Long selectFlightId(FlightFileQuery query);

    /**
     * 根据条件查询配载数据
     * @param query 查询条件
     * @return 结果
     */
    List<DetailedVo> selectStatusLoadList(WaybillInfoQuery query);

    /**
     * 更新航班配载表
     * @param flightLoad 航班配载信息
     * @return 结果
     * */
    int updateFlightLoadById(FlightLoad flightLoad);

    /**
     * 根据拉下信息查询配载航段号
     * @param flightId 航班id
     * @return 配载航段号
     */
    List<Long> selectLegIdByVo(Long flightId);

    /**
     * 根据四位或多位航班号查出航司二字码
     * @param airlinesCode 四位或多位航班号
     */
    String getAirLinesByCode(@Param("airlinesCode") String airlinesCode,
                             @Param("flightDate") String flightDate,
                             @Param("type") String type);

    /**
     * 根据航班号和航班日期查询配载id
     * @param execDate 航班日期
     * @param flightNo 航班机号
     * @return 结果
     */
    List<Long> selectIdByInfo(@Param("execDate") Date execDate,@Param("flightNo") String flightNo);

    List<Long> selectFlightIds(@Param("loadIds") List<Long> loadIds);

    List<YesterdayLoadVo> selectYesterdayLoadList(Long flightId);

    /**
     * 根据集装器/板箱号 查询配载总票数
     * */
    int selectCountByUldNo(@Param("uldNo") String uldNo,@Param("flightId") Long flightId);

    List<Long> selectIdByFlightId(Long flightId);
}
