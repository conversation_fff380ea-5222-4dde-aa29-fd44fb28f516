package com.gzairports.hz.business.departure.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 退货管理表
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@Data
@TableName("hz_dep_exit_cargo")
public class HzDepExitCargo {

    /** 主键id */
    private Long id;

    /** 运单号 */
    private String waybillCode;

    /** 退货件数 */
    private Integer exitQuantity;

    /** 退货重量 */
    private BigDecimal exitWeight;

    /** 发起时间 */
    private Date startTime;

    /** 备注 */
    private String remark;

    /** 退货交换备注 */
    private String exitTransferRemark;

    /** 接收人签字图片地址 */
    private String receiveUrl;

    /** 退货状态
     * 申请退货:apply_return
     * 已退货:been_return
     * 同意退货:agree_return
     * 拒绝退货:refuse_return
     * */
    private String status;

    /** 操作人 */
    private String operName;

    /** 退货时间 */
    private Date exitStoreTime;
}
