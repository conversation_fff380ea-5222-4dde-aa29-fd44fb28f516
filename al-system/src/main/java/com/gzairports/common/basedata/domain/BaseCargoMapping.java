package com.gzairports.common.basedata.domain;

import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.util.Date;


/**
 * 货站代码映射
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Data
public class BaseCargoMapping {

    /** 主键id */
    private Long id;

    /** 货站货品代码 */
    @Excel(name = "货站货品代码")
    private String cargoCode;

    /** 货站货品名称 */
    @Excel(name = "货站货品名称")
    private String cargoName;

    /** 航空公司货品代码 */
    @Excel(name = "航空公司货品代码")
    private String airCargoCode;

    /** 航空公司货品名称 */
    @Excel(name = "航空公司货品名称")
    private String airCargoName;

    /** 航空公司代码 */
    @Excel(name = "航空公司代码")
    private String airCode;

    /**  部门编号 */
    private Long deptId;

    /** 是否公用 */
    private Long isCommon;

    /** 是否删除 */
    private Integer isDel;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;
}
