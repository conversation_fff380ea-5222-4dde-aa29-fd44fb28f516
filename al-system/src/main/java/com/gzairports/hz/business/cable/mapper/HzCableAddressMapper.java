package com.gzairports.hz.business.cable.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.hz.business.cable.domain.HzCableAddress;
import com.gzairports.hz.business.cable.domain.query.TypeAndAddressQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 电报地址管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
@Mapper
public interface HzCableAddressMapper extends BaseMapper<HzCableAddress>
{
    /**
     * 查询电报地址管理
     * 
     * @param id 电报地址管理主键
     * @return 电报地址管理
     */
    public HzCableAddress selectHzCableAddressById(Long id);

    /**
     * 查询电报地址管理列表
     * 
     * @param hzCableAddress 电报地址管理
     * @return 电报地址管理集合
     */
    public List<HzCableAddress> selectHzCableAddressList(HzCableAddress hzCableAddress);

    /**
     * 新增电报地址管理
     * 
     * @param hzCableAddress 电报地址管理
     * @return 结果
     */
    public int insertHzCableAddress(HzCableAddress hzCableAddress);

    /**
     * 修改电报地址管理
     * 
     * @param hzCableAddress 电报地址管理
     * @return 结果
     */
    public int updateHzCableAddress(HzCableAddress hzCableAddress);

    List<HzCableAddress> selectAddressByCarrier(@Param("carriers") List<String> carriers);

    List<HzCableAddress> selectAddressByAirportCode(@Param("desPortList") List<String> desPortList);

    List<HzCableAddress> selectAddress(@Param("cableAddress") List<TypeAndAddressQuery> cableAddress,@Param("caacAddress") List<TypeAndAddressQuery> caacAddress);

    List<TypeAndAddressQuery> newSelectHzCableAddressList();
}
