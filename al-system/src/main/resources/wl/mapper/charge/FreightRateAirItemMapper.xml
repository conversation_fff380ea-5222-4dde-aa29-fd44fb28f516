<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.wl.charge.mapper.FreightRateAirItemMapper">
    <update id="removeAirItem">
        update wl_sf_air_price_item set is_del = 1 where id = #{id}
    </update>
    <select id="selectRateItemList" resultType="com.gzairports.wl.charge.domain.vo.FreightRateAirItemVO">
        select wfri.id, wafr.rate_name, bcc.code, wfri.special_code, wfri.departure_city, wfri.destination_city, wfri.transit_city,
               wfri.special_cargo_code, wfri.master_prefix, wfri.minimum_freight, wfri.clause_name,
               wfri.face_minimum_freight, wfri.storage_shipping_notes, wfri.settlement_notes, wfri.remarks
        from wl_sf_air_price_item wfri
                 left join wl_sf_air_price wafr on wafr.id = wfri.rate_id
                 left join base_cargo_code bcc on bcc.id = wfri.cargo_id
             where wfri.is_del = 0 and wfri.rate_id = #{id}
    </select>
    <select id="selectAirList" resultType="com.gzairports.wl.charge.domain.vo.FreightRateAirItemVO">
        select wfri.id, wafr.rate_name, wfri.special_code, wfri.departure_city, wfri.destination_city, wfri.transit_city,
        wfri.special_code, wfri.master_prefix, wfri.minimum_freight, wfri.clause_name, wfri.cargo_category,
        wfri.face_minimum_freight, wfri.storage_shipping_notes, wfri.settlement_notes, wfri.remarks,wfri.cargo_code,
        wafr.effective_date as startTime,wafr.expiration_date as endTime
        from wl_sf_air_price_item wfri
        left join wl_sf_air_price wafr on wafr.id = wfri.rate_id
        <where>
            wfri.is_del = 0 and wfri.rate_id = #{id}
            <if test="rateName != null  and rateName != ''"> and wafr.rate_name = #{rateName}</if>
            <if test="sourcePort != null  and sourcePort.size() > 0">
                and wfri.departure_city in
                <foreach collection="sourcePort" item="sourceCity" index="index" open="(" close=")" separator=",">
                    #{sourceCity}
                </foreach>
            </if>
<!--            <if test="desPort != null  and desPort.size() > 0">-->
<!--                and wfri.destination_city in-->
<!--                <foreach collection="desPort" item="desCity" index="index" open="(" close=")" separator=",">-->
<!--                    #{desCity}-->
<!--                </foreach>-->
<!--            </if>-->
            <if test="destinationCity != null  and destinationCity != ''"> and wfri.destination_city = #{destinationCity}</if>
            <if test="rateName != null  and rateName != ''"> and wafr.rate_name = #{rateName}</if>
        </where>
    </select>
</mapper>