package com.gzairports.common.business.departure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.departure.domain.Transfer;
import com.gzairports.common.business.departure.domain.vo.PrintTransferVo;
import com.gzairports.hz.business.departure.domain.query.HzTransferQuery;
import com.gzairports.hz.business.departure.domain.vo.HzTransferVo;
import com.gzairports.wl.departure.domain.query.TransferQuery;
import com.gzairports.wl.departure.domain.vo.TransferVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 货站出港货物交接Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Mapper
public interface TransferMapper extends BaseMapper<Transfer> {

    /**
     * 查询运单收运列表
     * @param query 查询参数
     * @return 结果
     */
    List<HzTransferVo> selectCollectWaybillList(HzTransferQuery query);

    /**
     * 查询出港货物交接单详情
     * @param id 出港货物交接单数据
     * @return 出港货物交接单详情
     */
    HzTransferVo selectByIds(Long id);

    /**
     * 根据条件查询货物交接列表
     * @param query 查询条件
     * @return 货物交接列表
     */
    List<TransferVo> selectListByQuery(TransferQuery query);

    /**
     * 查询出港货物交接单详情
     * @param id 出港货物交接单数据
     * @return 出港货物交接单详情
     */
    TransferVo selectByIds(@Param("id") Long id, @Param("deptId") Long deptId);

    PrintTransferVo selectPrintTransferData(Long id);

    List<HzTransferVo> selectAppList(HzTransferQuery query);
}
