package com.gzairports.common.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 业务号生成规则类
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
public class SerialNumberGenerator {

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMddHHmm");
    private static final AtomicInteger SEQUENCE = new AtomicInteger(0);

    public static synchronized String generateSerialNumber() {
        String datePart = DATE_FORMAT.format(new Date());
        int sequencePart = SEQUENCE.incrementAndGet();
        // 如果编号超过9999，需要处理重置或抛出异常等情况
        if (sequencePart > 9999) {
            // 这里可以重置编号或抛出异常，根据你的需求来决定
            SEQUENCE.set(0);
        }
        // 格式化四位编号，不足四位前面补0
        String formattedSequence = String.format("%04d", sequencePart);
        return datePart + formattedSequence;
    }
}
