package com.gzairports.wl.departure.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 主单异常备注表
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
@Data
@TableName("all_mawb_error_remark")
public class MawbErrorRemark {

    /** 主键id */
    private Long id;

    /** 分单id */
    private Long waybillId;

    /** 分单运单号 */
    private String waybillCode;

    /** 备注人 */
    private String userName;

    /** 备注时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date remarkTime;

    /** 备注内容 */
    private String remark;

    /** 是否删除 0 否 1 是 */
    private Integer isDel;
}
