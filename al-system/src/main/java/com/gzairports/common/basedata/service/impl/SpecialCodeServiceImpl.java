package com.gzairports.common.basedata.service.impl;


import com.gzairports.common.basedata.domain.BaseSpecialCode;
import com.gzairports.common.basedata.domain.query.SpecialCodeQuery;
import com.gzairports.common.basedata.mapper.SpecialCodeMapper;
import com.gzairports.common.basedata.service.ISpecialCodeService;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.exception.ServiceException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.bean.BeanValidators;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.Validator;
import java.util.List;

/**
 * 货物特殊处理代码Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Service
public class SpecialCodeServiceImpl implements ISpecialCodeService {

    private static final Logger log = LoggerFactory.getLogger(SpecialCodeServiceImpl.class);

    @Autowired
    private SpecialCodeMapper specialCodeMapper;

    @Autowired
    private Validator validator;

    /**
     * 查询货物特殊处理代码列表
     *
     * @param query 查询参数
     * @return 货物特殊处理代码列表
     */
    @Override
    public List<BaseSpecialCode> selectSpecialCodeList(SpecialCodeQuery query) {
        return specialCodeMapper.selectSpecialCodeList(query);
    }

    /**
     * 导入货物特殊处理代码
     *
     * @param specialCodes 货物特殊处理代码数据列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importSpecialCode(List<BaseSpecialCode> specialCodes, boolean updateSupport) {
        if (StringUtils.isNull(specialCodes) || specialCodes.size() == 0)
        {
            throw new ServiceException("导入货物特殊处理代码数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (BaseSpecialCode specialCode : specialCodes) {
            try {
                // 验证是否存在相同的货物特殊处理代码
                BaseSpecialCode special = specialCodeMapper.selectSpecialByCode(specialCode.getCode());
                if (StringUtils.isNull(special)) {
                    BeanValidators.validateWithException(validator, specialCode);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    specialCode.setCreateBy(loginUser.getUsername());
                    specialCode.setCreateTime(DateUtils.getNowDate());
                    specialCodeMapper.insertSpecialCode(specialCode);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、代码 " + specialCode.getCode() + " 导入成功");
                }
                else if (updateSupport) {
                    BeanValidators.validateWithException(validator, specialCode);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    specialCode.setId(special.getId());
                    specialCode.setUpdateBy(loginUser.getUsername());
                    specialCode.setUpdateTime(DateUtils.getNowDate());
                    specialCodeMapper.updateSpecialCode(specialCode);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、代码 " + specialCode.getCode() + " 更新成功");
                }
                else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、代码 " + specialCode.getCode() + " 已存在");
                }
            }catch (Exception e){
                failureNum++;
                String msg = "<br/>" + failureNum + "、代码 " + specialCode.getCode() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 新增货物特殊处理代码
     *
     * @param specialCode 货物特殊处理代码
     * @return 结果
     */
    @Override
    public int addSpecialCode(BaseSpecialCode specialCode) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        specialCode.setCreateBy(loginUser.getUsername());
        specialCode.setCreateTime(DateUtils.getNowDate());
        return specialCodeMapper.insertSpecialCode(specialCode);
    }

    /**
     * 修改货物特殊处理代码
     *
     * @param specialCode 货物特殊处理代码
     * @return 结果
     */
    @Override
    public int editSpecialCode(BaseSpecialCode specialCode) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        specialCode.setUpdateBy(loginUser.getUsername());
        specialCode.setUpdateTime(DateUtils.getNowDate());
        return specialCodeMapper.updateSpecialCode(specialCode);
    }

    /**
     * 删除货物特殊处理代码
     *
     * @param ids 货物特殊处理代码id集合
     * @return 结果
     */
    @Override
    public int delSpecialCode(Long[] ids) {
        return specialCodeMapper.delSpecialCode(ids);
    }

}
