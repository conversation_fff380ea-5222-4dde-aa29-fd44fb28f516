package com.gzairports.web.controller.departure;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.common.business.wrong.domain.Wrong;
import com.gzairports.wl.departure.domain.query.WrongQuery;
import com.gzairports.common.business.wrong.service.IWrongService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 不正常货邮Controller
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@RestController
@RequestMapping("/dep/wrong")
public class WrongController extends BaseController {

    @Autowired
    private IWrongService wrongService;

    /**
     * 查询不正常货邮列表
     */
//    @PreAuthorize("@ss.hasPermi('dep:wrong:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询不正常货邮列表")
    public TableDataInfo list(WrongQuery query){
        startPage();
        Long deptId = SecurityUtils.getHighParentId();
        query.setDeptId(deptId);
        List<Wrong> list = wrongService.selectList(query);
        return getDataTable(list);
    }

    /**
     * 导出不正常货邮列表
     */
    @PreAuthorize("@ss.hasPermi('dep:wrong:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出不正常货邮列表")
    public void export(HttpServletResponse response, WrongQuery query)
    {
        List<Wrong> list = wrongService.selectList(query);
        ExcelUtil<Wrong> util = new ExcelUtil<Wrong>(Wrong.class);
        util.exportExcel(response, list, "不正常货邮");
    }
    /**
     * 新增不正常货邮
     */
    @PreAuthorize("@ss.hasPermi('dep:wrong:add')")
    @PostMapping("/add")
    @ApiOperation(value = "新增不正常货邮")
    public AjaxResult add(@RequestBody Wrong wrong)
    {
        return toAjax(wrongService.add(wrong));
    }

    /**
     * 获取不正常货邮详细信息
     */
    @PreAuthorize("@ss.hasPermi('dep:wrong:getInfo')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取不正常货邮详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(wrongService.getInfo(id));
    }

    /**
     * 选择处理方式
     */
    @PreAuthorize("@ss.hasPermi('dep:wrong:method')")
    @PostMapping(value = "/method")
    @ApiOperation(value = "选择处理方式")
    public AjaxResult method(@RequestBody Wrong wrong)
    {
        return toAjax(wrongService.method(wrong));
    }

    /**
     * 修改该条信息,注意只能修改当前登录人所新建的
     */
    @PreAuthorize("@ss.hasPermi('dep:wrong:edit')")
    @PutMapping(value = "/update")
    @ApiOperation(value = "修改该条信息,注意只能修改当前登录人所新建的")
    public AjaxResult update(@RequestBody Wrong wrong)
    {
        return toAjax(wrongService.update(wrong));
    }

    /**
     * 根据运单号查询代理人等数据
     */
    @GetMapping("/selectAgent/{waybillCode}")
    @ApiOperation(value = "根据运单号查询数据")
    public AjaxResult selectCargoName(@PathVariable("waybillCode") String waybillCode){
        return AjaxResult.success("操作成功",wrongService.selectAgent(waybillCode));
    }

    /**
     * 根据运单后四位查询运单号
     */
    @GetMapping("/getWaybillCodeByFour")
    @ApiOperation(value = "根据运单后四位查询运单号")
    public AjaxResult getWaybillCodeByFour(@RequestParam("waybillCodeLastFour") String waybillCode){
        return AjaxResult.success(wrongService.getWaybillCodeByFour(waybillCode, SecurityUtils.getHighParentId()));
    }

    /**
     * 查询是否可以退货 运单拉下
     */
    @GetMapping("/isExitPull/{waybillCode}")
    @ApiOperation(value = "查询是否可以退货")
    public AjaxResult isExitPull(@PathVariable("waybillCode") String waybillCode){
        return toAjax(wrongService.isExitPull(waybillCode));
    }

    /**
     * 查询是否可以退货 航班结算
     */
    @GetMapping("/isExitSettle/{waybillCode}")
    @ApiOperation(value = "查询是否可以退货")
    public AjaxResult isExitSettle(@PathVariable("waybillCode") String waybillCode){
        return toAjax(wrongService.isExitSettle(waybillCode));
    }


    /**
     * 处理
     */
    @PreAuthorize("@ss.hasPermi('abnormal:wrong:handle')")
    @PostMapping(value = "/handle")
    @ApiOperation(value = "处理")
    public AjaxResult handle(@RequestBody Wrong wrong) {
        return toAjax(wrongService.handle(wrong));
    }

}
