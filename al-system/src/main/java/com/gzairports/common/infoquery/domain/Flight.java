package com.gzairports.common.infoquery.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * Created by david on 2024/5/8
 * <AUTHOR>
 */
@Data
@TableName("all_flight_info")
public class Flight {

    /** 主键id */
    @TableId("flight_id")
    private Long id;

    /** 日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @TableField("exec_date")
    private Date flightDate;

    /** 航司 */
    @TableField("air_ways")
    private String air;

    /** 航班号 */
    @TableField("flight_no")
    private String flightNo;

    /** 机型 */
    @TableField("craft_type")
    private String craftType;

    /** 机号 */
    @TableField("craft_no")
    private String craftNo;

    /** 航班状态 */
    @TableField("providing_state")
    private String status;

    /** 始发站 */
    @TableField("start_station")
    private String sourcePort;

    /** 始发站中文 */
    @TableField("start_station_cn")
    private String sourcePortChinese;

    /** 目的站 */
    @TableField("terminal_station")
    private String desPort;

    /** 目的站中文    */
    @TableField("terminal_station_cn")
    private String desPortChinese;

    /** 计飞 */
    @TableField("start_scheme_takeoff_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planTakeoffTime;

    /** 预飞 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("start_alterate_takeoff_time")
    private Date expectTakeoffTime;

    /** 实飞 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("start_real_takeoff_time")
    private Date realityTakeoffTime;

    /** 计降 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("terminal_scheme_land_in_time")
    private Date planLandingTime;

    /** 预降 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("terminal_reall_and_in_time")
    private Date expectLandingTime;

    /** 实降 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("terminal_alteratel_and_in_time")
    private Date realityLandingTime;

    /** 进出港状态 */
    @TableField("is_offin")
    private String isOffin;

    /** 短航线 */
    @TableField("air_line_short")
    private String airLineShort;

    /** 长航线 */
    @TableField("air_line_full")
    private String airLineFull;
}
