package com.gzairports.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * bigDecimal类型四舍五入方法
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
public class BigDecimalRoundUtils {

    public static BigDecimal bigDecimalRound(Integer roundRule, BigDecimal defaultCostRate){
        switch (roundRule){
            case 0:
                // 实际
                return defaultCostRate;
            case 1:
                // 四舍五入至个位
                return defaultCostRate.setScale(0, RoundingMode.HALF_UP);
            case 2:
                // 四舍五入至0.1
                return defaultCostRate.setScale(1, RoundingMode.HALF_UP);
            case 3:
                // 四舍五入至0.01
                return defaultCostRate.setScale(2, RoundingMode.HALF_UP);
            case 4:
                // 四舍五入至0.001
                return defaultCostRate.setScale(3, RoundingMode.HALF_UP);
            case 5:
                // 直接进位到0.1
                return defaultCostRate.setScale(1, RoundingMode.CEILING);
            case 6:
                // 直接舍位到0.1
                return defaultCostRate.setScale(1, RoundingMode.FLOOR);
            case 7:
                // 直接进位到个位
                return defaultCostRate.setScale(0,RoundingMode.CEILING);
            case 8:
                // 直接舍位到个位
                return defaultCostRate.setScale(0,RoundingMode.FLOOR);
            case 9:
                // 直接进位到0.5
                BigDecimal integerPart = defaultCostRate.setScale(0, RoundingMode.FLOOR);
                BigDecimal fractionalPart = defaultCostRate.subtract(integerPart);
                if (fractionalPart.compareTo(new BigDecimal("0.5")) < 0) {
                    return integerPart.add(new BigDecimal("0.5"));
                } else {
                    return defaultCostRate.setScale(0,RoundingMode.HALF_UP);
                }
            case 10:
                // 直接舍位到0.5
                BigDecimal one = defaultCostRate.setScale(0, RoundingMode.FLOOR);
                BigDecimal two = defaultCostRate.subtract(one);
                if (two.compareTo(new BigDecimal("0.5")) < 0) {
                    return one;
                } else {
                    return one.add(new BigDecimal("0.5"));
                }
            default:
                return defaultCostRate;
        }
    }
}
