package com.gzairports.common.basedata.domain;

import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.util.Date;


/**
 * 货物不兼容性
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Data
public class BaseCargoIncompatible {

    /** 主键id */
    private Long id;

    /** 航空公司 */
    @Excel(name = "航空公司")
    private String airCompany;

    /** 特货代码1 */
    @Excel(name = "特货代码1")
    private String specialCargoOne;

    /** 特货代码2 */
    @Excel(name = "特货代码2")
    private String specialCargoTwo;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;
}
