package com.gzairports.wl.charge.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.wl.charge.domain.FreightRateAir;
import com.gzairports.wl.charge.domain.FreightRateAirItem;
import com.gzairports.wl.charge.domain.query.FreightRateAirQuery;
import com.gzairports.wl.charge.domain.vo.FreightRateAirItemVO;
import com.gzairports.wl.charge.domain.vo.FreightRateAirVO;
import com.gzairports.wl.charge.domain.vo.RateItemImportVo;

import java.util.List;

/**
 * 航司运价Service接口
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
public interface FreightRateAirService extends IService<FreightRateAir> {

    /**
     * 查询航司运价列表
     *
     * @param query 查询参数
     * @return 航司运价列表
     */
    List<FreightRateAirVO> selectList(FreightRateAirQuery query);

    /**
     * 查询航司运价
     *
     * @param id 航司运价主键
     * @return 航司运价
     */
    FreightRateAirVO selectRateAirById(Long id);

    /**
     * 新增航司运价
     *
     * @param air 航司运价
     * @return 结果
     */
    Long insertFreightRateAir(FreightRateAir air);

    /**
     * 修改航司运价
     *
     * @param air 航司运价
     * @return 结果
     */
    int updateFreightRateAir(FreightRateAir air);

    /**
     * 查看运价条目
     *
     * @param id 航司运价id
     * @return 航司运价条目集合
     */
    List<FreightRateAirItemVO> selectRateItemList(Long id);
    
    /**
     * 删除航司运价
     *
     * @param id 航司运价主键
     * @return 结果
     */
    int deleteFreightRateAir(Long id);

    String importAirlines(List<FreightRateAirVO> freightRateAirs, boolean updateSupport);

    /**
     * 导入航司运价条目
     * @param list 文件数据
     * @param rateId 导入所需数据
     * @param updateSupport 导入所需数据
     * @return 结果
     */
    Long importItem(List<FreightRateAirItem> list, Long rateId, boolean updateSupport);

    int deleteAll(Long[] ids);
}
