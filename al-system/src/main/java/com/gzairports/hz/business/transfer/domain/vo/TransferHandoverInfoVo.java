package com.gzairports.hz.business.transfer.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 中转交接查看详情数据
 *
 * <AUTHOR>
 * @date 2024-07-29
 */
@Data
public class TransferHandoverInfoVo {

    /** 主键id */
    private Long id;

    /** 交接单号 */
    private String handoverNo;

    /** 总票数 */
    private Integer votes;

    /** 总件数 */
    private Integer totalPrices;

    /** 总重量 */
    private BigDecimal totalWeight;

    /** 文件交接状态 */
    private Integer fileStatus;

    /** 文件交接人 */
    private String fileBy;

    /** 文件转出时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date fileOutTime;

    /** 文件接收时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date fileInTime;

    /** 货物交接状态 */
    private Integer cargoStatus;

    /** 货物交接人 */
    private String cargoBy;

    /** 货物转出时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cargoOutTime;

    /** 货物接收时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cargoInTime;

    /** 转出仓库 */
    private String outStore;

    /** 转出库位 */
    private String outLocator;

    /** 转入仓库 */
    private String inStore;

    /** 转入库位 */
    private String inLocator;

    /** 转出人 */
    private String outName;

    /** 备注 */
    private String remark;

    /** 中转的运单数据 */
    private List<TransferPickVo> waybills;
}
