package com.gzairports.hz.business.cable.domain.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-04-07
 */
@Data
public class FWBJsonVO {

    /** 代理人CASS编号 */
    private String agentCassNum;

    /** 代理人所在城市 */
    private String agentCity;

    /** 代理人国际航协代码 */
    private String agentIataCode;

    /** 代理人名词 */
    private String agentName;

    /** 运单类型 */
    private String awbType;

    /** 承运人 */
    private String carrier;

    /** 费率 */
    private String chargeOrRate;

    /** 计费件数 */
    private String chargePieces;

    /** 计费重量 */
    private String chargeWeight;

    /** 计费毛重 */
    private String chargegrossWeight;

    /** 计费毛重单位代码 */
    private String chargegrossWeightcode;

    /** 支付代码 */
    private String chgsCode;

    /** 商品项目编号 */
    private String commodityItemNo;

    /** 收货人地址 */
    private String consigneeAddr;

    /** 收货人城市 */
    private String consigneeCity;

    /** 收货人国家 */
    private String consigneeCountry;

    /** 收货人名称 */
    private String consigneeName;

    /** 收货人编号 */
    private String consigneeNum;

    /** 收货人州/省 */
    private String consigneeStateProvince;

    /** 收货人电话 */
    private String consigneeTel;

    /** 收货人邮政编码 */
    private String consigneeZipCode;

    /** 货币类型 */
    private String currency;

    /** 海关货物原产国代码 */
    private String customsOriginCode;

    /** 运输声明价值 */
    private String declaredValueCarriage;

    /** 海关申报价值 */
    private String declaredValueCustoms;

    /** 启运港 */
    private String depAirport;

    /** 目的港 */
    private String desAirport;

    /** 目的地 */
    private String destination;

    /** 尺寸 */
    private String dimensions;

    /** 执行日期 */
    private String executeDate;

    /** 执行地点 */
    private String executePlace;

    /** 航班日期 */
    private String flightDate;

    /** 航班号 */
    private String flightNo;

    /** 第四承运人 */
    private String fourCarrier;

    /** 第四目的地 */
    private String fourDestination;

    /** 货物品名描述 */
    private String goodsDesc;

    /** 货物品名描述 */
    private String goodsDescnc;

    /** 协调商品编码 */
    private String harmonisedCommodityCode;

    /** 保险金额 */
    private String insuranceAmount;

    private String mawbId;

    /** 主运单号 */
    private String mawbNo;

    /** 其他服务信息 */
    private String osi;

    /** 其他费用 */
    private String other;

    /** 其他海关、安全与监管控制信息 */
    private OtherCustoms otherCustoms;

    /** 其他费用 */
    private String[] otherFee;

    /** 发货方参与方类型标识符 */
    private String participantIdentifier;

    /** 装载件数 */
    private String pieces;

    /** 预支付 */
    private String prepaid;

    private String prepaidTax;

    private String prepaidValuationCharge;

    private String rateClass;

    /** 费率描述体积代码 */
    private String ratedescVolume;

    /** 费率描述体积代码 */
    private String ratedescVolumecode;

    /** 第二承运人 */
    private String secondCarrier;

    /** 第二目的地 */
    private String secondDestination;

    /** 发货方办公地点机场代码 */
    private String senderOfficeAirport;

    /** 发货方办公公司标识符 */
    private String senderOfficeCompanyDesignator;

    /** 发货方办公职能标识符 */
    private String senderOfficeFunctionDesignator;

    /** 特殊处理详情 */
    private String[] shc;

    /** 托运人地址 */
    private String shipperAddr;

    /** 托运人所在城市 */
    private String shipperCity;

    /** 托运人所在国家代码 */
    private String shipperCountry;

    /** 托运人名称 */
    private String shipperName;

    /** 托运人所在州 / 省 */
    private String shipperStateProvince;

    /** 托运人电话号码 */
    private String shipperTel;

    /** 托运人邮政编码 */
    private String shipperZipCode;

    /** 货物件数 */
    private String slac;

    /** 特殊服务请求 */
    private String ssr;

    /** 第三承运人 */
    private String thirdCarrier;

    /** 第三目的地 */
    private String thirdDestination;

    private String total;

    /** 总费用 */
    private String totalChargeamount;

    /** 代理总费用 */
    private String totalChargesAgent;

    /** 承运人总费用 */
    private String totalChargesCarrier;

    /** 总预付金额 */
    private String totalPrepaid;

    /** 体积 */
    private String volume;

    /** 体积单位 */
    private String volumeUnit;

    /** 重量 */
    private String weight;

    /** 重量单位 */
    private String weightUnit;

    /** 航空运费 */
    private String wtVal;
}
