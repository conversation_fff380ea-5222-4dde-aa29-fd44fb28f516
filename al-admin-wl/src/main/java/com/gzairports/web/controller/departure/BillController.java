package com.gzairports.web.controller.departure;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.wl.departure.domain.Bill;
import com.gzairports.wl.departure.domain.query.BillQuery;
import com.gzairports.wl.departure.domain.vo.BillVo;
import com.gzairports.wl.departure.service.IBillService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * 国内分单账单管理Controller
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@RestController
@RequestMapping("/dep/bill")
public class Bill<PERSON>ontroller extends BaseController {

    @Autowired
    private IBillService billService;

    /**
     * 查询国内分单账单
     */
//    @PreAuthorize("@ss.hasPermi('dep:bill:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询国内分单账单")
    public AjaxResult list(BillQuery query){
        return AjaxResult.success(billService.selectListByQuery(query));
    }

    /**
     * 导出国内分单账单
     */
    @PreAuthorize("@ss.hasPermi('dep:bill:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出国内分单账单")
    public void export(HttpServletResponse response, BillQuery query)
    {
        List<BillVo> list = billService.selectList(query);
        ExcelUtil<BillVo> util = new ExcelUtil<BillVo>(BillVo.class);
        util.exportExcel(response, list, "国内分单账单");
    }

    /**
     * 查看详情
     */
    @PreAuthorize("@ss.hasPermi('dep:bill:getInfo')")
    @GetMapping("/getInfo/{id}")
    @ApiOperation(value = "查看详情")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(billService.getInfo(id));
    }

    /**
     * 支付审核
     */
    @PreAuthorize("@ss.hasPermi('dep:bill:edit')")
    @PostMapping("/edit")
    @ApiOperation(value = "支付审核")
    public AjaxResult edit(@RequestBody Bill bill)
    {
        return toAjax(billService.edit(bill));
    }

    /**
     * 删除
     */
    @PreAuthorize("@ss.hasPermi('dep:bill:del')")
    @GetMapping("/del/{id}")
    @ApiOperation(value = "删除")
    public AjaxResult del(@PathVariable Long id)
    {
        return toAjax(billService.del(id));
    }
}
