package com.gzairports.wl.departure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.wl.departure.domain.Bill;
import com.gzairports.wl.departure.domain.query.BillQuery;
import com.gzairports.wl.departure.domain.vo.BillVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 国内分单账单管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@Mapper
public interface BillMapper extends BaseMapper<Bill> {

    /**
     * 查询国内分单账单
     * @param query 查询参数
     * @return 国内分单账单数据
     */
    List<BillVo> selectListByQuery(BillQuery query);

    /**
     * 国内分单账单详情
     * @param id 国内分单账单id
     * @return 详情
     */
    BillVo getInfo(Long id);
}
