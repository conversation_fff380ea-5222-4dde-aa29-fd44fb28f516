package com.gzairports.web.controller.charge;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.wl.charge.domain.ChargeItem;
import com.gzairports.wl.charge.domain.query.ChargeItemQuery;
import com.gzairports.wl.charge.domain.vo.ChargeItemVO;
import com.gzairports.wl.charge.service.ChargeItemService;
import com.gzairports.common.annotation.OperLog;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.wl.log.service.WlOperLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 收费项目Controller
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@RestController
@RequestMapping("/item")
@Api(tags = "收费项目接口")
public class ChargeItemController extends BaseController {

    @Autowired
    private ChargeItemService itemService;

    @Autowired
    private WlOperLogService logService;

    /**
     * 查询收费项目列表
     */
//    @PreAuthorize("@ss.hasPermi('charge:item:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询收费项目列表")
    public TableDataInfo list(ChargeItemQuery query){
        startPage();
        List<ChargeItemVO> list = itemService.selectList(query);
        return getDataTable(list);
    }

    /**
     * 导出收费项目列表
     */
    @PreAuthorize("@ss.hasPermi('charge:item:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出收费项目")
    public void export(HttpServletResponse response, ChargeItemQuery query){
        List<ChargeItemVO> list = itemService.selectList(query);
        ExcelUtil<ChargeItemVO> util = new ExcelUtil<ChargeItemVO>(ChargeItemVO.class);
        util.exportExcel(response, list, "收费项目");
    }

    /**
     * 获取收费项目详细信息
     */
    @PreAuthorize("@ss.hasPermi('charge:item:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取收费项目详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(itemService.selectChargeById(id));
    }

    /**
     * 新增收费项目
     */
    @PreAuthorize("@ss.hasPermi('charge:item:add')")
    @OperLog(title = "收费项目", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ApiOperation(value = "新增收费项目")
    public AjaxResult add(@RequestBody ChargeItem item)
    {
        String name = item.getName();
        LambdaQueryWrapper<ChargeItem> wrapper = new LambdaQueryWrapper<>();
//        wrapper.eq(ChargeItem::getName, name);
        wrapper.eq(ChargeItem::getIsDel,0);
        wrapper.eq(ChargeItem::getCode,0);
        wrapper.eq(ChargeItem::getOperationType,0);
        wrapper.eq(ChargeItem::getApplicableDocuments,0);
        wrapper.eq(ChargeItem::getDeptId, SecurityUtils.getHighParentId());
        ChargeItem duplicatedNameItem = itemService.getOne(wrapper);
        if (duplicatedNameItem != null) {
            throw new CustomException("收费项目重复");
        }
        return success(itemService.insertChargeItem(item));
    }

    /**
     * 修改收费项目
     */
    @PreAuthorize("@ss.hasPermi('charge:item:edit')")
    @OperLog(title = "收费项目", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ApiOperation(value = "修改收费项目")
    public AjaxResult edit(@RequestBody ChargeItem item)
    {
        ChargeItem byId = itemService.getById(item.getId());
        if (!byId.getName().equals(item.getName())){
            LambdaQueryWrapper<ChargeItem> wrapper = new LambdaQueryWrapper<>();
//            wrapper.eq(ChargeItem::getName, item.getName());
            wrapper.eq(ChargeItem::getIsDel,0);
            wrapper.eq(ChargeItem::getDeptId,SecurityUtils.getHighParentId());
            wrapper.eq(ChargeItem::getApplicableDocuments,item.getApplicableDocuments());
            wrapper.eq(ChargeItem::getCode,item.getCode());
            wrapper.eq(ChargeItem::getOperationType,item.getOperationType());
            ChargeItem duplicatedNameItem = itemService.getOne(wrapper);
            if (duplicatedNameItem != null) {
                throw new CustomException("收费项目名称重复");
            }
        }
        return toAjax(itemService.updateChargeItem(item));
    }

    /**
     * 删除收费项目
     */
    @PreAuthorize("@ss.hasPermi('charge:item:del')")
    @OperLog(title = "收费项目", businessType = BusinessType.DELETE)
    @GetMapping("/del/{id}")
    @ApiOperation(value = "删除收费项目")
    public AjaxResult del(@PathVariable Long id)
    {
        return toAjax(itemService.deleteChargeItem(id));
    }

    /**
     * 收费项目操作日志查询
     */
    @PreAuthorize("@ss.hasPermi('charge:item:logList')")
    @GetMapping("/logList/{id}")
    @ApiOperation(value = "收费项目操作日志查询")
    public AjaxResult logList(@PathVariable Long id)
    {
        return success(logService.log("收费项目",id));
    }

    /**
     * 收费项目操作日志详情
     */
    @PreAuthorize("@ss.hasPermi('charge:item:logInfo')")
    @GetMapping("/logInfo/{operId}")
    @ApiOperation(value = "收费项目操作日志详情")
    public AjaxResult logInfo(@PathVariable Long operId)
    {
        return success(logService.logInfo(operId));
    }
}
