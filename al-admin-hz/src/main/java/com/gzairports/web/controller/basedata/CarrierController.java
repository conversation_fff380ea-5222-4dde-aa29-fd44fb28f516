package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BaseCarrier;
import com.gzairports.common.basedata.domain.query.CarrierQuery;
import com.gzairports.common.basedata.service.ICarrierService;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 承运人管理Controller
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@RestController
@RequestMapping("/base/carrier")
@Api(tags = "承运人管理数据接口")
public class CarrierController extends BaseController {

    @Autowired
    private ICarrierService carrierService;
    /**
     * 查询承运人管理列表
     */
//    @PreAuthorize("@ss.hasPermi('base:carrier:carrierList')")
    @GetMapping("/carrierList")
    @ApiOperation(value = "查询承运人管理列表")
    public TableDataInfo carrierList(CarrierQuery query)
    {
        startPage();
        List<BaseCarrier> list = carrierService.selectCarrierList(query);
        return getDataTable(list);
    }

    /**
     * 导出承运人
     */
    @PreAuthorize("@ss.hasPermi('base:carrier:exportCarrier')")
    @Log(title = "导出承运人", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCarrier")
    @ApiOperation(value = "导出承运人")
    public void exportCarrier(HttpServletResponse response, CarrierQuery query)
    {
        List<BaseCarrier> list = carrierService.selectCarrierList(query);
        ExcelUtil<BaseCarrier> util = new ExcelUtil<BaseCarrier>(BaseCarrier.class);
        util.exportExcel(response, list, "承运人管理");
    }

    /**
     * 导出承运人模板
     */
    @PreAuthorize("@ss.hasPermi('base:carrier:exportCarrier')")
    @Log(title = "导出承运人模板", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCarrierNull")
    @ApiOperation(value = "导出承运人模板")
    public void exportCarrierNull(HttpServletResponse response)
    {
        List<BaseCarrier> list = new ArrayList<>();
        ExcelUtil<BaseCarrier> util = new ExcelUtil<BaseCarrier>(BaseCarrier.class);
        util.exportExcel(response, list, "承运人管理模板");
    }

    /**
     * 导入承运人
     */
    @PreAuthorize("@ss.hasPermi('base:carrier:importCarrier')")
    @Log(title = "导入承运人", businessType = BusinessType.IMPORT)
    @PostMapping("/importCarrier")
    @ApiOperation(value = "导入承运人")
    public AjaxResult importCarrier(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<BaseCarrier> util = new ExcelUtil<BaseCarrier>(BaseCarrier.class);
        List<BaseCarrier> carriers = util.importExcel(file.getInputStream());
        String message = carrierService.importCarrier(carriers, updateSupport);
        return success(message);
    }

    /**
     * 新增承运人
     */
    @PreAuthorize("@ss.hasPermi('base:carrier:addCarrier')")
    @Log(title = "新增承运人", businessType = BusinessType.INSERT)
    @PostMapping("/addCarrier")
    @ApiOperation(value = "新增承运人")
    public AjaxResult addCarrier(@RequestBody BaseCarrier carrier)
    {
        return toAjax(carrierService.addCarrier(carrier));
    }

    /**
     * 修改承运人
     */
    @PreAuthorize("@ss.hasPermi('base:carrier:editCarrier')")
    @Log(title = "修改承运人", businessType = BusinessType.UPDATE)
    @PostMapping("/editCarrier")
    @ApiOperation(value = "修改承运人")
    public AjaxResult editCarrier(@RequestBody BaseCarrier carrier)
    {
        return toAjax(carrierService.editCarrier(carrier));
    }

    /**
     * 删除承运人
     */
    @PreAuthorize("@ss.hasPermi('base:carrier:delCarrier')")
    @Log(title = "删除承运人", businessType = BusinessType.UPDATE)
    @GetMapping("/delCarrier/{ids}")
    @ApiOperation(value = "删除承运人")
    public AjaxResult delCarrier(@PathVariable("ids") Long[] ids)
    {
        return toAjax(carrierService.delCarrier(ids));
    }
}
