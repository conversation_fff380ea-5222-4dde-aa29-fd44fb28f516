package com.gzairports.common.basedata.service;

import com.gzairports.common.basedata.domain.BaseCraftNo;
import com.gzairports.common.basedata.domain.BaseCraftNoMail;
import com.gzairports.common.basedata.domain.query.BaseCraftNoQuery;

import java.util.List;

/**
 * 基础数据机号管理Service接口
 *
 * <AUTHOR>
 * @date 2024-12-09
 */
public interface IBaseCraftNoService {

    /**
     * 查询机号管理列表
     */
    List<BaseCraftNo> selectCraftNoList(BaseCraftNo baseCraftNo);

    /**
     * 查询副邮箱机号管理列表
     */
    List<BaseCraftNoMail> selectCraftNoMailList(BaseCraftNo baseCraftNo);

    /**
     * 新增机号
     * */
    int insertCraft(BaseCraftNo baseCraftNo);

    /**
     * 查询详情
     * */
    BaseCraftNo getInfoCraftNo(Long id);

    /**
     * 删除机号
     * */
    int delete(Long id);

    /**
     * 修改机号详情
     * */
    int update(BaseCraftNo baseCraftNo);

    /**
     * 根据航班号和航班时间查询相应机号配置
     * */
    void selectCraftNoConfig(BaseCraftNoQuery quary);
}
