package com.gzairports.common.business.arrival.domain.vo;

import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 已提货办单运单数据
 * @date 2025-06-24
 **/
@Data
public class PickedUpWaybillVo {
    /** 主键id */
    private Long id;

    /** 理货id */
    private Long tallyId;

    /** 流水号 */
    @Excel(name = "流水号")
    private String serialNo;

    /** 运单号 */
    @Excel(name = "运单号")
    private String waybillCode;

    /** 提货件数 */
    @Excel(name = "提货件数")
    private Integer waybillQuantity;

    /** 提货重量 */
    @Excel(name = "提货重量")
    private BigDecimal waybillWeight;

    /** 进处费 */
    @Excel(name = "进处费")
    private BigDecimal processingFee = new BigDecimal(0);

    /** 保管费 */
    @Excel(name = "保管费")
    private BigDecimal storageFee = new BigDecimal(0);

    /** 冷藏费 */
    @Excel(name = "冷藏费")
    private BigDecimal refrigerationFee = new BigDecimal(0);

    /** 搬运费 */
    @Excel(name = "搬运费")
    private BigDecimal handlingFee = new BigDecimal(0);

    /** 电报费 */
    @Excel(name = "电报费")
    private BigDecimal cableCharge = new BigDecimal(0);
}
