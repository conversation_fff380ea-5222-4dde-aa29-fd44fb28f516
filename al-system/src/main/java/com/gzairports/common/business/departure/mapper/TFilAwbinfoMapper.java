package com.gzairports.common.business.departure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.departure.domain.vo.PageVO;
import com.gzairports.common.business.departure.domain.vo.TFilAwbinfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 航空运单Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-24
 */
@Mapper
public interface TFilAwbinfoMapper extends BaseMapper<TFilAwbinfo>
{

    public List<TFilAwbinfo> selectTFilAwbinfoStockno(String stockno);

    /**
     * 分页查询运单数据
     * @param pageVO 分页信息
     * @return 结果
     */
    List<TFilAwbinfo> selectTFilAwbinfoPageList(PageVO pageVO);
}
