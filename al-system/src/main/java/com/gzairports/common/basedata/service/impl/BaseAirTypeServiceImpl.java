package com.gzairports.common.basedata.service.impl;

import com.gzairports.common.basedata.domain.BaseAirType;
import com.gzairports.common.basedata.mapper.BaseAirTypeMapper;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.exception.ServiceException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.bean.BeanValidators;
import com.gzairports.common.basedata.service.IBaseAirTypeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.Validator;
import java.util.List;

/**
 * 机型管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-14
 */
@Service
public class BaseAirTypeServiceImpl implements IBaseAirTypeService
{
    @Autowired
    private BaseAirTypeMapper baseAirTypeMapper;

    private static final Logger log = LoggerFactory.getLogger(WeightServiceImpl.class);

    @Autowired
    private Validator validator;

    /**
     * 查询机型管理
     * 
     * @param id 机型管理主键
     * @return 机型管理
     */
    @Override
    public BaseAirType selectBaseAirTypeById(Long id)
    {
        return baseAirTypeMapper.selectBaseAirTypeById(id);
    }

    /**
     * 查询机型管理列表
     * 
     * @param baseAirType 机型管理
     * @return 机型管理
     */
    @Override
    public List<BaseAirType> selectBaseAirTypeList(BaseAirType baseAirType)
    {
        return baseAirTypeMapper.selectBaseAirTypeList(baseAirType);
    }

    /**
     * 新增机型管理
     * 
     * @param baseAirType 机型管理
     * @return 结果
     */
    @Override
    public int insertBaseAirType(BaseAirType baseAirType)
    {
        BaseAirType type = baseAirTypeMapper.selectByType(baseAirType.getAirType());
        if (type != null){
            throw new CustomException("已存在相同数据");
        }
        baseAirType.setCreateTime(DateUtils.getNowDate());
        return baseAirTypeMapper.insertBaseAirType(baseAirType);
    }

    /**
     * 修改机型管理
     * 
     * @param baseAirType 机型管理
     * @return 结果
     */
    @Override
    public int updateBaseAirType(BaseAirType baseAirType)
    {
        baseAirType.setUpdateTime(DateUtils.getNowDate());
        return baseAirTypeMapper.updateBaseAirType(baseAirType);
    }

    /**
     * 批量删除机型管理
     * 
     * @param ids 需要删除的机型管理主键
     * @return 结果
     */
    @Override
    public int deleteBaseAirTypeByIds(Long[] ids)
    {
        return baseAirTypeMapper.deleteBaseAirTypeByIds(ids);
    }

    /**
     * 删除机型管理信息
     * 
     * @param id 机型管理主键
     * @return 结果
     */
    @Override
    public int deleteBaseAirTypeById(Long id)
    {
        return baseAirTypeMapper.deleteBaseAirTypeById(id);
    }

    /**
     * 导入机型管理
     *
     * @param types 机型管理列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importAirType(List<BaseAirType> types, boolean updateSupport) {
        if (StringUtils.isNull(types) || types.size() == 0)
        {
            throw new ServiceException("导入机型管理数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (BaseAirType type : types) {
            try {
                // 验证是否存在相同的货站货品代码
                BaseAirType code = baseAirTypeMapper.selectByType(type.getType());
                if (StringUtils.isNull(code)) {
                    BeanValidators.validateWithException(validator, type);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    type.setCreateBy(loginUser.getUsername());
                    type.setCreateTime(DateUtils.getNowDate());
                    baseAirTypeMapper.insertBaseAirType(type);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、货站货品代码 " + type.getType() + " 导入成功");
                }
                else if (updateSupport) {
                    BeanValidators.validateWithException(validator, type);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    type.setId(code.getId());
                    type.setUpdateBy(loginUser.getUsername());
                    type.setUpdateTime(DateUtils.getNowDate());
                    baseAirTypeMapper.updateBaseAirType(type);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、货站货品代码 " + type.getType() + " 更新成功");
                }
                else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、货站货品代码 " + type.getType() + " 已存在");
                }
            }catch (Exception e){
                failureNum++;
                String msg = "<br/>" + failureNum + "、货站货品代码 " + type.getType() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
