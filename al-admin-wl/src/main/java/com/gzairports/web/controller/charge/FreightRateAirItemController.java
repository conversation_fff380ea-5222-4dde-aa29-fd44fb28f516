package com.gzairports.web.controller.charge;

import com.gzairports.wl.charge.domain.FreightRateAirItem;
import com.gzairports.wl.charge.domain.query.ChargeItemQuery;
import com.gzairports.wl.charge.domain.query.FareAirItemQuery;
import com.gzairports.wl.charge.domain.query.FreightRateItemQuery;
import com.gzairports.wl.charge.domain.vo.ChargeItemVO;
import com.gzairports.wl.charge.domain.vo.FareAirItemVo;
import com.gzairports.wl.charge.domain.vo.FreightRateAirItemVO;
import com.gzairports.wl.charge.service.FreightRateAirItemService;
import com.gzairports.common.annotation.OperLog;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 航司运价条目Controller
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@RestController
@RequestMapping("/freightAirItem")
@Api(tags = "航司运价条目接口")
public class FreightRateAirItemController extends BaseController {

    @Autowired
    private FreightRateAirItemService airItemService;

    /**
     * 查询航司运价条目列表
     */
//    @PreAuthorize("@ss.hasPermi('charge:freightAirItem:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询航司运价条目列表")
    public TableDataInfo list(FreightRateItemQuery query){
        startPage();
        List<FreightRateAirItemVO> list = airItemService.selectList(query);
        return getDataTable(list);
    }

    /**
     * 导出航司运价条目
     */
    @PreAuthorize("@ss.hasPermi('charge:freightAirItem:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出航司运价条目")
    public void export(HttpServletResponse response, FreightRateItemQuery query){
        List<FreightRateAirItemVO> list = airItemService.selectList(query);
        ExcelUtil<FreightRateAirItemVO> util = new ExcelUtil<FreightRateAirItemVO>(FreightRateAirItemVO.class);
        util.exportExcel(response, list, "航司运价条目");
    }

    /**
     * 获取运价条目详细信息
     */
    @PreAuthorize("@ss.hasPermi('charge:freightAirItem:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取运价条目详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(airItemService.selectAirItemById(id));
    }

    /**
     * 新增运价条目
     */
    @PreAuthorize("@ss.hasPermi('charge:freightAirItem:add')")
    @OperLog(title = "运价条目", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ApiOperation(value = "新增运价条目")
    public AjaxResult add(@RequestBody FreightRateAirItem item)
    {
        return toAjax(airItemService.insertAirItem(item));
    }

    /**
     * 修改运价条目
     */
    @PreAuthorize("@ss.hasPermi('charge:freightAirItem:edit')")
    @OperLog(title = "运价条目", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ApiOperation(value = "修改运价条目")
    public AjaxResult edit(@RequestBody FreightRateAirItem item)
    {
        return toAjax(airItemService.updateAirItem(item));
    }

    /**
     * 删除运价条目
     */
    @PreAuthorize("@ss.hasPermi('charge:freightAirItem:del')")
    @OperLog(title = "运价条目", businessType = BusinessType.DELETE)
    @GetMapping("/del/{id}")
    @ApiOperation(value = "删除运价条目")
    public AjaxResult del(@PathVariable Long id)
    {
        return toAjax(airItemService.removeAirItem(id));
    }

    /**
     * 查询航司运价条目列表
     */
//    @PreAuthorize("@ss.hasPermi('charge:freightAirItem:itemList')")
    @GetMapping("/itemList")
    @ApiOperation(value = "查询收费项目列表")
    public AjaxResult itemList(FareAirItemQuery query){
        List<FareAirItemVo> list = airItemService.selectItemList(query);
        return AjaxResult.success(list);
    }

    /**
     * 批量删除航司运价条目
     */
    @PreAuthorize("@ss.hasPermi('charge:freightCustom:del')")
    @OperLog(title = "运价条目", businessType = BusinessType.DELETE)
    @GetMapping("/delBatch/{ids}")
    @ApiOperation(value = "批量删除航司运价")
    public AjaxResult delAll(@PathVariable("ids") Long[] ids)
    {
        airItemService.delAll(ids);
        return AjaxResult.success("操作成功");
    }
}
