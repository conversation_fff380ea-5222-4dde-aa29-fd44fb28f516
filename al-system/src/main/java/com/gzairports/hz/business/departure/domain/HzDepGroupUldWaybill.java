package com.gzairports.hz.business.departure.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 组货调度板箱表
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
@Data
@TableName("hz_dep_group_uld_waybill")
public class HzDepGroupUldWaybill {

    /** 主键id */
    private Long id;

    /** 组货散舱运单id */
    @TableField(exist = false)
    private Long groupWaybillId;

    /** 装载板箱id */
    private Long groupUldId;

    /** 板车号 */
    @TableField(exist = false)
    private Long newGroupUldId;

    /** 运单号 */
    private String waybillCode;

    /** 目的站 */
    private String desPort;

    /** 类型 */
    private String type;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;
}
