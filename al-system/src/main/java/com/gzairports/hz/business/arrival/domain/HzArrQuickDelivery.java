package com.gzairports.hz.business.arrival.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 快速交付表
 * <AUTHOR>
 * @date 2024-07-24
 */
@Data
@TableName("hz_arr_quick_delivery")
public class HzArrQuickDelivery {

    /** 主键id */
    private Long id;

    /** 代理人 */
    @Excel(name = "代理人")
    private String agent;

    /** 代理人简称 */
    @Excel(name = "代理人简称")
    private String agentAbb;

    /** 状态 */
    @Excel(name = "状态")
    private Integer status;

    /** 开始时间 */
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 结算时间 */
    @Excel(name = "结算时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 是否删除 0 否 1 是 */
    private Integer isDel;

    /** 部门id */
    private Long deptId;
}
