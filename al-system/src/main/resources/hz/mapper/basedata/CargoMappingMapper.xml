<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.CargoMappingMapper">
    <insert id="insertCargoMapping" parameterType="baseCargoMapping">
        insert into base_cargo_mapping
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="cargoCode != null and cargoCode != ''">cargo_code,</if>
            <if test="cargoName != null and cargoName != ''">cargo_name,</if>
            <if test="airCargoCode != null and airCargoCode != ''">air_cargo_code,</if>
            <if test="airCargoName != null and airCargoName != ''">air_cargo_name,</if>
            <if test="airCode != null and airCode != ''">air_code,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="deptId != null">dept_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="cargoCode != null and cargoCode != ''">#{cargoCode},</if>
            <if test="cargoName != null and cargoName != ''">#{cargoName},</if>
            <if test="airCargoCode != null and airCargoCode != ''">#{airCargoCode},</if>
            <if test="airCargoName != null and airCargoName != ''">#{airCargoName},</if>
            <if test="airCode != null and airCode != ''">#{airCode},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="deptId != null">#{deptId},</if>
        </trim>
    </insert>
    <update id="updateCargoMapping" parameterType="baseCargoMapping">
        update base_cargo_mapping
        <trim prefix="SET" suffixOverrides=",">
            <if test="cargoCode != null and cargoCode != ''">cargo_code = #{cargoCode},</if>
            <if test="cargoName != null and cargoName != ''">cargo_name = #{cargoName},</if>
            <if test="airCargoCode != null and airCargoCode != ''">air_cargo_code = #{airCargoCode},</if>
            <if test="airCargoName != null and airCargoName != ''">air_cargo_name = #{airCargoName},</if>
            <if test="airCode != null and airCode != ''">air_code = #{airCode},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="delCargoMapping" parameterType="long">
        update base_cargo_mapping set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <select id="selectCargoCodeMappingList" resultType="com.gzairports.common.basedata.domain.BaseCargoMapping">
        select id, cargo_code, cargo_name, air_cargo_code, air_cargo_name, air_code from base_cargo_mapping
        <where>
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="cargoCode != null  and cargoCode != ''"> and cargo_code = #{cargoCode}</if>
            <if test="airCargoCode != null and airCargoCode != ''"> and air_cargo_code = #{airCargoCode}</if>
            <if test="airCode != null and airCode != ''"> and air_code = #{airCode}</if>
            <if test="deptId != null"> and (is_cpmmon = 0 or is_common = #{deptId})</if>
            and is_del = 0
        </where>
    </select>
    <select id="selectCargoMappingByCode" resultType="com.gzairports.common.basedata.domain.BaseCargoMapping">
        select id, cargo_code, cargo_name, air_cargo_code, air_cargo_name, air_code
        from base_cargo_mapping
        where cargo_code = #{cargoCode} and air_cargo_code = #{airCargoCode}
          and air_code = #{airCode} and is_del = 0
    </select>
</mapper>
