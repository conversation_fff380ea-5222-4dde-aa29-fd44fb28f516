<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.departure.mapper.FlightLoadMapper">
    <update id="updateFlightLoadById">
        update hz_flight_load
        <trim prefix="SET" suffixOverrides=",">

            <if test="h1Quantity == null">h1_quantity = null,</if>
            <if test="h1Quantity != null">h1_quantity = #{h1Quantity},</if>
            <if test="h1Weight == null">h1_weight = null,</if>
            <if test="h1Weight != null">h1_weight = #{h1Weight},</if>

            <if test="h2Quantity == null">h2_quantity = null,</if>
            <if test="h2Quantity != null">h2_quantity = #{h2Quantity},</if>
            <if test="h2Weight == null">h2_weight = null,</if>
            <if test="h2Weight != null">h2_weight = #{h2Weight},</if>

            <if test="h3Quantity == null">h3_quantity = null,</if>
            <if test="h3Quantity != null">h3_quantity = #{h3Quantity},</if>
            <if test="h3Weight == null">h3_weight = null,</if>
            <if test="h3Weight != null">h3_weight = #{h3Weight},</if>

            <if test="h4Quantity == null">h4_quantity = null,</if>
            <if test="h4Quantity != null">h4_quantity = #{h4Quantity},</if>
            <if test="h4Weight == null">h4_weight = null,</if>
            <if test="h4Weight != null">h4_weight = #{h4Weight},</if>

            <if test="h5Quantity == null">h5_quantity = null,</if>
            <if test="h5Quantity != null">h5_quantity = #{h5Quantity},</if>
            <if test="h5Weight == null">h5_weight = null,</if>
            <if test="h5Weight != null">h5_weight = #{h5Weight},</if>

            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>
    <select id="addQuery" resultType="com.gzairports.hz.business.departure.domain.vo.ForwardImportVo">
        select hfl.id, afi.is_settle, afi.is_comp, hfl.state from hz_flight_load hfl
            left join all_flight_info afi on afi.flight_id = hfl.flight_id
        <where>
            hfl.is_off_in = 'D'
            <if test="flightDate != null">
                AND afi.exec_date = #{flightDate}
            </if>
            <if test="flightNo != null and flightNo != ''">
                AND CONCAT(afi.air_ways,afi.flight_no) = #{flightNo}
            </if>
            <if test="leg != null and leg != ''">
                AND hfl.leg = #{leg}
            </if>
        </where>
    </select>
    <select id="selectLoadList" resultType="com.gzairports.hz.business.departure.domain.vo.FlightLoadVo">
        select hfl.id, hfl.flight_id, CONCAT(afi.air_ways,afi.flight_no) AS flight_no, afi.exec_date, afi.start_scheme_takeoff_time, hfl.state,
               hfl.load_time, hfl.h1_quantity, hfl.h1_weight,  hfl.h2_quantity, hfl.h2_weight, hfl.h3_quantity, hfl.h3_weight, afi.is_create,
               hfl.h4_quantity, hfl.h4_weight, hfl.h5_quantity, hfl.h5_weight, hfl.leg as legs from hz_flight_load hfl
            left join all_flight_info afi on afi.flight_id = hfl.flight_id
        <where>
            hfl.is_off_in = 'D'
            <if test="query.startExecDate != null">
                AND DATE_FORMAT(afi.exec_date,'%Y%m%d') <![CDATA[>=]]> DATE_FORMAT(#{query.startExecDate, jdbcType=DATE},'%Y%m%d')
            </if>
            <if test="query.stopExecDate != null">
                AND DATE_FORMAT(afi.exec_date,'%Y%m%d') <![CDATA[<=]]> DATE_FORMAT(#{query.stopExecDate, jdbcType=DATE},'%Y%m%d')
            </if>
            <if test="query.flightNo != null and query.flightNo != ''">
                AND CONCAT(afi.air_ways,afi.flight_no) like CONCAT('%',#{query.flightNo},'%')
            </if>
            <if test="query.isPre != null">
                <choose>
                    <when test="query.isPre == 1">
                        AND hfl.is_pre = #{query.isPre}
                    </when>
                    <otherwise>
                        AND hfl.state != 'been_dep' AND hfl.is_pre = '0'
                    </otherwise>
                </choose>
            </if>
            <if test="noDisplayedList != null  and noDisplayedList.length > 0"> and afi.air_ways not in
                <foreach collection="noDisplayedList" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="query.enabledAirlineCodeList != null and query.enabledAirlineCodeList.size() > 0">
                AND afi.air_ways in
                <foreach collection="query.enabledAirlineCodeList" item="airline" open="(" separator="," close=")">
                    #{airline}
                </foreach>
            </if>
        </where>
        order by afi.start_scheme_takeoff_time asc
    </select>
    <select id="legQuery" resultType="java.lang.String">
        select hfl.leg from hz_flight_load hfl
        left join all_flight_info afi on afi.flight_id = hfl.flight_id
        <where>
            hfl.is_off_in = 'D'
            <if test="flightDate != null">
                AND afi.exec_date = #{flightDate}
            </if>
            <if test="flightNo != null and flightNo != ''">
                AND CONCAT(afi.air_ways,afi.flight_no) = #{flightNo}
            </if>
        </where>
    </select>
    <select id="selectForMalManifestById"
            resultType="com.gzairports.hz.business.departure.domain.vo.FormalManifestVo">
        select hfl.id, CONCAT(afi.air_ways,afi.flight_no) AS flight_no, afi.exec_date, afi.craft_no, afi.craft_site,afi.start_scheme_takeoff_time, hfl.leg,
               hfl.load_time, hfl.load_user
        from hz_flight_load hfl
             left join all_flight_info afi on afi.flight_id = hfl.flight_id where hfl.id = #{id}
    </select>
    <select id="selectGroupList" resultType="com.gzairports.hz.business.departure.domain.vo.GroupCargoVo">
        select hfl.id, CONCAT(afi.air_ways,afi.flight_no) AS flight_no, afi.exec_date, afi.start_scheme_takeoff_time, hfl.state,
               afi.craft_no,hfl.load_time, hfl.load_user, hfl.group_user, hfl.group_time
        from hz_flight_load hfl
        left join all_flight_info afi on afi.flight_id = hfl.flight_id
        <where>
            hfl.is_off_in = 'D'
            <if test="execDate != null">
                AND afi.exec_date = #{execDate}
            </if>
            <if test="flightNo != null and flightNo != ''">
                AND CONCAT(afi.air_ways,afi.flight_no) = #{flightNo}
            </if>
            <if test="state != null and state != ''">
                AND
                case #{state}
                when 'been_pre' then
                (hfl.state = 'been_pre' or hfl.group_user is null)
                else
                hfl.state = #{state}
                end
            </if>
            <if test="groupUser != null and groupUser != ''">
                AND hfl.group_user = #{groupUser}
            </if>
        </where>
    </select>
    <select id="selectByGroupById" resultType="com.gzairports.hz.business.departure.domain.vo.GroupCargoVo">
        select hfl.id, CONCAT(afi.air_ways,afi.flight_no) AS flight_no, afi.exec_date, afi.start_scheme_takeoff_time, hfl.state,
               hfl.load_time, hfl.load_user, hfl.group_user, hfl.group_time, hfl.weight_time, hfl.transfer_time, hdh.sign_url,
               hdh.handover_time
        from hz_flight_load hfl
                 left join all_flight_info afi on afi.flight_id = hfl.flight_id
                 left join hz_dep_handover hdh on hfl.id = hdh.flight_load_id
        where hfl.id = #{id}
    </select>
    <select id="selectNoById" resultType="java.lang.String">
        select CONCAT(afi.air_ways,afi.flight_no)
        from hz_flight_load hfl
            left join all_flight_info afi on afi.flight_id = hfl.flight_id where hfl.id = #{id}
    </select>
    <select id="selectLegId" resultType="java.lang.Long">
        select hfl.id
        from hz_flight_load hfl
                 left join all_flight_info afi on afi.flight_id = hfl.flight_id
        <where>
            hfl.is_off_in = 'A'
            <if test="execDate != null">
                AND afi.exec_date = #{execDate}
            </if>
            <if test="flightNo != null and flightNo != ''">
                AND CONCAT(afi.air_ways,afi.flight_no) = #{flightNo}
            </if>
            <if test="leg != null and leg != ''">
                AND hfl.leg = #{leg}
            </if>
        </where>
    </select>
    <select id="getLeg" resultType="com.gzairports.hz.business.arrival.domain.vo.LegVo">
        select afi.flight_id, hfl.id as legId, hfl.leg, afi.terminal_scheme_land_in_time, afi.is_comp,
               afi.terminal_alteratel_and_in_time from hz_flight_load hfl
        left join all_flight_info afi on afi.flight_id = hfl.flight_id
        <where>
            hfl.is_off_in = 'A'
            <if test="execDate != null">
                AND afi.exec_date = #{execDate}
            </if>
            <if test="flightNo != null and flightNo != ''">
                AND CONCAT(afi.air_ways,afi.flight_no) like CONCAT('%',#{flightNo},'%')
            </if>
        </where>
    </select>
    <select id="selectFlightId" resultType="java.lang.Long">
        select afi.flight_id from hz_flight_load hfl
        left join all_flight_info afi on afi.flight_id = hfl.flight_id
        <where>
            afi.is_offin = 'A'
            <if test="execDate != null">
                AND afi.exec_date = #{execDate}
            </if>
            <if test="flightNo != null and flightNo != ''">
                AND CONCAT(afi.air_ways,afi.flight_no) = #{flightNo}
            </if>
            <if test="leg != null and leg != ''">
                AND hfl.leg = #{leg}
            </if>
        </where>
    </select>
    <select id="selectStatusLoadList" resultType="com.gzairports.hz.business.departure.domain.vo.DetailedVo">
        (
            select afi.exec_date as createTime, CONCAT(afi.air_ways,afi.flight_no) as flightNo, 'BLK' as uld, '' as cabin, hw.quantity,
            hw.weight, hl.load_user as operName, hl.load_time as operTime
            from hz_flight_load_waybill hw
            left join hz_flight_load hl on hl.id = hw.flight_load_id
            left join all_flight_info afi on afi.flight_id = hl.flight_id
            <where>
                <if test="id != null and id != ''">
                    hw.waybill_id = #{id}
                </if>
            </where>
        )
       union all
        (
            select afi.exec_date as createTime, CONCAT(afi.air_ways,afi.flight_no) as flightNo, hu.uld, hu.cabin, hw.quantity,
            hw.weight, hl.load_user as operName, hl.load_time as operTime
            from hz_flight_load_uld_waybill hw
                left join hz_flight_load_uld hu on hu.id = hw.load_uld_id
                left join hz_flight_load hl on hl.id = hu.flight_load_id
                left join all_flight_info afi on afi.flight_id = hl.flight_id
            <where>
                <if test="id != null and id != ''">
                    hw.waybill_id = #{id}
                </if>
            </where>
        )
    </select>
    <select id="selectLegIdByVo" resultType="java.lang.Long">
        select id from hz_flight_load where flight_id = #{flightId}
    </select>
    <select id="getAirLinesByCode" resultType="java.lang.String">
        select air_ways
        from all_flight_info
        where flight_no = #{airlinesCode}
          and exec_date = #{flightDate}
          and is_offin = #{type}
        limit 1
    </select>
    <select id="selectIdByInfo" resultType="java.lang.Long">
        select hfl.id from hz_flight_load hfl left join all_flight_info afi on afi.flight_id = hfl.flight_id
        where afi.is_offin = 'D' and afi.exec_date = #{execDate} and CONCAT(afi.air_ways,afi.flight_no) = #{flightNo}
    </select>
    <select id="selectFlightIds" resultType="java.lang.Long">
        select hfl.id from hz_flight_load hfl left join all_flight_info afi on afi.flight_id = hfl.flight_id
        where afi.is_offin = 'D' and afi.start_real_takeoff_time is not null
        <if test="loadIds != null and loadIds.size() > 0">
            and hfl.id in
            <foreach collection="loadIds" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="selectYesterdayLoadList"
            resultType="com.gzairports.hz.business.departure.domain.vo.YesterdayLoadVo">
        select id, weight_time from hz_flight_load where flight_id = #{flightId}
    </select>
    <select id="selectCountByUldNo" resultType="java.lang.Integer">
        SELECT sum(count) from
            (select count(*) as count
             from hz_flight_load_uld_waybill hw
                 left join hz_flight_load_uld hu on hu.id = hw.load_uld_id
                 left join hz_flight_load hl on hl.id = hu.flight_load_id
                 left join all_flight_info afi on afi.flight_id = hl.flight_id
             where hu.uld = #{uldNo} and afi.flight_id = #{flightId}
             )AS combined
    </select>
    <select id="selectIdByFlightId" resultType="java.lang.Long">
        select id from hz_flight_load where flight_id = #{flightId}
    </select>
</mapper>