package com.gzairports.common.charge.service.impl;

import com.gzairports.common.business.arrival.domain.HzArrItem;
import com.gzairports.common.business.arrival.domain.ItemDetail;
import com.gzairports.common.charge.domain.HzChargeItemRule;
import com.gzairports.common.charge.domain.vo.BillRuleVo;
import com.gzairports.common.charge.service.BillingRule;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 冷库计费规则
 * <AUTHOR>
 * @date 2024-08-03
 */
public class ColdStorageBillingRule implements BillingRule {
    @Override
    public String className() {
        return "ColdStorageBillingRule.class";
    }

    @Override
    public BillRuleVo calculateFee(List<HzChargeItemRule> rule, BigDecimal weight, Integer quantity, ItemDetail item) {
        BillRuleVo vo = new BillRuleVo();
        List<HzChargeItemRule> collect = rule.stream().filter(e -> e.getStore() != null && e.getStore().equals(item.getColdStore())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)){
            HzChargeItemRule itemRule = collect.get(0);
            long start = item.getStoreStartTime().getTime();
            long end = item.getStoreEndTime().getTime();
            long diff = end - start;
            long totalHours = TimeUnit.MILLISECONDS.toHours(diff);
            if (diff % (60 * 60 * 1000) != 0) {
                totalHours++;
            }
            long actualDays = totalHours / 24;
            if (totalHours % 24 != 0) {
                actualDays++;
            }
            BigDecimal totalCost = itemRule.getOneHour()
                    .add(itemRule.getAfter()
                            .multiply(weight)
                            .multiply(BigDecimal.valueOf(actualDays)));
            vo.setQuantity(String.valueOf(actualDays));
            vo.setTotalCharge(totalCost);
            return vo;
        }
        return vo;
    }
}
