package com.gzairports.web.controller.business.departure;

import com.gzairports.common.basedata.domain.BaseAgent;
import com.gzairports.common.basedata.domain.BaseCargoUldForSave;
import com.gzairports.common.basedata.domain.BaseCarrier;
import com.gzairports.common.basedata.mapper.CarrierMapper;
import com.gzairports.common.basedata.service.IAirportCodeService;
import com.gzairports.common.basedata.service.IBaseAgentService;
import com.gzairports.common.business.departure.mapper.MawbMapper;
import com.gzairports.common.core.domain.PageQuery;
import com.gzairports.common.core.domain.entity.SysDept;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.pdf.PdfPrintHelper;
import com.gzairports.common.securitySubmit.domain.AllSecurityWaybill;
import com.gzairports.common.securitySubmit.domain.SecurityWaybillInfo;
import com.gzairports.common.securitySubmit.mapper.AllSecurityWaybillMapper;
import com.gzairports.common.system.domain.SysConfig;
import com.gzairports.common.system.mapper.SysConfigMapper;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.sign.Base64;
import com.gzairports.common.utils.spring.SpringUtils;
import com.gzairports.hz.business.departure.domain.AirWaybill;
import com.gzairports.hz.business.departure.domain.HzCollectWaybill;
import com.gzairports.hz.business.departure.domain.query.*;
import com.gzairports.hz.business.departure.domain.vo.*;
import com.gzairports.hz.business.departure.mapper.AllAirWaybillMapper;
import com.gzairports.hz.business.departure.service.IHzCollectWaybillService;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.oss.domain.SysOss;
import com.gzairports.oss.service.impl.SysOssServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.net.ssl.HttpsURLConnection;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * 运单收运Controller
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@RestController
@RequestMapping("/dep/collectWaybill")
@Api(tags = "运单收运")
public class HzCollectWaybillController extends BaseController {

    @Autowired
    private IHzCollectWaybillService collectWaybillService;
    @Autowired
    private IBaseAgentService baseAgentService;
    @Autowired
    private AllAirWaybillMapper airWaybillMapper;
    @Autowired
    private MawbMapper mawbMapper;
    @Autowired
    private SysDeptMapper sysDeptMapper;
    @Autowired
    private IAirportCodeService airportCodeService;
    @Autowired
    private CarrierMapper carrierMapper;
    @Autowired
    private SysConfigMapper configMapper;
    @Autowired
    private AllSecurityWaybillMapper allSecurityWaybillMapper;


    /**
     * 查询运单收运列表
     */
//    @PreAuthorize("@ss.hasPermi('dep:collectWaybill:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询运单收运列表")
    public TableDataInfo list(HzTransferQuery query) {
        startPage();
        List<HzTransferVo> list = collectWaybillService.selectCollectWaybillList(query);
        return getDataTable(list);
    }

    /**
     * app查询运单收运列表
     */
//    @PreAuthorize("@ss.hasPermi('dep:collectWaybill:appList')")
    @GetMapping("/appList")
    @ApiOperation(value = "app查询运单收运列表")
    public AjaxResult appList(HzTransferQuery query) {
        AppCollectVo vo = collectWaybillService.selectCollectWaybillAppList(query);
        return AjaxResult.success(vo);
    }

    /**
     * 导出运单收运数据
     */
    @PreAuthorize("@ss.hasPermi('dep:collectWaybill:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出运单收运数据")
    public void export(HttpServletResponse response, HzTransferQuery query) {
        List<HzTransferVo> list = collectWaybillService.selectCollectWaybillList(query);
        ExcelUtil<HzTransferVo> util = new ExcelUtil<HzTransferVo>(HzTransferVo.class);
        util.exportExcel(response, list, "开箱抽查");
    }

    /**
     * 保存运单
     */
    @PreAuthorize("@ss.hasPermi('dep:collectWaybill:saveWaybill')")
    @PostMapping("/saveWaybill")
    @ApiOperation(value = "保存运单")
    public AjaxResult saveWaybill(@RequestBody WaybillInfoVo vo) {
        return toAjax(collectWaybillService.saveWaybill(vo));
    }

    /**
     * 生成运单
     */
//    @PreAuthorize("@ss.hasPermi('dep:collectWaybill:generateWaybill')")
    @GetMapping("/generateWaybill/{waybillCode}")
    @ApiOperation(value = "生成运单")
    public AjaxResult generateWaybill(@PathVariable("waybillCode") String waybillCode) throws Exception {

        WaybillInfoVo vo = collectWaybillService.getInfo(waybillCode);
        String substring = vo.getWaybillCode().substring(4);
        String waybillCodeAbb = substring.substring(0, 3) + "-" + substring.substring(3);
        vo.setWaybillCodeAbb(waybillCodeAbb);
        if (StringUtils.isNotEmpty(vo.getDangerCode())) {
            vo.setWxp("YES");
        } else if (StringUtils.isNotEmpty(vo.getSpecialCargoCode1())) {
            vo.setTzhw("YES");
        } else {
            vo.setPthw("YES");
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if (vo.getFlightDate1() != null) {
            vo.setFlightDate1Str(simpleDateFormat.format(vo.getFlightDate1()));
        }
        if (vo.getFlightDate2() != null) {
            vo.setFlightDate2Str(simpleDateFormat.format(vo.getFlightDate2()));
        }
        if (vo.getWriteTime() != null) {
            vo.setWriteTimeStr(simpleDateFormat.format(vo.getWriteTime()));
        }
        BaseAgent agent = baseAgentService.selectBaseAgentByName(vo.getAgentCompany());
        if (agent != null && StringUtils.isNotEmpty(agent.getSealUrl())) {
            byte[] bytes = downloadFileFromUrl(agent.getSealUrl());
            String sealImage = Base64.encode(bytes);
            vo.setSealUrl(sealImage);
        }
        String mawbUrl = getPdfUrl(vo, "electronic/mawb.pdf");
        vo.setPdfUrl(mawbUrl);
        collectWaybillService.saveWaybillUrl(vo);
        return success(mawbUrl);
    }

    /**
     * 生成安检申报单
     */
//    @PreAuthorize("@ss.hasPermi('dep:collectWaybill:generateSecurity')")
    @GetMapping("/generateSecurity/{waybillCode}")
    @ApiOperation(value = "生成安检申报单")
    public AjaxResult generateSecurity(@PathVariable("waybillCode") String waybillCode) throws Exception {
        WaybillInfoVo vo = collectWaybillService.getInfo(waybillCode);

        //将目的站从英文转化为中文
        String desPortChinese = airportCodeService.selectChineseName(vo.getDesPort());
        if (StringUtils.isNotEmpty(desPortChinese)) {
            vo.setDesPort(desPortChinese);
        }

        String substring = vo.getWaybillCode().substring(4);
        String waybillCodeAbb = substring.substring(0, 3) + "-" + substring.substring(3);
        vo.setWaybillCodeAbb(waybillCodeAbb);
        if (StringUtils.isNotEmpty(vo.getDangerCode())) {
            vo.setWxp("YES");
        } else if (StringUtils.isNotEmpty(vo.getSpecialCargoCode1())) {
            vo.setTzhw("YES");
        } else {
            vo.setPthw("YES");
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if (vo.getFlightDate1() != null) {
            vo.setFlightDate1Str(simpleDateFormat.format(vo.getFlightDate1()));
        }
        BaseAgent agent = baseAgentService.selectBaseAgentByName(vo.getAgentCompany());
        if (agent != null && StringUtils.isNotEmpty(agent.getSealUrl())) {
            byte[] bytes = downloadFileFromUrl(agent.getSealUrl());
            String sealImage = Base64.encode(bytes);
            vo.setSealUrl(sealImage);
        }
        String securityUrl = getPdfUrl(vo, "electronic/security.pdf");
        vo.setSecurityUrl(securityUrl);
        collectWaybillService.saveWaybillUrl(vo);
        return success(securityUrl);
    }

    private byte[] downloadFileFromUrl(String urlStr) throws Exception {
        // 创建一个信任所有证书的信任管理器
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }

                    @Override
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                    }

                    @Override
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                    }
                }
        };

        // 安装信任所有证书的信任管理器
        SSLContext sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, trustAllCerts, new java.security.SecureRandom());

        URL url = new URL(urlStr);
        HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
        connection.setSSLSocketFactory(sslContext.getSocketFactory());

        // 设置自定义 HostnameVerifier（仅限测试环境）
        connection.setHostnameVerifier((hostname, session) -> true);

        connection.setRequestMethod("GET");
        connection.setConnectTimeout(5000);
        connection.setReadTimeout(5000);

        int responseCode = connection.getResponseCode();
        if (responseCode != 200) {
            throw new RuntimeException("Failed to connect, HTTP error code : " + responseCode);
        }

        try (InputStream inputStream = connection.getInputStream();
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            return baos.toByteArray();
        } catch (IOException e) {
            throw new RuntimeException("IO Exception occurred during file download", e);
        }
    }

    /**
     * 运单收运
     */
    @PreAuthorize("@ss.hasPermi('dep:collectWaybill:getInfo')")
    @GetMapping("/getInfo/{waybillCode}")
    @ApiOperation(value = "运单收运")
    public AjaxResult getInfo(@PathVariable("waybillCode") String waybillCode) {
        WaybillInfoVo vo = collectWaybillService.getInfo(waybillCode);
        return AjaxResult.success(vo);
    }

    @GetMapping("/getInfoApp/{waybillCode}")
    @ApiOperation(value = "运单收运")
    public AjaxResult getInfoApp(@PathVariable("waybillCode") String waybillCode) {
        WaybillInfoVo vo = collectWaybillService.getInfoApp(waybillCode);
        return AjaxResult.success(vo);
    }

    /**
     * 收运取消
     */
    @PreAuthorize("@ss.hasPermi('dep:collectWaybill:getInfo')")
    @PostMapping("/cancelWaybill")
    @ApiOperation(value = "收运取消")
    public AjaxResult cancelWaybill(@RequestBody WaybillInfoVo vo) {
        return AjaxResult.success(collectWaybillService.cancelWaybill(vo));
    }

    /**
     * 收运
     */
    @PreAuthorize("@ss.hasPermi('dep:collectWaybill:collect')")
    @GetMapping("/collect/{id}")
    @ApiOperation(value = "收运")
    public AjaxResult collect(@PathVariable("id") Long id) {
        return AjaxResult.success(collectWaybillService.collect(id));
    }

    /**
     * 收运
     */
    @PreAuthorize("@ss.hasPermi('dep:collectWaybill:collectWaybill')")
    @GetMapping("/collectWaybill/{waybillId}")
    @ApiOperation(value = "收运")
    public AjaxResult collectWaybill(@PathVariable("waybillId") Long waybillId) {
        return AjaxResult.success(collectWaybillService.collectWaybill(waybillId));
    }

    /**
     * 收运完成
     */
    @PreAuthorize("@ss.hasPermi('dep:collectWaybill:add')")
    @PostMapping("/add")
    @ApiOperation(value = "收运完成")
    public AjaxResult add(@RequestBody CollectWaybillVo vo) {
        return AjaxResult.success("操作成功", collectWaybillService.addCollectWaybill(vo));
    }

    /**
     * 虚拟收运
     */
    @PreAuthorize("@ss.hasPermi('dep:collectWaybill:virtualCollect')")
    @PostMapping("/virtualCollect")
    @ApiOperation(value = "虚拟收运")
    public AjaxResult virtualCollect(@RequestBody HzCollectWaybill waybill) {
        return AjaxResult.success("操作成功", collectWaybillService.virtualCollect(waybill));
    }


    /**
     * 批量虚拟收运
     */
    @PostMapping("/virtualCollectBatch")
    @ApiOperation(value = "批量虚拟收运")
    public AjaxResult virtualCollectBatch(@RequestBody Long[] ids) {
        return AjaxResult.success("操作成功", collectWaybillService.virtualCollectBatch(ids));
    }

    /**
     * 拒绝收运
     */
    @PreAuthorize("@ss.hasPermi('dep:collectWaybill:refuseCollect')")
    @PostMapping("/refuseCollect")
    @ApiOperation(value = "拒绝收运")
    public AjaxResult refuseCollect(@RequestBody HzCollectWaybill waybill) {
        return toAjax(collectWaybillService.refuseCollect(waybill));
    }

    /**
     * 货站安检提交
     */
    @PreAuthorize("@ss.hasPermi('dep:collectWaybill:securitySubmit')")
    @PostMapping("/securitySubmit")
    @ApiOperation(value = "安检提交")
    public AjaxResult securitySubmit(@RequestBody SecuritySubmitQuery query) throws Exception {
        AirWaybill airWaybill = airWaybillMapper.selectById(query.getWaybillId());
        AllSecurityWaybill allSecurityWaybill = allSecurityWaybillMapper.selectById(query.getWaybillId());
        if (airWaybill == null) {
            SecurityWaybillInfo info = new SecurityWaybillInfo();
            BeanUtils.copyProperties(allSecurityWaybill, info);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            info.setFlightDate1Str(simpleDateFormat.format(allSecurityWaybill.getFlightDate1()));
            info.setAgentCompany(allSecurityWaybill.getAgent());
            if (allSecurityWaybill.getCargoType() != null) {
                if (allSecurityWaybill.getCargoType() == 0) {
                    info.setPthw("YES");
                } else if (allSecurityWaybill.getCargoType() == 1) {
                    info.setTzhw("YES");
                } else {
                    info.setWxp("YES");
                }
            }
            SysDept sysDept = sysDeptMapper.selectDeptById(allSecurityWaybill.getDeptId());
            if (sysDept != null) {
                if (StringUtils.isNotEmpty(sysDept.getSealUrl())) {
                    byte[] bytes = downloadFileFromUrl(sysDept.getSealUrl());
                    String logoImage = Base64.encode(bytes);
                    info.setSealUrl(logoImage);
                    info.setSealUrl2(logoImage);
                }
//                info.setShipperStr(sysDept.getDeptName());
                info.setShipperStr(sysDept.getDeptName());
                if (StringUtils.isEmpty(sysDept.getCreateUser())) {
                    throw new CustomException("安检申报姓名未设置,请到部门管理进行设置");
                }
                info.setAgentSignature(sysDept.getCreateUser());
                info.setShipperSignature(sysDept.getCreateUser());
            }
            //将目的站从英文转化为中文
            String desPortChinese = airportCodeService.selectChineseName(info.getDesPort());
            if (StringUtils.isNotEmpty(desPortChinese)) {
                info.setDesPort(desPortChinese);
            }
            info.setCheckConclusionCollect(query.getDeclarationConsistent() == 1 ? "符合运输" : "退回");
            info.setSealUrlCollect(SecurityUtils.getNickName());
            String securityUrl;
            if (allSecurityWaybill.getWaybillCode().contains("AWBA")) {
                String substring = info.getWaybillCode().substring(4);
                String waybillCodeAbb = substring.substring(0, 3) + "-" + substring.substring(3);
                info.setWaybillCodeAbb(waybillCodeAbb);
                securityUrl = getPdfUrl(info, "electronic/security.pdf");
                allSecurityWaybill.setSecurityUrl(securityUrl);
            } else {
                String substring = info.getWaybillCode().substring(4);
                String waybillCodeAbb = substring.substring(0, 2) + "-" + substring.substring(2);
                info.setWaybillCodeAbb(waybillCodeAbb);
                info.setShipperAbb(allSecurityWaybill.getShipper());
                securityUrl = getPdfUrl(info, "electronic/mailSecurity.pdf");
                allSecurityWaybill.setSecurityUrl(securityUrl);
            }
            allSecurityWaybill.setSecurityUrl(securityUrl);
            allSecurityWaybill.setSecuritySubmitOperator(SecurityUtils.getNickName());
            allSecurityWaybillMapper.updateById(allSecurityWaybill);
            return toAjax(collectWaybillService.securitySubmit(query));
        } else {
            //参数配置 如果开启为true 则必须预支付才能安检和配载
//            SysConfig sysConfig = configMapper.checkConfigKeyUnique("isPay.submitLoad");
//            if("true".equals(sysConfig.getConfigValue())
//                    && (airWaybill.getPayStatus() == 0 || airWaybill.getPayStatus() == 14)
//                    && !airWaybill.getWaybillCode().contains("DN")){
//                String substring = airWaybill.getWaybillCode().substring(4);
//                throw new CustomException("运单"+substring.substring(0,3) + "-" + substring.substring(3)+"未支付");
//            }
            String securityUrl = "";
            SysDept sysDept = sysDeptMapper.selectDeptById(airWaybill.getDeptId());
            //这里要区分是主单还是邮件单
            if (airWaybill.getWaybillCode().contains("AWBA")) {
                SecurityWaybillInfo vo = mawbMapper.getInfo(airWaybill.getWaybillCode());
                vo.setCheckConclusionCollect(query.getDeclarationConsistent() == 1 ? "符合运输" : "退回");
                vo.setSealUrlCollect(SecurityUtils.getNickName());
                if (sysDept != null) {
                    if (sysDept.getSealUrl() != null) {
                        byte[] bytes2 = downloadFileFromUrl(sysDept.getSealUrl());
                        String logoImage = Base64.encode(bytes2);
                        vo.setSealUrl(logoImage);
                        vo.setSealUrl2(logoImage);
                    }
                    vo.setShipperStr(sysDept.getDeptName());
                    if (StringUtils.isEmpty(sysDept.getCreateUser())) {
                        throw new CustomException("安检申报姓名未设置,请到部门管理进行设置");
                    }
                    vo.setAgentSignature(sysDept.getCreateUser());
                    vo.setShipperSignature(sysDept.getCreateUser());
                }
                vo.setCheckConclusionCollect(query.getDeclarationConsistent() == 1 ? "符合运输" : "退回");
                vo.setSealUrlCollect(SecurityUtils.getNickName());
                String substring = vo.getWaybillCode().substring(4);
                String waybillCodeAbb = substring.substring(0, 3) + "-" + substring.substring(3);
                vo.setWaybillCodeAbb(waybillCodeAbb);
                if (StringUtils.isNotEmpty(vo.getDangerCode())) {
                    vo.setWxp("YES");
                } else if (StringUtils.isNotEmpty(vo.getSpecialCargoCode1())) {
                    vo.setTzhw("YES");
                } else {
                    vo.setPthw("YES");
                }
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                if (vo.getFlightDate1() != null) {
                    vo.setFlightDate1Str(simpleDateFormat.format(vo.getFlightDate1()));
                }
                //将目的站从英文转化为中文
                String desPortChinese = airportCodeService.selectChineseName(vo.getDesPort());
                if (StringUtils.isNotEmpty(desPortChinese)) {
                    vo.setDesPort(desPortChinese);
                }

                if (StringUtils.isNotEmpty(airWaybill.getShipperSignature())) {
                    vo.setShipperSignature(airWaybill.getShipperSignature());
                }
                if (StringUtils.isNotEmpty(airWaybill.getAgentSignature())) {
                    vo.setAgentSignature(airWaybill.getAgentSignature());
                }
                securityUrl = getPdfUrl(vo, "electronic/security.pdf");
            } else {
                SecurityWaybillInfo mailVo = new SecurityWaybillInfo();
                BeanUtils.copyProperties(airWaybill, mailVo);
                if (sysDept != null) {
                    if (StringUtils.isNotEmpty(sysDept.getSealUrl())) {
                        byte[] bytes3 = downloadFileFromUrl(sysDept.getSealUrl());
                        String logoImage = Base64.encode(bytes3);
                        mailVo.setSealUrl(logoImage);
                    }
                }
                mailVo.setCheckConclusionCollect(query.getDeclarationConsistent() == 1 ? "符合运输" : "退回");
                mailVo.setSealUrlCollect(SecurityUtils.getNickName());
                if (StringUtils.isNotEmpty(airWaybill.getShipperSignature())) {
                    mailVo.setShipperSignature(airWaybill.getShipperSignature());
                }
                if (StringUtils.isNotEmpty(airWaybill.getAgentSignature())) {
                    mailVo.setAgentSignature(airWaybill.getAgentSignature());
                }
                String substring = mailVo.getWaybillCode().substring(4);
                String waybillCodeAbb = substring.substring(0, 2) + "-" + substring.substring(2);
                mailVo.setWaybillCodeAbb(waybillCodeAbb);
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                if (mailVo.getFlightDate1() != null) {
                    mailVo.setFlightDate1Str(simpleDateFormat.format(mailVo.getFlightDate1()));
                }
                //将目的站从英文转化为中文
                String desPortChinese = airportCodeService.selectChineseName(mailVo.getDesPort());
                if (StringUtils.isNotEmpty(desPortChinese)) {
                    mailVo.setDesPort(desPortChinese);
                }
                String carrier1 = mailVo.getCarrier1();
                BaseCarrier baseCarrier = carrierMapper.selectByCode(carrier1);
                if (baseCarrier != null) {
                    mailVo.setCarrier1(baseCarrier.getChineseName());
                }
                securityUrl = getPdfUrl(mailVo, "electronic/mailSecurity.pdf");
            }

            airWaybill.setSecurityUrl(securityUrl);
            airWaybill.setSecuritySubmitOperator(SecurityUtils.getNickName());
            airWaybillMapper.updateById(airWaybill);
            if (allSecurityWaybill != null) {
                allSecurityWaybill.setSecurityUrl(securityUrl);
                allSecurityWaybill.setSecuritySubmitOperator(SecurityUtils.getNickName());
                allSecurityWaybillMapper.updateById(allSecurityWaybill);
            }
            return toAjax(collectWaybillService.securitySubmit(query));
        }

    }

    /**
     * 查看随附文件
     */
    @PreAuthorize("@ss.hasPermi('dep:collectWaybill:viewTransportFile')")
    @GetMapping("/viewTransportFile/{waybillId}")
    @ApiOperation(value = "查看随附文件")
    public AjaxResult viewTransportFile(@PathVariable("waybillId") Long waybillId) {
        return AjaxResult.success(collectWaybillService.viewTransportFile(waybillId));
    }

    /**
     * 虚拟收运列表
     */
//    @PreAuthorize("@ss.hasPermi('dep:collectWaybill:virtualList')")
    @GetMapping("/virtualList")
    @ApiOperation(value = "虚拟收运列表")
    public TableDataInfo virtualList(VirtualListQuery query) {
        startPage();
        List<VirtualListVo> virtualListVos = collectWaybillService.virtualList(query);
        return getDataTable(virtualListVos);
    }

    /**
     * 安检申报列表
     */
    @GetMapping("/securitySubmitList")
    @ApiOperation(value = "安检申报列表")
    public AjaxResult securitySubmitList(SecurityQuery query) {
//        startPage();
        PageQuery<List<SecurityVo>> securityListVos = collectWaybillService.securitySubmitList(query);
        return AjaxResult.success(securityListVos);
    }

    /**
     * 查询安检申报历史
     */
    @GetMapping(value = "/getHistoryList/{id}")
    @ApiOperation(value = "查询安检申报历史")
    public AjaxResult getHistoryList(@PathVariable("id") Long id) {
        return AjaxResult.success("操作成功", collectWaybillService.getHistoryList(id));
    }

    /**
     * 保存生成的安检申报单
     */
    @GetMapping("/saveWaybillForSecurity")
    @ApiOperation(value = "保存生成的安检申报单")
    public AjaxResult saveWaybillForSecurity(WaybillInfoVo query) {
        return AjaxResult.success(collectWaybillService.saveWaybillForSecurity(query));
    }

    /**
     * 根据id查询运单数据
     */
    @GetMapping("/selectWaybillbyid")
    @ApiOperation(value = "根据id查询运单数据")
    public AjaxResult selectWaybillbyid(String id) {
        return AjaxResult.success(collectWaybillService.selectWaybillbyid(id));
    }

    /**
     * 查询特货代码列表
     */
    @PreAuthorize("@ss.hasPermi('dep:collectWaybill:list')")
    @GetMapping("/getSpecialCargoCode")
    @ApiOperation(value = "查询特货代码列表")
    public AjaxResult getSpecialCargoCode() {
        return AjaxResult.success(collectWaybillService.selectSpecialCargoCode());
    }

    /**
     * 根据特货代码得到中文描述
     */
    /*@PreAuthorize("@ss.hasPermi('dep:collectWaybill:list')")*/
    @PostMapping("/getChineseDescription")
    @ApiOperation(value = "根据特货代码得到中文描述")
    public AjaxResult getChineseDescription(@RequestBody(required = false) String specialCargoCode) {
        if (StringUtils.isNull(specialCargoCode)) {
            return AjaxResult.success();
        }
        return AjaxResult.success("操作成功", collectWaybillService.selectChineseDescriptionBySpecialCargoCode(specialCargoCode));
    }

    /**
     * 查询大类,查所有或者根据货品代码查
     */
    /*@PreAuthorize("@ss.hasPermi('dep:collectWaybill:list')")*/
    @PostMapping("/getCategoryCodeList")
    @ApiOperation(value = "查询大类,查所有或者根据货品代码查")
    public AjaxResult getCategoryCodeList(@RequestBody(required = false) String cargoCode) {
        return AjaxResult.success(collectWaybillService.selectCategoryCodeList(cargoCode));
    }

    /**
     * 查询货品代码和品名,查所有或者根据大类查
     */
    /*@PreAuthorize("@ss.hasPermi('dep:collectWaybill:list')")*/
    @PostMapping("/getCargoCodeList")
    @ApiOperation(value = "查询货品代码和品名,查所有或者根据大类查")
    public AjaxResult getCargoCodeList(@RequestBody(required = false) String categoryCode) {
        return AjaxResult.success(collectWaybillService.selectCargoCodeList(categoryCode));
    }

    /**
     * 根据货品代码查品名
     */
    /*@PreAuthorize("@ss.hasPermi('dep:collectWaybill:list')")*/
    @PostMapping("/getCargoName")
    @ApiOperation(value = "根据货品代码查品名")
    public AjaxResult getCargoNameByCategoryCode(@RequestBody(required = false) String cargoCode) {
        return AjaxResult.success("操作成功", collectWaybillService.selectCargoNameByCategoryCode(cargoCode));
    }

    /**
     * 根据货品代码查相关信息
     */
    @PostMapping("/getCargoInfo")
    @ApiOperation(value = "根据货品代码查相关信息")
    public AjaxResult getCargoInfo(@RequestBody(required = false) String cargoCode) {
        return AjaxResult.success("操作成功", collectWaybillService.selectCargoInfo(cargoCode));
    }

    /**
     * 查看修改详情
     */
    @PreAuthorize("@ss.hasPermi('dep:waybillStatus:editInfo')")
    @GetMapping("/editInfo/{id}")
    @ApiOperation(value = "查看修改详情")
    public AjaxResult editInfo(@PathVariable("id") Long id) {
        CollectWaybillVo vo = collectWaybillService.editInfo(id);
        return AjaxResult.success(vo);
    }

    /**
     * 修改
     */
    @PreAuthorize("@ss.hasPermi('dep:waybillStatus:edit')")
    @PostMapping("/edit")
    @ApiOperation(value = "修改")
    public AjaxResult edit(@RequestBody CollectWaybillVo vo) {
        return AjaxResult.success("操作成功", collectWaybillService.edit(vo));
    }


    /**
     * 查询当前uld状态
     */
    @PreAuthorize("@ss.hasPermi('base:uld:saveUld')")
    @PostMapping("/selectStatus")
    @ApiOperation(value = "查询当前uld状态")
    public AjaxResult selectStatus(@RequestBody BaseCargoUldForSave uld) {
        if (collectWaybillService.selectStatus(uld)) {
            return warn("当前uld正在使用");
        }
        return null;
    }

    /**
     * 保存
     */
    @PreAuthorize("@ss.hasPermi('base:uld:saveUld')")
    @PostMapping("/saveUld")
    @ApiOperation(value = "保存")
    public AjaxResult saveUld(@RequestBody BaseCargoUldForSave uld) {
        return toAjax(collectWaybillService.saveUld(uld));
    }

    /**
     * 虚拟收运转为真实收运
     */
//    @PreAuthorize("@ss.hasPermi('dep:waybillStatus:weightInfo')")
    @GetMapping("/realCollect/{id}")
    @ApiOperation(value = "虚拟收运转为真实收运")
    public AjaxResult realCollect(@PathVariable("id") Long id) {
        return AjaxResult.success(collectWaybillService.realCollect(id));
    }

    /**
     * 虚拟收运转为真实收运
     */
//    @PreAuthorize("@ss.hasPermi('dep:waybillStatus:appRealCollect')")
    @GetMapping("/appRealCollect/{id}")
    @ApiOperation(value = "app虚拟收运转为真实收运")
    public AjaxResult appRealCollect(@PathVariable("id") Long id) {
        return toAjax(collectWaybillService.appRealCollect(id));
    }

    private String getPdfUrl(@RequestBody WaybillInfoVo vo, String s) throws Exception {
        ClassPathResource securityUrl = new ClassPathResource(s);
        if (securityUrl.exists()) {
            String path = securityUrl.getPath();
            byte[] pdfDataFromTemplate = PdfPrintHelper.getPdfDataFromTemplate(vo, path);
            // 使用StandardMultipartHttpServletRequest来创建MultipartFile
            DiskFileItem fileItem = new DiskFileItem(vo.getWaybillCode() + ".pdf", "application/pdf", true, vo.getWaybillCode() + ".pdf", -1, null);

            // 将 InputStream 写入 DiskFileItem
            fileItem.getOutputStream().write(pdfDataFromTemplate);
            fileItem.getOutputStream().close();

            // 使用 FileItem 构建 MultipartFile
            MultipartFile multipartFile = new CommonsMultipartFile(fileItem);
            SysOss upload = SpringUtils.getBean(SysOssServiceImpl.class).upload(multipartFile);
            return upload.getUrl();
        }
        return null;
    }

    private String getPdfUrl(@RequestBody SecurityWaybillInfo vo, String s) throws Exception {
        ClassPathResource securityUrl = new ClassPathResource(s);
        if (securityUrl.exists()) {
            String path = securityUrl.getPath();
            byte[] pdfDataFromTemplate = PdfPrintHelper.getPdfDataFromTemplate(vo, path);
            // 使用StandardMultipartHttpServletRequest来创建MultipartFile
            DiskFileItem fileItem = new DiskFileItem(vo.getWaybillCode() + ".pdf", "application/pdf", true, vo.getWaybillCode() + ".pdf", -1, null);

            // 将 InputStream 写入 DiskFileItem
            fileItem.getOutputStream().write(pdfDataFromTemplate);
            fileItem.getOutputStream().close();

            // 使用 FileItem 构建 MultipartFile
            MultipartFile multipartFile = new CommonsMultipartFile(fileItem);
            SysOss upload = SpringUtils.getBean(SysOssServiceImpl.class).uploadByNoHttp(multipartFile);
            return upload.getUrl();
        }
        return null;
    }

    /**
     * 查询该航班是否为宽体机
     */
    @PostMapping("/getCraftType")
    @ApiOperation(value = "查询该航班是否为宽体机")
    public AjaxResult getCraftType(@RequestBody AddQuery query) {
        return AjaxResult.success("操作成功", collectWaybillService.getCraftType(query));
    }

    /**
     * app根据运单id更新储运事项
     */
    @PostMapping("/appUpdateStorageTransportNotes")
    @ApiOperation(value = "app根据运单id更新储运事项")
    public AjaxResult appUpdateStorageTransportNotes(@RequestBody WaybillInfoVo vo) {
        return AjaxResult.success("操作成功", collectWaybillService.appUpdateStorageTransportNotes(vo));
    }

    /**
     * 取消支付
     */
    @PreAuthorize("@ss.hasPermi('dep:collectWaybill:cancelPay')")
    @GetMapping("/cancelPay/{waybillId}")
    @ApiOperation(value = "取消支付")
    public AjaxResult cancelPay(@PathVariable("waybillId") Long waybillId) {
        return toAjax(collectWaybillService.cancelPay(waybillId));
    }

}
