<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.departure.mapper.MailWaybillMapper">

    <select id="selectOneByCode" resultType="com.gzairports.wl.departure.domain.vo.WaybillTraceVo">
        select id, waybill_code as masterWaybillCode, source_port, des_port, shipper, shipper_phone,
               consign, consign_phone,  write_time, writer, flight_no1 as flightNo,
               flight_date1 as flightDate, quantity as totalQuantity, weight as totalWeight
        from all_air_waybill where waybill_code = #{waybillCode} and is_del = 0
    </select>

    <select id="selectAllByCode" resultType="com.gzairports.wl.departure.domain.vo.WaybillTraceVo">
        select id, waybill_code as masterWaybillCode, type as businessType, status, source_port, des_port, shipper, shipper_phone,
               consign, consign_phone,  write_time, writer, flight_no1 as flightNo,
               flight_date1 as flightDate, quantity as totalQuantity, weight as totalWeight
        from all_air_waybill where is_del = 0 and waybill_code = #{waybillCode}
    </select>

    <select id="queryList" resultType="com.gzairports.wl.departure.domain.vo.MailWaybillVo">
        select id, waybill_code, source_port, des_port, cargo_name, category_name, flight_no1, rate_per_kg, cost_sum,
               w_rate, w_cost_sum, r_rate, r_cost_sum, quantity, weight, charge_weight, status
        from all_air_waybill
        <where>
            and dept_id = #{deptId} and is_del = 0 and waybill_type = 'AWBM'
            <if test="startTime != null">
                and write_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and write_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="waybillCode != null and waybillCode != ''">
                and waybill_code like '%${waybillCode}%'
            </if>
            <if test="carrierCode != null and carrierCode != ''">
                and substring(flight_no1,1,2) = #{carrierCode}
            </if>
            <if test="cargoName != null and cargoName != ''">
                and cargo_name = #{cargoName}
            </if>
            <if test="desPort != null and desPort != ''">
                and des_port = #{desPort}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
        </where>
    </select>
</mapper>