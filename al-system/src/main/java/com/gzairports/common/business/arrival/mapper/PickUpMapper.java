package com.gzairports.common.business.arrival.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.arrival.domain.PickUp;
import com.gzairports.common.business.arrival.domain.query.*;
import com.gzairports.common.business.arrival.domain.vo.*;
import com.gzairports.common.business.reporter.domain.ReportPickUp;
import com.gzairports.hz.business.arrival.domain.query.WaybillQuery;
import com.gzairports.hz.business.arrival.domain.vo.BdVo;
import com.gzairports.hz.business.arrival.domain.vo.WaybillQueryVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 国内进港提货办理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-03
 */
@Mapper
public interface PickUpMapper extends BaseMapper<PickUp> {

    /**
     * 根据条件查询已提货办单数据
     * @param query 查询条件
     * @return 已提货办单数据
     */
    List<PickedUpVo> selectByQuery(PickedUpQuery query);

    List<PickedUpWaybillVo> selectWaybillByQuery(PickedUpQuery query);

    /**
     * 查看申请修改列表
     * @param query 查询参数
     * @return 列表
     */
    List<ApplyEditVo> selectApplyList(ApplyEditQuery query);

    /**
     * 根据id查询出库单数据
     * @param pickUpId 办单id
     * @return 出库单数据
     */
    PrintOutOrderVo selectOutOrderById(Long pickUpId);

    /**
     * 修改详情
     * @param id 办理提货id
     * @return 修改参数
     */
    HzApplyEditVo selectEditById(Long id);



    /**
     * 根据流水号查询办理提货详情
     * */
    HzApplyEditVo selectEditBySerialNo(Long serialNo);

    /**
     * 提货出库查询
     * @param query 查询条件
     * @return 数据
     */
    PickOrderVo selectOutList(PickUpOutQuery query);

    /**
     * 查询提货结算数据
     * @param pickUpId 办理提货id
     * @return 提货结算数据
     */
    PickUpOutVo selectPickUpOutVo(Long pickUpId);

    /**
     * 查询办单数据
     * @param query 查询条件
     * @return 结果
     */
    List<WaybillQueryVo> selectQueryList(WaybillQuery query);

    /**
     * 运单明细办单数据查询
     * @param waybillCode 运单号
     * @return 结果
     */
    List<BdVo> selectBdList(String waybillCode);

    /**
     * 代理人H5提货出库列表
     * @param query 查询条件
     * @return 列表数据
     */
    List<H5PickUpOutVo> arrH5List(H5PickUpOutQuery query);

    /**
     * 办单详情
     * @param pickUpId 办单id
     * @return 详情
     */
    H5PickUpOutVo selectH5Info(Long pickUpId);

    /**
     * 查询扫描出库数据
     * @param pickUpCode 提货码
     * @return 扫描出库数据
     */
    ScanOutVo selectByCode(@Param("pickUpCode") String pickUpCode,@Param("date") Date date);

    /**
     * 查询快速交付配置数据
     * @return 快速交付代理人列表
     */
    List<Long> selectRapidAgent();

    List<BillListVo> selectBillList(@Param("query") BillOfLadingQuery query,@Param("deptIdList") List<String> deptIdList);

    PrintOutOrderVo selectH5PrintData(@Param("pickUpCode") String pickUpCode,@Param("date") Date date);

    PickUp selectBySerial(PickUpCodeQuery query);

    List<ReportPickUp> selectReportPuckUpList(@Param("lastSyncTime") Date lastSyncTime,@Param("dateNow") Date dateNow);

    void updatePrintCount(Long id);

    boolean existsByCodeAndTime(@Param("code") String code,@Param("date") Date date);
}
