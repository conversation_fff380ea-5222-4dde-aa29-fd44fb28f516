<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.reporter.mapper.ReportPickOutMapper">
    <insert id="saveOrUpdateBatch">
        insert into all_report_pick_out
        (
        id, report_pick_up_id, pieces, weight, pick_up_code, out_time, serial_no, customer_name, total_count, waybill_code
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id}, #{item.reportPickUpId}, #{item.pieces}, #{item.weight}, #{item.pickUpCode}, #{item.outTime}, #{item.serialNo},
            #{item.customerName}, #{item.totalCount}, #{item.waybillCode}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        report_pick_up_id = VALUES(report_pick_up_id),
        pieces = VALUES(pieces),
        weight = VALUES(weight),
        pick_up_code = VALUES(pick_up_code),
        out_time = VALUES(out_time),
        serial_no = VALUES(serial_no),
        customer_name = VALUES(customer_name),
        total_count = VALUES(total_count),
        waybill_code = VALUES(waybill_code)
    </insert>
</mapper>