<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.BaseAppWlMapper">
    
    <resultMap type="BaseApp" id="BaseAppResultWl">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="versionNumber"    column="version_number"    />
        <result property="status"    column="status"    />
        <result property="uploadTime"    column="upload_time"    />
        <result property="releaseTime"    column="release_time"    />
        <result property="remark"    column="remark"    />
        <result property="appUrl"    column="app_url"    />
    </resultMap>

    <sql id="selectBaseAppVo">
        select id, name, version_number, status, upload_time, release_time, remark, app_url from base_app_wl
    </sql>

    <select id="selectBaseAppListWl" parameterType="BaseApp" resultMap="BaseAppResultWl">
        <include refid="selectBaseAppVo"/>
        <where>  
            <if test="versionNumber != null "> and version_number = #{versionNumber}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="uploadTime != null "> and upload_time = #{uploadTime}</if>
            <if test="releaseTime != null "> and release_time = #{releaseTime}</if>
        </where>
        order by upload_time desc
    </select>
    
    <select id="selectBaseAppByIdWl" parameterType="Long" resultMap="BaseAppResultWl">
        <include refid="selectBaseAppVo"/>
        where id = #{id}
    </select>
    <select id="selectOneDownloadWl" resultType="java.lang.String">
        select app_url from base_app_wl where status = 'release' order by release_time desc limit 1
    </select>
    <select id="selectCompareVersionWl" resultType="com.gzairports.common.basedata.domain.BaseApp">
        SELECT id,
               name,
               version_number,
               status,
               upload_time,
               release_time,
               remark,
               app_url
        FROM base_app_wl
        WHERE status = 'release'
        ORDER BY release_time DESC
        limit 1
    </select>

    <insert id="insertBaseAppWl" parameterType="BaseApp">
        insert into base_app_wl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="versionNumber != null">version_number,</if>
            <if test="status != null">status,</if>
            <if test="uploadTime != null">upload_time,</if>
            <if test="releaseTime != null">release_time,</if>
            <if test="remark != null">remark,</if>
            <if test="appUrl != null">app_url,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="versionNumber != null">#{versionNumber},</if>
            <if test="status != null">#{status},</if>
            <if test="uploadTime != null">#{uploadTime},</if>
            <if test="releaseTime != null">#{releaseTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="appUrl != null">#{appUrl},</if>
         </trim>
    </insert>

    <update id="updateBaseAppWl" parameterType="BaseApp">
        update base_app_wl
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="versionNumber != null">version_number = #{versionNumber},</if>
            <if test="status != null">status = #{status},</if>
            <if test="uploadTime != null">upload_time = #{uploadTime},</if>
            <if test="releaseTime != null">release_time = #{releaseTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="appUrl != null">app_url = #{appUrl},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBaseAppByIdWl" parameterType="Long">
        delete from base_app_wl where id = #{id}
    </delete>

    <delete id="deleteBaseAppByIdsWl" parameterType="String">
        delete from base_app_wl where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>