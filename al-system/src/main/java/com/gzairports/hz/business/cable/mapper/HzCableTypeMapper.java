package com.gzairports.hz.business.cable.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.hz.business.cable.domain.HzCableType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 电报类型Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
@Mapper
public interface HzCableTypeMapper extends BaseMapper<HzCableType>
{
    /**
     * 查询电报类型
     * 
     * @param id 电报类型主键
     * @return 电报类型
     */
    public HzCableType selectHzCableTypeById(Long id);

    /**
     * 查询电报类型列表
     * 
     * @param hzCableType 电报类型
     * @return 电报类型集合
     */
    public List<HzCableType> selectHzCableTypeList(HzCableType hzCableType);

    /**
     * 新增电报类型
     * 
     * @param hzCableType 电报类型
     * @return 结果
     */
    public int insertHzCableType(HzCableType hzCableType);

    /**
     * 修改电报类型
     * 
     * @param hzCableType 电报类型
     * @return 结果
     */
    public int updateHzCableType(HzCableType hzCableType);

    /**
     * 删除电报类型
     * 
     * @param id 电报类型主键
     * @return 结果
     */
    public int deleteHzCableTypeById(Long id);

    HzCableType selectTemplateByType(String type);
}
