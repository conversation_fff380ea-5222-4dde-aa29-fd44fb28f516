<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.reporter.mapper.HzReportSetFilterMapper">


    <select id="getFilter" resultType="com.gzairports.hz.business.reporter.domain.HzReportSetFilter">
        select id, set_id, field_name_cn, field_type, field_name, field_filter_type, field_enum_value, field_filter_fix, field_filter_value
        from hz_report_set_filter where set_id = #{setId}
    </select>
</mapper>