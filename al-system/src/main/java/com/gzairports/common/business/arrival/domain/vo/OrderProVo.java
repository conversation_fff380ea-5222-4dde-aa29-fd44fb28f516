package com.gzairports.common.business.arrival.domain.vo;

import com.gzairports.common.business.arrival.domain.HzArrItem;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 散客在线提货办单数据
 *
 * <AUTHOR>
 * @date 2024-08-05
 */
@Data
public class OrderProVo {

    /** 办单id */
    private Long pickUpId;

    /** 运单号 */
    private String waybillCode;

    /** 运单件数 */
    private Integer quantity;

    /** 运单重量 */
    private BigDecimal weight;

    /** 已提件数 */
    private Integer pickedQuantity;

    /** 已提重量 */
    private BigDecimal pickedWeight;

    /** 可提件数 */
    private Integer canPickQuantity;

    /** 可提重量 */
    private BigDecimal canPickWeight;

    /** 品名 */
    private String cargoName;

    /** 特货代码 */
    private String specialCaroCode1;

    /** 收货代理人 */
    private String consign;

    /** 流水号 */
    private String serialNo;

    /** 费用明细 */
    private List<HzArrItem> hzArrItems;

    /** 合计 */
    private BigDecimal totalCost;
}
