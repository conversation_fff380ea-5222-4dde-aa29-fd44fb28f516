package com.gzairports.common.business.arrival.domain;

import java.time.LocalTime;
import java.util.Date;


/**
 * 计算费用明细规则接口
 * <AUTHOR>
 */
public interface ItemDetail {

    /**
     * 份数
     * @return 结果
     */
    Integer getUnit();

    /**
     * 小件件数
     * @return 结果
     */
    Integer getSmallItem();

    /**
     * 大件件数
     * @return 结果
     */
    Integer getLargeItem();

    /**
     * 超大件件数
     * @return 结果
     */
    Integer getSuperLargeItem();

    /**
     * 分时段计费开始时间
     * @return 结果
     */
    LocalTime getStartTime();

    /**
     * 分时段计费结束时间
     * @return 结果
     */
    LocalTime getEndTime();

    /**
     * 保存天数
     * @return 结果
     */
    Double getDaysInStorage();

    /**
     * 冷库类型
     * @return 结果
     */
    String getColdStore();

    /**
     * 冷库开始时间
     * @return 结果
     */
    Date getStoreStartTime();

    /**
     * 冷库结束时间
     * @return 结果
     */
    Date getStoreEndTime();

    /**
     * 时间点
     * @return 结果
     */
    LocalTime getPointTime();


    Long getIrId();
}
