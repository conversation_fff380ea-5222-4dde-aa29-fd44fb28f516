package com.gzairports.web.controller.departure;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.basedata.service.IAirportCodeService;
import com.gzairports.common.basedata.service.ICarrierService;
import com.gzairports.common.business.departure.domain.Mawb;
import com.gzairports.common.business.departure.mapper.MawbMapper;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.domain.entity.SysDept;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.common.log.service.IWaybillLogService;
import com.gzairports.common.pdf.PdfPrintHelper;
import com.gzairports.common.securitySubmit.domain.AllSecurityWaybill;
import com.gzairports.common.securitySubmit.mapper.AllSecurityWaybillMapper;
import com.gzairports.common.system.service.ISysDeptService;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.ServletUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.common.utils.sign.Base64;
import com.gzairports.common.utils.spring.SpringUtils;
import com.gzairports.oss.domain.SysOss;
import com.gzairports.oss.service.impl.SysOssServiceImpl;
import com.gzairports.wl.departure.domain.MawbErrorRemark;
import com.gzairports.wl.departure.domain.MawbItem;
import com.gzairports.wl.departure.domain.query.FareQuery;
import com.gzairports.wl.departure.domain.query.HawbQuery;
import com.gzairports.wl.departure.domain.query.ItemQuery;
import com.gzairports.wl.departure.domain.vo.*;
import com.gzairports.wl.departure.service.IMawbService;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;


import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 主单制单Controller
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
@RestController
@RequestMapping("/dep/mawb")
public class MawbController extends BaseController {

    @Autowired
    private IMawbService mawbService;

    @Autowired
    private ICarrierService iCarrierService;

    @Autowired
    private IWaybillLogService waybillLogService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private IAirportCodeService airportCodeService;

    @Autowired
    private AllSecurityWaybillMapper allSecurityWaybillMapper;

    @Autowired
    private MawbMapper mawbMapper;

    /**
     * 运单新增
     */
    @PreAuthorize("@ss.hasPermi('dep:mawb:add')")
    @PostMapping("/add")
    @ApiOperation(value = "暂存与保存并发送")
    public AjaxResult add(@RequestBody MawbVo vo) throws Exception {
        HttpServletResponse response = ServletUtils.getResponse();
        //运单日志的新增
        WaybillLog waybillLog = waybillLogService.getWaybillLog(
                vo.getWaybillCode(), 1, SecurityUtils.getNickName(),
                vo.getWeight().toString(), vo.getQuantity().toString(), vo.getFlightNo1(),
                vo, null, 0, null, new Date(),
                "staging".equals(vo.getStatus()) ? "暂存" : "保存并发送", "DEP", null);
        try{
            if (vo.getChargeWeight().compareTo(vo.getWeight()) < 0){
                throw new CustomException("计费重量不能小于实际重量");
            }
            if(vo.getId() == null){
                vo.setWriteTime(new Date());
            }
            //12.24 主单制单时运单号不能重复
            Mawb waybillCode = mawbMapper.selectOne(new QueryWrapper<Mawb>()
                    .eq("waybill_code", vo.getWaybillCode())
                    .eq("type","DEP")
                    .eq("is_del",0));
            if(waybillCode!=null){
                vo.setVersion(waybillCode.getVersion());
                vo.setId(waybillCode.getId());
                if("staging".equals(vo.getStatus()) && !"staging".equals(waybillCode.getStatus())){
                    throw new CustomException("运单已保存,暂存失败");
                }
                vo.setPayStatus(waybillCode.getPayStatus());
                if("staging".equals(waybillCode.getStatus())){
                    vo.setStatus("been_sent");
                }else{
                    vo.setStatus(waybillCode.getStatus());
                }
                if(vo.getId()==null){
                    throw new CustomException("运单号重复,请重新录入");
                }
                if("INVALID".equals(waybillCode.getStatus())){
                    throw new CustomException("运单已作废,保存失败");
                }
                if(waybillCode.getPayStatus() != 0 && waybillCode.getPayStatus() != 14){
                    //已支付后的运单 件数重量计费重量货品代码都不能修改(加一个取消支付的状态判断)
                    if(!(waybillCode.getCargoCode().equals(vo.getCargoCode())
                    && waybillCode.getWeight().compareTo(vo.getWeight())==0
                    && Objects.equals(waybillCode.getQuantity(), vo.getQuantity())
                    && waybillCode.getChargeWeight().compareTo(vo.getChargeWeight())==0)
                    && waybillCode.getFlightNo1().equals(vo.getFlightNo1())
                    && waybillCode.getFlightDate1().equals(vo.getFlightDate1())){
                        throw new CustomException("运单已支付,保存失败");
                    }
                }
            }
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String substring = vo.getWaybillCode().substring(4);
            String waybillCodeAbb = substring.substring(0, 3) + "-" + substring.substring(3);
            vo.setWaybillCodeAbb(waybillCodeAbb);
            if (vo.getWriteTime() != null){
                String format = simpleDateFormat.format(vo.getWriteTime());
                vo.setWriteTimeStr(format);
            }
            if (StringUtils.isNotEmpty(vo.getDangerCode())){
                vo.setWxp("YES");
            }else if (StringUtils.isNotEmpty(vo.getSpecialCargoCode1())){
                vo.setTzhw("YES");
            }else {
                vo.setPthw("YES");
            }
            String desPort = airportCodeService.selectChineseName(vo.getDesPort());
            vo.setDesPortStr(desPort);
            vo.setSourcePortStr("贵阳");
            vo.setInsurance("无");
            vo.setDeclaredValue("无");
            vo.setGroundCost(BigDecimal.ZERO.setScale(2, RoundingMode.DOWN));
            if (!CollectionUtils.isEmpty(vo.getItems())){
                List<MawbItem> airRates = vo.getItems().stream().filter(e -> e.getRateType().equals(1)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(airRates)){
                    MawbItem item = airRates.get(0);
                    //金额保留两位小数
                    BigDecimal decimalAirCost = item.getCharging().setScale(2, RoundingMode.DOWN);
                    vo.setAirCost(decimalAirCost);
                    vo.setAirRate(item.getRate());
                }
                List<MawbItem> otherRates = vo.getItems().stream().filter(e -> e.getRateType().equals(0)).collect(Collectors.toList());
                StringBuilder builder = new StringBuilder();
                for (MawbItem otherRate : otherRates) {
                    builder.append(otherRate.getCostType()).append(": ").append(otherRate.getCharging().setScale(2, RoundingMode.DOWN)).append(" ");
                }
                vo.setOtherInfo(builder.toString());
                if (!CollectionUtils.isEmpty(otherRates)){
                    BigDecimal reduce = otherRates.stream().map(MawbItem::getCharging).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    vo.setOtherCost(reduce.setScale(2, RoundingMode.DOWN));
                }
            }
            if (vo.getAirCost() == null){
                vo.setAirRate(new BigDecimal(0));
            }
            if (vo.getOtherCost() == null){
                vo.setOtherCost(new BigDecimal(0).setScale(2, RoundingMode.DOWN));
            }
            BigDecimal airCost = vo.getAirCost() == null ? new BigDecimal(0) : vo.getAirCost();
            BigDecimal otherCost = vo.getOtherCost() == null ? new BigDecimal(0) : vo.getOtherCost();
            vo.setTotalCost(airCost.add(otherCost).setScale(2, RoundingMode.DOWN));
            if (vo.getPrintAmount() != null){
                if(vo.getPrintAmount() == 0){
                    vo.setAirRate(new BigDecimal(0).setScale(2, RoundingMode.DOWN));
                    vo.setOtherCost(new BigDecimal(0).setScale(2, RoundingMode.DOWN));
                    vo.setTotalCost(new BigDecimal(0).setScale(2, RoundingMode.DOWN));
                    vo.setAirCost(new BigDecimal(0).setScale(2, RoundingMode.DOWN));
                }
            }
            if (vo.getFlightDate1() != null){
                vo.setFlightDate1Str(simpleDateFormat.format(vo.getFlightDate1()));
            }
            SysDept dept = deptService.selectDeptById(SecurityUtils.getHighParentId());
            if (StringUtils.isNotEmpty(dept.getSealUrl())){
                byte[] bytes = downloadFileFromUrl(dept.getSealUrl());
                String sealImage = Base64.encode(bytes);
                vo.setSealUrl(sealImage);
                vo.setSealUrl2(sealImage);
            }
            vo.setSizeVolume((vo.getSize() == null ? "" : vo.getSize()) + " " + (vo.getVolume() == null ? "" : vo.getVolume() + "m³"));
            vo.setChargeWeight(vo.getChargeWeight().setScale(0, RoundingMode.DOWN));
            vo.setShipperStr(dept.getDeptName());
            String desPort1 = vo.getDesPort();
            //将目的站从英文转化为中文
            String desPortChinese = airportCodeService.selectChineseName(desPort1);
            if(StringUtils.isNotEmpty(desPortChinese)){
                vo.setDesPort(desPortChinese);
            }
            String securityUrl;
            AllSecurityWaybill allSecurityWaybill = allSecurityWaybillMapper.selectOne(new QueryWrapper<AllSecurityWaybill>().eq("waybill_code", vo.getWaybillCode()));
            if(allSecurityWaybill != null){
                securityUrl = allSecurityWaybill.getSecurityUrl();
            }else{
                securityUrl = getPdfUrl(vo, "electronic/security.pdf");
            }
            vo.setSecurityUrl(securityUrl);
            vo.setDesPort(desPort1);

            if (StringUtils.isNotEmpty(vo.getCarrier1())){
                String logoUrl = iCarrierService.selectLogoByCode(vo.getCarrier1());
                if (StringUtils.isNotEmpty(logoUrl)){
                    byte[] bytes = downloadFileFromUrl(logoUrl);
                    String logoImage = Base64.encode(bytes);
                    vo.setAirLogo(logoImage);
                }
            }
            vo.setPayMethod("现金");
            vo.setAgentSign(SecurityUtils.getNickName());
            //如果是补货单 打印要在品名后面加上补货两字 但是不能影响新增
            String cargoName = vo.getCargoName();
            if(vo.getSwitchBill() == 2){
                vo.setCargoName(vo.getCargoName() + "(补货)");
            }
            if(vo.getSwitchBill() == 3){
                vo.setCargoName(vo.getCargoName() + "(补重)");
            }
            String mawbUrl = getPdfUrl(vo, "electronic/mawb.pdf");
            if(vo.getSwitchBill() == 2 || vo.getSwitchBill() == 3){
                vo.setCargoName(cargoName);
            }
            vo.setPdfUrl(mawbUrl);
            vo.setAirLogo(null);

            String add = mawbService.add(vo);
            waybillLog.setJsonResult(waybillLogService.getJson(
                 "msg:" + "操作成功" +  "," +
                    "code:" + response.getStatus() + "," +
                    "data:" + add));
            return AjaxResult.success(add);
        }catch(Exception e){
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" +  "," +
                       "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            return AjaxResult.error(e.getMessage());
        }finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }

    private String getPdfUrl(@RequestBody MawbVo vo, String s) throws Exception {
        ClassPathResource securityUrl = new ClassPathResource(s);
        if (securityUrl.exists()) {
            String path = securityUrl.getPath();
            byte[] pdfDataFromTemplate = PdfPrintHelper.getPdfDataFromTemplate(vo, path);
            // 使用StandardMultipartHttpServletRequest来创建MultipartFile
            DiskFileItem fileItem = new DiskFileItem(vo.getWaybillCode() + ".pdf", "application/pdf", true, vo.getWaybillCode() + ".pdf", -1, null);

            // 将 InputStream 写入 DiskFileItem
            fileItem.getOutputStream().write(pdfDataFromTemplate);
            fileItem.getOutputStream().close();

            // 使用 FileItem 构建 MultipartFile
            MultipartFile multipartFile = new CommonsMultipartFile(fileItem);
            SysOss upload = SpringUtils.getBean(SysOssServiceImpl.class).upload(multipartFile);
            return upload.getUrl();
        }
        return null;
    }




    private byte[] downloadFileFromUrl(String urlStr) throws Exception {
        // 创建一个信任所有证书的信任管理器
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }
                    @Override
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                    @Override
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
        };

        // 安装信任所有证书的信任管理器
        SSLContext sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, trustAllCerts, new java.security.SecureRandom());

        URL url = new URL(urlStr);
        HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
        connection.setSSLSocketFactory(sslContext.getSocketFactory());

        // 设置自定义 HostnameVerifier（仅限测试环境）
        connection.setHostnameVerifier((hostname, session) -> true);

        connection.setRequestMethod("GET");
        connection.setConnectTimeout(5000);
        connection.setReadTimeout(5000);

        int responseCode = connection.getResponseCode();
        if (responseCode != 200) {
            throw new RuntimeException("Failed to connect, HTTP error code : " + responseCode);
        }

        try (InputStream inputStream = connection.getInputStream();
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            return baos.toByteArray();
        } catch (IOException e) {
            throw new RuntimeException("IO Exception occurred during file download", e);
        }
    }

    /**
     * 运单校验
     */
    @PreAuthorize("@ss.hasPermi('dep:mawb:check')")
    @GetMapping("/check/{waybillCode}")
    @ApiOperation(value = "运单校验")
    public AjaxResult check(@PathVariable("waybillCode") String waybillCode)
    {
        return AjaxResult.success(mawbService.check(waybillCode));
    }

    /**
     * 根据代理人信息查询账号
     */
    @PreAuthorize("@ss.hasPermi('dep:mawb:agent')")
    @GetMapping("/agent")
    @ApiOperation(value = "根据代理人信息查询账号")
    public AjaxResult agent(String agentCom, String agentCode)
    {
        return AjaxResult.success(mawbService.agent(agentCom,agentCode));
    }

    /**
     * 自动运价
     */
    @PreAuthorize("@ss.hasPermi('dep:mawb:fare')")
    @PostMapping("/fare")
    @ApiOperation(value = "自动运价")
    public AjaxResult fare(@RequestBody FareQuery query)
    {
        return AjaxResult.success(mawbService.fare(query));
    }

    /**
     * 添加费用项
     */
    @PreAuthorize("@ss.hasPermi('dep:mawb:addFees')")
    @PostMapping("/addFees")
    @ApiOperation(value = "添加费用项")
    public AjaxResult addFees(@RequestBody ItemQuery query)
    {
        return AjaxResult.success(mawbService.addFees(query));
    }

    /**
     * 修改费用项
     */
    @PreAuthorize("@ss.hasPermi('dep:mawb:editFee')")
    @PostMapping("/editFee")
    @ApiOperation(value = "修改费用项")
    public AjaxResult editFee(@RequestBody MawbItem item)
    {
        return AjaxResult.success(mawbService.editFee(item));
    }

    /**
     * 删除费用项
     */
    @PreAuthorize("@ss.hasPermi('dep:mawb:delFee')")
    @PostMapping("/delFee")
    @ApiOperation(value = "删除费用项")
    public AjaxResult delFee(@RequestBody MawbItem item)
    {
        return AjaxResult.success(mawbService.delFee(item));
    }

    /**
     * 运单作废
     */
    @PreAuthorize("@ss.hasPermi('dep:mawb:invalid')")
    @GetMapping("/invalid/{waybillCode}")
    @ApiOperation(value = "运单作废")
    public AjaxResult invalid(@PathVariable("waybillCode") String waybillCode)
    {
        return toAjax(mawbService.invalid(waybillCode));
    }

    /**
     * 运单取消作废
     */
    @PreAuthorize("@ss.hasPermi('dep:mawb:cancelVoid')")
    @GetMapping("/cancelVoid/{waybillCode}")
    @ApiOperation(value = "运单取消作废")
    public AjaxResult cancelVoid(@PathVariable("waybillCode") String waybillCode)
    {
        return toAjax(mawbService.cancelVoid(waybillCode));
    }

    /**
     * 根据运单号查询历史运单
     */
    @PreAuthorize("@ss.hasPermi('dep:mawb:getInfo')")
    @GetMapping("/getInfo/{waybillCode}")
    @ApiOperation(value = "运单详情")
    public AjaxResult getInfo(@PathVariable("waybillCode") String waybillCode)
    {
        return AjaxResult.success(mawbService.getInfo(waybillCode));
    }

    /**
     * 编辑运单
     */
    @PreAuthorize("@ss.hasPermi('dep:mawb:edit')")
    @GetMapping("/edit/{waybillCode}")
    @ApiOperation(value = "编辑运单")
    public void edit(@PathVariable("waybillCode") String waybillCode)
    {
        mawbService.edit(waybillCode);
    }

    /**
     * 更改拼单
     */
    @PreAuthorize("@ss.hasPermi('dep:mawb:alter')")
    @GetMapping("/alter")
    @ApiOperation(value = "更改拼单")
    public AjaxResult alter(String waybillCode, String masterWaybillCode)
    {
        return toAjax(mawbService.alter(waybillCode,masterWaybillCode));
    }

    /**
     * 增加备注
     */
    @PreAuthorize("@ss.hasPermi('dep:mawb:addRemark')")
    @PostMapping("/addRemark")
    @ApiOperation(value = "增加备注")
    public AjaxResult addRemark(@RequestBody MawbErrorRemark remark)
    {
        return toAjax(mawbService.addRemark(remark));
    }

    /**
     * 查询备注
     */
    @PreAuthorize("@ss.hasPermi('dep:mawb:remarkList')")
    @GetMapping("/remarkList/{waybillCode}")
    @ApiOperation(value = "查询备注")
    public AjaxResult remarkList(@PathVariable("waybillCode") String waybillCode)
    {
        return AjaxResult.success(mawbService.remarkList(waybillCode));
    }

    /**
     * 国内主单查询
     */
    @PreAuthorize("@ss.hasPermi('dep:mawb:query')")
    @PostMapping("/query")
    @ApiOperation(value = "国内主单查询")
    public AjaxResult query(@RequestBody HawbQuery query) throws IOException {
        HawbQueryVo hawbQueryVo = mawbService.queryList(query);
        return AjaxResult.success(hawbQueryVo);
    }

    /**
     * 告知
     * */
    @GetMapping("/notify/{id}")
    @ApiOperation(value = "告知")
    public AjaxResult notifyWaybillById(@PathVariable("id") Long id){
        return AjaxResult.success(mawbService.notifyWaybillById(id));
    }

    /**
     * 导出查询的国内主单
     */
    @PreAuthorize("@ss.hasPermi('dep:mawb:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出国内主单")
    public void export(HttpServletResponse response, HawbQuery query)
    {
        SysDept dept = deptService.selectDeptById(SecurityUtils.getHighParentId());
        List<MawbQueryVo> mawbs = mawbService.exportList(query);
        String startTime = "";
        String endTime = "";
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (query.getStartTime() != null){
            startTime = simpleDateFormat.format(query.getStartTime());
        }
        if (query.getEndTime() != null){
            endTime = simpleDateFormat.format(query.getEndTime());
        }
        ExcelUtil<MawbQueryVo> util = new ExcelUtil<MawbQueryVo>(MawbQueryVo.class);
        util.exportExcel(response, mawbs, "国内主单",SecurityUtils.getNickName(), dept.getDeptName(), startTime, endTime);
    }

    /**
     * 打印主单标签
     */
    @PreAuthorize("@ss.hasPermi('dep:mawb:print')")
    @GetMapping("/print/{waybillCode}")
    @ApiOperation(value = "打印主单标签")
    public AjaxResult print(@PathVariable("waybillCode") String waybillCode)
    {
        return AjaxResult.success(mawbService.printMawb(waybillCode));
    }

    /**
     * 查询可补货数据
     */
    @PreAuthorize("@ss.hasPermi('dep:mawb:replenish')")
    @GetMapping("/replenish/{waybillCode}")
    @ApiOperation(value = "查询可补货数据")
    public AjaxResult replenish(@PathVariable("waybillCode") String waybillCode)
    {
        return AjaxResult.success(mawbService.replenish(waybillCode));
    }

    /**
     * 主单打印
     */
    @PreAuthorize("@ss.hasPermi('dep:mawb:printMawbData')")
    @GetMapping(value = "/printMawbData/{id}")
    @ApiOperation(value = "主单打印")
    public void printMawbData(HttpServletResponse response, @PathVariable("id") Long id) throws Exception
    {
        mawbService.printMawbData(id,response);
    }

    /**
     * 主单标签打印
     */
    @PreAuthorize("@ss.hasPermi('dep:mawb:printLabel')")
    @GetMapping(value = "/printLabel/{waybillCode}/{quantity}/{isWeight}")
    @ApiOperation(value = "主单标签打印")
    public void printLabel(HttpServletResponse response, @PathVariable("waybillCode") String waybillCode,
                           @PathVariable("quantity") Integer quantity, @PathVariable("isWeight") Integer isWeight) throws Exception
    {
        mawbService.printLabel(waybillCode,response,quantity,isWeight);
    }

    /**
     * 主单标签打印
     */
    @PreAuthorize("@ss.hasPermi('dep:mawb:printCurrencyLabel')")
    @GetMapping(value = "/printCurrencyLabel/{waybillCode}/{quantity}/{isWeight}")
    @ApiOperation(value = "通用标签打印")
    public void printCurrencyLabel(HttpServletResponse response, @PathVariable("waybillCode") String waybillCode,
                                   @PathVariable("quantity") Integer quantity, @PathVariable("isWeight") Integer isWeight) throws Exception
    {
        mawbService.printCurrencyLabel(waybillCode,response,quantity,isWeight);
    }

    /**
     * 主单标签打印
     */
    @PreAuthorize("@ss.hasPermi('dep:mawb:printHTLDLabel')")
    @GetMapping(value = "/printHTLDLabel/{waybillCode}/{quantity}/{isWeight}")
    @ApiOperation(value = "航投立达标签打印")
    public void printHTLDLabel(HttpServletResponse response, @PathVariable("waybillCode") String waybillCode,
                               @PathVariable("quantity") Integer quantity, @PathVariable("isWeight") Integer isWeight) throws Exception
    {
        mawbService.printHTLDLabel(waybillCode,response,quantity,isWeight);
    }

    /**
     * 打印安检申报单
     */
    @PreAuthorize("@ss.hasPermi('dep:mawb:printSecurityCheck')")
    @GetMapping(value = "/printSecurityCheck/{waybillCode}")
    @ApiOperation(value = "打印安检申报单")
    public void printSecurityCheck(HttpServletResponse response, @PathVariable("waybillCode") String waybillCode) throws Exception
    {
        MawbVo vo = mawbService.getInfo(waybillCode);
        mawbService.printSecurityCheck(vo.getId(),response);
    }

    /**
     * 上传随附文件
     */
    @PreAuthorize("@ss.hasPermi('dep:mawb:uploadTransportFile')")
    @PostMapping(value = "/uploadTransportFile")
    @ApiOperation(value = "上传随附文件")
    public AjaxResult uploadTransportFile(@RequestBody TransportFileVo vo)
    {
        if (vo.getId() == null){
            return warn("请选择运单");
        }
        return toAjax(mawbService.uploadTransportFile(vo));
    }

    /**
     * 查询随附文件
     * */
    @GetMapping(value = "/selectTransportFile/{id}")
    @ApiOperation(value = "查询随附文件")
    public AjaxResult selectTransportFile(@PathVariable("id") String waybillId)
    {
        if (waybillId == null){
            return warn("请选择运单");
        }
        return AjaxResult.success(mawbService.selectTransportFile(waybillId));
    }

    /**
     * 根据主单id查询交接单id
     * */
    @GetMapping(value = "/selectTransferIdByMawbId/{id}")
    @ApiOperation(value = "根据主单id查询交接单id")
    public AjaxResult selectTransferIdByMawbId(@PathVariable("id") String waybillId)
    {
        return AjaxResult.success(mawbService.selectTransferIdByMawbId(waybillId));
    }

    /**
     * 更改运单号
     */
//    @PreAuthorize("@ss.hasPermi('dep:mawb:editWaybillCode')")
    @PostMapping("/editWaybillCode")
    @ApiOperation(value = "更改运单号")
    public AjaxResult editWaybillCode(@RequestBody EditWaybillCode editWaybillCode)
    {
        return toAjax(mawbService.editWaybillCode(editWaybillCode));
    }

    /**
     * 添加运价条目
     */
//    @PreAuthorize("@ss.hasPermi('dep:mawb:addItems')")
    @PostMapping("/addItems")
    @ApiOperation(value = "添加运价条目")
    public AjaxResult addItems(@RequestBody ItemQuery query)
    {
        return AjaxResult.success(mawbService.addItems(query));
    }

    /**
     * 取消支付
     */
    @PreAuthorize("@ss.hasPermi('dep:collectWaybill:cancelPay')")
    @GetMapping("/cancelPay/{waybillId}")
    @ApiOperation(value = "取消支付")
    public AjaxResult cancelPay(@PathVariable("waybillId") Long waybillId)
    {
        return toAjax(mawbService.cancelPay(waybillId));
    }

}
