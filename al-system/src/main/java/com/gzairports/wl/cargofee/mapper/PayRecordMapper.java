package com.gzairports.wl.cargofee.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.wl.cargofee.domain.PayRecord;
import com.gzairports.wl.cargofee.domain.query.PayRecordQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 支付流水记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@Mapper
public interface PayRecordMapper extends BaseMapper<PayRecord> {

    /**
     * 查询支付流水记录
     * @param query 查询条件
     * @return 结果
     */
    List<PayRecord> selectListByQuery(PayRecordQuery query);

    List<PayRecord> selectListByQueryForBalance(PayRecordQuery query);
}
