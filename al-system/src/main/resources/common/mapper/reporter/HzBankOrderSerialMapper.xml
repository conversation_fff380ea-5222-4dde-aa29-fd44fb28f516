<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.bank.mapper.HzBankOrderSerialMapper">
    <select id="selectListByOrderWaybillId" resultType="com.gzairports.common.bank.domain.HzBankOrderSerial">
        select id, order_waybill_id, serial_no, create_time
        from hz_bank_order_serial
        where order_waybill_id = #{orderWaybillId}
    </select>
    <select id="selectSerialNoPay" resultType="java.lang.String">
        select serial_no
        from hz_bank_order_serial
        where order_waybill_id = #{id} and type = 1
        order by create_time desc
        limit 1
    </select>
    <select id="selectSerialNoRefund" resultType="java.lang.String">
        select serial_no
        from hz_bank_order_serial
        where order_waybill_id = #{id} and type = 3
        order by create_time desc
        limit 1
    </select>
</mapper>