package com.gzairports.hz.business.departure.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 退货管理列表返回数据
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
public class ExitCargoVo {

    /** 退货主键id */
    private Long id;

    /** 运单id */
    private Long waybillId;

    /** 航班日期 */
    private Date execDate;

    /** 航班号 */
    private String flightNo1;

    /** 运单号 */
    private String waybillCode;

    /** 开单时间 */
    private Date writeTime;

    /** 运单状态 */
    private String waybillStatus;

    /** 申请状态 */
    private String exitStatus;

    /** 发货代理人 */
    private String agentCode;

    /** 入库件数 */
    private Integer collectQuantity;

    /** 入库重量 */
    private BigDecimal collectWeight;

    /** 已出港件数 */
    private Integer depQuantity;

    /** 已出港重量 */
    private BigDecimal depWeight;

    /** 退货件数 */
    private Integer exitQuantity;

    /** 退货重量 */
    private BigDecimal exitWeight;

    /** 退货交换备注 */
    private String exitTransferRemark;

    /** 接收人签字图片地址 */
    private String receiveUrl;

    /** 备注 */
    private String remark;
}
