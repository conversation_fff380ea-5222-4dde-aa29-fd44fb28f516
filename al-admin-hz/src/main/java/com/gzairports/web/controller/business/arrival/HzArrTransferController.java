package com.gzairports.web.controller.business.arrival;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.hz.business.arrival.domain.HzArrTransfer;
import com.gzairports.hz.business.arrival.domain.query.HzArrTransferQuery;
import com.gzairports.hz.business.arrival.service.IHzArrTransferService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 进港交接Controller
 *
 * <AUTHOR>
 * @date 2024-07-12
 */
@RestController
@RequestMapping("/arr/transfer")
@Api(tags = "进港交接")
public class HzArrTransferController extends BaseController {

    @Autowired
    private IHzArrTransferService transferService;


    /**
     * 查询进港交接列表
     */
//    @PreAuthorize("@ss.hasPermi('dep:transfer:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询进港交接列表")
    public TableDataInfo list(HzArrTransferQuery query){
        startPage();
        List<HzArrTransfer> list = transferService.selectList(query);
        return getDataTable(list);
    }

    /**
     * 导出进港交接列表
     */
    @PreAuthorize("@ss.hasPermi('dep:transfer:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出进港交接列表")
    public void export(HttpServletResponse response, HzArrTransferQuery query)
    {
        List<HzArrTransfer> list = transferService.selectList(query);
        ExcelUtil<HzArrTransfer> util = new ExcelUtil<HzArrTransfer>(HzArrTransfer.class);
        util.exportExcel(response, list, "进港交接");
    }

    /**
     * 进港交接详情
     */
    @PreAuthorize("@ss.hasPermi('dep:transfer:getInfo')")
    @GetMapping("/getInfo/{id}")
    @ApiOperation(value = "进港交接详情")
    public AjaxResult getInfo(@PathVariable("id") Long id){
        return AjaxResult.success(transferService.getInfo(id));
    }

    /**
     * 新增交接
     */
    @PreAuthorize("@ss.hasPermi('dep:transfer:add')")
    @PostMapping("/add")
    @ApiOperation(value = "新增交接")
    public AjaxResult add(@RequestBody HzArrTransfer transfer){
        return toAjax(transferService.add(transfer));
    }
}
