<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.CargoIncompatibleMapper">
    <select id="selectCargoIncompatibleList" resultType="com.gzairports.common.basedata.domain.BaseCargoIncompatible">
        select id, air_company, special_cargo_one, special_cargo_two, remark from base_cargo_incompatible
        <where>
        and is_del = 0
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="airCompany != null  and airCompany != ''"> and air_company like concat('%',#{airCompany},'%')</if>
            <if test="specialCargoOne != null and specialCargoOne != ''"> and special_cargo_one like concat('%',#{specialCargoOne},'%')</if>
            <if test="specialCargoTwo != null and specialCargoTwo != ''"> and special_cargo_two like concat('%',#{specialCargoTwo},'%')</if>
        </where>
    </select>
</mapper>
