package com.gzairports.hz.business.abnormal.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.hz.business.abnormal.domain.HzNoLabel;
import com.gzairports.hz.business.abnormal.domain.vo.HzNoLabelVo;

import java.util.List;


/**
 * 无标签货物Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-26
 */
public interface IHzNoLabelService  extends IService<HzNoLabel>
{
    /**
     * 查询无标签货物
     * 
     * @param id 无标签货物ID
     * @return 无标签货物
     */
    HzNoLabel selectHzNoLabelById(Long id);

    /**
     * 查询无标签货物列表
     * 
     * @param hzNoLabel 无标签货物
     * @return 无标签货物集合
     */
    List<HzNoLabel> selectHzNoLabelList(HzNoLabel hzNoLabel);

    /**
     * 新增无标签货物
     * 
     * @param hzNoLabel 无标签货物
     * @return 结果
     */
    int insertHzNoLabel(HzNoLabel hzNoLabel);

    /**
     * 修改无标签货物
     * 
     * @param hzNoLabel 无标签货物
     * @return 结果
     */
    int updateHzNoLabel(HzNoLabel hzNoLabel);

    /**
     * 处理无标签货物
     * @param vo 处理数据
     * @return 结果
     */
    int handleHzNoLabel(HzNoLabelVo vo);
}
