package com.gzairports.hz.business.cable.domain.query;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 生成电报参数
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@Data
public class GenerateCableQuery {

    private String type;

    /** 发报地址 */
    private String cableAddress;

    /** 接收地址 */
    private String receiveAddress;

    /** 类型以及接收地址 */
    private List<TypeAndAddressQuery> typeAndAddress;

    private List<Long> ids;
}
