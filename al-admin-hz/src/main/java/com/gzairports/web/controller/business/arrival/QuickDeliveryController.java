package com.gzairports.web.controller.business.arrival;

import com.gzairports.common.annotation.Log;
import com.gzairports.common.basedata.domain.BaseAgent;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.hz.business.arrival.domain.HzArrQuickDelivery;
import com.gzairports.hz.business.arrival.domain.query.QuickDeliveryQuery;
import com.gzairports.hz.business.arrival.service.IQuickDeliveryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 快速交互Controller
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
@RestController
@RequestMapping("/arr/quick")
@Api(tags = "快速交付")
public class QuickDeliveryController extends BaseController {
    
    @Autowired
    private IQuickDeliveryService quickDeliveryService;

    /**
     * 查询快速交付列表
     */
//    @PreAuthorize("@ss.hasPermi('arr:quick:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询快速交付列表")
    public TableDataInfo list(QuickDeliveryQuery query)
    {
        startPage();
        List<HzArrQuickDelivery> list = quickDeliveryService.selectList(query);
        return getDataTable(list);
    }

    /**
     * 导出快速交付列表
     */
    @PreAuthorize("@ss.hasPermi('arr:quick:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出快速交付列表")
    public void export(HttpServletResponse response, QuickDeliveryQuery query)
    {
        List<HzArrQuickDelivery> list = quickDeliveryService.selectList(query);
        ExcelUtil<HzArrQuickDelivery> util = new ExcelUtil<HzArrQuickDelivery>(HzArrQuickDelivery.class);
        util.exportExcel(response, list, "快速交付数据");
    }

    /**
     * 获取快速交付详细信息
     */
    @PreAuthorize("@ss.hasPermi('arr:quick:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取快速交付详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(quickDeliveryService.getInfo(id));
    }

    /**
     * 新增快速交付
     */
    @PreAuthorize("@ss.hasPermi('arr:quick:add')")
    @PostMapping("/add")
    @ApiOperation(value = "新增快速交付")
    public AjaxResult add(@RequestBody HzArrQuickDelivery delivery)
    {
        return toAjax(quickDeliveryService.add(delivery));
    }

    /**
     * 修改快速交付
     */
    @PreAuthorize("@ss.hasPermi('arr:quick:edit')")
    @PostMapping("/edit")
    @ApiOperation(value = "修改快速交付")
    public AjaxResult edit(@RequestBody HzArrQuickDelivery delivery)
    {
        return toAjax(quickDeliveryService.edit(delivery));
    }
}
