package com.gzairports.sms.mapper.log;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.sms.domain.dto.MessageConditionDto;
import com.gzairports.sms.domain.SmsSendLogPo;
import com.gzairports.sms.domain.dto.SmsSendQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: Ring
 * @Date: 2020/2/26
 * @Description:
 */
@Mapper
public interface SmsSendLogMapper extends BaseMapper<SmsSendLogPo> {

    /**
     * 查询短信发送日志
     *
     * @param condition
     * @return
     */
    List<SmsSendLogPo> selectSmsSendLog(MessageConditionDto condition);

    /**
     * 插入短信发送日志
     * @param smsSendLogPo
     * @return
     */
    int insertSmsSendLog(SmsSendLogPo smsSendLogPo);

    /**
     * 查询短信发送历史记录
     *
     * @param smsSendLogId 短信发送历史记录ID
     * @return 短信发送历史记录
     */
    SmsSendLogPo selectSmsSendLogById(Long smsSendLogId);

    /**
     * 查询短信发送历史记录列表
     *
     * @param query 短信发送历史记录
     * @return 短信发送历史记录集合
     */
    List<SmsSendLogPo> selectSmsSendLogList(SmsSendQuery query);

    /**
     * 根据四位运单号查询运单
     * @param code 运单后四位
     * @return 运单集合
     */
    List<String> getCode(String code);
}
