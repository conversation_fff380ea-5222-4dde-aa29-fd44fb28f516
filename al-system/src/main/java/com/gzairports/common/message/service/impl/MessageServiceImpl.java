package com.gzairports.common.message.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.core.domain.entity.SysUser;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.message.domain.Message;
import com.gzairports.common.message.domain.query.MessageQuery;
import com.gzairports.common.message.mapper.MessageMapper;
import com.gzairports.common.message.service.IMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 消息管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@Service
public class MessageServiceImpl extends ServiceImpl<MessageMapper, Message> implements IMessageService {

    @Autowired
    private MessageMapper messageMapper;

    /**
     * 获取消息详情
     * @param id 消息id
     * @return 详情
     */
    @Override
    public Message getInfo(Long id) {
        return messageMapper.selectById(id);
    }

    /**
     * 查询消息列表
     * @param query 查询参数
     * @return 列表
     */
    @Override
    public List<Message> selectList(MessageQuery query) {
        Long[] postIds = SecurityUtils.getLoginUser().getUser().getPostIds();
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<Message> messageList = messageMapper.selectListByQuery(query);
        if (CollectionUtils.isEmpty(messageList)){
            return messageList;
        }
        if (user.isAdmin()){
            return messageList;
        }
        return messageList.stream().filter(e -> e.getHandleName().equals(SecurityUtils.getUsername()) ||
                Arrays.stream(postIds).allMatch(n -> n.equals(e.getPostId()))).collect(Collectors.toList());
    }
}
