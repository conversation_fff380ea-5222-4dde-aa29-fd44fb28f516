package com.gzairports.wl.cargofee.domain.query;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 运单费用明细查询参数
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@Data
public class WaybillFeeQuery {

    /** 预授权时间 */
    private Date startPayTime;

    /** 预授权时间 */
    private Date endPayTime;

    /** 结算时间 */
    private Date startSettleTime;

    /** 结算时间 */
    private Date endSettleTime;

    /** 结算时间 */
    private String waybillCode;

    /** 结算时间 */
    private String serialNo;

    /** 交易状态 */
    private Integer status;

    /** 所属单位 */
    private Long deptId;

    private List<String> deptIds;
}
