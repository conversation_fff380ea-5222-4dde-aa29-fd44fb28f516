package com.gzairports.hz.business.arrival.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.gzairports.common.basedata.domain.BaseCargoCategory;
import com.gzairports.common.basedata.domain.BaseCarrier;
import com.gzairports.common.basedata.mapper.CargoCategoryMapper;
import com.gzairports.common.basedata.mapper.CarrierMapper;
import com.gzairports.common.business.arrival.domain.AllPickUpOut;
import com.gzairports.common.business.arrival.domain.HzArrItem;
import com.gzairports.common.business.arrival.domain.HzArrTally;
import com.gzairports.common.business.arrival.domain.PickUpWaybill;
import com.gzairports.common.business.arrival.mapper.*;
import com.gzairports.common.business.departure.domain.FlightInfo;
import com.gzairports.common.business.wrong.domain.Wrong;
import com.gzairports.common.business.wrong.mapper.WrongMapper;
import com.gzairports.common.config.AddressMsgVO;
import com.gzairports.common.constant.HttpStatus;
import com.gzairports.common.core.domain.entity.SysDept;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.common.log.service.IWaybillLogService;
import com.gzairports.common.message.domain.Message;
import com.gzairports.common.message.domain.vo.SocketMessageVo;
import com.gzairports.common.message.mapper.MessageMapper;
import com.gzairports.common.system.mapper.SysConfigMapper;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.SerialNumberGenerator;
import com.gzairports.common.utils.ServletUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.hz.business.arrival.domain.HzArrRecordOrder;
import com.gzairports.hz.business.arrival.domain.query.FlightFileQuery;
import com.gzairports.hz.business.arrival.domain.query.TallyManifestQuery;
import com.gzairports.hz.business.arrival.domain.vo.*;
import com.gzairports.hz.business.arrival.mapper.HzArrRecordOrderMapper;
import com.gzairports.hz.business.arrival.service.IFlightFileService;
import com.gzairports.hz.business.cable.domain.HzCable;
import com.gzairports.hz.business.cable.domain.HzCableAddress;
import com.gzairports.hz.business.cable.domain.vo.*;
import com.gzairports.hz.business.cable.mapper.HzCableAddressMapper;
import com.gzairports.hz.business.cable.mapper.HzCableMapper;
import com.gzairports.hz.business.cable.service.impl.HttpServiceImpl;
import com.gzairports.hz.business.departure.domain.AirWaybill;
import com.gzairports.hz.business.departure.domain.FlightLoad;
import com.gzairports.hz.business.departure.mapper.AllAirWaybillMapper;
import com.gzairports.hz.business.departure.mapper.FlightLoadMapper;
import com.gzairports.hz.business.departure.rabbitmq.WaybillMessageProducer;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Marker;
import org.slf4j.MarkerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 航班文件Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
@Slf4j
@Service
public class FlightFileServiceImpl implements IFlightFileService {


    @Autowired
    private AllAirWaybillMapper airWaybillMapper;

    @Autowired
    private CargoCategoryMapper cargoCategoryMapper;

    @Autowired
    private HzArrTallyMapper hzArrTallyMapper;

    @Autowired
    private PickUpWaybillMapper pickUpWaybillMapper;

    @Autowired
    private AllPickUpOutMapper pickUpOutMapper;

    @Autowired
    private HzArrRecordOrderMapper recordOrderMapper;

    @Autowired
    private FlightLoadMapper flightLoadMapper;

    @Autowired
    private FlightInfoMapper flightInfoMapper;

    @Autowired
    private WrongMapper wrongMapper;

    @Autowired
    private HzArrItemMapper itemMapper;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    @Autowired
    private IWaybillLogService waybillLogService;

    @Autowired
    private CarrierMapper carrierMapper;

    @Autowired
    private WaybillMessageProducer waybillMessageProducer;

    @Autowired
    private MessageMapper messageMapper;

    @Autowired
    private SysConfigMapper sysConfigMapper;

    @Autowired
    private HzCableMapper hzCableMapper;

    @Autowired
    private HzCableAddressMapper cableAddressMapper;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private HttpServiceImpl httpService;

    @Value("${hzCable.account}")
    private String account;

    @Value("${hzCable.loginUrl}")
    private String loginUrl;

    @Value("${hzCable.getMsg}")
    private String getMsg;

    @Value("${hzCable.FTPAddress}")
    private String ftpAddress;

    private static final Marker CABLE_ERROR_MARKER  = MarkerFactory.getMarker("CABLE-ERROR");

    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMdd");

    /**
     * 进港运单录入
     * @param vo 进港运单数据
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int enter(EnterWaybillVo vo) {
        vo.setConsign(StrUtil.isNotBlank(vo.getConsign()) ? vo.getConsign().trim() : null);
        vo.setAgentCode(StrUtil.isNotBlank(vo.getAgentCode()) ? vo.getAgentCode().trim() : null);
        vo.setShipper(StrUtil.isNotBlank(vo.getShipper()) ? vo.getShipper().trim() : null);
        String waybillCodeHandled = StrUtil.cleanBlank(vo.getWaybillCode());
        vo.setWaybillCode(waybillCodeHandled);
        if ("AWBA".equals(vo.getType()) && waybillCodeHandled.length() != (4 + 3 + 8)) {
            throw new RuntimeException("请输入正确的运单号");
        }
        if (vo.getLegId() == null){
            throw new CustomException("无航段信息");
        }
        FlightInfo flight = flightInfoMapper.selectFlightNo(vo.getLegId());
        List<SysDept> deptList = sysDeptMapper.selectList();
        AirWaybill oldAirWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                .eq("waybill_code", vo.getWaybillCode())
                .eq("type", "ARR"));
        AirWaybill airWaybill = new AirWaybill();
        if (oldAirWaybill != null){
            if (oldAirWaybill.getIsDel() == 0){
                 throw new CustomException("当前运单已存在");
            }else{
                airWaybill.setId(oldAirWaybill.getId());
            }
        }
        BeanUtils.copyProperties(vo,airWaybill);
        airWaybill.setWaybillType(airWaybill.getType());
        airWaybill.setType("ARR");
        if (flight != null){
            airWaybill.setFlightNo1(flight.getAirWays() + flight.getFlightNo());
            Instant instant = flight.getStartSchemeTakeoffTime().atZone(ZoneId.systemDefault()).toInstant();
            Date date = Date.from(instant);
            airWaybill.setFlightDate1(date);
        }
        airWaybill.setStatus("record_order");
        airWaybill.setWriter(SecurityUtils.getNickName());
        airWaybill.setWriteTime(new Date());
        airWaybill.setUpdateTime(new Date());
        airWaybill.setIsDel(0);
        //收货人是肯定有值的
        List<SysDept> depts = deptList.stream()
                .filter(e -> vo.getConsign().equals(e.getDeptName()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(depts)) {
            SysDept dept = depts.get(0);
            airWaybill.setDeptId(dept.getDeptId());
        } else {
            airWaybill.setDeptId(1L);
        }
        if(StringUtils.isNotEmpty(vo.getAgentCode())){
            //前端填了简称 那么收货人和代理人都是一个值
            airWaybill.setShipperAbb(vo.getAgentCode());
            airWaybill.setAgentCompany(vo.getShipper());
        }
        if (airWaybill.getId() != null){
            airWaybillMapper.updateById(airWaybill);
            LambdaUpdateWrapper<AirWaybill> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(AirWaybill::getId, airWaybill.getId())
                    .set(AirWaybill::getIsDel, 0);
            airWaybillMapper.update(null, wrapper);

        }else {
            airWaybillMapper.insert(airWaybill);
        }
        int count = recordOrderMapper.selectCountByLegId(vo.getWaybillCode(),vo.getLegId());
        int insert = 0;
        if (count == 0){
            HzArrRecordOrder recordOrder = new HzArrRecordOrder();
            recordOrder.setWaybillCode(airWaybill.getWaybillCode());
            recordOrder.setCabinPieces(vo.getCabinQuantity());
            recordOrder.setCabinWeight(vo.getCabinWeight());
            recordOrder.setLegId(vo.getLegId());
            recordOrder.setStatus("ld");
            recordOrder.setOrderTime(new Date());
            if(airWaybill.getWaybillCode().contains("AWBMDN")){
                recordOrder.setIsExamine(1);
            }
            insert = recordOrderMapper.insert(recordOrder);
        }
        //这里在制单的时候 执行生成理货舱单
        TallyManifestQuery tallyManifestQuery = new TallyManifestQuery();
        tallyManifestQuery.setLegId(vo.getLegId());
        List<Long> waybillIds = new LinkedList<>();
        waybillIds.add(airWaybill.getId());
        tallyManifestQuery.setWaybillIds(waybillIds);
        createTallyManifest(tallyManifestQuery);
        if (flight != null){
            String token = getToken();
            String sendAddress = sysConfigMapper.selectValue("arr.sendAddress");
            if (vo.getTransferBill() == 1){
                try {
                    MsgJsonVO msgJsonVO = setMsgVo(flight, sendAddress, "RCT");
                    setAWROrRctMsg(flight,token,msgJsonVO,vo.getCabinWeight().toString(),vo.getCabinQuantity().toString(),airWaybill);
                }catch (Exception e){
                    log.error(CABLE_ERROR_MARKER, "运单"+airWaybill.getWaybillCode()+"发送FSU-RCT报文失败：" + e.getMessage());
                }

            }
            try {
                MsgJsonVO msgJsonVO = setMsgVo(flight, sendAddress,"AWR");
                setAWROrRctMsg(flight,token,msgJsonVO,vo.getCabinWeight().toString(),vo.getCabinQuantity().toString(),airWaybill);
            }catch (Exception e){
                log.error(CABLE_ERROR_MARKER, "运单"+airWaybill.getWaybillCode()+"发送FSU-AWR报文失败：" + e.getMessage());
            }
    }
        return insert;
    }

    /**
     * 根据运单号查询录入信息
     * @param query 查询条件
     * @return 结果
     */
    @Override
    public EnterWaybillVo getWaybillInfo(FlightFileQuery query) {
        if (StringUtils.isEmpty(query.getWaybillCode())){
            return null;
        }
        EnterWaybillVo infoByCode = airWaybillMapper.getInfoByCode(query);
        HzArrRecordOrder recordOrder = recordOrderMapper.selectOne(new QueryWrapper<HzArrRecordOrder>()
                .eq("leg_id", query.getLegId()).eq("waybill_code", query.getWaybillCode()));
        if (recordOrder != null){
            infoByCode.setCabinQuantity(recordOrder.getCabinPieces());
            infoByCode.setCabinWeight(recordOrder.getCabinWeight());
            infoByCode.setTallyId(recordOrder.getId());
            infoByCode.setOrderId(recordOrder.getId());
            infoByCode.setIsExamine(recordOrder.getIsExamine());
            infoByCode.setLegId(recordOrder.getLegId());
            infoByCode.setOrderTime(recordOrder.getOrderTime());
        }
        return infoByCode;
    }

    /**
     * 运单修改保存
     * @param vo 运单修改参数
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int edit(EnterWaybillVo vo) {
        AirWaybill airWaybill = airWaybillMapper.selectById(vo.getId());
        AirWaybill updateWaybill = new AirWaybill();
        BeanUtils.copyProperties(vo,updateWaybill);
        updateWaybill.setId(airWaybill.getId());
        updateWaybill.setVersion(airWaybill.getVersion());
        List<HzArrRecordOrder> recordOrderList = recordOrderMapper.selectList(new QueryWrapper<HzArrRecordOrder>()
                .eq("waybill_code", airWaybill.getWaybillCode()));
        if (!CollectionUtils.isEmpty(recordOrderList)){
            List<HzArrRecordOrder> sameLegOrder = recordOrderList.stream()
                    .filter(e -> e.getLegId().equals(vo.getLegId())).collect(Collectors.toList());
            if(sameLegOrder.size() > 0){
                HzArrRecordOrder hzArrRecordOrder = sameLegOrder.get(0);
                hzArrRecordOrder.setWaybillCode(vo.getWaybillCode());
                hzArrRecordOrder.setCabinWeight(vo.getCabinWeight());
                hzArrRecordOrder.setCabinPieces(vo.getCabinQuantity());
                hzArrRecordOrder.setIsExamine(0);
                recordOrderMapper.updateById(hzArrRecordOrder);
            }else{
                //不同航段下修改, 新增一条记录，关联新的航段
                HzArrRecordOrder hzArrRecordOrder = new HzArrRecordOrder();
                hzArrRecordOrder.setWaybillCode(vo.getWaybillCode());
                hzArrRecordOrder.setCabinPieces(vo.getCabinQuantity());
                hzArrRecordOrder.setCabinWeight(vo.getCabinWeight());
                hzArrRecordOrder.setLegId(vo.getLegId());
                hzArrRecordOrder.setIsExamine(0);
                hzArrRecordOrder.setId(null);
                hzArrRecordOrder.setOrderTime(new Date());
                hzArrRecordOrder.setStatus("cd");
                recordOrderMapper.insert(hzArrRecordOrder);
            }
            for (HzArrRecordOrder hzArrRecordOrder : recordOrderList) {
                List<HzArrTally> hzArrTallyList = hzArrTallyMapper.selectList(new QueryWrapper<HzArrTally>()
                        .eq("waybill_code", airWaybill.getWaybillCode())
                        .eq("record_order_id",hzArrRecordOrder.getId()));
                if (hzArrTallyList.size() > 0 && !"lh_comp".equals(hzArrTallyList.get(0).getStatus())) {
                    throw new CustomException("该运单已办单,修改失败");
                }
                for (HzArrTally hzArrTally : hzArrTallyList) {
                    hzArrTally.setWaybillCode(vo.getWaybillCode());
                    hzArrTallyMapper.updateById(hzArrTally);
                }
            }
//            for (HzArrRecordOrder recordOrder : recordOrderList) {
//                List<HzArrTally> hzArrTallyList = hzArrTallyMapper.selectList(new QueryWrapper<HzArrTally>()
//                        .eq("waybill_code", airWaybill.getWaybillCode())
//                        .eq("record_order_id",recordOrder.getId()));
//                recordOrder.setWaybillCode(vo.getWaybillCode());
//                recordOrder.setCabinWeight(vo.getCabinWeight());
//                recordOrder.setCabinPieces(vo.getCabinQuantity());
//                recordOrder.setIsExamine(0);
//                if (recordOrder.getLegId().equals(vo.getLegId())){
//                    //相同航段下修改
//                    if(hzArrTallyList.size() > 0){
//                        if (!"lh_comp".equals(hzArrTallyList.get(0).getStatus())) {
//                            throw new CustomException("该运单已办单,修改失败");
//                        }
//                    }
//                    for (HzArrTally hzArrTally : hzArrTallyList) {
//                        hzArrTally.setWaybillCode(vo.getWaybillCode());
//                        hzArrTallyMapper.updateById(hzArrTally);
//                    }
//                    recordOrderMapper.updateById(recordOrder);
//                }else {
//                    //不同航段下修改, 新增一条记录，关联新的航段
//                    recordOrder.setLegId(vo.getLegId());
//                    recordOrder.setId(null);
//                    recordOrder.setOrderTime(new Date());
//                    recordOrderMapper.insert(recordOrder);
//                    break;
//                }
//            }

        }else {
            HzArrRecordOrder order = new HzArrRecordOrder();
            order.setWaybillCode(vo.getWaybillCode());
            order.setCabinPieces(vo.getCabinQuantity());
            order.setCabinWeight(vo.getCabinWeight());
            order.setLegId(vo.getLegId());
            order.setOrderTime(new Date());
            order.setStatus("cd");
            recordOrderMapper.insert(order);
        }
        FlightInfo flight = flightInfoMapper.selectFlightNo(vo.getLegId());
        if (flight != null){
            updateWaybill.setFlightNo1(flight.getAirWays() + flight.getFlightNo());
            Instant instant = flight.getStartSchemeTakeoffTime().atZone(ZoneId.systemDefault()).toInstant();
            Date date = Date.from(instant);
            updateWaybill.setFlightDate1(date);
        }
        //这里是因为vo里面的type字段带过来的是AWBA类似的数据
        updateWaybill.setType("ARR");
        updateWaybill.setWaybillType(vo.getType());
        updateWaybill.setUpdateTime(new Date());
        updateWaybill.setIsDel(0);
        if(vo.getAgentCode() == null || "".equals(vo.getAgentCode())){
            //简称没有值 只录入的收货人 代理人数据清空
            updateWaybill.setConsign(vo.getConsign());
            updateWaybill.setShipper("");
            updateWaybill.setShipperAbb("");
            updateWaybill.setAgentCode("");
            updateWaybill.setAgentCompany("");
        }else{
            //简称有值 代理人和收货人都要有值
            updateWaybill.setConsign(vo.getConsign());
            updateWaybill.setShipper(vo.getShipper());
            updateWaybill.setShipperAbb(vo.getAgentCode());
            updateWaybill.setAgentCode(vo.getAgentCode());
            updateWaybill.setAgentCompany(vo.getShipper());
        }
        List<SysDept> deptList = sysDeptMapper.selectList();
        //收货人是肯定有值的
        List<SysDept> depts = deptList.stream()
                .filter(e -> vo.getConsign().equals(e.getDeptName()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(depts)) {
            SysDept dept = depts.get(0);
            updateWaybill.setDeptId(dept.getDeptId());
        } else {
            updateWaybill.setDeptId(1L);
        }
        int i = airWaybillMapper.updateById(updateWaybill);
        if (i == 0){
            throw new CustomException("修改失败，运单已被其他操作修改，请刷新后重试");
        }
        return i;
    }

    /**
     * 根据航班日期航班号查询航段
     * @param query 查询参数
     * @return 航段列表
     */
    @Override
    public List<LegVo> getLeg(FlightFileQuery query) {
        return flightLoadMapper.getLeg(query);
    }

    @Override
    public FlightNoVo getPrefix(FlightFileQuery query) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String airLinesByCode = flightLoadMapper.getAirLinesByCode(query.getFlightNo(), sdf.format(query.getExecDate()),"A");
        FlightNoVo flightVo = new FlightNoVo();
        //根据航班号找到了航司
        if(StringUtils.isNotNull(airLinesByCode)){
            flightVo.setFlightNo(airLinesByCode + query.getFlightNo());
            BaseCarrier prefix = carrierMapper.selectByCode(airLinesByCode);
            if (StringUtils.isNotNull(prefix)){
                flightVo.setPrefix(prefix.getPrefix().toString());
            }
        }
        //没找到的话,可能是不存在,也有可能是输入的完整航班,输的是完整航班,也要带一个前缀回去
        if (query.getFlightNo().length() > 4 && airLinesByCode == null){
            flightVo.setFlightNo(query.getFlightNo());
            BaseCarrier prefix = carrierMapper.selectByCode(query.getFlightNo().substring(0,2));
            if (StringUtils.isNotNull(prefix)){
                flightVo.setPrefix(prefix.getPrefix().toString());
            }
        }

        return flightVo;
    }

    /**
     * 取消理货历史
     * */
    @Override
    public int tallyCancel(TallyHistoryVo vo) {
        HttpServletResponse response = ServletUtils.getResponse();
        HzArrTally hzArrTally = hzArrTallyMapper.selectById(vo.getId());
        if (!"lh_comp".equals(hzArrTally.getStatus())) {
            throw new CustomException("该运单已办理提货,取消失败");
        }
        //运单日志的新增
        WaybillLog waybillLog = waybillLogService.getWaybillLog(
                hzArrTally.getWaybillCode(), 0, SecurityUtils.getNickName(),
                hzArrTally.getWeight().toString(), hzArrTally.getPieces().toString(), null,
                vo, null, 0, null, new Date(),
                "取消理货历史", "ARR", hzArrTally.getUld());
        try{
            int i = hzArrTallyMapper.deleteById(vo.getId());

            //去修改收费项目hz_arr_waybill_item里面的对应的字段tally_id,防止这个单没有费用
            List<HzArrTally> hzArrTallyList = hzArrTallyMapper.selectList(new QueryWrapper<HzArrTally>()
                    .eq("record_order_id", hzArrTally.getRecordOrderId())
                    .last("order by tally_time asc"));

            if(hzArrTallyList.size() > 0){
//                hzArrTallyList.remove(hzArrTally);
                //拿到hzArrTallyList中最后的一条数据
                HzArrTally hzArrTally1 = hzArrTallyList.get(hzArrTallyList.size()-1);
                List<HzArrItem> itemList = itemMapper.selectList(new QueryWrapper<HzArrItem>()
                        .eq("waybill_code", hzArrTally1.getWaybillCode())
                        .eq("order_id", hzArrTally1.getRecordOrderId()));
                for (HzArrItem item:itemList) {
                    item.setTallyId(hzArrTally1.getId());
                    itemMapper.updateById(item);
                }
            }

            Wrong wrong = wrongMapper.selectOne(new QueryWrapper<Wrong>()
                    .eq("type", "ARR")
                    .eq("wrong_type", "理货数量异常")
                    .eq("tally_id", hzArrTally.getId()));
            //在不正常货邮中删除本条理货历史的
            if(wrong != null){
                wrongMapper.deleteById(wrong.getId());
            }

            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作成功" +  "," +
                            "code:" + response.getStatus() + "," +
                            "data:" + i));
            return i;
        }catch (Exception e){
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" +  "," +
                            "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        }finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }

    /**
     * 批量理货
     * @param tallyIds 理货id集合
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchTally(List<String> tallyIds) {
        String token = getToken();
        for (String tallyId : tallyIds) {
            HzArrRecordOrder order = recordOrderMapper.selectById(tallyId);
            AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                    .eq("waybill_code", order.getWaybillCode())
                    .eq("type","ARR"));
            if (airWaybill == null){
                throw new CustomException("无当前运单信息");
            }
            TallyWaybillVo tallyWaybillVo = recordOrderMapper.getTallyInfo(order.getWaybillCode(),order.getLegId());
            if (tallyWaybillVo == null){
                throw new CustomException("该运单未生成理货任务");
            }
            List<HzArrTally> hzArrTallyList = hzArrTallyMapper.selectList(new QueryWrapper<HzArrTally>().eq("record_order_id", tallyId));
            if(!CollectionUtils.isEmpty(hzArrTallyList)){
                throw new CustomException("运单:" + airWaybill.getWaybillCode() + "已有理货历史,批量理货失败");
            }
            HttpServletResponse response = ServletUtils.getResponse();
            //运单日志的新增
            WaybillLog waybillLog = waybillLogService.getWaybillLog(
                    airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                    airWaybill.getWeight().toString(), airWaybill.getQuantity().toString(), airWaybill.getFlightNo1(),
                    tallyIds, null, 0, null, new Date(),
                    "理货完成,理货件数:" + airWaybill.getQuantity() + ",理货重量:" + airWaybill.getWeight(),
                    "ARR", null);
            try{
                AirWaybill updateWaybill = new AirWaybill();
                updateWaybill.setId(airWaybill.getId());
                updateWaybill.setVersion(airWaybill.getVersion());
                updateWaybill.setStatus("tally_comp");
                updateWaybill.setUpdateTime(new Date());
                int i = airWaybillMapper.updateById(updateWaybill);
                if (i == 0){
                    throw new CustomException("运单"+airWaybill.getWaybillCode()+"理货失败，运单已被其他操作修改，请刷新后重试");
                }
                BaseCargoCategory category = cargoCategoryMapper.selectOne(new QueryWrapper<BaseCargoCategory>()
                        .eq("code", airWaybill.getCategoryName()).eq("is_del",0));
                HzArrTally hzArrTally = new HzArrTally();
                if (category != null){
                    hzArrTally.setStore(category.getInStore());
                    hzArrTally.setLocator(category.getInLocator());
                }
                hzArrTally.setWaybillCode(airWaybill.getWaybillCode());
                hzArrTally.setPieces(airWaybill.getQuantity());
                hzArrTally.setWeight(airWaybill.getWeight());
                hzArrTally.setRecordOrderId(order.getId());
                hzArrTally.setStatus("lh_comp");
                hzArrTally.setLegId(order.getLegId());
                hzArrTally.setUsername(SecurityUtils.getNickName());
                hzArrTally.setCabinPieces(order.getCabinPieces());
                hzArrTally.setCabinWeight(order.getCabinWeight());
                hzArrTally.setTallyTime(new Date());
                int insert = hzArrTallyMapper.insert(hzArrTally);
                List<HzArrItem> item = itemMapper.selectList(new QueryWrapper<HzArrItem>()
                        .eq("waybill_code", order.getWaybillCode())
                        .eq("order_id", order.getId()));
                for (HzArrItem arrItem : item) {
                    arrItem.setTallyId(hzArrTally.getId());
                    itemMapper.updateById(arrItem);
                }
                try {
                    setRCFMsg(order, airWaybill, token);
                }catch (Exception e){
                    log.error(CABLE_ERROR_MARKER, "运单"+airWaybill.getWaybillCode()+"发送FSU-RCF报文失败：" + e.getMessage());
                }
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作成功" +  "," +
                                "code:" + response.getStatus() + "," +
                                "data:" + insert));
            }catch (Exception e){
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作失败" +  "," +
                                "code:" + response.getStatus()));
                waybillLog.setErrorMsg(e.getMessage());
                waybillLog.setStatus(1);
                throw new CustomException(e.getMessage());
            }finally {
                waybillLogService.insertWaybillLog(waybillLog);
            }
        }
        return 1;
    }

    private void setRCFMsg(HzArrRecordOrder order, AirWaybill airWaybill,String token) {
        String sendAddress = sysConfigMapper.selectValue("arr.sendAddress");
        FlightInfo info = flightInfoMapper.selectFlightNo(order.getLegId());
        MsgJsonVO msgJson = setMsgVo(info, sendAddress,"RCF");
        List<HzCableAddress> addressList = cableAddressMapper.selectAddressByAirportCode(Collections.singletonList(airWaybill.getDesPort()));
        StringBuilder builder = new StringBuilder();
        FSUJsonVO fsuJsonVO = new FSUJsonVO();
        fsuJsonVO.setDepAirport(airWaybill.getSourcePort());
        fsuJsonVO.setDesAirport(airWaybill.getDesPort());
        if(airWaybill.getWaybillCode().startsWith("AWBA")){
            String substring = airWaybill.getWaybillCode().substring(4);
            StringBuilder stringBuilder = new StringBuilder(substring);
            fsuJsonVO.setMawbNo(stringBuilder.insert(3, "-").toString());
        }else{
            String substring = airWaybill.getWaybillCode().substring(4);
            StringBuilder stringBuilder = new StringBuilder(substring);
            fsuJsonVO.setMawbNo(stringBuilder.insert(2, "-").toString());
        }
        fsuJsonVO.setPieces(order.getCabinPieces().toString());
        fsuJsonVO.setShipmentDescriptionCode("T");
        List<StatusDetails> statusDetails = new ArrayList<>();
        StatusDetails details = new StatusDetails();
        details.setDeliveryName("NSK");
        details.setMovementAirport(info.getStartStation());
        details.setMovementArrivalAirport(info.getStartStation());
        details.setMovementCarrier(info.getAirWays());
        details.setMovementDate(LocalDateTime.now().format(DATE_FORMATTER));
        details.setMovementDepartureAirport(info.getStartStation());
        details.setMovementFlightNo(info.getFlightNo());
        details.setMovementTime(LocalDateTime.now().format(TIME_FORMATTER));
        details.setNotificationName("NSK");
        details.setPieces(order.getCabinPieces().toString());
        details.setReceivedName("NSK");
        details.setReceivingCarrier(info.getAirWays());
        details.setReportingAirport(info.getStartStation());
        details.setReportingDate(LocalDateTime.now().format(DATE_FORMATTER));
        details.setReportingTime(LocalDateTime.now().format(TIME_FORMATTER));
        details.setShipmentDescriptionCode("T");
        details.setStatusCode("RCF");
        details.setTransferredName("NSK");
        if (airWaybill.getVolume() != null){
            details.setVolume(airWaybill.getVolume().toString());
            details.setVolumeUnit("MC");
        }
        details.setWeight(order.getCabinWeight().toString());
        details.setWeightUnit("K");
        statusDetails.add(details);
        fsuJsonVO.setStatusDetails(statusDetails);
        msgJson.setMsgJson(JSON.toJSONString(fsuJsonVO));
        Map<String, List<HzCableAddress>> addressMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(addressList)){
            addressMap = addressList.stream().collect(Collectors.groupingBy(HzCableAddress::getAirportCode));
        }
        List<HzCableAddress> cableAddresses = addressMap.get(airWaybill.getDesPort());
        setInteractionType(cableAddresses,msgJson);
        restExchange(msgJson,token,builder);
        insertCableAndSendMsg(msgJson,info,builder,token);
    }

    @Override
    public BaseCargoCategory getLocator(FlightFileQuery query) {
        AirWaybill airWaybill = new AirWaybill();
        if(query.getFlightNo() != null){
            airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                    .eq("waybill_code", query.getWaybillCode())
                    .eq("flight_no1", query.getFlightNo())
                    .eq("type",query.getType()));
        }

        if(airWaybill == null){
            airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                    .eq("waybill_code", query.getWaybillCode())
                    .eq("type",query.getType())
                    .last("limit 1"));
        }

        BaseCargoCategory category = new BaseCargoCategory();
        if(StringUtils.isNotNull(airWaybill.getCategoryName())){
            category = cargoCategoryMapper.selectOne(new QueryWrapper<BaseCargoCategory>()
                    .eq("code", airWaybill.getCategoryName())
                    .eq("is_del",0));
        }
        return category;
    }

    @Override
    public int compFlight(Long flightId) {
        List<WaybillLog> waybillLogs = new ArrayList<>();
        try {
            FlightInfo info = flightInfoMapper.selectById(flightId);
            String sendAddress = sysConfigMapper.selectValue("arr.sendAddress");
            if (StringUtils.isNotNull(sendAddress)) {
                String token = getToken();
                try {
                    List<AddressMsgVO> originMsg = getFFMMsg(flightId, sendAddress,token);
                    for (AddressMsgVO msgVO : originMsg) {
                        if (StringUtils.isEmpty(msgVO.getSendType())){
                            continue;
                        }
                        List<String> sendTypes = Arrays.asList(msgVO.getSendType().split(","));
                        if (!sendTypes.contains("FD")){
                            sendTypes.add("FD");
                        }
                        for (String sendType : sendTypes) {
                            HzCable cable = new HzCable();
                            Date date = new Date();
                            SimpleDateFormat sdf = new SimpleDateFormat("ddHHmm");
                            cable.setSerialNo(SerialNumberGenerator.generateSerialNumber());
                            cable.setCableNo(sdf.format(date));
                            cable.setCableTime(date);
                            cable.setCreateBy("系统");
                            cable.setIsSend(1);
                            cable.setIsAuto(1);
                            cable.setType("FSU");
                            cable.setVersion("12");
                            cable.setPriority("QD");
                            cable.setCableAddress(sendAddress);
                            if (msgVO.getAddress() != null){
                                cable.setReceiveAddress(String.join(",", msgVO.getAddress()));
                            }
                            cable.setFlightNo(info.getFlightNo());
                            cable.setFlightDate(info.getExecDate());
                            cable.setContent(msgVO.getMsg());
                            hzCableMapper.insert(cable);
                            ForwardOriginMsgVO originMsgVO = new ForwardOriginMsgVO();
                            originMsgVO.setOriginMsg(msgVO.getMsg());
                            originMsgVO.setMsgType("FSU");
                            originMsgVO.setSendType(sendType);
                            originMsgVO.setReceiveAddress("-");
                            if ("FD".equals(sendType)) {
                                if (msgVO.getAddress() != null){
                                    originMsgVO.setReceiveAddress(String.join(",",msgVO.getAddress()));
                                }
                            }
                            if ("MAIL".equals(sendType)){
                                originMsgVO.setMailAddress(String.join(",",msgVO.getReceiveMailAddress()));
                            }
                            if ("FTP".equals(sendType)){
                                originMsgVO.setReceiveFtpAddress(ftpAddress);
                                originMsgVO.setReceiveFtpFolder(msgVO.getReceiveFtpFolder());
                            }
                            if ("MQ".equals(sendType)){
                                originMsgVO.setReceiveMQQueue(msgVO.getReceiveMQQueue());
                            }
                            originMsgVO.setSendAddress(sendAddress);
                            originMsgVO.setPriority("QD");
                            httpService.sendCable(originMsgVO, cable, getToken());
                        }
                    }
                }catch (Exception e){
                    log.error(CABLE_ERROR_MARKER, "发送FFM报文失败：" + e.getMessage());
                }
            }
            flightInfoMapper.compFlight(flightId);
            List<FlightLoad> flightLoadList = flightLoadMapper.selectList(new QueryWrapper<FlightLoad>().eq("flight_id", flightId));
            List<HzArrRecordOrder> recordOrderList = recordOrderMapper.selectList(new QueryWrapper<HzArrRecordOrder>().in("leg_id", flightLoadList.stream().map(FlightLoad::getId).collect(Collectors.toList())));
            for(HzArrRecordOrder hzArrRecordOrder : recordOrderList){
                WaybillLog log = new WaybillLog();
                log.setWaybillCode(hzArrRecordOrder.getWaybillCode());
                log.setOperRemark(0);
                log.setOperName(SecurityUtils.getNickName());
                log.setOperWeight(hzArrRecordOrder.getCabinWeight().toString());
                log.setOperPieces(hzArrRecordOrder.getCabinPieces().toString());
                log.setStatus(0);
                log.setOperParam(JSON.toJSONString(flightId));
                log.setOperTime(new Date());
                log.setType("ARR");
                log.setJsonResult("msg:操作成功,code:200,data:1");
                log.setFlightNo(info.getAirWays() + info.getFlightNo());
                log.setRemark("关闭航班");
                waybillLogs.add(log);
            }
        }catch (Exception e){
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson("操作失败"));
                waybillLog.setErrorMsg(e.getMessage());
                waybillLog.setStatus(1);
            }
        }finally {
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLogService.insertWaybillLog(waybillLog);
            }
        }
        return 1;
    }

    @Override
    public int openFlight(Long flightId) {
        return flightInfoMapper.openFlight(flightId);
    }

    @Override
    public int examine(Long orderId) {
        HzArrRecordOrder hzArrRecordOrder = recordOrderMapper.selectById(orderId);
        WaybillLog log = new WaybillLog();
        log.setWaybillCode(hzArrRecordOrder.getWaybillCode());
        log.setOperRemark(0);
        log.setOperName(SecurityUtils.getNickName());
        log.setOperWeight(hzArrRecordOrder.getCabinWeight().toString());
        log.setOperPieces(hzArrRecordOrder.getCabinPieces().toString());
        log.setStatus(0);
        log.setOperParam(JSON.toJSONString(orderId));
        log.setOperTime(new Date());
        log.setType("ARR");
        log.setJsonResult("msg:操作成功,code:200,data:1");
        log.setRemark("运单审核");
        waybillLogService.insertWaybillLog(log);
        return recordOrderMapper.updateExamine(orderId);
    }

    @Override
    public int cancelExamine(Long orderId) {
        return recordOrderMapper.cancelExamine(orderId);
    }

    /**
     * 查询运单列表
     * @param query 查询条件
     * @return 运单列表
     */
    @Override
    public FlightFileVo waybillList(FlightFileQuery query) {
        FlightFileVo vo = new FlightFileVo();
        String flightOper = flightInfoMapper.getFlightOper(query.getLegId());
        if (flightOper != null){
            vo.setFlightOper(flightOper);
        }
        List<FlightFileWaybillVo> flightFileWaybillVos = recordOrderMapper.waybillListQuery(query);
        if (!CollectionUtils.isEmpty(flightFileWaybillVos)){
            flightFileWaybillVos.forEach(e-> e.setIsTallyWrong(getIsTallyWrong(e)));
            vo.setWaybillVos(flightFileWaybillVos);
            for (FlightFileWaybillVo flightFileWaybillVo : flightFileWaybillVos) {
                List<HzArrTally> tallyList = hzArrTallyMapper.selectList(new QueryWrapper<HzArrTally>().eq("record_order_id", flightFileWaybillVo.getOrderId()));
                if (!CollectionUtils.isEmpty(tallyList)){
                    int sum = tallyList.stream().mapToInt(HzArrTally::getPieces).sum();
                    flightFileWaybillVo.setTallyQuantity(sum);
                    BigDecimal reduce = tallyList.stream().map(HzArrTally::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                    flightFileWaybillVo.setTallyWeight(reduce);
                }
                if (flightFileWaybillVo.getTallyQuantity() == null){
                    flightFileWaybillVo.setIsTally("否");
                }else {
                    flightFileWaybillVo.setIsTally("是");
                }

                if (!flightFileWaybillVo.getReplenishBill().equals(1)){
                    flightFileWaybillVo.setMethod("提货");
                }
                if (flightFileWaybillVo.getTransferBill().equals(1)){
                    flightFileWaybillVo.setMethod("中转");
                }
            }
            int totalSum = flightFileWaybillVos.size();
            vo.setTotalFile(totalSum);
            BigDecimal totalWeight = flightFileWaybillVos.stream().map(FlightFileWaybillVo::getWeight).reduce(BigDecimal.ZERO,BigDecimal::add);
            vo.setTotalFileWeight(totalWeight);
            Integer totalQuantity = flightFileWaybillVos.stream().map(FlightFileWaybillVo::getQuantity).reduce(0, Integer::sum);
            vo.setTotalFileQuantity(totalQuantity);
            List<FlightFileWaybillVo> awba = flightFileWaybillVos.stream().filter(e -> e.getWaybillCode().contains("AWBA")).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(awba)){
                vo.setTotalAWBA(awba.size());
            }
            List<FlightFileWaybillVo> awbm = flightFileWaybillVos.stream().filter(e -> e.getWaybillCode().contains("AWBM")).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(awbm)){
                vo.setTotalAWBM(awbm.size());
            }

            int tallySum = flightFileWaybillVos.stream().filter(e -> e.getTallyQuantity() != null).mapToInt(FlightFileWaybillVo::getTallyQuantity).sum();
            vo.setTotalTally(tallySum);
            BigDecimal tallyWeight = flightFileWaybillVos.stream().map(FlightFileWaybillVo::getTallyWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setTotalTallyWeight(tallyWeight);
        }
        return vo;
    }

    /**
     * 判断运单列表是否显示红色
     * */
    private Integer getIsTallyWrong(FlightFileWaybillVo vo){
        if("有货无单".equals(vo.getConsign())){
            return 1;
        }
        List<TallyHistoryVo> tallyHistoryVos = hzArrTallyMapper.selectTallyHistoryList(vo.getWaybillCode());
        if(tallyHistoryVos.size() == 0){
            return 1;
        }
        int sum = tallyHistoryVos.stream().mapToInt(TallyHistoryVo::getTallyQuantity).sum();
        BigDecimal reduce = tallyHistoryVos.stream().map(TallyHistoryVo::getTallyWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
        if(sum == vo.getQuantity() && reduce.compareTo(vo.getWeight()) == 0){
            return 0;
        }
        List<Wrong> wrongs = wrongMapper.selectList(new QueryWrapper<Wrong>()
                .eq("waybill_code", vo.getWaybillCode())
                .eq("wrong_type", "理货数量异常")
                .eq("is_over", "0"));
        if(wrongs.size() > 0){
            return 1;
        }
        return 0;
    }

    /**
     * 删除
     * @param id 运单id
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int del(Long id,Long legId) {
        AirWaybill airWaybill = airWaybillMapper.selectById(id);
        int outCount = pickUpOutMapper.selectCount(new QueryWrapper<AllPickUpOut>()
                .eq("waybill_code", airWaybill.getWaybillCode())
                .eq("is_cancel",0));
        if (outCount > 0) {
            throw new CustomException("当前运单已出库");
        }
        int pickUpCount = pickUpWaybillMapper.selectCount(new QueryWrapper<PickUpWaybill>()
                .eq("waybill_code", airWaybill.getWaybillCode())
                .eq("is_cancel",0));
        if (pickUpCount > 0) {
            throw new CustomException("当前运单已提货");
        }
        List<HzArrTally> hzArrTallyList = hzArrTallyMapper.selectList(new QueryWrapper<HzArrTally>()
                .eq("waybill_code", airWaybill.getWaybillCode()));
        if (hzArrTallyList.size() > 0) {
            throw new CustomException("当前运单已理货");
        }
        AirWaybill updateWaybill = new AirWaybill();
        updateWaybill.setId(airWaybill.getId());
        updateWaybill.setVersion(airWaybill.getVersion());
        updateWaybill.setIsDel(1);
        int i = airWaybillMapper.updateById(updateWaybill);
        if (i == 0) {
            throw new CustomException("删除失败，运单已被其他操作修改，请刷新后重试");
        }
        recordOrderMapper.delete(new QueryWrapper<HzArrRecordOrder>()
                .eq("waybill_code", airWaybill.getWaybillCode()));
        WaybillLog log = new WaybillLog();
        log.setWaybillCode(airWaybill.getWaybillCode());
        log.setOperRemark(0);
        log.setOperName(SecurityUtils.getNickName());
        log.setStatus(0);
        log.setOperParam(JSON.toJSONString(id));
        log.setOperTime(new Date());
        log.setType("ARR");
        log.setJsonResult("msg:操作成功,code:200,data:1");
        log.setRemark("航班文件删除运单");
        log.setFlightNo(airWaybill.getFlightNo1());
        waybillLogService.insertWaybillLog(log);
        return 1;
    }

    /**
     * 保存航班操作
     * @param query 查询条件
     * @return 结果
     */
    @Override
    public int saveFlightOper(FlightFileQuery query) {
        FlightLoad load = flightLoadMapper.selectById(query.getLegId());
        FlightInfo flightInfo = flightInfoMapper.selectById(load.getFlightId());
        flightInfo.setFlightOper(query.getFlightOper());
        return flightInfoMapper.updateById(flightInfo);
    }

    /**
     * 理货舱单
     * @param query 查询参数
     * @return 理货舱单列表
     */
    @Override
    public TallyManifestVo tallyManifest(TallyManifestQuery query) {
        TallyManifestVo vo = flightInfoMapper.selectByLegId(query.getLegId());
        if (vo != null){
            List<FlightFileWaybillVo> flightFileWaybillVos = recordOrderMapper.waybillList(query.getLegId(), query.getWaybillIds(),null);
            if (!CollectionUtils.isEmpty(flightFileWaybillVos)){
                for (FlightFileWaybillVo flightFileWaybillVo : flightFileWaybillVos) {
                    List<HzArrTally> tallyList = hzArrTallyMapper.selectList(new QueryWrapper<HzArrTally>().eq("record_order_id", flightFileWaybillVo.getOrderId()));
                    if (!CollectionUtils.isEmpty(tallyList)){
                        int sum = tallyList.stream().mapToInt(HzArrTally::getPieces).sum();
                        flightFileWaybillVo.setTallyQuantity(sum);
                        BigDecimal reduce = tallyList.stream().map(HzArrTally::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                        flightFileWaybillVo.setTallyWeight(reduce);
                    }
                    Integer count = hzArrTallyMapper.selectCount(new QueryWrapper<HzArrTally>().eq("waybill_code", flightFileWaybillVo.getWaybillCode()));
                    flightFileWaybillVo.setNumber(count);
                    AirWaybill airWaybill = airWaybillMapper.selectById(flightFileWaybillVo.getId());
                    if(airWaybill.getShipper() != null){
                        String s = sysDeptMapper.selectDeptAbbByName(airWaybill.getShipper());
                        if(s != null){
                            flightFileWaybillVo.setDeptNameAbb(s);
                        }
                    }
                }
                vo.setWaybillVos(flightFileWaybillVos);
            }
        }
        return vo;
    }

    /**
     * 生成理货舱单
     * @param query 查询条件
     * @return 结果
     */
    @Override
    public int createTallyManifest(TallyManifestQuery query) {
        FlightLoad flightLoad = flightLoadMapper.selectById(query.getLegId());
        FlightInfo flightInfo = flightInfoMapper.selectById(flightLoad.getFlightId());
        Integer tallyNum = flightInfo.getTallyNum();
        flightInfo.setTallyNum(tallyNum + 1);
        flightInfo.setCabinDate(new Date());
        flightInfo.setTallyStatus("未理货");
        List<AirWaybill> airWaybills = airWaybillMapper.selectBatchIds(query.getWaybillIds());
        if (!CollectionUtils.isEmpty(airWaybills)){
            List<String> waybillCodes = airWaybills.stream().map(AirWaybill::getWaybillCode).collect(Collectors.toList());
            List<HzArrRecordOrder> list = recordOrderMapper.selectList(new QueryWrapper<HzArrRecordOrder>()
                    .eq("leg_id", query.getLegId())
                    .in("waybill_code", waybillCodes));
            for (HzArrRecordOrder recordOrder : list) {
                recordOrder.setStatus("cd");
                recordOrderMapper.updateById(recordOrder);
            }
        }
        return flightInfoMapper.updateById(flightInfo);
    }

    /**
     * 理货列表查询
     * @param query 查询条件
     * @return 列表
     */
    @Override
    public FlightFileVo tallyList(FlightFileQuery query) {
        FlightFileVo vo = new FlightFileVo();
        List<FlightFileWaybillVo> flightFileWaybillVos = recordOrderMapper.waybillList(query.getLegId(), null,"cd");
        if (!CollectionUtils.isEmpty(flightFileWaybillVos)){
            vo.setWaybillVos(flightFileWaybillVos);
            for (FlightFileWaybillVo flightFileWaybillVo : flightFileWaybillVos) {
                List<HzArrTally> tallyList = hzArrTallyMapper.selectList(new QueryWrapper<HzArrTally>().eq("record_order_id", flightFileWaybillVo.getOrderId()));
                if (!CollectionUtils.isEmpty(tallyList)){
                    int sum = tallyList.stream().mapToInt(HzArrTally::getPieces).sum();
                    flightFileWaybillVo.setTallyQuantity(sum);
                    BigDecimal reduce = tallyList.stream().map(HzArrTally::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                    flightFileWaybillVo.setTallyWeight(reduce);
                }
                if (!flightFileWaybillVo.getReplenishBill().equals(1)){
                    flightFileWaybillVo.setMethod("提货");
                }
                if (flightFileWaybillVo.getTransferBill().equals(1)){
                    flightFileWaybillVo.setMethod("中转");
                }
            }
            int totalCabin = flightFileWaybillVos.stream().mapToInt(FlightFileWaybillVo::getCabinQuantity).sum();
            vo.setTotalCabin(totalCabin);
            BigDecimal totalCabinWeight = flightFileWaybillVos.stream().map(FlightFileWaybillVo::getCabinWeight).reduce(BigDecimal.ZERO,BigDecimal::add);
            vo.setTotalCabinWeight(totalCabinWeight);

            int tallySum = flightFileWaybillVos.stream().filter(e -> e.getTallyQuantity() != null).mapToInt(FlightFileWaybillVo::getTallyQuantity).sum();
            vo.setTotalTally(tallySum);
            BigDecimal tallyWeight = flightFileWaybillVos.stream().map(FlightFileWaybillVo::getTallyWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setTotalTallyWeight(tallyWeight);
        }
        return vo;
    }

    /**
     * 查询理货运单详情
     * @param query 查询条件
     * @return 理货详情数据
     */
    @Override
    public TallyWaybillVo getTallyInfo(FlightFileQuery query) {
        Long legId = query.getLegId();
        if (legId == null){
             legId = flightLoadMapper.selectLegId(query);
        }
        AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                .eq("waybill_code", query.getWaybillCode()).eq("type","ARR"));
        if (airWaybill == null){
            throw new CustomException("无当前运单信息");
        }
        TallyWaybillVo vo = recordOrderMapper.getTallyInfo(query.getWaybillCode(),legId);
        if (vo == null){
            throw new CustomException("该运单未生成理货任务");
        }
        List<TallyHistoryVo> historyVos = hzArrTallyMapper.selectTallyHistoryList(vo.getWaybillCode());
        if (!CollectionUtils.isEmpty(historyVos)){
            vo.setTallyNum(historyVos.size());
            int sum = historyVos.stream().mapToInt(TallyHistoryVo::getTallyQuantity).sum();
            vo.setTotalTallyQuantity(sum);
            BigDecimal reduce = historyVos.stream().map(TallyHistoryVo::getTallyWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setTotalTallyWeight(reduce);
            vo.setHistoryVos(historyVos);
        }
        return vo;
    }

    /**
     * 理货保存
     * @param vo 理货保存参数
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int tallySave(TallyWaybillVo vo) {
        AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                .eq("waybill_code", vo.getWaybillCode()).eq("type", "ARR"));
        if (airWaybill == null) {
            throw new CustomException("无当前运单信息");
        }
        TallyWaybillVo tallyWaybillVo = recordOrderMapper.getTallyInfo(vo.getWaybillCode(), vo.getLegId());
        if (tallyWaybillVo == null) {
            throw new CustomException("该运单未生成理货任务");
        }
        BigDecimal allWeight = vo.getTallyWeight();
        int allQuantity = vo.getTallyQuantity();
        LhVo lhVo = hzArrTallyMapper.selectTallyInfo(vo.getWaybillCode(),tallyWaybillVo.getFlightId());
        HzArrRecordOrder order = recordOrderMapper.selectById(vo.getTallyId());
        if (lhVo != null){
            allWeight = lhVo.getWeight().add(allWeight);
            allQuantity += lhVo.getQuantity();
        }
        if (allQuantity > airWaybill.getQuantity() || allWeight.compareTo(airWaybill.getWeight()) > 0) {
            throw new CustomException("理货件数或重量不能超过运单件数或重量");
        }
//        if (allQuantity > order.getCabinPieces() || allWeight.compareTo(order.getCabinWeight()) > 0){
//            throw new CustomException("理货件数或重量不能超过舱单件数或重量");
//        }
        HttpServletResponse response = ServletUtils.getResponse();
        //运单日志的新增
        WaybillLog waybillLog = waybillLogService.getWaybillLog(
                vo.getWaybillCode(), 0, SecurityUtils.getNickName(),
                vo.getTallyWeight().toString(), vo.getTallyQuantity().toString(), airWaybill.getFlightNo1(),
                vo, null, 0, null, new Date(),
                "理货完成,理货件数:" + vo.getTallyQuantity() + ",理货重量:" + vo.getTallyWeight(),
                "ARR", null);
        try {
            AirWaybill updateWaybill = new AirWaybill();
            updateWaybill.setId(airWaybill.getId());
            updateWaybill.setVersion(airWaybill.getVersion());
            updateWaybill.setStatus("tally_comp");
            updateWaybill.setUpdateTime(new Date());
            int i = airWaybillMapper.updateById(updateWaybill);
            if (i == 0){
                throw new CustomException("保存失败，运单已被其他操作修改，请刷新后重试");
            }

            HzArrTally hzArrTally = new HzArrTally();
            BeanUtils.copyProperties(vo, hzArrTally);
            hzArrTally.setRecordOrderId(vo.getTallyId());
            hzArrTally.setStatus("lh_comp");
            hzArrTally.setLegId(order.getLegId());
            hzArrTally.setCabinPieces(vo.getCabinQuantity());
            hzArrTally.setCabinWeight(vo.getCabinWeight());
            hzArrTally.setPieces(vo.getTallyQuantity());
            hzArrTally.setWeight(vo.getTallyWeight());
            hzArrTally.setTallyTime(new Date());
            int insert = hzArrTallyMapper.insert(hzArrTally);
            String sendAddress = sysConfigMapper.selectValue("arr.sendAddress");
            FlightInfo info = flightInfoMapper.selectFlightNo(order.getLegId());
            String token = getToken();
            MsgJsonVO msgJson = setMsgVo(info, sendAddress,"RCF");
            List<HzCableAddress> addressList = cableAddressMapper.selectAddressByAirportCode(Collections.singletonList(airWaybill.getDesPort()));
            StringBuilder builder = new StringBuilder();
            FSUJsonVO fsuJsonVO = new FSUJsonVO();
            fsuJsonVO.setDepAirport(airWaybill.getSourcePort());
            fsuJsonVO.setDesAirport(airWaybill.getDesPort());
            if(airWaybill.getWaybillCode().startsWith("AWBA")){
                String substring = airWaybill.getWaybillCode().substring(4);
                StringBuilder stringBuilder = new StringBuilder(substring);
                fsuJsonVO.setMawbNo(stringBuilder.insert(3, "-").toString());
            }else{
                String substring = airWaybill.getWaybillCode().substring(4);
                StringBuilder stringBuilder = new StringBuilder(substring);
                fsuJsonVO.setMawbNo(stringBuilder.insert(2, "-").toString());
            }
            fsuJsonVO.setPieces(vo.getTallyQuantity().toString());
            if (!order.getCabinPieces().equals(vo.getTallyQuantity())){
                fsuJsonVO.setShipmentDescriptionCode("P");
                fsuJsonVO.setTotalPieces(order.getCabinPieces().toString());
            }else {
                fsuJsonVO.setShipmentDescriptionCode("T");
            }
            List<StatusDetails> statusDetails = new ArrayList<>();
            StatusDetails details = new StatusDetails();
            details.setDeliveryName("NSK");
            details.setMovementAirport(info.getStartStation());
            details.setMovementArrivalAirport(info.getStartStation());
            details.setMovementCarrier(info.getAirWays());
            details.setMovementDate(LocalDateTime.now().format(DATE_FORMATTER));
            details.setMovementDepartureAirport(info.getStartStation());
            details.setMovementFlightNo(info.getFlightNo());
            details.setMovementTime(LocalDateTime.now().format(TIME_FORMATTER));
            details.setNotificationName("NSK");
            details.setPieces(vo.getQuantity().toString());
            details.setReceivedName("NSK");
            details.setReceivingCarrier(info.getAirWays());
            details.setReportingAirport(info.getStartStation());
            details.setReportingDate(LocalDateTime.now().format(DATE_FORMATTER));
            details.setReportingTime(LocalDateTime.now().format(TIME_FORMATTER));
            details.setShipmentDescriptionCode(fsuJsonVO.getShipmentDescriptionCode());
            details.setStatusCode("RCF");
            details.setTransferredName("NSK");
            if (airWaybill.getVolume() != null){
                details.setVolume(airWaybill.getVolume().toString());
                details.setVolumeUnit("MC");
            }
            details.setWeight(vo.getTallyWeight().toString());
            details.setWeightUnit("K");
            statusDetails.add(details);
            fsuJsonVO.setStatusDetails(statusDetails);
            msgJson.setMsgJson(JSON.toJSONString(fsuJsonVO));
            Map<String, List<HzCableAddress>> addressMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(addressList)){
                addressMap = addressList.stream().collect(Collectors.groupingBy(HzCableAddress::getAirportCode));
            }
            List<HzCableAddress> cableAddresses = addressMap.get(airWaybill.getDesPort());
            try {
                setInteractionType(cableAddresses,msgJson);
                restExchange(msgJson,token,builder);
                insertCableAndSendMsg(msgJson,info, builder,token);
            }catch (Exception e){
                log.error(CABLE_ERROR_MARKER, "运单"+vo.getWaybillCode()+"发送FSU-RCF报文失败：" + e.getMessage());
            }
            if (!order.getCabinPieces().equals(vo.getQuantity()) || !vo.getQuantity().equals(vo.getTallyQuantity())) {
                Wrong wrong = new Wrong();
                wrong.setWaybillCode(vo.getWaybillCode());
                wrong.setType("ARR");
                wrong.setStatus(0);
                wrong.setWrongType("理货数量异常");
                wrong.setRegisterTime(new Date());
                wrong.setCreateTime(new Date());
                //默认设置为不可提货办单
                wrong.setIsPickUp(0);
                wrong.setAgent(airWaybill.getShipper());
                wrong.setDeptId(airWaybill.getDeptId());
                wrong.setTallyId(hzArrTally.getId());
                wrongMapper.insert(wrong);
                //发送消息
                sendMessage(vo.getWaybillCode(), "理货数量异常", airWaybill.getShipper());
                statusDetails.clear();
                MsgJsonVO msgJsonVO = setMsgVo(info, sendAddress,"DIS");
                StringBuilder sb = new StringBuilder();
                StatusDetails statusDetail = new StatusDetails();
                statusDetail.setDiscrepancyCode("MSCA");
                statusDetail.setMovementAirport(info.getStartStation());
                statusDetail.setMovementArrivalAirport(info.getTerminalStation());
                statusDetail.setMovementCarrier(info.getAirWays());
                statusDetail.setMovementDate(LocalDateTime.now().format(DATE_FORMATTER));
                statusDetail.setMovementTime(LocalDateTime.now().format(TIME_FORMATTER));
                statusDetail.setMovementDepartureAirport(info.getStartStation());
                statusDetail.setMovementFlightNo(info.getFlightNo());
                statusDetail.setPieces(vo.getQuantity().toString());
                statusDetail.setReportingDate(DATE_FORMAT.format(new Date()));
                statusDetail.setShipmentDescriptionCode(fsuJsonVO.getShipmentDescriptionCode());
                statusDetail.setStatusCode("DIS");
                statusDetails.add(statusDetail);
                fsuJsonVO.setStatusDetails(statusDetails);
                msgJsonVO.setMsgJson(JSON.toJSONString(fsuJsonVO));
                try {
                    setInteractionType(cableAddresses,msgJsonVO);
                    restExchange(msgJsonVO,token,sb);
                    insertCableAndSendMsg(msgJsonVO, info, sb, token);
                }catch (Exception e){
                    log.error(CABLE_ERROR_MARKER, "运单"+vo.getWaybillCode()+"发送FSU-DIS报文失败：" + e.getMessage());
                }
            }
            // 全部件数重量都理货完成时，修改运单所有不正常货邮件的 is_over、is_pick_up, 自动处理不设置is_pick_up_time,
            if (order.getCabinPieces().equals(allQuantity) && allWeight.compareTo(order.getCabinWeight()) == 0) {
                LambdaUpdateWrapper<Wrong> updateWrongLuw = Wrappers.<Wrong>lambdaUpdate()
                        .eq(Wrong::getWaybillCode, order.getWaybillCode())
                        .eq(Wrong::getType, "ARR")
                        .set(Wrong::getIsOver, 1)
                        .set(Wrong::getStatus, 3)
                        .set(Wrong::getIsPickUp, 1);
                int isSuccess = wrongMapper.update(null, updateWrongLuw);
                if (!SqlHelper.retBool(isSuccess)) {
                    log.error("处理运单{}所有不正常货邮件数据异常", order.getWaybillCode());
                }
            }
            //这里是更新进港运单费用的理货id todo根据录单id走
            List<HzArrItem> item = itemMapper.selectList(new QueryWrapper<HzArrItem>()
                    .eq("waybill_code", vo.getWaybillCode())
                    .eq("order_id", vo.getTallyId()));
            for (HzArrItem arrItem : item) {
                arrItem.setTallyId(hzArrTally.getId());
                itemMapper.updateById(arrItem);
            }
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作成功" + "," +
                            "code:" + response.getStatus() + "," +
                            "data:" + insert));
            return insert;
        } catch (Exception e) {
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" + "," +
                            "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        } finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }

    private void insertCableAndSendMsg(MsgJsonVO vo, FlightInfo info, StringBuilder builder,String token) {
        if (StringUtils.isEmpty(vo.getSendType())){
            return;
        }
        List<String> sendTypes = Arrays.asList(vo.getSendType().split(","));
        if (!sendTypes.contains("FD")){
            sendTypes.add("FD");
        }
        for (String sendType : sendTypes) {
            HzCable cable = new HzCable();
            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("ddHHmm");
            cable.setSerialNo(SerialNumberGenerator.generateSerialNumber());
            cable.setCableNo(sdf.format(date));
            cable.setCableTime(date);
            cable.setCreateBy("系统");
            cable.setIsSend(1);
            cable.setIsAuto(1);
            cable.setType("FSU");
            cable.setVersion("12");
            cable.setPriority("QD");
            cable.setCableAddress(String.join(",", vo.getOrigin()));
            if (StringUtils.isNotEmpty(vo.getAddress())){
                cable.setReceiveAddress(String.join(",", vo.getAddress()));
            }
            cable.setFlightNo(info.getAirWays() + info.getFlightNo());
            cable.setFlightDate(info.getExecDate());
            cable.setContent(builder.toString());
            hzCableMapper.insert(cable);
            ForwardOriginMsgVO msgVO = new ForwardOriginMsgVO();
            msgVO.setOriginMsg(builder.toString());
            msgVO.setMsgType("FSU");
            msgVO.setSendType(sendType);
            msgVO.setReceiveAddress("-");
            if ("FD".equals(sendType)) {
                if (vo.getAddress() != null){
                    msgVO.setReceiveAddress(String.join(",",vo.getAddress()));
                }
            }
            if ("MAIL".equals(sendType)){
                msgVO.setMailAddress(String.join(",",vo.getReceiveMailAddress()));
            }
            if ("FTP".equals(sendType)){
                msgVO.setReceiveFtpAddress(ftpAddress);
                msgVO.setReceiveFtpFolder(vo.getReceiveFtpFolder());
            }
            if ("MQ".equals(sendType)){
                msgVO.setReceiveMQQueue(vo.getReceiveMQQueue());
            }
            msgVO.setSendAddress(String.join(",", vo.getOrigin()));
            msgVO.setPriority("QD");
            httpService.sendCable(msgVO, cable, token);
        }
    }

    @Async
    public void sendMessage(String waybillCode, String s, String agentCompany) {
        String message = "    运单"+waybillCode+"不正常货邮"+s+"  \n" +
                "运单："+ waybillCode +"不正常，不支持类型"+ s + "请及时处理。";
        SocketMessageVo vo = new SocketMessageVo();
        vo.setMessage(message);
        vo.setType(6);
        vo.setDeptId(sysDeptMapper.selectDeptIdByDeptName(agentCompany));

        Message mes = new Message();
        mes.setContent(message);
        mes.setCreateTime(new Date());
        mes.setIsHandle(0);
        mes.setPostId(SecurityUtils.getDeptId());
        mes.setTitle("不正常货邮提醒");
        messageMapper.insert(mes);
        waybillMessageProducer.send(vo);
    }

    /**
     * app进港理货航班列表
     * @param query 查询条件
     * @return 航班列表
     */
    @Override
    public AppFlightVo appFlightList(FlightFileQuery query) {
        AppFlightVo flightVo = new AppFlightVo();
        Page<AppFlightListVo> page = new Page<>(query.getCurrent(), query.getSize());
        Page<AppFlightListVo> vo = flightInfoMapper.appFlightList(page, query);
        for (AppFlightListVo appFlightVo : vo.getRecords()) {
            List<FlightLoad> flightLoads = flightLoadMapper.selectList(new QueryWrapper<FlightLoad>()
                    .eq("flight_id", appFlightVo.getFlightId()));
            if (!CollectionUtils.isEmpty(flightLoads)){
                 List<Long> legIds = flightLoads.stream().map(FlightLoad::getId).collect(Collectors.toList());
                 if (!CollectionUtils.isEmpty(legIds)){
                     Integer waybillNum = recordOrderMapper.selectCount(new QueryWrapper<HzArrRecordOrder>().in("leg_id",legIds));
                     appFlightVo.setWaybillNum(waybillNum);
                 }
            }
        }
        Integer totalNum = flightInfoMapper.selectTotalCount(query);
        flightVo.setTotalNum(totalNum);
        query.setTallyStatus("未理货");
        Integer noTallyNum = flightInfoMapper.selectTallyInfoCount(query);
        flightVo.setNoTallyNum(noTallyNum);
        query.setTallyStatus("理货完成");
        Integer tallyCompNum = flightInfoMapper.selectTallyInfoCount(query);
        flightVo.setTallyCompNum(tallyCompNum);
        flightVo.setList(vo);
        return flightVo;
    }

    /**
     * app进港理货运单列表
     * @param flightId 航班id
     * @return 运单列表
     */
    @Override
    public AppWaybillVo appWaybillList(Long flightId) {
        AppWaybillVo vo = new AppWaybillVo();
        FlightInfo flightInfo = flightInfoMapper.selectById(flightId);
        if (flightInfo != null){
            vo.setExecDate(flightInfo.getExecDate());
            vo.setFlightNo(flightInfo.getAirWays() + flightInfo.getFlightNo());
            List<FlightLoad> flightLoads = flightLoadMapper.selectList(new QueryWrapper<FlightLoad>()
                    .eq("flight_id", flightId));
            if (!CollectionUtils.isEmpty(flightLoads)){
                List<Long> legIds = flightLoads.stream().map(FlightLoad::getId).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(legIds)){
                    List<AppWaybillListVo> list = recordOrderMapper.selectWaybillList(legIds);
                    if (!CollectionUtils.isEmpty(list)){
                        vo.setList(list);
                    }
                }
            }
        }
        return vo;
    }

    /**
     * app理货完成
     * @param flightId 航班id
     * @return 结果
     */
    @Override
    public int appTallyComp(Long flightId) {
        FlightInfo flightInfo = flightInfoMapper.selectById(flightId);
        flightInfo.setTallyStatus("理货完成");
        return flightInfoMapper.updateById(flightInfo);
    }

    @Override
    public String shipperForAgentCode(String agentCode) {
        return sysDeptMapper.selectShipper(agentCode);
    }

    @Override
    public int tallyUpdate(TallyHistoryVo vo) {
        HttpServletResponse response = ServletUtils.getResponse();
        WaybillLog waybillLog = new WaybillLog();
        try {

            HzArrTally hzArrTally = hzArrTallyMapper.selectById(vo.getId());
            if (!"lh_comp".equals(hzArrTally.getStatus())) {
                throw new CustomException("该运单已办理提货,修改失败");
            }
            vo.setTallyTime(new Date());

            //运单日志的新增
            waybillLog = waybillLogService.getWaybillLog(
                    hzArrTally.getWaybillCode(), 0, SecurityUtils.getNickName(),
                    vo.getWeight().toString(), vo.getQuantity().toString(), null,
                    vo, null, 0, null, new Date(),
                    "修改理货历史,理货件数:" + vo.getTallyQuantity() + ",理货重量:" + vo.getTallyWeight(),
                    "ARR", hzArrTally.getUld());

            int i = hzArrTallyMapper.updateTally(vo);
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作成功" +  "," +
                            "code:" + response.getStatus() + "," +
                            "data:" + i));
            return i;
        }catch (Exception e){
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" +  "," +
                            "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        }finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }

    @Override
    public String agentCodeForShipper(String shipper) {
        return sysDeptMapper.selectAgentcode(shipper);
    }



    /**
     * 从list中获取部门信息
     * @param deptId 部门id
     * @param list 部门集合
     * @return 部门信息
     */
    private SysDept findDeptById(Long deptId, List<SysDept> list) {
        return list.stream()
                .filter(dept -> dept.getDeptId().equals(deptId))
                .findFirst()
                .orElse(null);
    }

    /**
     * 从list中获取部门信息
     * @param deptName 部门id
     * @param list 部门集合
     * @return 部门信息
     */
    private SysDept findDeptByName(String deptName, List<SysDept> list) {
        return list.stream()
                .filter(dept -> dept.getDeptName().equals(deptName))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据当前登录人获取父级公司
     */
    public Long getParentId(SysDept dept,List<SysDept> list){
        if (dept.getParentId() == 0) {
            return dept.getDeptId();
        } else {
            // 继续查找父部门
            SysDept parentDept = findDeptById(dept.getParentId(),list);
            return getParentId(parentDept,list);
        }
    }

    private MsgJsonVO setMsgVo(FlightInfo info,String sendAddress,String type){
        MsgJsonVO vo = new MsgJsonVO();
        vo.setCarrier(info.getAirWays());
        vo.setDepartureStation(info.getStartStation());
        vo.setMsgType("FSU");
        vo.setNextStation(info.getTerminalStation());
        vo.setOperationNode(type);
        vo.setOperationStation("KWE");
        if (StringUtils.isNotEmpty(sendAddress)) {
            vo.setOrigin(sendAddress.split(","));
        }else {
            vo.setOrigin(new String[]{"KWEFDCN"});
        }
        vo.setPriority(new String[]{"QD"});
        vo.setSourceId("44162409105767715");
        vo.setUniqueId("44162409105767715");
        vo.setMsgVersion("12");
        BaseCarrier baseCarrier = carrierMapper.selectByCode(info.getAirWays());
        if (baseCarrier != null){
            vo.setWaybillPrefix(baseCarrier.getPrefix());
        }
        return vo;
    }

    private void setAWROrRctMsg(FlightInfo info, String token, MsgJsonVO msgJsonVO, String weight, String quantity, AirWaybill airWaybill) {
        List<HzCableAddress> addressList = cableAddressMapper.selectAddressByAirportCode(Collections.singletonList(airWaybill.getDesPort()));
        StringBuilder sb = new StringBuilder();
        FSUJsonVO fsuJsonVO = new FSUJsonVO();
        fsuJsonVO.setDepAirport(airWaybill.getSourcePort());
        fsuJsonVO.setDesAirport(airWaybill.getDesPort());
        if(airWaybill.getWaybillCode().startsWith("AWBA")){
            String substring = airWaybill.getWaybillCode().substring(4);
            StringBuilder stringBuilder = new StringBuilder(substring);
            fsuJsonVO.setMawbNo(stringBuilder.insert(3, "-").toString());
        }else{
            String substring = airWaybill.getWaybillCode().substring(4);
            StringBuilder stringBuilder = new StringBuilder(substring);
            fsuJsonVO.setMawbNo(stringBuilder.insert(2, "-").toString());
        }
        fsuJsonVO.setPieces(quantity);
        if (!airWaybill.getQuantity().equals(Integer.valueOf(quantity))){
            fsuJsonVO.setShipmentDescriptionCode("P");
            fsuJsonVO.setTotalPieces(airWaybill.getQuantity().toString());
        }else {
            fsuJsonVO.setShipmentDescriptionCode("T");
        }
        fsuJsonVO.setWeight(weight);
        fsuJsonVO.setWeightUnit("K");
        List<StatusDetails> statusDetails = new ArrayList<>();
        StatusDetails statusDetail = new StatusDetails();
        statusDetail.setStatusCode(msgJsonVO.getOperationNode());
        statusDetail.setMovementCarrier(info.getAirWays());
        statusDetail.setMovementFlightNo(info.getFlightNo());
        statusDetail.setMovementDate(LocalDateTime.now().format(DATE_FORMATTER));
        statusDetail.setMovementTime(LocalDateTime.now().format(TIME_FORMATTER));
        statusDetail.setMovementAirport(info.getStartStation());
        statusDetail.setShipmentDescriptionCode(fsuJsonVO.getShipmentDescriptionCode());
        statusDetail.setPieces(quantity);
        statusDetail.setWeight(weight);
        statusDetail.setWeightUnit("K");
        statusDetail.setReportingDate(DATE_FORMAT.format(new Date()));
        statusDetails.add(statusDetail);
        fsuJsonVO.setStatusDetails(statusDetails);
        msgJsonVO.setMsgJson(JSON.toJSONString(fsuJsonVO));
        Map<String, List<HzCableAddress>> addressMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(addressList)){
            addressMap = addressList.stream().collect(Collectors.groupingBy(HzCableAddress::getAirportCode));
        }
        List<HzCableAddress> cableAddresses = addressMap.get(airWaybill.getDesPort());
        setInteractionType(cableAddresses,msgJsonVO);
        restExchange(msgJsonVO,token,sb);
        insertCableAndSendMsg(msgJsonVO,info,sb,token);
    }


    private List<AddressMsgVO> getFFMMsg(Long flightId, String sendAddress, String token){
        List<AddressMsgVO> msgVoList = new ArrayList<>();
        List<MsgFlightInfoVO> flightInfoVoList = flightInfoMapper.selectFlightInfoById(flightId,null);
        if (CollectionUtils.isEmpty(flightInfoVoList)) {
            return msgVoList;
        }
        MsgJsonVO vo = new MsgJsonVO();
        vo.setMsgType("FFM");
        vo.setOrigin(sendAddress.split(","));
        vo.setPriority(new String[]{"QD"});
        vo.setSourceId("1727215332084514819");
        vo.setUniqueId("1727215332084514819");
        List<String> desPortList = flightInfoVoList.stream().map(MsgFlightInfoVO::getNextStation).distinct().collect(Collectors.toList());
        List<HzCableAddress> desPortAddressList = cableAddressMapper.selectAddressByAirportCode(desPortList);
        List<String> carrierList = flightInfoVoList.stream().map(MsgFlightInfoVO::getCarrier).distinct().collect(Collectors.toList());
        List<HzCableAddress> carrierAddressList= cableAddressMapper.selectAddressByCarrier(carrierList);
        List<MsgFlightInfoVO> collect = flightInfoVoList.stream().distinct().collect(Collectors.toList());
        List<Long> list = flightLoadMapper.selectLegIdByVo(flightId);
        for (MsgFlightInfoVO infoVo : collect) {
            vo.setMsgVersion("5");
            vo.setIata("");
            vo.setClientShort("");
            vo.setOperationNode("");
            vo.setOperationStation("");
            vo.setCarrier(infoVo.getCarrier());
            vo.setFlightDate(infoVo.getFlightDate());
            vo.setDepartureStation(infoVo.getDepartureStation());
            vo.setFlightNo(infoVo.getFlightNo());
            vo.setNextStation(infoVo.getNextStation());
            vo.setFlightType(infoVo.getFlightType());
            FFMJsonVO ffmJsonVo = new FFMJsonVO();
            ffmJsonVo.setCarrier(infoVo.getCarrier());
            ffmJsonVo.setAircraftRegistration(infoVo.getAircraftRegistration());
            ffmJsonVo.setEtd(infoVo.getEtd());
            ffmJsonVo.setFlightNo(infoVo.getFlightNo());
            ffmJsonVo.setOriAirport(infoVo.getDepartureStation());
            PointOfUnloading unloading = new PointOfUnloading();
            unloading.setEta(infoVo.getEta());
            unloading.setArrAirport(infoVo.getNextStation());
            String date = infoVo.getStartSchemeTakeoffTime().toLocalDate().toString();
            unloading.setScheduledDepartureDate(date);
            String time = infoVo.getStartSchemeTakeoffTime().toLocalTime().format(TIME_FORMATTER);
            unloading.setScheduledDepartureTime(time);
            List<UldVO> uldVoList = new ArrayList<>();
            UldVO uldVO = new UldVO();
            uldVO.setUldType("");
            uldVO.setUldNum("");
            uldVO.setUldOwner(infoVo.getCarrier());
            uldVO.setUldNo("");
            List<ConsignmentDetail> detailList = recordOrderMapper.selectDetailList(list);
            if (!CollectionUtils.isEmpty(detailList)) {
                for (ConsignmentDetail detail : detailList) {
                    if (StringUtils.isEmpty(detail.getGoodsName())){
                        detail.setGoodsName("-");
                    }
                    String mawbNo = detail.getMawbNo();
                    if(mawbNo.startsWith("AWBA")){
                        String substring = mawbNo.substring(4);
                        StringBuilder stringBuilder = new StringBuilder(substring);
                        detail.setMawbNo(stringBuilder.insert(3, "-").toString());
                    }else{
                        String substring = mawbNo.substring(4);
                        StringBuilder stringBuilder = new StringBuilder(substring);
                        detail.setMawbNo(stringBuilder.insert(2, "-").toString());
                    }

                    detail.setShipmentDescriptionCode("T");
                    detail.setTotalPieces(null);
                    if (StringUtils.isNotEmpty(detail.getOsi())) {
                        detail.setShc(detail.getOsi().split(","));
                    }
                    if (StringUtils.isNotEmpty(detail.getVolume())) {
                        detail.setVolumeUnit("MC");
                    }
                    if (StringUtils.isNotEmpty(detail.getWeight())) {
                        detail.setWeightUnit("KG");
                    }
                }
                uldVO.setConsignmentDetail(detailList);
            }
            uldVoList.add(uldVO);
            unloading.setUld(uldVoList);
            ffmJsonVo.setPointOfUnloading(Collections.singletonList(unloading));
            vo.setMsgJson(JSON.toJSONString(ffmJsonVo));
            List<List<HzCableAddress>> targets = new ArrayList<>();
            Map<String, List<HzCableAddress>> desPortAddressMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(desPortAddressList)){
                desPortAddressMap = desPortAddressList.stream().collect(Collectors.groupingBy(HzCableAddress::getAirportCode));
            }
            List<HzCableAddress> desPortAddresses = desPortAddressMap.get(infoVo.getNextStation());
            targets.add(desPortAddresses);

            Map<String, List<HzCableAddress>> carrierAddressMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(carrierAddressList)){
                carrierAddressMap = carrierAddressList.stream().collect(Collectors.groupingBy(HzCableAddress::getAirlinesCode));
            }
            List<HzCableAddress> carrierAddresses = carrierAddressMap.get(infoVo.getCarrier());
            targets.add(carrierAddresses);
            for (List<HzCableAddress> target : targets) {
                StringBuilder sb = new StringBuilder();
                AddressMsgVO msgVo = new AddressMsgVO();
                setInteractionType(target,vo);
                if (vo.getAddress() != null){
                    restExchange(vo,token,sb);
                    msgVo.setMsg(sb.toString());
                    msgVo.setMsgType("FFM");
                    msgVo.setSendType(vo.getSendType());
                    msgVo.setAddress(vo.getAddress());
                    msgVo.setReceiveMailAddress(vo.getReceiveMailAddress());
                    msgVo.setReceiveFtpAddress(ftpAddress);
                    msgVo.setReceiveFtpFolder(vo.getReceiveFtpFolder());
                    msgVo.setReceiveMQQueue(vo.getReceiveMQQueue());
                    msgVoList.add(msgVo);
                }
            }
        }
        return msgVoList;
    }


    public void setInteractionType(List<HzCableAddress> cableAddresses, MsgJsonVO vo) {
        List<String> sendType = new ArrayList<>();
        if (cableAddresses == null || cableAddresses.isEmpty()) {
            sendType.add("FD");
            vo.setSendType("FD");
            vo.setAddress(new String[]{"-"});
            return;
        }
        List<String> sendTypeCn = cableAddresses.stream()
                .map(HzCableAddress::getInteractionTypes)
                .filter(StringUtils::isNotEmpty)
                .map(s -> s.split(","))
                .flatMap(Arrays::stream)
                .map(String::trim)
                .distinct()
                .collect(Collectors.toList());
        for (String type : sendTypeCn) {
            switch (type) {
                case "民航局报文":
                    sendType.add("FD");
                    break;
                case "邮箱报文":
                    sendType.add("MAIL");
                    break;
                case "FTP收发报文":
                    sendType.add("FTP");
                    break;
                case "rabbitmq收发报文":
                    sendType.add("MQ");
                    break;
                default:
                    break;
            }
        }
        if (!sendType.contains("FD")){
            sendType.add("FD");
        }
        vo.setSendType(String.join(",", sendType));
        List<String> emailAddresses = new ArrayList<>();
        List<String> ftpList = new ArrayList<>();
        List<String> mqQueueList = new ArrayList<>();
        List<String> caacAddresses = new ArrayList<>();
        for (HzCableAddress cableAddress : cableAddresses) {
            List<String> interactionTypeList = new ArrayList<>();
            if (StringUtils.isNotEmpty(cableAddress.getInteractionTypes())) {
                interactionTypeList = Arrays.asList(cableAddress.getInteractionTypes().split(","));
            }
            if (!CollectionUtils.isEmpty(interactionTypeList)) {
                for (String type : interactionTypeList) {
                    switch (type) {
                        case "邮箱报文":
                            emailAddresses.add(cableAddress.getEmailAddress());
                            break;
                        case "FTP收发报文":
                            ftpList.add(cableAddress.getFtpList());
                            break;
                        case "民航局报文":
                            caacAddresses.add(cableAddress.getCaacAddress());
                            break;
                        case "rabbitmq收发报文":
                            mqQueueList.add(cableAddress.getMqQueue());
                            break;
                        default:
                            break;
                    }
                }
            }
        }
        if (!emailAddresses.isEmpty()) {
            vo.setReceiveMailAddress(emailAddresses.toArray(new String[0]));
        }
        if (!ftpList.isEmpty()) {
            vo.setReceiveFtpFolder(String.join(",",ftpList));
        }
        if (!mqQueueList.isEmpty()) {
            vo.setReceiveMQQueue(String.join(",",mqQueueList));
        }
        if (!caacAddresses.isEmpty()) {
            vo.setAddress(caacAddresses.toArray(new String[0]));
        }else {
            vo.setAddress(new String[]{"-"});
        }
    }

    private String getToken(){
        System.out.println("*********调用登录接口获取token*********");
        String token = "";
        HttpHeaders headers = setHeaders();
        HttpEntity<?> httpEntity = new HttpEntity<>(headers);
        ResponseEntity<JSONObject> exchange = restTemplate.exchange(loginUrl + account, HttpMethod.GET, httpEntity, JSONObject.class);
        if (exchange.getBody() != null) {
            if (!"0".equals(exchange.getBody().getString("code"))) {
                log.error(CABLE_ERROR_MARKER, "登录失败：" + exchange.getBody().getString("message"));
            }
            JSONObject data = exchange.getBody().getJSONObject("data");
            token = data.getString("token");
        }
        return token;
    }

    private HttpHeaders setHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType("application/json;charset=UTF-8"));
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        headers.add("Accept-Charset", "UTF-8");
        return headers;
    }

    private void restExchange(MsgJsonVO vo,String token,StringBuilder sb){
        HttpHeaders header = setHeaders();
        header.add("X-Access-Token", token);
        HttpEntity<?> httpEntity = new HttpEntity<>(vo, header);
        System.out.println("参数：" + JSON.toJSONString(vo));
        ResponseEntity<JSONObject> exchange = restTemplate.exchange(getMsg, HttpMethod.POST, httpEntity, JSONObject.class);
        if (exchange.getBody() != null) {
            if (!"0".equals(exchange.getBody().getString("code"))) {
                throw new CustomException(exchange.getBody().getString("msg"));
            }
            JSONObject data = exchange.getBody().getJSONObject("data");
            String msgContent = data.getString("msgContent");
            sb.append(msgContent).append("\n");
        }
    }
}
