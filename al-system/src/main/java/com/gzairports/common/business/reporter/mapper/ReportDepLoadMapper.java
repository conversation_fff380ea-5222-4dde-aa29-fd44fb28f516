package com.gzairports.common.business.reporter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.reporter.domain.ReportDepLoad;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by david on 2025/3/3
 */
@Mapper
public interface ReportDepLoadMapper extends BaseMapper<ReportDepLoad> {
    void deleteBatch(@Param("reportIds") List<Long> reportIds);

    int insertBatchSomeColumn(List<ReportDepLoad> loadList);
}
