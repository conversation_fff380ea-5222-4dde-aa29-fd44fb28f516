package com.gzairports.common.rabbitmq;

import com.alibaba.fastjson2.JSON;
import com.gzairports.common.securitySubmit.domain.SecuritySubmitSendFirst;
import com.gzairports.common.securitySubmit.domain.SecuritySubmitSendSecond;
import com.gzairports.wl.websocket.WlWebSocketServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Desc 发送安检申报信息
 * @create 2024-11-15 09:54
 **/

@Component
public class SecurityProducer {
    @Autowired
    private RabbitTemplate rabbitTemplate;

    private static final Logger log = LoggerFactory.getLogger(SecurityProducer.class);

    /**
     * <AUTHOR>
     * @description 发送消息到mq (安检申报代理人提交的消息提示)
     * @date 2024/10/22
     */
    public void sendFirst(SecuritySubmitSendFirst vo) {
        log.info("运单"+vo.getWaybillCode()+"推送安检信息(物流):" + JSON.toJSONString(vo));
        rabbitTemplate.convertAndSend("cargo.security","securityData", JSON.toJSONString(vo));
    }

    /**
     * <AUTHOR>
     * @description 发送消息到mq (货站提交的消息提示)
     * @date 2024/10/22
     */
    public void sendSecond(SecuritySubmitSendSecond vo) {
        log.info("运单"+vo.getWaybillCode()+"推送安检信息(货站):" + JSON.toJSONString(vo));
        rabbitTemplate.convertAndSend("cargo.security","securityData", JSON.toJSONString(vo));
    }

    /**
     * <AUTHOR>
     * @description 推送运单数据到mq(运单各种流程)
     * type : 类型
     * 1.货站推送收运数据
     * 2.货站完成航班/定时任务自动关闭航班
     * 3.物流平台选择换单，将原单号、新单号推送货检系统
     * 4.代理人不正常货邮选择换单
     * 5.货运系统完成退单
     * 6.代理人重复提交品名清单附件
     */
    public <T> void sendOtherWaybill(T vo, String waybillCode, String remark){
        log.info(remark + "运单"+waybillCode + JSON.toJSONString(vo));
        rabbitTemplate.convertAndSend("cargo.waybill","waybillData", JSON.toJSONString(vo));
    }

}
