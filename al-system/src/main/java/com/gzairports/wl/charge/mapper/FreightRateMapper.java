package com.gzairports.wl.charge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.wl.charge.domain.FreightRate;
import com.gzairports.wl.charge.domain.query.FreightRateQuery;
import com.gzairports.wl.charge.domain.vo.FreightRateVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 公布运价Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@Mapper
public interface FreightRateMapper extends BaseMapper<FreightRate> {

    /**
     * 查询公布运价列表
     *
     * @param query 查询参数
     * @return 公布运价列表
     */
    List<FreightRateVO> selectListByQuery(FreightRateQuery query);

    /**
     * 查询公布运价
     *
     * @param id 公布运价主键
     * @return 公布运价
     */
    FreightRateVO selectRateById(Long id);

    /**
     * 新增公布运价
     *
     * @param rate 公布运价
     */
    void insertFreightRate(FreightRate rate);

    /**
     * 修改公布运价
     *
     * @param rate 公布运价
     * @return 结果
     */
    int updateFreightRate(FreightRate rate);

    /**
     * 删除公布运价
     *
     * @param id 公布运价主键
     * @return 结果
     */
    int removeFreightRate(Long id);

    /**
     * 根据货物代码合所属单位查询公布运价
     *
     * @param deptId 所属单位
     * @return 结果
     */
    FreightRate selectByCode(@Param("deptId") Long deptId, @Param("billTypeCode") String billTypeCode);
}
