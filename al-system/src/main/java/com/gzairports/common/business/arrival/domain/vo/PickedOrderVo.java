package com.gzairports.common.business.arrival.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 已提货办单数据
 *
 * <AUTHOR>
 * @date 2024-06-07
 */
@Data
public class PickedOrderVo {

    /** 总金额 */
    private BigDecimal totalMoney;

    /** 已支付金额 */
    private BigDecimal payMoney;

    /** 已结算金额 */
    private BigDecimal settleMoney;

    /** 已提货办单运单数据 */
    private List<PickedUpVo> vos;
}
