package com.gzairports.hz.business.departure.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 报文数据收集运单查询参数
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@Data
public class CableMawbQuery {

    /** 运单号 */
    private String waybillCode;

    /** 航班号 */
    private String flightNo;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date flightDate;

    /** 特货代码 */
    private String specialCode;

    /** 航司 */
    private String airCompany;

    /** IN/OUT */
    private String inOut;

    /** 不正常类型 */
    private String wrongType;

    /** 类型 */
    private String type;

    /** 查询日期 */
    private String queryDate;
}
