package com.gzairports.wl.log.service.impl;


import com.gzairports.wl.log.domain.WlOperLog;
import com.gzairports.wl.log.mapper.WlOperLogMapper;
import com.gzairports.wl.log.service.WlOperLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 物流日志 服务层处理
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@Service
public class WlOperLogServiceImpl implements WlOperLogService {

    @Autowired
    private WlOperLogMapper logMapper;

    /**
     * 新增操作日志
     *
     * @param operLog 操作日志对象
     */
    @Override
    public void insertOperLog(WlOperLog operLog) {
        logMapper.insertWlOperLog(operLog);
    }

    /**
     *  查询物流操作日志
     * @param title 模块标题
     * @param id 查询日志id
     * @return 操作日志
     */
    @Override
    public List<WlOperLog> log(String title, Long id) {
        return logMapper.selectByQuery(title,id);
    }

    /**
     *  查询物流操作日志详情
     * @param logId 操作日志id
     * @return 操作日志
     */
    @Override
    public WlOperLog logInfo(Long logId) {
        return logMapper.logInfo(logId);
    }
}
