package com.gzairports.hz.business.departure.domain.query;

import lombok.Data;

import java.util.Date;

/**
 * 冷藏登记查询参数
 *
 * <AUTHOR>
 * @date 2024-07-12
 */
@Data
public class HzColdRegisterQuery {

    /** 运单号 */
    private String waybillCode;

    /** 冷库 */
    private String coldStore;

    /** 状态 */
    private Integer status;

    /** 入库时间 */
    private Date startTime;

    /** 入库时间 */
    private Date endTime;

    /** 支付状态 */
    private Integer payStatus;

    /** 发货代理人 */
    private String agentCode;

    /** 进出港类型 ARR进港 DEP出港 */
    private String type;

    /** 当前登录人部门id */
    private Long deptId;
}
