package com.gzairports.web.controller.basedata;


import com.gzairports.common.annotation.Log;
import com.gzairports.common.basedata.service.IAbnormalTypeService;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.common.basedata.domain.BaseAbnormalType;
import com.gzairports.common.basedata.domain.query.AbnormalTypeQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 不正常类型Controller
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@RestController
@RequestMapping("/base/abnormalType")
@Api(tags = "不正常类型数据接口")
public class AbnormalTypeController extends BaseController {

    @Autowired
    private IAbnormalTypeService abnormalTypeService;

    /**
     * 查询不正常类型列表
     */
//    @PreAuthorize("@ss.hasPermi('base:abnormalType:specialCodeList')")
    @GetMapping("/abnormalTypeList")
    @ApiOperation(value = "查询不正常类型列表")
    public TableDataInfo specialCodeList(AbnormalTypeQuery query)
    {
        startPage();
        List<BaseAbnormalType> list = abnormalTypeService.selectAbnormalTypeList(query);
        return getDataTable(list);
    }

    /**
     * 导出不正常类型
     */
    @PreAuthorize("@ss.hasPermi('base:abnormalType:exportAbnormalType')")
    @Log(title = "导出不正常类型", businessType = BusinessType.EXPORT)
    @PostMapping("/exportAbnormalType")
    @ApiOperation(value = "导出不正常类型")
    public void exportAbnormalType(HttpServletResponse response, AbnormalTypeQuery query)
    {
        List<BaseAbnormalType> list = abnormalTypeService.selectAbnormalTypeList(query);
        ExcelUtil<BaseAbnormalType> util = new ExcelUtil<BaseAbnormalType>(BaseAbnormalType.class);
        util.exportExcel(response, list, "不正常类型");
    }
}
