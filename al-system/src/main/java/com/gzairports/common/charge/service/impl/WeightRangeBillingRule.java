package com.gzairports.common.charge.service.impl;

import com.gzairports.common.business.arrival.domain.HzArrItem;
import com.gzairports.common.business.arrival.domain.ItemDetail;
import com.gzairports.common.charge.domain.HzChargeItemRule;
import com.gzairports.common.charge.domain.vo.BillRuleVo;
import com.gzairports.common.charge.service.BillingRule;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalTime;
import java.util.List;

/**
 * 按重量区间计费规则
 * <AUTHOR>
 * @date 2024-08-03
 */
public class WeightRangeBillingRule implements BillingRule {

    @Override
    public String className() {
        return "WeightRangeBillingRule.class";
    }

    @Override
    public BillRuleVo calculateFee(List<HzChargeItemRule> rule, BigDecimal weight, Integer quantity, ItemDetail item) {
        if (quantity == null || quantity == 0){
            quantity = 1;
        }
        BillRuleVo vo = new BillRuleVo();
        BigDecimal divide = weight.divide(new BigDecimal(quantity), 2, RoundingMode.HALF_UP);
        BigDecimal rate = new BigDecimal(0);
        for (HzChargeItemRule itemRule : rule) {
            if (divide.compareTo(new BigDecimal(itemRule.getStartRange())) >= 0 && divide.compareTo(new BigDecimal(itemRule.getEndRange())) <= 0){
                rate = itemRule.getUnitPrice();
            }
        }
        BigDecimal multiply = rate.multiply(new BigDecimal(quantity));
        vo.setTotalCharge(multiply);
        vo.setRate(rate);
        vo.setQuantity(String.valueOf(quantity));
        return vo;
    }
}
