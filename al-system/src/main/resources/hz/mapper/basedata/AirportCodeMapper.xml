<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.AirportCodeMapper">
    <insert id="insertAirportCode" parameterType="baseAirportCode">
        insert into base_airport_code
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="code != null and code != ''">code,</if>
            <if test="fourCode != null and fourCode != ''">four_code,</if>
            <if test="cityCode != null and cityCode != ''">city_code,</if>
            <if test="cityName != null and cityName != ''">city_name,</if>
            <if test="chineseName != null and chineseName != ''">chinese_name,</if>
            <if test="englishName != null and englishName != ''">english_name,</if>
            <if test="isCommon != null and isCommon != ''">is_common,</if>
            <if test="isDomestic != null and isDomestic != ''">is_domestic,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="code != null and code != ''">#{code},</if>
            <if test="fourCode != null and fourCode != ''">#{fourCode},</if>
            <if test="cityCode != null and cityCode != ''">#{cityCode},</if>
            <if test="cityName != null and cityName != ''">#{cityName},</if>
            <if test="chineseName != null and chineseName != ''">#{chineseName},</if>
            <if test="englishName != null and englishName != ''">#{englishName},</if>
            <if test="isCommon != null and isCommon != ''">#{isCommon},</if>
            <if test="isDomestic != null and isDomestic != ''">#{isDomestic},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>
    <update id="updateAirportCode" parameterType="baseAirportCode">
        update base_airport_code
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="fourCode != null and fourCode != ''">four_code = #{fourCode},</if>
            <if test="cityCode != null and cityCode != ''">city_code = #{cityCode},</if>
            <if test="cityName != null and cityName != ''">city_name = #{cityName},</if>
            <if test="chineseName != null and chineseName != ''">chinese_name = #{chineseName},</if>


            <if test="englishName != null">
                english_name = #{englishName},
            </if>
            <if test="englishName == null">
                english_name = null,
            </if>

            <if test="isCommon != null and isCommon != ''">is_common = #{isCommon},</if>
            <if test="isDomestic != null and isDomestic != ''">is_domestic = #{isDomestic},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null and createTime != ''">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="delAirportCode" parameterType="long">
        update base_airport_code set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <select id="selectAirportCodeList" resultType="com.gzairports.common.basedata.domain.BaseAirportCode">
        select id, code, four_code, city_code, city_name, chinese_name, english_name, is_common, is_domestic from base_airport_code
        <where>
        and is_del = 0
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="code != null  and code != ''"> and code like concat('%',#{code},'%')</if>
            <if test="fourCode != null and fourCode != ''"> and four_code like concat('%',#{fourCode},'%')</if>
            <if test="cityCode != null and cityCode != ''"> and city_code like concat('%',#{cityCode},'%')</if>
            <if test="cityName != null and cityName != ''"> and city_name like concat('%',#{cityName},'%')</if>
            <if test="chineseName != null and chineseName != ''"> and chinese_name like concat('%',#{chineseName},'%')</if>
            <if test="englishName != null and englishName != ''"> and english_name = #{englishName}</if>
            <if test="isDomestic != null and isDomestic != ''"> and is_domestic = #{isDomestic}</if>
        </where>
    </select>
    <select id="selectByCode" resultType="com.gzairports.common.basedata.domain.BaseAirportCode">
        select id, code, four_code, city_code, city_name, chinese_name, english_name, is_common, is_domestic from base_airport_code
        where code = #{code} and is_del = 0
    </select>
    <select id="selectCityByName" resultType="com.gzairports.common.basedata.domain.BaseAirportCode">
        select id, code, four_code, city_code, city_name, chinese_name, english_name, is_common, is_domestic from base_airport_code
        where chinese_name = #{departureCity} and is_del = 0
    </select>
</mapper>
