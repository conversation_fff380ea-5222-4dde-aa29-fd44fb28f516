package com.gzairports.web.controller.business.departure;

import com.gzairports.hz.business.departure.domain.HzDepGroupUld;
import com.gzairports.hz.business.departure.domain.HzDepGroupUldWaybill;
import com.gzairports.hz.business.departure.domain.HzDepHandover;
import com.gzairports.hz.business.departure.domain.query.GroupCargoQuery;
import com.gzairports.hz.business.departure.domain.vo.GroupCargoVo;
import com.gzairports.hz.business.departure.service.IGroupCargoService;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 组货调度Controller
 *
 * <AUTHOR>
 * @date 2024-07-03
 */
@RestController
@RequestMapping("/dep/groupCargo")
@Api(tags = "组货调度")
public class GroupCargoController extends BaseController {

    @Autowired
    private IGroupCargoService groupCargoService;

    /**
     * 查询组货调度列表
     */
//    @PreAuthorize("@ss.hasPermi('dep:groupCargo:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询组货调度列表")
    public TableDataInfo list(GroupCargoQuery query)
    {
        startPage();
        List<GroupCargoVo> list = groupCargoService.selectList(query);
        return getDataTable(list);
    }

    /**
     * 导出货组货调度列表
     */
    @PreAuthorize("@ss.hasPermi('dep:groupCargo:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出货组货调度列表")
    public void export(HttpServletResponse response, GroupCargoQuery query)
    {
        List<GroupCargoVo> list = groupCargoService.selectList(query);
        ExcelUtil<GroupCargoVo> util = new ExcelUtil<GroupCargoVo>(GroupCargoVo.class);
        util.exportExcel(response, list, "导出货组货调度数据");
    }

    /**
     * 查看详情
     */
    @PreAuthorize("@ss.hasPermi('dep:groupCargo:getInfo')")
    @GetMapping("/getInfo/{id}")
    @ApiOperation(value = "查看详情")
    public AjaxResult getInfo(@PathVariable("id") Long id){
        return AjaxResult.success(groupCargoService.getInfo(id));
    }

    /**
     * 板箱详情
     */
    @PreAuthorize("@ss.hasPermi('dep:groupCargo:groupDataInfo')")
    @GetMapping("/groupDataInfo/{id}")
    @ApiOperation(value = "板箱详情")
    public AjaxResult groupDataInfo(@PathVariable("id") Long id){
        return AjaxResult.success(groupCargoService.groupDataInfo(id));
    }

    /**
     * 未放板车货物
     */
    @PreAuthorize("@ss.hasPermi('dep:groupCargo:notLoadCargo')")
    @GetMapping("/notLoadCargo/{id}")
    @ApiOperation(value = "未放板车货物")
    public AjaxResult notLoadCargo(@PathVariable("id") Long id){
        return AjaxResult.success(groupCargoService.notLoadCargo(id));
    }


    /**
     * 指派
     */
    @PreAuthorize("@ss.hasPermi('dep:groupCargo:assign')")
    @GetMapping("/assign")
    @ApiOperation(value = "指派")
    public AjaxResult assign(@RequestParam Long flightLoadId,@RequestParam String username){
        return toAjax(groupCargoService.assign(flightLoadId,username));
    }

    /**
     * 站坪交接
     */
    @PreAuthorize("@ss.hasPermi('dep:groupCargo:handover')")
    @PostMapping("/handover")
    @ApiOperation(value = "站坪交接")
    public AjaxResult handover(@RequestBody HzDepHandover hzDepHandover){
        return toAjax(groupCargoService.handover(hzDepHandover));
    }

    /**
     * 加货
     */
    @PreAuthorize("@ss.hasPermi('dep:groupCargo:addCargo')")
    @GetMapping("/addCargo/{id}")
    @ApiOperation(value = "加货")
    public AjaxResult addCargo(@PathVariable("id") Long id){
        return AjaxResult.success(groupCargoService.addCargo(id));
    }

    /**
     * 手动添加根据运单号查询
     */
    @PreAuthorize("@ss.hasPermi('dep:groupCargo:handAdd')")
    @GetMapping("/handAdd/{waybillCode}")
    @ApiOperation(value = "手动添加根据运单号查询")
    public AjaxResult handAdd(@PathVariable("waybillCode") String waybillCode){
        return AjaxResult.success(groupCargoService.handAdd(waybillCode));
    }

    /**
     * 确认加货
     */
    @PreAuthorize("@ss.hasPermi('dep:groupCargo:confirmAdd')")
    @PostMapping("/confirmAdd")
    @ApiOperation(value = "确认加货")
    public AjaxResult confirmAdd(@RequestBody HzDepGroupUldWaybill groupUldWaybill){
        return toAjax(groupCargoService.confirmAdd(groupUldWaybill));
    }

    /**
     * 移动
     */
    @PreAuthorize("@ss.hasPermi('dep:groupCargo:move')")
    @PostMapping("/move")
    @ApiOperation(value = "移动")
    public AjaxResult move(@RequestBody HzDepGroupUldWaybill groupUldWaybill){
        return toAjax(groupCargoService.move(groupUldWaybill));
    }

    /**
     * 删除板车
     */
    @PreAuthorize("@ss.hasPermi('dep:groupCargo:deleteUld')")
    @GetMapping("/deleteUld/{id}")
    @ApiOperation(value = "删除板车")
    public AjaxResult deleteUld(@PathVariable("id") Long id){
        return toAjax(groupCargoService.deleteUld(id));
    }

    /**
     * 增加板车
     */
    @PreAuthorize("@ss.hasPermi('dep:groupCargo:addUld')")
    @PostMapping("/addUld")
    @ApiOperation(value = "增加板车")
    public AjaxResult addUld(@RequestBody HzDepGroupUld uld){
        return AjaxResult.success(groupCargoService.addUld(uld));
    }

    /**
     * 删除运单
     */
    @PreAuthorize("@ss.hasPermi('dep:groupCargo:deleteWaybill')")
    @GetMapping("/deleteWaybill/{id}")
    @ApiOperation(value = "删除运单")
    public AjaxResult deleteWaybill(@PathVariable("id") Long id){
        return toAjax(groupCargoService.deleteWaybill(id));
    }


    /**
     * 组货完成
     */
    @PreAuthorize("@ss.hasPermi('dep:groupCargo:groupFinish')")
    @GetMapping("/groupFinish/{id}")
    @ApiOperation(value = "组货完成")
    public AjaxResult groupFinish(@PathVariable("id") Long id){
        return toAjax(groupCargoService.groupFinish(id));
    }

    /**
     * 根据板车号查询板重
     */
    @PreAuthorize("@ss.hasPermi('dep:groupCargo:selectWeight')")
    @GetMapping("/selectWeight/{uld}")
    @ApiOperation(value = "根据板车号查询板重")
    public AjaxResult selectWeight(@PathVariable("uld") String uld){
        return AjaxResult.success(groupCargoService.selectWeight(uld));
    }

}
