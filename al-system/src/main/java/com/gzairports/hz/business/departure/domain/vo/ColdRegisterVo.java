package com.gzairports.hz.business.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 冷藏登记计算参数
 *
 * <AUTHOR>
 * @date 2024-08-15
 */
@Data
public class ColdRegisterVo {

    /** 运单号 */
    private String waybillCode;

    /** 冷库类型 */
    private String coldStore;

    /** 冷库开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date storeStartTime;

    /** 冷库结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date storeEndTime;

    /** 进出港类型 */
    private String type;

    /** 申请时长 */
    private Integer timeLen;
}
