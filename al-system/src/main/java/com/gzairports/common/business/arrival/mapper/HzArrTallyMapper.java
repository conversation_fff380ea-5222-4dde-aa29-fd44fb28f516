package com.gzairports.common.business.arrival.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.arrival.domain.HzArrTally;
import com.gzairports.common.business.arrival.domain.query.*;
import com.gzairports.common.business.arrival.domain.vo.*;
import com.gzairports.common.business.reporter.domain.ReportTally;
import com.gzairports.hz.business.arrival.domain.HzArrRecordOrder;
import com.gzairports.hz.business.arrival.domain.query.ArrInventoryQuery;
import com.gzairports.hz.business.arrival.domain.query.WaybillQuery;
import com.gzairports.hz.business.arrival.domain.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 进港理货记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-03
 */
@Mapper
public interface HzArrTallyMapper extends BaseMapper<HzArrTally> {

    /**
     * 查询未提货办单数据
     * @param query 查询条件
     * @return 未提货办单数据
     */
    List<NotPickedUpVo> notPickedUp(@Param("query") PickedUpQuery query,@Param("deptIdList") List<String> deptIdList,@Param("value") String value);

    /**
     * 查询该运单号未提货办单数据的件数重量
     * @param waybillCode 运单号
     * @return 该运单号未提货办单数据的件数重量
     */
    NotPickedUpVo selectNotPickedUp(String waybillCode);

    /**
     * 查询该运单号理货的数据
     * @param waybillCode 运单号
     * @return 该运单号已理货的件数重量
     */
    NotPickedUpVo selectTallyData(String waybillCode);

    /**
     * 查询运单列表
     * @param legId 查询条件
     * @param waybillIds 运单id条件
     * @param status 状态条件
     * @return 运单列表
     */
    List<FlightFileWaybillVo> waybillList(@Param("legId") Long legId, @Param("waybillIds") List<Long> waybillIds, @Param("status") String status);

    /**
     * 查询运单理货历史
     * @param waybillCode 运单号
     * @return 理货历史
     */
    List<TallyHistoryVo> selectTallyHistoryList(String waybillCode);

    /**
     * 根据航班id查询航班运单数量
     * @param flightId 航班id
     * @return 结果
     */
    Integer selectWaybillCount(Long flightId);

    /**
     * 批量挑单数据
     * @param query 查询条件
     * @return 结果
     */
    List<PickUpVo> batchPickUp(@Param("query") PickUpQuery query,@Param("deptIdList") List<String> deptIdList,@Param("value") String value);

    /**
     * 加入挑单列表
     * @param tallyIds 理货id集合
     * @return 办单数据
     */
    List<PickUpVo> selectPickUpVoList(@Param("tallyIds") Long[] tallyIds);

    /**
     * 挑单数据查询
     * @param query 查询条件
     * @return 挑单数据
     */
    List<PickUpVo> selectPickUpVo(@Param("query") PickUpClickQuery query,@Param("deptIdList") List<String> deptIdList,@Param("value") String value);

    /**
     * 自动办单机办单
     * @param query 查询条件
     * @return 运单数据
     */
    AutoOrderVo autoOrder(AutoOrderQuery query);

    /**
     * 查询理货数据
     * @param query 查询条件
     * @return 结果
     */
    List<WaybillQueryVo> selectQueryList(WaybillQuery query);

    /**
     * 运单明细理货数据查询
     * @param waybillCode 运单号
     * @return 结果
     */
    List<LhVo> selectLhVos(String waybillCode);

    /**
     * 运单明细库存数据查询
     * @param waybillCode 运单号
     * @return 结果
     */
    List<KcVo> selectKcList(String waybillCode);

    /**
     * 库存管理列表查询
     * @param query 查询参数
     * @return 库存列表
     */
    List<InventoryListVo> selectInventoryList(ArrInventoryQuery query);

    /**
     * 散客H5提货办单（提交并查询运单）
     * @param query 查询参数
     * @return 运单列表
     */
    List<RetailVo> selectRetailList(AutoOrderQuery query);

    /**
     * 散客在线提货办单
     * @param tallyId 理货id
     * @return 结果
     */
    OrderProVo selectOrderData(Long tallyId);

    /**
     * 根据理货id查询入库时间
     * @param tallyId 理货id
     * @return 入库时间
     */
    Date selectTimeById(Long tallyId);

    int updateTally(TallyHistoryVo vo);

    List<NotBillListVo> notBillList(@Param("query") BillOfLadingQuery query,@Param("deptIdList") List<String> deptIdList,@Param("value") String value);

    HzArrRecordOrder selectId(@Param("desPort") String desPort,@Param("waybillCode") String waybillCode);

    LhVo selectWaybillInfo(String waybillCode);

    List<ReportTally> selectReportTallyList(@Param("waybillCodes") List<String> waybillCodes);

    LhVo selectTallyInfo(@Param("waybillCode") String waybillCode,@Param("flightId") Long flightId);

    List<NotPickedUpVo> selectTallyList(@Param("waybillCodeList") List<String> waybillCodeList);

    /**
     * 查询运单理货总件数重量
     * @param waybillCode 运单号
     * @return 理货数据
     */
//    HzArrTally selectTallyData(String waybillCode);
}
