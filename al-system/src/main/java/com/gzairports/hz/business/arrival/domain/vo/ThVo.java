package com.gzairports.hz.business.arrival.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 运单明细提货数据
 * <AUTHOR>
 * @date 2024-07-25
 */
@Data
public class ThVo {

    /** 提货时间 */
    private Date outTime;

    /** 办单件数 */
    private Integer orderQuantity;

    /** 办单重量 */
    private BigDecimal orderWeight;

    /** 提货件数 */
    private Integer pieces;

    /** 提货重量 */
    private BigDecimal weight;

    /** 提货码 */
    private String pickUpCode;

    /** 操作人 */
    private String customerName;

    /** 备注 */
    private String remark;
}
