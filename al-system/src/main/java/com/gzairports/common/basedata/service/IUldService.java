package com.gzairports.common.basedata.service;


import com.gzairports.common.basedata.domain.BaseCargoUld;
import com.gzairports.common.basedata.domain.BaseCargoUldForSave;
import com.gzairports.common.basedata.domain.query.CargoUldQuery;

import java.util.List;

/**
 * 集装器管理Service接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
public interface IUldService {

    /**
     * 查询集装器管理列表
     *
     * @param query 查询参数
     * @return 集装器管理列表
     */
    List<BaseCargoUld> ULDList(CargoUldQuery query);

    /**
     * 新增集装器
     *
     * @param uld 集装器
     * @return 结果
     */
    int addULD(BaseCargoUld uld);

    /**
     * 修改集装器
     *
     * @param uld 集装器
     * @return 结果
     */
    int editULD(BaseCargoUld uld);

    /**
     * 删除集装器
     * @param id 集装器id
     * @return 结果
     */
    int removeUld(Long id);

    /**
     * 集装器详情
     * @param id 集装器id
     * @return 结果
     */
    BaseCargoUld getInfo(Long id);

    /**
     * 导入集装器
     * @param ulds 集装器列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    String importUlds(List<BaseCargoUld> ulds, boolean updateSupport);

    /**
     * 保存uld
     * @param uld uld数据
     * @return 结果
     */
    int saveUld(BaseCargoUldForSave uld);

    /**
     * 查询当前uld使用状态
     * @param uld uld数据
     * @return 是否
     */
    boolean selectStatus(BaseCargoUldForSave uld);
}
