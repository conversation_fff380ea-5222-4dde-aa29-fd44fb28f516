package com.gzairports.common.basedata.domain.query;

import lombok.Data;

import java.util.List;

/**
 * 货站代码映射查询参数
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Data
public class CargoCodeMappingQuery {

    /** 主键id集合 */
    private List<Long> ids;

    /** 货站货品代码 */
    private String cargoCode;

    /** 航空公司货品代码 */
    private String airCargoCode;

    /** 航空公司代码 */
    private String airCode;

    /** 所属单位 */
    private Long deptId;

}
