package com.gzairports.common.basedata.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import com.gzairports.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 代理人配置对象 base_agent
 * 
 * <AUTHOR>
 * @date 2024-06-17
 */
public class BaseAgent extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 所属公司 */
    private Long deptId;

    /** 代理人 */
    @Excel(name = "代理人名称", width = 32)
    private String agent;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", dateFormat = "yyyy-MM-dd HH:mm:ss", width = 24)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 代理人出港简称 */
    @Excel(name = "代理人出港简称")
    private String agentAbb;

    /** 代理人进港简称 */
    @Excel(name = "代理人进港简称")
    private String agentAbbIn;

    /** 银联账号 */
    @Excel(name = "银联账号")
    private String unionPayAccount;

    /** 结算方式  0 预授权 1 余额 2 线下*/
    private Integer settleMethod;

    /** 结算方式 */
    @Excel(name = "结算方式")
    @TableField(exist = false)
    private String settleMethodStr;

    /** 结算方式 */
    @Excel(name = "已预付款总金额")
    @TableField(exist = false)
    private BigDecimal payCostSum;

    /** 结算方式 */
    @Excel(name = "已结算总金额")
    @TableField(exist = false)
    private BigDecimal settleCostSum;

    /** 结算方式 */
    @Excel(name = "已充值总金额")
    @TableField(exist = false)
    private BigDecimal rechargeCostSum;

    /** 账号余额 */
    @Excel(name = "账号余额")
    private BigDecimal balance;

    /**
     * 账号余额 大于零-1 等于零-0
     */
    @TableField(exist = false)
    private Integer isHadBalance;

    /** 余额支付预警值 */
    @Excel(name = "余额支付预警值")
    private BigDecimal balanceAlert;

    /** 电子印章 */
    private String sealUrl;

    /** 可办理提货代理人集合 */
    private String deptIds;

    /** 支付方式 */
    private Integer payMethod;

    public String getDeptIds() {
        return deptIds;
    }

    public void setDeptIds(String deptIds) {
        this.deptIds = deptIds;
    }

    /** 是否删除 0 否 1 是 */
    private Integer isDel;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setDeptId(Long deptId)
    {
        this.deptId = deptId;
    }

    public Long getDeptId()
    {
        return deptId;
    }

    public void setAgent(String agent) 
    {
        this.agent = agent;
    }

    public String getAgent() 
    {
        return agent;
    }
    public void setAgentAbb(String agentAbb) 
    {
        this.agentAbb = agentAbb;
    }

    public void setAgentAbbIn(String agentAbbIn)
    {
        this.agentAbbIn = agentAbbIn;
    }

    public String getAgentAbb() 
    {
        return agentAbb;
    }
    public String getAgentAbbIn()
    {
        return agentAbbIn;
    }
    public void setUnionPayAccount(String unionPayAccount)
    {
        this.unionPayAccount = unionPayAccount;
    }

    public String getUnionPayAccount()
    {
        return unionPayAccount;
    }
    public void setSettleMethod(Integer settleMethod) 
    {
        this.settleMethod = settleMethod;
    }

    public Integer getSettleMethod() 
    {
        return settleMethod;
    }
    public void setBalance(BigDecimal balance) 
    {
        this.balance = balance;
    }

    public BigDecimal getBalance() 
    {
        return balance;
    }
    public void setBalanceAlert(BigDecimal balanceAlert) 
    {
        this.balanceAlert = balanceAlert;
    }

    public void setPayMethod(Integer payMethod)
    {
        this.payMethod = payMethod;
    }
    public Integer getPayMethod()
    {
        return payMethod;
    }

    public BigDecimal getBalanceAlert() 
    {
        return balanceAlert;
    }
    public void setIsDel(Integer isDel) 
    {
        this.isDel = isDel;
    }

    public Integer getIsDel() 
    {
        return isDel;
    }

    public void setSealUrl(String sealUrl)
    {
        this.sealUrl = sealUrl;
    }

    public String getSealUrl()
    {
        return sealUrl;
    }

    public void setSettleMethodStr(String settleMethodStr)
    {
        this.settleMethodStr = settleMethodStr;
    }

    public String getSettleMethodStr()
    {
        return settleMethodStr;
    }

    public void setPayCostSum(BigDecimal payCostSum)
    {
        this.payCostSum = payCostSum;
    }

    public BigDecimal getPayCostSum()
    {
        return payCostSum;
    }

    public void setSettleCostSum(BigDecimal settleCostSum)
    {
        this.settleCostSum = settleCostSum;
    }

    public BigDecimal getSettleCostSum()
    {
        return settleCostSum;
    }

    public void setRechargeCostSum(BigDecimal rechargeCostSum)
    {
        this.rechargeCostSum = rechargeCostSum;
    }

    public BigDecimal getRechargeCostSum()
    {
        return rechargeCostSum;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getIsHadBalance() {
        return isHadBalance;
    }

    public void setIsHadBalance(Integer isHadBalance) {
        this.isHadBalance = isHadBalance;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deptId", getDeptId())
            .append("agent", getAgent())
            .append("agentAbb", getAgentAbb())
            .append("agentAbbIn", getAgentAbbIn())
            .append("unionPayAccount", getUnionPayAccount())
            .append("settleMethod", getSettleMethod())
            .append("payMethod", getPayMethod())
            .append("balance", getBalance())
            .append("balanceAlert", getBalanceAlert())
            .append("remark", getRemark())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .append("isDel", getIsDel())
            .append("sealUrl", getSealUrl())
            .append("settleMethodStr", getSettleMethodStr())
            .append("payCostSum", getPayCostSum())
            .append("settleCostSum", getSettleCostSum())
            .append("rechargeCostSum", getRechargeCostSum())
            .toString();
    }
}
