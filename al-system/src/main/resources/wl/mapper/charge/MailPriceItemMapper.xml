<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.wl.charge.mapper.MailPriceItemMapper">

    <update id="removeAirItem">
        update wl_sf_mail_price_item set is_del = 1 where id = #{id}
    </update>
    <select id="selectRateItemList" resultType="com.gzairports.wl.charge.domain.vo.MailPriceItemVo">
        select wfri.id, wafr.rate_name, wfri.clause_name, wfri.flight_no, wfri.carrier_code, wfri.departure_city, wfri.destination_city,
               wfri.minimum_freight, wfri.consign_name, wfri.mail_type, wfri.face_minimum_freight, wfri.storage_shipping_notes,
               wfri.settlement_notes, wfri.remarks
        from wl_sf_mail_price_item wfri
                 left join wl_sf_mail_price wafr on wafr.id = wfri.rate_id
        where wfri.is_del = 0 and wfri.rate_id = #{id}
    </select>
    <select id="selectAirList" resultType="com.gzairports.wl.charge.domain.vo.MailPriceItemVo">
        select wfri.id, wafr.rate_name, wfri.clause_name, wfri.flight_no, wfri.carrier_code, wfri.departure_city, wfri.destination_city,
        wfri.minimum_freight, wfri.consign_name, wfri.mail_type, wfri.face_minimum_freight, wfri.storage_shipping_notes,
        wfri.settlement_notes, wfri.remarks,wfri.clause_name,
        wafr.effective_date as startTime,wafr.expiration_date as endTime
        from wl_sf_mail_price_item wfri
        left join wl_sf_mail_price wafr on wafr.id = wfri.rate_id
        <where>
            and wfri.is_del = 0 and wfri.rate_id = #{id}
            <if test="rateName != null  and rateName != ''"> and wafr.rate_name = #{rateName}</if>
            <if test="rateType != null"> and wfri.rate_type = #{rateType}</if>
            <if test="applicableDocuments != null  and applicableDocuments != ''"> and wfri.applicable_documents = #{applicableDocuments}</if>
            <if test="effectiveDate != null"> and wfri.effective_date = #{effectiveDate}</if>
            <if test="expirationDate != null"> and wfri.expiration_date = #{expirationDate}</if>
            <if test="destinationCity != null"> and wfri.destination_city = #{destinationCity}</if>
        </where>
    </select>
</mapper>