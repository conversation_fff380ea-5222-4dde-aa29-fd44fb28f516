package com.gzairports.hz.business.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lan
 * @Desc: 出港费用管理导出结算明细
 * @create: 2025-01-17 15:01
 **/
@Data
public class ChargeSettleDetailVo {
    /** 结算时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结算时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date settleTime;

    /** 结算航班日期 */
    @Excel(name = "结算航班日期")
    private String settleFlightDate;

    /** 结算航班 */
    @Excel(name = "结算航班")
    private String settleFlightNo;

    /** 结算已出港件数 */
    @Excel(name = "结算已出港件数",cellType = Excel.ColumnType.NUMERIC)
    private Integer settleDepQuantity;

    /** 实际出港重量 */
    @Excel(name = "实际出港重量",cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal settleDepWeight;

    /** 结算计费重量 */
    @Excel(name = "结算计费重量")
    private String quantity;

    /** 结算出港费用 */
    @Excel(name = "结算出港费用",cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal settleCost;
}
