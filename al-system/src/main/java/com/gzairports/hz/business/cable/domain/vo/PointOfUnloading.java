package com.gzairports.hz.business.cable.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2025-04-07
 */
@Data
public class PointOfUnloading {

    /** 到达机场 */
    private String arrAirport;

    /** 散货 */
    private List<ConsignmentDetail> bulk;

    /** 预计到达时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date eta;

    /** 空载代码 */
    private String nilCargoCode;

    /** 计划起飞日期 */
    private String scheduledDepartureDate;

    /** 计划起飞时间 */
    private String scheduledDepartureTime;

    /** 板箱信息 */
    private List<UldVO> uld;
}
