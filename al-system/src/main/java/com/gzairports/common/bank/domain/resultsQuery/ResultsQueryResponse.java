package com.gzairports.common.bank.domain.resultsQuery;

import lombok.Data;

import java.io.Serializable;

/**
 * @author: lan
 * @Desc: 支付结果查询返回字段
 * @create: 2025-03-18 13:58
 **/

@Data
public class ResultsQueryResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 回显商户名 */
    private String merchantName;

    /** 商户订单号 50 Y
     * 原交易商户订单号
     */
    private String merchantSeq;

    /** 交易金额 */
    private Long amount;

    /** 订单详情 */
    private String orderInfo;

    /** 凭证号 */
    private String voucherNo;

    /** 银行流水号 */
    private String bankTradeNo;

    /** 内部流水号 */
    private String bankOrderNo;

    /** 交易结果 */
    private String tradeStatus;

    /** 保留域 */
    private String remark;

    /** 参考号 */
    private String refNo;

    /** 批次号 */
    private String batchNo;

    /** 卡类型 */
    private Integer cardType;

    /** 卡号 */
    private String cardNo;

    /** 发卡行行号 */
    private String cbCode;

    /** 发卡行行名 */
    private String cardName;

    /** 交易手续费 */
    private Double fee;

    /** 交易类型 */
    private Integer tradeType;

    /** 银联终端号 */
    private String cupTermId;

    /** 设备序列号 */
    private String cupTsamNo;

    /** 其他信息 */
    private String centerInfo;

    /** 第三方支付下单返回订单号 */
    private String centerSeqId;

    /** 数币营销信息 */
    private String redInfo;

}
