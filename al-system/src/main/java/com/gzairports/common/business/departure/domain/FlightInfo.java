package com.gzairports.common.business.departure.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.time.LocalDateTime;
import java.util.Date;


/**
 * 航班信息对象 bus_flight_info
 *
 * <AUTHOR>
 * @date 2023-09-18
 */
@Data
@TableName("all_flight_info")
public class FlightInfo
{
    private static final long serialVersionUID = 1L;

    /** 航班主键id */
    @TableId(value = "flight_id")
    @JSONField(name = "flightid")
    private Long flightId;

    /** 关联航班id */
    @JSONField(name = "assoicatedid")
    private String assoicatedId;

    /** 执行日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "执行日期", width = 30, dateFormat = "yyyy-MM-dd")
    @JSONField(name = "execdate")
    private Date execDate;

    /** 航司id */
    @JSONField(name = "airwaysid")
    private Long airwaysId;

    /** $column.columnComment */
    @JSONField(name = "agency")
    private String agency;

    /** 航司 */
    @JSONField(name = "airways")
    private String airWays;

    /** 航班号 */
    @Excel(name = "航班号")
    @JSONField(name = "flightno")
    private String flightNo;

    /** $column.columnComment */
    @JSONField(name = "bigflightno")
    private String bigFlightNo;

    /** 进出港 */
    @JSONField(name = "isoffin")
    private String isOffin;

    /** 任务码 */
    @JSONField(name = "task")
    private String task;

    /** 任务中文 */
    @JSONField(name = "taskcn")
    private String taskcn;

    /** $column.columnComment */
    @JSONField(name = "craftnoid")
    private Long craftNoId;

    /** 机号 */
    @JSONField(name = "craftno")
    private String craftNo;

    /** 机型 */
    @JSONField(name = "crafttype")
    private String craftType;

    /** 是否宽体机（0或空为否，1是） */
    @JSONField(name = "wideis")
    private String wideis;

    /** 机位 */
    @JSONField(name = "craftsite")
    private String craftSite;

    /** $column.columnComment */
    @JSONField(name = "craftnoold")
    private String craftNoOld;

    /** $column.columnComment */
    @JSONField(name = "craftsitetype")
    private String craftSiteType;

    /** 航班对外发布状态 */
    @JSONField(name = "providingstate")
    @Excel(name = "航班对外发布状态")
    private String providingState;

    /** 航班状态 */
    @JSONField(name = "abnormalstate")
    private String abnormalState;

    /** 航班状态中文 */
    @JSONField(name = "abnormalreasoncn")
    private String abnormalReasonCn;

    /** $column.columnComment */
    @JSONField(name = "startstationid")
    private Long startStationId;

    /** 始发站站三字码 */
    @JSONField(name = "startstation")
    private String startStation;

    /** 始发站中文 */
    @JSONField(name = "startstationcn")
    private String startStationCn;

    /** 计飞 */
    @JSONField(name = "startschemetakeofftime")
    private LocalDateTime startSchemeTakeoffTime;

    /** 预飞 */
    @JSONField(name = "startalteratetakeofftime")
    private LocalDateTime startAlterateTakeoffTime;

    /** 实飞 */
    @JSONField(name = "startrealtakeofftime")
    private LocalDateTime startRealTakeoffTime;

    /** $column.columnComment */
    @JSONField(name = "terminalstationid")
    private Integer terminalStationId;

    /** 目的站三字码 */
    @JSONField(name = "terminalstation")
    private String terminalStation;

    /** 目的站中文 */
    @Excel(name = "目的站中文")
    @JSONField(name = "terminalstationcn")
    private String terminalStationCn;

    /** 计降 */
    @JSONField(name = "terminalschemelandintime")
    private LocalDateTime terminalSchemeLandInTime;

    /** 预降 */
    @JSONField(name = "terminalreallandintime")
    private LocalDateTime terminalReallAndInTime;

    /** 实降 */
    @JSONField(name = "terminalalteratelandintime")
    private LocalDateTime terminalAlteratelAndInTime;

    /** 前站起飞时间 */
    @JSONField(name = "fronttakeofftime")
    private LocalDateTime frontTakeoffTime;

    /** 是否VIP */
    @JSONField(name = "isvip")
    private String isVip;

    /** 跑道 */
    @JSONField(name = "runway")
    private String runWay;

    /** 登机口 */
    @JSONField(name = "resgate")
    private String resgate;

    /** 行李转盘 */
    @JSONField(name = "resbelt")
    private String resbelt;

    /** 共享航班 */
    @JSONField(name = "shareflight")
    private String shareFlight;

    /** 经停站 */
    @JSONField(name = "sharevia")
    private String sharevia;

    /** 经停站中文 */
    @JSONField(name = "shareviacn")
    private String shareviaCn;

    /** 全航线 */
    @JSONField(name = "airlinefull")
    private String airLineFull;

    /** 全航线中文 */
    @JSONField(name = "airlinefullcn")
    private String airLineFullCn;

    /** 短航线 */
    @JSONField(name = "airlineshort")
    private String airLineShort;

    /** 短航线中文 */
    @JSONField(name = "airlineshortcn")
    private String airLineShortCn;

    /** $column.columnComment */
    @JSONField(name = "flighttype")
    private String flightType;

    /** $column.columnComment */
    @JSONField(name = "terminalinter")
    private String terminalinter;

    /** 航站楼 */
    @JSONField(name = "terminal")
    private String terminal;

    /** 航空公司中文 */
    @JSONField(name = "airwayscn")
    private String airWaysCn;

    /** 全航线简称 */
    @JSONField(name = "airlinefulljccn")
    private String airLineFullJccn;

    /** 短航线简称中文 */
    @JSONField(name = "airlineshortjccn")
    private String airLineShortJccn;

    /** 航线中文 */
    @JSONField(name = "airlinecn")
    private String airLineCn;

    /** 航线中文简称 */
    @JSONField(name = "airlinejccn")
    private String airLineJccn;

    /** 航线 */
    @JSONField(name = "airline")
    private String airLine;

    /** 异常原因 */
    @JSONField(name = "abnormalreason")
    private String abnormalReason;

    /** $column.columnComment */
    @JSONField(name = "nextstationschemelandintime")
    private Date nextStationSchemeLandInTime;

    /** $column.columnComment */
    @JSONField(name = "nextstationreallandintime")
    private Date nextStationReallAndInTime;

    /** 航班唯一标识 */
    @JSONField(name = "ffid")
    private String ffid;

    /** 属性 */
    @JSONField(name = "attribute")
    private String attribute;

    /** 属性中文 */
    @JSONField(name = "attributecn")
    private String attributeCn;

    /** 开始入库时间 */
    private String startStorageTime;

    /** 结束入库时间 */
    private String endStorageTime;

    /** 消息类型 */
    private String msgType;

    /** 是否手动排班 */
    private Integer isManual;

    /** 是否短信通知 */
    private Integer isSms;

    /** 航班操作 */
    private String flightOper;

    /** 理货次数 */
    private Integer tallyNum;

    /** 理货状态 */
    private String tallyStatus;

    /** 是否配载 */
    private Integer isPre;

    /** 生成舱单时间 */
    private Date cabinDate;

    /** 是否可制单 */
    private Integer isCreate;

    /** 是否航班完成 0 否 1 是 */
    private Integer isComp;

    /** 是否已结算 0 否 1 是 */
    private Integer isSettle;

    /** 航班完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date compTime;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("flightId", getFlightId())
            .append("assoicatedId", getAssoicatedId())
            .append("execDate", getExecDate())
            .append("airwaysId", getAirwaysId())
            .append("agency", getAgency())
            .append("airWays", getAirWays())
            .append("flightNo", getFlightNo())
            .append("bigFlightNo", getBigFlightNo())
            .append("isOffin", getIsOffin())
            .append("task", getTask())
            .append("taskcn", getTaskcn())
            .append("craftNoId", getCraftNoId())
            .append("craftNo", getCraftNo())
            .append("craftType", getCraftType())
            .append("wideis", getWideis())
            .append("craftSite", getCraftSite())
            .append("craftNoOld", getCraftNoOld())
            .append("craftSiteType", getCraftSiteType())
            .append("providingState", getProvidingState())
            .append("abnormalState", getAbnormalState())
            .append("abnormalReasonCn", getAbnormalReasonCn())
            .append("startStationId", getStartStationId())
            .append("startStation", getStartStation())
            .append("startStationCn", getStartStationCn())
            .append("startSchemeTakeoffTime", getStartSchemeTakeoffTime())
            .append("startAlterateTakeoffTime", getStartAlterateTakeoffTime())
            .append("startRealTakeoffTime", getStartRealTakeoffTime())
            .append("terminalStationId", getTerminalStationId())
            .append("terminalStation", getTerminalStation())
            .append("terminalStationCn", getTerminalStationCn())
            .append("terminalSchemeLandInTime", getTerminalSchemeLandInTime())
            .append("terminalReallAndInTime", getTerminalReallAndInTime())
            .append("terminalAlteratelAndInTime", getTerminalAlteratelAndInTime())
            .append("frontTakeoffTime", getFrontTakeoffTime())
            .append("isVip", getIsVip())
            .append("runWay", getRunWay())
            .append("resgate", getResgate())
            .append("resbelt", getResbelt())
            .append("shareFlight", getShareFlight())
            .append("sharevia", getSharevia())
            .append("shareviaCn", getShareviaCn())
            .append("airLineFull", getAirLineFull())
            .append("airLineFullCn", getAirLineFullCn())
            .append("airLineShort", getAirLineShort())
            .append("airLineShortCn", getAirLineShortCn())
            .append("flightType", getFlightType())
            .append("terminalinter", getTerminalinter())
            .append("terminal", getTerminal())
            .append("airWaysCn", getAirWaysCn())
            .append("airLineFullJccn", getAirLineFullJccn())
            .append("airLineShortJccn", getAirLineShortJccn())
            .append("airLineCn", getAirLineCn())
            .append("airLineJccn", getAirLineJccn())
            .append("airLine", getAirLine())
            .append("abnormalReason", getAbnormalReason())
            .append("nextStationSchemeLandInTime", getNextStationSchemeLandInTime())
            .append("nextStationReallAndInTime", getNextStationReallAndInTime())
            .append("ffid", getFfid())
            .append("attribute", getAttribute())
            .append("attributeCn", getAttributeCn())
            .append("isCreate", getIsCreate())
            .append("isComp", getIsComp())
            .append("isSettle", getIsSettle())
            .append("compTime", getCompTime())
            .toString();
    }
}
