package com.gzairports.common.basedata.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.basedata.domain.BaseCarrier;
import com.gzairports.common.basedata.domain.query.CarrierQuery;
import com.gzairports.wl.reporter.domain.vo.CarrierDataVO;
import com.gzairports.wl.ticket.domain.vo.TicketSourceVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 承运人管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Mapper
public interface CarrierMapper extends BaseMapper<BaseCarrier> {

    /**
     * 查询承运人管理列表
     *
     * @param query 查询参数
     * @return 承运人管理列表
     */
    List<BaseCarrier> selectCarrierList(CarrierQuery query);

    /**
     * 新增承运人
     *
     * @param carrier 承运人
     * @return 结果
     */
    int insertCarrier(BaseCarrier carrier);

    /**
     * 修改承运人
     *
     * @param carrier 承运人
     * @return 结果
     */
    int updateCarrier(BaseCarrier carrier);

    /**
     * 删除承运人
     *
     * @param ids 承运人id集合
     * @return 结果
     */
    int delCarrier(Long[] ids);

    /**
     * 根据code查询承运人数据
     * @param code 承运人code
     * @return 结果
     */
    BaseCarrier selectByCode(String code);

    /**
     * 根据航司查询logo
     * @param carrier1 航司
     * @return logo地址
     */
    String selectLogoByCode(String carrier1);

    List<TicketSourceVo> getTicketSource(String type);

    List<CarrierDataVO> selectIsShipmentList();

    List<String> selectCodeForSamePrefixByCode(String code);
}
