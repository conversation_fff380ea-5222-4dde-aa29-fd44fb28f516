package com.gzairports.common.charge.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.gzairports.common.charge.domain.HzChargeItemRule;
import lombok.Data;

import java.util.List;

/**
 * 货站收费规则项目表
 *
 * <AUTHOR>
 * @date 2024-10-21
 */
@Data
public class HzItemRuleVo {

    /** 主键id */
    private Long id;

    /** 收费规则id */
    private Long chargeRuleId;

    /** 计费规则名称 */
    private String ruleName;

    /** 收费项目里的规则名称 */
    private String itemRuleName;

    /** 执行类名称 */
    private String className;

    /** 优先级 */
    private Integer priority;

    /** 货品大类 */
    private String category;

    /** 货品大类集合 */
    @TableField(exist = false)
    private List<String> categoryList;

    /** 货物品名 */
    private String cargoName;

    /** 货物品名集合 */
    @TableField(exist = false)
    private List<String> cargoNames;

    /** 代理人 */
    private String noCharge;

    /** 代理人集合 */
    @TableField(exist = false)
    private List<String> noCharges;

    /** 运单前缀 */
    private String prefix;

    /** 运单前缀集合 */
    @TableField(exist = false)
    private List<String> prefixs;

    /** 跨航司运输 */
    private Integer crossAir;

    /** 转南航 */
    private Integer isSouth;

    /** 退货 */
    private Integer isExit;

    /** 计费规则列表 */
    private List<HzChargeItemRule> list;
}
