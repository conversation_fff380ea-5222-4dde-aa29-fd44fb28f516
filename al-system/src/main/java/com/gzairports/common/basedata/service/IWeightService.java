package com.gzairports.common.basedata.service;


import com.gzairports.common.basedata.domain.BaseWeight;
import com.gzairports.common.basedata.domain.query.WeightUnitQuery;

import java.util.List;

/**
 * 多重量单位Service接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
public interface IWeightService {

    /**
     * 查询重量单位列表
     *
     * @param query 查询参数
     * @return 重量单位列表
     */
    List<BaseWeight> selectWeightUnitList(WeightUnitQuery query);

    /**
     * 导入重量单位
     *
     * @param weights 重量单位数据列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    String importWeight(List<BaseWeight> weights, boolean updateSupport);

    /**
     * 新增重量单位
     *
     * @param weight 重量单位
     * @return 结果
     */
    int addWeight(BaseWeight weight);

    /**
     * 修改重量单位
     *
     * @param weight 重量单位
     * @return 结果
     */
    int editWeight(BaseWeight weight);

    /**
     * 删除重量单位
     *
     * @param ids 重量单位id集合
     * @return 结果
     */
    int delWeight(Long[] ids);
}
