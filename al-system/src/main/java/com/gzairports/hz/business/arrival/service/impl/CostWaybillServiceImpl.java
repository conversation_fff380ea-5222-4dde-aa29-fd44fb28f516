package com.gzairports.hz.business.arrival.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gzairports.common.business.arrival.domain.*;
import com.gzairports.common.business.arrival.domain.vo.ArrItemVo;
import com.gzairports.common.business.arrival.domain.vo.HzArrItemVo;
import com.gzairports.common.business.arrival.mapper.*;
import com.gzairports.common.business.departure.mapper.MawbMapper;
import com.gzairports.common.core.domain.entity.SysDept;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.hz.business.arrival.domain.query.CostWaybillQuery;
import com.gzairports.hz.business.arrival.domain.query.TallyWaybillKey;
import com.gzairports.hz.business.arrival.domain.vo.*;
import com.gzairports.hz.business.arrival.mapper.CostWaybillMapper;
import com.gzairports.hz.business.arrival.service.ICostWaybillService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 进港费用管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-12
 */
@Service
public class CostWaybillServiceImpl implements ICostWaybillService {

    @Autowired
    private CostWaybillMapper costWaybillMapper;

    @Autowired
    private AllPickUpOutMapper allPickUpOutMapper;

    @Autowired
    private MawbMapper mawbMapper;

    @Autowired
    private HzArrItemMapper itemMapper;

    @Autowired
    private PickUpMapper pickUpMapper;

    @Autowired
    private PickUpWaybillMapper pickUpWaybillMapper;

    @Autowired
    private HzArrTallyMapper hzArrTallyMapper;

    @Resource
    private SysDeptMapper deptMapper;

    /**
     * 列表查询数据
     * */
    @Override
    public List<CostWaybillVo> selectList(CostWaybillQuery query) {
        Integer offset = null;
        if(StringUtils.isNotNull(query.getPageNum()) && StringUtils.isNotNull(query.getPageSize())){
            offset = (query.getPageNum() - 1) * query.getPageSize();
        }
        query.setSettleUserArr(StrUtil.isNotBlank(query.getSettleUser()) ? query.getSettleUser().split(",") : null);
        query.setSettleUserAbbArr(StrUtil.isNotBlank(query.getSettleUserAbb()) ? query.getSettleUserAbb().split(",") : null);
        List<CostWaybillVo> costWaybillVoList = costWaybillMapper.selectList(query,offset);
        if(CollectionUtil.isEmpty(costWaybillVoList)){
            return Collections.emptyList();
        }
        List<TallyWaybillKey> pickUpWaybillPairs = costWaybillVoList.stream()
                .map(vo -> new TallyWaybillKey(vo.getId(), vo.getWaybillCode()))
                .distinct()
                .collect(Collectors.toList());
        List<AllPickUpOut> allPickUpOuts = allPickUpOutMapper.selectByKey(pickUpWaybillPairs);
        Map<String, List<AllPickUpOut>> map = allPickUpOuts.stream()
                .collect(Collectors.groupingBy(
                        out -> out.getPickUpId() + "_" + out.getWaybillCode()));
        for (CostWaybillVo vo : costWaybillVoList) {
            String key = vo.getId() + "_" + vo.getWaybillCode();
            List<AllPickUpOut> list = map.get(key);
            vo.setIsOut(0);
            if (list != null && !list.isEmpty()) {
                AllPickUpOut latest = list.stream()
                        .filter(e-> e.getUpdateTime() != null)
                        .max(Comparator.comparing(AllPickUpOut::getUpdateTime))
                        .orElse(null);
                if (latest != null) {
                    vo.setTallyQuantity(latest.getPieces());
                    vo.setTallyWeight(latest.getWeight());
                    if (latest.getOutTime() != null) {
                        vo.setOutTime(latest.getOutTime());
                        vo.setIsOut(1);
                    }
                }
            }
            vo.setCustomer(StringUtils.isNotEmpty(vo.getAgentCompany()) ? vo.getAgentCompany() : vo.getConsign());
            if(vo.getHandleQuantity() == null){
                int pickUpQuantity = pickUpWaybillMapper.selectList(new QueryWrapper<PickUpWaybill>()
                        .eq("pick_up_id", vo.getId())).stream().mapToInt(PickUpWaybill::getCanPickUpQuantity).sum();
                vo.setHandleQuantity(pickUpQuantity);
            }
        }
        if("办单".equals(query.getStatus())){
            costWaybillVoList.removeIf(costWaybillVo -> costWaybillVo.getIsOut()==1);
        }else if("提货".equals(query.getStatus())){
            costWaybillVoList.removeIf(costWaybillVo -> costWaybillVo.getIsOut()!=1);
        }
        return costWaybillVoList;
    }


    @Override
    public List<CostWaybillVo> selectListCount(CostWaybillQuery query) {
        query.setSettleUserArr(StrUtil.isNotBlank(query.getSettleUser()) ? query.getSettleUser().split(",") : null);
        return costWaybillMapper.selectListCount(query);
    }

    @Override
    public List<BillExportVo> billExport(CostWaybillQuery query) {
        query.setSettleUserArr(StrUtil.isNotBlank(query.getSettleUser()) ? query.getSettleUser().split(",") : null);
        List<BillExportVo> list = allPickUpOutMapper.billExport(query);
        List<TallyWaybillKey> keys = new ArrayList<>();
        list.forEach(bill ->
                bill.getOrderWaybillVos().stream()
                        .filter(waybill -> waybill.getPickUpId() != null && StringUtils.isNotBlank(waybill.getWaybillCode()))
                        .forEach(waybill ->
                                keys.add(new TallyWaybillKey(waybill.getPickUpId(), waybill.getWaybillCode()))
                        ));
        Map<TallyWaybillKey, List<ArrItemVo>> itemVoMap = keys.isEmpty() ?
                Collections.emptyMap() :
                itemMapper.selectAllItemVos(keys).stream()
                        .collect(Collectors.groupingBy(
                                item -> new TallyWaybillKey(item.getPickUpId(), item.getWaybillCode())
                        ));
        AtomicInteger idx = new AtomicInteger(1);
        list.forEach(bill -> {
            bill.setIdx(idx.getAndIncrement());
            bill.getOrderWaybillVos().forEach(waybill -> {
                TallyWaybillKey key = new TallyWaybillKey(waybill.getPickUpId(), waybill.getWaybillCode());
                List<ArrItemVo> items = itemVoMap.getOrDefault(key, Collections.emptyList());
                if (!items.isEmpty()) {
                    BigDecimal total = items.stream()
                            .map(item -> Optional.ofNullable(item.getEditCharge()).orElse(item.getTotalCharge()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    waybill.setSubtotal(total);
                    items.stream()
                            .collect(Collectors.groupingBy(ArrItemVo::getChargeAbb))
                            .forEach((chargeAbb, chargeItems) -> {
                                BigDecimal sum = chargeItems.stream()
                                        .map(item -> Optional.ofNullable(item.getEditCharge()).orElse(item.getTotalCharge()))
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                                switch (chargeAbb) {
                                    case "处置费":
                                        waybill.setProcessingFee(sum);
                                        break;
                                    case "仓储费":
                                        waybill.setStorageFee(sum);
                                        break;
                                    case "冷藏费":
                                        waybill.setRefrigerationFee(sum);
                                        break;
                                    case "搬运费":
                                        waybill.setHandlingFee(sum);
                                        break;
                                    case "电报费":
                                        waybill.setCableCharge(sum);
                                        break;
                                    default:
                                        break;
                                }
                            });
                }
            });
        });
        return list;
    }

    @Override
    public List<BillExportAWBMVo> billExportAWBM(CostWaybillQuery query) {
        query.setSettleUserArr(StrUtil.isNotBlank(query.getSettleUser()) ? query.getSettleUser().split(",") : null);
        //查询提货办单的流水号运单数据以及费用明细
        List<BillExportAWBMVo> list = allPickUpOutMapper.billExportAWBM(query);
        if(CollectionUtil.isEmpty(list)){
            return Collections.emptyList();
        }
        List<String> waybillCodeList = list.stream()
                .flatMap(v -> v.getOrderWaybillVos().stream())
                .map(OutOrderWaybillAWBMVo::getWaybillCode)
                .collect(Collectors.toList());
        //查询运单办单件数重量信息
        LambdaQueryWrapper<HzArrTally> bdCompLqw = Wrappers.<HzArrTally>lambdaQuery()
                .in(HzArrTally::getWaybillCode, waybillCodeList)
                .and(lqw -> lqw.eq(HzArrTally::getStatus, "settle")
                        .or().eq(HzArrTally::getStatus, "save_out"));

        List<HzArrTally> tallyList = hzArrTallyMapper.selectList(bdCompLqw);
        if (CollectionUtils.isEmpty(tallyList)) {
            return list;
        }
        //根据运单号分组
        Map<String, List<HzArrTally>> waybillCodeToTally = tallyList.stream()
                .collect(Collectors.groupingBy(HzArrTally::getWaybillCode));
        AtomicInteger inc = new AtomicInteger(1);
        list.forEach(vo -> {
            vo.setIdx(inc.getAndIncrement());
            for (OutOrderWaybillAWBMVo orderWaybillVo : vo.getOrderWaybillVos()) {
                SysDept sysDept = deptMapper.selectDeptById(orderWaybillVo.getDeptId());
                orderWaybillVo.setAgent(Optional.ofNullable(sysDept).orElseGet(SysDept::new).getDeptName());
                List<HzArrTally> tallies = waybillCodeToTally.getOrDefault(orderWaybillVo.getWaybillCode(), Collections.emptyList());
                Integer piecesSum = tallies.stream().map(HzArrTally::getPieces).reduce(0, Integer::sum);
                BigDecimal weightSum = tallies.stream().map(HzArrTally::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (orderWaybillVo.getQuantity().equals(piecesSum) && orderWaybillVo.getWeight().equals(weightSum)) {
                    piecesSum = 0;
                    weightSum = BigDecimal.ZERO;
                    orderWaybillVo.setAbnormalNotClosed("已结案");
                } else {
                    orderWaybillVo.setAbnormalNotClosed("未结案");
                }
                orderWaybillVo.setInventory(String.format("%s/%s", piecesSum, weightSum));
            }
        });
        return list;
    }


    /**
     * 挑单费用明细数据
     * @param waybillCode 进港运单号
     * @return 结果
     */
    @Override
    public HzArrItemVo cost(String waybillCode,String serial) {
        HzArrItemVo vo = mawbMapper.selectByCode(waybillCode);
        vo.setWaybillCode(waybillCode);
        PickUp pickUp = pickUpMapper.selectOne(new QueryWrapper<PickUp>()
                .eq("serial_no", serial)
                .last("limit 1"));
        List<HzArrItem> items = itemMapper.selectCostListByPickUpId(waybillCode,null, pickUp.getId());
        if (!CollectionUtils.isEmpty(items)){
            vo.setItems(items);
            BigDecimal reduce = items.stream()
                    .map(hzArrItem -> hzArrItem.getEditCharge() != null ? hzArrItem.getEditCharge() : hzArrItem.getTotalCharge())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setTotalCost(reduce);
        }
        return vo;
    }

    @Override
    public List<ArrChargeExportNewVo> chargeCostExport(CostWaybillQuery query) {
        query.setPageNum(null);
        query.setPageSize(null);
        query.setSettleUserArr(StrUtil.isNotBlank(query.getSettleUser()) ? query.getSettleUser().split(",") : null);
        List<ArrChargeExportNewVo> list = costWaybillMapper.chargeCostExportList(query);

        List<TallyWaybillKey> keys = new ArrayList<>();
        list.stream().filter(importVo -> importVo.getId() != null && StringUtils.isNotBlank(importVo.getWaybillCode()))
                .forEach(waybill ->
                        keys.add(new TallyWaybillKey(waybill.getId(), waybill.getWaybillCode()))
                );
        List<AllPickUpOut> outs = allPickUpOutMapper.selectBatch(keys);
        Map<String, AllPickUpOut> outMap = outs.stream()
                .collect(Collectors.toMap(
                        out -> out.getPickUpId() + "_" + out.getWaybillCode(),
                        Function.identity(),
                        (existing, replacement) -> existing));

        if("办单".equals(query.getStatus())){
            list.removeIf(costWaybillVo -> costWaybillVo.getIsOut()==1);
        }else if("提货".equals(query.getStatus())){
            list.removeIf(costWaybillVo -> costWaybillVo.getIsOut()!=1);
        }

        Map<String, List<ArrChargeExportNewVo>> voList = list.stream()
                .collect(Collectors.groupingBy(ArrChargeExportNewVo::getSerialNo));
        List<ArrChargeExportNewVo> voBacks = new ArrayList<>();
        for(List<ArrChargeExportNewVo> vos:voList.values()){
            ArrChargeExportNewVo arrChargeExportNewVo = vos.get(0);
            arrChargeExportNewVo.setCustomer(
                    !StringUtils.isEmpty(arrChargeExportNewVo.getAgentCompany()) ? arrChargeExportNewVo.getAgentCompany() : arrChargeExportNewVo.getConsign()
            );
            AllPickUpOut allPickUpOut = outMap.get(arrChargeExportNewVo.getId() + "_" + arrChargeExportNewVo.getWaybillCode());
            arrChargeExportNewVo.setIsOut(0);
            if(allPickUpOut!=null){
                if(allPickUpOut.getOutTime()!=null){
                    arrChargeExportNewVo.setIsOut(1);
                }
            }
            if(StringUtils.isNull(arrChargeExportNewVo.getTotalChargeWeight())){
                arrChargeExportNewVo.setTotalChargeWeight(arrChargeExportNewVo.getTotalWeight());
            }
            if(StringUtils.isNull(arrChargeExportNewVo.getTotalQuantity())){
                List<PickUpWaybill> pickUpWaybills = pickUpWaybillMapper.selectList(new QueryWrapper<PickUpWaybill>()
                        .eq("pick_up_id", arrChargeExportNewVo.getId()));
                arrChargeExportNewVo.setTotalQuantity(pickUpWaybills.stream().mapToInt(PickUpWaybill::getCanPickUpQuantity).sum());
            }

            voBacks.add(arrChargeExportNewVo);
        }

        if(!CollectionUtils.isEmpty(voBacks)){
            ArrChargeExportNewVo importVo = new ArrChargeExportNewVo();
            importVo.setCustomer("合计");
            importVo.setTotalCost(voBacks.stream().map(ArrChargeExportNewVo::getTotalCost).reduce(BigDecimal.ZERO,BigDecimal::add));
            voBacks.add(importVo);
        }
        return voBacks
                .stream()
                .sorted(Comparator.comparing(ArrChargeExportNewVo::getPickUpTime, Comparator.nullsFirst(Comparator.naturalOrder())).reversed())
                .collect(Collectors.toList());
    }


}
