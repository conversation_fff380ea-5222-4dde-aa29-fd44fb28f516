package com.gzairports.wl.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * Created by david on 2024/11/26
 * <AUTHOR>
 */
@Data
public class MawbQueryVo {

    private Long id;

    /** 运单号 */
    @Excel(name = "运单号")
    private String waybillCode;

    /** 目的站 */
    @Excel(name = "目的站")
    private String desPort;

    /** 承运人1 */
    @Excel(name = "航司")
    private String carrier1;

    /** 航班号 */
    @Excel(name = "航班号")
    private String flightNo1;

    /** 航班时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date flightDate1;

    /** 品名编码 */
    @Excel(name = "品名编码")
    private String cargoCode;

    /** 品名 */
    @Excel(name = "品名")
    private String cargoName;

    /** 件数 */
    @Excel(name = "件数")
    private Integer quantity;

    /** 重量 */
    @Excel(name = "重量")
    private BigDecimal weight;

    /** 计费重量 */
    @Excel(name = "计费重量")
    private BigDecimal chargeWeight;

    /** 航空费率 */
    @Excel(name = "航空费率")
    private BigDecimal airRate;

    /** 航空费用 */
    @Excel(name = "航空费用")
    private BigDecimal airCost;

    /** 燃油费 */
    @Excel(name = "燃油费")
    private BigDecimal fuelCost;

    /** 保险费 */
    @Excel(name = "保险费")
    private BigDecimal premium;

    /** 合计 */
    @Excel(name = "合计")
    private BigDecimal totalCost;

    /** 制单时间 */
    @Excel(name = "制单时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date writeTime;

    /** 作废备注 */
    @Excel(name = "作废备注")
    private String remark;

    private String deptName;

    private Long deptId;

    /** 运单状态 */
    private String status;

    /** 发货人 */
    private String shipper;

    /** 计飞时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime flightPlanTime;

    /** 实飞时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime flightFlyTime;

    /** 计降时间 */
    private LocalDateTime terminalSchemeLandInTime;

    /** 是否告知 0或者空表示未告知 1已告知 */
    private Integer isNotify;
}
