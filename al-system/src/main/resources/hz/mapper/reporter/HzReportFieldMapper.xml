<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.reporter.mapper.HzReportFieldMapper">
    <select id="selectReportFieldList" resultType="com.gzairports.hz.business.reporter.domain.HzReportField">
        select id as fieldId,field_name_cn,field_name,field_type,type
        from hz_report_field
        where type = #{type}
    </select>
    <select id="selectFields" resultType="java.lang.String">
        select field_name from hz_report_field where type in (#{masterTable},#{slaveTable},#{slaveTable2})
    </select>
</mapper>