package com.gzairports.hz.business.reporter.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzairports.hz.business.reporter.domain.vo.ReportConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-03-06
 */
@Mapper
public interface HzCountReportMapper {

    /**
     * 动态生成表格sql
     * @param config 动态数据
     * @return 结果
     */
    Page<Map<String, Object>> generateReport(@Param("config") ReportConfig config,@Param("page") Page<Map<String,Object>> page);


    Integer pageQuery(@Param("config") ReportConfig config);

    Long countReportTotal(@Param("config") ReportConfig config);
}
