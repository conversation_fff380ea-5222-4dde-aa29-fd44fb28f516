package com.gzairports.wl.charge.domain.vo;

import com.gzairports.common.annotation.Excel;
import lombok.Data;

/**
 * 收费项目返回数据
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@Data
public class ChargeItemVO {

    /** 主键id */
    private Long id;

    /** 名称 */
    @Excel(name = "名称")
    private String name;

    /** 航司code */
    @Excel(name = "航司")
    private String code;

    /** 进出港类型 */
    @Excel(name = "进出港类型")
    private String operationType;

    /** 适用单证类型 */
    private String applicableDocuments;

    @Excel(name = "适用单证类型")
    private String appDocuments;

    /** 用途 */
    @Excel(name = "用途")
    private String purpose;

    /** 结算收款对象 */
    @Excel(name = "结算收款对象")
    private String settlementPayee;

    /** 简称 */
    @Excel(name = "简称")
    private String abbreviation;

    /** 是否默认收费 */
    private Integer defaultChargeable;

    @Excel(name = "是否默认收费")
    private String isChargeable;

    /** 默认计费方式 */
    private Integer defaultBillingMethod;

    @Excel(name = "默认计费方式")
    private String billingMethod;

    /** 默认费用/费率 */
    @Excel(name = "默认费用/费率")
    private Float defaultCostRate;

    /** 四舍五入规则 */
    private Integer roundRule;

    @Excel(name = "四舍五入规则")
    private String rule;

    /** 是否可编辑 */
    private Integer editable;

    @Excel(name = "是否可编辑")
    private String isEdit;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    /** 所属单位 */
    private String dept;
}
