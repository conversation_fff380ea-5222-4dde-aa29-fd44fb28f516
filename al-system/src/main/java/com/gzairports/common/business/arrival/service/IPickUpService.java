package com.gzairports.common.business.arrival.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.basedata.domain.Customer;
import com.gzairports.common.basedata.domain.query.WayBillH5Query;
import com.gzairports.common.business.arrival.domain.ApplyEdit;
import com.gzairports.common.business.arrival.domain.HzArrItem;
import com.gzairports.common.business.arrival.domain.PickUp;
import com.gzairports.common.business.arrival.domain.query.*;
import com.gzairports.common.business.arrival.domain.vo.*;
import com.gzairports.common.business.departure.domain.Mawb;
import com.gzairports.common.business.departure.domain.vo.ItemDetailVo;
import com.gzairports.common.charge.domain.query.ItemsQuery;
import com.gzairports.common.charge.domain.vo.HzItemsVo;

import java.util.List;

/**
 * 国内进港提货办理Service接口
 *
 * <AUTHOR>
 * @date 2024-06-03
 */
public interface IPickUpService extends IService<PickUp> {

    /**
     * 根据运单号查询进港运单
     * @param query 查询参数
     * @return 结果
     */
    PickOrderVo one(PickUpClickQuery query,String deptIds);

    /**
     * 挑单费用明细数据
     * @param waybillCode 进港运单号
     * @param tallyId 理货id
     * @return 结果
     */
    HzArrItemVo cost(String waybillCode,Long tallyId);

    /**
     * 挑单费用明细详情
     * @param id 费用明细id
     * @return 详情
     */
    HzArrItem costInfo(Long id);

    /**
     * 保存提货办单数据
     * @param vo 提货办单数据
     * @return 结果
     */
    PickUp add(PickOrderVo vo);

    /**
     * 流水号查询
     * @param query 查询参数
     * @return 结果
     */
    PickOrderVo serial(PickUpCodeQuery query);

    /**
     * 批量挑单
     * @param query 查询参数
     * @return 运单数据
     */
    List<PickUpVo> batch(PickUpQuery query,String deptIds);

    /**
     * 加入提货列表
     * @param vo 提货数据集合
     * @return 办单数据
     */
    PickOrderVo pickList(PickUpListVo vo);

    /**
     * 查询已提货办单数据
     * @param query 查询参数
     * @return 已提货办单数据
     */
    PickedOrderVo selectByQuery(PickedUpQuery query);

    /**
     * 数据导出
     * @param query 查询条件
     * @return 结果
     */
    List<PickedUpVo> exportList(PickedUpQuery query);

    /**
     * 已提货办单运单导出
     * */
    List<PickedUpWaybillVo> exportWaybillList(PickedUpQuery query);

    /**
     * 查询未提货办单数据
     * @param query 查询条件
     * @return 未提货办单数据
     */
    List<NotPickedUpVo> notPickedUp(PickedUpQuery query,String deptIds);

    /**
     * 申请修改
     * @param applyEdit 申请参数
     * @return 结果
     */
    int applyEdit(ApplyEdit applyEdit);

    /**
     * 查看申请修改列表
     * @param query 查询参数
     * @return 列表
     */
    List<ApplyEditVo> selectApplyList(ApplyEditQuery query);

    /**
     * 打印出库单
     * @param pickUpId 办理提货id
     * @return 出库单数据
     */
    PrintOutOrderVo printOutOrder(Long pickUpId);


    /**
     * 自动办单机办单
     * @param query 查询条件
     * @return 运单数据
     */
    AutoOrderVo autoOrder(AutoOrderQuery query);

    /**
     * 发送验证码
     * @param query 发送参数
     * @return 结果
     */
    String sendCode(AutoOrderQuery query);

    /**
     * 散客H5提货办单（提交并查询运单）
     * @param query 查询参数
     * @return 运单列表
     */
    List<RetailVo> submitAndSelect(AutoOrderQuery query);

    /**
     * 修改详情
     * @param id 办理提货id
     * @return 修改参数
     */
    HzApplyEditVo hzApplyEdit(Long id);

    /**
     * 根据流水号查询修改提货办单需要的数据
     * @param serialNo 流水号
     */
    HzApplyEditVo getEditDataInfo(Long serialNo);

    /**
     * 编辑费用明细数据
     * @param item 更新后的费用明细数据
     * @return 结果
     */
    int editCost(HzArrItem item);

    /**
     * 删除费用明细
     * @param id 费用明细id
     * @return 结果
     */
    int delCost(Long id);

    /**
     * 新增费用明细
     * @param item 费用明细数据
     * @return 结果
     */
    int addCost(HzArrItem item);

    /**
     * 计算总费用
     * @param item 计算参数
     * @return 结果
     */
    String countCost(HzArrItem item);

    /**
     * 结算
     * @param waybillCode 运单号
     * @param type 进出港类型
     * @return 结果
     */
    SettleVo settle(String waybillCode, String type);

    /**
     * 散客在线提货办单
     * @param tallyId 理货id
     * @return 结果
     */
    OrderProVo orderPro(Long tallyId);

    /**
     * 根据id查询修改操作区数据
     * @param id 主键id
     * @return 操作区数据
     */
    OperDataVo operData(Long id);

    /**
     * 修改完成
     * @param vo 修改数据
     * @return 结果
     */
    int editComp(OperDataVo vo);

    /**
     * 拒绝修改
     * @param applyEdit 拒绝信息
     * @return 结果
     */
    int refuseEdit(ApplyEdit applyEdit);

    /**
     * 查看提货码
     * @param pickUpId 提货id
     * @return 结果
     */
    PickUpCode pickUpCode(Long pickUpId);

    /**
     * 代理人H5提货出库列表
     * @param query 查询条件
     * @return 列表数据
     */
    List<H5PickUpOutVo> arrH5List(H5PickUpOutQuery query);

    /**
     * 办单详情
     * @param pickUpId 办单id
     * @return 详情
     */
    H5PickUpOutVo getInfo(Long pickUpId);

    /**
     * 验证验证码
     * @param query 验证参数
     * @return 结果
     */
    String verifyCode(AutoOrderQuery query);

    /**
     * 余额支付
     * @param vo 办单id
     * @return 结果
     */
    PickUp balance(PickOrderVo vo);

    /**
     * 根据收费项目id查询最高优先级规则
     * @param vo 收费项目id
     * @return 最该优先级规则
     */
    ChargeRuleVo getHighRule(ItemDetailVo vo);

    /**
     * @author: lan
     * @description: 获取客户列表
     * @date: 2024/10/29
     */
    List<Customer> getCustomerList();

    /**
     * 过滤收费项目
     * @param itemQuery 查询条件
     * @return 结果
     */
    List<HzItemsVo> selectHzChargeItemsList(ItemsQuery itemQuery);

    /**
     * 取消提货办单数据
     * @param pickUpId 办单id
     * @return 结果
     */
    int cancel(Long pickUpId);

    /**
     * 修改提货办单数据
     * @param vo 提货办单数据
     * @return 结果
     */
    int edit(PickOrderVo vo);

    H5BillVo billList(BillOfLadingQuery query,String deptIds);

    PrintOutOrderVo h5PrintOutOrder(String pickUpCode, String platformType);

    PrintOutOrderVo printOutCount(String pickUpCode);

    PickOrderVo serialNo(String serialNo, Long deptId);

    /**
     * 货站结算
     * @param waybillCode 运单号
     * @param totalCost 总费用
     * @return 结果
     */
    SettleVo hzSettle(String waybillCode, String totalCost);

    /**
     * 查询与代理人相关的运单
     *
     * @param query
     * @return
     */
    Page<Mawb> listWaybill(WayBillH5Query query);

    ArrCostDetailVo selectCostDetail(CostDetailQuery query);

    List<ArrCostDetailListVo> selectCostDetailExport(CostDetailQuery query);
}
