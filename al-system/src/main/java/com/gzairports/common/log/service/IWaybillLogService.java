package com.gzairports.common.log.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.gzairports.common.log.domain.WaybillLog;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 运单日志Service接口
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
public interface IWaybillLogService extends IService<WaybillLog> {

    /**
     * 查询日志列表
     * @param waybillCode 运单号
     * @param type 进出港类型
     * @return 日志列表
     */
    List<WaybillLog> selectList(String waybillCode,String type);

    /**
     * 请求和返回数据
     * @param id 运单号
     * @return 请求和返回数据
     */
    WaybillLog info(Long id);

    /**
     * 新增运单日志接口
     * @param waybillLog 运单日志信息
     */
    void insertWaybillLog(WaybillLog waybillLog);

    /**
     *填入数据返回赋好值的运单日志对象
     * @param waybillCode 运单号,
     * @param operRemark 操作描述,
     * @param operName 操作人,
     * @param operWeight 操作重量,
     * @param operPieces 操作件数,
     * @param flightNo 航班号,
     * @param operParam 请求参数,
     * @param jsonResult 返回参数,
     * @param status 操作状态,
     * @param error_msg 错误消息,
     * @param operTime 操作时间,
     * @param remark 备注,
     * @param type 类型 ARR到港 DEP离港 TRANDFER 中转,
     * @param uld uld号,
     **/
    WaybillLog getWaybillLog(String waybillCode, Integer operRemark, String operName,
                             String operWeight, String operPieces, String flightNo,
                             Object operParam, Object jsonResult, int status,
                             String error_msg, Date operTime,String remark,
                             String type, String uld);

    String getJson(String s);

    boolean isExistLog(String waybillCode, String flightNo, String remark);
}
