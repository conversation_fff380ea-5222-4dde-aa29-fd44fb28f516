package com.gzairports.web.controller.departure;

import com.gzairports.common.basedata.service.IAirportCodeService;
import com.gzairports.common.business.departure.domain.Mawb;
import com.gzairports.common.business.departure.mapper.MawbMapper;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.domain.PageQuery;
import com.gzairports.common.core.domain.entity.SysDept;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.pdf.PdfPrintHelper;
import com.gzairports.common.securitySubmit.domain.AllSecurityWaybill;
import com.gzairports.common.securitySubmit.domain.SecurityWaybillInfo;
import com.gzairports.common.securitySubmit.mapper.AllSecurityWaybillMapper;
import com.gzairports.common.system.mapper.SysConfigMapper;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.sign.Base64;
import com.gzairports.common.utils.spring.SpringUtils;
import com.gzairports.oss.domain.SysOss;
import com.gzairports.oss.service.impl.SysOssServiceImpl;
import com.gzairports.wl.departure.domain.query.SecurityQueryWl;
import com.gzairports.wl.departure.domain.vo.SecurityInfoWl;
import com.gzairports.wl.departure.domain.vo.SecurityVoWl;
import com.gzairports.wl.departure.service.ISecuritySubmitService;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * @author: lan
 * @Desc: 物流安检提交controller
 * @create: 2024-11-14 11:56
 **/

@RestController
@RequestMapping("/dep/security")
public class SecuritySubmitController extends BaseController {

    @Autowired
    private ISecuritySubmitService securitySubmitService;
    @Autowired
    private MawbMapper mawbMapper;
    @Autowired
    private SysDeptMapper sysDeptMapper;
    @Autowired
    private IAirportCodeService airportCodeService;
    @Autowired
    private SysConfigMapper configMapper;
    @Autowired
    private AllSecurityWaybillMapper allSecurityWaybillMapper;


    /**
     * 安检申报列表
     * */
    @GetMapping("/securitySubmitList")
    @ApiOperation(value = "安检申报列表")
    public AjaxResult securitySubmitList(SecurityQueryWl query)
    {
//        startPage();
//        List<SecurityVoWl> securityListVos = securitySubmitService.securitySubmitList(query);
        PageQuery<List<SecurityVoWl>> securityListVos = securitySubmitService.securitySubmitList(query);
        return AjaxResult.success(securityListVos);
    }

    /**
     * 安检申报详情
     * */
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "安检申报详情")
    public AjaxResult securitySubmitInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success("操作成功",securitySubmitService.getInfo(id));
    }

    /**
     * 安检申报提交
     * */
    @PostMapping(value = "/submit")
    @ApiOperation(value = "安检申报提交")
    public AjaxResult submit(@RequestBody SecurityInfoWl securityInfoWl) throws Exception {
        Mawb mawb = mawbMapper.selectById(securityInfoWl.getId());
        //1.8增加判断 已经提交的运单则不能提交
        AllSecurityWaybill allSecurityWaybill = allSecurityWaybillMapper.selectById(securityInfoWl.getId());
        if(mawb == null){
            SecurityWaybillInfo info = new SecurityWaybillInfo();
            BeanUtils.copyProperties(allSecurityWaybill,info);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            info.setFlightDate1Str(simpleDateFormat.format(allSecurityWaybill.getFlightDate1()));
            info.setAgentCompany(allSecurityWaybill.getAgent());
            if (allSecurityWaybill.getCargoType() != null){
                if(allSecurityWaybill.getCargoType() == 0){
                    info.setPthw("YES");
                }else if(allSecurityWaybill.getCargoType() == 1){
                    info.setTzhw("YES");
                }else{
                    info.setWxp("YES");
                }
            }
            SysDept sysDept = sysDeptMapper.selectDeptById(allSecurityWaybill.getDeptId());
            if(sysDept!=null){
                if(StringUtils.isNotEmpty(sysDept.getSealUrl())){
                    byte[] bytes = downloadFileFromUrl(sysDept.getSealUrl());
                    String logoImage = Base64.encode(bytes);
                    info.setSealUrl(logoImage);
                    info.setSealUrl2(logoImage);
                }
                info.setShipperStr(sysDept.getDeptName());
            }
            //将目的站从英文转化为中文
            String desPortChinese = airportCodeService.selectChineseName(info.getDesPort());
            if(StringUtils.isNotEmpty(desPortChinese)){
                info.setDesPort(desPortChinese);
            }
            String securityUrl;
            if(allSecurityWaybill.getWaybillCode().contains("AWBA")) {
                String substring = info.getWaybillCode().substring(4);
                String waybillCodeAbb = substring.substring(0, 3) + "-" + substring.substring(3);
                info.setWaybillCodeAbb(waybillCodeAbb);
                securityUrl = getPdfUrl(info, "electronic/security.pdf");
                allSecurityWaybill.setSecurityUrl(securityUrl);
            }else{
                String substring = info.getWaybillCode().substring(4);
                String waybillCodeAbb = substring.substring(0, 2) + "-" + substring.substring(2);
                info.setWaybillCodeAbb(waybillCodeAbb);
                info.setShipperAbb(allSecurityWaybill.getShipper());
                securityUrl = getPdfUrl(info, "electronic/mailSecurity.pdf");
                allSecurityWaybill.setSecurityUrl(securityUrl);
            }
                allSecurityWaybill.setDeliveryIdNo(securityInfoWl.getDeliveryIdNo());
                allSecurityWaybill.setDeliveryProfilePhoto(securityInfoWl.getDeliveryProfilePhoto());
                allSecurityWaybill.setDeliveryFilePhoto(securityInfoWl.getDeliveryFilePhoto());
                allSecurityWaybill.setAgentSignature(securityInfoWl.getAgentSignature());
                allSecurityWaybill.setShipperSignature(securityInfoWl.getShipperSignature());
                allSecurityWaybillMapper.updateById(allSecurityWaybill);
                return AjaxResult.success(securitySubmitService.submit(securityInfoWl));
        }else {
            if(mawb.getPayStatus() == 0 || mawb.getPayStatus() == 14){
                throw new CustomException("运单未支付");
            }
            if(mawb.getSecuritySubmitWl() == 1 ||mawb.getSecuritySubmitWl() == 2){
                throw new CustomException("该运单安检已提交");
            }
//            //参数配置 如果开启为true 则必须预支付才能安检和配载
//            SysConfig sysConfig = configMapper.checkConfigKeyUnique("isPay.submitLoad");
//            //12.26 邮件单不需要支付也能安检提交
//            if ("true".equals(sysConfig.getConfigValue())
//                    && (mawb.getPayStatus() == 0 || mawb.getPayStatus() == 14)
//                    && !mawb.getWaybillCode().contains("DN")) {
//                String substring = mawb.getWaybillCode().substring(4);
//                throw new CustomException("运单" + substring.substring(0, 3) + "-" + substring.substring(3) + "未支付");
//            }
            SecurityWaybillInfo vo = mawbMapper.getInfo(mawb.getWaybillCode());
            //拼接pdf
            SysDept sysDept = sysDeptMapper.selectDeptById(mawb.getDeptId());
            //这里要区分是主单还是邮件单
            if (mawb.getWaybillCode().contains("AWBA")) {
                //要用http请求把图片转为base64 再转回字符串
                if (sysDept != null) {
                    if (StringUtils.isNotEmpty(sysDept.getSealUrl())) {
                        byte[] bytes = downloadFileFromUrl(sysDept.getSealUrl());
                        String logoImage = Base64.encode(bytes);
                        vo.setSealUrl(logoImage);
                        vo.setSealUrl2(logoImage);
                    }
                    vo.setShipperStr(sysDept.getDeptName());
                    if(StringUtils.isEmpty(sysDept.getCreateUser())){
                        throw new CustomException("安检申报姓名未设置,请到部门管理进行设置");
                    }
                    vo.setAgentSignature(sysDept.getCreateUser());
                    vo.setShipperSignature(sysDept.getCreateUser());
                }
//                if (StringUtils.isNotEmpty(securityInfoWl.getShipperSignature())) {
//                    vo.setShipperSignature(securityInfoWl.getShipperSignature());
//                }
//                if (StringUtils.isNotEmpty(securityInfoWl.getAgentSignature())) {
//                    vo.setAgentSignature(securityInfoWl.getAgentSignature());
//                }
                SecurityWaybillInfo waybillInfoVo = generateSecurity(vo);
                mawb.setSecurityUrl(waybillInfoVo.getSecurityUrl());
            } else {
                SecurityWaybillInfo mailVo = new SecurityWaybillInfo();
                BeanUtils.copyProperties(mawb, mailVo);
                if (sysDept != null) {
                    if (StringUtils.isNotEmpty(sysDept.getSealUrl())) {
                        byte[] bytes = downloadFileFromUrl(sysDept.getSealUrl());
                        String logoImage = Base64.encode(bytes);
                        mailVo.setSealUrl(logoImage);
                    }
                    if(StringUtils.isEmpty(sysDept.getCreateUser())){
                        throw new CustomException("安检申报姓名未设置,请到部门管理进行设置");
                    }
                    mailVo.setAgentSignature(sysDept.getCreateUser());
                    mailVo.setShipperSignature(sysDept.getCreateUser());
                }
//                if (StringUtils.isNotEmpty(securityInfoWl.getShipperSignature())) {
//                    mailVo.setShipperSignature(securityInfoWl.getShipperSignature());
//                }
//                if (StringUtils.isNotEmpty(securityInfoWl.getAgentSignature())) {
//                    mailVo.setAgentSignature(securityInfoWl.getAgentSignature());
//                }
                String substring = mailVo.getWaybillCode().substring(4);
                String waybillCodeAbb = substring.substring(0, 2) + "-" + substring.substring(2);
                mailVo.setWaybillCodeAbb(waybillCodeAbb);
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                if (mailVo.getFlightDate1() != null) {
                    mailVo.setFlightDate1Str(simpleDateFormat.format(mailVo.getFlightDate1()));
                }
                //将目的站从英文转化为中文
                String desPortChinese = airportCodeService.selectChineseName(mailVo.getDesPort());
                if (StringUtils.isNotEmpty(desPortChinese)) {
                    mailVo.setDesPort(desPortChinese);
                }
                String securityUrl = getPdfUrl(mailVo, "electronic/mailSecurity.pdf");
                mawb.setSecurityUrl(securityUrl);
            }

            mawb.setDeliveryIdNo(securityInfoWl.getDeliveryIdNo());
            mawb.setDeliveryProfilePhoto(securityInfoWl.getDeliveryProfilePhoto());
            mawb.setDeliveryFilePhoto(securityInfoWl.getDeliveryFilePhoto());
            mawb.setDeliveryCargoNames(securityInfoWl.getDeliveryCargoNames());
            mawb.setDeliveryCargoNamesPdf(securityInfoWl.getDeliveryCargoNamesPdf());

//            mawb.setAgentSignature(securityInfoWl.getAgentSignature());
//            mawb.setShipperSignature(securityInfoWl.getShipperSignature());
            if (sysDept != null){
                mawb.setShipperSignature(sysDept.getCreateUser());
                mawb.setShipperSignature(sysDept.getCreateUser());
            }
            mawbMapper.updateById(mawb);
            if(allSecurityWaybill!=null){
                allSecurityWaybill.setSecurityUrl(mawb.getSecurityUrl());
                allSecurityWaybill.setDeliveryIdNo(securityInfoWl.getDeliveryIdNo());
                allSecurityWaybill.setDeliveryProfilePhoto(securityInfoWl.getDeliveryProfilePhoto());
                allSecurityWaybill.setDeliveryFilePhoto(securityInfoWl.getDeliveryFilePhoto());
//                allSecurityWaybill.setAgentSignature(securityInfoWl.getAgentSignature());
//                allSecurityWaybill.setShipperSignature(securityInfoWl.getShipperSignature());
                if (sysDept != null){
                    allSecurityWaybill.setAgentSignature(sysDept.getCreateUser());
                    allSecurityWaybill.setShipperSignature(sysDept.getCreateUser());
                }

                allSecurityWaybillMapper.updateById(allSecurityWaybill);
            }
            return AjaxResult.success(securitySubmitService.submit(securityInfoWl));
        }
    }

    /**
     * 代理人重复提交品名清单附件
     * */
    @PostMapping(value = "/agentRepeatSubmit")
    @ApiOperation(value = "代理人重复提交品名清单附件")
    public AjaxResult submitRepeat(@RequestBody SecurityInfoWl securityInfoWl){
        return AjaxResult.success(securitySubmitService.submitRepeat(securityInfoWl));
    }

    /**
     * 生成安检申报单
     * */
    private SecurityWaybillInfo generateSecurity(SecurityWaybillInfo vo) throws Exception {
        String substring = vo.getWaybillCode().substring(4);
        String waybillCodeAbb = substring.substring(0, 3) + "-" + substring.substring(3);
        vo.setWaybillCodeAbb(waybillCodeAbb);
        if (StringUtils.isNotEmpty(vo.getDangerCode())){
            vo.setWxp("YES");
        }else if (StringUtils.isNotEmpty(vo.getSpecialCargoCode1())){
            vo.setTzhw("YES");
        }else {
            vo.setPthw("YES");
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if (vo.getFlightDate1() != null){
            vo.setFlightDate1Str(simpleDateFormat.format(vo.getFlightDate1()));
        }
//        BaseAgent agent = baseAgentService.selectBaseAgentByName(vo.getAgentCompany());
//        if (agent != null && StringUtils.isNotEmpty(agent.getSealUrl())){
//            byte[] bytes = downloadFileFromUrl(agent.getSealUrl());
//            String sealImage = Base64.encode(bytes);
//            vo.setSealUrl(sealImage);
//        }

        //将目的站从英文转化为中文
        String desPortChinese = airportCodeService.selectChineseName(vo.getDesPort());
        if(StringUtils.isNotEmpty(desPortChinese)){
            vo.setDesPort(desPortChinese);
        }


        String securityUrl = getPdfUrl(vo, "electronic/security.pdf");
        vo.setSecurityUrl(securityUrl);
        return vo;
    }

    private String getPdfUrl(@RequestBody SecurityWaybillInfo vo, String s) throws Exception {
        ClassPathResource securityUrl = new ClassPathResource(s);
        if (securityUrl.exists()) {
            String path = securityUrl.getPath();
            byte[] pdfDataFromTemplate = PdfPrintHelper.getPdfDataFromTemplate(vo, path);
            // 使用StandardMultipartHttpServletRequest来创建MultipartFile
            DiskFileItem fileItem = new DiskFileItem(vo.getWaybillCode() + ".pdf", "application/pdf", true, vo.getWaybillCode() + ".pdf", -1, null);

            // 将 InputStream 写入 DiskFileItem
            fileItem.getOutputStream().write(pdfDataFromTemplate);
            fileItem.getOutputStream().close();

            // 使用 FileItem 构建 MultipartFile
            MultipartFile multipartFile = new CommonsMultipartFile(fileItem);
            SysOss upload = SpringUtils.getBean(SysOssServiceImpl.class).upload(multipartFile);
            return upload.getUrl();
        }
        return null;
    }

    private byte[] downloadFileFromUrl(String urlStr) throws Exception {
        // 创建一个信任所有证书的信任管理器
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }
                    @Override
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                    @Override
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
        };

        // 安装信任所有证书的信任管理器
        SSLContext sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, trustAllCerts, new java.security.SecureRandom());

        URL url = new URL(urlStr);
        HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
        connection.setSSLSocketFactory(sslContext.getSocketFactory());

        // 设置自定义 HostnameVerifier（仅限测试环境）
        connection.setHostnameVerifier((hostname, session) -> true);

        connection.setRequestMethod("GET");
        connection.setConnectTimeout(5000);
        connection.setReadTimeout(5000);

        int responseCode = connection.getResponseCode();
        if (responseCode != 200) {
            throw new RuntimeException("Failed to connect, HTTP error code : " + responseCode);
        }

        try (InputStream inputStream = connection.getInputStream();
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            return baos.toByteArray();
        } catch (IOException e) {
            throw new RuntimeException("IO Exception occurred during file download", e);
        }
    }


    /**
     * 查询安检申报历史
     * */
    @GetMapping(value = "/getHistoryList/{id}")
    @ApiOperation(value = "查询安检申报历史")
    public AjaxResult getHistoryList(@PathVariable("id") Long id)
    {
        return AjaxResult.success("操作成功",securitySubmitService.getHistoryList(id));
    }

    /**
     * 新增安检申报
     * */
    @PostMapping(value = "/addSecurity")
    @ApiOperation(value = "新增安检申报")
    public AjaxResult addSecurity(@RequestBody AllSecurityWaybill allSecurityWaybill)
    {
        return AjaxResult.success("操作成功",securitySubmitService.addSecurity(allSecurityWaybill));
    }

}
