package com.gzairports.wl.reporter.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzairports.wl.reporter.domain.query.MawbHawbIncomeQuery;
import com.gzairports.wl.reporter.domain.vo.AHIncomeTotalVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface IMawbHawbIncomeTotalService {
    Page<AHIncomeTotalVO> queryExportDataList(MawbHawbIncomeQuery query);

    void export(List<AHIncomeTotalVO> records, HttpServletResponse response);

    void printPDF(MawbHawbIncomeQuery query, HttpServletResponse response);
}
