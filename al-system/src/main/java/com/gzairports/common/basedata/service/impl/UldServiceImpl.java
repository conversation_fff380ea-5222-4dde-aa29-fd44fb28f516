package com.gzairports.common.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.basedata.domain.BaseCargoUld;
import com.gzairports.common.basedata.domain.BaseCargoUldForSave;
import com.gzairports.common.basedata.domain.BaseCarrier;
import com.gzairports.common.basedata.domain.BaseFlatbedTruck;
import com.gzairports.common.basedata.domain.query.CargoUldQuery;
import com.gzairports.common.basedata.mapper.CarrierMapper;
import com.gzairports.common.basedata.mapper.TruckMapper;
import com.gzairports.common.basedata.mapper.UldMapper;
import com.gzairports.common.basedata.service.IUldService;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.exception.ServiceException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.bean.BeanValidators;
import com.gzairports.hz.business.departure.domain.AirWaybill;
import com.gzairports.hz.business.departure.domain.HzCollectWaybill;
import com.gzairports.hz.business.departure.domain.HzCollectWeight;
import com.gzairports.hz.business.departure.mapper.AllAirWaybillMapper;
import com.gzairports.hz.business.departure.mapper.HzCollectWaybillMapper;
import com.gzairports.hz.business.departure.mapper.HzCollectWeightMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.Validator;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 集装器管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Service
public class UldServiceImpl implements IUldService {

    @Autowired
    private UldMapper uldMapper;

    @Autowired
    private Validator validator;

    @Autowired
    private CarrierMapper carrierMapper;

    @Autowired
    private TruckMapper truckMapper;

    @Autowired
    private HzCollectWaybillMapper collectWaybillMapper;

    @Autowired
    private HzCollectWeightMapper collectWeightMapper;

    @Autowired
    private AllAirWaybillMapper allAirWaybillMapper;

    private static final Logger log = LoggerFactory.getLogger(CityCodeServiceImpl.class);

    /**
     * 查询集装器管理列表
     *
     * @param query 查询参数
     * @return 集装器管理列表
     */
    @Override
    public List<BaseCargoUld> ULDList(CargoUldQuery query) {
        return uldMapper.ULDList(query);
    }

    /**
     * 新增集装器
     *
     * @param uld 集装器
     * @return 结果
     */
    @Override
    public int addULD(BaseCargoUld uld) {
        if(StringUtils.isNotNull(uld.getExitTime())){
            if (uld.getEntranceTime().after(uld.getExitTime())) {
                throw new ServiceException("入场时间不能晚于出场时间");
            }
        }
        //查询是否存在承运管理的航司
        if (StringUtils.isNull(carrierMapper.selectByCode(uld.getAirCompany()))) {
            throw new ServiceException("承运管理的航司不存在");
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        uld.setCreateBy(loginUser.getUsername());
        uld.setCreateTime(DateUtils.getNowDate());
        return uldMapper.addULD(uld);
    }

    /**
     * 修改集装器
     *
     * @param uld 集装器
     * @return 结果
     */
    @Override
    public int editULD(BaseCargoUld uld) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        uld.setCreateBy(loginUser.getUsername());
        uld.setCreateTime(DateUtils.getNowDate());
        return uldMapper.editULD(uld);
    }

    /**
     * 删除集装器
     * @param id 集装器id
     * @return 结果
     */
    @Override
    public int removeUld(Long id) {
        return uldMapper.removeUld(id);
    }

    /**
     * 集装器详情
     * @param id 集装器id
     * @return 结果
     */
    @Override
    public BaseCargoUld getInfo(Long id) {
        return uldMapper.getInfo(id);
    }

    /**
     * 导入集装器
     * @param ulds 集装器列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importUlds(List<BaseCargoUld> ulds, boolean updateSupport) {
        if (StringUtils.isNull(ulds) || ulds.size() == 0)
        {
            throw new ServiceException("导入集装器数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (BaseCargoUld uld : ulds) {
            try {
                // 验证是否存在相同的城市代码
                BaseCargoUld cargoUld = uldMapper.selectByCode(uld.getCode());
                //判断是否存在承运管理的航司
                BaseCarrier baseCarrier = carrierMapper.selectByCode(uld.getAirCompany());
                //当没有相同的城市代码并且承运管理的航司存在时
                if (StringUtils.isNull(cargoUld) && !StringUtils.isNull(baseCarrier)) {
                    BeanValidators.validateWithException(validator, uld);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    uld.setCreateBy(loginUser.getUsername());
                    uld.setCreateTime(DateUtils.getNowDate());
                    uldMapper.addULD(uld);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、代码 " + uld.getCode() + " 导入成功");
                }
                //当有相同的城市代码并且承运管理的航司存在时
                else if (updateSupport && !StringUtils.isNull(baseCarrier)) {
                    BeanValidators.validateWithException(validator, uld);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    uld.setId(cargoUld.getId());
                    uld.setUpdateBy(loginUser.getUsername());
                    uld.setUpdateTime(DateUtils.getNowDate());
                    uldMapper.editULD(uld);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、代码 " + uld.getCode() + " 更新成功");
                }
                //没有相同的城市代码并且承运管理的航司不存在时
                else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、代码 " + uld.getCode() + " 已存在");
                }
            }catch (Exception e){
                failureNum++;
                String msg = "<br/>" + failureNum + "、代码 " + uld.getCode() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 保存uld
     * @param uldForSave uldForSave数据
     * @return 结果
     */
    @Override
    public int saveUld(BaseCargoUldForSave uldForSave) {
        BaseCargoUld uld = uldForSave.getBaseCargoUld();
        if (uld.getType() == null){
            return 1;
        }
        BigDecimal loadWeight = null;
        BaseFlatbedTruck cargoUld;
        BaseCargoUld baseCargoUld;
        if ("CAR".equals(uld.getType())){
            cargoUld = truckMapper.selectOne(new QueryWrapper<BaseFlatbedTruck>()
                    .eq("code", uld.getCode()).eq("is_del",0));
            if (cargoUld != null){
                //这里需要判断该板车上面的第一条订单的目的地问题
                HzCollectWaybill hzCollectWaybill = collectWaybillMapper.selectOne(new QueryWrapper<HzCollectWaybill>()
                        .eq("uld", cargoUld.getCode())
                        .orderBy(true, false, "collect_time")
                        .last("limit 1"));
                if (hzCollectWaybill != null){
                    HzCollectWeight collectWeight = collectWeightMapper.selectOne(new QueryWrapper<HzCollectWeight>()
                            .eq("collect_id", hzCollectWaybill.getId()));
                    if (!collectWeight.getDesPort().equals(uldForSave.getDesPort())){
                        throw new ServiceException("目的地不一致,收运失败");
                    }
                    AirWaybill airWaybill = allAirWaybillMapper.selectById(hzCollectWaybill.getWaybillId());
                    if(!airWaybill.getCarrier1().equals(uldForSave.getCarrier())){
                        throw new ServiceException("承运人不一致,收运失败");
                    }
                }
                if (!"正常".equals(cargoUld.getStatus())){
                    throw new ServiceException("板车状态异常");
                }
                //运单收运新增的板车暂不设置最大载重,则不判断
                if (StringUtils.isNotNull(cargoUld.getLoadWeight())){
                    loadWeight = new BigDecimal(cargoUld.getLoadWeight());
                }
            } else {
                BaseFlatbedTruck truck = new BaseFlatbedTruck();
                truck.setCode(uld.getCode());
                truck.setCreateBy(SecurityUtils.getUsername());
                truck.setCreateTime(new Date());
                truck.setType("板车");
                truck.setStatus("正常");
                truckMapper.insert(truck);
            }
        }else {
            baseCargoUld = uldMapper.selectOne(new QueryWrapper<BaseCargoUld>()
                    .eq("type",uld.getType())
                    .eq("code",uld.getCode()));
            if (baseCargoUld != null){
                if(StringUtils.isNotNull(baseCargoUld.getLoadWeight())){
                    loadWeight = baseCargoUld.getLoadWeight();
                }
            }else {
                uldMapper.insert(uld);
            }
        }
        if (loadWeight != null){
            if (new BigDecimal(uld.getWeight()).compareTo(loadWeight) > 0){
                throw new CustomException("板车超重");
            }
        }
        return 1;
    }

    /**
     * 查询当前uld使用状态
     * @param baseCargoUld uld数据
     * @return 是否
     */
    @Override
    public boolean selectStatus(BaseCargoUldForSave baseCargoUld) {
        BaseCargoUld uld = baseCargoUld.getBaseCargoUld();
        if (uld.getType() == null){
            return false;
        }
        if ("CAR".equals(uld.getType())){
            BaseFlatbedTruck truck1 = truckMapper.selectOne(new QueryWrapper<BaseFlatbedTruck>()
                    .eq("code", uld.getCode())
                    .eq("is_del",0));
            if (truck1 != null){
                return truck1.getUseStatus() == 1;
            }
        }else {
            BaseCargoUld cargoUld = uldMapper.selectOne(new QueryWrapper<BaseCargoUld>()
                    .eq("type", uld.getType())
                    .eq("code", uld.getCode()));
            if (cargoUld != null){
                return "OUT".equals(cargoUld.getInOut());
            }
        }
        return false;
    }
}
