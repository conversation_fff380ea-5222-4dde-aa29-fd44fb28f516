package com.gzairports.hz.business.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 开箱抽查返回数据
 * <AUTHOR>
 * @date 2024-06-24
 */
@Data
public class PinCheckVo {

    /** 主键id */
    private Long id;

    /** 运单id */
    private Long waybillId;

    /** 运单号 */
    @Excel(name = "运单号")
    private String waybillCode;

    /** 代理人 */
    @Excel(name = "代理人")
    private String agent;

    /** 代理公司 */
    private String agentCompany;

    /** 目的站 */
    @Excel(name = "目的站")
    private String desPort;

    /** 品名 */
    @Excel(name = "品名")
    private String cargoName;

    /** 用户姓名 */
    @Excel(name = "用户姓名")
    private String userName;

    /** 件数 */
    @Excel(name = "件数")
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 开箱件数 */
    @Excel(name = "开箱件数")
    private Integer openedPieces;

    /** 申报是否与实际一致 0 否 1 是*/
    private Integer declarationConsistent;

    @Excel(name = "申报是否与实际一致")
    private String declaration;

    /** 检查时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "检查时间")
    private Date inspectTime;

    /** 完成状态（1已完成，0未完成） */
    private Integer checkStatus;

    /** 运输文件是否齐全 0 否 1 是  */
    private Integer documentsComplete;

    /** 包装是否符合要求 0 否 1 是 */
    private Integer packagingCompliant;

    /** 备注 */
    private String remark;

    /** 检查图片 */
    private String inspectImages;

}
