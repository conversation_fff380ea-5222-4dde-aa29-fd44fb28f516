package com.gzairports.common.business.departure.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.annotation.DataSource;
import com.gzairports.common.business.departure.domain.vo.PageVO;
import com.gzairports.common.business.departure.domain.vo.TFilAwbinfo;
import com.gzairports.common.business.departure.mapper.TFilAwbinfoMapper;
import com.gzairports.common.business.departure.service.ITFilAwbinfoService;
import com.gzairports.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 航空运单Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-24
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class TFilAwbinfoServiceImpl extends ServiceImpl<TFilAwbinfoMapper, TFilAwbinfo> implements ITFilAwbinfoService {
    @Autowired
    private TFilAwbinfoMapper tFilAwbinfoMapper;


    @Override
    public List<TFilAwbinfo> selectTFilAwbinfoDateBillId(Date startTime, Date endTime, String billid) {
        LambdaQueryWrapper<TFilAwbinfo> query = Wrappers.lambdaQuery(TFilAwbinfo.class);
        if (StringUtils.isNotBlank(billid)) {
            if (billid.length() == 4) {
                query =query.eq(TFilAwbinfo::getSubbillid, billid);
                if (startTime != null && endTime != null) {
                    query = query.ge(TFilAwbinfo::getCrtopetime, startTime).le(TFilAwbinfo::getCrtopetime, endTime);
                }
            } else if(billid.length() == 8){
                query= query.eq(TFilAwbinfo::getStockno, billid);
            }else{
                query= query.eq(TFilAwbinfo::getBillid, billid);
            }
        }else{
            query = query.ge(TFilAwbinfo::getCrtopetime, startTime).le(TFilAwbinfo::getCrtopetime, endTime);
        }
        return list(query);
    }


    @Override
    public List<TFilAwbinfo> listByScokNo(Collection<String> strings) {
        if(strings.isEmpty()){
            new ArrayList<>();
        }
        LambdaQueryWrapper<TFilAwbinfo> query = Wrappers.lambdaQuery(TFilAwbinfo.class).in(TFilAwbinfo::getStockno,strings);
        return list(query);
    }

    @Override
    public List<TFilAwbinfo> listByScokNo(String scokNo) {
        List<TFilAwbinfo> list =  tFilAwbinfoMapper.selectTFilAwbinfoStockno(scokNo);
        if(list==null){
            return  new ArrayList<>();
        }
        return list;
    }

    /**
     * 分页查询运单数据
     * @param pageVO 分页信息
     * @return 结果
     */
    @Override
    public List<TFilAwbinfo> selectTFilAwbinfoPageList(PageVO pageVO) {
        return tFilAwbinfoMapper.selectTFilAwbinfoPageList(pageVO);
    }
}
