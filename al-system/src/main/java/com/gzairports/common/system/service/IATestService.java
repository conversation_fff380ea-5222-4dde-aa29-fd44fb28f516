package com.gzairports.common.system.service;

import java.util.List;
import com.gzairports.common.system.domain.ATest;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 代码生成测试Service接口
 * 
 * <AUTHOR>
 * @date 2023-09-18
 */
public interface IATestService  extends IService<ATest>
{
    /**
     * 查询代码生成测试
     * 
     * @param id 代码生成测试ID
     * @return 代码生成测试
     */
    ATest selectATestById(Long id);

    /**
     * 查询代码生成测试列表
     * 
     * @param aTest 代码生成测试
     * @return 代码生成测试集合
     */
    List<ATest> selectATestList(ATest aTest);

    /**
     * 新增代码生成测试
     * 
     * @param aTest 代码生成测试
     * @return 结果
     */
    int insertATest(ATest aTest);

    /**
     * 修改代码生成测试
     * 
     * @param aTest 代码生成测试
     * @return 结果
     */
    int updateATest(ATest aTest);

    /**
     * 批量删除代码生成测试
     * 
     * @param ids 需要删除的代码生成测试ID
     * @return 结果
     */
    int deleteATestByIds(Long[] ids);

    /**
     * 删除代码生成测试信息
     * 
     * @param id 代码生成测试ID
     * @return 结果
     */
    int deleteATestById(Long id);
}
