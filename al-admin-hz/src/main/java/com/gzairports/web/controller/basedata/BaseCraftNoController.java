package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BaseCraftNo;
import com.gzairports.common.basedata.domain.BaseCraftNoMail;
import com.gzairports.common.basedata.domain.query.BaseCraftNoQuery;
import com.gzairports.common.basedata.service.IBaseCraftNoService;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @author: lan
 * @Desc: 基础数据机号管理
 * @create: 2024-12-09 13:43
 **/
@RestController
@RequestMapping("/base/CraftNo")
@Api(tags = "基础数据机号管理")
public class BaseCraftNoController extends BaseController{
    @Autowired
    private IBaseCraftNoService baseCraftNoService;

    /**
     * 查询副邮箱机号管理列表
     */
    @GetMapping("/listMail")
    @ApiOperation(value = "查询副邮箱机号管理列表")
    public TableDataInfo listMail(BaseCraftNo baseCraftNo)
    {
        startPage();
        baseCraftNo.setType(1);
        List<BaseCraftNo> list = baseCraftNoService.selectCraftNoList(baseCraftNo);
        return getDataTable(list);
    }

    /**
     * 新增副邮箱机号
     */
    @PostMapping("/addMail")
    @ApiOperation(value = "新增副邮箱机号")
    public AjaxResult addMail(@RequestBody BaseCraftNo baseCraftNo)
    {
        baseCraftNo.setType(1);
        return AjaxResult.success(baseCraftNoService.insertCraft(baseCraftNo));
    }

    /**
     * 导出副邮箱机号数据
     */
    @PostMapping("/exportMail")
    @ApiOperation(value = "导出副邮箱机号数据")
    public void exportMail(HttpServletResponse response, BaseCraftNo baseCraftNo)
    {
        baseCraftNo.setType(1);
        List<BaseCraftNoMail> list = baseCraftNoService.selectCraftNoMailList(baseCraftNo);
        ExcelUtil<BaseCraftNoMail> util = new ExcelUtil<BaseCraftNoMail>(BaseCraftNoMail.class);
        util.exportExcel(response, list, "副油箱机号数据");
    }

    /**
     * 查询特殊供氧机号管理列表
     */
    @GetMapping("/listOxygen")
    @ApiOperation(value = "查询特殊供氧机号管理列表")
    public TableDataInfo listOxygen(BaseCraftNo baseCraftNo)
    {
        startPage();
        baseCraftNo.setType(2);
        List<BaseCraftNo> list = baseCraftNoService.selectCraftNoList(baseCraftNo);
        return getDataTable(list);
    }

    /**
     * 新增特殊供氧机号管理
     */
    @PostMapping("/addOxygen")
    @ApiOperation(value = "新增特殊供氧机号管理")
    public AjaxResult addOxygen(@RequestBody BaseCraftNo baseCraftNo)
    {
        baseCraftNo.setType(2);
        return AjaxResult.success(baseCraftNoService.insertCraft(baseCraftNo));
    }

    /**
     * 导出特殊供氧机号数据
     */
    @PostMapping("/exportOxygen")
    @ApiOperation(value = "导出特殊供氧机号数据")
    public void exportOxygen(HttpServletResponse response, BaseCraftNo baseCraftNo)
    {
        baseCraftNo.setType(2);
        List<BaseCraftNo> list = baseCraftNoService.selectCraftNoList(baseCraftNo);
        ExcelUtil<BaseCraftNo> util = new ExcelUtil<BaseCraftNo>(BaseCraftNo.class);
        util.exportExcel(response, list, "特殊供氧机号数据");
    }


    /**
     * 查询特殊舱位机号管理列表
     */
    @GetMapping("/listCabins")
    @ApiOperation(value = "查询特殊供氧机号管理列表")
    public TableDataInfo listCabins(BaseCraftNo baseCraftNo)
    {
        startPage();
        baseCraftNo.setType(3);
        List<BaseCraftNo> list = baseCraftNoService.selectCraftNoList(baseCraftNo);
        return getDataTable(list);
    }

    /**
     * 新增特殊舱位机号管理
     */
    @PostMapping("/addCabins")
    @ApiOperation(value = "新增特殊舱位机号管理")
    public AjaxResult addCabins(@RequestBody BaseCraftNo baseCraftNo)
    {
        baseCraftNo.setType(3);
        return AjaxResult.success(baseCraftNoService.insertCraft(baseCraftNo));
    }

    /**
     * 导出特殊舱位机号数据
     */
    @PostMapping("/exportCabins")
    @ApiOperation(value = "导出特殊舱位机号数据")
    public void exportCabins(HttpServletResponse response, BaseCraftNo baseCraftNo)
    {
        baseCraftNo.setType(3);
        List<BaseCraftNo> list = baseCraftNoService.selectCraftNoList(baseCraftNo);
        ExcelUtil<BaseCraftNo> util = new ExcelUtil<BaseCraftNo>(BaseCraftNo.class);
        util.exportExcel(response, list, "特殊舱位机号数据");
    }

    /**
     * 查询机号管理详情
     */
    @GetMapping("/getInfo/{id}")
    @ApiOperation(value = "查询机号管理详情")
    public AjaxResult getInfo(@PathVariable Long id)
    {
        BaseCraftNo baseCraftNo = baseCraftNoService.getInfoCraftNo(id);
        return AjaxResult.success(baseCraftNo);
    }

    /**
     * 删除机号
     */
    @GetMapping("/delete/{id}")
    @ApiOperation(value = "删除机号")
    public AjaxResult delete(@PathVariable Long id)
    {
        return AjaxResult.success(baseCraftNoService.delete(id));
    }

    /**
     * 修改机号
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改机号")
    public AjaxResult update(@RequestBody BaseCraftNo baseCraftNo)
    {
        return AjaxResult.success(baseCraftNoService.update(baseCraftNo));
    }

    /**
     * 根据航班号和航班时间查询相应机号配置
     * */
    @PostMapping("/selectConfig")
    @ApiOperation(value = "根据航班号和航班时间查询相应机号配置")
    public AjaxResult selectConfig(@RequestBody BaseCraftNoQuery query)
    {
        baseCraftNoService.selectCraftNoConfig(query);
        return AjaxResult.success(1);
    }


}
