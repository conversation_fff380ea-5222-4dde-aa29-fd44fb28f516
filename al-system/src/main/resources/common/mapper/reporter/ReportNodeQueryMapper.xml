<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.reporter.mapper.ReportNodeQueryMapper">
    <insert id="saveOrUpdateBatch">
        insert into all_report_node_query
            (
                id, waybill_code, flight_no, terminal_alteratel_and_in_time, tally_time, order_time, handover_time, pick_up_time, out_time,
                tally_consume, site_consume, pay_time, pick_up_consume, out_consume
            )
        values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.waybillCode}, #{item.flightNo}, #{item.terminalAlteratelAndInTime}, #{item.tallyTime}, #{item.orderTime}, #{item.handoverTime},
                #{item.pickUpTime}, #{item.outTime}, #{item.tallyConsume}, #{item.siteConsume}, #{item.payTime}, #{item.pickUpConsume}, #{item.outConsume}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            waybill_code = VALUES(waybill_code),
            flight_no = VALUES(flight_no),
            terminal_alteratel_and_in_time = VALUES(terminal_alteratel_and_in_time),
            tally_time = VALUES(tally_time),
            order_time = VALUES(order_time),
            handover_time = VALUES(handover_time),
            pick_up_time = VALUES(pick_up_time),
            out_time = VALUES(out_time),
            tally_consume = VALUES(tally_consume),
            site_consume = VALUES(site_consume),
            pay_time = VALUES(pay_time),
            pick_up_consume = VALUES(pick_up_consume),
            out_consume = VALUES(out_consume)
    </insert>
</mapper>