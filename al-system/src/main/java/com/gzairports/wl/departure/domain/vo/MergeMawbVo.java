package com.gzairports.wl.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 主分单拼单主单展示参数
 *
 * <AUTHOR>
 * @date 2024-05-23
 */
@Data
public class MergeMawbVo {

    /** 主键id */
    private Long id;

    /** 运单号 */
    private String waybillCode;

    /** 拼单状态 0 未拼单 1 已拼单 */
    private Integer mergeStatus;

    /** 承运人 */
    private String carrier1;

    /** 目的站 */
    private String desPort;

    /** 总件数 */
    private Integer totalCount;

    /** 总重量 */
    private BigDecimal totalWeight;

    /** 计费重量 */
    private BigDecimal chargeWeight;

    /** 已拼单数 */
    private Integer totalMerge;

    /** 已拼单重量 */
    private BigDecimal totalMergeWeight;

    /** 发货人 */
    private String shipper;

    /** 收货人 */
    private String consign;

    /** 预配航班 */
    private String flight1;

    /** 制单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date writeTime;
}
