package com.gzairports.wl.charge.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.gzairports.common.annotation.Excel;
import com.gzairports.wl.departure.domain.RateItem;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 公布运价条目表
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@Data
@TableName("wl_sf_report_price_item")
public class FreightRateItem extends Model<FreightRateItem> implements RateItem {

    /** 主键id */
    private Long id;

    /** 所属运价id */
    private Long rateId;

    /** 运价名称 */
    @TableField(exist = false)
    private String rateName;

    /** 运价名称 */
    @Excel(name = "条款名称")
    private String clauseName;

    /** 适用单证类型 */
    private String applicableDocuments;

    /** 始发城市 */
    @Excel(name = "始发城市")
    private String departureCity;

    /** 目的城市 */
    @Excel(name = "目的城市")
    private String destinationCity;

    /** 航司 */
    private String airline;

    /** 航班号 */
    @Excel(name = "航班号")
    private String flightNumber;

    /** 货物类别 */
    private String cargoCategory;

    /** 中转城市 */
    @Excel(name = "中转城市")
    private String transitCity;

    /** 特货代码 */
    @Excel(name = "特货代码")
    private String specialCargoCode;

    /** 货物代码 */
    @Excel(name = "货物种类")
    private String productCode;

    /** 最低运费 */
    @Excel(name = "结算M")
    private BigDecimal minimumFreight;

    /** 运价重量区间5 */
    @TableField(value = "rate_weight_range_5")
    @Excel(name = "结算5")
    private BigDecimal rateWeightRange5;

    /** 运价重量区间10 */
    @TableField(value = "rate_weight_range_10")
    @Excel(name = "结算10")
    private BigDecimal rateWeightRange10;

    /** 基础运价N */
    @Excel(name = "结算N")
    private BigDecimal baseRateN;

    /** 运价重量区间45 */
    @TableField(value = "rate_weight_range_45")
    @Excel(name = "结算45")
    private BigDecimal rateWeightRange45;

    /** 运价重量区间100 */
    @TableField(value = "rate_weight_range_100")
    @Excel(name = "结算100")
    private BigDecimal rateWeightRange100;

    /** 运价重量区间300 */
    @TableField(value = "rate_weight_range_300")
    @Excel(name = "结算300")
    private BigDecimal rateWeightRange300;

    /** 运价重量区间500 */
    @TableField(value = "rate_weight_range_500")
    @Excel(name = "结算500")
    private BigDecimal rateWeightRange500;

    /** 运价重量区间1000 */
    @TableField(value = "rate_weight_range_1000")
    @Excel(name = "结算1000")
    private BigDecimal rateWeightRange1000;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    /** 所属单位 */
    private Long deptId;

    /** 条目是否逻辑删除 */
    private Integer isDelete;

    /** 更新时间 */
    private Date updateTime;

    /** 更新人 */
    private String updateBy;

    /** 创建时间 */
    private Date createTime;

    /** 创建人 */
    private String createBy;
}
