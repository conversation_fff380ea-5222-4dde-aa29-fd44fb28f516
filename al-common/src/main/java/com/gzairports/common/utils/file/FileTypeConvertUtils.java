package com.gzairports.common.utils.file;

import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

@Slf4j
public class FileTypeConvertUtils {

    /**
     * 处理pdf文件,返回base64编码的图片
     * @param inputPdfPath
     * @return
     */
    public static String pdfToBase64Image(String inputPdfPath) {
        try {
            // 加载PDF文档
            byte[] downloadedBytes = HttpUtil.downloadBytes(inputPdfPath);
            return pdfToBase64Image(downloadedBytes);
        } catch (IOException e) {
            log.error("{} pdf转png失败:{}", inputPdfPath, e);
            throw new RuntimeException(e);
        }
    }

    //将每页pdf转成png并存储
    public static String pdfToBase64Image(byte[] downloadedBytes) throws IOException {
        PDDocument pdDocument = PDDocument.load(downloadedBytes);
        return pdfToBase64Image(pdDocument);
    }

    //将每页pdf转成png并存储
    public static String pdfToBase64Image(InputStream inputStream) throws IOException {
        PDDocument pdDocument = PDDocument.load(inputStream);
        return pdfToBase64Image(pdDocument);
    }

    //将每页pdf转成png并存储
    private static String pdfToBase64Image(PDDocument document) throws IOException {
        PDFRenderer renderer = new PDFRenderer(document);
        // 获取PDF页面数量
        int pageCount = document.getNumberOfPages();
        // 存储转换后的PNG图像
        List<BufferedImage> images = new ArrayList<>();
        // 遍历每一页并将其转换为PNG图像
        for (int i = 0; i < pageCount; i++) {
            BufferedImage image = renderer.renderImageWithDPI(i, 100);
//      ImageIO.write(image,"PNG",new File(i+".png"));
            images.add(image);
        }
        // 关闭PDF文档
        document.close();

        // 将PNG图像垂直排列合并为一个图像
        return mergeVertically(images);
    }

    /**
     * 合并png为一个整体并返回base64编码
     * @param images
     * @return
     * @throws IOException
     */
    private static String mergeVertically(List<BufferedImage> images) throws IOException {
        // 计算合并后的图像尺寸
        int totalHeight = 0;
        for (BufferedImage image : images) {
            totalHeight += image.getHeight();
        }

        // 创建一个新的空白图像，用于存放合并后的图像
        BufferedImage mergedImage = new BufferedImage(images.get(0).getWidth(), totalHeight, BufferedImage.TYPE_INT_RGB);

        // 将每一页的图像垂直堆叠在一起
        Graphics g = mergedImage.getGraphics();
        g.setColor(Color.WHITE);
        g.fillRect(0, 0, mergedImage.getWidth(), mergedImage.getHeight());
        int y = 0;
        for (BufferedImage image : images) {
            g.drawImage(image, 0, y, null);
            y += image.getHeight();
        }


        // 将合并后的图像写入ByteArrayOutputStream
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(mergedImage, "PNG", baos);
        baos.flush();
        byte[] imageBytes = baos.toByteArray();
        baos.close();

        // 将字节数组转换为Base64编码的字符串
        return Base64.getEncoder().encodeToString(imageBytes);
    }

}
