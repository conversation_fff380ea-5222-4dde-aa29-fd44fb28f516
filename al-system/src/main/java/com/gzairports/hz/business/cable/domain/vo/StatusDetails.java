package com.gzairports.hz.business.cable.domain.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-04-07
 */
@Data
public class StatusDetails {

    /** 状态代码 */
    private String statusCode;

    /** 交付名称 */
    private String deliveryName;

    private String densityGroup;

    private String departureDayChangeIndicator;

    private String receivedName;

    private String volume;


    private String volumeUnit;

    /** 启运标识 */
    private String departureIndicator;

    /** 启运港 */
    private String depAirport;

    /** 启运时间 */
    private String departureTime;

    /** 异常代码 */
    private String discrepancyCode;

    /** 运输机场 */
    private String movementAirport;

    /** 到达机场 */
    private String movementArrivalAirport;

    /** 运输承运人 */
    private String movementCarrier;

    /** 运输日期 */
    private String movementDate;

    /** 启运机场 */
    private String movementDepartureAirport;

    /** 运输航班号 */
    private String movementFlightNo;

    /** 运输时间 */
    private String movementTime;

    /** 通知名称 */
    private String notificationName;

    /** 装载件数 */
    private String pieces;

    /** 运输描述代码
     分割托运 D
     多批托运 M
     部分托运 P
     拆分托运 S
     总托运 T*/
    private String shipmentDescriptionCode;

    /** 转运舱单号 */
    private String transferManifestNumber;

    /** 转运名称 */
    private String transferredName;

    /** 重量 */
    private String weight;

    /** 重量单位 */
    private String weightUnit;

    private String receivingCarrier;
    private String movementDayChangeIndicator;
    private String arrivalIndicator;
    private String arrivalTime;
    private String arrivalDayChangeIndicator;
    private String transferredCarrier;
    private String reportingDate;
    private String reportingTime;
    private String reportingAirport;
}
