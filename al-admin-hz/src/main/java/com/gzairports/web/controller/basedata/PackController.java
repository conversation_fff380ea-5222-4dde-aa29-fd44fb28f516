package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BasePack;
import com.gzairports.common.basedata.domain.query.BasePackQuery;
import com.gzairports.common.basedata.service.IPackService;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 包装管理Controller
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@RestController
@RequestMapping("/base/pack")
@Api(tags = "包装管理")
public class PackController extends BaseController {

    @Autowired
    private IPackService packService;

    /**
     * 查询包装管理列表
     */
//    @PreAuthorize("@ss.hasPermi('base:pack:BasePackList')")
    @GetMapping("/BasePackList")
    @ApiOperation(value = "查询包装管理列表")
    public TableDataInfo basePackList(BasePackQuery query)
    {
        startPage();
        List<BasePack> list = packService.selectBasePackList(query);
        return getDataTable(list);
    }

    /**
     * 新增包装管理
     */
    @PreAuthorize("@ss.hasPermi('base:pack:addBasePack')")
    @Log(title = "新增包装管理", businessType = BusinessType.INSERT)
    @PostMapping("/addBasePack")
    @ApiOperation(value = "新增包装管理")
    public AjaxResult addBasePack(@RequestBody BasePack basePack)
    {
        return toAjax(packService.addBasePack(basePack));
    }

    /**
     * 包装管理详情
     */
    @PreAuthorize("@ss.hasPermi('base:pack:getInfo')")
    @GetMapping("/{id}")
    @ApiOperation(value = "包装管理详情")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(packService.getInfo(id));
    }

    /**
     * 修改包装管理
     */
    @PreAuthorize("@ss.hasPermi('base:pack:editBasePack')")
    @Log(title = "修改包装管理", businessType = BusinessType.UPDATE)
    @PostMapping("/editBasePack")
    @ApiOperation(value = "修改包装管理")
    public AjaxResult editBasePack(@RequestBody BasePack basePack)
    {
        return toAjax(packService.editBasePack(basePack));
    }

    /**
     * 删除包装管理
     */
    @PreAuthorize("@ss.hasPermi('base:pack:delBasePack')")
    @Log(title = "删除包装管理", businessType = BusinessType.UPDATE)
    @GetMapping("/delBasePack/{id}")
    @ApiOperation(value = "删除包装管理")
    public AjaxResult delBasePack(@PathVariable("id") Long id)
    {
        return toAjax(packService.delBasePack(id));
    }
}
