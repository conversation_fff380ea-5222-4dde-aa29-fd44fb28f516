package com.gzairports.hz.business.departure.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.basedata.domain.BaseAgent;
import com.gzairports.common.basedata.domain.BaseBalance;
import com.gzairports.common.basedata.mapper.BaseAgentMapper;
import com.gzairports.common.basedata.mapper.BaseBalanceMapper;
import com.gzairports.common.business.arrival.domain.FlightLoadWaybill;
import com.gzairports.common.business.arrival.mapper.FlightLoadUldWaybillMapper;
import com.gzairports.common.business.arrival.mapper.FlightLoadWaybillMapper;
import com.gzairports.common.business.departure.domain.CostDetail;
import com.gzairports.common.business.departure.domain.WaybillFee;
import com.gzairports.common.business.departure.domain.WaybillTrace;
import com.gzairports.common.business.departure.domain.vo.LoadInfoVo;
import com.gzairports.common.business.departure.mapper.CostDetailMapper;
import com.gzairports.common.business.departure.mapper.PullDownMapper;
import com.gzairports.common.business.departure.mapper.WaybillFeeMapper;
import com.gzairports.common.business.departure.service.impl.WaybillTraceServiceImpl;
import com.gzairports.common.charge.domain.HzChargeIrRelation;
import com.gzairports.common.charge.domain.HzChargeItemRule;
import com.gzairports.common.charge.domain.HzChargeItems;
import com.gzairports.common.charge.domain.HzChargeRule;
import com.gzairports.common.charge.domain.vo.BillRuleVo;
import com.gzairports.common.charge.mapper.HzChargeIrRelationMapper;
import com.gzairports.common.charge.mapper.HzChargeItemRuleMapper;
import com.gzairports.common.charge.mapper.HzChargeItemsMapper;
import com.gzairports.common.charge.mapper.HzChargeRuleMapper;
import com.gzairports.common.charge.service.BillingRule;
import com.gzairports.common.charge.service.impl.BillingRuleFactory;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.common.log.service.impl.WaybillLogServiceImpl;
import com.gzairports.common.utils.BigDecimalRoundUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.ServletUtils;
import com.gzairports.hz.business.departure.domain.*;
import com.gzairports.hz.business.departure.domain.query.WaybillInfoQuery;
import com.gzairports.hz.business.departure.domain.query.WaybillStatusQuery;
import com.gzairports.hz.business.departure.domain.vo.*;
import com.gzairports.hz.business.departure.mapper.*;
import com.gzairports.hz.business.departure.service.IWaybillStatusService;
import com.gzairports.wl.departure.domain.MawbErrorRemark;
import com.gzairports.wl.departure.domain.MawbItem;
import com.gzairports.wl.departure.domain.vo.ReplenishVo;
import com.gzairports.wl.departure.mapper.MawbErrorRemarkMapper;
import com.gzairports.wl.departure.mapper.MawbItemMapper;
import com.gzairports.wl.ticket.domain.TicketNum;
import com.gzairports.wl.ticket.mapper.TicketMapper;
import com.gzairports.wl.ticket.mapper.TicketNumMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 运单状态Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Service
public class WaybillStatusServiceImpl implements IWaybillStatusService {

    @Autowired
    private AllAirWaybillMapper airWaybillMapper;

    @Autowired
    private HzCollectWaybillMapper collectWaybillMapper;

    @Autowired
    private HzCollectWeightMapper collectWeightMapper;

    @Autowired
    private FlightLoadUldWaybillMapper loadUldWaybillMapper;

    @Autowired
    private FlightLoadWaybillMapper loadWaybillMapper;

    @Autowired
    private PullDownMapper pullDownMapper;

    @Autowired
    private ExitCargoMapper exitCargoMapper;

    @Autowired
    private FlightLoadMapper loadMapper;

    @Autowired
    private CostDetailMapper costDetailMapper;

    @Autowired
    private WaybillLogServiceImpl waybillLogService;

    @Autowired
    private BaseAgentMapper baseAgentMapper;

    @Autowired
    private BaseBalanceMapper baseBalanceMapper;

    @Autowired
    private WaybillFeeMapper feeMapper;

    @Autowired
    private HzChargeRuleMapper ruleMapper;

    @Autowired
    private HzChargeIrRelationMapper relationMapper;

    @Autowired
    private HzChargeItemsMapper chargeItemsMapper;

    @Autowired
    private HzChargeItemRuleMapper itemRuleMapper;

    @Autowired
    private TicketNumMapper ticketNumMapper;

    @Autowired
    private MawbItemMapper mawbItemMapper;

    @Autowired
    private TicketMapper ticketMapper;

    @Autowired
    private MawbErrorRemarkMapper errorRemarkMapper;

    @Autowired
    private WaybillTraceServiceImpl waybillTraceService;

    /**
     * 运单状态列表查询
     * @param query 查询条件
     * @return 列表数据
     */
    @Override
    public List<WaybillStatusVo> selectList(WaybillStatusQuery query) {
        switch (query.getWaybillStatus()) {
            case "录单":
                //填开时间
                List<WaybillStatusVo> waybillStatusVos = airWaybillMapper.selectWaybillStatusListForRecordOrder(query);
                getAirCost(waybillStatusVos);
                return waybillStatusVos;
            case "收运":
                //收运时间
                List<WaybillStatusVo> waybillStatusVos1 = airWaybillMapper.selectWaybillStatusListForCollect(query);
                getAirCost(waybillStatusVos1);
                return waybillStatusVos1;
            case "退货":
                //发起时间
                List<WaybillStatusVo> waybillStatusVos2 = airWaybillMapper.selectWaybillStatusListForExitCargo(query);
                getAirCost(waybillStatusVos2);
                return waybillStatusVos2;
            case "航班承运":
                //航班时间
                List<WaybillStatusVo> waybillStatusVos3 = airWaybillMapper.selectWaybillStatusListForFlightAcceptance(query);
                getAirCost(waybillStatusVos3);
                return waybillStatusVos3;
            default:
                return null;
        }
    }

    private void getAirCost(List<WaybillStatusVo> waybillStatusVos1) {
        for (WaybillStatusVo waybillStatusVo : waybillStatusVos1) {
            List<MawbItem> list = mawbItemMapper.selectList(new QueryWrapper<MawbItem>()
                    .eq("waybill_code", waybillStatusVo.getWaybillCode())
                    .eq("dept_id", waybillStatusVo.getDeptId())
                    .eq("is_del", 0));
            if (!CollectionUtils.isEmpty(list)) {
                List<MawbItem> airRates = list.stream().filter(e -> e.getRateType().equals(1)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(airRates)) {
                    MawbItem item = airRates.get(0);
                    waybillStatusVo.setRatePerKg(item.getRate());
                    waybillStatusVo.setAirFreight(item.getCharging().setScale(2, RoundingMode.DOWN));
                }
            }
        }
    }

    /**
     * 运单明细查询
     * @param query 查询条件
     * @return 结果
     */
    @Override
    public WaybillInfoVo getInfo(WaybillInfoQuery query) {
        if (query.getWaybillCode() == null){
            throw new CustomException("请输入完整运单号");
        }
        WaybillInfoVo vo = airWaybillMapper.getStatusInfo(query);
        if (vo != null){
            Integer count = exitCargoMapper.selectCount(new QueryWrapper<HzDepExitCargo>().eq("waybill_code", vo.getWaybillCode()));
            if (count > 0){
                vo.setIsExit(1);
            }else {
                vo.setIsExit(0);
            }
            List<HzCollectWaybill> collectWaybills = collectWaybillMapper.selectList(new QueryWrapper<HzCollectWaybill>().eq("waybill_id", vo.getId()));
            List<DetailedVo> detailedVos = new ArrayList<>();
            if (!CollectionUtils.isEmpty(collectWaybills)){
                for (HzCollectWaybill collectWaybill : collectWaybills) {
                    DetailedVo detailedVo = new DetailedVo();
                    List<HzCollectWeight> weights = collectWeightMapper.selectList(new QueryWrapper<HzCollectWeight>().eq("collect_id", collectWaybill.getId()));
                    if (!CollectionUtils.isEmpty(weights)){
                        int sum = weights.stream().mapToInt(HzCollectWeight::getQuantity).sum();
                        detailedVo.setQuantity(sum);
                        BigDecimal reduce = weights.stream().map(HzCollectWeight::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                        detailedVo.setWeight(reduce);
                    }else {
                        detailedVo.setQuantity(collectWaybill.getQuantity());
                        detailedVo.setWeight(collectWaybill.getWeight());
                    }
                    detailedVo.setId(collectWaybill.getId());
                    detailedVo.setUld(collectWaybill.getUld());
                    detailedVo.setOperName(collectWaybill.getOperName());
                    detailedVo.setStatus(collectWaybill.getStatus());
                    detailedVo.setOperTime(collectWaybill.getCollectTime());
                    detailedVos.add(detailedVo);
                }
                vo.setDetailedVos(detailedVos);
            }
            ReplenishVo replenish = pullDownMapper.replenish(query.getWaybillCode());
            if(replenish!=null){
                vo.setCanRestockWeight(replenish.getCanRestockWeight());
            }
        }
        return vo;
    }

    /**
     * 过磅记录
     * @param id 收运id
     * @return 记录
     */
    @Override
    public List<HzCollectWeight> weightInfo(Long id) {
        return collectWeightMapper.selectList(new QueryWrapper<HzCollectWeight>().eq("collect_id",id));
    }

    /**
     * 运单状态列表查询
     * @param query 查询条件
     * @return 结果
     */
    /** 类型 COLLECT 收运 LOAD 预配 PULLDOWN 拉下 EXIT 退货 INVENTORY 库存 */
    @Override
    public List<DetailedVo> getStatusList(WaybillInfoQuery query) {
        if (query.getWaybillCode() == null){
            return null;
        }
        List<DetailedVo> list = new ArrayList<>();
        switch (query.getType()){
            case "COLLECT":
                List<HzCollectWaybill> collectWaybills = collectWaybillMapper.selectList(new QueryWrapper<HzCollectWaybill>().eq("waybill_id", query.getId()));
                if (!CollectionUtils.isEmpty(collectWaybills)){
                    for (HzCollectWaybill collectWaybill : collectWaybills) {
                        List<HzCollectWeight> weights = collectWeightMapper.selectList(new QueryWrapper<HzCollectWeight>().eq("collect_id", collectWaybill.getId()));
                        DetailedVo detailedVo = new DetailedVo();
                        if (!CollectionUtils.isEmpty(weights)){
                            int sum = weights.stream().mapToInt(HzCollectWeight::getQuantity).sum();
                            detailedVo.setQuantity(sum);
                            BigDecimal reduce = weights.stream().map(HzCollectWeight::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                            detailedVo.setWeight(reduce);
                        }else {
                            detailedVo.setQuantity(collectWaybill.getQuantity());
                            detailedVo.setWeight(collectWaybill.getWeight());
                        }
                        detailedVo.setId(collectWaybill.getId());
                        detailedVo.setUld(collectWaybill.getUld());
                        detailedVo.setOperName(collectWaybill.getOperName());
                        detailedVo.setStatus(collectWaybill.getStatus());
                        detailedVo.setOperTime(collectWaybill.getCollectTime());
                        list.add(detailedVo);
                    }
                }
                break;
            case "LOAD":
                list = loadMapper.selectStatusLoadList(query);
                break;
            case "PULLDOWN":
                String waybillCode = query.getWaybillCode();
                List<HzDepPullDown> listByWaybillCode = pullDownMapper.selectList(new QueryWrapper<HzDepPullDown>()
                        .eq("waybill_code", waybillCode));

                //收集listByWaybillCode中的所有HzDepPullDown对象的属性flightLoadId都不为空的Id集合
                List<Long> pullIdForFlightLoadIdNotNull = listByWaybillCode.stream()
                        .filter(item -> item.getFlightId() != null)
                        .map(HzDepPullDown::getId)
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(pullIdForFlightLoadIdNotNull)){
                    List<DetailedVo> detailedVos = pullDownMapper.selectPullDownListForFlightLoadIdNotNull(pullIdForFlightLoadIdNotNull);
                    list.addAll(detailedVos);
                }

                //这里是flightLoadId为空的Id集合
                List<Long> pullIdForFlightLoadIdNull = listByWaybillCode.stream()
                        .filter(item -> item.getFlightId() == null)
                        .map(HzDepPullDown::getId)
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(pullIdForFlightLoadIdNull)){
                    List<DetailedVo> detailedVos = pullDownMapper.selectPullDownListForFlightLoadIdNull(pullIdForFlightLoadIdNull);
                    list.addAll(detailedVos);
                }
                break;
            case "EXIT":
                list = exitCargoMapper.selectExitCargoList(query);
                break;
            case "INVENTORY":
                List<InventoryVo> vos = collectWaybillMapper.selectInventoryList(query);
                for (InventoryVo inventoryVo : vos) {
                    List<HzCollectWeight> weights = collectWeightMapper.selectList(new QueryWrapper<HzCollectWeight>().eq("collect_id", inventoryVo.getId()));
                    if (!CollectionUtils.isEmpty(weights)){
                        int sum = weights.stream().mapToInt(HzCollectWeight::getQuantity).sum();
                        inventoryVo.setQuantity(sum);
                        BigDecimal reduce = weights.stream().map(HzCollectWeight::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                        inventoryVo.setWeight(reduce);
                        HzCollectWeight hzCollectWeight = weights.get(0);
                        inventoryVo.setStore(hzCollectWeight.getStore());
                        inventoryVo.setLocator(hzCollectWeight.getLocator());
                    }
                    List<FlightLoadWaybill> uldWaybills = loadUldWaybillMapper.selectLoadList(inventoryVo.getId());
                    if (!CollectionUtils.isEmpty(uldWaybills)){
                        Map<Long, List<FlightLoadWaybill>> collect = uldWaybills.stream().collect(Collectors.groupingBy(FlightLoadWaybill::getFlightLoadId));
                        for (Map.Entry<Long, List<FlightLoadWaybill>> longListEntry : collect.entrySet()) {
                            int sum = longListEntry.getValue().stream().mapToInt(FlightLoadWaybill::getQuantity).sum();
                            BigDecimal reduce = longListEntry.getValue().stream().map(FlightLoadWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                            FlightLoadWaybill flightLoadWaybill = longListEntry.getValue().get(0);
                            if (flightLoadWaybill.getStartRealTakeoffTime() != null && flightLoadWaybill.getStartRealTakeoffTime().before(new Date())){
                                int inventoryQuantity = inventoryVo.getCollectQuantity() - sum;
                                inventoryVo.setInventoryQuantity(inventoryQuantity);
                                BigDecimal inventoryWeight = inventoryVo.getCollectWeight().subtract(reduce);
                                inventoryVo.setInventoryWeight(inventoryWeight);
                            }else {
                                inventoryVo.setInventoryQuantity(inventoryVo.getCollectQuantity());
                                inventoryVo.setInventoryWeight(inventoryVo.getCollectWeight());
                            }
                        }
                    }else {
                        inventoryVo.setInventoryQuantity(inventoryVo.getCollectQuantity());
                        inventoryVo.setInventoryWeight(inventoryVo.getCollectWeight());
                    }
                    DetailedVo detailedVo = new DetailedVo();
                    detailedVo.setStoreName(inventoryVo.getStore());
                    detailedVo.setLocator(inventoryVo.getLocator());
                    detailedVo.setUld(inventoryVo.getUld());
                    detailedVo.setQuantity(inventoryVo.getInventoryQuantity() < 0 ? 0 : inventoryVo.getInventoryQuantity());
                    if (inventoryVo.getInventoryWeight() != null && inventoryVo.getInventoryWeight().compareTo(new BigDecimal(0)) < 0){
                        detailedVo.setWeight(new BigDecimal(0));
                    }else {
                        detailedVo.setWeight(inventoryVo.getInventoryWeight());
                    }
                    list.add(detailedVo);
                }
                break;
            default:
                throw new CustomException("无当前状态信息");
        }
        return list;
    }

    /**
     * 运单明细查询收费
     * @param query 查询条件
     * @return 收费数据
     */
    @Override
    public ChargeStatusVo chargeStatus(WaybillInfoQuery query) {
        if (query.getWaybillCode() == null){
            return null;
        }
        ChargeStatusVo vo = airWaybillMapper.selectChargeInfo(query);
        List<CostDetail> payList = costDetailMapper.selectPayOrSettleList(query.getWaybillCode(),0,3, vo.getDeptId());
        if (!CollectionUtils.isEmpty(payList)){
            BigDecimal reduce = payList.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setTotalPay(reduce);
            vo.setPayList(payList);
        }
        List<CostDetail> settleList = costDetailMapper.selectPayOrSettleList(query.getWaybillCode(),1,3, vo.getDeptId());
        if (!CollectionUtils.isEmpty(settleList)){
            BigDecimal reduce = settleList.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setTotalSettle(reduce);
            vo.setSettleList(settleList);
        }
        return vo;
    }

    /**
     * 运单作废
     * @param id 运单id
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int waybillCancel(Long id) {
        if (id == null) {
            throw new CustomException("作废id不能为空");
        }
        AirWaybill mawb = airWaybillMapper.selectById(id);
        if (mawb == null) {
            throw new CustomException("无当前主单信息");
        }
        if ("INVALID".equals(mawb.getStatus())){
            throw new CustomException("当前运单已作废");
        }
        if(mawb.getPayStatus() > 0 && mawb.getPayStatus() < 14){
            throw new CustomException("运单已支付,作废失败");
        }
        List<LoadInfoVo> infoVos = loadWaybillMapper.selectByLoadInfo(mawb.getId());
        if (!CollectionUtils.isEmpty(infoVos)){
            throw new CustomException("当前运单已配载不能作废");
        }
        AirWaybill updateWaybill = new AirWaybill();
        updateWaybill.setId(mawb.getId());
        updateWaybill.setVersion(mawb.getVersion());
        updateWaybill.setWaybillCode(mawb.getWaybillCode());
        WaybillTrace waybillTrace = new WaybillTrace();
        waybillTrace.setOperTime(new Date());
        waybillTrace.setOperPieces(mawb.getQuantity());
        waybillTrace.setOperWeight(mawb.getWeight());
        waybillTrace.setWaybillCode(mawb.getWaybillCode());
        waybillTrace.setNodeName("已作废");
        waybillTraceService.insertWaybillTrace(waybillTrace);

        BigDecimal weightRate;
        BigDecimal chargeWeight = mawb.getChargeWeight() == null ? new BigDecimal(0) : mawb.getChargeWeight();
        if (mawb.getWeight() == null || mawb.getWeight().compareTo(new BigDecimal(0)) == 0){
            weightRate = new BigDecimal(0);
        }else {
            BigDecimal bigDecimal = chargeWeight.divide(mawb.getWeight(),5, RoundingMode.DOWN).multiply(mawb.getWeight());
            weightRate = bigDecimal.setScale(0, RoundingMode.CEILING);
        }
        //运单日志的处理
        HttpServletResponse response = ServletUtils.getResponse();
        //运单日志的新增
        WaybillLog waybillLog = waybillLogService.getWaybillLog(
                mawb.getWaybillCode(), 0, SecurityUtils.getNickName(),
                mawb.getWeight().toString(), mawb.getQuantity().toString(), mawb.getFlightNo1(),
                mawb.getWaybillCode(), null, 0, null, new Date(),
                "运单作废", "DEP", null);
        try {
            BaseAgent agent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", mawb.getDeptId()));
            BigDecimal costSum = new BigDecimal(0);
            List<CostDetail> details = costDetailMapper.selectPayOrSettleList(mawb.getWaybillCode(),0,1, mawb.getDeptId());
            if (!CollectionUtils.isEmpty(details)){
                List<CostDetail> collect = details.stream().filter(e -> "处置费".equals(e.getChargeAbb())).collect(Collectors.toList());
                for (CostDetail detail : collect) {
                    countCost(detail, mawb.getWeight(), weightRate, mawb.getQuantity());
                    costSum = costSum.add(detail.getTotalCharge());
                }
            }
            WaybillFee waybillFee = feeMapper.selectOne(new QueryWrapper<WaybillFee>()
                    .eq("waybill_code", mawb.getWaybillCode())
                    .eq("dept_id", mawb.getDeptId())
                    .eq("type","DEP"));
            if (agent != null) {
                if (agent.getSettleMethod() == 0){
                    updateStatus(updateWaybill, costSum, waybillFee,mawb.getDeptId(),9);
                }else if (agent.getSettleMethod() == 1){
                    BigDecimal balance = agent.getBalance() == null ? new BigDecimal(0) : agent.getBalance();
                    BigDecimal subtract = balance.add(costSum);
                    agent.setBalance(subtract);
                    baseAgentMapper.updateBaseAgent(agent);
                    BaseBalance baseBalance = new BaseBalance();
                    baseBalance.setAgentId(agent.getId());
                    baseBalance.setBalance(agent.getBalance());
                    baseBalance.setType("增加余额");
                    baseBalance.setCreateTime(new Date());
                    baseBalance.setCreateBy(SecurityUtils.getNickName());
                    // todo 流水号需从银联支付接口获取
                    //baseBalance.setSerialNo();
                    baseBalance.setTradeMoney(costSum);
                    baseBalance.setWaybillCode(mawb.getWaybillCode());
                    baseBalance.setRemark("增加余额");
                    baseBalanceMapper.insertBaseBalance(baseBalance);
                    updateStatus(updateWaybill, costSum, waybillFee,mawb.getDeptId(),10);
                }else {
                    if (agent.getPayMethod() == 0){
                        updateStatus(updateWaybill, costSum, waybillFee, mawb.getDeptId(), 11);
                    }else {
                        updateStatus(updateWaybill, costSum, waybillFee, mawb.getDeptId(), 12);
                    }
                }
            }
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作成功" +  "," +
                            "code:" + response.getStatus() + "," +
                            "data:" + 1));
            return 1;
        } catch (Exception e) {
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" +  "," +
                            "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        } finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }

    /**
     * 删除运单
     * @param id 运单id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int waybillDel(Long id) {
        if (id == null){
            throw new CustomException("运单id不能为空");
        }
        AirWaybill mawb = airWaybillMapper.selectDelInfoById(id);
        if (mawb == null){
            throw new CustomException("无当前主单信息");
        }
        if (!"INVALID".equals(mawb.getStatus())){
            throw new CustomException("当前运单未处于作废状态");
        }
        String code = mawb.getWaybillCode().substring(0, 4);
        Integer ticketNum = Integer.valueOf(mawb.getWaybillCode().substring(7,14));
        Long ticketId = ticketMapper.selectCheck(code, mawb.getWaybillCode().substring(4,7), ticketNum);
        if (ticketId != null){
            TicketNum num = ticketNumMapper.selectOne(new QueryWrapper<TicketNum>().eq("ticket_id", ticketId).eq("num", ticketNum));
            if (num == null){
                throw new CustomException("当前运单未发放");
            }
            num.setStatus("NOTUSED");
            ticketNumMapper.updateById(num);
        }
        List<MawbItem> items = mawbItemMapper.selectList(new QueryWrapper<MawbItem>().eq("waybill_code", mawb.getWaybillCode()));
        items.forEach(e->{
            e.setIsDel(1);
            mawbItemMapper.updateById(e);
        });
        List<MawbErrorRemark> errorRemarks = errorRemarkMapper.selectList(new QueryWrapper<MawbErrorRemark>().eq("waybill_code", mawb.getWaybillCode()));
        errorRemarks.forEach(e->{
            e.setIsDel(1);
            errorRemarkMapper.updateById(e);
        });
        int i = airWaybillMapper.updateDelById(id, mawb.getVersion());
        if (i == 0){
            throw new CustomException("删除失败，运单已被其他操作修改，请刷新后重试");
        }
        return i;
    }

    /**
     * 运单出港状态查询
     * @param status 查询条件
     * @return 结果 英文字符串
     * */
    public String getStatus(String status){
        switch (status){
            case "录单":
                /*return "record_order";*/
                return "been_sent";
            case "收运":
                return "put_in";
            case "退货":
                return "been_return";
            case "航班承运":
                return "FlightAcceptance";
            default:
                throw new CustomException("无当前运单出港状态信息");
        }
    }

    private static Map<Integer,String> STATUSMAP = new HashMap<>();
    static {
        STATUSMAP.put(0,"been_sent");
        STATUSMAP.put(1,"pre_pay");
        STATUSMAP.put(2,"put_in");
        STATUSMAP.put(3,"refuse_collect");
        STATUSMAP.put(4,"been_sent");
        STATUSMAP.put(5,"been_sent");
        STATUSMAP.put(6,"been_sent");
        STATUSMAP.put(7,"been_sent");
        STATUSMAP.put(8,"been_sent");
    }

    private void updateStatus(AirWaybill mawb, BigDecimal costSum, WaybillFee waybillFee, Long parentId, Integer payStatus) {
        if (waybillFee != null) {
            waybillFee.setRefund(costSum);
            waybillFee.setSettleMoney(costSum);
            waybillFee.setSettleTime(new Date());
            waybillFee.setRefund(costSum);
            waybillFee.setStatus(2);
            feeMapper.updateById(waybillFee);
        } else {
            WaybillFee fee = new WaybillFee();
            fee.setRefund(costSum);
            fee.setSettleMoney(costSum);
            fee.setDeptId(parentId);
            fee.setSettleTime(new Date());
            fee.setWaybillCode(mawb.getWaybillCode());
            fee.setStatus(2);
            fee.setType("DEP");
            feeMapper.insert(fee);
        }
        mawb.setPayStatus(payStatus);
        mawb.setRefund(costSum);
        mawb.setSettleTime(new Date());
        mawb.setStatus("INVALID");
        int i = airWaybillMapper.updateById(mawb);
        if (i == 0){
            throw new CustomException("作废失败，运单已被其他操作修改，请刷新后重试");
        }
    }

    public BillRuleVo countCost(CostDetail detail,BigDecimal weight, BigDecimal weightRate, Integer quantity) {
        HzChargeIrRelation relation = relationMapper.selectById(detail.getIrId());
        BillRuleVo vo = new BillRuleVo();
        if (relation == null){
            vo.setTotalCharge(new BigDecimal(0));
            return vo;
        }
        HzChargeItems hzChargeItems = chargeItemsMapper.selectById(relation.getItemId());
        HzChargeRule hzChargeRule = ruleMapper.selectById(relation.getRuleId());
        List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>().eq("ir_id", detail.getIrId()));
        if (CollectionUtils.isEmpty(itemRules)){
            vo.setTotalCharge(new BigDecimal(0));
            return vo;
        }
        BillingRule rule = BillingRuleFactory.createRule(hzChargeRule.getClassName());
        BillRuleVo ruleVo = rule.calculateFee(itemRules, weightRate, quantity, detail);
        BigDecimal bigDecimal = BigDecimalRoundUtils.bigDecimalRound(hzChargeItems.getRoundRule(), ruleVo.getTotalCharge());
        detail.setQuantity(ruleVo.getQuantity());
        detail.setFlightId(3L);
        detail.setIsSettle(1);
        detail.setSettleDepWeight(weight);
        detail.setSettleDepQuantity(quantity);
        detail.setType(1);
        detail.setCreateTime(new Date());
        detail.setTotalCharge(bigDecimal);
        detail.setRate(ruleVo.getRate());
        detail.setId(null);
        costDetailMapper.insert(detail);
        return ruleVo;
    }
}
