package com.gzairports.web.controller.departure;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.common.log.service.IWaybillLogService;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.hz.business.departure.domain.AirWaybill;
import com.gzairports.hz.business.departure.mapper.AllAirWaybillMapper;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 运单日志查询Controller
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@RestController
@RequestMapping("/dep/waybillLog")
public class WaybillLogController extends BaseController {

    @Autowired
    private IWaybillLogService logService;

    @Autowired
    private AllAirWaybillMapper allAirWaybillMapper;

    /**
     * 查询日志列表
     */
//    @PreAuthorize("@ss.hasPermi('dep:waybillLog:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询日志列表")
    public TableDataInfo list(String waybillCode){
        AirWaybill airWaybill = allAirWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                .eq("waybill_code", waybillCode)
                .eq("type", "DEP"));
        if(airWaybill!=null && !airWaybill.getDeptId().equals(SecurityUtils.getHighParentId())){
            return null;
        }
        startPage();
        List<WaybillLog> list = logService.selectList(waybillCode,"DEP");
        return getDataTable(list);
    }

    /**
     * 请求和返回数据
     */
    @PreAuthorize("@ss.hasPermi('dep:waybillLog:info')")
    @GetMapping("/info/{id}")
    @ApiOperation(value = "请求和返回数据")
    public AjaxResult info(@PathVariable("id") Long id){
        WaybillLog log = logService.info(id);
        return AjaxResult.success(log);
    }
}
