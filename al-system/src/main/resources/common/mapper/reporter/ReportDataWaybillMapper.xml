<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.reporter.mapper.ReportDataWaybillMapper">
    <select id="yearCargoAnalyse" resultType="com.gzairports.wl.reporter.domain.vo.WaybillWeightVO">
        select  DATE_FORMAT(write_time, '%m') as monthData, type, sum(weight) as weight
        from all_report_data_waybill
        where DATE_FORMAT(write_time, '%Y')  = #{year} and dept_id = #{deptId}
        GROUP BY DATE_FORMAT(write_time, '%m'), type
    </select>
    <select id="yearProfit" resultType="com.gzairports.wl.reporter.domain.vo.ReportWaybillInfoVO">
        select id, waybill_code, weight, DATE_FORMAT(write_time, '%m') as monthData
        from all_report_data_waybill
        where DATE_FORMAT(write_time, '%Y')  = #{year}
          and dept_id = #{deptId}
          and carrier1 = #{code}
          and type = '出港'
    </select>
    <select id="baseCargoAnalysis" resultType="com.gzairports.wl.reporter.domain.vo.WaybillWeightVO">
        select  DATE_FORMAT(write_time, '%m') as monthData, sum(weight) as weight, 2 as sign
        from all_report_data_waybill
        where DATE_FORMAT(write_time, '%Y')  = #{baseYear} and dept_id = #{deptId} and type = '进港'
        <if test="baseMonth != null and baseMonth.size() > 0">
            and DATE_FORMAT(write_time, '%m') in
            <foreach collection="baseMonth" item="month" open="(" separator="," close=")">
                #{month}
            </foreach>
        </if>
        GROUP BY DATE_FORMAT(write_time, '%m')
    </select>
    <select id="compareCargoAnalysis" resultType="com.gzairports.wl.reporter.domain.vo.WaybillWeightVO">
        select  DATE_FORMAT(write_time, '%m') as monthData, sum(weight) as weight, 3 as sign
        from all_report_data_waybill
        where DATE_FORMAT(write_time, '%Y')  = #{compareYear} and dept_id = #{deptId} and type = '进港'
        <if test="baseMonth != null and baseMonth.size() > 0">
            and DATE_FORMAT(write_time, '%m') in
            <foreach collection="baseMonth" item="month" open="(" separator="," close=")">
                #{month}
            </foreach>
        </if>
        GROUP BY DATE_FORMAT(write_time, '%m')
    </select>
    <select id="monthCargoAnalysis" resultType="com.gzairports.wl.reporter.domain.vo.ReportWaybillInfoVO">
        select id, waybill_code, weight, w_cost_sum, r_cost_sum, shipper
        from all_report_data_waybill
        where DATE_FORMAT(write_time, '%Y-%m')  = #{month}
          and dept_id = #{deptId}
          and type = '出港'
    </select>
    <select id="airlineProfit" resultType="com.gzairports.wl.reporter.domain.vo.AirlineWaybillVO">
        select id, waybill_code, weight, CONCAT_WS('_',source_port,des_port) as leg
        from all_report_data_waybill
        where DATE_FORMAT(write_time, '%Y-%m')  = #{month}
          and dept_id = #{deptId}
          and type = '出港' and (waybill_type = '主单' or waybill_type = 'AWBA')
    </select>
    <select id="selectBillWeightList" resultType="com.gzairports.wl.reporter.domain.vo.WaybillWeightVO">
        select id, waybill_code, weight, type
        from all_report_data_waybill
        where DATE_FORMAT(write_time, '%Y-%m')  = #{month}
          and dept_id = #{deptId}
    </select>
    <select id="selectWaybillCount" resultType="com.gzairports.wl.reporter.domain.vo.ReportWaybillInfoVO">
        select id, waybill_code, w_cost_sum, r_cost_sum from all_report_data_waybill
        where dept_id = #{deptId} and DATE_FORMAT(write_time, '%Y-%m')  = #{month}
        and writer = #{writer} and type = '出港'
    </select>
    <select id="carrierDayList" resultType="com.gzairports.wl.reporter.domain.vo.ReportWaybillInfoVO">
        select id, waybill_code, weight, carrier1, DATE_FORMAT(write_time, '%Y-%m-%d') as monthData
        from all_report_data_waybill
        where  dept_id = #{deptId}
        <if test="startTime != null">
            and write_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            and write_time <![CDATA[<=]]> #{endTime}
        </if>
        <if test="waybillType == 0">
            and (waybill_type = '主单' or waybill_type = 'AWBA')
        </if>
        <if test="waybillType == 1">
            and (waybill_type = '邮件单' or waybill_type = 'AWBM')
        </if>
    </select>
</mapper>