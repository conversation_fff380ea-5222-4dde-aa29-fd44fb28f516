package com.gzairports.hz.websocket;

import com.alibaba.fastjson2.JSON;
import com.gzairports.common.core.redis.RedisCache;
import com.gzairports.common.message.domain.vo.SocketMessageVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * webSocket 实现
 * <AUTHOR>
 */

//@ServerEndpoint("/webSocket/hzMessage/{deptId}")
@ServerEndpoint("/webSocket/hzMessage/{userId}")
@Component
public class HzWebSocketServer{

    @Autowired
    private RedisTemplate<String,String> redisTemplate;

    private static RedisCache redisCache;

    @Autowired
    private void setRedisCache(RedisCache redisCache) {
        HzWebSocketServer.redisCache = redisCache;
    }


    /**
     * 存放所有在线的客户端
     */
    private static Map<String, Session> clients = new ConcurrentHashMap<>();

    /**
     * 保存用户角色的映射
     */
    private static final Map<Session, Long> userDepts = new ConcurrentHashMap<>();

    private static final Logger log = LoggerFactory.getLogger(HzWebSocketServer.class);

    @OnOpen
    public void onOpen(Session session, @PathParam("userId") Long userId) {
        log.info("有新的客户端连接了: {}", session.getId());
        //将新用户存入在线的组
        clients.put(session.getId(), session);
        userDepts.put(session,userId);
        //去redis拉取所有的消息
        /*CompletableFuture.runAsync(()->{
            getMessageByRedis(userId);
        });*/
        getMessageByRedis(userId);
    }

    /**
     * 客户端关闭
     * @param session session
     */
    @OnClose
    public void onClose(Session session) {
        log.info("有用户断开了, id为:{}", session.getId());
        //将掉线的用户移除在线的组里
        clients.remove(session.getId());
        userDepts.remove(session);
    }

    /**
     * 发生错误
     * @param throwable e
     */
    @OnError
    public void onError(Throwable throwable) {
        throwable.printStackTrace();
    }

    /**
     * 收到客户端发来消息
     * @param message  消息对象
     */
    @OnMessage
    public void onMessage(String message) {
        log.info("服务端收到客户端发来的消息: {}", message);
        this.sendAll(message);
    }

    /**
     * 群发消息
     * @param message 消息内容
     */
    public void sendAll(String message) {
        for (Map.Entry<String, Session> sessionEntry : clients.entrySet()) {
            sessionEntry.getValue().getAsyncRemote().sendText(message);
        }
    }

    /**
     * 群发消息给特定部门的方法
     * @param userId 用户id
     * @param vo 消息对象
     */
    public void sendMessageToDept(Long userId, SocketMessageVo vo) {
        Set<Session> sessions = userDepts.entrySet().stream()
                .filter(entry -> entry.getValue().equals(userId))
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());

        sessions.forEach(session -> {
            try {
                session.getBasicRemote().sendText(JSON.toJSONString(vo.getMessage()));
            } catch (IOException e) {
                e.printStackTrace();
            }
        });
    }

    public void sendMessageToUser(List<Long> userIds, SocketMessageVo vo) {
        //这是给在线用户发的消息
        Set<Session> sessions = userDepts.entrySet().stream()
                .filter(entry -> userIds.contains(entry.getValue()))
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());

        sessions.forEach(session -> {
            try {
                session.getBasicRemote().sendText(JSON.toJSONString(vo.getMessage()));
            } catch (IOException e) {
                e.printStackTrace();
            }
        });

        //离线用户的要存起来 存入redis
        Set<Long> collect = userDepts.entrySet().stream()
                .filter(entry -> userIds.contains(entry.getValue()))
                .map(Map.Entry::getValue)
                .collect(Collectors.toSet());

        userIds.removeAll(collect);


        for (Long userId:userIds) {
            //以userId为key,vo为值存入redis,数据结构为列表
            redisTemplate.opsForList().rightPush("airc-message:" + userId,JSON.toJSONString(vo.getMessage()));
            redisTemplate.opsForList().trim("airc-message:" + userId, -100, -1);
        }
    }

    /**
     * @author: lan
     * @description: 用户刚登陆时从redis拉取所有的消息
     * @date: 2024/10/23
     */
    public void getMessageByRedis(Long userId) {
        //从redis读取key的userId的数据,其中redis采用的数据结构为集合
        ListOperations listOperations = redisCache.redisTemplate.opsForList();
        List<String> range = listOperations.range("airc-message:" + userId, 0, -1);
//        List<String> range = redisCache.redisTemplate.opsForList().range(userId.toString(), 0, -1);
         if (range != null) {
             range.forEach(message -> {
                 Set<Session> sessions = userDepts.entrySet().stream()
                         .filter(entry -> entry.getValue().equals(userId))
                         .map(Map.Entry::getKey)
                         .collect(Collectors.toSet());

                 sessions.forEach(session -> {
                     try {
                         session.getBasicRemote().sendText(JSON.toJSONString(message));
                     } catch (IOException e) {
                         e.printStackTrace();
                     }
                 });
             });
             redisCache.redisTemplate.delete("airc-message:" + userId);
         }
    }
}
