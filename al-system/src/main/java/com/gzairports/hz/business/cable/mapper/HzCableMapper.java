package com.gzairports.hz.business.cable.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.hz.business.cable.domain.HzCable;
import com.gzairports.hz.business.cable.domain.query.HzCableQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 电报数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
@Mapper
public interface HzCableMapper extends BaseMapper<HzCable>
{
    /**
     * 查询电报数据
     * 
     * @param id 电报数据主键
     * @return 电报数据
     */
    public HzCable selectHzCableById(Long id);

    /**
     * 查询电报数据列表
     * 
     * @param query 电报数据
     * @return 电报数据集合
     */
    public List<HzCable> selectHzCableList(HzCableQuery query);

    /**
     * 新增电报数据
     * 
     * @param hzCable 电报数据
     * @return 结果
     */
    public int insertHzCable(HzCable hzCable);

    /**
     * 修改电报数据
     * 
     * @param hzCable 电报数据
     * @return 结果
     */
    public int updateHzCable(HzCable hzCable);
}
