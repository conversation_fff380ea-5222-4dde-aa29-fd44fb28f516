package com.gzairports.hz.business.departure.domain.vo;

import com.gzairports.common.business.departure.domain.CostDetail;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 收费管理明细返回参数
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
public class ChargeInfoVo {

    /** 预支付合计 */
    private BigDecimal payTotal;

    /** 货品编码 */
    private String cargoCode;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 预授权费用明细 */
    private List<CostDetail> details;

    /** 结算费用明细 */
    private List<SettleDetailVo> voList;

    /** 待结算费用 */
    private List<CostDetail> waitPayList;

}
