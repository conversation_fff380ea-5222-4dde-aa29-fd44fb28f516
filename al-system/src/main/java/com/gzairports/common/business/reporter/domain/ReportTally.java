package com.gzairports.common.business.reporter.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-02-19
 */
@Data
@TableName("all_report_tally")
public class ReportTally {

    /** 主键id */
    private Long id;

    /** 报表id */
    private Long reportId;

    /** 运单号 */
    private String waybillCode;

    /** 航班号 */
    private String flightNo;

    /** 理货人 */
    private String username;

    /** 舱单件数 */
    private Integer cabinPieces;

    /** 舱单重量 */
    private BigDecimal cabinWeight;

    /** 理货件数 */
    private Integer pieces;

    /** 理货重量 */
    private BigDecimal weight;

    /** 仓库 */
    private String store;

    /** 理货时间 */
    private String tallyTime;

    /** 航班日期 */
    private LocalDateTime flightDate;
}
