<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.PostMapper">
    <select id="getInfoByCode" resultType="com.gzairports.common.basedata.domain.BasePost">
        select id, office_name, office_type, air_code, link_name, phone, location, address from base_post_office
        where is_del = 0 and office_type = #{type} and office_name = #{abb}
    </select>
    <select id="getInfoByPost" resultType="com.gzairports.common.basedata.domain.BasePost">
        select id, office_name, office_type, air_code, link_name, phone, location, address from base_post_office
        where is_del = 0 and office_type = #{type} and air_code = #{port}
    </select>
</mapper>