package com.gzairports.wl.charge.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 公布运价条目返回数据
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@Data
public class FreightRateItemVO {

    /** 主键id */
    private Long id;

    /** 所属运价名称 */
    private String rateName;

    /** 条目名称 */
    private String clauseName;

    /** 适用单证类型 */
    private String applicableDocuments;

    /** 始发城市 */
    private String departureCity;

    /** 目的城市 */
    private String destinationCity;

    /** 航司 */
    private String airline;

    /** 航班号 */
    private String flightNumber;

    /** 运价类别 */
    private String rateCategory;

    /** 货物类别 */
    private String cargoCategory;

    /** 中转城市 */
    private String transitCity;

    /** 特货代码 */
    private String specialCargoCode;

    /** 货物代码 */
    private String productCode;

    /** 最低运费 */
    private BigDecimal minimumFreight;

    /** 备注 */
    private String remarks;

    /** 生效开始时间 */
    private Date startTime;

    /** 生效截止时间 */
    private Date endTime;

    /** 是否生效中 */
    private Integer isEffective;
}
