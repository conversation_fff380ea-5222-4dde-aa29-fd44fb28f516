package com.gzairports.hz.business.departure.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.hz.business.departure.domain.HzDepExitCargo;
import com.gzairports.hz.business.departure.domain.query.ExitCargoQuery;
import com.gzairports.hz.business.departure.domain.vo.ExitCargoVo;

import java.util.List;

/**
 * 退货管理Service接口
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
public interface IExitCargoService extends IService<HzDepExitCargo> {

    /**
     * 查询退货管理列表
     * @param query 查询参数
     * @return 退货列表
     */
    List<ExitCargoVo> selectExitCargoList(ExitCargoQuery query);

    /**
     * 新增退货数据
     * @param cargo 退货数据
     * @return 结果
     */
    int insertExitCargo(HzDepExitCargo cargo);

    /**
     * 修改退货数据
     * @param cargo 退货数据
     * @return 结果
     */
    int editExitCargo(HzDepExitCargo cargo);

    /**
     * 查看退货数据详情
     * @param id 退货id
     * @return 结果
     */
    ExitCargoVo getInfo(Long id);

    /**
     * 根据运单号查询详情
     * @param waybillCode 运单号
     * @return 运单详情
     */
    ExitCargoVo getWaybill(String waybillCode);
}
