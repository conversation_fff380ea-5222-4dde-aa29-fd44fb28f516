package com.gzairports.web.controller.basedata;

import com.gzairports.common.annotation.Log;
import com.gzairports.common.basedata.domain.BaseDutySchedule;
import com.gzairports.common.basedata.service.IBaseDutyScheduleService;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 值班人员信息Controller
 * 
 * <AUTHOR>
 * @date 2025-04-01
 */
@RestController
@RequestMapping("/base/duty")
public class BaseDutyScheduleController extends BaseController
{
    @Autowired
    private IBaseDutyScheduleService hzDutyScheduleService;

    /**
     * 查询值班人员信息列表
     */
//    @PreAuthorize("@ss.hasPermi('base:duty:list')")
    @GetMapping("/list")
    public TableDataInfo list(BaseDutySchedule baseDutySchedule)
    {
        startPage();
        List<BaseDutySchedule> list = hzDutyScheduleService.selectHzDutyScheduleList(baseDutySchedule);
        return getDataTable(list);
    }

    /**
     * 导出值班人员信息列表
     */
//    @PreAuthorize("@ss.hasPermi('base:duty:export')")
    @Log(title = "值班人员信息", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(BaseDutySchedule baseDutySchedule)
    {
        List<BaseDutySchedule> list = hzDutyScheduleService.selectHzDutyScheduleList(baseDutySchedule);
        ExcelUtil<BaseDutySchedule> util = new ExcelUtil<BaseDutySchedule>(BaseDutySchedule.class);
        return util.exportExcel(list, "值班人员信息数据");
    }

    /**
     * 获取值班人员信息详细信息
     */
//    @PreAuthorize("@ss.hasPermi('base:duty:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(hzDutyScheduleService.selectHzDutyScheduleById(id));
    }

    /**
     * 新增值班人员信息
     */
//    @PreAuthorize("@ss.hasPermi('base:duty:add')")
    @Log(title = "值班人员信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BaseDutySchedule baseDutySchedule)
    {
        return toAjax(hzDutyScheduleService.insertHzDutySchedule(baseDutySchedule));
    }

    /**
     * 修改值班人员信息
     */
//    @PreAuthorize("@ss.hasPermi('base:duty:edit')")
    @Log(title = "值班人员信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody BaseDutySchedule baseDutySchedule)
    {
        return toAjax(hzDutyScheduleService.updateHzDutySchedule(baseDutySchedule));
    }

    /**
     * 删除值班人员信息
     */
//    @PreAuthorize("@ss.hasPermi('base:duty:remove')")
    @Log(title = "值班人员信息", businessType = BusinessType.DELETE)
	@GetMapping("/delete/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(hzDutyScheduleService.deleteHzDutyScheduleByIds(ids));
    }
}
