package com.gzairports.common.business.arrival.mapper;

import com.gzairports.common.business.arrival.domain.vo.SerialNumberRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;

@Mapper
public interface SerialNumberMapper {
    SerialNumberRecord selectByDateAndPrefix(@Param("dateStr") String dateStr,@Param("prefix") String prefix);

    void insertNewRecord(@Param("dateStr") String dateStr,@Param("prefix") String prefix);

    int incrementSequence(@Param("dateStr") String dateStr,@Param("prefix") String prefix,@Param("updateTime") LocalDateTime updateTime);
}
