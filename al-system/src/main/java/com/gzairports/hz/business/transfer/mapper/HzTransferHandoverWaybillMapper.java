package com.gzairports.hz.business.transfer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.hz.business.transfer.domain.HzTransferHandoverWaybill;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 交接单与运单关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-27
 */
@Mapper
public interface HzTransferHandoverWaybillMapper extends BaseMapper<HzTransferHandoverWaybill>
{

    /**
     * 查询交接后的运单id
     * @return 运单id集合
     */
    List<Long> selectIds();

    /**
     * 更新中转状态
     * @param collectId 中转运单状态
     */
    void updateLoad(Long collectId);

    List<Long> selectImportDataByCode(Long waybillId);
}
