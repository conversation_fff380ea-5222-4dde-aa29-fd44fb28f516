package com.gzairports.common.basedata.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.basedata.domain.BaseCraftNo;
import com.gzairports.common.basedata.domain.BaseCraftNoMail;
import lombok.Data;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 基础数据机号管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-19
 */
@Mapper
public interface BaseCraftNoMapper extends BaseMapper<BaseCraftNo> {

    /**
     * 查询机号管理列表
     */
    List<BaseCraftNo> selectCraftNoList(BaseCraftNo baseCraftNo);

    /**
     * 查询副邮箱机号管理列表
     */
    List<BaseCraftNoMail> selectCraftNoMailList(BaseCraftNo baseCraftNo);

    /**
     * 根据机号 时间 类型 查询
     */
    BaseCraftNo selectCraftNoInfo(@Param("craftNo") String craftNo,
                                        @Param("date") Date date,
                                        @Param("type") Integer type);

}
