package com.gzairports.common.basedata.domain.query;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 代理人配置增加余额参数
 *
 * <AUTHOR>
 * @date 2024-06-17
 */
@Data
public class BaseAgentQuery {

    /** 代理人配置id */
    private Long agentId;

    /** 交易金额 */
    private BigDecimal tradeMoney;

    /** 凭证 */
    private String voucher;

    /** 凭证 */
    private String voucherPDF;

    /** 当前余额 */
    private BigDecimal balance;
}
