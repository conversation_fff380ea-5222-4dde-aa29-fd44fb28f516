package com.gzairports.web.controller.basedata;


import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.common.basedata.domain.BaseCurrency;
import com.gzairports.common.basedata.domain.query.CurrencyQuery;
import com.gzairports.common.basedata.service.ICurrencyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 币种管理Controller
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@RestController
@RequestMapping("/base/currency")
@Api(tags = "币种管理数据接口")
public class CurrencyController extends BaseController {

    @Autowired
    private ICurrencyService currencyService;

    /**
     * 查询币种管理列表
     */
//    @PreAuthorize("@ss.hasPermi('base:currency:currencyList')")
    @GetMapping("/currencyList")
    @ApiOperation(value = "查询币种管理列表")
    public TableDataInfo specialCodeList(CurrencyQuery query)
    {
        startPage();
        List<BaseCurrency> list = currencyService.selectCurrencyList(query);
        return getDataTable(list);
    }

    /**
     * 导出币种管理
     */
    @PreAuthorize("@ss.hasPermi('base:currency:exportCurrency')")
    @Log(title = "导出币种管理", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCurrency")
    @ApiOperation(value = "导出币种管理")
    public void exportCurrency(HttpServletResponse response, CurrencyQuery query)
    {
        List<BaseCurrency> list = currencyService.selectCurrencyList(query);
        ExcelUtil<BaseCurrency> util = new ExcelUtil<BaseCurrency>(BaseCurrency.class);
        util.exportExcel(response, list, "币种管理");
    }
}
