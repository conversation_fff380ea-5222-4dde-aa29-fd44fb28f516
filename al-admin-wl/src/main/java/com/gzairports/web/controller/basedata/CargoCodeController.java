package com.gzairports.web.controller.basedata;

import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.common.basedata.domain.BaseCargoCode;
import com.gzairports.common.basedata.domain.query.CargoCodeQuery;
import com.gzairports.common.basedata.service.ICargoCodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 货站货品代码Controller
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@RestController
@RequestMapping("/base/cargoCode")
@Api(tags = "货站货品代码数据接口")
public class CargoCodeController extends BaseController {

    @Autowired
    private ICargoCodeService cargoCodeService;

    /**
     * 查询货站货品代码列表
     */
//    @PreAuthorize("@ss.hasPermi('base:cargoCode:cargoCodeList')")
    @GetMapping("/cargoCodeList")
    @ApiOperation(value = "查询货站货品代码列表")
    public TableDataInfo cargoCodeList(CargoCodeQuery query)
    {
        startPage();
        List<BaseCargoCode> list = cargoCodeService.selectCargoCodeList(query);
        return getDataTable(list);
    }

    /**
     * 根据多个货物类别查询货品代码
     * */
    @PostMapping("/selectCargoCodeBatch")
    @ApiOperation(value = "根据多个货物类别查询货品代码")
    public AjaxResult selectCargoCodeBatch(@RequestBody(required = false) String[] codes)
    {
        return AjaxResult.success(cargoCodeService.selectCargoCodeBatch(codes));
    }

    /**
     * 根据多个货品代码查询货品信息
     * */
    @PostMapping("/selectCodeBatch")
    @ApiOperation(value = "根据多个货品代码查询货品信息")
    public AjaxResult selectCodeBatch(@RequestBody(required = false) String[] codes)
    {
        return AjaxResult.success(cargoCodeService.selectCodeBatch(codes));
    }

    /**
     * 导出货站货品代码
     */
    @PreAuthorize("@ss.hasPermi('base:cargoCode:exportCargoCode')")
    @Log(title = "导出货站货品代码", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCargoCode")
    @ApiOperation(value = "导出货站货品代码")
    public void exportCargoCode(HttpServletResponse response, CargoCodeQuery query)
    {
        List<BaseCargoCode> list = cargoCodeService.selectCargoCodeList(query);
        ExcelUtil<BaseCargoCode> util = new ExcelUtil<BaseCargoCode>(BaseCargoCode.class);
        util.exportExcel(response, list, "货站货品代码");
    }

    /**
     *
     * 根据代码查询货品信息
     */
    @PreAuthorize("@ss.hasPermi('base:customer:selectByCode')")
    @GetMapping("/selectByCode/{code}")
    @ApiOperation(value = "根据代码查询货品信息")
    public AjaxResult selectByCode(@PathVariable("code") String code)
    {
        return AjaxResult.success(cargoCodeService.selectByCode(code));
    }
}
