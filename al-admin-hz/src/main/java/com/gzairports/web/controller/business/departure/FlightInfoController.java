package com.gzairports.web.controller.business.departure;


import com.gzairports.common.business.departure.domain.FlightInfo;
import com.gzairports.hz.business.departure.domain.query.FlightInfoQuery;
import com.gzairports.hz.business.departure.domain.vo.FlightInfoVO;
import com.gzairports.hz.business.departure.service.IFlightInfoService;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.PermitAll;
import java.util.Date;
import java.util.List;

/**
 * 航班信息Controller
 * 
 * <AUTHOR>
 * @date 2024-06-24
 */
@RestController
@RequestMapping("/business/info")
@Api(tags = "入库排班")
public class FlightInfoController extends BaseController
{
    @Autowired
    private IFlightInfoService businessFlightInfoService;

    /**
     * 查询入库排班列表
     */
//    @PreAuthorize("@ss.hasPermi('business:info:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询入库排班列表")
    public TableDataInfo list(FlightInfoQuery query)
    {
        startPage();
        List<FlightInfoVO> list = businessFlightInfoService.selectBusinessFlightInfoList(query);
        return getDataTable(list);
    }

    /**
     * 获取入库排班详细信息
     */
    @PreAuthorize("@ss.hasPermi('business:info:query')")
    @GetMapping(value = "/{flightId}")
    @ApiOperation(value = "获取入库排班详细信息")
    public AjaxResult getInfo(@PathVariable("flightId") Long flightId)
    {
        return AjaxResult.success(businessFlightInfoService.selectBusinessFlightInfoById(flightId));
    }

    /**
     * 修改入库排班信息
     */
    @PreAuthorize("@ss.hasPermi('business:info:edit')")
    @ApiOperation(value = "修改入库排班信息")
    @Log(title = "修改入库排班", businessType = BusinessType.UPDATE)
    @PostMapping
    public AjaxResult edit(@RequestBody FlightInfo businessFlightInfo)
    {
        return toAjax(businessFlightInfoService.updateBusinessFlightInfo(businessFlightInfo));
    }

    /**
     * 生成排班
     */
    @PreAuthorize("@ss.hasPermi('business:info:generate')")
    @GetMapping(value = "/generate/{date}")
    @Log(title = "生成排班", businessType = BusinessType.INSERT)
    @ApiOperation(value = "生成排班")
    public AjaxResult generate (@PathVariable String date)
    {
        return AjaxResult.success(businessFlightInfoService.generate(date));
    }

    /**
     * 排班显示
     */
    @PermitAll
    @GetMapping(value = "/show")
    @ApiOperation(value = "排班显示")
    public TableDataInfo show (Integer pageNum, Integer pageSize)
    {
        startPage();
        List<FlightInfoVO> list = businessFlightInfoService.show();
        return getDataTable(list);
    }

    /**
     * 入库排班短信发送接口
     */
    @PreAuthorize("@ss.hasPermi('business:info:sendSms')")
    @GetMapping("/sendSms/{flightId}")
    @Log(title = "入库排班短信发送", businessType = BusinessType.INSERT)
    @ApiOperation(value = "入库排班短信发送接口")
    public AjaxResult sendSms(@PathVariable Long flightId)
    {
        return AjaxResult.success(businessFlightInfoService.sendSms(flightId));
    }

    /**
     * 入库排班未通知短信发送接口
     */
    @PreAuthorize("@ss.hasPermi('business:info:noSendSms')")
    @PostMapping("/noSendSms")
    @Log(title = "入库排班未通知短信发送", businessType = BusinessType.INSERT)
    @ApiOperation(value = "入库排班未通知短信发送接口")
    public AjaxResult noSendSms(@RequestBody FlightInfoQuery query)
    {
        return AjaxResult.success(businessFlightInfoService.noSendSms(query));
    }

    /**
     * 修改宽体机维护列表
     */
    @PreAuthorize("@ss.hasPermi('business:info:updateWideBody')")
    @Log(title = "自动通知设置", businessType = BusinessType.UPDATE)
    @PostMapping("/updateWideBody")
    @ApiOperation(value = "修改宽体机维护列表")
    public AjaxResult updateWideBody(@RequestBody String[] strings)
    {
        return toAjax(businessFlightInfoService.updateWideBody(strings));
    }

    /**
     * 获取宽体机维护列表
     */
    @PreAuthorize("@ss.hasPermi('business:info:getWideBody')")
    @GetMapping(value = "/getWideBody")
    @ApiOperation(value = "获取宽体机维护列表")
    public AjaxResult getWideBody()
    {
        return AjaxResult.success(businessFlightInfoService.getWideBody());
    }
}
