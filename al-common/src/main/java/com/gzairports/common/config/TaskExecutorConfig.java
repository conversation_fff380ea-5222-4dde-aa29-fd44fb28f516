package com.gzairports.common.config;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.*;

/**
 * Created by david on 2025/4/2
 */
@Configuration
@Slf4j
public class TaskExecutorConfig {

    @Bean(name = "reportTaskExecutor")
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        int corePoolSize = Math.max(10, Runtime.getRuntime().availableProcessors() * 2);
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(80);
        executor.setQueueCapacity(500);
        executor.setThreadNamePrefix("ReportTask-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    @Bean(name = "ffmSchedulerExecutor", destroyMethod = "shutdown")
    public ScheduledExecutorService ffmSchedulerExecutor() {
        int corePoolSize = calculateOptimalPoolSize();
        ScheduledThreadPoolExecutor executor = new ScheduledThreadPoolExecutor(
                corePoolSize,
                createThreadFactory(),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
        // 配置核心线程超时
        executor.setKeepAliveTime(60, TimeUnit.SECONDS);
        executor.allowCoreThreadTimeOut(true);
        return executor;
    }

    private int calculateOptimalPoolSize() {
        int availableProcessors = Runtime.getRuntime().availableProcessors();
        // 根据业务类型调整：
        // IO密集型：核心数 * 2
        // CPU密集型：核心数 + 1
        return Math.max(4, availableProcessors * 2);
    }
    private ThreadFactory createThreadFactory() {
        return new ThreadFactoryBuilder()
                .setNamePrefix("ffm-scheduler-%d")
                // 必须为非守护线程
                .setDaemon(false)
                .setUncaughtExceptionHandler((thread, ex) ->
                        log.error("线程 {} 意外终止: {}", thread.getName(), ex.getMessage()))
                .build();
    }
}
