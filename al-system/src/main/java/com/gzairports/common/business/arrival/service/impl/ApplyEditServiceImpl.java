package com.gzairports.common.business.arrival.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.business.arrival.domain.ApplyEdit;
import com.gzairports.common.business.arrival.mapper.ApplyEditMapper;
import com.gzairports.common.business.arrival.service.IApplyEditService;
import org.springframework.stereotype.Service;

/**
 * 办理提货申请修改Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@Service
public class ApplyEditServiceImpl extends ServiceImpl<ApplyEditMapper, ApplyEdit> implements IApplyEditService {
}
