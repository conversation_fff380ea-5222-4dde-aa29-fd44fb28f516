package com.gzairports.common.basedata.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.basedata.domain.BaseCarrier;
import com.gzairports.common.basedata.domain.query.CarrierQuery;
import com.gzairports.common.basedata.mapper.CarrierMapper;
import com.gzairports.common.basedata.service.ICarrierService;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.exception.ServiceException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.bean.BeanValidators;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.Validator;
import java.util.List;

/**
 * 承运人管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Service
public class CarrierServiceImpl implements ICarrierService {

    @Autowired
    private CarrierMapper carrierMapper;

    private static final Logger log = LoggerFactory.getLogger(WeightServiceImpl.class);

    @Autowired
    private Validator validator;

    /**
     * 查询承运人管理列表
     *
     * @param query 查询参数
     * @return 承运人管理列表
     */
    @Override
    public List<BaseCarrier> selectCarrierList(CarrierQuery query) {
        return carrierMapper.selectCarrierList(query);
    }

    /**
     * 新增承运人
     *
     * @param carrier 承运人
     * @return 结果
     */
    @Override
    public int addCarrier(BaseCarrier carrier) {
        BaseCarrier baseCarrier = carrierMapper.selectByCode(carrier.getCode());
        if (baseCarrier != null){
            throw new CustomException("承运人代码重复");
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        carrier.setCreateBy(loginUser.getUsername());
        carrier.setCreateTime(DateUtils.getNowDate());
        return carrierMapper.insertCarrier(carrier);
    }

    /**
     * 修改承运人
     *
     * @param carrier 承运人
     * @return 结果
     */
    @Override
    public int editCarrier(BaseCarrier carrier) {
        List<BaseCarrier> baseCarriers = carrierMapper.selectList(new QueryWrapper<>(carrier)
                .eq("code", carrier.getCode())
                .eq("is_del", 0));
        if (baseCarriers.size() > 1){
            throw new CustomException("承运人代码重复");
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        carrier.setUpdateBy(loginUser.getUsername());
        carrier.setUpdateTime(DateUtils.getNowDate());
        return carrierMapper.updateCarrier(carrier);
    }

    /**
     * 删除承运人
     *
     * @param ids 承运人id集合
     * @return 结果
     */
    @Override
    public int delCarrier(Long[] ids) {
        return carrierMapper.delCarrier(ids);
    }

    /**
     * 导入承运人
     * @param carriers 承运人列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importCarrier(List<BaseCarrier> carriers, boolean updateSupport) {
        if (StringUtils.isNull(carriers) || carriers.size() == 0)
        {
            throw new ServiceException("导入承运人数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (BaseCarrier carrier : carriers) {
            try {
                // 验证是否存在相同的货站货品代码
                BaseCarrier baseCarrier = carrierMapper.selectByCode(carrier.getCode());
                if (StringUtils.isNull(baseCarrier)) {
                    BeanValidators.validateWithException(validator, carrier);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    carrier.setCreateBy(loginUser.getUsername());
                    carrier.setCreateTime(DateUtils.getNowDate());
                    carrierMapper.insertCarrier(carrier);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、货站货品代码 " + carrier.getCode() + " 导入成功");
                }
                else if (updateSupport) {
                    BeanValidators.validateWithException(validator, carrier);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    carrier.setId(baseCarrier.getId());
                    carrier.setUpdateBy(loginUser.getUsername());
                    carrier.setUpdateTime(DateUtils.getNowDate());
                    carrierMapper.updateCarrier(carrier);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、货站货品代码 " + carrier.getCode() + " 更新成功");
                }
                else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、货站货品代码 " + carrier.getCode() + " 已存在");
                }
            }catch (Exception e){
                failureNum++;
                String msg = "<br/>" + failureNum + "、货站货品代码 " + carrier.getCode() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 根据航司查询logo
     * @param carrier1 航司
     * @return logo地址
     */
    @Override
    public String selectLogoByCode(String carrier1) {
        return carrierMapper.selectLogoByCode(carrier1);
    }
}
