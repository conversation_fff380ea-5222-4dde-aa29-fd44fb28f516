package com.gzairports.hz.business.reporter.domain.vo;

import com.gzairports.hz.business.reporter.domain.HzReportSetDisplay;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-06
 */
@Data
public class ReportConfig {

    /** 主表名*/
    private String mainTable;

    /** 关联表（最多2个）*/
    private List<JoinTable> joinTables;

    /** 显示字段*/
    private List<String> displayFields;

    /** 显示字段中文 */
    private List<String> displayFieldsCn;

    /** 是否只展示小计字段 */
    private List<String> isSubtotalList;

    /** 小计分组字段 */
    private List<String> subtotalFields;

    /** 小计字段 */
    private List<String> subtotalFieldList;

    /** 需要合并的字段 */
    private List<String> mergeFields;

    /** 需要计算的值 */
    private List<HzReportSetDisplay> formulasFields;

    /** 过滤条件 */
    private List<FieldKeyValue> filters;

    /** 排序字段 */
    private List<SortField> sortFields;
}
