package com.gzairports.common.business.arrival.domain.vo;

import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 已提货办单运单数据
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Data
public class ArrCostDetailListVo {

    /** 主键id */
    private Long id;

    /** 流水号 */
    @Excel(name = "流水号", width = 26)
    private String serialNo;

    /** 运单号 */
    @Excel(name = "运单号", width = 26)
    private String waybillCode;

    /** 运单类型 */
    private String waybillType;

    /** 航班号 */
//    @Excel(name = "航班号")
    private String flightNo;

    /** 航班日期 */
//    @Excel(name = "航班日期")
    private LocalDateTime flightDate;

    /** 航班号/日期 */
    @Excel(name = "航班号/日期", width = 26)
    private String flightNoAndDate;

    /** 收货人 */
    @Excel(name = "收货人")
    private String consignee;

    /** 件数 */
    @Excel(name = "件数", cellType = Excel.ColumnType.NUMERIC)
    private Integer quantity;

    /** 重量 */
    @Excel(name = "重量", cellType = Excel.ColumnType.NUMERIC)
    private Double weight;

    /** 提货件数 */
    @Excel(name = "提货件数", cellType = Excel.ColumnType.NUMERIC)
    private Integer waybillQuantity;

    /** 提货重量 */
    @Excel(name = "提货重量", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal waybillWeight;

    /** 费用 */
    @Excel(name = "费用", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal waybillCost;

    /** 办单经办人 */
    @Excel(name = "办单经办人")
    private String handleBy;

    /** 办单时间 */
    @Excel(name = "办单时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date pickUpTime;

    /** 支付方式(结算状态) */
    private Integer payStatus;

    /** 支付方式(结算状态) */
    @Excel(name = "结算状态")
    private String payStatusLabel;

    /** 提货时间 */
    @Excel(name = "提货时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date outTime;



    /** 运单数 */
//    @Excel(name = "运单数")
    private Integer totalCount;

    /** 提货总件数 */
    private Integer totalQuantity;

    /** 提货总重量 */
    private BigDecimal totalWeight;

    /** 总费用 */
//    @Excel(name = "总费用")
    private BigDecimal totalCost;

    /** 提货人 */
//    @Excel(name = "提货人")
    private String customerName;

    /** 提货人证件号 */
//    @Excel(name = "提货人证件号")
    private String customerIdNo;

    /** 是否已支付 */
    private Integer isPay;

}
