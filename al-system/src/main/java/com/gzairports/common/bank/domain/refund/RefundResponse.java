package com.gzairports.common.bank.domain.refund;

import lombok.Data;

import java.io.Serializable;

/**
 * @author: lan
 * @Desc: 退款响应字段
 * @create: 2025-03-18 14:24
 **/

@Data
public class RefundResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 回显商户名 */
    private String merchantName;

    /** 商户订单号 50 Y
     * 原交易商户订单号
     */
    private String merchantSeq;

    /** 交易金额 */
    private Long amount;

    /** 订单详情 */
    private String orderInfo;

    /** 凭证号 */
    private String voucherNo;

    /** 银行流水号 */
    private String bankTradeNo;

    /** 交易结果
     * S 退款成功
     * E 退款失败
     * R交易结果未知
     * */
    private String tradeStatus;

    /** 保留域 */
    private String remark;
}
