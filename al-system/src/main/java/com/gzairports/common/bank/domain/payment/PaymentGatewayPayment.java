package com.gzairports.common.bank.domain.payment;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: lan
 * @Desc: 网关支付/下单
 * @create: 2025-03-18 11:09
 **/

@Data
public class PaymentGatewayPayment implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 一级商户号 16 Y
     * 民生银行为签约商户提供的一级商户号，与签约证书一一对应
     * */
    private String platformId;

    /** 一级签约编码 20 Y
     * 一级商户号对应的签约编码
     * */
    private String merchantNo;

    /** 商户名 255 Y
     * 商户名称，用于在民生页面回显
     * */
    private String merchantName;

    /** 可选支付类型 8 Y
     * B2C B2B
     */
    private String selectTradeType;

    /** 直接跳转支付 1 Y
     * 0-API模式直跳下单
     * 1-使用民生提供的聚合收银台页面，页面上将列出商户所签约的所有支付类型，客户可自行选择支付类型下单支付
     * 当 isShowPayPage 为 0 时，selectTradeType 送且只能送一个；
     * 为 1 时，selectTradeType 不送，将在聚合收银台页面展示出商户签约的所有支付类型；
     */
    private Integer isShowPayPage;

    /** 交易金额 30 Y
     * 交易金额，以分为单位
     * 如果支付类型为积分消费，该字段上送积分
     */
    private Long amount;

    /** 订单信息 300 N
     * 商户订单内容，商品信息(300字符，超长截断)
     */
    private String orderInfo;

    /** 商户订单号 50 Y
     * 商户的订单号，该订单号有长度限制，必须为50位，用于商户后续查询、退款等交易，须定义成商户号+自定义流水。
     */
    private String merchantSeq;

    /** 订单日期 8 Y
     * yyyyMMdd
     */
//    @DateTimeFormat(pattern = "yyyyMMdd")
//    @JsonFormat(pattern = "yyyyMMdd")
    private String transDate;

    /** 订单时间 17 Y
     * yyyyMMddHHmmssSSS
     */
//    @DateTimeFormat(pattern = "yyyyMMdd")
//    @JsonFormat(pattern = "yyyyMMdd")
    private String transTime;

    /** 订单最晚失效时间 14 N
     * YyyyyMMddHHmmss，非必输字段，如果上送订单失效时间以上送为主，如果不上送默认30分钟失效(失效时间最长不得超过2小时)
     */
    @DateTimeFormat(pattern = "yyyyMMdd")
    @JsonFormat(pattern = "yyyyMMdd")
    private Date expireTime;

    /** 通知地址 512 Y
     * 商户实现的接收异步通知的url地址
     * 地址前两位用于标识TLS版本，10-TLSv1.0、11-TLSv1.1、12-TLSv1.2，例如：11https://wxpay.cmbc.com.cn/...
     * 如果商户不想接收异步通知，该字段可以送固定字符串NO_NOTIFY
     */
    private String notifyUrl;

    /** 商户首页地址 1024 Y
     * 用于跳回商户首页
     */
    private String redirectUrl;

    /** 实名支付标记 1 N
     * 该字段用于微信、支付宝或手机控件支付时，标记是否需要实名支付
     * realNameAuthn=Y时，需要实名支付，此时需要把付款人名称、付款人证件类型、付款人证件号填入inForm对应字段中。
     * realNameAuthn=N或realNameAuthn为空时，不需要实名支付
     */
    private String realNameAuthn;

    /** 扩展字段 8000 Y
     * 该字段用于存放子订单信息，具体参考说明，见下文
     * 该inForm字段以xml格式构造报文，并将该xml以BASE64编码（UTF-8）存放于inForm字段中
     */
    private String inForm;

    /** 扩展字段 8000 C
     * 网联(1285)、银联(1286)通道必输，数字货币H5支付、数字货币二维码支付必输，具体说明见下文
     * 该inForm字段以xml格式构造报文，并将该xml以BASE64编码（UTF-8）存放于inForm字段中
     */
    private String extendInfo;

    /** 银联二维码服务商ID N
     * 银联二维码服务商ID
     * */
    private String pnrInsIdCd;

    /** 银联云微小程序支付appId 32 C
     * 银联云微小程序支付API下，该部分必输，填小程序的appId
     * */
    private String merWxMpAppId;

    /** 交易发起场景 2 C
     * 01:APP
     * 02:H5
     * 03:小程序
     * 04:公众号
     * */
    private String invokeScene;

    /** 子账溥账号 16 N
     * 16位纯数字
     * */
    private String accNo;
}
