package com.gzairports.wl.charge.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 邮件运价返回数据
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@Data
public class MailPriceVo {

    /** 主键id */
    private Long id;

    /** 运价名称 */
    @Excel(name = "运价名称")
    private String rateName;

    /** 运价类型 */
    private Integer rateType;

    @Excel(name = "运价类型")
    private String type;

    /** 适用单证类型 */
    private String applicableDocuments;


    @Excel(name = "适用单证类型")
    private String applicable;

    /** 航司 */
    @Excel(name = "航司")
    private String carrierCode;

    /** 是否自动从低 */
    private Integer automaticFromLow;

    @Excel(name = "是否自动从低")
    private String automatic;

    /** 取舍方式 */
    private Integer roundingMethod;

    @Excel(name = "取舍方式")
    private String rounding;

    /** 生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生效日期",dateFormat = "yyyy-MM-dd")
    private Date effectiveDate;

    /** 截止日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "截止日期",dateFormat = "yyyy-MM-dd")
    private Date expirationDate;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;
}
