package com.gzairports.common.business.departure.domain.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 运单日志对象 t_log_awblog
 *
 * <AUTHOR>
 * @date 2023-09-25
 */
@Data
@TableName(schema = "KWECFPS", value = "T_LOG_AWBLOG")
public class LogAwblog
{
    private static final long serialVersionUID = 1L;

    /** 记录ID */
    private Long recid;

    /** 日志状态/操作代码 */
    private String statusid;

    /** 日志状态/操作代码子代码 */
    private String subid;

    /** 运单类型 */
    private String stocktypeid;

    /** 运单前缀 */
    private String stockpre;

    /** 运单号 */
    private String stockno;

    /** 件数 */
    private Long pcs;

    /** ULD全号码 */
    private String uldno;

    /** 承运航班号全信息,如CZ3101/0825 */
    private String flightno;

    /** 运单航程 */
    private String awbvoyage;

    /** 客户简称（收\发\提\取货人，提单人） */
    private String customerid;

    /** 业务操作航站 */
    private String airport;

    /** 业务操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date opetime;

    /** 业务操作营业点 */
    private String opedepartid;

    /** 业务操作人 */
    private String operator;

    /** 业务操作号码（报关号、订舱号、交接单号) */
    private String openo;

    /** XML扩展 */
    private String particularinfo;

    /** 重量 */
    private BigDecimal weight;

    /** 运单操作变化前状态的XML扩展 */
    private String oldparticularinfo;





















}
