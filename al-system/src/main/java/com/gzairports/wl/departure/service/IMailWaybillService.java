package com.gzairports.wl.departure.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.business.departure.domain.MailWaybill;
import com.gzairports.wl.departure.domain.query.FareQuery;
import com.gzairports.wl.departure.domain.query.HawbQuery;
import com.gzairports.wl.departure.domain.vo.EditWaybillCode;
import com.gzairports.wl.departure.domain.vo.FareVo;
import com.gzairports.wl.departure.domain.vo.MailWaybillVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 邮件单Service接口
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
public interface IMailWaybillService extends IService<MailWaybill> {

    /**
     * 根据运单号以及单证控制校验运单号
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    MailWaybill check(String waybillCode);

    /**
     * 邮件单制单
     *
     * @param waybill 邮件单制单数据
     * @return 返回结果
     */
    String add(MailWaybill waybill);

    /**
     * 编辑运单
     *
     * @param waybill 运单详情信息
     * @return 返回结果
     */
    int edit(MailWaybill waybill);

    /**
     * 运单作废
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    int invalid(String waybillCode);

    /**
     * 运单取消作废
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    int cancelVoid(String waybillCode);

    /**
     * 邮件单查询
     * @param query 查询条件
     * @return 结果
     */
    List<MailWaybillVo> queryList(HawbQuery query);

    /**
     * 根据运单code查询运单详情
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    MailWaybill getInfo(String waybillCode);

    /**
     * 根据默认收费方式计算运单运价
     *
     * @param query 新增运单信息
     * @return 返回结果
     */
    FareVo fare(FareQuery query);

    /**
     * 打印邮件单接口
     * @param id 邮件单id
     * @param response 返回流
     */
    void printMailData(Long id, HttpServletResponse response) throws Exception;

    int editWaybillCode(EditWaybillCode editWaybillCode);

    List<MailWaybillVo> exportList(HawbQuery query);

    int cancelPay(Long waybillId);
}
