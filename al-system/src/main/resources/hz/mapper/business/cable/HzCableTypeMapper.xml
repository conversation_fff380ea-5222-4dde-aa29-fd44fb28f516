<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.cable.mapper.HzCableTypeMapper">
    
    <resultMap type="HzCableType" id="HzCableTypeResult">
        <result property="id"    column="id"    />
        <result property="cableName"    column="cable_name"    />
        <result property="cableCode"    column="cable_code"    />
        <result property="prefix"    column="prefix"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="isDel"    column="is_del"    />
        <result property="template"    column="template"    />
    </resultMap>

    <sql id="selectHzCableTypeVo">
        select id, cable_name, cable_code, prefix, remark, template, create_time, create_by, update_time, update_by, is_del from hz_cable_type
    </sql>

    <select id="selectHzCableTypeList" parameterType="HzCableType" resultMap="HzCableTypeResult">
        <include refid="selectHzCableTypeVo"/>
        <where>
            and is_del = 0
            <if test="cableName != null  and cableName != ''"> and cable_name like concat('%', #{cableName}, '%')</if>
            <if test="cableCode != null  and cableCode != ''"> and cable_code = #{cableCode}</if>
            <if test="prefix != null  and prefix != ''"> and prefix like concat('%', #{prefix}, '%')</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectHzCableTypeById" parameterType="Long" resultMap="HzCableTypeResult">
        <include refid="selectHzCableTypeVo"/>
        where id = #{id}
    </select>

    <insert id="insertHzCableType" parameterType="HzCableType">
        insert into hz_cable_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="cableName != null">cable_name,</if>
            <if test="cableCode != null">cable_code,</if>
            <if test="prefix != null">prefix,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDel != null">is_del,</if>
            <if test="template != null">template,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="cableName != null">#{cableName},</if>
            <if test="cableCode != null">#{cableCode},</if>
            <if test="prefix != null">#{prefix},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="template != null">#{template},</if>
         </trim>
    </insert>

    <update id="updateHzCableType" parameterType="HzCableType">
        update hz_cable_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="cableName != null">cable_name = #{cableName},</if>
            <if test="cableCode != null">cable_code = #{cableCode},</if>
            <if test="prefix != null">prefix = #{prefix},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="template != null">template = #{template},</if>
        </trim>
        where id = #{id}
    </update>

    <select id="selectTemplateByType" resultType="com.gzairports.hz.business.cable.domain.HzCableType">
        <include refid="selectHzCableTypeVo"/>
        where cable_code = #{type}
    </select>

</mapper>