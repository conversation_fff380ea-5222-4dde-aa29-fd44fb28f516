package com.gzairports.common.basedata.mapper;


import com.gzairports.common.basedata.domain.BaseRegionCode;
import com.gzairports.common.basedata.domain.query.RegionCodeQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 国家或地区代码Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Mapper
public interface RegionCodeMapper {

    /**
     * 查询国家或地区代码列表
     *
     * @param query 查询参数
     * @return 国家或地区代码列表
     */
    List<BaseRegionCode> selectRegionCodeList(RegionCodeQuery query);

    /**
     * 根据国家或地区代码查询国家或地区
     *
     * @param code 国家或地区代码
     * @return 结果
     */
    BaseRegionCode selectRegionByCode(String code);

    /**
     * 新增国家或地区代码
     *
     * @param baseRegionCode 国家或地区代码
     * @return 结果
     */
    int insertRegionCode(BaseRegionCode baseRegionCode);

    /**
     * 修改国家或地区代码
     *
     * @param baseRegionCode 国家或地区代码
     * @return 结果
     */
    int updateRegionCode(BaseRegionCode baseRegionCode);

    /**
     * 删除国家或地区代码
     *
     * @param ids 国家或地区代码id集合
     * @return 结果
     */
    int delRegionCode(Long[] ids);
}
