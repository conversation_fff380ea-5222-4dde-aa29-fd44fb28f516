package com.gzairports.web.controller.cargofee;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.hz.business.cargofee.domain.WaybillFeeHz;
import com.gzairports.hz.business.cargofee.domain.query.WaybillFeeHzQuery;
import com.gzairports.hz.business.cargofee.domain.vo.WaybillFeeHzVo;
import com.gzairports.hz.business.cargofee.service.IWaybillFeeHzService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * 运单费用明细Controller
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@RestController
@RequestMapping("/cargofee/waybillFee")
public class WaybillFeeController extends BaseController {

    @Autowired
    private IWaybillFeeHzService waybillFeeService;

    /**
     * 查询运单费用明细列表
     */
//    @PreAuthorize("@ss.hasPermi('cargofee:waybillFee:list')")
    @PostMapping("/list")
    @ApiOperation(value = "查询运单费用明细列表")
    public AjaxResult list(@RequestBody WaybillFeeHzQuery query){
        WaybillFeeHzVo vo = waybillFeeService.selectList(query);
        return AjaxResult.success(vo);
    }

    /**
     * 导出运单费用明细列表
     */
    @PreAuthorize("@ss.hasPermi('cargofee:waybillFee:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出运单费用明细列表")
    public void export(HttpServletResponse response, WaybillFeeHzQuery query)
    {
        WaybillFeeHzVo vo = waybillFeeService.selectList(query);
        ExcelUtil<WaybillFeeHz> util = new ExcelUtil<WaybillFeeHz>(WaybillFeeHz.class);
        util.exportExcel(response, vo.getList(), "运单费用明细");
    }
}
