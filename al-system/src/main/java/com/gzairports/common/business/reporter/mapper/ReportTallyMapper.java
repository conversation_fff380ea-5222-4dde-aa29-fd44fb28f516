package com.gzairports.common.business.reporter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.reporter.domain.ReportTally;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ReportTallyMapper extends BaseMapper<ReportTally> {
    void deleteBatch(@Param("arrReportIds") List<Long> arrReportIds);

    int insertBatchSomeColumn(List<ReportTally> tallyList);
}
