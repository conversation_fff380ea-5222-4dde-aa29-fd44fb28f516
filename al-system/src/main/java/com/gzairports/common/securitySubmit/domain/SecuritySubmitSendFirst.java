package com.gzairports.common.securitySubmit.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.business.departure.domain.vo.CargoInfoVo;
import com.gzairports.common.business.departure.domain.vo.IdentityInfoVo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: lan
 * @Desc: 第一次推送：代理人安检信息申报时推送代理人申报的运单和申报相关数据。
 * @create: 2024-11-14 18:21
 **/
@Data
public class SecuritySubmitSendFirst {
    /**
     * 货站，0:国内,1:国际
     * */
    private String freightStation;
    /**
     * 航班号
     * */
    private String flightNo;
    /**
     * 日期
     * */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date execDate;
    /**
     * 航空货运单号
     * */
    private String waybillCode;
    /**
     * 航空货物性质，1:普通货物，2:特种货物，3:危险品，4:航空快件
     * */
    private String nature;
    /**
     * 航空货运销售代理人名称
     * */
    private String agentName;
    /**
     * 航空货运托运人名称
     * */
    private String shipper;
    /**
     * 电子安检申报单地址
     * */
    private List<String> securityPdfUrls;
    /**
     * 创建人：货运平台
     * */
    private String createUser;
    /**
     * 相关随附文件，包括安全数据说明书、运输条件鉴定书、航空公司同意运输证明、品名清单、代理人经办人员头像等
     * */
    private List<String> transportFilePdfs;

    /**
     * 代理人经办人员身份证信息
     * */
    private IdentityInfoVo identityInfoVo;
    /**
     * 货物列表
     * */
    private List<CargoInfoVo> cargoInfoVos;

    private String messageType;

    /**
     * 品名清单
     * */
    private List<String> cargoNameList;
}
