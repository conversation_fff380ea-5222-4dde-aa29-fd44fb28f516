package com.gzairports.common.basedata.service.impl;


import com.gzairports.common.basedata.domain.BaseCargoIncompatible;
import com.gzairports.common.basedata.domain.query.CargoIncompatibleQuery;
import com.gzairports.common.basedata.mapper.CargoIncompatibleMapper;
import com.gzairports.common.basedata.service.ICargoIncompatibleService;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.exception.ServiceException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.bean.BeanValidators;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.Validator;
import java.util.List;

/**
 * 货物不兼容性Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Service
public class CargoIncompatibleServiceImpl implements ICargoIncompatibleService {

    private static final Logger log = LoggerFactory.getLogger(WeightServiceImpl.class);

    @Autowired
    private Validator validator;

    @Autowired
    private CargoIncompatibleMapper incompatibleMapper;

    /**
     * 查询货物不兼容性列表
     *
     * @param query 查询参数
     * @return 货物不兼容性列表
     */
    @Override
    public List<BaseCargoIncompatible> selectCargoIncompatibleList(CargoIncompatibleQuery query) {
        return incompatibleMapper.selectCargoIncompatibleList(query);
    }

    /**
     * 导入货物不兼容性
     *
     * @param incompatibles 货物不兼容性列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importCargoIncompatible(List<BaseCargoIncompatible> incompatibles, boolean updateSupport) {
        if (StringUtils.isNull(incompatibles) || incompatibles.size() == 0)
        {
            throw new ServiceException("导入货物不兼容性数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (BaseCargoIncompatible incompatible : incompatibles) {
            try {
                // 验证是否存在相同的货物不兼容性
                BaseCargoIncompatible cargoIncompatible = incompatibleMapper.selectIncompatibleByCode(incompatible.getAirCompany(),
                        incompatible.getSpecialCargoOne(),incompatible.getSpecialCargoTwo());
                if (StringUtils.isNull(cargoIncompatible)) {
                    BeanValidators.validateWithException(validator, incompatible);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    incompatible.setCreateBy(loginUser.getUsername());
                    incompatible.setCreateTime(DateUtils.getNowDate());
                    incompatibleMapper.insertCargoIncompatible(incompatible);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、特货代码 " + incompatible.getSpecialCargoOne() + " 导入成功");
                }
                else if (updateSupport) {
                    BeanValidators.validateWithException(validator, incompatible);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    incompatible.setId(cargoIncompatible.getId());
                    incompatible.setUpdateBy(loginUser.getUsername());
                    incompatible.setUpdateTime(DateUtils.getNowDate());
                    incompatibleMapper.updateCargoIncompatible(incompatible);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、特货代码 " + incompatible.getSpecialCargoOne() + " 更新成功");
                }
                else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、特货代码 " + incompatible.getSpecialCargoOne() + " 已存在相同不兼容数据");
                }
            }catch (Exception e){
                failureNum++;
                String msg = "<br/>" + failureNum + "、特货代码 " + incompatible.getSpecialCargoOne() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 新增货物不兼容性
     *
     * @param incompatible 货物不兼容性
     * @return 结果
     */
    @Override
    public int addCargoIncompatible(BaseCargoIncompatible incompatible) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        incompatible.setCreateBy(loginUser.getUsername());
        incompatible.setCreateTime(DateUtils.getNowDate());
        return incompatibleMapper.insertCargoIncompatible(incompatible);
    }

    /**
     * 修改货物不兼容性
     *
     * @param incompatible 货物不兼容性
     * @return 结果
     */
    @Override
    public int editCargoIncompatible(BaseCargoIncompatible incompatible) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        incompatible.setUpdateBy(loginUser.getUsername());
        incompatible.setUpdateTime(DateUtils.getNowDate());
        return incompatibleMapper.updateCargoIncompatible(incompatible);
    }

    /**
     * 删除货物不兼容性
     *
     * @param ids 货物不兼容性id集合
     * @return 结果
     */
    @Override
    public int delCargoIncompatible(Long[] ids) {
        return incompatibleMapper.delCargoIncompatible(ids);
    }

}
