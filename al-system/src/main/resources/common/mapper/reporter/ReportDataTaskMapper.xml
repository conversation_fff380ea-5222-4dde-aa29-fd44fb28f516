<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.reporter.mapper.ReportDataTaskMapper">
    <insert id="updateSyncMetadata">
        INSERT INTO sync_metadata (table_name, last_sync_time) VALUES (#{tableName}, #{newSyncTime})
        ON DUPLICATE KEY UPDATE last_sync_time = #{newSyncTime}
    </insert>
    <insert id="updateOrSave">
        INSERT INTO sync_metadata (table_name, last_sync_time) VALUES (#{tableName}, #{newSyncTime})
        ON DUPLICATE KEY UPDATE last_sync_time = #{newSyncTime}
    </insert>
    <insert id="batchUpsert">
        INSERT INTO all_report_data_waybill
            (
            id, waybill_code, waybill_prefix, type, waybill_type, status, shipping_station, agent_company, transfer_bill, origin_bill, can_restock_weight, cross_air,
            source_port, carrier1, des1, carrier2, des2, carrier3, des_port, flight_no1, flight_date1, flight_no2, flight_date2, shipper, city,
            storage_transport_notes, settlement_notes, customs_supervision, official_form, bulk_warehouse, cargo_code, cargo_name, quantity, weight,
            charge_weight, pack, size, volume, special_cargo_code1, special_cargo_code2, special_cargo_code3, other_special_cargo_code, danger_code,
            danger_type, cost_sum, rate_type, write_time, write_location, writer, remark, pay_status, pay_time, dept_id, cargo_quantity, cargo_weight,
            mail_quantity, mail_weight, category_name, main_or_email, pay_money, dep_quantity, dep_weight, collect_time, mail_dep_quantity, mail_dep_weight,
            w_cost_sum,r_cost_sum
            )
         VALUES
        <foreach collection="list" item="item" separator=",">
             (
              #{item.id}, #{item.waybillCode}, #{item.waybillPrefix}, #{item.type}, #{item.waybillType}, #{item.status}, #{item.shippingStation}, #{item.agentCompany}, #{item.transferBill},
              #{item.originBill}, #{item.canRestockWeight}, #{item.crossAir}, #{item.sourcePort}, #{item.carrier1}, #{item.des1}, #{item.carrier2}, #{item.des2},
              #{item.carrier3}, #{item.desPort}, #{item.flightNo1}, #{item.flightDate1}, #{item.flightNo2}, #{item.flightDate2}, #{item.shipper}, #{item.city},
              #{item.storageTransportNotes}, #{item.settlementNotes}, #{item.customsSupervision}, #{item.officialForm}, #{item.bulkWarehouse}, #{item.cargoCode},
              #{item.cargoName}, #{item.quantity}, #{item.weight}, #{item.chargeWeight}, #{item.pack}, #{item.size}, #{item.volume}, #{item.specialCargoCode1},
              #{item.specialCargoCode2}, #{item.specialCargoCode3}, #{item.otherSpecialCargoCode}, #{item.dangerCode}, #{item.dangerType}, #{item.costSum},
              #{item.rateType}, #{item.writeTime}, #{item.writeLocation}, #{item.writer}, #{item.remark}, #{item.payStatus}, #{item.payTime}, #{item.deptId},
              #{item.cargoQuantity}, #{item.cargoWeight}, #{item.mailQuantity}, #{item.mailWeight}, #{item.categoryName}, #{item.mainOrEmail}, #{item.payMoney},
              #{item.depQuantity}, #{item.depWeight}, #{item.collectTime}, #{item.mailDepQuantity}, #{item.mailDepWeight}, #{item.wCostSum}, #{item.rCostSum}
              )
        </foreach>
        ON DUPLICATE KEY UPDATE
            waybill_code = VALUES(waybill_code),
            waybill_prefix = VALUES(waybill_prefix),
            type = VALUES(type),
            waybill_type = VALUES(waybill_type),
            status = VALUES(status),
            shipping_station = VALUES(shipping_station),
            agent_company = VALUES(agent_company),
            transfer_bill = VALUES(transfer_bill),
            origin_bill = VALUES(origin_bill),
            can_restock_weight = VALUES(can_restock_weight),
            cross_air = VALUES(cross_air),
            source_port = VALUES(source_port),
            carrier1 = VALUES(carrier1),
            des1 = VALUES(des1),
            carrier2 = VALUES(carrier2),
            des2 = VALUES(des2),
            carrier3 = VALUES(carrier3),
            des_port = VALUES(des_port),
            flight_no1 = VALUES(flight_no1),
            flight_date1 = VALUES(flight_date1),
            flight_no2 = VALUES(flight_no2),
            flight_date2 = VALUES(flight_date2),
            shipper = VALUES(shipper),
            city = VALUES(city),
            storage_transport_notes = VALUES(storage_transport_notes),
            settlement_notes = VALUES(settlement_notes),
            customs_supervision = VALUES(customs_supervision),
            official_form = VALUES(official_form),
            bulk_warehouse = VALUES(bulk_warehouse),
            cargo_code = VALUES(cargo_code),
            cargo_name = VALUES(cargo_name),
            quantity = VALUES(quantity),
            weight = VALUES(weight),
            charge_weight = VALUES(charge_weight),
            pack = VALUES(pack),
            size = VALUES(size),
            volume = VALUES(volume),
            special_cargo_code1 = VALUES(special_cargo_code1),
            special_cargo_code2 = VALUES(special_cargo_code2),
            special_cargo_code3 = VALUES(special_cargo_code3),
            other_special_cargo_code = VALUES(other_special_cargo_code),
            danger_code = VALUES(danger_code),
            danger_type = VALUES(danger_type),
            cost_sum = VALUES(cost_sum),
            rate_type = VALUES(rate_type),
            write_time = VALUES(write_time),
            write_location = VALUES(write_location),
            writer = VALUES(writer),
            remark = VALUES(remark),
            pay_status = VALUES(pay_status),
            pay_time = VALUES(pay_time),
            dept_id = VALUES(dept_id),
            cargo_quantity = VALUES(cargo_quantity),
            cargo_weight = VALUES(cargo_weight),
            mail_quantity = VALUES(mail_quantity),
            mail_weight = VALUES(mail_weight),
            category_name = VALUES(category_name),
            main_or_email = VALUES(main_or_email),
            pay_money = VALUES(pay_money),
            dep_quantity = VALUES(dep_quantity),
            dep_weight = VALUES(dep_weight),
            collect_time = VALUES(collect_time),
            mail_dep_quantity = VALUES(mail_dep_quantity),
            mail_dep_weight = VALUES(mail_dep_weight),
            w_cost_sum = VALUES(w_cost_sum),
            r_cost_sum = VALUES(r_cost_sum)
    </insert>

    <select id="getLastTime" resultType="java.util.Date">
        select last_sync_time from sync_metadata where table_name = #{tableName}
    </select>
    <select id="selectWaybillDataList" resultType="com.gzairports.common.business.reporter.domain.ReportDataWaybill">
        select id, waybill_code, type, waybill_type, status, shipping_station, agent_company, transfer_bill, origin_bill, can_restock_weight, cross_air,
        source_port, carrier1, des1, carrier2, des2, carrier3, des3, des_port, flight_no1, flight_date1, flight_no2, flight_date2, shipper, city,
        storage_transport_notes, settlement_notes, customs_supervision, official_form, bulk_warehouse, category_name, cargo_code, cargo_name, quantity, weight,
        charge_weight, pack, size, volume, special_cargo_code1, special_cargo_code2, special_cargo_code3, other_special_cargo_code, danger_code, is_del,
        danger_type, cost_sum, rate_type, write_time, write_location, writer, remark, pay_status, pay_time, dept_id, pay_money, w_cost_sum, r_cost_sum
        from all_air_waybill
        <where>
            1 = 1
            <if test="lastSyncTime != null">
                and update_time > #{lastSyncTime}
            </if>
            <if test="dateNow != null">
                and update_time <![CDATA[<=]]> #{dateNow}
            </if>
        </where>
        ORDER BY update_time ASC
        LIMIT #{offset}, #{size}
    </select>
    <select id="selectHawbDataList" resultType="com.gzairports.common.business.reporter.domain.ReportDataHawb">
        select id, waybill_code, status, master_waybill_code, source_port, carrier1, carrier2, des2, carrier3, des3, des_port, flight_no,
        flight_date, collect_method, shipper, consignee as consign, cargo_code, cargo_name, quantity, weight, charge_weight,
        pack, delivery_method, size, volume, storage_transport_notes, settlement_notes, transport_insure_value, special_cargo_code1,
        special_cargo_code2, special_cargo_code3, other_special_cargo_code, payment_method, pay_status, pay_time, cost_sum, write_time,
        write_location, writer, dept_id
        from wl_dep_hawb
        <where>
            1 = 1
            <if test="lastSyncTime != null">
                and update_time > #{lastSyncTime}
            </if>
            <if test="dateNow != null">
                and update_time <![CDATA[<=]]> #{dateNow}
            </if>
        </where>
    </select>
</mapper>