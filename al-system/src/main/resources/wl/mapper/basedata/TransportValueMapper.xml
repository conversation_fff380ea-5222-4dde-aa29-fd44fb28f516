<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.TransportValueMapper">
    <select id="selectListByQuery" resultType="com.gzairports.common.basedata.domain.BaseTransportValue">
        select btv.id, btv.name, btv.transport_type, btv.rate, btv.is_enable, btv.cargo_category, btv.cargo_code, btv.cargo_name
        from base_transport_value btv
        <where>
            and btv.is_del = 0
            <if test="name != null and name != ''"> and btv.name like concat('%', #{name}, '%')</if>
            <if test="transportType != null"> and btv.transport_type = #{transportType}</if>
            <if test="deptId != null"> and btv.dept_id = #{deptId}</if>
        </where>
    </select>
    <select id="selectOneByQuery" resultType="com.gzairports.common.basedata.domain.BaseTransportValue">
        select btv.id, btv.name, btv.transport_type, btv.rate, btv.is_enable, btv.cargo_code, btv.cargo_category, btv.cargo_name from base_transport_value btv
                 where btv.is_del = 0 and btv.transport_type = 0 and btv.is_enable = 1 and btv.dept_id = #{deptId}
    </select>
</mapper>