package com.gzairports.hz.business.departure.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.gzairports.hz.business.departure.domain.FlightLoad;
import com.gzairports.hz.business.departure.domain.query.*;
import com.gzairports.hz.business.departure.domain.vo.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 航班配载Service接口
 *
 * <AUTHOR>
 * @date 2024-06-26
 */
public interface IFlightLoadService extends IService<FlightLoad> {

    /**
     * 查询航班配载列表
     * @param query 查询条件
     * @return 配载列表
     */
    List<FlightLoadVo> selectList(FlightLoadQuery query,String[] split);

    /**
     * 查询代运导入
     * @param query 查询条件
     * @return 结果
     */
    ForwardImportVo forwardImport(ForwardImportQuery query);

    /**
     * 装配
     * @param query 装配数据
     * @return 结果
     */
    ForwardImportVo assemble(AssembleQuery query);

    /**
     * 装箱
     * @param query 装箱参数
     * @return 结果
     */
    ForwardImportVo packing(FlightLoadPackQuery query);

    /**
     * 部分装箱
     * @param query 部分装箱参数
     * @return 结果
     */
    ForwardImportVo partPack(FlightLoadPackQuery query);

    /**
     * 更新舱位
     * @param id 配载板箱id
     * @param cabin 舱位
     * @return 结果
     */
    int updateCabin(Long id, String cabin);

    /**
     * 放散舱
     * @param query 放散舱参数
     * @return 结果
     */
    ForwardImportVo looseCabin(FlightLoadPackQuery query);

    /**
     * 部分放散舱
     * @param query 部分放散舱参数
     * @return 结果
     */
    ForwardImportVo partLooseCabin(FlightLoadPackQuery query);

    /**
     * 航班配载
     * @param id 航班配载id
     * @return 结果
     */
    ForwardImportVo load(Long id);

    /**
     * 新增配置查询
     * @param query 查询条件
     * @return 结果
     */
    ForwardImportVo addQuery(AddQuery query);

    /**
     * 航段查询
     * @param query 查询条件
     * @return 航段集合
     */
    LoadFlightVo legQuery(AddQuery query);

    /**
     * 卸下
     * @param query 卸下参数
     * @return 结果
     */
    ForwardImportVo removeByQuery(FlightLoadPackQuery query);

    /**
     * 根据运单号查询仓库库位
     * @param waybillCode 运单号
     * @return 仓库库位
     */
    LoadLocatorVo getLocator(String waybillCode);

    /**
     * 板箱配上
     * @param query 参数
     * @return 结果
     */
    ForwardImportVo uldAdd(UldAddQuery query);

    /**
     * 正式舱单
     * @param id 航班配载id
     * @return 结果
     */
    FormalManifestVo formalManifest(Long id);

    /**
     * 通过id查询正式舱单数据
     * */
    FormalManifestVo formalManifestById(FormalManifestIds ids);

    /**
     * 舱单打印
     * @param id 航班配载id
     */
    void printManifest(Long id) throws Exception;

    /**
     * 航班配载拉下
     * @param query 拉下参数
     * @return 结果
     */
    ForwardImportVo pullDown(FlightLoadPackQuery query);

    /**
     * 无货
     * @param id 航段id
     * @return 结果
     */
    int editState(Long id);

    /**
     * 打印机长通知单
     * @param id 配载id
     * @param response 返回流
     */
    void printCaptain(HttpServletResponse response,Long id,String type) throws Exception;

    /**
     * 远程打印舱单
     * @param id 配载id
     */
    void remotePrint(Long id) throws Exception;

    /**
     * 根据四位或多位航班号查出航司二字码
     * @param airlinesCode 四位或多位航班号 flightDate 航班时间
     */
    String getAirLinesByCode(String airlinesCode,String flightDate);

    /**
     * 重新开放制单
     * @param flightId 航班id
     * @return 结果
     */
    int openCreate(Long flightId);


    int compFlight(Long flightId) throws JsonProcessingException;

    int openFlight(Long flightId);
}
