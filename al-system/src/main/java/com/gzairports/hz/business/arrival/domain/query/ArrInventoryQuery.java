package com.gzairports.hz.business.arrival.domain.query;

import lombok.Data;

import java.util.Date;

/**
 * 库存管理查询参数
 * <AUTHOR>
 * @date 2024-07-26
 */
@Data
public class ArrInventoryQuery {

    /** 录单时间 */
    private Date startTime;

    /** 录单时间 */
    private Date endTime;

    /** 航班号 */
    private String flightNo;

    /** 发货人（代理人） */
    private String shipper;

    /** 运单号 */
    private String waybillCode;

    /** 仓库 */
    private String store;

    /** 库位 */
    private String locator;
}
