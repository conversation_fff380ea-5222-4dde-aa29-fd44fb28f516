<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.wl.reporter.mapper.ReportSetFieldMapper">

    <select id="getInfoBySetId" resultType="com.gzairports.wl.reporter.domain.vo.SelectFieldsVo">
        select field_name_cn, field_name from wl_report_set_field where set_id = #{id}
    </select>
    <select id="selectSortFields" resultType="com.gzairports.wl.reporter.domain.vo.SortFieldVo">
        select field_name_cn, field_name, sort_type from wl_report_set_field where set_id = #{id} and sort_type != 0
    </select>
    <select id="selectReportDataFields" resultType="com.gzairports.wl.reporter.domain.ReportSetField">
        select wrsf.*,wrs.*
        from wl_report_set wrs
                 left join wl_report_set_field wrsf
                           on wrs.id = wrsf.set_id
        where wrs.report_title = #{reportTitle}
        order by wrsf.field_index
    </select>
</mapper>