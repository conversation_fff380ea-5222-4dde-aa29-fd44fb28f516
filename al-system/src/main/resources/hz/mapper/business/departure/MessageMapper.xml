<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.message.mapper.MessageMapper">
    <select id="selectListByQuery" resultType="com.gzairports.common.message.domain.Message">
        select id, title, handle_name, is_handle, content, post_id, create_time, handle_time from all_message
        <where>
            <if test="startHandleTime != null">
                and handle_time <![CDATA[>=]]> #{startHandleTime}
            </if>
            <if test="endHandleTime != null">
                and handle_time <![CDATA[<=]]> #{endHandleTime}
            </if>
            <if test="startCreateTime != null">
                and create_time <![CDATA[>=]]> #{startCreateTime}
            </if>
            <if test="endCreateTime != null">
                and create_time <![CDATA[<=]]> #{endCreateTime}
            </if>
            <if test="postId != null">
                and post_id = #{postId}
            </if>
            <if test="handleName != null">
                and handle_name like '%${handleName}%'
            </if>
            <if test="content != null">
                and content like '%${content}%'
            </if>
        </where>
    </select>
</mapper>