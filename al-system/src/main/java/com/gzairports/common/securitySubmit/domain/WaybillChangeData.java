package com.gzairports.common.securitySubmit.domain;

import lombok.Data;

/**
 * <AUTHOR>
 * @description 3.物流平台选择换单，将原单号、新单号推送货检系统
 * @date 2025-06-17
 **/
@Data
public class WaybillChangeData {
    /** 类型
     * 1.货站推送收运数据
     * 2.货站完成航班/定时任务自动关闭航班
     * 3.物流平台选择换单，将原单号、新单号推送货检系统
     * 4.代理人不正常货邮选择换单
     * 5.货运系统完成退单
     * 6.代理人重复提交品名清单附件
     **/
    private Integer type;

    /** 原运单号 */
    private String waybillCode;

    /** 换单单号 */
    private String waybillCodeNew;

}
