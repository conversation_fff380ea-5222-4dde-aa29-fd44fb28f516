package com.gzairports.common.basedata.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 仓库管理
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Data
@TableName("base_store")
public class BaseStore {

    /** 主键id */
    private Long id;

    /** 仓库编码 */
    @Excel(name = "仓库编码")
    private String code;

    /** 仓库名称 */
    @Excel(name = "仓库名称")
    private String name;

    /** 库位 */
    @Excel(name = "库位")
    private String locator;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 是否删除 0 否 1 是 */
    private Integer isDel;
}
