package com.gzairports.hz.business.cable.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-07
 */
@Data
public class FFMJsonVO {

    /** 承运人 */
    private String carrier;

    /** 航班号 */
    private String flightNo;

    /** 预计起飞时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date etd;

    /** 起运机场 */
    private String oriAirport;

    /** 飞机注册号 */
    private String aircraftRegistration;

    private List<PointOfUnloading> pointOfUnloading;

    /** 到达站 */
    private String arrival;

    /** 批序列号 */
    private String batchSerialNo;

    /** 国家代码 */
    private String countryCode;

    /** 结束标志 */
    private String endFlag;

    /** 预计到达时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date eta;
}
