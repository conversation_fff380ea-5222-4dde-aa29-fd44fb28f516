package com.gzairports.common.infoquery.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.gzairports.common.core.domain.entity.SysDept;
import com.gzairports.common.infoquery.service.IManifestService;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.hz.business.departure.domain.query.FlightLoadQuery;
import com.gzairports.hz.business.departure.domain.vo.FlightLoadVo;
import com.gzairports.hz.business.departure.domain.vo.FormalManifestVo;
import com.gzairports.hz.business.departure.service.IFlightLoadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 舱单查询 服务层impl
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@Service
public class ManifestServiceImpl implements IManifestService {

    @Autowired
    private IFlightLoadService flightLoadService;

    @Resource
    private SysDeptMapper deptMapper;

    @Override
    public List<FlightLoadVo> selectList(FlightLoadQuery query, String[] split) {
        if (!setEnableAirLines(query)) {
            return Collections.emptyList();
        }
        return flightLoadService.selectList(query, split);
    }

    @Override
    public FormalManifestVo infoFormalManifest(Long id) {
        return flightLoadService.formalManifest(id);
    }


    /**
     * 检查并设置部门设置的可查询航司二字码 query
     *
     * @param query
     * @return false-无设置，true-有设置
     */
    private boolean setEnableAirLines(FlightLoadQuery query) {
        //查询 当前部门即子部门 设置的可查询的 航司二字码
        Long deptId = SecurityUtils.getDeptId();
        SysDept sysDept = deptMapper.selectDeptById(deptId);

        if (sysDept.getDeptType().equals(2)) {
            //航司类型的部门，添加可查询航司限制
            List<SysDept> allEnableTmpDept = deptMapper.selectEnabledPrefixAndAirline(deptId);
            Set<String> allAirline = allEnableTmpDept.stream()
                    .filter(ObjectUtil::isNotNull)
                    .map(SysDept::getAirlineCodes)
                    .filter(StrUtil::isNotBlank)
                    .flatMap(airline -> Arrays.stream(airline.split(",")))
                    .map(String::trim)
                    .collect(Collectors.toSet());
            if (CollectionUtil.isEmpty(allAirline)) {
                return false;
            }
            query.setEnabledAirlineCodeList(allAirline);
            return true;
        }
        return true;
    }
}
