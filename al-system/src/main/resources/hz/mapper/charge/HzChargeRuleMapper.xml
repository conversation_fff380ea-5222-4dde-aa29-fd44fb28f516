<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.charge.mapper.HzChargeRuleMapper">
    
    <resultMap type="HzChargeRule" id="HzChargeRuleResult">
        <result property="id"    column="id"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="remark"    column="remark"    />
        <result property="className"    column="class_name"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectHzChargeRuleVo">
        select id, rule_name, remark, class_name, is_del from hz_charge_rule
    </sql>

    <select id="selectHzChargeRuleList" parameterType="HzChargeRule" resultMap="HzChargeRuleResult">
        <include refid="selectHzChargeRuleVo"/>
        <where>
            and is_del = 0
            <if test="ruleName != null  and ruleName != ''"> and rule_name like concat('%', #{ruleName}, '%')</if>
            <if test="className != null  and className != ''"> and class_name like concat('%', #{className}, '%')</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectHzChargeRuleById" parameterType="Long" resultMap="HzChargeRuleResult">
        <include refid="selectHzChargeRuleVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertHzChargeRule" parameterType="HzChargeRule" useGeneratedKeys="true" keyProperty="id">
        insert into hz_charge_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleName != null and ruleName != ''">rule_name,</if>
            <if test="remark != null">remark,</if>
            <if test="className != null">class_name,</if>
            <if test="isDel != null">is_del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleName != null and ruleName != ''">#{ruleName},</if>
            <if test="remark != null">#{remark},</if>
            <if test="className != null">#{className},</if>
            <if test="isDel != null">#{isDel},</if>
         </trim>
    </insert>

    <update id="updateHzChargeRule" parameterType="HzChargeRule">
        update hz_charge_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleName != null and ruleName != ''">rule_name = #{ruleName},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="className != null">class_name = #{className},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteHzChargeRuleById" parameterType="Long">
        update hz_charge_rule set is_del = 1 where id = #{id}
    </update>

</mapper>