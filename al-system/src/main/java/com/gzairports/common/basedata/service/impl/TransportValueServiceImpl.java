package com.gzairports.common.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.BaseCargoCode;
import com.gzairports.common.basedata.domain.BaseTransportValue;
import com.gzairports.common.basedata.domain.query.BaseTransportValueQuery;
import com.gzairports.common.basedata.mapper.CargoCodeMapper;
import com.gzairports.common.basedata.mapper.TransportValueMapper;
import com.gzairports.common.basedata.service.ITransportValueService;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.exception.ServiceException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 运输价值维护Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Service
public class TransportValueServiceImpl extends ServiceImpl<TransportValueMapper, BaseTransportValue> implements ITransportValueService {

    @Autowired
    private TransportValueMapper valueMapper;

    @Autowired
    private CargoCodeMapper codeMapper;

    /**
     * 查询运输价值维护列表
     *
     * @param query 查询参数
     * @return 运输价值维护列表
     */
    @Override
    public List<BaseTransportValue> selectTransportValueList(BaseTransportValueQuery query) {
        return valueMapper.selectListByQuery(query);
    }

    /**
     * 新增运输价值维护
     *
     * @param value 运输价值维护
     * @return 结果
     */
    @Override
    public int addTransportValue(BaseTransportValue value) {
        BaseTransportValue baseTransportValue = valueMapper.selectOne(new QueryWrapper<BaseTransportValue>()
                .eq("transport_type", value.getTransportType())
                .eq("cargo_code", value.getCargoCode()));
        if (baseTransportValue != null){
            throw new CustomException("运输价值不能重复");
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        value.setCreateBy(loginUser.getUsername());
        value.setCreateTime(DateUtils.getNowDate());
        return valueMapper.insert(value);
    }

    /**
     * 修改运输价值维护
     *
     * @param value 运输价值维护
     * @return 结果
     */
    @Override
    public int editTransportValue(BaseTransportValue value) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        value.setUpdateBy(loginUser.getUsername());
        value.setUpdateTime(DateUtils.getNowDate());
        return valueMapper.updateById(value);
    }

    /**
     * 删除运输价值维护
     *
     * @param id 运输价值维护id集合
     * @return 结果
     */
    @Override
    public int delTransportValue(Long id) {
        BaseTransportValue value = valueMapper.selectById(id);
        if (value == null){
            throw new ServiceException("未查询到当前运输价值维护数据");
        }
        value.setIsDel(1);
        return valueMapper.updateById(value);
    }

    /**
     * 根据id获取运输价值维护
     *
     * @param id 运输价值维护id
     * @return 运输价值维护
     */
    @Override
    public BaseTransportValue getInfo(Long id) {
        return valueMapper.selectById(id);
    }
}
