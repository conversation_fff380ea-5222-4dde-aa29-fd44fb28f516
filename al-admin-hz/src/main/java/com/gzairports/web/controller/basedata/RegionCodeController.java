package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BaseRegionCode;
import com.gzairports.common.basedata.domain.query.RegionCodeQuery;
import com.gzairports.common.basedata.service.IRegionCodeService;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * 国家或地区代码Controller
 *
 * <AUTHOR>
 * @date 2024-02-20
 */
@RestController
@RequestMapping("/base/region")
@Api(tags = "国家或地区代码数据接口")
public class RegionCodeController extends BaseController {

    @Autowired
    private IRegionCodeService regionCodeService;

    /**
     * 查询国家或地区代码列表
     */
//    @PreAuthorize("@ss.hasPermi('base:region:regionCodeList')")
    @GetMapping("/regionCodeList")
    @ApiOperation(value = "查询国家或地区代码列表")
    public TableDataInfo regionCodeList(RegionCodeQuery query)
    {
        startPage();
        List<BaseRegionCode> list = regionCodeService.selectRegionCodeList(query);
        return getDataTable(list);
    }

    /**
     * 导出国家或地区代码
     */
    @PreAuthorize("@ss.hasPermi('base:region:exportRegionCode')")
    @Log(title = "导出国家或地区代码", businessType = BusinessType.EXPORT)
    @PostMapping("/exportRegionCode")
    @ApiOperation(value = "导出国家或地区代码")
    public void exportRegionCode(HttpServletResponse response, RegionCodeQuery query)
    {
        List<BaseRegionCode> list = regionCodeService.selectRegionCodeList(query);
        ExcelUtil<BaseRegionCode> util = new ExcelUtil<BaseRegionCode>(BaseRegionCode.class);
        util.exportExcel(response, list, "国家或地区代码");
    }

    /**
     * 导出国家或地区代码的模板
     */
    @PreAuthorize("@ss.hasPermi('base:region:exportRegionCode')")
    @Log(title = "导出国家或地区代码下载模板", businessType = BusinessType.EXPORT)
    @PostMapping("/exportRegionCodeNull")
    @ApiOperation(value = "导出国家或地区代码下载模板")
    public void exportRegionCodeNull(HttpServletResponse response)
    {
        List<BaseRegionCode> list = new ArrayList<>();
        ExcelUtil<BaseRegionCode> util = new ExcelUtil<BaseRegionCode>(BaseRegionCode.class);
        util.exportExcel(response, list, "国家或地区代码下载模板");
    }

    /**
     * 导入国家或地区代码
     */
    @PreAuthorize("@ss.hasPermi('base:region:importRegionCode')")
    @Log(title = "导入国家或地区代码", businessType = BusinessType.IMPORT)
    @PostMapping("/importRegionCode")
    @ApiOperation(value = "导入国家或地区代码")
    public AjaxResult importRegionCode(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<BaseRegionCode> util = new ExcelUtil<BaseRegionCode>(BaseRegionCode.class);
        List<BaseRegionCode> regionCodeList = util.importExcel(file.getInputStream());
        String message = regionCodeService.importRegionCode(regionCodeList, updateSupport);
        return success(message);
    }

    /**
     * 新增国家或地区代码
     */
    @PreAuthorize("@ss.hasPermi('base:region:addRegionCode')")
    @Log(title = "新增国家或地区代码", businessType = BusinessType.INSERT)
    @PostMapping("/addRegionCode")
    @ApiOperation(value = "新增国家或地区代码")
    public AjaxResult addRegionCode(@RequestBody BaseRegionCode regionCode)
    {
        return toAjax(regionCodeService.addRegionCode(regionCode));
    }

    /**
     * 修改国家或地区代码
     */
    @PreAuthorize("@ss.hasPermi('base:region:editRegionCode')")
    @Log(title = "修改国家或地区代码", businessType = BusinessType.UPDATE)
    @PostMapping("/editRegionCode")
    @ApiOperation(value = "修改国家或地区代码")
    public AjaxResult editRegionCode(@RequestBody BaseRegionCode regionCode)
    {
        return toAjax(regionCodeService.editRegionCode(regionCode));
    }

    /**
     * 删除国家或地区代码
     */
    @PreAuthorize("@ss.hasPermi('base:region:delRegionCode')")
    @Log(title = "删除国家或地区代码", businessType = BusinessType.UPDATE)
    @GetMapping("/delRegionCode/{ids}")
    @ApiOperation(value = "删除国家或地区代码")
    public AjaxResult delRegionCode(@PathVariable("ids") Long[] ids)
    {
        return toAjax(regionCodeService.delRegionCode(ids));
    }

}
