package com.gzairports.web.controller.charge;

import com.gzairports.wl.charge.domain.FreightRate;
import com.gzairports.wl.charge.domain.FreightRateAirItem;
import com.gzairports.wl.charge.domain.FreightRateItem;
import com.gzairports.wl.charge.domain.query.FreightRateQuery;
import com.gzairports.wl.charge.domain.vo.FreightRateItemVO;
import com.gzairports.wl.charge.domain.vo.FreightRateVO;
import com.gzairports.wl.charge.service.FreightRateService;
import com.gzairports.common.annotation.OperLog;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.wl.log.service.WlOperLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 公布运价Controller
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@RestController
@RequestMapping("/freight")
@Api(tags = "公布运价接口")
public class FreightRateController extends BaseController {

    @Autowired
    private FreightRateService rateService;

    @Autowired
    private WlOperLogService logService;

    /**
     * 查询公布运价列表
     */
//    @PreAuthorize("@ss.hasPermi('charge:freight:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询公布运价列表")
    public TableDataInfo list(FreightRateQuery query){
        startPage();
        List<FreightRateVO> list = rateService.selectList(query);
        return getDataTable(list);
    }

    /**
     * 导出公布运价列表
     */
    @PreAuthorize("@ss.hasPermi('charge:freight:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出公布运价")
    public void export(HttpServletResponse response, FreightRateQuery query){
        List<FreightRateVO> list = rateService.selectList(query);
        ExcelUtil<FreightRateVO> util = new ExcelUtil<FreightRateVO>(FreightRateVO.class);
        util.exportExcel(response, list, "公布运价");
    }

    /**
     * 导入公布运价条目
     */
//    @PreAuthorize("@ss.hasPermi('charge:freightAir:importItem')")
    @OperLog(title = "公布运价", businessType = BusinessType.IMPORT)
    @PostMapping("/importItem")
    @ApiOperation(value = "导入公布运价条目")
    public AjaxResult importItem(@RequestParam("file") MultipartFile file, @RequestParam("rateId") Long rateId, @RequestParam("updateSupport") boolean updateSupport) throws Exception
    {
        if (file == null){
            return warn("请上传文件");
        }
        ExcelUtil<FreightRateItem> util = new ExcelUtil<FreightRateItem>(FreightRateItem.class);
        List<FreightRateItem> rateItems = util.importRateExcel(file.getInputStream(),1);
        Long message = rateService.importItem(rateItems,rateId, updateSupport);
        return success(message);
    }

    /**
     * 获取公布运价详细信息
     */
    @PreAuthorize("@ss.hasPermi('charge:freight:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取公布运价详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rateService.selectRateById(id));
    }

    /**
     * 新增公布运价
     */
    @PreAuthorize("@ss.hasPermi('charge:freight:add')")
    @OperLog(title = "公布运价", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ApiOperation(value = "新增公布运价")
    public AjaxResult add(@RequestBody FreightRate rate)
    {
        return success(rateService.insertFreightRate(rate));
    }

    /**
     * 修改公布运价
     */
    @PreAuthorize("@ss.hasPermi('charge:freight:edit')")
    @OperLog(title = "公布运价", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ApiOperation(value = "修改公布运价")
    public AjaxResult edit(@RequestBody FreightRate rate)
    {
        return toAjax(rateService.updateFreightRate(rate));
    }

    /**
     * 查看运价条目
     */
    @PreAuthorize("@ss.hasPermi('charge:freight:rateItemList')")
    @GetMapping("/rateItemList/{id}")
    @ApiOperation(value = "查看运价条目")
    public TableDataInfo rateItemList(@PathVariable Long id)
    {
        startPage();
        List<FreightRateItemVO> freightRateItemVOS = rateService.selectRateItemList(id);
        return getDataTable(freightRateItemVOS);
    }

    /**
     * 删除公布运价
     */
    @PreAuthorize("@ss.hasPermi('charge:freight:del')")
    @OperLog(title = "公布运价", businessType = BusinessType.DELETE)
    @GetMapping("/del/{id}")
    @ApiOperation(value = "删除公布运价")
    public AjaxResult del(@PathVariable Long id)
    {
        return toAjax(rateService.deleteFreightRate(id));
    }

    /**
     * 公布运价操作日志查询
     */
    @PreAuthorize("@ss.hasPermi('charge:freight:logList')")
    @GetMapping("/logList/{id}")
    @ApiOperation(value = "公布运价操作日志查询")
    public AjaxResult logList(@PathVariable Long id)
    {
        return success(logService.log("公布运价",id));
    }

    /**
     * 公布运价操作日志详情
     */
    @PreAuthorize("@ss.hasPermi('charge:freight:logInfo')")
    @GetMapping("/logInfo/{operId}")
    @ApiOperation(value = "公布运价操作日志详情")
    public AjaxResult logInfo(@PathVariable Long operId)
    {
        return success(logService.logInfo(operId));
    }
}
