package com.gzairports.web.controller.business.arrival;

import com.gzairports.common.business.arrival.domain.query.AutoOrderQuery;
import com.gzairports.common.business.arrival.domain.query.H5PickUpOutQuery;
import com.gzairports.common.business.arrival.service.IPickUpService;
import com.gzairports.common.core.domain.AjaxResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 代理人H5提货出库
 *
 * <AUTHOR>
 * @date 2024-08-06
 */
@RestController
@RequestMapping("/arrH5/pickUpOut")
public class H5PickUpOutController {

    @Autowired
    private IPickUpService pickUpService;

    /**
     * 列表查询
     */
    @PostMapping("/list")
    @ApiOperation(value = "列表查询")
    public AjaxResult list(@RequestBody H5PickUpOutQuery query){
        return AjaxResult.success(pickUpService.arrH5List(query));
    }

    /**
     * 办单详情
     */
    @GetMapping("/getInfo/{pickUpId}")
    @ApiOperation(value = "办单详情")
    public AjaxResult getInfo(@PathVariable("pickUpId") Long pickUpId){
        return AjaxResult.success(pickUpService.getInfo(pickUpId));
    }

    /**
     * 查看提货码
     */
    @GetMapping("/pickUpCode/{pickUpId}")
    @ApiOperation(value = "查看提货码")
    public AjaxResult pickUpCode(@PathVariable("pickUpId") Long pickUpId){
        return AjaxResult.success(pickUpService.pickUpCode(pickUpId));
    }

    /**
     * 散客H5提货办单
     */
    @PostMapping("/retailH5Order")
    @ApiOperation(value = "散客H5提货办单")
    public AjaxResult retailH5Order(@RequestBody AutoOrderQuery query){
        return AjaxResult.success(pickUpService.autoOrder(query));
    }

    /**
     * 打印出库单
     */
    @GetMapping("/printOutOrder/{pickUpCode}")
    @ApiOperation(value = "打印出库单")
    public AjaxResult printOutOrder(@PathVariable("pickUpCode") String pickUpCode,
                                    @RequestHeader(value = "Platform-Type",required = false) String platformType) {
        return AjaxResult.success(pickUpService.h5PrintOutOrder(pickUpCode, platformType));
    }

    /**
     * 查询该提货码打印次数
     * */
    @GetMapping("/printOutCount/{pickUpCode}")
    @ApiOperation(value = "查询该提货码打印次数")
    public AjaxResult printOutCount(@PathVariable("pickUpCode") String pickUpCode){
        return AjaxResult.success(pickUpService.printOutCount(pickUpCode));
    }

    /**
     * 散客H5提货办单（提交并查询运单）
     */
    @PostMapping("/submitAndSelect")
    @ApiOperation(value = "散客H5提货办单（提交并查询运单）")
    public AjaxResult submitAndSelect(@RequestBody AutoOrderQuery query){
        return AjaxResult.success(pickUpService.submitAndSelect(query));
    }

    /**
     * 散客H5提货办单（办单）
     */
    @GetMapping("/orderPro/{tallyId}")
    @ApiOperation(value = "散客H5提货办单（办单）")
    public AjaxResult orderPro(@PathVariable("tallyId") Long tallyId){
        return AjaxResult.success(pickUpService.orderPro(tallyId));
    }
}
