package com.gzairports.web.controller.business.departure;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.hz.business.departure.domain.query.SpecialTraceQuery;
import com.gzairports.hz.business.departure.domain.vo.SpecialTraceVo;
import com.gzairports.hz.business.departure.service.ISpecialTraceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 特货跟踪Controller
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@RestController
@RequestMapping("/dep/specialTrace")
@Api(tags = "特货跟踪")
public class SpecialTraceController extends BaseController {

    @Autowired
    private ISpecialTraceService specialTraceService;

    /**
     * 查询特货跟踪列表
     */
//    @PreAuthorize("@ss.hasPermi('dep:specialTrace:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询特货跟踪列表")
    public TableDataInfo list(SpecialTraceQuery query){
        startPage();
        List<SpecialTraceVo> list = specialTraceService.selectList(query);
        return getDataTable(list);
    }
}
