package com.gzairports.wl.departure.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @author: lan
 * @Desc: 物流安检申报查询参数
 * @create: 2024-10-11 16:58
 **/
@Data
public class SecurityQueryWl {
    /**开始时间*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**结束时间*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**安检状态*/
    private String securitySubmitWl;

    /** 运单号 */
    private String waybillCode;

    /** 是否特货 0否 1是 */
    private Integer isSpecial;

    /** 部门id */
    private Long deptId;

    /**页数*/
    private Integer startRow;

    /**条数*/
    private Integer endRow;
}
