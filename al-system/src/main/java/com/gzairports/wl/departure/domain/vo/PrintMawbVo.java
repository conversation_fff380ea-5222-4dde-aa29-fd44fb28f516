package com.gzairports.wl.departure.domain.vo;

import com.gzairports.common.pdf.PdfPrintAnnotation;
import lombok.Data;

import java.math.BigDecimal;

import static com.gzairports.common.pdf.PdfPrintAnnotation.PdfFieldType.IMAGE;
import static com.gzairports.common.pdf.PdfPrintAnnotation.PdfFieldType.TEXT;

/**
 * 打印主单标签
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
@Data
public class PrintMawbVo {

    /** 运单号 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "waybillCode")
    private String waybillCode;

    /** 重量 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "weight")
    private BigDecimal weight;

    /** 件数 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "quantity")
    private Integer quantity;

    /** 航班号 */
    private String flightNo;

    /** 目的站 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "desPort")
    private String desPort;

    /** 目的站 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "sourcePort")
    private String sourcePort;

    /** 打印个数 */
    private Integer printNum;

    /** 二维码 */
    @PdfPrintAnnotation(pdfFieldType = IMAGE, pdfFieldName = "qrCode")
    private String qrCode;

    /** 标签序号 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "count")
    private Integer count;

    private Long deptId;

    @PdfPrintAnnotation(pdfFieldType = IMAGE, pdfFieldName = "logoUrlSmall")
    private String logoUrlSmall;

    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "deptName")
    private String deptName;

    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "phone")
    private String phone;
}
