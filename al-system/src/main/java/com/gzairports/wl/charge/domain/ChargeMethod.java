package com.gzairports.wl.charge.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 客户收费方式表
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@Data
@TableName("wl_sf_custom_pay_method")
public class ChargeMethod {

    /** 主键id */
    private Long id;

    /** 客户名称 */
    private String customerName;

    /** 收费方式 */
    private String paymentMethod;

    /** 结算周期 */
    private Integer cycle;

    /** 是否启用 0 否 1 是 */
    private Integer enabled;

    /** 生效时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date effectiveTime;

    /** 所属单位 */
    private Long deptId;

    /** 条目是否逻辑删除 0 否 1 是 */
    private Integer isDelete;

    /** 更新时间 */
    private Date updateTime;

    /** 更新人 */
    private String updateBy;

    /** 创建时间 */
    private Date createTime;

    /** 创建人 */
    private String createBy;
}
