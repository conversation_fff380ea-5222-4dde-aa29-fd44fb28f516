package com.gzairports.common.basedata.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 票证管理
 *
 * <AUTHOR>
 * @date 2024-05-17
 */
@Data
@TableName("base_bill_type")
public class BaseBillType {

    /** 主键id */
    private Long id;

    /** 编码 */
    @Excel(name = "编码")
    private String code;

    /** 名称 */
    @Excel(name = "名称")
    private String name;

    /** 描述 */
    @Excel(name = "描述")
    private String remark;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 是否删除 0 否 1 是 */
    private Integer isDel;

    /** 所属单位 */
    private Long deptId;

    /** 国际国内 */
    private String domint;
}
