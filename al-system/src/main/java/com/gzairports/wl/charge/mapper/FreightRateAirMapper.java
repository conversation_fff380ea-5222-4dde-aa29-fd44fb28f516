package com.gzairports.wl.charge.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.wl.charge.domain.FreightRateAir;
import com.gzairports.wl.charge.domain.query.FreightRateAirQuery;
import com.gzairports.wl.charge.domain.vo.FreightRateAirVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 航司运价Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@Mapper
public interface FreightRateAirMapper extends BaseMapper<FreightRateAir> {

    /**
     * 查询航司运价列表
     *
     * @param query 查询参数
     * @return 航司运价列表
     */
    List<FreightRateAirVO> selectListByQuery(FreightRateAirQuery query);

    /**
     * 删除航司运价
     *
     * @param id 航司运价主键
     * @return 结果
     */
    int removeRateAir(Long id);
}
