package com.gzairports.web.controller.business.arrival;

import com.gzairports.common.business.arrival.domain.AllPickUpOut;
import com.gzairports.common.business.arrival.domain.HzArrItem;
import com.gzairports.common.business.arrival.domain.query.AppPickUpQuery;
import com.gzairports.common.business.arrival.domain.query.PickUpOutQuery;
import com.gzairports.common.business.arrival.domain.vo.PickOrderVo;
import com.gzairports.common.business.arrival.domain.vo.PickUpInfoVo;
import com.gzairports.common.business.arrival.domain.vo.PickUpOutWaybillVo;
import com.gzairports.common.business.arrival.domain.vo.ScanOutVo;
import com.gzairports.common.business.arrival.service.IAllPickUpOutService;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * 进港提货出库操作Controller
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
@RestController
@RequestMapping("/arr/pickUpOut")
@Api(tags = "提货出库")
public class PickUpOutController extends BaseController {

    @Autowired
    private IAllPickUpOutService pickUpOutService;

    /**
     * 提货出库列表查询
     */
//    @PreAuthorize("@ss.hasPermi('arr:pickUpOut:list')")
    @PostMapping("/list")
    @ApiOperation(value = "提货出库列表查询")
    public AjaxResult list(@RequestBody PickUpOutQuery query){
        return AjaxResult.success(pickUpOutService.selectList(query));
    }

    /**
     * 提货办单信息
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUpOut:getInfo')")
    @GetMapping("/getInfo/{id}")
    @ApiOperation(value = "提货办单信息")
    public AjaxResult getInfo(@PathVariable("id") Long id){
        return AjaxResult.success(pickUpOutService.getInfo(id));
    }

    /**
     * 保存提货件数和提货重量
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUpOut:savePickUpData')")
    @PostMapping("/savePickUpData")
    @ApiOperation(value = "保存提货件数和提货重量")
    public AjaxResult savePickUpData(@RequestBody PickUpOutWaybillVo vo){
        return toAjax(pickUpOutService.savePickUpData(vo));
    }

    /**
     * 修改提货信息
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUpOut:editPickUpData')")
    @PostMapping("/editPickUpData")
    @ApiOperation(value = "修改提货信息")
    public AjaxResult editPickUpData(@RequestBody PickUpOutWaybillVo vo){
        return toAjax(pickUpOutService.editPickUpData(vo));
    }

    /**
     * 提货出库
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUpOut:pickUpOut')")
    @GetMapping("/pickUpOut/{pickUpId}")
    @ApiOperation(value = "提货出库")
    public AjaxResult pickUpOut(@PathVariable("pickUpId") Long pickUpId){
        return AjaxResult.success(pickUpOutService.pickUpOut(pickUpId));
    }
    /**
     * 转南航 先简单标识一下
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUpOut:transferSouthernAirlines')")
    @GetMapping("/transferSouthernAirlines/{pickUpId}")
    @ApiOperation(value = "转南航")
    public AjaxResult transferSouthernAirlines(@PathVariable("pickUpId") Long pickUpId){
        return AjaxResult.success(pickUpOutService.transferSouthernAirlines(pickUpId));
    }

    /**
     * 费用明细
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUpOut:cost')")
    @GetMapping("/cost")
    @ApiOperation(value = "费用明细")
    public AjaxResult cost(@NotNull @RequestParam String waybillCode, @NotNull @RequestParam Long tallyId)
    {
        return AjaxResult.success(pickUpOutService.cost(waybillCode,tallyId));
    }

    /**
     * 费用明细详情
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUpOut:costInfo')")
    @GetMapping("/costInfo/{id}")
    @ApiOperation(value = "费用明细详情")
    public AjaxResult costInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(pickUpOutService.costInfo(id));
    }

    /**
     * 编辑费用明细
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUpOut:editCost')")
    @PostMapping("/editCost")
    @ApiOperation(value = "编辑费用明细")
    public AjaxResult editCost(@RequestBody HzArrItem item)
    {
        return toAjax(pickUpOutService.editCost(item));
    }

    /**
     * 删除费用明细
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUpOut:delCost')")
    @GetMapping("/delCost/{id}")
    @ApiOperation(value = "删除费用明细")
    public AjaxResult delCost(@PathVariable("id") Long id)
    {
        return toAjax(pickUpOutService.delCost(id));
    }

    /**
     * 新增费用明细
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUpOut:addCost')")
    @PostMapping("/addCost")
    @ApiOperation(value = "新增费用明细")
    public AjaxResult addCost(@RequestBody HzArrItem item)
    {
        return toAjax(pickUpOutService.addCost(item));
    }

    /**
     * 计算总费用
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUpOut:countCost')")
    @PostMapping("/countCost")
    @ApiOperation(value = "计算总费用")
    public AjaxResult countCost(@RequestBody HzArrItem item)
    {
        return AjaxResult.success(pickUpOutService.countCost(item));
    }

    /**
     * 备出库列表查询
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUpOut:planPickUpOut')")
    @PostMapping("/planPickUpOut")
    @ApiOperation(value = "备出库列表查询")
    public AjaxResult planPickUpOut(@RequestBody PickUpOutQuery query){
        return AjaxResult.success(pickUpOutService.planPickUpOut(query));
    }

    /**
     * 已提货出库查询
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUpOut:pickedUp')")
    @PostMapping("/pickedUp")
    @ApiOperation(value = "已提货出库查询")
    public AjaxResult pickedUp(@RequestBody PickUpOutQuery query){
        return AjaxResult.success(pickUpOutService.pickedUp(query));
    }

    /**
     * app提货出库列表
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUpOut:appPickUpList')")
    @PostMapping("/appPickUpList")
    @ApiOperation(value = "app提货出库列表")
    public AjaxResult appPickUpList(@RequestBody AppPickUpQuery query){
        return AjaxResult.success(pickUpOutService.appPickUpList(query));
    }

    /**
     * app扫描出库
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUpOut:scanOut')")
    @GetMapping("/scanOut/{pickUpCode}")
    @ApiOperation(value = "app扫描出库")
    public AjaxResult scanOut(@PathVariable("pickUpCode") String pickUpCode){
        ScanOutVo scanOutVo = pickUpOutService.scanOut(pickUpCode);
        if (scanOutVo.getStatus() == 5){
            return warn("该办单已作废，请重新办单");
        }
        return AjaxResult.success(scanOutVo);
    }

    /**
     * app提货详情
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUpOut:pickUpInfo')")
    @GetMapping("/pickUpInfo")
    @ApiOperation(value = "app提货详情")
    public AjaxResult pickUpInfo(@NotNull @RequestParam String waybillCode, @RequestParam(required = false) Long tallyId){
        return AjaxResult.success(pickUpOutService.pickUpInfo(waybillCode,tallyId));
    }

    /**
     * app交付
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUpOut:deliver')")
    @PostMapping("/deliver")
    @ApiOperation(value = "app交付")
    public AjaxResult deliver(@RequestBody AllPickUpOut out){
        return toAjax(pickUpOutService.deliver(out));
    }

    /**
     * app快速交付
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUpOut:rapidDeliver')")
    @PostMapping("/rapidDeliver")
    @ApiOperation(value = "app快速交付")
    public AjaxResult rapidDeliver(@RequestBody AppPickUpQuery query){
        return AjaxResult.success(pickUpOutService.rapidDeliver(query));
    }

    /**
     * app新增快速交付运单号查询
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUpOut:waybillCodeQuery')")
    @GetMapping("/waybillCodeQuery/{waybillCode}")
    @ApiOperation(value = "app新增快速交付运单号查询")
    public AjaxResult waybillCodeQuery(@PathVariable("waybillCode") String waybillCode){
        return AjaxResult.success(pickUpOutService.waybillCodeQuery(waybillCode));
    }

    /**
     * 根据后四位或者后八位运单号查询
     */
    @GetMapping("/getWaybillCode/{code}")
    @ApiOperation(value = "根据八位运单号查询运单")
    public AjaxResult getEightCode(@PathVariable String code)
    {
        return AjaxResult.success(pickUpOutService.checkWaybillCode(code));
    }

    /**
     * 批量提货出库
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUpOut:batchPickUpOut')")
    @GetMapping("/batchPickUpOut/{pickUpId}")
    @ApiOperation(value = "批量提货出库")
    public AjaxResult batchPickUpOut(@PathVariable("pickUpId") Long pickUpId){
        return AjaxResult.success(pickUpOutService.batchPickUpOut(pickUpId));
    }

}
