package com.gzairports.hz.business.departure.service;

import com.gzairports.hz.business.departure.domain.EasyBill;
import com.gzairports.hz.business.departure.domain.query.EasyBillInfoQuery;
import com.gzairports.hz.business.departure.domain.query.EasyBillQuery;
import com.gzairports.hz.business.departure.domain.vo.EasyBillVo;

import java.util.List;

/**
 * 简易开单Service接口
 *
 * <AUTHOR>
 * @date 2024-06-20
 */
public interface IEasyBillService {

    /**
     * 简易开单列表查询
     * @param query 查询参数
     * @return 简易开单列表
     */
    List<EasyBillVo> selectEasyBillList(EasyBillQuery query);

    /**
     * 新增简易开单
     * @param bill 简易开单数据
     * @return 结果
     */
    int insertEasyBill(EasyBill bill);

    /**
     * 根据运单号以及单证控制校验运单号
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    String check(String waybillCode);

    /**
     * 修改简易开单
     * @param bill 简易开单数据
     * @return 结果
     */
    int editEasyBill(EasyBill bill);

    /**
     * 查看详情
     * @param query 查询条件
     * @return 结果
     */
    EasyBill getInfo(EasyBillInfoQuery query);

    /**
     * 删除简易开单
     * @param query 查询条件
     * @return 结果
     */
    int delEasyBill(EasyBillInfoQuery query);

    /**
     * 运单作废
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    int invalid(String waybillCode);
}
