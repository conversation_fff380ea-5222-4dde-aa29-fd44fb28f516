package com.gzairports.common.basedata.mapper;

import com.gzairports.common.basedata.domain.BaseCargoIncompatible;
import com.gzairports.common.basedata.domain.query.CargoIncompatibleQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 货物不兼容性Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Mapper
public interface CargoIncompatibleMapper {

    /**
     * 查询货物不兼容性列表
     *
     * @param query 查询参数
     * @return 货物不兼容性列表
     */
    List<BaseCargoIncompatible> selectCargoIncompatibleList(CargoIncompatibleQuery query);

    /**
     * 根据航空公司 特货代码1 特货代码2查询货物不兼容性
     *
     * @param airCompany 航空公司
     * @param specialCargoOne 特货代码1
     * @param specialCargoTwo 特货代码2
     * @return 结果
     */
    BaseCargoIncompatible selectIncompatibleByCode(@Param("airCompany") String airCompany,
                                                   @Param("specialCargoOne") String specialCargoOne,
                                                   @Param("specialCargoTwo") String specialCargoTwo);

    /**
     * 新增货物不兼容性
     *
     * @param incompatible 货物不兼容性
     * @return 结果
     */
    int insertCargoIncompatible(BaseCargoIncompatible incompatible);

    /**
     * 修改货物不兼容性
     *
     * @param incompatible 货物不兼容性
     * @return 结果
     */
    int updateCargoIncompatible(BaseCargoIncompatible incompatible);

    /**
     * 删除货物不兼容性
     *
     * @param ids 货物不兼容性id集合
     * @return 结果
     */
    int delCargoIncompatible(Long[] ids);
}
