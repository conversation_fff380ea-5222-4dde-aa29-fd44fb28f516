package com.gzairports.common.basedata.service;

import com.gzairports.common.basedata.domain.BaseCargoDanger;
import com.gzairports.common.basedata.domain.query.CargoDangerQuery;

import java.util.List;

/**
 * 危险品管理Service接口
 *
 * <AUTHOR>
 * @date 2024-06-14
 */
public interface ICargoDangerService {

    /**
     * 查询危险品管理列表
     *
     * @param query 查询参数
     * @return 危险品管理列表
     */
    List<BaseCargoDanger> selectDangerList(CargoDangerQuery query);

    /**
     * 新增危险品管理
     *
     * @param danger 危险品管理
     * @return 结果
     */
    int addDanger(BaseCargoDanger danger);

    /**
     * 修改危险品管理
     *
     * @param danger 危险品管理
     * @return 结果
     */
    int editDanger(BaseCargoDanger danger);

    /**
     * 删除危险品管理
     *
     * @param ids 危险品管理id集合
     * @return 结果
     */
    int delDanger(Long[] ids);
}
