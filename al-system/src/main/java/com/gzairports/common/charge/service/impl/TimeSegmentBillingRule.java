package com.gzairports.common.charge.service.impl;

import com.gzairports.common.business.arrival.domain.HzArrItem;
import com.gzairports.common.business.arrival.domain.ItemDetail;
import com.gzairports.common.charge.domain.HzChargeItemRule;
import com.gzairports.common.charge.domain.vo.BillRuleVo;
import com.gzairports.common.charge.service.BillingRule;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 分时段计费规则
 * <AUTHOR>
 * @date 2024-08-03
 */
public class TimeSegmentBillingRule implements BillingRule {
    @Override
    public String className() {
        return "TimeSegmentBillingRule.class";
    }

    @Override
    public BillRuleVo calculateFee(List<HzChargeItemRule> rule, BigDecimal weight, Integer quantity, ItemDetail item) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        BigDecimal totalCost = new BigDecimal(0);
        BillRuleVo vo = new BillRuleVo();
        if (item.getStartTime() == null || item.getEndTime() == null){
            return vo;
        }
        for (HzChargeItemRule itemRule : rule) {
            BigDecimal decimal = itemRule.getMinimum() == null ? new BigDecimal(0) : itemRule.getMinimum();
            String[] split = itemRule.getTimeSlot().split(",");
            LocalTime ruleStartTime = LocalTime.parse(split[0], formatter);
            LocalTime ruleEndTime = LocalTime.parse(split[1], formatter);
            // 判断时间段是否重叠
            if (isTimeRangeOverlap(item.getStartTime(), item.getEndTime(), ruleStartTime, ruleEndTime)) {
                BigDecimal price = weight.multiply(itemRule.getUnitPrice());
                vo.setRate(itemRule.getUnitPrice());
                BigDecimal cost = price.max(decimal);
                if (itemRule.getMaximum() != null){
                    cost = cost.min(itemRule.getMaximum());
                }
                totalCost = totalCost.add(cost);
            }
        }
        vo.setTotalCharge(totalCost);
        vo.setQuantity(weight.toString());
        return vo;
    }

    private boolean isTimeRangeOverlap(LocalTime startTime, LocalTime endTime, LocalTime ruleStartTime, LocalTime ruleEndTime) {
        // 非跨午夜的时间段
        if (ruleStartTime.isBefore(ruleEndTime)) {
            return !(startTime.isAfter(ruleEndTime) || endTime.isBefore(ruleStartTime));
        } else {
            // 跨午夜的时间段
            return !(startTime.isAfter(ruleEndTime) && endTime.isBefore(ruleStartTime));
        }
    }

}
