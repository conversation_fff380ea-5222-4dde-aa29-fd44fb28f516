package com.gzairports.web.controller.business.arrival;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.basedata.domain.BaseAgent;
import com.gzairports.common.basedata.mapper.BaseAgentMapper;
import com.gzairports.common.business.arrival.domain.ApplyEdit;
import com.gzairports.common.business.arrival.domain.query.*;
import com.gzairports.common.business.arrival.domain.vo.*;
import com.gzairports.common.business.arrival.service.IPickUpService;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.ApiOperation;
import oracle.jdbc.proxy.annotation.Post;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 进港提货办单操作Controller
 *
 * <AUTHOR>
 * @date 2024-06-03
 */
@RestController
@RequestMapping("/arr/pickUp")
public class PickUpController extends BaseController {

    @Autowired
    private IPickUpService pickUpService;

    @Autowired
    private BaseAgentMapper baseAgentMapper;

    /**
     * 挑单
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUp:one')")
    @PostMapping("/one")
    @ApiOperation(value = "挑单")
    public AjaxResult one(@RequestBody PickUpClickQuery query){
        Long deptId = SecurityUtils.getHighParentId();
        query.setDeptId(deptId);
        String deptIds = null;
        BaseAgent baseAgent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", deptId));
        if(baseAgent != null){
            if(StringUtils.isNotEmpty(baseAgent.getDeptIds())){
                deptIds = baseAgent.getDeptIds();
            }
        }
        query.setType("wl");
        return AjaxResult.success(pickUpService.one(query,deptIds));
    }

    /**
     * 费用明细
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUp:cost')")
    @GetMapping("/cost")
    @ApiOperation(value = "费用明细")
    public AjaxResult cost(@NotNull @RequestParam String waybillCode, @NotNull @RequestParam Long tallyId)
    {
        return AjaxResult.success(pickUpService.cost(waybillCode,tallyId));
    }

    /**
     * 费用明细详情
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUp:costInfo')")
    @GetMapping("/costInfo/{id}")
    @ApiOperation(value = "费用明细详情")
    public AjaxResult costInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(pickUpService.costInfo(id));
    }

    /**
     * 保存
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUp:add')")
    @PostMapping("/add")
    @ApiOperation(value = "保存")
    public AjaxResult add(@RequestBody PickOrderVo vo)
    {
        vo.setDeptId(SecurityUtils.getHighParentId());
        vo.setType("wl");
        return AjaxResult.success(pickUpService.add(vo));
    }


    /**
     * 流水号查询
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUp:serial')")
    @PostMapping("/serial")
    @ApiOperation(value = "流水号查询")
    public AjaxResult serial(@RequestBody PickUpCodeQuery query){
        Long deptId = SecurityUtils.getHighParentId();
        query.setDeptId(deptId);
        return AjaxResult.success(pickUpService.serial(query));
    }

    /**
     * 批量挑单
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUp:batch')")
    @PostMapping("/batch")
    @ApiOperation(value = "批量挑单")
    public AjaxResult batch(@RequestBody PickUpQuery query){
        Long deptId = SecurityUtils.getHighParentId();
        query.setDeptId(deptId);
        String deptIds = null;
        BaseAgent baseAgent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", deptId));
        if(baseAgent != null){
            if(StringUtils.isNotEmpty(baseAgent.getDeptIds())){
                deptIds = baseAgent.getDeptIds();
            }
        }
        return AjaxResult.success(pickUpService.batch(query,deptIds));
    }

    /**
     * 加入提货列表
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUp:pickList')")
    @PostMapping("/pickList")
    @ApiOperation(value = "加入提货列表")
    public AjaxResult pickList(@RequestBody PickUpListVo vo){
        vo.setType("wl");
        return AjaxResult.success(pickUpService.pickList(vo));
    }


    /**
     * 查询已提货办单数据
     */
//    @PreAuthorize("@ss.hasPermi('arr:pickUp:pickedUp')")
    @PostMapping("/pickedUp")
    @ApiOperation(value = "查询已提货办单数据")
    public AjaxResult pickedUp(@RequestBody PickedUpQuery query){
        query.setDeptId(SecurityUtils.getHighParentId());
        PickedOrderVo pickedOrderVo = pickUpService.selectByQuery(query);
        return AjaxResult.success(pickedOrderVo);
    }

    /**
     * 导出已提货办单数据
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUp:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出已提货办单数据")
    public void export(HttpServletResponse response, PickedUpQuery query){
        query.setDeptId(SecurityUtils.getHighParentId());
        List<PickedUpVo> list = pickUpService.exportList(query);
        ExcelUtil<PickedUpVo> excelUtil = new ExcelUtil<>(PickedUpVo.class);
        excelUtil.exportExcel(response,list,"已提货办单数据");
    }


    /**
     * 物流导出已提货办单运单数据
     * 物流办理提货-已提货办单标签增加于丹导出导出功能，将已提货物每条主单号对应的详细信息，比如:流水号，主单号，件数，重量，货物仓存费，搬运费，进港处置费 字段导出
     * */
    @PostMapping("/exportWaybill")
    @ApiOperation(value = "导出已提货办单运单数据")
    public void exportWaybill(HttpServletResponse response, PickedUpQuery query){
        List<PickedUpWaybillVo> list = pickUpService.exportWaybillList(query);
        ExcelUtil<PickedUpWaybillVo> excelUtil = new ExcelUtil<>(PickedUpWaybillVo.class);
        excelUtil.exportExcel(response,list,"已提货办单数据");
    }

    /**
     * 查询未提货办单数据
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUp:notPickedUp')")
    @PostMapping("/notPickedUp")
    @ApiOperation(value = "查询未提货办单数据")
    public AjaxResult notPickedUp(@RequestBody PickedUpQuery query){
        Long deptId = SecurityUtils.getHighParentId();
        query.setDeptId(deptId);
        String deptIds = null;
        BaseAgent baseAgent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", deptId));
        if(baseAgent != null){
            if(StringUtils.isNotEmpty(baseAgent.getDeptIds())){
                deptIds = baseAgent.getDeptIds();
            }
        }
        return AjaxResult.success(pickUpService.notPickedUp(query,deptIds));
    }

    /**
     * 申请修改
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUp:applyEdit')")
    @PostMapping("/applyEdit")
    @ApiOperation(value = "申请修改")
    public AjaxResult applyEdit(@RequestBody ApplyEdit applyEdit){
        applyEdit.setDeptId(SecurityUtils.getHighParentId());
        return toAjax(pickUpService.applyEdit(applyEdit));
    }

    /**
     * 查询申请修改列表
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUp:applyList')")
    @PostMapping("/applyList")
    @ApiOperation(value = "查询申请修改列表")
    public AjaxResult applyList(@RequestBody ApplyEditQuery query){
        return AjaxResult.success(pickUpService.selectApplyList(query));
    }

    /**
     * 结算
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUp:settle')")
    @GetMapping("/settle/{waybillCode}")
    @ApiOperation(value = "结算")
    public AjaxResult settle(@PathVariable("waybillCode") String waybillCode)
    {
        return AjaxResult.success(pickUpService.settle(waybillCode, "WL"));
    }

    /**
     * 余额支付
     */
    @PreAuthorize("@ss.hasPermi('arr:pickUp:balance')")
    @PostMapping("/balance")
    @ApiOperation(value = "余额支付")
    public AjaxResult balance(@RequestBody PickOrderVo vo)
    {
        vo.setDeptId(SecurityUtils.getHighParentId());
        vo.setType("wl");
        return AjaxResult.success(pickUpService.balance(vo));
    }

    /**
     *
     * 生成提货码
     */
    @GetMapping("/getPickUpCode/{pickUpId}")
    @ApiOperation(value = "生成提货码")
    public AjaxResult printOutOrder(@PathVariable("pickUpId") Long pickUpId){
        return AjaxResult.success(pickUpService.printOutOrder(pickUpId));
    }

    /**
     * 打印出库单
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:printOut')")
    @GetMapping("/printOut/{pickUpId}")
    @ApiOperation(value = "打印出库单")
    public AjaxResult printOut(@PathVariable("pickUpId") Long pickUpId){
        return AjaxResult.success(pickUpService.printOutOrder(pickUpId));
    }
}
