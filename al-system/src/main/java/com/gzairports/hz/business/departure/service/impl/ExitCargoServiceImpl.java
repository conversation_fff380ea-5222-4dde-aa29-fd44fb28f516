package com.gzairports.hz.business.departure.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.business.arrival.domain.FlightLoadUldWaybill;
import com.gzairports.common.business.arrival.domain.FlightLoadWaybill;
import com.gzairports.common.business.arrival.mapper.FlightInfoMapper;
import com.gzairports.common.business.arrival.mapper.FlightLoadUldWaybillMapper;
import com.gzairports.common.business.arrival.mapper.FlightLoadWaybillMapper;
import com.gzairports.common.business.departure.domain.FlightInfo;
import com.gzairports.common.business.departure.domain.WaybillTrace;
import com.gzairports.common.business.departure.domain.vo.LoadInfoVo;
import com.gzairports.common.business.departure.service.impl.WaybillTraceServiceImpl;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.hz.business.departure.domain.*;
import com.gzairports.hz.business.departure.domain.query.ExitCargoQuery;
import com.gzairports.hz.business.departure.domain.vo.ExitCargoVo;
import com.gzairports.hz.business.departure.mapper.*;
import com.gzairports.hz.business.departure.service.IExitCargoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 退货管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@Service
public class ExitCargoServiceImpl extends ServiceImpl<ExitCargoMapper,HzDepExitCargo> implements IExitCargoService {

    @Autowired
    private ExitCargoMapper exitCargoMapper;

    @Autowired
    private AllAirWaybillMapper airWaybillMapper;

    @Autowired
    private FlightInfoMapper flightInfoMapper;

    @Autowired
    private HzCollectWaybillMapper collectWaybillMapper;

    @Autowired
    private FlightLoadWaybillMapper loadWaybillMapper;

    @Autowired
    private FlightLoadUldWaybillMapper loadUldWaybillMapper;

    @Autowired
    private WaybillTraceServiceImpl traceService;

    /**
     * 查询退货管理列表
     * @param query 查询参数
     * @return 退货列表
     */
    @Override
    public List<ExitCargoVo> selectExitCargoList(ExitCargoQuery query) {
        List<ExitCargoVo> list = exitCargoMapper.selectListByQuery(query);
        for (ExitCargoVo exitCargoVo : list) {
            selectQuantityAndWeight(exitCargoVo);
            int depQuantity = exitCargoVo.getDepQuantity() == null ? 0 : exitCargoVo.getDepQuantity();
            BigDecimal depWeight = exitCargoVo.getDepWeight() == null ? new BigDecimal(0) : exitCargoVo.getDepWeight();
            //这里更改一下 如果从库里面查询的数据为空,则计算出来;不为空就直接展示库里面的数据
            if(StringUtils.isNull(exitCargoVo.getExitQuantity()) || StringUtils.isNull(exitCargoVo.getExitWeight())){
                if (exitCargoVo.getCollectQuantity() != null && exitCargoVo.getCollectWeight() != null){
                    exitCargoVo.setExitQuantity(exitCargoVo.getCollectQuantity() - depQuantity);
                    exitCargoVo.setExitWeight(exitCargoVo.getCollectWeight().subtract(depWeight));
                }
            }
        }
        return list;
    }


    /**
     * 新增退货数据 由于前端是把新增闭掉了的 所以在另一个同步新增的地方做运单日志的新增
     * @param cargo 退货数据
     * @return 结果
     */
    @Override
    public int insertExitCargo(HzDepExitCargo cargo) {
        cargo.setStartTime(new Date());
        cargo.setStatus("apply_return");
        return exitCargoMapper.insert(cargo);
    }

    /**
     * 修改退货数据
     * @param cargo 退货数据
     * @return 结果
     */
    @Override
    public int editExitCargo(HzDepExitCargo cargo) {
        HzDepExitCargo exitCargo = exitCargoMapper.selectById(cargo.getId());
        if (!exitCargo.getExitQuantity().equals(cargo.getExitQuantity()) && cargo.getRemark() == null){
            throw new CustomException("修改了件数和重量必须在备注说明");
        }
        if (exitCargo.getExitWeight().compareTo(cargo.getExitWeight()) != 0 && cargo.getRemark() == null){
            throw new CustomException("修改了件数和重量必须在备注说明");
        }
        cargo.setOperName(SecurityUtils.getUsername());
        if ("been_return".equals(cargo.getStatus())){
            cargo.setExitStoreTime(new Date());
            cargo.setStatus("been_return");

            AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>().eq("waybill_code", cargo.getWaybillCode()));
            airWaybill.setStatus("been_return");
            airWaybill.setUpdateTime(new Date());
            airWaybillMapper.updateById(airWaybill);

            WaybillTrace waybillTrace = new WaybillTrace();
            waybillTrace.setOperTime(new Date());
            waybillTrace.setOperPieces(cargo.getExitQuantity());
            waybillTrace.setOperWeight(cargo.getExitWeight());
            waybillTrace.setWaybillCode(cargo.getWaybillCode());
            waybillTrace.setNodeName("已退货");
            traceService.insertWaybillTrace(waybillTrace);
        }
        return exitCargoMapper.updateById(cargo);
    }

    /**
     * 查看退货数据详情
     * @param id 退货id
     * @return 结果
     */
    @Override
    public ExitCargoVo getInfo(Long id) {
        ExitCargoVo exitCargoVo = exitCargoMapper.selectByExitId(id);
        selectQuantityAndWeight(exitCargoVo);
        return exitCargoVo;
    }

    /**
     * 根据运单号查询详情
     * @param waybillCode 运单号
     * @return 运单详情
     */
    @Override
    public ExitCargoVo getWaybill(String waybillCode) {
        AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                .eq("waybill_code", waybillCode)
                .eq("is_del", 0)
                .eq("type", "DEP"));
        ExitCargoVo vo = new ExitCargoVo();
        vo.setWriteTime(airWaybill.getWriteTime());
        vo.setWaybillStatus(airWaybill.getStatus());
        vo.setAgentCode(airWaybill.getAgentCode());
        vo.setWaybillId(airWaybill.getId());
        vo.setFlightNo1(airWaybill.getFlightNo1());
        vo.setExecDate(airWaybill.getFlightDate1());
        selectQuantityAndWeight(vo);
        return vo;
    }

    /**
     * 查询件数和重量
     * @param exitCargoVo 参数
     */
    private void selectQuantityAndWeight(ExitCargoVo exitCargoVo) {
        List<HzCollectWaybill> collectWaybills = collectWaybillMapper.selectList(new QueryWrapper<HzCollectWaybill>().eq("waybill_id", exitCargoVo.getWaybillId()));
        int collectQuantity = 0;
        BigDecimal collectWeight = new BigDecimal(0);
        if (!CollectionUtils.isEmpty(collectWaybills)) {
            collectQuantity = collectWaybills.stream().mapToInt(HzCollectWaybill::getQuantity).sum();
            collectWeight = collectWaybills.stream().map(HzCollectWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        List<LoadInfoVo> infoVos = loadWaybillMapper.selectByLoadInfo(exitCargoVo.getWaybillId());
        int depQuantity = 0;
        BigDecimal depWeight = new BigDecimal(0);
        for (LoadInfoVo infoVo : infoVos) {
            FlightInfo info = flightInfoMapper.selectFlightNo(infoVo.getLoadId());
            if (info.getStartRealTakeoffTime() != null){
                depQuantity+=infoVo.getQuantity();
                depWeight = depWeight.add(infoVo.getWeight());
            }
        }
        exitCargoVo.setCollectQuantity(collectQuantity);
        exitCargoVo.setCollectWeight(collectWeight);
        exitCargoVo.setDepQuantity(depQuantity);
        exitCargoVo.setDepWeight(depWeight);
    }
}
