package com.gzairports.common.utils.poi;

import com.gzairports.common.utils.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 统计报表Excel相关处理
 *
 * <AUTHOR>
 * @date 2025-02-21
 */
public class ReportExcelUtil {

    private static final Logger log = LoggerFactory.getLogger(ReportExcelUtil.class);

    /**
     * Excel sheet最大行数，默认65536
     */
    public static final int sheetSize = 65536;

    /**
     * 工作表名称
     */
    private String sheetName;


    /**
     * 工作薄对象
     */
    private Workbook wb;


    /**
     * 工作表对象
     */
    private Sheet sheet;

    /**
     * 样式列表
     */
    private Map<String, CellStyle> styles;

    /**
     * 当前行号
     */
    private int rownum;

    /**
     * 标题
     */
    private String title;

    private List<String> fieldList;

    /**
     * 导入导出数据列表
     */
    private List<Map<String,Object>> list;


    public void exportReportExcel(HttpServletResponse response, List<Map<String,Object>> list, List<String> fieldList, String sheetName,String title, String printBy, String startTime)
    {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        this.reportInit(list, fieldList, sheetName, title, printBy, startTime);
        exportExcel(response);
    }


    public void reportInit(List<Map<String,Object>> list, List<String> fieldList, String sheetName, String title, String printBy, String startTime)
    {
        if (list == null)
        {
            list = new ArrayList<Map<String,Object>>();
        }
        this.list = list;
        this.fieldList = fieldList;
        this.sheetName = sheetName;
        this.title = title;
        createWorkbook();
        createTitle();
        createTwoTitle(printBy,startTime);
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @return 结果
     */
    public void exportExcel(HttpServletResponse response)
    {
        try
        {
            writeSheet();
            wb.write(response.getOutputStream());
        }
        catch (Exception e)
        {
            log.error("导出Excel异常{}", e.getMessage());
        }
        finally
        {
            IOUtils.closeQuietly(wb);
        }
    }

    /**
     * 创建写入数据到Sheet
     */
    public void writeSheet()
    {
        // 取出一共有多少个sheet.
        int sheetNo = Math.max(1, (int) Math.ceil(list.size() * 1.0 / sheetSize));
        for (int index = 0; index < sheetNo; index++) {
            createSheet(sheetNo, index);
            // 产生一行
            Row row = sheet.createRow(rownum);
            int column = 0;
            // 写入各个字段的列头名称
            for (String s : fieldList) {
                this.createHeadCell(s, row, column++);
            }
            fillExcelData(index, row, fieldList);
        }
    }

    /**
     * 创建工作表
     *
     * @param sheetNo sheet数量
     * @param index 序号
     */
    public void createSheet(int sheetNo, int index)
    {
        // 设置工作表的名称.
        if (sheetNo > 1 && index > 0)
        {
            this.sheet = wb.createSheet();
            this.createTitle();
            wb.setSheetName(index, sheetName + index);
        }
    }

    /**
     * 创建excel第一行标题
     */
    public void createTitle()
    {
        if (StringUtils.isNotEmpty(title))
        {
            int titleLastCol = this.list.size() - 1;
            Row titleRow = sheet.createRow(rownum == 0 ? rownum++ : 0);
            titleRow.setHeightInPoints(30);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellStyle(styles.get("title"));
            titleCell.setCellValue(title);
            sheet.addMergedRegion(new CellRangeAddress(titleRow.getRowNum(), titleRow.getRowNum(), titleRow.getRowNum(), titleLastCol));
        }
    }

    /**
     * 创建单元格
     */
    public Cell createHeadCell(String fieldName, Row row, int column)
    {
        // 创建列
        Cell cell = row.createCell(column);
        // 写入列信息
        cell.setCellValue(fieldName);
        sheet.setColumnWidth(column, (int) ((16 + 0.72) * 256));
        cell.setCellStyle(styles.get(StringUtils.format("header_{}_{}", IndexedColors.WHITE, IndexedColors.GREY_50_PERCENT)));
        return cell;
    }


    /**
     * 创建一个工作簿
     */
    public void createWorkbook()
    {
        this.wb = new SXSSFWorkbook(500);
        this.sheet = wb.createSheet();
        wb.setSheetName(0, sheetName);
        this.styles = createStyles(wb);
    }


    /**
     * 创建excel第二行打印人信息
     */
    public void createTwoTitle (String printedBy, String startTime)
    {
        if (StringUtils.isNotEmpty(printedBy)) {
            int printLastCol = this.list.size() - 1;
            Row printRow = sheet.createRow(rownum++);
            printRow.setHeightInPoints(20);
            Cell printCell = printRow.createCell(0);
            printCell.setCellStyle(styles.get("two"));
            printCell.setCellValue("制单人: " + printedBy + "  统计时间：" + startTime);
            sheet.addMergedRegion(new CellRangeAddress(printRow.getRowNum(), printRow.getRowNum(), 0, printLastCol));
        }
    }

    /**
     * 创建表格样式
     *
     * @param wb 工作薄对象
     * @return 样式列表
     */
    private static Map<String, CellStyle> createStyles(Workbook wb)
    {
        // 写入各条记录,每条记录对应excel表中的一行
        Map<String, CellStyle> styles = new HashMap<>();
        CellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font titleFont = wb.createFont();
        titleFont.setFontName("Arial");
        titleFont.setFontHeightInPoints((short) 16);
        titleFont.setBold(true);
        style.setFont(titleFont);
        styles.put("title", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        Font twoByFont = wb.createFont();
        twoByFont.setFontHeightInPoints((short) 12);
        style.setFont(twoByFont);
        styles.put("two", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        Font dataFont = wb.createFont();
        dataFont.setFontName("Arial");
        dataFont.setFontHeightInPoints((short) 10);
        style.setFont(dataFont);
        styles.put("data", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font totalFont = wb.createFont();
        totalFont.setFontName("Arial");
        totalFont.setFontHeightInPoints((short) 10);
        style.setFont(totalFont);
        styles.put("total", style);
        return styles;
    }

    /**
     * 填充excel数据
     *
     * @param index 序号
     * @param row 单元格行
     */
    @SuppressWarnings("unchecked")
    public void fillExcelData(int index, Row row, List<String> nameList)
    {
        int startNo = index * sheetSize;
        int endNo = Math.min(startNo + sheetSize, list.size());
        int rowNo;
        for (int i = startNo; i < endNo; i++)
        {
            rowNo = i + 1 + rownum - startNo;
            row = sheet.createRow(rowNo);
            int column = 0;
            for (String s : nameList) {
                this.addCell(s, row,column++);
            }
        }
    }

    /**
     * 添加单元格
     */
    public Cell addCell(String s, Row row, int column)
    {
        Cell cell = null;
        try
        {
            // 设置行高
            row.setHeight((short) (14 * 20));
            for (Map<String, Object> objectMap : list) {
                // 创建cell
                cell = row.createCell(column);
                cell.setCellStyle(styles.get(StringUtils.format("data_{}_{}_{}", HorizontalAlignment.CENTER, IndexedColors.BLACK, IndexedColors.WHITE)));
                Object cellValue = objectMap.get(s);
                if (cellValue != null) {
                    if (cellValue instanceof String) {
                        cell.setCellValue((String) cellValue);
                    } else if (cellValue instanceof Integer) {
                        cell.setCellValue((Integer) cellValue);
                    } else if (cellValue instanceof Double) {
                        cell.setCellValue((Double) cellValue);
                    } else if (cellValue instanceof Date) {
                        CellStyle dateCellStyle = wb.createCellStyle();
                        CreationHelper createHelper = wb.getCreationHelper();
                        dateCellStyle.setDataFormat(createHelper.createDataFormat().getFormat("yyyy-MM-dd"));
                        cell.setCellStyle(dateCellStyle);
                        cell.setCellValue((Date) cellValue);
                    } else {
                        cell.setCellValue(cellValue.toString());
                    }
                }
            }
        }
        catch (Exception e)
        {
            log.error("导出Excel失败{}", e);
        }
        return cell;
    }


    /**
     * 主装统计报表Workbook
     *
     * @param filteredData    数据
     * @param fieldNameCnList 表头信息
     * @param reportTitle     报表标题
     * @return Workbook
     * @throws IOException e
     */
    public static void exportToExcel(OutputStream outputStream, List<Map<String, Object>> filteredData, List<String> fieldNameCnList, String reportTitle, String reportMsg) throws IOException {
        // 创建一个新的工作簿
        Workbook workbook = new XSSFWorkbook();
        Map<String, CellStyle> styles = createStyles(workbook);

        // 创建一个工作表
        Sheet sheet = workbook.createSheet(reportTitle);

        //行索引
        int rowIdx = 0;

        if (StringUtils.isNotEmpty(reportTitle)) {
            int titleLastCol = fieldNameCnList.size() - 1;
            Row titleRow = sheet.createRow(rowIdx++);
            titleRow.setHeightInPoints(30);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellStyle(styles.get("title"));
            titleCell.setCellValue(reportTitle);
            sheet.addMergedRegion(new CellRangeAddress(titleRow.getRowNum(), titleRow.getRowNum(), titleRow.getRowNum(), titleLastCol));
        }

        //第二行的用户信息
        Row printRow = sheet.createRow(rowIdx++);
        printRow.setHeightInPoints(20);
        Cell printCell = printRow.createCell(0);
        printCell.setCellStyle(styles.get("two"));
        printCell.setCellValue(reportMsg);
        sheet.addMergedRegion(new CellRangeAddress(printRow.getRowNum(), printRow.getRowNum(), 0, fieldNameCnList.size() - 1));

        // 创建表头行
        Row headerRow = sheet.createRow(rowIdx++);
        for (int i = 0; i < fieldNameCnList.size(); i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(fieldNameCnList.get(i));
        }

        int size = filteredData.size();
        for (int i = 0; i < size; i++) {
            Row row = sheet.createRow(i + rowIdx);
            int colNum = 0;
            //序号
            Cell cellIndex = row.createCell(colNum++);
            cellIndex.setCellValue(i + 1);
            for (Map.Entry<String, Object> entry : filteredData.get(i).entrySet()) {
                Cell cell = row.createCell(colNum++);
                Object value = entry.getValue();
                if (value instanceof String) {
                    cell.setCellValue((String) value);
                } else if (value instanceof Number) {
                    cell.setCellValue((Double) value);
                } else if (value instanceof LocalDateTime) {
                    cell.setCellValue((LocalDateTime) value);
                } else if (value instanceof LocalDate) {
                    cell.setCellValue((LocalDate) value);
                } else if (value instanceof Date) {
                    cell.setCellValue((Date) value);
                }
            }
        }

        //处理最后一行的合计数据
        Map<String, Object> lastMapObj = size > 1 ? filteredData.get(size - 1) : new HashMap<>();
        if (lastMapObj.containsKey("lastCount")) {
            int lastRowNum = sheet.getLastRowNum();
            //移除原来的row
            sheet.removeRow(sheet.getRow(lastRowNum));

            Row lastRow = sheet.createRow(lastRowNum);
            String lastTotalData = lastMapObj.values()
                    .stream().map(Object::toString)
                    .collect(Collectors.joining("   "));
            Cell cell = lastRow.createCell(0);
            cell.setCellValue(lastTotalData);
            cell.setCellStyle(styles.get("two"));
            sheet.addMergedRegion(new CellRangeAddress(lastRowNum, lastRowNum, 0, fieldNameCnList.size() - 1));
        }

        for (int i = 0; i < fieldNameCnList.size(); i++) {
            // 自动调整列宽
//            sheet.autoSizeColumn(i);
            // 设置列宽
            sheet.setColumnWidth(i, (int) ((20 + 0.72) * 256));
        }
//        return workbook;
        // 将工作簿写入响应输出流
        try (OutputStream _outputStream = outputStream) {
            workbook.write(_outputStream);
        } catch (IOException e) {
            throw new IOException("导出Excel失败", e);
        } finally {
            // 关闭工作簿
            workbook.close();
        }
    }


    public void exportToHzExcel(HttpServletResponse response, List<Map<String, Object>> filteredData, List<String> fieldNameCnList, String reportTitle, List<String> mergeFields) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=Workbook.xlsx");

        // 创建工作簿和工作表
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet(reportTitle);

        // 1. 定义标题样式
        CellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 16);
        titleFont.setFontName("Arial");
        titleFont.setBold(true);
        titleStyle.setFont(titleFont);
        // 行索引
        int rowIdx = 0;
        if (StringUtils.isNotEmpty(reportTitle)) {
            int titleLastCol = fieldNameCnList.size() - 1;
            Row titleRow = sheet.createRow(rowIdx++);
            titleRow.setHeightInPoints(30);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellStyle(titleStyle);
            titleCell.setCellValue(reportTitle);
            sheet.addMergedRegion(new CellRangeAddress(0, 0,0, titleLastCol));
        }

        // 2. 创建表头行（原有代码保留）
        Row headerRow = sheet.createRow(rowIdx++);
        for (int i = 0; i < fieldNameCnList.size(); i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(fieldNameCnList.get(i));
        }

        // 3. 填充数据行（原有代码保留）
        for (int i = 0; i < filteredData.size(); i++) {
            Row row = sheet.createRow(i + rowIdx);
            int colNum = 0;
            for (Map.Entry<String, Object> entry : filteredData.get(i).entrySet()) {
                Cell cell = row.createCell(colNum++);
                Object value = entry.getValue();
                if (value instanceof String) {
                    cell.setCellValue((String) value);
                } else if (value instanceof Number) {
                    cell.setCellValue(((Number) value).doubleValue());
                } else if (value instanceof LocalDateTime) {
                    cell.setCellValue((LocalDateTime) value);
                } else if (value instanceof LocalDate) {
                    cell.setCellValue((LocalDate) value);
                } else if (value instanceof Date) {
                    cell.setCellValue((Date) value);
                }
            }
        }

        if (mergeFields != null && !mergeFields.isEmpty()) {
            for (String mergeField : mergeFields) {
                int colIndex = fieldNameCnList.indexOf(mergeField);
                if (colIndex == -1) continue;

                int startRow = rowIdx;
                Object currentValue = null;

                for (int i = 0; i < filteredData.size(); i++) {
                    Map<String, Object> rowData = filteredData.get(i);
                    Object value = rowData.get(mergeField);
                    int currentRowNum = rowIdx + i;

                    // 新增：检查是否是小计行
                    boolean isSubtotalRow = "小计".equals(rowData.get("序号"));

                    if (!Objects.equals(value, currentValue) || isSubtotalRow) {
                        if (currentValue != null && !"小计".equals(currentValue) && (currentRowNum - startRow) >= 2) {
                            sheet.addMergedRegion(new CellRangeAddress(
                                    startRow,
                                    currentRowNum - 1,
                                    colIndex,
                                    colIndex
                            ));
                        }
                        // 重置起始行和当前值
                        if (isSubtotalRow) {
                            // 小计行不参与合并，直接重置状态
                            currentValue = null;
                            startRow = currentRowNum + 1;
                        } else {
                            currentValue = value;
                            startRow = currentRowNum;
                        }
                    }

                    // 处理最后一行
                    if (i == filteredData.size() - 1 && !isSubtotalRow && Objects.equals(value, currentValue)) {
                        if ((currentRowNum - startRow) >= 1){
                            sheet.addMergedRegion(new CellRangeAddress(
                                    startRow,
                                    currentRowNum,
                                    colIndex,
                                    colIndex
                            ));
                        }
                    }
                }
            }
        }
        for (int i = 0; i < fieldNameCnList.size(); i++) {
            sheet.autoSizeColumn(i);
            sheet.setColumnWidth(i, sheet.getColumnWidth(i) + 512);
        }
        try (OutputStream outputStream = response.getOutputStream()) {
            workbook.write(outputStream);
        } finally {
            workbook.close();
        }
    }
}
