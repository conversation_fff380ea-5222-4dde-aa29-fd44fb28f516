package com.gzairports.common.business.arrival.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 已提货办单运单数据
 *
 * <AUTHOR>
 * @date 2024-06-07
 */
@Data
public class PickedUpVo {

    /** 主键id */
    private Long id;

    /** 流水号 */
    @Excel(name = "流水号")
    private String serialNo;

    /** 航班号 */
    @Excel(name = "航班号")
    private String flightNo;

    /** 运单号 */
    @Excel(name = "运单号")
    private String waybillCode;

    /** 简写运单 */
    @Excel(name = "简写运单")
    private String shortWaybillCode;

    /** 收货人 */
    @Excel(name = "收货人")
    private String consignee;

    /** 运单数 */
    @Excel(name = "运单数")
    private Integer totalCount;

    /** 提货总件数 */
//    @Excel(name = "提货总件数")
    private Integer totalQuantity;

    /** 提货总重量 */
//    @Excel(name = "提货总重量")
    private BigDecimal totalWeight;

    /** 总费用 */
//    @Excel(name = "总费用")
    private BigDecimal totalCost;


    /** 提货件数 */
    @Excel(name = "提货件数")
    private Integer waybillQuantity;

    /** 提货重量 */
    @Excel(name = "提货重量")
    private BigDecimal waybillWeight;

    /** 费用 */
    @Excel(name = "费用")
    private BigDecimal waybillCost;

    /** 提货人 */
    @Excel(name = "提货人")
    private String customerName;

    /** 提货人证件号 */
    @Excel(name = "提货人证件号")
    private String customerIdNo;

    /** 办单经办人 */
    @Excel(name = "办单经办人")
    private String handleBy;

    /** 提货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "提货时间",dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date outTime;

    /** 办单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "办单时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date pickUpTime;

    /** 是否已支付 */
    private Integer isPay;

    /** 支付方式 */
    private Integer payStatus;
}
