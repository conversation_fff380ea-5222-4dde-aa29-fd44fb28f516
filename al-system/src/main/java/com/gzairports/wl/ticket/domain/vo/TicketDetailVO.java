package com.gzairports.wl.ticket.domain.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 单证详情返回VO数据
 *
 * <AUTHOR>
 * @date 2024-03-20
 */
@Data
public class TicketDetailVO {

    /** 主键id */
    private Long id;

    /** 部门id */
    private Long deptId;

    /** 票证来源 */
    private String billSource;

    /** 票证类型 */
    private String billType;

    /** 票证前缀 */
    private String billPrefix;

    /** 开始编号 */
    private Integer startNumber;

    /** 结束编号 */
    private Integer endNumber;

    /** 开始编号检查号 */
    private String startCode;

    /** 结束编号检查号 */
    private String endCode;

    /** 数量 */
    private Integer quantity;

    /** 已使用数量 */
    private Integer usedQuantity;

    /** 剩余数量 */
    private Integer remainQuantity;

    /** 是否：包舱单（包租整个货舱的运单） */
    private Integer isChartering;

    /** 所属单位 */
    private String department;

    /** 部门 */
    private String deptName;

    /** 记录人 */
    private String recorder;

    /** 记录时间 */
    private Date recordTime;

    /** 状态 */
    private Integer status;

    /** 使用明细列表 */
    private List<TicketGrantVO> numList;
}
