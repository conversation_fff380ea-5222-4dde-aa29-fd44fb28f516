package com.gzairports.common.basedata.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 客户对象 customer
 * 
 * <AUTHOR>
 * @date 2024-01-31
 */
@Data
@TableName("wl_custom")
public class Customer extends Model<Customer>
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private Long id;

    /** 公司名称 */
    @Excel(name = "公司名称")
    private String company;

    /** 客户名称 */
    @Excel(name = "客户名称")
    private String name;

    /** 客户简称 */
    @Excel(name = "客户简称")
    private String abbreviation;

    /** 身份证号码 */
    @Excel(name = "身份证号码")
    private String idCard;

    /** 客户电话 */
    @Excel(name = "客户电话")
    private String phone;

    /** 所在地区 */
    @Excel(name = "所在地区")
    private String location;

    /** 客户地址 */
    @Excel(name = "客户地址")
    private String address;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 是否删除 0 否 1 是 */
    private Integer isDel;

    /** 所属单位 */
    private Long deptId;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }
}
