package com.gzairports.wl.departure.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.wl.departure.domain.Bill;
import com.gzairports.wl.departure.domain.Hawb;
import com.gzairports.wl.departure.domain.query.BillQuery;
import com.gzairports.wl.departure.domain.vo.BillQueryVo;
import com.gzairports.wl.departure.domain.vo.BillVo;
import com.gzairports.wl.departure.domain.vo.HawbBillVo;
import com.gzairports.wl.departure.mapper.BillMapper;
import com.gzairports.wl.departure.mapper.HawbMapper;
import com.gzairports.wl.departure.service.IBillService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 国内分单账单管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@Service
public class BillServiceImpl extends ServiceImpl<BillMapper, Bill> implements IBillService {


    @Autowired
    private BillMapper billMapper;

    @Autowired
    private HawbMapper hawbMapper;

    /**
     * 查询国内分单账单
     * @param query 查询参数
     * @return 国内分单账单数据
     */
    @Override
    public BillQueryVo selectListByQuery(BillQuery query) {
        BillQueryVo vo = new BillQueryVo();
        query.setDeptId(SecurityUtils.getHighParentId());
        List<BillVo> billVo = billMapper.selectListByQuery(query);
        if (billVo == null){
            return vo;
        }
        vo.setVos(billVo);
        for (BillVo bill : billVo) {
            List<Hawb> list = hawbMapper.selectList(new QueryWrapper<Hawb>().eq("serial_no", bill.getSerialNo()).eq("is_del", 0));
            if (!CollectionUtils.isEmpty(list)){
                // 运单数
                bill.setTotalOrder(list.size());

                // 总金额
                BigDecimal totalMoney = list.stream().map(Hawb::getCostSum).reduce(BigDecimal.ZERO, BigDecimal::add);
                bill.setTotalMoney(totalMoney);

                // 总件数
                int totalQuantity = list.stream().mapToInt(Hawb::getQuantity).sum();
                bill.setTotalQuantity(totalQuantity);

                // 总重量
                BigDecimal totalWeight = list.stream().map(Hawb::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                bill.setTotalWeight(totalWeight);

                // 总计费重量
                BigDecimal totalChargeWeight = list.stream().map(Hawb::getChargeWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                bill.setTotalChargeWeight(totalChargeWeight);
            }
        }

        vo.setBillNum(billVo.size());
        // 总金额 (这里偶现空指针 就对空值或者0值进行过滤)
        BigDecimal reduce = billVo.stream()
                .filter(bill -> bill.getTotalMoney() != null && !bill.getTotalMoney().equals(BigDecimal.ZERO))
                .map(BillVo::getTotalMoney)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        vo.setTotalMoney(reduce);

        List<BillVo> unpaidList = billVo.stream().filter(e -> "UNPAID".equals(e.getPayStatus())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(unpaidList)){
            BigDecimal unpaid = unpaidList.stream().map(BillVo::getTotalMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setUnpaid(unpaid);
        }

        List<BillVo> paidList = billVo.stream().filter(e -> "PAID".equals(e.getPayStatus())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(paidList)){
            BigDecimal paid = paidList.stream().map(BillVo::getTotalMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setUnpaid(paid);
        }

        List<BillVo> refundedList = billVo.stream().filter(e -> "REFUNDED".equals(e.getPayStatus())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(refundedList)){
            BigDecimal refunded = refundedList.stream().map(BillVo::getTotalMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setUnpaid(refunded);
        }
        return vo;
    }

    /**
     * 国内分单账单详情
     * @param id 国内分单账单id
     * @return 详情
     */
    @Override
    public BillVo getInfo(Long id) {
        BillVo vo = billMapper.getInfo(id);
        if (vo == null){
            throw new CustomException("无当前账单信息");
        }
        switch (vo.getPayStatus()){
            case "UNPAID":
                vo.setPayStatus("未支付");
                break;
            case "PAID":
                vo.setPayStatus("已支付");
                break;
            case "REFUNDED":
                vo.setPayStatus("已退款");
                break;
            default:
                vo.setPayStatus("未知状态");
                break;
        }
        List<HawbBillVo> list = hawbMapper.seelectHawbList(vo.getSerialNo());
        if (!CollectionUtils.isEmpty(list)){
            vo.setBillVos(list);
            // 运单数
            vo.setTotalOrder(list.size());

            // 总金额
            BigDecimal totalMoney = list.stream().map(HawbBillVo::getCostSum).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setTotalMoney(totalMoney);

            // 总件数
            int totalQuantity = list.stream().mapToInt(HawbBillVo::getQuantity).sum();
            vo.setTotalQuantity(totalQuantity);

            // 总重量
            BigDecimal totalWeight = list.stream().map(HawbBillVo::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setTotalWeight(totalWeight);

            // 总计费重量
            BigDecimal totalChargeWeight = list.stream().map(HawbBillVo::getChargeWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setTotalChargeWeight(totalChargeWeight);
        }
        return vo;
    }

    /**
     * 支付审核
     * @param bill 账单数据
     * @return 结果
     */
    @Override
    public int edit(Bill bill) {
        return billMapper.updateById(bill);
    }

    /**
     * 删除分单账单数据
     * @param id 账单id
     * @return 结果
     */
    @Override
    public int del(Long id) {
        Bill bill = billMapper.selectById(id);
        bill.setIsDel(1);
        return billMapper.updateById(bill);
    }

    /**
     * 导出国内分单账单
     * @param query 查询参数
     * @return 国内分单账单数据
     */
    @Override
    public List<BillVo> selectList(BillQuery query) {
        query.setDeptId(SecurityUtils.getHighParentId());
        List<BillVo> billVo = billMapper.selectListByQuery(query);
        for (BillVo bill : billVo) {
            switch (bill.getPayStatus()){
                case "UNPAID":
                    bill.setPayStatus("未支付");
                    break;
                case "PAID":
                    bill.setPayStatus("已支付");
                    break;
                case "REFUNDED":
                    bill.setPayStatus("已退款");
                    break;
                default:
                    bill.setPayStatus("未知状态");
                    break;
            }
            List<Hawb> list = hawbMapper.selectList(new QueryWrapper<Hawb>().eq("serial_no", bill.getSerialNo()).eq("is_del", 0));
            if (!CollectionUtils.isEmpty(list)){
                // 运单数
                bill.setTotalOrder(list.size());

                // 总金额
                BigDecimal totalMoney = list.stream().map(Hawb::getCostSum).reduce(BigDecimal.ZERO, BigDecimal::add);
                bill.setTotalMoney(totalMoney);

                // 总件数
                int totalQuantity = list.stream().mapToInt(Hawb::getQuantity).sum();
                bill.setTotalQuantity(totalQuantity);

                // 总重量
                BigDecimal totalWeight = list.stream().map(Hawb::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                bill.setTotalWeight(totalWeight);

                // 总计费重量
                BigDecimal totalChargeWeight = list.stream().map(Hawb::getChargeWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                bill.setTotalChargeWeight(totalChargeWeight);
            }
        }
        return billVo;
    }
}
