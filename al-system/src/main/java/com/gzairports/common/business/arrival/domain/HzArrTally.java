package com.gzairports.common.business.arrival.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 进港理货记录表
 *
 * <AUTHOR>
 * @date 2024-06-03
 */
@Data
@TableName(value = "hz_arr_tally")
public class HzArrTally {

    /** 主键id */
    private Long id;

    /** 运单录单id */
    private Long recordOrderId;

    /** 进港运单号 */
    private String waybillCode;

    /** 舱单件数 */
    private Integer cabinPieces;

    /** 舱单重量 */
    private BigDecimal cabinWeight;

    /** 理货件数 */
    private Integer pieces;

    /** 理货重量 */
    private BigDecimal weight;

    /** 板车 */
    private String uld;

    /** 仓库 */
    private String store;

    /** 库位 */
    private String locator;

    /** 不正常情况说明 */
    private String abnormal;

    /** 理货人 */
    private String username;

    /** 理货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date tallyTime;

    /** 状态 ld 录单 cd 生成舱单 lh_comp 理货完成 bd_comp 已办单 th_comp 已提货 */
    /** 状态 ld 录单 cd 生成舱单 lh_comp 理货完成 save 已办单 save_out 已出库 */
    private String status;

    /** 办理提货id */
    private Long pickUpId;

    /** 航段id */
    private Long legId;

    /** 不正常货邮设置为可提货办理的时间 */
    private LocalDateTime isPickUpTime;
}
