package com.gzairports.web.controller.charge;

import com.gzairports.wl.charge.domain.FreightRateCustomItem;
import com.gzairports.wl.charge.domain.query.FreightRateItemQuery;
import com.gzairports.wl.charge.domain.vo.FreightRateItemVO;
import com.gzairports.wl.charge.service.FreightRateCustomItemService;
import com.gzairports.common.annotation.OperLog;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客户运价条目Controller
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@RestController
@RequestMapping("/freightCustomItem")
@Api(tags = "客户运价条目接口")
public class FreightRateCustomItemController extends BaseController {

    @Autowired
    private FreightRateCustomItemService customItemService;


    /**
     * 查询客户运价条目列表
     */
//    @PreAuthorize("@ss.hasPermi('charge:freightCustomItem:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询客户运价条目列表")
    public TableDataInfo list(FreightRateItemQuery query){
        startPage();
        List<FreightRateItemVO> list = customItemService.selectList(query);
        return getDataTable(list);
    }

    /**
     * 获取运价条目详细信息
     */
    @PreAuthorize("@ss.hasPermi('charge:freightCustomItem:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取运价条目详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(customItemService.selectCustomItemById(id));
    }

    /**
     * 新增运价条目
     */
    @PreAuthorize("@ss.hasPermi('charge:freightCustomItem:add')")
    @OperLog(title = "运价条目", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ApiOperation(value = "新增运价条目")
    public AjaxResult add(@RequestBody FreightRateCustomItem item)
    {
        return toAjax(customItemService.insertCustomItem(item));
    }

    /**
     * 修改运价条目
     */
    @PreAuthorize("@ss.hasPermi('charge:freightCustomItem:edit')")
    @OperLog(title = "运价条目", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ApiOperation(value = "修改运价条目")
    public AjaxResult edit(@RequestBody FreightRateCustomItem item)
    {
        return toAjax(customItemService.updateCustomItem(item));
    }

    /**
     * 删除运价条目
     */
    @PreAuthorize("@ss.hasPermi('charge:freightCustomItem:del')")
    @OperLog(title = "运价条目", businessType = BusinessType.DELETE)
    @GetMapping("/del/{id}")
    @ApiOperation(value = "删除运价条目")
    public AjaxResult del(@PathVariable Long id)
    {
        return toAjax(customItemService.removeCustomItem(id));}

    /**
     * 批量删除客户运价条目
     */
    @PreAuthorize("@ss.hasPermi('charge:freightCustom:del')")
    @OperLog(title = "运价条目", businessType = BusinessType.DELETE)
    @DeleteMapping("/delBatch/{ids}")
    @ApiOperation(value = "批量删除客户运价")
    public AjaxResult delAll(@PathVariable Long[] ids)
    {
        customItemService.deleteAllFreightRateCustomItem(ids);
        return AjaxResult.success("操作成功");
    }
}
