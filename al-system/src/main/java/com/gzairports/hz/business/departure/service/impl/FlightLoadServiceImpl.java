package com.gzairports.hz.business.departure.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.gzairports.common.basedata.domain.*;
import com.gzairports.common.basedata.mapper.*;
import com.gzairports.common.business.arrival.domain.FlightLoadUldWaybill;
import com.gzairports.common.business.arrival.domain.FlightLoadWaybill;
import com.gzairports.common.business.arrival.mapper.FlightInfoMapper;
import com.gzairports.common.business.arrival.mapper.FlightLoadUldWaybillMapper;
import com.gzairports.common.business.arrival.mapper.FlightLoadWaybillMapper;
import com.gzairports.common.business.departure.domain.FlightInfo;
import com.gzairports.common.business.departure.domain.WaybillTrace;
import com.gzairports.common.business.departure.mapper.CostDetailMapper;
import com.gzairports.common.business.departure.mapper.MawbMapper;
import com.gzairports.common.business.departure.mapper.PullDownMapper;
import com.gzairports.common.business.departure.service.impl.WaybillTraceServiceImpl;
import com.gzairports.common.business.wrong.domain.Wrong;
import com.gzairports.common.business.wrong.mapper.WrongMapper;
import com.gzairports.common.config.AddressMsgVO;
import com.gzairports.common.config.FFMRedisOperator;
import com.gzairports.common.config.FFMScheduler;
import com.gzairports.common.constant.HttpStatus;
import com.gzairports.common.core.domain.entity.SysRole;
import com.gzairports.common.core.domain.entity.SysUser;
import com.gzairports.common.enums.FFMStatus;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.common.log.service.IWaybillLogService;
import com.gzairports.common.message.domain.Message;
import com.gzairports.common.message.domain.vo.SocketMessageVo;
import com.gzairports.common.message.mapper.MessageMapper;
import com.gzairports.common.pdf.PdfPrintHelper;
import com.gzairports.common.pdf.PrintPdf;
import com.gzairports.common.rabbitmq.SecurityProducer;
import com.gzairports.common.securitySubmit.domain.WaybillFlightData;
import com.gzairports.common.securitySubmit.domain.WaybillFlightLoadInfoData;
import com.gzairports.common.system.domain.SysConfig;
import com.gzairports.common.system.mapper.SysConfigMapper;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.system.mapper.SysUserMapper;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.SerialNumberGenerator;
import com.gzairports.common.utils.ServletUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.hz.business.cable.domain.HzCable;
import com.gzairports.hz.business.cable.domain.HzCableAddress;
import com.gzairports.hz.business.cable.domain.vo.*;
import com.gzairports.hz.business.cable.mapper.HzCableAddressMapper;
import com.gzairports.hz.business.cable.mapper.HzCableMapper;
import com.gzairports.hz.business.cable.service.impl.HttpServiceImpl;
import com.gzairports.hz.business.departure.domain.*;
import com.gzairports.hz.business.departure.domain.query.*;
import com.gzairports.hz.business.departure.domain.vo.*;
import com.gzairports.hz.business.departure.mapper.*;
import com.gzairports.hz.business.departure.rabbitmq.WaybillMessageProducer;
import com.gzairports.hz.business.departure.service.IFlightLoadService;
import com.gzairports.hz.business.transfer.mapper.HzTransferHandoverWaybillMapper;
import com.gzairports.hz.websocket.HzWebSocketServer;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Marker;
import org.slf4j.MarkerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 航班配载Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-26
 */
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class FlightLoadServiceImpl extends ServiceImpl<FlightLoadMapper, FlightLoad> implements IFlightLoadService {

    @Autowired
    private FlightLoadMapper flightLoadMapper;

    @Autowired
    private HzCollectWaybillMapper waybillMapper;

    @Autowired
    private HzCollectWeightMapper weightMapper;

    @Autowired
    private FlightLoadWaybillMapper loadWaybillMapper;

    @Autowired
    private FlightLoadUldWaybillMapper loadUldWaybillMapper;

    @Autowired
    private AirDifferentMapper airDifferentMapper;

    @Autowired
    private AllAirWaybillMapper airWaybillMapper;

    @Autowired
    private FlightInfoMapper flightInfoMapper;

    @Autowired
    private FlightLoadUldMapper loadUldMapper;

    @Autowired
    private HzDepGroupUldMapper groupUldMapper;

    @Autowired
    private HzDepGroupUldWaybillMapper groupUldWaybillMapper;

    @Autowired
    private HzDepGroupWaybillMapper groupWaybillMapper;

    @Autowired
    private PullDownMapper pullDownMapper;

    @Autowired
    private HzDisBoardMapper boardMapper;

    @Autowired
    private WaybillTraceServiceImpl traceService;

    @Autowired
    private WrongMapper wrongMapper;

    @Autowired
    private HzTransferHandoverWaybillMapper handoverWaybillMapper;

    @Autowired
    private IWaybillLogService waybillLogService;

    @Autowired
    private UldMapper uldMapper;

    @Autowired
    private TruckMapper truckMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private RepeatWeightMapper repeatWeightMapper;

    @Autowired
    private CargoCodeMapper cargoCodeMapper;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    @Autowired
    private WaybillMessageProducer waybillMessageProducer;

    @Autowired
    private MessageMapper messageMapper;

    @Autowired
    private SysConfigMapper configMapper;

    @Autowired
    private HzWebSocketServer webSocketServer;

    @Autowired
    private BaseCraftNoMapper baseCraftNoMapper;

    @Autowired
    private HzCableMapper hzCableMapper;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private HttpServiceImpl httpService;

    @Autowired
    private CarrierMapper carrierMapper;

    @Autowired
    private SecurityProducer securityProducer;

    @Autowired
    private HzCableAddressMapper cableAddressMapper;

    @Value("${hzCable.account}")
    private String account;

    @Value("${hzCable.loginUrl}")
    private String loginUrl;

    @Value("${hzCable.getMsg}")
    private String getMsg;

    @Value("${hzCable.FTPAddress}")
    private String ftpAddress;

    private static final Marker CABLE_ERROR_MARKER  = MarkerFactory.getMarker("CABLE-ERROR");

    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMdd");

    private static final SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
    private static final Pattern LETTERS_PATTERN = Pattern.compile("([A-Za-z]+)(\\d+)");

    private final FFMRedisOperator ffmRedisOperator;
    private final FFMScheduler ffmScheduler;

    /**
     * 查询航班配载列表
     *
     * @param query 查询条件
     * @return 配载列表
     */
    @Override
    public List<FlightLoadVo> selectList(FlightLoadQuery query,String[] split) {
        if (StringUtils.isNotNull(query.getState())) {
            if ("not_pre".equals(query.getState())) {
                query.setIsPre(0);
            } else if ("been_pre".equals(query.getState())) {
                query.setIsPre(1);
            }
        }
        List<FlightLoadVo> flightLoadVos = flightLoadMapper.selectLoadList(query,split);
        Date date = new Date();
        for (FlightLoadVo flightLoadVo : flightLoadVos) {
            LocalDateTime startSchemeTakeoffTime = flightLoadVo.getStartSchemeTakeoffTime();
            LocalDateTime currentLocalDateTime = date.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();

            Duration between = Duration.between(currentLocalDateTime, startSchemeTakeoffTime);
            if (startSchemeTakeoffTime.isAfter(currentLocalDateTime) && between.toMinutes() <= 150 && flightLoadVo.getIsCreate() == 0) {
                flightLoadVo.setIsOpen(1);
            } else {
                flightLoadVo.setIsOpen(0);
            }
            FlightInfo info = flightInfoMapper.selectById(flightLoadVo.getFlightId());
            if (!"not_pre".equals(flightLoadVo.getState())) {
                flightLoadVo.setStatusColor("green");
            } else {
                if (info.getStartSchemeTakeoffTime() != null) {
                    Duration duration = Duration.between(info.getStartSchemeTakeoffTime(), LocalDateTime.now());
                    long minutes = duration.toMinutes();
                    if (minutes >= 120) {
                        flightLoadVo.setStatusColor("white");
                    } else if (minutes >= 100) {
                        flightLoadVo.setStatusColor("yellow");
                    } else {
                        flightLoadVo.setStatusColor("read");
                    }
                } else {
                    flightLoadVo.setStatusColor("white");
                }
            }
            StringBuilder h1 = new StringBuilder();
            if (flightLoadVo.getH1Quantity() != null) {
                h1.append(flightLoadVo.getH1Quantity()).append("件");
            }
            if (flightLoadVo.getH1Weight() != null) {
                h1.append(" ").append(flightLoadVo.getH1Weight()).append("kg");
            }
            flightLoadVo.setH1(h1.toString());

            StringBuilder h2 = new StringBuilder();
            if (flightLoadVo.getH2Quantity() != null) {
                h2.append(flightLoadVo.getH2Quantity()).append("件");
            }
            if (flightLoadVo.getH2Weight() != null) {
                h2.append(" ").append(flightLoadVo.getH2Weight()).append("kg");
            }
            flightLoadVo.setH2(h2.toString());

            StringBuilder h3 = new StringBuilder();
            if (flightLoadVo.getH3Quantity() != null) {
                h3.append(flightLoadVo.getH3Quantity()).append("件");
            }
            if (flightLoadVo.getH3Weight() != null) {
                h3.append(" ").append(flightLoadVo.getH3Weight()).append("kg");
            }
            flightLoadVo.setH3(h3.toString());

            StringBuilder h4 = new StringBuilder();
            if (flightLoadVo.getH4Quantity() != null) {
                h4.append(flightLoadVo.getH4Quantity()).append("件");
            }
            if (flightLoadVo.getH4Weight() != null) {
                h4.append(" ").append(flightLoadVo.getH4Weight()).append("kg");
            }
            flightLoadVo.setH4(h4.toString());

            StringBuilder h5 = new StringBuilder();
            if (flightLoadVo.getH5Quantity() != null) {
                h5.append(flightLoadVo.getH5Quantity()).append("件");
            }
            if (flightLoadVo.getH5Weight() != null) {
                h5.append(" ").append(flightLoadVo.getH5Weight()).append("kg");
            }
            flightLoadVo.setH5(h5.toString());
        }
        return flightLoadVos;
    }

    /**
     * 查询代运导入
     *
     * @param query 查询条件
     * @return 结果
     */
    @Override
    public ForwardImportVo forwardImport(ForwardImportQuery query) {
        ForwardImportQuery queryNew = new ForwardImportQuery();
        if(query.getWaybillCode() != null &&
                ((query.getWaybillCode().length() ==  14 && query.getWaybillCode().startsWith("AWBMDN"))
                || query.getWaybillCode().length() == 15)){
            queryNew.setWaybillCode(query.getWaybillCode());
            queryNew.setStartTime(query.getStartTime() == null ? null : query.getStartTime());
            queryNew.setEndTime(query.getEndTime() == null ? null : query.getEndTime());
        }else{
            BeanUtils.copyProperties(query, queryNew);
        }
        if(StringUtils.isNotEmpty(query.getCarrier1())){
            Set<String> carrierList = new HashSet<>();
            carrierList.add(query.getCarrier1());
            List<String> strings = carrierMapper.selectCodeForSamePrefixByCode(query.getCarrier1());
            carrierList.addAll(strings);
            queryNew.setCarrierList(carrierList);
        }
        ForwardImportVo vo = new ForwardImportVo();
        List<ForwardImportWaybillVo> waybillVos = new ArrayList<>();
        List<ForwardImportWaybillVo> forwardImportWaybillVos = weightMapper.selectCollectWaybillList(queryNew);

        List<ForwardImportWaybillVo> pullDownData = pullDownMapper.selectImportData(queryNew);

        List<ForwardImportWaybillVo> disBoardList = boardMapper.selectImportDate(queryNew);
        if (!CollectionUtils.isEmpty(pullDownData)) {
            waybillVos.addAll(pullDownData);
        }
        if (!CollectionUtils.isEmpty(disBoardList)) {
            waybillVos.addAll(disBoardList);
        }
        if (!CollectionUtils.isEmpty(forwardImportWaybillVos)) {
            waybillVos.addAll(forwardImportWaybillVos);
        }
        if (!CollectionUtils.isEmpty(waybillVos)) {
            List<ForwardImportWaybillVo> mergedList = waybillVos.stream()
                    .collect(Collectors.groupingBy(ForwardImportWaybillVo::getWaybillId))
                    .values().stream()
                    .map(group -> {
                        ForwardImportWaybillVo mergedVo = group.get(0);
                        int totalQuantity = group.stream().filter(e->e.getQuantity() != null).mapToInt(ForwardImportWaybillVo::getQuantity).sum();
                        BigDecimal totalWeight = group.stream().map(ForwardImportWaybillVo::getWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                        mergedVo.setQuantity(totalQuantity);
                        mergedVo.setWeight(totalWeight);
                        return mergedVo;
                    })
                    .collect(Collectors.toList());
            vo.setVos(mergedList);
            vo.setWaybillNum(waybillVos.size());
            int quantity = waybillVos.stream()
                    .filter(item -> item.getQuantity() != null && item.getQuantity() >= 0)
                    .mapToInt(ForwardImportWaybillVo::getQuantity)
                    .sum();
            vo.setTotalQuantity(quantity);
            BigDecimal weight = waybillVos.stream()
                    .filter(item -> item.getWeight() != null && item.getWeight().compareTo(BigDecimal.ZERO) >= 0)
                    .map(ForwardImportWaybillVo::getWeight)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setTotalWeight(weight);
        }
        List<ForwardImportUldVo> uldVos = new ArrayList<>();
        List<ForwardImportUldVo> forwardImportUldVos = weightMapper.selectCollectList(queryNew);
        if (!CollectionUtils.isEmpty(forwardImportUldVos)) {
            forwardImportUldVos.forEach(e->{
                if(StringUtils.isNotNull(e.getBoardWeight()) && StringUtils.isNotNull(e.getPlateWeight())){
                    e.setTotalWeight(e.getWeight().subtract(e.getPlateWeight()).subtract(e.getBoardWeight()));
                }else{
                    e.setTotalWeight(e.getWeight());
                }
            });
            uldVos.addAll(forwardImportUldVos);
        }
        List<ForwardImportUldVo> pullUldDownData = pullDownMapper.selectImportUldData(queryNew);
        if (!CollectionUtils.isEmpty(pullUldDownData)) {
            uldVos.addAll(pullUldDownData);
        }
        List<ForwardImportUldVo> disBoardData = boardMapper.selectImportUldData(queryNew);
        if (!CollectionUtils.isEmpty(disBoardData)) {
            uldVos.addAll(disBoardData);
        }
        List<ForwardImportUldVo> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(uldVos)) {
            Map<String, List<ForwardImportUldVo>> collect = uldVos.stream().collect(Collectors.groupingBy(ForwardImportUldVo::getUld));
            for (Map.Entry<String, List<ForwardImportUldVo>> vos : collect.entrySet()) {
                ForwardImportUldVo uldVo = new ForwardImportUldVo();
                List<ForwardImportWaybillVo> importWaybillVoList = new ArrayList<>();
                uldVo.setUld(vos.getKey());
                int sum = vos.getValue().stream().mapToInt(ForwardImportUldVo::getTotalQuantity).sum();
                uldVo.setTotalQuantity(sum);
                BigDecimal reduce = vos.getValue().stream().map(ForwardImportUldVo::getTotalWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                uldVo.setTotalWeight(reduce);
                List<Long> collectIds = vos.getValue().stream().map(ForwardImportUldVo::getCollectId).collect(Collectors.toList());
                uldVo.setCollectIds(collectIds);
                List<ForwardImportUldVo> collect1 = vos.getValue().stream().filter(e -> Objects.equals(e.getType(), 1)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(collect1)) {
                    List<Long> collect2 = collect1.stream().map(ForwardImportUldVo::getCollectId).collect(Collectors.toList());
                    List<ForwardImportWaybillVo> pullDownList = pullDownMapper.selectListByCollectIds(collect2);
                    if (!CollectionUtils.isEmpty(pullDownList)) {
                        importWaybillVoList.addAll(pullDownList);
                    }
                }
                List<ForwardImportUldVo> disBoard = vos.getValue().stream().filter(e -> Objects.equals(e.getType(), 2)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(disBoard)) {
                    List<Long> collect2 = disBoard.stream().map(ForwardImportUldVo::getCollectId).collect(Collectors.toList());
                    List<ForwardImportWaybillVo> disBoardListData = boardMapper.selectListByCollectIds(collect2);
                    if (!CollectionUtils.isEmpty(disBoardListData)) {
                        importWaybillVoList.addAll(disBoardListData);
                    }
                }
                List<ForwardImportWaybillVo> waybillVoList = waybillMapper.selectListByIds(uldVo.getCollectIds(),vos.getKey());
                waybillVoList.forEach(e->{
                    if(StringUtils.isNotNull(e.getBoardWeight()) && StringUtils.isNotNull(e.getPlateWeight())){
                        e.setWeight(e.getWeight().subtract(e.getPlateWeight()).subtract(e.getBoardWeight()));
                    }else{
                        e.setWeight(e.getWeight());
                    }
                });
                if (!CollectionUtils.isEmpty(waybillVoList)) {
                    importWaybillVoList.addAll(waybillVoList);
                }
                if (!CollectionUtils.isEmpty(importWaybillVoList)) {
                    List<ForwardImportWaybillVo> mergedList = importWaybillVoList.stream()
                            //非选择时间段内容的 板车 不显示
                            .filter(v -> {
                                boolean afterStart = (queryNew.getStartTime() == null || v.getWriteTime().after(queryNew.getStartTime()));
                                boolean beforeEnd = (queryNew.getEndTime() == null || v.getWriteTime().before(queryNew.getEndTime()));
                                return afterStart && beforeEnd;
                            })
                            .filter(v -> StringUtils.isEmpty(queryNew.getWaybillCode()) ||
                                    Objects.equals(queryNew.getWaybillCode(), v.getWaybillCode()))
                            .filter(v -> queryNew.getWaybillCode() != null &&
                                    (queryNew.getWaybillCode().length() != 14 && queryNew.getWaybillCode().length() != 15
                                    || Objects.equals(queryNew.getWaybillCode(), v.getWaybillCode())))
                            .collect(Collectors.groupingBy(ForwardImportWaybillVo::getWaybillId))
                            .values().stream()
                            .map(group -> {
                                ForwardImportWaybillVo mergedVo = group.get(0);
                                int totalQuantity = group.stream().mapToInt(ForwardImportWaybillVo::getQuantity).sum();
                                BigDecimal totalWeight = group.stream().map(ForwardImportWaybillVo::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                                mergedVo.setQuantity(totalQuantity);
                                mergedVo.setWeight(totalWeight);
                                return mergedVo;
                            })
                            .collect(Collectors.toList());
                    uldVo.setWaybillVos(mergedList);
                }
                if (!CollectionUtils.isEmpty(uldVo.getWaybillVos())){
                    list.add(uldVo);
                }
            }
        }
        vo.setUldVos(list);
        return vo;
    }

    /**
     * 装配
     *
     * @param query 装配数据
     * @return 结果
     */
    @Override
    public synchronized ForwardImportVo assemble(AssembleQuery query) {
        HttpServletResponse response = ServletUtils.getResponse();
        ArrayList<WaybillLog> waybillLogs = new ArrayList<>();
        int isSendMessage = 0;
        try {
            Date date = new Date();
            FlightLoad flightLoad = flightLoadMapper.selectById(query.getFlightLoadId());
            if (flightLoad == null) {
                throw new CustomException("无配载航段信息");
            }
            FlightInfo flightInfo = flightInfoMapper.selectBusinessFlightInfoById(flightLoad.getFlightId());
            List<BaseAirDifferent> baseAirDifferent = airDifferentMapper.selectList(new QueryWrapper<BaseAirDifferent>()
                    .eq("air_company", flightInfo.getAirWays())
                    .le("start_time", date)
                    .ge("end_time", date));
            List<BaseCargoCode> baseCargoCodeLiveList = cargoCodeMapper.selectList(new QueryWrapper<BaseCargoCode>()
                    .eq("category_code", "02")
                    .eq("is_del", "0"));
            List<String> liveLoads = baseCargoCodeLiveList.stream().map(BaseCargoCode::getChineseName).distinct().collect(Collectors.toList());
            ForwardImportVo vo = new ForwardImportVo();
            List<String> noLoads = new ArrayList<>();
            if (!CollectionUtils.isEmpty(query.getVos())) {
                List<String> cargoNames = query.getVos().stream().map(ForwardImportWaybillVo::getCargoName).distinct().collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(baseAirDifferent)) {
                    for (BaseAirDifferent airDifferent : baseAirDifferent) {
                        if (airDifferent.getNoLoad() != null) {
                            String[] split = airDifferent.getNoLoad().split(";");
                            for (String s : split) {
                                String[] split1 = s.split(":");
                                if (split1.length == 2) {
                                    //选中的是大类,需要找到该大类下的所有货品代码以及品名
                                    List<BaseCargoCode> categoryCodeList = cargoCodeMapper.selectList(new QueryWrapper<BaseCargoCode>()
                                            .eq("category_code", split1[0]));
                                    for (BaseCargoCode b : categoryCodeList) {
                                        noLoads.add(b.getChineseName());
                                    }
                                } else {
                                    //选中的是具体的货品 只需要加入当前的品名即可
                                    noLoads.add(split1[2]);
                                }
                            }
                        }
                    }
                    for (String cargoName : cargoNames) {
                        if (noLoads.contains(cargoName)) {
                            throw new CustomException("航班配载代运导入存在不可配载货物:" + cargoName);
                        }
                    }
                }
                for(String cargoName:cargoNames){
                    if(liveLoads.contains(cargoName)){
                        isSendMessage++;
                    }
                }
                for (ForwardImportWaybillVo forwardImportWaybillVo : query.getVos()) {
                    checkIsAssemble(forwardImportWaybillVo.getWeightId(), forwardImportWaybillVo.getWaybillCode());
                    FlightLoadWaybill loadWaybill = loadWaybillMapper.selectOne(new QueryWrapper<FlightLoadWaybill>()
                            .eq("flight_load_id", query.getFlightLoadId())
                            .eq("waybill_id", forwardImportWaybillVo.getWaybillId()));
                    Long loadWaybillId = null;
                    if (loadWaybill != null) {
                        Integer quantity = loadWaybill.getQuantity();
                        BigDecimal weight = loadWaybill.getWeight();
                        loadWaybill.setQuantity(forwardImportWaybillVo.getQuantity() + quantity);
                        loadWaybill.setWeight(weight.add(forwardImportWaybillVo.getWeight()));
                        loadWaybillId = loadWaybill.getId();
                        loadWaybillMapper.updateById(loadWaybill);
                    } else {
                        FlightLoadWaybill waybill = new FlightLoadWaybill();
                        waybill.setWaybillId(forwardImportWaybillVo.getWaybillId());
                        waybill.setQuantity(forwardImportWaybillVo.getQuantity());
                        waybill.setWeight(forwardImportWaybillVo.getWeight());
                        waybill.setFlightLoadId(query.getFlightLoadId());
                        waybill.setCollectId(forwardImportWaybillVo.getCollectId());
                        loadWaybillMapper.insert(waybill);
                        loadWaybillId = waybill.getId();
                    }
                    AirWaybill airWaybill = airWaybillMapper.selectById(forwardImportWaybillVo.getWaybillId());
                    if (airWaybill.getSwitchBill() != 2){
                        getConfigForLoad(airWaybill);
                    }
                    airWaybill.setIsLoad(1);
                    airWaybill.setUpdateTime(new Date());
                    airWaybillMapper.updateById(airWaybill);
                    //运单日志
                    WaybillLog waybillLog = waybillLogService.getWaybillLog(
                            airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                            forwardImportWaybillVo.getWeight().toString(), forwardImportWaybillVo.getQuantity().toString(), flightInfo.getFlightNo(),
                            query, "操作成功", 0, null, new Date(),
                            "装配件数:" + forwardImportWaybillVo.getQuantity() + ",重量:" + forwardImportWaybillVo.getWeight(),
                            "DEP", null);
                    waybillLogs.add(waybillLog);


                    forwardImportWaybillVo.setId(loadWaybillId);
                    List<Long> pullIdList = pullDownMapper.selectImportDataByCode(forwardImportWaybillVo.getWaybillCode());
                    for (Long pullId : pullIdList) {
                        pullDownMapper.updateLoad(pullId,flightLoad.getFlightId());
                    }
                    List<Long> boardIdList = boardMapper.selectImportDataByCode(forwardImportWaybillVo.getWaybillId());
                    for (Long boardId : boardIdList) {
                        boardMapper.updateLoad(boardId);
                    }
                    List<Long> handoverIdList = handoverWaybillMapper.selectImportDataByCode(forwardImportWaybillVo.getWaybillId());
                    for (Long handoverId : handoverIdList) {
                        handoverWaybillMapper.updateLoad(handoverId);
                    }
                    List<Long> weightIdList = weightMapper.selectImportDataByCode(forwardImportWaybillVo.getWaybillId());
                    for (Long weightId : weightIdList) {
                        HzCollectWeight weight = weightMapper.selectById(weightId);
                        weight.setIsLoad(1);
                        weightMapper.updateById(weight);
                    }
                }
                vo.setVos(query.getVos());
                vo.setWaybillNum(query.getVos().size());
                int quantity = query.getVos().stream().mapToInt(ForwardImportWaybillVo::getQuantity).sum();
                vo.setTotalQuantity(quantity);
                BigDecimal weight = query.getVos().stream().map(ForwardImportWaybillVo::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                vo.setTotalWeight(weight);
            }
            if (!CollectionUtils.isEmpty(query.getUldVos())) {
                for (ForwardImportUldVo forwardImportUldVo : query.getUldVos()) {
                    List<HzCollectWaybill> collectWaybill = waybillMapper.selectList(new QueryWrapper<HzCollectWaybill>().in("id", forwardImportUldVo.getCollectIds()));
                    if (!CollectionUtils.isEmpty(collectWaybill)) {
                        List<Long> collect = collectWaybill.stream().map(HzCollectWaybill::getWaybillId).collect(Collectors.toList());
                        List<String> cargoNames = airWaybillMapper.selectCargoNames(collect);
                        if (!CollectionUtils.isEmpty(baseAirDifferent)) {
                            for (String cargoName : cargoNames) {
                                if (noLoads.contains(cargoName)) {
                                    throw new CustomException("航班配载代运导入存在不可配载货物:" + cargoName);
                                }
                            }
                        }
                        for(String cargoName:cargoNames){
                            if(liveLoads.contains(cargoName)){
                                isSendMessage++;
                            }
                        }
                    }
                    Long loadUldId;
                    FlightLoadUld flightLoadUld = loadUldMapper.selectOne(new QueryWrapper<FlightLoadUld>()
                            .eq("flight_load_id", query.getFlightLoadId())
                            .eq("uld", forwardImportUldVo.getUld()));
                    if (flightLoadUld == null) {
                        FlightLoadUld loadUld = new FlightLoadUld();
                        loadUld.setFlightLoadId(query.getFlightLoadId());
                        loadUld.setUld(forwardImportUldVo.getUld());
                        loadUldMapper.insert(loadUld);
                        loadUldId = loadUld.getId();
                    } else {
                        loadUldId = flightLoadUld.getId();
                    }
                    List<ForwardImportWaybillVo> waybillVos1 = forwardImportUldVo.getWaybillVos();
                    if (!CollectionUtils.isEmpty(waybillVos1)) {
                        List<Long> collect = waybillVos1.stream().map(ForwardImportWaybillVo::getWaybillId).collect(Collectors.toList());
                        List<String> cargoNames = airWaybillMapper.selectCargoNames(collect);
                        for(String cargoName:cargoNames){
                            if(liveLoads.contains(cargoName)){
                                isSendMessage++;
                            }
                        }

                        for (ForwardImportWaybillVo forwardImportWaybillVo : waybillVos1) {
                            checkIsAssemble(forwardImportWaybillVo.getWeightId(), forwardImportWaybillVo.getWaybillCode());
                            FlightLoadUldWaybill flightLoadUldWaybill = loadUldWaybillMapper.selectOne(new QueryWrapper<FlightLoadUldWaybill>()
                                    .eq("load_uld_id", loadUldId)
                                    .eq("waybill_id", forwardImportWaybillVo.getWaybillId()));
                            AirWaybill airWaybill = airWaybillMapper.selectById(forwardImportWaybillVo.getWaybillId());
                            if (airWaybill.getSwitchBill() != 2){
                                getConfigForLoad(airWaybill);
                            }
                            List<Long> list = pullDownMapper.selectImportUldDataByCode(forwardImportWaybillVo.getWaybillCode(), forwardImportUldVo.getUld());
                            Integer quantity = forwardImportWaybillVo.getQuantity();
                            BigDecimal weight = forwardImportWaybillVo.getWeight();
                            for (Long aLong : list) {
                                ForwardImportWaybillVo hzDepPullDown = pullDownMapper.selectByCollectId(aLong);
                                HzDepPullDown hzDepPullDown1 = pullDownMapper.selectById(aLong);
                                hzDepPullDown1.setIsLoad(1);
                                hzDepPullDown1.setFlightIdNew(flightLoad.getFlightId());
                                pullDownMapper.updateById(hzDepPullDown1);
                                airWaybill.setIsLoad(1);
                                airWaybill.setUpdateTime(new Date());
                                airWaybillMapper.updateById(airWaybill);

                                //运单日志
                                WaybillLog waybillLog = waybillLogService.getWaybillLog(
                                        airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                                        forwardImportWaybillVo.getWeight().toString(), forwardImportWaybillVo.getQuantity().toString(), flightInfo.getFlightNo(),
                                        query, "操作成功", 0, null, new Date(),
                                        "装配件数:" + forwardImportWaybillVo.getQuantity() + ",重量:" + forwardImportWaybillVo.getWeight(),
                                        "DEP", null);
                                waybillLogs.add(waybillLog);

                                if (flightLoadUldWaybill != null) {
                                    quantity += flightLoadUldWaybill.getQuantity();
                                    weight = weight.add(flightLoadUldWaybill.getWeight());
                                    flightLoadUldWaybill.setQuantity(quantity);
                                    flightLoadUldWaybill.setWeight(weight);
                                    loadUldWaybillMapper.updateById(flightLoadUldWaybill);
                                } else {
                                    FlightLoadUldWaybill waybill = new FlightLoadUldWaybill();
                                    waybill.setWaybillId(airWaybill.getId());
                                    waybill.setQuantity(hzDepPullDown.getQuantity());
                                    waybill.setWeight(hzDepPullDown.getWeight());
                                    waybill.setLoadUldId(loadUldId);
                                    waybill.setCollectId(hzDepPullDown.getCollectId());
                                    loadUldWaybillMapper.insert(waybill);
                                }
                            }
                            List<Long> boardIdList = boardMapper.selectImportUldDataByCode(forwardImportWaybillVo.getWaybillId(), forwardImportUldVo.getUld());
                            for (Long aLong : boardIdList) {
                                ForwardImportWaybillVo boardWaybill = boardMapper.selectByCollectId(aLong);
                                HzDisBoard hzDisBoard = boardMapper.selectById(aLong);
                                hzDisBoard.setIsLoad(1);
                                boardMapper.updateById(hzDisBoard);
                                if (flightLoadUldWaybill != null) {
                                    quantity += forwardImportWaybillVo.getQuantity();
                                    weight = weight.add(forwardImportWaybillVo.getWeight());
                                    flightLoadUldWaybill.setQuantity(quantity);
                                    flightLoadUldWaybill.setWeight(weight);
                                    loadUldWaybillMapper.updateById(flightLoadUldWaybill);
                                } else {
                                    FlightLoadUldWaybill waybill = new FlightLoadUldWaybill();
                                    waybill.setWaybillId(airWaybill.getId());
                                    waybill.setQuantity(boardWaybill.getQuantity());
                                    waybill.setWeight(boardWaybill.getWeight());
                                    waybill.setLoadUldId(loadUldId);
                                    waybill.setCollectId(boardWaybill.getCollectId());
                                    loadUldWaybillMapper.insert(waybill);
                                }

                                //运单日志
                                WaybillLog waybillLog = waybillLogService.getWaybillLog(
                                        airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                                        forwardImportWaybillVo.getWeight().toString(), forwardImportWaybillVo.getQuantity().toString(), flightInfo.getFlightNo(),
                                        query, "操作成功", 0, null, new Date(),
                                        "装配件数:" + forwardImportWaybillVo.getQuantity() + ",重量:" + forwardImportWaybillVo.getWeight(),
                                        "DEP", null);
                                waybillLogs.add(waybillLog);
                            }
                            List<ForwardImportWaybillVo> importWaybillVos = waybillMapper.selectByCollectId(forwardImportWaybillVo.getWaybillId(), forwardImportUldVo.getUld());
                            importWaybillVos.forEach(e->{
                                if(StringUtils.isNotNull(e.getBoardWeight()) && StringUtils.isNotNull(e.getPlateWeight())){
                                    e.setWeight(e.getWeight().subtract(e.getPlateWeight()).subtract(e.getBoardWeight()));
                                }else{
                                    e.setWeight(e.getWeight());
                                }
                            });
                            if (!CollectionUtils.isEmpty(importWaybillVos)) {
                                List<ForwardImportWaybillVo> mergedList = importWaybillVos.stream()
                                        .collect(Collectors.groupingBy(ForwardImportWaybillVo::getWaybillId))
                                        .values().stream()
                                        .map(group -> {
                                            ForwardImportWaybillVo mergedVo = group.get(0);
                                            int totalQuantity = group.stream().mapToInt(ForwardImportWaybillVo::getQuantity).sum();
                                            BigDecimal totalWeight = group.stream().map(ForwardImportWaybillVo::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                                            mergedVo.setQuantity(totalQuantity);
                                            mergedVo.setWeight(totalWeight);
                                            return mergedVo;
                                        })
                                        .collect(Collectors.toList());
                                for (ForwardImportWaybillVo importWaybillVo : mergedList) {
                                    if (flightLoadUldWaybill != null) {
                                        quantity += forwardImportWaybillVo.getQuantity();
                                        weight = weight.add(forwardImportWaybillVo.getWeight());
                                        flightLoadUldWaybill.setQuantity(quantity);
                                        flightLoadUldWaybill.setWeight(weight);
                                        loadUldWaybillMapper.updateById(flightLoadUldWaybill);
                                    } else {
                                        FlightLoadUldWaybill waybill = new FlightLoadUldWaybill();
                                        waybill.setWaybillId(forwardImportWaybillVo.getWaybillId());
                                        waybill.setQuantity(importWaybillVo.getQuantity());
                                        waybill.setWeight(importWaybillVo.getWeight());
                                        waybill.setLoadUldId(loadUldId);
                                        waybill.setCollectId(importWaybillVo.getCollectId());
                                        loadUldWaybillMapper.insert(waybill);
                                        airWaybill.setIsLoad(1);
                                        airWaybill.setUpdateTime(new Date());
                                        airWaybillMapper.updateById(airWaybill);
                                    }
                                }
                            }
                        }
                    }
                    if (!CollectionUtils.isEmpty(waybillVos1)) {
                        forwardImportUldVo.setWaybillVos(waybillVos1);
                    }
                    for (Long collectId : forwardImportUldVo.getCollectIds()) {
                        List<HzCollectWeight> weight = weightMapper.selectList(new QueryWrapper<HzCollectWeight>().eq("uld", forwardImportUldVo.getUld()).eq("collect_id", collectId));
                        for (HzCollectWeight hzCollectWeight : weight) {
                            hzCollectWeight.setIsLoad(1);
                            weightMapper.updateById(hzCollectWeight);
                        }
                    }
                    forwardImportUldVo.setId(loadUldId);
                    String uld = forwardImportUldVo.getUld();
                    if (StringUtils.isNotNull(uld)) {
                        if ("CAR".equals(uld.substring(0, 3))) {
                            BaseFlatbedTruck baseFlatbedTruck = truckMapper.selectByCode(uld.substring(3));
                            if (StringUtils.isNotNull(baseFlatbedTruck)) {
                                baseFlatbedTruck.setLcStatus("been_pre");
                                truckMapper.updateById(baseFlatbedTruck);
                            }
                        } else {
                            BaseCargoUld baseCargoUld = uldMapper.selectByCode(uld);
                            if (StringUtils.isNotNull(baseCargoUld)) {
                                baseCargoUld.setStatus("been_pre");
                                uldMapper.updateById(baseCargoUld);
                            }
                        }
                    }

                }
                setTotal(vo, query.getUldVos());
            }

            if(isSendMessage > 0){
            BaseCraftNo baseCraftNo = baseCraftNoMapper.selectCraftNoInfo(flightInfo.getCraftNo(), new Date(), 2);
            SocketMessageVo liveLoadsVo = new SocketMessageVo();
                if(baseCraftNo != null && StringUtils.isNotNull(baseCraftNo.getCraftType())){
                    liveLoadsVo.setMessage("请注意:" + baseCraftNo.getCraftNo() + "该机号配载鲜活舱位如下:" + baseCraftNo.getCabins());
                }else{
                    liveLoadsVo.setMessage("请注意:" + flightInfo.getCraftNo() + "该机号不能配载鲜活");
                }
                webSocketServer.sendMessageToDept(SecurityUtils.getUserId(), liveLoadsVo);
            }

            return vo;
        } catch (Exception e) {
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作失败" + "," +
                                "code:" + response.getStatus()));
                waybillLog.setErrorMsg(e.getMessage());
                waybillLog.setStatus(1);
            }
            throw new CustomException(e.getMessage());
        } finally {
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLogService.insertWaybillLog(waybillLog);
            }
        }
    }

    /**
     * 判断该代运导入数据是否已装配
     * */
    private void checkIsAssemble(Long weightId,String waybillCode) {
        HzCollectWeight collectWeight = weightMapper.selectById(weightId);
        if (StringUtils.isNotNull(collectWeight)) {
            if (collectWeight.getIsLoad() == 1) {
                throw new CustomException(waybillCode + "已装配");
            }
        }
        HzDepPullDown hzDepPullDown = pullDownMapper.selectById(weightId);
        if (StringUtils.isNotNull(hzDepPullDown)) {
            if (hzDepPullDown.getIsLoad() == 1) {
                throw new CustomException(waybillCode + "已装配");
            }
        }
        HzDisBoard hzDisBoard = boardMapper.selectById(weightId);
        if (StringUtils.isNotNull(hzDisBoard)) {
            if (hzDisBoard.getIsLoad() == 1) {
                throw new CustomException(waybillCode + "已装配");
            }
        }
    }

    /**
     * 装箱
     *
     * @param query 装箱参数
     * @return 结果
     */
    @Override
    public ForwardImportVo packing(FlightLoadPackQuery query) {
        HttpServletResponse response = ServletUtils.getResponse();
        ArrayList<WaybillLog> waybillLogs = new ArrayList<>();
        if (query.getFlightLoadUldIds() == null || query.getFlightLoadUldIds().size() > 1) {
            throw new CustomException("勾选一个板箱");
        }
        if (CollectionUtils.isEmpty(query.getFlightLoadWaybillIds())) {
            throw new CustomException("请勾选散货区的运单");
        }
        try {
            ForwardImportVo vo = new ForwardImportVo();
            List<FlightLoadWaybill> flightLoadWaybills = loadWaybillMapper.selectBatchIds(query.getFlightLoadWaybillIds());
            for (FlightLoadWaybill flightLoadWaybill : flightLoadWaybills) {
                FlightLoadUldWaybill waybill = loadUldWaybillMapper.selectOne(new QueryWrapper<FlightLoadUldWaybill>()
                        .eq("waybill_id", flightLoadWaybill.getWaybillId()).eq("load_uld_id", query.getFlightLoadUldIds().get(0)));
                if (waybill != null) {
                    int quantity = waybill.getQuantity() + flightLoadWaybill.getQuantity();
                    waybill.setQuantity(quantity);
                    BigDecimal weight = waybill.getWeight().add(flightLoadWaybill.getWeight());
                    waybill.setWeight(weight);
                    //不是第一次正式舱单打印后的操作 赋值为1
                    waybill.setIsEdit(1);
                    loadUldWaybillMapper.updateById(waybill);
                } else {
                    FlightLoadUldWaybill uldWaybill = new FlightLoadUldWaybill();
                    uldWaybill.setWaybillId(flightLoadWaybill.getWaybillId());
                    uldWaybill.setLoadUldId(query.getFlightLoadUldIds().get(0));
                    uldWaybill.setQuantity(flightLoadWaybill.getQuantity());
                    uldWaybill.setWeight(flightLoadWaybill.getWeight());
                    uldWaybill.setCollectId(flightLoadWaybill.getCollectId());
                    uldWaybill.setIsEdit(1);
                    loadUldWaybillMapper.insert(uldWaybill);
                }
                FlightLoadUld flightLoadUld = loadUldMapper.selectById(query.getFlightLoadUldIds().get(0));
                flightLoadUld.setIsEdit(1);
                loadUldMapper.updateById(flightLoadUld);
                //修改板车更新状态时将板车上运单的状态也一起更新
//                updateUldWaybill(flightLoadUld.getId());
                loadWaybillMapper.deleteById(flightLoadWaybill.getId());
                FlightLoad flightLoad = flightLoadMapper.selectById(flightLoadUld.getFlightLoadId());
                FlightInfo flightInfo = flightInfoMapper.selectById(flightLoad.getFlightId());
                //运单日志
                String waybillCode = airWaybillMapper.selectWaybillCode(flightLoadWaybill.getWaybillId());
                WaybillLog waybillLog = waybillLogService.getWaybillLog(
                        waybillCode, 0, SecurityUtils.getNickName(),
                        flightLoadWaybill.getWeight().toString(), flightLoadWaybill.getQuantity().toString(), flightInfo.getAirWays() + flightInfo.getFlightNo(),
                        query, "操作成功", 0, null, new Date(),
                        "装箱件数:" + flightLoadWaybill.getQuantity() + ",重量:" + flightLoadWaybill.getWeight(),
                        "DEP", null);
                waybillLogs.add(waybillLog);

            }
            selectVo(query.getFlightLoadId(), vo);
            return vo;
        } catch (Exception e) {
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作失败" + "," +
                                "code:" + response.getStatus()));
                waybillLog.setErrorMsg(e.getMessage());
                waybillLog.setStatus(1);
            }
            throw new CustomException(e.getMessage());
        } finally {
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLogService.insertWaybillLog(waybillLog);
            }
        }
    }

    private void setTotal(ForwardImportVo vo, List<ForwardImportUldVo> forwardImportUldVos) {
        if (!CollectionUtils.isEmpty(forwardImportUldVos)) {
            List<ForwardImportUldVo> forwardImportUldVosSorted;
            //按照12345h的顺序进行排序
            if (Objects.equals(vo.getType(), 1)) {
                forwardImportUldVosSorted = sortByCabin(forwardImportUldVos);
            } else {
                forwardImportUldVosSorted = forwardImportUldVos;
            }
            vo.setUldVos(forwardImportUldVosSorted);
            int totalQuantity = forwardImportUldVos.stream().filter(e -> e.getTotalQuantity() != null).mapToInt(ForwardImportUldVo::getTotalQuantity).sum();
            vo.setUldTotalQuantity(totalQuantity);
            BigDecimal totalWeight = forwardImportUldVos.stream().map(ForwardImportUldVo::getTotalWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setUldTotalWeight(totalWeight);
        }
    }

    private final String[] order = {"1H", "2H", "3H", "4H", "5H"};
    private final int maxIndex = order.length;

    /**
     * 对舱位进行排序 -> 航班配载列表页
     */
    private List<ForwardImportUldVo> sortByCabin(List<ForwardImportUldVo> forwardImportUldVos) {
        forwardImportUldVos.sort(new Comparator<ForwardImportUldVo>() {
            @Override
            public int compare(ForwardImportUldVo o1, ForwardImportUldVo o2) {
                int index1 = getIndex(o1.getCabin());
                int index2 = getIndex(o2.getCabin());
                return Integer.compare(index1, index2);
            }

            private int getIndex(String cabin) {
                if (cabin == null || cabin.isEmpty()) {
                    return maxIndex; // 将空值排在最后
                }
                for (int i = 0; i < order.length; i++) {
                    if (order[i].equals(cabin)) {
                        return i;
                    }
                }
                return maxIndex;
            }
        });
        return forwardImportUldVos;
    }

    /**
     * 对舱位进行排序 -> 正式舱单打印
     */
    private List<FormalUldVo> sortByCabin2(List<FormalUldVo> forwardUldVos) {
        forwardUldVos.sort(new Comparator<FormalUldVo>() {
            @Override
            public int compare(FormalUldVo o1, FormalUldVo o2) {
                int index1 = getIndex(o1.getCabin());
                int index2 = getIndex(o2.getCabin());
                return Integer.compare(index1, index2);
            }

            private int getIndex(String cabin) {
                if (cabin == null || cabin.isEmpty()) {
                    return maxIndex; // 将空值排在最后
                }
                for (int i = 0; i < order.length; i++) {
                    if (order[i].equals(cabin)) {
                        return i;
                    }
                }
                return maxIndex;
            }
        });
        return forwardUldVos;
    }

    /**
     * 对舱位进行排序 -> 机长通知单打印 危险品
     */
    private List<DangerNoticeVo> sortByCabin3(List<DangerNoticeVo> dangerNoticeVos) {
        if (!CollectionUtils.isEmpty(dangerNoticeVos) && dangerNoticeVos.size() == 1){
            return dangerNoticeVos;
        }
        dangerNoticeVos.sort(new Comparator<DangerNoticeVo>() {
            @Override
            public int compare(DangerNoticeVo o1, DangerNoticeVo o2) {
                int index1 = getIndex(o1.getDangerCabin());
                int index2 = getIndex(o2.getDangerCabin());
                return Integer.compare(index1, index2);
            }

            private int getIndex(String cabin) {
                if (cabin == null || cabin.isEmpty()) {
                    return maxIndex; // 将空值排在最后
                }
                for (int i = 0; i < order.length; i++) {
                    if (order[i].equals(cabin)) {
                        return i;
                    }
                }
                return maxIndex;
            }
        });
        return dangerNoticeVos;
    }


    /**
     * 对舱位进行排序 -> 机长通知单打印 特货
     */
    private List<SpecialNoticeVo> sortByCabin4(List<SpecialNoticeVo> specialNoticeVos) {
        if (!CollectionUtils.isEmpty(specialNoticeVos) && specialNoticeVos.size() == 1){
            return specialNoticeVos;
        }
        specialNoticeVos.sort(new Comparator<SpecialNoticeVo>() {
            @Override
            public int compare(SpecialNoticeVo o1, SpecialNoticeVo o2) {
                int index1 = getIndex(o1.getCabin());
                int index2 = getIndex(o2.getCabin());
                return Integer.compare(index1, index2);
            }

            private int getIndex(String cabin) {
                if (cabin == null || cabin.isEmpty()) {
                    return maxIndex; // 将空值排在最后
                }
                for (int i = 0; i < order.length; i++) {
                    if (order[i].equals(cabin)) {
                        return i;
                    }
                }
                return maxIndex;
            }
        });
        return specialNoticeVos;
    }

    /**
     * 部分装箱
     *
     * @param query 部分装箱参数
     * @return 结果
     */
    @Override
    public ForwardImportVo partPack(FlightLoadPackQuery query) {
        HttpServletResponse response = ServletUtils.getResponse();
        ArrayList<WaybillLog> waybillLogs = new ArrayList<>();
        if (query.getFlightLoadUldIds() == null || query.getFlightLoadUldIds().size() > 1) {
            throw new CustomException("勾选一个板箱");
        }
        if (query.getFlightLoadWaybillIds() == null || query.getFlightLoadWaybillIds().size() > 1) {
            throw new CustomException("只能选择一个运单");
        }
        try {
            ForwardImportVo vo = new ForwardImportVo();
            List<FlightLoadWaybill> flightLoadWaybills = loadWaybillMapper.selectBatchIds(query.getFlightLoadWaybillIds());
            if (!CollectionUtils.isEmpty(flightLoadWaybills)) {
                for (FlightLoadWaybill loadWaybill : flightLoadWaybills) {
                    if (query.getQuantity() > loadWaybill.getQuantity()) {
                        throw new CustomException("装箱数量超过装配数量");
                    }
                    if (query.getWeight().compareTo(loadWaybill.getWeight()) > 0) {
                        throw new CustomException("装箱重量超过装配重量");
                    }
                    FlightLoadUldWaybill loadUldWaybill = loadUldWaybillMapper.selectOne(new QueryWrapper<FlightLoadUldWaybill>()
                            .eq("load_uld_id", query.getFlightLoadUldIds().get(0))
                            .eq("waybill_id", loadWaybill.getWaybillId()));
                    if (loadUldWaybill == null) {
                        // 增加板箱区运单数据
                        FlightLoadUldWaybill flightLoadUldWaybill = new FlightLoadUldWaybill();
                        flightLoadUldWaybill.setWaybillId(loadWaybill.getWaybillId());
                        flightLoadUldWaybill.setQuantity(query.getQuantity());
                        flightLoadUldWaybill.setWeight(query.getWeight());
                        flightLoadUldWaybill.setLoadUldId(query.getFlightLoadUldIds().get(0));
                        flightLoadUldWaybill.setCollectId(loadWaybill.getCollectId());
                        flightLoadUldWaybill.setIsEdit(1);
                        loadUldWaybillMapper.insert(flightLoadUldWaybill);
                    } else {
                        // 修改散货区运单数据
                        int quantity = loadUldWaybill.getQuantity() + query.getQuantity();
                        loadUldWaybill.setQuantity(quantity);
                        BigDecimal weight = loadUldWaybill.getWeight().add(query.getWeight());
                        loadUldWaybill.setWeight(weight);
                        loadUldWaybill.setIsEdit(1);
                        loadUldWaybillMapper.updateById(loadUldWaybill);
                    }
                    FlightLoadUld flightLoadUld = loadUldMapper.selectById(query.getFlightLoadUldIds().get(0));
                    flightLoadUld.setIsEdit(1);
                    loadUldMapper.updateById(flightLoadUld);
//                    updateUldWaybill(flightLoadUld.getId());
                    // 修改散货区运单数据
                    int quantity = loadWaybill.getQuantity() - query.getQuantity();
                    BigDecimal weight = loadWaybill.getWeight().subtract(query.getWeight());
                    if (quantity <= 0 && weight.compareTo(BigDecimal.ZERO) < 0) {
                        loadWaybillMapper.deleteById(loadWaybill.getId());
                    } else {
                        loadWaybill.setQuantity(quantity);
                        loadWaybill.setWeight(weight);
                        loadWaybill.setIsEdit(1);
                        loadWaybillMapper.updateById(loadWaybill);
                    }
                    FlightLoad flightLoad = flightLoadMapper.selectById(flightLoadUld.getFlightLoadId());
                    FlightInfo flightInfo = flightInfoMapper.selectById(flightLoad.getFlightId());
                    //运单日志
                    String waybillCode = airWaybillMapper.selectWaybillCode(loadWaybill.getWaybillId());
                    WaybillLog waybillLog = waybillLogService.getWaybillLog(
                            waybillCode, 0, SecurityUtils.getNickName(),
                            loadWaybill.getWeight().toString(), loadWaybill.getQuantity().toString(), flightInfo.getAirWays() + flightInfo.getFlightNo(),
                            query, "操作成功", 0, null, new Date(),
                            "部分装箱件数:" + loadWaybill.getQuantity() + ",重量:" + loadWaybill.getWeight(),
                            "DEP", null);
                    waybillLogs.add(waybillLog);
                }
            }

            selectVo(query.getFlightLoadId(), vo);
            return vo;
        } catch (Exception e) {
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作失败" + "," +
                                "code:" + response.getStatus()));
                waybillLog.setErrorMsg(e.getMessage());
                waybillLog.setStatus(1);
            }
            throw new CustomException(e.getMessage());
        } finally {
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLogService.insertWaybillLog(waybillLog);
            }
        }
    }

    private void selectVo(Long flightLoadId, ForwardImportVo vo) {
        vo.setId(flightLoadId);
        List<ForwardImportWaybillVo> waybillVos = loadWaybillMapper.selectLoadWaybillList(flightLoadId);
        if (!CollectionUtils.isEmpty(waybillVos)) {
            vo.setVos(waybillVos);
            vo.setWaybillNum(waybillVos.size());
            int quantity = waybillVos.stream().mapToInt(ForwardImportWaybillVo::getQuantity).sum();
            vo.setTotalQuantity(quantity);
            BigDecimal weight = waybillVos.stream().map(ForwardImportWaybillVo::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setTotalWeight(weight);
        }
        List<ForwardImportUldVo> forwardImportUldVos = loadUldMapper.selectLoadUldList(flightLoadId);
        for (ForwardImportUldVo forwardImportUldVo : forwardImportUldVos) {
            List<ForwardImportWaybillVo> list = loadUldWaybillMapper.selectLoadWaybillListByUldId(forwardImportUldVo.getId());
            if (!CollectionUtils.isEmpty(list)) {
                int sum = list.stream().mapToInt(ForwardImportWaybillVo::getQuantity).sum();
                forwardImportUldVo.setTotalQuantity(sum);
                BigDecimal reduce = list.stream().map(ForwardImportWaybillVo::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                forwardImportUldVo.setTotalWeight(reduce);
                forwardImportUldVo.setWaybillVos(list);
            }
        }
        setTotal(vo, forwardImportUldVos);
    }

    /**
     * 更新舱位
     *
     * @param id    配载板箱id
     * @param cabin 舱位
     * @return 结果
     */
    @Override
    public int updateCabin(Long id, String cabin) {
        FlightLoadUld loadUld = loadUldMapper.selectById(id);
        FlightLoad flightLoad = flightLoadMapper.selectById(loadUld.getFlightLoadId());
        List<ForwardImportWaybillVo> list = loadUldWaybillMapper.selectLoadWaybillListByUldId(id);
        loadUld.setCabin(cabin);
        if (!CollectionUtils.isEmpty(list)) {
            int sum = list.stream().mapToInt(ForwardImportWaybillVo::getQuantity).sum();
            BigDecimal reduce = list.stream().map(ForwardImportWaybillVo::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            switch (cabin) {
                case "1H":
                    Integer h1Quantity = flightLoad.getH1Quantity() == null ? new Integer(0) : flightLoad.getH1Quantity();
                    h1Quantity += sum;
                    flightLoad.setH1Quantity(h1Quantity);
                    BigDecimal h1Weight = flightLoad.getH1Weight() == null ? new BigDecimal(0) : flightLoad.getH1Weight();
                    h1Weight = h1Weight.add(reduce);
                    flightLoad.setH1Weight(h1Weight);
                    break;
                case "2H":
                    Integer h2Quantity = flightLoad.getH2Quantity() == null ? new Integer(0) : flightLoad.getH2Quantity();
                    h2Quantity += sum;
                    flightLoad.setH2Quantity(h2Quantity);
                    BigDecimal h2Weight = flightLoad.getH2Weight() == null ? new BigDecimal(0) : flightLoad.getH2Weight();
                    h2Weight = h2Weight.add(reduce);
                    flightLoad.setH2Weight(h2Weight);
                    break;
                case "3H":
                    Integer h3Quantity = flightLoad.getH3Quantity() == null ? new Integer(0) : flightLoad.getH3Quantity();
                    h3Quantity += sum;
                    flightLoad.setH3Quantity(h3Quantity);
                    BigDecimal h3Weight = flightLoad.getH3Weight() == null ? new BigDecimal(0) : flightLoad.getH3Weight();
                    h3Weight = h3Weight.add(reduce);
                    flightLoad.setH3Weight(h3Weight);
                    break;
                case "4H":
                    Integer h4Quantity = flightLoad.getH4Quantity() == null ? new Integer(0) : flightLoad.getH4Quantity();
                    h4Quantity += sum;
                    flightLoad.setH4Quantity(h4Quantity);
                    BigDecimal h4Weight = flightLoad.getH4Weight() == null ? new BigDecimal(0) : flightLoad.getH4Weight();
                    h4Weight = h4Weight.add(reduce);
                    flightLoad.setH4Weight(h4Weight);
                    break;
                case "5H":
                    Integer h5Quantity = flightLoad.getH5Quantity() == null ? new Integer(0) : flightLoad.getH5Quantity();
                    h5Quantity += sum;
                    flightLoad.setH5Quantity(h5Quantity);
                    BigDecimal h5Weight = flightLoad.getH5Weight() == null ? new BigDecimal(0) : flightLoad.getH5Weight();
                    h5Weight = h5Weight.add(reduce);
                    flightLoad.setH5Weight(h5Weight);
                    break;
                default:
                    throw new CustomException("无当前舱位信息");
            }
            switch (loadUld.getCabin()) {
                case "1H":
                    Integer h1Quantity = flightLoad.getH1Quantity() == null ? new Integer(0) : flightLoad.getH1Quantity();
                    h1Quantity -= sum;
                    flightLoad.setH1Quantity(h1Quantity);
                    BigDecimal h1Weight = flightLoad.getH1Weight() == null ? new BigDecimal(0) : flightLoad.getH1Weight();
                    h1Weight = h1Weight.subtract(reduce);
                    flightLoad.setH1Weight(h1Weight);
                    break;
                case "2H":
                    Integer h2Quantity = flightLoad.getH2Quantity() == null ? new Integer(0) : flightLoad.getH2Quantity();
                    h2Quantity -= sum;
                    flightLoad.setH2Quantity(h2Quantity);
                    BigDecimal h2Weight = flightLoad.getH2Weight() == null ? new BigDecimal(0) : flightLoad.getH2Weight();
                    h2Weight = h2Weight.subtract(reduce);
                    flightLoad.setH2Weight(h2Weight);
                    break;
                case "3H":
                    Integer h3Quantity = flightLoad.getH3Quantity() == null ? new Integer(0) : flightLoad.getH3Quantity();
                    h3Quantity -= sum;
                    flightLoad.setH3Quantity(h3Quantity);
                    BigDecimal h3Weight = flightLoad.getH3Weight() == null ? new BigDecimal(0) : flightLoad.getH3Weight();
                    h3Weight = h3Weight.subtract(reduce);
                    flightLoad.setH3Weight(h3Weight);
                    break;
                case "4H":
                    Integer h4Quantity = flightLoad.getH4Quantity() == null ? new Integer(0) : flightLoad.getH4Quantity();
                    h4Quantity -= sum;
                    flightLoad.setH4Quantity(h4Quantity);
                    BigDecimal h4Weight = flightLoad.getH4Weight() == null ? new BigDecimal(0) : flightLoad.getH4Weight();
                    h4Weight = h4Weight.subtract(reduce);
                    flightLoad.setH4Weight(h4Weight);
                    break;
                case "5H":
                    Integer h5Quantity = flightLoad.getH5Quantity() == null ? new Integer(0) : flightLoad.getH5Quantity();
                    h5Quantity -= sum;
                    flightLoad.setH5Quantity(h5Quantity);
                    BigDecimal h5Weight = flightLoad.getH5Weight() == null ? new BigDecimal(0) : flightLoad.getH5Weight();
                    h5Weight = h5Weight.subtract(reduce);
                    flightLoad.setH5Weight(h5Weight);
                    break;
                default:
                    throw new CustomException("无当前舱位信息");
            }
            flightLoadMapper.updateById(flightLoad);
        }
        loadUld.setIsEdit(1);
        List<FlightLoadUldWaybill> loadUldWaybillList = loadUldWaybillMapper.selectList(new QueryWrapper<FlightLoadUldWaybill>().eq("load_uld_id", loadUld.getId()));
        if(loadUldWaybillList.size() > 0){
            loadUldWaybillList.forEach(e->{
                e.setIsEdit(1);
                loadUldWaybillMapper.updateById(e);
            });
        }
        return loadUldMapper.updateById(loadUld);
    }

    /**
     * 放散舱
     *
     * @param query 放散舱参数
     * @return 结果
     */
    @Override
    public ForwardImportVo looseCabin(FlightLoadPackQuery query) {
        HttpServletResponse response = ServletUtils.getResponse();
        ArrayList<WaybillLog> waybillLogs = new ArrayList<>();
        try {
            ForwardImportVo vo = new ForwardImportVo();
            if (!CollectionUtils.isEmpty(query.getFlightLoadWaybillIds())) {
                List<FlightLoadUldWaybill> uldWaybills = loadUldWaybillMapper.selectListByIds(query.getFlightLoadWaybillIds());
                if (!CollectionUtils.isEmpty(uldWaybills)) {
                    for (FlightLoadUldWaybill uldWaybill : uldWaybills) {
                        FlightLoadWaybill waybill = loadWaybillMapper.selectOne(new QueryWrapper<FlightLoadWaybill>()
                                .eq("flight_load_id", query.getFlightLoadId())
                                .eq("waybill_id", uldWaybill.getWaybillId()));
                        FlightLoadUld flightLoadUld = loadUldMapper.selectById(uldWaybill.getLoadUldId());
                        flightLoadUld.setIsEdit(1);
                        loadUldMapper.updateById(flightLoadUld);
//                        updateUldWaybill(flightLoadUld.getId());
                        if (waybill == null) {
                            FlightLoadWaybill loadWaybill = new FlightLoadWaybill();
                            BeanUtils.copyProperties(uldWaybill, loadWaybill);
                            loadWaybill.setFlightLoadId(query.getFlightLoadId());
                            loadWaybill.setId(null);
                            loadWaybill.setIsEdit(1);
                            loadWaybill.setCollectId(uldWaybill.getCollectId());
                            loadWaybillMapper.insert(loadWaybill);
                        } else {
                            int quantity = waybill.getQuantity() + uldWaybill.getQuantity();
                            waybill.setQuantity(quantity);
                            BigDecimal weight = waybill.getWeight().add(uldWaybill.getWeight());
                            waybill.setWeight(weight);
                            waybill.setIsEdit(1);
                            loadWaybillMapper.updateById(waybill);
                        }
                        loadUldWaybillMapper.deleteById(uldWaybill.getId());
                        FlightLoad flightLoad = flightLoadMapper.selectById(flightLoadUld.getFlightLoadId());
                        FlightInfo flightInfo = flightInfoMapper.selectById(flightLoad.getFlightId());
                        //运单日志
                        String waybillCode = airWaybillMapper.selectWaybillCode(uldWaybill.getWaybillId());
                        WaybillLog waybillLog = waybillLogService.getWaybillLog(
                                waybillCode, 0, SecurityUtils.getNickName(),
                                uldWaybill.getWeight().toString(), uldWaybill.getQuantity().toString(), flightInfo.getAirWays() + flightInfo.getFlightNo(),
                                query, "操作成功", 0, null, new Date(),
                                "放散舱件数:" + uldWaybill.getQuantity() + ",重量:" + uldWaybill.getWeight(),
                                "DEP", null);
                        waybillLogs.add(waybillLog);
                    }
                }
            } else {
                List<FlightLoadUldWaybill> loadUldWaybills = loadUldWaybillMapper.selectList(new QueryWrapper<FlightLoadUldWaybill>()
                        .in("load_uld_id", query.getFlightLoadUldIds()));
                for (FlightLoadUldWaybill loadUldWaybill : loadUldWaybills) {
                    FlightLoadWaybill waybill = loadWaybillMapper.selectOne(new QueryWrapper<FlightLoadWaybill>()
                            .eq("flight_load_id", query.getFlightLoadId())
                            .eq("waybill_id", loadUldWaybill.getWaybillId()));
                    if (waybill == null) {
                        FlightLoadWaybill loadWaybill = new FlightLoadWaybill();
                        BeanUtils.copyProperties(loadUldWaybill, loadWaybill);
                        loadWaybill.setFlightLoadId(query.getFlightLoadId());
                        loadWaybill.setId(null);
                        loadWaybill.setIsEdit(1);
                        loadWaybillMapper.insert(loadWaybill);
                    } else {
                        int quantity = waybill.getQuantity() + loadUldWaybill.getQuantity();
                        waybill.setQuantity(quantity);
                        BigDecimal weight = waybill.getWeight().add(loadUldWaybill.getWeight());
                        waybill.setWeight(weight);
                        waybill.setIsEdit(1);
                        loadWaybillMapper.updateById(waybill);
                    }
                    loadUldWaybillMapper.deleteById(loadUldWaybill.getId());

                    //运单日志
                    WaybillLog waybillLog = waybillLogService.getWaybillLog(
                            loadUldWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                            loadUldWaybill.getWeight().toString(), loadUldWaybill.getQuantity().toString(), null,
                            query, "操作成功", 0, null, new Date(),
                            "放散舱件数:" + loadUldWaybill.getQuantity() + ",重量:" + loadUldWaybill.getWeight(),
                            "DEP", null);
                    waybillLogs.add(waybillLog);
                }
                loadUldMapper.deleteBatchIds(query.getFlightLoadUldIds());
            }
            selectVo(query.getFlightLoadId(), vo);
            return vo;
        } catch (Exception e) {
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作失败" + "," +
                                "code:" + response.getStatus()));
                waybillLog.setErrorMsg(e.getMessage());
                waybillLog.setStatus(1);
            }
            throw new CustomException(e.getMessage());
        } finally {
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLogService.insertWaybillLog(waybillLog);
            }
        }
    }

    /**
     * 部分放散舱
     *
     * @param query 部分放散舱参数
     * @return 结果
     */
    @Override
    public ForwardImportVo partLooseCabin(FlightLoadPackQuery query) {
        HttpServletResponse response = ServletUtils.getResponse();
        ArrayList<WaybillLog> waybillLogs = new ArrayList<>();

        if (query.getFlightLoadWaybillIds() == null || query.getFlightLoadWaybillIds().size() > 1) {
            throw new CustomException("只能选择一个运单");
        }
        ForwardImportVo vo = new ForwardImportVo();
        FlightLoadUldWaybill flightLoadUldWaybill = loadUldWaybillMapper.selectById(query.getFlightLoadWaybillIds().get(0));
        String waybillCode = airWaybillMapper.selectWaybillCode(flightLoadUldWaybill.getWaybillId());
        if (query.getQuantity() > flightLoadUldWaybill.getQuantity()) {
            throw new CustomException("放散舱数量超过装配件数");
        }
        if (query.getWeight().compareTo(flightLoadUldWaybill.getWeight()) > 0) {
            throw new CustomException("放散舱重量超过装配件数");
        }
        try {
            FlightLoadUld flightLoadUld = loadUldMapper.selectById(flightLoadUldWaybill.getLoadUldId());
            flightLoadUld.setIsEdit(1);
            loadUldMapper.updateById(flightLoadUld);
            FlightLoadWaybill waybill = loadWaybillMapper.selectOne(new QueryWrapper<FlightLoadWaybill>()
                    .eq("flight_load_id", query.getFlightLoadId())
                    .eq("waybill_id", flightLoadUldWaybill.getWaybillId()));
            if (waybill == null) {
                FlightLoadWaybill loadWaybill = new FlightLoadWaybill();
                BeanUtils.copyProperties(flightLoadUldWaybill, loadWaybill);
                loadWaybill.setQuantity(query.getQuantity());
                loadWaybill.setWeight(query.getWeight());
                loadWaybill.setFlightLoadId(query.getFlightLoadId());
                loadWaybill.setIsEdit(1);
                loadWaybillMapper.insert(loadWaybill);
            } else {
                int quantity = waybill.getQuantity() + query.getQuantity();
                waybill.setQuantity(quantity);
                BigDecimal weight = waybill.getWeight().add(query.getWeight());
                waybill.setWeight(weight);
                waybill.setIsEdit(1);
                loadWaybillMapper.updateById(waybill);
            }
            FlightLoad flightLoad = flightLoadMapper.selectById(flightLoadUld.getFlightLoadId());
            FlightInfo flightInfo = flightInfoMapper.selectById(flightLoad.getFlightId());
            //运单日志
            WaybillLog waybillLog = waybillLogService.getWaybillLog(
                    waybillCode, 0, SecurityUtils.getNickName(),
                    flightLoadUldWaybill.getWeight().toString(), flightLoadUldWaybill.getQuantity().toString(), flightInfo.getAirWays() + flightInfo.getFlightNo(),
                    query, "操作成功", 0, null, new Date(),
                    "部分放散舱件数:" + flightLoadUldWaybill.getQuantity() + ",重量:" + flightLoadUldWaybill.getWeight(),
                    "DEP", null);
            waybillLogs.add(waybillLog);
            int quantity = flightLoadUldWaybill.getQuantity() - query.getQuantity();
            if (quantity <= 0) {
                loadUldWaybillMapper.deleteById(flightLoadUldWaybill.getId());
            } else {
                flightLoadUldWaybill.setQuantity(quantity);
                BigDecimal weight = flightLoadUldWaybill.getWeight().subtract(query.getWeight());
                flightLoadUldWaybill.setWeight(weight);
                flightLoadUldWaybill.setIsEdit(1);
                loadUldWaybillMapper.updateById(flightLoadUldWaybill);
            }
            selectVo(query.getFlightLoadId(), vo);
            return vo;
        } catch (Exception e) {
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作失败" + "," +
                                "code:" + response.getStatus()));
                waybillLog.setErrorMsg(e.getMessage());
                waybillLog.setStatus(1);
            }
            throw new CustomException(e.getMessage());
        } finally {
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLogService.insertWaybillLog(waybillLog);
            }
        }
    }

    /**
     * 航班配载
     *
     * @param id 航班配载id
     * @return 结果
     */
    @Override
    public ForwardImportVo load(Long id) {
        ForwardImportVo vo = new ForwardImportVo();
        selectVo(id, vo);
        return vo;
    }

    /**
     * 新增配置查询
     *
     * @param query 查询条件
     * @return 结果
     */
    @Override
    public ForwardImportVo addQuery(AddQuery query) {
        ForwardImportVo vo = flightLoadMapper.addQuery(query);
        if (StringUtils.isEmpty(query.getLeg())) {
            throw new CustomException("请选择航段");
        }
        if (vo == null) {
            throw new CustomException("无当前航段信息");
        }
        vo.setType(query.getType());
        selectVo(vo.getId(), vo);
        //票数是运单数量 票数要分板箱票数(uldNum)和散舱票数(waybillNum)
        //拿到板箱信息 再拿到板箱下面的运单的信息
        List<FlightLoadUld> flightLoadUldList = loadUldMapper.selectList(new QueryWrapper<FlightLoadUld>()
                .eq("flight_load_id", vo.getId()));
        if (!CollectionUtils.isEmpty(flightLoadUldList)) {
            List<Long> loadUldIds = flightLoadUldList.stream().map(FlightLoadUld::getId).collect(Collectors.toList());
            List<FlightLoadUldWaybill> flightLoadWaybills = loadUldWaybillMapper.selectList(new QueryWrapper<FlightLoadUldWaybill>()
                    .in("load_uld_id", loadUldIds));
            //根据waybill_id去重flightLoadWaybills中的数据
            List<Long> waybillIds = flightLoadWaybills.stream()
                    .map(FlightLoadUldWaybill::getWaybillId).distinct()
                    .collect(Collectors.toList());
            vo.setUldNum(waybillIds.size());
        }
        vo.setWaybillNum(loadWaybillMapper.selectWaybillNum(vo.getId()));
        if (!CollectionUtils.isEmpty(vo.getUldVos())
                && (StringUtils.isNotNull(vo.getUldTotalQuantity()) || StringUtils.isNotNull(vo.getUldTotalWeight()))) {
            Map<String, List<Object>> uldMap = vo.getUldMap();
            if (uldMap == null) {
                uldMap = new HashMap<>();
            }
            //计算每种舱位的总件数和质量
            for (ForwardImportUldVo uldVo : vo.getUldVos()) {
                if (CollectionUtils.isEmpty(uldMap.get(uldVo.getCabin()))) {
                    ArrayList<Object> totalQuantityAndTotalWeight = new ArrayList<>();
                    totalQuantityAndTotalWeight.add(uldVo.getTotalQuantity());
                    totalQuantityAndTotalWeight.add(uldVo.getTotalWeight());
                    totalQuantityAndTotalWeight.add(loadUldMapper.selectWaybillNum(uldVo.getId()));
                    uldMap.put(uldVo.getCabin(), totalQuantityAndTotalWeight);
                } else {
                    List<Object> objects = uldMap.get(uldVo.getCabin());
                    if (StringUtils.isNull(uldVo.getTotalQuantity())) {
                        uldVo.setTotalQuantity(0);
                    }
                    if (StringUtils.isNull(objects.get(0))) {
                        objects.set(0, 0);
                    }
                    int totalQuantity = ((Integer) objects.get(0)) + uldVo.getTotalQuantity();
                    if (StringUtils.isNull(uldVo.getTotalWeight())) {
                        uldVo.setTotalWeight(BigDecimal.ZERO);
                    }
                    if (StringUtils.isNull(objects.get(1))) {
                        objects.set(1, BigDecimal.ZERO);
                    }
                    BigDecimal totalWeight = ((BigDecimal) objects.get(1)).add(uldVo.getTotalWeight());
                    objects.set(0, totalQuantity);
                    objects.set(1, totalWeight);
                    objects.set(2, loadUldMapper.selectWaybillNum(uldVo.getId()));
                }
            }
            //过滤掉空值
            Map<String, List<Object>> cleanMap = uldMap.entrySet().stream()
                    .filter(entry -> entry.getKey() != null)
                    .filter(entry -> !entry.getKey().equals(""))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            ArrayList<Object> List = new ArrayList<>();
            List.add(0);
            List.add(0);
            List.add(0);
            //分割map
            if (vo.getUldMapLeft() == null) {
                //给ConcurrentHashMap赋默认值
                ConcurrentHashMap<String, List<Object>> concurrentHashMapForLeft = new ConcurrentHashMap<>();
                concurrentHashMapForLeft.put("2H", List);
                concurrentHashMapForLeft.put("1H", List);
                vo.setUldMapLeft(concurrentHashMapForLeft);
            }
            if (vo.getUldMapRight() == null) {
                ConcurrentHashMap<String, List<Object>> concurrentHashMapForRight = new ConcurrentHashMap<>();
                concurrentHashMapForRight.put("5H", List);
                concurrentHashMapForRight.put("4H", List);
                concurrentHashMapForRight.put("3H", List);
                vo.setUldMapRight(concurrentHashMapForRight);
            }
            for (String key : cleanMap.keySet()) {
                //使用switch case来判断字符串key的值并进行后续操作
                switch (key) {
                    case "1H":
                        vo.getUldMapLeft().put(key, cleanMap.get(key));
                        break;
                    case "2H":
                        vo.getUldMapLeft().put(key, cleanMap.get(key));
                        break;
                    case "3H":
                        vo.getUldMapRight().put(key, cleanMap.get(key));
                        break;
                    case "4H":
                        vo.getUldMapRight().put(key, cleanMap.get(key));
                        break;
                    case "5H":
                        vo.getUldMapRight().put(key, cleanMap.get(key));
                        break;
                }
            }
            updateFlightLoad(vo.getId(), uldMap);
        }
        return vo;
    }

    /**
     * 更新航班配载表中的时间件数重量等
     *
     * @param id 航班配载表主键  uldMap 数据
     * @return 结果
     */
    public void updateFlightLoad(Long id, Map<String, List<Object>> uldMap) {
        FlightLoad flightLoad = new FlightLoad();
        flightLoad.setId(id);
        if (uldMap.get("1H") != null && uldMap.get("1H").get(0) != null) {
            flightLoad.setH1Quantity(Integer.parseInt(uldMap.get("1H").get(0).toString()));
            flightLoad.setH1Weight(new BigDecimal(uldMap.get("1H").get(1).toString()));
        }
        if (uldMap.get("2H") != null && uldMap.get("2H").get(0) != null) {
            flightLoad.setH2Quantity(Integer.parseInt(uldMap.get("2H").get(0).toString()));
            flightLoad.setH2Weight(new BigDecimal(uldMap.get("2H").get(1).toString()));
        }
        if (uldMap.get("3H") != null && uldMap.get("3H").get(0) != null) {
            flightLoad.setH3Quantity(Integer.parseInt(uldMap.get("3H").get(0).toString()));
            flightLoad.setH3Weight(new BigDecimal(uldMap.get("3H").get(1).toString()));
        }
        if (uldMap.get("4H") != null && uldMap.get("4H").get(0) != null) {
            flightLoad.setH4Quantity(Integer.parseInt(uldMap.get("4H").get(0).toString()));
            flightLoad.setH4Weight(new BigDecimal(uldMap.get("4H").get(1).toString()));
        }
        if (uldMap.get("5H") != null && uldMap.get("5H").get(0) != null) {
            flightLoad.setH5Quantity(Integer.parseInt(uldMap.get("5H").get(0).toString()));
            flightLoad.setH5Weight(new BigDecimal(uldMap.get("5H").get(1).toString()));
        }
        FormalManifestVo vo = flightLoadMapper.selectForMalManifestById(id);
        flightLoad.setLoadTime(vo.getLoadTime() == null ? new Date() : vo.getLoadTime());
        flightLoad.setUpdateTime(new Date());
        flightLoadMapper.updateFlightLoadById(flightLoad);
    }

    /**
     * 航段查询
     *
     * @param query 查询条件
     * @return 航段集合
     */
    @Override
    public LoadFlightVo legQuery(AddQuery query) {
        List<String> list = flightLoadMapper.legQuery(query);
        LoadFlightVo vo = flightInfoMapper.selectLoadVo(query);
        if (vo != null){
            vo.setLegList(list);
        }
        return vo;
    }

    /**
     * 卸下
     *
     * @param query 卸下参数
     * @return 结果
     */
    @Override
    public ForwardImportVo removeByQuery(FlightLoadPackQuery query) {
        ForwardImportVo vo = new ForwardImportVo();
        ArrayList<WaybillLog> waybillLogs = new ArrayList<>();
        HttpServletResponse response = ServletUtils.getResponse();
        FlightLoad flightLoad = flightLoadMapper.selectById(query.getFlightLoadId());
        FlightInfo flightId = flightInfoMapper.selectOne(new QueryWrapper<FlightInfo>()
                .eq("flight_id", flightLoad.getFlightId())
                .last("limit 1"));
        try {
            // 运单卸下
            if ((query.getFlightLoadUldIds() == null && query.getFlightLoadWaybillIds() != null) || query.getFlightLoadUldIds().size() == 0) {
                if (query.getFlightLoadWaybillIds().size() == 1) {
                    Long waybillId = null;
                    if ("L".equals(query.getType())) {
                        FlightLoadWaybill loadWaybill = loadWaybillMapper.selectById(query.getFlightLoadWaybillIds().get(0));

                        waybillId = loadWaybill.getWaybillId();
                        if (query.getQuantity() > loadWaybill.getQuantity()) {
                            throw new CustomException("卸下件数不能超过装配件数");
                        }
                        if (query.getWeight().compareTo(loadWaybill.getWeight()) > 0) {
                            throw new CustomException("卸下重量不能超过装配重量");
                        }
                        int quantity = loadWaybill.getQuantity() - query.getQuantity();
                        BigDecimal weight = loadWaybill.getWeight().subtract(query.getWeight());
                        setBoard(query.getFlightLoadId(), loadWaybill.getWaybillId(), null, query.getQuantity(), query.getWeight());
                        if (quantity <= 0 && weight.compareTo(BigDecimal.ZERO) <= 0) {
                            loadWaybillMapper.deleteById(loadWaybill.getId());
                        } else {
                            loadWaybill.setQuantity(quantity);
                            loadWaybill.setWeight(weight);
                            loadWaybill.setIsEdit(1);
                            loadWaybillMapper.updateById(loadWaybill);
                        }

                        AirWaybill airWaybill = airWaybillMapper.selectById(waybillId);
                        //运单日志的新增
                        WaybillLog waybillLog = waybillLogService.getWaybillLog(
                                airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                                query.getWeight().toString(), query.getQuantity().toString(), flightId.getAirWays() + flightId.getFlightNo(),
                                query, null, 0, null, new Date(),
                                "卸下", "DEP", loadWaybill.getUld()!=null ? loadWaybill.getUld() : null);
                        waybillLogs.add(waybillLog);

                    }
                    if ("R".equals(query.getType())) {
                        FlightLoadUldWaybill flightLoadUldWaybill = loadUldWaybillMapper.selectById(query.getFlightLoadWaybillIds().get(0));
                        waybillId = flightLoadUldWaybill.getWaybillId();
                        if (query.getQuantity() > flightLoadUldWaybill.getQuantity()) {
                            throw new CustomException("卸下件数不能超过装配件数");
                        }
                        if (query.getWeight().compareTo(flightLoadUldWaybill.getWeight()) > 0) {
                            throw new CustomException("卸下重量不能超过装配重量");
                        }
                        int quantity = flightLoadUldWaybill.getQuantity() - query.getQuantity();
                        setBoard(query.getFlightLoadId(), flightLoadUldWaybill.getWaybillId(), null, query.getQuantity(), query.getWeight());
                        BigDecimal weight = flightLoadUldWaybill.getWeight().subtract(query.getWeight());
                        if (quantity <= 0 && weight.compareTo(BigDecimal.ZERO) <= 0) {
                            loadUldWaybillMapper.deleteById(flightLoadUldWaybill.getId());
                        } else {
                            flightLoadUldWaybill.setQuantity(quantity);
                            flightLoadUldWaybill.setWeight(weight);
                            flightLoadUldWaybill.setIsEdit(1);
                            loadUldWaybillMapper.updateById(flightLoadUldWaybill);
                            FlightLoadUld uld = loadUldMapper.selectById(flightLoadUldWaybill.getLoadUldId());
                            uld.setIsEdit(1);
                            loadUldMapper.updateById(uld);
                        }

                        AirWaybill airWaybill = airWaybillMapper.selectById(waybillId);
                        //运单日志的新增
                        WaybillLog waybillLog = waybillLogService.getWaybillLog(
                                airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                                flightLoadUldWaybill.getWeight().toString(), flightLoadUldWaybill.getQuantity().toString(), flightId.getAirWays() + flightId.getFlightNo(),
                                query, null, 0, null, new Date(),
                                "卸下", "DEP", null);
                        waybillLogs.add(waybillLog);

                    }
                    List<Long> ids = loadWaybillMapper.isHaveDate(waybillId, query.getFlightLoadId());
                    if (CollectionUtils.isEmpty(ids)) {
                        AirWaybill airWaybill = airWaybillMapper.selectById(waybillId);
                        airWaybill.setStatus("put_in");
                        airWaybill.setIsLoad(0);
                        airWaybill.setUpdateTime(new Date());
                        airWaybillMapper.updateById(airWaybill);
                    }
                } else {
                    List<Long> waybillIds = new ArrayList<>();
                    if ("L".equals(query.getType())) {
                        for (Long flightLoadWaybillId : query.getFlightLoadWaybillIds()) {
                            FlightLoadWaybill loadWaybill = loadWaybillMapper.selectById(flightLoadWaybillId);
                            waybillIds.add(loadWaybill.getWaybillId());
                            setBoard(query.getFlightLoadId(), loadWaybill.getWaybillId(), null, loadWaybill.getQuantity(), loadWaybill.getWeight());

                            AirWaybill airWaybill = airWaybillMapper.selectById(loadWaybill.getWaybillId());
                            //运单日志的新增
                            WaybillLog waybillLog = waybillLogService.getWaybillLog(
                                    airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                                    loadWaybill.getWeight().toString(), loadWaybill.getQuantity().toString(), flightId.getAirWays() + flightId.getFlightNo(),
                                    query, null, 0, null, new Date(),
                                    "卸下", "DEP", loadWaybill.getUld()!=null ? loadWaybill.getUld() : null);
                            waybillLogs.add(waybillLog);

                        }
                        loadWaybillMapper.deleteBatchIds(query.getFlightLoadWaybillIds());

                    }
                    if ("R".equals(query.getType())) {
                        for (Long flightLoadWaybillId : query.getFlightLoadWaybillIds()) {
                            FlightLoadUldWaybill loadWaybill = loadUldWaybillMapper.selectById(flightLoadWaybillId);
                            waybillIds.add(loadWaybill.getWaybillId());
                            setBoard(query.getFlightLoadId(), loadWaybill.getWaybillId(), null, loadWaybill.getQuantity(), loadWaybill.getWeight());

                            AirWaybill airWaybill = airWaybillMapper.selectById(loadWaybill.getWaybillId());
                            //运单日志的新增
                            WaybillLog waybillLog = waybillLogService.getWaybillLog(
                                    airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                                    loadWaybill.getWeight().toString(), loadWaybill.getQuantity().toString(), flightId.getAirWays() + flightId.getFlightNo(),
                                    query, null, 0, null, new Date(),
                                    "卸下", "DEP", null);
                            waybillLogs.add(waybillLog);

                        }
                        loadUldWaybillMapper.deleteBatchIds(query.getFlightLoadWaybillIds());
                    }
                    if (!CollectionUtils.isEmpty(waybillIds)) {
                        List<Long> collect = waybillIds.stream().distinct().collect(Collectors.toList());
                        for (Long aLong : collect) {
                            List<Long> ids = loadWaybillMapper.isHaveDate(aLong, query.getFlightLoadId());
                            if (CollectionUtils.isEmpty(ids)) {
                                AirWaybill airWaybill = airWaybillMapper.selectById(aLong);
                                airWaybill.setStatus("put_in");
                                airWaybill.setIsLoad(0);
                                airWaybill.setUpdateTime(new Date());
                                airWaybillMapper.updateById(airWaybill);
                            }
                        }
                    }
                }
            }
            // 板箱卸下
            if (query.getFlightLoadWaybillIds() == null && query.getFlightLoadUldIds() != null) {
                List<FlightLoadUldWaybill> loadUldWaybills = loadUldWaybillMapper.selectList(new QueryWrapper<FlightLoadUldWaybill>().in("load_uld_id", query.getFlightLoadUldIds()));
                List<Long> waybillIds = new ArrayList<>();
                for (FlightLoadUldWaybill loadUldWaybill : loadUldWaybills) {
                    FlightLoadUld uld = loadUldMapper.selectById(loadUldWaybill.getLoadUldId());
                    waybillIds.add(loadUldWaybill.getWaybillId());
                    setBoard(query.getFlightLoadId(), loadUldWaybill.getWaybillId(), uld.getUld(), loadUldWaybill.getQuantity(), loadUldWaybill.getWeight());

                    AirWaybill airWaybill = airWaybillMapper.selectById(loadUldWaybill.getWaybillId());
                    //运单日志的新增
                    WaybillLog waybillLog = waybillLogService.getWaybillLog(
                            airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                            loadUldWaybill.getWeight().toString(), loadUldWaybill.getQuantity().toString(), flightId.getAirWays() + flightId.getFlightNo(),
                            query, null, 0, null, new Date(),
                            "板箱卸下", "DEP", null);
                    waybillLogs.add(waybillLog);
                }
                removeUld(loadUldWaybills);
                loadUldMapper.deleteBatchIds(query.getFlightLoadUldIds());
                if (!CollectionUtils.isEmpty(waybillIds)) {
                    List<Long> collect = waybillIds.stream().distinct().collect(Collectors.toList());
                    for (Long aLong : collect) {
                        List<Long> ids = loadWaybillMapper.isHaveDate(aLong, query.getFlightLoadId());
                        if (CollectionUtils.isEmpty(ids)) {
                            AirWaybill airWaybill = airWaybillMapper.selectById(aLong);
                            airWaybill.setStatus("put_in");
                            airWaybill.setIsLoad(0);
                            airWaybill.setUpdateTime(new Date());
                            airWaybillMapper.updateById(airWaybill);
                        }
                    }
                }
            }
            // 板箱和运单卸下
            if (query.getFlightLoadWaybillIds() != null && query.getFlightLoadWaybillIds().size() > 0 && query.getFlightLoadUldIds() != null && query.getFlightLoadUldIds().size() > 0) {
                List<FlightLoadUldWaybill> loadUldWaybills = loadUldWaybillMapper.selectList(new QueryWrapper<FlightLoadUldWaybill>()
                        .in("load_uld_id", query.getFlightLoadUldIds()));
                List<Long> waybillIds = new ArrayList<>();
                for (FlightLoadUldWaybill loadUldWaybill : loadUldWaybills) {
                    FlightLoadUld uld = loadUldMapper.selectById(loadUldWaybill.getLoadUldId());
                    waybillIds.add(loadUldWaybill.getWaybillId());
                    setBoard(query.getFlightLoadId(), loadUldWaybill.getWaybillId(), uld.getUld(), loadUldWaybill.getQuantity(), loadUldWaybill.getWeight());
                }
                removeUld(loadUldWaybills);
                loadUldMapper.deleteBatchIds(query.getFlightLoadUldIds());
                List<FlightLoadUldWaybill> loadUldWaybillList = loadUldWaybillMapper.selectBatchIds(query.getFlightLoadWaybillIds());
                for (FlightLoadUldWaybill flightLoadUldWaybill : loadUldWaybillList) {
                    waybillIds.add(flightLoadUldWaybill.getWaybillId());
                    setBoard(query.getFlightLoadId(), flightLoadUldWaybill.getWaybillId(), null, flightLoadUldWaybill.getQuantity(), flightLoadUldWaybill.getWeight());
                    loadUldWaybillMapper.deleteById(flightLoadUldWaybill.getId());
                }
                if (!CollectionUtils.isEmpty(waybillIds)) {
                    List<Long> collect = waybillIds.stream().distinct().collect(Collectors.toList());
                    for (Long aLong : collect) {
                        List<Long> ids = loadWaybillMapper.isHaveDate(aLong, query.getFlightLoadId());
                        if (CollectionUtils.isEmpty(ids)) {
                            AirWaybill airWaybill = airWaybillMapper.selectById(aLong);
                            airWaybill.setStatus("put_in");
                            airWaybill.setIsLoad(0);
                            airWaybill.setUpdateTime(new Date());
                            airWaybillMapper.updateById(airWaybill);

                            //运单日志的新增
                            WaybillLog waybillLog = waybillLogService.getWaybillLog(
                                    airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                                    airWaybill.getWeight().toString(), airWaybill.getQuantity().toString(), flightId.getAirWays() + flightId.getFlightNo(),
                                    query, null, 0, null, new Date(),
                                    "板箱运单卸下", "DEP", null);
                            waybillLogs.add(waybillLog);

                        }
                    }
                }
            }
            selectVo(query.getFlightLoadId(), vo);
            return vo;
        }catch(Exception e){
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作失败" + "," +
                                "code:" + response.getStatus()));
                waybillLog.setErrorMsg(e.getMessage());
                waybillLog.setStatus(1);
            }
            throw new CustomException(e.getMessage());
        }finally {
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLogService.insertWaybillLog(waybillLog);
            }
        }
    }

    private void setBoard(Long flightLoadId, Long waybillId, String uld, Integer quantity, BigDecimal weight) {
        HzDisBoard board = new HzDisBoard();
        FlightLoad load = flightLoadMapper.selectById(flightLoadId);
        board.setFlightId(load.getFlightId());
        board.setQuantity(quantity);
        board.setWeight(weight);
        board.setUld(uld);
        board.setWaybillId(waybillId);
        board.setOperName(SecurityUtils.getUsername());
        board.setOperTime(new Date());
        boardMapper.insert(board);
    }

    /**
     * 根据运单号查询仓库库位
     *
     * @param waybillCode 运单号
     * @return 仓库库位
     */
    @Override
    public LoadLocatorVo getLocator(String waybillCode) {
        LoadLocatorVo vo = new LoadLocatorVo();
        vo.setWaybillCode(waybillCode);
        List<HzCollectWaybill> collectWaybills = waybillMapper.selectList(new QueryWrapper<HzCollectWaybill>().eq("waybill_code", waybillCode));
        List<Long> collect = collectWaybills.stream().map(HzCollectWaybill::getId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            List<HzCollectWeight> collectWeights = weightMapper.selectList(new QueryWrapper<HzCollectWeight>()
                    .in("collect_id", collect));
            List<String> stores = collectWeights.stream().map(HzCollectWeight::getStore).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(stores)) {
                List<String> storesDistinct = stores.stream().distinct().collect(Collectors.toList());
                vo.setStore(storesDistinct);
            }
            List<String> locators = collectWeights.stream().map(HzCollectWeight::getLocator).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(locators)) {
                List<String> locatorsDistinct = locators.stream().distinct().collect(Collectors.toList());
                vo.setLocator(locatorsDistinct);
            }
        }
        return vo;
    }

    /**
     * 板箱配上
     *
     * @param query 参数
     * @return 结果
     */
    @Override
    public ForwardImportVo uldAdd(UldAddQuery query) {
        ForwardImportVo vo = new ForwardImportVo();
        FlightLoadUld uld = new FlightLoadUld();
        BeanUtils.copyProperties(query, uld);

        List<FlightLoadUld> flightLoadUlds = loadUldMapper.selectList(new QueryWrapper<FlightLoadUld>()
                .eq("uld", query.getUld())
                .eq("flight_load_id", query.getFlightLoadId()));
        if (!CollectionUtils.isEmpty(flightLoadUlds)) {
            throw new CustomException("该航班上已存在相同板箱号");
        }

        loadUldMapper.insert(uld);
        selectVo(query.getFlightLoadId(), vo);
        return vo;
    }

    /**
     * 正式舱单
     *
     * @param id 航班配载id
     * @return 结果
     */
    @Override
    public FormalManifestVo formalManifest(Long id) {
        FormalManifestVo vo = flightLoadMapper.selectForMalManifestById(id);
        String[] split = vo.getLeg().split("-");
        vo.setDesPort(split[split.length - 1]);
        vo.setSourcePort(split[0]);
        List<FormalUldVo> uldList = loadUldMapper.selectListByFlightLoadId(id, null);
        int cargoQuantity = 0;
        int cargoNum = 0;
        BigDecimal cargoWeight = new BigDecimal(0);
        Set<String> cargoNumSets = new HashSet<>();
        Set<String> mailNumSets = new HashSet<>();
        int mailQuantity = 0;
        int mailNum = 0;
        BigDecimal mailWeight = new BigDecimal(0);
        if (!CollectionUtils.isEmpty(uldList)) {
            for (FormalUldVo formalUldVo : uldList) {
                List<FormalWaybillVo> waybillVos = loadUldWaybillMapper.selectListByLoadUldId(formalUldVo.getId(), null);
                if (!CollectionUtils.isEmpty(waybillVos)) {
                    for (FormalWaybillVo waybillVo : waybillVos) {
                        if (waybillVo.getVolume() == null || waybillVo.getVolume().compareTo(new BigDecimal(0)) != 0) {
                            continue;
                        }
                        waybillVo.setVolume(null);
                    }
                    List<FormalWaybillVo> waybillVosSort = sortByWaybillType(waybillVos);
                    formalUldVo.setVos(waybillVosSort);
                    formalUldVo.setQuantity(
                            waybillVos.stream().mapToInt(FormalWaybillVo::getQuantity).sum());
                    formalUldVo.setWeight(
                            waybillVos.stream().map(FormalWaybillVo::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add)
                    );
                    List<FormalWaybillVo> awba = waybillVos.stream().filter(e -> e.getWaybillCode().contains("AWBA")).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(awba)) {
                        for(FormalWaybillVo e:awba){
                            if(cargoNumSets.add(e.getWaybillCode())){
                                cargoNum++;
                            }
                        }
//                        cargoNum = cargoNum + awba.size();
                        int sum = awba.stream().mapToInt(FormalWaybillVo::getQuantity).sum();
                        cargoQuantity = cargoQuantity + sum;
                        BigDecimal reduce = awba.stream().map(FormalWaybillVo::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                        cargoWeight = cargoWeight.add(reduce);
                    }
                    List<FormalWaybillVo> awbm = waybillVos.stream().filter(e -> e.getWaybillCode().contains("AWBM")).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(awbm)) {
                        for(FormalWaybillVo e:awbm){
                            if(mailNumSets.add(e.getWaybillCode())){
                                mailNum++;
                            }
                        }
//                        mailNum = mailNum + awbm.size();
                        int sum = awbm.stream().mapToInt(FormalWaybillVo::getQuantity).sum();
                        mailQuantity = mailQuantity + sum;
                        BigDecimal reduce = awbm.stream().map(FormalWaybillVo::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                        mailWeight = mailWeight.add(reduce);
                    }
                    waybillVos.forEach(e->{
                        if (e.getWaybillCode().contains("DN")){
                            //邮件单 品名回显取货品代码
                            e.setWaybillCode(e.getWaybillCode().substring(0,14));
                            if(e.getCategoryName()!=null){
                                e.setCargoName(e.getCategoryName().equals("08")?"普邮":
                                        e.getCategoryName().equals("12")?"经邮":
                                                e.getCategoryName().equals("01")?"特快":
                                                        e.getCargoName());
                            }
                        }else if(e.getWaybillCode().endsWith("B")) {
                            e.setWaybillCode(e.getWaybillCode().substring(0,16));
                        }else{
                            e.setWaybillCode(e.getWaybillCode().substring(0,15));
                        }
                    });
                }

            }
            //按照舱位进行排序
            List<FormalUldVo> uldVosSorted = sortByCabin2(uldList);
            vo.setVos(uldVosSorted);
            vo.setUldUnm(uldList.size());
        }
        List<FormalWaybillVo> waybillVos = loadWaybillMapper.selectListByFlightLoadId(id, null);
        if (!CollectionUtils.isEmpty(waybillVos)) {
            List<FormalWaybillVo> awba = waybillVos.stream().filter(e -> e.getWaybillCode().contains("AWBA")).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(awba)) {
                for(FormalWaybillVo e:awba){
                    if(cargoNumSets.add(e.getWaybillCode())){
                        cargoNum++;
                    }
                }
//                cargoNum = cargoNum + awba.size();
                int sum = awba.stream().mapToInt(FormalWaybillVo::getQuantity).sum();
                cargoQuantity = cargoQuantity + sum;
                BigDecimal reduce = awba.stream().map(FormalWaybillVo::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                cargoWeight = cargoWeight.add(reduce);
            }
            List<FormalWaybillVo> awbm = waybillVos.stream().filter(e -> e.getWaybillCode().contains("AWBM")).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(awbm)) {
                for(FormalWaybillVo e:awbm){
                    if(mailNumSets.add(e.getWaybillCode())){
                        mailNum++;
                    }
                }
//                mailNum = mailNum + awbm.size();
                int sum = awbm.stream().mapToInt(FormalWaybillVo::getQuantity).sum();
                mailQuantity = mailQuantity + sum;
                BigDecimal reduce = awbm.stream().map(FormalWaybillVo::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                mailWeight = mailWeight.add(reduce);
            }
            waybillVos.forEach(e->{
                if (e.getWaybillCode().contains("DN")){
                    //邮件单 品名回显取货品代码
                    e.setWaybillCode(e.getWaybillCode().substring(0,14));
                    if(e.getCategoryName()!=null){
                        e.setCargoName(e.getCategoryName().equals("08")?"普邮":
                                e.getCategoryName().equals("12")?"经邮":
                                        e.getCategoryName().equals("01")?"特快":
                                                e.getCargoName());
                    }
                }else if(e.getWaybillCode().endsWith("B")) {
                    e.setWaybillCode(e.getWaybillCode().substring(0,16));
                }else{
                    e.setWaybillCode(e.getWaybillCode().substring(0,15));
                }
            });
            List<FormalWaybillVo> waybillVosSort = sortByWaybillType(waybillVos);
            vo.setWaybillVos(waybillVosSort);
        }
        vo.setCargoNum(cargoNum);
        vo.setCargoQuantity(cargoQuantity);
        vo.setCargoWeight(cargoWeight);
        vo.setMailNum(mailNum);
        vo.setMailQuantity(mailQuantity);
        vo.setMailWeight(mailWeight);
        vo.setTotalNum(cargoNum + mailNum);
        vo.setTotalWeight(cargoWeight.add(mailWeight));
        vo.setTotalQuantity(cargoQuantity + mailQuantity);
        if (StringUtils.isNull(vo.getLoadTime())) {
            vo.setLoadTime(new Date());
        }
        if (StringUtils.isNull(vo.getLoadUser())) {
            SysUser sysUser = sysUserMapper.selectUserById(SecurityUtils.getUserId());
            vo.setLoadUser(sysUser.getNickName());
        }


        FlightLoad load = flightLoadMapper.selectById(id);
        FlightInfo info = flightInfoMapper.selectById(load.getFlightId());
        String sendAddress = configMapper.selectValue("dep.sendAddress");

        HttpServletResponse response = ServletUtils.getResponse();
        ArrayList<WaybillLog> waybillLogs = new ArrayList<>();
        try {
            syncFuChongData(id, info, waybillLogs, sendAddress);
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作成功" + "," +
                                "code:" + response.getStatus() + "," +
                                "data:" + 0));
            }
        } catch (Exception e) {
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作失败" + "," +
                                "code:" + response.getStatus()));
                waybillLog.setErrorMsg(e.getMessage());
                waybillLog.setStatus(1);
            }
        } finally {
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLogService.insertWaybillLog(waybillLog);
            }
        }

        return vo;
    }

    /**
     * 对正式舱单的数据进行排序 主单在前邮件单在后
     * */
    public List<FormalWaybillVo> sortByWaybillType(List<FormalWaybillVo> waybillVos){
        waybillVos.sort(new Comparator<FormalWaybillVo>() {
            @Override
            public int compare(FormalWaybillVo o1, FormalWaybillVo o2) {
                int index1 = getIndex(o1.getWaybillCode());
                int index2 = getIndex(o2.getWaybillCode());
                return Integer.compare(index1, index2);
            }

            private int getIndex(String waybillCode) {
                if (waybillCode.contains("AWBA")) {
                    return 0; // AWBA排在最前面
                } else if (waybillCode.equals("AWBM")) {
                    return 1; // AWBM排在AWBA之后
                } else {
                    return 2; // 其他字符串排在最后
                }
            }
        });
        return waybillVos;
    }

    /**
     * 通过id查询正式舱单数据
     * */
    @Override
    public FormalManifestVo formalManifestById(FormalManifestIds ids) {
        Long id = ids.getId();
        List<Long> uldIds = new ArrayList<>();
        if(ids.getUldIds()!=null && !"".equals(ids.getUldIds())){
            uldIds =  Arrays.stream(ids.getUldIds().split(","))
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
        }
        List<Long> uldWaybillIds = new ArrayList<>();
        if(ids.getUldWaybillIds() != null && !"".equals(ids.getUldWaybillIds())){
            uldWaybillIds = Arrays.stream(ids.getUldWaybillIds().split(","))
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
        }
        List<Long> waybillIds = new ArrayList<>();
        if(ids.getWaybillIds() != null && !"".equals(ids.getWaybillIds())){
            waybillIds = Arrays.stream(ids.getWaybillIds().split(","))
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
        }
        FormalManifestVo vo = flightLoadMapper.selectForMalManifestById(id);
        String[] split = vo.getLeg().split("-");
        vo.setDesPort(split[split.length - 1]);
        vo.setSourcePort(split[0]);
        int cargoQuantity = 0;
        int cargoNum = 0;
        //12.24 相同运单号的货物算作一票
        Set<String> cargoNumSets = new HashSet<>();
        Set<String> mailNumSets = new HashSet<>();
        BigDecimal cargoWeight = new BigDecimal(0);
        int mailQuantity = 0;
        int mailNum = 0;
        BigDecimal mailWeight = new BigDecimal(0);
        List<Long> uldIdsOld = new ArrayList<>();
        //如果没选板箱 只选了板箱运单 需要根据板箱运单id去找到板箱id
        if(!CollectionUtils.isEmpty(uldWaybillIds)){
            List<FlightLoadUldWaybill> loadUldWaybillList = loadUldWaybillMapper.selectList(new QueryWrapper<FlightLoadUldWaybill>()
                    .in("id", uldWaybillIds));
            List<Long> uldIdsNew = loadUldWaybillList.stream().map(FlightLoadUldWaybill::getLoadUldId).collect(Collectors.toList());
            //如果只勾选了板箱运单 没勾选板箱 需要去掉多余的板箱运单id(这个old装的就是只勾选了板箱运单没有勾选板箱的板箱id集合)
            uldIdsOld = uldIdsNew;
            if (!CollectionUtils.isEmpty(uldIdsNew)){
                uldIdsOld.removeAll(uldIds);
                uldIds.addAll(uldIdsNew);
                uldIds = uldIds.stream().distinct().collect(Collectors.toList());
            }
        }
        if(!CollectionUtils.isEmpty(uldIds)){
            //将板箱对应的所有板箱运单id加入板箱运单id集合(这里要去掉只勾选了板箱运单 没有勾选板箱的板箱运单id)
            List<FlightLoadUldWaybill> loadUldWaybillList= loadUldWaybillMapper.selectList(new QueryWrapper<FlightLoadUldWaybill>()
                    .in("load_uld_id", uldIds));
            if(!CollectionUtils.isEmpty(uldIdsOld)){
                List<FlightLoadUldWaybill> loadUldWaybillListOld= loadUldWaybillMapper.selectList(new QueryWrapper<FlightLoadUldWaybill>()
                        .in("load_uld_id", uldIdsOld));
                loadUldWaybillList.removeAll(loadUldWaybillListOld);
            }
            List<Long> uldWaybillIdsNew = loadUldWaybillList.stream().map(FlightLoadUldWaybill::getId).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(uldWaybillIds)){
                uldWaybillIds.addAll(uldWaybillIdsNew);
                uldWaybillIds = uldWaybillIds.stream().distinct().collect(Collectors.toList());
            }
            List<FormalUldVo> uldList = loadUldMapper.selectListByFlightLoadIdAndUldIds(id,uldIds);
            if (!CollectionUtils.isEmpty(uldList)) {
                for (FormalUldVo formalUldVo : uldList) {
                    List<FormalWaybillVo> waybillVos = loadUldWaybillMapper.selectListByLoadUldIdAndUldWaybillId(formalUldVo.getId(), uldWaybillIds);
                    if (!CollectionUtils.isEmpty(waybillVos)) {
                        for (FormalWaybillVo waybillVo : waybillVos) {
                            if (waybillVo.getVolume() == null || waybillVo.getVolume().compareTo(new BigDecimal(0)) != 0) {
                                continue;
                            }
                            waybillVo.setVolume(null);
                        }
                        //2.5换单也不需要在最后加上原单号 去掉在备注后面加上原单号的功能
                        waybillVos.forEach(e->{
                            if (e.getWaybillCode().contains("DN")){
                                //邮件单 品名回显取货品代码
                                if(e.getCategoryName()!=null){
                                    e.setCargoName(e.getCategoryName().equals("08")?"普邮":
                                            e.getCategoryName().equals("12")?"经邮":
                                                    e.getCategoryName().equals("01")?"特快":
                                                            e.getCargoName());
                                }
                                e.setWaybillCode(e.getWaybillCode().substring(0,14));
                            }else if(e.getWaybillCode().endsWith("B")) {
                                e.setWaybillCode(e.getWaybillCode().substring(0,16));
                            }else{
                                e.setWaybillCode(e.getWaybillCode().substring(0,15));
                            }
                        });
                        List<FormalWaybillVo> waybillVosSort = sortByWaybillType(waybillVos);
                        formalUldVo.setVos(waybillVosSort);
                        formalUldVo.setQuantity(
                                waybillVos.stream().mapToInt(FormalWaybillVo::getQuantity).sum());
                        formalUldVo.setWeight(
                                waybillVos.stream().map(FormalWaybillVo::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add)
                        );

                        List<FormalWaybillVo> awba = waybillVos.stream().filter(e -> e.getWaybillCode().contains("AWBA")).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(awba)) {
                            for(FormalWaybillVo e:awba){
                                if(cargoNumSets.add(e.getWaybillCode())){
                                    cargoNum++;
                                }
                            }
//                            cargoNum = cargoNum + awba.size();
                            int sum = awba.stream().mapToInt(FormalWaybillVo::getQuantity).sum();
                            cargoQuantity = cargoQuantity + sum;
                            BigDecimal reduce = awba.stream().map(FormalWaybillVo::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                            cargoWeight = cargoWeight.add(reduce);
                        }
                        List<FormalWaybillVo> awbm = waybillVos.stream().filter(e -> e.getWaybillCode().contains("AWBM")).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(awbm)) {
                            for(FormalWaybillVo e:awbm){
                                if(mailNumSets.add(e.getWaybillCode())){
                                    mailNum++;
                                }
                            }
//                            mailNum = mailNum + awbm.size();
                            int sum = awbm.stream().mapToInt(FormalWaybillVo::getQuantity).sum();
                            mailQuantity = mailQuantity + sum;
                            BigDecimal reduce = awbm.stream().map(FormalWaybillVo::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                            mailWeight = mailWeight.add(reduce);
                        }
                    }

                }
                //按照舱位进行排序
                List<FormalUldVo> uldVosSorted = sortByCabin2(uldList);
                vo.setVos(uldVosSorted);
                vo.setUldUnm(uldList.size());
            }
        }
        if(!CollectionUtils.isEmpty(waybillIds)){
            List<FormalWaybillVo> waybillVos = loadWaybillMapper.selectListByFlightLoadIdAndWaybillId(id, waybillIds);
            if (!CollectionUtils.isEmpty(waybillVos)) {
                List<FormalWaybillVo> awba = waybillVos.stream().filter(e -> e.getWaybillCode().contains("AWBA")).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(awba)) {
                    for(FormalWaybillVo e:awba){
                        if(cargoNumSets.add(e.getWaybillCode())){
                            cargoNum++;
                        }
                    }
//                    cargoNum = cargoNum + awba.size();
                    int sum = awba.stream().mapToInt(FormalWaybillVo::getQuantity).sum();
                    cargoQuantity = cargoQuantity + sum;
                    BigDecimal reduce = awba.stream().map(FormalWaybillVo::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                    cargoWeight = cargoWeight.add(reduce);
                }
                List<FormalWaybillVo> awbm = waybillVos.stream().filter(e -> e.getWaybillCode().contains("AWBM")).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(awbm)) {
                    for(FormalWaybillVo e:awbm){
                        if(mailNumSets.add(e.getWaybillCode())){
                            mailNum++;
                        }
                    }
//                    mailNum = mailNum + awbm.size();
                    int sum = awbm.stream().mapToInt(FormalWaybillVo::getQuantity).sum();
                    mailQuantity = mailQuantity + sum;
                    BigDecimal reduce = awbm.stream().map(FormalWaybillVo::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                    mailWeight = mailWeight.add(reduce);
                }
                waybillVos.forEach(e->{
                    if (e.getWaybillCode().contains("DN")){
                        //邮件单 品名回显取货品代码
                        e.setWaybillCode(e.getWaybillCode().substring(0,14));
                        if(e.getCategoryName()!=null){
                            e.setCargoName(e.getCategoryName().equals("08")?"普邮":
                                    e.getCategoryName().equals("12")?"经邮":
                                            e.getCategoryName().equals("01")?"特快":
                                                    e.getCargoName());
                        }
                    }else if(e.getWaybillCode().endsWith("B")) {
                        e.setWaybillCode(e.getWaybillCode().substring(0,16));
                    }else{
                        e.setWaybillCode(e.getWaybillCode().substring(0,15));
                    }
                });
                List<FormalWaybillVo> waybillVosSort = sortByWaybillType(waybillVos);
                vo.setWaybillVos(waybillVosSort);
            }
        }
        vo.setCargoNum(cargoNum);
        vo.setCargoQuantity(cargoQuantity);
        vo.setCargoWeight(cargoWeight);
        vo.setMailNum(mailNum);
        vo.setMailQuantity(mailQuantity);
        vo.setMailWeight(mailWeight);
        vo.setTotalNum(cargoNum + mailNum);
        vo.setTotalWeight(cargoWeight.add(mailWeight));
        vo.setTotalQuantity(cargoQuantity + mailQuantity);
        if (StringUtils.isNull(vo.getLoadTime())) {
            vo.setLoadTime(new Date());
        }
        if (StringUtils.isNull(vo.getLoadUser())) {
            SysUser sysUser = sysUserMapper.selectUserById(SecurityUtils.getUserId());
            vo.setLoadUser(sysUser.getNickName());
        }
        return vo;
    }

    /**
     * 舱单打印
     *
     * @param id 航班配载id
     * @return 结果
     */
    @Override
    public void printManifest(Long id) throws Exception {
        Integer count = groupUldMapper.selectCount(new QueryWrapper<HzDepGroupUld>()
                .eq("flight_load_id", id));
        Integer count1 = groupWaybillMapper.selectCount(new QueryWrapper<HzDepGroupWaybill>()
                .eq("flight_load_id", id));
        FlightLoad load = flightLoadMapper.selectById(id);
        FlightInfo info = flightInfoMapper.selectById(load.getFlightId());
        if (info.getIsComp() == 1){
            throw new CustomException("当前航班已完成");
        }
        if (count > 0 || count1 > 0) {
            if (info.getIsCreate() == 1) {
                flightInfoMapper.closeCreate(info.getFlightId());
            }
            //非首次舱单打印 需要去组货/复重删除数据 再有后面流程新增 --->这一步改到点击正式舱单
            printManifestAgain(id);
        }
        ArrayList<WaybillLog> waybillLogs = new ArrayList<>();
        HttpServletResponse response = ServletUtils.getResponse();
        try {
            String sendAddress = configMapper.selectValue("dep.sendAddress");
            List<FormalUldVo> uldList = loadUldMapper.selectListByFlightLoadId(id, null);
            String token = getToken();
            if (!CollectionUtils.isEmpty(uldList)) {
                for (FormalUldVo formalUldVo : uldList) {
                    //舱单打印后 将板车状态改为0 成为旧数据
                    FlightLoadUld flightLoadUld = loadUldMapper.selectById(formalUldVo.getId());
                    flightLoadUld.setIsEdit(0);
                    loadUldMapper.updateById(flightLoadUld);
                    //新增组货的板箱数据
                    HzDepGroupUld group = new HzDepGroupUld();
                    List<FormalWaybillVo> waybillVos = loadUldWaybillMapper.selectListByLoadUldId(formalUldVo.getId(), null);
                    if (!CollectionUtils.isEmpty(waybillVos)) {
                        waybillVos.forEach(e -> {
                            //舱单打印后 将板车上运单状态改为0 成为旧数据
                            FlightLoadUldWaybill flightLoadUldWaybill = loadUldWaybillMapper.selectById(e.getId());
                            flightLoadUldWaybill.setIsEdit(0);
                            loadUldWaybillMapper.updateById(flightLoadUldWaybill);
                        });
                        group.setDes1(waybillVos.get(0).getDesPort());
                    }
                    group.setFlightLoadId(id);
                    group.setCabin(formalUldVo.getCabin());
                    group.setUld(formalUldVo.getWaybillCode());
                    groupUldMapper.insert(group);

                    //修改板箱/集装器状态
                    String uld = formalUldVo.getWaybillCode();
                    if (StringUtils.isNotNull(uld)) {
                        if ("CAR".equals(uld.substring(0, 3))) {
                            BaseFlatbedTruck baseFlatbedTruck = truckMapper.selectByCode(uld.substring(3));
                            if (StringUtils.isNotNull(baseFlatbedTruck)) {
                                baseFlatbedTruck.setLcStatus("been_group");
                                truckMapper.updateById(baseFlatbedTruck);
                            }
                        } else {
                            BaseCargoUld baseCargoUld = uldMapper.selectByCode(uld);
                            if (StringUtils.isNotNull(baseCargoUld)) {
                                baseCargoUld.setStatus("been_group");
                                uldMapper.updateById(baseCargoUld);
                            }
                        }
                    }

                    if (!CollectionUtils.isEmpty(waybillVos)) {
                        for (FormalWaybillVo waybillVo : waybillVos) {
                            AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                                    .eq("waybill_code", waybillVo.getWaybillCode())
                                    .eq("type", "DEP"));
                            airWaybill.setStatus("been_pre");
                            airWaybill.setUpdateTime(new Date());
                            airWaybillMapper.updateById(airWaybill);

                            //运单日志的新增
                            WaybillLog waybillLog = waybillLogService.getWaybillLog(
                                    airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                                    airWaybill.getWeight().toString(), airWaybill.getQuantity().toString(), info.getAirWays() + info.getFlightNo(),
                                    id.toString(), null, 0, null, new Date(),
                                    "配载，航班:" + info.getAirWays() + info.getFlightNo() + ",件数:" + waybillVo.getQuantity() + ",重量:" + waybillVo.getWeight() + ",ULD号:" + group.getUld(),
                                    airWaybill.getType(), group.getUld());
                            waybillLogs.add(waybillLog);

                            //新增运单追踪数据
                            WaybillTrace waybillTrace = new WaybillTrace();
                            waybillTrace.setOperTime(new Date());
                            waybillTrace.setOperPieces(waybillVo.getQuantity());
                            waybillTrace.setOperWeight(waybillVo.getWeight());
                            waybillTrace.setWaybillCode(airWaybill.getWaybillCode());
                            waybillTrace.setFlightNo(info.getFlightNo());
                            waybillTrace.setPlanTakeoffTime(info.getStartSchemeTakeoffTime());
                            waybillTrace.setNodeName("已预配");
                            traceService.insertWaybillTrace(waybillTrace);

                            //新增板箱上面的运单数据
                            HzDepGroupUldWaybill waybill = new HzDepGroupUldWaybill();
                            waybill.setGroupUldId(group.getId());
                            waybill.setQuantity(waybillVo.getQuantity());
                            waybill.setWeight(waybillVo.getWeight());
                            waybill.setDesPort(waybillVo.getDesPort());
                            waybill.setWaybillCode(waybillVo.getWaybillCode());
                            waybill.setType(waybillVo.getWaybillCode().contains("AWBA") ? "货物" : "邮件");
                            groupUldWaybillMapper.insert(waybill);
                            try{
                                sendRCSMsg(info, sendAddress, waybillVo,token);
                            }catch (Exception e){
                                log.error(CABLE_ERROR_MARKER, "运单"+waybillVo.getWaybillCode()+"发送FSU-RCS报文失败：" + e.getMessage());
                            }
                        }
                    }
                }
                //新增复重数据
                List<HzDepGroupUld> groupUlds = groupUldMapper.selectList(new QueryWrapper<HzDepGroupUld>()
                        .eq("flight_load_id", id));
                List<HzDepRepeatWeight> repeatWeightList = new ArrayList<>();
                for (HzDepGroupUld groupUld : groupUlds) {
                    //用新组货数据找旧复重数据 将匹配到的复重数据都留下 然后删除所有旧复重数据
                    RepeatWeightQuery query = new RepeatWeightQuery();
                    BeanUtils.copyProperties(groupUld, query);
                    HzDepRepeatWeight repeatWeight = repeatWeightMapper.selectDataByQuery(query);
                    List<HzDepGroupUldWaybill> groupUldWaybills = groupUldWaybillMapper.selectList(new QueryWrapper<HzDepGroupUldWaybill>()
                            .eq("group_uld_id", groupUld.getId()));
                    int quantity = 0;
                    BigDecimal weight = BigDecimal.ZERO;
                    if (!CollectionUtils.isEmpty(groupUldWaybills)){
                        quantity = groupUldWaybills.stream().mapToInt(HzDepGroupUldWaybill::getQuantity).sum();
                        weight = groupUldWaybills.stream().map(HzDepGroupUldWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    String nickName = SecurityUtils.getNickName();
                    if (repeatWeight != null) {
                        repeatWeight.setQuantity(quantity);
                        repeatWeight.setFileWeight(weight);
                        repeatWeight.setGroupUldId(groupUld.getId());
                        repeatWeight.setOperName(nickName);
                        repeatWeightList.add(repeatWeight);
                    } else {
                        HzDepRepeatWeight hzDepRepeatWeight = new HzDepRepeatWeight();
                        hzDepRepeatWeight.setFlightLoadId(id);
                        hzDepRepeatWeight.setUld(groupUld.getUld());
                        hzDepRepeatWeight.setDes1(groupUld.getDes1());
                        hzDepRepeatWeight.setCabin(groupUld.getCabin());
                        hzDepRepeatWeight.setGroupUldId(groupUld.getId());
                        hzDepRepeatWeight.setQuantity(quantity);
                        hzDepRepeatWeight.setFileWeight(weight);
                        hzDepRepeatWeight.setOperName(nickName);
                        repeatWeightList.add(hzDepRepeatWeight);
                    }
                }
                List<HzDepRepeatWeight> repeatWeightListForDelete = repeatWeightMapper.selectList(new QueryWrapper<HzDepRepeatWeight>()
                        .eq("flight_load_id", id));
                if (repeatWeightListForDelete.size() > 0) {
                    List<Long> collect = repeatWeightListForDelete.stream().map(HzDepRepeatWeight::getId).collect(Collectors.toList());
                    repeatWeightMapper.deleteBatchIds(collect);
                }
                repeatWeightList.forEach(e -> {
                    repeatWeightMapper.insert(e);
                });
            }
            List<FormalWaybillVo> waybillVos = loadWaybillMapper.selectListByFlightLoadId(id, 1);
            for (FormalWaybillVo waybillVo : waybillVos) {
                //第一次舱单打印 将散舱上的运单的状态改为0
                FlightLoadWaybill flightLoadWaybill = loadWaybillMapper.selectById(waybillVo.getId());
                flightLoadWaybill.setIsEdit(0);
                loadWaybillMapper.updateById(flightLoadWaybill);

                AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                        .eq("waybill_code", waybillVo.getWaybillCode())
                        .eq("type","DEP"));
                airWaybill.setStatus("been_pre");
                airWaybill.setUpdateTime(new Date());
                airWaybillMapper.updateById(airWaybill);

                //运单日志的新增
                WaybillLog waybillLog = waybillLogService.getWaybillLog(
                        airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                        airWaybill.getWeight().toString(), airWaybill.getQuantity().toString(), info.getAirWays() + info.getFlightNo(),
                        id.toString(), null, 0, null, new Date(),
                        "配载，航班:" + info.getAirWays() + info.getFlightNo() + ",件数:" + waybillVo.getQuantity() + ",重量:" + waybillVo.getWeight() + ",ULD号:" + null,
                        airWaybill.getType(), null);
                waybillLogs.add(waybillLog);


                WaybillTrace waybillTrace = new WaybillTrace();
                waybillTrace.setOperTime(new Date());
                waybillTrace.setOperPieces(waybillVo.getQuantity());
                waybillTrace.setOperWeight(waybillVo.getWeight());
                waybillTrace.setWaybillCode(airWaybill.getWaybillCode());
                waybillTrace.setFlightNo(info.getFlightNo());
                waybillTrace.setPlanTakeoffTime(info.getStartSchemeTakeoffTime());
                waybillTrace.setNodeName("已预配");
                traceService.insertWaybillTrace(waybillTrace);

                //这里是放散舱的运单数据
                HzDepGroupWaybill waybill = new HzDepGroupWaybill();
                waybill.setFlightLoadId(id);
                waybill.setQuantity(waybillVo.getQuantity());
                waybill.setWeight(waybillVo.getWeight());
                waybill.setDesPort(waybillVo.getDesPort());
                waybill.setWaybillCode(waybillVo.getWaybillCode());
                waybill.setType(waybillVo.getWaybillCode().contains("AWBA") ? "货物" : "邮件");
                groupWaybillMapper.insert(waybill);
                try{
                    sendRCSMsg(info, sendAddress, waybillVo,token);
                }catch (Exception e){
                    log.error("运单"+waybillVo.getWaybillCode()+"发送报文失败：",e);
                }
            }
            FlightLoad flightLoad = flightLoadMapper.selectById(id);
            //航班已离港/起飞则不修改状态为已配载
            if(!("been_dep").equals(flightLoad.getState())){
                flightLoad.setState("been_pre");
            }
            flightLoad.setLoadTime(new Date());
            SysUser sysUser = sysUserMapper.selectUserById(SecurityUtils.getUserId());
            flightLoad.setLoadUser(sysUser.getNickName());
            flightLoad.setIsPre(1);
            flightInfoMapper.updatePre(info.getFlightId());
            int i = flightLoadMapper.updateById(flightLoad);
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作成功" + "," +
                                "code:" + response.getStatus() + "," +
                                "data:" + i));
            }

        } catch (Exception e) {
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作失败" + "," +
                                "code:" + response.getStatus()));
                waybillLog.setErrorMsg(e.getMessage());
                waybillLog.setStatus(1);
            }
        } finally {
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLogService.insertWaybillLog(waybillLog);
            }
        }
    }

    /**
     * 正式舱单时同步复重相关数据
     * @param id
     * @param info
     * @param waybillLogs
     * @param sendAddress
     */
    private void syncFuChongData(Long id, FlightInfo info, ArrayList<WaybillLog> waybillLogs, String sendAddress) {
        List<FormalUldVo> uldList = loadUldMapper.selectListByFlightLoadId(id, null);
        if (CollectionUtils.isEmpty(uldList)) {
            return;
        }
        for (FormalUldVo formalUldVo : uldList) {
            //舱单打印后 将板车状态改为0 成为旧数据
//            FlightLoadUld flightLoadUld = loadUldMapper.selectById(formalUldVo.getId());
//            flightLoadUld.setIsEdit(0);
//            loadUldMapper.updateById(flightLoadUld);
            //新增组货的板箱数据
            HzDepGroupUld group = new HzDepGroupUld();
            List<FormalWaybillVo> waybillVos = loadUldWaybillMapper.selectListByLoadUldId(formalUldVo.getId(), null);
            if (!CollectionUtils.isEmpty(waybillVos)) {
//                waybillVos.forEach(e -> {
//                    //舱单打印后 将板车上运单状态改为0 成为旧数据
//                    FlightLoadUldWaybill flightLoadUldWaybill = loadUldWaybillMapper.selectById(e.getId());
//                    flightLoadUldWaybill.setIsEdit(0);
//                    loadUldWaybillMapper.updateById(flightLoadUldWaybill);
//                });
                group.setDes1(waybillVos.get(0).getDesPort());
            }
            group.setFlightLoadId(id);
            group.setCabin(formalUldVo.getCabin());
            group.setUld(formalUldVo.getWaybillCode());
            LambdaQueryWrapper<HzDepGroupUld> eqULD = Wrappers.<HzDepGroupUld>lambdaQuery()
                    .eq(HzDepGroupUld::getFlightLoadId, id)
                    .eq(HzDepGroupUld::getUld, formalUldVo.getWaybillCode());
            HzDepGroupUld hzDepGroupUldOld = groupUldMapper.selectOne(eqULD);
            if(ObjectUtil.isNotNull(hzDepGroupUldOld)){
                groupUldMapper.update(group, eqULD);
                group.setId(hzDepGroupUldOld.getId());
            }else{
                groupUldMapper.insert(group);
            }

            //修改板箱/集装器状态
            String uld = formalUldVo.getWaybillCode();
            if (StringUtils.isNotNull(uld)) {
                if ("CAR".equals(uld.substring(0, 3))) {
                    BaseFlatbedTruck baseFlatbedTruck = truckMapper.selectByCode(uld.substring(3));
                    if (StringUtils.isNotNull(baseFlatbedTruck)) {
                        baseFlatbedTruck.setLcStatus("been_group");
                        truckMapper.updateById(baseFlatbedTruck);
                    }
                } else {
                    BaseCargoUld baseCargoUld = uldMapper.selectByCode(uld);
                    if (StringUtils.isNotNull(baseCargoUld)) {
                        baseCargoUld.setStatus("been_group");
                        uldMapper.updateById(baseCargoUld);
                    }
                }
            }

            if (!CollectionUtils.isEmpty(waybillVos)) {
                String token = getToken();
                for (FormalWaybillVo waybillVo : waybillVos) {
                    AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                            .eq("waybill_code", waybillVo.getWaybillCode())
                            .eq("type", "DEP"));
                    airWaybill.setStatus("been_pre");
                    airWaybill.setUpdateTime(new Date());
                    airWaybillMapper.updateById(airWaybill);

                    //运单日志的新增
                    WaybillLog waybillLog = waybillLogService.getWaybillLog(
                            airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                            airWaybill.getWeight().toString(), airWaybill.getQuantity().toString(), info.getAirWays() + info.getFlightNo(),
                            id.toString(), null, 0, null, new Date(),
                            "配载，航班:" + info.getAirWays() + info.getFlightNo() + ",件数:" + waybillVo.getQuantity() + ",重量:" + waybillVo.getWeight() + ",ULD号:" + group.getUld(),
                            airWaybill.getType(), group.getUld());
                    waybillLogs.add(waybillLog);

                    //新增运单追踪数据
                    WaybillTrace waybillTrace = new WaybillTrace();
                    waybillTrace.setOperTime(new Date());
                    waybillTrace.setOperPieces(waybillVo.getQuantity());
                    waybillTrace.setOperWeight(waybillVo.getWeight());
                    waybillTrace.setWaybillCode(airWaybill.getWaybillCode());
                    waybillTrace.setFlightNo(info.getFlightNo());
                    waybillTrace.setPlanTakeoffTime(info.getStartSchemeTakeoffTime());
                    waybillTrace.setNodeName("已预配");
                    traceService.insertWaybillTrace(waybillTrace);

                    //新增板箱上面的运单数据
                    insertULDWaybill(waybillVo, group);
                    try {
                        sendRCSMsg(info, sendAddress, waybillVo,token);
                    }catch (Exception e){
                        log.error("运单"+airWaybill.getWaybillCode()+"发送报文失败：",e);
                    }
                }
            }
        }
        //新增复重数据
        addRepeatWeightData(id);
    }

    private void insertULDWaybill(FormalWaybillVo waybillVo, HzDepGroupUld group) {
        HzDepGroupUldWaybill waybill = new HzDepGroupUldWaybill();
        waybill.setGroupUldId(group.getId());
        waybill.setQuantity(waybillVo.getQuantity());
        waybill.setWeight(waybillVo.getWeight());
        waybill.setDesPort(waybillVo.getDesPort());
        waybill.setWaybillCode(waybillVo.getWaybillCode());
        waybill.setType(waybillVo.getWaybillCode().contains("AWBA") ? "货物" : "邮件");

        //查询是否已存在
        LambdaQueryWrapper<HzDepGroupUldWaybill> lqw = Wrappers.<HzDepGroupUldWaybill>lambdaQuery()
                .eq(HzDepGroupUldWaybill::getGroupUldId, waybill.getGroupUldId())
                .eq(HzDepGroupUldWaybill::getWaybillCode, waybill.getWaybillCode());
        HzDepGroupUldWaybill hzDepGroupUldOld = groupUldWaybillMapper.selectOne(lqw);
        if(ObjectUtil.isNotNull(hzDepGroupUldOld)){
            groupUldWaybillMapper.update(waybill, lqw);
        }else{
            groupUldWaybillMapper.insert(waybill);
        }
    }

    /**
     * 新增复重数据
     *
     * @param id flight_load_id
     */
    private void addRepeatWeightData(Long id) {
        List<HzDepGroupUld> groupUlds = groupUldMapper.selectList(new QueryWrapper<HzDepGroupUld>()
                .eq("flight_load_id", id));
        List<HzDepRepeatWeight> repeatWeightList = new ArrayList<>();
        for (HzDepGroupUld groupUld : groupUlds) {
            //用新组货数据找旧复重数据 将匹配到的复重数据都留下 然后删除所有旧复重数据
            RepeatWeightQuery query = new RepeatWeightQuery();
            BeanUtils.copyProperties(groupUld, query);
            HzDepRepeatWeight repeatWeight = repeatWeightMapper.selectDataByQuery(query);
            List<HzDepGroupUldWaybill> groupUldWaybills = groupUldWaybillMapper.selectList(new QueryWrapper<HzDepGroupUldWaybill>()
                    .eq("group_uld_id", groupUld.getId()));
            int quantity = 0;
            BigDecimal weight = BigDecimal.ZERO;
            if (!CollectionUtils.isEmpty(groupUldWaybills)) {
                quantity = groupUldWaybills.stream().mapToInt(HzDepGroupUldWaybill::getQuantity).sum();
                weight = groupUldWaybills.stream().map(HzDepGroupUldWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            String nickName = SecurityUtils.getNickName();
            if (repeatWeight != null) {
                repeatWeight.setQuantity(quantity);
                repeatWeight.setFileWeight(weight);
                repeatWeight.setGroupUldId(groupUld.getId());
                repeatWeight.setOperName(nickName);
                repeatWeightList.add(repeatWeight);
            } else {
                HzDepRepeatWeight hzDepRepeatWeight = new HzDepRepeatWeight();
                hzDepRepeatWeight.setFlightLoadId(id);
                hzDepRepeatWeight.setUld(groupUld.getUld());
                hzDepRepeatWeight.setDes1(groupUld.getDes1());
                hzDepRepeatWeight.setCabin(groupUld.getCabin());
                hzDepRepeatWeight.setGroupUldId(groupUld.getId());
                hzDepRepeatWeight.setQuantity(quantity);
                hzDepRepeatWeight.setFileWeight(weight);
                hzDepRepeatWeight.setOperName(nickName);
                repeatWeightList.add(hzDepRepeatWeight);
            }
        }
        List<HzDepRepeatWeight> repeatWeightListForDelete = repeatWeightMapper.selectList(new QueryWrapper<HzDepRepeatWeight>()
                .eq("flight_load_id", id));
        if (repeatWeightListForDelete.size() > 0) {
            List<Long> collect = repeatWeightListForDelete.stream().map(HzDepRepeatWeight::getId).collect(Collectors.toList());
            repeatWeightMapper.deleteBatchIds(collect);
        }
        repeatWeightList.forEach(e -> {
            repeatWeightMapper.insert(e);
        });
        log.info("新增复重数据");
    }

    private void sendRCSMsg(FlightInfo info, String sendAddress,FormalWaybillVo waybillVo,String token) {
        MsgJsonVO msgJsonVO = setMsgVo(info, sendAddress, "RCS");
        List<HzCableAddress> addressList = cableAddressMapper.selectAddressByAirportCode(Collections.singletonList(waybillVo.getDesPort()));
        StringBuilder sb = new StringBuilder();
        FSUJsonVO fsuJsonVO = new FSUJsonVO();
        fsuJsonVO.setDepAirport(waybillVo.getSourcePort());
        fsuJsonVO.setDesAirport(waybillVo.getDesPort());
        if(waybillVo.getWaybillCode().startsWith("AWBA")){
            String substring = waybillVo.getWaybillCode().substring(4);
            StringBuilder stringBuilder = new StringBuilder(substring);
            fsuJsonVO.setMawbNo(stringBuilder.insert(3, "-").toString());
        }else{
            String substring = waybillVo.getWaybillCode().substring(4);
            StringBuilder stringBuilder = new StringBuilder(substring);
            fsuJsonVO.setMawbNo(stringBuilder.insert(2, "-").toString());
        }
        fsuJsonVO.setPieces(waybillVo.getQuantity().toString());
        if (!waybillVo.getWaybillQuantity().equals(waybillVo.getQuantity())){
            fsuJsonVO.setShipmentDescriptionCode("P");
            fsuJsonVO.setTotalPieces(waybillVo.getWaybillQuantity().toString());
        }else {
            fsuJsonVO.setShipmentDescriptionCode("T");
        }
        List<StatusDetails> statusDetails = new ArrayList<>();
        StatusDetails statusDetail = new StatusDetails();
        statusDetail.setMovementAirport(info.getStartStation());
        statusDetail.setMovementDate(LocalDateTime.now().format(DATE_FORMATTER));
        statusDetail.setMovementTime(LocalDateTime.now().format(TIME_FORMATTER));
        statusDetail.setShipmentDescriptionCode("T");
        statusDetail.setPieces(waybillVo.getQuantity().toString());
        statusDetail.setStatusCode("RCS");
        statusDetail.setWeight(waybillVo.getWeight().toString());
        statusDetail.setWeightUnit("K");
        statusDetails.add(statusDetail);
        fsuJsonVO.setStatusDetails(statusDetails);
        fsuJsonVO.setWeight(waybillVo.getWeight().toString());
        fsuJsonVO.setWeightUnit("K");
        msgJsonVO.setMsgJson(JSON.toJSONString(fsuJsonVO));
        Map<String, List<HzCableAddress>> addressMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(addressList)){
            addressMap = addressList.stream().collect(Collectors.groupingBy(HzCableAddress::getAirportCode));
        }
        List<HzCableAddress> cableAddresses = addressMap.get(waybillVo.getDesPort());
        setInteractionType(cableAddresses,msgJsonVO);
        restExchange(msgJsonVO,token,sb);
        insertAndSendMsg(msgJsonVO, info.getAirWays() + info.getFlightNo(), info.getExecDate(),sb.toString(), token);
    }

    /**
     * 非首次舱单打印的操作 需要将组货复重的数据清空 让后续再次新增
     *
     * @param id 航班配载id
     * @return 结果
     */
    private void printManifestAgain(Long id) {
        List<FormalUldVo> uldList = loadUldMapper.selectListByFlightLoadId(id, null);
        if (!CollectionUtils.isEmpty(uldList)) {
            for (FormalUldVo f : uldList) {
                HzDepGroupUld groupUld = groupUldMapper.selectOne(new QueryWrapper<HzDepGroupUld>()
                        .eq("flight_load_id", id)
                        .eq("uld", f.getWaybillCode()));
                if (groupUld != null) {
                    List<HzDepGroupUldWaybill> groupUldWaybillList = groupUldWaybillMapper.selectList(new QueryWrapper<HzDepGroupUldWaybill>()
                            .eq("group_uld_id", groupUld.getId()));
                    if (groupUldWaybillList.size() > 0) {
                        List<Long> collectByGroupUldWaybill = groupUldWaybillList.stream().map(HzDepGroupUldWaybill::getId).collect(Collectors.toList());
                        groupUldWaybillMapper.deleteBatchIds(collectByGroupUldWaybill);
                    }
                    groupUldMapper.deleteById(groupUld.getId());
                }
            }
            List<FormalWaybillVo> waybillVos = loadWaybillMapper.selectListByFlightLoadId(id, null);
            for (FormalWaybillVo waybillVo : waybillVos) {
                HzDepGroupWaybill groupWaybill = groupWaybillMapper.selectOne(new QueryWrapper<HzDepGroupWaybill>()
                        .eq("flight_load_id", id)
                        .eq("waybill_code", waybillVo.getWaybillCode()));
                if (groupWaybill != null) {
                    groupWaybillMapper.deleteById(groupWaybill.getId());
                }
            }
        }
        //配载删除的板箱组货也清除一下
        List<HzDepGroupUld> groupUldList = groupUldMapper.selectList(new QueryWrapper<HzDepGroupUld>().eq("flight_load_id", id));
        List<FlightLoadUld> flightLoadList = loadUldMapper.selectList(new QueryWrapper<FlightLoadUld>().eq("flight_load_id", id));
        List<String> collect = flightLoadList.stream().map(FlightLoadUld::getUld).collect(Collectors.toList());
        for(HzDepGroupUld groupUld : groupUldList){
            if(!collect.contains(groupUld.getUld())){
                groupUldMapper.deleteById(groupUld.getId());
                List<HzDepGroupUldWaybill> groupUldWaybillList = groupUldWaybillMapper.selectList(new QueryWrapper<HzDepGroupUldWaybill>()
                        .eq("group_uld_id", groupUld.getId()));
                if (groupUldWaybillList.size() > 0) {
                    List<Long> collectByGroupUldWaybill = groupUldWaybillList.stream().map(HzDepGroupUldWaybill::getId).collect(Collectors.toList());
                    groupUldWaybillMapper.deleteBatchIds(collectByGroupUldWaybill);
                }
            }
        }

    }

    /**
     * 拉下
     *
     * @param query 拉下参数
     * @return 结果
     */
    @Override
    public ForwardImportVo pullDown(FlightLoadPackQuery query) {
        ForwardImportVo vo = new ForwardImportVo();
        ArrayList<WaybillLog> waybillLogs = new ArrayList<>();
        HttpServletResponse response = ServletUtils.getResponse();
        try {
            FlightLoad load = flightLoadMapper.selectById(query.getFlightLoadId());
            FlightInfo flightId = flightInfoMapper.selectOne(new QueryWrapper<FlightInfo>()
                    .eq("flight_id", load.getFlightId())
                    .last("limit 1"));
            String sendAddress = configMapper.selectValue("dep.sendAddress");
            MsgJsonVO msgJsonVO = setMsgVo(flightId, sendAddress,"DIS");
            int cabinQuantity = 0;
            BigDecimal cabinWeight = BigDecimal.ZERO;
            String token = getToken();
            // 运单拉下
            if (CollectionUtils.isEmpty(query.getPullDownUlds()) && !CollectionUtils.isEmpty(query.getPullDownWaybills())) {
                if (query.getPullDownWaybills().size() == 1) {
                    Long waybillId = null;
                    int loadQuantity = 0;
                    BigDecimal loadWeight = new BigDecimal(0);
                    if ("L".equals(query.getType())) {
                        PullDownWaybill pullDownWaybill = query.getPullDownWaybills().get(0);
                        FlightLoadWaybill loadWaybill = loadWaybillMapper.selectById(pullDownWaybill.getFlightLoadWaybillId());
                        waybillId = loadWaybill.getWaybillId();
                        AirWaybill airWaybill = airWaybillMapper.selectById(waybillId);
                        if (query.getQuantity() > loadWaybill.getQuantity()) {
                            throw new CustomException("拉下件数不能超过当前配载件数");
                        }
                        if (query.getWeight().compareTo(loadWaybill.getWeight()) > 0) {
                            throw new CustomException("拉下重量不能超过当前配载重量");
                        }
                        int quantity = loadWaybill.getQuantity() - query.getQuantity();
                        List<FlightLoadUld> loadUlds = loadUldMapper.selectList(new QueryWrapper<FlightLoadUld>().eq("flight_load_id", load.getId()));
                        List<Long> collect = loadUlds.stream().map(FlightLoadUld::getId).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(collect)) {
                            List<FlightLoadUldWaybill> loadUldWaybills = loadUldWaybillMapper.selectList(new QueryWrapper<FlightLoadUldWaybill>()
                                    .eq("waybill_id", waybillId).in("load_uld_id", collect));
                            if (!CollectionUtils.isEmpty(loadUldWaybills)) {
                                int quantity1 = loadUldWaybills.stream().mapToInt(FlightLoadUldWaybill::getQuantity).sum();
                                loadQuantity = loadQuantity + quantity1;
                                BigDecimal weight = loadUldWaybills.stream().map(FlightLoadUldWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                                loadWeight = loadWeight.add(weight);
                            }
                        }
                        List<FlightLoadWaybill> waybills = loadWaybillMapper.selectList(new QueryWrapper<FlightLoadWaybill>()
                                .eq("waybill_id", waybillId).in("flight_load_id", load.getId()));
                        if (!CollectionUtils.isEmpty(waybills)) {
                            int quantity2 = waybills.stream().mapToInt(FlightLoadWaybill::getQuantity).sum();
                            loadQuantity = loadQuantity + quantity2;
                            BigDecimal weight = waybills.stream().map(FlightLoadWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                            loadWeight = loadWeight.add(weight);
                        }
                        List<WaybillTrace> waybillTraces = traceService.getBaseMapper().selectList(new QueryWrapper<WaybillTrace>()
                                .eq("waybill_code", airWaybill.getWaybillCode()).eq("node_index", 8));
                        BigDecimal weight = loadWaybill.getWeight().subtract(query.getWeight());
                        if (quantity <= 0 && weight.compareTo(BigDecimal.ZERO) <= 0) {
                            loadWaybillMapper.deleteById(loadWaybill.getId());
                            for (WaybillTrace waybillTrace : waybillTraces) {
                                waybillTrace.setOperPieces(0);
                                waybillTrace.setOperWeight(BigDecimal.ZERO);
                                traceService.updateById(waybillTrace);
                            }
                            airWaybill.setIsLoad(0);
                        } else {
                            loadWaybill.setQuantity(quantity);
                            loadWaybill.setWeight(weight);
                            loadWaybill.setIsEdit(1);
                            loadWaybillMapper.updateById(loadWaybill);
                            for (WaybillTrace waybillTrace : waybillTraces) {
                                waybillTrace.setOperPieces(quantity);
                                waybillTrace.setOperWeight(weight);
                                traceService.updateById(waybillTrace);
                            }
                        }

                        WaybillTrace waybillTrace = new WaybillTrace();
                        waybillTrace.setOperTime(new Date());
                        waybillTrace.setOperPieces(query.getQuantity());
                        waybillTrace.setOperWeight(query.getWeight());
                        waybillTrace.setWaybillCode(airWaybill.getWaybillCode());
                        waybillTrace.setNodeName("临时拉下");
                        traceService.insertWaybillTrace(waybillTrace);
                        Long aLong = pullDownTrace(load.getFlightId(), airWaybill.getWaybillCode(), loadQuantity, query.getQuantity(), loadWeight, query.getWeight(), null, query.getRemark());
                        Wrong wrong = new Wrong();
                        wrong.setDeptId(airWaybill.getDeptId());
                        wrong.setWrongType("OFLD 拉货");
                        wrong.setWaybillCode(airWaybill.getWaybillCode());
                        wrong.setStatus(1);
                        wrong.setProMethod("0");
                        wrong.setType("DEP");
                        wrong.setCreateTime(new Date());
                        wrong.setRegisterTime(new Date());
                        wrong.setAgent(airWaybill.getShipper() != null ? airWaybill.getShipper() : airWaybill.getAgentCompany());
                        wrong.setPullId(aLong);
                        wrongMapper.insert(wrong);
                        try {
                            setFSUMsg(airWaybill,flightId,query.getQuantity(),msgJsonVO,token);
                        }catch (Exception e){
                            log.error(CABLE_ERROR_MARKER,"运单"+airWaybill.getWaybillCode()+"发送FSU-DIS报文失败：" + e.getMessage());
                        }
                        // 修改运单状态
                        airWaybill.setStatus("pull_down");
                        airWaybill.setUpdateTime(new Date());
                        airWaybillMapper.updateById(airWaybill);
                        //发送消息
                        sendMessage(airWaybill.getWaybillCode(), "OFLD 拉货", airWaybill.getAgentCompany());
                        //运单日志的新增
                        WaybillLog waybillLog = waybillLogService.getWaybillLog(
                                airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                                query.getWeight().toString(), query.getQuantity().toString(), flightId.getAirWays() + flightId.getFlightNo(),
                                query, null, 0, null, new Date(),
                                "拉下", "DEP", loadWaybill.getUld()!=null ? loadWaybill.getUld() : null);
                        waybillLogs.add(waybillLog);

                    }
                    if ("R".equals(query.getType())) {
                        PullDownWaybill pullDownWaybill = query.getPullDownWaybills().get(0);
                        FlightLoadUldWaybill flightLoadUldWaybill = loadUldWaybillMapper.selectById(pullDownWaybill.getFlightLoadWaybillId());
                        waybillId = flightLoadUldWaybill.getWaybillId();
                        AirWaybill airWaybill = airWaybillMapper.selectById(waybillId);
                        if (query.getQuantity() > flightLoadUldWaybill.getQuantity()) {
                            throw new CustomException("拉下件数不能超过当前配载件数");
                        }
                        if (query.getWeight().compareTo(flightLoadUldWaybill.getWeight()) > 0) {
                            throw new CustomException("拉下重量不能超过当前配载重量");
                        }
                        List<FlightLoadUld> loadUlds = loadUldMapper.selectList(new QueryWrapper<FlightLoadUld>().eq("flight_load_id", load.getId()));
                        List<Long> collect = loadUlds.stream().map(FlightLoadUld::getId).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(collect)) {
                            List<FlightLoadUldWaybill> loadUldWaybills = loadUldWaybillMapper.selectList(new QueryWrapper<FlightLoadUldWaybill>()
                                    .eq("waybill_id", waybillId).in("load_uld_id", collect));
                            if (!CollectionUtils.isEmpty(loadUldWaybills)) {
                                int quantit1 = loadUldWaybills.stream().mapToInt(FlightLoadUldWaybill::getQuantity).sum();
                                loadQuantity = loadQuantity + quantit1;
                                BigDecimal weight = loadUldWaybills.stream().map(FlightLoadUldWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                                loadWeight = loadWeight.add(weight);
                            }

                        }
                        List<FlightLoadWaybill> waybills = loadWaybillMapper.selectList(new QueryWrapper<FlightLoadWaybill>()
                                .eq("waybill_id", waybillId).in("flight_load_id", load.getId()));
                        if (!CollectionUtils.isEmpty(waybills)) {
                            int quantity2 = waybills.stream().mapToInt(FlightLoadWaybill::getQuantity).sum();
                            loadQuantity = loadQuantity + quantity2;
                            BigDecimal weight = waybills.stream().map(FlightLoadWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                            loadWeight = loadWeight.add(weight);
                        }
                        int quantity = flightLoadUldWaybill.getQuantity() - query.getQuantity();
                        BigDecimal weight = flightLoadUldWaybill.getWeight().subtract(query.getWeight());
                        List<WaybillTrace> waybillTraces = traceService.getBaseMapper().selectList(new QueryWrapper<WaybillTrace>()
                                .eq("waybill_code", airWaybill.getWaybillCode()).eq("node_index", 8));
                        if (quantity <= 0 && weight.compareTo(BigDecimal.ZERO) <= 0) {
                            loadUldWaybillMapper.deleteById(flightLoadUldWaybill.getId());
                            for (WaybillTrace trace : waybillTraces) {
                                trace.setOperPieces(0);
                                trace.setOperWeight(BigDecimal.ZERO);
                                traceService.updateById(trace);
                            }
                            airWaybill.setIsLoad(0);
                        } else {
                            flightLoadUldWaybill.setQuantity(quantity);
                            flightLoadUldWaybill.setWeight(weight);
                            flightLoadUldWaybill.setIsEdit(1);
                            loadUldWaybillMapper.updateById(flightLoadUldWaybill);
                            for (WaybillTrace trace : waybillTraces) {
                                trace.setOperPieces(quantity);
                                trace.setOperWeight(weight);
                                traceService.updateById(trace);
                            }
                        }
                        WaybillTrace waybillTrace = new WaybillTrace();
                        waybillTrace.setOperTime(new Date());
                        waybillTrace.setOperPieces(query.getQuantity());
                        waybillTrace.setOperWeight(query.getWeight());
                        waybillTrace.setWaybillCode(airWaybill.getWaybillCode());
                        waybillTrace.setNodeName("临时拉下");
                        traceService.insertWaybillTrace(waybillTrace);
                        Long aLong = pullDownTrace(load.getFlightId(), airWaybill.getWaybillCode(), loadQuantity, query.getQuantity(), loadWeight, query.getWeight(), null, query.getRemark());
                        Wrong wrong = new Wrong();
                        wrong.setDeptId(airWaybill.getDeptId());
                        wrong.setWrongType("OFLD 拉货");
                        wrong.setWaybillCode(airWaybill.getWaybillCode());
                        wrong.setStatus(1);
                        wrong.setProMethod("0");
                        wrong.setType("DEP");
                        wrong.setCreateTime(new Date());
                        wrong.setRegisterTime(new Date());
                        wrong.setAgent(airWaybill.getShipper() != null ? airWaybill.getShipper() : airWaybill.getAgentCompany());
                        wrong.setPullId(aLong);
                        wrongMapper.insert(wrong);
                        try {
                            setFSUMsg(airWaybill,flightId,query.getQuantity(),msgJsonVO,token);
                        }catch (Exception e){
                            log.error(CABLE_ERROR_MARKER,"运单"+airWaybill.getWaybillCode()+"发送FSU-DIS报文失败：" + e.getMessage());
                        }
                        // 修改运单状态
                        airWaybill.setStatus("pull_down");
                        airWaybill.setUpdateTime(new Date());
                        airWaybillMapper.updateById(airWaybill);
                        //发送消息
                        sendMessage(airWaybill.getWaybillCode(), "OFLD 拉货", airWaybill.getAgentCompany());
                        //运单日志的新增
                        WaybillLog waybillLog = waybillLogService.getWaybillLog(
                                airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                                query.getWeight().toString(), query.getQuantity().toString(), flightId.getAirWays() + flightId.getFlightNo(),
                                query, null, 0, null, new Date(),
                                "拉下", "DEP", null);
                        waybillLogs.add(waybillLog);
                        updateFlightLoad(load, pullDownWaybill.getCabin(), query.getQuantity(), query.getWeight());
                    }
                } else {
                    List<Long> waybillIdList = query.getPullDownWaybills().stream().map(PullDownWaybill::getFlightLoadWaybillId).collect(Collectors.toList());
                    if ("L".equals(query.getType())) {
                        List<FlightLoadWaybill> waybills = loadWaybillMapper.selectListByIds(waybillIdList);
                        for (FlightLoadWaybill waybill : waybills) {
                            AirWaybill airWaybill = airWaybillMapper.selectById(waybill.getWaybillId());
                            // 运单跟踪数据
                            WaybillTrace waybillTrace = new WaybillTrace();
                            waybillTrace.setOperTime(new Date());
                            waybillTrace.setOperPieces(airWaybill.getQuantity());
                            waybillTrace.setOperWeight(airWaybill.getWeight());
                            waybillTrace.setWaybillCode(airWaybill.getWaybillCode());
                            waybillTrace.setNodeName("临时拉下");
                            traceService.insertWaybillTrace(waybillTrace);
                            List<WaybillTrace> waybillTraces = traceService.getBaseMapper().selectList(new QueryWrapper<WaybillTrace>()
                                    .eq("waybill_code", airWaybill.getWaybillCode()).eq("node_index", 8));
                            for (WaybillTrace trace : waybillTraces) {
                                trace.setOperPieces(0);
                                trace.setOperWeight(BigDecimal.ZERO);
                                traceService.updateById(trace);
                            }
                            Long aLong = pullDownTrace(load.getFlightId(), airWaybill.getWaybillCode(), waybill.getQuantity(), waybill.getQuantity(), waybill.getWeight(), waybill.getWeight(), null, query.getRemark());
                            Wrong wrong = new Wrong();
                            wrong.setDeptId(airWaybill.getDeptId());
                            wrong.setWrongType("OFLD 拉货");
                            wrong.setWaybillCode(waybill.getWaybillCode());
                            wrong.setType("DEP");
                            wrong.setStatus(1);
                            wrong.setProMethod("0");
                            wrong.setCreateTime(new Date());
                            wrong.setRegisterTime(new Date());
                            wrong.setAgent(airWaybill.getShipper());
                            wrong.setPullId(aLong);
                            wrongMapper.insert(wrong);
                            try {
                                setFSUMsg(airWaybill,flightId,waybill.getQuantity(),msgJsonVO,token);
                            }catch (Exception e){
                                log.error(CABLE_ERROR_MARKER,"运单"+airWaybill.getWaybillCode()+"发送FSU-DIS报文失败：" + e.getMessage());
                            }
                            // 修改运单状态
                            airWaybill.setIsLoad(0);
                            airWaybill.setStatus("pull_down");
                            airWaybill.setUpdateTime(new Date());
                            airWaybillMapper.updateById(airWaybill);
                            //发送消息
                            sendMessage(airWaybill.getWaybillCode(), "OFLD 拉货", airWaybill.getAgentCompany());

                            //运单日志的新增
                            WaybillLog waybillLog = waybillLogService.getWaybillLog(
                                    airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                                    waybill.getWeight().toString(), waybill.getQuantity().toString(), flightId.getAirWays() + flightId.getFlightNo(),
                                    query, null, 0, null, new Date(),
                                    "拉下", "DEP", null);
                            waybillLogs.add(waybillLog);
                        }
                        loadWaybillMapper.deleteBatchIds(waybillIdList);
                    }
                    if ("R".equals(query.getType())) {
                        for (PullDownWaybill pullDownWaybill : query.getPullDownWaybills()) {
                            FlightLoadUldWaybill waybill = loadUldWaybillMapper.selectById(pullDownWaybill.getFlightLoadWaybillId());
                            AirWaybill airWaybill = airWaybillMapper.selectById(waybill.getWaybillId());
                            // 运单跟踪数据
                            WaybillTrace waybillTrace = new WaybillTrace();
                            waybillTrace.setOperTime(new Date());
                            waybillTrace.setOperPieces(airWaybill.getQuantity());
                            waybillTrace.setOperWeight(airWaybill.getWeight());
                            waybillTrace.setWaybillCode(airWaybill.getWaybillCode());
                            waybillTrace.setNodeName("临时拉下");
                            traceService.insertWaybillTrace(waybillTrace);
                            List<WaybillTrace> waybillTraces = traceService.getBaseMapper().selectList(new QueryWrapper<WaybillTrace>()
                                    .eq("waybill_code", airWaybill.getWaybillCode()).eq("node_index", 8));
                            for (WaybillTrace trace : waybillTraces) {
                                trace.setOperPieces(0);
                                trace.setOperWeight(BigDecimal.ZERO);
                                traceService.updateById(trace);
                            }
                            Long aLong = pullDownTrace(load.getFlightId(), airWaybill.getWaybillCode(), waybill.getQuantity(), waybill.getQuantity(), waybill.getWeight(), waybill.getWeight(), null, query.getRemark());
                            Wrong wrong = new Wrong();
                            wrong.setDeptId(airWaybill.getDeptId());
                            wrong.setWrongType("OFLD 拉货");
                            wrong.setWaybillCode(airWaybill.getWaybillCode());
                            wrong.setType("DEP");
                            wrong.setStatus(1);
                            wrong.setProMethod("0");
                            wrong.setCreateTime(new Date());
                            wrong.setRegisterTime(new Date());
                            wrong.setAgent(airWaybill.getShipper());
                            wrong.setPullId(aLong);
                            wrongMapper.insert(wrong);
                            try {
                                setFSUMsg(airWaybill,flightId,waybill.getQuantity(),msgJsonVO,token);
                            }catch (Exception e){
                                log.error(CABLE_ERROR_MARKER,"运单"+airWaybill.getWaybillCode()+"发送FSU-DIS报文失败：" + e.getMessage());
                            }
                            // 修改运单状态
                            airWaybill.setIsLoad(0);
                            airWaybill.setStatus("pull_down");
                            airWaybill.setUpdateTime(new Date());
                            airWaybillMapper.updateById(airWaybill);
                            //发送消息
                            sendMessage(airWaybill.getWaybillCode(), "OFLD 拉货", airWaybill.getAgentCompany());
                            //运单日志的新增
                            WaybillLog waybillLog = waybillLogService.getWaybillLog(
                                    airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                                    waybill.getWeight().toString(), waybill.getQuantity().toString(), flightId.getAirWays() + flightId.getFlightNo(),
                                    query, null, 0, null, new Date(),
                                    "拉下", "DEP", null);
                            waybillLogs.add(waybillLog);
                            updateFlightLoad(load, pullDownWaybill.getCabin(), waybill.getQuantity(), waybill.getWeight());
                        }
                        loadUldWaybillMapper.deleteBatchIds(waybillIdList);
                    }
                }
            }
            // 板箱拉下
            if (CollectionUtils.isEmpty(query.getPullDownWaybills()) && !CollectionUtils.isEmpty(query.getPullDownUlds())) {
                for (PullDownUld pullDownUld : query.getPullDownUlds()) {
                    List<FlightLoadUldWaybill> loadUldWaybills = loadUldWaybillMapper.selectList(new QueryWrapper<FlightLoadUldWaybill>()
                            .eq("load_uld_id", pullDownUld.getFlightLoadUldId()));
                    for (FlightLoadUldWaybill loadUldWaybill : loadUldWaybills) {
                        FlightLoadUld uld = loadUldMapper.selectById(loadUldWaybill.getLoadUldId());
                        AirWaybill airWaybill = airWaybillMapper.selectById(loadUldWaybill.getWaybillId());
                        WaybillTrace waybillTrace = new WaybillTrace();
                        waybillTrace.setOperTime(new Date());
                        waybillTrace.setOperPieces(airWaybill.getQuantity());
                        waybillTrace.setOperWeight(airWaybill.getWeight());
                        waybillTrace.setWaybillCode(airWaybill.getWaybillCode());
                        waybillTrace.setNodeName("临时拉下");
                        traceService.insertWaybillTrace(waybillTrace);
                        List<WaybillTrace> waybillTraces = traceService.getBaseMapper().selectList(new QueryWrapper<WaybillTrace>()
                                .eq("waybill_code", airWaybill.getWaybillCode()).eq("node_index", 8));
                        for (WaybillTrace trace : waybillTraces) {
                            trace.setOperPieces(0);
                            trace.setOperWeight(BigDecimal.ZERO);
                            traceService.updateById(trace);
                        }
                        Long aLong = pullDownTrace(load.getFlightId(), airWaybill.getWaybillCode(), loadUldWaybill.getQuantity(), loadUldWaybill.getQuantity(), loadUldWaybill.getWeight(), loadUldWaybill.getWeight(), uld.getUld(), query.getRemark());
                        Wrong wrong = new Wrong();
                        wrong.setDeptId(airWaybill.getDeptId());
                        wrong.setWrongType("OFLD 拉货");
                        wrong.setWaybillCode(airWaybill.getWaybillCode());
                        wrong.setType("DEP");
                        wrong.setStatus(1);
                        wrong.setProMethod("0");
                        wrong.setCreateTime(new Date());
                        wrong.setRegisterTime(new Date());
                        wrong.setAgent(airWaybill.getShipper());
                        wrong.setPullId(aLong);
                        wrongMapper.insert(wrong);
                        try {
                            setFSUMsg(airWaybill,flightId,loadUldWaybill.getQuantity(),msgJsonVO,token);
                        }catch (Exception e){
                            log.error(CABLE_ERROR_MARKER,"运单"+airWaybill.getWaybillCode()+"发送FSU-DIS报文失败：" + e.getMessage());
                        }
                        // 修改运单状态
                        airWaybill.setIsLoad(0);
                        airWaybill.setStatus("pull_down");
                        airWaybill.setUpdateTime(new Date());
                        airWaybillMapper.updateById(airWaybill);
                        //发送消息
                        sendMessage(airWaybill.getWaybillCode(), "OFLD 拉货", airWaybill.getAgentCompany());
                        //运单日志的新增
                        WaybillLog waybillLog = waybillLogService.getWaybillLog(
                                airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                                loadUldWaybill.getWeight().toString(), loadUldWaybill.getQuantity().toString(), flightId.getAirWays() + flightId.getFlightNo(),
                                query, null, 0, null, new Date(),
                                "拉下", "DEP", null);
                        waybillLogs.add(waybillLog);

                        cabinQuantity += loadUldWaybill.getQuantity();
                        cabinWeight = cabinWeight.add(loadUldWaybill.getWeight());
                    }
                    updateFlightLoad(load, pullDownUld.getCabin(), cabinQuantity, cabinWeight);
                    removeUld(loadUldWaybills);
                    loadUldMapper.deleteById(pullDownUld.getFlightLoadUldId());
                }
            }
            // 板箱和运单拉下
            if (!CollectionUtils.isEmpty(query.getPullDownWaybills()) && !CollectionUtils.isEmpty(query.getPullDownUlds())) {
                for (PullDownUld pullDownUld : query.getPullDownUlds()) {
                    List<FlightLoadUldWaybill> loadUldWaybills = loadUldWaybillMapper.selectList(new QueryWrapper<FlightLoadUldWaybill>()
                            .eq("load_uld_id", pullDownUld.getFlightLoadUldId()));
                    for (FlightLoadUldWaybill loadUldWaybill : loadUldWaybills) {
                        FlightLoadUld uld = loadUldMapper.selectById(loadUldWaybill.getLoadUldId());
                        AirWaybill airWaybill = airWaybillMapper.selectById(loadUldWaybill.getWaybillId());
                        WaybillTrace waybillTrace = new WaybillTrace();
                        waybillTrace.setOperTime(new Date());
                        waybillTrace.setOperPieces(airWaybill.getQuantity());
                        waybillTrace.setOperWeight(airWaybill.getWeight());
                        waybillTrace.setWaybillCode(airWaybill.getWaybillCode());
                        waybillTrace.setNodeName("临时拉下");
                        traceService.insertWaybillTrace(waybillTrace);
                        List<WaybillTrace> waybillTraces = traceService.getBaseMapper().selectList(new QueryWrapper<WaybillTrace>()
                                .eq("waybill_code", airWaybill.getWaybillCode()).eq("node_index", 8));
                        for (WaybillTrace trace : waybillTraces) {
                            trace.setOperPieces(0);
                            trace.setOperWeight(BigDecimal.ZERO);
                            traceService.updateById(trace);
                        }
                        Long aLong = pullDownTrace(load.getFlightId(), airWaybill.getWaybillCode(), loadUldWaybill.getQuantity(), loadUldWaybill.getQuantity(), loadUldWaybill.getWeight(), loadUldWaybill.getWeight(), uld.getUld(), query.getRemark());
                        Wrong wrong = new Wrong();
                        wrong.setDeptId(airWaybill.getDeptId());
                        wrong.setWrongType("OFLD 拉货");
                        wrong.setWaybillCode(airWaybill.getWaybillCode());
                        wrong.setType("DEP");
                        wrong.setStatus(1);
                        wrong.setProMethod("0");
                        wrong.setCreateTime(new Date());
                        wrong.setRegisterTime(new Date());
                        wrong.setAgent(airWaybill.getShipper());
                        wrong.setPullId(aLong);
                        wrongMapper.insert(wrong);
                        try {
                            setFSUMsg(airWaybill,flightId,loadUldWaybill.getQuantity(),msgJsonVO,token);
                        }catch (Exception e){
                            log.error(CABLE_ERROR_MARKER,"运单"+airWaybill.getWaybillCode()+"发送FSU-DIS报文失败：" + e.getMessage());
                        }
                        // 修改运单状态
                        airWaybill.setIsLoad(0);
                        airWaybill.setStatus("pull_down");
                        airWaybill.setUpdateTime(new Date());
                        airWaybillMapper.updateById(airWaybill);
                        //发送消息
                        sendMessage(airWaybill.getWaybillCode(), "OFLD 拉货", airWaybill.getAgentCompany());

                        //运单日志的新增
                        WaybillLog waybillLog = waybillLogService.getWaybillLog(
                                airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                                loadUldWaybill.getWeight().toString(), loadUldWaybill.getQuantity().toString(), flightId.getAirWays() + flightId.getFlightNo(),
                                query, null, 0, null, new Date(),
                                "拉下", "DEP", null);
                        waybillLogs.add(waybillLog);
                        cabinQuantity += loadUldWaybill.getQuantity();
                        cabinWeight = cabinWeight.add(loadUldWaybill.getWeight());
                    }
                    updateFlightLoad(load,pullDownUld.getCabin(),cabinQuantity,cabinWeight);
                    removeUld(loadUldWaybills);
                    loadUldMapper.deleteById(pullDownUld.getFlightLoadUldId());
                }
                for (PullDownWaybill pullDownWaybill : query.getPullDownWaybills()) {
                    FlightLoadUldWaybill flightLoadUldWaybill = loadUldWaybillMapper.selectById(pullDownWaybill.getFlightLoadWaybillId());
                    if (flightLoadUldWaybill != null){
                        AirWaybill airWaybill = airWaybillMapper.selectById(flightLoadUldWaybill.getWaybillId());
                        WaybillTrace waybillTrace = new WaybillTrace();
                        waybillTrace.setOperTime(new Date());
                        waybillTrace.setOperPieces(airWaybill.getQuantity());
                        waybillTrace.setOperWeight(airWaybill.getWeight());
                        waybillTrace.setWaybillCode(airWaybill.getWaybillCode());
                        waybillTrace.setNodeName("临时拉下");
                        traceService.insertWaybillTrace(waybillTrace);
                        List<WaybillTrace> waybillTraces = traceService.getBaseMapper().selectList(new QueryWrapper<WaybillTrace>()
                                .eq("waybill_code", airWaybill.getWaybillCode()).eq("node_index", 8));
                        for (WaybillTrace trace : waybillTraces) {
                            trace.setOperPieces(0);
                            trace.setOperWeight(BigDecimal.ZERO);
                            traceService.updateById(trace);
                        }
                        Long aLong = pullDownTrace(load.getFlightId(), airWaybill.getWaybillCode(), flightLoadUldWaybill.getQuantity(), flightLoadUldWaybill.getQuantity(), flightLoadUldWaybill.getWeight(), flightLoadUldWaybill.getWeight(), null, query.getRemark());
                        Wrong wrong = new Wrong();
                        wrong.setDeptId(airWaybill.getDeptId());
                        wrong.setWrongType("OFLD 拉货");
                        wrong.setWaybillCode(airWaybill.getWaybillCode());
                        wrong.setType("DEP");
                        wrong.setStatus(1);
                        wrong.setProMethod("0");
                        wrong.setCreateTime(new Date());
                        wrong.setRegisterTime(new Date());
                        wrong.setAgent(airWaybill.getShipper());
                        wrong.setPullId(aLong);
                        wrongMapper.insert(wrong);
                        try {
                            setFSUMsg(airWaybill,flightId,flightLoadUldWaybill.getQuantity(),msgJsonVO,token);
                        }catch (Exception e){
                            log.error(CABLE_ERROR_MARKER,"运单"+airWaybill.getWaybillCode()+"发送FSU-DIS报文失败：" + e.getMessage());
                        }
                        // 修改运单状态
                        airWaybill.setIsLoad(0);
                        airWaybill.setStatus("pull_down");
                        airWaybill.setUpdateTime(new Date());
                        airWaybillMapper.updateById(airWaybill);

                        updateFlightLoad(load, pullDownWaybill.getCabin(), flightLoadUldWaybill.getQuantity(), flightLoadUldWaybill.getWeight());
                        //发送消息
                        sendMessage(airWaybill.getWaybillCode(), "OFLD 拉货", airWaybill.getAgentCompany());
                        loadUldWaybillMapper.deleteById(flightLoadUldWaybill.getId());
                    }
                }
            }
            selectVo(query.getFlightLoadId(), vo);
            return vo;
        }catch(Exception e){
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作失败" + "," +
                                "code:" + response.getStatus()));
                waybillLog.setErrorMsg(e.getMessage());
                waybillLog.setStatus(1);
            }
            throw new CustomException(e.getMessage());
        }finally {
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLogService.insertWaybillLog(waybillLog);
            }
        }
    }

    private void updateFlightLoad(FlightLoad load, String cabin, Integer quantity, BigDecimal weight) {
        if (StringUtils.isNotEmpty(cabin)) {
            switch (cabin) {
                case "1H":
                    int h1q = load.getH1Quantity() == null ? 0 : load.getH1Quantity();
                    BigDecimal h1w = load.getH1Weight() == null ? BigDecimal.ZERO : load.getH1Weight();
                    int h1Quantity = Math.max((h1q - quantity), 0);
                    BigDecimal h1Weight = h1w.subtract(weight).max(BigDecimal.ZERO);
                    load.setH1Quantity(h1Quantity);
                    load.setH1Weight(h1Weight);
                    break;
                case "2H":
                    int h2q = load.getH2Quantity() == null ? 0 : load.getH2Quantity();
                    BigDecimal h2w = load.getH2Weight() == null ? BigDecimal.ZERO : load.getH2Weight();
                    int h2Quantity = Math.max((h2q - quantity), 0);
                    BigDecimal h2Weight = h2w.subtract(weight).max(BigDecimal.ZERO);
                    load.setH2Quantity(h2Quantity);
                    load.setH2Weight(h2Weight);
                    break;
                case "3H":
                    int h3q = load.getH3Quantity() == null ? 0 : load.getH3Quantity();
                    BigDecimal h3w = load.getH3Weight() == null ? BigDecimal.ZERO : load.getH3Weight();
                    int h3Quantity = Math.max((h3q - quantity), 0);
                    BigDecimal h3Weight = h3w.subtract(weight).max(BigDecimal.ZERO);
                    load.setH3Quantity(h3Quantity);
                    load.setH3Weight(h3Weight);
                    break;
                case "4H":
                    int h4q = load.getH4Quantity() == null ? 0 : load.getH4Quantity();
                    BigDecimal h4w = load.getH4Weight() == null ? BigDecimal.ZERO : load.getH4Weight();
                    int h4Quantity = Math.max((h4q - quantity), 0);
                    BigDecimal h4Weight = h4w.subtract(weight).max(BigDecimal.ZERO);
                    load.setH4Quantity(h4Quantity);
                    load.setH4Weight(h4Weight);
                    break;
                case "5H":
                    int h5q = load.getH5Quantity() == null ? 0 : load.getH5Quantity();
                    BigDecimal h5w = load.getH5Weight() == null ? BigDecimal.ZERO : load.getH5Weight();
                    int h5Quantity = Math.max((h5q - quantity), 0);
                    BigDecimal h5Weight = h5w.subtract(weight).max(BigDecimal.ZERO);
                    load.setH5Quantity(h5Quantity);
                    load.setH5Weight(h5Weight);
                    break;
            }
            flightLoadMapper.updateById(load);
        }
    }

    @Async
    public void sendMessage(String waybillCode, String s, String agentCompany) {
        String message = "    运单" + waybillCode + "不正常货邮" + s + "  \n" +
                "运单：" + waybillCode + "不正常，不支持类型" + s + "请及时处理。";
        SocketMessageVo vo = new SocketMessageVo();
        vo.setMessage(message);
        vo.setType(6);
        vo.setDeptId(sysDeptMapper.selectDeptIdByDeptName(agentCompany));

        Message mes = new Message();
        mes.setContent(message);
        mes.setCreateTime(new Date());
        mes.setIsHandle(0);
        mes.setPostId(SecurityUtils.getDeptId());
        mes.setTitle("不正常货邮提醒");
        messageMapper.insert(mes);
        waybillMessageProducer.send(vo);
    }

    /**
     * 无货
     *
     * @param id 航段id
     * @return 结果
     */
    @Override
    public int editState(Long id) {
        FlightLoad load = flightLoadMapper.selectById(id);
        load.setState("out_of_stock");
        return flightLoadMapper.updateById(load);
    }

    /**
     * 打印机长通知单
     *
     * @param id       配载id
     * @param response 返回流
     */
    @Override
    public void printCaptain(HttpServletResponse response, Long id,String type) throws Exception {
        FlightLoad load = flightLoadMapper.selectById(id);
        FlightInfo info = flightInfoMapper.selectById(load.getFlightId());
        List<FlightLoadWaybill> waybillList = loadWaybillMapper.selectWaybillIdsByLoadId(id);
        int dangerSize = 0;
        int specialSize = 0;
        List<List<PrintCaptainVo>> dangerousGoods = new ArrayList<>();
        List<List<PrintCaptainVo>> specialLoads = new ArrayList<>();
        List<byte[]> pages = new ArrayList<>();
        if (!CollectionUtils.isEmpty(waybillList)) {
            List<PrintCaptainVo> dangerWaybillList = new ArrayList<>();
            List<PrintCaptainVo> specialWaybillList = new ArrayList<>();
            for (FlightLoadWaybill loadWaybill : waybillList) {
                PrintCaptainVo dangerWaybill = airWaybillMapper.selectDangerInfo(loadWaybill.getWaybillId());
                if (dangerWaybill != null){
                    PrintCaptainVo dangerWaybill1 = new PrintCaptainVo();
                    dangerWaybill1.setQuantity(loadWaybill.getQuantity());
                    dangerWaybill1.setWeight(loadWaybill.getWeight());
                    dangerWaybill1.setUld(loadWaybill.getUld());
                    dangerWaybill1.setCabin(loadWaybill.getCabin());
                    dangerWaybill1.setWaybillCode(dangerWaybill.getWaybillCode());
                    dangerWaybill1.setDesPort(dangerWaybill.getDesPort());
                    dangerWaybill1.setDangerCode(dangerWaybill.getDangerCode());
                    dangerWaybill1.setDangerType(dangerWaybill.getDangerType());
                    dangerWaybill1.setCargoName(dangerWaybill.getCargoName());
                    dangerWaybill1.setId(dangerWaybill.getId());
                    dangerWaybillList.add(dangerWaybill1);
                }
                PrintCaptainVo specialWaybill = airWaybillMapper.selectSpecialInfo(loadWaybill.getWaybillId());
                if (specialWaybill != null){
                    PrintCaptainVo specialWaybill1 = new PrintCaptainVo();
                    specialWaybill1.setQuantity(loadWaybill.getQuantity());
                    specialWaybill1.setWeight(loadWaybill.getWeight());
                    specialWaybill1.setUld(loadWaybill.getUld());
                    specialWaybill1.setCabin(loadWaybill.getCabin());
                    specialWaybill1.setWaybillCode(specialWaybill.getWaybillCode());
                    specialWaybill1.setDesPort(specialWaybill.getDesPort());
                    specialWaybill1.setSpecialCargoCode1(specialWaybill.getSpecialCargoCode1());
                    specialWaybill1.setId(specialWaybill.getId());
                    specialWaybill1.setCargoName(specialWaybill.getCargoName());
                    specialWaybillList.add(specialWaybill1);
                }
            }
            if (!CollectionUtils.isEmpty(dangerWaybillList)) {
                dangerousGoods = splitListIntoSubLists(dangerWaybillList, 3);
                dangerSize = dangerousGoods.size();
            }
            if (!CollectionUtils.isEmpty(specialWaybillList)) {
                specialLoads = splitListIntoSubLists(specialWaybillList, 4);
                specialSize = specialLoads.size();
            }
            int totalPages;
            if("special".equals(type)){
                totalPages = specialSize;
            }else{
                totalPages = dangerSize;
            }
            for (int page = 1; page <= totalPages; page++) {
                CaptainNotice notice = new CaptainNotice();
                notice.setCraftNo(info.getCraftNo());
                notice.setExecDate(format.format(info.getExecDate()));
                notice.setFlightNo(info.getAirWays() + info.getFlightNo());
                notice.setSourcePort(info.getStartStation());
                notice.setPageNum(page);
                notice.setPageSize(totalPages);
                // 获取当前页面的危险品和特殊货物子列表
                List<PrintCaptainVo> currentDangerousGoods = page <= dangerSize ? dangerousGoods.get(page - 1) : new ArrayList<>();
                List<PrintCaptainVo> currentSpecialLoads = page <= specialSize ? specialLoads.get(page - 1) : new ArrayList<>();
                byte[] bytes = generateNotice(notice, currentDangerousGoods, currentSpecialLoads, id, type);
                if (bytes != null) {
                    pages.add(bytes);
                }
            }
        }
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        Document document = new Document();
        PdfCopy copy = new PdfCopy(document, baos);
        document.open();
        if (CollectionUtils.isEmpty(pages)) {
            ClassPathResource resource;
            if("special".equals(type)){
                resource = new ClassPathResource("template/captainSpecialNotice.pdf");
            }else{
                resource = new ClassPathResource("template/captainDangerousNotice.pdf");
            }
            if (resource.exists()) {
                CaptainNotice notice = new CaptainNotice();
                notice.setPageSize(1);
                notice.setPageNum(1);
                String path = resource.getPath();
                byte[] bytes = PdfPrintHelper.getPdfDataFromTemplate(notice, path);
                try (ByteArrayInputStream bais = new ByteArrayInputStream(bytes)) {
                    PdfReader reader = new PdfReader(bais);
                    for (int i = 1; i <= reader.getNumberOfPages(); i++) {
                        PdfImportedPage page = copy.getImportedPage(reader, i);
                        copy.addPage(page);
                    }
                    reader.close();
                }
            }
        }else {
            for (byte[] pageContent : pages) {
                try (ByteArrayInputStream bais = new ByteArrayInputStream(pageContent)) {
                    PdfReader reader = new PdfReader(bais);
                    for (int i = 1; i <= reader.getNumberOfPages(); i++) {
                        PdfImportedPage page = copy.getImportedPage(reader, i);
                        copy.addPage(page);
                    }
                    reader.close();
                }
            }
        }
        document.close();
        // 设置响应头
        response.reset();
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "inline; filename=example.pdf");
        // 获取输出流并写入字节数据
        response.getOutputStream().write(baos.toByteArray());
        response.getOutputStream().flush();
    }

    public static <T> List<List<T>> splitListIntoSubLists(List<T> list, int subListSize) {
        List<List<T>> subLists = new ArrayList<>();
        int size = list.size();
        for (int start = 0; start < size; start += subListSize) {
            int end = Math.min(size, start + subListSize);
            subLists.add(new ArrayList<>(list.subList(start, end)));
        }
        return subLists;
    }

    public byte[] generateNotice(CaptainNotice notice, List<PrintCaptainVo> dangerousGoods,
                                 List<PrintCaptainVo> specialLoads, Long id,String type) throws Exception {
        List<DangerNoticeVo> dangerNoticeVos = new ArrayList<>();
        for (PrintCaptainVo captainVo : dangerousGoods) {
            DangerNoticeVo dangerNoticeVo = new DangerNoticeVo();
            dangerNoticeVo.setDangerUld(captainVo.getUld());
            dangerNoticeVo.setDangerCabin(captainVo.getCabin());
            String substring = captainVo.getWaybillCode().substring(4);
            if(substring.contains("DN")){
                dangerNoticeVo.setDangerWaybill(substring.substring(0,2) + "-" + substring.substring(2));
            }else{
                dangerNoticeVo.setDangerWaybill(substring.substring(0,3) + "-" + substring.substring(3));
            }
            dangerNoticeVo.setDangerDesPort(captainVo.getDesPort());
            dangerNoticeVo.setDangerCode(captainVo.getDangerCode());
            dangerNoticeVo.setDangerType(captainVo.getDangerType());
            dangerNoticeVo.setDangerQuantity(captainVo.getQuantity());
            dangerNoticeVo.setDangerWeight(captainVo.getWeight());
            dangerNoticeVos.add(dangerNoticeVo);
        }
        //对仓位做一个排序
        List<DangerNoticeVo> dangerNoticeVosNew = sortByCabin3(dangerNoticeVos);
        notice.setDangerNoticeVos(dangerNoticeVosNew);
        List<SpecialNoticeVo> specialNoticeVos = new ArrayList<>();
        for (PrintCaptainVo captainVo : specialLoads) {
            SpecialNoticeVo specialNoticeVo = new SpecialNoticeVo();
            specialNoticeVo.setUld(captainVo.getUld());
            specialNoticeVo.setCabin(captainVo.getCabin());
            String substring = captainVo.getWaybillCode().substring(4);
            if(substring.contains("DN")){
                specialNoticeVo.setWaybillCode(substring.substring(0,2) + "-" + substring.substring(2));
            }else{
                specialNoticeVo.setWaybillCode(substring.substring(0,3) + "-" + substring.substring(3));
            }
            specialNoticeVo.setDesPort(captainVo.getDesPort());
            specialNoticeVo.setCargoName(captainVo.getCargoName());
            specialNoticeVo.setQuantity(captainVo.getQuantity());
            specialNoticeVo.setWeight(captainVo.getWeight());
            specialNoticeVo.setSpecialCargoCode1(captainVo.getSpecialCargoCode1());
            specialNoticeVos.add(specialNoticeVo);
        }
        List<SpecialNoticeVo> specialNoticeVosNew = sortByCabin4(specialNoticeVos);
        notice.setSpecialNoticeVos(specialNoticeVosNew);
        ClassPathResource resource;
        if("special".equals(type)){
            resource = new ClassPathResource("template/captainSpecialNotice.pdf");
        }else{
            resource = new ClassPathResource("template/captainDangerousNotice.pdf");
        }
        if (resource.exists()) {
            String path = resource.getPath();
            return PdfPrintHelper.getPdfDataFromTemplate(notice, path);
        }
        return null;
    }

    /**
     * 远程打印舱单
     *
     * @param id 配载id
     */
    @Override
    public void remotePrint(Long id) throws Exception {
        FormalManifestVo formalManifestVo = formalManifest(id);
        Workbook excelData = ExcelTableGenerator.getExcelData(formalManifestVo);
        byte[] bytes = ExcelTableGenerator.convertExcelToPdf(excelData);
        PrintPdf.printerPdfIp(bytes);
    }

    /**
     * 根据四位或多位航班号查出航司二字码
     *
     * @param airlinesCode 四位或多位航班号
     */
    @Override
    public String getAirLinesByCode(String airlinesCode, String flightDate) {
        return flightLoadMapper.getAirLinesByCode(airlinesCode, flightDate, "D");
    }

    /**
     * 重新开放制单
     *
     * @param flightId 航班id
     * @return 结果
     */
    @Override
    public int openCreate(Long flightId) {
        return flightInfoMapper.openCreate(flightId);
    }

    @Override
    public int compFlight(Long flightId) throws JsonProcessingException {
        FlightInfo info = flightInfoMapper.selectById(flightId);
        String sendAddress = configMapper.selectValue("dep.sendAddress");
        if (StringUtils.isNotNull(sendAddress)){
            String token = getToken();
            List<Long> list = flightLoadMapper.selectLegIdByVo(flightId);
            List<AddressMsgVO> msgVoList = new ArrayList<>();
            for (Long aLong : list) {
                List<FlightLoadWaybill> waybillList = loadWaybillMapper.selectWaybillIdsByFlightLoadId(aLong);
                for (FlightLoadWaybill loadWaybill : waybillList) {
                    AddressMsgVO addressMsgVo = new AddressMsgVO();
                    StringBuilder sb = new StringBuilder();
                    AirWaybill airWaybill = airWaybillMapper.selectById(loadWaybill.getWaybillId());
                    MsgJsonVO msgJsonVO = setMsgVo(info, sendAddress, "MAN");
                    setMANMsg(info, msgJsonVO, loadWaybill.getQuantity(), loadWaybill.getWeight().toString(), airWaybill);
                    List<HzCableAddress> addressList = cableAddressMapper.selectAddressByAirportCode(Collections.singletonList(airWaybill.getDesPort()));
                    Map<String, List<HzCableAddress>> addressMap = new HashMap<>();
                    if (!CollectionUtils.isEmpty(addressList)){
                        addressMap = addressList.stream().collect(Collectors.groupingBy(HzCableAddress::getAirportCode));
                    }
                    List<HzCableAddress> cableAddresses = addressMap.get(airWaybill.getDesPort());
                    try {
                        setInteractionType(cableAddresses,msgJsonVO);
                        restExchange(msgJsonVO, token, sb);
                    }catch (Exception e){
                        log.error(CABLE_ERROR_MARKER,"运单"+airWaybill.getWaybillCode()+"发送FSU-MAN报文失败" + e.getMessage());
                    }
                    addressMsgVo.setAddress(msgJsonVO.getAddress());
                    addressMsgVo.setReceiveMailAddress(msgJsonVO.getReceiveMailAddress());
                    addressMsgVo.setReceiveFtpAddress(ftpAddress);
                    addressMsgVo.setReceiveFtpFolder(msgJsonVO.getReceiveFtpFolder());
                    addressMsgVo.setReceiveMQQueue(msgJsonVO.getReceiveMQQueue());
                    addressMsgVo.setMsg(sb.toString());
                    addressMsgVo.setMsgType("FSU");
                    addressMsgVo.setSendType(msgJsonVO.getSendType());
                    msgVoList.add(addressMsgVo);
                }
            }
            List<AddressMsgVO> originMsg = setFFMMsg(flightId,sendAddress,token);
            msgVoList.addAll(originMsg);
            ffmRedisOperator.updateData(flightId, FFMStatus.CLOSED, msgVoList);
            // 2. 取消之前的定时任务
            ffmScheduler.cancel(flightId.toString());
            try {
                for (AddressMsgVO msgVo : msgVoList) {
                    insertCableAndSendMsg(info, sendAddress, msgVo);
                }
                log.info("航班关闭触发发送FFM电报: {}", flightId);
            } catch (Exception e) {
                log.error("航班关闭发送FFM失败: {}", flightId, e);
            } finally {
                ffmRedisOperator.deleteData(flightId);
            }
        }

        WaybillFlightData waybillFlightData = new WaybillFlightData();
        waybillFlightData.setType(2);
        waybillFlightData.setFlightNo(info.getAirWays() + info.getFlightNo());
        waybillFlightData.setFlightDate(info.getExecDate());

        List<WaybillFlightLoadInfoData> waybillFlightLoadInfoDataList = flightInfoMapper.selectWaybillDataByFlightId(info.getFlightId());
        waybillFlightData.setFlightLoadList(waybillFlightLoadInfoDataList);
        securityProducer.sendOtherWaybill(waybillFlightData,
                waybillFlightLoadInfoDataList.stream().map(WaybillFlightLoadInfoData::getWaybillCode).collect(Collectors.toList()).toString()
                , "货站完成航班,推送数据");

        return flightInfoMapper.compFlight(flightId);
    }

    private void insertCableAndSendMsg(FlightInfo info, String sendAddress, AddressMsgVO msgVo) {
        if (StringUtils.isEmpty(msgVo.getSendType())){
            return;
        }
        List<String> sendTypes = Arrays.asList(msgVo.getSendType().split(","));
        if (!sendTypes.contains("FD")){
            sendTypes.add("FD");
        }
        for (String sendType : sendTypes) {
            HzCable cable = new HzCable();
            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("ddHHmm");
            cable.setSerialNo(SerialNumberGenerator.generateSerialNumber());
            cable.setCableNo(sdf.format(date));
            cable.setCableTime(date);
            cable.setCreateBy("系统");
            cable.setIsSend(1);
            cable.setIsAuto(1);
            cable.setType("FSU");
            cable.setVersion("12");
            cable.setPriority("QD");
            cable.setCableAddress(sendAddress);
            if (msgVo.getAddress() != null){
                cable.setReceiveAddress(String.join(",", msgVo.getAddress()));
            }
            cable.setFlightNo(info.getAirWays() + info.getFlightNo());
            cable.setFlightDate(info.getExecDate());
            cable.setContent(msgVo.getMsg());
            hzCableMapper.insert(cable);
            ForwardOriginMsgVO msgVO = new ForwardOriginMsgVO();
            msgVO.setOriginMsg(msgVo.getMsg());
            msgVO.setMsgType("FSU");
            msgVO.setSendType(sendType);
            msgVO.setReceiveAddress("-");
            if ("FD".equals(sendType)) {
                if (msgVo.getAddress() != null){
                    msgVO.setReceiveAddress(String.join(",",msgVo.getAddress()));
                }
            }
            if ("MAIL".equals(sendType)){
                msgVO.setMailAddress(String.join(",",msgVo.getReceiveMailAddress()));
            }
            if ("FTP".equals(sendType)){
                msgVO.setReceiveFtpAddress(ftpAddress);
                msgVO.setReceiveFtpFolder(msgVo.getReceiveFtpFolder());
            }
            if ("MQ".equals(sendType)){
                msgVO.setReceiveMQQueue(msgVo.getReceiveMQQueue());
            }
            msgVO.setSendAddress(sendAddress);
            msgVO.setPriority("QD");
            httpService.sendCable(msgVO, cable, getToken());
        }
    }

    @Override
    public int openFlight(Long flightId) {
        List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
        if (!CollectionUtils.isEmpty(roles)){
            List<SysRole> dk = roles.stream().filter(e -> e.getRoleName().contains("吨控")).collect(Collectors.toList());
            List<SysRole> cgbzz = roles.stream().filter(e -> e.getRoleName().contains("出港班组长")).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(dk) && CollectionUtils.isEmpty(cgbzz)){
                LocalDateTime nowTime = LocalDateTime.now();
                LocalTime noon = LocalTime.of(12, 0,0);
                LocalDate today = LocalDate.now();
                LocalDate yesterday = today.minusDays(1);
                FlightInfo info = flightInfoMapper.selectById(flightId);
                LocalDate flightDate = info.getExecDate().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDate();
                if (nowTime.toLocalTime().isBefore(noon)){
                    if (!flightDate.equals(today) && !flightDate.equals(yesterday)) {
                        return HttpStatus.FORBIDDEN;
                    }
                }else {
                    if (!flightDate.equals(today)) {
                        return HttpStatus.FORBIDDEN;
                    }
                }
            }
        }
        return flightInfoMapper.openFlight(flightId);
    }

    /**
     * 运单拉下记录方法
     *
     * @param flightId     航班id
     * @param waybillCode  运单号
     * @param quantity     件数
     * @param weight       重量
     * @param loadQuantity 配载件数
     * @param loadWeight   配载重量
     */
    private Long pullDownTrace(Long flightId, String waybillCode, Integer loadQuantity, Integer quantity, BigDecimal loadWeight,
                               BigDecimal weight, String uld, String remark) {
        // 拉下记录
        FlightInfo info = flightInfoMapper.selectById(flightId);
        HzDepPullDown hzDepPullDown = new HzDepPullDown();
        hzDepPullDown.setFlightId(flightId);
        hzDepPullDown.setFlightNo(info.getAirWays() + info.getFlightNo());
        hzDepPullDown.setExecDate(info.getExecDate());
        hzDepPullDown.setFlightId(flightId);
        hzDepPullDown.setWaybillCode(waybillCode);
        hzDepPullDown.setOperName(SecurityUtils.getUsername());
        hzDepPullDown.setQuantity(quantity);
        hzDepPullDown.setWeight(weight);
        hzDepPullDown.setRemark(remark);
        hzDepPullDown.setUld(uld);
        hzDepPullDown.setLoadQuantity(loadQuantity);
        hzDepPullDown.setLoadWeight(loadWeight);
        hzDepPullDown.setOperTime(new Date());
        pullDownMapper.insert(hzDepPullDown);
        return hzDepPullDown.getId();


    }

    private void removeUld(List<FlightLoadUldWaybill> loadUldWaybills) {
        if (!CollectionUtils.isEmpty(loadUldWaybills)) {
            List<Long> ids = loadUldWaybills.stream().map(FlightLoadUldWaybill::getId).collect(Collectors.toList());
            loadUldWaybillMapper.deleteBatchIds(ids);
        }
    }

    private void getConfigForLoad(AirWaybill airWaybill){
        SysConfig sysConfig = configMapper.checkConfigKeyUnique("isPay.submitLoad");
        if("true".equals(sysConfig.getConfigValue())
                && (airWaybill.getPayStatus() == 0 || airWaybill.getPayStatus() == 14)) {
            BigDecimal costSum = calcCostSum(airWaybill);
            if (airWaybill.getTransferBill() == 1 && (costSum.compareTo(BigDecimal.ZERO) <= 0)) {
                //中转单费用为零
               return;
            }
            //费用为0 可以航班配载
            String substring = airWaybill.getWaybillCode().substring(4);
            throw new CustomException("运单"+substring.substring(0,3) + "-" + substring.substring(3)+"未支付");
        }
    }

    @Autowired
    private CostDetailMapper costDetailMapper;

    @Autowired
    private MawbMapper mawbMapper;
    /**
     * 计算出港运单总费用
     *
     * @param airWaybill 运单
     * @return
     */
    private BigDecimal calcCostSum(AirWaybill airWaybill) {
        BigDecimal costSum = new BigDecimal(0);
//        //这里的isSettle要改成1 因为制好单就要看到预授权支付明细 在制单的时候就赋值为1->又改成0了
//        List<CostDetail> details = costDetailMapper.selectPayOrSettleList(waybillCode, 0, 0, SecurityUtils.getHighParentId());
//        if (!CollectionUtils.isEmpty(details)) {
//            costSum = details.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
//            for (CostDetail detail : details) {
//                detail.setIsSettle(1);
//                costDetailMapper.updateById(detail);
//            }
//        }
//        log.info(">>>>>>>>>>>> 运单{}总费用：costSum = {}", waybillCode, costSum);

//        OnlineInfoVo infoVo = mawbMapper.selectInfo(airWaybill.getId());
        LambdaQueryWrapper<AirWaybill> eq = Wrappers.<AirWaybill>lambdaQuery()
                .select(AirWaybill::getPayMoney)
                .eq(AirWaybill::getType, "DEP")
                .eq(AirWaybill::getWaybillCode, airWaybill.getWaybillCode());
        AirWaybill airWaybill1 = airWaybillMapper.selectOne(eq);
        return airWaybill1.getPayMoney();
    }

    private List<AddressMsgVO> setFFMMsg(Long flightId,String sendAddress,String token){
        List<AddressMsgVO> msgVoList = new ArrayList<>();
        List<MsgFlightInfoVO> flightInfoVoList = flightInfoMapper.selectFlightInfoById(flightId, null);
        if (CollectionUtils.isEmpty(flightInfoVoList)) {
            return msgVoList;
        }
        MsgJsonVO vo = new MsgJsonVO();
        vo.setMsgType("FFM");
        vo.setOrigin(sendAddress.split(","));
        vo.setPriority(new String[]{"QD"});
        vo.setSourceId("1727215332084514819");
        vo.setUniqueId("1727215332084514819");
        List<String> desPortList = flightInfoVoList.stream().map(MsgFlightInfoVO::getNextStation).distinct().collect(Collectors.toList());
        List<HzCableAddress> desPortAddressList = cableAddressMapper.selectAddressByAirportCode(desPortList);
        List<String> carrierList = flightInfoVoList.stream().map(MsgFlightInfoVO::getCarrier).distinct().collect(Collectors.toList());
        List<HzCableAddress> carrierAddressList= cableAddressMapper.selectAddressByCarrier(carrierList);
        List<MsgFlightInfoVO> collect = flightInfoVoList.stream().distinct().collect(Collectors.toList());
        for (MsgFlightInfoVO infoVo : collect) {
            vo.setMsgVersion("5");
            vo.setCarrier(infoVo.getCarrier());
            vo.setFlightDate(infoVo.getFlightDate());
            vo.setDepartureStation(infoVo.getDepartureStation());
            vo.setFlightNo(infoVo.getFlightNo());
            vo.setNextStation(infoVo.getNextStation());
            vo.setFlightType(infoVo.getFlightType());
            FFMJsonVO ffmJsonVo = new FFMJsonVO();
            ffmJsonVo.setCarrier(infoVo.getCarrier());
            ffmJsonVo.setAircraftRegistration(infoVo.getAircraftRegistration());
            ffmJsonVo.setEtd(infoVo.getEtd());
            ffmJsonVo.setFlightNo(infoVo.getFlightNo());
            ffmJsonVo.setOriAirport(infoVo.getDepartureStation());
            PointOfUnloading unloading = new PointOfUnloading();
            unloading.setEta(infoVo.getEta());
            unloading.setArrAirport(infoVo.getNextStation());
            String date = infoVo.getStartSchemeTakeoffTime().toLocalDate().toString();
            unloading.setScheduledDepartureDate(date);
            String time = infoVo.getStartSchemeTakeoffTime().toLocalTime().format(TIME_FORMATTER);
            unloading.setScheduledDepartureTime(time);
            List<Long> legIds = flightLoadMapper.selectIdByFlightId(flightId);
            List<UldVO> uldVoList = uldMapper.selectUldByFlightId(infoVo.getFlightId(),null);
            List<ConsignmentDetail> bulk = new ArrayList<>();
            for (UldVO uldVO : uldVoList) {
                String uldNo = uldVO.getUldNo();
                List<ConsignmentDetail> detailList = loadWaybillMapper.selectDetailListForLegId(legIds, uldNo);
                if (!CollectionUtils.isEmpty(detailList)) {
                    for (ConsignmentDetail detail : detailList) {
                        if (StringUtils.isEmpty(detail.getGoodsName())){
                            detail.setGoodsName("-");
                        }
                        String mawbNo = detail.getMawbNo();
                        if(mawbNo.startsWith("AWBA")){
                            String substring = mawbNo.substring(4);
                            StringBuilder stringBuilder = new StringBuilder(substring);
                            detail.setMawbNo(stringBuilder.insert(3, "-").toString());
                        }else{
                            String substring = mawbNo.substring(4);
                            StringBuilder stringBuilder = new StringBuilder(substring);
                            detail.setMawbNo(stringBuilder.insert(2, "-").toString());
                        }
                        detail.setShipmentDescriptionCode("T");
                        detail.setTotalPieces(null);
                        if (StringUtils.isNotEmpty(detail.getShcStr())) {
                            detail.setShc(detail.getShcStr().split(","));
                        }
                        if (StringUtils.isNotEmpty(detail.getVolume())) {
                            detail.setVolumeUnit("MC");
                        }
                        if (StringUtils.isNotEmpty(detail.getWeight())) {
                            detail.setWeightUnit("KG");
                        }
                    }
                    if ("BLK".equals(uldNo)){
                        bulk.addAll(detailList);
                    }else {
                        Matcher matcher = LETTERS_PATTERN.matcher(uldNo);
                        if (matcher.find()) {
                            String letters = matcher.group(1);
                            uldVO.setUldType(letters);
                            String numbers = matcher.group(2);
                            uldVO.setUldNum(numbers);
                        }
                        uldVO.setUldOwner(infoVo.getCarrier());
                        uldVO.setUldNo(uldNo + infoVo.getCarrier());
                        uldVO.setConsignmentDetail(detailList);
                    }
                }
            }
            if (!bulk.isEmpty()){
                unloading.setBulk(bulk);
            }
            if (!uldVoList.isEmpty()){
                unloading.setUld(uldVoList);
            }
            ffmJsonVo.setPointOfUnloading(Collections.singletonList(unloading));
            vo.setMsgJson(JSON.toJSONString(ffmJsonVo));
            List<List<HzCableAddress>> targets = new ArrayList<>();
            Map<String, List<HzCableAddress>> desPortAddressMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(desPortAddressList)){
                desPortAddressMap = desPortAddressList.stream().collect(Collectors.groupingBy(HzCableAddress::getAirportCode));
            }
            List<HzCableAddress> desPortAddresses = desPortAddressMap.get(infoVo.getNextStation());
            targets.add(desPortAddresses);

            Map<String, List<HzCableAddress>> carrierAddressMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(carrierAddressList)){
                carrierAddressMap = carrierAddressList.stream().collect(Collectors.groupingBy(HzCableAddress::getAirlinesCode));
            }
            List<HzCableAddress> carrierAddresses = carrierAddressMap.get(infoVo.getCarrier());
            targets.add(carrierAddresses);
            for (List<HzCableAddress> target : targets) {
                StringBuilder sb = new StringBuilder();
                AddressMsgVO msgVo = new AddressMsgVO();
                try {
                    setInteractionType(target,vo);
                    if (vo.getAddress() != null){
                        restExchange(vo,token,sb);
                        msgVo.setMsg(sb.toString());
                        msgVo.setMsgType("FFM");
                        msgVo.setSendType(vo.getSendType());
                        msgVo.setAddress(vo.getAddress());
                        msgVo.setReceiveMailAddress(vo.getReceiveMailAddress());
                        msgVo.setReceiveFtpAddress(ftpAddress);
                        msgVo.setReceiveFtpFolder(vo.getReceiveFtpFolder());
                        msgVo.setReceiveMQQueue(vo.getReceiveMQQueue());
                        msgVoList.add(msgVo);
                    }
                }catch (Exception e){
                    log.error(CABLE_ERROR_MARKER,"发送FFM报文失败" + e.getMessage());
                }
            }
        }
        return msgVoList;
    }

    private void setMANMsg(FlightInfo info,MsgJsonVO msgJsonVO, Integer quantity, String weight, AirWaybill airWaybill){
        FSUJsonVO fsuJsonVO = new FSUJsonVO();
        fsuJsonVO.setDepAirport(airWaybill.getSourcePort());
        fsuJsonVO.setDesAirport(airWaybill.getDesPort());
        fsuJsonVO.setMawbId(airWaybill.getId().toString());
        if(airWaybill.getWaybillCode().startsWith("AWBA")){
            String substring = airWaybill.getWaybillCode().substring(4);
            StringBuilder stringBuilder = new StringBuilder(substring);
            fsuJsonVO.setMawbNo(stringBuilder.insert(3, "-").toString());
        }else{
            String substring = airWaybill.getWaybillCode().substring(4);
            StringBuilder stringBuilder = new StringBuilder(substring);
            fsuJsonVO.setMawbNo(stringBuilder.insert(2, "-").toString());
        }
        fsuJsonVO.setPieces(quantity.toString());
        if (!airWaybill.getQuantity().equals(quantity)){
            fsuJsonVO.setShipmentDescriptionCode("P");
            fsuJsonVO.setTotalPieces(airWaybill.getQuantity().toString());
        }else {
            fsuJsonVO.setShipmentDescriptionCode("T");
        }
        fsuJsonVO.setWeight(weight);
        fsuJsonVO.setWeightUnit("K");
        List<StatusDetails> statusDetails = new ArrayList<>();
        StatusDetails statusDetail = new StatusDetails();
        statusDetail.setStatusCode(msgJsonVO.getOperationNode());
        statusDetail.setMovementCarrier(info.getAirWays());
        statusDetail.setMovementFlightNo(info.getFlightNo());
        statusDetail.setMovementDate(LocalDateTime.now().format(DATE_FORMATTER));
        statusDetail.setMovementTime(LocalDateTime.now().format(TIME_FORMATTER));
        statusDetail.setMovementAirport(info.getStartStation());
        statusDetail.setMovementArrivalAirport(info.getTerminalStation());
        statusDetail.setMovementDepartureAirport(info.getStartStation());
        statusDetail.setShipmentDescriptionCode(fsuJsonVO.getShipmentDescriptionCode());
        statusDetail.setPieces(quantity.toString());
        statusDetail.setWeight(weight);
        statusDetail.setWeightUnit("K");
        statusDetail.setReportingDate(DATE_FORMAT.format(new Date()));
        statusDetails.add(statusDetail);
        fsuJsonVO.setStatusDetails(statusDetails);
        fsuJsonVO.setTotalWeight(airWaybill.getWeight().toString());
        msgJsonVO.setMsgJson(JSON.toJSONString(fsuJsonVO));
    }


    private MsgJsonVO setMsgVo(FlightInfo info,String sendAddress,String type){
        MsgJsonVO vo = new MsgJsonVO();
        vo.setCarrier(info.getAirWays());
        vo.setDepartureStation(info.getStartStation());
        vo.setMsgType("FSU");
        vo.setNextStation(info.getTerminalStation());
        vo.setOperationNode(type);
        vo.setOperationStation("KWE");
        if (StringUtils.isNotEmpty(sendAddress)) {
            vo.setOrigin(sendAddress.split(","));
        }else {
            vo.setOrigin(new String[]{"KWEFDCN"});
        }
        vo.setPriority(new String[]{"QD"});
        vo.setSourceId("44162409105767715");
        vo.setUniqueId("44162409105767715");
        vo.setMsgVersion("12");
        BaseCarrier baseCarrier = carrierMapper.selectByCode(info.getAirWays());
        if (baseCarrier != null){
            vo.setWaybillPrefix(baseCarrier.getPrefix());
        }
        return vo;
    }

    private void setFSUMsg(AirWaybill airWaybill, FlightInfo info, Integer pullDownQuantity,MsgJsonVO msgJsonVO,String token){
        List<HzCableAddress> addressList = cableAddressMapper.selectAddressByAirportCode(Collections.singletonList(airWaybill.getDesPort()));
        StringBuilder sb = new StringBuilder();
        FSUJsonVO fsuJsonVO = new FSUJsonVO();
        fsuJsonVO.setDepAirport(airWaybill.getSourcePort());
        fsuJsonVO.setDesAirport(airWaybill.getDesPort());
        if(airWaybill.getWaybillCode().startsWith("AWBA")){
            String substring = airWaybill.getWaybillCode().substring(4);
            StringBuilder stringBuilder = new StringBuilder(substring);
            fsuJsonVO.setMawbNo(stringBuilder.insert(3, "-").toString());
        }else{
            String substring = airWaybill.getWaybillCode().substring(4);
            StringBuilder stringBuilder = new StringBuilder(substring);
            fsuJsonVO.setMawbNo(stringBuilder.insert(2, "-").toString());
        }
        fsuJsonVO.setPieces(pullDownQuantity.toString());
        if (!airWaybill.getQuantity().equals(pullDownQuantity)){
            fsuJsonVO.setShipmentDescriptionCode("P");
            fsuJsonVO.setTotalPieces(airWaybill.getQuantity().toString());
        }else {
            fsuJsonVO.setShipmentDescriptionCode("T");
        }
        List<StatusDetails> statusDetails = new ArrayList<>();
        StatusDetails statusDetail = new StatusDetails();
        statusDetail.setDiscrepancyCode("OFLD");
        statusDetail.setMovementAirport(info.getStartStation());
        statusDetail.setMovementArrivalAirport(info.getTerminalStation());
        statusDetail.setMovementCarrier(info.getAirWays());
        statusDetail.setMovementDate(LocalDateTime.now().format(DATE_FORMATTER));
        statusDetail.setMovementTime(LocalDateTime.now().format(TIME_FORMATTER));
        statusDetail.setMovementDepartureAirport(info.getStartStation());
        statusDetail.setMovementFlightNo(info.getFlightNo());
        statusDetail.setPieces(pullDownQuantity.toString());
        statusDetail.setReportingDate(DATE_FORMAT.format(new Date()));
        statusDetail.setShipmentDescriptionCode(fsuJsonVO.getShipmentDescriptionCode());
        statusDetail.setStatusCode("DIS");
        statusDetails.add(statusDetail);
        fsuJsonVO.setStatusDetails(statusDetails);
        msgJsonVO.setMsgJson(JSON.toJSONString(fsuJsonVO));
        Map<String, List<HzCableAddress>> addressMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(addressList)){
            addressMap = addressList.stream().collect(Collectors.groupingBy(HzCableAddress::getAirportCode));
        }
        List<HzCableAddress> cableAddresses = addressMap.get(airWaybill.getDesPort());
        setInteractionType(cableAddresses,msgJsonVO);
        restExchange(msgJsonVO,token,sb);
        insertAndSendMsg(msgJsonVO,info.getAirWays() + info.getFlightNo(), info.getExecDate(),sb.toString(), token);
    }


    public void setInteractionType(List<HzCableAddress> cableAddresses, MsgJsonVO vo) {
        List<String> sendType = new ArrayList<>();
        if (cableAddresses == null || cableAddresses.isEmpty()) {
            sendType.add("FD");
            vo.setSendType("FD");
            vo.setAddress(new String[]{"-"});
            return;
        }
        List<String> sendTypeCn = cableAddresses.stream()
                .map(HzCableAddress::getInteractionTypes)
                .filter(StringUtils::isNotEmpty)
                .map(s -> s.split(","))
                .flatMap(Arrays::stream)
                .map(String::trim)
                .distinct()
                .collect(Collectors.toList());
        for (String type : sendTypeCn) {
            switch (type) {
                case "民航局报文":
                    sendType.add("FD");
                    break;
                case "邮箱报文":
                    sendType.add("MAIL");
                    break;
                case "FTP收发报文":
                    sendType.add("FTP");
                    break;
                case "rabbitmq收发报文":
                    sendType.add("MQ");
                    break;
                default:
                    break;
            }
        }
        if (!sendType.contains("FD")){
            sendType.add("FD");
        }
        vo.setSendType(String.join(",", sendType));
        List<String> emailAddresses = new ArrayList<>();
        List<String> ftpList = new ArrayList<>();
        List<String> mqQueueList = new ArrayList<>();
        List<String> caacAddresses = new ArrayList<>();
        for (HzCableAddress cableAddress : cableAddresses) {
            List<String> interactionTypeList = new ArrayList<>();
            if (StringUtils.isNotEmpty(cableAddress.getInteractionTypes())) {
                interactionTypeList = Arrays.asList(cableAddress.getInteractionTypes().split(","));
            }
            if (!CollectionUtils.isEmpty(interactionTypeList)) {
                for (String type : interactionTypeList) {
                    switch (type) {
                        case "邮箱报文":
                            emailAddresses.add(cableAddress.getEmailAddress());
                            break;
                        case "FTP收发报文":
                            ftpList.add(cableAddress.getFtpList());
                            break;
                        case "民航局报文":
                            caacAddresses.add(cableAddress.getCaacAddress());
                            break;
                        case "rabbitmq收发报文":
                            mqQueueList.add(cableAddress.getMqQueue());
                            break;
                        default:
                            break;
                    }
                }
            }
        }
        if (!emailAddresses.isEmpty()) {
            vo.setReceiveMailAddress(emailAddresses.toArray(new String[0]));
        }
        if (!ftpList.isEmpty()) {
            vo.setReceiveFtpFolder(String.join(",",ftpList));
        }
        if (!mqQueueList.isEmpty()) {
            vo.setReceiveMQQueue(String.join(",",mqQueueList));
        }
        if (!caacAddresses.isEmpty()) {
            vo.setAddress(caacAddresses.toArray(new String[0]));
        }else {
            vo.setAddress(new String[]{"-"});
        }
    }

    private String getToken(){
        System.out.println("*********调用登录接口获取token*********");
        String token = "";
        HttpHeaders headers = setHeaders();
        HttpEntity<?> httpEntity = new HttpEntity<>(headers);
        ResponseEntity<JSONObject> exchange = restTemplate.exchange(loginUrl + account, HttpMethod.GET, httpEntity, JSONObject.class);
        if (exchange.getBody() != null) {
            if (!"0".equals(exchange.getBody().getString("code"))) {
                throw new CustomException(exchange.getBody().getString("message"));
            }
            JSONObject data = exchange.getBody().getJSONObject("data");
            token = data.getString("token");
            System.out.println(token);
        }
        return token;
    }



    private void insertAndSendMsg(MsgJsonVO vo, String flightNo, Date flightDate, String msg, String token){
        if (StringUtils.isEmpty(vo.getSendType())){
            return;
        }
        List<String> sendTypes = Arrays.asList(vo.getSendType().split(","));
        if (!sendTypes.contains("FD")){
            sendTypes.add("FD");
        }
        for (String sendType : sendTypes) {
            HzCable cable = new HzCable();
            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("ddHHmm");
            cable.setSerialNo(SerialNumberGenerator.generateSerialNumber());
            cable.setCableNo(sdf.format(date));
            cable.setCableTime(date);
            cable.setCreateBy("系统");
            cable.setIsSend(1);
            cable.setIsAuto(1);
            cable.setType("FSU");
            cable.setVersion("12");
            cable.setPriority("QD");
            cable.setCableAddress(String.join(",", vo.getOrigin()));
            if (vo.getAddress() != null){
                cable.setReceiveAddress(String.join(",", vo.getAddress()));
            }
            cable.setFlightNo(flightNo);
            cable.setFlightDate(flightDate);
            cable.setContent(msg);
            hzCableMapper.insert(cable);
            ForwardOriginMsgVO msgVO = new ForwardOriginMsgVO();
            msgVO.setOriginMsg(msg);
            msgVO.setMsgType("FSU");
            msgVO.setSendType(sendType);
            msgVO.setReceiveAddress("-");
            if ("FD".equals(sendType)) {
                if (vo.getAddress() != null){
                    msgVO.setReceiveAddress(String.join(",",vo.getAddress()));
                }
            }
            if ("MAIL".equals(sendType)){
                msgVO.setMailAddress(String.join(",",vo.getReceiveMailAddress()));
            }
            if ("FTP".equals(sendType)){
                msgVO.setReceiveFtpAddress(ftpAddress);
                msgVO.setReceiveFtpFolder(vo.getReceiveFtpFolder());
            }
            if ("MQ".equals(sendType)){
                msgVO.setReceiveMQQueue(vo.getReceiveMQQueue());
            }
            msgVO.setSendAddress(String.join(",", vo.getOrigin()));
            msgVO.setPriority("QD");
            httpService.sendCable(msgVO, cable, getToken());
        }
    }

    private HttpHeaders setHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType("application/json;charset=UTF-8"));
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        headers.add("Accept-Charset", "UTF-8");
        return headers;
    }

    private void restExchange(MsgJsonVO vo, String token, StringBuilder sb){
        HttpHeaders header = setHeaders();
        header.add("X-Access-Token", token);
        HttpEntity<?> httpEntity = new HttpEntity<>(vo, header);
        System.out.println("参数：" + JSON.toJSONString(vo));
        ResponseEntity<JSONObject> exchange = restTemplate.exchange(getMsg, HttpMethod.POST, httpEntity, JSONObject.class);
        if (exchange.getBody() != null) {
            if (!"0".equals(exchange.getBody().getString("code"))) {
                throw new CustomException(exchange.getBody().getString("msg"));
            }
            JSONObject data = exchange.getBody().getJSONObject("data");
            String msgContent = data.getString("msgContent");
            sb.append(msgContent).append("\n");
        }
    }

}
