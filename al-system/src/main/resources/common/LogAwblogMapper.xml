<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.departure.mapper.LogAwblogMapper">
    
    <resultMap type="LogAwblog" id="LogAwblogResult">
        <result property="recid"    column="RECID"    />
        <result property="statusid"    column="STATUSID"    />
        <result property="subid"    column="SUBID"    />
        <result property="stocktypeid"    column="STOCKTYPEID"    />
        <result property="stockpre"    column="STOCKPRE"    />
        <result property="stockno"    column="STOCKNO"    />
        <result property="pcs"    column="PCS"    />
        <result property="uldno"    column="ULDNO"    />
        <result property="flightno"    column="FLIGHTNO"    />
        <result property="awbvoyage"    column="AWBVOYAGE"    />
        <result property="customerid"    column="CUSTOMERID"    />
        <result property="airport"    column="AIRPORT"    />
        <result property="opetime"    column="OPETIME"    />
        <result property="opedepartid"    column="OPEDEPARTID"    />
        <result property="operator"    column="OPERATOR"    />
        <result property="openo"    column="OPENO"    />
        <result property="particularinfo"    column="PARTICULARINFO"    />
        <result property="weight"    column="WEIGHT"    />
        <result property="oldparticularinfo"    column="OLDPARTICULARINFO"    />
    </resultMap>

    <sql id="selectLogAwblogVo">
        select RECID, STATUSID, SUBID, STOCKTYPEID, STOCKPRE, STOCKNO, PCS, ULDNO, FLIGHTNO, AWBVOYAGE, CUSTOMERID, AIRPORT, OPETIME, OPEDEPARTID, OPERATOR, OPENO, REMARK, WEIGHT from KWECFPS.t_log_awblog
    </sql>

    <select id="selectLogAwblogList" parameterType="LogAwblog" resultMap="LogAwblogResult">
        <include refid="selectLogAwblogVo"/>
        <where>  
            <if test="statusid != null  and statusid != ''"> and STATUSID = #{statusid}</if>
            <if test="subid != null  and subid != ''"> and SUBID = #{subid}</if>
            <if test="stockno != null  and stockno != ''"> and STOCKNO = #{stockno}</if>
        </where>
    </select>
    
    <select id="selectLogAwblogById" parameterType="Long" resultMap="LogAwblogResult">
        <include refid="selectLogAwblogVo"/>
        where RECID = #{recid}
    </select>
</mapper>