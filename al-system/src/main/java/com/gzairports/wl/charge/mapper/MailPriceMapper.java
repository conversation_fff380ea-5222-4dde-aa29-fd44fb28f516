package com.gzairports.wl.charge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.wl.charge.domain.MailPrice;
import com.gzairports.wl.charge.domain.query.FreightRateAirQuery;
import com.gzairports.wl.charge.domain.vo.MailPriceVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 邮件运价Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@Mapper
public interface MailPriceMapper extends BaseMapper<MailPrice> {

    /**
     * 查询航司运价列表
     *
     * @param query 查询参数
     * @return 航司运价列表
     */
    List<MailPriceVo> selectListByQuery(FreightRateAirQuery query);

    /**
     * 删除航司运价
     *
     * @param id 航司运价主键
     * @return 结果
     */
    int removeRateAir(Long id);
}
