<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.wl.reporter.mapper.ReportSetMapper">

    <select id="getInfoById" resultType="com.gzairports.wl.reporter.domain.vo.ReportSetVo">
        select id, report_type, report_title, report_source from wl_report_set where id = #{id}
    </select>
    <select id="getList" resultType="com.gzairports.wl.reporter.domain.ReportSet">
        select id, report_type, report_title, report_source from wl_report_set
        <where>
            <if test="type != null and type != ''">
               and report_type = #{type}
            </if>
        </where>
    </select>
</mapper>