package com.gzairports.common.bank.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @author: lan
 * @Desc: 银行接口日志表
 * @create: 2025-03-19 11:25
 **/

@Data
@TableName("hz_bank_logs")
public class HzBankLogs {
    /** 主键id */
    private Long id;

    /** 运单id  type为0就是主单id type为1/2就是分单id type为3是进港主单办单id type为4是服务申请表id type为5是冷藏表id */
    private Long waybillId;

    /** 运单号 */
    private String waybillCode;

    /** 运单类型 0出港主单/邮件单 1分单 2分单账单 3进港主单  4服务申请 5冷藏 */
    private Integer waybillType;

    /** 请求时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date requestTime;

    /** 响应时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date responseTime;

    /** 请求内容 */
    private String requestParam;

    /** 请求接口 */
    private String requestApi;

    /** 响应内容 */
    private String responseParam;

    /** 响应状态 */
    private String responseStatus;

    /** 商户订单号 */
    private String orderNo;

    /** 商户流水号 */
    private String serialNo;

    /** 类型 1下单/网关支付 2确认支付 3退款 4支付结果查询 5交易明细单下载 6订单分账信息查询 7异步通知 */
    private Integer type;
}
