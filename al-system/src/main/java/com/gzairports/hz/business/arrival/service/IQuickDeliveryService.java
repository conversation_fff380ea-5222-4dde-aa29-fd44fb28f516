package com.gzairports.hz.business.arrival.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.hz.business.arrival.domain.HzArrQuickDelivery;
import com.gzairports.hz.business.arrival.domain.query.QuickDeliveryQuery;

import java.util.List;

/**
 * 快速交付管理Service接口
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
public interface IQuickDeliveryService extends IService<HzArrQuickDelivery> {

    /**
     * 查询快速交付列表
     * @param query 查询条件
     * @return 结果
     */
    List<HzArrQuickDelivery> selectList(QuickDeliveryQuery query);

    /**
     * 查看交付详情
     * @param id 交付id
     * @return 详情
     */
    HzArrQuickDelivery getInfo(Long id);

    /**
     * 新增交付数据
     * @param delivery 新增数据
     * @return 结果
     */
    int add(HzArrQuickDelivery delivery);

    /**
     * 修改交付数据
     * @param delivery 修改数据
     * @return 结果
     */
    int edit(HzArrQuickDelivery delivery);
}
