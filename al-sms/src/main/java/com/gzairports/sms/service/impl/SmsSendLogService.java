package com.gzairports.sms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.sms.domain.SmsSendLogPo;
import com.gzairports.sms.domain.dto.SmsSendQuery;
import com.gzairports.sms.mapper.log.SmsSendLogMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 短信发送记录业务
 * @date 2022/7/5 17:22
 */
@Service
@EnableAsync
public class SmsSendLogService extends ServiceImpl<SmsSendLogMapper,SmsSendLogPo> {

    @Autowired
    private SmsSendLogMapper smsSendLogMapper;

    /**
     * 记录发送日志
     *
     * @param smsSendLogPo
     * @return
     */
    @Async
    public void insertSmsSendLog(SmsSendLogPo smsSendLogPo) {
        smsSendLogMapper.insertSmsSendLog(smsSendLogPo);
    }

    /**
     * 查询短信发送历史记录
     *
     * @param smsSendLogId 短信发送历史记录ID
     * @return 短信发送历史记录
     */
    public SmsSendLogPo selectSmsSendLogById(Long smsSendLogId) {
        return smsSendLogMapper.selectSmsSendLogById(smsSendLogId);
    }

    /**
     * 查询短信发送历史记录列表
     *
     * @param query 短信发送历史记录
     * @return 短信发送历史记录列表
     */
    public List<SmsSendLogPo> selectSmsSendLogList(SmsSendQuery query) {
        return smsSendLogMapper.selectSmsSendLogList(query);
    }

    /**
     * 根据四位运单号查询运单
     * @param code 运单后四位
     * @return 运单集合
     */
    public List<String> getCode(String code) {
        return smsSendLogMapper.getCode(code);
    }
}
