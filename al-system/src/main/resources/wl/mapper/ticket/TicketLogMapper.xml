<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.wl.ticket.mapper.TicketLogMapper">
    <insert id="insertTicketLog" parameterType="TicketLog">
        insert into wl_ticket_log(ticket_id, business_type, request_method, oper_name, oper_param, json_result, status, error_msg, oper_time)
        values (#{ticketId}, #{businessType}, #{requestMethod}, #{operName}, #{operParam}, #{jsonResult}, #{status}, #{errorMsg}, sysdate())
    </insert>
    <select id="selectList" resultType="com.gzairports.wl.ticket.domain.vo.TicketOprLogVO">
        select oper_id, business_type, oper_name, oper_time, status, oper_param, json_result
        from wl_ticket_log where ticket_id = #{id}
    </select>
</mapper>