package com.gzairports.common.bank.domain.resultsQuery;

import lombok.Data;

import java.io.Serializable;

/**
 * @author: lan
 * @Desc: 支付结果查询请求字段
 * @create: 2025-03-18 13:55
 **/

@Data
public class ResultsQueryRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 一级商户号 16 Y
     * 民生银行为签约商户提供的一级商户号，与签约证书一一对应
     */
    private String platformId;

    /** 一级签约编码 20 Y
     * 一级商户号对应的签约编码
     */
    private String merchantNo;

    /** 商户订单号 50 Y
     * 原交易商户订单号
     */
    private String merchantSeq;

    /** 商户流水号 50 Y
     * 本次查询交易流水号(每笔请求重新生成)
     */
    private String mchSeqNo;

    /** 查询流水号 50 Y
     * 查下单送订单号；查退款送退款流水号
     */
    private String querySeq;

}
