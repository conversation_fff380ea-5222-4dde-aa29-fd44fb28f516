<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.wl.departure.mapper.AwaitTransportWaybillMapper">


    <select id="listAwaitTransportWaybill" resultType="com.gzairports.wl.departure.domain.vo.ATWaybillVO">
        select id, waybill_code, des_port, source_port, flight_no1, flight_date1, shipper, cargo_code, cargo_name,
        quantity, weight, charge_weight, write_time, remark, dept_id, shipper, status,
        is_notify, special_cargo_code1, pay_status, cost_sum, pay_time, settle_time, carrier1
        from all_air_waybill
        <where>
            is_del = 0
            and waybill_type = 'AWBA'
            and type = 'DEP'
            and shipper = #{query.shipper}
            <if test="waybillCodeList != null and waybillCodeList.size() > 0">
                and waybill_code in
                <foreach collection="waybillCodeList" item="waybillCode" open="(" separator="," close=")">
                    #{waybillCode}
                </foreach>
            </if>
            <if test="query.startTime != null">
                and write_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and write_time <![CDATA[<=]]> #{query.endTime}
            </if>
        </where>
        order by write_time desc

    </select>
    <select id="selectAwaitTransportWaybillCode" resultType="java.lang.String">
        -- 已支付且未配载的运单号
        SELECT DISTINCT waybill_code
        FROM wl_dep_waybill_trace wdwt1
        WHERE node_name = '预授权支付'
          AND NOT EXISTS (SELECT 1
                          FROM wl_dep_waybill_trace wdwt2
                          WHERE wdwt2.waybill_code = wdwt1.waybill_code
                            AND wdwt2.node_name = '已预配')

        union

        -- 卸下且申请退款
        select distinct waybill_code
        from hz_dep_dis_board hddb
                 left join all_air_waybill aaw on aaw.id = hddb.waybill_id
        where aaw.waybill_code in
              (select distinct waybill_code
               from all_wrong
               where pro_method = '2')

        union

        -- 拉下且申请退款
        select distinct waybill_code
        from hz_dep_pull_down hdpd
        where hdpd.waybill_code in
              (select distinct waybill_code
               from all_wrong
               where pro_method = '2')
    </select>
</mapper>