package com.gzairports.common.business.departure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.reporter.domain.ReportPullDown;
import com.gzairports.common.business.wrong.domain.Wrong;
import com.gzairports.hz.business.cable.domain.vo.FSUJsonVO;
import com.gzairports.hz.business.cable.domain.vo.MsgFlightInfoVO;
import com.gzairports.hz.business.departure.domain.AirWaybill;
import com.gzairports.hz.business.departure.domain.HzDepPullDown;
import com.gzairports.hz.business.departure.domain.query.CableMawbQuery;
import com.gzairports.hz.business.departure.domain.query.ForwardImportQuery;
import com.gzairports.hz.business.departure.domain.query.PullDownQuery;
import com.gzairports.hz.business.departure.domain.query.WaybillInfoQuery;
import com.gzairports.hz.business.departure.domain.vo.*;
import com.gzairports.wl.departure.domain.vo.ReplenishVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 临时拉下Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-26
 */
@Mapper
public interface PullDownMapper extends BaseMapper<HzDepPullDown> {

    /**
     * 根据条件查询拉下数据
     * @param query 查询条件
     * @return 结果
     */
    List<DetailedVo> selectPullDownList(WaybillInfoQuery query);

    /**
     * 根据id和运单号查询拉下数据,flightLoadId为空的情况
     * @param pullId 查询条件
     * @return 结果
     */
    List<DetailedVo> selectPullDownListForFlightLoadIdNull(@Param("pullId") List<Long> pullId);

    /**
     * 根据id和运单号查询拉下数据,flightLoadId不为空的情况
     * @param pullId 查询条件
     * @return 结果
     */
    List<DetailedVo> selectPullDownListForFlightLoadIdNotNull(@Param("pullId") List<Long> pullId);

    /**
     * 根据条件查询拉下数据
     * @param query 查询条件
     * @return 拉下数据
     */
    List<PullDownInfoVo> selectListByQuery(PullDownQuery query);

    /**
     * 根据id查询拉下数据详情
     * @param id 拉下id
     * @return 详情
     */
    PullDownInfoVo selectInfoById(Long id);

    /**
     * 查询运单可补货重量
     * @param waybillCode
     * @return
     */
    ReplenishVo replenish(String waybillCode);


    List<ForwardImportWaybillVo>  selectImportData(@Param("query") ForwardImportQuery query);

    List<ForwardImportUldVo> selectImportUldData(@Param("query") ForwardImportQuery query);

    void updateLoad(@Param("collectId") Long collectId, @Param("flightId") Long flightId);

    List<ForwardImportWaybillVo> selectListByCollectIds(@Param("collect2") List<Long> collect2);

    ForwardImportWaybillVo selectByCollectId(Long collectId);

    List<Long> selectImportDataByCode(String waybillCode);

    List<Long> selectImportUldDataByCode(@Param("waybillCode") String waybillCode,@Param("uld") String uld);

    /**
     * 查询出港拉货报文运单数据
     * @param query 查询条件
     * @return 运单数据
     */
    List<PullCargoVo> selectPullCargoList(CableMawbQuery query);

    List<PullCargoVo> selectPullCargoListByIds(@Param("ids") List<Long> ids);

    Wrong selectReturnCargo(AirWaybill airWaybill);

    Wrong selectOldWrong(AirWaybill airWaybill);

    List<ReportPullDown> selectReportPullDownList(@Param("waybillCodes") List<String> waybillCodes);

    List<HzDepPullDown> selectWaybillList(String waybillCode);

    List<HzDepPullDown> selectNoHandleList(String operTime);

    List<MsgFlightInfoVO> selectFlightByWaybillId(@Param("waybillIdList") List<Long> waybillIdList);

    FSUJsonVO selectFSUDataList(MsgFlightInfoVO infoVo);

    /**
     * 查询当前运单是否已结算完成
     * @param waybillCode 运单号
     * @return 结果
     */
    Integer selectIsSettle(String waybillCode);

    List<Long> selectWaybillCodes(Long flightId);

    List<Long> selectIdByCodes(@Param("waybillCodeList") List<String> waybillCodeList);
}
