package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BaseAirDifferent;
import com.gzairports.common.basedata.domain.query.AirDifferentQuery;
import com.gzairports.common.basedata.service.IAirDifferentService;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 航司机型差异Controller
 *
 * <AUTHOR>
 * @date 2024-02-20
 */
@RestController
@RequestMapping("/base/airDiff")
@Api(tags = "航司机型差异数据接口")
public class AirDifferentController extends BaseController {

    @Autowired
    private IAirDifferentService airDifferentService;

    /**
     * 查询航司机型差异列表
     */
    /*@PreAuthorize("@ss.hasPermi('base:airDiff:airDifferentList')")*/
    @GetMapping("/airDifferentList")
    @ApiOperation(value = "航司机型差异列表")
    public TableDataInfo airDifferentList(AirDifferentQuery query)
    {
        startPage();
        List<BaseAirDifferent> list = airDifferentService.airDifferentList(query);
        return getDataTable(list);
    }

    /**
     * 导出航司机型差异列表
     */
    @PreAuthorize("@ss.hasPermi('base:airDiff:exportAirDifferent')")
    @Log(title = "导出航司机型差异列表", businessType = BusinessType.EXPORT)
    @PostMapping("/exportAirDifferent")
    @ApiOperation(value = "导出航司机型差异列表")
    public void exportAirDifferent(HttpServletResponse response, AirDifferentQuery query)
    {
        List<BaseAirDifferent> list = airDifferentService.airDifferentList(query);
        ExcelUtil<BaseAirDifferent> util = new ExcelUtil<BaseAirDifferent>(BaseAirDifferent.class);
        util.exportExcel(response, list, "航司机型差异列表");
    }

    /**
     * 新增航司机型差异
     */
    @PreAuthorize("@ss.hasPermi('base:airDiff:addAirDifferent')")
    @Log(title = "新增航司机型差异", businessType = BusinessType.INSERT)
    @PostMapping("/addAirDifferent")
    @ApiOperation(value = "新增航司机型差异")
    public AjaxResult addAirDifferent(@RequestBody BaseAirDifferent different)
    {
        return toAjax(airDifferentService.addAirDifferent(different));
    }

    /**
     * 修改航司机型差异
     */
    @PreAuthorize("@ss.hasPermi('base:airDiff:editAirDifferent')")
    @Log(title = "修改航司机型差异", businessType = BusinessType.UPDATE)
    @PostMapping("/editAirDifferent")
    @ApiOperation(value = "修改航司机型差异")
    public AjaxResult editAirDifferent(@RequestBody BaseAirDifferent different)
    {
        return toAjax(airDifferentService.editAirDifferent(different));
    }

    /**
     * 删除航司机型差异
     */
    @PreAuthorize("@ss.hasPermi('base:airDiff:removeAirDifferent')")
    @Log(title = "删除航司机型差异", businessType = BusinessType.DELETE)
    @GetMapping("removeAirDifferent/{id}")
    @ApiOperation(value = "删除航司机型差异")
    public AjaxResult removeAirDifferent(@PathVariable Long id)
    {
        return toAjax(airDifferentService.removeAirDifferent(id));
    }

    /**
     * 详情
     */
    @PreAuthorize("@ss.hasPermi('base:airDiff:getInfo')")
    @GetMapping("/getInfo/{id}")
    @ApiOperation(value = "详情")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(airDifferentService.getInfo(id));
    }
}
