package com.gzairports.hz.business.arrival.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 航班文件查询参数
 * <AUTHOR>
 * @date 2024-07-17
 */
@Data
public class FlightFileQuery {

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date execDate;

    /** 航班号 */
    private String flightNo;

    /** 航段 */
    private String leg;

    /** 运单号 */
    private String waybillCode;

    /** 航班操作 */
    private String flightOper;

    /** 航段id */
    private Long legId;

    /** 理货状态 */
    private String tallyStatus;

    /** 当前页 默认值1*/
    private Integer current = 1;

    /** 页大小 默认值10*/
    private Integer size = 10;

    /** 运单类型 从前端传递 */
    private String type;

    /** 是否审核 0 否 1 是 */
    private Integer isExamine;
}
