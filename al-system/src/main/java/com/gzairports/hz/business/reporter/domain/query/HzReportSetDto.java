package com.gzairports.hz.business.reporter.domain.query;

import com.gzairports.hz.business.reporter.domain.HzReportSetCount;
import com.gzairports.hz.business.reporter.domain.HzReportSetDisplay;
import com.gzairports.hz.business.reporter.domain.HzReportSetFilter;
import com.gzairports.hz.business.reporter.domain.HzReportSetRelation;
import lombok.Data;

import java.util.List;

/**
 * @author: lan
 * @Desc: 报表新增数据
 * @create: 2025-03-06 16:07
 **/

@Data
public class HzReportSetDto {
    /** 报表设置id */
    private Long id;

    /** 报表标题 */
    private String reportTitle;

    /** 报表国内国际类型 I国内D国际 */
    private String reportDomint;

    /** 报表进出港类型 */
    private String reportType;

    /** 报表进出港类型 */
    private String reportRole;

    /** 报表状态 0未发布 1已发布 */
    private Integer reportStatus;

    /** 删除状态 */
    private Integer isDel;

    /** 字段来源 0 主表 1 子表1 2 子表2*/
    private Integer tableField;

    /** 是否显示小计 0否 1是 */
    private Integer fieldSortSubtotal;

    /** 是否为外链（0是 1否） */
    private String isFrame;

    /**页面地址 */
    private String pageSite;

    /** 主子表关联 */
    private HzReportSetRelation hzReportSetRelation;

    /** 字段过滤设置 */
    private List<HzReportSetFilter> hzReportSetFilter;

    /** 字段显示设置 */
    private List<HzReportSetDisplay> hzReportSetDisplay;

    /** 字段合计设置 */
    private List<HzReportSetCount> HzReportSetCount;
}
