package com.gzairports.common.basedata.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.basedata.domain.BasePost;
import com.gzairports.common.basedata.domain.query.BasePostQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 邮局维护Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Mapper
public interface PostMapper extends BaseMapper<BasePost> {

    /**
     * 根据接收局/托运局名称查询邮局信息
     *
     * @param abb 接收局/托运局名称
     * @return 邮局信息
     */
    BasePost getInfoByCode(@Param("abb") String abb, @Param("type") Integer type);

    BasePost getInfoByPost(@Param("port") String port,@Param("type") Integer type);
}
