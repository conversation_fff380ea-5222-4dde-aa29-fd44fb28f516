package com.gzairports.framework.config.jdk8timsjson;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.TimeZone;

/**
 * @Description: 增加对json对 java8 的LocalDateTime ，LocalDate LocalTime 解析的支持，
 *              不在字段上写@JsonFormat的注解就能自动解析
 * @Author: guobangli
 * @Date: 2022/10/9
 */
@Slf4j
@Configuration
//@ConditionalOnClass(ObjectMapper.class)
//@AutoConfigureBefore(JacksonAutoConfiguration.class)
public class JacksonConfig {
//    @Bean
//    public MappingJackson2HttpMessageConverter jackson2HttpMessageConverter()
//    {
//        final Jackson2ObjectMapperBuilder builder = new Jackson2ObjectMapperBuilder();
//        builder.serializationInclusion(JsonInclude.Include.NON_NULL);
//        final ObjectMapper objectMapper = builder.build();
//        SimpleModule simpleModule = new SimpleModule();
//        // Long 转为 String 防止 js 丢失精度
//        simpleModule.addSerializer(Long.class, ToStringSerializer.instance);
//        objectMapper.registerModule(simpleModule);
//        // 忽略 transient 关键词属性
//        objectMapper.configure(MapperFeature.PROPAGATE_TRANSIENT_MARKER, true);
//        // 时区设置
//        objectMapper.setTimeZone(TimeZone.getTimeZone("GMT+8"));
//        objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
//        return new MappingJackson2HttpMessageConverter(objectMapper);
//    }

    /**
     * 初始化Jackson配置 - 类型序列化处理 (去除上面的配置和其他注解)
     * @return
     */
    @Bean
    public Jackson2ObjectMapperBuilderCustomizer customizer() {
        return builder -> {
            // 全局配置序列化返回 JSON 处理
            JavaTimeModule javaTimeModule = new JavaTimeModule();
            javaTimeModule.addSerializer(Long.class, ToStringSerializer.instance);
//        javaTimeModule.addSerializer(Long.class, BigNumberSerializer.INSTANCE);
//        javaTimeModule.addSerializer(Long.TYPE, BigNumberSerializer.INSTANCE);
//        javaTimeModule.addSerializer(BigInteger.class, BigNumberSerializer.INSTANCE);
            //处理BigDecimal类型数据
//            javaTimeModule.addSerializer(BigDecimal.class, ToStringSerializer.instance);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(formatter));
            javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(formatter));
            //忽略空值
            builder.serializationInclusion(JsonInclude.Include.NON_NULL);
            builder.modules(javaTimeModule);
            builder.timeZone(TimeZone.getDefault());
            log.info("初始化 jackson 配置");
        };
    }
}
