package com.gzairports.hz.business.arrival.domain.vo;

import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Created by david on 2024/12/6
 * <AUTHOR>
 */
@Data
public class ArrChargeImportVo {

    /** 办单id */
    private Long id;

    private Long tallyId;

    private Integer isOut;

    /** 提货单号 */
    @Excel(name = "提货单号")
    private String serialNo;

    /** 运单号 */
    @Excel(name = "运单号")
    private String waybillCode;

    /** 件数 */
    @Excel(name = "件数")
    private Integer quantity;

    /** 重量 */
    @Excel(name = "重量")
    private BigDecimal weight;

    /** 办单重量 */
    @Excel(name = "办单重量")
    private BigDecimal handleWeight;

    /** 进处费 */
    @Excel(name = "进处费")
    private BigDecimal processingFee = new BigDecimal(0);

    /** 保管费 */
    @Excel(name = "保管费")
    private BigDecimal storageFee = new BigDecimal(0);

    /** 冷藏费 */
    @Excel(name = "冷藏费")
    private BigDecimal refrigerationFee = new BigDecimal(0);

    /** 搬运费 */
    @Excel(name = "搬运费")
    private BigDecimal handlingFee = new BigDecimal(0);

    /** 电报费 */
    @Excel(name = "电报费")
    private BigDecimal cableCharge = new BigDecimal(0);

    /** 费用合计 */
    @Excel(name = "费用合计")
    private BigDecimal subtotal = new BigDecimal(0);

    /** 收款人(经办人) */
    @Excel(name = "收款人")
    private String handleBy;

}
