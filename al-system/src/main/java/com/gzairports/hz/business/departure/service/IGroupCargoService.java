package com.gzairports.hz.business.departure.service;

import com.gzairports.hz.business.departure.domain.HzDepGroupUld;
import com.gzairports.hz.business.departure.domain.HzDepGroupUldWaybill;
import com.gzairports.hz.business.departure.domain.HzDepGroupWaybill;
import com.gzairports.hz.business.departure.domain.HzDepHandover;
import com.gzairports.hz.business.departure.domain.query.GroupCargoQuery;
import com.gzairports.hz.business.departure.domain.vo.GroupCargoVo;
import com.gzairports.hz.business.departure.domain.vo.TruckInfoVo;

import java.util.List;

/**
 * 组货调度Service接口
 *
 * <AUTHOR>
 * @date 2024-07-03
 */
public interface IGroupCargoService {

    /**
     * 组货调度列表查询
     * @param query 查询参数
     * @return 组货调度列表
     */
    List<GroupCargoVo> selectList(GroupCargoQuery query);

    /**
     * 查看详情
     * @param id 航班配载id
     * @return 结果
     */
    GroupCargoVo getInfo(Long id);

    /**
     * 指派
     * @param flightLoadId 航班配载id
     * @param username 组货员
     * @return 结果
     */
    int assign(Long flightLoadId, String username);

    /**
     * 板车详情
     * @param id 板车id
     * @return 结果
     */
    TruckInfoVo groupDataInfo(Long id);

    /**
     * 站坪交接
     * @param hzDepHandover 交接参数
     * @return 结果
     */
    int handover(HzDepHandover hzDepHandover);

    /**
     * 未放板车货物
     * @param id 航班配载id
     * @return 未放板车货物列表
     */
    List<HzDepGroupWaybill> notLoadCargo(Long id);

    /**
     * 加货运单查询
     * @param id 运单组货id
     * @return 结果
     */
    List<HzDepGroupWaybill> addCargo(Long id);

    /**
     * 根据运单号手动添加
     * @param waybillCode 运单号
     * @return 结果
     */
    HzDepGroupWaybill handAdd(String waybillCode);

    /**
     * 确认加货
     * @param groupUldWaybill 加货参数
     * @return 结果
     */
    int confirmAdd(HzDepGroupUldWaybill groupUldWaybill);

    /**
     * 删除板车
     * @param id 组货板车id
     * @return 结果
     */
    int deleteUld(Long id);

    /**
     * 组货完成
     * @param id 航班配载id
     * @return 结果
     */
    int groupFinish(Long id);

    /**
     * 删除运单
     * @param id 组货运单id
     * @return 结果
     */
    int deleteWaybill(Long id);

    /**
     * 新增板车
     * @param uld 板车数据
     * @return 结果
     */
    HzDepGroupUld addUld(HzDepGroupUld uld);

    /**
     * 货物移动
     * @param groupUldWaybill 移动参数
     * @return 结果
     */
    int move(HzDepGroupUldWaybill groupUldWaybill);

    /**
     * 根据板车号查询板重
     * @param uld 板车号
     * @return 结果
     */
    String selectWeight(String uld);
}
