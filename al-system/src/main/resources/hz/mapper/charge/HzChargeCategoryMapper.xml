<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.charge.mapper.HzChargeCategoryMapper">
    
    <resultMap type="hzChargeCategory" id="HzChargeCategoryResult">
        <result property="id"    column="id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="containCargo"    column="contain_cargo"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectHzChargeCategoryVo">
        select id, category_name, contain_cargo, is_del from hz_charge_category
    </sql>

    <select id="selectHzChargeCategoryList" parameterType="HzChargeCategory" resultMap="HzChargeCategoryResult">
        <include refid="selectHzChargeCategoryVo"/>
        <where>
            and is_del = 0
            <if test="categoryName != null  and categoryName != ''"> and category_name like concat('%', #{categoryName}, '%')</if>
            <if test="containCargo != null  and containCargo != ''"> and contain_cargo like concat('%', #{containCargo}, '%')</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectHzChargeCategoryById" parameterType="Long" resultMap="HzChargeCategoryResult">
        <include refid="selectHzChargeCategoryVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertHzChargeCategory" parameterType="HzChargeCategory" useGeneratedKeys="true" keyProperty="id">
        insert into hz_charge_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">category_name,</if>
            <if test="containCargo != null">contain_cargo,</if>
            <if test="isDel != null">is_del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">#{categoryName},</if>
            <if test="containCargo != null">#{containCargo},</if>
            <if test="isDel != null">#{isDel},</if>
         </trim>
    </insert>

    <update id="updateHzChargeCategory" parameterType="HzChargeCategory">
        update hz_charge_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">category_name = #{categoryName},</if>
            <if test="containCargo != null">contain_cargo = #{containCargo},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteHzChargeCategoryById" parameterType="Long">
        update hz_charge_category set is_del = 1 where id = #{id}
    </update>
</mapper>