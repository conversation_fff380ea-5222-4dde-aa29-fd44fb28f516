<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.wl.charge.mapper.FreightRateCustomMapper">
    <update id="removeRateCustom">
        update wl_sf_custom_price set is_del = 1 where id = #{id}
    </update>
    <select id="selectRateCustomList" resultType="com.gzairports.wl.charge.domain.vo.FreightRateCustomVO">
        select wcfr.id, wcfr.rate_name, wcfr.customer, wcfr.applicable_documents, wcfr.automatic_from_low,
               wcfr.rounding_method, wcfr.effective_date, wcfr.expiration_date, wcfr.remarks
        from wl_sf_custom_price wcfr
        <where>
            wcfr.is_del = 0 and wcfr.dept_id = #{deptId}
            <if test="ids != null  and ids.size() > 0"> and wcfr.id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="rateName != null  and rateName != ''"> and wcfr.rate_name like concat('%',#{rateName},'%')</if>
            <if test="customer != null  and customer != ''"> and wcfr.customer like concat('%',#{customer},'%')</if>
            <if test="applicableDocuments != null and applicableDocuments != ''"> and wcfr.applicable_documents = #{applicableDocuments}</if>
            <if test="effectiveDate != null">
                AND wcfr.effective_date <![CDATA[>=]]> #{effectiveDate}
            </if>
            <if test="expirationDate != null">
                AND wcfr.expiration_date <![CDATA[<=]]> #{expirationDate}
            </if>
        </where>
    </select>
    <select id="selectRateCustomListByIds" resultType="com.gzairports.wl.charge.domain.vo.FreightRateCustomVO">
        select wcfr.id, wcfr.rate_name, wcfr.customer, wcfr.applicable_documents, wcfr.automatic_from_low,
        wcfr.rounding_method, wcfr.effective_date, wcfr.expiration_date, wcfr.remarks
        from wl_sf_custom_price wcfr
        where
        wcfr.is_del = 0 and
            id in
            <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
    </select>
</mapper>