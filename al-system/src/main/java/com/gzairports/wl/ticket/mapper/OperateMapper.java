package com.gzairports.wl.ticket.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.wl.ticket.domain.TicketCtrl;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 票证控制管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Mapper
public interface OperateMapper extends BaseMapper<TicketCtrl> {

    /**
     * 查询单证控制管理列表
     *
     * @return 证控制管理列表
     */
    List<TicketCtrl> selectCtrlList();
}
