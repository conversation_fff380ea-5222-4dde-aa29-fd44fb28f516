package com.gzairports.common.basedata.domain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;


/**
 * 专为新增收运的对象
 *
 * <AUTHOR>
 * @date 2024-02-19
 */
@Data
public class BaseCargoUldForSave {

    private BaseCargoUld baseCargoUld;

    /** 自重 */
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal deadWeight;

    /**  过磅重量 */
    private BigDecimal weight;

    /** 编号 */
    private String code;

    /** 类型 */
    private String type;

    /** 目的站 */
    private String desPort;

    /** 承运人 */
    private String carrier;

    /** 当前配载运单号 */
    private String waybillCode;
}
