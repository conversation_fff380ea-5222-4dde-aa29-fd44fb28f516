package com.gzairports.common.charge.service;


import com.gzairports.common.charge.domain.HzChargeRule;

import java.util.List;


/**
 * 计费规则Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-13
 */
public interface IHzChargeRuleService 
{
    /**
     * 查询计费规则
     * 
     * @param id 计费规则主键
     * @return 计费规则
     */
    public HzChargeRule selectHzChargeRuleById(Long id);

    /**
     * 查询计费规则列表
     * 
     * @param hzChargeRule 计费规则
     * @return 计费规则集合
     */
    public List<HzChargeRule> selectHzChargeRuleList(HzChargeRule hzChargeRule);

    /**
     * 新增计费规则
     * 
     * @param hzChargeRule 计费规则
     * @return 结果
     */
    public int insertHzChargeRule(HzChargeRule hzChargeRule);

    /**
     * 修改计费规则
     * 
     * @param hzChargeRule 计费规则
     * @return 结果
     */
    public int updateHzChargeRule(HzChargeRule hzChargeRule);


    /**
     * 删除计费规则信息
     * 
     * @param id 计费规则主键
     * @return 结果
     */
    public int deleteHzChargeRuleById(Long id);
}
