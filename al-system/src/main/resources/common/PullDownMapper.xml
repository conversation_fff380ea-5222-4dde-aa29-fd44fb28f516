<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.departure.mapper.PullDownMapper">
    <update id="updateLoad">
        update hz_dep_pull_down
        set is_load = 1, flight_id_new = #{flightId}, is_handle = 1
        where id = #{collectId}
    </update>
    <select id="selectPullDownList" resultType="com.gzairports.hz.business.departure.domain.vo.DetailedVo">
        select afi.exec_date as createTime, CONCAT(afi.air_ways,afi.flight_no) AS flight_no, hdp.uld, hdp.quantity, hdp.weight,hdp.oper_time, hdp.oper_name, hdp.remark
        from hz_dep_pull_down hdp
            left join all_flight_info afi on afi.flight_id = hdp.flight_id
        <where>
            <if test="waybillCode != null and waybillCode != ''">
                hdp.waybill_code = #{waybillCode}
            </if>
        </where>
    </select>
    <select id="selectListByQuery" resultType="com.gzairports.hz.business.departure.domain.vo.PullDownInfoVo">
        select hdpd.id, aaw.id as waybill_id, hdpd.exec_date, hdpd.flight_no, hdpd.waybill_code, aaw.write_time,
               aaw.agent_code, hdpd.quantity, hdpd.weight, hdpd.oper_time, hdpd.load_quantity, hdpd.load_weight, hdpd.remark
        from hz_dep_pull_down hdpd
            left join all_air_waybill aaw on aaw.waybill_code = hdpd.waybill_code
        <where>
            aaw.type = 'DEP'
            <if test="startExecDate != null">
                and hdpd.exec_date <![CDATA[>=]]> #{startExecDate}
            </if>
            <if test="endExecDate != null">
                and hdpd.exec_date <![CDATA[<=]]> #{endExecDate}
            </if>
            <if test="flightNo != null and flightNo != ''">
                and hdpd.flight_no like CONCAT('%',#{flightNo},'%')
            </if>
            <if test="agentCode != null and agentCode != ''">
                and aaw.agent_code like CONCAT('%',#{agentCode},'%')
            </if>
            <if test="waybillCode != null and waybillCode != ''">
                and hdpd.waybill_code like CONCAT('%',#{waybillCode},'%')
            </if>
        </where>
    </select>
    <select id="selectInfoById" resultType="com.gzairports.hz.business.departure.domain.vo.PullDownInfoVo">
        select hdpd.id, hdpd.waybill_code, hdpd.uld_weight, hdpd.plate_weight, hdpd.quantity, hdpd.weight, hdpd.uld,
               hdpd.flight_no, hdpd.exec_date, hdpd.store, hdpd.locator, hdpd.remark, hdpd.load_quantity, hdpd.load_weight
        from hz_dep_pull_down hdpd
            left join all_flight_info afi on afi.flight_id = hdpd.flight_id
        where hdpd.id = #{id}
    </select>
    <select id="replenish" resultType="com.gzairports.wl.departure.domain.vo.ReplenishVo">
        select sum(quantity) as replenishNum, sum(weight) as canRestockWeight, sum(weight) as replenishWeight
        from hz_dep_pull_down
        where waybill_code = #{waybillCode} group by waybill_code
    </select>
    <select id="selectPullDownListForFlightLoadIdNotNull"
            resultType="com.gzairports.hz.business.departure.domain.vo.DetailedVo">
        select afi.exec_date as createTime, CONCAT(afi.air_ways,afi.flight_no) AS flight_no, hdp.uld, hdp.quantity, hdp.weight,hdp.oper_time, hdp.oper_name, hdp.remark
        from hz_dep_pull_down hdp
        left join all_flight_info afi on afi.flight_id = hdp.flight_id
        <where>
            <if test="pullId != null">
                hdp.id in
                <foreach collection="pullId" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectPullDownListForFlightLoadIdNull"
            resultType="com.gzairports.hz.business.departure.domain.vo.DetailedVo">
        select hdp.exec_date as createTime , hdp.flight_no, hdp.uld, hdp.quantity, hdp.weight,hdp.oper_time, hdp.oper_name, hdp.remark
        from hz_dep_pull_down hdp
        <where>
            <if test="PullId != null">
                hdp.id in
                <foreach collection="pullId" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectImportData"
            resultType="com.gzairports.hz.business.departure.domain.vo.ForwardImportWaybillVo">
        select hdpd.id as collectId, hdpd.id as weightId, aaw.id as waybillId, aaw.cargo_name, aaw.special_cargo_code1, aaw.waybill_code,
               aaw.des1, hdpd.quantity, hdpd.weight, @num := 1 as type, aaw.write_time, aaw.quantity as waybillQuantity, aaw.weight as waybillWeight,
               aaw.storage_transport_notes,sd.dept_name_abb
        from hz_dep_pull_down hdpd
        left join all_wrong aw on hdpd.id = aw.pull_id
        left join all_air_waybill aaw on hdpd.waybill_code = aaw.waybill_code
        left join sys_dept sd on sd.dept_id = aaw.dept_id
        <where>
            aw.pro_method = '0' and hdpd.is_load = 0 and (hdpd.uld is null or hdpd.uld = '') and aaw.status != 'INVALID' and aaw.type = 'DEP'
            <if test="query.waybillCode != null  and query.waybillCode != ''"> and hdpd.waybill_code = #{query.waybillCode}</if>
            <if test="query.flightNo1 != null  and query.flightNo1 != ''"> and aaw.flight_no1 like concat('%',#{query.flightNo1},'%')</if>
            <if test="query.flightDate1 != null"> and aaw.flight_date1 = #{query.flightDate1}</if>
            <if test="query.sourcePort != null  and query.sourcePort != ''"> and aaw.source_port = #{query.sourcePort}</if>
            <if test="query.startStation != null  and query.startStation != ''"> and aaw.source_port = #{query.startStation}</if>
            <if test="query.carrierList != null and query.carrierList.size() > 0">
                and aaw.carrier1 in
                <foreach collection="query.carrierList" item="code" index="index" open="(" close=")" separator=",">
                    #{code}
                </foreach>
            </if>
            <if test="query.des1 != null  and query.des1 != ''"> and aaw.des1 = #{query.des1}</if>
            <if test="query.specialCargoCode1 != null  and query.specialCargoCode1 != ''"> and aaw.special_cargo_code1 = #{query.specialCargoCode1}</if>
            <if test="query.shipper != null  and query.shipper != ''"> and aaw.shipper = #{query.shipper}</if>
            <if test="query.startTime != null">
                and aaw.write_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and aaw.write_time <![CDATA[<=]]> #{query.endTime}
            </if>
        </where>
    </select>
    <select id="selectImportUldData"
            resultType="com.gzairports.hz.business.departure.domain.vo.ForwardImportUldVo">
        select hdpd.id as weightId, hdpd.id as collectId, hdpd.uld, hdpd.quantity as totalQuantity, hdpd.weight as totalWeight, @num := 1 as type,
               aaw.quantity as waybillQuantity, aaw.weight as waybillWeight
        from hz_dep_pull_down hdpd
        left join all_wrong aw on hdpd.id = aw.pull_id
        left join all_air_waybill aaw on hdpd.waybill_code = aaw.waybill_code
        <where>
            hdpd.is_load = 0 and (hdpd.uld != '' or hdpd.uld is not null)
            and aaw.status != 'INVALID' and aaw.type = 'DEP'
            and aw.pro_method = '0'
            <if test="query.des1 != null and query.des1 != ''">
                and aaw.des1 = #{query.des1}
            </if>
            <if test="query.carrierList != null and query.carrierList.size() > 0">
                and aaw.carrier1 in
                <foreach collection="query.carrierList" item="code" index="index" open="(" close=")" separator=",">
                    #{code}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectListByCollectIds"
            resultType="com.gzairports.hz.business.departure.domain.vo.ForwardImportWaybillVo">
        select hdpd.id as collectId, hdpd.id as weightId, aaw.id as waybillId, aaw.cargo_name, aaw.special_cargo_code1, aaw.waybill_code,
               aaw.source_port, aaw.des1, hdpd.quantity, hdpd.weight, @num := 1 as type, aaw.flight_date1 as flightDate, aaw.write_time,
               aaw.quantity as waybillQuantity, aaw.weight as waybillWeight
        from hz_dep_pull_down hdpd
            left join all_air_waybill aaw on hdpd.waybill_code = aaw.waybill_code
         <where>
             aaw.type = 'DEP' and hdpd.id in
             <foreach collection="collect2" item="id" open="(" separator="," close=")">
                 #{id}
             </foreach>
         </where>
    </select>
    <select id="selectByCollectId"
            resultType="com.gzairports.hz.business.departure.domain.vo.ForwardImportWaybillVo">
        select hdpd.id as collectId, hdpd.id as weightId, aaw.id as waybillId, aaw.cargo_name, aaw.special_cargo_code1, aaw.waybill_code, aaw.source_port,
               aaw.des1, hdpd.quantity, hdpd.weight, @num := 1 as type
        from hz_dep_pull_down hdpd
            left join all_air_waybill aaw on hdpd.waybill_code = aaw.waybill_code where hdpd.id = #{collectId} and aaw.type = 'DEP'
    </select>
    <select id="selectImportDataByCode" resultType="java.lang.Long">
        select hdpd.id from hz_dep_pull_down hdpd
        left join all_wrong aw on hdpd.id = aw.pull_id
        left join all_air_waybill aaw on hdpd.waybill_code = aaw.waybill_code
          where aw.pro_method = '0' and hdpd.waybill_code = #{waybillCode} and hdpd.is_load = 0 and (hdpd.uld is null or hdpd.uld = '')
    </select>
    <select id="selectImportUldDataByCode" resultType="java.lang.Long">
        select hdpd.id from hz_dep_pull_down hdpd
                                left join all_wrong aw on hdpd.id = aw.pull_id
                                left join all_air_waybill aaw on hdpd.waybill_code = aaw.waybill_code
        where aw.pro_method = '0' and hdpd.waybill_code = #{waybillCode}
          and hdpd.is_load = 0 and hdpd.uld = #{uld} and aaw.type = 'DEP'
    </select>
    <select id="selectPullCargoList" resultType="com.gzairports.hz.business.departure.domain.vo.PullCargoVo">
        (
            select id, waybill_code, exec_date, flight_no, quantity, weight from hz_dep_pull_down
            <where>
                <if test="wrongType != null and wrongType!= ''">and 1 = 1 </if>
                <if test="waybillCode != null  and waybillCode != ''"> and waybill_code = #{waybillCode}</if>
                <if test="flightNo != null  and flightNo != ''"> and flight_no like '%${flightNo}%'</if>
                <if test="flightDate != null"> and exec_date = #{flightDate}</if>
            </where>
        ) union all
        (
        select hddb.id, aaw.waybill_code, afi.exec_date, CONCAT(afi.air_ways,afi.flight_no) AS flight_no, hddb.quantity, hddb.weight
        from hz_dep_dis_board hddb
            left join all_air_waybill aaw on hddb.waybill_id = aaw.id
            left join all_flight_info afi on hddb.flight_id = afi.flight_id
        <where>
            <if test="wrongType != null and wrongType!= ''">and 1 = 0</if>
            <if test="waybillCode != null  and waybillCode != ''"> and waybill_code = #{waybillCode}</if>
            <if test="flightNo != null  and flightNo != ''"> and flight_no like '%${flightNo}%'</if>
            <if test="flightDate != null"> and exec_date = #{flightDate}</if>
        </where>
        )
    </select>
    <select id="selectPullCargoListByIds"
            resultType="com.gzairports.hz.business.departure.domain.vo.PullCargoVo">
        (
        select hdpd.id, hdpd.waybill_code, hdpd.exec_date, hdpd.flight_no, hdpd.quantity, hdpd.weight, aaw.des_port, @num := 'OFLD' as type from hz_dep_pull_down hdpd
        left join all_air_waybill  aaw on hdpd.waybill_code = aaw.waybill_code
        <where>
            (aaw.type = 'DEP' or (aaw.type = 'ARR' and aaw.transfer_bill = 1)) and aaw.is_del = 0
            and hdpd.id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
        ) union all
        (
        select hddb.id, aaw.waybill_code, afi.exec_date, CONCAT(afi.air_ways,afi.flight_no) AS flight_no, hddb.quantity, hddb.weight, aaw.des_port,
               @num := '' as type
        from hz_dep_dis_board hddb
        left join all_air_waybill aaw on hddb.waybill_id = aaw.id
        left join all_flight_info afi on hddb.flight_id = afi.flight_id
        <where>
            (aaw.type = 'DEP' or (aaw.type = 'ARR' and aaw.transfer_bill = 1)) and aaw.is_del = 0
            and hddb.id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
        )
    </select>
    <select id="selectReturnCargo" resultType="com.gzairports.common.business.wrong.domain.Wrong">
        select SUM(pullWeight) AS pullWeight, SUM(pullQuantity) AS pullQuantity
        from (select sum(hdpd.weight) as pullWeight, sum(hdpd.quantity) as pullQuantity
              from hz_dep_pull_down hdpd
                       left join all_wrong aw on hdpd.id = aw.pull_id
              where aw.pro_method = '1'
                and hdpd.is_load = 0
                and hdpd.waybill_code = #{waybillCode}
              union all
              select sum(hddb.weight) as pullWeight, sum(hddb.quantity) as pullQuantity
              from hz_dep_dis_board hddb
              where hddb.is_load = 0
                and hddb.waybill_id = #{id}) as table1
    </select>
    <select id="selectReportPullDownList"
            resultType="com.gzairports.common.business.reporter.domain.ReportPullDown">
        select aaw.id as reportId, aaw.agent_company as agentCompany, hdpd.waybill_code, hdpd.flight_id, hdpd.oper_time, hdpd.quantity, hdpd.weight, hdpd.load_weight,
               hdpd.load_quantity, hdpd.flight_id_new, afi.exec_date as flight_date, CONCAT(afi.air_ways,afi.flight_no) AS flightNo, hdpd.remark as downReason,
        aaw.cargo_name, aaw.cargo_code, hdpd.oper_name
        from hz_dep_pull_down hdpd
            left join all_flight_info afi on hdpd.flight_id = afi.flight_id
            left join all_air_waybill  aaw on hdpd.waybill_code = aaw.waybill_code
        where 1 = 1
        <if test="waybillCodes != null and waybillCodes.size() > 0">
            and hdpd.waybill_code in
            <foreach collection="waybillCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
    </select>
    <select id="selectWaybillList" resultType="com.gzairports.hz.business.departure.domain.HzDepPullDown">
        select quantity, weight from hz_dep_pull_down where waybill_code = #{waybillCode}
    </select>
    <select id="selectNoHandleList" resultType="com.gzairports.hz.business.departure.domain.HzDepPullDown">
        select * from hz_dep_pull_down where oper_time <![CDATA[<=]]> #{operTime} and is_handle = 0 and is_load = 0
    </select>
    <select id="selectFlightByWaybillId"
            resultType="com.gzairports.hz.business.cable.domain.vo.MsgFlightInfoVO">
        (
        select hdpd.id, afi.flight_id, hdpd.waybill_code
        from hz_dep_pull_down hdpd
            left join all_flight_info afi on hdpd.flight_id = afi.flight_id
        <where>
            1=1
            and hdpd.id in
            <foreach collection="waybillIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
        ) union all
        (
        select hddb.id, afi.flight_id, aaw.waybill_code
        from hz_dep_dis_board hddb
        left join all_air_waybill aaw on hddb.waybill_id = aaw.id
        left join all_flight_info afi on hddb.flight_id = afi.flight_id
        <where>
            1=1
            and hddb.id in
            <foreach collection="waybillIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
        )
    </select>
    <select id="selectFSUDataList" resultType="com.gzairports.hz.business.cable.domain.vo.FSUJsonVO">
        (
        select aaw.source_port as depAirport, aaw.des_port as desAirport, aaw.waybill_code as mawbNo,
               hdpd.quantity as pieces, hdpd.weight, hdpd.oper_time, aaw.quantity as totalPieces
        from hz_dep_pull_down hdpd
            left join all_air_waybill aaw on hdpd.waybill_code = aaw.waybill_code
        where hdpd.flight_id = #{flightId} and hdpd.id = #{id} and aaw.type = 'DEP'
        ) union all
        (
        select aaw.source_port as depAirport, aaw.des_port as desAirport, aaw.waybill_code as mawbNo,
               hddb.quantity as pieces, hddb.weight, hddb.oper_time, aaw.quantity as totalPieces
        from hz_dep_dis_board hddb
        left join all_air_waybill aaw on hddb.waybill_id = aaw.id
        where hddb.flight_id = #{flightId} and hddb.id = #{id} and aaw.type = 'DEP'
        )
    </select>
    <select id="selectIsSettle" resultType="java.lang.Integer">
        select count(1) from hz_dep_pull_down hdpd
            left join all_flight_info afi on afi.flight_id = hdpd.flight_id
        where hdpd.waybill_code = #{waybillCode} and afi.is_settle = 0
    </select>
    <select id="selectOldWrong" resultType="com.gzairports.common.business.wrong.domain.Wrong">
        select SUM(pullWeight) AS pullWeight, SUM(pullQuantity) AS pullQuantity
        from (select sum(hdpd.weight) as pullWeight, sum(hdpd.quantity) as pullQuantity
              from hz_dep_pull_down hdpd
                       left join all_wrong aw on hdpd.id = aw.pull_id
              where hdpd.is_load = 0
                and hdpd.waybill_code = #{waybillCode}
              union all
              select sum(hddb.weight) as pullWeight, sum(hddb.quantity) as pullQuantity
              from hz_dep_dis_board hddb
              where hddb.is_load = 0
                and hddb.waybill_id = #{id}) as table1
    </select>
    <select id="selectWaybillCodes" resultType="java.lang.Long">
        select distinct aaw.id
        from hz_dep_pull_down hdpd
            left join all_air_waybill aaw on hdpd.waybill_code = aaw.waybill_code
        where hdpd.is_handle = 0 and hdpd.flight_id = #{flightId}
    </select>
    <select id="selectIdByCodes" resultType="java.lang.Long">
        select aaw.id from hz_dep_pull_down hdpd
            left join all_air_waybill aaw on hdpd.waybill_code = aaw.waybill_code
                      where aaw.waybill_code in
        <foreach item="code" collection="codes" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>
</mapper>
