package com.gzairports.wl.reporter.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gzairports.common.business.reporter.domain.ReportDataHawb;
import com.gzairports.wl.reporter.domain.query.GoodsWeightStatisticsQuery;
import com.gzairports.wl.reporter.domain.query.HawbSalesQuery;
import com.gzairports.wl.reporter.domain.query.ReportExportData;
import com.gzairports.wl.reporter.domain.vo.GoodsWeightStatisticsVO;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2025-02-21
 */
public interface IHawbSalesDailyService {

    /**
     * 查询分单销售表格
     * @param query 查询条件
     * @return 报表数据
     */
    ReportExportData queryExportDataList(HawbSalesQuery query);

    /**
     * 导出报表数据
     * @param reportExportData 报表数据
     * @param response 响应
     */
    void export(ReportExportData reportExportData, HttpServletResponse response);

    /**
     * 分页查询报表数据
     * @param query 查询参数
     * @return 结果
     */
    IPage<ReportDataHawb> pageQuery(HawbSalesQuery query);

    /**
     * 打印报表
     * @param reportExportData 数据
     * @param response 响应
     */
    void printPDF(ReportExportData reportExportData , HttpServletResponse response);

    /**
     * 查询货量统计
     * @param query 查询参数
     * @return 统计数据
     */
    GoodsWeightStatisticsVO selectGoodsWeightStatistics(GoodsWeightStatisticsQuery query);
}
