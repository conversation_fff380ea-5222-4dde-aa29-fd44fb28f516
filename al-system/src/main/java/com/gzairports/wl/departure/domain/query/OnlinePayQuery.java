package com.gzairports.wl.departure.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 货站在线缴费查询参数
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
public class OnlinePayQuery {

    /** 填开时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 填开时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 运单号 */
    private String waybillCode;

    /** 缴费状态 0 未授权支付 1 已授权支付 2 已结算 */
    private Integer payStatus;

    /** 特货代码1 */
    private String specialCargoCode1;

    /** 货品代码 */
    private String cargoCode;

    /** 目的站 */
    private String desPort;

    /** 所属单位 */
    private Long deptId;

    private Integer pageNum = 1;

    private Integer pageSize = 10;
}
