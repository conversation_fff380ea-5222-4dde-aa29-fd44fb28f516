package com.gzairports.common.business.departure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzairports.common.basedata.domain.query.WayBillH5Query;
import com.gzairports.common.business.arrival.domain.query.PickUpQuery;
import com.gzairports.common.business.arrival.domain.vo.HzArrItemVo;
import com.gzairports.common.business.arrival.domain.vo.PickUpVo;
import com.gzairports.common.business.arrival.domain.vo.WaybillItemVo;
import com.gzairports.common.business.departure.domain.Mawb;
import com.gzairports.common.business.departure.domain.vo.ItemDetailVo;
import com.gzairports.common.business.departure.domain.vo.ItemWaybillVo;
import com.gzairports.common.securitySubmit.domain.SecurityWaybillInfo;
import com.gzairports.hz.business.departure.domain.HzColdRegister;
import com.gzairports.hz.business.departure.domain.query.CableMawbQuery;
import com.gzairports.hz.business.departure.domain.vo.CableMawbVo;
import com.gzairports.hz.business.departure.domain.vo.SpecialCargoVo;
import com.gzairports.wl.departure.domain.query.HawbQuery;
import com.gzairports.wl.departure.domain.query.MergeQuery;
import com.gzairports.wl.departure.domain.query.OnlinePayQuery;
import com.gzairports.wl.departure.domain.query.TransFerWaybillsVo;
import com.gzairports.wl.departure.domain.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 主单制单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@Mapper
public interface MawbMapper extends BaseMapper<Mawb> {

    /**
     * 报文数据收集运单列表数据
     * @param query 查询参数
     * @return 运单列表
     */
    List<CableMawbVo> selectWaybillList(CableMawbQuery query);

    /**
     * 根据代理人公司和code码查询代理人账号
     * @param agentCom 代理人公司
     * @param agentCode code码
     * @return 代理人账号
     */
    String selectAgent(@Param("agentCode") String agentCode, @Param("agentCom") String agentCom);

    /**
     * 根据条件查询主单信息
     * @param query 查询条件
     * @return 主单列表
     */
    List<MergeMawbVo> selectListByQuery(MergeQuery query);

    /**
     * 根据条件查询货站在线缴费列表
     * @param query 查询条件
     * @return 货站在线缴费列表
     */
    Page<OnlineMawbVo> selectPay(@Param("query") OnlinePayQuery query,@Param("page") Page<OnlineMawbVo> page);

    /**
     * 根据id查询货站在线缴费详情
     * @param id 运单id
     * @return 货站在线缴费详情
     */
    OnlineInfoVo selectInfo(Long id);

    /**
     * 根据id集合查询运单详情列表
     * @param mawbIds 运单id集合
     * @param deptId 所属单位
     * @return 运单详情列表
     */
    List<TransferMawbVo> selectByIds(@Param("mawbIds") List<Long> mawbIds,@Param("deptId") Long deptId);

    /**
     * 查询运单跟踪主运单号
     * @param mawbId 主单id
     * @param deptId 部门id
     * @return 结果
     */
    String selectCodeById(@Param("mawbId") Long mawbId,@Param("deptId") Long deptId);

    /**
     * 根据运单号查询主运单信息
     * @param waybillCode 主运单号
     * @param deptId 所属单位
     * @return 运单跟踪信息
     */
    WaybillTraceVo selectCodeByCode(@Param("waybillCode") String waybillCode, @Param("deptId") Long deptId);

    /**
     * 根据进港和出港运单号查询主运单信息
     * @param waybillCode 主运单号
     * @param deptId 所属单位
     * @return 运单跟踪信息
     */
    List<WaybillTraceVo> selectAllCodeByCode(@Param("waybillCode") String waybillCode, @Param("deptId") Long deptId);

    /**
     * 国内主单查询
     * @param query 查询条件
     * @return 结果
     */
    List<MawbQueryVo> queryList(HawbQuery query);

    /**
     * 获取可交接运单
     *
     * @param vo 查询参数
     * @return 交接运单列表
     */
    List<TransferMawbVo> getWaybillList(TransFerWaybillsVo vo);

    /**
     * 批量挑单
     * @param query 查询参数
     * @return 运单数据
     */
    List<PickUpVo> batch(PickUpQuery query);

    /**
     * 打印主单标签
     * @param waybillCode 运单号
     * @return 主单标签数据
     */
    PrintMawbVo printMawb(String waybillCode);

    /**
     * 更新运单安检申报单地址
     * @param id 运单id
     * @param securityUrl 安检申报单地址
     */
    void updateSecurityUrl(@Param("id") Long id,@Param("securityUrl") String securityUrl);

    /**
     * 上传随附文件
     * @param vo 上传参数
     * @return 结果
     */
    int uploadTransportFile(TransportFileVo vo);

    /**
     * 查询随附文件
     * @param waybillId 运单id
     * @return 结果
     */
    TransportFileVo selectTransportFile(String waybillId);

    /**
     * 根据运单号查询运单目的地
     * @param waybillCode 运单号
     * @return 目的地
     */
    String selectDesPort(String waybillCode);

    /**
     * 根据选择的id查询运单数据
     * @param ids 选择的id
     * @return 运单数据
     */
    List<SpecialCargoVo> selectSpecialWaybillListByIds(@Param("ids") List<Long> ids);

    /**
     * 根据运单号查询部门
     * @param waybillCode 运单号
     * @return 部门
     */
    WaybillItemVo selectDeptId(String waybillCode);

    /**
     * 查询制单时间
     * @param waybillCode 运单号
     * @return 结果
     */
    Date selectWriteTime(String waybillCode);

    ItemWaybillVo selectWaybillItemInfo(ItemDetailVo vo);

    HzArrItemVo selectByCode(String waybillCode);

    String selectColdStore(String waybillCode);

    /**
     * 根据运单号查询运单详情
     * @param waybillCode 运单号
     * @return 运单详情
     */
    SecurityWaybillInfo getInfo(String waybillCode);

    Mawb selectIdByCode(String waybillCode);

    Long selectId(String waybillCode);

    Integer selectBillStatus(String waybillCode);

    Mawb selectInfoByCode(String originBill);

    Mawb selectChargeWeight(String waybillCode);

    Mawb selectWeight(@Param("waybillCode") String waybillCode,@Param("deptId") Long deptId);

    Mawb selectComeInfo(@Param("waybillCode") String waybillCode, @Param("type") String type, @Param("deptId") Long deptId);

    Mawb selectComeInfoNew(@Param("waybillCode") String waybillCode, @Param("type") String type, @Param("deptIds") List<Long> deptIds);

    Integer selectPayCount(@Param("page") Page page,@Param("query") OnlinePayQuery query);

    List<String> selectByStatus(@Param("query") OnlinePayQuery query,@Param("status") int payStatus);

    BigDecimal selectRefund(@Param("page") Page page,@Param("query") OnlinePayQuery query);

    Integer queryListCount(@Param("query") HawbQuery query,@Param("page") Page page);

    Integer queryListQuantity(@Param("query")HawbQuery query,@Param("page") Page page);

    BigDecimal queryListWeight(@Param("query")HawbQuery query,@Param("page") Page page);

    Page<MawbQueryVo> queryListNew(@Param("query")HawbQuery query,@Param("page") Page<MawbQueryVo> page);

    /** 出港主单查询 */
    Page<MawbQueryVo> waybillQueryListNew(@Param("query")HawbQuery query,@Param("page") Page<MawbQueryVo> page);

    MawbQueryVo waybillQueryInfo(@Param("query")HawbQuery query,@Param("vo") MawbQueryVo vo);

    HzColdRegister selectCargoName(@Param("waybillCode") String waybillCode, @Param("type") String type,@Param("deptId") Long deptId);

    void updatePayMoney(@Param("id") Long id,@Param("payMoney") BigDecimal payMoney);

    Mawb selectInfoByWaybill(String waybillCode);

    Long selectDeptIdByCode(String waybillCode);

    Mawb selectpullWaybillInfo(String waybillCode);

    Integer selectPayStatus(@Param("waybillCode") String waybillCode,@Param("deptId") Long DeptId);

    Page<Mawb> listWaybill(@Param("page") Page<Mawb> page,@Param("query")  WayBillH5Query query);

    MawbQueryVo queryFlightInfo(@Param("flightDate")Date flightDate, @Param("flightNo")String flightNo);

    List<NewWaybillTraceVO> selectNewTraceList(@Param("codeList") List<String> codeList);
}
