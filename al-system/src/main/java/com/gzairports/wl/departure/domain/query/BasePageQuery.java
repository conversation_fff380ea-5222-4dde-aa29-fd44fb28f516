package com.gzairports.wl.departure.domain.query;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;

@Data
public class BasePageQuery {
    /**
     * 当前页码
     */
    private Integer pageNum = 1;

    /**
     * 每页数量
     */
    private Integer pageSize = 10;

    private final static int DEFAULT_PAGE_NUM = 1;
    private final static int DEFAULT_PAGE_SIZE = 10;

    public <T> Page<T> build(){
        Integer pageNum = ObjectUtil.defaultIfNull(getPageNum(), DEFAULT_PAGE_NUM);
        Integer pageSize = ObjectUtil.defaultIfNull(getPageSize(), DEFAULT_PAGE_SIZE);
        if (pageNum <= 0) {
            pageNum = DEFAULT_PAGE_NUM;
        }
        if (pageSize <= 0) {
            pageNum = DEFAULT_PAGE_SIZE;
        }
        return new Page<>(pageNum, pageSize);
    }

    public <T> Page<T> build(int pageNum, int pageSize) {
        return new Page<>(pageNum, pageSize);
    }
}
