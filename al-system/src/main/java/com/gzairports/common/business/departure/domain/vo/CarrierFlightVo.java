package com.gzairports.common.business.departure.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 配载航班数据
 *
 * <AUTHOR>
 * @date 2024-08-02
 */
@Data
public class CarrierFlightVo {

    /** 航班id */
    private Long flightId;

    /** 承运航班号 */
    private String flightNo;

    /** 承运航班号日期 */
    private Date execDate;

    /** 承运航班件数 */
    private Integer quantity;

    /** 承运航班重量 */
    private BigDecimal weight;
}
