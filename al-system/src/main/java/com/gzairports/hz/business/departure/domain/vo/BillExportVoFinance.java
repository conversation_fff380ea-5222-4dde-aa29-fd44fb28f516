package com.gzairports.hz.business.departure.domain.vo;

import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 财务报表导出
 * @date 2025-07-26
 **/
@Data
public class BillExportVoFinance {
    /**
     * 标题
     */
    @Excel(name = "标题", needMerge = true)
    private String title;

    /**
     * 结算周期
     */
    @Excel(name = "结算周期", needMerge = true)
    private String settlementCycle;

    /**
     * 备注
     */
    @Excel(name = "备注", needMerge = true)
    private String remarks;

    /**
     * 充值金额
     */
    @Excel(name = "本期充值金额", needMerge = true, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal rechargeAmount = BigDecimal.ZERO;

    /**
     * 上期余额
     * */
    @Excel(name = "本期系统余额", needMerge = true, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal thisBalance = BigDecimal.ZERO;

    /**
     * 进港金额
     * */
    @Excel(name = "本期出港已结算", needMerge = true, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal depAmount = BigDecimal.ZERO;

    /**
     * 进港金额
     * */
    @Excel(name = "本期进港已结算", needMerge = true, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal arrAmount = BigDecimal.ZERO;

    /**
     * 总未结算
     * */
    @Excel(name = "总未结算", needMerge = true, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal notSettleAmount = BigDecimal.ZERO;

    /**
     * 本期财务余额
     */
    @Excel(name = "本期财务余额", needMerge = true, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal thisTimeFinanceBalance = BigDecimal.ZERO;

    /**
     * 上期财务余额
     */
    @Excel(name = "上期财务余额", needMerge = true, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal lastTimeFinanceBalance = BigDecimal.ZERO;

    /**
     * 出港已结算
     * */
    private List<ChargeBilExportVO> settledAmountList;

    /**
     * 进港已结算
     * */
    private List<ChargeBillArrSettleExportVO> arrAmountList;

    /**
     * 总未结算
     * */
    private List<ChargeBilExportNotSettleVO> notSettleAmountList;
}
