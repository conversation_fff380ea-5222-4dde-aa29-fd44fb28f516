package com.gzairports.common.charge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.charge.domain.HzChargeItems;
import com.gzairports.common.charge.domain.query.ItemsQuery;
import com.gzairports.common.charge.domain.vo.HzItemsVo;
import com.gzairports.common.charge.domain.vo.HzChargeItemsVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 货站收费条目Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-13
 */
@Mapper
public interface HzChargeItemsMapper extends BaseMapper<HzChargeItems>
{
    /**
     * 查询货站收费条目
     * 
     * @param id 货站收费条目主键
     * @return 货站收费条目
     */
    public HzChargeItems selectHzChargeItemsById(Long id);

    /**
     * 查询货站收费条目列表
     * 
     * @param query 货站收费条目查询参数
     * @return 货站收费条目集合
     */
    public List<HzChargeItemsVo> selectHzChargeItemsList(ItemsQuery query);

    /**
     * 新增货站收费条目
     * 
     * @param hzChargeItems 货站收费条目
     * @return 结果
     */
    public int insertHzChargeItems(HzChargeItems hzChargeItems);

    /**
     * 修改货站收费条目
     * 
     * @param hzChargeItems 货站收费条目
     * @return 结果
     */
    public int updateHzChargeItems(HzChargeItems hzChargeItems);

    /**
     * 删除货站收费条目
     * 
     * @param id 货站收费条目主键
     * @return 结果
     */
    public int deleteHzChargeItemsById(Long id);

    /**
     * 批量删除货站收费条目
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHzChargeItemsByIds(Long[] ids);

    /**
     * 查询冷库计费项目
     * @return 结果
     */
    List<HzChargeItemsVo> selectColdItem();

    /**
     * 过滤有效日期
     * @param itemQuery 过滤条件
     * @return 结果
     */
    List<HzItemsVo> selectHzChargeItems(ItemsQuery itemQuery);
}
