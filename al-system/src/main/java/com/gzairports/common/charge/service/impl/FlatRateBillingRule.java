package com.gzairports.common.charge.service.impl;

import com.gzairports.common.business.arrival.domain.HzArrItem;
import com.gzairports.common.business.arrival.domain.ItemDetail;
import com.gzairports.common.charge.domain.HzChargeItemRule;
import com.gzairports.common.charge.domain.vo.BillRuleVo;
import com.gzairports.common.charge.service.BillingRule;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.List;

/**
 * 按费率计费规则
 * <AUTHOR>
 * @date 2024-08-03
 */
public class FlatRateBillingRule implements BillingRule {

    @Override
    public String className() {
        return "FlatRateBillingRule.class";
    }

    @Override
    public BillRuleVo calculateFee(List<HzChargeItemRule> rule, BigDecimal weight, Integer quantity, ItemDetail item) {
        BillRuleVo vo = new BillRuleVo();
        HzChargeItemRule itemRule = rule.get(0);
        BigDecimal decimal = itemRule.getMinimum() == null ? new BigDecimal(0) : itemRule.getMinimum();
        BigDecimal multiply = itemRule.getUnitPrice().multiply(weight);
        if (multiply.compareTo(decimal) >= 0){
            vo.setRate(itemRule.getUnitPrice());
        }
        BigDecimal totalCharge = multiply.max(decimal);
        vo.setTotalCharge(totalCharge);
        vo.setQuantity(weight.toString());
        return vo;
    }
}
