<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.wl.reporter.mapper.HawbSalesDailyMapper">

    <sql id="hawbWhereIfCondition">
        <if test="query.waybillCode != null and query.waybillCode != ''">
            and waybill_code =  #{query.waybillCode}
        </if>
        <if test="query.masterWaybillCode != null and query.masterWaybillCode != ''">
            and master_waybill_code = #{query.masterWaybillCode}
        </if>
        <if test="query.carrier1 != null and query.carrier1 != ''">
            and carrier1 like concat('%', #{query.carrier1}, '%')
        </if>
        <if test="query.sourcePort != null and query.sourcePort != ''">
            and source_port = #{query.sourcePort}
        </if>
        <if test="query.cargoCode != null and query.cargoCode != ''">
            and cargo_code = #{query.cargoCode}
        </if>
        <if test="query.cargoName != null and query.cargoName != ''">
            and cargo_name = #{query.cargoName}
        </if>
        <if test="query.desPort != null and query.desPort != ''">
            and des_port = #{query.desPort}
        </if>
        <if test="query.shipperAbb != null and query.shipperAbb != ''">
            and shipper_abb = #{query.shipperAbb}
        </if>
        <if test="query.shipper != null and query.shipper != ''">
            and shipper = #{query.shipper}
        </if>
        <if test="query.consignAbb != null and query.consignAbb != ''">
            and consign_abb = #{query.consignAbb}
        </if>
        <if test="query.consign != null and query.consign != ''">
            and consign = #{query.consign}
        </if>
        <if test="query.startWriteTime != null">
            and write_time <![CDATA[>=]]>  #{query.startWriteTime}
        </if>
        <if test="query.endWriteTime != null">
            and write_time <![CDATA[<=]]>  #{query.endWriteTime}
        </if>
        <if test="query.isChange != null">
            and is_change = #{query.isChange}
        </if>
        <if test="query.isOversight != null">
            and is_oversight = #{query.isOversight}
        </if>
        <if test="query.businessPoint != null and query.businessPoint != ''">
            and business_point = #{query.businessPoint}
        </if>
        <if test="query.flightNo != null and query.flightNo != ''">
            and flight_no = #{query.flightNo}
        </if>
        <if test="query.isInvalid != null">
            and is_invalid = #{query.isInvalid}
        </if>
        <if test="query.writer != null and query.writer != ''">
            and writer = #{query.writer}
        </if>
<!--        <if test="query.tariffType != null and query.tariffType != ''">-->
<!--            and tariff_type = #{query.tariffType}-->
<!--        </if>-->
    </sql>
    <sql id="awbaWhereIfCondition">
        <if test="query.waybillCode != null and query.waybillCode != ''">
            and waybill_code =  #{query.waybillCode}
        </if>
        <if test="query.masterWaybillCode != null and query.masterWaybillCode != ''">
            and master_waybill_code = #{query.masterWaybillCode}
        </if>
        <if test="query.carrier1 != null and query.carrier1 != ''">
            and carrier1 like concat('%', #{query.carrier1}, '%')
        </if>
        <if test="query.sourcePort != null and query.sourcePort != ''">
            and source_port = #{query.sourcePort}
        </if>
        <if test="query.cargoCode != null and query.cargoCode != ''">
            and cargo_code = #{query.cargoCode}
        </if>
        <if test="query.cargoName != null and query.cargoName != ''">
            and cargo_name = #{query.cargoName}
        </if>
        <if test="query.desPort != null and query.desPort != ''">
            and des_port = #{query.desPort}
        </if>
        <if test="query.shipperAbb != null and query.shipperAbb != ''">
            and shipper_abb = #{query.shipperAbb}
        </if>
        <if test="query.shipper != null and query.shipper != ''">
            and shipper = #{query.shipper}
        </if>
        <if test="query.consignAbb != null and query.consignAbb != ''">
            and consign_abb = #{query.consignAbb}
        </if>
        <if test="query.consign != null and query.consign != ''">
            and consign = #{query.consign}
        </if>
        <if test="query.startWriteTime != null">
            and write_time <![CDATA[>=]]> #{query.startWriteTime}
        </if>
        <if test="query.endWriteTime != null">
            and write_time <![CDATA[<=]]> #{query.endWriteTime}
        </if>
        <if test="query.isChange != null">
            and is_change = #{query.isChange}
        </if>
        <if test="query.isOversight != null">
            and is_oversight = #{query.isOversight}
        </if>
        <if test="query.businessPoint != null and query.businessPoint != ''">
            and business_point = #{query.businessPoint}
        </if>
        <if test="query.flightNo != null and query.flightNo != ''">
            and flight_no = #{query.flightNo}
        </if>
        <if test="query.isInvalid != null">
            and is_invalid = #{query.isInvalid}
        </if>
        <if test="query.writer != null and query.writer != ''">
            and writer = #{query.writer}
        </if>
<!--        <if test="query.reportTitle != null and query.reportTitle != ''">-->
<!--            and report_title = #{query.reportTitle}-->
<!--        </if>-->
    </sql>
    <select id="selectAwbaFieldByName" resultType="com.gzairports.common.business.reporter.domain.ReportDataWaybill">
        select ${fieldNames} from all_report_data_waybill
        <where>
            waybill_type = #{query.waybillType}
            <include refid="awbaWhereIfCondition"/>
        </where>
        <if test="sortData != null and sortData != ''">
            order by ${sortData}
        </if>
    </select>

    <select id="selectHawbFieldByName" resultType="com.gzairports.common.business.reporter.domain.ReportDataHawb">
        select ${fieldNames} from all_report_data_hawb
        <where>
            <include refid="hawbWhereIfCondition"/>
        </where>
        <if test="sortData != null and sortData != ''">
            order by ${sortData}
        </if>
    </select>

    <select id="selectAwbaPageData" resultType="com.gzairports.common.business.reporter.domain.ReportDataWaybill">
        select ${fieldNamesJoin} from all_report_data_waybill
        <where>
            waybill_type = #{query.waybillType}
            <include refid="awbaWhereIfCondition"/>
        </where>
    </select>


    <select id="selectHawbPageData" resultType="com.gzairports.common.business.reporter.domain.ReportDataHawb">
        select ${fieldNamesJoin} from all_report_data_hawb
        <where>
            <include refid="hawbWhereIfCondition"/>
        </where>
    </select>

    <select id="selectAwbaTotal" resultType="com.gzairports.wl.reporter.domain.vo.ReportTotalVo">
        select count(id) as count, sum(quantity) as quantity, sum(weight) as weight, sum(cost_sum) as cost_sum
        from all_report_data_waybill
        where waybill_type = #{query.waybillType}
        <include refid="awbaWhereIfCondition"/>
    </select>

    <select id="selectHawbTotal" resultType="com.gzairports.wl.reporter.domain.vo.ReportTotalVo">
        select count(id) as count, sum(quantity) as quantity, sum(weight) as weight, sum(cost_sum) as costSum
        from all_report_data_hawb
        <where>
            <include refid="hawbWhereIfCondition"/>
        </where>
    </select>

    <select id="selectGoodsWeightStatistics"
            resultType="com.gzairports.wl.reporter.domain.GoodsWeightStatistics">
        select
        DATE_FORMAT(write_time, #{query.statisticsType}) AS write_date,
        SUM(weight) as weight
        from all_air_waybill
        where is_del = 0
        <if test="query.startTime != null and query.endTime != null">
            AND write_time between #{query.startTime} and #{query.endTime}
        </if>
        <if test="query.sourcePort != null and query.sourcePort != ''">
            AND source_port = #{query.sourcePort}
        </if>
        <if test="query.desPort != null and query.desPort != ''">
            AND des_port = #{query.desPort}
        </if>
        <if test="query.waybillType != null and query.waybillType != ''">
            AND waybill_type = #{query.waybillType}
        </if>
        <if test="query.shipper != null and query.shipper != ''">
            AND shipper like concat('%', #{query.shipper},'%')
        </if>
        <if test="query.cargoName != null and query.cargoName != ''">
            AND cargo_name = #{query.cargoName}
        </if>
<!--        <if test="query.transferType != null and query.transferType != ''">-->
<!--            AND transfer_type like concat('%', #{query.transferType},'%')-->
<!--        </if>-->
        group by write_date
    </select>
    <select id="selectHAWBGoodsWeightStatistics"
            resultType="com.gzairports.wl.reporter.domain.GoodsWeightStatistics">
        select
        DATE_FORMAT(write_time, #{query.statisticsType}) AS write_date,
        SUM(weight) as weight
        from wl_dep_hawb
        where is_del = 0
        <if test="query.startTime != null and query.endTime != null">
            AND write_time between #{query.startTime} and #{query.endTime}
        </if>
        <if test="query.sourcePort != null and query.sourcePort != ''">
            AND source_port = #{query.sourcePort}
        </if>
        <if test="query.desPort != null and query.desPort != ''">
            AND des_port = #{query.desPort}
        </if>
        <if test="query.shipper != null and query.shipper != ''">
            AND shipper like concat('%', #{query.shipper},'%')
        </if>
        <if test="query.cargoName != null and query.cargoName != ''">
            AND cargo_name = #{query.cargoName}
        </if>
        <!--        <if test="query.transferType != null and query.transferType != ''">-->
        <!--            AND transfer_type like concat('%', #{query.transferType},'%')-->
        <!--        </if>-->
        group by write_date
    </select>
    <select id="selectAllColumnByTableName" resultType="java.lang.String">
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = #{tableName}
        ORDER BY ORDINAL_POSITION;
    </select>
</mapper>