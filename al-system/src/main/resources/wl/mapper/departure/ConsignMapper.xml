<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.wl.departure.mapper.ConsignMapper">

    <sql id="baseField">
        id, code, des_port, shipper_company, shipper_name, shipper_phone, shipper_address, shipper_id_card, shipper_sign,
        consignee_company, consignee_name, consignee_phone, consignee_address, waybill_code, cargo_name, pack, quantity, actual_weight,
        charge_weight,charge_size, insurance, insurance_value, is_danger, is_valuable, agent, consign_time, dept_id, is_edit, pdf_url, create_time
    </sql>

    <select id="selectConsignList" resultType="com.gzairports.wl.departure.domain.Consign">
        select <include refid="baseField"/>
        from wl_dep_consign
        <where>
            dept_id = #{deptId}
            <if test="code != null and code != ''">
                AND code like concat('%', #{code}, '%')
            </if>
            <if test="waybillCode != null and waybillCode != ''">
                AND waybill_code like concat('%', #{waybillCode}, '%')
            </if>
            <if test="shipperIdCard != null and shipperIdCard != ''">
                AND shipper_id_card like concat('%', #{shipperIdCard}, '%')
            </if>
            <if test="createTime != null">
               AND CONCAT(DATE_FORMAT(create_time, '%Y-%m-%d'), ' 00:00:00') = #{createTime}
            </if>
        </where>
    </select>
</mapper>