package com.gzairports.common.serviceRequest.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.serviceRequest.domain.ServiceItem;
import com.gzairports.common.serviceRequest.domain.ServiceRequest;
import com.gzairports.common.serviceRequest.domain.bo.ServiceRequestBo;
import com.gzairports.common.serviceRequest.domain.query.ServiceRequestQuery;
import com.gzairports.common.serviceRequest.domain.vo.ServiceInfoVo;
import com.gzairports.common.serviceRequest.domain.vo.ServiceRequestVo;

import java.util.List;

/**
 * 服务申请Service接口
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
public interface IServiceRequestService extends IService<ServiceRequest> {

    /**
     * 查询服务申请列表
     * @param query 查询条件
     * @return 服务申请列表
     */
    ServiceRequestVo selectListByQuery(ServiceRequestQuery query);

    /**
     * 新增服务申请
     * @param request 新增参数
     * @return 结果
     */
    int add(ServiceRequest request);

    /**
     * 详情
     * @param id 服务申请id
     * @return 详情
     */
    ServiceInfoVo wlGetInfo(Long id);

    /**
     * 提交
     * @param id 服务id
     * @return 结果
     */
    int commit(Long id);

    /**
     * 详情
     * @param id 服务申请id
     * @return 详情
     */
    ServiceInfoVo hzGetInfo(Long id);

    /**
     * 审核
     * @param vo 审核参数
     * @return 结果
     */
    int examine(ServiceInfoVo vo);

    /**
     * 物流服务申请 查询服务项目列表数据
     * */
    List<ServiceItem> itemList();

    /**
     * 货站端选择 完成服务或者是拒绝服务等操作
     * */
    int handleService(ServiceInfoVo vo);

    /**
     * 物流端的预授权支付
     * */
    int pay(Long id);

    int payAndAdd(ServiceRequest request);

    void edit(ServiceRequestBo bo);

    /** 货站端支付 */
    int hzPay(Long id);
}
