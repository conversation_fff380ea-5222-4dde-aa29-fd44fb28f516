package com.gzairports.common.basedata.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 货品大类
 *
 * <AUTHOR>
 * @date 2024-06-14
 */
@Data
@TableName("base_cargo_category")
public class BaseCargoCategory {

    /** 主键id */
    @Excel(name = "主键id", prompt = "编号")
    private Long id;

    /** 代码 */
    @Excel(name = "代码")
    private String code;

    /** 中文名称 */
    @Excel(name = "中文名称")
    private String chineseName;

    /** 英文名称 */
    @Excel(name = "英文名称")
    private String englishName;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 是否删除 0 否 1 是 */
    private Integer isDel;

    /** 入库仓库 */
    private String inStore;

    /** 入库库位 */
    private String inLocator;

    /** 出库仓库 */
    private String outStore;

    /** 出库库位 */
    private String outLocator;

    /** 货品代码集合 */
    @TableField(exist = false)
    private List<BaseCargoCode> cargoCodeList;
}
