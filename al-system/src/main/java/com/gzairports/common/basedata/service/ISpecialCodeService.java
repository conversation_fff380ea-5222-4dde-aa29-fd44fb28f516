package com.gzairports.common.basedata.service;


import com.gzairports.common.basedata.domain.BaseSpecialCode;
import com.gzairports.common.basedata.domain.query.SpecialCodeQuery;

import java.util.List;

/**
 * 货物特殊处理代码Service接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
public interface ISpecialCodeService {

    /**
     * 查询货物特殊处理代码列表
     *
     * @param query 查询参数
     * @return 货物特殊处理代码列表
     */
    List<BaseSpecialCode> selectSpecialCodeList(SpecialCodeQuery query);


    /**
     * 导入货物特殊处理代码
     *
     * @param specialCodes 货物特殊处理代码数据列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    String importSpecialCode(List<BaseSpecialCode> specialCodes, boolean updateSupport);

    /**
     * 新增货物特殊处理代码
     *
     * @param specialCode 货物特殊处理代码
     * @return 结果
     */
    int addSpecialCode(BaseSpecialCode specialCode);

    /**
     * 修改货物特殊处理代码
     *
     * @param specialCode 货物特殊处理代码
     * @return 结果
     */
    int editSpecialCode(BaseSpecialCode specialCode);

    /**
     * 删除货物特殊处理代码
     *
     * @param ids 货物特殊处理代码id集合
     * @return 结果
     */
    int delSpecialCode(Long[] ids);
}
