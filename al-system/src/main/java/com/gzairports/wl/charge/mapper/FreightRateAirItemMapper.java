package com.gzairports.wl.charge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.wl.charge.domain.FreightRateAirItem;
import com.gzairports.wl.charge.domain.query.FreightRateItemQuery;
import com.gzairports.wl.charge.domain.vo.FreightRateAirItemVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 航司运价条目Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@Mapper
public interface FreightRateAirItemMapper extends BaseMapper<FreightRateAirItem> {

    /**
     * 查看运价条目
     *
     * @param id 航司运价id
     * @return 航司运价条目集合
     */
    List<FreightRateAirItemVO> selectRateItemList(Long id);

    /**
     * 查询运价条目列表
     *
     * @param query 运价条目查询参数
     * @return 结果
     */
    List<FreightRateAirItemVO> selectAirList(FreightRateItemQuery query);

    /**
     * 删除运价条目
     *
     * @param id 运价条目主键
     * @return 结果
     */
    int removeAirItem(Long id);
}
