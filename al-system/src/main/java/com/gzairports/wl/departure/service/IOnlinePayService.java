package com.gzairports.wl.departure.service;

import com.gzairports.wl.departure.domain.query.OnlinePayQuery;
import com.gzairports.wl.departure.domain.vo.OnlineInfoVo;
import com.gzairports.wl.departure.domain.vo.OnlinePayVo;

/**
 * 货站在线缴费Service接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface IOnlinePayService {

    /**
     * 根据条件查询货站在线缴费列表
     * @param query 查询条件
     * @return 货站在线缴费列表
     */
    OnlinePayVo selectList(OnlinePayQuery query);

    /**
     * 货站在线缴费详情
     * @param id 运单id
     * @return 货站在线缴费详情
     */
    OnlineInfoVo getInfo(Long id);

    /**
     * 支付
     * @param id 运单id
     * @return 结果
     */
    int payment(Long id);
}
