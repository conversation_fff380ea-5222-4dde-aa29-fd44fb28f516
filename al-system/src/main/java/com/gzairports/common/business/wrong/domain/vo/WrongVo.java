package com.gzairports.common.business.wrong.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import com.gzairports.common.business.wrong.domain.WrongRecord;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 不正常货邮表
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Data
public class WrongVo {

    /** 主键id */
    private Long id;

    /** 运单号 */
    @Excel(name = "运单号")
    private String waybillCode;

    /** 进出港类型 DEP离港 ARR进港 */
    @Excel(name = "进出港")
    private String type;

    /** 代理人 */
    @Excel(name = "代理人")
    private String agent;

    /** 状态
     * 0>增加
     * 1>代理人选择处理
     * 2>货站处理
     * 3>处理结束
     * */
    @Excel(name = "状态")
    private Integer status;

    /** 不正常类型 */
    @Excel(name = "不正常类型")
    private String wrongType;

    /** 不正常描述 */
    @Excel(name = "不正常描述")
    private String wrongRemark;

    /** 登记时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "登记时间", dateFormat =  "yyyy-MM-dd HH:mm:ss")
    private Date registerTime;

    /** 处理方式 0继续运输 1换单 2退货 */
    private String proMethod;

    /** 处理方式描述 */
    private String methodRemark;

    /** 处理描述 */
    private String proRemark;

    /** 图片地址 */
    private String imgUrl;

    /** 是否处理结束 0 否 1 是 */
    private Integer isOver;

    /** 是否可办理提货 0 否 1 是 */
    private Integer isPickUp;

    /** 退货方式 0不可退 1退货退款 2仅退款 */
    private Integer isReturnCargo;

    /** 创建人 */
    private String createBy;

    /** 所属单位 */
    private Long deptId;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 临时拉下id */
    private Long pullId;

    /** 卸下id集合 */
    private String disBoardIds;

    /** 理货id */
    private Long tallyId;

    /** 拉下件数 */
    private Integer pullQuantity;

    /** 拉下重量 */
    private BigDecimal pullWeight;

    /** 拉下时间 */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date pullTime;

    /** 拉货出港件数 */
    @TableField(exist = false)
    private Integer pullDepQuantity;

    /** 拉货出港重量 */
    @TableField(exist = false)
    private BigDecimal pullDepWeight;

    /** 拉货出港时间 */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date pullDepTime;

    /** 处理记录列表 */
    @TableField(exist = false)
    List<WrongRecord> recordList;

    /** 收货人 */
    @Excel(name = "收货人")
    @TableField(exist = false)
    private String consign;

    /** 支付状态 0 未支付 1 已支付*/
    @TableField(exist = false)
    private Integer payStatus;

    /** 是否办单 0 否 1 是 */
    @TableField(exist = false)
    private Integer isOrder;

    /** 舱单件数 */
    @TableField(exist = false)
    @Excel(name = "舱单件数", cellType = Excel.ColumnType.NUMERIC)
    private Integer cabinPieces;

    /** 舱单重量 */
    @TableField(exist = false)
    @Excel(name = "舱单重量", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal cabinWeight;

    /** 实到件数 理货件数*/
    @TableField(exist = false)
    @Excel(name = "实到件数", cellType = Excel.ColumnType.NUMERIC)
    private Integer actualPieces;

    /** 实到重量  理货重量*/
    @TableField(exist = false)
    @Excel(name = "实到重量", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal actualWeight;

    /** 待运输件数 - 未到件数 “件数”-“理货件数” */
    @TableField(exist = false)
    @Excel(name = "待运输件数", cellType = Excel.ColumnType.NUMERIC)
    private Integer noArrivePieces;

    /** 仓库 */
    @Excel(name = "仓库")
    @TableField(exist = false)
    private String store;

    /** 库位 */
    @TableField(exist = false)
    @Excel(name = "库位")
    private String locator;
}
