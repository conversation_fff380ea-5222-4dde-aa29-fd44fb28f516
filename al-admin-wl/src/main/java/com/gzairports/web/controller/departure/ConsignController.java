package com.gzairports.web.controller.departure;

import com.gzairports.common.annotation.OperLog;
import com.gzairports.common.basedata.domain.BaseAirportCode;
import com.gzairports.common.basedata.service.IAirportCodeService;
import com.gzairports.common.business.arrival.domain.query.AutoOrderQuery;
import com.gzairports.common.business.departure.domain.WaybillTrace;
import com.gzairports.common.business.departure.service.IWaybillTraceService;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.pdf.PdfPrintHelper;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.common.utils.sign.Base64;
import com.gzairports.common.utils.spring.SpringUtils;
import com.gzairports.oss.domain.SysOss;
import com.gzairports.oss.service.impl.SysOssServiceImpl;
import com.gzairports.wl.departure.domain.Consign;
import com.gzairports.wl.departure.domain.query.ConsignQuery;
import com.gzairports.wl.departure.domain.query.EditAuthQuery;
import com.gzairports.wl.departure.domain.vo.WaybillTraceVo;
import com.gzairports.wl.departure.service.IConsignService;
import com.gzairports.wl.log.domain.WlOperLog;
import com.gzairports.wl.log.service.WlOperLogService;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

/**
 * 托运管理Controller
 *
 * <AUTHOR>
 * @date 2024-05-08
 */
@RestController
@RequestMapping("/dep/consign")
public class ConsignController extends BaseController {

    @Autowired
    private IConsignService consignService;

    @Autowired
    private WlOperLogService operLogService;

    @Autowired
    private IAirportCodeService airportCodeService;

    @Autowired
    private IWaybillTraceService waybillTraceService;

    /**
     * 查询托运列表
     */
//    @PreAuthorize("@ss.hasPermi('dep:consign:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询托运列表")
    public TableDataInfo list(ConsignQuery query){
        startPage();
        List<Consign> list = consignService.selectList(query);
        return getDataTable(list);
    }

    /**
     * 导出托运列表
     */
    @PreAuthorize("@ss.hasPermi('dep:consign:export')")
    @OperLog(title = "导出托运列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出托运列表")
    public void export(HttpServletResponse response, ConsignQuery query)
    {
        List<Consign> list = consignService.selectList(query);
        ExcelUtil<Consign> util = new ExcelUtil<Consign>(Consign.class);
        util.exportExcel(response, list, "托运列表");
    }
    /**
     * 新增托运
     */
    @PreAuthorize("@ss.hasPermi('dep:consign:add')")
    @OperLog(title = "托运管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ApiOperation(value = "新增托运")
    public AjaxResult add(@RequestBody Consign consign) throws Exception {
        setPdf(consign);
        return toAjax(consignService.add(consign));
    }

    private byte[] downloadFileFromUrl(String urlStr) throws Exception {
        URL url = new URL(urlStr);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(5000);
        connection.setReadTimeout(5000);

        if (connection.getResponseCode() != 200) {
            throw new RuntimeException("Failed to connect, HTTP error code : " + connection.getResponseCode());
        }

        try (InputStream inputStream = connection.getInputStream();
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            return baos.toByteArray();
        }
    }

    /**
     * 获取托运详细信息
     */
    @PreAuthorize("@ss.hasPermi('dep:consign:getInfo')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取托运详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(consignService.getInfo(id));
    }

    /**
     * 柜台录入
     */
//    @PreAuthorize("@ss.hasPermi('dep:consign:edit')")
    @OperLog(title = "柜台录入", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ApiOperation(value = "柜台录入")
    public AjaxResult edit(@RequestBody Consign consign) throws Exception {
        setPdf(consign);
        return toAjax(consignService.edit(consign));
    }

    private void setPdf(Consign consign) throws Exception {
        if (StringUtils.isNotEmpty(consign.getWaybillCode())){
            String substring = consign.getWaybillCode().substring(4);
            String waybillCodeAbb = substring.substring(0, 3) + "-" + substring.substring(3);
            consign.setMasterWaybillCode(waybillCodeAbb);
        }
        String baseAirportCode = airportCodeService.selectChineseName(consign.getDesPort());
        consign.setDesPortStr(baseAirportCode);
        consign.setInsure(consign.getInsurance() == 0 ? "否":"是");
        consign.setDanger(consign.getIsDanger() == 0 ? "NO":"YES");
        consign.setValuable(consign.getIsValuable() == 0 ? "NO":"YES");
        BigDecimal actualWeight = consign.getActualWeight();
        consign.setActualWeightStr(actualWeight.toString());
        if (consign.getChargeWeight() != null){
            consign.setChargeWeightStr(consign.getChargeWeight().toString());
        }
        if (consign.getInsuranceValue() != null){
            consign.setInsuranceValueStr(consign.getInsuranceValue().toString());
        }
        if (StringUtils.isNotEmpty(consign.getShipperSign())) {
            byte[] bytes = downloadFileFromUrl(consign.getShipperSign());
            String signImage = Base64.encode(bytes);
            consign.setSignImage(signImage);
        }
        ClassPathResource resource = new ClassPathResource("electronic/consign.pdf");
        if (resource.exists()) {
            String path = resource.getPath();
            byte[] pdfDataFromTemplate = PdfPrintHelper.getPdfDataFromTemplate(consign, path);
            // 使用StandardMultipartHttpServletRequest来创建MultipartFile
            DiskFileItem fileItem = new DiskFileItem("consign.pdf", "application/pdf", true, "consign.pdf", -1, null);

            // 将 InputStream 写入 DiskFileItem
            fileItem.getOutputStream().write(pdfDataFromTemplate);
            fileItem.getOutputStream().close();

            // 使用 FileItem 构建 MultipartFile
            MultipartFile multipartFile = new CommonsMultipartFile(fileItem);
            SysOss upload = SpringUtils.getBean(SysOssServiceImpl.class).upload(multipartFile);
            consign.setPdfUrl(upload.getUrl());
        }
    }


    /**
     * 查看电子托运书
     */
    @PreAuthorize("@ss.hasPermi('dep.consign.pdfUrl')")
    @GetMapping("/pdfUrl/{waybillCode}")
    @ApiOperation(value = "查看日志报文")
    public AjaxResult pdfUrl(@PathVariable("waybillCode") String waybillCode){
        return AjaxResult.success(consignService.pdfUrl(waybillCode));
    }

    /**
     * 更改客户权限
     */
    @PreAuthorize("@ss.hasPermi('dep:consign:editAuth')")
    @PostMapping("/editAuth")
    @ApiOperation(value = "更改客户权限")
    public AjaxResult editAuth(@RequestBody EditAuthQuery query)
    {
        return toAjax(consignService.editAuth(query));
    }

    /**
     * 查看操作日志
     */
    @PreAuthorize("@ss.hasPermi('dep.consign.log')")
    @GetMapping("/log/{id}")
    @ApiOperation(value = "查看操作日志")
    public AjaxResult log(@PathVariable("id") Long id){
        List<WlOperLog> list = operLogService.log("托运管理", id);
        list.addAll(operLogService.log("柜台录入", id));
        return AjaxResult.success(list);
    }

    /**
     * 查看日志报文
     */
    @PreAuthorize("@ss.hasPermi('dep.consign.logInfo')")
    @GetMapping("/logInfo/{logId}")
    @ApiOperation(value = "查看日志报文")
    public AjaxResult logInfo(@PathVariable("logId") Long logId){
        return AjaxResult.success(operLogService.logInfo(logId));
    }

    /**
     * 查询H5托运列表
     */
    @GetMapping("/h5List")
    @ApiOperation(value = "查询H5托运列表")
    public AjaxResult h5List(ConsignQuery query){
        List<Consign> list = consignService.selectList(query);
        return AjaxResult.success(list);
    }

    /**
     * 获取托运详细信息
     */
    @GetMapping(value = "/getH5Info/{id}")
    @ApiOperation(value = "获取托运详细信息")
    public AjaxResult getH5Info(@PathVariable("id") Long id)
    {
        return success(consignService.getInfo(id));
    }

    /**
     * H5新增托运
     */
    @PostMapping("/h5Add")
    @OperLog(title = "托运管理", businessType = BusinessType.INSERT)
    @ApiOperation(value = "H5新增托运")
    public AjaxResult h5Add(@RequestBody Consign consign) throws Exception {
        setPdf(consign);
        return toAjax(consignService.h5Add(consign));
    }

    /**
     * H5运单跟踪
     */
    @PostMapping("/info")
    @ApiOperation(value = "H5运单跟踪")
    public AjaxResult list(@RequestBody AutoOrderQuery query){
        List<WaybillTrace> vo = waybillTraceService.h5WaybillTrace(query);
        return AjaxResult.success(vo);
    }

    /**
     * 根据身份证号和目的站查询历史记录
     */
    @GetMapping("/getHistory")
    @ApiOperation(value = "根据身份证号和目的站查询历史记录")
    public AjaxResult getHistory(@NotNull @RequestParam String shipperIdCard,@NotNull @RequestParam String desPort){
        Consign consign = consignService.getHistory(shipperIdCard,desPort);
        return AjaxResult.success(consign);
    }

    /**
     * 打印托运书
     */
//    @PreAuthorize("@ss.hasPermi('dep:consign:printConsign')")
    @GetMapping(value = "/printConsign/{id}")
    @ApiOperation(value = "打印托运书")
    public void printConsign(HttpServletResponse response, @PathVariable("id") Long id) throws Exception
    {
        consignService.printConsign(response,id);
    }
}
