package com.gzairports.web.controller.reporter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.wl.reporter.domain.query.MawbHawbIncomeQuery;
import com.gzairports.wl.reporter.domain.vo.AHIncomeDetailVO;
import com.gzairports.wl.reporter.domain.vo.AHIncomeTotalVO;
import com.gzairports.wl.reporter.service.IMawbHawbIncomeTotalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * @author: lan
 * @Desc: 主分单收入汇总报表
 * @create: 2025-03-13 14:29
 **/

@RestController
@RequestMapping("/wl/mawbHawbIncomeTotal")
@Api(tags = "主分单收入汇总报表")
public class MawbHawbIncomeTotalController {
    @Autowired
    private IMawbHawbIncomeTotalService mawbHawbIncomeTotalService;

    /**
     * 报表列表查询/导出
     */
    @GetMapping("/export")
    @ApiOperation(value = "报表列表查询/导出")
    public void export(MawbHawbIncomeQuery query, HttpServletResponse response) {
        Page<AHIncomeTotalVO> pageData = mawbHawbIncomeTotalService.queryExportDataList(query);
        mawbHawbIncomeTotalService.export(pageData.getRecords(), response);
    }

    /**
     * 列表数据条数
     * */
    @GetMapping("/page")
    @ApiOperation(value = "报表列表分页数据")
    public TableDataInfo pageQuery(MawbHawbIncomeQuery query) {
        Page<AHIncomeTotalVO> pageData = mawbHawbIncomeTotalService.queryExportDataList(query);
        return new TableDataInfo(pageData.getRecords(),(int)pageData.getTotal());
    }

    /**
     * 报表打印
     * */
    @GetMapping("/print/pdf")
    @ApiOperation(value = "报表打印-PDF文件")
    public void printPDF(MawbHawbIncomeQuery query, HttpServletResponse response) {
        mawbHawbIncomeTotalService.printPDF(query,response);
    }


}
