package com.gzairports.wl.ticket.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.wl.ticket.domain.TicketAlert;
import com.gzairports.wl.ticket.domain.query.TicketQuery;

import java.util.List;

/**
 * 单证预警Service接口
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
public interface TicketAlertService extends IService<TicketAlert> {

    /**
     * 查询单证预警列表
     *
     * @param query 查询参数
     * @return 单证预警列表
     */
    List<TicketAlert> selectList(TicketQuery query);

    /**
     * 单证预警新增
     *
     * @param alert 新增参数
     * @return 结果
     */
    int add(TicketAlert alert);

    /**
     * 单证预警修改
     *
     * @param alert 修改参数
     * @return 结果
     */
    int edit(TicketAlert alert);

    /**
     * 单证预警删除
     *
     * @param id 单证预警id
     * @return 结果
     */
    int removeAlert(Long id);

    /**
     * 查看预警详情
     *
     * @param id 预警id
     * @return 预警详情
     */
    TicketAlert getInfo(Long id);

    /**
     * 票证预警
     *
     */
    void alert();
}
