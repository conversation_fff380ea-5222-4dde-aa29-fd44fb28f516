package com.gzairports.web.controller.charge;


import com.gzairports.wl.charge.domain.ChargeMethod;
import com.gzairports.wl.charge.domain.query.ChargeMethodQuery;
import com.gzairports.wl.charge.domain.vo.ChargeMethodVO;
import com.gzairports.wl.charge.service.ChargeMethodService;
import com.gzairports.common.annotation.OperLog;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.wl.log.service.WlOperLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 客户结算方式Controller
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@RestController
@RequestMapping("/method")
@Api(tags = "客户结算方式接口")
public class ChargeMethodController extends BaseController {

    @Autowired
    private ChargeMethodService methodService;

    @Autowired
    private WlOperLogService logService;

    /**
     * 查询客户结算方式列表
     */
//    @PreAuthorize("@ss.hasPermi('charge:method:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询客户结算方式列表")
    public TableDataInfo list(ChargeMethodQuery query){
        startPage();
        List<ChargeMethodVO> list = methodService.selectList(query);
        return getDataTable(list);
    }

    /**
     * 导出客户结算方式列表
     */
    @PreAuthorize("@ss.hasPermi('charge:method:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出客户结算方式")
    public void export(HttpServletResponse response, ChargeMethodQuery query){
        List<ChargeMethodVO> list = methodService.selectList(query);
        ExcelUtil<ChargeMethodVO> util = new ExcelUtil<ChargeMethodVO>(ChargeMethodVO.class);
        util.exportExcel(response, list, "客户结算方式");
    }

    /**
     * 获取客户结算方式详细信息
     */
    @PreAuthorize("@ss.hasPermi('charge:method:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取客户结算方式详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(methodService.selectChargeById(id));
    }

    /**
     * 新增客户结算方式
     */
    @PreAuthorize("@ss.hasPermi('charge:method:add')")
    @OperLog(title = "客户结算方式", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ApiOperation(value = "新增客户结算方式")
    public AjaxResult add(@RequestBody ChargeMethod method)
    {
        return success(methodService.insertChargeMethod(method));
    }

    /**
     * 修改客户结算方式
     */
    @PreAuthorize("@ss.hasPermi('charge:method:edit')")
    @OperLog(title = "客户结算方式", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ApiOperation(value = "修改客户结算方式")
    public AjaxResult edit(@RequestBody ChargeMethod method)
    {
        return toAjax(methodService.updateChargeMethod(method));
    }

    /**
     * 删除客户结算方式
     */
    @PreAuthorize("@ss.hasPermi('charge:method:del')")
    @OperLog(title = "客户结算方式", businessType = BusinessType.DELETE)
    @GetMapping("/del/{id}")
    @ApiOperation(value = "删除客户结算方式")
    public AjaxResult del(@PathVariable Long id)
    {
        return toAjax(methodService.deleteChargeMethod(id));
    }

    /**
     * 客户结算方式操作日志查询
     */
    @PreAuthorize("@ss.hasPermi('charge:method:logList')")
    @GetMapping("/logList/{id}")
    @ApiOperation(value = "客户结算方式操作日志查询")
    public AjaxResult logList(@PathVariable Long id)
    {
        return success(logService.log("客户结算方式",id));
    }

    /**
     * 客户结算方式操作日志详情
     */
    @PreAuthorize("@ss.hasPermi('charge:method:logInfo')")
    @GetMapping("/logInfo/{operId}")
    @ApiOperation(value = "客户结算方式操作日志详情")
    public AjaxResult logInfo(@PathVariable Long operId)
    {
        return success(logService.logInfo(operId));
    }

}
