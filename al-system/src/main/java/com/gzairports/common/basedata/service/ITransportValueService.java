package com.gzairports.common.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.basedata.domain.BaseTransportValue;
import com.gzairports.common.basedata.domain.query.BaseTransportValueQuery;

import java.util.List;

/**
 * 运输价值维护Service接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
public interface ITransportValueService extends IService<BaseTransportValue> {

    /**
     * 查询运输价值维护列表
     *
     * @param query 查询参数
     * @return 运输价值维护列表
     */
    List<BaseTransportValue> selectTransportValueList(BaseTransportValueQuery query);

    /**
     * 新增运输价值维护
     *
     * @param value 运输价值维护
     * @return 结果
     */
    int addTransportValue(BaseTransportValue value);

    /**
     * 修改运输价值维护
     *
     * @param value 运输价值维护
     * @return 结果
     */
    int editTransportValue(BaseTransportValue value);

    /**
     * 删除运输价值维护
     *
     * @param id 运输价值维护id
     * @return 结果
     */
    int delTransportValue(Long id);

    /**
     * 根据id获取运输价值维护
     *
     * @param id 运输价值维护id
     * @return 运输价值维护
     */
    BaseTransportValue getInfo(Long id);
}
