package com.gzairports.common.business.departure.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 订舱参数
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@Data
public class BookingQuery {

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /** 航班号 */
    private String flightNo;

    /** 运单号 */
    private String waybillCode;

    /** 状态 */
    private Integer status;

    /** 所属单位 */
    private Long deptId;
}
