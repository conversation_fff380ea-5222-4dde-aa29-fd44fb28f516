package com.gzairports.wl.reporter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.wl.reporter.domain.ReportSet;
import com.gzairports.wl.reporter.domain.vo.ReportSetVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-19 09:41
 */
@Mapper
public interface ReportSetMapper extends BaseMapper<ReportSet> {

    /***
     * 查看详情
     * @param id 报表设置id
     * @return 结果
     */
    ReportSetVo getInfoById(Long id);

    /**
     * 报表列表查询
     * @param type 查询参数
     * @return 结果
     */
    List<ReportSet> getList(String type);
}
