package com.gzairports.hz.business.departure.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.hz.business.departure.domain.HzDepSpecialTrace;
import com.gzairports.hz.business.departure.domain.query.SpecialTraceQuery;
import com.gzairports.hz.business.departure.domain.vo.SpecialTraceVo;

import java.util.List;

/**
 * 特货跟踪Service接口
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface ISpecialTraceService extends IService<HzDepSpecialTrace> {

    /**
     * 特货跟踪列表查询
     * @param query 查询参数
     * @return 列表
     */
    List<SpecialTraceVo> selectList(SpecialTraceQuery query);
}
