package com.gzairports.sms.mapper.sms;


import com.gzairports.sms.domain.HzAgentUser;
import com.gzairports.sms.domain.dto.SmsReceiveUserDto;
import com.gzairports.sms.domain.vo.SmsReceiveUserVo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 短信发送Mapper
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Mapper
public interface SmsSendMapper {

    int agentSendSmsConfig(@Param("agentUserList") List<HzAgentUser> agentUserList);

    List<SmsReceiveUserVo> selectSmsReceiveUser(SmsReceiveUserDto smsReceiveUserDto);

    void deleteAgentSendSmsConfig(@Param("agentIds") List<Long> agentIds);
}
