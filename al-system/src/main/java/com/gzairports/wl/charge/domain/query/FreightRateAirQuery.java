package com.gzairports.wl.charge.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 航司运价查询参数
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@Data
public class FreightRateAirQuery {

    /** id集合 */
    private List<Long> ids;

    /** 运价名称 */
    private String rateName;

    /** 运价类型 */
    private String rateType;

    /** 所属单位 */
    private Long deptId;

    /** 适用单证类型 */
    private String applicableDocuments;

    /** 航司 */
    private String carrierCode;

    /** 生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date effectiveDate;

    /** 截止日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expirationDate;
}
