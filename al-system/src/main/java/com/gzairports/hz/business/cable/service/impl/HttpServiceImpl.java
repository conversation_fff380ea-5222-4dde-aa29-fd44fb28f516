package com.gzairports.hz.business.cable.service.impl;

import com.alibaba.fastjson2.JSON;
import com.gzairports.hz.business.cable.domain.HzCable;
import com.gzairports.hz.business.cable.domain.vo.ForwardOriginMsgVO;
import com.gzairports.hz.business.cable.domain.vo.MsgJsonVO;
import com.gzairports.hz.business.cable.mapper.HzCableMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * Created by david on 2024/6/20
 */
@Service
@Slf4j
public class HttpServiceImpl {

    @Value("${hzCable.sendMsg}")
    private String sendMsg;

    @Autowired
    private HzCableMapper cableMapper;

    /**
     * 不要每次都New一个OKHttpClient，而是把OkHttpClient写成一个静态的final变量，或者使用单例动态创建。其时OkHttpClient本身是一个线程池，每次都new OkHttpClient 就相当于new一个线程池出来，所以会OOM
     */
    private static final OkHttpClient OK_HTTP_CLIENT = new OkHttpClient().newBuilder()
            .connectTimeout(60, TimeUnit.SECONDS)
            .readTimeout(5 * 60, TimeUnit.SECONDS)
            .writeTimeout(5 * 60, TimeUnit.SECONDS)
            .build();

    public static final MediaType JSONS = MediaType.parse("application/json; charset=utf-8");

    public synchronized void sendCable(ForwardOriginMsgVO vo, HzCable originalCable, String header) {
        HzCable cable = new HzCable();
        BeanUtils.copyProperties(originalCable, cable);
        RequestBody body = RequestBody.create(JSONS, JSON.toJSONString(vo, String.valueOf(false)));
        Request request = new Request.Builder()
                .url(sendMsg)
                .post(body)
                .header("X-Access-Token",header)
                .build();
        executeCall(request, cable);
    }

    private void executeCall(Request request, HzCable cable) {

        OK_HTTP_CLIENT.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                System.out.println(e.toString());
                log.info("通讯异常 url:" + request.url(), e);
                if (cable != null) {
                    cable.setStatus(0);
                    cableMapper.updateHzCable(cable);
                }
            }

            @Override
            public void onResponse(Call call, Response response) {
                response.close();
                log.info(LocalDateTime.now() + "电报发送成功，发送目的为" + request.url());
                if (cable != null) {
                    cable.setCableNo("I" + cable.getCableNo());
                    cable.setStatus(1);
                    cableMapper.updateHzCable(cable);
                }
            }
        });
    }

}
