package com.gzairports.common.bank.domain.refund;

import lombok.Data;

import java.io.Serializable;

/**
 * @author: lan
 * @Desc: 退款请求字段
 * @create: 2025-03-18 14:24
 **/

@Data
public class RefundRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 一级商户号 String/16 Y
     * 民生银行为签约商户提供的一级商户号，与签约证书一一对应
     */
    private String platformId;

    /** 一级签约编码 String/20 Y
     * 一级商户号对应的签约编码
     */
    private String merchantNo;

    /** 商户订单号 String/50 Y
     * 原交易商户订单号
     */
    private String merchantSeq;

    /** 商户流水号 String/50 Y
     * 商户退款流水号(每笔请求重新生成，长度必须为50位)
     */
    private String mchSeqNo;

    /** 退款金额 String/20 Y
     * 如果原交易是积分消费，该字段为积分
     */
    private String orderAmount;

    /** 退款说明 String/256 N
     */
    private String orderNote;

    /** 备注字段 String/256 N
     */
    private String reserve;

    /** 扩展字段 String/8000 Y
     * 该inForm结构同下单格式，构造xml报文并对该报文进行BASE64编码，具体参考下文
     */
    private String inForm;


}
