package com.gzairports.wl.departure.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.pdf.PdfPrintAnnotation;
import com.gzairports.wl.departure.domain.HawbErrorRemark;
import com.gzairports.wl.departure.domain.HawbItem;
import lombok.Data;

import javax.xml.soap.Text;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static com.gzairports.common.pdf.PdfPrintAnnotation.PdfFieldType.*;

/**
 * 分运单详情参数
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@Data
public class HawbVo {

    /** 主键id */
    private Long id;

    /** 托运业务号 */
    private String consignCode;

    /** 托运书id */
    private Long consignId;

    /** 运单号 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "waybillCode")
    private String waybillCode;

    /** 校验码 */
    private Integer code;

    /** 始发站 */
    private String sourcePort;

    /** 目的站 */
    private String desPort;

    /** 始发站 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "sourcePort")
    private String sourcePortStr;

    /** 目的站 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "desPort")
    private String desPortStr;

    /** 航班号 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "flightNo")
    private String flightNo;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date flightDate;

    /** 航班日期 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "flightDate")
    private String flightDateStr;

    /** 关联主单号 */

    private String masterWaybillCode;

    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "masterWaybillCode")
    private String masterAbb;

    /** 承运人1 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "carrier1")
    private String carrier1;

    /** 到达站1 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "des1")
    private String des1;

    /** 承运人2 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "carrier2")
    private String carrier2;

    /** 到达站2 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "des2")
    private String des2;

    /** 承运人3 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "carrier3")
    private String carrier3;

    /** 到达站3 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "des3")
    private String des3;

    /** 收运方式 */
    private String collectMethod;

    /** 发货人简称 */
    private String shipperAbb;

    /** 发货人 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipper")
    private String shipper;

    /** 发货人电话 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipperPhone")
    private String shipperPhone;

    /** 发货人地址 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipperAddress")
    private String shipperAddress;

    /** 发货人地区 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipperRegion")
    private String shipperRegion;

    /** 收货人简称 */
    private String consigneeAbb;

    /** 收货人 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "consignee")
    private String consignee;

    /** 收货人电话 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "consigneePhone")
    private String consigneePhone;

    /** 收货人地址 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "consigneeAddress")
    private String consigneeAddress;

    /** 收货人地区 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "consigneeRegion")
    private String consigneeRegion;

    /** 货品代码 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "cargoCode")
    private String cargoCode;

    /** 品名 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "cargoName")
    private String cargoName;

    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "cargoInfo")
    private String cargoInfo;

    /** 特货代码1 */
    private String specialCargoCode1;

    /** 特货代码2 */
    private String specialCargoCode2;

    /** 特货代码3 */
    private String specialCargoCode3;

    /** 其他特货代码 */
    private String otherSpecialCargoCode;

    /** 包装 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "pack")
    private String pack;

    /** 包装code */
    private String packCode;

    /** 件数 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "quantity")
    private Integer quantity;

    /** 重量 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "weight")
    private BigDecimal weight;

    /** 计费重量 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "chargeWeight")
    private BigDecimal chargeWeight;

    /** 体积（M3） */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "volume")
    private BigDecimal volume;

    /** 尺寸 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "size")
    private String size;

    /** 长宽高 体积 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "sizeVolume")
    private String sizeVolume;

    /** 储运注意事项 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "storageTransportNotes")
    private String storageTransportNotes;

    /** 结算注意事项 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "settlementNotes")
    private String settlementNotes;

    /** 运输保险价值 */
    private BigDecimal transportInsureValue;

    /** 提货方式 */
    private Integer deliveryMethod;

    /** 出港付费方式 */
    private Integer paymentMethod;

    /** 出港付费方式 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "paymentMethod")
    private String paymentMethodStr;

    /** 费用总计 */
    private BigDecimal costSum;

    /** 填开时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date writeTime;

    /** 填开时间打印 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "writeTime")
    private String writeTimeStr;

    /** 填开地点 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "writeLocation")
    private String writeLocation;

    /** 填开人 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "writer")
    private String writer;

    /** 备注 */
    private String remark;

    /** 分运单状态 NORMAL 正常  INVALID 作废*/
    private String status;

    /** 所属单位 */
    private Long deptId;

    /** 支付状态 0 未支付 1 已支付 2 已退款 */
    private Integer payStatus;

    /** 支付时间 */
    private Date payTime;

    /** 电子分单pdf地址 */
    private String pdfUrl;

    /** 拼单状态 0 否 1 是 */
    private Integer mergeStatus;

    /** 航司logo */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = IMAGE, pdfFieldName = "airLogo")
    private String airLogo;

    /** 运单出港费用列表 */
    private List<HawbItem> items;

    private Long itemId;

    /** 运单异常状态备注 */
    private List<HawbErrorRemark> errorRemarks;

    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "transportInsureValue")
    private String insurance;

    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "declaredValue")
    private String declaredValue;

    /** 航空费用 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "airCost")
    private BigDecimal airCost;

    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "airRate")
    private BigDecimal airRate;

    /** 费用类型 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "costType")
    private String costType;

    /** 其他费用 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "otherCost")
    private BigDecimal otherCost;

    /** 总额 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "totalCost")
    private BigDecimal totalCost;

    /** 地面运费 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "groundCost")
    private BigDecimal groundCost;

    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "otherInfo")
    private String otherInfo;

    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "rateType")
    private String rateType;

    /** 预付 */
    @PdfPrintAnnotation(pdfFieldType = CHOICE, pdfFieldName = "prePay")
    private String prePay;
}
