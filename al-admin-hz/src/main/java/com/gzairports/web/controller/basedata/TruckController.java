package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BaseFlatbedTruck;
import com.gzairports.common.basedata.domain.query.FlatbedTruckQuery;
import com.gzairports.common.basedata.service.ITruckService;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 板车管理Controller
 *
 * <AUTHOR>
 * @date 2024-02-20
 */
@RestController
@RequestMapping("/base/truck")
@Api(tags = "板车管理数据接口")
public class TruckController extends BaseController {

    @Autowired
    private ITruckService truckService;

    /**
     * 查询板车列表
     */
//    @PreAuthorize("@ss.hasPermi('base:truck:truckList')")
    @GetMapping("/truckList")
    @ApiOperation(value = "板车管理查询")
    public TableDataInfo truckList(FlatbedTruckQuery query)
    {
        startPage();
        List<BaseFlatbedTruck> list = truckService.flatbedTruckList(query);
        return getDataTable(list);
    }

    /**
     * 导出板车列表
     */
    @PreAuthorize("@ss.hasPermi('base:truck:exportTruck')")
    @Log(title = "导出板车列表", businessType = BusinessType.EXPORT)
    @PostMapping("/exportTruck")
    @ApiOperation(value = "导出板车列表")
    public void exportFlatbedTruck(HttpServletResponse response, FlatbedTruckQuery query)
    {
        List<BaseFlatbedTruck> list = truckService.flatbedTruckList(query);
        ExcelUtil<BaseFlatbedTruck> util = new ExcelUtil<BaseFlatbedTruck>(BaseFlatbedTruck.class);
        util.exportExcel(response, list, "板车列表");
    }


    /**
     * 导出板车模板
     */
    @PreAuthorize("@ss.hasPermi('base:truck:exportTruck')")
    @Log(title = "导出板车列表模板", businessType = BusinessType.EXPORT)
    @PostMapping("/exportTruckNull")
    @ApiOperation(value = "导出板车列表模板")
    public void exportTruckNull(HttpServletResponse response)
    {
        List<BaseFlatbedTruck> list = new ArrayList<>();
        ExcelUtil<BaseFlatbedTruck> util = new ExcelUtil<BaseFlatbedTruck>(BaseFlatbedTruck.class);
        util.exportExcel(response, list, "板车列表模板");
    }

    /**
     * 导入板车
     */
    @PreAuthorize("@ss.hasPermi('base:truck:importTruck')")
    @Log(title = "导入板车", businessType = BusinessType.IMPORT)
    @PostMapping("/importTruck")
    @ApiOperation(value = "导入板车")
    public AjaxResult importCityCode(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<BaseFlatbedTruck> util = new ExcelUtil<BaseFlatbedTruck>(BaseFlatbedTruck.class);
        List<BaseFlatbedTruck> trucks = util.importExcel(file.getInputStream());
        String message = truckService.importTrucks(trucks, updateSupport);
        return success(message);
    }

    /**
     * 新增板车
     */
    @PreAuthorize("@ss.hasPermi('base:truck:addTruck')")
    @Log(title = "新增板车", businessType = BusinessType.INSERT)
    @PostMapping("/addTruck")
    @ApiOperation(value = "新增板车")
    public AjaxResult addTruck(@RequestBody BaseFlatbedTruck truck)
    {
        return toAjax(truckService.addFlatbedTruck(truck));
    }

    /**
     * 修改板车
     */
    @PreAuthorize("@ss.hasPermi('base:truck:editTruck')")
    @Log(title = "修改板车", businessType = BusinessType.UPDATE)
    @PostMapping("/editTruck")
    @ApiOperation(value = "修改板车")
    public AjaxResult editTruck(@RequestBody BaseFlatbedTruck truck)
    {
        return toAjax(truckService.editFlatbedTruck(truck));
    }

    /**
     * 删除板车
     */
    @PreAuthorize("@ss.hasPermi('base:truck:removeTruck')")
    @Log(title = "删除板车", businessType = BusinessType.DELETE)
    @GetMapping("removeTruck/{ids}")
    @ApiOperation(value = "删除板车")
    public AjaxResult removeTruck(@PathVariable Long[] ids)
    {
        return toAjax(truckService.removeFlatbedTruckByIds(ids));
    }

    /**
     * 详情
     */
    @PreAuthorize("@ss.hasPermi('base:truck:getInfo')")
    @GetMapping("/getInfo/{id}")
    @ApiOperation(value = "详情")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(truckService.getInfo(id));
    }

}
