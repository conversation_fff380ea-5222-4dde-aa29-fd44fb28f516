package com.gzairports.web.controller.business.departure;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.hz.business.departure.domain.EasyBill;
import com.gzairports.hz.business.departure.domain.HzDepExitCargo;
import com.gzairports.hz.business.departure.domain.query.EasyBillInfoQuery;
import com.gzairports.hz.business.departure.domain.query.EasyBillQuery;
import com.gzairports.hz.business.departure.domain.query.ExitCargoQuery;
import com.gzairports.hz.business.departure.domain.vo.EasyBillVo;
import com.gzairports.hz.business.departure.domain.vo.ExitCargoVo;
import com.gzairports.hz.business.departure.service.IExitCargoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 退货管理Controller
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@RestController
@RequestMapping("/dep/exitCargo")
@Api(tags = "退货管理")
public class ExitCargoController extends BaseController {

    @Autowired
    private IExitCargoService exitCargoService;


    /**
     * 查询退货管理列表
     */
//    @PreAuthorize("@ss.hasPermi('dep:exitCargo:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询退货管理列表")
    public TableDataInfo list(ExitCargoQuery query)
    {
        startPage();
        List<ExitCargoVo> list = exitCargoService.selectExitCargoList(query);
        return getDataTable(list);
    }

    /**
     * 新增退货
     */
    @PreAuthorize("@ss.hasPermi('dep:exitCargo:add')")
    @PostMapping("/add")
    @ApiOperation(value = "新增退货")
    public AjaxResult add(@RequestBody HzDepExitCargo cargo){
        return toAjax(exitCargoService.insertExitCargo(cargo));
    }

    /**
     * 修改退货
     */
    @PreAuthorize("@ss.hasPermi('dep:exitCargo:edit')")
    @PostMapping("/edit")
    @ApiOperation(value = "修改退货")
    public AjaxResult edit(@RequestBody HzDepExitCargo cargo){
        return toAjax(exitCargoService.editExitCargo(cargo));
    }

    /**
     * 查看退货详情
     */
    @PreAuthorize("@ss.hasPermi('dep:exitCargo:getInfo')")
    @GetMapping("/getInfo/{id}")
    @ApiOperation(value = "查看退货详情")
    public AjaxResult getInfo(@PathVariable("id") Long id){
        return AjaxResult.success(exitCargoService.getInfo(id));
    }

    /**
     * 根据运单号查询
     */
    @PreAuthorize("@ss.hasPermi('dep:exitCargo:getWaybill')")
    @GetMapping("/getWaybill/{waybillCode}")
    @ApiOperation(value = "根据运单号查询")
    public AjaxResult getWaybill(@PathVariable("waybillCode") String waybillCode){
        return AjaxResult.success(exitCargoService.getWaybill(waybillCode));
    }
}
