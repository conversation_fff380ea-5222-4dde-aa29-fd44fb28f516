package com.gzairports.hz.business.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 虚拟收运列表返回参数
 *
 * <AUTHOR>
 * @date 2024-06-26
 */
@Data
public class VirtualListVo {

    /** 主键id（运单id） */
    private Long id;

    /** 运单号 */
    private String waybillCode;

    /** 代理人 */
    private String agent;

    /** 代理人简称 */
    private String agentAbb;

    /** 特货代码 */
    private String specialCargoCode1;

    /** 品名 */
    private String cargoName;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 体积（M3） */
    private BigDecimal volume;

    /** 尺寸 */
    private String size;

    /** 目的站 */
    private String desPort;

    /** 航班号 */
    private String flightNo1;

    /** 日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date writeTime;

    /** 储运注意事项 */
    private String storageTransportNotes;

    /** 部门id */
    private Long deptId;

}
