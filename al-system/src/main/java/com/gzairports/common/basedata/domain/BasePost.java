package com.gzairports.common.basedata.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 邮局维护
 *
 * <AUTHOR>
 * @date 2024-04-01/post
 */
@Data
@TableName("base_post_office")
public class BasePost {

    /** 主键id */
    private Long id;

    /** 邮局名称 */
    private String officeName;

    /** 邮局局类型  0:接收局;1:托运局 */
    private String officeType;

    /** 机场编码 */
    private String airCode;

    /** 联系人 */
    private String linkName;

    /** 电话 */
    private String phone;

    /** 邮件种类 */
    private Integer mailType;

    /** 所在地区 */
    private String location;

    /** 详细地址 */
    private String address;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 是否删除 */
    private Integer isDel;

    /** 是否公用 */
    private Long isCommon;
}
