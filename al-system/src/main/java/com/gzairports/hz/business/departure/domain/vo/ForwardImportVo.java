package com.gzairports.hz.business.departure.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 代运导入返回数据
 * <AUTHOR>
 * @date 2024-06-28
 */
@Data
public class ForwardImportVo {

   /** 航班配载id */
   private Long id;

   /** 是否已结算 */
   private Integer isSettle;

   /** 是否已完成 */
   private Integer isComp;

   /** 航班状态 状态（未配载 已配载 未组货 已组货 未复重 未交接 已完成 无货 航班起飞） */
   private String state;

    /** 总票数 */
   private Integer waybillNum;

    /** 总件数 */
    private Integer totalQuantity;

    /** 总重量 */
    private BigDecimal totalWeight;

    /** 代运导入运单集合 */
    private List<ForwardImportWaybillVo> vos;

    /** 总票数 */
    private Integer uldNum;

    /** 总件数 */
    private Integer uldTotalQuantity;

    /** 总重量 */
    private BigDecimal uldTotalWeight;

    /** 刷新类型 0 不刷新 1 刷新 */
    private Integer type;

    /** 代运导入板箱集合 */
    private List<ForwardImportUldVo> uldVos;

    /** 计算不同舱位的重量和体积, */
    private Map<String,List<Object>> uldMap;

    /** 计算不同舱位的重量和体积,右边遍历的数据 */
    private Map<String,List<Object>> uldMapLeft;

    /** 计算不同舱位的重量和体积,左边遍历的数据 */
    private Map<String,List<Object>> uldMapRight;
}
