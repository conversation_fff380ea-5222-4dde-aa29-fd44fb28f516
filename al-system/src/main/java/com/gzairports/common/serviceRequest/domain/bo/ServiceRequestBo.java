package com.gzairports.common.serviceRequest.domain.bo;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 修改服务申请参数
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@Data
public class ServiceRequestBo {

    /** 服务申请ID */
    @NotNull(message = "服务申请ID不能为空")
    private Long id;

//    /** 运单号 */
//    private String waybillCode;

    /** 服务项目ID */
    private Long serviceItemId;

    /** 预算选择时间 */
    private Integer selectTime;

    /** 申请描述 */
    private String applyRemark;
}
