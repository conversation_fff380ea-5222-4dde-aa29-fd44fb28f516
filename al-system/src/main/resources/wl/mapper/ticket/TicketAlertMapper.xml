<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.wl.ticket.mapper.TicketAlertMapper">
    <update id="remove">
        update wl_ticket_alert set is_del = 1 where id = #{id}
    </update>
    <select id="selectListByQuery" resultType="com.gzairports.wl.ticket.domain.TicketAlert">
        select wta.id,
               wta.bill_source,
               wta.bill_type,
               wta.bill_prefix,
               wta.is_chartering,
               wta.number_alert, sd.dept_name
        from wl_ticket_alert wta
        left join sys_dept sd on sd.dept_id = wta.dept_id
        <where>
            wta.is_del = 0 and wta.dept_id = #{deptId}
            <if test="billType != null and billType != ''">and wta.bill_type  = #{billType}</if>
            <if test="billPrefix != null and billPrefix != ''">and wta.bill_prefix = #{billPrefix}</if>
        </where>
    </select>
    <select id="getInfo" resultType="com.gzairports.wl.ticket.domain.TicketAlert">
        select wta.id,wta.bill_source_id,
               wta.bill_source,
               wta.bill_type,
               wta.bill_prefix,
               wta.is_chartering,
               wta.number_alert, sd.dept_name,wta.dept_id
        from wl_ticket_alert wta
                 left join sys_dept sd on sd.dept_id = wta.dept_id
        where wta.is_del = 0 and wta.id = #{id}
    </select>
    <select id="selectByBillId" resultType="com.gzairports.wl.ticket.domain.TicketAlert">
        select wta.id,
               wta.bill_source,
               wta.bill_type,
               wta.bill_prefix,
               wta.is_chartering,
               wta.number_alert, sd.dept_name,wta.dept_id
        from wl_ticket_alert wta
                 left join sys_dept sd on sd.dept_id = wta.dept_id
        where wta.is_del = 0 and wta.dept_id = #{deptId} and wta.bill_source = #{billSource} and wta.is_chartering = #{isChartering}
    </select>
</mapper>