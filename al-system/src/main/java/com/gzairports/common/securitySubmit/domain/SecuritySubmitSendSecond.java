package com.gzairports.common.securitySubmit.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: lan
 * @Desc: 第二次推送：库管员审单是推送审单相关数据。
 * @create: 2024-11-14 18:22
 **/
@Data
public class SecuritySubmitSendSecond {
    /**
     * 航班号
     * */
    private String flightNo;
    /**
     * 日期
     * */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date execDate;
    /**
     * 航空货运单号
     * */
    private String waybillCode;
    /**
     * 收运员安全检查结论
     * */
    private String checkInference;
    /**
     * 收运员
     * */
    private String collectName;
    /**
     * 审单时间
     * */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;

    /**
     * 电子安检申报单地址
     * */
    private List<String> securitypdfUrls;

    private String messageType;
}
