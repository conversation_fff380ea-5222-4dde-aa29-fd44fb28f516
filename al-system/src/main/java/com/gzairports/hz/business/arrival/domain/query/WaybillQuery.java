package com.gzairports.hz.business.arrival.domain.query;

import lombok.Data;

import java.util.Date;

/**
 * 运单查询参数
 * <AUTHOR>
 * @date 2024-07-25
 */
@Data
public class WaybillQuery {

    /** 运单类型 */
    private String waybillType;

    /** 运单前缀 */
    private String prefix;

    /** 运单号 */
    private String waybillCode;

    /** 国际国内 D国内 I国际 */
    private String domint;

    /** 进出港类型 */
    private String type;

    /** 始发站 */
    private String sourcePort;

    /** 目的站 */
    private String desPort;

    /** 发货代理人 */
    private String shipper;

    /** 收货人 */
    private String consign;

    /** 收货代理人*/
    private String agentCode;

    /** 特货代码 */
    private String specialCode;

    /** 承运人 */
    private String carrier;

    /** 进港航班号 */
    private String flightNo;

    /** 货品代码 */
    private String cargoCode;

    /** 运单状态 */
    private String status;

    /** 运单状态 */
    private String waybillStatus;

    /** 开始时间 */
    private Date startTime;

    /** 结束时间 */
    private Date endTime;
}
