package com.gzairports.common.basedata.service.impl;


import com.gzairports.common.basedata.domain.BaseWeight;
import com.gzairports.common.basedata.domain.query.WeightUnitQuery;
import com.gzairports.common.basedata.mapper.WeightMapper;
import com.gzairports.common.basedata.service.IWeightService;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.exception.ServiceException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.bean.BeanValidators;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.Validator;
import java.util.List;

/**
 * 多重量单位Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Service
public class WeightServiceImpl implements IWeightService {

    private static final Logger log = LoggerFactory.getLogger(WeightServiceImpl.class);

    @Autowired
    private Validator validator;

    @Autowired
    private WeightMapper weightMapper;

    /**
     * 查询重量单位列表
     *
     * @param query 查询参数
     * @return 重量单位列表
     */
    @Override
    public List<BaseWeight> selectWeightUnitList(WeightUnitQuery query) {
        return weightMapper.selectWeightUnitList(query);
    }

    /**
     * 导入重量单位
     *
     * @param weights 重量单位数据列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importWeight(List<BaseWeight> weights, boolean updateSupport) {
        if (StringUtils.isNull(weights) || weights.size() == 0)
        {
            throw new ServiceException("导入重量单位数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (BaseWeight weight : weights) {
            try {
                // 验证是否存在相同的重量单位
                BaseWeight baseWeight = weightMapper.selectWeightByWeightUnit(weight.getWeightUnit());
                if (StringUtils.isNull(baseWeight)) {
                    BeanValidators.validateWithException(validator, weight);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    weight.setCreateBy(loginUser.getUsername());
                    weight.setCreateTime(DateUtils.getNowDate());
                    weightMapper.insertWeight(weight);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、重量单位 " + weight.getWeightUnit() + " 导入成功");
                }
                else if (updateSupport) {
                    BeanValidators.validateWithException(validator, weight);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    weight.setId(baseWeight.getId());
                    weight.setUpdateBy(loginUser.getUsername());
                    weight.setUpdateTime(DateUtils.getNowDate());
                    weightMapper.updateWeight(weight);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、重量单位 " + weight.getWeightUnit() + " 更新成功");
                }
                else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、重量单位 " + weight.getWeightUnit() + " 已存在");
                }
            }catch (Exception e){
                failureNum++;
                String msg = "<br/>" + failureNum + "、重量单位 " + weight.getWeightUnit() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 新增重量单位
     *
     * @param weight 重量单位
     * @return 结果
     */
    @Override
    public int addWeight(BaseWeight weight) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        weight.setCreateBy(loginUser.getUsername());
        weight.setCreateTime(DateUtils.getNowDate());
        return weightMapper.insertWeight(weight);
    }

    /**
     * 修改重量单位
     *
     * @param weight 重量单位
     * @return 结果
     */
    @Override
    public int editWeight(BaseWeight weight) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        weight.setUpdateBy(loginUser.getUsername());
        weight.setUpdateTime(DateUtils.getNowDate());
        return weightMapper.updateWeight(weight);
    }

    /**
     * 删除重量单位
     *
     * @param ids 重量单位id集合
     * @return 结果
     */
    @Override
    public int delWeight(Long[] ids) {
        return weightMapper.delWeight(ids);
    }
}
