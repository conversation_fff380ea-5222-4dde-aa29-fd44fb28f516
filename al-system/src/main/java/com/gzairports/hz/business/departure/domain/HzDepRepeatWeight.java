package com.gzairports.hz.business.departure.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 复重表
 *
 * <AUTHOR>
 * @date 2024-07-06
 */
@Data
@TableName("hz_dep_repeat_weight")
public class HzDepRepeatWeight {

    /** 主键id */
    private Long id;

    /** 航班配载id */
    private Long flightLoadId;

    /** 舱位 */
    private String cabin;

    /** 板车号 */
    private String uld;

    /** 目的站 */
    private String des1;

    /** 货物件数 */
    private Integer quantity;

    /** 货物净重 */
    private BigDecimal weight;

    /** 板货总量 */
    private BigDecimal boardCargoWeight;

    /** 垫板重量 */
    private BigDecimal plateWeight;

    /** 板箱自重 */
    private BigDecimal boardWeight;

    /** 文件重量 */
    private BigDecimal fileWeight;

    /** 实际重量 */
    private BigDecimal realityWeight;

    /** 组货板箱id */
    private Long groupUldId;

    /** 操作人（昵称） */
    private String operName;
}
