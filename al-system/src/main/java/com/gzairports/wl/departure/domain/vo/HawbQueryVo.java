package com.gzairports.wl.departure.domain.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzairports.wl.departure.domain.Hawb;
import com.gzairports.common.business.departure.domain.Mawb;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 国内分单返回参数
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Data
public class HawbQueryVo {

    /** 总单数 */
    private Integer totalOrder;

    /** 总件数 */
    private Integer totalQuantity;

    /** 总重量 */
    private BigDecimal totalWeight;

    /** 计费总重量 */
    private BigDecimal totalChargeWeight;

    /** 总金额 */
    private BigDecimal totalMoney;

    /** 未支付 */
    private BigDecimal unPay;

    /** 已支付 */
    private BigDecimal pay;

    /** 已退款 */
    private BigDecimal refund;

    /** 客户（发货人） */
    private String shipper;

    /** 分单列表 */
    private List<Hawb> list;

    /** 主单列表 */
    private List<Mawb> mawbs;

    private Page<MawbQueryVo> vos;

    private Page<HawbQueryVo1> vo1List;
}
