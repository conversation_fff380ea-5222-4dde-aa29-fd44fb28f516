package com.gzairports.common.basedata.service.impl;


import com.gzairports.common.basedata.domain.BaseSpecialCargoCode;
import com.gzairports.common.basedata.domain.query.SpecialCargoCodeQuery;
import com.gzairports.common.basedata.mapper.SpecialCargoCodeMapper;
import com.gzairports.common.basedata.service.ISpecialCargoCodeService;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.exception.ServiceException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.bean.BeanValidators;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.Validator;
import java.util.List;

/**
 * 装载特货代码Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Service
public class SpecialCargoCodeServiceImpl implements ISpecialCargoCodeService {

    private static final Logger log = LoggerFactory.getLogger(WeightServiceImpl.class);

    @Autowired
    private Validator validator;

    @Autowired
    private SpecialCargoCodeMapper specialCargoCodeMapper;

    /**
     * 查询装载特货代码列表
     *
     * @param query 查询参数
     * @return 装载特货代码列表
     */
    @Override
    public List<BaseSpecialCargoCode> selectSpecialCargoCodeList(SpecialCargoCodeQuery query) {
        return specialCargoCodeMapper.selectSpecialCargoCodeList(query);
    }

    /**
     * 导入装载特货代码
     *
     * @param codes 装载特货代码列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importSpecialCargoCode(List<BaseSpecialCargoCode> codes, boolean updateSupport) {
        if (StringUtils.isNull(codes) || codes.size() == 0)
        {
            throw new ServiceException("导入装载特货代码数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (BaseSpecialCargoCode code : codes) {
            try {
                // 验证是否存在相同的装载特货代码,特殊处理代码,航司,是否危险品,承运人代码不能同时重复
                BaseSpecialCargoCode cargoCode = specialCargoCodeMapper.selectSpecialCargoCodeByCode(code.getHandleCode(), code.getAirCompany(),
                        code.getIsDanger(),code.getCarrierCode());
                if (StringUtils.isNull(cargoCode)) {
                    BeanValidators.validateWithException(validator, code);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    code.setCreateBy(loginUser.getUsername());
                    code.setCreateTime(DateUtils.getNowDate());
                    specialCargoCodeMapper.insertSpecialCargoCode(code);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、装载特货代码 " + code.getHandleCode() + " 导入成功");
                }
                else if (updateSupport) {
                    BeanValidators.validateWithException(validator, code);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    code.setId(cargoCode.getId());
                    code.setUpdateBy(loginUser.getUsername());
                    code.setUpdateTime(DateUtils.getNowDate());
                    specialCargoCodeMapper.updateSpecialCargoCode(code);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、装载特货代码 " + code.getHandleCode() + " 更新成功");
                }
                else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、装载特货代码 " + code.getHandleCode() + " 已存在");
                }
            }catch (Exception e){
                failureNum++;
                String msg = "<br/>" + failureNum + "、装载特货代码 " + code.getHandleCode() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 新增装载特货代码
     *
     * @param code 装载特货代码
     * @return 结果
     */
    @Override
    public int addSpecialCargoCode(BaseSpecialCargoCode code) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        code.setCreateBy(loginUser.getUsername());
        code.setCreateTime(DateUtils.getNowDate());
        return specialCargoCodeMapper.insertSpecialCargoCode(code);
    }

    /**
     * 修改装载特货代码
     *
     * @param code 装载特货代码
     * @return 结果
     */
    @Override
    public int editSpecialCargoCode(BaseSpecialCargoCode code) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        code.setUpdateBy(loginUser.getUsername());
        code.setUpdateTime(DateUtils.getNowDate());
        return specialCargoCodeMapper.updateSpecialCargoCode(code);
    }

    /**
     * 删除装载特货代码
     *
     * @param ids 装载特货代码d集合
     * @return 结果
     */
    @Override
    public int delSpecialCargoCode(Long[] ids) {
        return specialCargoCodeMapper.delSpecialCargoCode(ids);
    }
}
