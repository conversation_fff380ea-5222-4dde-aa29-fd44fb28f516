package com.gzairports.hz.business.arrival.domain.vo;

import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 出库单运单数据
 *
 * <AUTHOR>
 * @date 2024-07-19
 */
@Data
public class OutOrderWaybillAWBMVo {

    /** 运单号 */
    @Excel(name = "运单号", width = 20)
    private String waybillCode;

    /** 特货代码 */
    @Excel(name = "特货代码")
    private String specialCode;

    /** 品名代码 */
    @Excel(name = "品名代码")
    private String cargoCode;

    /** 品名 */
    @Excel(name = "品名")
    private String cargoName;

    /** 件数 */
    @Excel(name = "件数", cellType = Excel.ColumnType.NUMERIC)
    private Integer quantity;

    /** 重量 */
    @Excel(name = "重量", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal weight;

    /** 体积 */
    @Excel(name = "体积", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal volume;

    /** 航班 （航班号/航班日期）*/
    @Excel(name = "航班")
    private String flight;

    /** 航程 （始发站/目的站）*/
    @Excel(name = "航程")
    private String voyage;

    /** 国内国际 */
    @Excel(name = "国内国际")
    private String domesticOrInternational;

    /** 海关监管 */
    @Excel(name = "海关监管")
    private String customsSupervision;

    /** 部门ID */
    private Long deptId;

    /** 代理人 */
    @Excel(name = "代理人")
    private String agent;

    /** 发货人 */
    @Excel(name = "发货人")
    private String shipper;

    /** 收货人 */
    @Excel(name = "收货人")
    private String consign;

//    /** 创建时间 */
//    @Excel(name = "创建时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
//    private LocalDateTime createTime;
//
//    /** 创建人 */
//    @Excel(name = "创建人")
//    private String createBy;

    /** 填开时间 */
    @Excel(name = "创建时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime writeTime;

    /** 创建人 */
    @Excel(name = "创建人")
    private String writer;

    /** 不正常未结案 */
    @Excel(name = "不正常未结案")
    private String abnormalNotClosed;

    /** 库存 (运单办单件数之和/重量之和)*/
    @Excel(name = "库存")
    private String inventory;

    /** 进港状态*/
    @Excel(name = "进港状态")
    private String arrStatus;

    /** 出港状态*/
    @Excel(name = "出港状态")
    private String depStatus;

    /** 审核完毕*/
    @Excel(name = "审核完毕")
    private String reviewComplete;
}
