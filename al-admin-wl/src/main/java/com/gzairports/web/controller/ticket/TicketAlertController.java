package com.gzairports.web.controller.ticket;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.wl.ticket.domain.TicketAlert;
import com.gzairports.wl.ticket.domain.query.TicketQuery;
import com.gzairports.wl.ticket.service.TicketAlertService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 单证预警Controller
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@RestController
@RequestMapping("/alert")
@Api(tags = "单证预警接口")
public class TicketAlertController extends BaseController {

    @Autowired
    private TicketAlertService alertService;

    /**
     * 查询单证预警列表
     */
//    @PreAuthorize("@ss.hasPermi('ticket.alert.select')")
    @GetMapping("/list")
    @ApiOperation(value = "查询单证预警列表")
    public TableDataInfo list(TicketQuery query){
        startPage();
        List<TicketAlert> list = alertService.selectList(query);
        return getDataTable(list);
    }

    /**
     * 预警新增
     */
    @PreAuthorize("@ss.hasPermi('ticket.alert.add')")
    @PostMapping("/add")
    @ApiOperation(value = "预警新增")
    public AjaxResult add(@RequestBody TicketAlert alert){
        return toAjax(alertService.add(alert));
    }

    /**
     * 预警删除
     */
    @PreAuthorize("@ss.hasPermi('ticket.alert.remove')")
    @GetMapping("/remove/{id}")
    @ApiOperation(value = "预警删除")
    public AjaxResult remove(@PathVariable("id") Long id){
        return toAjax(alertService.removeAlert(id));
    }

    /**
     * 查看预警详情
     */
    @PreAuthorize("@ss.hasPermi('ticket.alert.getInfo')")
    @GetMapping("/{id}")
    @ApiOperation(value = "查看预警详情")
    public AjaxResult getInfo(@PathVariable("id") Long id){
        return AjaxResult.success(alertService.getInfo(id));
    }

    /**
     * 预警修改
     */
    @PreAuthorize("@ss.hasPermi('ticket.alert.edit')")
    @PostMapping("/edit")
    @ApiOperation(value = "预警修改")
    public AjaxResult edit(@RequestBody TicketAlert alert){
        return toAjax(alertService.edit(alert));
    }

    /**
     * 预警
     */
//    @PreAuthorize("@ss.hasPermi('ticket.alert.alert')")
    @GetMapping("/alert")
    @ApiOperation(value = "预警")
    public void alert(){
       alertService.alert();
    }
}
