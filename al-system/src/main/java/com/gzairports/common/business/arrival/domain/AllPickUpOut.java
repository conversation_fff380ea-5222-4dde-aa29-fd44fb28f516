package com.gzairports.common.business.arrival.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 提货出库表
 *
 * <AUTHOR>
 * @date 2024-07-19
 */
@Data
@TableName(value = "all_pick_up_out")
public class AllPickUpOut {

    /** 主键id */
    private Long id;

    /** 办理提货id */
    private Long pickUpId;

    /** 提货件数 */
    private Integer pieces;

    /** 提货重量 */
    private BigDecimal weight;

    /** 提货码 */
    private String pickUpCode;

    /** 理货id */
    private Long tallyId;

    /** 运单号 */
    private String waybillCode;

    /** 提货时间 */
    private Date outTime;

    /** 出库备注 */
    private String remark;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /** 修改后的提货件数 */
    private Integer piecesChanged;

    /** 修改后的提货重量 */
    private BigDecimal weightChanged;

    /** 是否取消办单 0 否 1 是*/
    @TableLogic
    private Integer isCancel;

    /** app交付界面的提货件数 */
    @TableField(exist = false)
    private Integer canPickUpQuantity;

    /** app交付界面的提货重量 */
    @TableField(exist = false)
    private BigDecimal canPickUpWeight;

    /** app交付界面的办单件数 */
    @TableField(exist = false)
    private Integer orderQuantity;

    /** app交付界面的办单重量 */
    @TableField(exist = false)
    private BigDecimal orderWeight;

}
