package com.gzairports.web.controller.reporter;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.hz.business.reporter.domain.HzReportSet;
import com.gzairports.hz.business.reporter.domain.query.HzReportQuery;
import com.gzairports.hz.business.reporter.service.CountReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;


/**
 * <AUTHOR>
 * @date 2025-03-06
 **/
@RestController
@RequestMapping("/hz/countReport")
@Api(tags = "报表设置")
public class CountReportController extends BaseController {

    @Autowired
    private CountReportService reportService;

    /**
     * 统计报表数据
     * */
    @GetMapping("/list")
    @ApiOperation(value = "统计报表数据")
    public AjaxResult list(){
        return AjaxResult.success(reportService.selectReportTitle());
    }

    /**
     * 查询过滤字段
     * */
    @GetMapping("/getFilter/{setId}")
    @ApiOperation(value = "查询过滤字段")
    public AjaxResult getFilter(@PathVariable("setId") Long setId){
        //查询报表信息
        HzReportSet hzReportSet = reportService.selectById(setId);
        if ("0".equals(hzReportSet.getIsFrame())) {
            return AjaxResult.success("操作成功", hzReportSet.getPageSite());
        }
        return AjaxResult.success(reportService.getFilter(setId));
    }

    /**
     * 查询统计报表
     * */
    @PostMapping("/getReportExcel")
    @ApiOperation(value = "查询统计报表")
    public void getReportExcel(@RequestBody HzReportQuery query, HttpServletResponse response) throws IOException {
        reportService.getReportExcel(query,response);
    }

    /**
     * 报表列表查询
     */
    @PostMapping("/pageQuery")
    @ApiOperation(value = "报表列表分页数据")
    public AjaxResult pageQuery(@RequestBody HzReportQuery query){
        Integer count = reportService.pageQuery(query);
        return AjaxResult.success(count);
    }

}
