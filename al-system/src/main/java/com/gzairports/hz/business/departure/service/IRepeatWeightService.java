package com.gzairports.hz.business.departure.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.hz.business.departure.domain.HzDepRepeatWeight;
import com.gzairports.hz.business.departure.domain.query.GroupCargoQuery;
import com.gzairports.hz.business.departure.domain.vo.HzDepRepeatWaybillsVo;
import com.gzairports.hz.business.departure.domain.vo.PrintRepeatVo;
import com.gzairports.hz.business.departure.domain.vo.RepeatWeightVo;
import com.gzairports.hz.business.departure.domain.vo.WeightInfoVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 复重Service接口
 *
 * <AUTHOR>
 * @date 2024-07-06
 */
public interface IRepeatWeightService extends IService<HzDepRepeatWeight> {

    /**
     * 查询复重列表
     * @param query 查询条件
     * @return 结果
     */
    List<RepeatWeightVo> selectList(GroupCargoQuery query);

    /**
     * 复磅
     * @param id 航班配载id
     * @return 复磅列表
     */
    List<HzDepRepeatWeight> weight(Long id);

    /**
     * 板箱复重详情
     * @param id 板箱复重id
     * @return 详情
     */
    WeightInfoVo getInfo(Long id);

    /**
     * 编辑
     * @param repeatWeight 编辑参数
     * @return 结果
     */
    int edit(HzDepRepeatWeight repeatWeight);

    /**
     * 复磅完成
     * @param id 航班配载id
     * @return 结果
     */
    int finishWeight(Long id);

    /**
     * 新增板车
     * @param weight 新增数据
     * @return 结果
     */
    List<HzDepRepeatWeight> addCar(HzDepRepeatWeight weight);

    /**
     * 打印挂牌
     * @param id 复重id
     * @param response 输出流
     */
    void printInstallSigns(Long id, HttpServletResponse response) throws Exception;

    /**
     * app打印挂牌
     * @param id 复磅id
     */
    PrintRepeatVo printAppInstallSigns(Long id);

    /**
     * 查询板车上的运单详情
     * @param id 组货的板车id
     * */
    List<HzDepRepeatWaybillsVo> getWaybills(Long id);
}
