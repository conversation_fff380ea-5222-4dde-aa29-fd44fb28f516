package com.gzairports.hz.business.arrival.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 航班文件运单理货历史对象
 * <AUTHOR>
 * @date 2024-07-18
 */
@Data
public class TallyHistoryVo {
    /** 理货历史id */
    private Long id;

    /** 航班号 */
    private String flightNo;

    /** 时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date tallyTime;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 舱单件数 */
    private Integer cabinPieces;

    /** 舱单重量 */
    private BigDecimal cabinWeight;

    /** 理货件数 */
    private Integer tallyQuantity;

    /** 理货重量 */
    private BigDecimal tallyWeight;

    /** 仓库 */
    private String store;

    /** 库位 */
    private String locator;

    /** 理货不正常描述 */
    private String abnormal;

    /** 板箱号 */
    private String uld;

    /** 理货人 */
    private String username;
}
