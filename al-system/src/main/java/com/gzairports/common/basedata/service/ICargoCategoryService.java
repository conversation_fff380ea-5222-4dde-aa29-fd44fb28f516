package com.gzairports.common.basedata.service;

import com.gzairports.common.basedata.domain.BaseCargoCategory;
import com.gzairports.common.basedata.domain.query.CargoCategoryQuery;

import java.util.List;

/**
 * 货品大类Service接口
 *
 * <AUTHOR>
 * @date 2024-06-14
 */
public interface ICargoCategoryService {

    /**
     * 查询货品大类列表
     *
     * @param query 查询参数
     * @return 货品大类列表
     */
    List<BaseCargoCategory> selectCargoCategoryList(CargoCategoryQuery query);

    /**
     * 新增货品大类
     *
     * @param code 货品大类
     * @return 结果
     */
    int addCargoCategory(BaseCargoCategory code);

    /**
     * 修改货品大类
     *
     * @param code 货品大类
     * @return 结果
     */
    int editCargoCategory(BaseCargoCategory code);

    /**
     * 删除货品大类
     *
     * @param ids 货品大类id集合
     * @return 结果
     */
    int delCargoCategory(Long[] ids);
}
