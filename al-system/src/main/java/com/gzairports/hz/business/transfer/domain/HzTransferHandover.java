package com.gzairports.hz.business.transfer.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import com.gzairports.common.core.domain.BizEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * 中转交接对象 hz_transfer_handover
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
@Data
public class HzTransferHandover {

    /** 主键id */
    private Long id;

    /** 创建时间 */
    @Excel(name = "创建时间",dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 交接单号 */
    @Excel(name = "交接单号")
    private String handoverNo;

    /** 总票数 */
    @Excel(name = "总票数")
    private Integer votes;

    /** 总件数 */
    @Excel(name = "总件数")
    private Integer totalPrices;

    /** 总重量 */
    @Excel(name = "总重量")
    private BigDecimal totalWeight;

    /** 文件交接状态 */
    @Excel(name = "文件交接状态", readConverterExp = "0=未交接,1=未接收,2=已接收")
    private Integer fileStatus;

    /** 文件交接人 */
    private String fileBy;

    /** 文件转出时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date fileOutTime;

    /** 文件接收时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date fileInTime;

    /** 货物交接状态 */
    @Excel(name = "货物交接状态", readConverterExp = "0=未交接,1=未接收,2=已接收")
    private Integer cargoStatus;

    /** 货物交接人 */
    private String cargoBy;

    /** 货物转出时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cargoOutTime;

    /** 货物接收时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cargoInTime;

    /** 转出仓库 */
    private String outStore;

    /** 转出库位 */
    private String outLocator;

    /** 转出库 */
    @Excel(name = "转出库")
    @TableField(exist = false)
    private String out;

    /** 转入仓库 */
    private String inStore;

    /** 转入库位 */
    private String inLocator;

    /** 转入库 */
    @Excel(name = "转入库")
    @TableField(exist = false)
    private String in;

    /** 转出人 */
    private String outName;

    /** 备注 */
    private String remark;

    @TableField(exist = false)
    private Date startTime;

    @TableField(exist = false)
    private Date endTime;

    /** 运单号 */
    @TableField(exist = false)
    private String waybillCode;
}
