package com.gzairports.web.controller.serviceProcessing;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.serviceProcessing.domain.ServiceProcessing;
import com.gzairports.common.serviceProcessing.domain.query.ServiceProcessingQuery;
import com.gzairports.common.serviceProcessing.domain.vo.ServiceProcessingVo;
import com.gzairports.common.serviceProcessing.service.IServiceProcessingService;
import com.gzairports.common.serviceRequest.domain.query.ServiceRequestQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @author: lan
 * @Desc: 自助办单controller
 * @create: 2024-11-04 14:24
 **/
@RestController
@RequestMapping("/wl/serviceProcessing")
@Api(tags = "自助办单")
public class serviceProcessingController extends BaseController {
    @Autowired
    private IServiceProcessingService serviceProcessingService;

    /**
     * 自助办单界面办理提货 -> 相当于挑单
     */
//    @PreAuthorize("@ss.hasPermi('wl:serviceProcessing:pickUpOne')")
    @PostMapping("/pickUpOne")
    @ApiOperation(value = "自助办单界面办理提货")
    public AjaxResult pickUpOne(@RequestBody ServiceProcessing serviceProcessing){
        return AjaxResult.success(serviceProcessingService.pickUpOne(serviceProcessing));
    }


    /**
     * 自助办单界面支付 ->相当于保存+结算 返回一个二维码用于支付
     */
//    @PreAuthorize("@ss.hasPermi('wl:serviceProcessing:pay')")
    @PostMapping("/pay")
    @ApiOperation(value = "自助办单界面支付")
    public AjaxResult pay(@RequestBody ServiceProcessingQuery vo){
        return AjaxResult.success(serviceProcessingService.pay(vo));
    }

    /**
     * 生成提货二维码
     */
//    @PreAuthorize("@ss.hasPermi('wl:serviceProcessing:printOutOrder')")
    @GetMapping("/printOutOrder/{pickUpId}")
    @ApiOperation(value = "生成提货二维码")
    public AjaxResult printOutOrder(@PathVariable("pickUpId") Long pickUpId){
        return AjaxResult.success(serviceProcessingService.printOutOrder(pickUpId));
    }


    @PostMapping("/comparison")
    @ApiOperation(value = "比对人像")
    public AjaxResult comparison(@RequestBody ServiceProcessingQuery vo){
        return AjaxResult.success(serviceProcessingService.comparison(vo));
    }




}
