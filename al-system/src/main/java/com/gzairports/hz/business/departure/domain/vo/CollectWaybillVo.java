package com.gzairports.hz.business.departure.domain.vo;

import com.gzairports.hz.business.departure.domain.HzCollectWeight;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 运单收运返回参数
 *
 * <AUTHOR>
 * @date 2024-06-25
 */
@Data
public class CollectWaybillVo {

    /** 主键id */
    private Long id;

    /** 运单id */
    private Long waybillId;

    /** uld号 */
    private String uld;

    /** 收运件数 */
    private Integer quantity;

    /** 收运重量 */
    private BigDecimal weight;

    /** 过磅记录 */
    List<HzCollectWeight> weights;
}
