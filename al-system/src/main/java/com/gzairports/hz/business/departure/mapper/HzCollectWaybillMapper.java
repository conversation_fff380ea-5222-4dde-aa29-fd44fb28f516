package com.gzairports.hz.business.departure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.gzairports.common.business.reporter.domain.ReportDataCollect;
import com.gzairports.hz.business.departure.domain.HzCollectWaybill;
import com.gzairports.hz.business.departure.domain.query.ForwardImportQuery;
import com.gzairports.hz.business.departure.domain.query.InventoryQuery;
import com.gzairports.hz.business.departure.domain.query.WaybillInfoQuery;
import com.gzairports.hz.business.departure.domain.vo.CollectWaybillVo;
import com.gzairports.hz.business.departure.domain.vo.DetailedVo;
import com.gzairports.hz.business.departure.domain.vo.ForwardImportWaybillVo;
import com.gzairports.hz.business.departure.domain.vo.InventoryVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 运单收运Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Mapper
public interface HzCollectWaybillMapper extends BaseMapper<HzCollectWaybill> {

    /**
     * 根据运单id查询收运总条数
     * @param waybillIds 运单id
     * @return 条数
     */
    Integer selectCountByIds(@Param("waybillIds") List<Long> waybillIds);

    /**
     * 根据id集合查询收运数据
     * @param collectWaybillId id集合
     * @return 运单收运列表
     */
    List<ForwardImportWaybillVo> selectListByIds(@Param("collectWaybillId") List<Long> collectWaybillId,
                                                 @Param("uld") String uld);

    /**
     * 查询运单库存列表
     * @param query 查询条件
     * @return 结果
     */
    List<InventoryVo> selectListByQuery(InventoryQuery query);

    /**
     * 查看修改详情
     * @param id 收运id
     * @return 详情
     */
    CollectWaybillVo selectEditInfoById(Long id);

    /**
     * 查询库存数据
     * @param query 查询条件
     * @return 库存数据
     */
    List<InventoryVo> selectInventoryList(WaybillInfoQuery query);

    List<ForwardImportWaybillVo> selectByCollectId(@Param("collectId") Long collectId,@Param("uld") String uld);

    List<ReportDataCollect> selectCollectList(@Param("depReportIds") List<Long> depReportIds);
}
