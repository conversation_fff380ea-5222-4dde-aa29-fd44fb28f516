package com.gzairports.common.log.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 运单日志表
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
@Data
@TableName("all_waybill_log")
public class WaybillLog {

    private static final long serialVersionUID = 1L;

    /** 日志主键 */
    private Long id;

    /** 运单号 */
    private String waybillCode;

    /** 航班号 */
    private String flightNo;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private LocalDateTime flightDate;

    /** 业务类型（0其它 1新增 2修改 3删除） */
    private Integer operRemark;

    /** 操作人员 */
    private String operName;

    /** 操作重量 */
    private String operWeight;

    /** 操作件数 */
    private String operPieces;

    /** 请求参数 */
    private String operParam;

    /** 返回参数 */
    private String jsonResult;

    /** 操作状态 */
    private Integer status;

    /** 操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operTime;

    /** 错误消息 */
    private String errorMsg;

    /** 备注 */
    private String remark;

    /** uld号 */
    private String uld;

    /** 类型 ARR到港 DEP离港 TRANDFER 中转  */
    private String type;

    /** 请求方式 */
    @TableField(exist = false)
    private String requestMethod;
}
