package com.gzairports.wl.charge.domain.vo;


import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 邮件运价条目返回数据
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@Data
public class MailPriceItemVo {

    /** 主键id */
    private Long id;

    /** 所属运价名称 */
    private String rateName;

    /** 运价条目名称 */
    private String clauseName;

    /** 航班号 */
    private String flightNo;

    /** 托运局 */
    private String consignName;

    /** 承运人 */
    private String carrierCode;

    /** 始发城市 */
    private String departureCity;

    /** 目的城市 */
    private String destinationCity;

    /** 结算最低运费 */
    private BigDecimal minimumFreight;

    /** 票面最低运费 */
    private BigDecimal faceMinimumFreight;

    /** 储运注意事项 */
    private String storageShippingNotes;

    /** 结算注意事项 */
    private String settlementNotes;

    /** 备注 */
    private String remarks;

    /** 生效开始时间 */
    private Date startTime;

    /** 生效截止时间 */
    private Date endTime;

    /** 是否生效中 */
    private Integer isEffective;
}
