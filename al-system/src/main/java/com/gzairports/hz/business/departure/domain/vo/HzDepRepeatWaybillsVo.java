package com.gzairports.hz.business.departure.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: lan
 * @Desc: 板车运单返回参数
 * @create: 2024-11-07 11:50
 **/
@Data
public class HzDepRepeatWaybillsVo {
    /**
     * 主单id
     * */
    private Long id;
    /**
     * 运单号
     * */
    private String waybillCode;
    /**
     * 配载件数
     * */
    private Integer quantity;
    /**
     * 配载重量
     * */
    private BigDecimal weight;
    /**
     * 收运过磅重量
     * */
    private BigDecimal weighWeight;
    /**
     * 品名
     * */
    private String cargoName;
    /**
     *目的地
     */
    private String desPort;
    /**
     * 特货代码
     * */
    private String specialCargoCode1;
    /**
     * 收运id
     * */
    private Long collectId;
    /**
     * 板箱号
     * */
    private String uld;
    /**
     * 板箱id
     * */
    private Long loadUldId;


    /** 是否真实收运 */
    private Integer isReal;

    /** 是否冷藏 */
    private String isCold;
}
