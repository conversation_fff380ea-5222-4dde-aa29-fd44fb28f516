package com.gzairports.hz.business.arrival.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 运单明细办单数据
 * <AUTHOR>
 * @date 2024-07-25
 */
@Data
public class BdVo {

    /** 办单时间 */
    private Date pickUpTime;

    /** 提货客户 */
    private String customerName;

    /** 证件类型 */
    private String customerIdType;

    /** 证件号 */
    private String customerIdNo;

    /** 办单件数 */
    private Integer orderQuantity;

    /** 办单重量 */
    private BigDecimal orderWeight;

    /** 费用合计 */
    private BigDecimal costSum;

    /** 操作人 */
    private String handleBy;

    /** 流水号 */
    private String serialNo;

    /** 备注 */
    private String remark;

    /** 理货id */
    private Long tallyId;
}
