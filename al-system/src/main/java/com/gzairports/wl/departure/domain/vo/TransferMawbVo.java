package com.gzairports.wl.departure.domain.vo;

import com.gzairports.common.pdf.PdfPrintAnnotation;
import lombok.Data;

import java.math.BigDecimal;

import static com.gzairports.common.pdf.PdfPrintAnnotation.PdfFieldType.*;

/**
 * 出港货物交接运单参数
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
public class TransferMawbVo {

    /** 主键id */
    private Long id;

    /** 运单号 */
    @PdfPrintAnnotation(pdfFieldType = TEXT,pdfFieldName = "waybillCode")
    private String waybillCode;

    /** 目的站 */
    @PdfPrintAnnotation(pdfFieldType = TEXT,pdfFieldName = "desPort")
    private String desPort;

    /** 预配航班 */
    @PdfPrintAnnotation(pdfFieldType = TEXT,pdfFieldName = "flightNo")
    private String flight;

    /** 货品代码 */
    private String cargoCode;

    private String dangerCode;

    /** 品名 */
    @PdfPrintAnnotation(pdfFieldType = TEXT,pdfFieldName = "cargoName")
    private String cargoName;

    /** 件数 */
    @PdfPrintAnnotation(pdfFieldType = TEXT,pdfFieldName = "quantity")
    private Integer quantity;

    /** 重量 */
    @PdfPrintAnnotation(pdfFieldType = TEXT,pdfFieldName = "weight")
    private BigDecimal weight;

    /** 计费重量 */
    private BigDecimal chargeWeight;

    /** 入库件数 */
    private Integer storeQuantity;

    /** 入库重量 */
    private BigDecimal storeWeight;

    /** 状态 */
    private String status;

}
