package com.gzairports.web.controller.basedata;


import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.common.basedata.domain.BaseSpecialCargoCode;
import com.gzairports.common.basedata.domain.query.SpecialCargoCodeQuery;
import com.gzairports.common.basedata.service.ISpecialCargoCodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 装载特货代码Controller
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@RestController
@RequestMapping("/base/specialCargoCode")
@Api(tags = "装载特货代码数据接口")
public class SpecialCargoCodeController extends BaseController {

    @Autowired
    private ISpecialCargoCodeService specialCargoCodeService;

    /**
     * 查询装载特货代码列表
     */
//    @PreAuthorize("@ss.hasPermi('base:specialCargoCode:specialCargoCodeList')")
    @GetMapping("/specialCargoCodeList")
    @ApiOperation(value = "查询装载特货代码列表")
    public TableDataInfo specialCargoCodeList(SpecialCargoCodeQuery query)
    {
        startPage();
        List<BaseSpecialCargoCode> list = specialCargoCodeService.selectSpecialCargoCodeList(query);
        return getDataTable(list);
    }

    /**
     * 导出装载特货代码
     */
    @PreAuthorize("@ss.hasPermi('base:specialCargoCode:exportSpecialCargoCode')")
    @Log(title = "导出装载特货代码", businessType = BusinessType.EXPORT)
    @PostMapping("/exportSpecialCargoCode")
    @ApiOperation(value = "导出装载特货代码")
    public void exportSpecialCargoCode(HttpServletResponse response, SpecialCargoCodeQuery query)
    {
        List<BaseSpecialCargoCode> list = specialCargoCodeService.selectSpecialCargoCodeList(query);
        ExcelUtil<BaseSpecialCargoCode> util = new ExcelUtil<BaseSpecialCargoCode>(BaseSpecialCargoCode.class);
        util.exportExcel(response, list, "装载特货代码");
    }
}
