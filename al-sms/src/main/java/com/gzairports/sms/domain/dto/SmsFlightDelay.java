package com.gzairports.sms.domain.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 *
 * 航班延误短信对象
 * Created by david on 2023/9/22
 * <AUTHOR>
 */
@Data
public class SmsFlightDelay {

    /***
     * 运单号
     */
    @JSONField(ordinal = 1)
    private String billId;

    /***
     * 航班号
     */
    @JSONField(ordinal = 2)
    private String flightNo;

    /***
     * 计划起飞时间
     */
    @JSONField(ordinal = 3)
    private String startSchemeTakeoffTime;

    /***
     * 延误时长
     */
    @JSONField(ordinal = 4)
    private Long delayTime;
}
