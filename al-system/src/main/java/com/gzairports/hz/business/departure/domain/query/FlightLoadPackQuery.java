package com.gzairports.hz.business.departure.domain.query;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 航班配载装箱和部分装箱参数
 * <AUTHOR>
 * @date 2024-07-01
 */
@Data
public class FlightLoadPackQuery {

    /** 航班配载id */
    private Long flightLoadId;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 类型 区分左右 */
    private String type;

    /** 拉下原因 */
    private String remark;

    /** 运单id集合 */
    private List<Long> flightLoadWaybillIds;

    /** 拉下运单数据 */
    private List<PullDownWaybill> pullDownWaybills;

    /** 拉下板箱数据 */
    private List<PullDownUld> pullDownUlds;

    /** 装配uld集合 */
    private List<Long> flightLoadUldIds;
}
