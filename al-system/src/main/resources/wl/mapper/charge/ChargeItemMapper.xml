<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.wl.charge.mapper.ChargeItemMapper">
    <insert id="insertChargeItem" parameterType="chargeItem" useGeneratedKeys="true" keyProperty="id">
        insert into wl_sf_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="code != null and code != ''">code,</if>
            <if test="operationType != null and operationType != ''">operation_type,</if>
            <if test="applicableDocuments != null">applicable_documents,</if>
            <if test="purpose != null and purpose != ''">purpose,</if>
            <if test="settlementPayee != null and settlementPayee != ''">settlement_payee,</if>
            <if test="abbreviation != null and abbreviation != ''">abbreviation,</if>
            <if test="defaultChargeable != null">default_chargeable,</if>
            <if test="defaultBillingMethod != null">default_billing_method,</if>
            <if test="defaultCostRate != null">default_cost_rate,</if>
            <if test="editable != null">editable,</if>
            <if test="remarks != null and remarks != ''">remarks,</if>
            <if test="deptId != null ">dept_id,</if>
            <if test="roundRule != null ">round_rule,</if>
            <if test="isDel != null">is_del,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="code != null and code != ''">#{code},</if>
            <if test="operationType != null and operationType != ''">#{operationType},</if>
            <if test="applicableDocuments != null">#{applicableDocuments},</if>
            <if test="purpose != null and purpose != ''">#{purpose},</if>
            <if test="settlementPayee != null and settlementPayee != ''">#{settlementPayee},</if>
            <if test="abbreviation != null and abbreviation != ''">#{abbreviation},</if>
            <if test="defaultChargeable != null">#{defaultChargeable},</if>
            <if test="defaultBillingMethod != null">#{defaultBillingMethod},</if>
            <if test="defaultCostRate != null">#{defaultCostRate},</if>
            <if test="editable != null">#{editable},</if>
            <if test="remarks != null and remarks != ''">#{remarks},</if>
            <if test="deptId != null ">#{deptId},</if>
            <if test="roundRule != null ">#{roundRule},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
        </trim>
    </insert>
    <update id="updateChargeItem">
        update wl_sf_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="operationType != null and operationType != ''">operation_type = #{operationType},</if>
            <if test="applicableDocuments != null">applicable_documents = #{applicableDocuments},</if>
            <if test="purpose != null and purpose != ''">purpose = #{purpose},</if>
            <if test="settlementPayee != null and settlementPayee != ''">settlement_payee = #{settlementPayee},</if>
            <if test="abbreviation != null and abbreviation != ''">abbreviation = #{abbreviation},</if>
            <if test="defaultChargeable != null">default_chargeable = #{defaultChargeable},</if>
            <if test="defaultBillingMethod != null">default_billing_method = #{defaultBillingMethod},</if>
            <if test="defaultCostRate != null">default_cost_rate = #{defaultCostRate},</if>
            <if test="editable != null">editable = #{editable},</if>
            <if test="remarks != null and remarks != ''">remarks = #{remarks},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="roundRule != null ">round_rule = #{roundRule},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="removeChargeItem">
        update wl_sf_item set is_del = 1 where id = #{id}
    </update>
    <select id="selectListByQuery" resultType="com.gzairports.wl.charge.domain.vo.ChargeItemVO">
        select wci.id, wci.name, wci.code, wci.operation_type, wci.applicable_documents, wci.purpose, wci.settlement_payee,
               wci.abbreviation, wci.default_chargeable, wci.round_rule, wci.default_billing_method, wci.default_cost_rate, wci.editable,
               wci.remarks, sd.dept_name as dept
        from wl_sf_item wci
        left join sys_dept sd on sd.dept_id = wci.dept_id
        <where>
            wci.is_del = 0 and wci.dept_id = #{deptId}
            <if test="ids != null  and ids.size() > 0"> and wci.id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="name != null  and name != ''"> and wci.name like concat('%',#{name},'%')</if>
            <if test="code != null  and code != ''"> and wci.code like concat('%',#{code},'%')</if>
            <if test="operationType != null and operationType != ''"> and wci.operation_type = #{operationType}</if>
            <if test="applicableDocuments != null and applicableDocuments != ''"> and wci.applicable_documents = #{applicableDocuments}</if>
            <if test="abbreviation != null and abbreviation != ''"> and wci.abbreviation like concat('%',#{abbreviation},'%')</if>
        </where>
    </select>
    <select id="selectChargeById" resultType="com.gzairports.wl.charge.domain.vo.ChargeItemVO">
        select wci.id, wci.name, wci.code, wci.operation_type, wci.applicable_documents, wci.purpose, wci.settlement_payee,
               wci.abbreviation, wci.default_chargeable, wci.round_rule, wci.default_billing_method, wci.default_cost_rate, wci.editable,
               wci.remarks, sd.dept_name as dept
        from wl_sf_item wci
                 left join sys_dept sd on sd.dept_id = wci.dept_id where wci.id = #{id}
    </select>
    <select id="selectByDept" resultType="com.gzairports.wl.charge.domain.ChargeItem">
        select id, name, code, operation_type, applicable_documents, purpose, settlement_payee,
               abbreviation, default_chargeable, round_rule, default_billing_method, default_cost_rate, dept_id
        from wl_sf_item where dept_id = #{deptId}
                          and default_chargeable = 1
                          and applicable_documents = #{type}
                          and operation_type = 'DEP'
                          and purpose = '应收'
                          and is_del = 0
    </select>
</mapper>