<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.BaseDutyScheduleMapper">
    
    <resultMap type="BaseDutySchedule" id="HzDutyScheduleResult">
        <result property="id"    column="id"    />
        <result property="companyName"    column="company_name"    />
        <result property="companyPhone"    column="company_phone"    />
        <result property="dutyDuration"    column="duty_duration"    />
        <result property="dutyTime"    column="duty_time"    />
        <result property="dutyContent"    column="duty_content"    />
        <result property="primaryDutyName"    column="primary_duty_name"    />
        <result property="primaryDutyPhone"    column="primary_duty_phone"    />
        <result property="secondaryDutyName"    column="secondary_duty_name"    />
        <result property="secondaryDutyPhone"    column="secondary_duty_phone"    />
        <result property="domesticCargoDutyName"    column="domestic_cargo_duty_name"    />
        <result property="domesticCargoDutyPhone"    column="domestic_cargo_duty_phone"    />
        <result property="internationalCargoDutyName"    column="international_cargo_duty_name"    />
        <result property="internationalCargoDutyPhone"    column="international_cargo_duty_phone"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectHzDutyScheduleVo">
        select id, company_name, company_phone, duty_duration, duty_time, duty_content, primary_duty_name, primary_duty_phone, secondary_duty_name, secondary_duty_phone, domestic_cargo_duty_name, domestic_cargo_duty_phone, international_cargo_duty_name, international_cargo_duty_phone, create_time, create_by, update_time, update_by from hz_duty_schedule
    </sql>

    <select id="selectHzDutyScheduleList" parameterType="BaseDutySchedule" resultMap="HzDutyScheduleResult">
        select id, company_name, company_phone, duty_duration, duty_time, duty_content, primary_duty_name, primary_duty_phone, secondary_duty_name, secondary_duty_phone, domestic_cargo_duty_name, domestic_cargo_duty_phone, international_cargo_duty_name, international_cargo_duty_phone, create_time, create_by, update_time, update_by from hz_duty_schedule

        <where> is_del = 0
            <if test="id != null "> and id = #{id}</if>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="dutyDuration != null  and dutyDuration != ''"> and duty_duration = #{dutyDuration}</if>
            <if test="companyPhone != null  and companyPhone != ''"> and company_phone = #{companyPhone}</if>
            <if test="dutyTime != null"> and duty_time = #{dutyTime}</if>
            <if test="dutyContent != null  and dutyContent != ''"> and duty_content like concat('%', #{dutyContent}, '%')</if>
            <if test="primaryDutyName != null  and primaryDutyName != ''"> and primary_duty_name like concat('%', #{primaryDutyName}, '%')</if>
            <if test="primaryDutyPhone != null  and primaryDutyPhone != ''"> and primary_duty_phone = #{primaryDutyPhone}</if>
            <if test="secondaryDutyName != null  and secondaryDutyName != ''"> and secondary_duty_name like concat('%', #{secondaryDutyName}, '%')</if>
            <if test="secondaryDutyPhone != null  and secondaryDutyPhone != ''"> and secondary_duty_phone = #{secondaryDutyPhone}</if>
            <if test="domesticCargoDutyName != null  and domesticCargoDutyName != ''"> and domestic_cargo_duty_name like concat('%', #{domesticCargoDutyName}, '%')</if>
            <if test="domesticCargoDutyPhone != null  and domesticCargoDutyPhone != ''"> and domestic_cargo_duty_phone = #{domesticCargoDutyPhone}</if>
            <if test="internationalCargoDutyName != null  and internationalCargoDutyName != ''"> and international_cargo_duty_name like concat('%', #{internationalCargoDutyName}, '%')</if>
            <if test="internationalCargoDutyPhone != null  and internationalCargoDutyPhone != ''"> and international_cargo_duty_phone = #{internationalCargoDutyPhone}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
            <if test="createBy != null "> and create_by = #{createBy}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
            <if test="updateBy != null "> and update_by = #{updateBy}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectHzDutyScheduleById" parameterType="Long" resultMap="HzDutyScheduleResult">
        <include refid="selectHzDutyScheduleVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertHzDutySchedule" parameterType="BaseDutySchedule" useGeneratedKeys="true" keyProperty="id">
        insert into hz_duty_schedule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyName != null">company_name,</if>
            <if test="companyPhone != null">company_phone,</if>
            <if test="dutyDuration != null">duty_duration,</if>
            <if test="dutyTime != null">duty_time,</if>
            <if test="dutyContent != null">duty_content,</if>
            <if test="primaryDutyName != null">primary_duty_name,</if>
            <if test="primaryDutyPhone != null">primary_duty_phone,</if>
            <if test="secondaryDutyName != null">secondary_duty_name,</if>
            <if test="secondaryDutyPhone != null">secondary_duty_phone,</if>
            <if test="domesticCargoDutyName != null">domestic_cargo_duty_name,</if>
            <if test="domesticCargoDutyPhone != null">domestic_cargo_duty_phone,</if>
            <if test="internationalCargoDutyName != null">international_cargo_duty_name,</if>
            <if test="internationalCargoDutyPhone != null">international_cargo_duty_phone,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyName != null">#{companyName},</if>
            <if test="companyPhone != null">#{companyPhone},</if>
            <if test="dutyDuration != null">#{dutyDuration},</if>
            <if test="dutyTime != null">#{dutyTime},</if>
            <if test="dutyContent != null">#{dutyContent},</if>
            <if test="primaryDutyName != null">#{primaryDutyName},</if>
            <if test="primaryDutyPhone != null">#{primaryDutyPhone},</if>
            <if test="secondaryDutyName != null">#{secondaryDutyName},</if>
            <if test="secondaryDutyPhone != null">#{secondaryDutyPhone},</if>
            <if test="domesticCargoDutyName != null">#{domesticCargoDutyName},</if>
            <if test="domesticCargoDutyPhone != null">#{domesticCargoDutyPhone},</if>
            <if test="internationalCargoDutyName != null">#{internationalCargoDutyName},</if>
            <if test="internationalCargoDutyPhone != null">#{internationalCargoDutyPhone},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateHzDutySchedule" parameterType="BaseDutySchedule">
        update hz_duty_schedule
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="dutyDuration != null  and dutyDuration != ''">duty_duration = #{dutyDuration},</if>
            <if test="companyPhone != null  and companyPhone != ''">company_phone = #{companyPhone},</if>
            <if test="dutyTime != null">duty_time = #{dutyTime},</if>
            <if test="dutyContent != null">duty_content = #{dutyContent},</if>
            <if test="primaryDutyName != null">primary_duty_name = #{primaryDutyName},</if>
            <if test="primaryDutyPhone != null">primary_duty_phone = #{primaryDutyPhone},</if>
            <if test="secondaryDutyName != null">secondary_duty_name = #{secondaryDutyName},</if>
            <if test="secondaryDutyPhone != null">secondary_duty_phone = #{secondaryDutyPhone},</if>
            <if test="domesticCargoDutyName != null">domestic_cargo_duty_name = #{domesticCargoDutyName},</if>
            <if test="domesticCargoDutyPhone != null">domestic_cargo_duty_phone = #{domesticCargoDutyPhone},</if>
            <if test="internationalCargoDutyName != null">international_cargo_duty_name = #{internationalCargoDutyName},</if>
            <if test="internationalCargoDutyPhone != null">international_cargo_duty_phone = #{internationalCargoDutyPhone},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHzDutyScheduleById" parameterType="Long">
        delete from hz_duty_schedule where id = #{id}
    </delete>

    <delete id="deleteHzDutyScheduleByIds" parameterType="String">
        delete from hz_duty_schedule where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>