package com.gzairports.hz.business.transfer.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.gzairports.common.basedata.domain.BaseCargoCode;
import com.gzairports.common.basedata.domain.BaseCarrier;
import com.gzairports.common.basedata.mapper.CargoCodeMapper;
import com.gzairports.common.basedata.mapper.CarrierMapper;
import com.gzairports.common.business.arrival.domain.HzArrTally;
import com.gzairports.common.business.arrival.mapper.FlightLoadWaybillMapper;
import com.gzairports.common.business.arrival.mapper.HzArrTallyMapper;
import com.gzairports.common.business.departure.domain.CostDetail;
import com.gzairports.common.business.departure.domain.WaybillTrace;
import com.gzairports.common.business.departure.domain.vo.LoadInfoVo;
import com.gzairports.common.business.departure.mapper.CostDetailMapper;
import com.gzairports.common.business.departure.service.impl.WaybillTraceServiceImpl;
import com.gzairports.common.charge.domain.HzChargeIrRelation;
import com.gzairports.common.charge.domain.HzChargeItemRule;
import com.gzairports.common.charge.domain.HzChargeItems;
import com.gzairports.common.charge.domain.HzChargeRule;
import com.gzairports.common.charge.domain.vo.BillRuleVo;
import com.gzairports.common.charge.domain.vo.IrRelationVo;
import com.gzairports.common.charge.mapper.HzChargeIrRelationMapper;
import com.gzairports.common.charge.mapper.HzChargeItemRuleMapper;
import com.gzairports.common.charge.mapper.HzChargeItemsMapper;
import com.gzairports.common.charge.mapper.HzChargeRuleMapper;
import com.gzairports.common.charge.service.BillingRule;
import com.gzairports.common.charge.service.impl.BillingRuleFactory;
import com.gzairports.common.core.domain.entity.SysDept;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.common.log.service.IWaybillLogService;
import com.gzairports.common.message.domain.Message;
import com.gzairports.common.message.domain.vo.SocketMessageVo;
import com.gzairports.common.message.mapper.MessageMapper;
import com.gzairports.common.system.domain.SysConfig;
import com.gzairports.common.system.mapper.SysConfigMapper;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.utils.*;
import com.gzairports.hz.business.arrival.domain.vo.EnterWaybillVo;
import com.gzairports.hz.business.arrival.mapper.HzArrRecordOrderMapper;
import com.gzairports.hz.business.cable.domain.HzCable;
import com.gzairports.hz.business.cable.domain.HzCableAddress;
import com.gzairports.hz.business.cable.domain.vo.FSUJsonVO;
import com.gzairports.hz.business.cable.domain.vo.ForwardOriginMsgVO;
import com.gzairports.hz.business.cable.domain.vo.MsgJsonVO;
import com.gzairports.hz.business.cable.domain.vo.StatusDetails;
import com.gzairports.hz.business.cable.mapper.HzCableAddressMapper;
import com.gzairports.hz.business.cable.mapper.HzCableMapper;
import com.gzairports.hz.business.cable.service.impl.HttpServiceImpl;
import com.gzairports.hz.business.departure.domain.AirWaybill;
import com.gzairports.hz.business.departure.domain.HzCollectWaybill;
import com.gzairports.hz.business.departure.domain.HzCollectWeight;
import com.gzairports.hz.business.departure.domain.vo.WaybillInfoVo;
import com.gzairports.hz.business.departure.mapper.AllAirWaybillMapper;
import com.gzairports.hz.business.departure.mapper.HzCollectWaybillMapper;
import com.gzairports.hz.business.departure.mapper.HzCollectWeightMapper;
import com.gzairports.hz.business.departure.rabbitmq.WaybillMessageProducer;
import com.gzairports.hz.business.departure.service.IHzCollectWaybillService;
import com.gzairports.hz.business.transfer.domain.HzTransferHandover;
import com.gzairports.hz.business.transfer.domain.HzTransferHandoverWaybill;
import com.gzairports.hz.business.transfer.domain.query.FileAndCargoTransferQuery;
import com.gzairports.hz.business.transfer.domain.query.TransferPickQuery;
import com.gzairports.hz.business.transfer.domain.vo.*;
import com.gzairports.hz.business.transfer.mapper.HzTransferHandoverMapper;
import com.gzairports.hz.business.transfer.mapper.HzTransferHandoverWaybillMapper;
import com.gzairports.hz.business.transfer.service.IHzTransferHandoverService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Marker;
import org.slf4j.MarkerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 中转交接Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-27
 */
@Slf4j
@Service
public class HzTransferHandoverServiceImpl extends ServiceImpl<HzTransferHandoverMapper, HzTransferHandover> implements IHzTransferHandoverService
{
    @Autowired
    private HzTransferHandoverMapper hzTransferHandoverMapper;

    @Autowired
    private AllAirWaybillMapper airWaybillMapper;

    @Autowired
    private HzArrRecordOrderMapper recordOrderMapper;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    @Autowired
    private HzArrTallyMapper tallyMapper;

    @Autowired
    private HzTransferHandoverWaybillMapper handoverWaybillMapper;

    @Autowired
    private IWaybillLogService waybillLogService;

    @Autowired
    private MessageMapper messageMapper;

    @Autowired
    private WaybillMessageProducer waybillMessageProducer;

    @Autowired
    private CostDetailMapper costDetailMapper;

    @Autowired
    private HzChargeItemsMapper itemsMapper;

    @Autowired
    private SysConfigMapper sysConfigMapper;

    @Autowired
    private HzChargeIrRelationMapper relationMapper;

    @Autowired
    private HzChargeItemRuleMapper itemRuleMapper;

    @Autowired
    private HzChargeRuleMapper ruleMapper;

    @Autowired
    private CargoCodeMapper cargoCodeMapper;

    @Autowired
    private CarrierMapper carrierMapper;

    @Autowired
    private HzCableMapper hzCableMapper;

    @Autowired
    private HzCableAddressMapper cableAddressMapper;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private HttpServiceImpl httpService;

    @Autowired
    private HzCollectWaybillMapper collectWaybillMapper;

    @Autowired
    private HzCollectWeightMapper collectWeightMapper;

    @Autowired
    private WaybillTraceServiceImpl waybillTraceService;

    @Autowired
    private IHzCollectWaybillService collectWaybillService;

    @Autowired
    private FlightLoadWaybillMapper loadWaybillMapper;

    @Value("${hzCable.account}")
    private String account;

    @Value("${hzCable.loginUrl}")
    private String loginUrl;

    @Value("${hzCable.getMsg}")
    private String getMsg;

    @Value("${hzCable.FTPAddress}")
    private String ftpAddress;

    private static final Marker CABLE_ERROR_MARKER  = MarkerFactory.getMarker("CABLE-ERROR");

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMdd");
    private static final SimpleDateFormat FORMAT = new SimpleDateFormat("yyyy-MM-dd");
    private static final SimpleDateFormat TIME_FORMAT = new SimpleDateFormat("HH:mm");

    /**
     * 查询中转交接
     * 
     * @param id 中转交接ID
     * @return 中转交接
     */
    @Override
    public TransferHandoverInfoVo selectHzTransferHandoverById(Long id)
    {
        TransferHandoverInfoVo vo = hzTransferHandoverMapper.selectHzTransferHandoverById(id);
        selectInfo(vo);
        return vo;
    }

    /**
     * 查询中转交接列表
     * 
     * @param hzTransferHandover 中转交接
     * @return 中转交接
     */
    @Override
    public List<HzTransferHandover> selectHzTransferHandoverList(HzTransferHandover hzTransferHandover)
    {
        List<HzTransferHandover> hzTransferHandovers = hzTransferHandoverMapper.selectHzTransferHandoverList(hzTransferHandover);

        if(StringUtils.isNotEmpty(hzTransferHandover.getWaybillCode())){
            hzTransferHandovers.clear();
            AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                    .eq("waybill_code", hzTransferHandover.getWaybillCode())
                    .eq("type", "ARR")
                    .eq("transfer_bill", 1));
            if(airWaybill != null){
                HzTransferHandoverWaybill transferHandoverWaybill = handoverWaybillMapper.selectOne(new QueryWrapper<HzTransferHandoverWaybill>()
                        .eq("waybill_id", airWaybill.getId())
                        .last("limit 1"));
                if(transferHandoverWaybill != null){
                    HzTransferHandover hzTransferHandoverById = hzTransferHandoverMapper.selectById(transferHandoverWaybill.getTransferId());
                    hzTransferHandovers.add(hzTransferHandoverById);
                }
            }
        }

        if(!CollectionUtils.isEmpty(hzTransferHandovers)){
            for (HzTransferHandover transferHandover : hzTransferHandovers) {
                String out = transferHandover.getOutStore() + "-" + transferHandover.getOutLocator();
                transferHandover.setOut(out);
                String in = transferHandover.getInStore() + "-" + transferHandover.getInLocator();
                transferHandover.setIn(in);
            }
        }
        return hzTransferHandovers;
    }

    /**
     * 保存中转交接
     * 
     * @param vo 中转交接数据
     * @return 结果
     */
    @Override
    public TransferHandoverVo insertHzTransferHandover(TransferHandoverVo vo) {
        HzTransferHandover hzTransferHandover = new HzTransferHandover();
        BeanUtils.copyProperties(vo,hzTransferHandover);
        if (vo.getId() != null){
            handoverWaybillMapper.delete(new QueryWrapper<HzTransferHandoverWaybill>().eq("transfer_id", vo.getId()));
            for (Long waybillId : vo.getWaybillIds()) {
                HzTransferHandoverWaybill handoverWaybill = new HzTransferHandoverWaybill();
                handoverWaybill.setWaybillId(waybillId);
                handoverWaybill.setTransferId(vo.getId());
                handoverWaybillMapper.insert(handoverWaybill);
            }
            hzTransferHandoverMapper.updateById(hzTransferHandover);
            return vo;
        }else {
            String code = SerialNumberGenerator.generateSerialNumber();
            Date date = new Date();
            vo.setHandoverNo(code);
            vo.setCreateTime(date);
            hzTransferHandover.setHandoverNo(code);
            hzTransferHandover.setCreateTime(date);
            hzTransferHandoverMapper.insert(hzTransferHandover);
            vo.setId(hzTransferHandover.getId());
            for (Long waybillId : vo.getWaybillIds()) {
                HzTransferHandoverWaybill handoverWaybill = new HzTransferHandoverWaybill();
                handoverWaybill.setWaybillId(waybillId);
                handoverWaybill.setTransferId(hzTransferHandover.getId());
                handoverWaybillMapper.insert(handoverWaybill);
            }
            return vo;
        }
    }

    /**
     *  中转交接挑单
     * @param waybillCode 运单号
     * @return 挑单数据
     */
    @Override
    public TransferPickVo pickOne(String waybillCode) {
        TransferPickVo vo = airWaybillMapper.pickOne(waybillCode);
        if (vo == null){
            throw new CustomException("该运费非中转运单");
        }
        HzArrTally hzArrTally = tallyMapper.selectOne(new QueryWrapper<HzArrTally>().eq("waybill_code", waybillCode)
                .last("limit 1"));
        if(hzArrTally!=null){
            if(hzArrTally.getStore()!=null){
                vo.setStore(hzArrTally.getStore());
            }
            if(hzArrTally.getLocator()!=null){
                vo.setLocator(hzArrTally.getLocator());
            }
        }
        List<HzArrTally> tallyList = tallyMapper.selectList(new QueryWrapper<HzArrTally>().eq("waybill_code",vo.getWaybillCode()));
        if (!CollectionUtils.isEmpty(tallyList)){
            int sum = tallyList.stream().mapToInt(HzArrTally::getPieces).sum();
            vo.setTallyQuantity(sum);
            BigDecimal reduce = tallyList.stream().map(HzArrTally::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setTallyWeight(reduce);
        }

        List<String> list = recordOrderMapper.selectListByCode(vo.getWaybillCode());
        if (!CollectionUtils.isEmpty(list)) {
            String flightNo = String.join(",", list);
            vo.setFlightNo(flightNo);
        }
        List<HzTransferHandoverWaybill> waybills = handoverWaybillMapper.selectList(new QueryWrapper<HzTransferHandoverWaybill>().eq("waybill_id", vo.getWaybillId()));
        if (!CollectionUtils.isEmpty(waybills)){
            throw new CustomException("当前运单已存在交接单");
        }
        return vo;
    }

    /**
     *  中转交接批量挑单
     * @param query 查询参数
     * @return 批量挑单数据
     */
    @Override
    public List<TransferPickVo> batchPick(TransferPickQuery query) {
        List<TransferPickVo> list = airWaybillMapper.batchPick(query);
        if (!CollectionUtils.isEmpty(list)){
            List<Long> waybillIds = handoverWaybillMapper.selectIds();
            if (!CollectionUtils.isEmpty(waybillIds)){
                return list.stream().filter(e -> waybillIds.stream().noneMatch(id -> Objects.equals(e.getWaybillId(),id))).collect(Collectors.toList());
            }
        }
        return list;
    }

    /**
     *  删除交接单
     * @param id 交接单id
     * @return 结果
     */
    @Override
    public int delete(Long id) {
        HzTransferHandover hzTransferHandover = hzTransferHandoverMapper.selectById(id);
        if (hzTransferHandover == null){
            throw new CustomException("当前交接单不存在");
        }
        if (hzTransferHandover.getFileStatus().equals(1) || hzTransferHandover.getCargoStatus().equals(1)){
            throw new CustomException("文件和货物都未交接时才可删除");
        }
        handoverWaybillMapper.delete(new QueryWrapper<HzTransferHandoverWaybill>().eq("transfer_id",id));
        return hzTransferHandoverMapper.deleteById(id);
    }

    /**
     * 根据流水号查询交接单详情
     * @param handoverNo 交接单流水号
     * @return 详情数据
     */
    @Override
    public TransferHandoverInfoVo getInfoByNo(String handoverNo) {
        TransferHandoverInfoVo vo = hzTransferHandoverMapper.selectOneByNo(handoverNo);
        selectInfo(vo);
        return vo;
    }

    /**
     * 文件交接
     * @param id 交接单id
     * @return 数据
     */
    @Override
    public FileAndCargoTransferVo fileTransfer(Long id) {
        FileAndCargoTransferVo vo = hzTransferHandoverMapper.selectFileAndCargoInfoById(id);
        selectFileAndCargoInfo(vo);
        return vo;
    }

    /**
     * 根据条件查询文件或货物交接单详情
     * @param query 查询条件
     * @return 交接单详情
     */
    @Override
    public FileAndCargoTransferVo getInfoByQuery(FileAndCargoTransferQuery query) {
        if((query.getHandoverNo() == null || query.getHandoverNo().equals("")) && StringUtils.isNull(query.getStartTime())
        && StringUtils.isNull(query.getEndTime())){
            return null;
        }
        FileAndCargoTransferVo vo = hzTransferHandoverMapper.getInfoByQuery(query);
        selectFileAndCargoInfo(vo);
        return vo;
    }

    /**
     * 文件交接
     * @param vo 文件交接数据
     * @return 结果
     */
    @Override
    public int fileHandover(FileAndCargoHandoverVo vo) {
        HzTransferHandover hzTransferHandover = getHzTransferHandover(vo);
        if(hzTransferHandover.getFileStatus() == 1){
            throw new CustomException("文件已交接");
        }
        if (hzTransferHandover.getFileStatus() == 2) {
            throw new CustomException("文件已接收");
        }

        HttpServletResponse response = ServletUtils.getResponse();
        ArrayList<WaybillLog> waybillLogs = new ArrayList<>();

        //运单日志的新增
        List<HzTransferHandoverWaybill> waybills = handoverWaybillMapper.selectList(new QueryWrapper<HzTransferHandoverWaybill>()
                .eq("transfer_id", vo.getId()));
        for (HzTransferHandoverWaybill waybill:waybills) {
            AirWaybill airWaybill = airWaybillMapper.selectById(waybill.getWaybillId());
            //发送消息
            sendMessage(airWaybill.getWaybillCode());
            WaybillLog waybillLog = waybillLogService.getWaybillLog(
                    airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                    airWaybill.getWeight().toString(), airWaybill.getQuantity().toString(), airWaybill.getFlightNo1(),
                    airWaybill, null, 0, null, new Date(),
                    "中转文件交接", airWaybill.getType(), null);
            waybillLogs.add(waybillLog);
        }

        try {
            hzTransferHandover.setFileOutTime(new Date());
            hzTransferHandover.setFileStatus(1);
            int i = hzTransferHandoverMapper.updateById(hzTransferHandover);
            for (WaybillLog waybillLog:waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作成功" +  "," +
                                "code:" + response.getStatus() + "," +
                                "data:" + i));
            }
            return i;
        } catch (Exception e) {
            for (WaybillLog waybillLog:waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作失败" +  "," +
                                "code:" + response.getStatus()));
                waybillLog.setErrorMsg(e.getMessage());
                waybillLog.setStatus(1);
            }
            throw new CustomException(e.getMessage());
        } finally {
            for (WaybillLog waybillLog:waybillLogs) {
                waybillLogService.insertWaybillLog(waybillLog);
            }
        }
    }

    /**
     * 系统自动做运单收运
     *
     * @param airWaybill
     */
    private void collectWaybill(AirWaybill airWaybill) {
        HzCollectWaybill hzCollectWaybill = new HzCollectWaybill();
        // 新增收运数据
        hzCollectWaybill.setQuantity(airWaybill.getQuantity());
        hzCollectWaybill.setWeight(airWaybill.getWeight());
        hzCollectWaybill.setWaybillId(airWaybill.getId());
        hzCollectWaybill.setWaybillCode(airWaybill.getWaybillCode());
        hzCollectWaybill.setCollectTime(new Date());
//            waybill.setOperName(SecurityUtils.getUsername());
        hzCollectWaybill.setOperName("系统");
        hzCollectWaybill.setCollectTime(new Date());
        hzCollectWaybill.setIsReal(1);
        hzCollectWaybill.setStatus("REAL");
        collectWaybillMapper.insert(hzCollectWaybill);
        HzCollectWeight weight = new HzCollectWeight();
        weight.setCollectId(hzCollectWaybill.getId());
        weight.setWeightTime(new Date());
        weight.setDesPort(airWaybill.getDesPort());
        weight.setTotalWeight(hzCollectWaybill.getWeight());
        weight.setQuantity(hzCollectWaybill.getQuantity());
        weight.setWeight(hzCollectWaybill.getWeight());
        collectWeightMapper.insert(weight);
        // 运单跟踪数据
        WaybillTrace waybillTrace = new WaybillTrace();
        waybillTrace.setOperTime(new Date());
        waybillTrace.setOperPieces(airWaybill.getQuantity());
        waybillTrace.setOperWeight(airWaybill.getWeight());
        waybillTrace.setWaybillCode(airWaybill.getWaybillCode());
        waybillTrace.setNodeName("货站入库");
        waybillTrace.setChargeWeight(airWaybill.getChargeWeight());
        waybillTraceService.insertWaybillTrace(waybillTrace);
        //修改运单状态
        LambdaUpdateWrapper<AirWaybill> putInLuw = Wrappers.<AirWaybill>lambdaUpdate()
                .eq(AirWaybill::getId, airWaybill.getId())
//                .set(AirWaybill::getPayStatus, 1)
                .set(AirWaybill::getStatus, "put_in");
        airWaybillMapper.update(null, putInLuw);
        log.info("自动收运完成");
    }


    @Async
    public void sendMessage(String waybillCode) {
        String message = "    中转运单"+waybillCode+"文件接收  \n" +
                "收到中转运单："+ waybillCode +"文件交接申请，请及时处理";
        SocketMessageVo vo = new SocketMessageVo();
        vo.setMessage(message);
        vo.setType(4);


        Message mes = new Message();
        mes.setContent(message);
        mes.setCreateTime(new Date());
        mes.setIsHandle(0);
        mes.setPostId(SecurityUtils.getDeptId());
        mes.setTitle("中转文件交接");
        messageMapper.insert(mes);
        waybillMessageProducer.send(vo);
    }

    /**
     * 获取交接单数据设置交接单数据
     * @param vo 交接单交接数据
     * @return 交接单数据
     */
    @NotNull
    private HzTransferHandover getHzTransferHandover(FileAndCargoHandoverVo vo) {
        HzTransferHandover hzTransferHandover = hzTransferHandoverMapper.selectById(vo.getId());
        hzTransferHandover.setOutName(vo.getOutName());
        hzTransferHandover.setOutStore(vo.getOutStore());
        hzTransferHandover.setOutLocator(vo.getOutLocator());
        hzTransferHandover.setInStore(vo.getInStore());
        hzTransferHandover.setInLocator(vo.getInLocator());
        hzTransferHandover.setRemark(vo.getRemark());
        return hzTransferHandover;
    }

    /**
     * 取消文件交接
     * @param id 交接单id
     * @return 结果
     */
    @Override
    public int cancelFileHandover(Long id) {
        HzTransferHandover hzTransferHandover = hzTransferHandoverMapper.selectById(id);
        if (hzTransferHandover.getFileStatus().equals(0)) {
            throw new CustomException("文件未交接");
        }

        LambdaQueryWrapper<HzTransferHandoverWaybill> lqw = Wrappers.lambdaQuery();
        lqw.eq(HzTransferHandoverWaybill::getTransferId, id);
        lqw.last("limit 1");
        HzTransferHandoverWaybill waybill = handoverWaybillMapper.selectOne(lqw);

        AirWaybill arrWaybill = airWaybillMapper.selectById(waybill.getWaybillId());

        //查询中转单对应出港运单
        LambdaQueryWrapper<AirWaybill> depLqw = Wrappers.lambdaQuery();
        depLqw.eq(AirWaybill::getWaybillCode, arrWaybill.getWaybillCode());
        depLqw.eq(AirWaybill::getType, "DEP");
        AirWaybill depWaybill = airWaybillMapper.selectOne(depLqw);

        //配载后不可取消
        List<LoadInfoVo> infoVos = loadWaybillMapper.selectByLoadInfo(depWaybill.getId());
        if (!CollectionUtils.isEmpty(infoVos)) {
            throw new CustomException("当前运单已配载不能取消交接");
        }

        if (hzTransferHandover.getFileStatus().equals(2)) {
            //取消收运
            WaybillInfoVo waybillInfoVo = collectWaybillService.getInfo(depWaybill.getWaybillCode());
            collectWaybillService.cancelWaybill(waybillInfoVo);
        }

        hzTransferHandover.setFileStatus(0);
        hzTransferHandover.setFileOutTime(null);
        int i = hzTransferHandoverMapper.updateById(hzTransferHandover);
        if (SqlHelper.retBool(i)) {
            WaybillLog waybillLog = waybillLogService.getWaybillLog(
                    depWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                    depWaybill.getWeight().toString(), depWaybill.getQuantity().toString(), depWaybill.getFlightNo1(),
                    depWaybill, null, 0, null, new Date(),
                    "取消文件交接", depWaybill.getType(), null);
            waybillLogService.insertWaybillLog(waybillLog);
        }
        return i;
    }

    /**
     * 文件接收
     * @param id 交接单id
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public int fileReceive(Long id) {
        HzTransferHandover hzTransferHandover = hzTransferHandoverMapper.selectById(id);
        Integer fileStatus = hzTransferHandover.getFileStatus();
        // 检查交接单状态
        if(fileStatus.equals(0)){
            throw new CustomException("文件未交接");
        }
        if(fileStatus.equals(2)){
            throw new CustomException("文件已接收");
        }

        HttpServletResponse response = ServletUtils.getResponse();
        ArrayList<WaybillLog> waybillLogs = new ArrayList<>();

        //运单日志的新增
        List<HzTransferHandoverWaybill> waybills = handoverWaybillMapper.selectList(new QueryWrapper<HzTransferHandoverWaybill>()
                .eq("transfer_id", id));
        for (HzTransferHandoverWaybill waybill : waybills) {
            AirWaybill arrWaybill = airWaybillMapper.selectById(waybill.getWaybillId());
            //新增的出港运单
            AirWaybill airWaybill = getAirWaybill(arrWaybill);

            String sendAddress = sysConfigMapper.selectValue("arr.sendAddress");
            String token = getToken();
            MsgJsonVO msgJsonVO = setMsgVo(arrWaybill, sendAddress);
            try {
                setTFDMsg(token,msgJsonVO,arrWaybill);
            }catch (Exception e){
                log.error(CABLE_ERROR_MARKER, "运单"+airWaybill.getWaybillCode()+"发送FSU-TFD报文失败：" + e.getMessage());
            }
            if(ObjectUtil.isNotNull(airWaybill)){
                WaybillLog waybillLog = waybillLogService.getWaybillLog(
                        airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                        airWaybill.getWeight().toString(), airWaybill.getQuantity().toString(), airWaybill.getFlightNo1(),
                        airWaybill, null, 0, null, new Date(),
                        "中转文件接收", airWaybill.getType(), null);
                waybillLogs.add(waybillLog);

                if(fileStatus == 1){
                    // 所有通单中转运单 在文件交接完成后，就默认收运，在待运导入就能查询到，但是必须支付了才能配
                    collectWaybill(airWaybill);
                }
            }
        }
        try {
            hzTransferHandover.setFileBy(SecurityUtils.getNickName());
            hzTransferHandover.setFileInTime(new Date());
            hzTransferHandover.setFileStatus(2);
            int i = hzTransferHandoverMapper.updateById(hzTransferHandover);
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作成功" + "," +
                                "code:" + response.getStatus() + "," +
                                "data:" + i));
            }
            return i;
        } catch (Exception e) {
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作失败" + "," +
                                "code:" + response.getStatus()));
                waybillLog.setErrorMsg(e.getMessage());
                waybillLog.setStatus(1);
            }
            throw new CustomException(e.getMessage());
        } finally {
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLogService.insertWaybillLog(waybillLog);
            }
        }
    }

    /**
     * 货物交接
     * @param vo 文件交接数据
     * @return 结果
     */
    @Override
    public int cargoHandover(FileAndCargoHandoverVo vo) {
        HzTransferHandover hzTransferHandover = getHzTransferHandover(vo);
        if(hzTransferHandover.getCargoStatus().equals(1)){
            throw new CustomException("货物已交接");
        }
        if (hzTransferHandover.getCargoStatus().equals(2)) {
            throw new CustomException("货物已接收");
        }


        HttpServletResponse response = ServletUtils.getResponse();
        ArrayList<WaybillLog> waybillLogs = new ArrayList<>();

        //运单日志的新增
        List<HzTransferHandoverWaybill> waybills = handoverWaybillMapper.selectList(new QueryWrapper<HzTransferHandoverWaybill>()
                .eq("transfer_id", vo.getId()));
        for (HzTransferHandoverWaybill waybill : waybills) {
            AirWaybill airWaybill = airWaybillMapper.selectById(waybill.getWaybillId());

            //发送消息
            sendMessageForHandover(airWaybill.getWaybillCode());

            WaybillLog waybillLog = waybillLogService.getWaybillLog(
                    airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                    airWaybill.getWeight().toString(), airWaybill.getQuantity().toString(), airWaybill.getFlightNo1(),
                    airWaybill, null, 0, null, new Date(),
                    "中转货物交接", airWaybill.getType(), null);
            waybillLogs.add(waybillLog);
        }
        try {
            hzTransferHandover.setCargoOutTime(new Date());
            hzTransferHandover.setCargoStatus(1);
            int i = hzTransferHandoverMapper.updateById(hzTransferHandover);

            for (WaybillLog waybillLog : waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作成功" + "," +
                                "code:" + response.getStatus() + "," +
                                "data:" + i));
            }
            return i;
        } catch (Exception e) {
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作失败" + "," +
                                "code:" + response.getStatus()));
                waybillLog.setErrorMsg(e.getMessage());
                waybillLog.setStatus(1);
            }
            throw new CustomException(e.getMessage());
        } finally {
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLogService.insertWaybillLog(waybillLog);
            }
        }
    }

    @Async
    public void sendMessageForHandover(String waybillCode) {
        String message = "    中转运单"+waybillCode+"接收  \n" +
                "收到中转运单："+ waybillCode +"货物交接申请，请及时处理";
        SocketMessageVo vo = new SocketMessageVo();
        vo.setMessage(message);
        vo.setType(5);


        Message mes = new Message();
        mes.setContent(message);
        mes.setCreateTime(new Date());
        mes.setIsHandle(0);
        mes.setPostId(SecurityUtils.getDeptId());
        mes.setTitle("中转货物交接");
        messageMapper.insert(mes);
        waybillMessageProducer.send(vo);
    }

    /**
     * 取消货物交接
     * @param id 交接单id
     * @return 结果
     */
    @Override
    public int cancelCargoHandover(Long id) {
        HzTransferHandover hzTransferHandover = hzTransferHandoverMapper.selectById(id);
        if(hzTransferHandover.getCargoStatus().equals(0)){
            throw new CustomException("货物未交接");
        }
        LambdaQueryWrapper<HzTransferHandoverWaybill> lqw = Wrappers.lambdaQuery();
        lqw.eq(HzTransferHandoverWaybill::getTransferId, id);
        lqw.last("limit 1");
        HzTransferHandoverWaybill waybill = handoverWaybillMapper.selectOne(lqw);

        AirWaybill arrWaybill = airWaybillMapper.selectById(waybill.getWaybillId());

        //查询中转单对应出港运单
        LambdaQueryWrapper<AirWaybill> depLqw = Wrappers.lambdaQuery();
        depLqw.eq(AirWaybill::getWaybillCode, arrWaybill.getWaybillCode());
        depLqw.eq(AirWaybill::getType, "DEP");
        AirWaybill depWaybill = airWaybillMapper.selectOne(depLqw);

        //配载后不可取消
        List<LoadInfoVo> infoVos = loadWaybillMapper.selectByLoadInfo(depWaybill.getId());
        if (!CollectionUtils.isEmpty(infoVos)) {
            throw new CustomException("当前运单已配载不能取消交接");
        }

        hzTransferHandover.setCargoStatus(0);
        hzTransferHandover.setCargoOutTime(null);
        int i = hzTransferHandoverMapper.updateById(hzTransferHandover);
        if (SqlHelper.retBool(i)) {
            WaybillLog waybillLog = waybillLogService.getWaybillLog(
                    depWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                    depWaybill.getWeight().toString(), depWaybill.getQuantity().toString(), depWaybill.getFlightNo1(),
                    depWaybill, null, 0, null, new Date(),
                    "取消货物交接", depWaybill.getType(), null);
            waybillLogService.insertWaybillLog(waybillLog);
        }
        return i;
    }

    /**
     * 货物接收
     * @param id 交接单id
     * @return 结果
     */
    @Override
    public int cargoReceive(Long id) {
        HzTransferHandover hzTransferHandover = hzTransferHandoverMapper.selectById(id);
        Integer cargoStatus = hzTransferHandover.getCargoStatus();
        // 检查交接单状态
        if(cargoStatus.equals(0)){
            throw new CustomException("货物未交接");
        }
        if(cargoStatus.equals(2)){
            throw new CustomException("货物已接收");
        }

        HttpServletResponse response = ServletUtils.getResponse();
        ArrayList<WaybillLog> waybillLogs = new ArrayList<>();

        //运单日志的新增
        List<HzTransferHandoverWaybill> waybills = handoverWaybillMapper.selectList(new QueryWrapper<HzTransferHandoverWaybill>()
                .eq("transfer_id", id));
        for (HzTransferHandoverWaybill waybill : waybills) {
            AirWaybill arrWaybill = airWaybillMapper.selectById(waybill.getWaybillId());
            // 创建新的副本，不修改原始对象
            AirWaybill copyWaybill = new AirWaybill();
            BeanUtils.copyProperties(arrWaybill, copyWaybill);
            AirWaybill airWaybill = getAirWaybill(copyWaybill);
            WaybillLog waybillLog = waybillLogService.getWaybillLog(
                    airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                    airWaybill.getWeight().toString(), airWaybill.getQuantity().toString(), airWaybill.getFlightNo1(),
                    airWaybill, null, 0, null, new Date(),
                    "中转货物接收", airWaybill.getType(), null);
            waybillLogs.add(waybillLog);
        }
        try {
            hzTransferHandover.setCargoBy(SecurityUtils.getNickName());
            hzTransferHandover.setCargoInTime(new Date());
            hzTransferHandover.setCargoStatus(2);
            int i = hzTransferHandoverMapper.updateById(hzTransferHandover);
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作成功" + "," +
                                "code:" + response.getStatus() + "," +
                                "data:" + i));
            }
            return i;
        } catch (Exception e) {
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作失败" + "," +
                                "code:" + response.getStatus()));
                waybillLog.setErrorMsg(e.getMessage());
                waybillLog.setStatus(1);
            }
            throw new CustomException(e.getMessage());
        } finally {
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLogService.insertWaybillLog(waybillLog);
            }
        }
    }

    private AirWaybill getAirWaybill(AirWaybill airWaybill) {
        List<AirWaybill> airWaybills = airWaybillMapper.selectList(new QueryWrapper<AirWaybill>()
                .eq("waybill_code", airWaybill.getWaybillCode())
                .eq("type", "DEP")
                .eq("is_del", 0));
        if (CollectionUtils.isEmpty(airWaybills)) {
            BaseCargoCode baseCargoCode2 = cargoCodeMapper.selectByCode(airWaybill.getCargoCode());
            costDetailMapper.delete(new QueryWrapper<CostDetail>().eq("waybill_code", airWaybill.getWaybillCode()));
            // 生成默认收费项目
            List<HzChargeItems> hzChargeItems = itemsMapper.selectList(new QueryWrapper<HzChargeItems>()
                    .eq("operation_type", "DEP")
                    .eq("is_default", 1).eq("status",1)
                    .le("start_effective_time", airWaybill.getWriteTime())
                    .ge("end_effective_time", airWaybill.getWriteTime())
                    .eq("is_del", 0));
            SysConfig sysConfig = sysConfigMapper.selectConfigById(16L);
            Date date = new Date();
            LocalTime localTime = LocalTime.of(6, 0, 0);
            Instant startInstant = date.toInstant();
            long aLong = Long.parseLong(sysConfig.getConfigValue());
            long times = aLong * 60 * 60;
            Instant endInstant = startInstant.plusSeconds(times);
            LocalTime endTime = LocalTime.of(6, 0, 1);
            Date storeEndTime = Date.from(endInstant);
            BigDecimal costSum = new BigDecimal(0);
            BigDecimal weightRate;
            BigDecimal chargeWeight = airWaybill.getChargeWeight() == null ? new BigDecimal(0) : airWaybill.getChargeWeight();
            if (airWaybill.getWeight() == null || airWaybill.getWeight().compareTo(new BigDecimal(0)) == 0){
                weightRate = new BigDecimal(0);
            }else {
                BigDecimal bigDecimal = chargeWeight.divide(airWaybill.getWeight(),5, RoundingMode.DOWN).multiply(airWaybill.getWeight());
                weightRate = bigDecimal.setScale(0, RoundingMode.CEILING);
            }
            for (HzChargeItems hzChargeItem : hzChargeItems) {
                List<HzChargeIrRelation> relations = relationMapper.selectList(new QueryWrapper<HzChargeIrRelation>().eq("item_id", hzChargeItem.getId()).eq("is_del",0));
                int maxMatchCount = 0;
                List<HzChargeIrRelation> ruleList = new ArrayList<>();
                for (HzChargeIrRelation hzChargeRule : relations) {
                    if (StringUtils.isNotEmpty(hzChargeRule.getNoCharge()) && !hzChargeRule.getNoCharge().contains(airWaybill.getDeptId().toString())){
                        continue;
                    }
                    if (StringUtils.isNotEmpty(hzChargeRule.getPrefix()) && !hzChargeRule.getPrefix().contains(airWaybill.getWaybillCode().substring(4,7))){
                        continue;
                    }
                    if (StringUtils.isNotEmpty(hzChargeRule.getCategory()) && !hzChargeRule.getCategory().contains(baseCargoCode2.getCategoryCode())){
                        continue;
                    }
                    if (hzChargeRule.getIsSouth() == 1){
                        continue;
                    }
                    if (hzChargeRule.getIsExit() == 1){
                        continue;
                    }
                    if (hzChargeRule.getCrossAir() == 1){
                        continue;
                    }
                    int matchCount = 0;
                    // 根据判断货品代码
                    int cargoMatchCount = isCargoCodeMatch(hzChargeRule, airWaybill.getCargoCode());
                    if (cargoMatchCount >= 0) {
                        matchCount += cargoMatchCount;
                    }
                    if (matchCount > 0) {
                        if (matchCount > maxMatchCount) {
                            maxMatchCount = matchCount;
                            ruleList.clear();
                            ruleList.add(hzChargeRule);
                        } else if (matchCount == maxMatchCount) {
                            ruleList.add(hzChargeRule);
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(ruleList)) {
                    HzChargeIrRelation relation = ruleList.stream().max(Comparator.comparing(HzChargeIrRelation::getPriority)).orElse(null);
                    if (relation != null) {
                        HzChargeRule rule1 = ruleMapper.selectById(relation.getRuleId());
                        List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>().eq("ir_id", relation.getId()));
                        if ("ColdStorageBillingRule.class".equals(rule1.getClassName()) && StringUtils.isEmpty(airWaybill.getColdStore())) {
                            continue;
                        }
                        CostDetail detail = new CostDetail();
                        detail.setWaybillCode(airWaybill.getWaybillCode());
                        detail.setIrId(relation.getId());
                        detail.setColdStore(airWaybill.getColdStore());
                        detail.setUnit(1);
                        detail.setSmallItem(1);
                        detail.setLargeItem(1);
                        detail.setSuperLargeItem(1);
                        detail.setStartTime(localTime);
                        detail.setEndTime(endTime);
                        detail.setDaysInStorage(1.0);
                        detail.setStoreStartTime(date);
                        detail.setStoreEndTime(storeEndTime);
                        detail.setPointTime(localTime);
                        BillingRule rule = BillingRuleFactory.createRule(rule1.getClassName());
                        BillRuleVo vo = rule.calculateFee(itemRules, weightRate, airWaybill.getQuantity(), detail);
                        BigDecimal totalCharge = BigDecimalRoundUtils.bigDecimalRound(hzChargeItem.getRoundRule(), vo.getTotalCharge());
                        detail.setTotalCharge(totalCharge);
                        detail.setDeptId(airWaybill.getDeptId());
                        detail.setQuantity(vo.getQuantity());
                        detail.setRate(vo.getRate());
                        costSum = costSum.add(totalCharge);
                        costDetailMapper.insert(detail);
                    }
                }
            }

            AirWaybill copyWaybill = new AirWaybill();
            BeanUtils.copyProperties(airWaybill, copyWaybill);
            copyWaybill.setFlightNo1(null);
            copyWaybill.setFlightDate1(null);
            copyWaybill.setPayMoney(costSum);
            copyWaybill.setStatus("put_in");
            copyWaybill.setType("DEP");
            copyWaybill.setId(null);
            airWaybillMapper.insert(copyWaybill);
            return copyWaybill;
        }
        return airWaybills.get(0);
    }

    /**
     * 加入提货列表
     * @param waybillIds 运单id集合
     * @return 中转列表
     */
    @Override
    public List<TransferPickVo> pickList(Long[] waybillIds) {
        List<TransferPickVo> list = airWaybillMapper.pickList(waybillIds);
        for (TransferPickVo transferPickVo : list) {
            HzArrTally hzArrTally = tallyMapper.selectOne(new QueryWrapper<HzArrTally>()
                    .eq("waybill_code", transferPickVo.getWaybillCode())
                    .last("limit 1"));
            if(hzArrTally!=null){
                if(hzArrTally.getStore()!=null){
                    transferPickVo.setStore(hzArrTally.getStore());
                }
                if(hzArrTally.getLocator()!=null){
                    transferPickVo.setLocator(hzArrTally.getLocator());
                }
            }
            List<String> flights = recordOrderMapper.selectListByCode(transferPickVo.getWaybillCode());
            if (!CollectionUtils.isEmpty(flights)) {
                String flightNo = String.join(",", flights);
                transferPickVo.setFlightNo(flightNo);
            }
        }
        return list;
    }

    /**
     * 根据录单id查询运单详情
     * @param orderId 录单id
     * @return 运单详情
     */
    @Override
    public EnterWaybillVo getWaybillInfo(Long orderId) {
        EnterWaybillVo waybillInfo = recordOrderMapper.getWaybillInfo(orderId);
        SysDept dept = sysDeptMapper.selectDeptById(Long.valueOf(waybillInfo.getShipper()));
        if (dept != null){
            waybillInfo.setShipper(dept.getDeptName());
        }
        return waybillInfo;
    }

    /**
     * 查询文件或货物交接详情
     * @param vo 文件或货物交接详情
     */
    private void selectFileAndCargoInfo(FileAndCargoTransferVo vo) {
        if (vo != null) {
            Long id = vo.getId();
            List<HzTransferHandoverWaybill> waybills = handoverWaybillMapper.selectList(new QueryWrapper<HzTransferHandoverWaybill>()
                    .eq("transfer_id", vo.getId()));
            if (!CollectionUtils.isEmpty(waybills)) {
                List<Long> waybillIds = waybills.stream().map(HzTransferHandoverWaybill::getWaybillId).collect(Collectors.toList());
                List<TransferPickVo> list = airWaybillMapper.selectByIds(waybillIds);
                for (TransferPickVo transferPickVo : list) {
                    setVoInfo(transferPickVo);
                    transferPickVo.setHandoverNo(vo.getHandoverNo());
                }
                if (!CollectionUtils.isEmpty(list)) {
                    TransferPickVo transferPickVo = list.get(0);
                    EnterWaybillVo infoByCode = airWaybillMapper.selectInfoById(transferPickVo.getWaybillId());
                    BeanUtils.copyProperties(infoByCode, vo);
                    if (infoByCode.getShipper() != null){
                        boolean matches = infoByCode.getShipper().matches("\\d+");
                        if (matches){
                            SysDept dept = sysDeptMapper.selectDeptById(Long.valueOf(infoByCode.getShipper()));
                            if (dept != null){
                                vo.setShipper(dept.getDeptName());
                            }
                        }
                    }
                    vo.setId(id);
                    vo.setCabinQuantity(transferPickVo.getTallyQuantity());
                    vo.setCabinWeight(transferPickVo.getTallyWeight());
                    vo.setVoList(list);
                }
            }
        }
    }

    /**
     * 设置vo
     * @param transferPickVo 挑单数据
     */
    private void setVoInfo(TransferPickVo transferPickVo) {
        if (transferPickVo.getShipper() != null){
            boolean matches = transferPickVo.getShipper().matches("\\d+");
            if (matches){
                SysDept dept = sysDeptMapper.selectDeptById(Long.valueOf(transferPickVo.getShipper()));
                if (dept != null){
                    transferPickVo.setShipper(dept.getDeptName());
                }
            }
        }
        List<String> flightNos = recordOrderMapper.selectListByCode(transferPickVo.getWaybillCode());
        if (!CollectionUtils.isEmpty(flightNos)) {
            String flightNo = String.join(",", flightNos);
            transferPickVo.setFlightNo(flightNo);
        }
        List<HzArrTally> tallyList = tallyMapper.selectList(new QueryWrapper<HzArrTally>().eq("waybill_code", transferPickVo.getWaybillCode()));
        if (!CollectionUtils.isEmpty(tallyList)) {
            int tallyQuantity = tallyList.stream().mapToInt(HzArrTally::getPieces).sum();
            transferPickVo.setTallyQuantity(tallyQuantity);
            BigDecimal tallyWeight = tallyList.stream().map(HzArrTally::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            transferPickVo.setTallyWeight(tallyWeight);
        }
    }

    /**
     * 查询交接单详情
     * @param vo 交接单详情数据
     */
    private void selectInfo(TransferHandoverInfoVo vo) {
        if (vo != null) {
            List<HzTransferHandoverWaybill> waybills = handoverWaybillMapper.selectList(new QueryWrapper<HzTransferHandoverWaybill>()
                    .eq("transfer_id", vo.getId()));
            if (!CollectionUtils.isEmpty(waybills)) {
                List<Long> waybillIds = waybills.stream().map(HzTransferHandoverWaybill::getWaybillId).collect(Collectors.toList());
                List<TransferPickVo> list = airWaybillMapper.selectByIds(waybillIds);
                for (TransferPickVo transferPickVo : list) {
                    setVoInfo(transferPickVo);
                }
                if (!CollectionUtils.isEmpty(list)) {
                    vo.setWaybills(list);
                }
            }
        }
    }

    private boolean isCategoryMatch(HzChargeIrRelation hzChargeRule, String categoryName) {
        if (hzChargeRule.getCategory() != null && !hzChargeRule.getCategory().isEmpty()) {
            Set<String> categorySet = new HashSet<>(Arrays.asList(hzChargeRule.getCategory().split(",")));
            return categorySet.contains(categoryName);
        }
        return false;
    }

    private int isCargoCodeMatch(HzChargeIrRelation hzChargeRule, String waybillCargoCode) {
        if (StringUtils.isEmpty(hzChargeRule.getCargoName())) {
            return 1;
        }else if (StringUtils.isEmpty(hzChargeRule.getCategory())){
            if (hzChargeRule.getCargoName().contains(waybillCargoCode)){
                return 1;
            }
            return 0;
        }
        List<IrRelationVo> relationVos = Arrays.stream(hzChargeRule.getCargoName().split(","))
                .map(cargoCodeMapper::selectIrByName)
                .collect(Collectors.toList());

        List<IrRelationVo> cargoNames = Arrays.stream(hzChargeRule.getCategory().split(","))
                .flatMap(category -> cargoCodeMapper.selectListByCategory(Collections.singletonList(category)).stream())
                .collect(Collectors.toList());

        Map<String, List<IrRelationVo>> relationVosMap = relationVos.stream()
                .collect(Collectors.groupingBy(IrRelationVo::getCategoryCode));

        List<IrRelationVo> combinedList = new ArrayList<>();
        Set<String> addedCodes = new HashSet<>();
        for (IrRelationVo vo : cargoNames) {
            String code = vo.getCategoryCode();
            if (relationVosMap.containsKey(code) && !addedCodes.contains(code)) {
                combinedList.addAll(relationVosMap.get(code));
                addedCodes.add(code);
            } else if (!addedCodes.contains(code)) {
                combinedList.add(vo);
            }
        }
        List<String> collect1 = combinedList.stream().map(IrRelationVo::getCode).collect(Collectors.toList());
        if (collect1.stream().anyMatch(cargoCode -> cargoCode.equals(waybillCargoCode))) {
            String category = hzChargeRule.getCategory();
            StringBuilder stringBuffer = new StringBuilder();
            if(category != null){
                String[] split = category.split(",");
                for (String categoryCode:split) {
                    List<BaseCargoCode> cargoCodeList = cargoCodeMapper.selectList(new QueryWrapper<>(new BaseCargoCode())
                            .eq("category_code", categoryCode)
                            .eq("is_del",0));
                    if(cargoCodeList!=null && cargoCodeList.size() > 0){
                        for (BaseCargoCode e:cargoCodeList) {
                            stringBuffer.append(e.getCode());
                        }
                    }
                }
            }else{
                return 1;
            }
            if (stringBuffer.toString().contains(waybillCargoCode)) {
                return 2;
            } else {
                return 1;
            }
        }
        return 0;
    }

    private MsgJsonVO setMsgVo(AirWaybill airWaybill, String sendAddress){
        MsgJsonVO vo = new MsgJsonVO();
        vo.setCarrier(airWaybill.getCarrier1());
        vo.setDepartureStation(airWaybill.getSourcePort());
        vo.setMsgType("FSU");
        vo.setNextStation(airWaybill.getDesPort());
        vo.setOperationNode("TFD");
        vo.setOperationStation("KWE");
        if (StringUtils.isNotEmpty(sendAddress)) {
            vo.setOrigin(sendAddress.split(","));
        }else {
            vo.setOrigin(new String[]{"KWEFDCN"});
        }
        vo.setPriority(new String[]{"QD"});
        vo.setSourceId("44162409105767715");
        vo.setUniqueId("44162409105767715");
        vo.setMsgVersion("12");
        BaseCarrier baseCarrier = carrierMapper.selectByCode(airWaybill.getCarrier1());
        if (baseCarrier != null){
            vo.setWaybillPrefix(baseCarrier.getPrefix());
        }
        return vo;
    }

    private void setTFDMsg(String token, MsgJsonVO msgJsonVO, AirWaybill airWaybill) {
        List<HzCableAddress> addressList = cableAddressMapper.selectAddressByAirportCode(Collections.singletonList(airWaybill.getDesPort()));
        StringBuilder sb = new StringBuilder();
        FSUJsonVO fsuJsonVO = new FSUJsonVO();
        fsuJsonVO.setDepAirport(airWaybill.getSourcePort());
        fsuJsonVO.setDesAirport(airWaybill.getDesPort());
        if(airWaybill.getWaybillCode().startsWith("AWBA")){
            String substring = airWaybill.getWaybillCode().substring(4);
            StringBuilder stringBuilder = new StringBuilder(substring);
            fsuJsonVO.setMawbNo(stringBuilder.insert(3, "-").toString());
        }else{
            String substring = airWaybill.getWaybillCode().substring(4);
            StringBuilder stringBuilder = new StringBuilder(substring);
            fsuJsonVO.setMawbNo(stringBuilder.insert(2, "-").toString());
        }
        fsuJsonVO.setPieces(airWaybill.getQuantity().toString());
        fsuJsonVO.setShipmentDescriptionCode("T");
        fsuJsonVO.setWeight(airWaybill.getWeight().toString());
        fsuJsonVO.setWeightUnit("K");
        List<StatusDetails> statusDetails = new ArrayList<>();
        StatusDetails statusDetail = new StatusDetails();
        statusDetail.setStatusCode(msgJsonVO.getOperationNode());
        statusDetail.setMovementCarrier(airWaybill.getCarrier1());
        statusDetail.setMovementFlightNo(airWaybill.getFlightNo1());
        statusDetail.setMovementDate(FORMAT.format(airWaybill.getFlightDate1()));
        statusDetail.setMovementTime(TIME_FORMAT.format(airWaybill.getFlightDate1()));
        statusDetail.setMovementAirport(airWaybill.getSourcePort());
        statusDetail.setShipmentDescriptionCode(fsuJsonVO.getShipmentDescriptionCode());
        statusDetail.setPieces(airWaybill.getQuantity().toString());
        statusDetail.setWeight(airWaybill.getWeight().toString());
        statusDetail.setWeightUnit("K");
        statusDetail.setReportingDate(DATE_FORMAT.format(new Date()));
        statusDetails.add(statusDetail);
        fsuJsonVO.setStatusDetails(statusDetails);
        msgJsonVO.setMsgJson(JSON.toJSONString(fsuJsonVO));
        Map<String, List<HzCableAddress>> addressMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(addressList)){
            addressMap = addressList.stream().collect(Collectors.groupingBy(HzCableAddress::getAirportCode));
        }
        List<HzCableAddress> cableAddresses = addressMap.get(airWaybill.getDesPort());
        setInteractionType(cableAddresses,msgJsonVO);
        restExchange(msgJsonVO,token,sb);
        insertCableAndSendMsg(msgJsonVO,airWaybill,sb,token);
    }

    public void setInteractionType(List<HzCableAddress> cableAddresses, MsgJsonVO vo) {
        List<String> sendType = new ArrayList<>();
        if (cableAddresses == null || cableAddresses.isEmpty()) {
            sendType.add("FD");
            vo.setSendType("FD");
            vo.setAddress(new String[]{"-"});
            return;
        }
        List<String> sendTypeCn = cableAddresses.stream()
                .map(HzCableAddress::getInteractionTypes)
                .filter(StringUtils::isNotEmpty)
                .map(s -> s.split(","))
                .flatMap(Arrays::stream)
                .map(String::trim)
                .distinct()
                .collect(Collectors.toList());
        for (String type : sendTypeCn) {
            switch (type) {
                case "民航局报文":
                    sendType.add("FD");
                    break;
                case "邮箱报文":
                    sendType.add("MAIL");
                    break;
                case "FTP收发报文":
                    sendType.add("FTP");
                    break;
                case "rabbitmq收发报文":
                    sendType.add("MQ");
                    break;
                default:
                    break;
            }
        }
        if (!sendType.contains("FD")){
            sendType.add("FD");
        }
        vo.setSendType(String.join(",", sendType));
        List<String> emailAddresses = new ArrayList<>();
        List<String> ftpList = new ArrayList<>();
        List<String> mqQueueList = new ArrayList<>();
        List<String> caacAddresses = new ArrayList<>();
        for (HzCableAddress cableAddress : cableAddresses) {
            List<String> interactionTypeList = new ArrayList<>();
            if (StringUtils.isNotEmpty(cableAddress.getInteractionTypes())) {
                interactionTypeList = Arrays.asList(cableAddress.getInteractionTypes().split(","));
            }
            if (!CollectionUtils.isEmpty(interactionTypeList)) {
                for (String type : interactionTypeList) {
                    switch (type) {
                        case "邮箱报文":
                            emailAddresses.add(cableAddress.getEmailAddress());
                            break;
                        case "FTP收发报文":
                            ftpList.add(cableAddress.getFtpList());
                            break;
                        case "民航局报文":
                            caacAddresses.add(cableAddress.getCaacAddress());
                            break;
                        case "rabbitmq收发报文":
                            mqQueueList.add(cableAddress.getMqQueue());
                            break;
                        default:
                            break;
                    }
                }
            }
        }
        if (!emailAddresses.isEmpty()) {
            vo.setReceiveMailAddress(emailAddresses.toArray(new String[0]));
        }
        if (!ftpList.isEmpty()) {
            vo.setReceiveFtpFolder(String.join(",",ftpList));
        }
        if (!mqQueueList.isEmpty()) {
            vo.setReceiveMQQueue(String.join(",",mqQueueList));
        }
        if (!caacAddresses.isEmpty()) {
            vo.setAddress(caacAddresses.toArray(new String[0]));
        }else {
            vo.setAddress(new String[]{"-"});
        }
    }

    private void insertCableAndSendMsg(MsgJsonVO vo, AirWaybill airWaybill, StringBuilder builder, String token) {
        if (StringUtils.isEmpty(vo.getSendType())){
            return;
        }
        List<String> sendTypes = Arrays.asList(vo.getSendType().split(","));
        if (!sendTypes.contains("FD")){
            sendTypes.add("FD");
        }
        for (String sendType : sendTypes) {
            HzCable cable = new HzCable();
            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("ddHHmm");
            cable.setSerialNo(SerialNumberGenerator.generateSerialNumber());
            cable.setCableNo(sdf.format(date));
            cable.setCableTime(date);
            cable.setCreateBy("系统");
            cable.setIsSend(1);
            cable.setIsAuto(1);
            cable.setType("FSU");
            cable.setVersion("12");
            cable.setPriority("QD");
            cable.setCableAddress(String.join(",", vo.getOrigin()));
            if (StringUtils.isNotEmpty(vo.getAddress())){
                cable.setReceiveAddress(String.join(",", vo.getAddress()));
            }
            cable.setFlightNo(airWaybill.getFlightNo1());
            cable.setFlightDate(airWaybill.getFlightDate1());
            cable.setContent(builder.toString());
            hzCableMapper.insert(cable);
            ForwardOriginMsgVO msgVO = new ForwardOriginMsgVO();
            msgVO.setOriginMsg(builder.toString());
            msgVO.setMsgType("FSU");
            msgVO.setSendType(sendType);
            msgVO.setReceiveAddress("-");
            if ("FD".equals(sendType)) {
                if (vo.getAddress() != null){
                    msgVO.setReceiveAddress(String.join(",",vo.getAddress()));
                }
            }
            if ("MAIL".equals(sendType)){
                msgVO.setMailAddress(String.join(",",vo.getReceiveMailAddress()));
            }
            if ("FTP".equals(sendType)){
                msgVO.setReceiveFtpAddress(ftpAddress);
                msgVO.setReceiveFtpFolder(vo.getReceiveFtpFolder());
            }
            if ("MQ".equals(sendType)){
                msgVO.setReceiveMQQueue(vo.getReceiveMQQueue());
            }
            msgVO.setSendAddress(String.join(",", vo.getOrigin()));
            msgVO.setPriority("QD");
            httpService.sendCable(msgVO, cable, token);
        }
    }

    private String getToken(){
        System.out.println("*********调用登录接口获取token*********");
        String token = "";
        HttpHeaders headers = setHeaders();
        HttpEntity<?> httpEntity = new HttpEntity<>(headers);
        ResponseEntity<JSONObject> exchange = restTemplate.exchange(loginUrl + account, HttpMethod.GET, httpEntity, JSONObject.class);
        if (exchange.getBody() != null) {
            if (!"0".equals(exchange.getBody().getString("code"))) {
                throw new CustomException(exchange.getBody().getString("message"));
            }
            JSONObject data = exchange.getBody().getJSONObject("data");
            token = data.getString("token");
            System.out.println(token);
        }
        return token;
    }

    private HttpHeaders setHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType("application/json;charset=UTF-8"));
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        headers.add("Accept-Charset", "UTF-8");
        return headers;
    }

    private void restExchange(MsgJsonVO vo,String token,StringBuilder sb){
        HttpHeaders header = setHeaders();
        header.add("X-Access-Token", token);
        HttpEntity<?> httpEntity = new HttpEntity<>(vo, header);
        System.out.println("参数：" + JSON.toJSONString(vo));
        ResponseEntity<JSONObject> exchange = restTemplate.exchange(getMsg, HttpMethod.POST, httpEntity, JSONObject.class);
        if (exchange.getBody() != null) {
            if (!"0".equals(exchange.getBody().getString("code"))) {
                throw new CustomException(exchange.getBody().getString("msg"));
            }
            JSONObject data = exchange.getBody().getJSONObject("data");
            String msgContent = data.getString("msgContent");
            sb.append(msgContent).append("\n");
        }
    }
}
