package com.gzairports.hz.business.arrival.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 进港交接表
 * <AUTHOR>
 * @date 2024-07-12
 */
@Data
@TableName("hz_arr_transfer")
public class HzArrTransfer {

    /** 主键id */
    private Long id;

    /** 航班日期 */
    @Excel(name = "航班日期", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date execDate;

    /** 航班号 */
    @Excel(name = "航班号")
    private String flightNo;

    /** 板车数量 */
    @Excel(name = "板车数量")
    private Integer uldCount;

    /** 业务袋数 */
    @Excel(name = "业务袋数")
    private Integer businessBagCount;

    /** 站坪交接人签字（图片） */
    @Excel(name = "站坪交接人签字")
    private String signUrl;

    /** 分拣员 */
    @Excel(name = "分拣员")
    private String sorter;

    /** 交接时间 */
    @Excel(name = "交接时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date handoverTime;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;
}
