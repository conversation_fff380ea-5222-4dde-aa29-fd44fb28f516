<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.departure.mapper.FlightBizMapper">
    <select id="selectBizByFlight" resultType="java.lang.Long">
        select hfl.id from all_flight_info afi
            left join hz_flight_load hfl on afi.flight_id = hfl.flight_id
        where CONCAT(afi.air_ways,afi.flight_no) = #{flightNo} and afi.exec_date = #{flightDate} and hfl.leg = #{desPort}
    </select>
</mapper>