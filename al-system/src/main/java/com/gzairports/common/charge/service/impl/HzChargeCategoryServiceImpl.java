package com.gzairports.common.charge.service.impl;


import com.gzairports.common.charge.domain.HzChargeCategory;
import com.gzairports.common.charge.mapper.HzChargeCategoryMapper;
import com.gzairports.common.charge.service.IHzChargeCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 收费货物类别Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-13
 */
@Service
public class HzChargeCategoryServiceImpl implements IHzChargeCategoryService
{
    @Autowired
    private HzChargeCategoryMapper hzChargeCategoryMapper;

    /**
     * 查询收费货物类别
     * 
     * @param id 收费货物类别主键
     * @return 收费货物类别
     */
    @Override
    public HzChargeCategory selectHzChargeCategoryById(Long id)
    {
        return hzChargeCategoryMapper.selectHzChargeCategoryById(id);
    }

    /**
     * 查询收费货物类别列表
     * 
     * @param hzChargeCategory 收费货物类别
     * @return 收费货物类别
     */
    @Override
    public List<HzChargeCategory> selectHzChargeCategoryList(HzChargeCategory hzChargeCategory)
    {
        return hzChargeCategoryMapper.selectHzChargeCategoryList(hzChargeCategory);
    }

    /**
     * 新增收费货物类别
     * 
     * @param hzChargeCategory 收费货物类别
     * @return 结果
     */
    @Override
    public int insertHzChargeCategory(HzChargeCategory hzChargeCategory)
    {
        return hzChargeCategoryMapper.insert(hzChargeCategory);
    }

    /**
     * 修改收费货物类别
     * 
     * @param hzChargeCategory 收费货物类别
     * @return 结果
     */
    @Override
    public int updateHzChargeCategory(HzChargeCategory hzChargeCategory)
    {
        return hzChargeCategoryMapper.updateHzChargeCategory(hzChargeCategory);
    }

    /**
     * 删除收费货物类别信息
     * 
     * @param id 收费货物类别主键
     * @return 结果
     */
    @Override
    public int deleteHzChargeCategoryById(Long id)
    {
        return hzChargeCategoryMapper.deleteHzChargeCategoryById(id);
    }
}
