package com.gzairports.common.basedata.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 集装器管理
 *
 * <AUTHOR>
 * @date 2024-02-19
 */
@Data
@TableName("base_cargo_uld")
public class BaseCargoUld {

    /** 主键id */
    private Long id;

    /** 编号 */
    @Excel(name = "编号")
    private String code;

    /** 类型 */
    @Excel(name = "类型")
    private String type;

    /** 航司 */
    @Excel(name = "航司")
    private String airCompany;

    /** 入场航班 */
    @Excel(name = "入场航班")
    private String entranceFlight;

    /** 自重 */
    @Excel(name = "自重")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal deadWeight;

    /** 最大载重 */
    @Excel(name = "最大载重")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal loadWeight;

    /** 最大装载尺寸 */
    @Excel(name = "最大装载尺寸")
    private String loadSize;

    /** 出场航班 */
    @Excel(name = "出场航班")
    private String exitFlight;

    /** 入场时间 */
    @Excel(name = "入场时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date entranceTime;

    /** 出场时间 */
    @Excel(name = "出场时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date exitTime;

    /** 图片 */
    @Excel(name = "图片")
    private String imageUrl;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** IN/OUT */
    private String inOut;

    /** 过磅重量 */
    @TableField(exist = false)
    private String weight;

    /** 使用状态 0 未使用 1 使用中 */
    private Integer useStatus;

    /** 开始使用时间 */
    private Date useTime;

    /** 目的站 */
    private String desPort;

    /** 流程状态 */
    private String status;
}
