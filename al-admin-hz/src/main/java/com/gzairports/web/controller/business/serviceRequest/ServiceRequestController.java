package com.gzairports.web.controller.business.serviceRequest;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.serviceRequest.domain.ServiceRequest;
import com.gzairports.common.serviceRequest.domain.query.ServiceRequestQuery;
import com.gzairports.common.serviceRequest.service.IServiceRequestService;
import com.gzairports.common.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 服务申请Controller
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@RestController
@RequestMapping("/hz/serviceRequest")
@Api(tags = "服务申请")
public class ServiceRequestController extends BaseController {

    @Autowired
    private IServiceRequestService requestService;

    /**
     * 查询服务申请列表
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询服务申请列表")
    public AjaxResult list(@RequestBody ServiceRequestQuery query){
        query.setDeptId(1L);
        return AjaxResult.success(requestService.selectListByQuery(query));
    }

    /**
     * 新增服务申请
     */
    @PreAuthorize("@ss.hasPermi('hz:serviceRequest:add')")
    @PostMapping("/add")
    @ApiOperation(value = "新增服务申请")
    public AjaxResult add(@RequestBody ServiceRequest request){
        return toAjax(requestService.add(request));
    }

    /**
     * 支付提交
     */
    @PostMapping("/payAndAdd")
    @ApiOperation(value = "支付提交")
    public AjaxResult payAndAdd(@RequestBody ServiceRequest request){
        request.setSysType("hz");
        return toAjax(requestService.payAndAdd(request));
    }

    /**
     * 详情
     */
    @PreAuthorize("@ss.hasPermi('hz:service:getInfo')")
    @GetMapping("/getInfo/{id}")
    @ApiOperation(value = "详情")
    public AjaxResult getInfo(@PathVariable("id") Long id){
        return AjaxResult.success(requestService.wlGetInfo(id));
    }

    /**
     * 提交
     */
    @PreAuthorize("@ss.hasPermi('hz:service:commit')")
    @GetMapping("/commit/{id}")
    @ApiOperation(value = "提交")
    public AjaxResult commit(@PathVariable("id") Long id){
        return toAjax(requestService.commit(id));
    }

    /**
     * 支付
     * */
    @GetMapping("/pay/{id}")
    @ApiOperation(value = "支付")
    public AjaxResult pay(@PathVariable("id") Long id){
        return AjaxResult.success(requestService.hzPay(id));
    }


    /**
     * 查询服务项目管理列表
     * */
    @GetMapping("/itemList")
    @ApiOperation(value = "查询服务项目管理列表")
    public AjaxResult itemList(){
        return AjaxResult.success(requestService.itemList());
    }
}
