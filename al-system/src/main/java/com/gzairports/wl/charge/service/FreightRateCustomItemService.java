package com.gzairports.wl.charge.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.wl.charge.domain.FreightRateCustomItem;
import com.gzairports.wl.charge.domain.query.FreightRateItemQuery;
import com.gzairports.wl.charge.domain.vo.FreightRateItemVO;

import java.util.List;

/**
 * 客户运价条目Service接口
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
public interface FreightRateCustomItemService extends IService<FreightRateCustomItem> {

    /**
     * 查询运价条目详情
     *
     * @param id 运价条目主键
     * @return 运价条目详情
     */
    FreightRateCustomItem selectCustomItemById(Long id);

    /**
     * 新增运价条目
     *
     * @param item 运价条目
     * @return 结果
     */
    int insertCustomItem(FreightRateCustomItem item);

    /**
     * 修改运价条目
     *
     * @param item 运价条目
     * @return 结果
     */
    int updateCustomItem(FreightRateCustomItem item);

    /**
     * 删除运价条目
     *
     * @param id 运价条目主键
     * @return 结果
     */
    int removeCustomItem(Long id);


    /**
     * 查询运价条目列表
     *
     * @param query 运价条目查询参数
     * @return 结果
     */
    List<FreightRateItemVO> selectList(FreightRateItemQuery query);

    void deleteAllFreightRateCustomItem(Long[] ids);
}
