package com.gzairports.wl.departure.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.wl.departure.domain.Consign;
import com.gzairports.wl.departure.domain.query.ConsignQuery;
import com.gzairports.wl.departure.domain.query.EditAuthQuery;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 托运管理Service接口
 *
 * <AUTHOR>
 * @date 2024-05-08
 */
public interface IConsignService extends IService<Consign> {

    /**
     * 新增托运
     *
     * @param consign 托运数据
     * @return 返回结果
     */
    int add(Consign consign);

    /**
     * h5新增托运
     *
     * @param consign 托运数据
     * @return 返回结果
     */
    int h5Add(Consign consign);

    /**
     * 查询托运数据
     *
     * @param query 查询参数
     * @return 托运列表
     */
    List<Consign> selectList(ConsignQuery query);

    /**
     * 柜台录入托运数据
     *
     * @param consign 柜台录入数据
     * @return 结果
     */
    int edit(Consign consign);

    /**
     * 更改客户权限
     *
     * @param query 更改参数
     * @return 结果
     */
    int editAuth(EditAuthQuery query);

    /**
     * 根据id查询托运详情
     *
     * @param id 托运id
     * @return 托运详情
     */
    Consign getInfo(Long id);

    /**
     * 根据分单运单号查询托运详情
     *
     * @param waybillCode 分单运单号
     * @return 电子托运书pdf地址
     */
    String pdfUrl(String waybillCode);

    /**
     * 根据身份证号和目的站查询历史记录
     * @param shipperIdCard 身份证号
     * @param desPort 目的站
     * @return 历史记录
     */
    Consign getHistory(String shipperIdCard, String desPort);

    /**
     * 打印托运书
     * @param response 返回流
     * @param id 托运书id
     */
    void printConsign(HttpServletResponse response, Long id) throws Exception;
}
