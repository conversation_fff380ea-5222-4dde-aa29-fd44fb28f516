package com.gzairports.hz.business.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import com.gzairports.hz.business.departure.domain.HzDepGroupUld;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 组货调度返回数据
 * <AUTHOR>
 * @date 2024-07-03
 */
@Data
public class GroupCargoVo {

    /** 航班配载id */
    private Long id;

    /** 航班号 */
    @Excel(name = "航班号")
    private String flightNo;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "航班日期")
    private Date execDate;

    /** 计飞 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "计飞")
    private LocalDateTime startSchemeTakeoffTime;

    /** 配载板箱数 */
    @Excel(name = "配载板箱数")
    private Integer loadUldNum;

    /** 运单数 */
    @Excel(name = "运单数")
    private Integer waybillNum;

    /** 组货板箱数 */
    @Excel(name = "组货板箱数")
    private Integer groupUldNum;

    /** 状态（未配载 已配载 未组货 已组货 未复重 未交接 已完成 无货） */
    private String state;

    @Excel(name = "状态")
    private String status;

    /** 配载员 */
    @Excel(name = "配载员")
    private String loadUser;

    /** 配载时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "配载时间")
    private Date loadTime;

    /** 组货员 */
    @Excel(name = "组货员")
    private String groupUser;

    /** 组货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "组货时间")
    private Date groupTime;

    /** 复重时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date weightTime;

    /** 站坪交接时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date transferTime;

    /** 站坪交接人签字 */
    private String signUrl;

    /** 交接时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date handoverTime;

    /**  机号  */
    private String craftNo;

    /** 组货数据 */
    private List<HzDepGroupUld> vos;
}
