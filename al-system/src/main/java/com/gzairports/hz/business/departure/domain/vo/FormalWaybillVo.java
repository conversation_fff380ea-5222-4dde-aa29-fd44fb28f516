package com.gzairports.hz.business.departure.domain.vo;

import com.gzairports.common.pdf.PdfPrintAnnotation;
import com.gzairports.hz.business.departure.service.Formal;
import lombok.Data;

import java.math.BigDecimal;

import static com.gzairports.common.pdf.PdfPrintAnnotation.PdfFieldType.TEXT;

/**
 * 正式舱单运单列表返回数据
 * @date 2024-07-04
 * <AUTHOR>
 */
@Data
public class FormalWaybillVo implements Formal {

    /** 主键id */
    private Long id;

    /** 舱位 */
    private String cabin;

    /** 运单号 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "waybillCode")
    private String waybillCode;

    /** 原单单号 */
    private String originBill;

    /** 件数 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "quantity")
    private Integer quantity;

    /** 重量 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "weight")
    private BigDecimal weight;

    /** 运单件数 */
    private Integer waybillQuantity;

    /** 运单重量 */
    private BigDecimal waybillWeight;

    /** 体积 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "volume")
    private BigDecimal volume;

    /** 品名 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "cargoName")
    private String cargoName;

    /** 品名编码 */
    private String categoryName;

    /** 始发站 */
    private String sourcePort;

    /** 目的站 */
    private String desPort;

    /** 特殊处理代码 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "specialCargoCode1")
    private String specialCargoCode1;

    /** 优先级 */
    private String priority;

    /** 备注 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "remark")
    private String remark;

    /** 是否有修改 */
    private Integer isEdit;

    /** 散舱 */
    private Integer looseCabin;
}
