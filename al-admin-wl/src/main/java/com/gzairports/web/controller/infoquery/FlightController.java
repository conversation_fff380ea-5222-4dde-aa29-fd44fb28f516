package com.gzairports.web.controller.infoquery;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.infoquery.domain.Flight;
import com.gzairports.common.infoquery.domain.query.FlightQuery;
import com.gzairports.common.infoquery.service.IFlightService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 航班信息Controller
 *
 * <AUTHOR>
 * @date 2024-05-08
 */
@RestController
@RequestMapping("/info/flight")
public class FlightController extends BaseController {

    @Autowired
    private IFlightService flightService;

    /**
     * 获取航班列表
     */
//    @PreAuthorize("@ss.hasPermi('info:flight:list')")
    @GetMapping("/list")
    public TableDataInfo list(FlightQuery query)
    {
        startPage();
        List<Flight> list = flightService.selectFlightList(query);
        return getDataTable(list);
    }

    @GetMapping("/flightBookList")
    public AjaxResult flightBookList(FlightQuery query)
    {
        List<Flight> list = flightService.selectFlightBookList(query);
        return AjaxResult.success(list);
    }

    /**
     * 查询航班信息
     */
    @PreAuthorize("@ss.hasPermi('info:flight:getCraft')")
    @GetMapping("/getCraft")
    public AjaxResult getCraft(FlightQuery query)
    {
        Flight flight = flightService.getCraft(query);
        return AjaxResult.success(flight);
    }
}
