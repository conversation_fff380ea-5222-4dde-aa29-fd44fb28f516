package com.gzairports.common.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.basedata.domain.BaseBillSource;

import java.util.List;

/**
 * 票证来源Service接口
 *
 * <AUTHOR>
 * @date 2024-05-17
 */
public interface IBillSourceService extends IService<BaseBillSource> {

    /**
     * 查询票证来源列表
     *
     * @return 票证来源列表
     */
    List<BaseBillSource> selectList();

    /**
     * 查询票证来源详情
     *
     * @param id 票证来源id
     * @return 票证来源详情
     */
    BaseBillSource getInfo(Long id);

    /**
     * 新增票证来源
     *
     * @param baseBillSource 票证来源
     * @return 结果
     */
    int insertSource(BaseBillSource baseBillSource);

    /**
     * 新增票证来源
     *
     * @param baseBillSource 票证来源
     * @return 结果
     */
    int updateSource(BaseBillSource baseBillSource);

    /**
     * 根据id查询票证类型和前缀
     *
     * @param id 票证来源id
     * @return 结果
     */
    BaseBillSource byId(Long id);

    /**
     * 根据id逻辑删除票证来源
     *
     * @param id 票证来源id
     * @return 结果
     */
    int deleteById(Long id);
}
