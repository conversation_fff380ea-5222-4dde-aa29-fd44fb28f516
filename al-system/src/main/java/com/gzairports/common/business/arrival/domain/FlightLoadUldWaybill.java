package com.gzairports.common.business.arrival.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 航班配载集装器预配关联的运单表
 *
 * <AUTHOR>
 * @date 2024-07-01
 */
@Data
@TableName("hz_flight_load_uld_waybill")
public class FlightLoadUldWaybill {

    /** 主键id */
    private Long id;

    /** 装载板箱id */
    private Long loadUldId;

    /** 运单id */
    private Long waybillId;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 收运id */
    private Long collectId;

    /** 运单号 */
    @TableField(exist = false)
    private String waybillCode;

    /** 是否修改 */
    private Integer isEdit;
}
