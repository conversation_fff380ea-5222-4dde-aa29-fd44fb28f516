package com.gzairports.common.basedata.domain.query;

import lombok.Data;

import java.util.List;

/**
 * 航司机型差异查询参数
 *
 * <AUTHOR>
 * @date 2024-02-19
 */
@Data
public class AirDifferentQuery {

    /** 主键id集合 */
    private List<Long> ids;

    /** 航司 */
    private String airCompany;

    /** 机型 */
    private String flightType;

    /** 机号 */
    private String flightNumber;

    /** 目的站 */
    private String desPort;

    /** 不可配载货品 */
    private String noLoad;

    /** 生效开始生效时间 */
    private String startTime;

    /** 生效结算生效时间 */
    private String endTime;
}
