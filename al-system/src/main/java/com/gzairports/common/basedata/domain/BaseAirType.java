package com.gzairports.common.basedata.domain;

import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 机型管理
 *
 * <AUTHOR>
 * @date 2024-02-19
 */
@Data
public class BaseAirType {

    /** 主键id */
    private Long id;

    /** 机型 */
    @Excel(name = "机型")
    private String type;

    /** 机型类型 */
    @Excel(name = "机型类型")
    private String airType;

    /** 分舱情况 */
    @Excel(name = "分舱情况")
    private String cabin;

    /** 实际可装板数 */
    @Excel(name = "实际可装板数")
    private String number;

    /** 舱位限载 */
    @Excel(name = "舱位限载")
    private String cabinLimit;

    /** 地面承受力（KG/M2） */
    @Excel(name = "地面承受力（KG/M2）")
    private BigDecimal support;

    /** 前舱门尺寸 */
    @Excel(name = "前舱门尺寸")
    private String beforeHatchSize;

    /** 后舱门尺寸 */
    @Excel(name = "后舱门尺寸")
    private String afterHatchSize;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 是否删除 0 否 1 是 */
    private Integer isDel;
}
