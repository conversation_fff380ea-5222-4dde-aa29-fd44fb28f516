package com.gzairports.wl.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.business.departure.domain.CostDetail;
import com.gzairports.hz.business.departure.domain.vo.SettleDetailVo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 货站在线缴费运单详情参数
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
public class OnlineInfoVo {

    /** 主键id */
    private Long id;

    /** 运单号 */
    private String waybillCode;

    /** 始发站 */
    private String sourcePort;

    /** 目的站 */
    private String desPort;

    /** 航班号 */
    private String flightNo1;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date flightDate1;

    /** 发货人 */
    private String shipper;

    /** 特货代码1 */
    private String specialCargoCode1;

    /** 品名编码 */
    private String cargoCode;

    /** 品名 */
    private String cargoName;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 计费重量 */
    private BigDecimal chargeWeight;

    /** 缴费状态 0 未授权支付 1 已授权支付 2 已结算 */
    private Integer payStatus;

    /** 缴费金额 */
    private BigDecimal payMoney;

    /** 出港件数 */
    private Integer depQuantity;

    /** 出港重量 */
    private BigDecimal depWeight;

    /** 结算时间 */
    private Date settleTime;

    /** 预授权支付时间 */
    private Date payTime;

    private String type;

    /** 预授权费用明细 */
    List<CostDetail> payList;

    /** 结算费用明细 */
    List<SettleDetailVo> settleList;

    /** 待结算费用 */
    private List<CostDetail> waitPayList;

}
