<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.charge.mapper.HzChargeIrRelationMapper">
    <select id="selectColdCharge" resultType="com.gzairports.common.charge.domain.HzChargeIrRelation">
        select hcir.id, hcir.item_id, hcir.rule_id, hcir.priority, hcir.category, hcir.cargo_name, hcir.no_charge,
               hcir.is_exit, hcir.is_south, hcir.cross_air
        from hz_charge_ir_relation hcir
            left join hz_charge_rule hcr on hcr.id = hcir.rule_id where hcir.item_id = #{id} and hcr.class_name = 'ColdStorageBillingRule.class'
    </select>
</mapper>