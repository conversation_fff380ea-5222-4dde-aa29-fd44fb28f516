package com.gzairports.common.basedata.domain.query;

import lombok.Data;

import java.util.List;

/**
 * 货站货品代码查询参数
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Data
public class CargoCodeQuery {

    /** 主键id集合 */
    private List<Long> ids;

    /** 货品代码 */
    private String code;

    /** 是否特货 */
    private Integer isSpecial;

    /** 适用进港 */
    private Integer isArrival;

    /** 适用出港 */
    private Integer isOut;

    /** 适用国内 */
    private Integer domestic;

    /** 适用国际 */
    private Integer international;

    /** 大类 */
    private String categoryCode;

    /** 中文名称 */
    private String chineseName;
}
