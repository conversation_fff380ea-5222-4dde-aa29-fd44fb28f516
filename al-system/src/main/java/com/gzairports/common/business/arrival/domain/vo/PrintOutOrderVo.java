package com.gzairports.common.business.arrival.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 打印出库单数据
 *
 * <AUTHOR>
 * @date 2024-07-19
 */
@Data
public class PrintOutOrderVo {

    /** 办理提货id */
    private Long id;

    /** 提货客户 */
    private String customerName;

    /** 提货人证件类型 */
    private String customerIdType;

    /** 提货人证件号 */
    private String customerIdNo;

    /** 提货人手机号 */
    private String customerPhone;

    /** 收货人 */
    private String consign;

    /** 收货人证件号 */
    private String consignIdNo;

    /** 收货人手机号 */
    private String consignPhone;

    /** 流水号 */
    private String serialNo;

    /** 提货码 */
    private String pickUpCode;

    /** 提货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date outTime;

    /** 提货码二维码 */
    private String qrCode;

    /** 出库员 */
    private String outName;

    /** 搬运员 */
    private String moverName;

    /** 签收人 */
    private String signName;

    /** 出库单运单列表 */
    private List<OutOrderWaybillVo> waybillVos;

    /** 提货码打印次数 */
    private Integer printCount;

    /** 是否支付 */
    private Integer isPay;
}
