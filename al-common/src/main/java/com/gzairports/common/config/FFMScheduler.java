package com.gzairports.common.config;

import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * Created by david on 2025/6/11
 */
@Service
public class FFMScheduler {

    private final ScheduledExecutorService executor;
    private final Map<String, ScheduledFuture<?>> tasks = new ConcurrentHashMap<>();

    public FFMScheduler(@Qualifier("ffmSchedulerExecutor") ScheduledExecutorService executor) {
        this.executor = executor;
    }

    public void scheduleOrReset(Long flightId, Runnable task, Duration delay) {
        cancel(flightId.toString());
        ScheduledFuture<?> future = executor.schedule(
                withMdcContext(task),
                delay.toMillis(),
                TimeUnit.MILLISECONDS
        );
        tasks.put(flightId.toString(), future);
    }

    public void cancel(String flightId) {
        ScheduledFuture<?> future = tasks.remove(flightId);
        if (future != null) {
            future.cancel(false);
        }
    }

    private Runnable withMdcContext(Runnable runnable) {
        Map<String, String> context = MDC.getCopyOfContextMap();
        return () -> {
            if (context != null) {
                MDC.setContextMap(context);
            }
            try {
                runnable.run();
            } finally {
                MDC.clear();
            }
        };
    }

    @PreDestroy
    public void shutdown() {
        executor.shutdownNow();
    }
}
