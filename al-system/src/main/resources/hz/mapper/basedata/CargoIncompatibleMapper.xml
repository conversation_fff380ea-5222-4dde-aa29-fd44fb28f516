<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.CargoIncompatibleMapper">
    <insert id="insertCargoIncompatible" parameterType="baseCargoIncompatible">
        insert into base_cargo_incompatible
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="airCompany != null and airCompany != ''">air_company,</if>
            <if test="specialCargoOne != null and specialCargoOne != ''">special_cargo_one,</if>
            <if test="specialCargoTwo != null and specialCargoTwo != ''">special_cargo_two,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="airCompany != null and airCompany != ''">#{airCompany},</if>
            <if test="specialCargoOne != null and specialCargoOne != ''">#{specialCargoOne},</if>
            <if test="specialCargoTwo != null and specialCargoTwo != ''">#{specialCargoTwo},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>
    <update id="updateCargoIncompatible" parameterType="baseCargoIncompatible">
        update base_cargo_incompatible
        <trim prefix="SET" suffixOverrides=",">
            <if test="airCompany != null and airCompany != ''">air_company = #{airCompany},</if>
            <if test="specialCargoOne != null and specialCargoOne != ''">special_cargo_one = #{specialCargoOne},</if>
            <if test="specialCargoTwo != null and specialCargoTwo != ''">special_cargo_two = #{specialCargoTwo},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null and createTime != ''">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="delCargoIncompatible" parameterType="long">
        update base_cargo_incompatible set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <select id="selectCargoIncompatibleList" resultType="com.gzairports.common.basedata.domain.BaseCargoIncompatible">
        select id, air_company, special_cargo_one, special_cargo_two, remark from base_cargo_incompatible
        <where>
        and is_del = 0
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="airCompany != null  and airCompany != ''"> and air_company like concat('%',#{airCompany},'%')</if>
            <if test="specialCargoOne != null and specialCargoOne != ''"> and special_cargo_one like concat('%',#{specialCargoOne},'%')</if>
            <if test="specialCargoTwo != null and specialCargoTwo != ''"> and special_cargo_two like concat('%',#{specialCargoTwo},'%')</if>
        </where>
    </select>
    <select id="selectIncompatibleByCode" resultType="com.gzairports.common.basedata.domain.BaseCargoIncompatible">
        select id, air_company, special_cargo_one, special_cargo_two, remark
        from base_cargo_incompatible
        <where>
            and is_del = 0
            <if test="airCompany != null  and airCompany != ''"> and air_company = #{airCompany}</if>
            <if test="specialCargoOne != null and specialCargoOne != ''"> and special_cargo_one = #{specialCargoOne}</if>
            <if test="specialCargoTwo != null and specialCargoTwo != ''"> and special_cargo_two = #{specialCargoTwo}</if>
        </where>
    </select>
</mapper>
