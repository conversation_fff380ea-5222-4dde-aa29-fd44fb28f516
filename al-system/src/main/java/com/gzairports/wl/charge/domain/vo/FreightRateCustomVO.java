package com.gzairports.wl.charge.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import com.gzairports.wl.charge.domain.FreightRateCustomAir;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 客户运价返回数据
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@Data
public class FreightRateCustomVO {

    /** 主键id */
    @TableId(value = "id")
    private Long id;

    /** 运价名称 */
    @Excel(name = "运价名称")
    private String rateName;

    /** 运价模式 */
    private Integer rateModel;

    /** 适用单证类型 */
    private String applicableDocuments;

    /** 客户名称 */
    @Excel(name = "客户名称")
    private String customer;

    /** 是否自动从低 */
    private Integer automaticFromLow;

    /** 取舍方式 */
    private Integer roundingMethod;

    /** 所属单位 */
    private Long deptId;

    /** 条目是否逻辑删除 */
    private Integer isDel;

    /** 更新时间 */
    private Date updateTime;

    /** 更新人 */
    private String updateBy;

    /** 创建时间 */
    private Date createTime;

    /** 创建人 */
    private String createBy;

    /** 运价模式中文 */
    private String fareModel;


    @Excel(name = "适用单证类型")
    private String applicable;

    @Excel(name = "是否自动从低")
    private String automatic;

    @Excel(name = "取舍方式")
    private String rounding;

    /** 运价增加值 */
    private List<FreightRateCustomAir> list;

    /** 生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生效日期", dateFormat = "yyyy-MM-dd")
    private Date effectiveDate;

    /** 截止日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "截止日期",dateFormat = "yyyy-MM-dd")
    private Date expirationDate;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;
}
