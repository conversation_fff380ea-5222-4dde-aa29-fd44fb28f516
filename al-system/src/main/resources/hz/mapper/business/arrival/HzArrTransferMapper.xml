<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.arrival.mapper.HzArrTransferMapper">
    <select id="selectListByQuery" resultType="com.gzairports.hz.business.arrival.domain.HzArrTransfer">
        select id, exec_date, flight_no, uld_count, business_bag_count, sign_url, sorter, handover_time, remark from hz_arr_transfer
        <where>
            <if test="execDate != null">
                and exec_date = #{execDate}
            </if>
            <if test="flightNo != null and flightNo != ''">
                and flight_no like '%${flightNo}%'
            </if>
            <if test="sorter != null and sorter != ''">
                and sorter = #{sorter}
            </if>
        </where>
            order by handover_time desc
    </select>
    <select id="batchSelect" resultType="com.gzairports.hz.business.arrival.domain.HzArrTransfer">
        SELECT exec_date, flight_no, handover_time
        FROM hz_arr_transfer
        <where>
            (flight_no, exec_date) in
            <foreach collection="keys" item="key" open="(" separator="," close=")">
                (#{key.flightNo}, #{key.execDate})
            </foreach>
        </where>
    </select>
</mapper>