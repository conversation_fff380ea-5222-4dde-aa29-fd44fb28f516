package com.gzairports.wl.charge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.wl.charge.domain.FreightRateAirItem;
import com.gzairports.wl.charge.domain.query.FareAirItemQuery;
import com.gzairports.wl.charge.domain.query.FreightRateItemQuery;
import com.gzairports.wl.charge.domain.vo.FareAirItemVo;
import com.gzairports.wl.charge.domain.vo.FreightRateAirItemVO;

import java.util.List;

/**
 * 航司运价条目Service接口
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
public interface FreightRateAirItemService extends IService<FreightRateAirItem> {

    /**
     * 查询运价条目列表
     *
     * @param query 运价条目查询参数
     * @return 结果
     */
    List<FreightRateAirItemVO> selectList(FreightRateItemQuery query);

    /**
     * 查询运价条目详情
     *
     * @param id 运价条目主键
     * @return 运价条目详情
     */
    FreightRateAirItem selectAirItemById(Long id);

    /**
     * 新增运价条目
     *
     * @param item 运价条目
     * @return 结果
     */
    int insertAirItem(FreightRateAirItem item);

    /**
     * 修改运价条目
     *
     * @param item 运价条目
     * @return 结果
     */
    int updateAirItem(FreightRateAirItem item);

    /**
     * 删除运价条目
     *
     * @param id 运价条目主键
     * @return 结果
     */
    int removeAirItem(Long id);


    List<FareAirItemVo> selectItemList(FareAirItemQuery query);

    void delAll(Long[] ids);
}
