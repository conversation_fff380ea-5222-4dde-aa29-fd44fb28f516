package com.gzairports.common.business.departure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.departure.domain.BookingRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订舱记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@Mapper
public interface BookingRecordMapper extends BaseMapper<BookingRecord> {
    void updateStatus(@Param("bookingId") Long bookingId,@Param("status") Integer status);

    /**
     * 批量审核
     * @param bookingIds 订舱id
     * @param status 状态
     * @return 结果
     */
    int updateStatusBatch(@Param("bookingIds") List<Long> bookingIds,@Param("status") Integer status);
}
