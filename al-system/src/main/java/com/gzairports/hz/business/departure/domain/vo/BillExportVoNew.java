package com.gzairports.hz.business.departure.domain.vo;

import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 账单导出 两个表版本
 * @date 2025-06-25
 **/

@Data
public class BillExportVoNew {
    /**
     * 结算周期
     */
    @Excel(name = "结算周期")
    private String settlementCycle;

    /**
     * 上期余额
     * */
    @Excel(name = "上期系统余额")
    private BigDecimal lastBalance;

    /**
     * 充值金额
     */
    @Excel(name = "充值金额")
    private BigDecimal rechargeAmount;

    /**
     * 当期系统余额
     */
    @Excel(name = "当期系统余额")
    private BigDecimal balance;

    /**
     * 财务余额
     * */
    @Excel(name = "财务余额")
    private BigDecimal financeBalance;

    /**
     * 本期结算
     */
    @Excel(name = "本期结算")
    private BigDecimal settledAmount;

    /**
     * 总未结算
     * */
    @Excel(name = "总未结算")
    private BigDecimal notSettleAmount;

    /**
     * 已结算
     * */
    private List<ChargeBilExportVO> chargeBilExportVOS;

    /**
     * 未结算
     * */
    private List<ChargeBilExportNotSettleVO> chargeBilExportVOSNotSettle;
}
