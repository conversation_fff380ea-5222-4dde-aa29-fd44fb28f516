package com.gzairports.common.business.arrival.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gzairports.common.business.arrival.domain.HzArrItem;
import com.gzairports.common.business.arrival.domain.query.CostDetailQuery;
import com.gzairports.common.business.arrival.domain.vo.ArrItemVo;
import com.gzairports.common.business.arrival.domain.vo.ArrCostDetailListVo;
import com.gzairports.common.business.reporter.domain.ReportArrCost;
import com.gzairports.common.business.reporter.domain.vo.ReportBillInfoVo;
import com.gzairports.hz.business.arrival.domain.query.TallyWaybillKey;
import com.gzairports.hz.business.departure.domain.vo.ChargeBillArrSettleExportVO;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.security.core.parameters.P;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 进港运单费用明细Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-03
 */
@Mapper
public interface HzArrItemMapper extends BaseMapper<HzArrItem> {

    /**
     * 挑单费用明细数据
     * @param waybillCode 进港运单号
     * @param tallyId 理货id
     * @return 结果
     */
    List<HzArrItem> selectCostList(@Param("waybillCode") String waybillCode,@Param("tallyId") Long tallyId);

    /**
     * 挑单费用明细数据
     * @param waybillCode 进港运单号
     * @param tallyIds 理货id集合
     * @return 结果
     */
    List<HzArrItem> selectCostListByCodeAndTallyList(@Param("waybillCode") String waybillCode,@Param("tallyIds")List<Long> tallyIds);

    /**
     * 挑单费用明细数据
     * @param waybillCode 进港运单号
     * @param tallyId 理货id
     * @return 结果
     */
    List<HzArrItem> selectCostListByPickUpId(@Param("waybillCode") String waybillCode,@Param("tallyId") Long tallyId,@Param("pickUpId") Long pickUpId);

    /**
     * 挑单费用明细详情
     * @param id 费用明细id
     * @return 详情
     */
    HzArrItem selectCostInfo(Long id);

    List<ArrItemVo> selectItemVo(@Param("tallyId") Long tallyId,@Param("waybillCode") String waybillCode);

    List<HzArrItem> selectPayOrSettleList(String waybillCode);

    List<ReportArrCost> selectReportList(@Param("waybillCodes") List<String> waybillCodes);

    BigDecimal selectFeeRate(String waybillCode);

    List<HzArrItem> selectItemList(@Param("waybillCode") String waybillCode,@Param("id") Long id,@Param("type") int type);

    List<HzArrItem> selectItemByTallyId(String waybillCode);

    IPage<ArrCostDetailListVo> selectCostDetail(@Param("query") CostDetailQuery query, @Param("page") IPage<ArrCostDetailListVo> page);

    List<ArrItemVo> selectAllItemVos(@Param("keys") List<TallyWaybillKey> keys);

    List<ArrItemVo> selectAllItemVosNew(@Param("keys") List<String> waybillCodeList,@Param("payMethod") Integer payMethod);

    List<ArrItemVo> selectBatch(@Param("keys") List<TallyWaybillKey> keys);

    List<HzArrItem> selectListByKey(@Param("keys") List<TallyWaybillKey> keys);

    List<HzArrItem> selectItemNotPayByWaybillCode(String waybillCode);

    List<ChargeBillArrSettleExportVO> selectListByQuery(@Param("startTime") LocalDateTime flightStartTime,
                                                        @Param("endTime") LocalDateTime flightEndTime,
                                                        @Param("deptIds") List<Long> deptIds);
}
