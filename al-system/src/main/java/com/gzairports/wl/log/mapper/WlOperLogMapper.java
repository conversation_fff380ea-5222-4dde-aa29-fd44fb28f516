package com.gzairports.wl.log.mapper;



import com.gzairports.wl.log.domain.WlOperLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 物流日志Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@Mapper
public interface WlOperLogMapper {

    /**
     * 新增操作日志
     *
     * @param operLog 操作日志对象
     */
    void insertWlOperLog(WlOperLog operLog);

    /**
     *  查询物流操作日志
     * @param title 模块标题
     * @param id 查询日志id
     * @return 操作日志
     */
    List<WlOperLog> selectByQuery(@Param("title") String title, @Param("id") Long id);

    /**
     *  查询物流操作日志详情
     * @param logId 操作日志id
     * @return 操作日志
     */
    WlOperLog logInfo(Long logId);
}
