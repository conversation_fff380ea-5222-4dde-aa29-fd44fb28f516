package com.gzairports.wl.departure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzairports.common.business.arrival.domain.query.AutoOrderQuery;
import com.gzairports.common.business.departure.domain.Mawb;
import com.gzairports.wl.departure.domain.Hawb;
import com.gzairports.wl.departure.domain.query.HawbQuery;
import com.gzairports.wl.departure.domain.vo.HawbBillVo;
import com.gzairports.wl.departure.domain.vo.HawbQueryVo1;
import com.gzairports.wl.departure.domain.vo.MergeHawbVo;
import com.gzairports.wl.departure.domain.vo.WaybillTraceVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;


/**
 * 分单制单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@Mapper
public interface HawbMapper extends BaseMapper<Hawb> {

    /**
     * 根据分单id查询拼单运单
     * @param hawbId 分单id
     * @param deptId 单位id
     * @return 拼单运单
     */
    MergeHawbVo selectMergeList(@Param("hawbId") Long hawbId,@Param("deptId") Long deptId);

    /**
     * 根据运单号查询运单跟踪数据
     * @param waybillCode 运单号
     * @param deptId 所属单位
     * @return 运单跟踪数据
     */
    WaybillTraceVo selectOneByCode(@Param("waybillCode") String waybillCode,@Param("deptId") Long deptId);

    /**
     * 根据运单id查询运单号
     * @param hawbId 分运单id
     * @return 运单号
     */
    String selectOneById(@Param("hawbId") Long hawbId,@Param("deptId") Long deptId);

    /**
     * 国内分单查询
     * @param query 国内分单查询参数
     * @return 结果
     */
    List<Hawb> queryList(HawbQuery query);

    /**
     * 生成账单
     * @param ids 国内分单主键id
     * @return 结果
     */
    List<Hawb> selectByIds(@Param("ids") Long[] ids,@Param("deptId") Long deptId);

    /**
     * 更新运单账单流水号
     * @param list 国内分单主键id集合
     * @param serialNo 账单流水号
     * @return 结果
     */
    int updateSerialNo(@Param("list") List<Long> list,@Param("serialNo") String serialNo);

    /**
     * 根据流水号查询运单列表
     * @param serialNo 账单流水号
     * @return 运单列表
     */
    List<HawbBillVo> seelectHawbList(String serialNo);

    /**
     * 根据分单id查询对应主单信息
     * @param id 分单id
     * @return 主单信息
     * */
    Mawb selectMawbInfoById(Long id);

    List<HawbQueryVo1> queryVos(HawbQuery query);

    Integer queryListCount(@Param("query") HawbQuery query,@Param("page") Page<HawbQueryVo1> page);
    Integer queryListQuantity(@Param("query") HawbQuery query,@Param("page") Page<HawbQueryVo1> page);
    BigDecimal queryListWeight(@Param("query") HawbQuery query,@Param("page") Page<HawbQueryVo1> page);
    Page<HawbQueryVo1> queryVosList(@Param("query") HawbQuery query,@Param("page") Page<HawbQueryVo1> page);
}
