package com.gzairports.wl.reporter.domain.query;

import lombok.Data;

import java.time.LocalDate;

@Data
public class GoodsWeightStatisticsQuery {

    /**
     * 统计方式 M-按月 D-按天（默认）
     */
    private String statisticsType;

    /**
     * 开单日期开始时间
     */
    private LocalDate startTime;

    /**
     * 开单日期结束时间
     */
    private LocalDate endTime;

    /**
     * 始发站
     */
    private String sourcePort;

    /**
     * 目的站
     */
    private String desPort;

    /**
     * 运单类型 AWBM-邮件单 AWBA-主单 HAWB-分单
     * 国际分单
     * 国际主单
     */
    private String waybillType;

    /**
     * 发货人
     */
    private String shipper;

    /**
     * 品名
     */
    private String cargoName;

    /**
     * 中转类型，1-全部 2-非中转 3-换单中转 4-通单中转
     */
    private String transferType;

    /**
     * 当前页
     */
    private Integer pageNum = 1;

    /**
     * 每页数量
     */
    private Integer pageSize = 10;
}
