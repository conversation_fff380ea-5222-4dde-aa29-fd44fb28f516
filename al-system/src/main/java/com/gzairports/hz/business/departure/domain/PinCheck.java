package com.gzairports.hz.business.departure.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 开箱抽查表
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Data
@TableName("hz_dep_pin_check")
public class PinCheck {

    /** 主键id */
    private Long id;

    /** 运单id */
    private Long waybillId;

    /** 用户id */
    private Long userId;

    /** 完成状态（1已完成，0未完成） */
    private Integer checkStatus;

    /** 开箱件数 */
    private Integer openedPieces;

    /** 运输文件是否齐全 0 否 1 是  */
    private Integer documentsComplete;

    /** 包装是否符合要求 0 否 1 是 */
    private Integer packagingCompliant;

    /** 申报是否与实际一致 0 否 1 是*/
    private Integer declarationConsistent;

    /** 备注 */
    private String remark;

    /** 检查图片 */
    private String inspectImages;

    /** 检查时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date inspectTime;

    /** 是否删除 0 否 1 是 */
    private Integer isDel;
}
