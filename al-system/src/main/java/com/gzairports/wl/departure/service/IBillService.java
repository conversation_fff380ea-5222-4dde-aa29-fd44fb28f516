package com.gzairports.wl.departure.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.wl.departure.domain.Bill;
import com.gzairports.wl.departure.domain.query.BillQuery;
import com.gzairports.wl.departure.domain.vo.BillQueryVo;
import com.gzairports.wl.departure.domain.vo.BillVo;

import java.util.List;

/**
 * 国内分单账单管理Service接口
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
public interface IBillService extends IService<Bill> {

    /**
     * 查询国内分单账单
     * @param query 查询参数
     * @return 国内分单账单数据
     */
    BillQueryVo selectListByQuery(BillQuery query);

    /**
     * 国内分单账单详情
     * @param id 国内分单账单id
     * @return 详情
     */
    BillVo getInfo(Long id);

    /**
     * 支付审核
     * @param bill 账单数据
     * @return 结果
     */
    int edit(Bill bill);

    /**
     * 删除分单账单数据
     * @param id 账单id
     * @return 结果
     */
    int del(Long id);

    /**
     * 导出国内分单账单
     * @param query 查询参数
     * @return 国内分单账单数据
     */
    List<BillVo> selectList(BillQuery query);
}
