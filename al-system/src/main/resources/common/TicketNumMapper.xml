<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.wl.ticket.mapper.TicketNumMapper">
    <update id="cancelByQuery" parameterType="ticketNumQuery">
       update wl_ticket_num set status = 'CANCEL' where ticket_id = #{id} and num BETWEEN #{startNum} and #{endNum}
    </update>
    <update id="grant" parameterType="ticketNumQuery">
        update wl_ticket_num
        set
            status = 'GRANT',
            record_id = #{id},
            <if test="query.useBy != null">
                use_by = #{query.useBy},
            </if>
            dept_id = #{query.deptId}
        where
            ticket_id = #{query.id} and
            num BETWEEN #{query.startNum} and #{query.endNum}
    </update>
    <update id="updateStatus">
        update wl_ticket_num set status = 'NOTGRANT' where status = 'GRANT' and record_id = #{recordId}
    </update>
    <update id="reissue">
        update wl_ticket_num set status = 'GRANT' where status = 'NOTGRANT' and record_id = #{recordId}
    </update>
    <select id="selectByTicketId" resultType="java.lang.Integer">
        select num from wl_ticket_num where status = #{status} and ticket_id = #{id}
    </select>
    <select id="selectListById" resultType="com.gzairports.wl.ticket.domain.TicketNum">
        select wtn.id, wtn.num, wtn.code, wtn.status, su.user_name, wtn.use_time from wl_ticket_num wtn
            left join sys_user su on su.user_id = wtn.use_by
        where wtn.record_id = #{recordId}
    </select>
</mapper>