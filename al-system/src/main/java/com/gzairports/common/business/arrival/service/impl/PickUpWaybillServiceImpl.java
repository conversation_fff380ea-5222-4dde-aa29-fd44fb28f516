package com.gzairports.common.business.arrival.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.business.arrival.domain.PickUp;
import com.gzairports.common.business.arrival.domain.PickUpWaybill;
import com.gzairports.common.business.arrival.domain.vo.H5OutInfoVo;
import com.gzairports.common.business.arrival.domain.vo.OutOrderWaybillVo;
import com.gzairports.common.business.arrival.mapper.PickUpWaybillMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 办理提货运单关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-03
 */
@Service
public class PickUpWaybillServiceImpl extends ServiceImpl<PickUpWaybillMapper, PickUpWaybill> {


    @Autowired
    private PickUpWaybillMapper pickUpWaybillMapper;

    /**
     * 根据办理提货id查询提货运单
     * @param pickUpId 办理提货id
     * @return 提货运单列表
     */
    public List<OutOrderWaybillVo> selectOutOrderById(Long pickUpId) {
        return pickUpWaybillMapper.selectOutOrderById(pickUpId);
    }

    /**
     * 根据理货id集合查询已办单数据
     * @param ids 理货id集合
     * @return 已办单数据
     */
    public List<Long> selectTallyIds(List<Long> ids) {
        return pickUpWaybillMapper.selectTallyIds(ids);
    }

    /**
     * 根据提货id查询数据
     * @param tallyId 提货id
     * @return 数据
     */
    public PickUp selectTallyId(Long tallyId) {
        return pickUpWaybillMapper.selectTallyId(tallyId);
    }

    /**
     * H5办单详情
     * @param pickUpId 办单id
     * @return 详情
     */
    public List<H5OutInfoVo> selectH5Info(Long pickUpId) {
        return pickUpWaybillMapper.selectH5Info(pickUpId);
    }

    /**
     * 根据办单id查询提货运单
     * @param id 办单id
     * @return 办单运单
     */
    public List<PickUpWaybill> selectWaybill(Long id) {
        return pickUpWaybillMapper.selectWaybill(id);
    }
}
