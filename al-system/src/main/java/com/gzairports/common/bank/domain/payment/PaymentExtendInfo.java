package com.gzairports.common.bank.domain.payment;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * @author: lan
 * @Desc: 网关支付扩展字段
 * @create: 2025-03-18 11:43
 **/

@Data
@XmlRootElement(name = "extendInfo")
@XmlAccessorType(XmlAccessType.FIELD)
public class PaymentExtendInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 服务点方式 5 C
     * 1285、1286通道必输字段
     * 00-手工输入；01-主扫；02-被扫
     */
    @XmlElement(name = "certifyMode")
    private String certifyMode;

    /** 交易类型 4 N
     */
    @XmlElement(name = "trxType")
    private String trxType;

    /** 付款方终端类型 4 C
     * 1285、1286通道必输字段
     * 07-电脑；08-手机；10-平板电脑；18-可穿戴设备；19-数字电视；00-其他
     */
    @XmlElement(name = "payerTermType")
    private String payerTermType;

    /** 付款方终端编码 32 N
     */
    @XmlElement(name = "payerTermNo")
    private String payerTermNo;

    /** 网络交易平台名称 128 N
     */
    @XmlElement(name = "merPlatformName")
    private String merPlatformName;

    /** 商户订单号 40 C
     * 按照银联网联要求，标示机构内订单号，合并支付为合并订单号，只支持数字或字母，保证唯一
     */
    @XmlElement(name = "merOrderNo")
    private String merOrderNo;

    /** 商户订单详情 600 C
     * 数字货币H5、二维码必输字段，上送商品名称或者商品简单描述，长度小于600字节
     */
    @XmlElement(name = "merOrderDesc")
    private String merOrderDesc;

    /** 营销类型 2 N
     */
    @XmlElement(name = "prmtTp")
    private String prmtTp;

    /** 营销信息 256 N
     */
    @XmlElement(name = "prmtInfo")
    private String prmtInfo;

    /** 业务种类 10 Y
     * 参考附录12
     */
    @XmlElement(name = "busiKind")
    private String busiKind;

    /** 设备型号 256 N
     */
    @XmlElement(name = "devMode")
    private String devMode;

    /** 设备语言 10 N
     */
    @XmlElement(name = "devLang")
    private String devLang;

    /** IP地址 64 Y
     * 填写受理终端IP地址
     */
    @XmlElement(name = "userIp")
    private String userIp;

    /** 交易地点 128 C
     * 该字段为数字货币H5、二维码支付类型必输字段，禁止中文，实体特约商户填写商户受理终端ip地址，网络特约商户填写网络交易平台网络地址
     */
    @XmlElement(name = "tradePlace")
    private String tradePlace;

    /** MAC地址 64 N
     */
    @XmlElement(name = "mac")
    private String mac;

    /** 设备号 129 N
     */
    @XmlElement(name = "devId")
    private String devId;

    /** GPS位置 32 N
     */
    @XmlElement(name = "devGPS")
    private String devGPS;

    /** SIM卡号码 32 N
     */
    @XmlElement(name = "devNum")
    private String devNum;

    /** SIM卡数量 8 N
     */
    @XmlElement(name = "devSIMNum")
    private Integer devSIMNum;

    /** 账户ID 64 N
     */
    @XmlElement(name = "accHashId")
    private String accHashId;

    /** 风险评分 8 N
     */
    @XmlElement(name = "riskScore")
    private Integer riskScore;

    /** 原因码 100 N
     */
    @XmlElement(name = "riskResCode")
    private String riskResCode;

    /** 商户端用户注册日期 14 N
     */
    @XmlElement(name = "mchntUsrRgstrTm")
    private String mchntUsrRgstrTm;

    /** 收单端注册账户邮箱地址 64 N
     */
    @XmlElement(name = "mchntUsrRgstrEmail")
    private String mchntUsrRgstrEmail;

    /** 收货省 4 N
     */
    @XmlElement(name = "rcvProv")
    private String rcvProv;

    /** 收货市 6 N
     */
    @XmlElement(name = "city")
    private String city;

    /** 商品类型 2 N
     */
    @XmlElement(name = "goodsClass")
    private String goodsClass;

    /** 受理渠道 2 Y
     * 01-电脑浏览器；02-手机浏览器；03-手机应用程序；99-其他
     */
    @XmlElement(name = "gwChnlTp")
    private String gwChnlTp;

    /** 限定账户类型 6 N
     * 00-混合；01-贷记；02-借记
     */
    @XmlElement(name = "resaccType")
    private String resaccType;



}
