<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.SpecialCodeMapper">
    <insert id="insertSpecialCode" parameterType="baseSpecialCode">
        insert into base_special_code
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="code != null and code != ''">code,</if>
            <if test="isDanger != null">is_danger,</if>
            <if test="chineseDescription != null and chineseDescription != ''">chinese_description,</if>
            <if test="englishDescription != null and englishDescription != ''">english_description,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="code != null and code != ''">#{code},</if>
            <if test="isDanger != null">#{isDanger},</if>
            <if test="chineseDescription != null and chineseDescription != ''">#{chineseDescription},</if>
            <if test="englishDescription != null and englishDescription != ''">#{englishDescription},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>
    <update id="updateSpecialCode" parameterType="baseSpecialCode">
        update base_special_code
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="isDanger != null">is_danger = #{isDanger},</if>
            <if test="chineseDescription != null and chineseDescription != ''">chinese_description = #{chineseDescription},</if>
            <if test="englishDescription != null and englishDescription != ''">english_description = #{englishDescription},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null and createTime != ''">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="delSpecialCode" parameterType="long">
        update base_special_code set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <select id="selectSpecialCodeList" resultType="com.gzairports.common.basedata.domain.BaseSpecialCode">
        select id, code, is_danger, chinese_description, english_description from base_special_code
        where is_del = 0
        <if test="ids != null  and ids.size() > 0"> and id in
            <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="code != null  and code != ''"> and code like concat('%',#{code},'%')</if>
        <if test="isDanger != null"> and is_danger = #{isDanger}</if>
    </select>
    <select id="selectSpecialByCode" resultType="com.gzairports.common.basedata.domain.BaseSpecialCode">
        select id, code, is_danger, chinese_description, english_description
        from base_special_code where code = #{code} and is_del = 0
    </select>
    <select id="selectCodeList" resultType="java.lang.String">
        select code from base_special_code where is_del = 0
    </select>
    <select id="selectChineseDescriptionBySpecialCargoCode" resultType="java.lang.String">
        select chinese_description from base_special_code where code = #{specialCargoCode} and is_del = 0
    </select>
</mapper>