package com.gzairports.hz.business.departure.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.BaseAgent;
import com.gzairports.common.basedata.domain.BaseBalance;
import com.gzairports.common.basedata.domain.BaseCargoCategory;
import com.gzairports.common.basedata.domain.BaseCargoCode;
import com.gzairports.common.basedata.mapper.BaseAgentMapper;
import com.gzairports.common.basedata.mapper.BaseBalanceMapper;
import com.gzairports.common.basedata.mapper.CargoCategoryMapper;
import com.gzairports.common.basedata.mapper.CargoCodeMapper;
import com.gzairports.common.business.arrival.domain.HzArrItem;
import com.gzairports.common.business.arrival.domain.HzArrTally;
import com.gzairports.common.business.arrival.mapper.FlightInfoMapper;
import com.gzairports.common.business.arrival.mapper.HzArrItemMapper;
import com.gzairports.common.business.arrival.mapper.HzArrItemMapper;
import com.gzairports.common.business.arrival.mapper.HzArrTallyMapper;
import com.gzairports.common.business.departure.domain.CostDetail;
import com.gzairports.common.business.departure.domain.FlightInfo;
import com.gzairports.common.business.departure.domain.vo.FlightVo;
import com.gzairports.common.business.departure.domain.vo.IdsVo;
import com.gzairports.common.business.departure.mapper.CostDetailMapper;
import com.gzairports.common.business.departure.mapper.CostDetailMapper;
import com.gzairports.common.charge.domain.HzChargeIrRelation;
import com.gzairports.common.charge.domain.HzChargeItemRule;
import com.gzairports.common.charge.domain.HzChargeItems;
import com.gzairports.common.charge.domain.HzChargeRule;
import com.gzairports.common.charge.domain.vo.BillRuleVo;
import com.gzairports.common.charge.domain.vo.HzChargeItemsVo;
import com.gzairports.common.charge.domain.vo.IrRelationVo;
import com.gzairports.common.charge.mapper.HzChargeIrRelationMapper;
import com.gzairports.common.charge.mapper.HzChargeRuleMapper;
import com.gzairports.common.charge.service.BillingRule;
import com.gzairports.common.charge.service.impl.BillingRuleFactory;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.common.log.service.IWaybillLogService;
import com.gzairports.common.serviceRequest.domain.ServiceRequest;
import com.gzairports.common.serviceRequest.mapper.ServiceRequestMapper;
import com.gzairports.common.utils.BigDecimalRoundUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.ServletUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.hz.business.arrival.domain.HzArrRecordOrder;
import com.gzairports.hz.business.arrival.mapper.HzArrRecordOrderMapper;
import com.gzairports.hz.business.departure.domain.AirWaybill;
import com.gzairports.hz.business.departure.domain.HzColdExamine;
import com.gzairports.hz.business.departure.domain.HzColdRegister;
import com.gzairports.hz.business.departure.domain.HzCollectWaybill;
import com.gzairports.hz.business.departure.domain.query.HzColdRegisterQuery;
import com.gzairports.hz.business.departure.domain.vo.ColdQueryVo;
import com.gzairports.hz.business.departure.domain.vo.ColdRegisterVo;
import com.gzairports.hz.business.departure.domain.vo.HzColdRegisterVo;
import com.gzairports.hz.business.departure.domain.vo.TotalChargeVo;
import com.gzairports.hz.business.departure.mapper.AllAirWaybillMapper;
import com.gzairports.hz.business.departure.mapper.HzColdExamineMapper;
import com.gzairports.hz.business.departure.mapper.HzColdRegisterMapper;
import com.gzairports.hz.business.departure.mapper.HzCollectWaybillMapper;
import com.gzairports.hz.business.departure.service.IHzColdRegisterService;
import com.gzairports.common.charge.mapper.HzChargeItemRuleMapper;
import com.gzairports.common.charge.mapper.HzChargeItemsMapper;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 冷藏登记Service业务层处理
 * <AUTHOR>
 * @date 2024-07-12
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class HzColdRegisterServiceImpl extends ServiceImpl<HzColdRegisterMapper, HzColdRegister> implements IHzColdRegisterService {

    @Autowired
    private HzColdRegisterMapper registerMapper;

    @Autowired
    private AllAirWaybillMapper airWaybillMapper;

    @Autowired
    private ServiceRequestMapper serviceRequestMapper;

    @Autowired
    private HzChargeItemsMapper hzChargeItemsMapper;

    @Autowired
    private HzChargeItemsMapper chargeItemsMapper;

    @Autowired
    private HzChargeItemRuleMapper itemRuleMapper;

    @Autowired
    private HzChargeIrRelationMapper relationMapper;

    @Autowired
    private HzArrItemMapper arrItemMapper;

    @Autowired
    private CostDetailMapper costDetailMapper;

    @Autowired
    private HzArrItemMapper itemMapper;

    @Autowired
    private BaseAgentMapper baseAgentMapper;

    @Autowired
    private BaseBalanceMapper baseBalanceMapper;

    @Autowired
    private HzArrTallyMapper tallyMapper;

    @Autowired
    private HzArrRecordOrderMapper recordOrderMapper;

    @Autowired
    private CargoCodeMapper cargoCodeMapper;

    @Autowired
    private HzCollectWaybillMapper collectWaybillMapper;

    @Autowired
    private IWaybillLogService waybillLogService;

    /**
     * 查询冷藏登记列表
     * @param query 查询参数
     * @return 冷藏登记列表
     */
    @Override
    public HzColdRegisterVo selectList(HzColdRegisterQuery query) {
        HzColdRegisterVo vo = new HzColdRegisterVo();
        List<ColdQueryVo> voList = registerMapper.selectNewListByQuery(query);
        if (!CollectionUtils.isEmpty(voList)){
            List<ColdQueryVo> coldRegisters = voList.stream().filter(e -> e.getStatus().equals(0)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(coldRegisters)){
                vo.setColdNum(coldRegisters.size());
            }
            List<ColdQueryVo> out = voList.stream().filter(e -> e.getStatus().equals(2)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(out)){
                vo.setOutNum(out.size());
                BigDecimal reduce = out.stream().map(ColdQueryVo::getSumMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (reduce != null){
                    vo.setOutMoney(reduce);
                }
            }
           vo.setList(voList);
        }
        return vo;
    }

    /**
     * 导出冷藏登记数据
     * @param query 查询条件
     * @return 结果
     */
    @Override
    public List<ColdQueryVo> selectListByQuery(HzColdRegisterQuery query) {
        return registerMapper.selectNewListByQuery(query);
    }

    /**
     * 新增冷藏登记
     * @param register 冷藏登记参数
     * @return 结果
     */
    @Override
    public int add(HzColdRegister register) {
        HttpServletResponse response = ServletUtils.getResponse();
        WaybillLog waybillLog = new WaybillLog();
        try{
            //2.13 冷藏登记入库时间取 支付提交时的时间
            register.setWareTime(new Date());
            Integer count = registerMapper.selectCount(new QueryWrapper<HzColdRegister>()
                    .eq("waybill_code", register.getWaybillCode())
                    .eq("type", register.getType()));
            if (count != 0){
                throw new CustomException("当前运单存在冷藏数据");
            }
            AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                    .eq("waybill_code", register.getWaybillCode())
                    .eq("type", register.getType())
                    .eq("is_del", 0)
                    .eq("dept_id",1));
            if (airWaybill == null){
                throw new CustomException("当前运单不是散客运单");
            }
            if ("ARR".equals(register.getType())){
                String port = airWaybill.getSourcePort() + "-KWE";
                HzArrRecordOrder orderId = recordOrderMapper.selectId(port,airWaybill.getWaybillCode());
                if (orderId != null){
                    HzArrItem hzArrItem = new HzArrItem();
                    hzArrItem.setWaybillCode(register.getWaybillCode());
                    hzArrItem.setIrId(register.getRelationId());
                    hzArrItem.setTotalCharge(register.getSumMoney());
                    hzArrItem.setStoreStartTime(register.getWareTime());
                    hzArrItem.setStoreEndTime(register.getOutTime());
                    hzArrItem.setColdStore(register.getColdStore());
                    hzArrItem.setOrderId(orderId.getId());
                    hzArrItem.setServiceType(2);
                    hzArrItem.setIsSettle(0);
                    HzArrTally hzArrTally = tallyMapper.selectOne(new QueryWrapper<HzArrTally>()
                            .eq("waybill_code", airWaybill.getWaybillCode())
                            .eq("record_order_id", orderId.getId()));
                    if (hzArrTally != null){
                        hzArrItem.setTallyId(hzArrTally.getId());
                    }
                    arrItemMapper.insert(hzArrItem);
                }
            }
            //出港的冷藏登记也需要
            if ("DEP".equals(register.getType())){
                CostDetail detail = new CostDetail();
                detail.setWaybillCode(register.getWaybillCode());
                detail.setColdStore(register.getColdStore());
                detail.setIrId(register.getRelationId());
                detail.setTotalCharge(register.getSumMoney());
                detail.setStoreStartTime(register.getWareTime());
                detail.setStoreEndTime(register.getOutTime());
                detail.setDeptId(airWaybill.getDeptId());
                detail.setServiceType(2);
                detail.setType(0);
                detail.setIsSettle(0);
                costDetailMapper.insert(detail);
            }
            register.setStatus(0);
            register.setApplyTime(new Date());
            register.setApplyUser(SecurityUtils.getNickName());
            if (register.getDeptId() == null){
                register.setDeptId(airWaybill.getDeptId());
            }

            //运单日志的新增
            waybillLog = waybillLogService.getWaybillLog(
                    airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                    null, null, null,
                    register, null, 0, null, new Date(),
                    "冷藏登记 新增,申请时长"+register.getTimeLen()+",支付金额:"+register.getSumMoney(),
                    register.getType(), null);
            register.setUpdateTime(new Date());
            return registerMapper.insert(register);
        }catch (Exception e){
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" +  "," +
                            "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        }finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }

    }

    @Nullable
    private HzChargeIrRelation getHzChargeIrRelation(AirWaybill airWaybill, HzChargeItems hzChargeItems) {
        List<HzChargeIrRelation> relations = relationMapper.selectColdCharge(hzChargeItems.getId());
        List<HzChargeIrRelation> ruleList = getHzChargeIrRelations(airWaybill, relations);
        if (CollectionUtils.isEmpty(ruleList)) {
            return null;
        }
        return ruleList.stream().max(Comparator.comparing(HzChargeIrRelation::getPriority)).orElse(null);
    }

    /**
     * 根据运单号查询品名
     * @param waybillCode 运单号
     * @param type 进出港类型
     * @return 品名
     */
    @Override
    public HzColdRegister selectCargoName(String waybillCode,String type, Long deptId) {
        HzColdRegister hzColdRegister = airWaybillMapper.selectCargoName(waybillCode, type, deptId);
        if (hzColdRegister == null){
            throw new CustomException("该运单不存在");
        }
        if ("DEP".equals(type)){
            HzCollectWaybill collectWaybill = collectWaybillMapper.selectOne(new QueryWrapper<HzCollectWaybill>()
                    .eq("waybill_id", hzColdRegister.getId())
                    .orderByDesc("collect_time")
                    .last("limit 1"));
            if (collectWaybill == null){
                throw new CustomException("当前运单未收运入库");
            }
            hzColdRegister.setWareTime(collectWaybill.getCollectTime());
        }else {
            HzArrTally hzArrTally = tallyMapper.selectOne(new QueryWrapper<HzArrTally>()
                    .eq("waybill_code", waybillCode)
                    .orderByDesc("tally_time")
                    .last("limit 1"));
            if (hzArrTally == null){
                throw new CustomException("当前运单未理货");
            }
            hzColdRegister.setWareTime(hzArrTally.getTallyTime());
        }
        hzColdRegister.setId(null);
        return hzColdRegister;
    }

    /**
     * 根据类型以及计费时间计算计费金额
     * @param vo 计费数据
     * @return 结果
     */
    @Override
    public TotalChargeVo countSum(ColdRegisterVo vo) {
        TotalChargeVo totalChargeVo = new TotalChargeVo();
        AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                .eq("waybill_code", vo.getWaybillCode())
                .eq("type", vo.getType())
                .eq("is_del", 0));
        Date date = new Date();
        if ("ARR".equals(vo.getType())){
            String port = airWaybill.getSourcePort() + "-KWE";
            HzArrRecordOrder orderId = recordOrderMapper.selectId(port,airWaybill.getWaybillCode());
            if (orderId == null){
                throw new CustomException("无当前航班下运单的库单信息");
            }
        }
        List<HzChargeItems> hzChargeItems = chargeItemsMapper.selectList(new QueryWrapper<HzChargeItems>()
                .eq("operation_type",vo.getType())
                .le("start_effective_time",date)
                .ge("end_effective_time",date)
                .eq("is_del",0));
        BigDecimal costSum = new BigDecimal(0);
        for (HzChargeItems hzChargeItem : hzChargeItems) {
            HzChargeIrRelation relation = getHzChargeIrRelation(airWaybill, hzChargeItem);
            if (relation == null) continue;
            totalChargeVo.setRelationId(relation.getId());
            List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>().eq("ir_id",relation.getId()));
            if (CollectionUtils.isEmpty(itemRules)){
                totalChargeVo.setCostSum("0");
                return totalChargeVo;
            }
            long millisInADay = vo.getTimeLen() * 60 * 60 * 1000L;
            Date plusHours = new Date(vo.getStoreStartTime().getTime() + millisInADay);
            HzArrItem item = new HzArrItem();
            item.setColdStore(vo.getColdStore());
            item.setStoreStartTime(vo.getStoreStartTime());
            item.setStoreEndTime(plusHours);
            BillingRule rule = BillingRuleFactory.createRule("ColdStorageBillingRule.class");
            BillRuleVo vo1 = rule.calculateFee(itemRules, airWaybill.getChargeWeight(), 1, item);
            BigDecimal bigDecimal = BigDecimalRoundUtils.bigDecimalRound(hzChargeItem.getRoundRule(), vo1.getTotalCharge());
            costSum = costSum.add(bigDecimal);
        }
        totalChargeVo.setCostSum(costSum.toString());
        return totalChargeVo;
    }

    /**
     * 查看详情接口
     * @param id 登记id
     * @return 详情
     */
    @Override
    public ColdQueryVo getInfo(Long id) {
        ColdQueryVo vo = new ColdQueryVo();
        HzColdRegister hzColdRegister = registerMapper.selectById(id);
        BeanUtils.copyProperties(hzColdRegister,vo);
        return vo;
    }

    /**
     * 查询冷库计费项目
     * @return 结果
     */
    @Override
    public List<HzChargeItemsVo> selectColdItem() {
        return hzChargeItemsMapper.selectColdItem();
    }

    @Override
    public List<String> getWaybillCodeByFour(String waybillCode) {
        return registerMapper.getWaybillCodeByFour(waybillCode);
    }

    @Override
    public int out(ColdQueryVo vo) {
        HttpServletResponse response = ServletUtils.getResponse();
        WaybillLog waybillLog = new WaybillLog();
        try{
        HzColdRegister hzColdRegister = registerMapper.selectById(vo.getId());
        BeanUtils.copyProperties(vo,hzColdRegister);
        AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                .eq("waybill_code", hzColdRegister.getWaybillCode())
                .eq("type", hzColdRegister.getType())
                .eq("dept_id", hzColdRegister.getDeptId()));
        hzColdRegister.setStatus(2);
        BigDecimal oldSum = new BigDecimal(0);
        if ("DEP".equals(hzColdRegister.getType())){
            List<CostDetail> details = costDetailMapper.selectList(new QueryWrapper<CostDetail>()
                    .eq("waybill_code", hzColdRegister.getWaybillCode())
                    .eq("dept_id", hzColdRegister.getDeptId())
                    .eq("service_type", 2));
            if (!CollectionUtils.isEmpty(details)) {
                oldSum = details.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                for (CostDetail detail : details) {
                    detail.setStoreEndTime(vo.getOutTime());
                    detail.setIsSettle(1);
                    detail.setTotalCharge(vo.getSumMoney());
                    detail.setQuantity(vo.getChargeTime().toString());
                    if (airWaybill != null){
                        detail.setSettleDepQuantity(airWaybill.getQuantity());
                    }
                    detail.setSettleDepWeight(vo.getUseTime());
                    detail.setType(1);
                    detail.setCreateTime(new Date());
                    detail.setFlightId(2L);
                    detail.setIsAuto(1);
                    detail.setId(null);
                    costDetailMapper.insert(detail);
                }
            }
        }else {
            List<HzArrItem> items = itemMapper.selectList(new QueryWrapper<HzArrItem>()
                    .eq("waybill_code", hzColdRegister.getWaybillCode())
                    .eq("service_type", 2));
            if (!CollectionUtils.isEmpty(items)) {
                oldSum = items.stream().map(HzArrItem::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                for (HzArrItem item : items) {
                    item.setStoreEndTime(vo.getOutTime());
                    item.setTotalCharge(vo.getSumMoney());
                    item.setIsSettle(1);
                    item.setIsAuto(1);
                    itemMapper.updateById(item);
                }
            }
        }
        if (hzColdRegister.getDeptId() == 1){
            return registerMapper.updateById(hzColdRegister);
        }
        BaseAgent agent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", hzColdRegister.getDeptId()));
        if (vo.getSumMoney().compareTo(oldSum) > 0){
            BigDecimal deduct = vo.getSumMoney().subtract(oldSum);
            if (agent != null) {
                if (agent.getSettleMethod() == 1) {
                    BigDecimal balance = agent.getBalance() == null ? new BigDecimal(0) : agent.getBalance();
                    BigDecimal subtract = balance.subtract(deduct);
                    if (subtract.compareTo(new BigDecimal(0)) < 0) {
                        throw new CustomException("当前代理人余额不足");
                    } else {
                        agent.setBalance(subtract);
                        baseAgentMapper.updateBaseAgent(agent);
                        BaseBalance baseBalance = new BaseBalance();
                        baseBalance.setAgentId(agent.getId());
                        baseBalance.setBalance(agent.getBalance());
                        baseBalance.setType("减少余额");
                        baseBalance.setCreateTime(new Date());
                        baseBalance.setCreateBy("系统");
                        // todo 流水号需从银联支付接口获取
                        //baseBalance.setSerialNo();
                        baseBalance.setTradeMoney(deduct);
                        baseBalance.setWaybillCode(hzColdRegister.getWaybillCode());
                        baseBalance.setRemark("冷藏申请支付");
                        baseBalance.setAddType(2);
                        baseBalanceMapper.insertBaseBalance(baseBalance);
                        hzColdRegister.setPayStatus(6);
                    }
                } else if (agent.getSettleMethod() == 0) {
                    hzColdRegister.setPayStatus(5);
                } else {
                    if (agent.getPayMethod() == 0) {
                        hzColdRegister.setPayStatus(7);
                    } else {
                        hzColdRegister.setPayStatus(8);
                    }
                }
            } else {
                hzColdRegister.setPayStatus(8);
            }
        }else if (vo.getSumMoney().compareTo(oldSum) == 0){
            if (agent != null) {
                if (agent.getSettleMethod() == 1) {
                    hzColdRegister.setPayStatus(6);
                } else if (agent.getSettleMethod() == 0) {
                    hzColdRegister.setPayStatus(5);
                } else {
                    if (agent.getPayMethod() == 0) {
                        hzColdRegister.setPayStatus(7);
                    } else {
                        hzColdRegister.setPayStatus(8);
                    }
                }
            } else {
                hzColdRegister.setPayStatus(8);
            }
        }else {
            BigDecimal add = oldSum.subtract(vo.getSumMoney());
            if (agent != null){
                if (agent.getSettleMethod() == 1) {
                    BigDecimal balance = agent.getBalance() == null ? new BigDecimal(0) : agent.getBalance();
                    BigDecimal subtract = balance.add(add);
                    agent.setBalance(subtract);
                    baseAgentMapper.updateBaseAgent(agent);
                    BaseBalance baseBalance = new BaseBalance();
                    baseBalance.setAgentId(agent.getId());
                    baseBalance.setBalance(agent.getBalance());
                    baseBalance.setType("增加余额");
                    baseBalance.setCreateTime(new Date());
                    baseBalance.setCreateBy("系统");
                    // todo 流水号需从银联支付接口获取
                    //baseBalance.setSerialNo();
                    baseBalance.setTradeMoney(add);
                    baseBalance.setWaybillCode(hzColdRegister.getWaybillCode());
                    baseBalance.setRemark("冷藏申请退款");
                    baseBalanceMapper.insertBaseBalance(baseBalance);
                    hzColdRegister.setPayStatus(10);
                } else if (agent.getSettleMethod() == 0){
                    hzColdRegister.setPayStatus(9);
                }else {
                    if (agent.getPayMethod() == 0){
                        hzColdRegister.setPayStatus(11);
                    }else {
                        hzColdRegister.setPayStatus(12);
                    }
                }
            }else {
                hzColdRegister.setPayStatus(12);
            }
        }
        //运单日志
        waybillLog = waybillLogService.getWaybillLog(
                hzColdRegister.getWaybillCode(), 0, SecurityUtils.getNickName(),
                null, null, null,
                hzColdRegister, null, 0, null, new Date(),
                "冷藏登记 手动出库,时间使用时长:" + hzColdRegister.getUseTime() + ",金额"+ hzColdRegister.getSumMoney(),
                hzColdRegister.getType(), null);
        return registerMapper.updateById(hzColdRegister);
        }catch (Exception e){
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" +  "," +
                            "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        }finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }

    @Override
    public int ware(Long id) {
        HzColdRegister hzColdRegister = registerMapper.selectById(id);
        hzColdRegister.setStatus(1);
        hzColdRegister.setUpdateTime(new Date());
        return registerMapper.updateById(hzColdRegister);
    }


    @Async
    public void addColdRegister(AirWaybill airWaybill) {
        Calendar calendar = Calendar.getInstance();
        Date wareTime = calendar.getTime();
        calendar.add(Calendar.HOUR_OF_DAY,12);
        Date outTime = calendar.getTime();
        if (!"SIMPLE".equals(airWaybill.getMakeType()) && !"ARR".equals(airWaybill.getType())){
            ServiceRequest serviceRequest = serviceRequestMapper.selectOne(new QueryWrapper<ServiceRequest>()
                    .eq("waybill_code", airWaybill.getWaybillCode())
                    .like("service_item", airWaybill.getColdStore()));
            //这里用likeleft查询不到->冷藏库与冷藏库冷藏
            serviceRequest.setBeginTime(wareTime);
            serviceRequest.setUpdateTime(new Date());
            serviceRequestMapper.updateById(serviceRequest);
        }
        HzColdRegister hzColdRegister = new HzColdRegister();
        hzColdRegister.setWaybillCode(airWaybill.getWaybillCode());
        hzColdRegister.setCargoName(airWaybill.getCargoName());
        hzColdRegister.setColdStore(airWaybill.getColdStore());
        hzColdRegister.setWareTime(wareTime);
        hzColdRegister.setOutTime(outTime);
        hzColdRegister.setType(airWaybill.getType());
        hzColdRegister.setUseTime(new BigDecimal(12));
        hzColdRegister.setChargeTime(new BigDecimal(12));
        hzColdRegister.setStatus(0);
        if ("DEP".equals(airWaybill.getType())){
            List<HzChargeItems> hzChargeItems1 = chargeItemsMapper.selectList(new QueryWrapper<HzChargeItems>()
                    .eq("operation_type","DEP")
                    .le("start_effective_time",airWaybill.getWriteTime())
                    .ge("end_effective_time",airWaybill.getWriteTime())
                    .eq("is_del",0));
            for (HzChargeItems hzChargeItems : hzChargeItems1) {
                HzChargeIrRelation relation = getHzChargeIrRelation(airWaybill, hzChargeItems);
                if (relation == null) continue;
                List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>().eq("ir_id",relation.getId()));
                countSum(airWaybill, wareTime, outTime, hzColdRegister, itemRules,relation.getItemId());
                CostDetail costDetail = new CostDetail();
                costDetail.setIrId(relation.getId());
                costDetail.setWaybillCode(airWaybill.getWaybillCode());
                costDetail.setTotalCharge(hzColdRegister.getSumMoney());
                costDetail.setColdStore(airWaybill.getColdStore());
                costDetail.setStoreStartTime(wareTime);
                costDetail.setStoreEndTime(outTime);
                costDetail.setDeptId(airWaybill.getDeptId());
                costDetail.setQuantity(airWaybill.getQuantity().toString());
                costDetailMapper.insert(costDetail);
            }
        }
        if ("ARR".equals(airWaybill.getType())){
            FlightVo vo = recordOrderMapper.selectFlightById(airWaybill.getOrderId());
            List<HzChargeItems> hzChargeItems1 = chargeItemsMapper.selectList(new QueryWrapper<HzChargeItems>()
                    .eq("operation_type","ARR")
                    .le("start_effective_time",vo.getOrderTime())
                    .ge("end_effective_time",vo.getOrderTime())
                    .eq("is_del",0).eq("status",1));
            for (HzChargeItems hzChargeItems : hzChargeItems1) {
                HzChargeIrRelation relation = getHzChargeIrRelation(airWaybill, hzChargeItems);
                if (relation == null) continue;
                List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>().eq("ir_id",relation.getId()).eq("is_del",0));
                countSum(airWaybill, wareTime, outTime, hzColdRegister, itemRules,relation.getItemId());
                HzArrItem item = new HzArrItem();
                item.setTotalCharge(hzColdRegister.getSumMoney());
                item.setWaybillCode(airWaybill.getWaybillCode());
                item.setIrId(relation.getId());
                item.setColdStore(airWaybill.getColdStore());
                item.setStoreStartTime(wareTime);
                item.setStoreEndTime(outTime);
                item.setOrderId(airWaybill.getOrderId());
                itemMapper.insert(item);
            }
        }
        hzColdRegister.setUpdateTime(new Date());
        registerMapper.insert(hzColdRegister);
    }

    @NotNull
    private List<HzChargeIrRelation> getHzChargeIrRelations(AirWaybill airWaybill, List<HzChargeIrRelation> relations) {
        int maxMatchCount = 0;
        List<HzChargeIrRelation> ruleList = new ArrayList<>();
        for (HzChargeIrRelation hzChargeRule : relations) {
            if (hzChargeRule.getIsSouth() == 1){
                continue;
            }
            if (hzChargeRule.getIsExit() == 1){
                continue;
            }
            if (!hzChargeRule.getCrossAir().equals(airWaybill.getCrossAir())){
                continue;
            }
            if (StringUtils.isNotEmpty(hzChargeRule.getNoCharge()) && !hzChargeRule.getNoCharge().contains(airWaybill.getDeptId().toString())){
                continue;
            }
            if (StringUtils.isNotEmpty(hzChargeRule.getPrefix()) && !hzChargeRule.getPrefix().contains(airWaybill.getWaybillCode().substring(4,7))){
                continue;
            }
            if (StringUtils.isNotEmpty(hzChargeRule.getCategory()) && !hzChargeRule.getCategory().contains(airWaybill.getCategoryName())){
                continue;
            }
            int matchCount = 0;
            int cargoMatchCount = isCargoCodeMatch(hzChargeRule, airWaybill.getCargoName());
            if (cargoMatchCount >= 0) {
                matchCount += cargoMatchCount;
            }

            if (matchCount > 0) {
                if (matchCount > maxMatchCount) {
                    maxMatchCount = matchCount;
                    ruleList.clear();
                    ruleList.add(hzChargeRule);
                } else if (matchCount == maxMatchCount) {
                    ruleList.add(hzChargeRule);
                }
            }
        }
        return ruleList;
    }
    private int isCargoCodeMatch(HzChargeIrRelation hzChargeRule, String waybillCargoCode) {
        if (StringUtils.isEmpty(hzChargeRule.getCargoName())) {
            return 1;
        }else if (StringUtils.isEmpty(hzChargeRule.getCategory())){
            if (hzChargeRule.getCargoName().contains(waybillCargoCode)){
                return 1;
            }
            return 0;
        }
        List<IrRelationVo> relationVos = Arrays.stream(hzChargeRule.getCargoName().split(","))
                .map(cargoCodeMapper::selectIrByName)
                .collect(Collectors.toList());

        List<IrRelationVo> cargoNames = Arrays.stream(hzChargeRule.getCategory().split(","))
                .flatMap(category -> cargoCodeMapper.selectListByCategory(Collections.singletonList(category)).stream())
                .collect(Collectors.toList());

        Map<String, List<IrRelationVo>> relationVosMap = relationVos.stream()
                .collect(Collectors.groupingBy(IrRelationVo::getCategoryCode));

        List<IrRelationVo> combinedList = new ArrayList<>();
        Set<String> addedCodes = new HashSet<>();
        for (IrRelationVo vo : cargoNames) {
            String code = vo.getCategoryCode();
            if (relationVosMap.containsKey(code) && !addedCodes.contains(code)) {
                combinedList.addAll(relationVosMap.get(code));
                addedCodes.add(code);
            } else if (!addedCodes.contains(code)) {
                combinedList.add(vo);
            }
        }
        List<String> collect1 = combinedList.stream().map(IrRelationVo::getCode).collect(Collectors.toList());
        if (collect1.stream().anyMatch(cargoCode -> cargoCode.equals(waybillCargoCode))) {
            String category = hzChargeRule.getCategory();
            StringBuilder stringBuffer = new StringBuilder();
            if(category != null){
                String[] split = category.split(",");
                for (String categoryCode:split) {
                    List<BaseCargoCode> cargoCodeList = cargoCodeMapper.selectList(new QueryWrapper<>(new BaseCargoCode())
                            .eq("category_code", categoryCode)
                            .eq("is_del",0));
                    if(cargoCodeList!=null && cargoCodeList.size() > 0){
                        for (BaseCargoCode e:cargoCodeList) {
                            stringBuffer.append(e.getCode());
                        }
                    }
                }
            }else{
                return 1;
            }
            if (stringBuffer.toString().contains(waybillCargoCode)) {
                return 2;
            } else {
                return 1;
            }
        }
        return 0;
    }

    public void countSum(AirWaybill airWaybill, Date wareTime, Date outTime, HzColdRegister hzColdRegister, List<HzChargeItemRule> itemRules,Long itemId) {
        HzChargeItems hzChargeItems = chargeItemsMapper.selectById(itemId);
        if (!CollectionUtils.isEmpty(itemRules)) {
            CostDetail detail = new CostDetail();
            detail.setColdStore(airWaybill.getColdStore());
            detail.setStoreStartTime(wareTime);
            detail.setStoreEndTime(outTime);
            BillingRule rule = BillingRuleFactory.createRule("ColdStorageBillingRule.class");
            BillRuleVo vo = rule.calculateFee(itemRules, new BigDecimal(0), 1, detail);
            BigDecimal bigDecimal = BigDecimalRoundUtils.bigDecimalRound(hzChargeItems.getRoundRule(), vo.getTotalCharge());
            hzColdRegister.setSumMoney(bigDecimal);
        }
    }
}
