package com.gzairports.common.business.departure.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import com.gzairports.common.business.arrival.domain.ItemDetail;
import lombok.Data;

import java.awt.event.PaintEvent;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.Date;

/**
 * 出港费用明细表
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
@TableName("wl_dep_cost_detail")
public class CostDetail implements ItemDetail {

    /** 主键id */
    private Long id;

    /** 收费项目与规则id */
    private Long irId;

    /** 运单号 */
    private String waybillCode;

    /** 收费项目id */
    @TableField(exist = false)
    private Long chargeItemsId;

    /** 收费名称 */
    @TableField(exist = false)
    private String chargeName;

    /** 简称 */
    @TableField(exist = false)
    private String chargeAbb;

    /** 计费方式 */
    @TableField(exist = false)
    private String ruleName;

    /** 执行类 */
    @TableField(exist = false)
    private String className;

    /** 收费规则id */
    @TableField(exist = false)
    private Long ruleId;

    /** 费率 */
    private BigDecimal rate;

    /** 总费用 */
    private BigDecimal totalCharge;

    /** 修改费用 */
    private BigDecimal editCharge;

    /** 描述 */
    private String remark;

    /** 份数 */
    private Integer unit;

    /** 小件件数 */
    private Integer smallItem;

    /** 大件件数 */
    private Integer largeItem;

    /** 超大件件数 */
    private Integer superLargeItem;

    /** 分时段计费开始时间 */
    @JsonFormat(pattern = "HH:mm")
    private LocalTime startTime;

    /** 分时段计费结束时间 */
    @JsonFormat(pattern = "HH:mm")
    private LocalTime endTime;

    /** 保存天数 */
    private Double daysInStorage;

    /** 冷库类型 */
    private String coldStore;

    /** 冷库开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date storeStartTime;

    /** 冷库结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date storeEndTime;

    /** 类型:0 预授权支付明细， 1 已结算支付明细 */
    private Integer type;

    /** 是否支付 0 未支付 1 已支付 2 取消支付*/
    private Integer isSettle;

    /** 时间点 */
    private LocalTime pointTime;

    /** 服务申请id */
    private Long serviceRequestId;

    /** 部门id */
    private Long deptId;

    /** -1 收运计费 0 服务申请 1 退货 2 冷藏 3 作废退款 4 退款 */
    private Long flightId;

    /** 计费重量 */
    private String quantity;

    /** 是否自动 */
    private Integer isAuto;

    /** 是否删除 */
    private Integer isDel;

    /** 收费服务类型 0 默认收费 1 服务申请收费 2 冷藏登记收费*/
    private Integer serviceType;

    /** 创建时间 */
    private Date createTime;

    /** 结算已出港件数 */
    private Integer settleDepQuantity;

    /** 实际出港重量 */
    private BigDecimal settleDepWeight;

    @TableField(exist = false)
    private String agent;
}
