<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.departure.mapper.TransferWaybillMapper">

    <select id="selectTransferStatus" resultType="java.lang.String">
        select wdt.status from wl_dep_transfer wdt left join wl_dep_transfer_waybill wdtw on wdt.id = wdtw.transfer_id where wdtw.mawb_id = #{id}
    </select>
    <select id="selectTransferIdByMawbId" resultType="com.gzairports.common.business.departure.domain.Transfer">
        select wdt.id,wdt.type
        from wl_dep_transfer wdt
        left join wl_dep_transfer_waybill wdtw on wdt.id = wdtw.transfer_id
        where wdtw.mawb_id = #{id}
    </select>
</mapper>