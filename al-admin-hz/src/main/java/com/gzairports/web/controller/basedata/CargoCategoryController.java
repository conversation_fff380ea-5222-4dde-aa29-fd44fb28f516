package com.gzairports.web.controller.basedata;


import com.gzairports.common.basedata.domain.BaseCargoCategory;
import com.gzairports.common.basedata.domain.query.CargoCategoryQuery;
import com.gzairports.common.basedata.service.ICargoCategoryService;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 货品大类Controller
 *
 * <AUTHOR>
 * @date 2024-06-14
 */
@RestController
@RequestMapping("/base/cargoCategory")
public class CargoCategoryController extends BaseController {

    @Autowired
    private ICargoCategoryService categoryService;

    /**
     * 查询货品大类列表
     */
//    @PreAuthorize("@ss.hasPermi('base:cargoCategory:cargoCategoryList')")
    @GetMapping("/cargoCategoryList")
    @ApiOperation(value = "查询货品大类列表")
    public TableDataInfo cargoCategoryList(CargoCategoryQuery query)
    {
        startPage();
        List<BaseCargoCategory> list = categoryService.selectCargoCategoryList(query);
        return getDataTable(list);
    }

    /**
     * 导出货品大类
     */
    @PreAuthorize("@ss.hasPermi('base:cargoCategory:exportCargoCategory')")
    @Log(title = "导出货品大类", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCargoCategory")
    @ApiOperation(value = "导出货品大类")
    public void exportCargoCategory(HttpServletResponse response, CargoCategoryQuery query)
    {
        List<BaseCargoCategory> list = categoryService.selectCargoCategoryList(query);
        ExcelUtil<BaseCargoCategory> util = new ExcelUtil<BaseCargoCategory>(BaseCargoCategory.class);
        util.exportExcel(response, list, "货品大类");
    }

    /**
     * 新增货品大类
     */
    @PreAuthorize("@ss.hasPermi('base:cargoCategory:addCargoCategory')")
    @Log(title = "新增货品大类", businessType = BusinessType.INSERT)
    @PostMapping("/addCargoCategory")
    @ApiOperation(value = "新增货品大类")
    public AjaxResult addCargoCategory(@RequestBody BaseCargoCategory code)
    {
        return toAjax(categoryService.addCargoCategory(code));
    }

    /**
     * 修改货品大类
     */
    @PreAuthorize("@ss.hasPermi('base:cargoCategory:editCargoCategory')")
    @Log(title = "修改货品大类", businessType = BusinessType.UPDATE)
    @PostMapping("/editCargoCategory")
    @ApiOperation(value = "修改货品大类")
    public AjaxResult editCargoCategory(@RequestBody BaseCargoCategory code)
    {
        return toAjax(categoryService.editCargoCategory(code));
    }

    /**
     * 删除货品大类
     */
    @PreAuthorize("@ss.hasPermi('base:cargoCategory:delCargoCategory')")
    @Log(title = "删除货品大类", businessType = BusinessType.UPDATE)
    @GetMapping("/delCargoCategory/{ids}")
    @ApiOperation(value = "删除货品大类")
    public AjaxResult delCargoCategory(@PathVariable("ids") Long[] ids)
    {
        return toAjax(categoryService.delCargoCategory(ids));
    }
}
