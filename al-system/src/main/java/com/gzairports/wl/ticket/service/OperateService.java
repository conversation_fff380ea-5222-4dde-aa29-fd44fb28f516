package com.gzairports.wl.ticket.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.wl.ticket.domain.TicketCtrl;

import java.util.List;

/**
 * 单证控制Service接口
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
public interface OperateService extends IService<TicketCtrl> {

    /**
     * 查询单证控制管理列表
     *
     * @return 单证控制管理列表
     */
    List<TicketCtrl> selectList();

    /**
     * 保存单证控制管理列表
     *
     * @param list 单证控制管理列表
     * @return 結果
     */
    int saveCtrl(List<TicketCtrl> list);
}
