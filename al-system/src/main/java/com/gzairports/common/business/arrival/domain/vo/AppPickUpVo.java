package com.gzairports.common.business.arrival.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * app提货出库返回数据
 *
 * <AUTHOR>
 * @date 2024-07-22
 */
@Data
public class AppPickUpVo {

    /** 办单id */
    private Long pickUpId;

    /** 理货id */
    private Long tallyId;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date execDate;

    /** 航班号 */
    private String flightNo;

    /** 运单号 */
    private String waybillCode;

    /** 流水号 */
    private String serialNo;

    /** 状态 */
    private String status;

    /** 代理人简称 */
    private String agentAbb;

    /** 提货码 */
    private String pickUpCode;

    /** 提货出库时间 */
    private Date outTime;
}
