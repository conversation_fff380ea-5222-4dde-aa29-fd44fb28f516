package com.gzairports.hz.business.departure.domain.query;

import com.gzairports.hz.business.departure.domain.vo.ForwardImportUldVo;
import com.gzairports.hz.business.departure.domain.vo.ForwardImportWaybillVo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 航班配载装配数据
 *
 * <AUTHOR>
 * @date 2024-07-01
 */
@Data
public class AssembleQuery {

    /** 航班配载id */
    private Long flightLoadId;

    /** 代运导入运单集合 */
    private List<ForwardImportWaybillVo> vos;

    /** 代运导入板箱集合 */
    private List<ForwardImportUldVo> uldVos;
}
