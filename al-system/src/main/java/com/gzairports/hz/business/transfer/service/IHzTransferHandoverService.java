package com.gzairports.hz.business.transfer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.hz.business.arrival.domain.vo.EnterWaybillVo;
import com.gzairports.hz.business.transfer.domain.HzTransferHandover;
import com.gzairports.hz.business.transfer.domain.query.FileAndCargoTransferQuery;
import com.gzairports.hz.business.transfer.domain.query.TransferPickQuery;
import com.gzairports.hz.business.transfer.domain.vo.*;

/**
 * 中转交接Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-27
 */
public interface IHzTransferHandoverService  extends IService<HzTransferHandover>
{
    /**
     * 查询中转交接
     * 
     * @param id 中转交接ID
     * @return 中转交接
     */
    TransferHandoverInfoVo selectHzTransferHandoverById(Long id);

    /**
     * 查询中转交接列表
     * 
     * @param hzTransferHandover 中转交接
     * @return 中转交接集合
     */
    List<HzTransferHandover> selectHzTransferHandoverList(HzTransferHandover hzTransferHandover);

    /**
     * 新增中转交接
     * 
     * @param vo 中转交接参数
     * @return 结果
     */
    TransferHandoverVo insertHzTransferHandover(TransferHandoverVo vo);


    /**
     *  中转交接挑单
     * @param waybillCode 运单号
     * @return 挑单数据
     */
    TransferPickVo pickOne(String waybillCode);

    /**
     *  中转交接批量挑单
     * @param query 查询参数
     * @return 批量挑单数据
     */
    List<TransferPickVo> batchPick(TransferPickQuery query);

    /**
     *  删除交接单
     * @param id 交接单id
     * @return 结果
     */
    int delete(Long id);

    /**
     * 根据流水号查询交接单详情
     * @param handoverNo 交接单流水号
     * @return 详情数据
     */
    TransferHandoverInfoVo getInfoByNo(String handoverNo);

    /**
     * 文件或货物交接
     * @param id 交接单id
     * @return 数据
     */
    FileAndCargoTransferVo fileTransfer(Long id);

    /**
     * 根据条件查询文件或货物交接单详情
     * @param query 查询条件
     * @return 交接单详情
     */
    FileAndCargoTransferVo getInfoByQuery(FileAndCargoTransferQuery query);

    /**
     * 文件交接
     * @param vo 文件交接数据
     * @return 结果
     */
    int fileHandover(FileAndCargoHandoverVo vo);

    /**
     * 取消文件交接
     * @param id 交接单id
     * @return 结果
     */
    int cancelFileHandover(Long id);

    /**
     * 文件接收
     * @param id 交接单id
     * @return 结果
     */
    int fileReceive(Long id);

    /**
     * 货物交接
     * @param vo 文件交接数据
     * @return 结果
     */
    int cargoHandover(FileAndCargoHandoverVo vo);

    /**
     * 取消货物交接
     * @param id 交接单id
     * @return 结果
     */
    int cancelCargoHandover(Long id);

    /**
     * 货物接收
     * @param id 交接单id
     * @return 结果
     */
    int cargoReceive(Long id);

    /**
     * 加入提货列表
     * @param waybillIds 运单id集合
     * @return 中转列表
     */
    List<TransferPickVo> pickList(Long[] waybillIds);

    /**
     * 根据录单id查询运单详情
     * @param orderId 录单id
     * @return 运单详情
     */
    EnterWaybillVo getWaybillInfo(Long orderId);
}
