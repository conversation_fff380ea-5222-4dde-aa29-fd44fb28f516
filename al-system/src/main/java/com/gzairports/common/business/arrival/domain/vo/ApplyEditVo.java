package com.gzairports.common.business.arrival.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 申请修改数据
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@Data
public class ApplyEditVo {

    /** 主键id */
    private Long id;

    /** 流水号 */
    private String serialNo;

    /** 代理人 */
    private String agentCompany;

    /** 运单数 */
    private Integer totalCount;

    /** 提货总件数 */
    private Integer totalQuantity;

    /** 提货总重量 */
    private BigDecimal totalWeight;

    /** 总费用 */
    private BigDecimal totalCost;

    /** 办单经办人 */
    private String handleBy;

    /** 申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;

    /** 状态 */
    private String status;
    private String applyStatus;
}
