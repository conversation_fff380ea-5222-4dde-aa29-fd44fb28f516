<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.wl.log.mapper.WlOperLogMapper">

    <insert id="insertWlOperLog" parameterType="wlOperLog">
        insert into wl_oper_log(oper_id, title, business_type, operator_type, request_method, oper_name, oper_param, json_result, status, error_msg, oper_time)
        values (#{operId}, #{title}, #{businessType}, #{operatorType}, #{requestMethod}, #{operName}, #{operParam}, #{jsonResult}, #{status}, #{errorMsg}, sysdate())
    </insert>
    <select id="selectByQuery" resultType="com.gzairports.wl.log.domain.WlOperLog">
        select id, oper_id, title, business_type, operator_type, request_method, oper_name, oper_param, json_result, status,
               error_msg, oper_time from wl_oper_log where oper_id = #{id} and title = #{title}
    </select>
    <select id="logInfo" resultType="com.gzairports.wl.log.domain.WlOperLog">
        select id, oper_id, title, business_type, operator_type, request_method, oper_name, oper_param, json_result, status,
               error_msg, oper_time from wl_oper_log where id = #{logId}
    </select>
</mapper>