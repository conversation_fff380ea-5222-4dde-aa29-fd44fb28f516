package com.gzairports.hz.business.cable.domain.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-04-07
 */
@Data
public class ForwardOriginMsgVO {

    /** id */
    private String id;

    /** 邮件接收地址 */
    private String mailAddress;

    /** 邮件主题 */
    private String mailTopic;

    /** 月份 */
    private String msgMonth;

    /** 消息类型 */
    private String msgType;

    /** 原始报文 */
    private String originMsg;

    /** 优先级 */
    private String priority;

    /** 收报地址 */
    private String receiveAddress;

    /** 发报地址 */
    private String sendAddress;

    /** 交互方式 */
    private String sendType;

    /** ftp地址 */
    private String receiveFtpAddress;

    /** ftp目录 */
    private String receiveFtpFolder;

    /** mq队列 */
    private String receiveMQQueue;
}
