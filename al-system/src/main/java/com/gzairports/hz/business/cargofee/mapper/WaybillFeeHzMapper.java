package com.gzairports.hz.business.cargofee.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.hz.business.cargofee.domain.WaybillFeeHz;
import com.gzairports.hz.business.cargofee.domain.query.WaybillFeeHzQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 运单费用明细Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@Mapper
public interface WaybillFeeHzMapper extends BaseMapper<WaybillFeeHz> {

    /**
     * 查询运单费用明细列表
     * @param query 查询参数
     * @return 运单费用明细列表
     */
    List<WaybillFeeHz> selectListByQuery(WaybillFeeHzQuery query);
}
