package com.gzairports.hz.business.departure.service.impl;

import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.hz.business.departure.domain.vo.FormalManifestVo;
import com.gzairports.hz.business.departure.domain.vo.FormalUldVo;
import com.gzairports.hz.business.departure.service.Formal;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 舱单远程打印生成excel
 * <AUTHOR>
 * @date 2024-09-23
 */
public class ExcelTableGenerator {

    private static final SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public static Workbook getExcelData(FormalManifestVo vo) throws Exception {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("舱单");

        // 设置字体和样式
        Font headerFont = workbook.createFont();
        headerFont.setFontName("Arial");
        headerFont.setBold(true);
        headerFont.setColor(IndexedColors.WHITE.getIndex());

        CellStyle headerCellStyle = workbook.createCellStyle();
        headerCellStyle.setFillForegroundColor(IndexedColors.BLUE_GREY.getIndex());
        headerCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerCellStyle.setFont(headerFont);

        CellStyle dateCellStyle = workbook.createCellStyle();
        // 水平居中
        dateCellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        dateCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 创建标题行
        Row titleRow = sheet.createRow(0);
        titleRow.createCell(0).setCellValue("贵阳机场货运舱单");
        titleRow.getCell(0).setCellStyle(dateCellStyle);
        titleRow.setHeightInPoints(30);

        // 创建航班信息行
        if (vo == null){
            return workbook;
        }
        Row flightInfoRow = sheet.createRow(1);
        flightInfoRow.createCell(0).setCellValue("航班号:");
        flightInfoRow.createCell(1).setCellValue(vo.getFlightNo());
        flightInfoRow.createCell(2).setCellValue("飞机号:");
        flightInfoRow.createCell(3).setCellValue(vo.getCraftNo());
        flightInfoRow.createCell(4).setCellValue("航班日期:");
        flightInfoRow.createCell(5).setCellValue(formatter.format(vo.getExecDate()));
        flightInfoRow.createCell(6).setCellValue("始发站:");
        flightInfoRow.createCell(7).setCellValue(vo.getSourcePort());
        flightInfoRow.createCell(8).setCellValue("到达站:");
        flightInfoRow.createCell(9).setCellValue(vo.getDesPort());

        // 创建表头行
        Row headerRow = sheet.createRow(2);
        for (int i = 0; i <= 11; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(getHeaderLabel(i));
            cell.setCellStyle(headerCellStyle);
        }

        // 创建数据行
        int rowNumber = 3;
        if (!CollectionUtils.isEmpty(vo.getVos())){
            for (int i = 0; i < vo.getVos().size(); i++) {
                rowNumber = getRowNumber(sheet, rowNumber, i,vo.getVos().get(i));
                FormalUldVo uldVo = vo.getVos().get(i);
                if (!CollectionUtils.isEmpty(uldVo.getVos())){
                    for (int j = 0; j < uldVo.getVos().size(); j++) {
                        rowNumber = getRowNumber(sheet, rowNumber, i,uldVo.getVos().get(j));
                    }
                }
            }
        }
        // 创建合计行
        Row totalRow = sheet.createRow(rowNumber++);
        totalRow.createCell(4).setCellValue("合计");
        totalRow.createCell(5).setCellValue("货物");
        totalRow.createCell(6).setCellValue((vo.getCargoNum() == null ? 0 : vo.getCargoNum()) + "票");
        totalRow.createCell(7).setCellValue((vo.getCargoQuantity() == null ? 0 : vo.getCargoQuantity()) + "件");
        totalRow.createCell(8).setCellValue((vo.getCargoWeight() == null ? "0" : vo.getCargoWeight().toString()) + "公斤");
        Row mailRow = sheet.createRow(rowNumber++);
        mailRow.createCell(5).setCellValue("邮件");
        mailRow.createCell(6).setCellValue((vo.getMailNum() == null ? 0 : vo.getMailNum()) + "票");
        mailRow.createCell(7).setCellValue((vo.getMailQuantity() == null ? 0 : vo.getMailQuantity()) + "件");
        mailRow.createCell(8).setCellValue((vo.getMailWeight() == null ? "0" : vo.getMailWeight().toString()) + "公斤");
        Row uldRow = sheet.createRow(rowNumber++);
        uldRow.createCell(5).setCellValue("ULD");
        uldRow.createCell(6).setCellValue((vo.getUldUnm() == null ? 0 : vo.getUldUnm()) + "个");
        Row countRow = sheet.createRow(rowNumber++);
        countRow.createCell(5).setCellValue("货物&邮件");
        countRow.createCell(6).setCellValue((vo.getTotalNum() == null ? 0 : vo.getTotalNum()) + "票");
        countRow.createCell(7).setCellValue((vo.getTotalQuantity() == null ? 0 : vo.getTotalQuantity()) + "件");
        countRow.createCell(8).setCellValue((vo.getTotalWeight() == null ? "0" : vo.getTotalWeight().toString()) + "公斤");

        // 创建制单人和打印时间行
        Date date = new Date();
        Row footerRow = sheet.createRow(rowNumber++);
        footerRow.createCell(0).setCellValue("制单人: " + SecurityUtils.getUsername());
        footerRow.createCell(9).setCellValue("打印时间: " + formatter.format(date));

        // 设置列宽
        for (int i = 0; i <= 9; i++) {
            sheet.autoSizeColumn(i);
        }
        // 输出到文件
        try (FileOutputStream out = new FileOutputStream("舱单.xlsx")) {
            workbook.write(out);
            workbook.close();
        }
        return workbook;
    }

    private static int getRowNumber(Sheet sheet, int rowNumber, int i, Formal formal) {
        Row dataRow = sheet.createRow(rowNumber++);
        dataRow.createCell(0).setCellValue(i + 1);
        dataRow.createCell(1).setCellValue(formal.getWaybillCode());
        dataRow.createCell(2).setCellValue(formal.getCabin());
        dataRow.createCell(3).setCellValue(formal.getQuantity() == null ? 0 : formal.getQuantity());
        dataRow.createCell(4).setCellValue(formal.getWeight() == null ? "0" : formal.getWeight().toString());
        dataRow.createCell(5).setCellValue(formal.getVolume()== null ? "0" : formal.getVolume().toString());
        dataRow.createCell(6).setCellValue(formal.getSpecialCargoCode1());
        dataRow.createCell(7).setCellValue(formal.getCargoName());
        dataRow.createCell(8).setCellValue(formal.getSourcePort());
        dataRow.createCell(9).setCellValue(formal.getDesPort());
        dataRow.createCell(10).setCellValue(formal.getPriority());
        dataRow.createCell(11).setCellValue(formal.getRemark() + formal.getOriginBill());
        return rowNumber;
    }

    private static String getHeaderLabel(int index) {
        switch (index) {
            case 0:
                return "序号";
            case 1:
                return "运单号";
            case 2:
                return "舱位";
            case 3:
                return "配载件数";
            case 4:
                return "配载重量";
            case 5:
                return "配载体积";
            case 6:
                return "特殊处理代码";
            case 7:
                return "品名";
            case 8:
                return "始发站";
            case 9:
                return "目的站";
            case 10:
                return "优先级";
            case 11:
                return "备注";
            default:
                return "";
        }
    }

    public static byte[] convertExcelToPdf(Workbook workbook) throws IOException, DocumentException {
        // 创建 PDF 文档
        Document document = new Document(PageSize.A4);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        PdfWriter.getInstance(document, baos);
        document.open();
        Sheet sheet = workbook.getSheetAt(0);

        // 设置字体
        ClassPathResource resource = new ClassPathResource("simsun.ttc");
        if (!resource.exists()){
            return null;
        }
        String path = resource.getPath();
        BaseFont baseFont = BaseFont.createFont(path + ",0", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);

        com.itextpdf.text.Font font = new com.itextpdf.text.Font(baseFont,12, com.itextpdf.text.Font.NORMAL);

        // 创建表格
        PdfPTable table = new PdfPTable(10);
        table.setWidthPercentage(100);

        // 添加标题行
        PdfPCell titleCell = new PdfPCell(new Phrase("贵阳机场货运舱单", font));
        titleCell.setColspan(10);
        titleCell.setHorizontalAlignment(Element.ALIGN_CENTER);
        titleCell.setPadding(10);
        table.addCell(titleCell);

        // 添加航班信息行
        addFlightInfoRow(table, sheet.getRow(1),font);

        // 添加表头行
        addHeaderRow(table, sheet.getRow(2),font);

        // 添加数据行
        for (int r = 3; r <= sheet.getLastRowNum(); r++) {
            Row dataRow = sheet.getRow(r);
            if (dataRow != null) {
                addDataRow(table, dataRow, font);
            }
        }

        // 添加合计行
        addTotalRow(table, sheet.getRow(sheet.getLastRowNum() - 1),font);

        // 添加制单人和打印时间行
        addFooterRow(table, sheet.getRow(sheet.getLastRowNum()),font);

        // 将表格添加到文档
        document.add(table);
        // 关闭文档
        document.close();
        return baos.toByteArray();
    }

    private static void addFlightInfoRow(PdfPTable table, Row row, com.itextpdf.text.Font font) {
        for (int c = 0; c < row.getLastCellNum(); c++) {
            Cell cell = row.getCell(c);
            if (cell != null) {
                PdfPCell pdfCell = new PdfPCell();
                switch (cell.getCellType()){
                    case STRING:
                        pdfCell = new PdfPCell(new Phrase(cell.getStringCellValue(), font));
                        break;
                    case NUMERIC:
                        if (DateUtil.isCellDateFormatted(cell)) {
                            pdfCell = new PdfPCell(new Phrase(formatter.format(cell.getDateCellValue()), font));
                        } else {
                            pdfCell = new PdfPCell(new Phrase(String.valueOf(cell.getNumericCellValue()), font));
                        }
                        break;
                    case BOOLEAN:
                        break;
                    case FORMULA:
                        pdfCell = new PdfPCell(new Phrase(cell.getCellFormula(), font));
                        break;
                    case BLANK:
                        System.out.print("BLANK\t");
                        break;
                    case ERROR:
                        System.out.print("ERROR\t");
                        break;
                    default:
                        System.out.print("UNKNOWN\t");
                        break;
                }
                pdfCell.setBorderWidth(0);
                table.addCell(pdfCell);
            }
        }
    }

    private static void addHeaderRow(PdfPTable table, Row row, com.itextpdf.text.Font font) {
        for (int c = 0; c < row.getLastCellNum(); c++) {
            Cell cell = row.getCell(c);
            if (cell != null) {
                PdfPCell pdfCell = new PdfPCell();
                switch (cell.getCellType()){
                    case STRING:
                        pdfCell = new PdfPCell(new Phrase(cell.getStringCellValue(), font));
                        break;
                    case NUMERIC:
                        if (DateUtil.isCellDateFormatted(cell)) {
                            pdfCell = new PdfPCell(new Phrase(formatter.format(cell.getDateCellValue()), font));
                        } else {
                            pdfCell = new PdfPCell(new Phrase(String.valueOf(cell.getNumericCellValue()), font));
                        }
                        break;
                    case BOOLEAN:
                        break;
                    case FORMULA:
                        pdfCell = new PdfPCell(new Phrase(cell.getCellFormula(), font));
                        break;
                    case BLANK:
                        System.out.print("BLANK\t");
                        break;
                    case ERROR:
                        System.out.print("ERROR\t");
                        break;
                    default:
                        System.out.print("UNKNOWN\t");
                        break;
                }
                pdfCell.setBackgroundColor(BaseColor.LIGHT_GRAY);
                pdfCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                table.addCell(pdfCell);
            }
        }
    }

    private static void addDataRow(PdfPTable table, Row row, com.itextpdf.text.Font font) {
        for (int c = 0; c < row.getLastCellNum(); c++) {
            Cell cell = row.getCell(c);
            if (cell != null) {
                PdfPCell pdfCell = new PdfPCell();
                switch (cell.getCellType()){
                    case STRING:
                        pdfCell = new PdfPCell(new Phrase(cell.getStringCellValue(), font));
                        break;
                    case NUMERIC:
                        if (DateUtil.isCellDateFormatted(cell)) {
                            pdfCell = new PdfPCell(new Phrase(formatter.format(cell.getDateCellValue()), font));
                        } else {
                            pdfCell = new PdfPCell(new Phrase(String.valueOf(cell.getNumericCellValue()), font));
                        }
                        break;
                    case BOOLEAN:
                        break;
                    case FORMULA:
                        pdfCell = new PdfPCell(new Phrase(cell.getCellFormula(), font));
                        break;
                    case BLANK:
                        System.out.print("BLANK\t");
                        break;
                    case ERROR:
                        System.out.print("ERROR\t");
                        break;
                    default:
                        System.out.print("UNKNOWN\t");
                        break;
                }
                table.addCell(pdfCell);
            }
        }
    }

    private static void addTotalRow(PdfPTable table, Row row, com.itextpdf.text.Font font) {
        for (int c = 0; c < row.getLastCellNum(); c++) {
            Cell cell = row.getCell(c);
            if (cell != null) {
                PdfPCell pdfCell = new PdfPCell();
                switch (cell.getCellType()){
                    case STRING:
                        pdfCell = new PdfPCell(new Phrase(cell.getStringCellValue(), font));
                        break;
                    case NUMERIC:
                        if (DateUtil.isCellDateFormatted(cell)) {
                            pdfCell = new PdfPCell(new Phrase(formatter.format(cell.getDateCellValue()),font));
                        } else {
                            pdfCell = new PdfPCell(new Phrase(String.valueOf(cell.getNumericCellValue()),font));
                        }
                        break;
                    case BOOLEAN:
                        break;
                    case FORMULA:
                        pdfCell = new PdfPCell(new Phrase(cell.getCellFormula(),font));
                        break;
                    case BLANK:
                        System.out.print("BLANK\t");
                        break;
                    case ERROR:
                        System.out.print("ERROR\t");
                        break;
                    default:
                        System.out.print("UNKNOWN\t");
                        break;
                }
                pdfCell.setBackgroundColor(BaseColor.LIGHT_GRAY);
                pdfCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                table.addCell(pdfCell);
            }
        }
    }

    private static void addFooterRow(PdfPTable table, Row row, com.itextpdf.text.Font font) {
        for (int c = 0; c < row.getLastCellNum(); c++) {
            Cell cell = row.getCell(c);
            if (cell != null) {
                PdfPCell pdfCell = new PdfPCell();
                switch (cell.getCellType()){
                    case STRING:
                        pdfCell = new PdfPCell(new Phrase(cell.getStringCellValue(), font));
                        break;
                    case NUMERIC:
                        if (DateUtil.isCellDateFormatted(cell)) {
                            pdfCell = new PdfPCell(new Phrase(formatter.format(cell.getDateCellValue()), font));
                        } else {
                            pdfCell = new PdfPCell(new Phrase(String.valueOf(cell.getNumericCellValue()), font));
                        }
                        break;
                    case BOOLEAN:
                        break;
                    case FORMULA:
                        pdfCell = new PdfPCell(new Phrase(cell.getCellFormula(), font));
                        break;
                    case BLANK:
                        System.out.print("BLANK\t");
                        break;
                    case ERROR:
                        System.out.print("ERROR\t");
                        break;
                    default:
                        System.out.print("UNKNOWN\t");
                        break;
                }
                pdfCell.setBorderWidth(0);
                table.addCell(pdfCell);
            }
        }
    }
}
