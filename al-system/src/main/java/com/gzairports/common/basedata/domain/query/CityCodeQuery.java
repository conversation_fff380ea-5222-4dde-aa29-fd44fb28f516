package com.gzairports.common.basedata.domain.query;

import lombok.Data;

import java.util.List;

/**
 * 城市代码查询参数
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Data
public class CityCodeQuery {

    /** 主键id集合 */
    private List<Long> ids;

    /** 城市代码 */
    private String code;

    /** 中文名称 */
    private String chineseName;

    /** 英文名称 */
    private String englishName;

    /** 州或省中文 */
    private String stateOrProvinceChinese;

    /** 国家或地区 */
    private String region;
}
