package com.gzairports.hz.business.departure.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 航班配载表
 *
 * <AUTHOR>
 * @date 2024-06-26
 */
@Data
@TableName("hz_flight_load")
public class FlightLoad {

    /** 主键id */
    private Long id;

    /** 运单id */
    private Long flightId;

    /** 航段 */
    private String leg;

    /** 状态（未配载 已配载 未组货 已组货 未复重 未交接 已完成 无货） */
    private String state;

    /** 舱位1H件数 */
    private Integer h1Quantity;

    /** 舱位1H重量 */
    private BigDecimal h1Weight;

    /** 舱位2H件数 */
    private Integer h2Quantity;

    /** 舱位2H重量 */
    private BigDecimal h2Weight;

    /** 舱位3H件数 */
    private Integer h3Quantity;

    /** 舱位3H重量 */
    private BigDecimal h3Weight;

    /** 舱位4H件数 */
    private Integer h4Quantity;

    /** 舱位4H重量 */
    private BigDecimal h4Weight;

    /** 舱位5H件数 */
    private Integer h5Quantity;

    /** 舱位5H重量 */
    private BigDecimal h5Weight;

    /** 配载员 */
    private String loadUser;

    /** 配载时间 */
    private Date loadTime;

    /** 组货员 */
    private String groupUser;

    /** 组货完成时间 */
    private Date groupTime;

    /** 复磅完成时间 */
    private Date weightTime;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间/处理时间 */
    private Date updateTime;

    /** 进出港类型 */
    private String isOffIn;

    /** 航班操作 */
    private String flightOper;

    /** 是否配载 */
    private Integer isPre;
}
