package com.gzairports.wl.cargofee.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 客户支付流水表
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@Data
@TableName("wl_custom_pay")
public class CustomPay {

    /** 主键id */
    private Long id;

    /** 交易时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "交易时间")
    private Date paymentTime;

    /** 交易金额 */
    @Excel(name = "交易金额")
    private BigDecimal money;

    /** 状态（0 失败 1 成功） */
    private Integer status;

    @TableField(exist = false)
    @Excel(name = "状态")
    private String strStart;

    /** 交易类型（0 支付 1 退款） */
    private Integer type;

    @TableField(exist = false)
    @Excel(name = "交易类型")
    private String strType;

    /** 运单号 */
    @Excel(name = "运单号")
    private String waybillCode;

    /** 银联唯一标识 */
    @Excel(name = "银联唯一标识")
    private String unionPay;

    /** 流水号 */
    @Excel(name = "流水号")
    private String serialNo;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 所属单位 */
    private Long deptId;
}
