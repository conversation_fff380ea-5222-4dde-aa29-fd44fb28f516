<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.AirCargoCodeMapper">
    <select id="selectAirCargoCodeList" resultType="com.gzairports.common.basedata.domain.BaseAirCargoCode">
        select id, code, cargo_code, chinese_description, english_description, carrier_code, domestic, international from base_air_cargo_code
        <where>
            and is_del = 0
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="code != null  and code != ''"> and code like concat('%',#{code},'%') </if>
            <if test="carrierCode != null  and carrierCode != ''"> and carrier_code = #{carrierCode}</if>
            <if test="domestic != null"> and domestic = #{domestic}</if>
            <if test="international != null"> and international = #{international}</if>
            and ((is_common = 0 and create_by = #{userName}) or is_common = 1)
        </where>
    </select>
</mapper>