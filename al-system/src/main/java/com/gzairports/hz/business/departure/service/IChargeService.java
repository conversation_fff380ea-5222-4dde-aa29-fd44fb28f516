package com.gzairports.hz.business.departure.service;

import com.gzairports.common.business.arrival.domain.vo.ChargeRuleVo;
import com.gzairports.common.business.departure.domain.CostDetail;
import com.gzairports.common.business.departure.domain.vo.CountCostVo;
import com.gzairports.common.business.departure.domain.vo.HandSettleVo;
import com.gzairports.common.business.departure.domain.vo.ItemDetailVo;
import com.gzairports.common.charge.domain.vo.BillRuleVo;
import com.gzairports.hz.business.departure.domain.query.BillExportQuery;
import com.gzairports.hz.business.departure.domain.query.ChargeQuery;
import com.gzairports.hz.business.departure.domain.vo.*;

import java.util.List;

/**
 * 收费管理Service接口
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface IChargeService {

    /**
     * 收费管理列表数据
     * @param query 查询参数
     * @return 列表数据
     */
    ChargeVo selectList(ChargeQuery query);

    /**
     * 收费明细
     * @param id 运单id
     * @return 明细
     */
    ChargeInfoVo getInfo(Long id);

    /**
     * 新增费用
     * @param detail 费用数据
     * @return 结果
     */
    int add(CostDetail detail);

    /**
     * 新增收费计算总费用
     * @param detail 新增数据
     * @return 结果
     */
    BillRuleVo countCost(CostDetail detail);

    /**
     * 根据收费项目id查询最高优先级规则
     * @param vo 收费参数
     * @return 最该优先级规则
     */
    ChargeRuleVo getHighRule(ItemDetailVo vo);

    /**
     * 导出收费管理列表
     * @param query 查询条件
     * @return 返回数据
     */
    List<ChargeWaybillVo> importData(ChargeQuery query);

    /**
     * 导出结算明细
     * */
    List<ChargeSettleWaybillVo> chargeSettleExport(ChargeQuery query);

    /**
     * 编辑费用明细数据
     * @param detail 更新后的费用明细数据
     * @return 结果
     */
    int editCost(CostDetail detail);

    /**
     * 删除费用明细
     * @param id 费用明细id
     * @return 结果
     */
    int delCost(Long id);

    /**
     * 费用明细详情
     * @param id 费用明细id
     * @return 详情
     */
    CostDetail costInfo(Long id);

    int handSettle(HandSettleVo vo);

    List<ChargeImportVo> chargeExport(ChargeQuery query);

    int errorDataCost(CountCostVo vo);

    BilExportVO selectBillExportData(BillExportQuery query);

    BillExportVoNew selectBillExportDataNew(BillExportQuery query);

    BillExportVoNewAgent selectBillExportDataNewAgent(BillExportQuery query);

    BilExportVO selectBillNewExportData(BillExportQuery query);

    BillExportVoFinance selectBillFinance(BillExportQuery query);
}
