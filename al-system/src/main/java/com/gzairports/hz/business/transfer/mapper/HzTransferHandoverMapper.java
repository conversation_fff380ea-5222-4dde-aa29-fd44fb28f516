package com.gzairports.hz.business.transfer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.hz.business.transfer.domain.HzTransferHandover;
import com.gzairports.hz.business.transfer.domain.query.FileAndCargoTransferQuery;
import com.gzairports.hz.business.transfer.domain.vo.FileAndCargoTransferVo;
import com.gzairports.hz.business.transfer.domain.vo.TransferHandoverInfoVo;
import com.gzairports.hz.business.transfer.domain.vo.TransferPickVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 中转交接Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-27
 */
@Mapper
public interface HzTransferHandoverMapper extends BaseMapper<HzTransferHandover>
{
    /**
     * 查询中转交接
     * 
     * @param id 中转交接ID
     * @return 中转交接
     */
    TransferHandoverInfoVo selectHzTransferHandoverById(Long id);

    /**
     * 查询中转交接列表
     * 
     * @param hzTransferHandover 中转交接
     * @return 中转交接集合
     */
    List<HzTransferHandover> selectHzTransferHandoverList(HzTransferHandover hzTransferHandover);

    /**
     * 新增中转交接
     * 
     * @param hzTransferHandover 中转交接
     * @return 结果
     */
    int insertHzTransferHandover(HzTransferHandover hzTransferHandover);

    /**
     * 修改中转交接
     * 
     * @param hzTransferHandover 中转交接
     * @return 结果
     */
    int updateHzTransferHandover(HzTransferHandover hzTransferHandover);

    /**
     * 根据流水号查询交接单详情
     * @param handoverNo 流水号
     * @return 详情
     */
    TransferHandoverInfoVo selectOneByNo(String handoverNo);

    /**
     * 根据id查询文件或货物交接单详情
     * @param id 交接单id
     * @return 结果
     */
    FileAndCargoTransferVo selectFileAndCargoInfoById(Long id);

    /**
     * 根据条件查询文件或货物交接单详情
     * @param query 查询条件
     * @return 交接单详情
     */
    FileAndCargoTransferVo getInfoByQuery(FileAndCargoTransferQuery query);
}
