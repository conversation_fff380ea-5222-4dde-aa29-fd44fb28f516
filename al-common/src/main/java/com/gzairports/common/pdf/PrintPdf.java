package com.gzairports.common.pdf;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.printing.PDFPageable;
import org.apache.pdfbox.printing.PDFPrintable;
import org.apache.pdfbox.printing.Scaling;

import javax.print.*;
import javax.print.attribute.HashPrintRequestAttributeSet;
import javax.print.attribute.PrintRequestAttributeSet;
import javax.print.attribute.standard.Copies;
import javax.print.attribute.standard.OrientationRequested;
import javax.print.attribute.standard.PrintQuality;
import java.awt.print.*;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.net.Socket;

/**
 * 打印机无驱连接
 * <AUTHOR>
 * @date 2024-08-19
 */
public class PrintPdf {

    /**
     * 无驱打印
     * @param bytes 打印的数据
     */
    public static void printerPdfIp(byte[] bytes) {
        Socket socket = new Socket();
        try {
            socket.connect(new InetSocketAddress("**************", 9100), 3000);
            OutputStream out = socket.getOutputStream();
            out.write(bytes);
            out.flush();
            socket.shutdownOutput();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                socket.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 驱动打印
     * @param bytes 打印的数据
     */
    public static void printerPdf(byte[] bytes) {
        PDDocument document = null;
        try {
            DocFlavor flavor = DocFlavor.SERVICE_FORMATTED.PRINTABLE;
            document = PDDocument.load(new ByteArrayInputStream(bytes));
            int pageCount = document.getNumberOfPages();

            // Select the print service by name
            String printName = "Honeywell PM42 (203 dpi)";
//            String printName = "EPSON LQ-675KT ESC/P2 (副本 1)";
            PrintService printService = null;
            for (PrintService ps : PrinterJob.lookupPrintServices()) {
                if (ps.getName().equals(printName)) {
                    printService = ps;
                    break;
                }
            }
            if (printService == null) {
                throw new RuntimeException("Printer not found: " + printName);
            }

            PrintRequestAttributeSet aset = new HashPrintRequestAttributeSet();
            aset.add(new Copies(1));
//            aset.add(MediaSize.NA.LETTER);
            aset.add(OrientationRequested.PORTRAIT);
            aset.add(PrintQuality.HIGH);

            PrinterJob job = PrinterJob.getPrinterJob();
            try {
                job.setPrintService(printService);
            } catch (PrinterException e) {
                e.printStackTrace();
            }

            job.setPageable(new PDFPageable(document));

            Paper paper = new Paper();
//            paper.setSize(595, 842);
            paper.setSize(317, 208);
//            paper.setSize(174,99);
            paper.setImageableArea(0, 0, paper.getWidth(), paper.getHeight());
            PageFormat pageFormat = new PageFormat();
            pageFormat.setPaper(paper);

            Book book = new Book();
            book.append(new PDFPrintable(document, Scaling.SCALE_TO_FIT), pageFormat, pageCount);
            job.setPageable(book);
            job.print(aset);
        } catch (IOException | PrinterException e) {
            e.printStackTrace();
        } finally {
            if (document != null) {
                try {
                    document.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 驱动打印
     * @param bytes 打印的数据
     */
    public static void printerWell(byte[] bytes) {
        try {
            InputStream inputStream = new ByteArrayInputStream(bytes);
            DocFlavor psInFormat = DocFlavor.INPUT_STREAM.GIF;
            Doc myDoc = new SimpleDoc(inputStream, psInFormat, null);


            // Select the print service by name
            String printName = "Honeywell PM42 (203 dpi)";
            PrintService printService = null;
            for (PrintService ps : PrinterJob.lookupPrintServices()) {
                if (ps.getName().equals(printName)) {
                    printService = ps;
                    break;
                }
            }
            if (printService == null) {
                throw new RuntimeException("Printer not found: " + printName);
            }

            PrintRequestAttributeSet aset = new HashPrintRequestAttributeSet();
            aset.add(new Copies(1));

            DocPrintJob printJob = printService.createPrintJob();

            try {
                printJob.print(myDoc, aset);
            } catch (PrintException e) {
                e.printStackTrace();
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
