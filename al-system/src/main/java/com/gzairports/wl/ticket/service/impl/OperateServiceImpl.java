package com.gzairports.wl.ticket.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.basedata.domain.BaseBillType;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.wl.ticket.domain.TicketCtrl;
import com.gzairports.wl.ticket.mapper.OperateMapper;
import com.gzairports.wl.ticket.service.OperateService;
import com.gzairports.common.basedata.mapper.BillTypeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 单证控制管理service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Service
public class OperateServiceImpl extends ServiceImpl<OperateMapper, TicketCtrl> implements OperateService {

    @Autowired
    private OperateMapper operateMapper;

    @Autowired
    private BillTypeMapper typeMapper;

    /**
     * 查询单证控制管理列表
     *
     * @return 证控制管理列表
     */
    @Override
    public List<TicketCtrl> selectList() {
        List<TicketCtrl> ticketCtrls = operateMapper.selectCtrlList();
        for (TicketCtrl ticketCtrl : ticketCtrls) {
            BaseBillType code = typeMapper.selectOne(new QueryWrapper<BaseBillType>()
                    .eq("code", ticketCtrl.getCode())
                    .eq("domint",ticketCtrl.getDomint())
                    .eq("is_del", 0));
            if (code != null){
                ticketCtrl.setName(code.getName());
            }
            if(StringUtils.isNotEmpty(ticketCtrl.getDeptIds())
            && ticketCtrl.getDeptIds().contains(SecurityUtils.getHighParentId().toString())){
                ticketCtrl.setControlEnabled(1);
            }else{
                ticketCtrl.setControlEnabled(0);
            }
        }
        return ticketCtrls;
    }

    /**
     * 保存单证控制管理列表
     *
     * @param list 单证控制管理列表
     * @return 結果
     */
    @Override
    public int saveCtrl(List<TicketCtrl> list) {
        if (!CollectionUtils.isEmpty(list)){
            for (TicketCtrl ticketCtrl : list) {
//                ticketCtrl.setDeptId(SecurityUtils.getHighParentId());
                Long highParentId = SecurityUtils.getHighParentId();
                TicketCtrl ctrl = operateMapper.selectOne(new QueryWrapper<TicketCtrl>()
                        .eq("code", ticketCtrl.getCode()));
                if(ticketCtrl.getControlEnabled() == 0){
                    String[] split = ctrl.getDeptIds().split(",");
                    String collect = Arrays.stream(split)
                            .filter(s -> !s.equals(highParentId.toString()))
                            .collect(Collectors.joining(","));
                    ticketCtrl.setDeptIds(collect);
                }
                if(ticketCtrl.getControlEnabled() == 1){
                    String[] split = ctrl.getDeptIds().split(",");
                    String collect = Arrays.stream(split)
                            .filter(s -> !s.equals(highParentId.toString()))
                            .collect(Collectors.joining(","));
                    collect = collect + "," + highParentId.toString();
                    ticketCtrl.setDeptIds(collect);
                }
                operateMapper.updateById(ticketCtrl);
            }
        }
        return 1;
    }
}
