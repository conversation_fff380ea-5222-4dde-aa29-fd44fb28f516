package com.gzairports.wl.charge.service.impl;

import com.gzairports.wl.charge.domain.ChargeMethod;
import com.gzairports.wl.charge.domain.query.ChargeMethodQuery;
import com.gzairports.wl.charge.domain.vo.ChargeMethodVO;
import com.gzairports.wl.charge.mapper.ChargeMethodMapper;
import com.gzairports.wl.charge.service.ChargeMethodService;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 客户结算方式service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@Service
public class ChargeMethodServiceImpl implements ChargeMethodService {

    @Autowired
    private ChargeMethodMapper methodMapper;

    /**
     * 查询客户结算方式列表
     *
     * @param query 查询参数
     * @return 客户结算方式列表
     */
    @Override
    public List<ChargeMethodVO> selectList(ChargeMethodQuery query) {
        query.setDeptId(SecurityUtils.getHighParentId());
        List<ChargeMethodVO> list = methodMapper.selectList(query);
        for (ChargeMethodVO chargeMethodVO : list) {
            chargeMethodVO.setIsEnable(chargeMethodVO.getEnabled() == 0 ? "否" : "是");
        }
        return list;
    }

    /**
     * 新增客户结算方式
     *
     * @param method 客户结算方式
     * @return 结果
     */
    @Override
    public Long insertChargeMethod(ChargeMethod method) {
        method.setCreateBy(SecurityUtils.getUsername());
        method.setCreateTime(DateUtils.getNowDate());
        method.setDeptId(SecurityUtils.getHighParentId());
        methodMapper.insertChargeMethod(method);
        return method.getId();
    }

    /**
     * 修改客户结算方式
     *
     * @param method 客户结算方式
     * @return 结果
     */
    @Override
    public int updateChargeMethod(ChargeMethod method) {
        method.setUpdateBy(SecurityUtils.getUsername());
        method.setUpdateTime(DateUtils.getNowDate());
        return methodMapper.updateChargeMethod(method);
    }

    /**
     * 查询客户结算方式
     *
     * @param id 客户结算方式主键
     * @return 客户结算方式
     */
    @Override
    public ChargeMethodVO selectChargeById(Long id) {
        return methodMapper.selectChargeById(id);
    }

    /**
     * 删除客户结算方式
     *
     * @param id 客户结算方式主键
     * @return 结果
     */
    @Override
    public int deleteChargeMethod(Long id) {
        return methodMapper.removeChargeMethod(id);
    }
}
