package com.gzairports.common.charge.service.impl;

import com.gzairports.common.business.arrival.domain.HzArrItem;
import com.gzairports.common.business.arrival.domain.ItemDetail;
import com.gzairports.common.charge.domain.HzChargeItemRule;
import com.gzairports.common.charge.domain.vo.BillRuleVo;
import com.gzairports.common.charge.service.BillingRule;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.List;

/**
 * 按份计费规则
 * <AUTHOR>
 * @date 2024-08-03
 */
public class PerUnitBillingRule implements BillingRule {

    @Override
    public String className() {
        return "PerUnitBillingRule.class";
    }

    @Override
    public BillRuleVo calculateFee(List<HzChargeItemRule> rule, BigDecimal weight, Integer quantity, ItemDetail item) {
        HzChargeItemRule itemRule = rule.get(0);
        BillRuleVo vo = new BillRuleVo();
        BigDecimal multiply = itemRule.getUnitPrice().multiply(new BigDecimal(item.getUnit()));
        vo.setTotalCharge(multiply);
        vo.setQuantity(String.valueOf(item.getUnit()));
        vo.setRate(itemRule.getUnitPrice());
        return vo;
    }
}
