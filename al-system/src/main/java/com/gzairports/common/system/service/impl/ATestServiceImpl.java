package com.gzairports.common.system.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.gzairports.common.system.mapper.ATestMapper;
import com.gzairports.common.system.domain.ATest;
import com.gzairports.common.system.service.IATestService;

/**
 * 代码生成测试Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-09-18
 */
@Service
public class ATestServiceImpl extends ServiceImpl<ATestMapper,ATest> implements IATestService
{
    @Autowired
    private ATestMapper aTestMapper;

    /**
     * 查询代码生成测试
     * 
     * @param id 代码生成测试ID
     * @return 代码生成测试
     */
    @Override
    public ATest selectATestById(Long id)
    {
        return aTestMapper.selectATestById(id);
    }

    /**
     * 查询代码生成测试列表
     * 
     * @param aTest 代码生成测试
     * @return 代码生成测试
     */
    @Override
    public List<ATest> selectATestList(ATest aTest)
    {
        return aTestMapper.selectATestList(aTest);
    }

    /**
     * 新增代码生成测试
     * 
     * @param aTest 代码生成测试
     * @return 结果
     */
    @Override
    public int insertATest(ATest aTest)
    {
        return aTestMapper.insertATest(aTest);
    }

    /**
     * 修改代码生成测试
     * 
     * @param aTest 代码生成测试
     * @return 结果
     */
    @Override
    public int updateATest(ATest aTest)
    {
        return getBaseMapper().updateATest(aTest);
    }

    /**
     * 批量删除代码生成测试
     * 
     * @param ids 需要删除的代码生成测试ID
     * @return 结果
     */
    @Override
    public int deleteATestByIds(Long[] ids)
    {
        return aTestMapper.deleteATestByIds(ids);
    }

    /**
     * 删除代码生成测试信息
     * 
     * @param id 代码生成测试ID
     * @return 结果
     */
    @Override
    public int deleteATestById(Long id)
    {
        return aTestMapper.deleteATestById(id);
    }
}
