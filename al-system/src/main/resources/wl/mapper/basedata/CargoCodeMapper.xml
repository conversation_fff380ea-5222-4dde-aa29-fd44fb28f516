<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.CargoCodeMapper">
    <select id="selectCargoCodeList" resultType="com.gzairports.common.basedata.domain.BaseCargoCode">
        select id, category_code, code, chinese_name, english_name, is_special, is_arrival, is_out, domestic, international, special_cargo_code1 from base_cargo_code
        <where>
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="code != null  and code != ''"> and code like concat('%',#{code},'%')</if>
            <if test="isSpecial != null"> and is_special = #{isSpecial}</if>
            <if test="isArrival != null"> and is_arrival = #{isArrival}</if>
            <if test="isOut != null"> and is_out = #{isOut}</if>
            <if test="domestic != null"> and domestic = #{domestic}</if>
            <if test="international != null"> and international = #{international}</if>
            <if test="categoryCode != null"> and category_code = #{categoryCode}</if>
            <if test="chineseName != null"> and chinese_name like concat('%',#{chineseName},'%')</if>
            and is_del = 0
        </where>
    </select>
    <select id="selectById" resultType="com.gzairports.common.basedata.domain.BaseCargoCode">
        select id, category_code, code, chinese_name, english_name, is_special, is_arrival, is_out, domestic, international, special_cargo_code1 from base_cargo_code where id = #{cargoId}
    </select>
    <select id="selectByCode" resultType="com.gzairports.common.basedata.domain.BaseCargoCode">
        select id, code, chinese_name, english_name, category_code, special_cargo_code1
        from base_cargo_code
        where code = #{code} and is_del = 0
    </select>
    <select id="selectByName" resultType="java.lang.String">
        select code
        from base_cargo_code
        where LEFT(code,2) = category_code
        and chinese_name = #{s}
        and is_del = 0
        limit 1
    </select>
    <select id="selectCodeByName" resultType="com.gzairports.common.basedata.domain.BaseCargoCode">
        select code, category_code from base_cargo_code where LEFT(code,2) = category_code and chinese_name = #{s} and is_del = 0
    </select>
</mapper>
