package com.gzairports.wl.cargofee.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.wl.cargofee.domain.CustomPay;
import com.gzairports.wl.cargofee.domain.query.PayRecordQuery;
import com.gzairports.wl.cargofee.mapper.CustomPayMapper;
import com.gzairports.wl.cargofee.service.ICustomPayService;
import com.gzairports.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 客户支付流水Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@Service
public class CustomPayService extends ServiceImpl<CustomPayMapper, CustomPay> implements ICustomPayService {

    @Autowired
    private CustomPayMapper customPayMapper;

    /**
     * 查询客户支付流水数据
     * @param query 查询参数
     * @return 客户支付流水数据
     */
    @Override
    public List<CustomPay> selectList(PayRecordQuery query) {
        query.setDeptId(SecurityUtils.getHighParentId());
        List<CustomPay> customPays = customPayMapper.selectListByQuery(query);
        for (CustomPay customPay : customPays) {
            String s = customPay.getStatus() == 0 ? "失败" : "成功";
            customPay.setStrStart(s);
            String type = customPay.getType() == 0 ? "支付" : "退款";
            customPay.setStrType(type);
        }
        return customPays;
    }
}
