package com.gzairports.common.business.arrival.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 未提货办单运单数据
 *
 * <AUTHOR>
 * @date 2024-06-07
 */
@Data
public class NotPickedUpVo {

    /** 理货id */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long tallyId;

    /** 运单号 */
    private String waybillCode;

    /** 航班号 */
    private String flightNo;

    /** 始发站 */
    private String sourcePort;

    /** 品名 */
    private String cargoName;

    /** 特货代码 */
    private String specialCargoCode1;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 收货人 */
    private String consignee;

    /** 收货代理人 */
    private String agentCompany;

    /** 可提货件数 */
    private Integer canQuantity;

    /** 可提货重量 */
    private BigDecimal canWeight;

    /** 入库时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date storeTime;

    /** 理货完成时间 */
    private String tallyTime;

    /** 理货完成时间 */
    private Date tallyTime1;
}
