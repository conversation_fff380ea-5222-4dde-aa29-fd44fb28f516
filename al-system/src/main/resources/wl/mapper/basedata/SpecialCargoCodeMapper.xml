<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.SpecialCargoCodeMapper">
    <select id="selectSpecialCargoCodeList" resultType="com.gzairports.common.basedata.domain.BaseSpecialCargoCode">
        select id, handle_code, air_company, is_danger, chinese_description, english_description, carrier_code from base_special_cargo_code
        <where>
        and is_del = 0
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="airCompany != null  and airCompany != ''"> and air_company like concat('%',#{airCompany},'%')</if>
            <if test="handleCode != null and handleCode != ''"> and handle_code like concat('%',#{handleCode},'%')</if>
            <if test="isDanger != null"> and is_danger = #{isDanger}</if>
        </where>
    </select>
</mapper>