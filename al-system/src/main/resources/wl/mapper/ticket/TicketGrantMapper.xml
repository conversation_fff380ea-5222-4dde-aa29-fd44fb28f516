<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.wl.ticket.mapper.TicketGrantMapper">
    <update id="updateByRecordId">
        update wl_ticket_grant set status = 1 where id = #{recordId}
    </update>
    <update id="reissue">
        update wl_ticket_grant set status = 0 where id = #{recordId}
    </update>
    <select id="selectListByTicketId" resultType="com.gzairports.wl.ticket.domain.vo.TicketGrantVO">
        select wgr.id, wgr.ticket_id, wgr.grant_num, wgr.total, su.user_name, sd.dept_name, wgr.grant_time, wgr.status
        from wl_ticket_grant wgr
                 left join sys_user su on su.user_id = wgr.user_id
                 left join sys_dept sd on su.dept_id = sd.dept_id
        where  wgr.ticket_id = #{id} and wgr.status = 0
    </select>
    <select id="selectNew" resultType="com.gzairports.wl.ticket.domain.TicketGrant">
        select wgr.id, wgr.ticket_id, wgr.grant_num, wgr.total, wgr.grant_time
        from wl_ticket_grant wgr where wgr.ticket_id = #{id} order by wgr.grant_time desc limit 1
    </select>
    <select id="selectTotal" resultType="java.lang.Integer">
        select sum(total) from wl_ticket_grant where ticket_id = #{id}
    </select>
</mapper>