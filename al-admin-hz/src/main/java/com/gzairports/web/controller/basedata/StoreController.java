package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BaseStore;
import com.gzairports.common.basedata.domain.query.StoreQuery;
import com.gzairports.common.basedata.service.IStoreService;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 仓库管理Controller
 *
 * <AUTHOR>
 * @date 2024-02-20
 */
@RestController
@RequestMapping("/base/store")
@Api(tags = "仓库管理数据接口")
public class StoreController extends BaseController {

    @Autowired
    private IStoreService storeService;

    /**
     * 查询仓库列表
     */
//    @PreAuthorize("@ss.hasPermi('base:store:storeList')")
    @GetMapping("/storeList")
    @ApiOperation(value = "仓库管理查询")
    public TableDataInfo storeList(StoreQuery query)
    {
        startPage();
        List<BaseStore> list = storeService.storeList(query);
        return getDataTable(list);
    }

    @GetMapping("/storeSelect")
    @ApiOperation(value = "查询仓库和库位")
    public AjaxResult storeSelect()
    {
        return AjaxResult.success(storeService.storeSelect());
    }

    /**
     * 导出仓库列表
     */
    @PreAuthorize("@ss.hasPermi('base:store:exportStore')")
    @Log(title = "导出仓库列表", businessType = BusinessType.EXPORT)
    @PostMapping("/exportStore")
    @ApiOperation(value = "导出仓库列表")
    public void exportStore(HttpServletResponse response, StoreQuery query)
    {
        List<BaseStore> list = storeService.storeList(query);
        ExcelUtil<BaseStore> util = new ExcelUtil<BaseStore>(BaseStore.class);
        util.exportExcel(response, list, "仓库列表");
    }

    /**
     * 新增仓库
     */
    @PreAuthorize("@ss.hasPermi('base:store:add')")
    @Log(title = "新增仓库", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ApiOperation(value = "新增仓库")
    public AjaxResult add(@RequestBody BaseStore store)
    {
        return toAjax(storeService.add(store));
    }

    /**
     * 修改仓库
     */
    @PreAuthorize("@ss.hasPermi('base:store:edit')")
    @Log(title = "修改仓库", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ApiOperation(value = "修改仓库")
    public AjaxResult edit(@RequestBody BaseStore store)
    {
        return toAjax(storeService.edit(store));
    }

    /**
     * 删除仓库
     */
    @PreAuthorize("@ss.hasPermi('base:store:del')")
    @Log(title = "删除仓库", businessType = BusinessType.UPDATE)
    @GetMapping("/del/{id}")
    @ApiOperation(value = "删除仓库")
    public AjaxResult del(@PathVariable("id") Long id)
    {
        return toAjax(storeService.del(id));
    }

    /**
     * 详情
     */
    @PreAuthorize("@ss.hasPermi('base:store:getInfo')")
    @GetMapping("/getInfo/{id}")
    @ApiOperation(value = "详情")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(storeService.getInfo(id));
    }
}
