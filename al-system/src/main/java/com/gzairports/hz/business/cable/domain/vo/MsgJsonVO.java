package com.gzairports.hz.business.cable.domain.vo;

import lombok.Data;

import java.util.List;

/**
 * 发报数据对象
 * <AUTHOR>
 * @date 2025/3/28
 */
@Data
public class MsgJsonVO {

    /** 收报地址 */
    private String[] address;

    /** 邮件地址 */
    private String[] receiveMailAddress;

    /** FTP地址 */
    private String receiveFtpAddress;

    /** FTP目录 */
    private String receiveFtpFolder;

    /** mq队列 */
    private String receiveMQQueue;

    /** 发送类型 */
    private String sendType;

    /** 承运人（ SCM UCM FBL SPH FWB FHL必填） */
    private String carrier;

    /** 出发航站 （FFM SCM UCM FBL SPH FWB FHL必填） */
    private String departureStation;

    /** 航班日期（FFM SCM UCM FBL SPH必填） */
    private String flightDate;

    /** 航班号 */
    private String flightNo;

    /** 航班类型 */
    private String flightType;

    /** 报文类型 */
    private String msgType;

    /** 报文版本号 */
    private String msgVersion;

    /** 下一到达站/目的航站 （FFM SCM UCM FBL SPH必填） */
    private String nextStation;

    /** 操作日期（FWB FHL FSU必填） */
    private String operationDate;

    /** 操作节点（FSU必填） */
    private String operationNode;

    /** 操作航站（FWB FHL FSU必填） */
    private String operationStation;

    /** 发报地址 */
    private String[] origin;

    /** 优先级 */
    private String[] priority;

    /** 运单前缀（FWB FHL FSH必填） */
    private String waybillPrefix;

    private String sender;

    private String receiver;

    /** 报文json */
    private String msgJson;

    /** 报文内容 */
    private String content;

    private String sourceId;

    /** 消息唯一标识 */
    private String uniqueId;

    /** iata代码 */
    private String iata;

    /** 客户代码 */
    private String clientShort;
}
