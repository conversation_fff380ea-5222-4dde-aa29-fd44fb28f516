package com.gzairports.hz.business.departure.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.business.arrival.domain.FlightLoadUldWaybill;
import com.gzairports.common.business.arrival.domain.FlightLoadWaybill;
import com.gzairports.common.business.arrival.mapper.FlightInfoMapper;
import com.gzairports.common.business.arrival.mapper.FlightLoadUldWaybillMapper;
import com.gzairports.common.business.arrival.mapper.FlightLoadWaybillMapper;
import com.gzairports.common.business.departure.domain.WaybillTrace;
import com.gzairports.common.business.departure.mapper.PullDownMapper;
import com.gzairports.common.business.departure.service.impl.WaybillTraceServiceImpl;
import com.gzairports.common.business.wrong.domain.Wrong;
import com.gzairports.common.business.wrong.mapper.WrongMapper;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.common.log.service.IWaybillLogService;
import com.gzairports.common.message.domain.Message;
import com.gzairports.common.message.domain.vo.SocketMessageVo;
import com.gzairports.common.message.mapper.MessageMapper;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.ServletUtils;
import com.gzairports.hz.business.departure.domain.*;
import com.gzairports.hz.business.departure.domain.query.PullDownQuery;
import com.gzairports.hz.business.departure.domain.vo.PullDownInfoVo;
import com.gzairports.hz.business.departure.domain.vo.PullDownLoadInfoVo;
import com.gzairports.hz.business.departure.domain.vo.PullDownVo;
import com.gzairports.hz.business.departure.domain.vo.WaybillIdAndCodeVo;
import com.gzairports.hz.business.departure.mapper.*;
import com.gzairports.hz.business.departure.rabbitmq.WaybillMessageProducer;
import com.gzairports.hz.business.departure.service.IPullDownService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 临时拉下Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-08
 */
@Service
public class PullDownServiceImpl extends ServiceImpl<PullDownMapper, HzDepPullDown> implements IPullDownService {

    @Autowired
    private PullDownMapper pullDownMapper;

    @Autowired
    private FlightLoadMapper flightLoadMapper;

    @Autowired
    private FlightInfoMapper infoMapper;

    @Autowired
    private FlightLoadUldMapper loadUldMapper;

    @Autowired
    private WaybillTraceServiceImpl traceService;

    @Autowired
    private AllAirWaybillMapper airWaybillMapper;

    @Autowired
    private FlightLoadUldWaybillMapper loadUldWaybillMapper;

    @Autowired
    private FlightLoadWaybillMapper loadWaybillMapper;

    @Autowired
    private WrongMapper wrongMapper;

    @Autowired
    private IWaybillLogService waybillLogService;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    @Autowired
    private WaybillMessageProducer waybillMessageProducer;

    @Autowired
    private MessageMapper messageMapper;



    /**
     * 查询临时拉下列表
     * @param query 查询条件
     * @return 结果
     */
    @Override
    public PullDownVo selectList(PullDownQuery query) {
        PullDownVo vo = new PullDownVo();
        List<PullDownInfoVo> list = pullDownMapper.selectListByQuery(query);
        if (!CollectionUtils.isEmpty(list)){
            vo.setTotalNum(list.size());
            vo.setVos(list);
            ArrayList<PullDownInfoVo> collect = list.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                    new TreeSet<>(Comparator.comparing(
                            o -> o.getFlightNo() + ";" + o.getExecDate() + ";" + o.getWaybillCode()))), ArrayList::new));
            int totalLoadQuantity = collect.stream()
                    .filter(item -> item.getLoadQuantity() != null && item.getLoadQuantity() != 0)
                    .mapToInt(PullDownInfoVo::getLoadQuantity).sum();
            vo.setTotalLoadQuantity(totalLoadQuantity);

            BigDecimal totalLoadWeight = collect.stream()
                    .filter(item -> item.getLoadWeight() != null && item.getLoadWeight().compareTo(BigDecimal.ZERO) != 0)
                    .map(PullDownInfoVo::getLoadWeight).reduce(BigDecimal.ZERO,BigDecimal::add);
            vo.setTotalLoadWeight(totalLoadWeight);

            int totalPullQuantity = list.stream()
                    .filter(item -> item.getQuantity() != null && item.getQuantity() != 0)
                    .mapToInt(PullDownInfoVo::getQuantity).sum();
            vo.setTotalPullQuantity(totalPullQuantity);

            BigDecimal totalPullWeight = list.stream()
                    .filter(item -> item.getWeight() != null && item.getWeight().compareTo(BigDecimal.ZERO) != 0)
                    .map(PullDownInfoVo::getWeight).reduce(BigDecimal.ZERO,BigDecimal::add);
            vo.setTotalPullWeight(totalPullWeight);
        }
        return vo;
    }

    /**
     * 新增拉下数据
     * @param pullDown 新增参数
     * @return 结果
     */
    @Override
    public int add(HzDepPullDown pullDown) {
        if (pullDown.getQuantity() > pullDown.getLoadQuantity()){
            throw new CustomException("拉下件数不能超过配载件数");
        }
        if (pullDown.getWeight().compareTo(pullDown.getLoadWeight()) > 0){
            throw new CustomException("拉下重量不能超过配载重量");
        }

        HttpServletResponse response = ServletUtils.getResponse();
        WaybillLog waybillLog = new WaybillLog();
        try {
            AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                    .eq("waybill_code", pullDown.getWaybillCode())
                    .eq("type", "DEP")
                    .eq("is_del", 0));
            airWaybill.setStatus("pull_down");
            airWaybill.setUpdateTime(new Date());
            airWaybillMapper.updateById(airWaybill);

            waybillLog = waybillLogService.getWaybillLog(
                    airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                    pullDown.getWeight().toString(), pullDown.getQuantity().toString(), airWaybill.getFlightNo1(),
                    pullDown, null, 0, null, new Date(),
                    "临时拉下,件数:" + pullDown.getQuantity() + ",重量:" + pullDown.getWeight(),
                    airWaybill.getType(), pullDown.getUld());


            // todo 拉下重量=总重-uld重量-垫板质量,否则拉下不成功

            WaybillTrace waybillTrace = new WaybillTrace();
            waybillTrace.setOperTime(new Date());
            waybillTrace.setOperPieces(pullDown.getQuantity());
            waybillTrace.setOperWeight(pullDown.getWeight());
            waybillTrace.setWaybillCode(airWaybill.getWaybillCode());
            waybillTrace.setNodeName("临时拉下");
            traceService.insertWaybillTrace(waybillTrace);


            pullDown.setOperTime(new Date());
            pullDown.setOperName(SecurityUtils.getUsername());

            int insert = pullDownMapper.insert(pullDown);

            Wrong wrong = new Wrong();
            wrong.setDeptId(airWaybill.getDeptId());
            wrong.setWrongType("OFLD 拉货");
            wrong.setWaybillCode(pullDown.getWaybillCode());
            wrong.setType("DEP");
            wrong.setCreateTime(new Date());
            wrong.setRegisterTime(new Date());
            wrong.setWrongRemark(pullDown.getRemark());
            wrong.setAgent(airWaybill.getAgentCompany());
            //默认设置为货站处理 ->1213 默认设置为新增
            wrong.setStatus(0);
            wrong.setPullId(pullDown.getId());
            wrongMapper.insert(wrong);

            //发送消息
//            CompletableFuture.runAsync(()->
//            sendMessage(pullDown.getWaybillCode(),"OFLD 拉货",airWaybill.getAgentCompany()));
            sendMessage(pullDown.getWaybillCode(),"OFLD 拉货",airWaybill.getAgentCompany());

            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作成功" +  "," +
                            "code:" + response.getStatus() + "," +
                            "data:" + insert));
            return insert;
        } catch (Exception e) {
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" +  "," +
                            "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        } finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }

    @Async
    public void sendMessage(String waybillCode, String s,String agentCompany) {
        String message = "    运单"+waybillCode+"不正常货邮"+s+"  \n" +
                "运单："+ waybillCode +"不正常，不支持类型"+ s + "请及时处理。";
        SocketMessageVo vo = new SocketMessageVo();
        vo.setMessage(message);
        vo.setType(6);
        vo.setDeptId(sysDeptMapper.selectDeptIdByDeptName(agentCompany));

        Message mes = new Message();
        mes.setContent(message);
        mes.setCreateTime(new Date());
        mes.setIsHandle(0);
        mes.setPostId(SecurityUtils.getDeptId());
        mes.setTitle("不正常货邮提醒");
        messageMapper.insert(mes);
        waybillMessageProducer.send(vo);
    }

    /**
     * 查询航班配载信息
     * @param vo 查询条件
     * @return 结果
     */
    @Override
    public PullDownLoadInfoVo selectLoadInfo(PullDownInfoVo vo) {
        PullDownLoadInfoVo infoVo = new PullDownLoadInfoVo();
        int loadQuantity = 0;
        BigDecimal loadWeight = new BigDecimal(0);
        Long flightId = infoMapper.selectIdByVo(vo);
        if (flightId == null){
            throw new CustomException("无当前航班信息");
        }

        AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                .eq("waybill_code", vo.getWaybillCode())
                .eq("type","DEP")
                .eq("is_del",0));
        if (airWaybill == null){
            throw new CustomException("无运单信息");
        }

        List<Long> legIds = flightLoadMapper.selectLegIdByVo(flightId);
        for (Long legId : legIds) {
            List<FlightLoadWaybill> list = loadWaybillMapper.selectLoadInfoList(legId,airWaybill.getId());
            if (!CollectionUtils.isEmpty(list)){
                BigDecimal weight = list.stream().map(FlightLoadWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                loadWeight = loadWeight.add(weight);
                int quantity = list.stream().mapToInt(FlightLoadWaybill::getQuantity).sum();
                loadQuantity += quantity;
            }
        }
        infoVo.setLoadWeight(loadWeight);
        infoVo.setLoadQuantity(loadQuantity);
        infoVo.setFlightId(flightId);
        return infoVo;
    }

    /**
     * 根据id查询详情
     * @param id 拉下id
     * @return 详情
     */
    @Override
    public PullDownInfoVo getInfo(Long id) {
//        AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
//                .eq("waybill_code", vo.getWaybillCode())
//                .eq("type", "DEP"));
//        int loadQuantity = 0;
//        BigDecimal loadWeight = new BigDecimal(0);
//        List<FlightLoadUldWaybill> loadUldWaybills = loadUldWaybillMapper.selectList(new QueryWrapper<FlightLoadUldWaybill>()
//                .eq("waybill_id", airWaybill.getId()));
//        if (!CollectionUtils.isEmpty(loadUldWaybills)){
//            int quantity = loadUldWaybills.stream().mapToInt(FlightLoadUldWaybill::getQuantity).sum();
//            loadQuantity = loadQuantity + quantity;
//            BigDecimal weight = loadUldWaybills.stream().map(FlightLoadUldWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
//            loadWeight = loadWeight.add(weight);
//        }
//
//        List<FlightLoadWaybill> waybills = loadWaybillMapper.selectList(new QueryWrapper<FlightLoadWaybill>()
//                .eq("waybill_id", airWaybill.getId()));
//        if (!CollectionUtils.isEmpty(waybills)){
//            int quantity = waybills.stream().mapToInt(FlightLoadWaybill::getQuantity).sum();
//            loadQuantity = loadQuantity + quantity;
//            BigDecimal weight = waybills.stream().map(FlightLoadWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
//            loadWeight = loadWeight.add(weight);
//        }
//        vo.setLoadQuantity(loadQuantity);
//        vo.setLoadWeight(loadWeight);
        return pullDownMapper.selectInfoById(id);
    }

    /**
     * 修改拉下数据
     * @param pullDown 拉下数据
     * @return 结果
     */
    @Override
    public int edit(HzDepPullDown pullDown) {
        return pullDownMapper.updateById(pullDown);
    }

    /**
     * 根据运单后四位或八位运单号查询运单
     * @param waybillCode 后四位或八位运单号
     * @return 运单列表
     */
    @Override
    public List<WaybillIdAndCodeVo> getWaybill(String waybillCode) {
        LambdaQueryWrapper<AirWaybill> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.likeLeft(AirWaybill::getWaybillCode,waybillCode);
        List<AirWaybill> waybillList = airWaybillMapper.selectList(queryWrapper);
        List<WaybillIdAndCodeVo> resultList = new ArrayList<>();
        for (AirWaybill airWaybill : waybillList) {
            WaybillIdAndCodeVo resultOne = new WaybillIdAndCodeVo();
            resultOne.setWaybillId(airWaybill.getId());
            resultOne.setWaybillCode(airWaybill.getWaybillCode());
            resultList.add(resultOne);
        }
        return resultList;
    }

    /**
     *  列表对象去重
     */
    public <K,T> Predicate<K> distinctPredicate(Function<K,T> function){
        ConcurrentHashMap<T,Boolean> map = new ConcurrentHashMap<>();
        return t->null == map.putIfAbsent(function.apply(t),true);
    }
}
