<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.SpecialCodeMapper">
    <select id="selectSpecialCodeList" resultType="com.gzairports.common.basedata.domain.BaseSpecialCode">
        select id, code, is_danger, chinese_description, english_description from base_special_code
        <where>
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="list" item="id" index="index" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="code != null  and code != ''"> and code like concat('%',#{code},'%')</if>
            <if test="isDanger != null"> and is_danger = #{isDanger}</if>
        </where>
    </select>

    <select id="selectListById" resultType="java.lang.String">
        SELECT code FROM base_special_code WHERE id in
        <foreach collection="specialId" item="id" open="(" separator="," close=")">
            #{specialId}
        </foreach>
    </select>
</mapper>