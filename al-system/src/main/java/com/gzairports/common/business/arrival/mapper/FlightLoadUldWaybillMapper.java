package com.gzairports.common.business.arrival.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.gzairports.common.business.arrival.domain.FlightLoadUldWaybill;
import com.gzairports.common.business.arrival.domain.FlightLoadWaybill;
import com.gzairports.hz.business.cable.domain.vo.MsgFlightInfoVO;
import com.gzairports.hz.business.departure.domain.FlightLoadUld;
import com.gzairports.hz.business.departure.domain.vo.FormalWaybillVo;
import com.gzairports.hz.business.departure.domain.vo.ForwardImportWaybillVo;
import com.gzairports.hz.business.departure.domain.vo.LoadUldVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 集装器预配关联的运单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@Mapper
public interface FlightLoadUldWaybillMapper extends BaseMapper<FlightLoadUldWaybill> {

    /**
     * 根据配载id查询板箱运单
     * @param bizId 配载id
     * @return 板箱运单
     */
    List<LoadUldVo> selectListById(Long bizId);

    /**
     * 根据装载板箱id查询运单数据
     * @param id 装载板箱id
     * @return 运单数据
     */
    List<ForwardImportWaybillVo> selectLoadWaybillListByUldId(Long id);

    /**
     * 根据板箱装载id查询运单数据
     * @param id 板箱装载id
     * @return 结果
     */
    List<FormalWaybillVo> selectListByLoadUldId(@Param("id") Long id,@Param("type") Integer type);

    /**
     * 根据板箱装载id和板箱运单id查询运单数据
     * */
    List<FormalWaybillVo> selectListByLoadUldIdAndUldWaybillId(@Param("id")Long id,@Param("uldWaybillIds") List<Long> uldWaybillIds);

    /**
     * 查询板箱的配载数据
     * @param collectId 运单id
     * @return 配载数据
     */
    List<FlightLoadWaybill> selectLoadList(Long collectId);

    List<FlightLoadUld> selectUldInfo(@Param("id") Long id, @Param("id1") Long id1);

    List<FlightLoadUldWaybill> selectListByIds(@Param("flightLoadWaybillIds") List<Long> flightLoadWaybillIds);

    List<MsgFlightInfoVO> selectFlightInfoById(@Param("waybillIdList") List<Long> waybillIdList);
}
