package com.gzairports.hz.business.arrival.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 航班文件运单列表参数
 * <AUTHOR>
 * @date 2024-07-16
 */
@Data
public class FlightFileWaybillVo {

    /** 运单id */
    private Long id;

    /** 录单id */
    private Long orderId;

    /** 代理人 */
    private Long deptId;

    /** 计费方式 */
    private String billingMethod;

    /** 运单号 */
    private String waybillCode;

    /** 是否补货单 */
    private Integer replenishBill;

    /** 是否中转单 */
    private Integer transferBill;

    /** 处理方式 */
    private String method;

    /** 是否理货 */
    private String isTally;

    /** 运单到达 */
    private Integer fileArr;

    /** 件数 */
    private Integer quantity;

    /** 舱单件数 */
    private Integer cabinQuantity;

    /** 理货件数 */
    private Integer tallyQuantity;

    /** 重量 */
    private BigDecimal weight;

    /** 舱单重量 */
    private BigDecimal cabinWeight;

    /** 理货重量 */
    private BigDecimal tallyWeight;

    /** 收货人 */
    private String consign;

    /** 代理人简称 */
    private String deptNameAbb;

    /** 品名（邮件种类） */
    private String cargoName;

    /** 特货代码 */
    private String specialCargoCode1;

    /** 备注 */
    private String remark;

    /** 次数 */
    private Integer number;

    /** 航段id */
    private Long legId;

    /** 运单列表是否显示红色 0否 1是 */
    private Integer isTallyWrong;
    
    /** 是否审核 0 否 1 是 */
    private Integer isExamine;
}
