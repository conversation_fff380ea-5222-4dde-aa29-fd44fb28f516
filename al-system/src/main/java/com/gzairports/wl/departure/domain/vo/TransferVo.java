package com.gzairports.wl.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 出港货物交接列表返回参数
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
public class TransferVo {

    /** 主键id */
    private Long id;

    /** 编号 */
    private String num;

    /** 代理人 */
    private String agent;

    /** 运单数 */
    private Integer waybillNum;

    /** 提交时间 */
    private String submitTime;

    /** 提交人 */
    private String submitter;

    /** 库管员 */
    private String storeKeeper;

    /** 状态（提交 暂存 取消） */
    private String status;

    /** 类型 0 普货交接单 1 特货交接单 */
    private Integer type;

    /** 日期 */
    private String date;

    /** 备注 */
    private String remark;

    /** 航班号 */
    private String flightNo;

    /** 目的站 */
    private String des;

    /** 装机地点 */
    private String loading;

    /** 卸机地点 */
    private String unloading;

    /** 运单号 */
    private String waybillCode;

    /** 件数 */
    private String quantity;

    /** 重量 */
    private String weight;

    /** 包装方式 */
    private String packWay;

    /** 货品编码 */
    private String cargoCode;

    /** 品名 */
    private String cargoName;

    /** 货物情况 */
    private String cargoCondition;

    /** 交货人 */
    private String delivery;

    /** 交货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deliveryTime;

    /** 交货备注 */
    private String deliveryRemark;

    /** 第一接货人 */
    private String firstReceiver;

    /** 第一接货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date firstReceiverTime;

    /** 第一接货备注 */
    private String firstReceiverRemark;

    /** 第二接货人 */
    private String secondReceiver;

    /** 第二接货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date secondReceiverTime;

    /** 第二接货备注 */
    private String secondReceiverRemark;

    /** 第三接货人 */
    private String thirdReceiver;

    /** 第三接货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date thirdReceiverTime;

    /** 第三接货备注 */
    private String thirdReceiverRemark;

    /** 货物交接运单列表 */
    List<TransferMawbVo> vos;
}
