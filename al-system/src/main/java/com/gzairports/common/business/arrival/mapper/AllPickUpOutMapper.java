package com.gzairports.common.business.arrival.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.arrival.domain.AllPickUpOut;
import com.gzairports.common.business.arrival.domain.HzArrTally;
import com.gzairports.common.business.arrival.domain.query.PickUpOutQuery;
import com.gzairports.common.business.arrival.domain.vo.AppPickUpVo;
import com.gzairports.common.business.arrival.domain.vo.PickUpInfoVo;
import com.gzairports.common.business.arrival.domain.vo.PickUpVo;
import com.gzairports.common.business.reporter.domain.ReportPickOut;
import com.gzairports.common.charge.domain.vo.HzChargeItemsVo;
import com.gzairports.hz.business.arrival.domain.HzArrRecordOrder;
import com.gzairports.hz.business.arrival.domain.query.CostWaybillQuery;
import com.gzairports.hz.business.arrival.domain.query.TallyWaybillKey;
import com.gzairports.hz.business.arrival.domain.query.WaybillQuery;
import com.gzairports.hz.business.arrival.domain.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 国内提货出库Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-19
 */
@Mapper
public interface AllPickUpOutMapper extends BaseMapper<AllPickUpOut> {

    /**
     * 查询提货数据
     * @param query 查询条件
     * @return 结果
     */
    List<WaybillQueryVo> selectQueryList(WaybillQuery query);

    /**
     * 运单明细提货列表查询
     * @param waybillCode 运单号
     * @return 提货列表
     */
    List<ThVo> selectThList(String waybillCode);

    /**
     * 根据运单号查询理货id
     * @param waybillCode 运单号
     * @return 结果
     */
    List<Long> selectTallyIdtList(String waybillCode);

    /**
     * 根据理货id查询提货时间
     * @param tallyId 理货id
     * @param waybillCode 运单号
     * @return 提货时间
     */
    Date selectTime(@Param("tallyId") Long tallyId,@Param("waybillCode") String waybillCode);

    /**
     * 已提货出库查询列表
     * @param query 查询条件
     * @return 已提货出库列表
     */
    List<PickUpVo> pickedUp(PickUpOutQuery query);

    /**
     * 查询提货信息
     * @param waybillCode 运单号
     * @return 结果
     */
    PickUpInfoVo getInfo(String waybillCode);

    /**
     * 转南航
     * */
    int updateTransferSouth(Long id);

    /**
     * 查询转南航
     * */
    List<HzArrRecordOrder> selectTransferSouthByPickUpId(Long pickUpId);

    /**
     * 根据运单号(理货id/办单id)查询是否为出库
     * */
    AllPickUpOut selectOutTime(AppPickUpVo appPickUpVo);

    List<ReportPickOut> selectReportPuckOutList(@Param("lastSyncTime") Date lastSyncTime,@Param("dateNow") Date dateNow);

    String selectReportOutTime(@Param("tallyId") Long tallyId,@Param("waybillCode") String waybillCode);

    int updateAllPickUpOutById(AllPickUpOut pickUpOut);

    List<BillExportVo> billExport(@Param("query") CostWaybillQuery query);

    List<AllPickUpOut> selectByKey(@Param("pickUpWaybillPairs") List<TallyWaybillKey> pickUpWaybillPairs);

    List<AllPickUpOut> selectBatch(@Param("pickUpWaybillPairs") List<TallyWaybillKey> keys);
//    List<AllPickUpOut> selectBatch(@Param("keys") List<TallyWaybillKey> keys);

    /**
     * 查询提货办单数据
     * @param query
     * @return
     */
    List<BillExportAWBMVo> billExportAWBM(@Param("query") CostWaybillQuery query);

    /**
     * 查询运单的费用项目
     * @param waybillCodes
     * @return
     */
    List<HzChargeItemsVo> selectBillCostDetail(List<String> waybillCodes);


    List<HzArrTally> selectTallyList(List<String> waybillCodeList);

    List<AllPickUpOut> selectBatchByTallyId(@Param("tallyKeys") List<NodeTallyInfoVo> tallyKeys);

    List<AllPickUpOut> selectBatchById(@Param("idList") List<Long> idList);
}
