package com.gzairports.hz.business.departure.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 过磅记录表
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Data
@TableName("hz_collect_weight")
public class HzCollectWeight{

    /** 主键id */
    private Long id;

    /** 运单收运id */
    private Long collectId;

    /** uld号 */
    private String uld;

    /** 仓库 */
    private String store;

    /** 库位 */
    private String locator;

    /** 时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date weightTime;

    /** 过磅件数 */
    private Integer quantity;

    /** 过磅重量 */
    private BigDecimal weight;

    /** 总重量 */
    private BigDecimal totalWeight;

    /** 垫板重量 */
    private BigDecimal plateWeight;

    /** 板自重 */
    private BigDecimal boardWeight;

    /** 备注 */
    private String remark;

    /** 目的站 */
    private String desPort;

    private String airWays;

    /** 是否代运导入 */
    private Integer isLoad;
}
