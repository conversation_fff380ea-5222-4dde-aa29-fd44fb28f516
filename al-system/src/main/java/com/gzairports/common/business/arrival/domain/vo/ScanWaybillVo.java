package com.gzairports.common.business.arrival.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * app扫码出库运单数据
 *
 * <AUTHOR>
 * @date 2024-08-07
 */
@Data
public class ScanWaybillVo {

    /** 理货id */
    private Long tallyId;

    /** 运单号 */
    private String waybillCode;

    /** 代理人 */
    private String consign;

    /** 收货人 */
    private String shipper;

    /** 办单件数 */
    private BigDecimal canPickUpWeight;

    /** 办单重量 */
    private Integer canPickUpQuantity;

    /** 提货出库时间 */
    private Date outTime;
}
