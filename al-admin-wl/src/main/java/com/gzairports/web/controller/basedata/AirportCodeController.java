package com.gzairports.web.controller.basedata;


import com.gzairports.common.annotation.Log;
import com.gzairports.common.business.departure.domain.FlightInfo;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.common.basedata.domain.BaseAirportCode;
import com.gzairports.common.basedata.domain.query.AirportCodeQuery;
import com.gzairports.common.basedata.service.IAirportCodeService;
import com.gzairports.hz.business.departure.service.IFlightInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 机场代码Controller
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@RestController
@RequestMapping("/base/airportCode")
@Api(tags = "机场代码数据接口")
public class AirportCodeController extends BaseController {

    @Autowired
    private IAirportCodeService airportCodeService;

    @Autowired
    private IFlightInfoService businessFlightInfoService;

    /**
     * 查询机场代码列表
     */
//    @PreAuthorize("@ss.hasPermi('base:airportCode:airportCodeList')")
    @GetMapping("/airportCodeList")
    @ApiOperation(value = "查询机场代码列表")
    public TableDataInfo airportCodeList(AirportCodeQuery query)
    {
        startPage();
        List<BaseAirportCode> list = airportCodeService.selectAirportCodeList(query);
        return getDataTable(list);
    }

    /**
     * 导出机场代码
     */
    @PreAuthorize("@ss.hasPermi('base:airportCode:exportAirportCode')")
    @Log(title = "导出机场代码", businessType = BusinessType.EXPORT)
    @PostMapping("/exportAirportCode")
    @ApiOperation(value = "导出机场代码")
    public void exportAirportCode(HttpServletResponse response, AirportCodeQuery query)
    {
        List<BaseAirportCode> list = airportCodeService.selectAirportCodeList(query);
        ExcelUtil<BaseAirportCode> util = new ExcelUtil<BaseAirportCode>(BaseAirportCode.class);
        util.exportExcel(response, list, "机场代码");
    }

    /**
     * 根据航班号获取最新航班日期
     */
    @GetMapping(value = "/getDateByNo")
    @ApiOperation(value = "根据航班号获取最新航班日期")
    public AjaxResult getDateByNo(String flightNo)
    {
        return AjaxResult.success("操作成功",businessFlightInfoService.getDateByNo(flightNo));
    }
}
