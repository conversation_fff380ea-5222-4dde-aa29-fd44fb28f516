package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BaseRegionCode;
import com.gzairports.common.basedata.domain.query.RegionCodeQuery;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.common.basedata.service.IRegionCodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 国家或地区代码Controller
 *
 * <AUTHOR>
 * @date 2024-02-20
 */
@RestController
@RequestMapping("/base/region")
@Api(tags = "国家或地区代码数据接口")
public class RegionCodeController extends BaseController {

    @Autowired
    private IRegionCodeService regionCodeService;

    /**
     * 查询国家或地区代码列表
     */
//    @PreAuthorize("@ss.hasPermi('base:region:regionCodeList')")
    @GetMapping("/regionCodeList")
    @ApiOperation(value = "查询国家或地区代码列表")
    public TableDataInfo regionCodeList(RegionCodeQuery query)
    {
        startPage();
        List<BaseRegionCode> list = regionCodeService.selectRegionCodeList(query);
        return getDataTable(list);
    }

    /**
     * 导出国家或地区代码
     */
    @PreAuthorize("@ss.hasPermi('base:region:exportRegionCode')")
    @Log(title = "导出国家或地区代码", businessType = BusinessType.EXPORT)
    @PostMapping("/exportRegionCode")
    @ApiOperation(value = "导出国家或地区代码")
    public void exportRegionCode(HttpServletResponse response, RegionCodeQuery query)
    {
        List<BaseRegionCode> list = regionCodeService.selectRegionCodeList(query);
        ExcelUtil<BaseRegionCode> util = new ExcelUtil<BaseRegionCode>(BaseRegionCode.class);
        util.exportExcel(response, list, "国家或地区代码");
    }

}
