package com.gzairports.common.basedata.domain;

import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 不正常类型
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Data
public class BaseAbnormalType {

    /** 主键id */
    private Long id;

    /** 不正常类型 */
    @Excel(name = "不正常类型")
    private String abnormalType;

    /** 中文描述 */
    @Excel(name = "中文描述")
    private String chineseDescription;

    /** 英文描述 */
    @Excel(name = "英文描述")
    private String englishDescription;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;
}
