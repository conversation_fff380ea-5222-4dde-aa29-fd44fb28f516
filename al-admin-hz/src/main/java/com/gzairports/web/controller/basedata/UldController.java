package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BaseCargoUld;
import com.gzairports.common.basedata.domain.BaseCargoUldForSave;
import com.gzairports.common.basedata.domain.query.CargoUldQuery;
import com.gzairports.common.basedata.service.IUldService;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 集装器管理Controller
 *
 * <AUTHOR>
 * @date 2024-02-20
 */
@RestController
@RequestMapping("/base/uld")
@Api(tags = "集装器管理数据接口")
public class UldController extends BaseController {

    @Autowired
    private IUldService uldService;

    /**
     * 查询集装器列表
     */
//    @PreAuthorize("@ss.hasPermi('base:uld:uldList')")
    @GetMapping("/uldList")
    @ApiOperation(value = "集装器管理查询")
    public TableDataInfo uldList(CargoUldQuery query)
    {
        startPage();
        List<BaseCargoUld> list = uldService.ULDList(query);
        return getDataTable(list);
    }

    /**
     * 导出集装器列表
     */
    @PreAuthorize("@ss.hasPermi('base:uld:exportUld')")
    @Log(title = "导出集装器列表", businessType = BusinessType.EXPORT)
    @PostMapping("/exportUld")
    @ApiOperation(value = "导出集装器列表")
    public void exportUld(HttpServletResponse response, CargoUldQuery query)
    {
        List<BaseCargoUld> list = uldService.ULDList(query);
        ExcelUtil<BaseCargoUld> util = new ExcelUtil<BaseCargoUld>(BaseCargoUld.class);
        util.exportExcel(response, list, "集装器列表");
    }


    /**
     * 导出集装器列表模板
     */
    @PreAuthorize("@ss.hasPermi('base:uld:exportUld')")
    @Log(title = "导出集装器模板", businessType = BusinessType.EXPORT)
    @PostMapping("/exportUldNull")
    @ApiOperation(value = "导出集装器模板")
    public void exportUldNull(HttpServletResponse response)
    {
        List<BaseCargoUld> list = new ArrayList<>();
        ExcelUtil<BaseCargoUld> util = new ExcelUtil<BaseCargoUld>(BaseCargoUld.class);
        util.exportExcel(response, list, "集装器列表模板");
    }

    /**
     * 导入集装器
     */
    @PreAuthorize("@ss.hasPermi('base:uld:importUld')")
    @Log(title = "导入集装器", businessType = BusinessType.IMPORT)
    @PostMapping("/importUld")
    @ApiOperation(value = "导入集装器")
    public AjaxResult importCityCode(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<BaseCargoUld> util = new ExcelUtil<BaseCargoUld>(BaseCargoUld.class);
        List<BaseCargoUld> ulds = util.importExcel(file.getInputStream());
        String message = uldService.importUlds(ulds, updateSupport);
        return success(message);
    }

    /**
     * 入场（新增）
     */
    @PreAuthorize("@ss.hasPermi('base:uld:addUld')")
    @Log(title = "入场（新增）", businessType = BusinessType.INSERT)
    @PostMapping("/addUld")
    @ApiOperation(value = "入场（新增）")
    public AjaxResult addUld(@RequestBody BaseCargoUld uld)
    {
        return toAjax(uldService.addULD(uld));
    }

    /**
     * 出场（修改）
     */
    @PreAuthorize("@ss.hasPermi('base:uld:editUld')")
    @Log(title = "出场（修改）", businessType = BusinessType.UPDATE)
    @PostMapping("/editUld")
    @ApiOperation(value = "出场（修改）")
    public AjaxResult editUld(@RequestBody BaseCargoUld uld)
    {
        return toAjax(uldService.editULD(uld));
    }

    /**
     * 删除板车
     */
    @PreAuthorize("@ss.hasPermi('base:uld:removeUld')")
    @Log(title = "删除板车", businessType = BusinessType.DELETE)
    @GetMapping("removeUld/{id}")
    @ApiOperation(value = "删除板车")
    public AjaxResult removeUld(@PathVariable Long id)
    {
        return toAjax(uldService.removeUld(id));
    }

    /**
     * 详情
     */
    @PreAuthorize("@ss.hasPermi('base:uld:getInfo')")
    @GetMapping("/getInfo/{id}")
    @ApiOperation(value = "详情")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(uldService.getInfo(id));
    }

    /**
     * 查询当前uld状态
     */
    @PreAuthorize("@ss.hasPermi('base:uld:saveUld')")
    @PostMapping("/selectStatus")
    @ApiOperation(value = "查询当前uld状态")
    public AjaxResult selectStatus(@RequestBody BaseCargoUldForSave uld)
    {
        if (uldService.selectStatus(uld)){
            return warn("当前uld正在使用");
        }
        return null;
    }

    /**
     * 保存
     */
    @PreAuthorize("@ss.hasPermi('base:uld:saveUld')")
    @PostMapping("/saveUld")
    @ApiOperation(value = "保存")
    public AjaxResult saveUld(@RequestBody BaseCargoUldForSave uld)
    {
        return toAjax(uldService.saveUld(uld));
    }

}
