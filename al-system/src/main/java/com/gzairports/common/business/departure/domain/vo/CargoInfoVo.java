package com.gzairports.common.business.departure.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 货物信息列表
 *
 * <AUTHOR>
 * @date 2024-11-04
 */
@Data
public class CargoInfoVo {

    /** 货物品名 */
    private String cargoName;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 目的地 */
    private String desPort;

    /** 安全数据说明书 */
    private String instructions;

    /** 运输条件鉴定书 */
    private String identify;

    /** 航空公司同意运输证明 */
    private String prove;

    /** 始发站 */
    private String startPort;
}
