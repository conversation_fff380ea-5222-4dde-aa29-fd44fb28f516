<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.CustomerMapper">
    
    <resultMap type="com.gzairports.common.basedata.domain.Customer" id="CustomerResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="abbreviation"    column="abbreviation"    />
        <result property="phone"    column="phone"    />
        <result property="address"    column="address"    />
        <result property="remark"    column="remark"    />
        <result property="company"    column="company"    />
        <result property="location"    column="location"    />
        <result property="idCard"    column="id_card"    />
        <result property="isDel"    column="is_del"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectCustomerVo">
        select id, name, abbreviation, phone, address, remark, company, location, id_card, is_del, create_time, dept_id from wl_custom
    </sql>

    <insert id="insertCustomer" parameterType="com.gzairports.common.basedata.domain.Customer">
        insert into wl_custom
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="abbreviation != null and abbreviation != ''">abbreviation,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="address != null">address,</if>
            <if test="remark != null">remark,</if>
            <if test="company != null and company != ''">company,</if>
            <if test="location != null and location != ''">location,</if>
            <if test="idCard != null and idCard != ''">id_card,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createTime != null">create_time,</if>
            <if test="deptId != null">dept_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="abbreviation != null and abbreviation != ''">#{abbreviation},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="address != null">#{address},</if>
            <if test="remark != null">#{remark},</if>
            <if test="company != null and company != ''">#{company},</if>
            <if test="location != null and location != ''">#{location},</if>
            <if test="idCard != null and idCard != ''">#{idCard},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="deptId != null">#{deptId},</if>
        </trim>
    </insert>

    <select id="selectCustomerList" parameterType="com.gzairports.common.basedata.domain.Customer" resultMap="CustomerResult">
        <include refid="selectCustomerVo"/>
        <where>
            and is_del = 0
            <if test="company != null  and company != ''"> and company like concat('%', #{company}, '%')</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="abbreviation != null  and abbreviation != ''"> and abbreviation = #{abbreviation}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="deptId != null  and deptId != ''"> and dept_id = #{deptId}</if>
        </where>
    </select>
    
    <select id="selectCustomerById" parameterType="Long" resultMap="CustomerResult">
        <include refid="selectCustomerVo"/>
        where id = #{id}
    </select>

    <update id="updateCustomer" parameterType="com.gzairports.common.basedata.domain.Customer">
        update wl_custom
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="abbreviation != null and abbreviation != ''">abbreviation = #{abbreviation},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="address != null">address = #{address},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="company != null and company != ''">company = #{company},</if>
            <if test="location != null and location != ''">location = #{location},</if>
            <if test="idCard != null and idCard != ''">id_card = #{idCard},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteCustomerById" parameterType="Long">
        update wl_custom set is_del = 1 where id = #{id}
    </update>

    <update id="deleteCustomerByIds" parameterType="String">
        update wl_custom set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>