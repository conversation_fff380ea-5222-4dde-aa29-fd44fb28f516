package com.gzairports.common.message.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.message.domain.Message;
import com.gzairports.common.message.domain.query.MessageQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 消息管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@Mapper
public interface MessageMapper extends BaseMapper<Message> {

    /**
     * 超级管理员和单位管理员查看的消息
     * @param query 查询条件
     * @return 消息列表
     */
    List<Message> selectListByQuery(MessageQuery query);
}
