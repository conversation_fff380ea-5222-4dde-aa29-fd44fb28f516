package com.gzairports.wl.cargofee.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.business.departure.domain.WaybillFee;
import com.gzairports.wl.cargofee.domain.query.WaybillFeeQuery;
import com.gzairports.wl.cargofee.domain.vo.WaybillFeeVo;

/**
 * 运单费用明细Service接口
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
public interface IWaybillFeeService extends IService<WaybillFee> {

    /**
     * 查询运单费用明细列表
     * @param query 查询参数
     * @return 运单费用明细列表
     */
    WaybillFeeVo selectList(WaybillFeeQuery query);
}
