package com.gzairports.hz.business.departure.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.basedata.domain.BaseCargoUld;
import com.gzairports.common.basedata.domain.BaseFlatbedTruck;
import com.gzairports.common.basedata.mapper.TruckMapper;
import com.gzairports.common.basedata.mapper.UldMapper;
import com.gzairports.common.business.arrival.domain.FlightLoadUldWaybill;
import com.gzairports.common.business.arrival.domain.FlightLoadWaybill;
import com.gzairports.common.business.arrival.mapper.FlightInfoMapper;
import com.gzairports.common.business.arrival.mapper.FlightLoadUldWaybillMapper;
import com.gzairports.common.business.arrival.mapper.FlightLoadWaybillMapper;
import com.gzairports.common.business.departure.domain.FlightInfo;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.common.log.service.IWaybillLogService;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.ServletUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.hz.business.departure.domain.*;
import com.gzairports.hz.business.departure.domain.query.GroupCargoQuery;
import com.gzairports.hz.business.departure.domain.vo.GroupCargoVo;
import com.gzairports.hz.business.departure.domain.vo.TruckInfoVo;
import com.gzairports.hz.business.departure.mapper.*;
import com.gzairports.hz.business.departure.service.IGroupCargoService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 组货调度Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-03
 */
@Service
public class GroupCargoServiceImpl implements IGroupCargoService {

    @Autowired
    private FlightLoadMapper flightLoadMapper;

    @Autowired
    private HzDepGroupUldMapper groupUldMapper;

    @Autowired
    private HzDepGroupUldWaybillMapper groupUldWaybillMapper;

    @Autowired
    private HzDepHandoverMapper hzDepHandoverMapper;

    @Autowired
    private HzDepGroupWaybillMapper groupWaybillMapper;

    @Autowired
    private FlightLoadUldMapper loadUldMapper;

    @Autowired
    private FlightLoadUldWaybillMapper flightLoadUldWaybillMapper;

    @Autowired
    private FlightLoadWaybillMapper flightLoadWaybillMapper;

    @Autowired
    private AllAirWaybillMapper airWaybillMapper;

    @Autowired
    private RepeatWeightMapper repeatWeightMapper;

    @Autowired
    private UldMapper uldMapper;

    @Autowired
    private TruckMapper truckMapper;

    @Autowired
    private IWaybillLogService waybillLogService;

    @Autowired
    private FlightInfoMapper flightInfoMapper;


    /**
     * 组货调度列表查询
     * @param query 查询参数
     * @return 组货调度列表
     */
    @Override
    public List<GroupCargoVo> selectList(GroupCargoQuery query) {
        List<GroupCargoVo> groupCargoVos = flightLoadMapper.selectGroupList(query);
        groupCargoVos.forEach(e->{
            //配载板箱数
            Integer uldCount = loadUldMapper.selectCount(new QueryWrapper<FlightLoadUld>()
                    .eq("flight_load_id", e.getId()));
            e.setLoadUldNum(uldCount);
            //运单数
            int waybillNum = 0;
            List<FlightLoadUld> flightLoadList = loadUldMapper.selectList(new QueryWrapper<FlightLoadUld>()
                    .eq("flight_load_id", e.getId()));
            if (!CollectionUtils.isEmpty(flightLoadList)) {
                for (FlightLoadUld flightLoad : flightLoadList) {
                    List<FlightLoadUldWaybill> loadUldWaybillList = flightLoadUldWaybillMapper.selectList(new QueryWrapper<FlightLoadUldWaybill>()
                            .eq("load_uld_id", flightLoad.getId()));
                    //得到loadUldWaybillList中的waybillId集合
                    List<Long> waybillIds = loadUldWaybillList.stream()
                            .map(FlightLoadUldWaybill::getWaybillId)
                            .collect(Collectors.toList());
                    //对waybillIds进行去重得到当前flight_load_id对应的运单号数量
                    long count = waybillIds.stream().distinct().count();
                    waybillNum += count;
                }
            }
            List<FlightLoadWaybill> flightLoadId = flightLoadWaybillMapper.selectList(new QueryWrapper<FlightLoadWaybill>()
                    .eq("flight_load_id", e.getId()));
            if (!CollectionUtils.isEmpty(flightLoadId)){
                //得到flightLoadId中的waybillId集合
                List<Long> waybillIds = flightLoadId.stream()
                        .map(FlightLoadWaybill::getWaybillId)
                        .collect(Collectors.toList());
                //对waybillIds进行去重得到当前flight_load_id对应的运单号数量
                long count = waybillIds.stream().distinct().count();
                waybillNum += count;
            }
            e.setWaybillNum(waybillNum);
            //组货板箱数
            List<HzDepGroupUld> flightGroupUldList = groupUldMapper.selectList(new QueryWrapper<HzDepGroupUld>()
                    .eq("flight_load_id", e.getId()));
            if(!CollectionUtils.isEmpty(flightGroupUldList)){
                //不用去重,有多少个flight_load_id就有多少个uld
                e.setGroupUldNum(flightGroupUldList.size());
            }else{
                e.setGroupUldNum(0);
            }
            String status = FLIGHT_STATUS.get(e.getState());
            e.setStatus(status);
        });
        return groupCargoVos;
    }

    /**
     * 查看详情
     * @param id 航班配载id
     * @return 结果
     */
    @Override
    public GroupCargoVo getInfo(Long id) {
        GroupCargoVo vo = flightLoadMapper.selectByGroupById(id);
        List<HzDepGroupUld> groupUlds = groupUldMapper.selectList(new QueryWrapper<HzDepGroupUld>()
                .eq("flight_load_id", id));
        for (HzDepGroupUld groupUld : groupUlds) {
            List<Long> ids = groupUlds.stream().map(HzDepGroupUld::getId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(ids)){
                List<HzDepGroupUldWaybill> waybills = groupUldWaybillMapper.selectList(new QueryWrapper<HzDepGroupUldWaybill>().eq("group_uld_id",groupUld.getId()));
                if (!CollectionUtils.isEmpty(waybills)){
                    List<HzDepGroupUldWaybill> cargoList = waybills.stream().filter(e -> "货物".equals(e.getType())).collect(Collectors.toList());
                    if(!CollectionUtils.isEmpty(cargoList)){
                        int cargoQuantity = cargoList.stream().mapToInt(HzDepGroupUldWaybill::getQuantity).sum();
                        groupUld.setCargoQuantity(cargoQuantity);
                        BigDecimal cargoWeight = cargoList.stream().map(HzDepGroupUldWaybill::getWeight).reduce(BigDecimal.ZERO,BigDecimal::add);
                        groupUld.setCargoWeight(cargoWeight);
                    }else {
                        groupUld.setCargoQuantity(0);
                        groupUld.setCargoWeight(new BigDecimal(0));
                    }
                    List<HzDepGroupUldWaybill> mailList = waybills.stream().filter(e -> "邮件".equals(e.getType())).collect(Collectors.toList());
                    if(!CollectionUtils.isEmpty(mailList)){
                        int cargoQuantity = mailList.stream().mapToInt(HzDepGroupUldWaybill::getQuantity).sum();
                        groupUld.setMailQuantity(cargoQuantity);
                        BigDecimal cargoWeight = mailList.stream().map(HzDepGroupUldWaybill::getWeight).reduce(BigDecimal.ZERO,BigDecimal::add);
                        groupUld.setMailWeight(cargoWeight);
                    }else {
                        groupUld.setMailQuantity(0);
                        groupUld.setMailWeight(new BigDecimal(0));
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(groupUlds)){
            vo.setVos(groupUlds);
        }
        //配载板箱数
        Integer uldCount = loadUldMapper.selectCount(new QueryWrapper<FlightLoadUld>()
                .eq("flight_load_id", id));
        vo.setLoadUldNum(uldCount);
        //运单数
        int waybillNum = 0;
        List<FlightLoadUld> flightLoadList = loadUldMapper.selectList(new QueryWrapper<FlightLoadUld>()
                .eq("flight_load_id", id));
        if (!CollectionUtils.isEmpty(flightLoadList)) {
            for (FlightLoadUld flightLoad : flightLoadList) {
                List<FlightLoadUldWaybill> loadUldWaybillList = flightLoadUldWaybillMapper.selectList(new QueryWrapper<FlightLoadUldWaybill>()
                        .eq("load_uld_id", flightLoad.getId()));
                //得到loadUldWaybillList中的waybillId集合
                List<Long> waybillIds = loadUldWaybillList.stream()
                        .map(FlightLoadUldWaybill::getWaybillId)
                        .collect(Collectors.toList());
                //对waybillIds进行去重得到当前flight_load_id对应的运单号数量
                long count = waybillIds.stream().distinct().count();
                waybillNum += count;
            }
        }
        List<FlightLoadWaybill> flightLoadId = flightLoadWaybillMapper.selectList(new QueryWrapper<FlightLoadWaybill>()
                .eq("flight_load_id", id));
        if (!CollectionUtils.isEmpty(flightLoadId)){
            //得到flightLoadId中的waybillId集合
            List<Long> waybillIds = flightLoadId.stream()
                    .map(FlightLoadWaybill::getWaybillId)
                    .collect(Collectors.toList());
            //对waybillIds进行去重得到当前flight_load_id对应的运单号数量
            long count = waybillIds.stream().distinct().count();
            waybillNum += count;
        }
        vo.setWaybillNum(waybillNum);

        if(!CollectionUtils.isEmpty(groupUlds)){
            //不用去重,有多少个flight_load_id就有多少个uld
            vo.setGroupUldNum(groupUlds.size());
        }else{
            vo.setGroupUldNum(0);
        }
        vo.setStatus(FLIGHT_STATUS.get(vo.getState()));
        return vo;
    }

    /**
     * 指派
     * @param flightLoadId 航班配载id
     * @param username 组货员
     * @return 结果
     */
    @Override
    public int assign(Long flightLoadId, String username) {
        FlightLoad flightLoad = flightLoadMapper.selectById(flightLoadId);
        if ("not_pre".equals(flightLoad.getState())){
            throw new CustomException("当前航班未配载");
        }
        if ("been_pre".equals(flightLoad.getState())){
            flightLoad.setState("not_group");
        }
        flightLoad.setGroupUser(username);
        return flightLoadMapper.updateById(flightLoad);
    }

    /**
     * 板车详情
     * @param id 板车id
     * @return 结果
     */
    @Override
    public TruckInfoVo groupDataInfo(Long id) {
       TruckInfoVo vo = groupUldMapper.selectTruckInfoById(id);
       if(vo != null){
           List<HzDepGroupUldWaybill> list = groupUldWaybillMapper.selectList(new QueryWrapper<HzDepGroupUldWaybill>().eq("group_uld_id",id));
           if (!CollectionUtils.isEmpty(list)){
               List<String> waybillCodes = list.stream().map(HzDepGroupUldWaybill::getWaybillCode).collect(Collectors.toList());
               List<AirWaybill> airWaybills = airWaybillMapper.selectList(new QueryWrapper<AirWaybill>().in("waybill_code", waybillCodes));
               if (!CollectionUtils.isEmpty(airWaybills)){
                   BigDecimal weight = airWaybills.stream().map(AirWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                   vo.setFileWeight(weight);
                   int quantity = airWaybills.stream().mapToInt(AirWaybill::getQuantity).sum();
                   vo.setQuantity(quantity);
               }
               vo.setVos(list);
           }
       }
        return vo;
    }

    /**
     * 站坪交接
     * @param hzDepHandover 交接参数
     * @return 结果
     */
    @Override
    public int handover(HzDepHandover hzDepHandover) {
        //站坪交接完成后应该把航班配载表中状态改为已完成
        FlightLoad flightLoad = flightLoadMapper.selectById(hzDepHandover.getFlightLoadId());
        flightLoad.setState("been_handed");
        flightLoadMapper.updateById(flightLoad);
        //给表hz_flight_load的字段transfer_time赋值或者查询的时候去查hz_dep_handover表,前者实体都没这个字段 还是去查吧
        hzDepHandover.setHandoverTime(new Date());
        return hzDepHandoverMapper.insert(hzDepHandover);
    }

    /**
     * 未放板车货物
     * @param id 航班配载id
     * @return 未放板车货物列表
     */
    @Override
    public List<HzDepGroupWaybill> notLoadCargo(Long id) {
        return groupWaybillMapper.selectList(new QueryWrapper<HzDepGroupWaybill>()
                .eq("flight_load_id",id));
    }

    /**
     * 加货运单查询
     * @param id 运单组货id
     * @return 结果
     */
    @Override
    public List<HzDepGroupWaybill> addCargo(Long id) {
        List<HzDepGroupWaybill> groupWaybills = groupWaybillMapper.selectListByFlightLoadId(id);
        groupWaybills.forEach(e->{
            if ("put_in".equals(e.getStatus())){
                e.setStatus("是");
            }else {
                e.setStatus("否");
            }
        });
        return groupWaybills;
    }

    /**
     * 根据运单号手动添加
     * @param waybillCode 运单号
     * @return 结果
     */
    @Override
    public HzDepGroupWaybill handAdd(String waybillCode) {
        HzDepGroupWaybill groupWaybill = groupWaybillMapper.selectOneByWaybillCode(waybillCode);
        if (groupWaybill != null){
            if ("put_in".equals(groupWaybill.getStatus())){
                groupWaybill.setStatus("是");
            }else {
                groupWaybill.setStatus("否");
            }
        }
        return groupWaybill;
    }

    /**
     * 确认加货
     * @param groupUldWaybill 加货参数
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int confirmAdd(HzDepGroupUldWaybill groupUldWaybill) {
        HzDepGroupWaybill groupWaybill = groupWaybillMapper.selectById(groupUldWaybill.getGroupWaybillId());

        HzDepGroupUldWaybill uldWaybill = groupUldWaybillMapper.selectOne(new QueryWrapper<HzDepGroupUldWaybill>()
                .eq("waybill_code", groupUldWaybill.getWaybillCode())
                .eq("group_uld_id",groupUldWaybill.getNewGroupUldId()));

        int quantity = groupWaybill.getQuantity() - groupUldWaybill.getQuantity();
        BigDecimal weight = groupWaybill.getWeight().subtract(groupUldWaybill.getWeight());
        if (quantity < 0){
            throw new CustomException("板车加货件数超过运单件数");
        }
        if (quantity == 0){
            if (uldWaybill == null){
                groupUldWaybillMapper.insert(groupUldWaybill);
            }else {
                updateUldWaybill(groupUldWaybill, uldWaybill);
            }
            groupWaybillMapper.deleteById(groupWaybill.getId());
        }else {
            if (uldWaybill == null){
                groupUldWaybillMapper.insert(groupUldWaybill);
            }else {
                updateUldWaybill(groupUldWaybill, uldWaybill);
            }
            groupWaybill.setQuantity(quantity);
            groupWaybill.setWeight(weight);
            groupWaybillMapper.updateById(groupWaybill);
        }
        return 1;
    }

    /**
     * 删除板车
     * @param id 组货板车id
     * @return 结果
     */
    @Override
    public int deleteUld(Long id) {
        HzDepGroupUld groupUld = groupUldMapper.selectById(id);
        List<HzDepGroupUldWaybill> groupUldWaybills = groupUldWaybillMapper.selectList(new QueryWrapper<HzDepGroupUldWaybill>()
                .eq("group_uld_id", id));
        for (HzDepGroupUldWaybill groupUldWaybill : groupUldWaybills) {
            HzDepGroupWaybill groupWaybill = groupWaybillMapper.selectOne(new QueryWrapper<HzDepGroupWaybill>()
                    .eq("flight_load_id",groupUld.getFlightLoadId())
                    .eq("waybill_code",groupUldWaybill.getWaybillCode()));
            if (groupWaybill != null){
                int quantity = groupWaybill.getQuantity() + groupUldWaybill.getQuantity();
                groupWaybill.setQuantity(quantity);
                BigDecimal weight = groupWaybill.getWeight().add(groupUldWaybill.getWeight());
                groupWaybill.setWeight(weight);
                groupWaybillMapper.updateById(groupWaybill);
            }else {
                HzDepGroupWaybill waybill = new HzDepGroupWaybill();
                BeanUtils.copyProperties(groupUldWaybill,waybill);
                waybill.setFlightLoadId(groupUld.getFlightLoadId());
                groupWaybillMapper.insert(waybill);
            }
        }
        groupUldWaybillMapper.delete(new QueryWrapper<HzDepGroupUldWaybill>().eq("group_uld_id",id));
        return groupUldMapper.deleteById(id);
    }

    /**
     * 组货完成
     * @param id 航班配载id
     * @return 结果
     */
    @Override
    public int groupFinish(Long id) {
        FlightLoad flightLoad = flightLoadMapper.selectById(id);
        if ("been_group".equals(flightLoad.getState())) {
            throw new CustomException("该航班已组货");
        }
        ArrayList<WaybillLog> waybillLogs = new ArrayList<>();
        HttpServletResponse response = ServletUtils.getResponse();
        FlightInfo flightInfo = flightInfoMapper.selectById(flightLoad.getFlightId());
        try {
            List<HzDepGroupUld> groupUlds = groupUldMapper.selectList(new QueryWrapper<HzDepGroupUld>()
                    .eq("flight_load_id", id));
            //先将复重那边关于本航班的所有数据清空,再做新增
            repeatWeightMapper.delete(new QueryWrapper<HzDepRepeatWeight>()
                    .eq("flight_load_id", id));
            for (HzDepGroupUld groupUld : groupUlds) {
                //这里依然要做新增,拿到该条航班的组货数据
                HzDepRepeatWeight hzDepRepeatWeight = new HzDepRepeatWeight();
                hzDepRepeatWeight.setFlightLoadId(flightLoad.getId());
                hzDepRepeatWeight.setUld(groupUld.getUld());
                hzDepRepeatWeight.setDes1(groupUld.getDes1());
                hzDepRepeatWeight.setCabin(groupUld.getCabin());
                hzDepRepeatWeight.setGroupUldId(groupUld.getId());
                List<HzDepGroupUldWaybill> groupUldWaybills = groupUldWaybillMapper.selectList(new QueryWrapper<HzDepGroupUldWaybill>()
                        .eq("group_uld_id", groupUld.getId()));
                int quantity = groupUldWaybills.stream().mapToInt(HzDepGroupUldWaybill::getQuantity).sum();
                hzDepRepeatWeight.setQuantity(quantity);
//                List<String> waybillCodes = groupUldWaybills.stream().map(HzDepGroupUldWaybill::getWaybillCode).collect(Collectors.toList());
//                if (!CollectionUtils.isEmpty(waybillCodes)) {
//                    List<AirWaybill> airWaybills = airWaybillMapper.selectList(new QueryWrapper<AirWaybill>().in("waybill_code", waybillCodes));
//                    BigDecimal weight = airWaybills.stream().map(AirWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    hzDepRepeatWeight.setFileWeight(weight);
//                }
                List<HzDepGroupUldWaybill> cargoList = groupUldWaybills.stream().filter(e -> "货物".equals(e.getType())).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(cargoList)){
                    BigDecimal cargoWeight = cargoList.stream().map(HzDepGroupUldWaybill::getWeight).reduce(BigDecimal.ZERO,BigDecimal::add);
                    hzDepRepeatWeight.setFileWeight(cargoWeight);
                }else {
                    hzDepRepeatWeight.setFileWeight(new BigDecimal(0));
                }
                repeatWeightMapper.insert(hzDepRepeatWeight);



                //将板车的状态修改为复重
                String uld = groupUld.getUld();
                if(StringUtils.isNotNull(uld)){
                    if(uld.substring(0,3).equals("CAR")){
                        BaseFlatbedTruck baseFlatbedTruck = truckMapper.selectByCode(uld.substring(3));
                        if (StringUtils.isNotNull(baseFlatbedTruck)){
                            baseFlatbedTruck.setLcStatus("");
                            baseFlatbedTruck.setUseStatus(0);
                            truckMapper.updateById(baseFlatbedTruck);
                        }
                    }else{
                        BaseCargoUld baseCargoUld = uldMapper.selectByCode(uld);
                        if (StringUtils.isNotNull(baseCargoUld)){
                            baseCargoUld.setStatus("");
                            baseCargoUld.setUseStatus(0);
                            uldMapper.updateById(baseCargoUld);
                        }
                    }
                }

                //运单日志
                for (HzDepGroupUldWaybill groupUldWaybill : groupUldWaybills) {
                    WaybillLog waybillLog = waybillLogService.getWaybillLog(
                            groupUldWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                            groupUldWaybill.getWeight().toString(), groupUldWaybill.getQuantity().toString(), flightInfo.getFlightNo(),
                            id.toString(), null, 0, null, new Date(),
                            "组货，ULD号:" + groupUld.getUld(), flightInfo.getIsOffin().equals("A") ? "ARR" : "DEP", groupUld.getUld());
                    waybillLogs.add(waybillLog);
                }
            }
            List<HzDepGroupWaybill> groupWaybills = groupWaybillMapper.selectList(new QueryWrapper<HzDepGroupWaybill>()
                    .eq("flight_load_id", id));
            if (!CollectionUtils.isEmpty(groupWaybills)) {
                for (HzDepGroupWaybill groupWaybill : groupWaybills) {
                    WaybillLog waybillLog = waybillLogService.getWaybillLog(
                            groupWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                            groupWaybill.getWeight().toString(), groupWaybill.getQuantity().toString(), flightInfo.getFlightNo(),
                            id.toString(), null, 0, null, new Date(),
                            "组货，ULD号:" + null, flightInfo.getIsOffin().equals("A") ? "ARR" : "DEP", null);
                    waybillLogs.add(waybillLog);
                }
            }
            flightLoad.setState("been_group");
            flightLoad.setGroupTime(new Date());
            int i = flightLoadMapper.updateById(flightLoad);
            for (WaybillLog waybillLog : waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作成功" + "," +
                                "code:" + response.getStatus() + "," +
                                "data:" + i));
            }
            return i;
        } catch (Exception e) {
            for (WaybillLog waybillLog:waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作失败" +  "," +
                                "code:" + response.getStatus()));
                waybillLog.setErrorMsg(e.getMessage());
                waybillLog.setStatus(1);
            }
            throw new CustomException(e.getMessage());
        } finally {
            for (WaybillLog waybillLog:waybillLogs) {
                waybillLogService.insertWaybillLog(waybillLog);
            }
        }
    }

    /**
     * 删除运单
     * @param id 组货运单id
     * @return 结果
     */
    @Override
    public int deleteWaybill(Long id) {
        HzDepGroupUldWaybill hzDepGroupUldWaybill = groupUldWaybillMapper.selectById(id);
        AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                .eq("waybill_code", hzDepGroupUldWaybill.getWaybillCode()));
        airWaybill.setStatus("put_in");
        airWaybill.setUpdateTime(new Date());
        return groupUldWaybillMapper.deleteById(id);
    }

    /**
     * 新增板车
     * @param uld 板车数据
     * @return 结果
     */
    @Override
    public HzDepGroupUld addUld(HzDepGroupUld uld) {
        Integer code = uldMapper.selectOneByUld(uld.getUld());
        HzDepGroupUld hzDepGroupUld = groupUldMapper.selectOne(new QueryWrapper<HzDepGroupUld>().eq("flight_load_id", uld.getFlightLoadId()).eq("uld", uld.getUld()));
        if (hzDepGroupUld == null){
            String flightNo = flightLoadMapper.selectNoById(uld.getFlightLoadId());
            if (code.equals(0)){
                BaseCargoUld cargoUld = new BaseCargoUld();
                cargoUld.setType(uld.getUld().substring(0,3));
                cargoUld.setCode(uld.getUld().substring(3));
                cargoUld.setAirCompany(flightNo.substring(0,2));
                cargoUld.setDeadWeight(uld.getCabinWeight());
                uldMapper.insert(cargoUld);
            }
            groupUldMapper.insert(uld);
            return uld;
        }




        return hzDepGroupUld;
    }

    /**
     * 货物移动
     * @param groupUldWaybill 移动参数
     * @return 结果
     */
    @Override
    public int move(HzDepGroupUldWaybill groupUldWaybill) {

        HzDepGroupUldWaybill oldUldWaybill = groupUldWaybillMapper.selectOne(new QueryWrapper<HzDepGroupUldWaybill>()
                .eq("waybill_code", groupUldWaybill.getWaybillCode())
                .eq("group_uld_id",groupUldWaybill.getGroupUldId()));

        HzDepGroupUldWaybill newUldWaybill = groupUldWaybillMapper.selectOne(new QueryWrapper<HzDepGroupUldWaybill>()
                .eq("waybill_code", groupUldWaybill.getWaybillCode())
                .eq("group_uld_id",groupUldWaybill.getNewGroupUldId()));

        int quantity = oldUldWaybill.getQuantity() - groupUldWaybill.getQuantity();
        BigDecimal weight = oldUldWaybill.getWeight().subtract(groupUldWaybill.getWeight());
        if (quantity < 0){
            throw new CustomException("运单移动件数超过运单剩余件数");
        }
        if (quantity == 0){
            if (newUldWaybill == null){
                oldUldWaybill.setGroupUldId(groupUldWaybill.getNewGroupUldId());
                groupUldWaybillMapper.updateById(oldUldWaybill);
            }else {
                updateUldWaybill(groupUldWaybill, newUldWaybill);
                groupUldWaybillMapper.deleteById(oldUldWaybill.getId());
            }
        }else {
            if (newUldWaybill == null){
                groupUldWaybill.setGroupUldId(groupUldWaybill.getNewGroupUldId());
                groupUldWaybillMapper.insert(groupUldWaybill);
            }else {
                updateUldWaybill(groupUldWaybill, newUldWaybill);
            }
            oldUldWaybill.setQuantity(quantity);
            oldUldWaybill.setWeight(weight);
            groupUldWaybillMapper.updateById(oldUldWaybill);
        }
        return 1;
    }

    @Override
    public String selectWeight(String uld) {
        if (StringUtils.isEmpty(uld)){
            return null;
        }
        String substring = uld.substring(0, 3);
        if ("CAR".equals(substring)){
            BaseFlatbedTruck truck = truckMapper.selectOne(new QueryWrapper<BaseFlatbedTruck>()
                    .eq("code", uld.substring(3))
                    .eq("is_del",0));
            if (truck != null){
                return truck.getDeadWeight();
            }
        }
        BaseCargoUld cargoUld = uldMapper.selectOne(new QueryWrapper<BaseCargoUld>()
                .eq("code", uld.substring(3))
                .eq("is_del",0));
        if (cargoUld != null){
            return cargoUld.getDeadWeight().toString();
        }
        return null;
    }

    private void updateUldWaybill(HzDepGroupUldWaybill groupUldWaybill, HzDepGroupUldWaybill newUldWaybill) {
        Integer newQuantity = newUldWaybill.getQuantity();
        newQuantity = newQuantity + groupUldWaybill.getQuantity();
        newUldWaybill.setQuantity(newQuantity);
        BigDecimal newWeight = newUldWaybill.getWeight();
        newWeight = newWeight.add(groupUldWaybill.getWeight());
        newUldWaybill.setWeight(newWeight);
        groupUldWaybillMapper.updateById(newUldWaybill);
    }

    private static final Map<String,String> FLIGHT_STATUS = new HashMap<>();
    static {
        FLIGHT_STATUS.put("not_pre","未配载");
        FLIGHT_STATUS.put("been_pre","未指派");
        FLIGHT_STATUS.put("not_group","未组货");
        FLIGHT_STATUS.put("been_group","未复重");
        FLIGHT_STATUS.put("not_weight","未复重");
        FLIGHT_STATUS.put("been_weight","未交接");
        FLIGHT_STATUS.put("not_handed","未交接");
        FLIGHT_STATUS.put("been_handed","已完成");
        FLIGHT_STATUS.put("out_of_stock","无货");
        FLIGHT_STATUS.put("been_dep","已出港");
    }
}
