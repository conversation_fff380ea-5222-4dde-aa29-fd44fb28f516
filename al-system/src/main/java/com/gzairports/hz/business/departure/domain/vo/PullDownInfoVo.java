package com.gzairports.hz.business.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 临时拉下列表返回参数
 *
 * <AUTHOR>
 * @date 2024-07-08
 */
@Data
public class PullDownInfoVo {

    /** 组货数据id */
    private Long id;

//    private Long flightLoadId;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date execDate;

    /** 航班号 */
    private String flightNo;

    /** 运单id */
    private Long waybillId;

    /** 运单号 */
    private String waybillCode;

    /** 开单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date writeTime;

    /** 拉下时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operTime;

    /** 发货代理人 */
    private String agentCode;

    /** 配载重量 */
    private BigDecimal loadWeight;

    /** 垫板重量 */
    private BigDecimal plateWeight;

    /** uld重量 */
    private BigDecimal uldWeight;

    /** 配载件数 */
    private Integer loadQuantity;

    /** 拉下件数 */
    private Integer quantity;

    /** 拉下重量 */
    private BigDecimal weight;

    /** 备注 */
    private String remark;

    /** uld号 */
    private String uld;

    /** 仓库 */
    private String store;

    /** 库位 */
    private String locator;
}
