package com.gzairports.common.basedata.service;

import com.gzairports.common.basedata.domain.BaseAgent;
import com.gzairports.common.basedata.domain.query.BaseAgentQuery;
import com.gzairports.common.basedata.domain.vo.BalanceInfoVo;

import java.util.Date;
import java.util.List;

/**
 * 代理人配置Service接口
 *
 * <AUTHOR>
 * @date 2024-06-17
 */
public interface IBaseAgentService
{
    /**
     * 查询代理人配置
     *
     * @param id 代理人配置主键
     * @return 代理人配置
     */
    public BaseAgent selectBaseAgentById(Long id);

    /**
     * 查询代理人配置列表
     *
     * @param baseAgent 代理人配置
     * @return 代理人配置集合
     */
    public List<BaseAgent> selectBaseAgentList(BaseAgent baseAgent);

    /**
     * 处理导出数据
     * @param list
     */
    List<BaseAgent> selectExportBaseAgentList(BaseAgent baseAgent);

    /**
     * 新增代理人配置
     *
     * @param baseAgent 代理人配置
     * @return 结果
     */
    public int insertBaseAgent(BaseAgent baseAgent);

    /**
     * 修改代理人配置
     *
     * @param baseAgent 代理人配置
     * @return 结果
     */
    public int updateBaseAgent(BaseAgent baseAgent);

    /**
     * 批量删除代理人配置
     *
     * @param ids 需要删除的代理人配置主键集合
     * @return 结果
     */
    public int deleteBaseAgentByIds(Long[] ids);

    /**
     * 删除代理人配置信息
     *
     * @param id 代理人配置主键
     * @return 结果
     */
    public int deleteBaseAgentById(Long id);

    /**
     * 增加余额
     * @param query 增加参数
     * @return 结果
     */
    int addMoney(BaseAgentQuery query);

    /**
     * 余额明细
     * @param id 代理人配置id
     * @return 余额明细
     */
    BalanceInfoVo balanceInfo(Long id,
                              Integer pageNum,
                              Integer pageSize,
                              String remark,
                              Date startTime,
                              Date endTime);

    /**
     * 根据代理公司名称查询代理人配置
     * @param agentCompany 代理人公司
     * @return 结果
     */
    BaseAgent selectBaseAgentByName(String agentCompany);

    List<BaseAgent> listAgentDeptIds(BaseAgent baseAgent);

}
