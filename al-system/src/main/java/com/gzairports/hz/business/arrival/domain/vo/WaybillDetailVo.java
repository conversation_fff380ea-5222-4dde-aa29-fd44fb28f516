package com.gzairports.hz.business.arrival.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 运单明细返回数据
 * <AUTHOR>
 * @date 2024-07-25
 */
@Data
public class WaybillDetailVo {

    private String waybillCode;

    /** 始发站 */
    private String sourcePort;

    /** 承运人1 */
    private String carrier1;

    /** 到达站1 */
    private String des1;

    /** 承运人2 */
    private String carrier2;

    /** 到达站2 */
    private String des2;

    /** 承运人3 */
    private String carrier3;

    /** 到达站3 */
    private String des3;

    /** 目的站 */
    private String desPort;

    /** 收货人 */
    private String consign;

    /** 特货代码 */
    private String specialCargoCode1;

    /** 品名编码 */
    private String cargoCode;

    /** 品名（邮件种类） */
    private String cargoName;

    /** 货品大类名称 */
    private String categoryName;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 计费重量 */
    private BigDecimal chargeWeight;

    /** 海关监管 */
    private Integer customsSupervision;

    /** 转关 */
    private Integer isTransfer;

    /** 录单时间和进港航班 */
    private List<ArrFlightVo> flightVos;

    /** 备注 */
    private String remark;

    /** 理货件数 */
    private Integer lhQuantity;

    /** 理货重量 */
    private BigDecimal lhWeight;

    /** 办单件数 */
    private Integer bdQuantity;

    /** 办单重量 */
    private BigDecimal bdWeight;

    /** 提货件数 */
    private Integer thQuantity;

    /** 提货重量 */
    private BigDecimal thWeight;

    /** 理货数据 */
    private List<LhVo> lhVos;
}
