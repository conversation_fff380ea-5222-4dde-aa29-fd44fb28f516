package com.gzairports.common.message.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 消息管理查询参数
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@Data
public class MessageQuery {

    /** 消息产生时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startCreateTime;

    /** 消息产生时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endCreateTime;

    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startHandleTime;

    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endHandleTime;

    /** 消息接收岗位 */
    private Long postId;

    /** 处理人 */
    private String handleName;

    /** 消息内容 */
    private String content;
}
