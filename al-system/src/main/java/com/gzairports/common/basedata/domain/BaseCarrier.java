package com.gzairports.common.basedata.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 承运人管理
 *
 * <AUTHOR>
 * @date 2024-02-01
 */
@Data
@TableName("base_carrier")
public class BaseCarrier {

    /** 主键id */
    private Long id;

    /** 承运人代码 */
    @Excel(name = "承运人代码")
    private String code;

    /** 中文名称 */
    @Excel(name = "中文名称")
    private String chineseName;

    /** 英文名称 */
    @Excel(name = "英文名称")
    private String englishName;

    /** 承运人简称 */
    @Excel(name = "承运人简称")
    private String abbreviation;

    /** 运单前缀 */
    @Excel(name = "运单前缀")
    private String prefix;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 航司logo */
    private String logoUrl;

    /** 是否本场承运 */
    private Integer isShipment;
}
