<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.reporter.mapper.ReportDataItemMapper">
    <delete id="deleteBatch">
        delete from all_report_data_item
        <where>
            <if test="waybillCodes != null and waybillCodes.size() > 0">
                and waybill_code in
                <foreach item="code" collection="waybillCodes" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
        </where>
    </delete>
    <select id="selectHawbItemList" resultType="com.gzairports.common.business.reporter.domain.ReportDataItem">
        select waybill_code, cost_type as chargeName, freight_type, rate, charging, dept_id, is_del
        from wl_dep_hawb_item
        where waybill_code = #{waybillCode} and dept_id = #{deptId}
    </select>
    <select id="selectMawbItemList" resultType="com.gzairports.common.business.reporter.domain.ReportDataItem">
        select waybill_code, waybill_id as reportId, cost_type as chargeName, rate, charging, dept_id, is_del, rate_type
        from all_mawb_item
        where 1 = 1 and waybill_id in
        <foreach item="id" collection="reportIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectSundryFee" resultType="java.math.BigDecimal">
        select sum(charging) from all_report_data_item where waybill_code = #{waybillCode}
    </select>
    <select id="selectMainCarrierFee"
            resultType="com.gzairports.common.business.reporter.domain.ReportDataItem">
        select charge_name, charging, freight_type from all_report_data_item where waybill_code = #{waybillCode}
    </select>
</mapper>