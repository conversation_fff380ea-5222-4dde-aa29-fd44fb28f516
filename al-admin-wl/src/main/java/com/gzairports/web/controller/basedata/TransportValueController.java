package com.gzairports.web.controller.basedata;


import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.basedata.domain.BaseTransportValue;
import com.gzairports.common.basedata.domain.query.BaseTransportValueQuery;
import com.gzairports.common.basedata.service.ITransportValueService;
import com.gzairports.common.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 运输价值维护Controller
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@RestController
@RequestMapping("/base/transportValue")
@Api(tags = "运输价值维护")
public class TransportValueController extends BaseController {

    @Autowired
    private ITransportValueService valueService;

    /**
     * 查询运输价值维护列表
     */
//    @PreAuthorize("@ss.hasPermi('base:transportValue:TransportValueList')")
    @GetMapping("/BaseTransportValueList")
    @ApiOperation(value = "查询运输价值维护列表")
    public TableDataInfo baseTransportValueList(BaseTransportValueQuery query)
    {
        startPage();
        query.setDeptId(SecurityUtils.getHighParentId());
        List<BaseTransportValue> list = valueService.selectTransportValueList(query);
        return getDataTable(list);
    }

    /**
     * 新增运输价值维护
     */
    @PreAuthorize("@ss.hasPermi('base:transportValue:addTransportValue')")
    @Log(title = "新增运输价值维护", businessType = BusinessType.INSERT)
    @PostMapping("/addBaseTransportValue")
    @ApiOperation(value = "新增运输价值维护")
    public AjaxResult addBaseTransportValue(@RequestBody BaseTransportValue value)
    {
        value.setDeptId(SecurityUtils.getHighParentId());
        return toAjax(valueService.addTransportValue(value));
    }

    /**
     * 运输价值维护详情
     */
    @PreAuthorize("@ss.hasPermi('base:transportValue:getInfo')")
    @GetMapping("/{id}")
    @ApiOperation(value = "运输价值维护详情")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(valueService.getInfo(id));
    }

    /**
     * 修改运输价值维护
     */
    @PreAuthorize("@ss.hasPermi('base:transportValue:editTransportValue')")
    @Log(title = "修改运输价值维护", businessType = BusinessType.UPDATE)
    @PostMapping("/editBaseTransportValue")
    @ApiOperation(value = "修改运输价值维护")
    public AjaxResult editTransportValue(@RequestBody BaseTransportValue value)
    {
        return toAjax(valueService.editTransportValue(value));
    }

    /**
     * 删除运输价值维护
     */
    @PreAuthorize("@ss.hasPermi('base:transportValue:delTransportValue')")
    @Log(title = "删除运输价值维护", businessType = BusinessType.UPDATE)
    @GetMapping("/delBaseTransportValue/{id}")
    @ApiOperation(value = "删除运输价值维护")
    public AjaxResult delTransportValue(@PathVariable("id") Long id)
    {
        return toAjax(valueService.delTransportValue(id));
    }
}
