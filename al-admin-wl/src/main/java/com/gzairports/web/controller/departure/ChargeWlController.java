package com.gzairports.web.controller.departure;

import com.gzairports.common.business.departure.domain.CostDetail;
import com.gzairports.common.business.departure.domain.vo.CountCostVo;
import com.gzairports.common.business.departure.domain.vo.HandSettleVo;
import com.gzairports.common.business.departure.domain.vo.ItemDetailVo;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.common.utils.poi.ExcelUtilBillDoubleSheet;
import com.gzairports.common.utils.poi.ExcelUtilBillNew;
import com.gzairports.common.utils.poi.ExcelUtilFinance;
import com.gzairports.hz.business.departure.domain.query.BillExportQuery;
import com.gzairports.hz.business.departure.domain.query.ChargeQuery;
import com.gzairports.hz.business.departure.domain.vo.*;
import com.gzairports.hz.business.departure.service.IChargeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-06
 **/

@RestController
@RequestMapping("/dep/chargeWl")
@Api(tags = "物流收费管理")
public class ChargeWlController extends BaseController {

    @Autowired
    private IChargeService chargeService;

    /**
     * 收费管理列表
     */
//    @PreAuthorize("@ss.hasPermi('dep:charge:list')")
    @GetMapping("/list")
    @ApiOperation(value = "收费管理列表")
    public AjaxResult list(ChargeQuery query){
        String agent = getAgent();
        query.setAgent(agent);
        query.setAgentCode(Collections.singletonList(agent));
        ChargeVo list = chargeService.selectList(query);
        return AjaxResult.success(list);
    }

    /**
     * 导出收费管理列表
     */
//    @PreAuthorize("@ss.hasPermi('dep:charge:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出收费管理列表")
    public void export(HttpServletResponse response, ChargeQuery query){
        String agent = getAgent();
        query.setAgent(agent);
        query.setAgentCode(Collections.singletonList(agent));
        List<ChargeWaybillVo> list = chargeService.importData(query);
        if(!CollectionUtils.isEmpty(list)){
            for (ChargeWaybillVo chargeWaybillVo : list) {
                if(StringUtils.isNotEmpty(chargeWaybillVo.getWaybillCode())){
                    String waybillCode = chargeWaybillVo.getWaybillCode();
                    if(waybillCode.contains("AWBA")){
                        chargeWaybillVo.setWaybillCode(waybillCode.substring(4,7) + "-" + waybillCode.substring(7));
                    }else{
                        chargeWaybillVo.setWaybillCode(waybillCode.substring(4,6) + "-" + waybillCode.substring(6));
                    }
                }
                chargeWaybillVo.setWriteDate(chargeWaybillVo.getWriteTime());
            }
        }
        ExcelUtil<ChargeWaybillVo> util = new ExcelUtil<ChargeWaybillVo>(ChargeWaybillVo.class);
        util.exportExcel(response, list, "收费管理");
    }

    /**
     * 费用导出
     */
//    @PreAuthorize("@ss.hasPermi('dep:charge:chargeExport')")
    @PostMapping("/chargeExport")
    @ApiOperation(value = "费用导出")
    public void chargeExport(HttpServletResponse response, ChargeQuery query){
        String agent = getAgent();
        query.setAgent(agent);
        query.setAgentCode(Collections.singletonList(agent));
        List<ChargeImportVo> list = chargeService.chargeExport(query);
        ExcelUtil<ChargeImportVo> util = new ExcelUtil<ChargeImportVo>(ChargeImportVo.class);
        util.exportExcel(response, list, "费用明细", "贵阳机场出港收费清单", SecurityUtils.getNickName());
    }


    /**
     * 结算明细费用导出
     */
    @PostMapping("/chargeSettleExport")
    @ApiOperation(value = "结算明细费用导出")
    public void chargeSettleExport(HttpServletResponse response, ChargeQuery query){
        String agent = getAgent();
        query.setAgent(agent);
        query.setAgentCode(Collections.singletonList(agent));
        List<ChargeSettleWaybillVo> list = chargeService.chargeSettleExport(query);
        ExcelUtil<ChargeSettleWaybillVo> util = new ExcelUtil<ChargeSettleWaybillVo>(ChargeSettleWaybillVo.class);
        util.exportExcel(response, list, "结算明细费用导出");
    }


    /**
     * 财务报表导出
     */
    @PreAuthorize("@ss.hasPermi('dep:chargeWl:financeExport')")
    @PostMapping("/finance/export")
    @ApiOperation(value = "财务报表导出")
    public void financeExport(HttpServletResponse response, @Validated BillExportQuery query){
        String agent = getAgent();
        query.setAgent(agent);
        query.setAgentCode(Collections.singletonList(agent));
        query.setPageNum(null);
        query.setPageSize(null);
        BillExportVoFinance vo = chargeService.selectBillFinance(query);
        ExcelUtilFinance<BillExportVoFinance> util = new ExcelUtilFinance<>(BillExportVoFinance.class);
        util.exportBillExcel(response, vo, "财务报表");
    }

    /**
     * 收费明细
     */
    @PreAuthorize("@ss.hasPermi('dep:chargeWl:getInfo')")
    @GetMapping(value = "/getInfo/{id}")
    @ApiOperation(value = "收费明细")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(chargeService.getInfo(id));
    }

    /**
     * 费用明细详情
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUpWl:costInfo')")
    @GetMapping("/costInfo/{id}")
    @ApiOperation(value = "费用明细详情")
    public AjaxResult costInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(chargeService.costInfo(id));
    }

    /**
     * 编辑费用明细
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUpWl:editCost')")
    @PostMapping("/editCost")
    @ApiOperation(value = "编辑费用明细")
    public AjaxResult editCost(@RequestBody CostDetail detail)
    {
        return toAjax(chargeService.editCost(detail));
    }

    /**
     * 删除费用明细
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUpWl:delCost')")
    @GetMapping("/delCost/{id}")
    @ApiOperation(value = "删除费用明细")
    public AjaxResult delCost(@PathVariable("id") Long id)
    {
        return toAjax(chargeService.delCost(id));
    }

    /**
     * 新增收费明细
     */
    @PreAuthorize("@ss.hasPermi('dep:chargeWl:add')")
    @PostMapping(value = "/add")
    @ApiOperation(value = "新增收费明细")
    public AjaxResult add(@RequestBody CostDetail detail)
    {
        return toAjax(chargeService.add(detail));
    }

    /**
     * 计算总费用
     */
    @PreAuthorize("@ss.hasPermi('dep:chargeWl:countCost')")
    @PostMapping("/countCost")
    @ApiOperation(value = "计算总费用")
    public AjaxResult countCost(@RequestBody CostDetail detail)
    {
        return AjaxResult.success(chargeService.countCost(detail));
    }

    /**
     * 根据收费项目id查询优先级最高的收费规则
     */
//    @PreAuthorize("@ss.hasPermi('dep:chargeService:getHighRule')")
    @PostMapping("/getHighRule")
    @ApiOperation(value = "根据收费项目id查询优先级最高的收费规则")
    public AjaxResult getHighRule(@RequestBody ItemDetailVo vo){
        return AjaxResult.success(chargeService.getHighRule(vo));
    }

    @PostMapping("/handSettle")
    @ApiOperation(value = "手动结算")
    public AjaxResult handSettle(@RequestBody HandSettleVo vo){
        return toAjax(chargeService.handSettle(vo));
    }

    @PostMapping("/errorDataCost")
    @ApiOperation(value = "计算费用")
    public AjaxResult errorDataCost(@RequestBody CountCostVo vo){
        return toAjax(chargeService.errorDataCost(vo));
    }

    private String getAgent(){
        return SecurityUtils.getLoginUser().getUser().getDept().getDeptName();
    }
}
