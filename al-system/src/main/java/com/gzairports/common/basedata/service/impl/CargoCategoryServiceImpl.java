package com.gzairports.common.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.basedata.domain.BaseCargoCategory;
import com.gzairports.common.basedata.domain.query.CargoCategoryQuery;
import com.gzairports.common.basedata.mapper.CargoCategoryMapper;
import com.gzairports.common.basedata.service.ICargoCategoryService;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 货品大类Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-14
 */
@Service
public class CargoCategoryServiceImpl implements ICargoCategoryService {

    @Autowired
    private CargoCategoryMapper categoryMapper;

    /**
     * 查询货品大类列表
     *
     * @param query 查询参数
     * @return 货品大类列表
     */
    @Override
    public List<BaseCargoCategory> selectCargoCategoryList(CargoCategoryQuery query) {
        return categoryMapper.selectCargoCategoryList(query);
    }

    /**
     * 新增货品大类
     *
     * @param code 货品大类
     * @return 结果
     */
    @Override
    public int addCargoCategory(BaseCargoCategory code) {
        BaseCargoCategory category = categoryMapper.selectOne(new QueryWrapper<BaseCargoCategory>()
                .eq("code", code.getCode()).eq("is_del",0));
        if (category != null){
            throw new CustomException("已存在相同数据");
        }
        code.setCreateTime(new Date());
        code.setCreateBy(SecurityUtils.getUsername());
        code.setIsDel(0);
        return categoryMapper.insert(code);
    }

    /**
     * 修改货品大类
     *
     * @param code 货品大类
     * @return 结果
     */
    @Override
    public int editCargoCategory(BaseCargoCategory code) {
        code.setUpdateTime(new Date());
        code.setUpdateBy(SecurityUtils.getUsername());
        return categoryMapper.updateById(code);
    }

    /**
     * 删除货品大类
     *
     * @param ids 货品大类id集合
     * @return 结果
     */
    @Override
    public int delCargoCategory(Long[] ids) {
        return categoryMapper.delCargoCategory(ids);
    }
}
