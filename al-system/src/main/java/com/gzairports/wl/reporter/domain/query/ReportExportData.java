package com.gzairports.wl.reporter.domain.query;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Builder
public class ReportExportData {
    /**
     * 报表标题
     */
    private String reportTitle;

    /**
     * 报表信息
     */
    private String reportMsg;

    /**
     * 报表表头
     */
    private List<String> fieldNameCnList;

    /**
     * 报表数据
     */
    private List<Map<String, Object>> columnNameToValueList;
}
