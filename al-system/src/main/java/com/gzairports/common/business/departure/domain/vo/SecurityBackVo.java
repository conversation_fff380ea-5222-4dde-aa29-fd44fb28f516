package com.gzairports.common.business.departure.domain.vo;

import lombok.Data;

import java.util.Date;

/**
 * Created by david on 2024/11/5
 */
@Data
public class SecurityBackVo {

    /** 航空货运单号 */
    private String waybillCode;

    /** 日期 */
    private Date execDate;

    /** 托运人或航空货运销售代理人信用情况 */
    private String creditSituation;

    /** 安全检查开始时间 */
    private Date startTime;

    /** 安全检查结束时间 */
    private Date endTime;

    /** 安全检查结论 */
    private String checkConclusion;

    /** 安全检查通道 */
    private String checkPass;

    /** 货邮快件安检设备操作员 */
    private String deviceOper;

    /** 开箱检查员 */
    private String openCheck;

    /** 安检勤务调度员 */
    private String dispatcher;

    /** 单据审核员 */
    private String auditor;

    /** 已检货物抽检员 */
    private String samplingInspector;

    /** 已检货物抽检情况 */
    private String samplingSituation;
}
