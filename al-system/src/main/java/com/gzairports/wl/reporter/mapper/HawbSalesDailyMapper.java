package com.gzairports.wl.reporter.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzairports.common.business.reporter.domain.ReportDataHawb;
import com.gzairports.common.business.reporter.domain.ReportDataWaybill;
import com.gzairports.wl.reporter.domain.GoodsWeightStatistics;
import com.gzairports.wl.reporter.domain.query.AwbaSalesQuery;
import com.gzairports.wl.reporter.domain.query.GoodsWeightStatisticsQuery;
import com.gzairports.wl.reporter.domain.query.HawbSalesQuery;
import com.gzairports.wl.reporter.domain.vo.ReportTotalVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-20
 */
@Mapper
public interface HawbSalesDailyMapper {

    /**
     * 动态SQL查询主单邮件单销售表
     * @param fieldNames 动态字段
     * @param query 查询条件
     * @return 结果
     */
    Page<ReportDataWaybill> selectAwbaFieldByName(@Param("fieldNames") String fieldNames, @Param("query") AwbaSalesQuery query, @Param("page") Page<ReportDataWaybill> page, @Param("sortData") String sortData);

    /**
     *  查询分单销售报表
     * @param fieldNames
     * @param query
     * @param page
     * @param sortData
     * @return
     */
    Page<ReportDataHawb> selectHawbFieldByName(@Param("fieldNames") String fieldNames, @Param("query") HawbSalesQuery query, @Param("page") Page<ReportDataHawb> page, @Param("sortData") String sortData);

    /**
     * 查询分页数据
     *
     * @param fieldNamesJoin 动态字段
     * @param query 查询条件
     * @return 结果
     */
    IPage<ReportDataWaybill> selectAwbaPageData(Page<ReportDataWaybill> page, @Param("fieldNamesJoin") String fieldNamesJoin, @Param("query") AwbaSalesQuery query);    /**

     * 查询分页数据
     *
     * @param fieldNamesJoin 动态字段
     * @param query 查询条件
     * @return 结果
     */
    IPage<ReportDataHawb> selectHawbPageData(Page<ReportDataHawb> page, @Param("fieldNamesJoin") String fieldNamesJoin, @Param("query") HawbSalesQuery query);

    /**
     * 主单邮件单总计查询
     * @param query 查询条件
     * @return 结果
     */
    ReportTotalVo selectAwbaTotal(@Param("query") AwbaSalesQuery query);

    /**
     * 分单总计查询
     * @param query 查询条件
     * @return 结果
     */
    ReportTotalVo selectHawbTotal(@Param("query") HawbSalesQuery query);

    /**
     * 主分单收入明细报表
     * @param page 分页参数
     * @return 分页数据
     */
//    IPage<AHIncomeDetailVO> incomeDetail(IPage<AHIncomeDetailVO> page);

    /**
     * 主单邮件单货量统计
     * @param page 分页参数
     * @param query
     * @return
     */
    IPage<GoodsWeightStatistics> selectGoodsWeightStatistics(@Param("page")Page<Object> page, @Param("query") GoodsWeightStatisticsQuery query);

    /**
     * 分单货量统计
     * @param page 分页参数
     * @param query
     * @return
     */
    IPage<GoodsWeightStatistics> selectHAWBGoodsWeightStatistics(@Param("page")Page<Object> page, @Param("query") GoodsWeightStatisticsQuery query);

    /**
     * 根据表名查询表的所有列名
     * @param tableName
     * @return
     */
    List<String> selectAllColumnByTableName(String tableName);

}
