<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.RegionCodeMapper">
    <select id="selectRegionCodeList" resultType="com.gzairports.common.basedata.domain.BaseRegionCode">
        select id, code, continent, remark,english_remark from base_region_code
        <where>
            and is_del = 0
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="code != null  and code != ''"> and code like concat('%',#{code},'%')</if>
            <if test="continent != null  and continent != ''"> and continent like concat('%',#{continent},'%')</if>
        </where>
    </select>
</mapper>
