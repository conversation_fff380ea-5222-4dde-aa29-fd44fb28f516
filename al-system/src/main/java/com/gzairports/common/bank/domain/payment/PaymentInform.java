package com.gzairports.common.bank.domain.payment;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;
import java.util.List;

/**
 * @author: lan
 * @Desc: 网关支付扩展字段
 * @create: 2025-03-18 11:30
 **/
@Data
@XmlRootElement(name = "InFormBody")
@XmlAccessorType(XmlAccessType.FIELD)
public class PaymentInform implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 付款人账号 40 N
     * 直跳模式B2B订单支付时，该字段必输；
     * 页面模式B2B订单支付时，即用民生聚合收银台页面模式，该字段如果输入，则在聚合收银台页面上反显；若不输入，支持在页面上补录。
     */
    @XmlElement(name = "draweeAccNo")
    private String draweeAccNo;

    /** 是否锁定账号 1 N
     * 默认为空；与付款人账号一起使用，如需锁定付款账号则填写
     */
    @XmlElement(name = "isLockCard")
    private Boolean isLockCard;

    /** 付款人名称 146 N
     * 微信、支付宝或手机控件支付需要实名支付、直跳模式B2B订单支付时，该字段必输；
     * 页面模式B2B订单支付时，即用民生聚合收银台页面模式，该字段如果输入，则在聚合收银台页面上反显；若不输入，支持在页面上补录。
     */
    @XmlElement(name = "draweeAccName")
    private String draweeAccName;

    /** 付款人证件类型 30 N
     * 微信、支付宝或手机控件支付需要实名支付时，该字段必输
     */
    @XmlElement(name = "draweeCertType")
    private String draweeCertType;

    /** 付款人证件号码 60 N
     * 微信、支付宝或手机控件支付需要实名支付时，该字段必输
     */
    @XmlElement(name = "draweeCertNo")
    private String draweeCertNo;

    /** 付款人行号 30 N
     * 支付类型为直跳模式B2B、B2C、B2B订单支付时，该字段必输，具体字段值信息参见附录10；
     * 页面模式B2B订单支付时，即用民生聚合收银台页面模式，该字段如果输入，则在聚合收银台页面上反显；若不输入，支持在页面上补录。
     */
    @XmlElement(name = "draweePartyId")
    private String draweePartyId;

    /** 付款人手机号 30 N
     * 通行证支付登录手机号
     */
    @XmlElement(name = "draweePhoneNum")
    private String draweePhoneNum;

    /** 付款账户类型 1 N
     * 直跳模式B2B订单支付时，该字段必输，具体字段值信息参见附录13；
     * 页面模式B2B订单支付时，即用民生聚合收银台页面模式，该字段如果输入，则在聚合收银台页面上反显；若不输入，支持在页面上补录。
     */
    @XmlElement(name = "draweeAccType")
    private String draweeAccType;

    /** 付款人协议号 16 N
     */
    @XmlElement(name = "draweeProtocolId")
    private String draweeProtocolId;

    /** 订单类型 2 N
     */
    @XmlElement(name = "orderType")
    private String orderType;

    /** 跳转地址 1024 N
     */
    @XmlElement(name = "specific3")
    private String specific3;

    /** 摘要 150 N
     * 当选择“手机app”支付时，输入则展示在手机银行转账页面，当选择使用“数币扫聚合码”支付时，输入则展示在支付结果页面，最长支持上送50个汉字或150字节
     */
    @XmlElement(name = "postscript")
    private String postscript;

    /** 收款子账簿号 16 N
     * 当选择“手机app”支付时，输入则展示在手机银行转账页面
     */
    @XmlElement(name = "subAccNo")
    private String subAccNo;

    /** 备注 300 N
     */
    @XmlElement(name = "summary")
    private String summary;

    /** 收款账号 60 N
     * 数币交易时，可以使用该字段。
     * 当签约多个收款钱包时，可以指定收款钱包id。
     */
    @XmlElement(name = "payeeAccNo")
    private String payeeAccNo;

    /** 子订单数 7 Y
     * 子订单数，与prodList一一对应
     */
    @XmlElement(name = "recNum")
    private Integer recNum;

    /**
     * 子订单列表
     * */
//    @XmlElementWrapper(name = "prodList")
    private List<PaymentProdList> prodList;
}
