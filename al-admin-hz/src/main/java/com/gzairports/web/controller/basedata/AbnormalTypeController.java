package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BaseAbnormalType;
import com.gzairports.common.basedata.domain.query.AbnormalTypeQuery;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.common.basedata.service.IAbnormalTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 不正常类型Controller
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@RestController
@RequestMapping("/base/abnormalType")
@Api(tags = "不正常类型数据接口")
public class AbnormalTypeController extends BaseController {

    @Autowired
    private IAbnormalTypeService abnormalTypeService;

    /**
     * 查询不正常类型列表
     */
    /*@PreAuthorize("@ss.hasPermi('base:abnormalType:specialCodeList')")*/
    @GetMapping("/abnormalTypeList")
    @ApiOperation(value = "查询不正常类型列表")
    public TableDataInfo specialCodeList(AbnormalTypeQuery query)
    {
        startPage();
        List<BaseAbnormalType> list = abnormalTypeService.selectAbnormalTypeList(query);
        return getDataTable(list);
    }

    /**
     * 导出不正常类型
     */
    @PreAuthorize("@ss.hasPermi('base:abnormalType:exportAbnormalType')")
    @Log(title = "导出不正常类型", businessType = BusinessType.EXPORT)
    @PostMapping("/exportAbnormalType")
    @ApiOperation(value = "导出不正常类型")
    public void exportAbnormalType(HttpServletResponse response, AbnormalTypeQuery query)
    {
        List<BaseAbnormalType> list = abnormalTypeService.selectAbnormalTypeList(query);
        ExcelUtil<BaseAbnormalType> util = new ExcelUtil<BaseAbnormalType>(BaseAbnormalType.class);
        util.exportExcel(response, list, "不正常类型");
    }

    /**
     * 导出不正常类型模板
     */
    @PreAuthorize("@ss.hasPermi('base:abnormalType:exportAbnormalType')")
    @Log(title = "导出不正常类型", businessType = BusinessType.EXPORT)
    @PostMapping("/exportAbnormalTypeNull")
    @ApiOperation(value = "导出不正常类型模板")
    public void exportAbnormalTypeNull(HttpServletResponse response)
    {
        List<BaseAbnormalType> list = new ArrayList<>();
        ExcelUtil<BaseAbnormalType> util = new ExcelUtil<BaseAbnormalType>(BaseAbnormalType.class);
        util.exportExcel(response, list, "不正常类型模板");
    }

    /**
     * 导入不正常类型
     */
    @PreAuthorize("@ss.hasPermi('base:abnormalType:importAbnormalType')")
    @Log(title = "导入不正常类型", businessType = BusinessType.IMPORT)
    @PostMapping("/importAbnormalType")
    @ApiOperation(value = "导入不正常类型")
    public AjaxResult importAbnormalType(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<BaseAbnormalType> util = new ExcelUtil<BaseAbnormalType>(BaseAbnormalType.class);
        List<BaseAbnormalType> abnormalTypes = util.importExcel(file.getInputStream());
        String message = abnormalTypeService.importAbnormalType(abnormalTypes, updateSupport);
        return success(message);
    }

    /**
     * 新增不正常类型
     */
    @PreAuthorize("@ss.hasPermi('base:abnormalType:addAbnormalType')")
    @Log(title = "新增不正常类型", businessType = BusinessType.INSERT)
    @PostMapping("/addAbnormalType")
    @ApiOperation(value = "新增不正常类型")
    public AjaxResult addAbnormalType(@RequestBody BaseAbnormalType abnormalType)
    {
        return toAjax(abnormalTypeService.addAbnormalType(abnormalType));
    }

    /**
     * 修改不正常类型
     */
    @PreAuthorize("@ss.hasPermi('base:abnormalType:editAbnormalType')")
    @Log(title = "修改不正常类型", businessType = BusinessType.UPDATE)
    @PostMapping("/editAbnormalType")
    @ApiOperation(value = "修改不正常类型")
    public AjaxResult editAbnormalType(@RequestBody BaseAbnormalType abnormalType)
    {
        return toAjax(abnormalTypeService.editAbnormalType(abnormalType));
    }

    /**
     * 删除不正常类型
     */
    @PreAuthorize("@ss.hasPermi('base:abnormalType:delAbnormalType')")
    @Log(title = "删除不正常类型", businessType = BusinessType.UPDATE)
    @GetMapping("/delAbnormalType/{ids}")
    @ApiOperation(value = "删除不正常类型")
    public AjaxResult delAbnormalType(@PathVariable("ids") Long[] ids)
    {
        return toAjax(abnormalTypeService.delAbnormalType(ids));
    }
}
