package com.gzairports.wl.reporter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzairports.common.basedata.domain.BaseCarrier;
import com.gzairports.common.basedata.mapper.CarrierMapper;
import com.gzairports.common.business.reporter.domain.*;
import com.gzairports.common.business.reporter.mapper.ReportDataHawbMapper;
import com.gzairports.common.business.reporter.mapper.ReportDataItemMapper;
import com.gzairports.common.business.reporter.mapper.ReportDataWaybillMapper;
import com.gzairports.common.business.reporter.mapper.ReportDepSettleCostMapper;
import com.gzairports.common.utils.poi.ExcelToPdfUtil;
import com.gzairports.common.utils.poi.ReportExcelUtil;
import com.gzairports.wl.reporter.domain.query.MawbHawbIncomeQuery;
import com.gzairports.wl.reporter.domain.vo.AHIncomeDetailVO;
import com.gzairports.wl.reporter.domain.vo.ChargeDetailItemVO;
import com.gzairports.wl.reporter.mapper.MawbHawbIncomeMapper;
import com.gzairports.wl.reporter.service.IMawbHawbIncomeDetailService;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @author: lan
 * @create: 2025-03-12 10:06
 **/

@Service
public class MawbHawbIncomeDetailServiceImpl implements IMawbHawbIncomeDetailService {

    @Autowired
    private MawbHawbIncomeMapper mawbHawbIncomeMapper;

    @Autowired
    private ReportDepSettleCostMapper reportDepSettleCostMapper;

    @Autowired
    private ReportDataItemMapper reportDataItemMapper;

    @Autowired
    private ReportDataWaybillMapper reportDataWaybillMapper;

    @Autowired
    private ReportDataHawbMapper reportDataHawbMapper;

    @Autowired
    private CarrierMapper carrierMapper;

    /**
     * 查询主分单收入明细表格
     * @param query 查询条件
     * @return 报表数据
     */
    @Override
    public  Page<AHIncomeDetailVO> queryExportDataList(MawbHawbIncomeQuery query) {
        Page<AHIncomeDetailVO> page = new Page<>(query.getPageNum() != null ? query.getPageNum() : 1, query.getPageSize() != null ? query.getPageSize() : -1);
        Page<AHIncomeDetailVO> pageData = mawbHawbIncomeMapper.selectExportDataList(query,page);
        List<AHIncomeDetailVO> records = pageData.getRecords();
        for(AHIncomeDetailVO record:records){
            List<ChargeDetailItemVO> chargeDetailItems = new ArrayList<>();
            List<ReportDataItem> itemList = new ArrayList<>();
            List<ReportDataItem> MawbList = reportDataItemMapper.selectList(new QueryWrapper<ReportDataItem>()
                    .eq("waybill_code", record.getAwbaWaybillCode()));
            List<String> strings = mawbHawbIncomeMapper.selectHawbWaybillCode(record.getAwbaWaybillCode());
            List<ReportDataItem> HawbList = reportDataItemMapper.selectList(new QueryWrapper<ReportDataItem>()
                    .in("waybill_code", strings));
            itemList.addAll(MawbList);
            itemList.addAll(HawbList);
            for(ReportDataItem item:itemList){
                ChargeDetailItemVO chargeDetailItemVO = new ChargeDetailItemVO();
                if(item.getWaybillCode().contains("AWBA")){
                    ReportDataWaybill waybillCode = reportDataWaybillMapper.selectOne(new QueryWrapper<ReportDataWaybill>()
                            .eq("waybill_code", item.getWaybillCode()));
                    BaseCarrier baseCarrier = carrierMapper.selectByCode(waybillCode.getCarrier1());
                    chargeDetailItemVO.setSettlementObject(baseCarrier.getChineseName());
                    chargeDetailItemVO.setSettlementType("应付");
                    chargeDetailItemVO.setWaybillType("AWBA");
                    chargeDetailItemVO.setBillingPerson(waybillCode.getWriter());
                    chargeDetailItemVO.setTotalChargeStr("-" + item.getCharging());
                }else{
                    ReportDataHawb waybillCode = reportDataHawbMapper.selectOne(new QueryWrapper<ReportDataHawb>()
                            .eq("waybill_code", item.getWaybillCode()));
                    chargeDetailItemVO.setHawbWaybillCode(item.getWaybillCode());
                    chargeDetailItemVO.setSettlementObject(waybillCode.getShipper());
                    chargeDetailItemVO.setSettlementType("应收");
                    chargeDetailItemVO.setWaybillType("HAWB");
                    chargeDetailItemVO.setTotalChargeStr(item.getCharging().toString());
                    chargeDetailItemVO.setHawbQuantity(waybillCode.getQuantity());
                    chargeDetailItemVO.setHawbWeight(waybillCode.getWeight());
                    chargeDetailItemVO.setHawbChargeWeight(waybillCode.getChargeWeight());
                    chargeDetailItemVO.setSourcePort(waybillCode.getSourcePort());
                    chargeDetailItemVO.setDesPort(waybillCode.getDesPort());
                    chargeDetailItemVO.setCargoName(waybillCode.getCargoName());
                    chargeDetailItemVO.setBillingPerson(waybillCode.getWriter());
                    chargeDetailItemVO.setSpecialCargoCode1(waybillCode.getSpecialCargoCode1());
                }
                chargeDetailItemVO.setChargeName(item.getChargeName());
                chargeDetailItemVO.setRate(item.getRate());
                chargeDetailItemVO.setTotalCharge(item.getCharging());
//                chargeDetailItemVO.setBillingTime();
//                chargeDetailItemVO.setBillingPerson("系统");
                chargeDetailItemVO.setId(record.getId());
                chargeDetailItems.add(chargeDetailItemVO);
            }

            List<ReportDepSettleCost> settleCosts = reportDepSettleCostMapper.selectList(new QueryWrapper<ReportDepSettleCost>()
                    .eq("waybill_code", record.getAwbaWaybillCode()));
            for(ReportDepSettleCost settleCost:settleCosts){
                ChargeDetailItemVO chargeDetailItemVO = new ChargeDetailItemVO();
                chargeDetailItemVO.setSettlementObject("货站");
                chargeDetailItemVO.setChargeName(settleCost.getRateName());
                chargeDetailItemVO.setSettlementType("应付");
                chargeDetailItemVO.setWaybillType("HZ");
                chargeDetailItemVO.setRate(settleCost.getRate());
                chargeDetailItemVO.setTotalCharge(settleCost.getTotalCharge());
                chargeDetailItemVO.setTotalChargeStr("-" + settleCost.getTotalCharge());
                chargeDetailItemVO.setBillingTime(settleCost.getCreateTime());
                chargeDetailItemVO.setBillingPerson("系统");
                chargeDetailItemVO.setId(record.getId());
                chargeDetailItems.add(chargeDetailItemVO);
            }
            record.setChargeDetailItems(chargeDetailItems);
        }

        records.sort(Comparator.comparing(AHIncomeDetailVO::getAwbaWaybillCode));
        List<AHIncomeDetailVO> resultList = new ArrayList<>();
        String currentMainWaybillNo = null;
        Double total = 0.0;
        for(AHIncomeDetailVO record:records){
            if (currentMainWaybillNo == null || !currentMainWaybillNo.equals(record.getAwbaWaybillCode())) {
                if (currentMainWaybillNo != null) {
                    AHIncomeDetailVO subTotal = new AHIncomeDetailVO();
                    subTotal.setId(record.getId());
                    subTotal.setAwbaWaybillCode("运单收入合计");
                    ChargeDetailItemVO chargeDetailItemVO = new ChargeDetailItemVO();
                    chargeDetailItemVO.setTotalCharge(new BigDecimal(total));
                    chargeDetailItemVO.setTotalChargeStr(new BigDecimal(total).toString());
                    chargeDetailItemVO.setWaybillType("TOTAL");
                    subTotal.setChargeDetailItems(Collections.singletonList(chargeDetailItemVO));
                    resultList.add(subTotal);
                }
                currentMainWaybillNo = record.getAwbaWaybillCode();
                total = 0.0;
            }
            resultList.add(record);
            for(ChargeDetailItemVO e:record.getChargeDetailItems()){
                if("HAWB".equals(e.getWaybillType())){
                    total += e.getTotalCharge().doubleValue();
                }else{
                    total -= e.getTotalCharge().doubleValue();
                }
            }
        }
        if (currentMainWaybillNo != null) {
            AHIncomeDetailVO subTotal = new AHIncomeDetailVO();
            subTotal.setAwbaWaybillCode("运单收入合计");
            ChargeDetailItemVO chargeDetailItemVO = new ChargeDetailItemVO();
            chargeDetailItemVO.setTotalCharge(new BigDecimal(total));
            chargeDetailItemVO.setTotalChargeStr(new BigDecimal(total).toString());
            chargeDetailItemVO.setWaybillType("AWBA");
            subTotal.setChargeDetailItems(Collections.singletonList(chargeDetailItemVO));
            resultList.add(subTotal);
        }

        pageData.setRecords(resultList);
        return pageData;
    }


    /**
     * 报表打印
     * */
    @Override
    public void printPDF(MawbHawbIncomeQuery query, HttpServletResponse response) {
        query.setPageNum(1);
        query.setPageSize(-1);
        Page<AHIncomeDetailVO> pageData = this.queryExportDataList(query);
        // 设置响应头
        try (Workbook workbook = writeDataToWorkbook(pageData.getRecords())) {

            //对文件名进行 URL 编码
            String fileName = "主分单收入明细" + "-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "attachment; filename=" + encodedFileName + ".pdf");

            // 将 Workbook 写入 ByteArrayOutputStream
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            workbook.write(baos);
            baos.close();
            // 从 ByteArrayOutputStream 获取 ByteArrayInputStream
            ByteArrayInputStream inputStream = new ByteArrayInputStream(baos.toByteArray());
            ExcelToPdfUtil.excelToPdf(inputStream, response.getOutputStream(), ".xlsx");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 导出报表数据
     * @param data 报表数据
     * @param response 响应
     */
    @Override
    public void export(List<AHIncomeDetailVO> data, HttpServletResponse response) {
        try (Workbook workbook = writeDataToWorkbook(data)) {
            //对文件名进行 URL 编码
            String fileName = "主分单收入明细" + "-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + encodedFileName + ".xlsx");
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 生成 Excel 工作簿
     */
    public Workbook writeDataToWorkbook(List<AHIncomeDetailVO> data) {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("收入明细");

        // 创建表头
        String[] headers = {"序号", "主单号", "分单号", "始发站", "目的站", "件数", "重量","计费重量","特货代码","品名","运单类型",
                "结算对象", "费用项目", "结算类型", "费率", "结算金额", "计费时间", "计费人"};
        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < headers.length; i++) {
            headerRow.createCell(i).setCellValue(headers[i]);
        }

        // 获取数据并写入 Excel
        List<List<Object>> excelData = convertToExcelData(data);
        for (int rowIndex = 0; rowIndex < excelData.size(); rowIndex++) {
            Row row = sheet.createRow(rowIndex + 1);
            List<Object> rowData = excelData.get(rowIndex);
            for (int colIndex = 0; colIndex < rowData.size(); colIndex++) {
                Cell cell = row.createCell(colIndex);
                Object value = rowData.get(colIndex);
                if (value instanceof Number) {
                    cell.setCellValue(((Number) value).doubleValue());
                } else {
                    cell.setCellValue(value == null ? "" : value.toString());
                }
            }
        }

        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
            sheet.setColumnWidth(i, (int) ((20 + 0.72) * 256));
        }

        int[] columnsToMerge = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
        //合并单元格
        mergeSimilarCells(workbook, 0, columnsToMerge,10,0);

        //移除用于合并的列
        removeColumnFromWorkbook(workbook, 0, 10);

        return workbook;
    }

    /**
     * 将数据转换为 Excel 数据格式
     */
    public List<List<Object>> convertToExcelData(List<AHIncomeDetailVO> data) {
        List<List<Object>> excelData = new ArrayList<>();
        int index = 1;

        for (AHIncomeDetailVO order : data) {
            List<ChargeDetailItemVO> charges = order.getChargeDetailItems();
            if (charges == null || charges.isEmpty()) {
                charges = new ArrayList<>();
                charges.add(new ChargeDetailItemVO("", "", "", BigDecimal.ZERO, BigDecimal.ZERO, "", "", "", ""));
            }

            for (ChargeDetailItemVO charge : charges) {
                List<Object> row = new ArrayList<>();
                row.add(charge.getId() == null ? index++ : index);  // 序号
                row.add("AWBA".equals(charge.getWaybillType()) ||
                        "TOTAL".equals(charge.getWaybillType())?
                        order.getAwbaWaybillCode():null);  // 主单号
                row.add("HAWB".equals(charge.getWaybillType())?
                        charge.getHawbWaybillCode():null);  // 分单号
                row.add("HZ".equals(charge.getWaybillType())? null:
                        "HAWB".equals(charge.getWaybillType())? charge.getSourcePort(): order.getSourcePort());  // 始发站
                row.add("HZ".equals(charge.getWaybillType())? null:
                        "HAWB".equals(charge.getWaybillType())? charge.getDesPort(): order.getDesPort());  // 目的站
                row.add("HZ".equals(charge.getWaybillType())? null:
                        "HAWB".equals(charge.getWaybillType())? charge.getHawbQuantity(): order.getQuantity());  // 件数
                row.add("HZ".equals(charge.getWaybillType())? null:
                        "HAWB".equals(charge.getWaybillType())? charge.getHawbWeight(): order.getWeight());  // 重量
                row.add("HZ".equals(charge.getWaybillType())? null:
                        "HAWB".equals(charge.getWaybillType())? charge.getHawbChargeWeight():order.getChargeWeight()); //计费重量
                row.add("HZ".equals(charge.getWaybillType())? null:
                        "HAWB".equals(charge.getWaybillType())? charge.getSpecialCargoCode1():order.getSpecialCargoCode1()); //特货代码
                row.add("HZ".equals(charge.getWaybillType())? null:
                        "HAWB".equals(charge.getWaybillType())? charge.getCargoName():order.getCargoName()); // 品名
                row.add(charge.getWaybillType()); //运单类型
                row.add(charge.getSettlementObject());  // 结算对象
                row.add(charge.getChargeName());  // 费用项目
                row.add(charge.getSettlementType()); //结算类型
                row.add(charge.getRate() == null ? null: charge.getRate().doubleValue());  // 费率
                row.add("TOTAL".equals(charge.getWaybillType())?
                        charge.getTotalCharge() == null ? null: charge.getTotalCharge().doubleValue():
                        charge.getTotalChargeStr()
                );  // 结算金额
                row.add(charge.getBillingTime());  // 计费时间
                row.add(charge.getBillingPerson());  // 计费人

                excelData.add(row);
            }
        }
        return excelData;
    }

    public static void mergeSimilarCells(Workbook workbook, int sheetIndex, int[] columnsToMerge, int waybillTypeColumn, int forceMergeColumn) {
        Sheet sheet = workbook.getSheetAt(sheetIndex);

        // 遍历每一列
        for (int col : columnsToMerge) {
            if (col == forceMergeColumn) {
                forceMergeColumnCells(sheet, col);
            } else {
                mergeColumnCells(sheet, col, waybillTypeColumn);
            }
        }
    }

    private static void mergeColumnCells(Sheet sheet, int columnIndex, int waybillTypeColumn) {
        Row previousRow = null;
        String currentValue = null;
        String currentWaybillType = null;
        int startRow = -1;

        // 遍历每一行
        for (int rowIndex = 0; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row currentRow = sheet.getRow(rowIndex);
            if (currentRow == null) continue;

            Cell currentCell = currentRow.getCell(columnIndex);
            Cell waybillTypeCell = currentRow.getCell(waybillTypeColumn);
            if (currentCell == null || waybillTypeCell == null) continue;

            String cellValue = getCellValueAsString(currentCell);
            String waybillType = getCellValueAsString(waybillTypeCell);

            if (previousRow == null || !cellValue.equals(currentValue)
                    || !waybillType.equals(currentWaybillType)) {
                // 如果当前值或指定的列与前一个值不同，则检查是否需要合并之前的单元格
                if (startRow != -1 && startRow < rowIndex - 1) {
                    sheet.addMergedRegion(new CellRangeAddress(startRow, rowIndex - 1, columnIndex, columnIndex));
                }
                startRow = rowIndex;
                currentValue = cellValue;
                currentWaybillType = waybillType;
            }

            previousRow = currentRow;
        }

        // 处理最后一组相同的单元格
        if (startRow != -1 && startRow < sheet.getLastRowNum()) {
            sheet.addMergedRegion(new CellRangeAddress(startRow, sheet.getLastRowNum(), columnIndex, columnIndex));
        }
    }

    private static void forceMergeColumnCells(Sheet sheet, int columnIndex) {
        Row previousRow = null;
        String currentValue = null;
        int startRow = -1;

        // 遍历每一行
        for (int rowIndex = 0; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row currentRow = sheet.getRow(rowIndex);
            if (currentRow == null) continue;

            Cell currentCell = currentRow.getCell(columnIndex);
            if (currentCell == null) continue;

            String cellValue = getCellValueAsString(currentCell);

            if (previousRow == null ||  !cellValue.equals(currentValue)) {
                // 如果当前行不是上一行的下一行，则检查是否需要合并之前的单元格
                if (startRow != -1 && startRow < rowIndex - 1) {
                    sheet.addMergedRegion(new CellRangeAddress(startRow, rowIndex - 1, columnIndex, columnIndex));
                }
                startRow = rowIndex;
                currentValue = cellValue;
            }

            previousRow = currentRow;
        }

        // 处理最后一组相同的单元格
        if (startRow != -1 && startRow < sheet.getLastRowNum()) {
            sheet.addMergedRegion(new CellRangeAddress(startRow, sheet.getLastRowNum(), columnIndex, columnIndex));
        }
    }

    private static String getCellValueAsString(Cell cell) {
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    public static void removeColumnFromWorkbook(Workbook workbook, int sheetIndex, int columnIndexToRemove) {
        Sheet sheet = workbook.getSheetAt(sheetIndex);

        // 遍历每一行
        for (Row row : sheet) {
            removeCellFromRow(row, columnIndexToRemove);
        }
    }

    private static void removeCellFromRow(Row row, int columnIndexToRemove) {
        Cell cellToRemove = row.getCell(columnIndexToRemove);
        if (cellToRemove != null) {
            row.removeCell(cellToRemove);
        }

        // 移动右侧的单元格向左
        for (int i = columnIndexToRemove + 1; i <= row.getLastCellNum(); i++) {
            Cell currentCell = row.getCell(i);
            if (currentCell != null) {
                Cell previousCell = row.createCell(i - 1);
                copyCell(currentCell, previousCell);
                row.removeCell(currentCell);
            } else {
                // 如果没有更多的单元格，则跳出循环
                break;
            }
        }
    }

    private static void copyCell(Cell sourceCell, Cell targetCell) {
        switch (sourceCell.getCellType()) {
            case STRING:
                targetCell.setCellValue(sourceCell.getStringCellValue());
                break;
            case NUMERIC:
                targetCell.setCellValue(sourceCell.getNumericCellValue());
                break;
            case BOOLEAN:
                targetCell.setCellValue(sourceCell.getBooleanCellValue());
                break;
            case FORMULA:
                targetCell.setCellFormula(sourceCell.getCellFormula());
                break;
            default:
                // Handle other types as needed
                break;
        }

        // Copy cell style
        if (sourceCell.getCellStyle() != null) {
            targetCell.setCellStyle(sourceCell.getCellStyle());
        }
    }

}
