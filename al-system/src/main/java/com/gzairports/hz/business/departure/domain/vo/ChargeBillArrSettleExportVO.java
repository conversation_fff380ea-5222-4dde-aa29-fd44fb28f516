package com.gzairports.hz.business.departure.domain.vo;

import com.gzairports.common.annotation.Excel;
import io.swagger.models.auth.In;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2025-07-11
 */
@Data
public class ChargeBillArrSettleExportVO {

    @Excel(name = "序号",width = 5)
    private Integer idx;

    private Long pickUpId;

    @Excel(name = "流水号", width = 18)
    private String serialNo;

    @Excel(name = "运单号", width = 16)
    private String waybillCode;

    /** 结算客户 */
    @Excel(name = "结算客户", width = 24)
    private String settleUser;

    /** 收货人 */
    @Excel(name = "收货人", width = 24)
    private String consign;

    /** 件数 */
    @Excel(name = "件数", cellType = Excel.ColumnType.NUMERIC, width = 8)
    private Integer quantity;

    /** 重量 */
    @Excel(name = "重量", cellType = Excel.ColumnType.NUMERIC, width = 8)
    private BigDecimal weight;

    /** 品名 */
    @Excel(name = "品名", width = 8)
    private String cargoName;

    /** 计费重量 */
    @Excel(name = "计费重量", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal chargeWeight;

    /** 处理费 */
    @Excel(name = "进处费", cellType = Excel.ColumnType.NUMERIC, width = 8)
    private BigDecimal processingFee = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);

    /** 保管费 */
    @Excel(name = "保管费", cellType = Excel.ColumnType.NUMERIC, width = 8)
    private BigDecimal storageFee = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);

    /** 冷藏费 */
    @Excel(name = "冷藏费", cellType = Excel.ColumnType.NUMERIC, width = 8)
    private BigDecimal refrigerationFee = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);

    /** 搬运费 */
    @Excel(name = "搬运费", cellType = Excel.ColumnType.NUMERIC, width = 8)
    private BigDecimal handlingFee = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);

    /** 电报费 */
    @Excel(name = "电报费", cellType = Excel.ColumnType.NUMERIC, width = 8)
    private BigDecimal cableCharge = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);

    /** 小计 */
    @Excel(name = "支付金额", cellType = Excel.ColumnType.NUMERIC, width = 10)
    private BigDecimal subtotal = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);

    @Excel(name = "结算时间", needMerge = true, width = 20)
    private String settleTime;
}
