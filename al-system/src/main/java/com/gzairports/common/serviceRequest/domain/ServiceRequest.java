package com.gzairports.common.serviceRequest.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 服务申请表
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Data
@TableName("all_service_request")
public class ServiceRequest {

    /** 主键id */
    private Long id;

    /** 服务编号 */
    @Excel(name = "服务编号")
    private String serviceNo;

    /** 运单号 */
    @Excel(name = "运单号")
    private String waybillCode;

    /** 代理人 */
    @Excel(name = "代理人")
    private String agent;

    /** 服务项目id */
    private Long serviceItemId;

    /** 服务项目 */
    @Excel(name = "服务项目")
    private String serviceItem;

    /** 预算选择时间 申请次数/小时 */
    @Excel(name = "申请次数/小时")
    private Integer selectTime;

    /** 实际次数/小时 */
    @Excel(name = "实际次数/小时")
    private Integer actualTime;

    /** 支付状态 取的字典 */
    private Integer payStatus;

    @Excel(name = "支付状态")
    @TableField(exist = false)
    private String payStatusStr;

    /** 状态  暂存:STAGING 申请:UNAUDITED 完成服务:SUCCESS 拒绝服务:REFUSE */
    @Excel(name = "状态")
    private String status;

    /** 申请时间 */
    @Excel(name = "申请时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date requestTime;

    /** 服务开始时间 */
    @Excel(name = "服务开始时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /** 预授权支付时间 */
    @Excel(name = "预授权支付时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /** 预授权支付金额 */
    @Excel(name = "预授权支付金额",  dateFormat = "yyyy-MM-dd HH:mm:ss")
    private BigDecimal payMoney;

    /** 结算时间 */
    @Excel(name = "结算时间",  dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date settleTime;

    /** 服务结束时间 */
    @Excel(name = "服务结束时间",  dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 结束支付金额 */
    @Excel(name = "结束支付金额")
    private BigDecimal endMoney;

    /** 结束退款金额 */
    @Excel(name = "结束退款金额")
    private BigDecimal refund;

    /** 拒绝描述 */
    @Excel(name = "拒绝描述")
    private String remark;

    /** 申请描述 */
    @Excel(name = "申请描述")
    private String applyRemark;

    /** 所属单位 */
    private Long deptId;

    /** 更新时间 */
    private Date updateTime;

    /** 支付方式 */
    private Integer payMethod;

    /** 系统类型 */
    @TableField(exist = false)
    private String sysType;

    /** 进出港类型 */
    @TableField(exist = false)
    private String type;
}
