package com.gzairports.common.basedata.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 板车管理
 *
 * <AUTHOR>
 * @date 2024-02-19
 */
@Data
@TableName("base_flatbed_truck")
public class BaseFlatbedTruck {

    /** 主键id */
    private Long id;

    /** 编号 */
    @Excel(name = "编号")
    private String code;

    /** 类型 */
    @Excel(name = "类型")
    private String type;

    /** 自重 */
    @Excel(name = "自重")
    private String deadWeight;

    /** 最大载重 */
    @Excel(name = "最大载重")
    private String loadWeight;

    /** 最大装载尺寸 */
    @Excel(name = "最大装载尺寸")
    private String loadSize;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 板车图片 */
    @Excel(name = "板车图片")
    private String imageUrl;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 是否删除（0 否 1 是） */
    private String isDel;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 使用状态 0 未使用 1 使用中 */
    private Integer useStatus;

    /** 流程状态 收运:been_collect,配载:been_pre,组货:been_group,复重:been_weight */
    private String lcStatus;

    /** 开始使用时间 */
    private Date useTime;

    /** 目的站 */
    private String desPort;
}
