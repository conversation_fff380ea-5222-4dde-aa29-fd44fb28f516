package com.gzairports.common.basedata.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @author: lan
 * @Desc:
 * @create: 2024-12-09 15:29
 **/

@Data
public class BaseCraftNoQuery {
    /** 航班号 */
    private String flightNo;
    /** 航班时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date flightDate;
    /** 机号 */
    private String craftNo;
}
