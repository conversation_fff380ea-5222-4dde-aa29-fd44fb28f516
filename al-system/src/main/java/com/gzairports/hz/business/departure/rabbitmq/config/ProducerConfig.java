package com.gzairports.hz.business.departure.rabbitmq.config;

import org.springframework.amqp.core.*;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @author: lan
 * @Desc: RabbitMq 配置类
 * @create: 2024-10-22 13:57
 **/

@Configuration
public class ProducerConfig {
    public static final String Waybill_QUEUE_NAME = "WaybillMessage";
    public static final String Waybill_EXCHANGE_NAME = "cargo.amq.notice";
    public static final String Security_QUEUE_NAME = "securityData";
    public static final String Security_EXCHANGE_NAME = "cargo.security";
    public static final String WaybillData_QUEUE_NAME = "waybillData";
    public static final String WaybillData_EXCHANGE_NAME = "cargo.waybill";
    /**
     * 定义交换机
     * @return 交换机对象
     */
//    @Bean
    public Exchange WaybillExchange(){
//        return ExchangeBuilder.directExchange(Waybill_EXCHANGE_NAME).build();
        return ExchangeBuilder
                .directExchange(Waybill_EXCHANGE_NAME)
                .durable(true)
                .build();
    }
    /**
     * 定义消息队列
     * @return 消息队列对象
     */
//    @Bean
    public Queue WaybillQueue(){
//        return new Queue(Waybill_QUEUE_NAME);
        return QueueBuilder.durable(Waybill_QUEUE_NAME)
                .withArgument("x-queue-type", "classic")
                .build();
    }


//    @Bean
    public Exchange SecurityExchange(){
//        return ExchangeBuilder.directExchange(Security_EXCHANGE_NAME).build();
        return ExchangeBuilder
                .directExchange(Security_EXCHANGE_NAME)
                .durable(true)
                .build();
    }

//    @Bean
    public Queue SecurityQueue(){
//        return new Queue(Security_QUEUE_NAME);
        return QueueBuilder.durable(Security_QUEUE_NAME)
                .withArgument("x-queue-type", "classic")
                .build();
    }

    @Bean
    public Exchange WaybillDataExchange(){
        return ExchangeBuilder
                .directExchange(WaybillData_EXCHANGE_NAME)
                .durable(true)
                .build();
    }

    @Bean
    public Queue WaybillDataQueue(){
        return QueueBuilder.durable(WaybillData_QUEUE_NAME)
                        .withArgument("x-queue-type", "classic")
                        .build();
    }
    /**
     * 定义绑定关系
     * @return 绑定关系
     */
//    @Bean
    public Binding WaybillBinding(@Qualifier("WaybillExchange") Exchange exchange,
                            @Qualifier("WaybillQueue") Queue queue){
        // 将定义的交换机和队列进行绑定
        return BindingBuilder
                // 绑定队列
                .bind(queue)
                // 到交换机
                .to(exchange)
                // 使用自定义的routingKey
                .with("WaybillNotice")
                // 不设置参数
                .noargs();
    }

//    @Bean
    public Binding SecurityBinding(@Qualifier("SecurityExchange") Exchange exchange,
                            @Qualifier("SecurityQueue") Queue queue){
        // 将定义的交换机和队列进行绑定
        return BindingBuilder
                .bind(queue)
                .to(exchange)
                .with("securityData")
                .noargs();
    }

    @Bean
    public Binding WaybillDataBinding(@Qualifier("WaybillDataExchange") Exchange exchange,
                            @Qualifier("WaybillDataQueue") Queue queue){
        return BindingBuilder
                .bind(queue)
                .to(exchange)
                .with("waybillData")
                .noargs();
    }

}
