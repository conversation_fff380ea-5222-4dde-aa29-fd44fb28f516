package com.gzairports.common.convert;

import cn.hutool.core.util.StrUtil;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 处理GET请求中的 yyyy-MM 格式的日期参数
 */
@Component
public class MultiDateDeserializer implements Converter<String, LocalDate> {
    @Override
    public LocalDate convert(String source) {
        if (StrUtil.isBlank(source)) {
            return null;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            if (source.length() == 7) { // 处理 yyyy-MM 格式
                return LocalDate.parse(source + "-01", formatter);
            }
            // 处理 yyyy-MM-dd 格式
            return LocalDate.parse(source, formatter);
        } catch (Exception e) {
            throw new IllegalArgumentException("无效的日期格式: " + source);
        }
    }
}
