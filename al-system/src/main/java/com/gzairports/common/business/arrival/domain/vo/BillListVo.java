package com.gzairports.common.business.arrival.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * Created by david on 2024/12/6
 * <AUTHOR>
 */
@Data
public class BillListVo {

    private Long id;

    /** 日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date pickUpTime;

    /** 流水号 */
    private String serialNo;

    /** 运单数 */
    private Integer totalCount;

    /** 结算状态 */
    private String isPay;
}
