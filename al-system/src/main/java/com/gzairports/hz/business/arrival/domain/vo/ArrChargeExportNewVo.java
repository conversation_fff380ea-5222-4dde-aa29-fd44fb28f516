package com.gzairports.hz.business.arrival.domain.vo;

import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 进港 费用管理 费用导出新模板
 * @date 2025-06-23
 **/

@Data
public class ArrChargeExportNewVo {

    /** 办单id */
    private Long id;

    /** 流水号 */
    @Excel(name = "收费流水号",width = 20)
    private String serialNo;

    /** 结算客户 ??*/
//    @Excel(name = "结算客户")
    private String customer;

    /** 结算客户 */
    @Excel(name = "结算客户")
    private String settleUser;

    /** 总费用 */
    @Excel(name = "总费用",cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal totalCost;

    /** 总件数 */
    @Excel(name = "总件数",cellType = Excel.ColumnType.NUMERIC)
    private Integer totalQuantity;

    /** 总重量 */
    @Excel(name = "总重量",cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal totalWeight;

    /** 总计费重量 */
    @Excel(name = "总计费重量",cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal totalChargeWeight;

    /** 是否出库 */
    private Integer isOut;

    /** 运单号 */
    private String waybillCode;

    /** 代理人 */
    private String agentCompany;

    /** 收货人 */
    private String consign;

    private Date pickUpTime;
}
