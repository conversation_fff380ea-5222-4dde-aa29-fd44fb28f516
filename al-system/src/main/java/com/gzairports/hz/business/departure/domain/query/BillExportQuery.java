package com.gzairports.hz.business.departure.domain.query;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 收费管理查询参数
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
public class BillExportQuery {
    /** 代理人 */
    private String agent;

    /** 代理人 */
    private List<String> agentCode;

    /** 航班开始时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime flightStartTime;

    /** 航班结束时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime flightEndTime;

    /** 结算时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTimeSettle;

    /** 结算时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTimeSettle;

    /** 入库时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /** 入库时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /** 制单时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime writeStartTime;

    /** 制单时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime writeEndTime;

    /** 运单号 */
    private String waybillCode;

    /** 运单状态 */
    private String status;

    /** 特货代码1 */
    private String specialCargoCode1;

    /** 流水号 */
    private String serialNo;

    private Integer pageNum = 1;

    private Integer pageSize = 10;
}
