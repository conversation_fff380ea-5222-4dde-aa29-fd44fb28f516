package com.gzairports.hz.business.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.pdf.PdfPrintAnnotation;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static com.gzairports.common.pdf.PdfPrintAnnotation.PdfFieldType.LIST;
import static com.gzairports.common.pdf.PdfPrintAnnotation.PdfFieldType.TEXT;

/**
 * 正式舱单返回数据
 * @date 2024-07-04
 * <AUTHOR>
 */
@Data
public class FormalManifestVo {

    /** 航班号 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "flightNo")
    private String flightNo;

    /** 飞机号 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "craftNo")
    private String craftNo;

    /** 航班日期 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "execDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date execDate;

    /** 航段 */
    private String leg;

    /** 始发站 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "sourcePort")
    private String sourcePort;

    /** 到达站 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "desPort")
    private String desPort;

    /** 舱位运单列表 */
    @PdfPrintAnnotation(pdfFieldType = LIST, pdfFieldName = "vos")
    private List<FormalUldVo> vos;

    /** 散舱运单列表 */
    private List<FormalWaybillVo> waybillVos;

    /** 货物票数 */
    private Integer cargoNum;

    /** 货物件数 */
    private Integer cargoQuantity;

    /** 货物重量 */
    private BigDecimal cargoWeight;

    /** 邮件票数 */
    private Integer mailNum;

    /** 邮件件数 */
    private Integer mailQuantity;

    /** 邮件重量 */
    private BigDecimal mailWeight;

    /** uld个数 */
    private Integer uldUnm;

    /** 总票数 */
    private Integer totalNum;

    /** 总件数 */
    private Integer totalQuantity;

    /** 总重量 */
    private BigDecimal totalWeight;

    /** 打印时间 */
    private Date loadTime;

    /** 制单人 */
    private String loadUser;

    /** 计飞 */
    private String startSchemeTakeoffTime;

    /** 机位 */
    private String craftSite;
}
