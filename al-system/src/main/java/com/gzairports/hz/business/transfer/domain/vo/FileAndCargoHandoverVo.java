package com.gzairports.hz.business.transfer.domain.vo;

import lombok.Data;


/**
 * 文件交接数据
 *
 * <AUTHOR>
 * @date 2024-07-29
 */
@Data
public class FileAndCargoHandoverVo {

    /** 交接单id */
    private Long id;

    /** 转出仓库 */
    private String outStore;

    /** 转出库位 */
    private String outLocator;

    /** 转入仓库 */
    private String inStore;

    /** 转入库位 */
    private String inLocator;

    /** 转出人 */
    private String outName;

    /** 备注 */
    private String remark;
}
