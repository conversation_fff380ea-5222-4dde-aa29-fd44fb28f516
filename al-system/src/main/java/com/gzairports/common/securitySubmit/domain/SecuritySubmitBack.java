package com.gzairports.common.securitySubmit.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: lan
 * @Desc: 接收货检数据
 * @create: 2024-11-15 09:30
 **/

@Data
public class SecuritySubmitBack {
    /**
     * 常量
     * 区分第一次发送 审单结果
     * 第二次发送  安检结果
     * */
    private String messageType;
    /**
     * 航空货运单号
     * */
    private String waybillCode;

    /**
     * 第一次发送 审单检查结论:审单退回，放行
     * */
    private String auditConclusion;
    /**
     * 第一次发送 审单时间
     * */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String auditTime;
    /**
     * 第一次发送 审单人
     * */
    private String auditUser;



    /**
     * 日期
     * */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date execDate;
    /**
     * 航班号
     * */
    private String flightNo;
    /**
     * 托运人或航空货运销售代理人信用情况
     * */
    private String creditSituation;
    /**
     * 安全检查开始时间
     * */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    /**
     * 安全检查结束时间
     * */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    /**
     * 安全检查结论
     * */
    private String checkConclusion;
    /**
     * 安全检查通道
     * */
    private String checkPass;
    /**
     * 货邮快件安检设备操作员
     * */
    private String deviceOper;
    /**
     * 开箱检查员
     * */
    private String openCheck;
    /**
     * 安检勤务调度员
     * */
    private String dispatcher;
    /**
     * 单据审核员
     * */
    private String auditor;
    /**
     * 已检货物列表
     * */
    private List<SampLingVo> samplingList;
}
