package com.gzairports.hz.business.transfer.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 文件和货物交接数据
 *
 * <AUTHOR>
 * @date 2024-07-29
 */
@Data
public class FileAndCargoTransferVo {

    /** 交接单id */
    private Long id;

    /** 交接单运单数据 */
    private List<TransferPickVo> voList;

    /** 运单号 */
    private String waybillCode;

    /** 始发站 */
    private String sourcePort;

    /** 承运人1 */
    private String carrier1;

    /** 到达站1 */
    private String des1;

    /** 承运人2 */
    private String carrier2;

    /** 到达站2 */
    private String des2;

    /** 承运人3 */
    private String carrier3;

    /** 到达站3 */
    private String des3;

    /** 目的站 */
    private String desPort;

    /** 总件数 */
    private Integer quantity;

    /** 舱单件数 */
    private Integer cabinQuantity;

    /** 总重量 */
    private BigDecimal weight;

    /** 舱单重量 */
    private BigDecimal cabinWeight;

    /** 计费重量 */
    private BigDecimal chargeWeight;

    /** 发货人（代理人） */
    private String shipper;

    /** 收货人 */
    private String consign;

    /** 收货人证件号 */
    private String consignIdCar;

    /** 联系电话 */
    private String consignPhone;

    /** 品名编码 */
    private String cargoCode;

    /** 品名（邮件种类） */
    private String cargoName;

    /** 货品大类名称 */
    private String categoryName;

    /** 特货代码 */
    private String specialCargoCode1;

    /** 冷藏库 */
    private String coldStore;

    /** 海关监管 */
    private Integer customsSupervision;

    /** 转关 */
    private Integer isTransfer;

    /** 海关转关号 */
    private String transferNo;

    /** 到付 */
    private Integer arrPay;

    /** 到付费用 */
    private BigDecimal costSum;

    /** 运单备注 */
    private String waybillRemark;

    /** 交接单号 */
    private String handoverNo;

    /** 总票数 */
    private Integer votes;

    /** 总件数 */
    private Integer totalPrices;

    /** 总重量 */
    private BigDecimal totalWeight;

    /** 文件交接状态 */
    private Integer fileStatus;

    /** 文件交接人 */
    private String fileBy;

    /** 文件转出时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date fileOutTime;

    /** 文件接收时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date fileInTime;

    /** 货物交接状态 */
    private Integer cargoStatus;

    /** 货物交接人 */
    private String cargoBy;

    /** 货物转出时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cargoOutTime;

    /** 货物接收时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cargoInTime;

    /** 转出仓库 */
    private String outStore;

    /** 转出库位 */
    private String outLocator;

    /** 转入仓库 */
    private String inStore;

    /** 转入库位 */
    private String inLocator;

    /** 转出人 */
    private String outName;

    /** 交接单备注 */
    private String transferRemark;

}
