package com.gzairports.hz.business.departure.rabbitmq;

import com.alibaba.fastjson2.JSON;
import com.gzairports.common.message.domain.vo.SocketMessageVo;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: lan
 * @Desc: 生产者
 * @create: 2024-10-22 15:01
 **/
@Component
public class WaybillMessageProducer {
    @Autowired
    private RabbitTemplate rabbitTemplate;

    /**
     * @author: lan
     * @description: 发送消息到mq (运单各种状态的消息提示)
     * @date: 2024/10/22
     */
    public void send(SocketMessageVo vo) {
        rabbitTemplate.convertAndSend("cargo.amq.notice","WaybillNotice", JSON.toJSONString(vo));
    }



}
