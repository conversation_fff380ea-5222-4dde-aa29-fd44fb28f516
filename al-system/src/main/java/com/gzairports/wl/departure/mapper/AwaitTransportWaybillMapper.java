package com.gzairports.wl.departure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzairports.wl.departure.domain.Bill;
import com.gzairports.wl.departure.domain.query.AwaitTransportWaybillQuery;
import com.gzairports.wl.departure.domain.vo.ATWaybillVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 待运运单
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@Mapper
public interface AwaitTransportWaybillMapper extends BaseMapper<Bill> {

    /**
     * 查询待运运单
     * @param waybillCodeList
     * @param query
     * @param page
     * @return
     */
    IPage<ATWaybillVO> listAwaitTransportWaybill(@Param("waybillCodeList") List<String> waybillCodeList, @Param("query") AwaitTransportWaybillQuery query, @Param("page") Page<ATWaybillVO> page);

    /**
     * 查询符合#拉下且申请退款、#卸下且申请退款、#支付且未配载的运单号
     * @return
     */
    List<String> selectAwaitTransportWaybillCode();
}
