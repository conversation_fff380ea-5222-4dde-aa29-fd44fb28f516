package com.gzairports.wl.charge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.wl.charge.domain.ChargeItem;
import com.gzairports.wl.charge.domain.query.ChargeItemQuery;
import com.gzairports.wl.charge.domain.vo.ChargeItemVO;

import java.util.List;

/**
 * 收费项目Service接口
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
public interface ChargeItemService extends IService<ChargeItem> {

    /**
     * 查询收费项目列表
     *
     * @param query 查询参数
     * @return 收费项目列表
     */
    List<ChargeItemVO> selectList(ChargeItemQuery query);

    /**
     * 查询收费项目
     *
     * @param id 收费项目主键
     * @return 收费项目
     */
    ChargeItemVO selectChargeById(Long id);

    /**
     * 新增收费项目
     *
     * @param item 收费项目
     * @return 结果
     */
    Long insertChargeItem(ChargeItem item);

    /**
     * 修改收费项目
     *
     * @param item 收费项目
     * @return 结果
     */
    int updateChargeItem(ChargeItem item);

    /**
     * 删除收费项目
     *
     * @param id 收费项目主键
     * @return 结果
     */
    int deleteChargeItem(Long id);
}
