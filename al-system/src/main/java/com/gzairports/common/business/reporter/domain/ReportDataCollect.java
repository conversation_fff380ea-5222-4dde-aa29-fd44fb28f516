package com.gzairports.common.business.reporter.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-02-19
 */
@Data
@TableName("all_report_data_collect")
public class ReportDataCollect {

    /** 主键id */
    private Long id;

    /** 报表id */
    private Long reportId;

    /** 运单号 */
    private String waybillCode;

    /** 操作人 */
    private String operName;

    /** 是否真实收运 */
    private String isReal;

    /** 收运时间 */
    private String collectTime;

    /** 收运件数 */
    private Integer quantity;

    /** 收运重量 */
    private BigDecimal weight;

    /** 板车类型 base_flatbed_truck*/
    private String flatbedType;

    /** 板车号 hz_dep_group_uld*/
    private String uld;
}
