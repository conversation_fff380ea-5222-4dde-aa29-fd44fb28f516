package com.gzairports.wl.ticket.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.wl.ticket.domain.TicketGrant;
import com.gzairports.wl.ticket.domain.vo.TicketGrantVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 单证发放记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@Mapper
public interface TicketGrantMapper extends BaseMapper<TicketGrant> {

    /**
     * 根据单证id查询票号发放记录
     *
     * @param id 单证id
     * @return 票号发放记录
     */
    List<TicketGrantVO> selectListByTicketId(Long id);

    /**
     * 根据id修改发放记录状态
     *
     * @param recordId id
     * @return 状态
     */
    int updateByRecordId(Long recordId);

    /**
     * 根据id查询最新一次发放记录
     *
     * @param id id
     * @return 发放记录
     */
    TicketGrant selectNew(Long id);

    /**
     * 根据id查询发放数量
     *
     * @param id id
     * @return 数量
     */
    Integer selectTotal(Long id);

    /**
     * 重新发放
     * @param recordId 发放记录id
     * @return 结果
     */
    int reissue(Long recordId);
}
