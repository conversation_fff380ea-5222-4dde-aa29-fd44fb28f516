package com.gzairports.common.basedata.service;


import com.gzairports.common.basedata.domain.BaseAirportCode;
import com.gzairports.common.basedata.domain.query.AirportCodeQuery;

import java.util.List;

/**
 * 机场代码Service接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
public interface IAirportCodeService {

    /**
     * 查询机场代码列表
     *
     * @param query 查询参数
     * @return 机场代码列表
     */
    List<BaseAirportCode> selectAirportCodeList(AirportCodeQuery query);

    /**
     * 新增机场代码
     *
     * @param code 机场代码
     * @return 结果
     */
    int addAirportCode(BaseAirportCode code);

    /**
     * 修改机场代码
     *
     * @param code 机场代码
     * @return 结果
     */
    int editAirportCode(BaseAirportCode code);

    /**
     * 删除机场代码
     *
     * @param ids 机场代码id集合
     * @return 结果
     */
    int delAirportCode(Long[] ids);

    /**
     * 导入机场代码
     *
     * @param airportCodes 机场代码列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    String importAirportCode(List<BaseAirportCode> airportCodes, boolean updateSupport);

    String selectChineseName(String desPort);
}
