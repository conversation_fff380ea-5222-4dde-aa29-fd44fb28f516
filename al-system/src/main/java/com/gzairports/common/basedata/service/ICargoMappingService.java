package com.gzairports.common.basedata.service;


import com.gzairports.common.basedata.domain.BaseCargoMapping;
import com.gzairports.common.basedata.domain.query.CargoCodeMappingQuery;

import java.util.List;

/**
 * 货品代码映射Service接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */

public interface ICargoMappingService {

    /**
     * 查询货品代码映射列表
     *
     * @param query 查询参数
     * @return 货品代码映射列表
     */
    List<BaseCargoMapping> selectCargoCodeMappingList(CargoCodeMappingQuery query);

    /**
     * 导入货品代码映射
     *
     * @param cargoMappings 货品代码映射列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    String importCargoMapping(List<BaseCargoMapping> cargoMappings, boolean updateSupport);

    /**
     * 新增货品代码映射
     *
     * @param cargoMapping 货品代码映射
     * @return 结果
     */
    int addCargoMapping(BaseCargoMapping cargoMapping);

    /**
     * 根据id获取货品代码映详情
     *
     * @param id 货品代码映射id
     * @return 结果
     */
    BaseCargoMapping getInfo(Long id);

    /**
     * 修改货品代码映射
     *
     * @param cargoMapping 货品代码映射
     * @return 结果
     */
    int editCargoMapping(BaseCargoMapping cargoMapping);

    /**
     * 删除货品代码映射
     *
     * @param ids 货品代码映射id集合
     * @return 结果
     */
    int delCargoMapping(Long[] ids);

    /**
     * 删除货品代码映射
     *
     * @param id 货品代码映射id集合
     * @return 结果
     */
    int delCargoMapping(Long id);
}
