<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.reporter.mapper.ReportPickUpMapper">
    <insert id="saveOrUpdateBatch">
        insert into all_report_pick_up
        (
        id, serial_no, waybill_code, customer_name, customer_id_type, customer_id_no, customer_phone, handle_by, pay_status, pay_method, total_cost,
        total_charge_weight, total_quantity, total_weight, total_count, pick_up_time
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id}, #{item.serialNo}, #{item.waybillCode}, #{item.customerName}, #{item.customerIdType}, #{item.customerIdNo}, #{item.customerPhone},
            #{item.handleBy}, #{item.payStatus}, #{item.payMethod}, #{item.totalCost}, #{item.totalChargeWeight}, #{item.totalQuantity}, #{item.totalWeight},
            #{item.totalCount}, #{item.pickUpTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        serial_no = VALUES(serial_no),
        waybill_code = VALUES(waybill_code),
        customer_name = VALUES(customer_name),
        customer_id_type = VALUES(customer_id_type),
        customer_id_no = VALUES(customer_id_no),
        customer_phone = VALUES(customer_phone),
        handle_by = VALUES(handle_by),
        pay_status = VALUES(pay_status),
        pay_method = VALUES(pay_method),
        total_cost = VALUES(total_cost),
        total_charge_weight = VALUES(total_charge_weight),
        total_quantity = VALUES(total_quantity),
        total_weight = VALUES(total_weight),
        total_count = VALUES(total_count),
        pick_up_time = VALUES(pick_up_time)
    </insert>
</mapper>