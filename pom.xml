<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
    <groupId>com.gzairports</groupId>
    <artifactId>al-master</artifactId>
    <version>1.0</version>

    <name>al-master</name>
    <description>贵州空港智能科技有限公司通用开发平台</description>
    <modules>
        <module>al-admin-hz</module>
        <module>al-admin-wl</module>
        <module>al-framework</module>
        <module>al-system</module>
        <module>al-quartz</module>
        <module>al-generator</module>
        <module>al-common</module>
        <module>al-oss</module>
        <module>al-sms</module>
    </modules>
    <packaging>pom</packaging>

    <properties>
        <springboot.version>2.5.15</springboot.version>
        <kgkj-master.version>1.0</kgkj-master.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <maven-jar-plugin.version>3.1.1</maven-jar-plugin.version>
        <druid.version>1.2.16</druid.version>
        <bitwalker.version>1.21</bitwalker.version>
        <swagger.version>3.0.0</swagger.version>
		<kaptcha.version>2.3.2</kaptcha.version>
		<mybatis-spring-boot.version>2.1.4</mybatis-spring-boot.version>
        <pagehelper.boot.version>1.3.1</pagehelper.boot.version>
        <fastjson.version>2.0.39</fastjson.version>
        <oshi.version>5.7.5</oshi.version>
        <jna.version>5.8.0</jna.version>
        <commons.io.version>2.10.0</commons.io.version>
        <commons.fileupload.version>1.4</commons.fileupload.version>
        <commons.collections.version>3.2.2</commons.collections.version>
        <poi.version>4.1.2</poi.version>
        <velocity.version>1.7</velocity.version>
        <jwt.version>0.9.1</jwt.version>
        <swagger.version>3.0.0</swagger.version>
        <hutool.version>5.4.0</hutool.version>
        <logstash-logback.version>5.3</logstash-logback.version>
        <mybatis-plus.version>3.4.1</mybatis-plus.version>
        <log4j2.version>2.17.0</log4j2.version>

        <okhttp.version>4.9.2</okhttp.version>

        <!-- OSS 配置 -->
        <qiniu.version>7.9.3</qiniu.version>
        <aliyun.oss.version>3.14.0</aliyun.oss.version>
        <qcloud.cos.version>5.6.68</qcloud.cos.version>
        <minio.version>8.3.8</minio.version>
    </properties>
	
    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- SpringBoot的依赖配置-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${springboot.version}</version>
                <type>pom</type>
                <scope>import</scope>
                <exclusions><!-- 去掉springboot默认配置 -->
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 阿里数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- 解析客户端操作系统、浏览器等 -->
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${bitwalker.version}</version>
            </dependency>

            <!-- SpringBoot集成mybatis框架 -->
<!--            <dependency>-->
<!--                <groupId>org.mybatis.spring.boot</groupId>-->
<!--                <artifactId>mybatis-spring-boot-starter</artifactId>-->
<!--                <version>${mybatis-spring-boot.version}</version>-->
<!--            </dependency>-->

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>

            <!-- 获取系统信息 -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi.version}</version>
            </dependency>

            <dependency>
                <groupId>net.java.dev.jna</groupId>
                <artifactId>jna</artifactId>
                <version>${jna.version}</version>
            </dependency>

            <dependency>
                <groupId>net.java.dev.jna</groupId>
                <artifactId>jna-platform</artifactId>
                <version>${jna.version}</version>
            </dependency>

            <!-- Swagger3依赖 -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>${swagger.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.swagger</groupId>
                        <artifactId>swagger-models</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!-- 文件上传工具类 -->
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons.fileupload.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- velocity代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity</artifactId>
                <version>${velocity.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-collections</groupId>
                        <artifactId>commons-collections</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- collections工具类 -->
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons.collections.version}</version>
            </dependency>

            <!-- 阿里JSON解析器 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>


            <!-- Token生成与解析-->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>

            <!-- 验证码 -->
            <dependency>
                <groupId>com.github.penggle</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!-- 定时任务-->
            <dependency>
                <groupId>com.gzairports</groupId>
                <artifactId>al-quartz</artifactId>
                <version>${kgkj-master.version}</version>
            </dependency>

            <!-- 代码生成-->
            <dependency>
                <groupId>com.gzairports</groupId>
                <artifactId>al-generator</artifactId>
                <version>${kgkj-master.version}</version>
            </dependency>

            <!-- 核心模块-->
            <dependency>
                <groupId>com.gzairports</groupId>
                <artifactId>al-framework</artifactId>
                <version>${kgkj-master.version}</version>
            </dependency>

            <!-- 系统模块-->
            <dependency>
                <groupId>com.gzairports</groupId>
                <artifactId>al-system</artifactId>
                <version>${kgkj-master.version}</version>
            </dependency>

            <!-- 系统模块-->
            <dependency>
                <groupId>com.gzairports</groupId>
                <artifactId>al-oss</artifactId>
                <version>1.0</version>
            </dependency>

            <!-- 通用工具-->
            <dependency>
                <groupId>com.gzairports</groupId>
                <artifactId>al-common</artifactId>
                <version>${kgkj-master.version}</version>
            </dependency>

            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>1.6.2</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!--集成logstash-->
            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>${logstash-logback.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-validation</artifactId>
                <version>${springboot.version}</version>
            </dependency>


            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
        </dependencies>


    </dependencyManagement>



    <dependencies>
        <!-- swagger3-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
        </dependency>


        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gzairports</groupId>
            <artifactId>ojdbc8</artifactId>
            <version>1.0.28</version>
        </dependency>

        <dependency>
            <groupId>com.gzairports</groupId>
            <artifactId>SADK-CMBC</artifactId>
            <version>3.1.0.8</version>
        </dependency>


        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
        </dependency>
        <dependency> <!-- 引入log4j2依赖 -->
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>


        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>


</project>