package com.gzairports.hz.business.departure.domain.vo;

import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: ljg
 * @Desc: 账单导出 VO
 * @create: 2025-03-17 10:00:00
 **/

@Data
public class BilExportVO {

    /**
     * 标题
     */
    @Excel(name = "标题", needMerge = true)
    private String title;

    /**
     * 充值金额
     */
    @Excel(name = "充值金额", needMerge = true, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal rechargeAmount = BigDecimal.ZERO;

    /**
     * 上期余额
     * */
    @Excel(name = "上期余额", needMerge = true, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal lastBalance = BigDecimal.ZERO;

    /**
     * 当期退款金额
     * */
    @Excel(name = "当期退款", needMerge = true, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal refundCurrentAmount = BigDecimal.ZERO;

    /**
     * 非当期退款金额
     * */
    @Excel(name = "非当期退款", needMerge = true, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal refundNotCurrentAmount = BigDecimal.ZERO;

    /**
     * 已结算金额
     */
    @Excel(name = "已结算", needMerge = true, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal settledAmount = BigDecimal.ZERO;

    /**
     * 非当期支付的本期配载的已结算金额
     */
    @Excel(name = "非当期已结算", needMerge = true, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal settledNotCurrentAmount = BigDecimal.ZERO;

    /**
     * 未结算金额
     */
    @Excel(name = "未结算", needMerge = true, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal unsettledAmount = BigDecimal.ZERO;

    /**
     * 非当期支付的本期配载的未结算金额
     */
    @Excel(name = "非当期未结算", needMerge = true, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal unsettledNotCurrentAmount = BigDecimal.ZERO;

    /**
     * 未来航班金额 当前已支付未配载的金额
     * */
    @Excel(name = "未来航班", needMerge = true, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal futureFlightAmount = BigDecimal.ZERO;

    /**
     * 进港金额
     * */
    @Excel(name = "进港已结算", needMerge = true, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal arrAmount = BigDecimal.ZERO;

    /**
     * 余额
     */
    @Excel(name = "当期余额", needMerge = true, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal balance = BigDecimal.ZERO;

    /**
     * 总未结算
     * */
    @Excel(name = "总未结算", needMerge = true, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal notSettleAmount = BigDecimal.ZERO;

    /**
     * 本期财务余额
     */
    @Excel(name = "本期财务余额", needMerge = true, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal thisTimeFinanceBalance = BigDecimal.ZERO;


    /**
     * 结算周期
     */
    @Excel(name = "结算周期", needMerge = true)
    private String settlementCycle;

    /**
     * 备注
     */
    @Excel(name = "备注", needMerge = true)
    private String remarks;

    /**
     * 已结算
     * */
    private List<ChargeBilExportVO> chargeBilExportVOS;

    /**
     * 未结算
     * */
    private List<ChargeBilExportNotSettleVO> chargeBilExportVOSNotSettle;

    /**
     * 当期退款
     * */
    private List<ChargeBillRefundExportVO> refundCurrentAmountList;

    /**
     * 非当期退款
     * */
    private List<ChargeBillRefundExportVO> refundNotCurrentAmountList;


    /**
     * 已结算
     * */
    private List<ChargeBilExportVO> settledAmountList;

    /**
     * 非当期已结算
     * */
    private List<ChargeBilExportNotSettleVO> settledNotCurrentAmountList;

    /**
     * 未结算
     * */
    private List<ChargeBilExportNotSettleVO> unsettledAmountList;

    /**
     * 非当期未结算
     * */
    private List<ChargeBilExportNotSettleVO> unsettledNotCurrentAmountList;

    /**
     * 未来航班
     * */
    private List<ChargeBilExportFutureFlightVO> futureFlightAmountList;

    /**
     * 进港已结算
     * */
    private List<ChargeBillArrSettleExportVO> arrAmountList;

    /**
     * 总未结算
     * */
    private List<ChargeBilExportNotSettleVO> notSettleAmountList;
}
