package com.gzairports.common.business.arrival.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 货站申请修改数据
 *
 * <AUTHOR>
 * @date 2024-07-23
 */
@Data
public class HzApplyEditVo {

    /** 办理提货id */
    private Long id;

    /** 流水号 */
    private String serialNo;

    /** 代理人 */
    private String agent;

    /** 件数 */
    private Integer totalQuantity;

    /** 重量 */
    private BigDecimal totalWeight;

    /** 申请修改描述 */
    private String applyEdit;

    /** 修改审批id */
    private Long editId;

    /** 运单数据集合 */
    private List<PickUpVo> pickUpVos;
}
