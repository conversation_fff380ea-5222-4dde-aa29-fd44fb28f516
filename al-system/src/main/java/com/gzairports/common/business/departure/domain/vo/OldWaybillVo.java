package com.gzairports.common.business.departure.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.opencsv.bean.CsvBindByName;
import com.opencsv.bean.CsvDate;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 国内主单表(旧数据导入)
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
@Data
public class OldWaybillVo {
    /** 主键id */
    private Long id;

    /** 运单号 */
    @CsvBindByName(column = "BILLID")
    private String waybillCode;


    /** 原单单号 */
    @CsvBindByName(column = "PREVIOUSBILLID")
    private String originBill;

    /** 状态 STAGING 暂存 NORMAL 正常 INVALID 作废*/
    @CsvBindByName(column = "STATUS")
    private String status;

    /** 始发站 */
    @CsvBindByName(column = "SAIRPORTID")
    private String sourcePort;

    /** 目的站 */
    @CsvBindByName(column = "EAIRPORTID")
    private String desPort;

    /** 航班号 */
    @CsvBindByName(column = "PREFLIGHTNO")
    private String flightNo1;

    @CsvBindByName(column = "PREAIRLINE")
    private String PREAIRLINE;

    /** 航班日期 */
    @CsvBindByName(column = "PREFLIGHTDATE")
    @CsvDate("yyyy-MM-dd HH:mm:ss")
    private Date flightDate1;


    /** 航班号 */
    @CsvBindByName(column = "PREFLIGHTNO1")
    private String flightNo2;

    /** 航班日期 */
    @CsvBindByName(column = "PREFLIGHTDATE1")
    @CsvDate("yyyy-MM-dd HH:mm:ss")
    private Date flightDate2;

    @CsvBindByName(column = "PREAIRLINE1")
    private String PREAIRLINE1;

    /** 承运人1 */
    @CsvBindByName(column = "BY1")
    private String carrier1;

    /** 到达站1 */
    @CsvBindByName(column = "DEST1")
    private String des1;

    /** 承运人2 */
    @CsvBindByName(column = "BY2")
    private String carrier2;

    /** 到达站2 */
    @CsvBindByName(column = "DEST2")
    private String des2;

    /** 承运人3 */
    @CsvBindByName(column = "BY3")
    private String carrier3;

    /** 到达站3 */
    @CsvBindByName(column = "DEST3")
    private String des3;

    /** 发货人简称 */
    @CsvBindByName(column = "SHPCUSTOMERID")
    private String shipperAbb;

    /** 发货人 */
    @CsvBindByName(column = "SHPRNAME")
    private String shipper;

    /** 发货人电话 */
    @CsvBindByName(column = "SHPRTEL")
    private String shipperPhone;

    /** 发货人地址 */
    @CsvBindByName(column = "SHPRADDRESS")
    private String shipperAddress;

    /** 发货人地区 */
    private String shipperRegion;

    /** 收货人简称 */
    @CsvBindByName(column = "CSGCUSTOMERID")
    private String consignAbb;

    /** 收货人 */
    @CsvBindByName(column = "CNSNNAME")
    private String consign;

    /** 收货人电话 */
    @CsvBindByName(column = "CNSNTEL")
    private String consignPhone;

    /** 收货人地址 */
    @CsvBindByName(column = "CNSNADDRESS")
    private String consignAddress;

    /** 收货人地区 */
    private String consignRegion;

    /** 代理人公司 */
    @CsvBindByName(column = "CTRLOPEDEPARTMENT")
    private String agentCompany;


    private String agentCode;

    /** 结算注意事项 */
    @CsvBindByName(column = "BALANCEREMARK")
    private String settlementNotes;

    /** 储运注意事项 */
    @CsvBindByName(column = "STOREREMARK")
    private String storageTransportNotes;

    /** 海关监管 */

    private Integer customsSupervision;

    @CsvBindByName(column = "CUSTOMCTL")
    private String CUSTOMCTL;

    /** 特货代码1 */
    @CsvBindByName(column = "SPECOPEID")
    private String specialCargoCode1;

    /** 其他特货代码 */
    @CsvBindByName(column = "SPECOPEIDEXT")
    private String otherSpecialCargoCode;

    /** 品名编码 */
    @CsvBindByName(column = "CARGONO")
    private String cargoCode;

    /** 品名 */
    @CsvBindByName(column = "CARGONM")
    private String cargoName;

    /** 包装 */
    @CsvBindByName(column = "PACK")
    private String pack;

    /** 包装code */
    private String packCode;

    /** 件数 */
    @CsvBindByName(column = "PCS")
    private Integer quantity;

    /** 重量 */
    @CsvBindByName(column = "WEIGHT")
    private BigDecimal weight;

    /** 计费重量 */
    @CsvBindByName(column = "FEEWT")
    private BigDecimal chargeWeight;

    /** 体积（M3） */
    @CsvBindByName(column = "VOL")
    private BigDecimal volume;

    /** 尺寸 */
    @CsvBindByName(column = "MEAS")
    private String size;

    /** 是否需要冷藏 0 否 1 是 */
    @CsvBindByName(column = "REFRIGERATED")
    private Integer isCold;

    /** 是否需要冷藏 0 否 1 是 */
    @CsvBindByName(column = "REFRIGERATED")
    private String REFRIGERATED;

    /** 费用总计 */
    @CsvBindByName(column = "CARRIAGE")
    private BigDecimal costSum;

    /** 填开时间 */
    @TableField(value = "CRTDATE")
    @CsvDate("yyyy-MM-dd HH:mm:ss")
    private Date writeTime;

    /** 填开地点 */
    private String writeLocation;

    /** 填开人 */
    @CsvBindByName(column = "CRTAGENT")
    private String writer;

    /** 所属单位 */
    private Long deptId;

    /** 类型 DEP 出港 ARR 进港 */
    private String type;


    /** 运单类型  AWBA 主单 AWBM 邮件单*/
    @CsvBindByName(column = "STOCKTYPEID")
    private String waybillType;

    /** 支付金额 */
    @CsvBindByName(column = "COLLECTED")
    private String arrPay;

    @CsvBindByName(column = "DOMINT")
    private String domint;
}
