package com.gzairports.wl.departure.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.business.departure.domain.Mawb;
import com.gzairports.common.business.departure.domain.Transfer;
import com.gzairports.wl.departure.domain.*;
import com.gzairports.wl.departure.domain.query.FareQuery;
import com.gzairports.wl.departure.domain.query.HawbQuery;
import com.gzairports.wl.departure.domain.query.ItemQuery;
import com.gzairports.wl.departure.domain.vo.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * 主单制单Service接口
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
public interface IMawbService extends IService<Mawb> {

    /**
     * 主单制单
     *
     * @param vo 分单制单数据
     * @return 返回结果
     */
    String add(MawbVo vo);

    /**
     * 根据运单号以及单证控制校验运单号
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    MawbVo check(String waybillCode);

    /**
     * 根据默认收费方式计算运单运价
     *
     * @param query 新增运单信息
     * @return 返回结果
     */
    FareVo fare(FareQuery query);

    /**
     * 添加收费管理
     *
     * @param query 分单收费项目参数
     * @return 返回结果
     */
    FareVo addFees(ItemQuery query);

    /**
     * 运单作废
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    int invalid(String waybillCode);

    /**
     * 根据运单code查询运单详情
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    MawbVo getInfo(String waybillCode);

    /**
     * 编辑运单
     *
     * @param waybillCode 运单详情信息
     */
    void edit(String waybillCode);

    /**
     * 修改费用项
     *
     * @param item 分单收费项目
     * @return 返回结果
     */
    FareVo editFee(MawbItem item);

    /**
     * 删除费用项
     *
     * @param item 分单收费项目
     * @return 返回结果
     */
    BigDecimal delFee(MawbItem item);

    /**
     * 更改拼单
     *
     * @param waybillCode 分单运单号
     * @param masterWaybillCode 分单关联主单号
     * @return 返回结果
     */
    int alter(String waybillCode, String masterWaybillCode);

    /**
     * 运单取消作废
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    int cancelVoid(String waybillCode);

    /**
     * 新增运单异常备注
     *
     * @param remark 运单异常备注
     * @return 返回结果
     */
    int addRemark(MawbErrorRemark remark);

    /**
     * 根据运单号查询异常备注列表
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    List<MawbErrorRemark> remarkList(String waybillCode);

    /**
     * 根据代理人公司和code码查询代理人账号
     * @param agentCom 代理人公司
     * @param agentCode code码
     * @return 代理人账号
     */
    String agent(String agentCom, String agentCode);

    /**
     * 国内主单查询
     * @param query 查询条件
     * @return 结果
     */
    HawbQueryVo queryList(HawbQuery query);

    /**
     * 打印主单标签
     * @param waybillCode 运单号
     * @return 主单标签数据
     */
    PrintMawbVo printMawb(String waybillCode);

    /**
     * 查询可补货数据
     * @param waybillCode 原单单号
     * @return 结果
     */
    ReplenishVo replenish(String waybillCode);

    /**
     * 打印接口
     * @param id 主单id
     * @param response 返回流
     */
    void printMawbData(Long id, HttpServletResponse response) throws Exception;

    /**
     * 打印主单标签
     * @param waybillCode 主单号
     * @param response 返回流
     */
    void printLabel(String waybillCode, HttpServletResponse response,Integer quantity, Integer isWeight) throws Exception;

    /**
     * 更新运单安检申报单地址
     * @param vo 更新数据
     */
    void updateSecurityUrl(MawbVo vo);

    /**
     * 安检申报单打印
     * @param id 运单id
     * @param response 返回流
     */
    void printSecurityCheck(Long id, HttpServletResponse response) throws Exception;

    /**
     * 上传随附文件
     * @param vo 上传参数
     * @return 结果
     */
    int uploadTransportFile(TransportFileVo vo);

    /**
     * 根据主单id查询随附文件
     * @param waybillId 主单id
     * @return 结果
     */
    TransportFileVo selectTransportFile(String waybillId);

    /**
     * 根据主单id查询交接单id
     * @param waybillId 主单id
     * @return 结果
     */
    Transfer selectTransferIdByMawbId(String waybillId);

    /**
     * 更换运单号
     * @param editWaybillCode 更换参数
     * @return 结果
     */
    int editWaybillCode(EditWaybillCode editWaybillCode);

    /**
     * 添加运价条目
     * @param query 添加参数
     * @return 结果
     */
    FareVo addItems(ItemQuery query);

    void printCurrencyLabel(String waybillCode, HttpServletResponse response, Integer quantity, Integer isWeight) throws Exception;

    void printHTLDLabel(String waybillCode, HttpServletResponse response, Integer quantity, Integer isWeight) throws Exception;

    List<MawbQueryVo> exportList(HawbQuery query);

    /**
     * 告知 -> 通过运单id修改运单某个字段的状态
     * */
    int notifyWaybillById(Long id);

    int cancelPay(Long waybillId);
}
