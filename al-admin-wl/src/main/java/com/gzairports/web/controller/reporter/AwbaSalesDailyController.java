package com.gzairports.web.controller.reporter;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gzairports.common.business.reporter.domain.ReportDataWaybill;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.wl.reporter.domain.query.AwbaSalesQuery;
import com.gzairports.wl.reporter.domain.query.ReportExportData;
import com.gzairports.wl.reporter.service.IAwbaSalesDailyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2025-02-20
 **/
@Validated
@RestController
@RequestMapping("/wl/awbaSales")
@Api(tags = "主单销售日报")
public class AwbaSalesDailyController extends BaseController {

    @Autowired
    private IAwbaSalesDailyService dailyService;


    /**
     * 报表列表导出
     */
    @GetMapping("/export")
    @ApiOperation(value = "报表列表导出")
    public void export(AwbaSalesQuery query, HttpServletResponse response) {
        ReportExportData reportExportData = dailyService.queryExportDataList(query);
        dailyService.export(reportExportData, response);
    }

    /**
     * 报表列表查询
     */
    @GetMapping("/page")
    @ApiOperation(value = "报表列表分页数据")
    public TableDataInfo pageQuery(AwbaSalesQuery query){
//        startPage();
        IPage<ReportDataWaybill> pageData = dailyService.pageQuery(query);
        return new TableDataInfo(Collections.emptyList(), (int) pageData.getTotal());
    }

    /**
     * 报表打印-PDF文件
     */
    @GetMapping("/print/pdf")
    @ApiOperation(value = "报表打印-PDF文件")
    public void printPDF(AwbaSalesQuery query, HttpServletResponse response) {
        dailyService.printPDF(query, response);
    }

}
