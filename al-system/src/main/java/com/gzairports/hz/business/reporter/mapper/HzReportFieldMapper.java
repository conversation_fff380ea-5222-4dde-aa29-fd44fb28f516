package com.gzairports.hz.business.reporter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.hz.business.reporter.domain.HzReportField;
import com.gzairports.hz.business.reporter.domain.HzReportSetCount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: lan
 * @create: 2025-03-05 17:25
 **/

@Mapper
public interface HzReportFieldMapper extends BaseMapper<HzReportField> {
    List<HzReportField> selectReportFieldList(Long type);


    List<String> selectFields(@Param("masterTable") Integer masterTable,
                              @Param("slaveTable") Integer slaveTable,
                              @Param("slaveTable2") Integer slaveTable2);
}
