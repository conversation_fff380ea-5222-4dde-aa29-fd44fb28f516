package com.gzairports.hz.business.reporter.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @author: lan
 * @Desc: 货站报表设置合计设置表
 * @create: 2025-03-05 17:00
 **/

@Data
@TableName("hz_report_field")
public class HzReportField {
    /** 主键id */
    private Long id;

    /** fieldId */
    @TableField(exist = false)
    private Long fieldId;

    /** 表类型
     * 0:分单 1:分单费用数据 2:主单 3:主单收运数据
     * 4:预支付费用数据 5:配载数据 6:结算费用数据
     * 7:拉下 8:理货数据 9:提货办单数据 10:进港费用数据
     * 11:提货出库数据 12:运单保障节点 13:冷藏登记
     * 14:服务 */
    private Integer type;

    /** 字段中文 */
    private String fieldNameCn;

    /** 字段 */
    private String fieldName;

    /** 字段类型 */
    private String fieldType;
}
