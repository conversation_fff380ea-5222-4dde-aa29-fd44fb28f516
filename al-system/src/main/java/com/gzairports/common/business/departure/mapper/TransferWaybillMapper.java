package com.gzairports.common.business.departure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.departure.domain.Transfer;
import com.gzairports.common.business.departure.domain.TransferWaybill;
import org.apache.ibatis.annotations.Mapper;

/**
 * 交接单与运单关联Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Mapper
public interface TransferWaybillMapper extends BaseMapper<TransferWaybill> {

    /**
     * 根据运单id查询交接单状态
     * @param id 运单id
     * @return 交接单状态
     */
    String selectTransferStatus(Long id);

    /**
     * 根据主单id查询交接单id
     * @param waybillId 运单id
     * @return 交接单状态
     */
    Transfer selectTransferIdByMawbId(String waybillId);
}
