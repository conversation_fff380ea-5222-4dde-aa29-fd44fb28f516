package com.gzairports.wl.charge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.*;
import com.gzairports.common.basedata.mapper.*;
import com.gzairports.common.basedata.service.impl.WeightServiceImpl;
import com.gzairports.common.charge.domain.vo.HzItemRuleVo;
import com.gzairports.common.exception.ServiceException;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.bean.BeanValidators;
import com.gzairports.wl.charge.domain.*;
import com.gzairports.wl.charge.domain.query.FreightRateCustomQuery;
import com.gzairports.wl.charge.domain.vo.FreightRateCustomVO;
import com.gzairports.wl.charge.domain.vo.FreightRateItemVO;
import com.gzairports.wl.charge.domain.vo.RateItemImportVo;
import com.gzairports.wl.charge.mapper.FreightRateCustomAirMapper;
import com.gzairports.wl.charge.mapper.FreightRateCustomItemMapper;
import com.gzairports.wl.charge.mapper.FreightRateCustomMapper;
import com.gzairports.wl.charge.service.FreightRateCustomService;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.Validator;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 客户运价service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@Service
public class FreightRateCustomServiceImpl extends ServiceImpl<FreightRateCustomMapper, FreightRateCustom>
        implements FreightRateCustomService {

    @Autowired
    private FreightRateCustomMapper customMapper;

    @Autowired
    private FreightRateCustomItemMapper customItemMapper;

    @Autowired
    private FreightRateCustomAirMapper customAirMapper;

    @Autowired
    private BillTypeMapper billTypeMapper;

    @Autowired
    private CargoCategoryMapper cargoCategoryMapper;

    @Autowired
    private CargoCodeMapper cargoCodeMapper;

    @Autowired
    private AirportCodeMapper airportCodeMapper;

    /**
     * 查询客户运价列表
     *
     * @param query 查询参数
     * @return 客户运价列表
     */
    @Override
    public List<FreightRateCustomVO> selectList(FreightRateCustomQuery query) {
        //查询票证类型并存入集合,仅查询一次
        if (DOCUMENTMAP.isEmpty()) {
            selectTypeList();
        }
        query.setDeptId(SecurityUtils.getHighParentId());
        List<FreightRateCustomVO> freightRateCustomVos = customMapper.selectRateCustomList(query);
        for (FreightRateCustomVO freightRateCustomVO : freightRateCustomVos) {
            freightRateCustomVO.setApplicable(DOCUMENTMAP.get(freightRateCustomVO.getApplicableDocuments()));
            freightRateCustomVO.setAutomatic(freightRateCustomVO.getAutomaticFromLow() == 0 ? "否":"是");
            freightRateCustomVO.setRounding(ROUNDRULE.get(freightRateCustomVO.getRoundingMethod()));
        }
        return freightRateCustomVos;
    }


    /**
     * 查询客户运价列表
     *
     * @param ids 查询参数
     * @return 客户运价列表
     */
    @Override
    public List<FreightRateCustomVO> selectListByIds(List<Long> ids) {
        //查询票证类型并存入集合,仅查询一次
        if (DOCUMENTMAP.isEmpty()) {
            selectTypeList();
        }
        List<FreightRateCustomVO> freightRateCustomVos = customMapper.selectRateCustomListByIds(ids);
        for (FreightRateCustomVO freightRateCustomVO : freightRateCustomVos) {
            freightRateCustomVO.setApplicable(DOCUMENTMAP.get(freightRateCustomVO.getApplicableDocuments()));
            freightRateCustomVO.setAutomatic(freightRateCustomVO.getAutomaticFromLow() == 0 ? "否":"是");
            freightRateCustomVO.setRounding(ROUNDRULE.get(freightRateCustomVO.getRoundingMethod()));
        }
        return freightRateCustomVos;
    }

    /**
     * 导入客户运价条目
     * @param list 文件数据
     * @param rateId 导入所需数据
     * @param updateSupport 导入所需数据
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long importItem(List<FreightRateCustomItem> list, Long rateId, boolean updateSupport) {
        if (StringUtils.isNull(list) || list.size() == 0)
        {
            throw new ServiceException("导入客户运价条目数据不能为空！");
        }
        for (int i = 0; i < list.size(); i++) {
            int j = i + 1;
            if (list.get(i).getDepartureCity() == null){
                throw new ServiceException("第" + j + "条数据导入失败，始发城市为空");
            }
            if (list.get(i).getDestinationCity() == null){
                throw new ServiceException("第" + j + "条数据导入失败，目的城市为空");
            }
            if (list.get(i).getAirline() == null){
                throw new ServiceException("第" + j + "条数据导入失败，航司为空");
            }
            if (list.get(i).getMinimumFreight() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算M为空");
            }
            if (list.get(i).getRateWeightRange5() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算5为空");
            }
            if (list.get(i).getRateWeightRange10() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算10为空");
            }
            if (list.get(i).getBaseRateN() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算N为空");
            }
            if (list.get(i).getRateWeightRange45() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算45为空");
            }
            if (list.get(i).getRateWeightRange100() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算100为空");
            }
            if (list.get(i).getRateWeightRange300() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算300为空");
            }
            if (list.get(i).getRateWeightRange500() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算500为空");
            }
            if (list.get(i).getRateWeightRange1000() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算1000为空");
            }
            FreightRateCustom freightRateCustom = customMapper.selectById(rateId);
            BaseAirportCode desPort = airportCodeMapper.selectOne(new QueryWrapper<BaseAirportCode>()
                    .eq("chinese_name", list.get(i).getDestinationCity()));
            if(desPort == null){
                throw new ServiceException("第" + j + "条数据导入失败，目的城市 " + list.get(i).getDestinationCity() + " 基础数据机场代码里未维护");
            }
                FreightRateCustomItem customItem = customItemMapper.selectOne(new QueryWrapper<FreightRateCustomItem>()
                        .eq("clause_name", list.get(i).getClauseName())
                        .eq("rate_id",rateId));
                if (customItem != null){
                    throw new ServiceException("第" + j + "条数据导入失败、条目名称 " + list.get(i).getClauseName() + " 已存在");
                }
                FreightRateCustomItem item = new FreightRateCustomItem();
                BeanUtils.copyProperties(list.get(i),item);
                LoginUser loginUser = SecurityUtils.getLoginUser();
                item.setRateId(rateId);
                item.setDepartureCity("KWE");
                item.setDestinationCity(desPort.getCode());
                item.setCreateBy(loginUser.getUsername());
                item.setApplicableDocuments(freightRateCustom.getApplicableDocuments());
                item.setCreateTime(DateUtils.getNowDate());
                item.setDeptId(SecurityUtils.getHighParentId());
                if (list.get(i).getProductCode() != null && !"".equals(list.get(i).getProductCode())){
                    String[] split = list.get(i).getProductCode().split(";");
                    List<String> cargoCategoryList = new ArrayList<>();
                    List<String> productCodeList = new ArrayList<>();
                    for(String s:split){
                        BaseCargoCode cargoCode = cargoCodeMapper.selectCodeByName(s);
                        if(cargoCode != null){
                            //表格里面的货品代码填的是具体货品
                            BaseCargoCategory code = cargoCategoryMapper.selectOne(new QueryWrapper<BaseCargoCategory>()
                                    .eq("code", cargoCode.getCategoryCode()));
                            cargoCategoryList.add(code.getCode());
                            productCodeList.add(cargoCode.getCode());
                        }else{
                            List<BaseCargoCategory> code = cargoCategoryMapper.selectList(new QueryWrapper<BaseCargoCategory>()
                                    .eq("chinese_name", s));
                            if(code != null && code.size() > 0){
                                //表格里面填的是大类
                                List<String> cargoCategoryCollect = code.stream().map(BaseCargoCategory::getCode).collect(Collectors.toList());
                                cargoCategoryList.addAll(cargoCategoryCollect);
                            }else{
                                throw new ServiceException("导入失败，第"+ j +"条数据识别失败，货物种类 "+ s +" 不存在，请修改后重新导入");
                            }
                        }
                    }
                    List<String> collect = cargoCategoryList.stream().distinct().collect(Collectors.toList());
                    List<String> collect2 = productCodeList.stream().distinct().collect(Collectors.toList());
                    item.setCargoCategory(String.join(",",collect));
                    item.setProductCode(String.join(",",collect2));
                }
                item.setSpecialCargoCode(list.get(i).getSpecialCargoCode());
                customItemMapper.insert(item);
        }
        return rateId;
    }

    @Override
    public int delCustomAir(Long id) {
        FreightRateCustomAir customAir = customAirMapper.selectById(id);
        customAir.setIsDel(1);
        return customAirMapper.updateById(customAir);
    }

    /**
     * 查询客户运价详情
     *
     * @param id 客户运价主键
     * @return 客户运价
     */
    @Override
    public FreightRateCustomVO selectRateById(Long id) {
        FreightRateCustomVO vo = new FreightRateCustomVO();
        FreightRateCustom freightRateCustom = customMapper.selectById(id);
        BeanUtils.copyProperties(freightRateCustom,vo);
        List<FreightRateCustomAir> customAirs = customAirMapper.selectList(new QueryWrapper<FreightRateCustomAir>()
                .eq("custom_price_id", id)
                .eq("is_del",0));
        vo.setList(customAirs);
        return vo;
    }

    /**
     * 新增客户运价
     *
     * @param vo 客户运价
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertFreightRateCustom(FreightRateCustomVO vo) {
        //客户名称+适用单证类型+生效时间不能同时相同
        List<FreightRateCustom> list = customMapper.selectList(new QueryWrapper<FreightRateCustom>()
                .eq("customer", vo.getCustomer())
                .eq("dept_id",SecurityUtils.getHighParentId())
                .eq("is_del",0));
        for (FreightRateCustom freightRateCustom : list) {
            if((freightRateCustom.getEffectiveDate().before(vo.getEffectiveDate()) &&
                    freightRateCustom.getExpirationDate().after(vo.getEffectiveDate()))
                    || (freightRateCustom.getEffectiveDate().before(vo.getExpirationDate()) &&
                    freightRateCustom.getExpirationDate().after(vo.getExpirationDate())))
            {
                throw new CustomException("相同客户的运价生效和截止日期不能重叠");
            }
        }
        if (vo.getEffectiveDate().after(vo.getExpirationDate())){
            throw new CustomException("生效时间不能晚于截止时间");
        }
        FreightRateCustom custom = new FreightRateCustom();
        BeanUtils.copyProperties(vo,custom);
        custom.setDeptId(SecurityUtils.getHighParentId());
        custom.setCreateBy(SecurityUtils.getUsername());
        custom.setCreateTime(DateUtils.getNowDate());
        customMapper.insert(custom);
        if (!CollectionUtils.isEmpty(vo.getList())){
            Map<String, Long> duplicateCheck = vo.getList().stream()
                    .filter(item -> StringUtils.isNotEmpty(item.getCargoCode()) &&StringUtils.isNotEmpty(item.getCargoCategory()))
                    .map(item -> item.getCargoCode() + "-" + item.getCargoCategory())
                    .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

            duplicateCheck.forEach((key, count) -> {
                if (count > 1) {
                    throw new CustomException("货品大类和货品代码不能重复");
                }
            });
            Map<String, Long> nullCargoCodeCheck = vo.getList().stream()
                    .filter(item -> StringUtils.isEmpty(item.getCargoCode()) && StringUtils.isNotEmpty(item.getCargoCategory()))
                    .map(FreightRateCustomAir::getCargoCategory)
                    .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

            nullCargoCodeCheck.forEach((category, count) -> {
                if (count > 1) {
                    throw new CustomException("货品大类不能重复");
                }
            });
            long nullCargoCodeAndCategoryCount = vo.getList().stream()
                    .filter(item -> StringUtils.isEmpty(item.getCargoCode()) && StringUtils.isEmpty(item.getCargoCategory()))
                    .count();

            if (nullCargoCodeAndCategoryCount > 1) {
                throw new CustomException("增加值不能重复");
            }
        }
        for (FreightRateCustomAir freightRateCustomAir : vo.getList()) {
            freightRateCustomAir.setCustomPriceId(custom.getId());
            customAirMapper.insert(freightRateCustomAir);
        }
        return custom.getId();
    }

    /**
     * 修改客户运价
     *
     * @param vo 客户运价
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateFreightRateCustom(FreightRateCustomVO vo) {
        FreightRateCustom custom = customMapper.selectById(vo.getId());
        if (!custom.getCustomer().equals(vo.getCustomer())){
            //客户名称+适用单证类型+生效时间不能同时相同
            List<FreightRateCustom> list = customMapper.selectList(new QueryWrapper<FreightRateCustom>()
                    .eq("customer", vo.getCustomer())
                    .eq("dept_id",SecurityUtils.getHighParentId())
                    .eq("is_del",0));
            for (FreightRateCustom freightRateCustom : list) {
                if((freightRateCustom.getEffectiveDate().before(vo.getEffectiveDate()) &&
                        freightRateCustom.getExpirationDate().after(vo.getEffectiveDate()))
                        || (freightRateCustom.getEffectiveDate().before(vo.getExpirationDate()) &&
                        freightRateCustom.getExpirationDate().after(vo.getExpirationDate())))
                {
                    throw new CustomException("相同客户的运价生效和截止日期不能重叠");
                }
            }
        }
        if (vo.getEffectiveDate().after(vo.getExpirationDate())){
            throw new CustomException("生效时间不能晚于截止时间");
        }
        BeanUtils.copyProperties(vo,custom);
        customAirMapper.delete(new QueryWrapper<FreightRateCustomAir>().eq("custom_price_id",vo.getId()));
        if (!CollectionUtils.isEmpty(vo.getList())){
            Map<String, Long> duplicateCheck = vo.getList().stream()
                    .filter(item -> StringUtils.isNotEmpty(item.getCargoCode()) &&StringUtils.isNotEmpty(item.getCargoCategory()))
                    .map(item -> item.getCargoCode() + "-" + item.getCargoCategory())
                    .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

            duplicateCheck.forEach((key, count) -> {
                if (count > 1) {
                    throw new CustomException("货品大类和货品代码不能重复");
                }
            });
            Map<String, Long> nullCargoCodeCheck = vo.getList().stream()
                    .filter(item -> StringUtils.isEmpty(item.getCargoCode()) && StringUtils.isNotEmpty(item.getCargoCategory()))
                    .map(FreightRateCustomAir::getCargoCategory)
                    .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

            nullCargoCodeCheck.forEach((category, count) -> {
                if (count > 1) {
                    throw new CustomException("货品大类不能重复");
                }
            });
            long nullCargoCodeAndCategoryCount = vo.getList().stream()
                    .filter(item -> StringUtils.isEmpty(item.getCargoCode()) && StringUtils.isEmpty(item.getCargoCategory()))
                    .count();

            if (nullCargoCodeAndCategoryCount > 1) {
                throw new CustomException("增加值不能重复");
            }
        }
        for (FreightRateCustomAir freightRateCustomAir : vo.getList()) {
            freightRateCustomAir.setCustomPriceId(custom.getId());
            customAirMapper.insert(freightRateCustomAir);
        }
        custom.setUpdateBy(SecurityUtils.getUsername());
        custom.setUpdateTime(DateUtils.getNowDate());
        return customMapper.updateById(custom);
    }

    /**
     * 查看客户运价条目
     *
     * @param id 客户运价id
     * @return 客户运价条目集合
     */
    @Override
    public List<FreightRateItemVO> selectRateItemList(Long id) {
        return customItemMapper.selectRateItemList(id);
    }

    /**
     * 删除客户运价
     *
     * @param id 客户运价主键
     * @return 结果
     */
    @Override
    public int deleteFreightRateCustom(Long id) {
        FreightRateCustom custom = customMapper.selectById(id);
        Date date = new Date();
        if (custom.getEffectiveDate().before(date) && custom.getExpirationDate().after(date)){
            throw new CustomException("在有效期内的运价不能删除");
        }
        return customMapper.removeRateCustom(id);
    }




    public void selectTypeList() {
        List<BaseBillType> baseBillTypes = billTypeMapper.selectTypeList();
        for (BaseBillType baseBillType:baseBillTypes) {
            String code = baseBillType.getCode();
            String name = baseBillType.getName();
            DOCUMENTMAP.put(code,name);
        }
    }

    private static final Map<String,String> DOCUMENTMAP = new HashMap<>();
 /*   static {
        DOCUMENTMAP.put(0,"国内分单");
        DOCUMENTMAP.put(1,"国际分单");
        DOCUMENTMAP.put(2,"国内主单");
        DOCUMENTMAP.put(3,"国际主单");
        DOCUMENTMAP.put(4,"邮件单");
    }*/

    private static final Map<Integer,String> ROUNDRULE = new HashMap<>();
    static {
        ROUNDRULE.put(0,"实际");
        ROUNDRULE.put(1,"四舍五入至个位");
        ROUNDRULE.put(2,"四舍五入至0.1");
        ROUNDRULE.put(3,"四舍五入至0.01");
        ROUNDRULE.put(4,"四舍五入至0.001");
        ROUNDRULE.put(5,"直接进位到0.1");
        ROUNDRULE.put(6,"直接舍位到0.1");
        ROUNDRULE.put(7,"直接进位到个位");
        ROUNDRULE.put(8,"直接舍位到个位");
        ROUNDRULE.put(9,"直接进位到0.5");
        ROUNDRULE.put(10,"直接舍位到0.5");
    }
}
