package com.gzairports.hz.business.reporter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.hz.business.reporter.domain.HzReportSet;
import com.gzairports.hz.business.reporter.domain.HzReportSetFilter;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @author: lan
 * @create: 2025-03-05 17:25
 **/

@Mapper
public interface HzReportSetFilterMapper extends BaseMapper<HzReportSetFilter> {

    /**
     * 根据设置id查询过滤字段
     * @param setId 设置id
     * @return 结果
     */
    List<HzReportSetFilter> getFilter(Long setId);
}
