package com.gzairports.common.charge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.charge.domain.HzChargeRule;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 计费规则Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-13
 */
@Mapper
public interface HzChargeRuleMapper extends BaseMapper<HzChargeRule>
{
    /**
     * 查询计费规则
     * 
     * @param id 计费规则主键
     * @return 计费规则
     */
    public HzChargeRule selectHzChargeRuleById(Long id);

    /**
     * 查询计费规则列表
     * 
     * @param hzChargeRule 计费规则
     * @return 计费规则集合
     */
    public List<HzChargeRule> selectHzChargeRuleList(HzChargeRule hzChargeRule);

    /**
     * 新增计费规则
     * 
     * @param hzChargeRule 计费规则
     * @return 结果
     */
    public int insertHzChargeRule(HzChargeRule hzChargeRule);

    /**
     * 修改计费规则
     * 
     * @param hzChargeRule 计费规则
     * @return 结果
     */
    public int updateHzChargeRule(HzChargeRule hzChargeRule);

    /**
     * 删除计费规则
     * 
     * @param id 计费规则主键
     * @return 结果
     */
    public int deleteHzChargeRuleById(Long id);

}
