package com.gzairports.wl.departure.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * Created by david on 2024/11/25
 * <AUTHOR>
 */
@Data
public class TransFerWaybillsVo {

    private String waybillCode;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date writeTime;

    private Date startTime;

    private Date endTime;

    private Long deptId;
}
