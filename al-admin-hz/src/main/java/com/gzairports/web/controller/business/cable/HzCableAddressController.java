package com.gzairports.web.controller.business.cable;


import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.hz.business.cable.domain.HzCableAddress;
import com.gzairports.hz.business.cable.domain.query.TypeAndAddressQuery;
import com.gzairports.hz.business.cable.service.IHzCableAddressService;
import com.gzairports.wl.charge.domain.query.FreightRateCustomQuery;
import com.gzairports.wl.charge.domain.vo.FreightRateAirVO;
import com.gzairports.wl.charge.domain.vo.FreightRateCustomVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 电报地址管理Controller
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
@RestController
@RequestMapping("/bus/cableAddress")
@Api(tags = "电报地址管理")
public class HzCableAddressController extends BaseController
{
    @Autowired
    private IHzCableAddressService hzCableAddressService;

    /**
     * 查询电报地址管理列表
     */
//    @PreAuthorize("@ss.hasPermi('bus:cableAddress:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询电报地址管理列表")
    public TableDataInfo list(HzCableAddress hzCableAddress)
    {
        startPage();
        List<HzCableAddress> list = hzCableAddressService.selectHzCableAddressList(hzCableAddress);
        return getDataTable(list);
    }

    /**
     * 导出地址管理列表
     */
    @PreAuthorize("@ss.hasPermi('bus:cableAddress:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出地址管理列表")
    public void export(HttpServletResponse response, HzCableAddress address){
        List<HzCableAddress> list = hzCableAddressService.selectHzCableAddressList(address);
        ExcelUtil<HzCableAddress> util = new ExcelUtil<HzCableAddress>(HzCableAddress.class);
        util.exportExcel(response, list, "地址管理");
    }

    /**
     * 下载模板
     */
    @PreAuthorize("@ss.hasPermi('bus:cableAddress:exportTemp')")
    @PostMapping("/exportTemp")
    @ApiOperation(value = "下载模板")
    public void exportTemp(HttpServletResponse response){
        List<HzCableAddress> list = new ArrayList<>();
        ExcelUtil<HzCableAddress> util = new ExcelUtil<HzCableAddress>(HzCableAddress.class);
        util.exportExcel(response, list, "地址管理");
    }

    /**
     * 导入地址管理
     */
    @PreAuthorize("@ss.hasPermi('bus:cableAddress:import')")
//    @Log(title = "电报地址管理", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    @ApiOperation(value = "导入地址管理")
    public AjaxResult importCableAddress(@RequestParam("file") MultipartFile file,@RequestParam("updateSupport") boolean updateSupport) throws Exception
    {
        ExcelUtil<HzCableAddress> util = new ExcelUtil<HzCableAddress>(HzCableAddress.class);
        List<HzCableAddress> cableAddresses = util.importExcel(file.getInputStream());
        String message = hzCableAddressService.importCableAddress(cableAddresses, updateSupport);
        return success(message);
    }

    /**
     * 获取电报地址管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('bus:cableAddress:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取电报地址管理详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(hzCableAddressService.selectHzCableAddressById(id));
    }

    /**
     * 新增电报地址管理
     */
    @PreAuthorize("@ss.hasPermi('bus:cableAddress:add')")
//    @Log(title = "电报地址管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ApiOperation(value = "电报地址管理")
    public AjaxResult add(@RequestBody HzCableAddress hzCableAddress)
    {
        return toAjax(hzCableAddressService.insertHzCableAddress(hzCableAddress));
    }

    /**
     * 修改电报地址管理
     */
    @PreAuthorize("@ss.hasPermi('bus:cableAddress:edit')")
//    @Log(title = "电报地址管理", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ApiOperation(value = "电报地址管理")
    public AjaxResult edit(@RequestBody HzCableAddress hzCableAddress)
    {
        return toAjax(hzCableAddressService.updateHzCableAddress(hzCableAddress));
    }

    /**
     * 删除电报地址管理
     */
    @PreAuthorize("@ss.hasPermi('bus:cableAddress:remove')")
//    @Log(title = "电报地址管理", businessType = BusinessType.DELETE)
	@GetMapping("/remove/{id}")
    @ApiOperation(value = "删除电报地址管理")
    public AjaxResult remove(@PathVariable("id") Long id)
    {
        return toAjax(hzCableAddressService.deleteHzCableAddressById(id));
    }

    /**
     * 查询电报地址管理列表
     */
    @GetMapping("/getList")
    @ApiOperation(value = "新查询电报地址管理列表")
    public AjaxResult getList()
    {
        return AjaxResult.success(hzCableAddressService.newSelectHzCableAddressList());
    }
}
