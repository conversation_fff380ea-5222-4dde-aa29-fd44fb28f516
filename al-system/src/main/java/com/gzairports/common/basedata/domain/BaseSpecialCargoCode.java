package com.gzairports.common.basedata.domain;

import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 装载特货代码
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Data
public class BaseSpecialCargoCode {

    /** 主键id */
    private Long id;

    /** 特殊处理代码 */
    @Excel(name = "特殊处理代码")
    private String handleCode;

    /** 航空公司 */
    @Excel(name = "航空公司")
    private String airCompany;

    /** 危险品 */
    @Excel(name = "危险品")
    private Integer isDanger;

    /** 中文描述 */
    @Excel(name = "中文描述")
    private String chineseDescription;

    /** 英文描述 */
    @Excel(name = "英文描述")
    private String englishDescription;

    /** 承运人代码 */
    @Excel(name = "承运人代码")
    private String carrierCode;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;
}
