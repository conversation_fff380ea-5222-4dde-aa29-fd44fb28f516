<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.arrival.mapper.SerialNumberMapper">
    <insert id="insertNewRecord">
        INSERT INTO serial_number (date_str, prefix, last_sequence) VALUES (#{dateStr}, #{prefix}, 1)
    </insert>
    <update id="incrementSequence">
        UPDATE serial_number SET last_sequence = last_sequence + 1 WHERE date_str = #{dateStr}
        AND prefix = #{prefix}
        AND update_time = #{updateTime}
    </update>
    <select id="selectByDateAndPrefix"
            resultType="com.gzairports.common.business.arrival.domain.vo.SerialNumberRecord">
        SELECT last_sequence, update_time FROM serial_number WHERE date_str = #{dateStr} AND prefix = #{prefix}
    </select>
</mapper>