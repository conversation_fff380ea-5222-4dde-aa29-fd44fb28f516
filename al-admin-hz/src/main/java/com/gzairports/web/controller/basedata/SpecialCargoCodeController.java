package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BaseSpecialCargoCode;
import com.gzairports.common.basedata.domain.query.SpecialCargoCodeQuery;
import com.gzairports.common.basedata.service.ISpecialCargoCodeService;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 装载特货代码Controller
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@RestController
@RequestMapping("/base/specialCargoCode")
@Api(tags = "装载特货代码数据接口")
public class SpecialCargoCodeController extends BaseController {

    @Autowired
    private ISpecialCargoCodeService specialCargoCodeService;

    /**
     * 查询装载特货代码列表
     */
//    @PreAuthorize("@ss.hasPermi('base:specialCargoCode:specialCargoCodeList')")
    @GetMapping("/specialCargoCodeList")
    @ApiOperation(value = "查询装载特货代码列表")
    public TableDataInfo specialCargoCodeList(SpecialCargoCodeQuery query)
    {
        startPage();
        List<BaseSpecialCargoCode> list = specialCargoCodeService.selectSpecialCargoCodeList(query);
        return getDataTable(list);
    }

    /**
     * 导出装载特货代码
     */
    @PreAuthorize("@ss.hasPermi('base:specialCargoCode:exportSpecialCargoCode')")
    @Log(title = "导出装载特货代码", businessType = BusinessType.EXPORT)
    @PostMapping("/exportSpecialCargoCode")
    @ApiOperation(value = "导出装载特货代码")
    public void exportSpecialCargoCode(HttpServletResponse response, SpecialCargoCodeQuery query)
    {
        List<BaseSpecialCargoCode> list = specialCargoCodeService.selectSpecialCargoCodeList(query);
        ExcelUtil<BaseSpecialCargoCode> util = new ExcelUtil<BaseSpecialCargoCode>(BaseSpecialCargoCode.class);
        util.exportExcel(response, list, "装载特货代码");
    }


    /**
     * 导出装载特货代码模板
     */
    @PreAuthorize("@ss.hasPermi('base:specialCargoCode:exportSpecialCargoCode')")
    @Log(title = "导出装载特货代码模板", businessType = BusinessType.EXPORT)
    @PostMapping("/exportSpecialCargoCodeNull")
    @ApiOperation(value = "导出装载特货代码模板")
    public void exportSpecialCargoCodeNull(HttpServletResponse response)
    {
        List<BaseSpecialCargoCode> list = new ArrayList<>();
        ExcelUtil<BaseSpecialCargoCode> util = new ExcelUtil<BaseSpecialCargoCode>(BaseSpecialCargoCode.class);
        util.exportExcel(response, list, "装载特货代码模板");
    }

    /**
     * 导入装载特货代码
     */
    @PreAuthorize("@ss.hasPermi('base:specialCargoCode:importSpecialCargoCode')")
    @Log(title = "导入装载特货代码", businessType = BusinessType.IMPORT)
    @PostMapping("/importSpecialCargoCode")
    @ApiOperation(value = "导入装载特货代码")
    public AjaxResult importSpecialCargoCode(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<BaseSpecialCargoCode> util = new ExcelUtil<BaseSpecialCargoCode>(BaseSpecialCargoCode.class);
        List<BaseSpecialCargoCode> codes = util.importExcel(file.getInputStream());
        String message = specialCargoCodeService.importSpecialCargoCode(codes, updateSupport);
        return success(message);
    }

    /**
     * 新增装载特货代码
     */
    @PreAuthorize("@ss.hasPermi('base:specialCargoCode:addSpecialCargoCode')")
    @Log(title = "新增装载特货代码", businessType = BusinessType.INSERT)
    @PostMapping("/addSpecialCargoCode")
    @ApiOperation(value = "新增装载特货代码")
    public AjaxResult addSpecialCargoCode(@RequestBody BaseSpecialCargoCode code)
    {
        return toAjax(specialCargoCodeService.addSpecialCargoCode(code));
    }

    /**
     * 修改装载特货代码
     */
    @PreAuthorize("@ss.hasPermi('base:specialCargoCode:editSpecialCargoCode')")
    @Log(title = "修改装载特货代码", businessType = BusinessType.UPDATE)
    @PostMapping("/editSpecialCargoCode")
    @ApiOperation(value = "修改装载特货代码")
    public AjaxResult editSpecialCargoCode(@RequestBody BaseSpecialCargoCode code)
    {
        return toAjax(specialCargoCodeService.editSpecialCargoCode(code));
    }

    /**
     * 删除装载特货代码
     */
    @PreAuthorize("@ss.hasPermi('base:specialCargoCode:delSpecialCargoCode')")
    @Log(title = "删除装载特货代码", businessType = BusinessType.UPDATE)
    @GetMapping("/delSpecialCargoCode/{ids}")
    @ApiOperation(value = "删除装载特货代码")
    public AjaxResult delSpecialCargoCode(@PathVariable("ids") Long[] ids)
    {
        return toAjax(specialCargoCodeService.delSpecialCargoCode(ids));
    }
}
