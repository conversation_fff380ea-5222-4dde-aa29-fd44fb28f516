package com.gzairports.wl.departure.domain.query;

import lombok.Data;

import java.util.Date;

/**
 * 分单账单管理查询参数
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@Data
public class BillQuery {

    /** 生成账单时间 */
    private Date startTime;

    /** 生成账单时间 */
    private Date endTime;

    /** 发货人 */
    private String shipper;

    /** 支付状态 */
    private String payStatus;

    /** 所属单位 */
    private Long deptId;
}
