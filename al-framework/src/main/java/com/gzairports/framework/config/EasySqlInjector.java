package com.gzairports.framework.config;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.injector.DefaultSqlInjector;
import com.baomidou.mybatisplus.extension.injector.methods.InsertBatchSomeColumn;

import java.util.List;

/**
 * mybatis拦截器
 * sql 默认注入器
 * <AUTHOR>
 * @date 2024/03/22
 */
public class EasySqlInjector extends DefaultSqlInjector {
    @Override
    public List<AbstractMethod> getMethodList(Class<?> mapperClass) {
        List<AbstractMethod> methodList = super.getMethodList(mapperClass);
        //增加自定义方法，字段注解上不等于FieldFill.DEFAULT的字段才会插入
        methodList.add(new InsertBatchSomeColumn());
        return methodList;
    }
}
