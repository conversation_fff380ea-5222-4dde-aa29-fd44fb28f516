package com.gzairports.hz.business.reporter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.hz.business.reporter.domain.HzReportFormula;
import com.gzairports.hz.business.reporter.domain.HzReportSet;
import com.gzairports.hz.business.reporter.domain.query.HzReportSetQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @author: lan
 * @create: 2025-03-05 17:25
 **/

@Mapper
public interface HzReportSetMapper extends BaseMapper<HzReportSet> {
    List<HzReportSet> selectHzReportSetList(HzReportSetQuery query);

    List<HzReportSet> selectReportTitle();

    int addFormula(HzReportFormula hzReportFormula);
}
