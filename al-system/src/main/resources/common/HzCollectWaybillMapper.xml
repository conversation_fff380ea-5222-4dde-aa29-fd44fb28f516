<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.departure.mapper.HzCollectWaybillMapper">
    <select id="selectCountByIds" resultType="java.lang.Integer">
        select count(1) from hz_collect_waybill where waybill_id in
        <foreach collection="waybillIds" item="waybillId" open="(" separator="," close=")">
            #{waybillId}
        </foreach>
    </select>
    <select id="selectListByIds"
            resultType="com.gzairports.hz.business.departure.domain.vo.ForwardImportWaybillVo">
        select hcw.id as collectId, aaw.id as waybillId, aaw.cargo_name, aaw.waybill_code, aaw.source_port, aaw.des1,
               hcwe.quantity, hcwe.weight, hcwe.plate_weight, hcwe.board_weight, aaw.flight_date1 as flightDate,
               hcwe.id as weightId, aaw.write_time,aaw.quantity as waybillQuantity, aaw.weight as waybillWeight
        from hz_collect_waybill hcw
                 left join all_air_waybill aaw on aaw.id = hcw.waybill_id
                 left join hz_collect_weight hcwe on hcwe.collect_id = hcw.id
        where hcw.id in
        <foreach collection="collectWaybillId" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="uld != null  and uld != ''"> and hcwe.uld = #{uld}</if>
    </select>
    <select id="selectListByQuery" resultType="com.gzairports.hz.business.departure.domain.vo.InventoryVo">
        select hcw.id, aaw.id as waybillId, hcw.waybill_code, hcw.uld, aaw.write_time, aaw.agent_code, aaw.quantity, aaw.weight,
               hcw.quantity as collectQuantity, hw.store, hw.locator, hcw.weight as collectWeight
        from hz_collect_waybill hcw
            left join hz_collect_weight hw on hcw.id = hw.collect_id
            left join all_air_waybill aaw on aaw.id = hcw.waybill_id
        <where>
            <if test="waybillCode != null  and waybillCode != ''"> and hcw.waybill_code = #{waybillCode}</if>
            <if test="agentCode != null  and agentCode != ''"> and aaw.agent_code = #{agentCode}</if>
            <if test="startTime != null">
                and hcw.collect_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and hcw.collect_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="store != null and store != ''">
                and hw.store = #{store}
            </if>
            <if test="locator != null and locator != ''">
                and hw.locator = #{locator}
            </if>
        </where>
    </select>
    <select id="selectEditInfoById"
            resultType="com.gzairports.hz.business.departure.domain.vo.CollectWaybillVo">
        select id, waybill_id, quantity, weight, uld from hz_collect_waybill where id = #{id}
    </select>
    <select id="selectInventoryList" resultType="com.gzairports.hz.business.departure.domain.vo.InventoryVo">
        select hcw.id, aaw.id as waybillId, hcw.waybill_code, hcw.uld, aaw.write_time, aaw.agent_code, aaw.quantity, aaw.weight, hcw.quantity as collectQuantity,
               hcw.weight as collectWeight
        from hz_collect_waybill hcw
                 left join all_air_waybill aaw on aaw.id = hcw.waybill_id
        <where>
            <if test="waybillCode != null and waybillCode != ''">
                hcw.waybill_code = #{waybillCode}
            </if>
        </where>
    </select>
    <select id="selectByCollectId"
            resultType="com.gzairports.hz.business.departure.domain.vo.ForwardImportWaybillVo">
        select hcw.id as collectId, hw.id as weightId, aaw.id as waybillId, aaw.cargo_name, aaw.special_cargo_code1, aaw.waybill_code,
               aaw.source_port, aaw.des1, hw.quantity, hw.weight, hw.board_weight, hw.plate_weight
        from hz_collect_waybill hcw
        left join hz_collect_weight hw on hcw.id = hw.collect_id
        left join all_air_waybill aaw on aaw.id = hcw.waybill_id
               where hw.is_load = 0 and hcw.waybill_id = #{collectId} and hw.uld =#{uld}
    </select>
    <select id="selectCollectList"
            resultType="com.gzairports.common.business.reporter.domain.ReportDataCollect">
        select aaw.waybill_code, hcw.waybill_id as reportId, hcw.oper_name, hcw.is_real, hcw.collect_time, hcw.quantity, hcw.weight,
        hcw.uld
        from hz_collect_waybill hcw
            left join all_air_waybill aaw on aaw.id = hcw.waybill_id
        where 1 = 1
        <if test="depReportIds != null and depReportIds.size() > 0">
            and hcw.waybill_id in
            <foreach collection="depReportIds" item="reportId" open="(" separator="," close=")">
                #{reportId}
            </foreach>
        </if>
    </select>
</mapper>