<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.wl.departure.mapper.BillMapper">

    <resultMap id="BillVOMap" type="com.gzairports.wl.departure.domain.vo.BillVo">
        <id column="id" property="id"/>
        <result column="serial_no" property="serialNo"/>
        <result column="shipper" property="shipper"/>
        <result column="pay_method" property="payMethod"/>
        <result column="pay_time" property="payTime"/>
        <result column="pay_status" property="payStatus"/>
        <result column="create_time" property="createTime"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="pay_voucher" property="payVoucher" typeHandler="com.gzairports.common.core.typehandler.StringListJsonTypeHandler"/>
    </resultMap>
    <select id="selectListByQuery" resultType="com.gzairports.wl.departure.domain.vo.BillVo">
        select id, serial_no, shipper, pay_method, pay_time, pay_status, create_time from wl_dep_bill
        <where>
            dept_id = #{deptId} and is_del = 0
            <if test="startTime != null">
                and create_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and create_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="shipper != null and shipper != ''">
                and shipper like '%${shipper}%'
            </if>
            <if test="payStatus != null and payStatus != ''">
                and pay_status = #{payStatus}
            </if>
        </where>
    </select>
    <select id="getInfo" resultMap="BillVOMap">
        select id, serial_no, shipper, pay_method, pay_time, pay_status, create_time, remark, status, pay_voucher from wl_dep_bill where id = #{id}
    </select>
</mapper>