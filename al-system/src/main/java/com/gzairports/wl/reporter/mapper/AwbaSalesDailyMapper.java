package com.gzairports.wl.reporter.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzairports.common.business.reporter.domain.ReportDataWaybill;
import com.gzairports.wl.reporter.domain.query.AwbaSalesQuery;
import com.gzairports.wl.reporter.domain.vo.ReportTotalVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-20
 */
@Mapper
public interface AwbaSalesDailyMapper {

    /**
     * 动态SQL查询分单销售表
     * @param fieldNames 动态字段
     * @param query 查询条件
     * @return 结果
     */
    Page<ReportDataWaybill> selectFieldByName(@Param("fieldNames") String fieldNames, @Param("query") AwbaSalesQuery query, @Param("page") Page page, @Param("sortData") String sortData);

    /**
     * 查询分页数据
     * @param query 查询条件
     * @return 结果
     */
    List<ReportDataWaybill> selectAwbaPageData(@Param("query") AwbaSalesQuery query);

    /**
     * 总计查询
     * @param query 查询条件
     * @return 结果
     */
    ReportTotalVo selectTotal(AwbaSalesQuery query);
}
