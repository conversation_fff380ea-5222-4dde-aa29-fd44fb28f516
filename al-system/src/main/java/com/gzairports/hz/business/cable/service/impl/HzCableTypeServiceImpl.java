package com.gzairports.hz.business.cable.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.hz.business.cable.domain.HzCableType;
import com.gzairports.hz.business.cable.mapper.HzCableTypeMapper;
import com.gzairports.hz.business.cable.service.IHzCableTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 电报类型Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
@Service
public class HzCableTypeServiceImpl implements IHzCableTypeService
{
    @Autowired
    private HzCableTypeMapper hzCableTypeMapper;

    /**
     * 查询电报类型
     * 
     * @param id 电报类型主键
     * @return 电报类型
     */
    @Override
    public HzCableType selectHzCableTypeById(Long id)
    {
        return hzCableTypeMapper.selectHzCableTypeById(id);
    }

    /**
     * 查询电报类型列表
     * 
     * @param hzCableType 电报类型
     * @return 电报类型
     */
    @Override
    public List<HzCableType> selectHzCableTypeList(HzCableType hzCableType)
    {
        String listString = "舱单控制报,集装器存场报,集装器控制报,特种货物电报,出港拉货报文,自由报文";
        List<HzCableType> hzCableTypes = hzCableTypeMapper.selectHzCableTypeList(hzCableType);
        for (HzCableType type : hzCableTypes) {
            if(listString.contains(type.getCableName())){
                type.setIsAbleDel(0);
            }else{
                type.setIsAbleDel(1);
            }
        }
        return hzCableTypes;
    }

    /**
     * 新增电报类型
     * 
     * @param hzCableType 电报类型
     * @return 结果
     */
    @Override
    public int insertHzCableType(HzCableType hzCableType)
    {
        HzCableType hzCableTypeBySelect1 = hzCableTypeMapper.selectOne(new QueryWrapper<HzCableType>()
                .eq("cable_code", hzCableType.getCableCode())
                .eq("is_del", 0));

        HzCableType hzCableTypeBySelect2 = hzCableTypeMapper.selectOne(new QueryWrapper<HzCableType>()
                .eq("cable_name", hzCableType.getCableName())
                .eq("is_del", 0));

        HzCableType hzCableTypeBySelect3 = hzCableTypeMapper.selectOne(new QueryWrapper<HzCableType>()
                .eq("prefix", hzCableType.getPrefix())
                .eq("is_del", 0));

        if (StringUtils.isNotNull(hzCableTypeBySelect1) ||
                StringUtils.isNotNull(hzCableTypeBySelect2) ||
                StringUtils.isNotNull(hzCableTypeBySelect3)){
            throw new CustomException("电报类型重复");
        }

        hzCableType.setCreateTime(DateUtils.getNowDate());
        hzCableType.setCreateBy(SecurityUtils.getUsername());
        return hzCableTypeMapper.insertHzCableType(hzCableType);
    }

    /**
     * 修改电报类型
     * 
     * @param hzCableType 电报类型
     * @return 结果
     */
    @Override
    public int updateHzCableType(HzCableType hzCableType)
    {
        hzCableType.setUpdateTime(DateUtils.getNowDate());
        hzCableType.setUpdateBy(SecurityUtils.getUsername());
        return hzCableTypeMapper.updateHzCableType(hzCableType);
    }

    /**
     * 删除电报类型信息
     * 
     * @param id 电报类型主键
     * @return 结果
     */
    @Override
    public int deleteHzCableTypeById(Long id)
    {
        HzCableType hzCableType = hzCableTypeMapper.selectHzCableTypeById(id);
        hzCableType.setIsDel(1);
        return hzCableTypeMapper.updateHzCableType(hzCableType);
    }
}
