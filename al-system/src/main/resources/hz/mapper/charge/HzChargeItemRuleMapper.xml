<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.charge.mapper.HzChargeItemRuleMapper">
    <select id="selectRuleIdsByItemId" resultType="java.lang.Long">
        select charge_rule_id from hz_charge_item_rule where item_id = #{id} group by charge_rule_id
    </select>
    <select id="selectColdRuleIdsByItemId" resultType="java.lang.Long">
        select charge_rule_id from hz_charge_item_rule where item_id = #{id} and store is not null group by charge_rule_id
    </select>
    <select id="selectColdRuleByIds" resultType="com.gzairports.common.business.departure.domain.vo.IdsVo">
        select id, item_id, charge_rule_id from hz_charge_item_rule where store is not null and item_id in
        <foreach collection="itemIds" item="itemId" open="(" separator="," close=")">
            #{itemId}
        </foreach>
        group by charge_rule_id
    </select>
</mapper>