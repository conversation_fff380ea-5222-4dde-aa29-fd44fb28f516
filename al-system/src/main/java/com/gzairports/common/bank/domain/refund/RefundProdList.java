package com.gzairports.common.bank.domain.refund;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * @author: lan
 * @Desc: 退款子订单列表
 * @create: 2025-03-18 14:30
 **/

@Data
@XmlRootElement(name = "InFormBody")
@XmlAccessorType(XmlAccessType.FIELD)
public class RefundProdList implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 原子商户流水号 50 Y
     * 参考下单接口 inForm 字段中 inateMeroChannelSerialNo
     */
    @XmlElement(name = "orginatesMeroChannelSerialNo")
    private String orginatesMeroChannelSerialNo;

    /** 商品金额 20 Y
     * 退款金额，以分为单位
     */
    @XmlElement(name = "prodAmt")
    private String prodAmt;

    /** 商品编号 20 Y
     */
    @XmlElement(name = "prodId")
    private String prodId;

    /** 商品名称 64 Y
     */
    @XmlElement(name = "prodName")
    private String prodName;

    /** 商品数量 10 Y
     */
    @XmlElement(name = "prodCount")
    private Integer prodCount;

    /** 二级商户签约编码 32 N
     */
    @XmlElement(name = "agtContractId")
    private String agtContractId;


}
