package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BaseCityCode;
import com.gzairports.common.basedata.domain.query.CityCodeQuery;
import com.gzairports.common.basedata.service.ICityCodeService;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 城市代码Controller
 *
 * <AUTHOR>
 * @date 2024-02-20
 */
@RestController
@RequestMapping("/base/city")
@Api(tags = "城市代码数据接口")
public class CityCodeController extends BaseController {

    @Autowired
    private ICityCodeService cityCodeService;

    /**
     * 查询城市代码列表
     */
//    @PreAuthorize("@ss.hasPermi('base:city:cityCodeList')")
    @GetMapping("/cityCodeList")
    @ApiOperation(value = "查询城市代码列表")
    public TableDataInfo cityCodeList(CityCodeQuery query)
    {
        startPage();
        List<BaseCityCode> list = cityCodeService.selectCityCodeList(query);
        return getDataTable(list);
    }

    /**
     * 导出城市代码
     */
    @PreAuthorize("@ss.hasPermi('base:city:exportCityCode')")
    @Log(title = "导出城市代码", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCityCode")
    @ApiOperation(value = "导出城市代码")
    public void exportCityCode(HttpServletResponse response, CityCodeQuery query)
    {
        List<BaseCityCode> list = cityCodeService.selectCityCodeList(query);
        ExcelUtil<BaseCityCode> util = new ExcelUtil<BaseCityCode>(BaseCityCode.class);
        util.exportExcel(response, list, "城市代码");
    }

    /**
     * 导出城市代码模板
     */
    @PreAuthorize("@ss.hasPermi('base:city:exportCityCode')")
    @Log(title = "导出城市代码", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCityCodeNull")
    @ApiOperation(value = "导出城市代码")
    public void exportCityCodeNull(HttpServletResponse response)
    {
        List<BaseCityCode> list = new ArrayList<>();
        ExcelUtil<BaseCityCode> util = new ExcelUtil<BaseCityCode>(BaseCityCode.class);
        util.exportExcel(response, list, "城市代码模板");
    }

    /**
     * 导入城市代码
     */
    @PreAuthorize("@ss.hasPermi('base:city:importCityCode')")
    @Log(title = "导入城市代码", businessType = BusinessType.IMPORT)
    @PostMapping("/importCityCode")
    @ApiOperation(value = "导入城市代码")
    public AjaxResult importCityCode(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<BaseCityCode> util = new ExcelUtil<BaseCityCode>(BaseCityCode.class);
        List<BaseCityCode> cityCodes = util.importExcel(file.getInputStream());
        String message = cityCodeService.importCityCode(cityCodes, updateSupport);
        return success(message);
    }

    /**
     * 新增城市代码
     */
    @PreAuthorize("@ss.hasPermi('base:city:addCityCode')")
    @Log(title = "新增城市代码", businessType = BusinessType.INSERT)
    @PostMapping("/addCityCode")
    @ApiOperation(value = "新增城市代码")
    public AjaxResult addCityCode(@RequestBody BaseCityCode cityCode)
    {
        return toAjax(cityCodeService.addCityCode(cityCode));
    }

    /**
     * 修改城市代码
     */
    @PreAuthorize("@ss.hasPermi('base:city:editCityCode')")
    @Log(title = "修改城市代码", businessType = BusinessType.UPDATE)
    @PostMapping("/editCityCode")
    @ApiOperation(value = "修改城市代码")
    public AjaxResult editCityCode(@RequestBody BaseCityCode cityCode)
    {
        return toAjax(cityCodeService.editCityCode(cityCode));
    }

    /**
     * 删除城市代码
     */
    @PreAuthorize("@ss.hasPermi('base:city:delCityCode')")
    @Log(title = "删除城市代码", businessType = BusinessType.UPDATE)
    @GetMapping("/delCityCode/{ids}")
    @ApiOperation(value = "删除城市代码")
    public AjaxResult delCityCode(@PathVariable("ids") Long[] ids)
    {
        return toAjax(cityCodeService.delCityCode(ids));
    }
}
