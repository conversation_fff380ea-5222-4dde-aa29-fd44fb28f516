package com.gzairports.common.basedata.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @author: lan
 * @Desc: 历史安检申报单
 * @create: 2024-11-14 16:00
 **/
@Data
public class AllSecurityUrl {
    /**
     * 主键
     * */
    private Long id;
    /**
     * 运单id
     * */
    private Long waybillId;
    /**
     * 运单号
     * */
    private String waybillCode;
    /**
     * 安检申报单地址
     * */
    private String securityUrl;
    /**
     * 时间
     * */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date execDate;
    /**
     * 创建时间
     * */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 创建人
     * */
    private String createBy;
}
