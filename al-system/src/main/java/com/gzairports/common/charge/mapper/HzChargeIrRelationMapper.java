package com.gzairports.common.charge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.charge.domain.HzChargeIrRelation;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface HzChargeIrRelationMapper extends BaseMapper<HzChargeIrRelation> {
    /**
     * 根据收费项目id查询冷藏的关联表
     * @param id 收费项目id
     * @return 冷藏关联表
     */
    List<HzChargeIrRelation> selectColdCharge(Long id);
}
