<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.reporter.mapper.ReportServiceRequestMapper">
    <insert id="saveOrUpdateBatch">
        insert into all_report_service_request
            (
                id, waybill_code, agent, service_item, select_time, actual_time, status, pay_status, pay_money
            )
        values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.waybillCode}, #{item.agent}, #{item.serviceItem}, #{item.selectTime}, #{item.actualTime}, #{item.status},
                #{item.payStatus}, #{item.payMoney}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            waybill_code = VALUES(waybill_code),
            agent = VALUES(agent),
            service_item = VALUES(service_item),
            select_time = VALUES(select_time),
            actual_time = VALUES(actual_time),
            status = VALUES(status),
            pay_status = VALUES(pay_status),
            pay_money = VALUES(pay_money)
    </insert>
</mapper>