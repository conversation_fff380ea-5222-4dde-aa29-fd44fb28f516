package com.gzairports.common.bank.domain.confirmPayment;

import lombok.Data;

import java.io.Serializable;

/**
 * @author: lan
 * @Desc: 确认支付请求字段
 * @create: 2025-03-18 14:40
 **/
@Data
public class ConfirmPaymentRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 一级商户号 String/16 Y
     * 填写签约一级商户号，与平台证书一一对应
     */
    private String platformId;

    /** 一级签约编码 String/20 Y
     * 填写签约一级签约编码
     */
    private String merchantNo;

    /** 二级商户号 String/16 N
     */
    private String agtMerNo;

    /** 二级签约编码 String/20 N
     */
    private String agtContractId;

    /** 二级产品编码 String/20 N
     */
    private String agtProductId;

    /** 原子商户流水号 String/50 N
     * 网关支付非必输；支付分账必输（按子订单确认）
     */
    private String inateMeroChannelSerialNo;

    /** 原交易流水 String/50 Y
     * 原交易单号
     */
    private String originateChannelSerialNo;

    /** 商品编号 String/20 N
     * 事后分账场景使用，实际产生的子订单商品编号
     */
    private String prodId;

    /** 商品名称 String/64 N
     * 事后分账场景使用，实际产生的子订单商品名称
     */
    private String prodName;

    /** 商品数量 String/10 N
     * 事后分账场景使用，实际产生的子订单商品数量
     */
    private Integer prodCount;

    /** 商品金额 String/20 N
     * 事后分账场景下必输，实际产生的子订单商品金额
     */
    private Long prodAmt;

    /** 子商户流水号 String/50 N
     * 事后分账场景下必输，单独的子商户流水号，不同于原子商户流水号
     */
    private String newInateMeroChannelSerialNo;

    /** 摘要 String/50 N
     */
    private String postscript;

    /** 扩展数据 String/60 N
     */
    private String summary;

    /** 扩展数据 String/300 N
     */
    private String extendData;

    /** 子账簿账号 String/16 N
     * 16位纯数字
     */
    private String accNo;

}
