package com.gzairports.web.controller.business.departure;

import com.gzairports.common.pdf.PdfPrintHelper;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.spring.SpringUtils;
import com.gzairports.hz.business.departure.domain.EasyBill;
import com.gzairports.hz.business.departure.domain.query.EasyBillInfoQuery;
import com.gzairports.hz.business.departure.domain.query.EasyBillQuery;
import com.gzairports.hz.business.departure.domain.vo.EasyBillVo;
import com.gzairports.hz.business.departure.service.IEasyBillService;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.oss.domain.SysOss;
import com.gzairports.oss.service.impl.SysOssServiceImpl;
import com.gzairports.wl.departure.domain.vo.MawbVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.util.Date;
import java.util.List;

/**
 * 简易开单Controller
 *
 * <AUTHOR>
 * @date 2024-06-20
 */
@RestController
@RequestMapping("/dep/easyBill")
@Api(tags = "简易开单")
public class EasyBillController extends BaseController {

    @Autowired
    private IEasyBillService billService;

    /**
     * 查询简易开单列表
     */
//    @PreAuthorize("@ss.hasPermi('dep:easyBill:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询简易开单列表")
    public TableDataInfo list(EasyBillQuery query)
    {
        startPage();
        List<EasyBillVo> list = billService.selectEasyBillList(query);
        return getDataTable(list);
    }

    /**
     * 运单校验
     */
    @PreAuthorize("@ss.hasPermi('dep:mawb:check')")
    @GetMapping("/check/{waybillCode}")
    @ApiOperation(value = "运单校验")
    public AjaxResult check(@PathVariable("waybillCode") String waybillCode)
    {
        return AjaxResult.success(billService.check(waybillCode));
    }

    /**
     * 新增简易开单
     */
    @PreAuthorize("@ss.hasPermi('dep:easyBill:add')")
    @PostMapping("/add")
    @ApiOperation(value = "新增")
    public AjaxResult add(@RequestBody EasyBill bill) throws Exception {
        String substring = bill.getWaybillCode().substring(4);
        String waybillCodeAbb = substring.substring(0, 3) + "-" + substring.substring(3);
        bill.setWaybillCodeAbb(waybillCodeAbb);
        bill.setWriter(SecurityUtils.getUsername());
        bill.setWriteTime(new Date());
        bill.setWriteLocation("贵阳");
        if (bill.getSpecialCargoCode1() != null){
            bill.setTzhw("YES");
        }else {
            bill.setPthw("YES");
        }
        String securityUrl = getPdfUrl(bill, "template/security.pdf");
        bill.setSecurityUrl(securityUrl);
        String mawbUrl = getPdfUrl(bill, "template/mawb.pdf");
        bill.setPdfUrl(mawbUrl);
        return toAjax(billService.insertEasyBill(bill));
    }

    /**
     * 修改简易开单
     */
    @PreAuthorize("@ss.hasPermi('dep:easyBill:edit')")
    @PostMapping("/edit")
    @ApiOperation(value = "修改简易开单")
    public AjaxResult edit(@RequestBody EasyBill bill){
        return toAjax(billService.editEasyBill(bill));
    }

    /**
     * 查看详情
     */
    @PreAuthorize("@ss.hasPermi('dep:easyBill:getInfo')")
    @PostMapping("/getInfo")
    @ApiOperation(value = "查看详情")
    public AjaxResult getInfo(@RequestBody EasyBillInfoQuery query){
        return AjaxResult.success(billService.getInfo(query));
    }

    /**
     * 删除
     */
    @PreAuthorize("@ss.hasPermi('dep:easyBill:remove')")
    @PostMapping("/remove")
    @ApiOperation(value = "删除")
    public AjaxResult remove(@RequestBody EasyBillInfoQuery query){
        return toAjax(billService.delEasyBill(query));
    }

    /**
     * 运单作废
     */
    @PreAuthorize("@ss.hasPermi('dep:mawb:invalid')")
    @GetMapping("/invalid/{waybillCode}")
    @ApiOperation(value = "运单作废")
    public AjaxResult invalid(@PathVariable("waybillCode") String waybillCode)
    {
        return toAjax(billService.invalid(waybillCode));
    }

    private String getPdfUrl(EasyBill bill, String s) throws Exception {
        ClassPathResource securityUrl = new ClassPathResource(s);
        if (securityUrl.exists()) {
            String path = securityUrl.getPath();
            byte[] pdfDataFromTemplate = PdfPrintHelper.getPdfDataFromTemplate(bill, path);
            // 使用StandardMultipartHttpServletRequest来创建MultipartFile
            DiskFileItem fileItem = new DiskFileItem(bill.getWaybillCode() + ".pdf", "application/pdf", true, bill.getWaybillCode() + ".pdf", -1, null);

            // 将 InputStream 写入 DiskFileItem
            fileItem.getOutputStream().write(pdfDataFromTemplate);
            fileItem.getOutputStream().close();

            // 使用 FileItem 构建 MultipartFile
            MultipartFile multipartFile = new CommonsMultipartFile(fileItem);
            SysOss upload = SpringUtils.getBean(SysOssServiceImpl.class).upload(multipartFile);
            return upload.getUrl();
        }
        return null;
    }


}
