package com.gzairports.common.charge.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 收费项目计费规则表
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@Data
@TableName("hz_charge_item_rule")
public class HzChargeItemRule {

    /** 主键id */
    private Long id;

    /** 收费项目与规则id */
    private Long irId;

    /** 时段 */
    private String timeSlot;

    /** 开始时段 */
    @TableField(exist = false)
    private String timeSlot1;

    /** 结束时段 */
    @TableField(exist = false)
    private String timeSlot2;

    /** 时间点 */
    private String pointTime;

    /** 时间点之后费率 */
    private BigDecimal afterRate;

    /** 时间点之前费率 */
    private BigDecimal beforeRate;

    /** 免费保管时间 */
    private String freeTime;

    /** 每天费率 */
    private BigDecimal rate;

    /** 单价 */
    private BigDecimal unitPrice;

    /** 小件单价 */
    private BigDecimal smallPrice;

    /** 大件单价 */
    private BigDecimal largePrice;

    /** 超大件单价 */
    private BigDecimal extraLargePrice;

    /** 开始区间 */
    private String startRange;

    /** 结束区间 */
    private String endRange;

    /** 最低收费 */
    private BigDecimal minimum;

    /** 最高收费 */
    private BigDecimal maximum;

    /** 第一小时 */
    private BigDecimal oneHour;

    /** 此后 */
    private BigDecimal after;

    /** 此前 */
    private BigDecimal beforeMin;

    /** 此后 */
    private BigDecimal afterMin;

    /** 冷藏库类型 */
    private String store;

    /** 是否删除 0 否 1 是 */
    private Integer isDel;
}
