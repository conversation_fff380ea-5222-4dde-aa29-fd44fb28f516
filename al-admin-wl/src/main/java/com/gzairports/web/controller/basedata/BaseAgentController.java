package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BaseAgent;
import com.gzairports.common.basedata.service.IBaseAgentService;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 代理人配置Controller
 *
 * <AUTHOR>
 * @date 2024-06-17
 */
@RestController
@RequestMapping("/base/agent")
@Api(tags = "代理人配置")
public class BaseAgentController extends BaseController
{
    @Autowired
    private IBaseAgentService baseAgentService;

    /**
     * 查询代理人配置列表
     */
//    @PreAuthorize("@ss.hasPermi('base:agent:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询代理人配置列表")
    public TableDataInfo list(BaseAgent baseAgent)
    {
        startPage();
        List<BaseAgent> list = baseAgentService.selectBaseAgentList(baseAgent);
        return getDataTable(list);
    }

    /**
     * 查询可提代理人详细列表
     */
//    @PreAuthorize("@ss.hasPermi('base:agent:list')")
    @GetMapping("/list/by/deptIds")
    @ApiOperation(value = "查询可提代理人详细列表")
    public TableDataInfo listAgentDeptIds(BaseAgent baseAgent)
    {
        List<BaseAgent> list = baseAgentService.listAgentDeptIds(baseAgent);
        return getDataTable(list);
    }

}
