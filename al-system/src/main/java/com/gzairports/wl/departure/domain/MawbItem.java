package com.gzairports.wl.departure.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 主单费用项目表
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
@Data
@TableName("all_mawb_item")
public class MawbItem {

    /** 主键id */
    private Long id;

    /** 分单id */
    private Long waybillId;

    /** 收费项目id */
    private Long chargeItemId;

    /** 分单运单号 */
    private String waybillCode;

    /** 费用类型 */
    private String costType;

    /** 计费金额 */
    private BigDecimal charging;

    /** 所属单位 */
    private Long deptId;

    /** 是否删除 0 否 1 是 */
    private Integer isDel;

    /** 费率 */
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal rate;

    /** 旧费率 */
    @JsonSerialize(using = ToStringSerializer.class)
    @TableField(exist = false)
    private BigDecimal oldRate;

    /** 旧计费金额 */
    @JsonSerialize(using = ToStringSerializer.class)
    @TableField(exist = false)
    private BigDecimal oldCharging;

    /** 费用总计 */
    @TableField(exist = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal costSum;

    /** 计费重量 */
    @TableField(exist = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal chargeWeight;

    /** 重量 */
    @TableField(exist = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal weight;

    /** 体积 */
    @TableField(exist = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal volume;

    /** 件数 */
    @TableField(exist = false)
    private Integer quantity;

    /** 费用类型 0 收费项目 1 航司运价 */
    private Integer rateType;
}
