<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.departure.mapper.CostDetailMapper">
    <delete id="delWaitSettleTask">
        delete from wl_dep_cost_detail
        where waybill_code = #{waybillCode}
              and dept_id = #{deptId}
              and service_type = 0
              and is_del = 0
              and type = 1 and is_settle = 0
    </delete>
    <select id="selectPayOrSettleList" resultType="com.gzairports.common.business.departure.domain.CostDetail">
        select wdcd.*, hci.charge_name, hci.charge_abb, hcr.rule_name, hcr.class_name
        from wl_dep_cost_detail wdcd
            left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
            left join hz_charge_items hci on hci.id = hcir.item_id
            left join hz_charge_rule hcr  on hcr.id = hcir.rule_id
        where wdcd.waybill_code = #{code} and wdcd.is_del = 0
        <if test="deptId != null">
            and wdcd.dept_id = #{deptId}
        </if>
        <if test="i == 0 or i == 1">
            and wdcd.type = #{i}
        </if>
        <if test="isSettle == 0 or isSettle == 1">
            and wdcd.is_settle = #{isSettle}
        </if>
    </select>
    <select id="selectPayOrSettleByWaybills" resultType="com.gzairports.common.business.departure.domain.CostDetail">
        select wdcd.*, hci.charge_name, hci.charge_abb, hcr.rule_name, hcr.class_name
        from wl_dep_cost_detail wdcd
        left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
        left join hz_charge_items hci on hci.id = hcir.item_id
        left join hz_charge_rule hcr on hcr.id = hcir.rule_id
        where wdcd.waybill_code in
        <foreach collection="codes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        and wdcd.is_del = 0
        and wdcd.dept_id = #{deptId}
        and wdcd.is_settle = #{isSettle}
        and wdcd.type = #{i}
    </select>

    <select id="selectPayOrSettleListBySettleTime"
            resultType="com.gzairports.common.business.departure.domain.CostDetail">
        select wdcd.flight_id, wdcd.create_time, wdcd.total_charge,wdcd.quantity,wdcd.settle_dep_quantity,wdcd.settle_dep_weight,
               hci.charge_name, hci.charge_abb, hcr.rule_name, hcr.class_name
        from wl_dep_cost_detail wdcd
        left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
        left join hz_charge_items hci on hci.id = hcir.item_id
        left join hz_charge_rule hcr  on hcr.id = hcir.rule_id
        left join all_flight_info afi on afi.flight_id = wdcd.flight_id
        where wdcd.waybill_code = #{code} and wdcd.is_del = 0
          and wdcd.flight_id != 4 and wdcd.flight_id != 3
        <if test="deptId != null">
            and wdcd.dept_id = #{deptId}
        </if>
        <if test="i == 0 or i == 1">
            and wdcd.type = #{i}
        </if>
        <if test="isSettle == 0 or isSettle == 1">
            and wdcd.is_settle = #{isSettle}
        </if>
        <if test="query.startTimeSettle != null">
            and wdcd.create_time <![CDATA[>=]]> #{query.startTimeSettle}
        </if>
        <if test="query.endTimeSettle != null">
            and wdcd.create_time <![CDATA[<=]]> #{query.endTimeSettle}
        </if>
        <if test="query.flightLoadStartTime != null">
            and afi.start_scheme_takeoff_time <![CDATA[>=]]> #{query.flightLoadStartTime}
        </if>
        <if test="query.flightLoadEndTime != null">
            and afi.start_scheme_takeoff_time <![CDATA[<=]]> #{query.flightLoadEndTime}
        </if>
    </select>

    <select id="selectListByCode" resultType="com.gzairports.common.business.departure.domain.CostDetail">
        select  wdcd.id,wdcd.waybill_code,wdcd.charge_item_id,wdcd.flight_id,wdcd.is_settle,wdcd.type,wdcd.is_settle,
                wdcd.total_charge,wdcd.ir_id,wdcd.charge_item_id,wdcd.create_time,
                wdcd.dept_id,wdcd.is_del,wdcd.quantity,wdcd.settle_dep_quantity,wdcd.settle_dep_weight,
                hcr.rule_name, hci.charge_name, hcr.class_name
        from wl_dep_cost_detail wdcd
                 left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
                 left join hz_charge_items hci on hci.id = hcir.item_id
                 left join hz_charge_rule hcr  on hcr.id = hcir.rule_id
        where wdcd.type = #{type} and wdcd.waybill_code = #{waybillCode} and wdcd.dept_id = #{deptId} and wdcd.is_del = 0
        <if test="isSettle == 0 or isSettle == 1">
            and wdcd.is_settle = #{isSettle}
        </if>
    </select>
    <select id="selectCostInfo" resultType="com.gzairports.common.business.departure.domain.CostDetail">
        select wdcd.id, wdcd.total_charge, wdcd.ir_id, hci.charge_name, hcr.rule_name, hcr.class_name, wdcd.remark, wdcd.unit,
               wdcd.small_item, wdcd.large_item, wdcd.super_large_item, wdcd.start_time, wdcd.end_time, wdcd.days_in_storage, wdcd.edit_charge,
               wdcd.cold_store, wdcd.store_start_time, wdcd.store_end_time
        from wl_dep_cost_detail wdcd
                 left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
                 left join hz_charge_items hci on hci.id = hcir.item_id
                 left join hz_charge_rule hcr on hcr.id = hcir.rule_id
        where wdcd.id = #{id}
    </select>
    <select id="selectItemVo" resultType="com.gzairports.common.business.arrival.domain.vo.ArrItemVo">
        select wdcd.total_charge, wdcd.flight_id, hci.charge_abb, wdcd.edit_charge
        from wl_dep_cost_detail wdcd
                 left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
                 left join hz_charge_items hci on hci.id = hcir.item_id
        where wdcd.waybill_code = #{waybillCode} and wdcd.type = 0
    </select>
    <select id="selectItemVoBySettleTime" resultType="com.gzairports.common.business.arrival.domain.vo.ArrItemVo">
        select wdcd.total_charge, wdcd.flight_id, hci.charge_abb, wdcd.edit_charge
        from wl_dep_cost_detail wdcd
                 left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
                 left join hz_charge_items hci on hci.id = hcir.item_id
                 left join all_flight_info afi on afi.flight_id = wdcd.flight_id
                 left join all_air_waybill aaw on aaw.waybill_code = wdcd.waybill_code
        where wdcd.waybill_code = #{waybillCode} and wdcd.type = 1 and aaw.type = 'DEP'
        <if test="query.startTimeSettle != null">
            and wdcd.create_time <![CDATA[>=]]> #{query.startTimeSettle}
        </if>
        <if test="query.endTimeSettle != null">
            and wdcd.create_time <![CDATA[<=]]> #{query.endTimeSettle}
        </if>
        <if test="query.flightLoadStartTime != null">
            and afi.start_scheme_takeoff_time <![CDATA[>=]]> #{query.flightLoadStartTime}
        </if>
        <if test="query.flightLoadEndTime != null">
            and afi.start_scheme_takeoff_time <![CDATA[<=]]> #{query.flightLoadEndTime}
        </if>
        <if test="query.writeStartTime != null">
            and aaw.write_time <![CDATA[>=]]> #{query.writeStartTime}
        </if>
        <if test="query.writeEndTime != null">
            and aaw.write_time <![CDATA[<=]]> #{query.writeEndTime}
        </if>
    </select>
    <select id="selectItemList" resultType="com.gzairports.common.business.departure.domain.CostDetail">
        SELECT *
        FROM wl_dep_cost_detail AS t1
        WHERE t1.type = 0 AND t1.waybill_code = #{waybillCode} AND t1.dept_id = #{deptId}
          AND t1.service_type = 1 AND t1.service_request_id = #{serviceRequestId} AND t1.is_del = 0
          AND NOT EXISTS (
                SELECT 1
                FROM wl_dep_cost_detail AS t2
                WHERE t2.type = 1
                  AND t2.total_charge = t1.total_charge and t2.service_request_id = t1.service_request_id
            );
    </select>
    <select id="depAutoSettleTask" resultType="com.gzairports.common.business.departure.domain.CostDetail">
        SELECT wdcd.*, hci.charge_abb, hci.operation_type
        FROM wl_dep_cost_detail wdcd
             left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
             left join hz_charge_items hci on hci.id = hcir.item_id
        WHERE wdcd.waybill_code = #{waybillCode}
          AND wdcd.dept_id = #{deptId}
          AND wdcd.service_type = 0
          AND wdcd.is_del = 0
          AND (
                (wdcd.type = 0) OR
                (wdcd.type = 1 AND wdcd.is_settle = 0)
            )
    </select>
    <select id="depIsSettleData" resultType="java.math.BigDecimal">
        select sum(total_charge)
        from wl_dep_cost_detail
        where type = 1 and is_settle = 1 and waybill_code = #{waybillCode} and dept_id = #{deptId} and service_type = 0 and is_del = 0
    </select>
    <select id="selectFlightInfo" resultType="java.lang.Long">
        select flight_id from wl_dep_cost_detail
        where type = 1 and waybill_code = #{waybillCode} and dept_id = #{deptId} and service_type = 0 and is_del = 0  ORDER BY create_time desc limit 1
    </select>
    <select id="selectSettleByFlightId" resultType="com.gzairports.common.business.departure.domain.CostDetail">
        select *
        from wl_dep_cost_detail
        where type = 1 and is_settle = 1 and waybill_code = #{waybillCode} and dept_id = #{deptId} and service_type = 0 and is_del = 0 and flight_id = #{flightId}
    </select>
    <select id="selectFlights" resultType="java.lang.Long">
        select wdcd.flight_id from wl_dep_cost_detail wdcd
            left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
            left join hz_charge_items hci on hci.id = hcir.item_id
        where wdcd.type = 1 and wdcd.waybill_code = #{waybillCode}
          and wdcd.dept_id = #{deptId} and wdcd.service_type = 0 and wdcd.is_del = 0 and hci.charge_abb = '处置费'
    </select>
    <select id="selectIsSettle" resultType="com.gzairports.common.business.departure.domain.CostDetail">
        select wdcd.*
        from wl_dep_cost_detail wdcd
             left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
             left join hz_charge_items hci on hci.id = hcir.item_id
        where wdcd.type = 1 and wdcd.is_settle = 1
          and wdcd.waybill_code = #{waybillCode}
          and wdcd.dept_id = #{deptId} and wdcd.service_type = 0
          and wdcd.is_del = 0 and hci.charge_abb = '处置费'
    </select>
    <select id="selectSettleCount" resultType="com.gzairports.common.business.departure.domain.CostDetail">
        select wdcd.*
        from wl_dep_cost_detail wdcd
             left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
             left join hz_charge_items hci on hci.id = hcir.item_id
        where wdcd.type = 1 and wdcd.waybill_code = #{waybillCode}
          and wdcd.dept_id = #{deptId} and wdcd.service_type = 0 and wdcd.is_del = 0 and hci.charge_abb = '处置费'
    </select>
    <select id="selectSettleInfo" resultType="com.gzairports.common.business.departure.domain.CostDetail">
        select wdcd.* from wl_dep_cost_detail wdcd
            left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
            left join hz_charge_items hci on hci.id = hcir.item_id
        where wdcd.waybill_code = #{waybillCode} and wdcd.dept_id = #{deptId} and wdcd.flight_id = #{flightId} and hci.charge_abb = '处置费' limit 1
    </select>
    <select id="selectCarryList" resultType="com.gzairports.common.business.departure.domain.CostDetail">
        select wdcd.* from wl_dep_cost_detail wdcd
                               left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
                               left join hz_charge_items hci on hci.id = hcir.item_id
        where wdcd.waybill_code = #{waybillCode} and wdcd.dept_id = #{deptId} and wdcd.flight_id = #{flightId} and type = 1 and hci.charge_abb = '搬运费'
    </select>
    <select id="selectReportList" resultType="com.gzairports.common.business.reporter.domain.ReportDepPayCost">
        select aaw.id as reportId, wdcd.waybill_code, hci.charge_name as rateName, wdcd.rate, wdcd.quantity as payQuantity, wdcd.settle_dep_quantity, wdcd.total_charge, wdcd.is_settle, wdcd.dept_id,  wdcd.service_type,
               wdcd.service_request_id, wdcd.create_time,  wdcd.settle_dep_weight
        from wl_dep_cost_detail wdcd
                 left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
                 left join hz_charge_items hci on hci.id = hcir.item_id
                 left join all_air_waybill aaw on aaw.waybill_code = wdcd.waybill_code
        where wdcd.type = 0 and aaw.type = 'DEP' and (wdcd.waybill_code, wdcd.dept_id) in
        <foreach collection="keys" item="key" open="(" separator="," close=")">
            (#{key.waybillCode}, #{key.deptId})
        </foreach>
    </select>
    <select id="selectSettleList"
            resultType="com.gzairports.common.business.reporter.domain.ReportDepSettleCost">
        select aaw.id as reportId, wdcd.waybill_code, hci.charge_name as rateName,  wdcd.rate, wdcd.quantity as settleQuantity, wdcd.settle_dep_quantity, wdcd.total_charge, wdcd.is_settle, wdcd.dept_id, wdcd.service_type,
               wdcd.service_request_id, wdcd.create_time, wdcd.settle_dep_weight, afi.exec_date as flightDate, CONCAT(afi.air_ways,afi.flight_no) as flightNo
        from wl_dep_cost_detail wdcd
                 left join all_flight_info afi on afi.flight_id = wdcd.flight_id
                 left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
                 left join hz_charge_items hci on hci.id = hcir.item_id
                 left join all_air_waybill aaw on aaw.waybill_code = wdcd.waybill_code
        where wdcd.type = 1 and aaw.type = 'DEP' and (wdcd.waybill_code, wdcd.dept_id) in
        <foreach collection="keys" item="key" open="(" separator="," close=")">
            (#{key.waybillCode}, #{key.deptId})
        </foreach>
    </select>

    <select id="selectExportItemVo" resultType="com.gzairports.hz.business.departure.domain.vo.ChargeBilExportVO">
        select aaw.waybill_code,aaw.quantity,aaw.charge_weight,hci.charge_abb,wdcd.total_charge,aaw.settle_time,aaw.status,
        wdcd.id,wdcd.type,aaw.dept_id
        from wl_dep_cost_detail wdcd
        left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
        left join hz_charge_items hci on hci.id = hcir.item_id
        left join all_air_waybill aaw on aaw.waybill_code = wdcd.waybill_code
        left join base_agent ba on ba.dept_id = aaw.dept_id
        left join (
        select hflw.waybill_id as waybill_id, hfl1.flight_id as flight_id
        from hz_flight_load_waybill hflw
        left join hz_flight_load hfl1 on hfl1.id = hflw.flight_load_id

        union all

        select hfluw.waybill_id as waybill_id, hfl2.flight_id as flight_id
        from hz_flight_load_uld_waybill hfluw
        left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
        left join hz_flight_load hfl2 on hfl2.id = hflu.flight_load_id
        ) combined_flights on aaw.id = combined_flights.waybill_id
        left join all_flight_info afi on afi.flight_id = combined_flights.flight_id
        where aaw.is_del = 0 and aaw.type = 'DEP'
          and wdcd.create_time between #{query.flightStartTime} and #{query.flightEndTime}
        and wdcd.is_del = 0 and wdcd.flight_id > 4
        <if test="query.agentCode != null">
            and ba.agent in
            <foreach collection="query.agentCode" item="agentCode" open="(" separator="," close=")">
                #{agentCode}
            </foreach>
        </if>
        <if test="type != null">
            and wdcd.type = #{type}
        </if>
        <if test="isSettle != null">
            and wdcd.is_settle = #{isSettle}
        </if>
        <if test="query.flightStartTime != null">
            and afi.start_scheme_takeoff_time <![CDATA[>=]]> #{query.flightStartTime}
        </if>
        <if test="query.flightEndTime != null">
            and afi.start_scheme_takeoff_time <![CDATA[<=]]> #{query.flightEndTime}
        </if>
        <if test="query.startTimeSettle != null">
            and wdcd.create_time <![CDATA[>=]]> #{query.startTimeSettle}
        </if>
        <if test="query.endTimeSettle != null">
            and wdcd.create_time <![CDATA[<=]]> #{query.endTimeSettle}
        </if>
        <if test="query.writeStartTime != null">
            and aaw.write_time <![CDATA[>=]]> #{query.writeStartTime}
        </if>
        <if test="query.writeEndTime != null">
            and aaw.write_time <![CDATA[<=]]> #{query.writeEndTime}
        </if>
        group by aaw.waybill_code, aaw.quantity, aaw.charge_weight, hci.charge_abb,
        wdcd.total_charge, aaw.settle_time,aaw.status,wdcd.id,wdcd.type,aaw.agent_company,aaw.dept_id
    </select>

    <select id="selectFutureFlightItemVo"
            resultType="com.gzairports.hz.business.departure.domain.vo.ChargeBilExportVO">
        select aaw.waybill_code,aaw.quantity,aaw.charge_weight,hci.charge_abb,wdcd.total_charge,aaw.settle_time,aaw.status,
        wdcd.id,wdcd.type,aaw.dept_id
        from wl_dep_cost_detail wdcd
        left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
        left join hz_charge_items hci on hci.id = hcir.item_id
        left join all_air_waybill aaw on aaw.waybill_code = wdcd.waybill_code
        left join base_agent ba on ba.dept_id = aaw.dept_id
        left join (
        select hflw.waybill_id as waybill_id, hfl1.flight_id as flight_id
        from hz_flight_load_waybill hflw
        left join hz_flight_load hfl1 on hfl1.id = hflw.flight_load_id

        union all

        select hfluw.waybill_id as waybill_id, hfl2.flight_id as flight_id
        from hz_flight_load_uld_waybill hfluw
        left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
        left join hz_flight_load hfl2 on hfl2.id = hflu.flight_load_id
        ) combined_flights on aaw.id = combined_flights.waybill_id
        left join all_flight_info afi on afi.flight_id = combined_flights.flight_id
        where aaw.is_del = 0 and aaw.type = 'DEP'
        and wdcd.is_del = 0
        and ba.agent = #{query.agent}
        <if test="type != null">
            and wdcd.type = #{type}
        </if>
        <if test="isSettle != null">
            and wdcd.is_settle = #{isSettle}
        </if>
        <if test="query.flightStartTime != null">
            and aaw.pay_time <![CDATA[>=]]> #{query.flightStartTime}
        </if>
        <if test="query.flightEndTime != null">
            and aaw.pay_time <![CDATA[<=]]> #{query.flightEndTime}
            and (afi.start_scheme_takeoff_time <![CDATA[>=]]> #{query.flightEndTime}
                     or afi.start_scheme_takeoff_time <![CDATA[<=]]> #{query.flightStartTime}
                     or afi.start_scheme_takeoff_time is null)
        </if>
        group by aaw.waybill_code, aaw.quantity, aaw.charge_weight, hci.charge_abb,
        wdcd.total_charge, aaw.settle_time,aaw.status,wdcd.id,wdcd.type,aaw.agent_company,aaw.dept_id
    </select>


    <select id="selectChargeItems"
            resultType="com.gzairports.hz.business.departure.domain.vo.ChargeBilExportVO">
        select  wdcd.total_charge, hci.charge_abb
        from wl_dep_cost_detail wdcd
                 left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
                 left join hz_charge_items hci on hci.id = hcir.item_id
        where wdcd.type = 1
          and wdcd.waybill_code in ('AWBA00125031701')

    </select>
    <select id="selectSettleByTime" resultType="com.gzairports.common.business.departure.domain.CostDetail">
        select wdcd.total_charge, wdcd.flight_id, wdcd.edit_charge
        from wl_dep_cost_detail wdcd
        left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
        left join hz_charge_items hci on hci.id = hcir.item_id
        left join all_flight_info afi on afi.flight_id = wdcd.flight_id
        where wdcd.waybill_code = #{waybillCode}
          and wdcd.type = 1
          and wdcd.is_settle = 1
          and wdcd.is_del = 0
        <if test="query.startTimeSettle != null">
            and wdcd.create_time <![CDATA[>=]]> #{query.startTimeSettle}
        </if>
        <if test="query.endTimeSettle != null">
            and wdcd.create_time <![CDATA[<=]]> #{query.endTimeSettle}
        </if>
        <if test="query.flightLoadStartTime != null">
            and afi.start_scheme_takeoff_time <![CDATA[>=]]> #{query.flightLoadStartTime}
        </if>
        <if test="query.flightLoadEndTime != null">
            and afi.start_scheme_takeoff_time <![CDATA[<=]]> #{query.flightLoadEndTime}
        </if>
    </select>
    <select id="selectNotSettleData"
            resultType="com.gzairports.hz.business.departure.domain.vo.ChargeBilExportNotSettleVO">
        select aaw.waybill_code, aaw.quantity, aaw.charge_weight, hci.charge_abb, wdcd.total_charge, aaw.settle_time,
        aaw.status,wdcd.id,wdcd.type
        from wl_dep_cost_detail wdcd
        left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
        left join hz_charge_items hci on hci.id = hcir.item_id
        left join all_air_waybill aaw on aaw.waybill_code = wdcd.waybill_code
        left join base_agent ba on ba.dept_id = aaw.dept_id

        where aaw.is_del = 0 and aaw.type = 'DEP'
        and wdcd.is_del = 0 and aaw.status != 'INVALID'
        and ba.agent = #{query.agent}
        <if test="type != null">
            and wdcd.type = #{type}
        </if>
        <if test="isSettle != null">
            and wdcd.is_settle = #{isSettle}
        </if>
        <if test="query != null">
            <if test="query.writeStartTime != null">
                and aaw.pay_time <![CDATA[>=]]> #{query.writeStartTime}
            </if>
            <if test="query.writeEndTime != null">
                and aaw.pay_time <![CDATA[<=]]> #{query.writeEndTime}
            </if>
        </if>
    </select>
<!--                and aaw.write_time <![CDATA[>=]]> #{query.writeStartTime}-->
<!--                and aaw.write_time <![CDATA[<=]]> #{query.writeEndTime}-->
    <select id="selectFlightNotSettleData"
            resultType="com.gzairports.hz.business.departure.domain.vo.ChargeBilExportNotSettleVO">
        select aaw.waybill_code, aaw.quantity, aaw.charge_weight, hci.charge_abb, wdcd.total_charge, aaw.settle_time,
        aaw.status,wdcd.id,wdcd.type
        from wl_dep_cost_detail wdcd
        left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
        left join hz_charge_items hci on hci.id = hcir.item_id
        left join all_air_waybill aaw on aaw.waybill_code = wdcd.waybill_code
        left join base_agent ba on ba.dept_id = aaw.dept_id
        left join (
        select hflw.waybill_id as waybill_id, hfl1.flight_id as flight_id
        from hz_flight_load_waybill hflw
        left join hz_flight_load hfl1 on hfl1.id = hflw.flight_load_id

        union all

        select hfluw.waybill_id as waybill_id, hfl2.flight_id as flight_id
        from hz_flight_load_uld_waybill hfluw
        left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
        left join hz_flight_load hfl2 on hfl2.id = hflu.flight_load_id
        ) combined_flights on aaw.id = combined_flights.waybill_id
        left join all_flight_info afi on afi.flight_id = combined_flights.flight_id

        where aaw.is_del = 0 and aaw.type = 'DEP'
        and wdcd.is_del = 0 and aaw.status != 'INVALID'
        and ba.agent = #{query.agent}
        <if test="type != null">
            and wdcd.type = #{type}
        </if>
        <if test="isSettle != null">
            and wdcd.is_settle = #{isSettle}
        </if>
        <if test="query != null">
              <if test="query.writeStartTime != null">
                  and aaw.pay_time <![CDATA[>=]]> #{query.writeStartTime}
              </if>
              <if test="query.writeEndTime != null">
                  and aaw.pay_time <![CDATA[<=]]> #{query.writeEndTime}
                  and afi.start_scheme_takeoff_time <![CDATA[>=]]> #{query.writeEndTime}
              </if>
        </if>
        group by aaw.waybill_code, aaw.quantity, aaw.charge_weight, hci.charge_abb,wdcd.total_charge, aaw.settle_time,
                 aaw.status,wdcd.id,wdcd.type
    </select>
<!--    and aaw.write_time <![CDATA[>=]]> #{query.writeStartTime}-->
<!--    and aaw.write_time <![CDATA[<=]]> #{query.writeEndTime}-->
    <select id="selectPullAllNotSettle" resultType="com.gzairports.hz.business.departure.domain.HzDepPullDown">
        select hdpd.waybill_code,hdpd.quantity,hdpd.weight,hdpd.oper_time,
               aaw.quantity as waybillQuantity,aaw.weight as waybillWeight
        from all_wrong aw
        left join hz_dep_pull_down hdpd on aw.pull_id = hdpd.id
        left join all_air_waybill aaw on aaw.waybill_code = hdpd.waybill_code
        left join base_agent ba on ba.dept_id = aaw.dept_id
        where aw.pro_method = 0 and hdpd.is_load = 0
        and ba.agent = #{agent} and aaw.status != 'INVALID'
        <if test="writeStartTime != null">
            and hdpd.oper_time <![CDATA[>=]]> #{writeStartTime}
        </if>
        <if test="writeEndTime != null">
            and hdpd.oper_time <![CDATA[<=]]> #{writeEndTime}
        </if>
    </select>
    <select id="selectPullAllNotSettleAgent"
            resultType="com.gzairports.hz.business.departure.domain.HzDepPullDown">
        select hdpd.waybill_code,hdpd.quantity,hdpd.weight,hdpd.oper_time,
        aaw.quantity as waybillQuantity,aaw.weight as waybillWeight
        from all_wrong aw
        left join hz_dep_pull_down hdpd on aw.pull_id = hdpd.id
        left join all_air_waybill aaw on aaw.waybill_code = hdpd.waybill_code
        left join base_agent ba on ba.dept_id = aaw.dept_id
        where aw.pro_method = 0 and hdpd.is_load = 0
        and aaw.status != 'INVALID'
        <if test="agentCode != null">
            and ba.agent in
            <foreach collection="agentCode" item="agentCode" open="(" separator="," close=")">
                #{agentCode}
            </foreach>
        </if>
        <if test="writeStartTime != null">
            and hdpd.oper_time <![CDATA[>=]]> #{writeStartTime}
        </if>
        <if test="writeEndTime != null">
            and hdpd.oper_time <![CDATA[<=]]> #{writeEndTime}
        </if>
    </select>
    <select id="selectPullAllNotSettleData"
            resultType="com.gzairports.hz.business.departure.domain.vo.ChargeBilExportNotSettleVO">
        select aaw.waybill_code, aaw.quantity, aaw.charge_weight, hci.charge_abb, wdcd.total_charge, aaw.settle_time,
               aaw.status,wdcd.id,wdcd.type
        from wl_dep_cost_detail wdcd
                 left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
                 left join hz_charge_items hci on hci.id = hcir.item_id
                 left join all_air_waybill aaw on aaw.waybill_code = wdcd.waybill_code
                 left join base_agent ba on ba.dept_id = aaw.dept_id
        where aaw.is_del = 0 and aaw.type = 'DEP'
          and wdcd.is_del = 0 and wdcd.type = 1 and aaw.waybill_code in
        <foreach collection="waybillCodes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>
    <select id="selectExportItemVoAgent"
            resultType="com.gzairports.hz.business.departure.domain.vo.ChargeBillExportVoAgent">
        select aaw.waybill_code,aaw.quantity,aaw.charge_weight,hci.charge_abb,wdcd.total_charge,aaw.settle_time,aaw.status,
        wdcd.id,wdcd.type,aaw.dept_id, ba.agent
        from wl_dep_cost_detail wdcd
        left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
        left join hz_charge_items hci on hci.id = hcir.item_id
        left join all_air_waybill aaw on aaw.waybill_code = wdcd.waybill_code
        left join base_agent ba on ba.dept_id = aaw.dept_id
        left join (
        select hflw.waybill_id as waybill_id, hfl1.flight_id as flight_id
        from hz_flight_load_waybill hflw
        left join hz_flight_load hfl1 on hfl1.id = hflw.flight_load_id

        union all

        select hfluw.waybill_id as waybill_id, hfl2.flight_id as flight_id
        from hz_flight_load_uld_waybill hfluw
        left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
        left join hz_flight_load hfl2 on hfl2.id = hflu.flight_load_id
        ) combined_flights on aaw.id = combined_flights.waybill_id
        left join all_flight_info afi on afi.flight_id = combined_flights.flight_id
        where aaw.is_del = 0 and aaw.type = 'DEP'
        and wdcd.is_del = 0 and wdcd.flight_id > 4
        <if test="query.agentCode != null">
            and ba.agent in
            <foreach collection="query.agentCode" item="agentCode" open="(" separator="," close=")">
                #{agentCode}
            </foreach>
        </if>
        <if test="type != null">
            and wdcd.type = #{type}
        </if>
        <if test="isSettle != null">
            and wdcd.is_settle = #{isSettle}
        </if>
        <if test="query.flightStartTime != null">
            and afi.start_scheme_takeoff_time <![CDATA[>=]]> #{query.flightStartTime}
        </if>
        <if test="query.flightEndTime != null">
            and afi.start_scheme_takeoff_time <![CDATA[<=]]> #{query.flightEndTime}
        </if>
        <if test="query.startTimeSettle != null">
            and wdcd.create_time <![CDATA[>=]]> #{query.startTimeSettle}
        </if>
        <if test="query.endTimeSettle != null">
            and wdcd.create_time <![CDATA[<=]]> #{query.endTimeSettle}
        </if>
        <if test="query.writeStartTime != null">
            and aaw.write_time <![CDATA[>=]]> #{query.writeStartTime}
        </if>
        <if test="query.writeEndTime != null">
            and aaw.write_time <![CDATA[<=]]> #{query.writeEndTime}
        </if>
        group by aaw.waybill_code, aaw.quantity, aaw.charge_weight, hci.charge_abb,
        wdcd.total_charge, aaw.settle_time,aaw.status,wdcd.id,wdcd.type,ba.agent,aaw.dept_id
    </select>
    <select id="selectPayOrSettleListBySettleTimeAgent"
            resultType="com.gzairports.common.business.departure.domain.CostDetail">
        select wdcd.flight_id, wdcd.create_time, wdcd.total_charge,wdcd.quantity,wdcd.settle_dep_quantity,wdcd.settle_dep_weight,
               hci.charge_name, hci.charge_abb, hcr.rule_name, hcr.class_name
        from wl_dep_cost_detail wdcd
        left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
        left join hz_charge_items hci on hci.id = hcir.item_id
        left join hz_charge_rule hcr  on hcr.id = hcir.rule_id
        left join all_flight_info afi on afi.flight_id = wdcd.flight_id
        where wdcd.waybill_code = #{code} and wdcd.is_del = 0
        and wdcd.flight_id != 4 and wdcd.flight_id != 3
        <if test="deptId != null">
            and wdcd.dept_id = #{deptId}
        </if>
        <if test="i == 0 or i == 1">
            and wdcd.type = #{i}
        </if>
        <if test="isSettle == 0 or isSettle == 1">
            and wdcd.is_settle = #{isSettle}
        </if>
        <if test="query.startTimeSettle != null">
            and wdcd.create_time <![CDATA[>=]]> #{query.startTimeSettle}
        </if>
        <if test="query.endTimeSettle != null">
            and wdcd.create_time <![CDATA[<=]]> #{query.endTimeSettle}
        </if>
        <if test="query.flightLoadStartTime != null">
            and afi.start_scheme_takeoff_time <![CDATA[>=]]> #{query.flightLoadStartTime}
        </if>
        <if test="query.flightLoadEndTime != null">
            and afi.start_scheme_takeoff_time <![CDATA[<=]]> #{query.flightLoadEndTime}
        </if>
    </select>
    <select id="selectNotSettleDataAgent"
            resultType="com.gzairports.hz.business.departure.domain.vo.ChargeBillExportNotSettleVoAgent">
        select aaw.waybill_code, aaw.quantity, aaw.charge_weight, hci.charge_abb, wdcd.total_charge, aaw.settle_time,
        aaw.status,wdcd.id,wdcd.type,aaw.agent_company as agent,aaw.dept_id
        from wl_dep_cost_detail wdcd
        left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
        left join hz_charge_items hci on hci.id = hcir.item_id
        left join all_air_waybill aaw on aaw.waybill_code = wdcd.waybill_code
        left join base_agent ba on ba.dept_id = aaw.dept_id
        where aaw.is_del = 0 and aaw.type = 'DEP'
        and wdcd.is_del = 0
        <if test="query.agentCode != null">
            and ba.agent in
            <foreach collection="query.agentCode" item="agentCode" open="(" separator="," close=")">
                #{agentCode}
            </foreach>
        </if>
        <if test="type != null">
            and wdcd.type = #{type}
        </if>
        <if test="isSettle != null">
            and wdcd.is_settle = #{isSettle}
        </if>
        <if test="query.writeStartTime != null">
            and aaw.pay_time <![CDATA[>=]]> #{query.writeStartTime}
            and aaw.write_time <![CDATA[>=]]> #{query.writeStartTime}
        </if>
        <if test="query.writeEndTime != null">
            and aaw.pay_time <![CDATA[<=]]> #{query.writeEndTime}
            and aaw.write_time <![CDATA[<=]]> #{query.writeEndTime}
        </if>

    </select>
    <select id="selectFlightNotSettleDataAgent"
            resultType="com.gzairports.hz.business.departure.domain.vo.ChargeBillExportNotSettleVoAgent">
        select aaw.waybill_code, aaw.quantity, aaw.charge_weight, hci.charge_abb, wdcd.total_charge, aaw.settle_time,
        aaw.status,wdcd.id,wdcd.type,aaw.agent_company as agent,aaw.dept_id
        from wl_dep_cost_detail wdcd
        left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
        left join hz_charge_items hci on hci.id = hcir.item_id
        left join all_air_waybill aaw on aaw.waybill_code = wdcd.waybill_code
        left join base_agent ba on ba.dept_id = aaw.dept_id
        left join (
        select hflw.waybill_id as waybill_id, hfl1.flight_id as flight_id
        from hz_flight_load_waybill hflw
        left join hz_flight_load hfl1 on hfl1.id = hflw.flight_load_id

        union all

        select hfluw.waybill_id as waybill_id, hfl2.flight_id as flight_id
        from hz_flight_load_uld_waybill hfluw
        left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
        left join hz_flight_load hfl2 on hfl2.id = hflu.flight_load_id
        ) combined_flights on aaw.id = combined_flights.waybill_id
        left join all_flight_info afi on afi.flight_id = combined_flights.flight_id

        where aaw.is_del = 0 and aaw.type = 'DEP'
        and wdcd.is_del = 0
        <if test="query.agentCode != null">
            and ba.agent in
            <foreach collection="query.agentCode" item="agentCode" open="(" separator="," close=")">
                #{agentCode}
            </foreach>
        </if>
        <if test="type != null">
            and wdcd.type = #{type}
        </if>
        <if test="isSettle != null">
            and wdcd.is_settle = #{isSettle}
        </if>
        <if test="query.writeStartTime != null">
            and aaw.pay_time <![CDATA[>=]]> #{query.writeStartTime}
            and aaw.write_time <![CDATA[>=]]> #{query.writeStartTime}
        </if>
        <if test="query.writeEndTime != null">
            and aaw.pay_time <![CDATA[<=]]> #{query.writeEndTime}
            and aaw.write_time <![CDATA[<=]]> #{query.writeEndTime}
            and afi.start_scheme_takeoff_time <![CDATA[>=]]> #{query.writeEndTime}
        </if>
        group by aaw.waybill_code, aaw.quantity, aaw.charge_weight, hci.charge_abb,
        wdcd.total_charge, aaw.settle_time,aaw.status,wdcd.id,wdcd.type,aaw.agent_company,aaw.dept_id
    </select>
    <select id="selectPullAllNotSettleDataAgent"
            resultType="com.gzairports.hz.business.departure.domain.vo.ChargeBillExportNotSettleVoAgent">
        select aaw.waybill_code, aaw.quantity, aaw.charge_weight, hci.charge_abb, wdcd.total_charge, aaw.settle_time,
        aaw.status,wdcd.id,wdcd.type,aaw.agent_company as agent,aaw.dept_id
        from wl_dep_cost_detail wdcd
        left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
        left join hz_charge_items hci on hci.id = hcir.item_id
        left join all_air_waybill aaw on aaw.waybill_code = wdcd.waybill_code
        left join base_agent ba on ba.dept_id = aaw.dept_id
        where aaw.is_del = 0 and aaw.type = 'DEP'
        and wdcd.is_del = 0 and wdcd.type = 1 and aaw.waybill_code in
        <foreach collection="waybillCodes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>
    <select id="selectPayAndSettleSumByWaybillCode"
            resultType="com.gzairports.common.business.departure.domain.vo.CostDetailSumVo">
        SELECT
            SUM(CASE WHEN type = 0 AND is_settle = 1 THEN total_charge ELSE 0 END) AS paySum,
            SUM(CASE WHEN type = 1 AND flight_id NOT IN ('1','3','4') THEN total_charge ELSE 0 END) AS settleSum
        FROM wl_dep_cost_detail
        WHERE waybill_code = #{waybillCode}
          AND is_del = 0
    </select>
    <select id="selectPayAndSettleSumByWaybillCodeRefund"
            resultType="com.gzairports.common.business.departure.domain.vo.CostDetailSumVo">
        SELECT
            SUM(CASE WHEN type = 0 AND is_settle = 1 THEN total_charge ELSE 0 END) AS paySum,
            SUM(CASE WHEN type = 1 THEN total_charge ELSE 0 END) AS settleSum
        FROM wl_dep_cost_detail
        WHERE waybill_code = #{waybillCode}
          AND is_del = 0
    </select>
    <select id="selectRefundAmountList" resultType="com.gzairports.hz.business.departure.domain.vo.ChargeBillRefundExportVO">
        select aaw.waybill_code, aaw.quantity, aaw.charge_weight, hci.charge_abb, wdcd.total_charge, aaw.settle_time,
               aaw.status,wdcd.id,wdcd.type, wdcd.flight_id, wdcd.is_settle
        from wl_dep_cost_detail wdcd
                 left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
                 left join hz_charge_items hci on hci.id = hcir.item_id
                 left join all_air_waybill aaw on aaw.waybill_code = wdcd.waybill_code
                 left join base_agent ba on ba.dept_id = aaw.dept_id
                 left join (
            select hflw.waybill_id as waybill_id, hfl1.flight_id as flight_id
            from hz_flight_load_waybill hflw
                     left join hz_flight_load hfl1 on hfl1.id = hflw.flight_load_id

            union all

            select hfluw.waybill_id as waybill_id, hfl2.flight_id as flight_id
            from hz_flight_load_uld_waybill hfluw
                     left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
                     left join hz_flight_load hfl2 on hfl2.id = hflu.flight_load_id
        ) combined_flights on aaw.id = combined_flights.waybill_id
                 left join all_flight_info afi on afi.flight_id = combined_flights.flight_id

        where aaw.is_del = 0 and aaw.type = 'DEP'
          and wdcd.is_del = 0
        <if test="reduceCurrentWaybillCodeList != null and reduceCurrentWaybillCodeList.size() > 0">
            and aaw.waybill_code in
            <foreach collection="reduceCurrentWaybillCodeList" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        group by aaw.waybill_code, aaw.quantity, aaw.charge_weight, hci.charge_abb,
        wdcd.total_charge, aaw.settle_time,aaw.status,wdcd.id,wdcd.type,wdcd.is_settle,wdcd.flight_id
    </select>
    <select id="selectRefundAmountListNew" resultType="com.gzairports.hz.business.departure.domain.vo.ChargeBillRefundExportVO">
        select wdcd.waybill_code, aaw.quantity, aaw.charge_weight, hci.charge_abb, wdcd.total_charge, aaw.settle_time,
               aaw.status,wdcd.id,wdcd.type,wdcd.is_settle
        from wl_dep_cost_detail wdcd
                 left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
                 left join hz_charge_items hci on hci.id = hcir.item_id
                 left join all_air_waybill aaw on aaw.waybill_code = wdcd.waybill_code
                 left join base_agent ba on ba.dept_id = aaw.dept_id
        where wdcd.is_del = 0
        <if test="reduceCurrentWaybillCodeList != null and reduceCurrentWaybillCodeList.size() > 0">
            and wdcd.waybill_code in
            <foreach collection="reduceCurrentWaybillCodeList" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        group by wdcd.waybill_code, aaw.quantity, aaw.charge_weight, hci.charge_abb,wdcd.total_charge, aaw.settle_time,
                 aaw.status,wdcd.id,wdcd.type,wdcd.is_settle
    </select>
    <select id="selectListByCodeList" resultType="com.gzairports.common.business.departure.domain.CostDetail">
        select waybill_code, flight_id, total_charge, type, is_settle from wl_dep_cost_detail
        where is_del = 0
        <if test="waybillCodeFlightList != null and waybillCodeFlightList.size()>0">
            and waybill_code in
            <foreach collection="waybillCodeFlightList" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
    </select>
    <select id="selectSettledNotCurrentList"
            resultType="com.gzairports.hz.business.departure.domain.vo.ChargeBilExportNotSettleVO">
        select aaw.waybill_code, aaw.quantity, aaw.charge_weight, hci.charge_abb, wdcd.total_charge, aaw.settle_time,
        aaw.status,wdcd.id,wdcd.type, wdcd.flight_id
        from wl_dep_cost_detail wdcd
        left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
        left join hz_charge_items hci on hci.id = hcir.item_id
        left join all_air_waybill aaw on aaw.waybill_code = wdcd.waybill_code
        left join base_agent ba on ba.dept_id = aaw.dept_id
        left join (
        select hflw.waybill_id as waybill_id, hfl1.flight_id as flight_id
        from hz_flight_load_waybill hflw
        left join hz_flight_load hfl1 on hfl1.id = hflw.flight_load_id

        union all

        select hfluw.waybill_id as waybill_id, hfl2.flight_id as flight_id
        from hz_flight_load_uld_waybill hfluw
        left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
        left join hz_flight_load hfl2 on hfl2.id = hflu.flight_load_id
        ) combined_flights on aaw.id = combined_flights.waybill_id
        left join all_flight_info afi on afi.flight_id = combined_flights.flight_id

        where aaw.is_del = 0 and aaw.type = 'DEP'
        and wdcd.is_del = 0
        <if test="waybillCodeList != null and waybillCodeList.size() > 0">
            and aaw.waybill_code in
            <foreach collection="waybillCodeList" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        group by aaw.waybill_code, aaw.quantity, aaw.charge_weight, hci.charge_abb,
        wdcd.total_charge, aaw.settle_time,aaw.status,wdcd.id,wdcd.type,wdcd.flight_id
    </select>
    <select id="selectFutureFlightItem"
            resultType="com.gzairports.hz.business.departure.domain.vo.ChargeBilExportFutureFlightVO">
        select aaw.waybill_code, aaw.quantity, aaw.charge_weight, hci.charge_abb, wdcd.total_charge, aaw.settle_time,
        aaw.status,wdcd.id,wdcd.type, wdcd.is_settle
        from wl_dep_cost_detail wdcd
        left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
        left join hz_charge_items hci on hci.id = hcir.item_id
        left join all_air_waybill aaw on aaw.waybill_code = wdcd.waybill_code
        left join base_agent ba on ba.dept_id = aaw.dept_id
        left join (
        select hflw.waybill_id as waybill_id, hfl1.flight_id as flight_id
        from hz_flight_load_waybill hflw
        left join hz_flight_load hfl1 on hfl1.id = hflw.flight_load_id

        union all

        select hfluw.waybill_id as waybill_id, hfl2.flight_id as flight_id
        from hz_flight_load_uld_waybill hfluw
        left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
        left join hz_flight_load hfl2 on hfl2.id = hflu.flight_load_id
        ) combined_flights on aaw.id = combined_flights.waybill_id
        left join all_flight_info afi on afi.flight_id = combined_flights.flight_id
        where aaw.is_del = 0 and aaw.type = 'DEP'
        and wdcd.is_del = 0
        <if test="query != null">
            <if test="query.agent != null">
                and ba.agent = #{query.agent}
            </if>
            <if test="query.flightStartTime != null">
                and aaw.pay_time <![CDATA[>=]]> #{query.flightStartTime}
            </if>
            <if test="query.flightEndTime != null">
                and aaw.pay_time <![CDATA[<=]]> #{query.flightEndTime}
                and (afi.start_scheme_takeoff_time <![CDATA[>=]]> #{query.flightEndTime}
                or afi.start_scheme_takeoff_time <![CDATA[<=]]> #{query.flightStartTime}
                or afi.start_scheme_takeoff_time is null)
            </if>
        </if>
        <if test="type != null">
            and wdcd.type = #{type}
        </if>
        <if test="isSettle != null">
            and wdcd.is_settle = #{isSettle}
        </if>
        <if test="waybillCodeList != null and waybillCodeList.size() > 0">
            and aaw.waybill_code in
            <foreach collection="waybillCodeList" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        group by aaw.waybill_code, aaw.quantity, aaw.charge_weight, hci.charge_abb, wdcd.total_charge, aaw.settle_time,
        aaw.status,wdcd.id,wdcd.type, wdcd.is_settle
    </select>
<!--    AND NOT EXISTS (-->
<!--    select 1-->
<!--    from wl_dep_cost_detail wdcd2-->
<!--    where wdcd2.waybill_code = wdcd.waybill_code-->
<!--    and wdcd2.flight_id in (1,3,4) and wdcd2.is_del = 0-->
<!--    and wdcd2.create_time <![CDATA[>=]]> #{query.flightStartTime} and wdcd2.create_time <![CDATA[<=]]> #{query.flightEndTime})-->

    <!--    <if test="isSettle != null">-->
<!--        and (wdcd.is_settle = #{isSettle} or wdcd.is_settle = 2)-->
<!--    </if>-->

<!--    AND NOT EXISTS (
    SELECT 1
    FROM (
    SELECT hflw.waybill_id, hfl1.flight_id
    FROM hz_flight_load_waybill hflw
    LEFT JOIN hz_flight_load hfl1 ON hfl1.id = hflw.flight_load_id
    UNION ALL
    SELECT hfluw.waybill_id, hfl2.flight_id
    FROM hz_flight_load_uld_waybill hfluw
    LEFT JOIN hz_flight_load_uld hflu ON hflu.id = hfluw.load_uld_id
    LEFT JOIN hz_flight_load hfl2 ON hfl2.id = hflu.flight_load_id
    ) combined_flights_exists
    LEFT JOIN all_flight_info afi_exists ON afi_exists.flight_id = combined_flights_exists.flight_id
    WHERE
    combined_flights_exists.waybill_id = aaw.id
    <if test="query != null">
        <if test="query.flightStartTime != null">
            AND afi_exists.start_scheme_takeoff_time <![CDATA[>=]]> #{query.flightStartTime}
        </if>
        <if test="query.flightEndTime != null">
            AND afi_exists.start_scheme_takeoff_time <![CDATA[<=]]> #{query.flightEndTime}
        </if>
    </if>
    )-->
    <select id="selectDetailListByCode"
            resultType="com.gzairports.common.business.departure.domain.CostDetail">
        select wdcd.id,wdcd.waybill_code,wdcd.total_charge,wdcd.quantity as settleDepQuantity,hci.charge_abb,aaw.weight as settleDepWeight,
               aaw.agent_company as agent
        from wl_dep_cost_detail wdcd
        left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
        left join hz_charge_items hci on hci.id = hcir.item_id
        left join hz_charge_rule hcr  on hcr.id = hcir.rule_id
        left join all_air_waybill aaw on aaw.waybill_code = wdcd.waybill_code
        where wdcd.waybill_code = #{waybillCode} and wdcd.is_del = 0 and wdcd.type = 0
        <if test="isSettle != null">
            and wdcd.is_settle = #{isSettle}
        </if>
    </select>
    <select id="selectPayWaybillNotSettle"
            resultType="com.gzairports.hz.business.departure.domain.vo.ChargeBilExportNotSettleVO">
        select aaw.waybill_code,aaw.quantity,aaw.charge_weight,hci.charge_abb,wdcd.total_charge,aaw.settle_time,aaw.status,
        wdcd.id,wdcd.type,aaw.dept_id
        from wl_dep_cost_detail wdcd
        left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
        left join hz_charge_items hci on hci.id = hcir.item_id
        left join all_air_waybill aaw on aaw.waybill_code = wdcd.waybill_code
        left join base_agent ba on ba.dept_id = aaw.dept_id
        left join (
        select hflw.waybill_id as waybill_id, hfl1.flight_id as flight_id
        from hz_flight_load_waybill hflw
        left join hz_flight_load hfl1 on hfl1.id = hflw.flight_load_id

        union all

        select hfluw.waybill_id as waybill_id, hfl2.flight_id as flight_id
        from hz_flight_load_uld_waybill hfluw
        left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
        left join hz_flight_load hfl2 on hfl2.id = hflu.flight_load_id
        ) combined_flights on aaw.id = combined_flights.waybill_id
        left join all_flight_info afi on afi.flight_id = combined_flights.flight_id
        where ba.agent = #{query.agent}
        and aaw.is_del = 0 and aaw.type = 'DEP'
        and wdcd.is_del = 0 and wdcd.type = 0 and wdcd.is_settle = 1
        AND NOT EXISTS (
        select 1
        from wl_dep_cost_detail wdcd2
        where wdcd2.waybill_code = wdcd.waybill_code
        and wdcd2.type = 1 and wdcd2.is_del = 0)
        <if test="query.flightStartTime != null">
            and aaw.pay_time <![CDATA[>=]]> #{query.flightStartTime}
        </if>
        <if test="query.flightEndTime != null">
            and aaw.pay_time <![CDATA[<=]]> #{query.flightEndTime}
            and (afi.start_scheme_takeoff_time <![CDATA[<=]]> #{query.flightEndTime}
            or afi.start_scheme_takeoff_time <![CDATA[>=]]> #{query.flightStartTime})
        </if>
        group by aaw.waybill_code, aaw.quantity, aaw.charge_weight, hci.charge_abb,wdcd.total_charge, aaw.settle_time,aaw.status,
        wdcd.id,wdcd.type,aaw.dept_id
    </select>
    <select id="selectPayOrSettleListBySettle" resultType="com.gzairports.common.business.departure.domain.CostDetail">
        select wdcd.flight_id, wdcd.create_time, wdcd.total_charge,wdcd.quantity,wdcd.settle_dep_quantity,wdcd.settle_dep_weight,
        hci.charge_name, hci.charge_abb, hcr.rule_name, hcr.class_name, wdcd.waybill_code, wdcd.dept_id
        from wl_dep_cost_detail wdcd
        left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
        left join hz_charge_items hci on hci.id = hcir.item_id
        left join hz_charge_rule hcr  on hcr.id = hcir.rule_id
        left join all_flight_info afi on afi.flight_id = wdcd.flight_id
        where  wdcd.is_del = 0
        and wdcd.flight_id != 4 and wdcd.flight_id != 3 and type = 1 and wdcd.is_settle = 1
        and (wdcd.dept_id,wdcd.waybill_code) in
        <foreach collection="keys" item="key" open="(" separator="," close=")">
            (#{key.deptId}, #{key.waybillCode})
        </foreach>
    </select>
    <select id="selectListByWaybillcodeList" resultType="com.gzairports.common.business.departure.domain.CostDetail">
        select waybill_code,total_charge from wl_dep_cost_detail
        where is_del = 0 and type = 1
        <if test="codeList != null and codeList.size()>0">
            and waybill_code in
            <foreach collection="codeList" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
    </select>
    <select id="selectPayAndSettleSumByCodeList"
            resultType="com.gzairports.common.business.departure.domain.vo.CostDetailSumVo">
        SELECT waybill_code,
            SUM(CASE WHEN type = 0 AND is_settle = 1 THEN total_charge ELSE 0 END) AS paySum,
            SUM(CASE WHEN type = 1 AND flight_id NOT IN ('1','3','4') THEN total_charge ELSE 0 END) AS settleSum
        FROM wl_dep_cost_detail
        WHERE is_del = 0 and waybill_code in
        <foreach collection="collect" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        group by waybill_code
    </select>
    <select id="selectPayOrSettleData" resultType="com.gzairports.common.business.departure.domain.CostDetail">
        select waybill_code, dept_id, total_charge from wl_dep_cost_detail
        where is_del = 0
        <if test="type != null">
            and type = #{type}
        </if>
        <if test="isSettle != null">
            and is_settle = #{isSettle}
        </if>
        and (waybill_code, dept_id) in
        <foreach collection="keys" item="key" open="(" separator="," close=")">
            (#{key.waybillCode}, #{key.deptId})
        </foreach>
    </select>
    <select id="selectSettleByCodeList"
            resultType="com.gzairports.common.business.departure.domain.CostDetail">
        select waybill_code, total_charge, flight_id
        from wl_dep_cost_detail
        where is_del = 0 and type = 1
        and is_settle = 1 and waybill_code in
        <foreach collection="waybillCodes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>
    <select id="selectRefundList" resultType="com.gzairports.common.business.departure.domain.CostDetail">
        select waybill_code, dept_id, total_charge from wl_dep_cost_detail
        where is_del = 0 and type = 1 and flight_id in (1,3,4)
        and (waybill_code, dept_id) in
        <foreach collection="keys" item="key" open="(" separator="," close=")">
            (#{key.waybillCode}, #{key.deptId})
        </foreach>
    </select>
    <select id="selectNotSettleDataNew"
            resultType="com.gzairports.hz.business.departure.domain.vo.ChargeBilExportNotSettleVO">
        select aaw.waybill_code, aaw.quantity, aaw.charge_weight, hci.charge_abb, wdcd.total_charge, aaw.settle_time,
        aaw.status,wdcd.id,wdcd.type
        from base_balance bb
        left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
        left join hz_charge_items hci on hci.id = hcir.item_id
        left join all_air_waybill aaw on aaw.waybill_code = wdcd.waybill_code
        left join base_agent ba on ba.dept_id = aaw.dept_id

        where aaw.is_del = 0 and aaw.type = 'DEP'
        and wdcd.is_del = 0 and aaw.status != 'INVALID'
        and ba.agent = #{query.agent}
        <if test="type != null">
            and wdcd.type = #{type}
        </if>
        <if test="isSettle != null">
            and wdcd.is_settle = #{isSettle}
        </if>
        <if test="query.writeStartTime != null">
            and aaw.pay_time <![CDATA[>=]]> #{query.writeStartTime}
            and aaw.write_time <![CDATA[>=]]> #{query.writeStartTime}
        </if>
        <if test="query.writeEndTime != null">
            and aaw.pay_time <![CDATA[<=]]> #{query.writeEndTime}
            and aaw.write_time <![CDATA[<=]]> #{query.writeEndTime}
        </if>
    </select>
    <select id="selectAllPostSum" resultType="java.math.BigDecimal">
        SELECT
                SUM(CASE WHEN type = 0 AND is_settle = 1 THEN total_charge ELSE 0 END) -
                SUM(CASE WHEN type = 1 THEN total_charge ELSE 0 END) AS diffSum
        FROM wl_dep_cost_detail
        WHERE is_del = 0 and waybill_code in
        <foreach collection="waybillCodeList" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>
    <select id="selectAllSettleSum" resultType="java.math.BigDecimal">
        select sum(total_charge) from FROM wl_dep_cost_detail
        WHERE AND is_del = 0 and type = 1 and waybill_code in
        <foreach collection="waybillCodeList" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>
    <select id="selectFlightRefundData"
            resultType="com.gzairports.hz.business.departure.domain.vo.ChargeBilExportNotSettleVO">
        select aaw.waybill_code, aaw.quantity, aaw.charge_weight, hci.charge_abb, wdcd.total_charge, aaw.settle_time,
        aaw.status,wdcd.id,wdcd.type,wdcd.total_charge as notSettleCharge
        from wl_dep_cost_detail wdcd
        left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
        left join hz_charge_items hci on hci.id = hcir.item_id
        left join all_air_waybill aaw on aaw.waybill_code = wdcd.waybill_code
        left join base_agent ba on ba.dept_id = aaw.dept_id
        where aaw.is_del = 0 and aaw.type = 'DEP'
        and wdcd.is_del = 0 and aaw.status != 'INVALID'
        and ba.agent = #{agent} and wdcd.type = 1 and flight_id in (1,3,4) and wdcd.total_charge > 0
            <if test="writeStartTime != null">
                and aaw.pay_time <![CDATA[>=]]> #{writeStartTime}
            </if>
            <if test="writeEndTime != null">
                and aaw.pay_time <![CDATA[<=]]> #{writeEndTime}
                and wdcd.create_time <![CDATA[>]]> #{writeEndTime}
            </if>
        group by aaw.waybill_code, aaw.quantity, aaw.charge_weight, hci.charge_abb,wdcd.total_charge, aaw.settle_time,
        aaw.status,wdcd.id,wdcd.type
    </select>
    <select id="selectCancelPayData"
            resultType="com.gzairports.hz.business.departure.domain.vo.ChargeBilExportNotSettleVO">
        select aaw.waybill_code, aaw.quantity, aaw.charge_weight, hci.charge_abb, wdcd.total_charge, aaw.settle_time,
        aaw.status,wdcd.id,wdcd.type
        from base_balance bb
        left join wl_dep_cost_detail wdcd on bb.waybill_code = wdcd.waybill_code
        left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
        left join hz_charge_items hci on hci.id = hcir.item_id
        left join all_air_waybill aaw on aaw.waybill_code = wdcd.waybill_code
        left join base_agent ba on ba.dept_id = aaw.dept_id
        where aaw.is_del = 0 and aaw.type = 'DEP'
        and wdcd.is_del = 0 and aaw.status != 'INVALID'
        and ba.agent = #{agent} and wdcd.type = 0 and wdcd.is_settle = 2
        <if test="writeStartTime != null">
            and aaw.pay_time <![CDATA[>=]]> #{writeStartTime}
        </if>
        <if test="writeEndTime != null">
            and aaw.pay_time <![CDATA[<=]]> #{writeEndTime}
            and bb.create_time <![CDATA[>]]> #{writeEndTime}
        </if>
        group by aaw.waybill_code, aaw.quantity, aaw.charge_weight, hci.charge_abb,wdcd.total_charge, aaw.settle_time,
        aaw.status,wdcd.id,wdcd.type
    </select>
    <select id="selectPayAndSettleSumNew"
            resultType="com.gzairports.common.business.departure.domain.vo.CostDetailSumVo">
        SELECT
            SUM(CASE WHEN type = 0 AND (is_settle = 1 or is_settle = 2)
                THEN total_charge ELSE 0 END) AS paySum,
            SUM(CASE WHEN type = 1
                AND create_time <![CDATA[<]]> #{endTime} AND create_time <![CDATA[>]]> #{startTime}
                THEN total_charge ELSE 0 END) AS settleSum
        FROM wl_dep_cost_detail
        WHERE waybill_code = #{waybillCode}
          AND is_del = 0
    </select>
<!--    AND flight_id NOT IN ('1','3','4')-->
    <select id="selectPayAndSettleSumNewByFlightTime"
            resultType="com.gzairports.common.business.departure.domain.vo.CostDetailSumVo">
        SELECT
            SUM(CASE WHEN wdcd.type = 0 AND wdcd.is_settle IN (1, 2)
                         THEN wdcd.total_charge ELSE 0 END) AS paySum,
            SUM(CASE WHEN wdcd.type = 1 AND (
                        (wdcd.flight_id IS NULL OR wdcd.flight_id IN (1,3,4)) AND
                        wdcd.create_time <![CDATA[>]]> #{startTime} AND
                        wdcd.create_time <![CDATA[<]]> #{endTime}
                    OR
                        (wdcd.flight_id >= 5 AND EXISTS (
                                SELECT 1 FROM all_air_waybill aaw
                                                  JOIN (
                                    SELECT hflw.waybill_id, hfl1.flight_id
                                    FROM hz_flight_load_waybill hflw
                                             JOIN hz_flight_load hfl1 ON hfl1.id = hflw.flight_load_id
                                    UNION ALL
                                    SELECT hfluw.waybill_id, hfl2.flight_id
                                    FROM hz_flight_load_uld_waybill hfluw
                                             JOIN hz_flight_load_uld hflu ON hflu.id = hfluw.load_uld_id
                                             JOIN hz_flight_load hfl2 ON hfl2.id = hflu.flight_load_id
                                ) combined_flights ON aaw.id = combined_flights.waybill_id
                                                  JOIN all_flight_info afi ON afi.flight_id = combined_flights.flight_id
                                WHERE aaw.waybill_code = wdcd.waybill_code
                                  AND afi.flight_id = wdcd.flight_id
                                  AND afi.start_scheme_takeoff_time <![CDATA[>]]> #{startTime}
                                  AND afi.start_scheme_takeoff_time <![CDATA[<]]> #{endTime}
                            ))
                ) THEN wdcd.total_charge ELSE 0 END) AS settleSum
        FROM wl_dep_cost_detail wdcd
        WHERE wdcd.waybill_code  = #{waybillCode}
          AND wdcd.is_del = 0
    </select>
    <select id="selectByFlightIds" resultType="com.gzairports.hz.business.departure.domain.vo.ChargeBilExportVO">
        select aaw.waybill_code,aaw.quantity,aaw.charge_weight,hci.charge_abb,wdcd.total_charge,wdcd.create_time as settleTime,aaw.status,
               wdcd.id,wdcd.type,aaw.dept_id,wdcd.flight_id
        from wl_dep_cost_detail wdcd
                 left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
                 left join hz_charge_items hci on hci.id = hcir.item_id
                 left join all_air_waybill aaw on aaw.waybill_code = wdcd.waybill_code
        where wdcd.type = 1 and wdcd.is_del = 0 and wdcd.total_charge > 0 and wdcd.dept_id = #{deptId}
          and wdcd.flight_id in
        <foreach collection="flightIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectNotCurrentAmountList"
            resultType="com.gzairports.hz.business.departure.domain.vo.ChargeBilExportNotSettleVO">
        select aaw.waybill_code, aaw.quantity, aaw.charge_weight, hci.charge_abb, wdcd.total_charge, wdcd.create_time as settleTime,
               aaw.status,wdcd.id,wdcd.type, wdcd.flight_id
        from wl_dep_cost_detail wdcd
                 left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
                 left join hz_charge_items hci on hci.id = hcir.item_id
                 left join all_air_waybill aaw on aaw.waybill_code = wdcd.waybill_code
        where aaw.is_del = 0 and aaw.type = 'DEP'
          and wdcd.is_del = 0
          and (wdcd.waybill_code, wdcd.flight_id) in
        <foreach collection="filteredList" item="key" open="(" separator="," close=")">
            (#{key.waybillCode},#{key.flightId})
        </foreach>
    </select>
    <select id="selectPayList"
            resultType="com.gzairports.hz.business.departure.domain.vo.ChargeBilExportNotSettleVO">
        select aaw.waybill_code, aaw.quantity, aaw.charge_weight, hci.charge_abb, wdcd.total_charge, aaw.settle_time,
               aaw.status,wdcd.id,wdcd.type, wdcd.flight_id
        from wl_dep_cost_detail wdcd
                 left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
                 left join hz_charge_items hci on hci.id = hcir.item_id
                 left join all_air_waybill aaw on aaw.waybill_code = wdcd.waybill_code
        where aaw.is_del = 0 and aaw.type = 'DEP'  and wdcd.type = 0 and is_settle = 1
          and wdcd.is_del = 0 and wdcd.waybill_code in
        <foreach collection="loadWaybillList" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>
    <select id="selectRefundListNew"
            resultType="com.gzairports.hz.business.departure.domain.vo.ChargeBilExportNotSettleVO">
        select aaw.waybill_code, aaw.quantity, aaw.charge_weight, hci.charge_abb, wdcd.total_charge, wdcd.create_time as settleTime,
               aaw.status,wdcd.id,wdcd.type, wdcd.flight_id
        from wl_dep_cost_detail wdcd
            left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
            left join hz_charge_items hci on hci.id = hcir.item_id
            left join all_air_waybill aaw on aaw.waybill_code = wdcd.waybill_code
        where aaw.is_del = 0 and aaw.type = 'DEP'  and wdcd.type = 1 and flight_id in (1,3,4)
            and wdcd.is_del = 0 and wdcd.waybill_code in
        <foreach collection="loadWaybillList" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>
    <select id="selectByFlightWaybills"
            resultType="com.gzairports.hz.business.departure.domain.vo.ChargeBilExportVO">
        select aaw.waybill_code,aaw.quantity,aaw.charge_weight,hci.charge_abb,wdcd.total_charge,wdcd.create_time as settleTime,aaw.status,
               wdcd.id,wdcd.type,aaw.dept_id,wdcd.flight_id, ba.agent
        from wl_dep_cost_detail wdcd
            left join hz_charge_ir_relation hcir on hcir.id = wdcd.ir_id
            left join hz_charge_items hci on hci.id = hcir.item_id
            left join all_air_waybill aaw on aaw.waybill_code = wdcd.waybill_code
            left join base_agent ba on ba.dept_id = aaw.dept_id
        where wdcd.type = 1 and wdcd.is_del = 0 and wdcd.total_charge > 0
          <if test="deptId != null">
              and wdcd.dept_id = #{deptId}
        </if>
        and (aaw.id, wdcd.flight_id, wdcd.settle_dep_quantity, wdcd.settle_dep_weight) in
        <foreach collection="waybillVoList" item="vo" open="(" separator="," close=")">
            (#{vo.waybillId}, #{vo.flightId}, #{vo.quantity}, #{vo.weight})
        </foreach>
    </select>
    <select id="selectArrBillExport"
            resultType="com.gzairports.hz.business.departure.domain.vo.ChargeBillArrSettleExportVO">
        with RankedData as (
        select aipu.id as pickUpId, aipu.serial_no, aipu.pay_time as settleTime, aipu.settle_user,
               aaw.waybill_code, aaw.quantity, aaw.weight,aaw.charge_weight, aaw.consign, aaw.cargo_name,
        ROW_NUMBER() OVER (partition by aaw.waybill_code, aipu.serial_no order by aipu.pick_up_time desc) as rn
        from all_in_pick_up aipu
        left join all_pick_up_waybill apuw on apuw.pick_up_id = aipu.id
        left join all_air_waybill aaw on aaw.waybill_code = apuw.waybill_code
        where aaw.type = 'ARR' and aaw.dept_id = #{deptId} and aipu.is_pay = 1
        <if test="query.flightStartTime != null">
            and aipu.pick_up_time <![CDATA[>=]]> #{query.flightStartTime}
        </if>
        <if test="query.flightEndTime != null">
            and aipu.pick_up_time <![CDATA[<=]]> #{query.flightEndTime}
        </if>
        )
        select distinct *
        from RankedData
        where rn = 1
    </select>
</mapper>