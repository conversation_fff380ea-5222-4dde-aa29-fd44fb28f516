package com.gzairports.wl.charge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.wl.charge.domain.MailPriceItem;
import com.gzairports.wl.charge.domain.query.PriceItemQuery;
import com.gzairports.wl.charge.domain.vo.MailPriceItemVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 邮件运价条目Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@Mapper
public interface MailPriceItemMapper extends BaseMapper<MailPriceItem> {

    /**
     * 查看运价条目
     *
     * @param id 航司运价id
     * @return 航司运价条目集合
     */
    List<MailPriceItemVo> selectRateItemList(Long id);

    /**
     * 查询运价条目列表
     *
     * @param query 运价条目查询参数
     * @return 结果
     */
    List<MailPriceItemVo> selectAirList(PriceItemQuery query);

    /**
     * 删除运价条目
     *
     * @param id 运价条目主键
     * @return 结果
     */
    int removeAirItem(Long id);
}
