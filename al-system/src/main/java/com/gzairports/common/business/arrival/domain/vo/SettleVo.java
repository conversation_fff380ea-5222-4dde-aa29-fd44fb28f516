package com.gzairports.common.business.arrival.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 结算返回数据
 *
 * <AUTHOR>
 * @date 2024-07-31
 */
@Data
public class SettleVo {

    /** 运单数 */
    private Integer totalCount;

    /** 提货总件数 */
    private Integer totalQuantity;

    /** 提货总重量 */
    private BigDecimal totalWeight;

    /** 费用合计 */
    private BigDecimal totalCost;

    /** 支付方式（现金 线上） */
    private String payMethod;

    /** 结算方式 */
    private Integer settleMethod;

    /** 二维码 */
    private String code;

    /** 余额 */
    private BigDecimal balance;
}
