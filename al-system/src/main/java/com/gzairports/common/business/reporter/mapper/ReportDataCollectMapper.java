package com.gzairports.common.business.reporter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.reporter.domain.ReportDataCollect;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-03
 */
@Mapper
public interface ReportDataCollectMapper extends BaseMapper<ReportDataCollect> {
    void deleteBatch(@Param("reportIds") List<Long> reportIds);

    int insertBatchSomeColumn(List<ReportDataCollect> collectList);
}
