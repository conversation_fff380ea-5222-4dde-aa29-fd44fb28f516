package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BaseApp;
import com.gzairports.common.basedata.service.IBaseAppService;
import com.gzairports.common.basedata.service.IBaseAppWlService;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * app物流管理Controller
 * 
 * <AUTHOR>
 * @date 2024-09-19
 */
@RestController
@RequestMapping("/base/appWl")
@Api(tags = "app管理")
public class BaseAppWlController extends BaseController
{
    @Autowired
    private IBaseAppWlService baseAppService;

    /**
     * 查询app管理列表
     */
    @PreAuthorize("@ss.hasPermi('base:wlApp:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询app管理列表")
    public TableDataInfo list(BaseApp baseApp)
    {
        startPage();
        List<BaseApp> list = baseAppService.selectBaseAppList(baseApp);
        return getDataTable(list);
    }

    /**
     * 导出app管理列表
     */
    @PreAuthorize("@ss.hasPermi('base:wlApp:export')")
    @GetMapping("/export")
    @ApiOperation(value = "导出app管理列表")
    public AjaxResult export(BaseApp baseApp)
    {
        List<BaseApp> list = baseAppService.selectBaseAppList(baseApp);
        ExcelUtil<BaseApp> util = new ExcelUtil<BaseApp>(BaseApp.class);
        return util.exportExcel(list, "app管理数据");
    }

    /**
     * 获取app管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('base:wlApp:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取app管理详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(baseAppService.selectBaseAppById(id));
    }

    /**
     * 新增app管理
     */
    @PreAuthorize("@ss.hasPermi('base:wlApp:add')")
    @PostMapping("/add")
    @ApiOperation(value = "新增app管理")
    public AjaxResult add(@RequestBody BaseApp baseApp)
    {
        return toAjax(baseAppService.insertBaseApp(baseApp));
    }

    /**
     * 修改app管理
     */
    @PreAuthorize("@ss.hasPermi('base:wlApp:edit')")
    @PostMapping("/edit")
    @ApiOperation(value = "修改app管理")
    public AjaxResult edit(@RequestBody BaseApp baseApp)
    {
        return toAjax(baseAppService.updateBaseApp(baseApp));
    }

    /**
     * 删除app管理
     */
    @PreAuthorize("@ss.hasPermi('base:wlApp:remove')")
	@GetMapping("/remove/{ids}")
    @ApiOperation(value = "删除app管理")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(baseAppService.deleteBaseAppByIds(ids));
    }

    /**
     * 发布APP
     */
    @PreAuthorize("@ss.hasPermi('base:wlApp:release')")
    @GetMapping("/release/{id}")
    @ApiOperation(value = "发布APP")
    public AjaxResult release(@PathVariable("id") Long id)
    {
        return toAjax(baseAppService.release(id));
    }

    /**
     * app下载
     */
    @GetMapping("/download")
    @ApiOperation(value = "app下载")
    public AjaxResult download()
    {
        return success(baseAppService.download());
    }

    /**
     * 版本对比
     */
    @GetMapping("/compare/{version}")
    @ApiOperation(value = "版本对比")
    public AjaxResult compare(@PathVariable("version") String version)
    {
        return success(baseAppService.compare(version));
    }
}
