package com.gzairports.common.business.arrival.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 提货办单信息数据
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
@Data
public class PickUpOutWaybillVo {

    /** 主键id */
    private Long id;

    /** 办理提货id */
    private Long pickUpId;

    /** 理货id */
    private Long tallyId;

    /** 运单号 */
    private String waybillCode;

    /** 次数 */
    private Integer num;

    /** 品名 */
    private String cargoName;

    /** 办单件数 */
    private Integer orderQuantity;

    /** 办单重量 */
    private BigDecimal orderWeight;

    /** 提货客户 */
    private String customerName;

    /** 证件类型 */
    private String customerIdType;

    /** 证件号 */
    private String customerIdNo;

    /** 支付方式 */
    private String payMethod;

    /** 是否支付 */
    private Integer isPay;

    /** 是否已提货 */
    private String isTake;

    /** 经办人 */
    private String handleBy;

    /** 备注 */
    private String remark;

    /** 提货件数 */
    private Integer pieces;

    /** 提货重量 */
    private BigDecimal weight;

    /** 提货码 */
    private String pickUpCode;
}
