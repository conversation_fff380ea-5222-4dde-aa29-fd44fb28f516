package com.gzairports.wl.ticket.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 单证票号表
 *
 * <AUTHOR>
 * @date 2024-03-20
 */
@Data
@TableName("wl_ticket_num")
public class TicketNum extends Model<TicketNum> {

    /** 主键id */
    private Long id;

    /** 单证id */
    private Long ticketId;

    /** 发放记录id */
    private Long recordId;

    /** 票号 */
    @Excel(name = "票号")
    private Integer num;

    /** 校验号 */
    private Integer code;

    /** 状态 状态（NOTUSED 未使用 USED 已使用 CANCEL 销号 INVALID 作废）*/
    private String status;

    /** 状态 */
    @Excel(name = "状态")
    @TableField(exist = false)
    private String state;

    /** 使用人 */
    private Long useBy;

    /** 使用部门 */
    private Long deptId;

    @Excel(name = "使用人")
    @TableField(exist = false)
    private String userName;

    /** 该号使用时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "使用时间")
    private LocalDateTime useTime;
}
