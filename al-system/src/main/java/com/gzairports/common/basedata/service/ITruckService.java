package com.gzairports.common.basedata.service;


import com.gzairports.common.basedata.domain.BaseFlatbedTruck;
import com.gzairports.common.basedata.domain.query.FlatbedTruckQuery;

import java.util.List;

/**
 * 板车管理Service接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
public interface ITruckService {

    /**
     * 查询板车管理列表
     *
     * @param query 查询参数
     * @return 板车管理列表
     */
    List<BaseFlatbedTruck> flatbedTruckList(FlatbedTruckQuery query);

    /**
     * 新增板车
     *
     * @param truck 板车
     * @return 结果
     */
    int addFlatbedTruck(BaseFlatbedTruck truck);

    /**
     * 修改板车
     *
     * @param truck 板车
     * @return 结果
     */
    int editFlatbedTruck(BaseFlatbedTruck truck);

    /**
     * 删除板车
     *
     * @param ids 板车id集合
     * @return 结果
     */
    int removeFlatbedTruckByIds(Long[] ids);

    /**
     * 详情
     * @param id 板车id
     * @return 结果
     */
    BaseFlatbedTruck getInfo(Long id);

    /**
     * 导入板车
     * @param trucks 板车数据列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    String importTrucks(List<BaseFlatbedTruck> trucks, boolean updateSupport);
}
