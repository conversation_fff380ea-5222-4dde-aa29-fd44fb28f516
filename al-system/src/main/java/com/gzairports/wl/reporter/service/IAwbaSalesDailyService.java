package com.gzairports.wl.reporter.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gzairports.common.business.reporter.domain.ReportDataWaybill;
import com.gzairports.wl.reporter.domain.query.AwbaSalesQuery;
import com.gzairports.wl.reporter.domain.query.ReportExportData;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2025-02-21
 */
public interface IAwbaSalesDailyService {

    /**
     * 查询分单销售表格
     * @param query 查询条件
     * @return 报表数据
     */
    ReportExportData queryExportDataList(AwbaSalesQuery query);

    /**
     * 导出报表数据
     * @param reportExportData 报表数据
     * @param response 响应
     */
    void export(ReportExportData reportExportData, HttpServletResponse response);

    /**
     * 分页查询报表数据
     * @param query 查询参数
     * @return 结果
     */
    IPage<ReportDataWaybill> pageQuery(AwbaSalesQuery query);

    /**
     * 打印报表
     * @param query 查询参数
     * @param response 响应
     */
    void printPDF(AwbaSalesQuery query, HttpServletResponse response);

}
