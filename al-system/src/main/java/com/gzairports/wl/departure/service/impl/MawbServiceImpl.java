package com.gzairports.wl.departure.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.*;
import com.gzairports.common.basedata.mapper.*;
import com.gzairports.common.basedata.service.IAirportCodeService;
import com.gzairports.common.business.arrival.domain.FlightLoadUldWaybill;
import com.gzairports.common.business.arrival.domain.FlightLoadWaybill;
import com.gzairports.common.business.arrival.mapper.FlightInfoMapper;
import com.gzairports.common.business.arrival.mapper.FlightLoadUldWaybillMapper;
import com.gzairports.common.business.arrival.mapper.FlightLoadWaybillMapper;
import com.gzairports.common.business.departure.domain.*;
import com.gzairports.common.business.departure.domain.vo.LoadInfoVo;
import com.gzairports.common.business.departure.mapper.*;
import com.gzairports.common.business.departure.service.impl.WaybillTraceServiceImpl;
import com.gzairports.common.business.wrong.domain.Wrong;
import com.gzairports.common.business.wrong.mapper.WrongMapper;
import com.gzairports.common.charge.domain.HzChargeIrRelation;
import com.gzairports.common.charge.domain.HzChargeItemRule;
import com.gzairports.common.charge.domain.HzChargeItems;
import com.gzairports.common.charge.domain.HzChargeRule;
import com.gzairports.common.charge.domain.vo.BillRuleVo;
import com.gzairports.common.charge.domain.vo.IrRelationVo;
import com.gzairports.common.charge.mapper.HzChargeIrRelationMapper;
import com.gzairports.common.charge.mapper.HzChargeItemRuleMapper;
import com.gzairports.common.charge.mapper.HzChargeItemsMapper;
import com.gzairports.common.charge.mapper.HzChargeRuleMapper;
import com.gzairports.common.charge.service.BillingRule;
import com.gzairports.common.charge.service.impl.BillingRuleFactory;
import com.gzairports.common.core.domain.entity.SysDept;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.common.log.service.IWaybillLogService;
import com.gzairports.common.message.domain.Message;
import com.gzairports.common.message.domain.vo.SocketMessageVo;
import com.gzairports.common.message.mapper.MessageMapper;
import com.gzairports.common.pdf.PdfPrintHelper;
import com.gzairports.common.rabbitmq.SecurityProducer;
import com.gzairports.common.securitySubmit.domain.AllSecurityWaybill;
import com.gzairports.common.securitySubmit.domain.WaybillChangeData;
import com.gzairports.common.securitySubmit.mapper.AllSecurityWaybillMapper;
import com.gzairports.common.system.domain.SysConfig;
import com.gzairports.common.system.mapper.SysConfigMapper;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.system.mapper.SysUserMapper;
import com.gzairports.common.utils.*;
import com.gzairports.common.utils.sign.Base64;
import com.gzairports.hz.business.departure.domain.HzCollectWaybill;
import com.gzairports.hz.business.departure.domain.HzDepPullDown;
import com.gzairports.hz.business.departure.domain.HzDisBoard;
import com.gzairports.hz.business.departure.mapper.HzCollectWaybillMapper;
import com.gzairports.hz.business.departure.mapper.HzDisBoardMapper;
import com.gzairports.hz.business.departure.rabbitmq.WaybillMessageProducer;
import com.gzairports.wl.charge.domain.ChargeItem;
import com.gzairports.wl.charge.domain.FreightRateAir;
import com.gzairports.wl.charge.domain.FreightRateAirItem;
import com.gzairports.wl.charge.mapper.ChargeItemMapper;
import com.gzairports.wl.charge.mapper.FreightRateAirItemMapper;
import com.gzairports.wl.charge.mapper.FreightRateAirMapper;
import com.gzairports.wl.departure.domain.MawbErrorRemark;
import com.gzairports.wl.departure.domain.MawbItem;
import com.gzairports.wl.departure.domain.MergeHawbMawb;
import com.gzairports.wl.departure.domain.RateItem;
import com.gzairports.wl.departure.domain.query.FareQuery;
import com.gzairports.wl.departure.domain.query.HawbQuery;
import com.gzairports.wl.departure.domain.query.ItemQuery;
import com.gzairports.wl.departure.domain.vo.*;
import com.gzairports.wl.departure.mapper.MergeHawbMawbMapper;
import com.gzairports.wl.departure.service.IMawbService;
import com.gzairports.wl.ticket.domain.TicketCtrl;
import com.gzairports.wl.ticket.domain.TicketNum;
import com.gzairports.wl.ticket.mapper.OperateMapper;
import com.gzairports.wl.ticket.mapper.TicketMapper;
import com.gzairports.wl.ticket.mapper.TicketNumMapper;
import com.itextpdf.text.Document;
import com.itextpdf.text.pdf.PdfCopy;
import com.itextpdf.text.pdf.PdfImportedPage;
import com.itextpdf.text.pdf.PdfReader;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.gzairports.common.utils.SecurityUtils.getUsername;

/**
 * 主单制单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class MawbServiceImpl extends ServiceImpl<MawbMapper, Mawb> implements IMawbService {

    @Autowired
    private MawbMapper mawbMapper;

    @Autowired
    private OperateMapper operateMapper;

    @Autowired
    private TicketNumMapper ticketNumMapper;

    @Autowired
    private CargoCodeMapper cargoCodeMapper;

    @Autowired
    private TicketMapper ticketMapper;

    @Autowired
    private ChargeItemMapper chargeItemMapper;

    @Autowired
    private MawbItemServiceImpl itemService;

    @Autowired
    private MawbErrorRemarkServiceImpl errorRemarkService;

    @Autowired
    private FreightRateAirMapper rateAirMapper;

    @Autowired
    private FreightRateAirItemMapper rateAirItemMapper;

    @Autowired
    private FlightInfoMapper flightInfoMapper;

    @Autowired
    private BookingMapper bookingMapper;

    @Autowired
    private WaybillTraceServiceImpl waybillTraceService;

    @Autowired
    private PullDownMapper pullDownMapper;

    @Autowired
    private CostDetailMapper costDetailMapper;

    @Autowired
    private BaseAgentMapper baseAgentMapper;

    @Autowired
    private BaseBalanceMapper baseBalanceMapper;

    @Autowired
    private TransferWaybillMapper transferWaybillMapper;

    @Autowired
    private HzChargeItemsMapper itemsMapper;

    @Autowired
    private HzChargeItemRuleMapper itemRuleMapper;

    @Autowired
    private HzChargeRuleMapper ruleMapper;

    @Autowired
    private HzChargeIrRelationMapper relationMapper;

    @Autowired
    private SysConfigMapper sysConfigMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private IWaybillLogService waybillLogService;

    @Autowired
    private CityCodeMapper cityCodeMapper;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    @Autowired
    private MessageMapper messageMapper;

    @Autowired
    private BaseAgentMapper agentMapper;

    @Autowired
    private AirportCodeMapper airportCodeMapper;

    @Autowired
    private IAirportCodeService airportCodeService;

    @Autowired
    private WaybillMessageProducer waybillMessageProducer;

    @Autowired
    private MergeHawbMawbMapper mergeHawbMawbMapper;

    @Autowired
    private FlightLoadWaybillMapper flightLoadWaybillMapper;

    @Autowired
    private FlightLoadUldWaybillMapper flightLoadUldWaybillMapper;

    @Autowired
    private HzCollectWaybillMapper collectWaybillMapper;

    @Autowired
    private HzDisBoardMapper disBoardMapper;

    @Autowired
    private WaybillFeeMapper feeMapper;

    @Autowired
    private AllSecurityWaybillMapper allSecurityWaybillMapper;

    @Autowired
    private WrongMapper wrongMapper;

    @Autowired
    private SecurityProducer securityProducer;


    @Override
    public String add(MawbVo vo) {
        try {
            if(!(vo.getWaybillCode().length() == 16 && vo.getWaybillCode().endsWith("B"))){
                check(vo.getWaybillCode());
            }
        }catch (CustomException e){
            throw new CustomException(e.getMessage());
        }
        String code = vo.getWaybillCode().substring(0, 4);
        Integer ticketNum = Integer.valueOf(vo.getWaybillCode().substring(7,14));

        Long ticketId = ticketMapper.selectCheck(code , vo.getWaybillCode().substring(4,7), ticketNum);
        if (ticketId != null){
            TicketNum num = ticketNumMapper.selectOne(new QueryWrapper<TicketNum>().eq("ticket_id", ticketId).eq("num", ticketNum));
            //查询是否有多个该票号，若有使用的，则不可制单，（若使用了，校验运单号是否重复时就校验了）
            LambdaQueryWrapper<TicketNum> numLqw = Wrappers.lambdaQuery();
            numLqw.eq(TicketNum::getNum, ticketNum);
            if(vo.getId() == null){
                List<TicketNum> ticketNums = ticketNumMapper.selectList(numLqw);
                ticketNums.forEach(v -> {
                    if (v.getStatus().equals("USED")) {
                        throw new CustomException("该单证票号已使用");
                    }
                });
            }

            if (num != null) {
                num.setStatus("USED");
                num.setUseBy(SecurityUtils.getUserId());
                num.setUseTime(LocalDateTime.now());
                ticketNumMapper.updateById(num);
            }
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String execDate = dateFormat.format(vo.getFlightDate1());
        List<FlightInfo> flightInfoList = flightInfoMapper.selectList(new QueryWrapper<FlightInfo>()
                .eq("air_ways", vo.getFlightNo1().substring(0, 2))
                .eq("flight_no", vo.getFlightNo1().substring(2))
                .eq("exec_date", execDate)
                .eq("is_offin", "D"));
        FlightInfo flight = null;
        if(flightInfoList.size() == 1){
            flight = flightInfoList.get(0);
        }else if(flightInfoList.size() > 1){
            List<FlightInfo> collect = flightInfoList.stream().filter(flightInfo -> "W/Z".equals(flightInfo.getTask())).collect(Collectors.toList());
            if (collect.size() > 0){
                flight = collect.get(0);
            }
        }
        BaseCargoCode baseCargoCode2 = cargoCodeMapper.selectByCode(vo.getCargoCode());
        Mawb mawb = new Mawb();
        BeanUtils.copyProperties(vo,mawb);
        mawb.setVersion(vo.getVersion());
        mawb.setConsignAbb(vo.getConsigneeAbb());
        mawb.setConsign(vo.getConsign());
        mawb.setConsignAddress(vo.getConsigneeAddress());
        mawb.setConsignPhone(vo.getConsigneePhone());
        mawb.setConsignRegion(vo.getConsigneeRegion());
        mawb.setType("DEP");
        mawb.setUpdateTime(new Date());
        mawb.setCategoryName(baseCargoCode2.getCategoryCode());
        String substring = vo.getCargoCode().substring(0, 2);
        mawb.setCategoryName(substring);
        Long deptId = SecurityUtils.getHighParentId();
        mawb.setDeptId(deptId);
        if (vo.getPayStatus() == null){
            vo.setPayStatus(0);
        }
        if (("been_sent".equals(vo.getStatus()) && vo.getPayStatus() == 0) || vo.getPayStatus() == 14){
            if (vo.getSwitchBill() != 2){
                costDetailMapper.delete(new QueryWrapper<CostDetail>().eq("waybill_code", vo.getWaybillCode()));
                // 生成默认收费项目
                List<HzChargeItems> hzChargeItems = itemsMapper.selectList(new QueryWrapper<HzChargeItems>()
                        .eq("operation_type", "DEP")
                        .eq("is_default", 1).eq("status",1)
                        .le("start_effective_time",vo.getWriteTime())
                        .ge("end_effective_time",vo.getWriteTime())
                        .eq("is_del",0));
                SysConfig sysConfig = sysConfigMapper.selectConfigById(16L);
                LocalTime startTime;
                LocalTime endTime;
                if (flight == null){
                    startTime = LocalTime.of(6, 0,0);
                    endTime = LocalTime.of(6, 0,1);
                }else {
                    LocalDateTime takeoffTime = flight.getStartSchemeTakeoffTime();
                    LocalDateTime time = takeoffTime.plusSeconds(1);
                    endTime = time.toLocalTime();
                    startTime = takeoffTime.toLocalTime();
                }
                Date date = new Date();
                Instant startInstant = date.toInstant();
                long aLong = Long.parseLong(sysConfig.getConfigValue());
                long times = aLong * 60 * 60;
                Instant endInstant = startInstant.plusSeconds(times);
                Date storeEndTime = Date.from(endInstant);
                BigDecimal costSum = new BigDecimal(0);
                BigDecimal weightRate;
                BigDecimal chargeWeight = mawb.getChargeWeight() == null ? new BigDecimal(0) : mawb.getChargeWeight();
                if (mawb.getWeight() == null || mawb.getWeight().compareTo(new BigDecimal(0)) == 0){
                    weightRate = new BigDecimal(0);
                }else {
                    BigDecimal bigDecimal = chargeWeight.divide(mawb.getWeight(),5, RoundingMode.DOWN).multiply(mawb.getWeight());
                    weightRate = bigDecimal.setScale(0, RoundingMode.CEILING);
                }
                for (HzChargeItems hzChargeItem : hzChargeItems) {
                    List<HzChargeIrRelation> relations = relationMapper.selectList(new QueryWrapper<HzChargeIrRelation>().eq("item_id", hzChargeItem.getId()).eq("is_del",0));
                    int maxMatchCount = 0;
                    List<HzChargeIrRelation> ruleList = new ArrayList<>();
                    for (HzChargeIrRelation hzChargeRule : relations) {
                        if (hzChargeRule.getIsSouth() == 1){
                            continue;
                        }
                        if (hzChargeRule.getIsExit() == 1){
                            continue;
                        }
                        if (StringUtils.isNotEmpty(hzChargeRule.getNoCharge()) && !hzChargeRule.getNoCharge().contains(deptId.toString())){
                            continue;
                        }
                        if (StringUtils.isNotEmpty(hzChargeRule.getPrefix()) && !hzChargeRule.getPrefix().contains(vo.getWaybillCode().substring(4,7))){
                            continue;
                        }
                        if (StringUtils.isNotEmpty(hzChargeRule.getCategory()) && !hzChargeRule.getCategory().contains(baseCargoCode2.getCategoryCode())){
                            continue;
                        }
                        if (!hzChargeRule.getCrossAir().equals(vo.getCrossAir())){
                            continue;
                        }
                        int matchCount = 0;
                        // 根据判断货品代码
                        int cargoMatchCount = isCargoCodeMatch(hzChargeRule, vo.getCargoCode());

                        if (cargoMatchCount >= 0) {
                            matchCount += cargoMatchCount;
                        }
                        if (matchCount > 0) {
                            if (matchCount > maxMatchCount) {
                                maxMatchCount = matchCount;
                                ruleList.clear();
                                ruleList.add(hzChargeRule);
                            } else if (matchCount == maxMatchCount) {
                                ruleList.add(hzChargeRule);
                            }
                        }
                    }
                    if (!CollectionUtils.isEmpty(ruleList)){
                        HzChargeIrRelation relation = ruleList.stream().max(Comparator.comparing(HzChargeIrRelation::getPriority)).orElse(null);
                        if (relation != null){
                            HzChargeRule rule1 = ruleMapper.selectById(relation.getRuleId());
                            List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>().eq("ir_id", relation.getId()));
                            if ("ColdStorageBillingRule.class".equals(rule1.getClassName()) && StringUtils.isEmpty(vo.getColdStore())){
                                continue;
                            }
                            CostDetail detail = new CostDetail();
                            detail.setWaybillCode(vo.getWaybillCode());
                            detail.setIrId(relation.getId());
                            detail.setColdStore(vo.getColdStore());
                            detail.setUnit(1);
                            detail.setSmallItem(1);
                            detail.setLargeItem(1);
                            detail.setSuperLargeItem(1);
                            detail.setStartTime(startTime);
                            detail.setEndTime(endTime);
                            detail.setSettleDepWeight(mawb.getWeight());
                            detail.setDaysInStorage(1.0);
                            detail.setStoreStartTime(date);
                            detail.setStoreEndTime(storeEndTime);
                            detail.setPointTime(startTime);
                            BillingRule rule = BillingRuleFactory.createRule(rule1.getClassName());
                            BillRuleVo vo1 = rule.calculateFee(itemRules, weightRate, vo.getQuantity(), detail);
                            BigDecimal totalCharge = BigDecimalRoundUtils.bigDecimalRound(hzChargeItem.getRoundRule(), vo1.getTotalCharge());
                            detail.setTotalCharge(totalCharge);
                            detail.setDeptId(deptId);
                            detail.setQuantity(vo1.getQuantity());
                            detail.setRate(vo1.getRate());
                            costSum = costSum.add(totalCharge);
                            costDetailMapper.insert(detail);
                        }
                    }
                }
                mawb.setPayMoney(costSum);
                mawb.setPayStatus(0);
            }
            // 生成订舱数据
            Booking booking = new Booking();
            if (flight != null){
                booking.setCraftType(flight.getCraftType());
                booking.setCraftNo(flight.getCraftNo());
                booking.setPlanTakeoffTime(flight.getStartSchemeTakeoffTime());
            }
            BeanUtils.copyProperties(vo,booking);
            booking.setBookingNo(SerialNumberGenerator.generateSerialNumber());
            booking.setSpecialCode(vo.getSpecialCargoCode1());
            booking.setFlightNo(vo.getFlightNo1());
            booking.setFlightDate(vo.getFlightDate1());
            booking.setStatus(0);
            booking.setDeptId(deptId);
            if (StringUtils.isNull(booking.getId())){
                bookingMapper.insert(booking);
            }else {
                bookingMapper.updateById(booking);
            }
            // 运单跟踪数据
            WaybillTrace waybillTrace = new WaybillTrace();
            waybillTrace.setOperTime(new Date());
            waybillTrace.setOperPieces(vo.getQuantity());
            waybillTrace.setOperWeight(vo.getWeight());
            waybillTrace.setWaybillCode(vo.getWaybillCode());
            waybillTrace.setNodeName("已发送");
            waybillTrace.setChargeWeight(vo.getChargeWeight());
            waybillTraceService.insertWaybillTrace(waybillTrace);
        }
        if (vo.getSwitchBill() != null) {
            //判断是否为补货单
            if(vo.getSwitchBill() == 2){
                Wrong wrong = wrongMapper.selectOne(new QueryWrapper<Wrong>()
                        .eq("waybill_code", vo.getOriginBill())
                        .eq("dept_id", deptId).eq("pro_method", 1)
                        .orderByDesc("create_time").last("limit 1"));
                if (wrong == null){
                    throw new CustomException("原运单不能补货");
                }
                //1.23只要是换单就可以补货 不用管是否处理完成
//                if (wrong.getStatus() != 3){
//                    throw new CustomException("请到“不正常货邮“中处理原单号");
//                }
                if (vo.getWeight().compareTo(vo.getReplenishWeight()) > 0){
                    throw new CustomException("补货单重量不能超过可补货重量");
                }
                //判断该运单与库里面存在的运单货品大类是否一致
                Mawb airWaybillForOriginBill = mawbMapper.selectOne(new QueryWrapper<Mawb>()
                        .eq("waybill_code", mawb.getOriginBill()).last("limit 1"));
                if (StringUtils.isNull(airWaybillForOriginBill)) {
                    throw new CustomException("该补货单无对应原单,录入失败");
                }
                if(airWaybillForOriginBill.getSwitchBill() == 2){
                    throw new CustomException("原单已是补货单,录入失败");
                }
                WaybillLog waybillLog = waybillLogService.getWaybillLog(
                        airWaybillForOriginBill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                        airWaybillForOriginBill.getWeight().toString(), airWaybillForOriginBill.getQuantity().toString(), airWaybillForOriginBill.getFlightNo1(),
                        airWaybillForOriginBill.getWaybillCode(), null, 0, null, new Date(),
                        "补货", "DEP", null);
                waybillLogService.insertWaybillLog(waybillLog);
                //判断原单是否已有补货单
                if (vo.getId() == null){
                    Integer originBill = mawbMapper.selectCount(new QueryWrapper<Mawb>()
                            .eq("origin_bill", mawb.getOriginBill())
                            .eq("type","DEP"));
                    if(originBill > 0){
                        throw new CustomException("原单已有补货单,录入失败");
                    }
                }
                //根据品名找到大类,再判断
                BaseCargoCode baseCargoCode = cargoCodeMapper.selectCargoCodeByCode(airWaybillForOriginBill.getCargoCode());
                BaseCargoCode baseCargoCode1 = cargoCodeMapper.selectCargoCodeByCode(mawb.getCargoCode());
                if (!baseCargoCode.getCategoryCode().equals(baseCargoCode1.getCategoryCode())) {
                    throw new CustomException("该补货单与原单货品大类不一致,录入失败");
                }
                //判断原单在系统里面是否还有库存(收运-配载+拉下)
                Integer originQuantity;
                BigDecimal originWeight;
                List<HzCollectWaybill> collectWaybillOriginList = collectWaybillMapper.selectList(new QueryWrapper<HzCollectWaybill>()
                        .eq("waybill_id", airWaybillForOriginBill.getId()));
                List<FlightLoadWaybill> loadWaybillOriginList = flightLoadWaybillMapper.selectList(new QueryWrapper<FlightLoadWaybill>()
                        .eq("waybill_id", airWaybillForOriginBill.getId()));
                List<FlightLoadUldWaybill> loadUldWaybillOriginList = flightLoadUldWaybillMapper.selectList(new QueryWrapper<FlightLoadUldWaybill>()
                        .eq("waybill_id", airWaybillForOriginBill.getId()));
                List<HzDepPullDown> pullDownWaybillOriginList = pullDownMapper.selectList(new QueryWrapper<HzDepPullDown>()
                        .eq("waybill_code", airWaybillForOriginBill.getWaybillCode())
                        .eq("is_load",0));
                List<HzDisBoard> hzDisBoardsWaybillOriginList = disBoardMapper.selectList(new QueryWrapper<HzDisBoard>()
                        .eq("waybill_id", airWaybillForOriginBill.getId())
                        .eq("is_load", 0));
                if(collectWaybillOriginList.size() > 0){
                    originQuantity = collectWaybillOriginList.stream().map(HzCollectWaybill::getQuantity).reduce(0,Integer::sum);
                    originWeight = collectWaybillOriginList.stream().map(HzCollectWaybill::getWeight).reduce(BigDecimal.ZERO,BigDecimal::add);
                    if(loadWaybillOriginList.size() > 0){
                        Integer reduce = loadWaybillOriginList.stream().map(FlightLoadWaybill::getQuantity).reduce(0, Integer::sum);
                        BigDecimal reduce1 = loadWaybillOriginList.stream().map(FlightLoadWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                        originQuantity -= reduce;
                        originWeight = originWeight.subtract(reduce1);
                    }
                    if(loadUldWaybillOriginList.size() > 0){
                        Integer reduce = loadUldWaybillOriginList.stream().map(FlightLoadUldWaybill::getQuantity).reduce(0, Integer::sum);
                        BigDecimal reduce1 = loadUldWaybillOriginList.stream().map(FlightLoadUldWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                        originQuantity -= reduce;
                        originWeight = originWeight.subtract(reduce1);
                    }
                    if(pullDownWaybillOriginList.size() > 0){
                        Integer reduce = pullDownWaybillOriginList.stream().map(HzDepPullDown::getQuantity).reduce(0, Integer::sum);
                        BigDecimal reduce1 = pullDownWaybillOriginList.stream().map(HzDepPullDown::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                        originQuantity += reduce;
                        originWeight = originWeight.add(reduce1);
                    }
                    if(hzDisBoardsWaybillOriginList.size() > 0){
                        Integer reduce = hzDisBoardsWaybillOriginList.stream().map(HzDisBoard::getQuantity).reduce(0, Integer::sum);
                        BigDecimal reduce1 = hzDisBoardsWaybillOriginList.stream().map(HzDisBoard::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                        originQuantity += reduce;
                        originWeight = originWeight.add(reduce1);
                    }
                    if(originQuantity <= 0 || originWeight.compareTo(BigDecimal.ZERO) <= 0){
                        throw new CustomException("原单无库存,录入失败");
                    }
                }
                Integer payStatus = airWaybillForOriginBill.getPayStatus();
                if (payStatus <= 4){
                    mawb.setPayStatus(payStatus);
                    mawb.setPayMoney(new BigDecimal(0));
                }else if (payStatus < 13){
                    boolean flag = true;
                    while(flag){
                        payStatus-=4;
                        if (payStatus <= 4){
                            flag = false;
                        }
                    }
                    mawb.setPayStatus(payStatus);
                    mawb.setPayMoney(new BigDecimal(0));
                }
            }
            //如果是换单 需要作废原单 + 运单日志 (原单号可能为进港中转的单)
            if(vo.getSwitchBill() == 1){
                Mawb oldWaybill = mawbMapper.selectOne(new QueryWrapper<Mawb>()
                        .eq("waybill_code",vo.getOriginBill())
                        .eq("type","DEP")
                        .eq("is_del",0));
                if(oldWaybill == null){
                    throw new CustomException("输入的原单号不存在");
                }
                WaybillLog waybillLog = waybillLogService.getWaybillLog(
                        oldWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                        oldWaybill.getWeight().toString(), oldWaybill.getQuantity().toString(), oldWaybill.getFlightNo1(),
                        oldWaybill.getWaybillCode(), null, 0, null, new Date(),
                        "换单（新单号:" + vo.getWaybillCode() +"）", "DEP", null);
                waybillLogService.insertWaybillLog(waybillLog);
                if (!"INVALID".equals(oldWaybill.getStatus())){
                    Long oldWaybillId = oldWaybill.getId();
                    List<LoadInfoVo> infoVos = flightLoadWaybillMapper.selectByLoadInfo(oldWaybillId);
                    if(infoVos.size() > 0){
                        throw new CustomException("输入的原单号在配载有数据");
                    }
                    refund(oldWaybill);
                }

                WaybillChangeData waybillChangeData = new WaybillChangeData();
                waybillChangeData.setType(3);
                waybillChangeData.setWaybillCode(vo.getOriginBill().substring(4));
                waybillChangeData.setWaybillCodeNew(vo.getWaybillCode().substring(4));
                securityProducer.sendOtherWaybill(waybillChangeData,vo.getWaybillCode() + "旧运单" + vo.getOriginBill(),"物流平台选择换单");
            }
            if(vo.getSwitchBill() == 3){
                WaybillLog waybillLog = waybillLogService.getWaybillLog(
                        vo.getWaybillCode(), 0, SecurityUtils.getNickName(),
                        vo.getWeight().toString(), vo.getQuantity().toString(), vo.getFlightNo1(),
                        vo, null, 0, null, new Date(),
                        "补重", "DEP", null);
                waybillLogService.insertWaybillLog(waybillLog);
            }
        }
        //这种情况是从制分单过来 但是主单不存在
        if(StringUtils.isNotEmpty(vo.getHawbMawbId())){
            mawb.setMergeStatus(1);
        }
        //去看看这个单是否在安检申报新增表存在 如果存在就和那边数据同步一下
        AllSecurityWaybill allSecurityWaybill = allSecurityWaybillMapper.selectOne(new QueryWrapper<AllSecurityWaybill>()
                .eq("waybill_code", mawb.getWaybillCode()));
        if(allSecurityWaybill != null){
            mawb.setSecurityUrl(allSecurityWaybill.getSecurityUrl());
            mawb.setSecuritySubmitWl(allSecurityWaybill.getSecuritySubmitWl());
            mawb.setSecuritySubmit(allSecurityWaybill.getSecuritySubmit());
            mawb.setSecuritySubmitOperator(allSecurityWaybill.getSecuritySubmitOperator());
            mawb.setDeclarationConsistent(allSecurityWaybill.getDeclarationConsistent());
            mawb.setDeliveryIdNo(allSecurityWaybill.getDeliveryIdNo());
            mawb.setDeliveryFilePhoto(allSecurityWaybill.getDeliveryFilePhoto());
            mawb.setDeliveryProfilePhoto(allSecurityWaybill.getDeliveryProfilePhoto());
            mawb.setShipperSignature(allSecurityWaybill.getShipperSignature());
            mawb.setAgentSignature(allSecurityWaybill.getAgentSignature());
        }
        if (vo.getId() != null){
            boolean b = updateById(mawb);
            if (!b){
                throw new CustomException("更新失败，运单已被其他操作修改，请刷新后重试");
            }
        }else {
            save(mawb);
        }
//        saveOrUpdate(mawb);
        if (flight != null && Objects.equals(1,flight.getIsPre())){
            //发送消息
            sendMessage(vo.getFlightNo1());
        }
        if (!CollectionUtils.isEmpty(vo.getItems())){
            for (MawbItem item : vo.getItems()) {
                item.setWaybillId(mawb.getId());
                item.setDeptId(deptId);
                item.setWaybillCode(mawb.getWaybillCode());
                itemService.saveOrUpdate(item);
            }
        }
        if (!CollectionUtils.isEmpty(vo.getErrorRemarks())){
            for (MawbErrorRemark errorRemark : vo.getErrorRemarks()) {
                errorRemark.setWaybillId(mawb.getId());
                errorRemarkService.saveOrUpdate(errorRemark);
            }
        }
        //这种情况是从制分单过来 但是主单不存在
        if(StringUtils.isNotEmpty(vo.getHawbMawbId())){
            MergeHawbMawb mergeHawbMawb = mergeHawbMawbMapper.selectById(vo.getHawbMawbId());
            mergeHawbMawb.setMawbId(mawb.getId());
            mergeHawbMawbMapper.updateById(mergeHawbMawb);
        }
        return String.valueOf(mawb.getId());
    }

    private void refund(Mawb mawb) {
        BaseAgent agent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", mawb.getDeptId()));
        BigDecimal costSum = new BigDecimal(0);
        BigDecimal weightRate;
        BigDecimal chargeWeight = mawb.getChargeWeight() == null ? new BigDecimal(0) : mawb.getChargeWeight();
        if (mawb.getWeight() == null || mawb.getWeight().compareTo(new BigDecimal(0)) == 0){
            weightRate = new BigDecimal(0);
        }else {
            BigDecimal bigDecimal = chargeWeight.divide(mawb.getWeight(),5, RoundingMode.DOWN).multiply(mawb.getWeight());
            weightRate = bigDecimal.setScale(0, RoundingMode.CEILING);
        }
        List<CostDetail> details = costDetailMapper.selectPayOrSettleList(mawb.getWaybillCode(), 0, 1, mawb.getDeptId());
        if (!CollectionUtils.isEmpty(details)){
            List<CostDetail> collect = details.stream().filter(e -> "处置费".equals(e.getChargeAbb())).collect(Collectors.toList());
            for (CostDetail detail : collect) {
                countCost(detail, mawb.getWeight(), weightRate, mawb.getQuantity());
                costSum = costSum.add(detail.getTotalCharge());
            }
        }
        WaybillFee waybillFee = feeMapper.selectOne(new QueryWrapper<WaybillFee>()
                .eq("waybill_code", mawb.getWaybillCode())
                .eq("dept_id", mawb.getDeptId())
                .eq("type", "DEP"));
        if (agent != null) {
            if (agent.getSettleMethod() == 0) {
                updateStatus(mawb, costSum, waybillFee, mawb.getDeptId(), 9);
            } else if (agent.getSettleMethod() == 1) {
                BigDecimal balance = agent.getBalance() == null ? new BigDecimal(0) : agent.getBalance();
                BigDecimal subtract = balance.add(costSum);
                agent.setBalance(subtract);
                baseAgentMapper.updateBaseAgent(agent);
                BaseBalance baseBalance = new BaseBalance();
                baseBalance.setAgentId(agent.getId());
                baseBalance.setBalance(agent.getBalance());
                baseBalance.setType("增加余额");
                baseBalance.setCreateTime(new Date());
                baseBalance.setCreateBy(SecurityUtils.getNickName());
                // todo 流水号需从银联支付接口获取
                //baseBalance.setSerialNo();
                baseBalance.setTradeMoney(costSum);
                baseBalance.setWaybillCode(mawb.getWaybillCode());
                baseBalance.setRemark("作废退款");
                baseBalanceMapper.insertBaseBalance(baseBalance);
                updateStatus(mawb, costSum, waybillFee, mawb.getDeptId(), 10);
            } else {
                if (agent.getPayMethod() == 0) {
                    updateStatus(mawb, costSum, waybillFee, mawb.getDeptId(), 11);
                } else {
                    updateStatus(mawb, costSum, waybillFee, mawb.getDeptId(), 12);
                }
            }
        } else {
            updateStatus(mawb, costSum, waybillFee, mawb.getDeptId(), 12);
        }
    }


    /**
     * 根据运单号以及单证控制校验运单号
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    @Override
    public MawbVo check(String waybillCode) {
        Long deptId = SecurityUtils.getHighParentId();
        //运单号校验
        if (waybillCode == null){
            throw new CustomException("运单号不能为空");
        }
        if((waybillCode.length() == 16 && waybillCode.endsWith("B"))){
            return getInfo(waybillCode);
        }
        if (waybillCode.length() != 15){
            throw new CustomException("运单号格式错误");
        }
        String code = waybillCode.substring(0, 4);
        TicketCtrl ctrl = operateMapper.selectOne(new QueryWrapper<TicketCtrl>()
                .eq("code", code)
                .eq("domint",'D'));
//                .eq("dept_id",deptId));

        //这个是取校验位 如果是补货单 最后一位是B 就不用校验
        Integer checkNum = Integer.valueOf(waybillCode.substring(waybillCode.length() - 1));

        Integer ticketNum = Integer.valueOf(waybillCode.substring(7,14));

        Mawb mawb = mawbMapper.selectOne(new QueryWrapper<Mawb>()
                .eq("waybill_code", waybillCode)
                .eq("dept_id", deptId)
                .eq("is_del",0)
                .eq("type","DEP"));

//        if (ctrl != null && ctrl.getControlEnabled() == 1){
        if (ctrl != null && ctrl.getDeptIds().contains(deptId.toString())){
            if (StringUtils.isEmpty(ctrl.getPrefix())){
                if (checkCtrl(waybillCode, code, checkNum, ticketNum, mawb)) return getInfo(waybillCode);
            }else if (ctrl.getPrefix().contains(waybillCode.substring(4,7))){
                if (checkCtrl(waybillCode, code, checkNum, ticketNum, mawb)) return getInfo(waybillCode);
            }else {
                return getInfo(waybillCode);
            }
        }else {
            if (mawb != null){
                return getInfo(waybillCode);
            }
            Long ticketId = ticketMapper.selectCheck(code, waybillCode.substring(4,7), ticketNum);
            if (ticketId == null){
                return null;
            }
            TicketNum num = ticketNumMapper.selectOne(new QueryWrapper<TicketNum>()
                    .eq("ticket_id", ticketId)
                    .eq("num", ticketNum));
            if (num == null){
                return null;
            }
            //还未发放
            if(num.getDeptId()==null){
                return null;
            }
            //校验当前登录账号是否可以领单
            checkUser(num.getDeptId());

            if ("NOTUSED".equals(num.getStatus())){
                return null;
            }
        }
        return null;
    }

    /**
     * 校验单号
     * @param waybillCode 运单号
     * @param code 运单前缀
     * @param checkNum 单证校验号
     * @param ticketNum 单证号
     * @param mawb 分单信息
     * @return 结果
     */
    private boolean checkCtrl(String waybillCode, String code, Integer checkNum, Integer ticketNum, Mawb mawb) {
        if (mawb != null) {
            return true;
        }
        Long ticketId = ticketMapper.selectCheck(code, waybillCode.substring(4, 7), ticketNum);
        if (ticketId == null) {
            throw new CustomException("无当前单证信息不可保存，请更改运单号");
        }
        TicketNum num = ticketNumMapper.selectOne(new QueryWrapper<TicketNum>().eq("ticket_id", ticketId).eq("num", ticketNum));
        if (num == null) {
            throw new CustomException("当前运单未发放");
        }else if(num.getDeptId() == null){
            throw new CustomException("当前运单未发放");
        }
        checkUser(num.getDeptId());

        if (!checkNum.equals(num.getCode())) {
//            throw new CustomException("运单校验失败");
            throw new CustomException("当前运单未发放");
        }
        if ("USED".equals(num.getStatus())) {
            return true;
        }
        if ("CANCEL".equals(num.getStatus())) {
            throw new CustomException("当前运单已销号");
        }
        if ("INVALID".equals(num.getStatus())) {
            throw new CustomException("当前运单已作废");
        }
        return false;
    }

    /**
     * 校验当前账号是否可以领单
     *
     * @param deptId 票证的部门id
     * @return 返回结果
     */
    private void checkUser(Long deptId) {
        //校验当前登录账号是否可以领单
        SysDept dept = sysUserMapper.selectUserById(SecurityUtils.getUserId()).getDept();
        //登录人部门的祖级列表 是否包含发放的部门id,如果包含,说明发放的部门是父级部门,则可以使用
        //祖级列表还不够,需要加上当前部门的id和所有子级部门的id
        StringBuilder stringBuilder = new StringBuilder(dept.getAncestors());
        List<SysDept> sysDepts = sysDeptMapper.selectDeptListForUser(SecurityUtils.getDeptId());
        for (SysDept sysDept : sysDepts) {
            stringBuilder.append(",").append(sysDept.getDeptId());
        }
        String s = new String(stringBuilder);
        boolean contains = s.contains(deptId.toString());
        if (!contains) {
            throw new CustomException("与当前运单领单人不符");
        }
    }

    /**
     * 根据默认收费方式计算运单运价
     *
     * @param query 新增运单信息
     * @return 返回结果
     */
    @Override
    public FareVo fare(FareQuery query) {
        FareVo vo = new FareVo();
        List<MawbItem> items = new ArrayList<>();
        if (query.getBulkWarehouse() != null && query.getBulkWarehouse() == 1){
            vo.setChargeWeight(query.getChargeWeight() == null ? query.getWeight() : query.getChargeWeight());
            vo.setCostSum(new BigDecimal(0));
            vo.setMawbItems(items);
            return vo;
        }
        itemService.remove(new QueryWrapper<MawbItem>().eq("waybill_code",query.getWaybillCode()));
        Long deptId = SecurityUtils.getHighParentId();
        BigDecimal bigDecimal = new BigDecimal(0);
        BigDecimal chargeWeight = null;
        String code = query.getWaybillCode().substring(0, 4);
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = currentDate.format(formatter);
        FreightRateAir rateAir1 = rateAirMapper.selectOne(new QueryWrapper<FreightRateAir>()
                .eq("applicable_documents", code)
                .eq("carrier_code", query.getCarrier1())
                .eq("dept_id", deptId)
                .le("effective_date", formattedDate)
                .ge("expiration_date", formattedDate)
                .eq("is_del", 0));
        if (rateAir1 != null){
            List<FreightRateAirItem> airItem = rateAirItemMapper.selectList(new QueryWrapper<FreightRateAirItem>()
                    .eq("rate_id", rateAir1.getId())
                    .eq("departure_city", query.getSourcePort())
                    .eq("destination_city", query.getDesPort())
                    .eq("dept_id",deptId)
                    .eq("is_del",0));
            int maxMatchCount = 0;
            List<FreightRateAirItem> itemList = new ArrayList<>();
            BaseCargoCode cargoCode = cargoCodeMapper.selectByCode(query.getCargoCode());
            for (FreightRateAirItem item : airItem) {
                if (item.getCargoCategory() != null && !item.getCargoCategory().isEmpty()) {
                    Set<String> categorySet = new HashSet<>(Arrays.asList(item.getCargoCategory().split(",")));
                    if (!categorySet.contains(cargoCode.getCategoryCode())){
                        continue;
                    }
                }
                int matchCount = 1;
                if (StringUtils.isEmpty(item.getCargoCode()) && StringUtils.isEmpty(item.getCargoCategory()) && StringUtils.isEmpty(item.getSpecialCode())){
                    matchCount++;
                }
                if (item.getCargoCode() != null && Arrays.asList(item.getCargoCode().split(",")).contains(query.getCargoCode())) {
                    matchCount++;
                }
                if (item.getSpecialCode() != null && item.getSpecialCode().equals(query.getSpecialCargoCode1())){
                    matchCount++;
                }
                if (matchCount > 0) {
                    if (matchCount > maxMatchCount) {
                        maxMatchCount = matchCount;
                        itemList.clear();
                        itemList.add(item);
                    } else if (matchCount == maxMatchCount) {
                        itemList.add(item);
                    }
                }
            }
            if (!CollectionUtils.isEmpty(itemList) && itemList.size() == 1){
                FreightRateAirItem rateAirItem = itemList.get(0);
                if (rateAirItem != null){
                    FareVo multiply = getInterval(rateAirItem, query.getWeight());
                    vo.setRateType(multiply.getRateType());
                    // 取舍方式计算
                    BigDecimal round = BigDecimalRoundUtils.bigDecimalRound(rateAir1.getRoundingMethod(), multiply.getCostSum());
                    MawbItem mawbItem = new MawbItem();
                    mawbItem.setWaybillCode(query.getWaybillCode());
                    mawbItem.setChargeItemId(rateAirItem.getId());
                    mawbItem.setDeptId(deptId);
                    mawbItem.setRate(multiply.getRate());
                    mawbItem.setCostType(rateAir1.getRateName());
                    mawbItem.setRateType(1);
                    if (round.compareTo(rateAirItem.getMinimumFreight()) <= 0 ){
                        mawbItem.setCharging(rateAirItem.getMinimumFreight());
                        items.add(mawbItem);
                        vo.setRateType("M");
                        bigDecimal = bigDecimal.add(rateAirItem.getMinimumFreight());
                    }else {
                        mawbItem.setCharging(round);
                        items.add(mawbItem);
                        bigDecimal = bigDecimal.add(round);
                    }
                    chargeWeight = multiply.getChargeWeight();
                    vo.setItemId(rateAirItem.getId());
                }
            }
        }
        // 收费项目默认计费
        List<ChargeItem> chargeItem = chargeItemMapper.selectByDept(deptId,code);
        List<ChargeItem> collect = chargeItem.stream()
                .filter(e -> StringUtils.isEmpty(e.getCode()) || query.getCarrier1().equals(e.getCode()))
                .collect(Collectors.toList());

        for (ChargeItem item : collect) {
            BigDecimal decimal = new BigDecimal(0);
            Integer defaultBillingMethod = item.getDefaultBillingMethod();
            switch (defaultBillingMethod){
                case 0:
                    decimal = BigDecimalRoundUtils.bigDecimalRound(item.getRoundRule(), item.getDefaultCostRate());
                    break;
                case 1:
                    BigDecimal weight = item.getDefaultCostRate().multiply(chargeWeight == null ? query.getChargeWeight() : chargeWeight);
                    decimal = BigDecimalRoundUtils.bigDecimalRound(item.getRoundRule(),weight);
                    break;
                case 2:
                    BigDecimal volume = item.getDefaultCostRate().multiply(query.getVolume());
                    decimal = BigDecimalRoundUtils.bigDecimalRound(item.getRoundRule(),volume);
                    break;
                case 3:
                    BigDecimal quantity = item.getDefaultCostRate().multiply(new BigDecimal(query.getQuantity()));
                    decimal = BigDecimalRoundUtils.bigDecimalRound(item.getRoundRule(),quantity);
                default:
                    break;
            }
            MawbItem mawbItem = new MawbItem();
            mawbItem.setWaybillCode(query.getWaybillCode());
            mawbItem.setChargeItemId(item.getId());
            mawbItem.setDeptId(item.getDeptId());
            mawbItem.setRate(item.getDefaultCostRate());
            mawbItem.setCostType(item.getName());
            mawbItem.setCharging(decimal);
            mawbItem.setRateType(0);
            items.add(mawbItem);
            bigDecimal = bigDecimal.add(decimal);
        }
        vo.setChargeWeight(chargeWeight == null ? query.getChargeWeight() : chargeWeight);
        vo.setMawbItems(items);
        vo.setCostSum(bigDecimal);
        return vo;
    }

    /**
     * 添加收费管理
     *
     * @param query 分单收费项目参数
     * @return 返回结果
     */
    @Override
    public FareVo addFees(ItemQuery query) {

        FareVo vo = new FareVo();
        BigDecimal costSum = query.getCostSum();
        if (CollectionUtils.isEmpty(query.getChargeItemIds())){
            return null;
        }
        List<ChargeItem> chargeItems = chargeItemMapper.selectBatchIds(query.getChargeItemIds());
        if (CollectionUtils.isEmpty(chargeItems)){
            return null;
        }

        Mawb mawb = mawbMapper.selectOne(new QueryWrapper<Mawb>()
                .eq("waybill_code", query.getWaybillCode())
                .eq("dept_id", SecurityUtils.getHighParentId())
                .eq("is_del",0));

        List<MawbItem> list = new ArrayList<>();
        for (ChargeItem chargeItem : chargeItems) {
            MawbItem one = itemService.getOne(new QueryWrapper<MawbItem>()
                    .eq("waybill_code",query.getWaybillCode())
                    .eq("charge_item_id",chargeItem.getId())
                    .eq("is_del",0));

            if (one == null) {
                BigDecimal decimal = new BigDecimal(0);
                Integer defaultBillingMethod = chargeItem.getDefaultBillingMethod();
                switch (defaultBillingMethod) {
                    case 0:
                        decimal = BigDecimalRoundUtils.bigDecimalRound(chargeItem.getRoundRule(), chargeItem.getDefaultCostRate());
                        break;
                    case 1:
                        BigDecimal weight = chargeItem.getDefaultCostRate().multiply(query.getWeight());
                        decimal = BigDecimalRoundUtils.bigDecimalRound(chargeItem.getRoundRule(), weight);
                        break;
                    case 2:
                        BigDecimal volume = chargeItem.getDefaultCostRate().multiply(query.getVolume());
                        decimal = BigDecimalRoundUtils.bigDecimalRound(chargeItem.getRoundRule(), volume);
                        break;
                    default:
                        break;
                }

                MawbItem item = new MawbItem();
                item.setWaybillCode(query.getWaybillCode());
                item.setChargeItemId(chargeItem.getId());
                item.setDeptId(chargeItem.getDeptId());
                item.setRate(chargeItem.getDefaultCostRate());
                item.setCostType(chargeItem.getName());
                item.setCharging(decimal);
                item.setRateType(0);

                if (mawb != null){
                    itemService.save(item);
                }
                list.add(item);

                costSum = costSum.add(decimal);
            }else {
                throw new CustomException("该运单已存在相同的收费项目");
            }

            if (mawb != null){
                mawb.setCostSum(costSum);
                mawb.setUpdateTime(new Date());
                mawbMapper.updateById(mawb);
            }
        }
        vo.setMawbItems(list);
        vo.setCostSum(costSum);
        vo.setChargeWeight(query.getChargeWeight());
        return vo;
    }

    /**
     * 根据运单code查询运单详情
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    @Override
    public MawbVo getInfo(String waybillCode) {
        MawbVo vo = new MawbVo();
        Mawb mawb;
        mawb= mawbMapper.selectOne(new QueryWrapper<Mawb>()
                .eq("waybill_code", waybillCode)
                .eq("dept_id",SecurityUtils.getHighParentId())
                .eq("type","DEP")
                .eq("is_del",0));
        if (mawb != null){
            List<LoadInfoVo> infoVos = flightLoadWaybillMapper.selectByLoadInfo(mawb.getId());
            BeanUtils.copyProperties(mawb,vo);
            if (!CollectionUtils.isEmpty(infoVos)){
                vo.setIsLoad(1);
            }else {
                vo.setIsLoad(0);
            }
            vo.setConsign(mawb.getConsign());
            vo.setConsigneeAbb(mawb.getConsignAbb());
            vo.setConsigneeAddress(mawb.getConsignAddress());
            vo.setConsigneePhone(mawb.getConsignPhone());
            vo.setConsigneeRegion(mawb.getConsignRegion());
            List<MawbItem> list = itemService.list(new QueryWrapper<MawbItem>()
                    .eq("waybill_code", waybillCode)
                    .eq("dept_id",SecurityUtils.getHighParentId())
                    .eq("is_del",0));
            vo.setItems(list);
            List<MawbErrorRemark> errorRemarks = errorRemarkService.list(new QueryWrapper<MawbErrorRemark>()
                    .eq("waybill_code", waybillCode));
            vo.setErrorRemarks(errorRemarks);
        }
        return vo;
    }

    /**
     * 编辑运单
     *
     * @param waybillCode 运单详情信息
     * @return 返回结果
     */
    @Override
    public void edit(String waybillCode) {
        Mawb mawb = mawbMapper.selectOne(new QueryWrapper<Mawb>()
                .eq("waybill_code", waybillCode)
                .eq("type", "DEP")
                .eq("is_del", 0));
        if (mawb == null){
            throw new CustomException("无当前运单信息");
        }
        String status = transferWaybillMapper.selectTransferStatus(mawb.getId());
        if (!"STAGING".equals(status)){
            throw new CustomException("该运单已交接货站，不可修改");
        }
    }

    /**
     * 修改费用项
     *
     * @param item 分单收费项目
     * @return 返回结果
     */
    @Override
    public FareVo editFee(MawbItem item) {
        FareVo vo = new FareVo();
        Mawb mawb = mawbMapper.selectOne(new QueryWrapper<Mawb>()
                .eq("waybill_code", item.getWaybillCode())
                .eq("dept_id", SecurityUtils.getHighParentId())
                .eq("is_del",0));
        BigDecimal decimal;
        if (item.getChargeItemId() == null){
            decimal = item.getRate().multiply(item.getChargeWeight());
        }else {
            if (item.getRateType() == 0){
                decimal = new BigDecimal(0);
                ChargeItem chargeItem = chargeItemMapper.selectById(item.getChargeItemId());
                if (chargeItem != null){
                    Integer defaultBillingMethod = chargeItem.getDefaultBillingMethod();
                    switch (defaultBillingMethod){
                        case 0:
                            decimal = BigDecimalRoundUtils.bigDecimalRound(chargeItem.getRoundRule(), item.getRate());
                            break;
                        case 1:
                            BigDecimal weight = item.getRate().multiply(item.getChargeWeight());
                            decimal = BigDecimalRoundUtils.bigDecimalRound(chargeItem.getRoundRule(),weight);
                            break;
                        case 2:
                            BigDecimal volume = item.getRate().multiply(item.getVolume());
                            decimal = BigDecimalRoundUtils.bigDecimalRound(chargeItem.getRoundRule(),volume);
                            break;
                        default:
                            break;
                    }
                }
            }else {
                decimal = item.getRate().multiply(item.getChargeWeight());
                FreightRateAirItem airItem = rateAirItemMapper.selectById(item.getChargeItemId());
                if (airItem != null){
                    FreightRateAir freightRateAir = rateAirMapper.selectById(airItem.getRateId());
                    // 取舍方式计算
                    decimal = BigDecimalRoundUtils.bigDecimalRound(freightRateAir.getRoundingMethod(), decimal);
                }
            }
        }
        BigDecimal sum = new BigDecimal(0);
        BigDecimal costSum = item.getCostSum() == null ? new BigDecimal(0) : item.getCostSum();
        if (item.getOldCharging() == null){
            sum = costSum.add(decimal.subtract(item.getCharging()));
            item.setCharging(item.getCharging());
        }
        if (item.getOldCharging().compareTo(item.getCharging()) < 0) {
            sum = costSum.add(item.getCharging().subtract(item.getOldCharging()));
            item.setCharging(item.getCharging());
        }
        if (item.getOldCharging().compareTo(item.getCharging()) > 0) {
            sum = costSum.subtract(item.getOldCharging().subtract(item.getCharging()));
            item.setCharging(item.getCharging());
        }
        if (item.getOldCharging().compareTo(item.getCharging()) == 0 && item.getOldRate().compareTo(item.getRate()) != 0) {
            sum = costSum.add(decimal.subtract(item.getCharging()));
            item.setCharging(decimal);
        }
        if (mawb != null) {
            mawb.setCostSum(sum);
            mawb.setUpdateTime(new Date());
            mawbMapper.updateById(mawb);
            vo.setCostSum(mawb.getCostSum());
        } else {
            vo.setCostSum(sum);
        }
        itemService.updateById(item);
        vo.setMawbItem(item);
        vo.setChargeWeight(item.getChargeWeight());
        return vo;
    }

    /**
     * 删除费用项
     *
     * @param item 分单收费项目
     * @return 返回结果
     */
    @Override
    public BigDecimal delFee(MawbItem item) {
        Mawb mawb = mawbMapper.selectOne(new QueryWrapper<Mawb>()
                .eq("waybill_code", item.getWaybillCode())
                .eq("dept_id", SecurityUtils.getHighParentId())
                .eq("is_del",0));
        if (item.getId() != null){
            MawbItem byId = itemService.getById(item.getId());
            byId.setIsDel(1);
            itemService.updateById(byId);
        }
        BigDecimal subtract = item.getCostSum().subtract(item.getCharging());
        if (subtract.compareTo(new BigDecimal(0)) <= 0){
            subtract = new BigDecimal(0);
        }
        if (mawb != null){
            mawb.setCostSum(subtract);
            mawb.setUpdateTime(new Date());
            mawbMapper.updateById(mawb);
        }
        return subtract;
    }

    /**
     * 更改拼单
     *
     * @param waybillCode 分单运单号
     * @param masterWaybillCode 分单关联主单号
     * @return 返回结果
     */
    @Override
    public int alter(String waybillCode, String masterWaybillCode) {

        return 0;
    }

    /**
     * 运单取消作废
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    @Override
    public int cancelVoid(String waybillCode) {
        if (waybillCode == null){
            throw new CustomException("运单号不能为空");
        }
        if (waybillCode.length() != 15 && waybillCode.length() != 16){
            throw new CustomException("运单号格式错误");
        }

        Mawb mawb = mawbMapper.selectOne(new QueryWrapper<Mawb>()
                .eq("waybill_code", waybillCode)
                .eq("dept_id", SecurityUtils.getHighParentId())
                .eq("is_del",0));

        if (mawb == null){
            throw new CustomException("无当前主单信息");
        }

        if (!"INVALID".equals(mawb.getStatus())){
            throw new CustomException("当前运单未处于作废状态");
        }

        String code = waybillCode.substring(0, 4);
        Integer ticketNum = Integer.valueOf(waybillCode.substring(7,14));
        Long ticketId = ticketMapper.selectCheck(code, waybillCode.substring(4,7), ticketNum);
        if (ticketId != null){
            TicketNum num = ticketNumMapper.selectOne(new QueryWrapper<TicketNum>().eq("ticket_id", ticketId).eq("num", ticketNum));
            if (num == null){
                throw new CustomException("当前运单未发放");
            }
            if (!SecurityUtils.getUserId().equals(num.getUseBy())){
                throw new CustomException("与当前运单领单人不符");
            }
            num.setStatus("NOTUSED");
            ticketNumMapper.updateById(num);
        }
        mawb.setStatus("staging");
        mawb.setPayStatus(0);
        mawb.setUpdateTime(new Date());
        return mawbMapper.updateById(mawb);
    }

    /**
     * 新增运单异常备注
     *
     * @param remark 运单异常备注
     * @return 返回结果
     */
    @Override
    public int addRemark(MawbErrorRemark remark) {
        Mawb mawb = mawbMapper.selectOne(new QueryWrapper<Mawb>()
                .eq("waybill_code", remark.getWaybillCode())
                .eq("dept_id", SecurityUtils.getHighParentId())
                .eq("is_del",0)
                .eq("waybill_type","AWBA")
                .eq("status", "been_sent"));
        if (mawb != null){
            remark.setWaybillId(mawb.getId());
        }
        remark.setRemarkTime(new Date());
        remark.setUserName(getUsername());
        boolean save = errorRemarkService.save(remark);
        if (save){
            return 1;
        }
        return 0;
    }

    /**
     * 根据运单号查询异常备注列表
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    @Override
    public List<MawbErrorRemark> remarkList(String waybillCode) {
        return errorRemarkService.list(new QueryWrapper<MawbErrorRemark>()
                .eq("waybill_code",waybillCode.substring(0,15))
                .eq("is_del",0));
    }

    /**
     * 根据代理人公司和code码查询代理人账号
     * @param agentCom 代理人公司
     * @param agentCode code码
     * @return 代理人账号
     */
    @Override
    public String agent(String agentCom, String agentCode) {
        return mawbMapper.selectAgent(agentCode,agentCom);
    }

    /**
     * 国内主单查询
     * @param query 查询条件
     * @return 结果
     */
    @Override
    public HawbQueryVo queryList(HawbQuery query) {
        HawbQueryVo vo = new HawbQueryVo();
        query.setDeptId(SecurityUtils.getHighParentId());
        Page<MawbQueryVo> page = new Page<>(1,-1);
//        Integer count = mawbMapper.queryListCount(query, page);
//        Integer quantity = mawbMapper.queryListQuantity(query, page);
//        BigDecimal weight = mawbMapper.queryListWeight(query, page);
        Integer count = 0;
        Integer quantity = 0;
        BigDecimal weight = new BigDecimal(0);
        Page<MawbQueryVo> mawbPageCount = mawbMapper.waybillQueryListNew(query,page);
        if (!CollectionUtils.isEmpty(mawbPageCount.getRecords())){
            for (MawbQueryVo mawb : mawbPageCount.getRecords()) {
                MawbQueryVo mawbQueryVo = mawbMapper.waybillQueryInfo(query, mawb);
                if(StringUtils.isNotNull(query.getStartTakeoffTime()) && StringUtils.isNull(mawbQueryVo)){
                    continue;
                }
                count ++;
                quantity = quantity + mawb.getQuantity();
                weight = weight.add(mawb.getWeight());
            }
        }
        Page<MawbQueryVo> pageOne = new Page<>(query.getPageNum(),query.getPageSize());
//        Page<MawbQueryVo> mawbPage = mawbMapper.queryListNew(query,pageOne);
        Page<MawbQueryVo> mawbPage = mawbMapper.waybillQueryListNew(query,pageOne);
        if (!CollectionUtils.isEmpty(mawbPage.getRecords())){
            List<MawbQueryVo> records = new ArrayList<>();
            for (MawbQueryVo mawb : mawbPage.getRecords()) {
                MawbQueryVo mawbQueryVo = mawbMapper.waybillQueryInfo(query, mawb);
                if(StringUtils.isNotNull(query.getStartTakeoffTime()) && StringUtils.isNull(mawbQueryVo)){
                    continue;
                }
                if(StringUtils.isNotNull(mawbQueryVo)){
                    mawb.setFlightPlanTime(mawbQueryVo.getFlightPlanTime());
                    mawb.setFlightFlyTime(mawbQueryVo.getFlightFlyTime());
                }
                BigDecimal totalCost = new BigDecimal(0);
                List<MawbItem> list = itemService.list(new QueryWrapper<MawbItem>()
                        .eq("waybill_id", mawb.getId())
                        .eq("is_del", 0));
                if (!CollectionUtils.isEmpty(list)){
                    List<MawbItem> items = list.stream().filter(e -> e.getRateType().equals(0)).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(items)){
                        List<MawbItem> fuelCostList = items.stream().filter(e -> e.getCostType().contains("燃油费")).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(fuelCostList)){
                            BigDecimal reduce = fuelCostList.stream().map(MawbItem::getCharging).reduce(BigDecimal.ZERO, BigDecimal::add);
                            mawb.setFuelCost(reduce);
                            totalCost = totalCost.add(reduce);
                        }
                        List<MawbItem> premiumList = items.stream().filter(e -> e.getCostType().contains("保险费")).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(premiumList)){
                            BigDecimal reduce = premiumList.stream().map(MawbItem::getCharging).reduce(BigDecimal.ZERO, BigDecimal::add);
                            mawb.setPremium(reduce);
                            totalCost = totalCost.add(reduce);
                        }
                    }
                    List<MawbItem> airItem = list.stream().filter(e -> e.getRateType().equals(1)).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(airItem)){
                        MawbItem item = airItem.get(0);
                        mawb.setAirRate(item.getRate());
                        mawb.setAirCost(item.getCharging());
                        totalCost = totalCost.add(item.getCharging());
                    }
                }
                mawb.setTotalCost(totalCost);
                records.add(mawb);
//                if(mawb.getFlightNo1()!=null && mawb.getFlightDate1()!=null){
//                    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
//                    String format1 = format.format(mawb.getFlightDate1());
//                    FlightInfo flightInfo = flightInfoMapper.selectOne(new QueryWrapper<FlightInfo>()
//                            .eq("air_ways", mawb.getFlightNo1().substring(0, 2))
//                            .eq("flight_no", mawb.getFlightNo1().substring(2))
//                            .eq("is_offin","D")
//                            .like("exec_date", format1));
//                    if(flightInfo!=null){
//                        mawb.setFlightPlanTime(flightInfo.getStartSchemeTakeoffTime());
//                        mawb.setFlightFlyTime(flightInfo.getStartRealTakeoffTime());
//                    }
//                }
            }
            mawbPage.setRecords(records);
            mawbPage.setTotal(count);
            vo.setTotalQuantity(quantity);
            vo.setTotalWeight(weight);
            vo.setTotalOrder(count);
            vo.setVos(mawbPage);
            return vo;
        }
        return vo;
    }

    /**
     * 打印主单标签
     * @param waybillCode 运单号
     * @return 主单标签数据
     */
    @Override
    public PrintMawbVo printMawb(String waybillCode) {
        return mawbMapper.printMawb(waybillCode);
    }

    /**
     * 查询可补货数据
     * @param waybillCode 原单单号
     * @return 结果
     */
    @Override
    public ReplenishVo replenish(String waybillCode) {
        if (waybillCode == null){
            throw new CustomException("原单单号不能为空");
        }
        return pullDownMapper.replenish(waybillCode);
    }

    @Override
    public void printMawbData(Long id, HttpServletResponse response) throws Exception{
        Mawb mawb = mawbMapper.selectById(id);
        List<MawbItem> list = itemService.list(new QueryWrapper<MawbItem>()
                .eq("waybill_code", mawb.getWaybillCode())
                .eq("dept_id",SecurityUtils.getHighParentId())
                .eq("is_del",0));
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        mawb.setWriteTimeStr(format.format(mawb.getWriteTime()));
        if (mawb.getFlightDate1() != null){
            mawb.setFlightDate1Str(format.format(mawb.getFlightDate1()));
        }
        if (mawb.getFlightDate2() != null){
            mawb.setFlightDate2Str(format.format(mawb.getFlightDate2()));
        }
        if (mawb.getWriteTime() != null){
             String format1 = format.format(mawb.getWriteTime());
             mawb.setWriteTimeStr(format1);
        }
        BaseAirportCode desPort = airportCodeMapper.selectByCode(mawb.getDesPort());
        mawb.setDesPort(desPort.getChineseName());
        mawb.setSourcePort("贵阳");
        mawb.setInsurance("无");
        mawb.setDeclaredValue("无");
        mawb.setGroundCost(new BigDecimal(0).setScale(2, RoundingMode.DOWN));
        mawb.setSizeVolume((mawb.getSize() == null ? "" : mawb.getSize()) + " " + (mawb.getVolume() == null ? "" : mawb.getVolume() +"m³"));
        if (!CollectionUtils.isEmpty(list)){
            List<MawbItem> airRates = list.stream().filter(e -> e.getRateType().equals(1)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(airRates)){
                MawbItem item = airRates.get(0);
                mawb.setAirCost(item.getCharging().setScale(2, RoundingMode.DOWN));
                mawb.setAirRate(item.getRate());
            }
            List<MawbItem> otherRates = list.stream().filter(e -> e.getRateType().equals(0)).collect(Collectors.toList());
            StringBuilder builder = new StringBuilder();
            for (MawbItem otherRate : otherRates) {
                builder.append(otherRate.getCostType()).append(": ").append(otherRate.getCharging().setScale(2, RoundingMode.DOWN)).append(" ");
            }
            mawb.setOtherInfo(builder.toString());
            if (!CollectionUtils.isEmpty(otherRates)){
                BigDecimal reduce = otherRates.stream().map(MawbItem::getCharging).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                mawb.setOtherCost(reduce.setScale(2, RoundingMode.DOWN));
            }
        }
        if (mawb.getAirCost() == null){
            mawb.setAirRate(new BigDecimal(0).setScale(2, RoundingMode.DOWN));
        }
        if (mawb.getOtherCost() == null){
            mawb.setOtherCost(new BigDecimal(0).setScale(2, RoundingMode.DOWN));
        }
        BigDecimal airCost = mawb.getAirCost() == null ? new BigDecimal(0) : mawb.getAirCost();
        BigDecimal otherCost = mawb.getOtherCost() == null ? new BigDecimal(0) : mawb.getOtherCost();
        mawb.setTotalCost(airCost.add(otherCost).setScale(2, RoundingMode.DOWN));
        if (mawb.getPrintAmount() == 0){
            mawb.setAirRate(new BigDecimal(0).setScale(2, RoundingMode.DOWN));
            mawb.setOtherCost(new BigDecimal(0).setScale(2, RoundingMode.DOWN));
            mawb.setTotalCost(new BigDecimal(0).setScale(2, RoundingMode.DOWN));
            mawb.setAirCost(new BigDecimal(0).setScale(2, RoundingMode.DOWN));
        }

        mawb.setPayMethod("现金");
        mawb.setAgentSign(SecurityUtils.getNickName());
        //主单打印不打印电子单号
        mawb.setWaybillCode(null);
        //重量去掉小数点
        mawb.setWeight(mawb.getWeight().setScale(0, RoundingMode.DOWN));
        mawb.setChargeWeight(mawb.getChargeWeight().setScale(0, RoundingMode.DOWN));
        //如果是补货单 打印要在品名后面加上补货两字
        if(mawb.getSwitchBill() == 2){
            mawb.setCargoName(mawb.getCargoName() + "(补货)");
        }
        if(mawb.getSwitchBill() == 3){
            mawb.setCargoName(mawb.getCargoName() + "(补重)");
        }
        ClassPathResource resource = new ClassPathResource("template/mawb.pdf");
        if (resource.exists()){
            String path = resource.getPath();
            byte[] bytes = PdfPrintHelper.getPdfDataFromTemplate(mawb,path);
            // 设置响应头
            response.reset();
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "inline; filename=example.pdf");
            // 获取输出流并写入字节数据
            response.getOutputStream().write(bytes);
            response.getOutputStream().flush();
        }
    }

    /**
     * 打印主单标签
     * @param waybillCode 主单号
     * @param response 返回流
     */
    @Override
    public void printLabel(String waybillCode, HttpServletResponse response, Integer quantity, Integer isWeight) throws Exception{
        String substring = waybillCode.substring(4);
        PrintMawbVo printMawbVo = mawbMapper.printMawb(waybillCode);
        BigDecimal decimal = printMawbVo.getWeight().setScale(0, RoundingMode.DOWN);
        //标签打印要求不要小数
        BigDecimal decimalNew = decimal.setScale(0, RoundingMode.DOWN);
        if (isWeight == 1){
            printMawbVo.setWeight(decimalNew);
        }else {
            printMawbVo.setWeight(null);
        }
        printMawbVo.setWaybillCode(substring);
        BaseCityCode baseCityCode = cityCodeMapper.selectCityByCode(printMawbVo.getDesPort());
        BaseAirportCode baseAirportCode = airportCodeMapper.selectByCode(printMawbVo.getDesPort());
        //12.26有些数据城市代码有 有些数据机场代码有 ...
        if (baseCityCode != null){
            printMawbVo.setDesPort(baseCityCode.getChineseName());
        }
        if (baseAirportCode != null){
            printMawbVo.setDesPort(baseAirportCode.getChineseName());
        }
        BaseCityCode baseCityCode1 = cityCodeMapper.selectCityByCode(printMawbVo.getDesPort());
        BaseAirportCode baseAirportCode1 = airportCodeMapper.selectByCode(printMawbVo.getDesPort());
        if (baseCityCode1 != null){
            printMawbVo.setSourcePort(baseCityCode1.getChineseName());
        }
        if (baseAirportCode1 != null){
            printMawbVo.setSourcePort(baseAirportCode1.getChineseName());
        }
        ClassPathResource resource = new ClassPathResource("template/label.pdf");
        setPagePdf(response, quantity, printMawbVo, resource);
    }

    /**
     * 打印主单标签
     * @param waybillCode 主单号
     * @param response 返回流
     */
    @Override
    public void printCurrencyLabel(String waybillCode, HttpServletResponse response, Integer quantity, Integer isWeight) throws Exception{
        Long deptId = SecurityUtils.getHighParentId();
        String substring = waybillCode.substring(4);
        String waybillCodeAbb = substring.substring(0, 3) + "-" + substring.substring(3);
        PrintMawbVo printMawbVo = mawbMapper.printMawb(waybillCode);
        BigDecimal decimal = printMawbVo.getWeight().setScale(0, RoundingMode.DOWN);
        BigDecimal decimalNew = decimal.setScale(0, RoundingMode.DOWN);
        //标签打印要求不要小数
        if (isWeight == 1){
            printMawbVo.setWeight(decimalNew);
        }else {
            printMawbVo.setWeight(null);
        }
        printMawbVo.setWaybillCode(waybillCodeAbb);
        BaseCityCode desCode = cityCodeMapper.selectCityByCode(printMawbVo.getDesPort());
        BaseAirportCode desCode2 = airportCodeMapper.selectByCode(printMawbVo.getDesPort());
        if (desCode != null){
            printMawbVo.setDesPort(desCode.getChineseName());
        }
        if (desCode2 != null){
            printMawbVo.setDesPort(desCode2.getChineseName());
        }
        BaseCityCode sourceCode = cityCodeMapper.selectCityByCode(printMawbVo.getSourcePort());
        BaseAirportCode sourceCode2 = airportCodeMapper.selectByCode(printMawbVo.getDesPort());
        if (sourceCode != null){
            printMawbVo.setSourcePort(sourceCode.getChineseName());
        }
        if (sourceCode2 != null){
            printMawbVo.setSourcePort(sourceCode2.getChineseName());
        }
        SysDept dept = sysDeptMapper.selectDeptById(deptId);
        if (StringUtils.isNotEmpty(dept.getLogoUrlSmall())){
            byte[] bytes = downloadFileFromUrl(dept.getLogoUrlSmall());
            String smallLogo = Base64.encode(bytes);
            printMawbVo.setLogoUrlSmall(smallLogo);
        }
        printMawbVo.setDeptName(dept.getDeptName());
        printMawbVo.setPhone(dept.getPhone());
        ClassPathResource resource = new ClassPathResource("template/currencyLabel.pdf");
        setPagePdf(response, quantity, printMawbVo, resource);
    }

    /**
     * 打印主单标签
     * @param waybillCode 主单号
     * @param response 返回流
     */
    @Override
    public void printHTLDLabel(String waybillCode, HttpServletResponse response, Integer quantity, Integer isWeight) throws Exception{
        String substring = waybillCode.substring(4);
        String waybillCodeAbb = substring.substring(0, 3) + "-" + substring.substring(3);
        PrintMawbVo printMawbVo = mawbMapper.printMawb(waybillCode);
        BigDecimal decimal = printMawbVo.getWeight().setScale(0, RoundingMode.DOWN);
        BigDecimal decimalNew = decimal.setScale(0, RoundingMode.DOWN);
        //标签打印要求不要小数
        if (isWeight == 1){
            printMawbVo.setWeight(decimalNew);
        }else {
            printMawbVo.setWeight(null);
        }
        printMawbVo.setWaybillCode(waybillCodeAbb);
        BaseCityCode desCode = cityCodeMapper.selectCityByCode(printMawbVo.getDesPort());
        BaseAirportCode desCode2 = airportCodeMapper.selectByCode(printMawbVo.getDesPort());
        if (desCode != null){
            printMawbVo.setDesPort(desCode.getChineseName());
        }
        if (desCode2 != null){
            printMawbVo.setDesPort(desCode2.getChineseName());
        }
        BaseCityCode sourceCode = cityCodeMapper.selectCityByCode(printMawbVo.getSourcePort());
        BaseAirportCode sourceCode2 = airportCodeMapper.selectByCode(printMawbVo.getDesPort());
        if (sourceCode != null){
            printMawbVo.setSourcePort(sourceCode.getChineseName());
        }
        if (sourceCode2 != null){
            printMawbVo.setSourcePort(sourceCode2.getChineseName());
        }
        ClassPathResource resource = new ClassPathResource("template/htld.pdf");
        setPagePdf(response, quantity, printMawbVo, resource);
    }

    private void setPagePdf(HttpServletResponse response, Integer quantity, PrintMawbVo printMawbVo, ClassPathResource resource) throws Exception {
        String s = "";
        //二维码回显的运单号不带杠
        if(printMawbVo.getWaybillCode().contains("-")){
            String[] split = printMawbVo.getWaybillCode().split("-");
            String s2 = split[0] + split[1];
            s = QRCodeGenerator.generateQRCodeBase64(s2);
        }else{
            s = QRCodeGenerator.generateQRCodeBase64(printMawbVo.getWaybillCode());
        }
        printMawbVo.setQrCode(s);
        List<byte[]> pages = new ArrayList<>();
        for (int page = 1; page <= quantity; page++) {
            if (resource.exists()) {
                String path = resource.getPath();
                String waybillCode = printMawbVo.getWaybillCode();
                if(waybillCode.endsWith("B")){
                    printMawbVo.setWaybillCode(waybillCode.substring(0, waybillCode.length() - 1));
                }
                printMawbVo.setCount(page);
                byte[] bytes = PdfPrintHelper.getPdfDataFromTemplate(printMawbVo, path);
                pages.add(bytes);
            }
        }

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        Document document = new Document();
        PdfCopy copy = new PdfCopy(document, baos);
        document.open();
        if (CollectionUtils.isEmpty(pages)) {
            if (resource.exists()) {
                PrintMawbVo printMawbVo1 = new PrintMawbVo();
                String path = resource.getPath();
                byte[] bytes = PdfPrintHelper.getPdfDataFromTemplate(printMawbVo1, path);
                try (ByteArrayInputStream bais = new ByteArrayInputStream(bytes)) {
                    PdfReader reader = new PdfReader(bais);
                    for (int i = 1; i <= reader.getNumberOfPages(); i++) {
                        PdfImportedPage page = copy.getImportedPage(reader, i);
                        copy.addPage(page);
                    }
                    reader.close();
                }
            }
        } else {
            for (byte[] pageContent : pages) {
                try (ByteArrayInputStream bais = new ByteArrayInputStream(pageContent)) {
                    PdfReader reader = new PdfReader(bais);
                    for (int i = 1; i <= reader.getNumberOfPages(); i++) {
                        PdfImportedPage page = copy.getImportedPage(reader, i);
                        copy.addPage(page);
                    }
                    reader.close();
                }
            }
        }
        document.close();
        // 设置响应头
        response.reset();
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "inline; filename=example.pdf");
        // 获取输出流并写入字节数据
        response.getOutputStream().write(baos.toByteArray());
        response.getOutputStream().flush();
    }

    @Override
    public List<MawbQueryVo> exportList(HawbQuery query) {
        query.setDeptId(SecurityUtils.getHighParentId());
        List<MawbQueryVo> mawbList = mawbMapper.queryList(query);
        if (!CollectionUtils.isEmpty(mawbList)) {
            for (MawbQueryVo mawb : mawbList) {
                BigDecimal totalCost = new BigDecimal(0);
                List<MawbItem> list = itemService.list(new QueryWrapper<MawbItem>()
                        .eq("waybill_id", mawb.getId())
                        .eq("is_del", 0));
                if (!CollectionUtils.isEmpty(list)) {
                    List<MawbItem> items = list.stream().filter(e -> e.getRateType().equals(0)).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(items)) {
                        List<MawbItem> fuelCostList = items.stream().filter(e -> e.getCostType().contains("燃油费")).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(fuelCostList)) {
                            BigDecimal reduce = fuelCostList.stream().map(MawbItem::getCharging).reduce(BigDecimal.ZERO, BigDecimal::add);
                            mawb.setFuelCost(reduce);
                            totalCost = totalCost.add(reduce);
                        }
                        List<MawbItem> premiumList = items.stream().filter(e -> e.getCostType().contains("保险费")).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(premiumList)) {
                            BigDecimal reduce = premiumList.stream().map(MawbItem::getCharging).reduce(BigDecimal.ZERO, BigDecimal::add);
                            mawb.setPremium(reduce);
                            totalCost = totalCost.add(reduce);
                        }
                    }
                    List<MawbItem> airItem = list.stream().filter(e -> e.getRateType().equals(1)).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(airItem)) {
                        MawbItem item = airItem.get(0);
                        mawb.setAirRate(item.getRate());
                        mawb.setAirCost(item.getCharging());
                        totalCost = totalCost.add(item.getCharging());
                    }
                }
                mawb.setTotalCost(totalCost);
            }
            MawbQueryVo vo = new MawbQueryVo();
            vo.setWaybillCode("合计");
            int quantity = mawbList.stream().mapToInt(MawbQueryVo::getQuantity).sum();
            vo.setQuantity(quantity);
            BigDecimal weight = mawbList.stream().map(MawbQueryVo::getWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setWeight(weight);
            BigDecimal chargeWeight = mawbList.stream().map(MawbQueryVo::getChargeWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setChargeWeight(chargeWeight);
            BigDecimal airRate = mawbList.stream().map(MawbQueryVo::getAirRate).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setAirRate(airRate);
            BigDecimal airCost = mawbList.stream().map(MawbQueryVo::getAirCost).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setAirCost(airCost);
            BigDecimal fuelCost = mawbList.stream().map(MawbQueryVo::getFuelCost).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setFuelCost(fuelCost);
            BigDecimal premium = mawbList.stream().map(MawbQueryVo::getPremium).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setPremium(premium);
            BigDecimal totalCost = mawbList.stream().map(MawbQueryVo::getTotalCost).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setTotalCost(totalCost);
            mawbList.add(vo);
        }
        return mawbList;
    }

    @Override
    public int notifyWaybillById(Long id) {
        Mawb mawb = mawbMapper.selectById(id);
        mawb.setIsNotify(1);
        mawb.setUpdateTime(new Date());
        return mawbMapper.updateById(mawb);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized int cancelPay(Long waybillId) {
        HttpServletResponse response = ServletUtils.getResponse();
        WaybillLog waybillLog = new WaybillLog();
        try {
            List<HzCollectWaybill> collectWaybills = collectWaybillMapper.selectList(new QueryWrapper<HzCollectWaybill>()
                    .eq("waybill_id", waybillId));
            if (!CollectionUtils.isEmpty(collectWaybills)) {
                throw new CustomException("运单已经收运不能取消支付");
            }
            Mawb airWaybill = mawbMapper.selectById(waybillId);
            if ("INVALID".equals(airWaybill.getStatus())){
                throw new CustomException("当前运单已作废");
            }
            BaseAgent agent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", airWaybill.getDeptId()));
            BigDecimal costSum = new BigDecimal(0);
            List<CostDetail> details = costDetailMapper.selectPayOrSettleList(airWaybill.getWaybillCode(), 0, 1, airWaybill.getDeptId());
            if (!CollectionUtils.isEmpty(details)) {
                costSum = details.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            for (CostDetail detail : details) {
                detail.setIsSettle(2);
                costDetailMapper.updateById(detail);
            }
            if (agent != null && agent.getSettleMethod() == 1) {
                BigDecimal balance = agent.getBalance() == null ? new BigDecimal(0) : agent.getBalance();
                BigDecimal subtract = balance.add(costSum);
                agent.setBalance(subtract);
                baseAgentMapper.updateBaseAgent(agent);
                BaseBalance baseBalance = new BaseBalance();
                baseBalance.setAgentId(agent.getId());
                baseBalance.setBalance(agent.getBalance());
                baseBalance.setType("增加余额");
                baseBalance.setCreateTime(new Date());
                baseBalance.setCreateBy(SecurityUtils.getNickName());
                // todo 流水号需从银联支付接口获取
                //baseBalance.setSerialNo();
                baseBalance.setTradeMoney(costSum);
                baseBalance.setWaybillCode(airWaybill.getWaybillCode());
                baseBalance.setRemark("取消支付退款");
                baseBalanceMapper.insertBaseBalance(baseBalance);
            }
            airWaybill.setPayStatus(14);
            airWaybill.setRefund(costSum);
            airWaybill.setSecuritySubmitWl(0);
            airWaybill.setSecuritySubmit(0);
            airWaybill.setUpdateTime(new Date());

            //运单日志的新增
            waybillLog = waybillLogService.getWaybillLog(
                    airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                    airWaybill.getWeight().toString(), airWaybill.getQuantity().toString(), airWaybill.getFlightNo1(),
                    airWaybill.getWaybillCode(), null, 0, null, new Date(),
                    "运单取消支付(代理人操作)", "DEP", null);
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作成功" +  "," +
                            "code:" + response.getStatus() + "," +
                            "data:" + 1));
            return mawbMapper.updateById(airWaybill);
        }catch(Exception e){
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" +  "," +
                            "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        }finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }

    /**
     * 更新运单安检申报单地址
     * @param vo 更新数据
     */
    @Override
    public void updateSecurityUrl(MawbVo vo) {
        mawbMapper.updateSecurityUrl(vo.getId(),vo.getSecurityUrl());
    }

    /**
     * 安检申报单打印
     * @param id 运单id
     * @param response 返回流
     */
    @Override
    public void printSecurityCheck(Long id, HttpServletResponse response) throws Exception {
        Mawb mawb = mawbMapper.selectById(id);

        //将目的站从英文转化为中文
        String desPortChinese = airportCodeService.selectChineseName(mawb.getDesPort());
        if(StringUtils.isNotEmpty(desPortChinese)){
            mawb.setDesPort(desPortChinese);
        }

        if (StringUtils.isNotEmpty(mawb.getDangerCode())){
            mawb.setWxp("YES");
        }else if (StringUtils.isNotEmpty(mawb.getSpecialCargoCode1())){
            mawb.setTzhw("YES");
        }else {
            mawb.setPthw("YES");
        }
        BaseAgent agent = agentMapper.selectBaseAgentByName(mawb.getAgentCompany());
        if (agent != null && StringUtils.isNotEmpty(agent.getSealUrl())){
            byte[] bytes = downloadFileFromUrl(agent.getSealUrl());
            String sealImage = Base64.encode(bytes);
            mawb.setSealUrl(sealImage);
        }
        SysDept dept = sysDeptMapper.selectDeptById(mawb.getDeptId());
        mawb.setShipper(dept.getDeptName());
        String substring = mawb.getWaybillCode().substring(4);
        String waybillCodeAbb = substring.substring(0, 3) + "-" + substring.substring(3);
        mawb.setWaybillCode(waybillCodeAbb);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        mawb.setFlightDate1Str(format.format(mawb.getFlightDate1()));
        ClassPathResource resource = new ClassPathResource("template/security.pdf");
        if (resource.exists()){
            String path = resource.getPath();
            byte[] bytes = PdfPrintHelper.getPdfDataFromTemplate(mawb,path);
            // 设置响应头
            response.reset();
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "inline; filename=example.pdf");
            // 获取输出流并写入字节数据
            response.getOutputStream().write(bytes);
            response.getOutputStream().flush();
        }
    }

    /**
     * 上传随附文件
     * @param vo 上传参数
     * @return 结果
     */
    @Override
    public int uploadTransportFile(TransportFileVo vo) {
        return mawbMapper.uploadTransportFile(vo);
    }

    /**
     * 查询随附文件
     * @param waybillId 运单id
     * @return 结果
     */
    @Override
    public TransportFileVo selectTransportFile(String waybillId) {
        return mawbMapper.selectTransportFile(waybillId);
    }

    /**
     * 根据主单id查询交接单id
     * @param waybillId 运单id
     * @return 结果
     */
    @Override
    public Transfer selectTransferIdByMawbId(String waybillId) {
        return transferWaybillMapper.selectTransferIdByMawbId(waybillId);
    }

    private byte[] downloadFileFromUrl(String urlStr) throws Exception {
        URL url = new URL(urlStr);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(5000);
        connection.setReadTimeout(5000);

        if (connection.getResponseCode() != 200) {
            throw new RuntimeException("Failed to connect, HTTP error code : " + connection.getResponseCode());
        }

        try (InputStream inputStream = connection.getInputStream();
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            return baos.toByteArray();
        }
    }

    /**
     * 运单作废
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    @Override
    public int invalid(String waybillCode) {
        if (waybillCode == null) {
            throw new CustomException("运单号不能为空");
        }
        if (waybillCode.length() != 15 && waybillCode.length() != 16) {
            throw new CustomException("运单号格式错误");
        }
        Mawb mawb = mawbMapper.selectOne(new QueryWrapper<Mawb>()
                .eq("waybill_code", waybillCode)
                .eq("dept_id", SecurityUtils.getHighParentId())
                .eq("is_del", 0));
        if (mawb == null) {
            throw new CustomException("无当前主单信息");
        }
        if ("INVALID".equals(mawb.getStatus())){
            throw new CustomException("当前运单已作废");
        }
        if(mawb.getPayStatus() > 0 && mawb.getPayStatus() < 14){
            throw new CustomException("运单已支付,作废失败");
        }
        List<HzCollectWaybill> collectWaybills = collectWaybillMapper.selectList(new QueryWrapper<HzCollectWaybill>()
                .eq("waybill_id", mawb.getId()));
        if (!CollectionUtils.isEmpty(collectWaybills)) {
            throw new CustomException("该运单已收运,作废失败");
        }
        List<LoadInfoVo> infoVos = flightLoadWaybillMapper.selectByLoadInfo(mawb.getId());
        if (infoVos.size() > 0) {
            throw new CustomException("该运单已配载,作废失败");
        }
        //运单日志的处理
        HttpServletResponse response = ServletUtils.getResponse();
        //运单日志的新增
        WaybillLog waybillLog = waybillLogService.getWaybillLog(
                waybillCode, 0, SecurityUtils.getNickName(),
                mawb.getWeight().toString(), mawb.getQuantity().toString(), mawb.getFlightNo1(),
                waybillCode, null, 0, null, new Date(),
                "运单作废", "DEP", null);
        try {
            String code = waybillCode.substring(0, 4);
            Integer ticketNum = Integer.valueOf(waybillCode.substring(7, 14));
            Long ticketId = ticketMapper.selectCheck(code, waybillCode.substring(4, 7), ticketNum);
            if (ticketId != null) {
                TicketNum num = ticketNumMapper.selectOne(new QueryWrapper<TicketNum>().eq("ticket_id", ticketId).eq("num", ticketNum));
                if (num == null) {
                    throw new CustomException("当前运单未发放");
                }
                if ("NOTGRANT".equals(num.getStatus())) {
                    throw new CustomException("当前运单未发放");
                }
                if (!SecurityUtils.getUserId().equals(num.getUseBy())) {
                    throw new CustomException("与当前运单领单人不符");
                }
                num.setStatus("INVALID");
                ticketNumMapper.updateById(num);
            }
            refund(mawb);
            WaybillTrace waybillTrace = new WaybillTrace();
            waybillTrace.setOperTime(new Date());
            waybillTrace.setOperPieces(mawb.getQuantity());
            waybillTrace.setOperWeight(mawb.getWeight());
            waybillTrace.setWaybillCode(mawb.getWaybillCode());
            waybillTrace.setNodeName("已作废");
            waybillTraceService.insertWaybillTrace(waybillTrace);
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作成功" + "," +
                            "code:" + response.getStatus() + "," +
                            "data:" + 1));
            return 1;
        } catch (Exception e) {
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" + "," +
                            "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        } finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }

    /**
     * 计算计费重量以及价格
     *
     * @param rateItem 计费区间费率
     * @param weight 实际重量
     * @return 自动运价返回参数
     */
    private FareVo getInterval(RateItem rateItem, BigDecimal weight){
        FareVo vo = new FareVo();
        // 初始化最接近的值为null
        BigDecimal closestValue = null;
        BigDecimal chargeWeight = null;
        BigDecimal rate = null;
        String type = null;
        BigDecimal multiply = new BigDecimal(0);
        String rateType = null;
        Map<RateItemVo, BigDecimal> lowest = lowest(rateItem);
        // (0,5)区间实际重量价格
        if (weight.compareTo(new BigDecimal(0)) > 0 && weight.compareTo(new BigDecimal(5)) < 0){
            multiply = weight.multiply(rateItem.getRateWeightRange5() == null ? new BigDecimal(0) : rateItem.getRateWeightRange5());
            rate = rateItem.getRateWeightRange5();
            rateType = "N";
        }

        // [5,10)区间实际重量价格
        if (weight.compareTo(new BigDecimal(5)) >= 0 && weight.compareTo(new BigDecimal(10)) < 0){
            multiply = weight.multiply(rateItem.getRateWeightRange10() == null ? new BigDecimal(0) : rateItem.getRateWeightRange10());
            rate = rateItem.getRateWeightRange10();
            rateType = "N";
        }

        // [10,45)区间实际重量价格
        if (weight.compareTo(new BigDecimal(10)) >= 0 && weight.compareTo(new BigDecimal(45)) < 0){
            multiply = weight.multiply(rateItem.getBaseRateN() == null ? new BigDecimal(0) : rateItem.getBaseRateN());
            rate = rateItem.getBaseRateN();
            rateType = "N";
        }

        // [45,100)区间实际重量价格
        if (weight.compareTo(new BigDecimal(45)) >= 0 && weight.compareTo(new BigDecimal(100)) < 0){
            multiply = weight.multiply(rateItem.getRateWeightRange45() == null ? new BigDecimal(0) : rateItem.getRateWeightRange45());
            rate = rateItem.getRateWeightRange45();
            rateType = "Q";
        }

        // [100,300)区间实际重量价格
        if (weight.compareTo(new BigDecimal(100)) >= 0 && weight.compareTo(new BigDecimal(300)) < 0){
            multiply = weight.multiply(rateItem.getRateWeightRange100() == null ? new BigDecimal(0) : rateItem.getRateWeightRange100());
            rate = rateItem.getRateWeightRange100();
            rateType = "Q";
        }

        // [300,500)区间实际重量价格
        if (weight.compareTo(new BigDecimal(300)) >= 0 && weight.compareTo(new BigDecimal(500)) < 0){
            multiply = weight.multiply(rateItem.getRateWeightRange300() == null ? new BigDecimal(0) : rateItem.getRateWeightRange300());
            rate = rateItem.getRateWeightRange300();
            rateType = "Q";
        }

        // [500,1000)区间实际重量价格
        if (weight.compareTo(new BigDecimal(500)) >= 0 && weight.compareTo(new BigDecimal(1000)) < 0){
            multiply = weight.multiply(rateItem.getRateWeightRange500() == null ? new BigDecimal(0) : rateItem.getRateWeightRange500());
            rate = rateItem.getRateWeightRange500();
            rateType = "Q";
        }

        if (weight.compareTo(new BigDecimal(1000)) >= 0){
            multiply = weight.multiply(rateItem.getRateWeightRange1000() == null ? new BigDecimal(0) : rateItem.getRateWeightRange1000());
            rate = rateItem.getRateWeightRange1000();
            rateType = "Q";
        }

        if (rateItem.getRateWeightRange5() == null
                && rateItem.getRateWeightRange10() == null
                && weight.compareTo(new BigDecimal(45)) <= 0){
            multiply = weight.multiply(rateItem.getBaseRateN() == null ? new BigDecimal(0) : rateItem.getBaseRateN());
            rate = rateItem.getBaseRateN();
            rateType = "N";
        }
        for (Map.Entry<RateItemVo, BigDecimal> bigDecimal : lowest.entrySet()) {
            RateItemVo key = bigDecimal.getKey();
            BigDecimal value = bigDecimal.getValue();
            boolean withinRange;
            if (key.getEndData() != null) {
                withinRange = weight.compareTo(key.getBeginData()) >= 0 && weight.compareTo(key.getEndData()) <= 0;
            } else {
                withinRange = weight.compareTo(key.getBeginData()) >= 0;
            }
            boolean beginDataGreaterThanWeight = key.getBeginData().compareTo(weight) >= 0;
            if ((withinRange || beginDataGreaterThanWeight)&& value.compareTo(multiply) <= 0) {
                if (closestValue == null || value.compareTo(closestValue) < 0) {
                    if (weight.compareTo(key.getBeginData()) >= 0 && weight.compareTo(key.getEndData()) <= 0){
                        chargeWeight = weight;
                        closestValue = multiply;
                        type = rateType;
                    }else {
                        chargeWeight = key.getBeginData();
                        closestValue = value;
                        rate = key.getRate();
                        type = key.getRateType();
                    }
                }
            }
        }
        vo.setCostSum(closestValue);
        vo.setChargeWeight(chargeWeight);
        vo.setRate(rate);
        vo.setRateType(type);
        return vo;
    }

    /**
     * 计算重量区间最低计费价格
     *
     * @param rateItem 计费区间费率
     * @return 计费区间价格
     */
    private Map<RateItemVo,BigDecimal> lowest(RateItem rateItem) {
        Map<RateItemVo,BigDecimal> map = new HashMap<>();
        // (0,5)区间最低价格
        BigDecimal multiply1 = new BigDecimal(1).multiply(rateItem.getRateWeightRange5() == null ? new BigDecimal(0) : rateItem.getRateWeightRange5());
        RateItemVo vo1 = new RateItemVo();
        vo1.setRate(rateItem.getRateWeightRange5());
        vo1.setBeginData(new BigDecimal(1));
        vo1.setEndData(new BigDecimal(5));
        vo1.setRateType("N");
        map.put(vo1,multiply1);

        // [5,10)区间最低价格
        BigDecimal multiply5 = new BigDecimal(5).multiply(rateItem.getRateWeightRange10() == null ? new BigDecimal(0) : rateItem.getRateWeightRange10());
        RateItemVo vo2 = new RateItemVo();
        vo2.setRate(rateItem.getRateWeightRange10());
        vo2.setBeginData(new BigDecimal(5));
        vo2.setEndData(new BigDecimal(10));
        vo2.setRateType("N");
        map.put(vo2,multiply5);

        // [10,45)区间最低价格
        BigDecimal multiply10 = new BigDecimal(10).multiply(rateItem.getBaseRateN() == null ? new BigDecimal(0) : rateItem.getBaseRateN());
        RateItemVo vo3 = new RateItemVo();
        vo3.setRate(rateItem.getBaseRateN());
        vo3.setBeginData(new BigDecimal(10));
        vo3.setEndData(new BigDecimal(45));
        vo3.setRateType("N");
        map.put(vo3,multiply10);

        // [45,100)区间最低价格
        BigDecimal multiply45 = new BigDecimal(45).multiply(rateItem.getRateWeightRange45() == null ? new BigDecimal(0) : rateItem.getRateWeightRange45());
        RateItemVo vo4 = new RateItemVo();
        vo4.setRate(rateItem.getRateWeightRange45());
        vo4.setBeginData(new BigDecimal(45));
        vo4.setEndData(new BigDecimal(100));
        vo4.setRateType("Q");
        map.put(vo4,multiply45);

        // [100,300)区间最低价格
        BigDecimal multiply100 = new BigDecimal(100).multiply(rateItem.getRateWeightRange100() == null ? new BigDecimal(0) : rateItem.getRateWeightRange100());
        RateItemVo vo5 = new RateItemVo();
        vo5.setRate(rateItem.getRateWeightRange100());
        vo5.setBeginData(new BigDecimal(100));
        vo5.setEndData(new BigDecimal(300));
        vo5.setRateType("Q");
        map.put(vo5,multiply100);

        // [300,500)区间最低价格
        BigDecimal multiply300 = new BigDecimal(300).multiply(rateItem.getRateWeightRange300() == null ? new BigDecimal(0) : rateItem.getRateWeightRange300());
        RateItemVo vo6 = new RateItemVo();
        vo6.setRate(rateItem.getRateWeightRange300());
        vo6.setBeginData(new BigDecimal(300));
        vo6.setEndData(new BigDecimal(500));
        vo6.setRateType("Q");
        map.put(vo6,multiply300);

        // [500,1000)区间最低价格
        BigDecimal multiply500 = new BigDecimal(500).multiply(rateItem.getRateWeightRange500() == null ? new BigDecimal(0) : rateItem.getRateWeightRange500());
        RateItemVo vo7 = new RateItemVo();
        vo7.setRate(rateItem.getRateWeightRange500());
        vo7.setBeginData(new BigDecimal(500));
        vo7.setEndData(new BigDecimal(1000));
        vo7.setRateType("Q");
        map.put(vo7,multiply500);

        // [1000,∞)区间最低价格
        BigDecimal multiply1000 = new BigDecimal(1000).multiply(rateItem.getRateWeightRange1000() == null ? new BigDecimal(0) : rateItem.getRateWeightRange1000());
        RateItemVo vo8 = new RateItemVo();
        vo8.setRate(rateItem.getRateWeightRange1000());
        vo8.setBeginData(new BigDecimal(1000));
        vo8.setEndData(new BigDecimal(Integer.MAX_VALUE));
        vo8.setRateType("Q");
        map.put(vo8,multiply1000);
        return map;
    }

    private static final Map<String, String> STATUSMAP = new HashMap<>();
    static {
        STATUSMAP.put("not_in", "been_sent");
        STATUSMAP.put("not_pre", "put_in");
        STATUSMAP.put("al_pre", "been_pre");
        STATUSMAP.put("fz_out", "been_out");
        STATUSMAP.put("pull_down", "pull_down");
        STATUSMAP.put("change_order", "order_change");
        STATUSMAP.put("a_in", "record_order");
        STATUSMAP.put("lh_comp", "tally_comp");
        STATUSMAP.put("ta_tx", "out_stock");
    }

    private static final HashMap<String,String> NODE_NAME = new HashMap<>();
    static {
        NODE_NAME.put("not_in","已发送");
        NODE_NAME.put("not_pre","货站入库");
        NODE_NAME.put("al_pre","已预配");
        NODE_NAME.put("fz_out","已出港");
        NODE_NAME.put("pull_down","临时拉下");
        NODE_NAME.put("change_order","已作废");
    }

    /**
     * 更换运单号
     * @param editWaybillCode 更换参数
     * @return 结果
     */
    @Override
    public int editWaybillCode(EditWaybillCode editWaybillCode) {
        if(!(editWaybillCode.getWaybillCode().length() == 16 && editWaybillCode.getWaybillCode().endsWith("B"))){
            check(editWaybillCode.getWaybillCode());
        }
        Mawb waybillCode = mawbMapper.selectOne(new QueryWrapper<Mawb>().eq("waybill_code", editWaybillCode.getWaybillCode()));
        if(waybillCode!=null){
            throw new CustomException("运单号已使用");
        }
        Mawb mawb = mawbMapper.selectById(editWaybillCode.getId());
        if(mawb.getPayStatus() > 0 && mawb.getPayStatus() < 14){
            throw new CustomException("运单已支付,更改失败");
        }
        List<HzCollectWaybill> collectWaybills = collectWaybillMapper.selectList(new QueryWrapper<HzCollectWaybill>()
                .eq("waybill_id", mawb.getId()));
        if (!CollectionUtils.isEmpty(collectWaybills)) {
            throw new CustomException("该运单已收运,更改失败");
        }
        List<LoadInfoVo> infoVos = flightLoadWaybillMapper.selectByLoadInfo(mawb.getId());
        if (infoVos.size() > 0) {
            throw new CustomException("该运单已配载,更改失败");
        }
        String code = mawb.getWaybillCode().substring(0, 4);
        Integer checkNum = Integer.valueOf(mawb.getWaybillCode().substring(mawb.getWaybillCode().length() - 1));
        Integer ticketNum = Integer.valueOf(mawb.getWaybillCode().substring(7,14));
        Long ticketId = ticketMapper.selectCheck(code, mawb.getWaybillCode().substring(4, 7), ticketNum);
        TicketNum num = ticketNumMapper.selectOne(new QueryWrapper<TicketNum>().eq("ticket_id", ticketId).eq("num", ticketNum));
        if (num != null) {
            if (!checkNum.equals(num.getCode())) {
                throw new CustomException("运单校验失败");
            }
            num.setStatus("NOTUSED");
            ticketNumMapper.updateById(num);
        }
        List<CostDetail> detailList = costDetailMapper.selectList(new QueryWrapper<CostDetail>()
                .eq("waybill_code", mawb.getWaybillCode()));
        detailList.forEach(e->{
            e.setWaybillCode(editWaybillCode.getWaybillCode());
            costDetailMapper.updateById(e);
        });
        mawb.setWaybillCode(editWaybillCode.getWaybillCode());
        mawb.setUpdateTime(new Date());
        return mawbMapper.updateById(mawb);
    }

    /**
     * 添加运价条目
     * @param query 添加参数
     * @return 结果
     */
    @Override
    public FareVo addItems(ItemQuery query) {
        FareVo vo = new FareVo();
        List<MawbItem> list = new ArrayList<>();
        Long deptId = SecurityUtils.getHighParentId();
        BigDecimal costSum = query.getCostSum() == null ? new BigDecimal(0) : query.getCostSum();
        if (CollectionUtils.isEmpty(query.getChargeItemIds())){
            return null;
        }
        List<MawbItem> items = itemService.list(new QueryWrapper<MawbItem>()
                .eq("waybill_code", query.getWaybillCode())
                .eq("is_del", 0));
        if (!CollectionUtils.isEmpty(items)){
            List<MawbItem> collect = items.stream().filter(e -> e.getRateType() == 0).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect)){
                list.addAll(collect);
            }
            List<MawbItem> collect1 = items.stream().filter(e -> e.getRateType() == 1).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect1)){
                itemService.removeById(collect1.get(0).getId());
            }
        }else {
            List<ChargeItem> chargeItem = chargeItemMapper.selectByDept(deptId,"AWBA");
            List<ChargeItem> collect = chargeItem.stream()
                    .filter(e -> StringUtils.isEmpty(e.getCode()) || query.getCarrier1().equals(e.getCode()))
                    .collect(Collectors.toList());
            for (ChargeItem item : collect) {
                BigDecimal decimal = new BigDecimal(0);
                Integer defaultBillingMethod = item.getDefaultBillingMethod();
                switch (defaultBillingMethod){
                    case 0:
                        decimal = BigDecimalRoundUtils.bigDecimalRound(item.getRoundRule(), item.getDefaultCostRate());
                        break;
                    case 1:
                        BigDecimal weight = item.getDefaultCostRate().multiply(vo.getChargeWeight() == null ? query.getWeight() : vo.getChargeWeight());
                        decimal = BigDecimalRoundUtils.bigDecimalRound(item.getRoundRule(),weight);
                        break;
                    case 2:
                        BigDecimal volume = item.getDefaultCostRate().multiply(query.getVolume());
                        decimal = BigDecimalRoundUtils.bigDecimalRound(item.getRoundRule(),volume);
                        break;
                    case 3:
                        BigDecimal quantity = item.getDefaultCostRate().multiply(new BigDecimal(query.getQuantity()));
                        decimal = BigDecimalRoundUtils.bigDecimalRound(item.getRoundRule(),quantity);
                    default:
                        break;
                }
                MawbItem mawbItem = new MawbItem();
                mawbItem.setWaybillCode(query.getWaybillCode());
                mawbItem.setChargeItemId(item.getId());
                mawbItem.setDeptId(item.getDeptId());
                mawbItem.setRate(item.getDefaultCostRate());
                mawbItem.setCostType(item.getName());
                mawbItem.setCharging(decimal);
                mawbItem.setRateType(0);
                list.add(mawbItem);
            }
        }
        if (query.getOldItemId() != null){
            FreightRateAirItem oldRateItem = rateAirItemMapper.selectById(query.getOldItemId());
            FreightRateAir oldRateAir = rateAirMapper.selectById(oldRateItem.getRateId());
            FareVo fareVo = getInterval(oldRateItem, query.getChargeWeight() == null ? query.getWeight() : query.getChargeWeight());
            BigDecimal bigDecimal = BigDecimalRoundUtils.bigDecimalRound(oldRateAir.getRoundingMethod(), fareVo.getCostSum());
            if (bigDecimal.compareTo(oldRateItem.getMinimumFreight()) <= 0 ){
                costSum = costSum.subtract(oldRateItem.getMinimumFreight());
            }else {
                costSum = costSum.subtract(bigDecimal);
            }
        }
        FreightRateAirItem rateAirItem = rateAirItemMapper.selectById(query.getChargeItemIds().get(0));
        FreightRateAir rateAir = rateAirMapper.selectById(rateAirItem.getRateId());
        FareVo multiply = getInterval(rateAirItem, query.getChargeWeight() == null ? query.getWeight() : query.getChargeWeight());
        vo.setRateType(multiply.getRateType());
        BigDecimal round = BigDecimalRoundUtils.bigDecimalRound(rateAir.getRoundingMethod(), multiply.getCostSum());
        MawbItem mawbItem = new MawbItem();
        mawbItem.setWaybillCode(query.getWaybillCode());
        mawbItem.setChargeItemId(rateAirItem.getId());
        mawbItem.setDeptId(deptId);
        mawbItem.setRate(multiply.getRate());
        mawbItem.setCostType(rateAir.getRateName());
        mawbItem.setRateType(1);
        if (round.compareTo(rateAirItem.getMinimumFreight()) <= 0 ){
            mawbItem.setCharging(rateAirItem.getMinimumFreight());
            list.add(mawbItem);
            costSum = costSum.add(rateAirItem.getMinimumFreight());
        }else {
            mawbItem.setCharging(round);
            list.add(mawbItem);
            costSum = costSum.add(round);
        }
        Mawb mawb = mawbMapper.selectOne(new QueryWrapper<Mawb>()
                .eq("waybill_code", query.getWaybillCode())
                .eq("dept_id",deptId)
                .eq("type", "DEP")
                .eq("is_del", 0));
        if (mawb != null){
            for (MawbItem item : list) {
                if (item.getId() != null){
                    itemService.updateById(item);
                }else {
                    itemService.save(item);
                }
            }
            mawb.setCostSum(costSum);
            mawb.setUpdateTime(new Date());
            mawbMapper.updateById(mawb);
        }
        vo.setItemId(query.getChargeItemIds().get(0));
        vo.setMawbItems(list);
        vo.setCostSum(costSum);
        vo.setChargeWeight(query.getChargeWeight());
        return vo;
    }

    private int isCargoCodeMatch(HzChargeIrRelation hzChargeRule, String waybillCargoCode) {
        if (StringUtils.isEmpty(hzChargeRule.getCargoName())) {
            return 1;
        }else if (StringUtils.isEmpty(hzChargeRule.getCategory())){
            if (hzChargeRule.getCargoName().contains(waybillCargoCode)){
                return 1;
            }
            return 0;
        }
        List<IrRelationVo> relationVos = Arrays.stream(hzChargeRule.getCargoName().split(","))
                .map(cargoCodeMapper::selectIrByName)
                .collect(Collectors.toList());

        List<IrRelationVo> cargoNames = Arrays.stream(hzChargeRule.getCategory().split(","))
                .flatMap(category -> cargoCodeMapper.selectListByCategory(Collections.singletonList(category)).stream())
                .collect(Collectors.toList());

        Map<String, List<IrRelationVo>> relationVosMap = relationVos.stream()
                .collect(Collectors.groupingBy(IrRelationVo::getCategoryCode));

        List<IrRelationVo> combinedList = new ArrayList<>();
        Set<String> addedCodes = new HashSet<>();
        for (IrRelationVo vo : cargoNames) {
            String code = vo.getCategoryCode();
            if (relationVosMap.containsKey(code) && !addedCodes.contains(code)) {
                combinedList.addAll(relationVosMap.get(code));
                addedCodes.add(code);
            } else if (!addedCodes.contains(code)) {
                combinedList.add(vo);
            }
        }
        List<String> collect1 = combinedList.stream().map(IrRelationVo::getCode).collect(Collectors.toList());
        if (collect1.stream().anyMatch(cargoCode -> cargoCode.equals(waybillCargoCode))) {
            String category = hzChargeRule.getCategory();
            StringBuilder stringBuffer = new StringBuilder();
            if(category != null){
                String[] split = category.split(",");
                for (String categoryCode:split) {
                    List<BaseCargoCode> cargoCodeList = cargoCodeMapper.selectList(new QueryWrapper<>(new BaseCargoCode())
                            .eq("category_code", categoryCode)
                            .eq("is_del",0));
                    if(cargoCodeList!=null && cargoCodeList.size() > 0){
                        for (BaseCargoCode e:cargoCodeList) {
                            stringBuffer.append(e.getCode());
                        }
                    }
                }
            }else{
                return 1;
            }
            if (stringBuffer.toString().contains(waybillCargoCode)) {
                return 2;
            } else {
                return 1;
            }
        }
        return 0;
    }

    /**
     * 消息时异常通知
     * @param flightNo 航班号
     */
    @Async
    public void sendMessage(String flightNo){
        String message = "已配载航班"+flightNo+"，有新的制单运单，请及时处理！";
        SocketMessageVo vo = new SocketMessageVo();
        vo.setMessage(message);
        vo.setType(9);
        Message mes = new Message();
        mes.setContent(message);
        mes.setCreateTime(new Date());
        mes.setIsHandle(0);
        mes.setPostId(SecurityUtils.getDeptId());
        mes.setTitle("配载航班制单提醒");
        messageMapper.insert(mes);
        waybillMessageProducer.send(vo);
    }

    public BillRuleVo countCost(CostDetail detail, BigDecimal weight, BigDecimal weightRate, Integer quantity) {
        HzChargeIrRelation relation = relationMapper.selectById(detail.getIrId());
        BillRuleVo vo = new BillRuleVo();
        if (relation == null){
            vo.setTotalCharge(new BigDecimal(0));
            return vo;
        }
        HzChargeItems hzChargeItems = itemsMapper.selectById(relation.getItemId());
        HzChargeRule hzChargeRule = ruleMapper.selectById(relation.getRuleId());
        List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>().eq("ir_id", detail.getIrId()));
        if (CollectionUtils.isEmpty(itemRules)){
            vo.setTotalCharge(new BigDecimal(0));
            return vo;
        }
        BillingRule rule = BillingRuleFactory.createRule(hzChargeRule.getClassName());
        BillRuleVo ruleVo = rule.calculateFee(itemRules, weightRate, quantity, detail);
        BigDecimal bigDecimal = BigDecimalRoundUtils.bigDecimalRound(hzChargeItems.getRoundRule(), ruleVo.getTotalCharge());
        detail.setQuantity(ruleVo.getQuantity());
        detail.setFlightId(3L);
        detail.setSettleDepQuantity(quantity);
        detail.setSettleDepWeight(weight);
        detail.setIsSettle(1);
        detail.setType(1);
        detail.setCreateTime(new Date());
        detail.setTotalCharge(bigDecimal);
        detail.setRate(ruleVo.getRate());
        detail.setId(null);
        costDetailMapper.insert(detail);
        return ruleVo;
    }

    private void updateStatus(Mawb mawb, BigDecimal costSum, WaybillFee waybillFee, Long parentId, Integer payStatus) {
        if (waybillFee != null) {
            waybillFee.setRefund(costSum);
            waybillFee.setSettleMoney(costSum);
            waybillFee.setSettleTime(new Date());
            waybillFee.setRefund(costSum);
            waybillFee.setStatus(2);
            feeMapper.updateById(waybillFee);
        } else {
            WaybillFee fee = new WaybillFee();
            fee.setRefund(costSum);
            fee.setSettleMoney(costSum);
            fee.setDeptId(parentId);
            fee.setSettleTime(new Date());
            fee.setWaybillCode(mawb.getWaybillCode());
            fee.setStatus(2);
            fee.setType("DEP");
            feeMapper.insert(fee);
        }
        mawb.setPayStatus(payStatus);
        mawb.setRefund(costSum);
        mawb.setSettleTime(new Date());
        mawb.setStatus("INVALID");
        mawb.setUpdateTime(new Date());
        mawbMapper.updateById(mawb);
    }
}
