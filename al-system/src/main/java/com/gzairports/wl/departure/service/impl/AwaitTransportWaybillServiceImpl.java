package com.gzairports.wl.departure.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzairports.common.basedata.domain.BaseAgent;
import com.gzairports.common.basedata.mapper.BaseAgentMapper;
import com.gzairports.common.business.arrival.domain.FlightLoadUldWaybill;
import com.gzairports.common.business.arrival.domain.FlightLoadWaybill;
import com.gzairports.common.business.arrival.mapper.FlightLoadUldWaybillMapper;
import com.gzairports.common.business.arrival.mapper.FlightLoadWaybillMapper;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.wl.departure.domain.query.AwaitTransportWaybillQuery;
import com.gzairports.wl.departure.domain.vo.ATWaybillVO;
import com.gzairports.wl.departure.domain.vo.AwaitTransportWaybillVO;
import com.gzairports.wl.departure.mapper.AwaitTransportWaybillMapper;
import com.gzairports.wl.departure.service.AwaitTransportWaybillService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 待运运单
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Service
public class AwaitTransportWaybillServiceImpl implements AwaitTransportWaybillService {

    @Autowired
    private AwaitTransportWaybillMapper mapper;

    @Autowired
    private BaseAgentMapper baseAgentMapper;

    @Autowired
    private FlightLoadUldWaybillMapper loadUldWaybillMapper;

    @Autowired
    private FlightLoadWaybillMapper loadWaybillMapper;

    @Override
    public AwaitTransportWaybillVO selectList(AwaitTransportWaybillQuery query) {
        Page<ATWaybillVO> page = new Page<>(query.getPageNum(), query.getPageSize());

        //查询符合条件的运单号
        List<String> waybillCodeList = mapper.selectAwaitTransportWaybillCode();
        if (StrUtil.isNotBlank(query.getWaybillCode())) {
            if (!waybillCodeList.contains(query.getWaybillCode())) {
                return null;
            }
            //查询用户指定的运单
            waybillCodeList = Collections.singletonList(query.getWaybillCode());
        }
        Long deptId = SecurityUtils.getLoginUser().getDeptId();
        String agent = Optional.ofNullable(baseAgentMapper.selectOne(Wrappers.lambdaQuery(BaseAgent.class).eq(BaseAgent::getDeptId, deptId)))
                .orElseGet(BaseAgent::new).getAgent();
        query.setShipper(agent);
        IPage<ATWaybillVO> atWaybillVOPage = mapper.listAwaitTransportWaybill(waybillCodeList, query, page);

        AwaitTransportWaybillVO awaitTransportWaybillVo = new AwaitTransportWaybillVO();
        List<ATWaybillVO> records = atWaybillVOPage.getRecords();
        for (ATWaybillVO record : records) {
            // 航班起飞才会有出港件数和运单
            int depQuantity = 0;
            BigDecimal depWeight = BigDecimal.ZERO;
            List<FlightLoadUldWaybill> loadUldWaybills = loadUldWaybillMapper.selectList(new QueryWrapper<FlightLoadUldWaybill>().eq("waybill_id", record.getId()));
            if (!CollectionUtils.isEmpty(loadUldWaybills)) {
                depQuantity = loadUldWaybills.stream().mapToInt(FlightLoadUldWaybill::getQuantity).sum();
                BigDecimal weight = loadUldWaybills.stream().map(FlightLoadUldWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                depWeight = depWeight.add(weight);
            }
            List<FlightLoadWaybill> loadWaybills = loadWaybillMapper.selectList(new QueryWrapper<FlightLoadWaybill>().eq("waybill_id", record.getId()));
            if (!CollectionUtils.isEmpty(loadWaybills)) {
                int quantity = loadWaybills.stream().mapToInt(FlightLoadWaybill::getQuantity).sum();
                depQuantity = depQuantity + quantity;
                BigDecimal weight = loadWaybills.stream().map(FlightLoadWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                depWeight = depWeight.add(weight);
            }
            record.setDepQuantity(depQuantity);
            record.setDepWeight(depWeight);
        }
        awaitTransportWaybillVo.setVos(records);
        awaitTransportWaybillVo.setTotal((int) atWaybillVOPage.getTotal());

        return awaitTransportWaybillVo;
    }
}
