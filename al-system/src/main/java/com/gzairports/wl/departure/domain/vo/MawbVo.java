package com.gzairports.wl.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.pdf.PdfPrintAnnotation;
import com.gzairports.wl.departure.domain.MawbErrorRemark;
import com.gzairports.wl.departure.domain.MawbItem;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static com.gzairports.common.pdf.PdfPrintAnnotation.PdfFieldType.*;

/**
 * 主运单详情参数
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
@Data
public class MawbVo {

    /** 主键id */
    private Long id;

    /** 分单id */
    private Long hawbId;

    /** 运单号 */
    private String waybillCode;

    /** 运单号 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "waybillCode")
    private String waybillCodeAbb;

    /** 运单类型 */
    private String waybillType;

    /** 交运货站 */
    private String shippingStation;

    /** 交运代理人 */
    private String shippingAgent;

    /** 换单 */
    private Integer switchBill;

    /** 进港中转 */
    private Integer transferBill;

    /** 原单单号 */
    private String originBill;

    /** 是否补货单 (运单类型 0 正常 1 换单 2补货 3补重) */
    private Integer replenishBill;

    /** 可补货重量 */
    private BigDecimal canRestockWeight;

    /** 补货重量 */
    private BigDecimal replenishWeight;

    /** 补货数量 */
    private Integer replenishNum;

    /** 是否跨航司承运 0 否 1 是 */
    private Integer crossAir;

    /** 状态 STAGING 暂存 NORMAL 正常 INVALID 作废*/
    private String status;

    /** 始发站 */
    private String sourcePort;

    /** 目的站 */
    private String desPort;

    /** 始发站 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "sourcePort")
    private String sourcePortStr;

    /** 目的站 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "desPort")
    private String desPortStr;

    /** 航班号 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "flightNo1")
    private String flightNo1;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date flightDate1;

    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "flightDate1")
    private String flightDate1Str;

    /** 航班号 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "flightNo2")
    private String flightNo2;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date flightDate2;

    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "flightDate2")
    private String flightDate2Str;

    /** 承运人1 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "carrier1")
    private String carrier1;

    /** 到达站1 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "des1")
    private String des1;

    /** 承运人2 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "carrier2")
    private String carrier2;

    /** 到达站2 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "des2")
    private String des2;

    /** 承运人3 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "carrier3")
    private String carrier3;

    /** 到达站3 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "des3")
    private String des3;

    /** 发货人简称 */
    private String shipperAbb;

    /** 发货人 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipper")
    private String shipper;

    /** 发货人 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipperStr")
    private String shipperStr;

    /** 发货人电话 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipperPhone")
    private String shipperPhone;

    /** 发货人地址 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipperAddress")
    private String shipperAddress;

    /** 发货人地区 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipperRegion")
    private String shipperRegion;

    /** 收货人简称 */
    private String consigneeAbb;

    /** 收货人 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "consign")
    private String consign;

    /** 收货人电话 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "consigneePhone")
    private String consigneePhone;

    /** 收货人地址 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "consigneeAddress")
    private String consigneeAddress;

    /** 收货人地区 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "consigneeRegion")
    private String consigneeRegion;

    /** 代理人公司 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "agentCompany")
    private String agentCompany;

    /** 代理人识别码 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "agentCode")
    private String agentCode;

    /** 城市 */
    private String city;

    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "rateType")
    private String rateType;

    /** 账号 */
    private String account;

    /** 结算注意事项 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "settlementNotes")
    private String settlementNotes;

    /** 储运注意事项 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "storageTransportNotes")
    private String storageTransportNotes;

    /** 海关监管 */
    private Integer customsSupervision;

    /** 公务单 */
    private Integer officialForm;

    /** 包量/包仓 */
    private Integer bulkWarehouse;

    /** 特货代码1 */
    private String specialCargoCode1;

    /** 特货代码2 */
    private String specialCargoCode2;

    /** 特货代码3 */
    private String specialCargoCode3;

    /** 其他特货代码 */
    private String otherSpecialCargoCode;

    /** 品名编码 12.4暂时不打印品名编码 */
//    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "cargoCode")
    private String cargoCode;

    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "cargoInfo")
    private String cargoInfo;

    /** 品名 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "cargoName")
    private String cargoName;

    /** 包装 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "pack")
    private String pack;

    /** 包装code */
    private String packCode;

    /** 件数 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "quantity")
    private Integer quantity;

    /** 重量 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "weight")
    private BigDecimal weight;

    /** 计费重量 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "chargeWeight")
    private BigDecimal chargeWeight;

    /** 体积（M3） */
    private BigDecimal volume;

    /** 尺寸 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "size")
    private String size;

    /** 长宽高 体积 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "sizeVolume")
    private String sizeVolume;

    /** 是否需要冷藏 */
    private String isCold;

    /** 冷藏库 */
    private String coldStore;

    /** 危险品UN编号 */
    private String dangerCode;

    /** 危险品类型 */
    private String dangerType;

    /** 紧急联系人 */
    private String emergentContact;

    /** 紧急联系人电话 */
    private String contactPhone;

    /** 费用总计 */
    private BigDecimal costSum;

    /** 填开时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date writeTime;

    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "writeTime")
    private String writeTimeStr;

    /** 填开地点 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "writeLocation")
    private String writeLocation;

    /** 填开人 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "writer")
    private String writer;

    /** 备注 */
    private String remark;

    /** 所属单位 */
    private Long deptId;

    /** 缴费状态 0 未授权支付 1 已授权支付 2 已结算 */
    private Integer payStatus;

    /** 支付时间 */
    private Date payTime;

    /** 电子分单pdf地址 */
    private String pdfUrl;

    /** 安检申报单地址 */
    private String securityUrl;

    /** 拼单状态 0 否 1 是 */
    private Integer mergeStatus;

    /** 是否删除 */
    private Integer isDel;

    /** 类型 DEP 出港 ARR 进港 */
    private String type;

    /** 运单出港费用列表 */
    private List<MawbItem> items;

    /** 运单异常状态备注 */
    private List<MawbErrorRemark> errorRemarks;

    @PdfPrintAnnotation(pdfFieldType = CHOICE, pdfFieldName = "pthw")
    private String pthw;

    @PdfPrintAnnotation(pdfFieldType = CHOICE, pdfFieldName = "tzhw")
    private String tzhw;

    @PdfPrintAnnotation(pdfFieldType = CHOICE, pdfFieldName = "wxp")
    private String wxp;

    @PdfPrintAnnotation(pdfFieldType = CHOICE, pdfFieldName = "hkkj")
    private String hkkj;

    @PdfPrintAnnotation(pdfFieldType = IMAGE, pdfFieldName = "sealUrl")
    private String sealUrl;

    @PdfPrintAnnotation(pdfFieldType = IMAGE, pdfFieldName = "sealUrl2")
    private String sealUrl2;

    @PdfPrintAnnotation(pdfFieldType = IMAGE, pdfFieldName = "airLogo")
    private String airLogo;

    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "insurance")
    private String insurance;

    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "declaredValue")
    private String declaredValue;

    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "otherInfo")
    private String otherInfo;

    /** 航空费用 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "airCost")
    private BigDecimal airCost;

    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "airRate")
    private BigDecimal airRate;

    /** 其他费用 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "otherCost")
    private BigDecimal otherCost;

    /** 总额 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "totalCost")
    private BigDecimal totalCost;

    /** 地面运费 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "groundCost")
    private BigDecimal groundCost;

    /** 付款方式 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "payMethod")
    private String payMethod;

    /** 托运人或其代理人签字、盖章 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "agentSign")
    private String agentSign;

    /** 是否打印金额 0不打印 1打印 */
    private Integer printAmount;

    /** 表wl_dep_merge_hawb_mawb的id */
    private String hawbMawbId;

    /** 是否配载 */
    private Integer isLoad;

    /** 版本号 */
    private Integer version;
}
