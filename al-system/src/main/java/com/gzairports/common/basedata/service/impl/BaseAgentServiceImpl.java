package com.gzairports.common.basedata.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gzairports.common.basedata.domain.BaseAgent;
import com.gzairports.common.basedata.domain.BaseBalance;
import com.gzairports.common.basedata.domain.query.BaseAgentQuery;
import com.gzairports.common.basedata.domain.vo.BalanceInfoVo;
import com.gzairports.common.basedata.mapper.BaseAgentMapper;
import com.gzairports.common.basedata.mapper.BaseBalanceMapper;
import com.gzairports.common.basedata.service.IBaseAgentService;
import com.gzairports.common.core.domain.PageQuery;
import com.gzairports.common.core.domain.entity.SysDictData;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.system.service.ISysDictDataService;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 代理人配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-17
 */
@Service
public class BaseAgentServiceImpl implements IBaseAgentService {
    @Autowired
    private BaseAgentMapper baseAgentMapper;

    @Autowired
    private BaseBalanceMapper baseBalanceMapper;

    @Autowired
    private ISysDictDataService sysDictDataService;

    /**
     * 查询代理人配置
     *
     * @param id 代理人配置主键
     * @return 代理人配置
     */
    @Override
    public BaseAgent selectBaseAgentById(Long id) {
        return baseAgentMapper.selectBaseAgentById(id);
    }

    /**
     * 查询代理人配置列表
     *
     * @param baseAgent 代理人配置
     * @return 代理人配置
     */
    @Override
    public List<BaseAgent> selectBaseAgentList(BaseAgent baseAgent) {
        List<BaseAgent> baseAgents = baseAgentMapper.selectBaseAgentList(baseAgent);
        if (!CollectionUtils.isEmpty(baseAgents)) {

        }
        return baseAgents;
    }

    /**
     * 查询代理人配置列表
     *
     * @param baseAgent 代理人配置
     * @return 代理人配置
     */
    public List<BaseAgent> selectExportBaseAgentList(BaseAgent baseAgent) {
        List<BaseAgent> baseAgents = baseAgentMapper.selectExportBaseAgentList(baseAgent);
        if (CollectionUtils.isEmpty(baseAgents)) {
            return Collections.emptyList();
        }

        //获取结算方式的字典数据
        SysDictData sysDictData = new SysDictData();
        sysDictData.setDictType("base_settle_method");
        List<SysDictData> dicList = sysDictDataService.selectDictDataList(sysDictData);
        Map<String, String> valueToLabel = dicList.stream().
                collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
        baseAgents.forEach(v -> {
            //设置结算方式字典值
            v.setSettleMethodStr(valueToLabel.getOrDefault("" + v.getSettleMethod(), "未知"));
            //账户余额为空时设置为 0
            if (v.getBalance() == null) {
                v.setBalance(new BigDecimal(0));
            }
            //账户余额为空时设置为 0
            if (v.getBalanceAlert() == null) {
                v.setBalanceAlert(new BigDecimal(0));
            }
        });
        return baseAgents;
    }

    /**
     * 新增代理人配置
     *
     * @param baseAgent 代理人配置
     * @return 结果
     */
    @Override
    public int insertBaseAgent(BaseAgent baseAgent) {
        BaseAgent agent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>()
                .eq("dept_id", baseAgent.getDeptId()));
        if (agent != null) {
            throw new CustomException("已存在当前代理人配置");
        }
        baseAgent.setCreateTime(DateUtils.getNowDate());
        return baseAgentMapper.insertBaseAgent(baseAgent);
    }

    /**
     * 修改代理人配置
     *
     * @param baseAgent 代理人配置
     * @return 结果
     */
    @Override
    public int updateBaseAgent(BaseAgent baseAgent) {
        baseAgent.setUpdateTime(DateUtils.getNowDate());
        return baseAgentMapper.updateBaseAgent(baseAgent);
    }

    /**
     * 批量删除代理人配置
     *
     * @param ids 需要删除的代理人配置主键
     * @return 结果
     */
    @Override
    public int deleteBaseAgentByIds(Long[] ids) {
        return baseAgentMapper.deleteBaseAgentByIds(ids);
    }

    /**
     * 删除代理人配置信息
     *
     * @param id 代理人配置主键
     * @return 结果
     */
    @Override
    public int deleteBaseAgentById(Long id) {
        return baseAgentMapper.deleteBaseAgentById(id);
    }

    /**
     * 增加余额
     *
     * @param query 增加参数
     * @return 结果
     */
    @Override
    public int addMoney(BaseAgentQuery query) {
        BaseAgent baseAgent = baseAgentMapper.selectBaseAgentById(query.getAgentId());
        // todo 流水号需从银联支付接口获取
//        baseBalance.setSerialNo();
        // todo 增加余额需要向银联接口新增
        BigDecimal balance = baseAgent.getBalance();
        if (balance != null) {
//            BigDecimal add = balance.add(query.getBalance());
            BigDecimal add = balance.add(query.getTradeMoney());
            baseAgent.setBalance(add);
        } else {
            baseAgent.setBalance(query.getBalance());
        }
        baseAgentMapper.updateBaseAgent(baseAgent);
        BaseBalance baseBalance = new BaseBalance();
        baseBalance.setAgentId(query.getAgentId());
//        baseBalance.setBalance(query.getBalance());
        baseBalance.setBalance(baseAgent.getBalance());
        baseBalance.setType("增加余额");
        baseBalance.setCreateTime(new Date());
        baseBalance.setCreateBy(SecurityUtils.getNickName());
        baseBalance.setVoucher(query.getVoucher());
        baseBalance.setTradeMoney(query.getTradeMoney());
        baseBalance.setRemark("增加余额");
        baseBalance.setAddType(1);
        baseBalance.setVoucherPDF(query.getVoucherPDF());
        return baseBalanceMapper.insertBaseBalance(baseBalance);
    }

    /**
     * 余额明细
     *
     * @param id 代理人配置id
     * @return 余额明细
     */
    @Override
    public BalanceInfoVo balanceInfo(Long id, Integer pageNum, Integer pageSize, String remark, Date startTime, Date endTime) {
        BalanceInfoVo vo = new BalanceInfoVo();
        BaseAgent baseAgent = baseAgentMapper.selectBaseAgentById(id);
        if (baseAgent != null) {
            vo.setAgent(baseAgent.getAgent());
            vo.setBalance(baseAgent.getBalance());
        }
        QueryWrapper<BaseBalance> wrapper = new QueryWrapper<BaseBalance>()
                .eq("agent_id", id)
                .gt("create_time", startTime)
                .lt("create_time", endTime);
        if(StrUtil.isNotBlank(remark)){
            wrapper.eq("remark", remark);
        }
        Integer agentCount = baseBalanceMapper.selectCount(wrapper);
        int start = (pageNum - 1) * pageSize;
        int end = Math.min(start + pageSize, agentCount);
        List<BaseBalance> list = baseBalanceMapper.selectList(wrapper.orderByDesc("create_time", "id"));
        if (!CollectionUtils.isEmpty(list)) {
            List<BaseBalance> balanceList = list.subList(start, end);
            vo.setBaseBalances(new PageQuery<List<BaseBalance>>(
                    balanceList,
                    pageNum,
                    pageSize,
                    agentCount
            ));
            vo.setTotal(agentCount);
            vo.setLastBalance(list.get(0).getBalance());
            Map<String, BigDecimal> typeToSum = list.stream().collect(
                    Collectors.toMap(
                            BaseBalance::getType,
                            e -> Optional.ofNullable(e.getTradeMoney()).orElse(new BigDecimal(0)),
                            BigDecimal::add)
            );
            vo.setAddBalance(typeToSum.get("增加余额"));
            vo.setSubtractBalance(typeToSum.get("减少余额"));
        }
        return vo;
    }

    /**
     * 根据代理公司名称查询代理人配置
     *
     * @param agentCompany 代理人公司
     * @return 结果
     */
    @Override
    public BaseAgent selectBaseAgentByName(String agentCompany) {
        return baseAgentMapper.selectBaseAgentByName(agentCompany);
    }

    @Override
    public List<BaseAgent> listAgentDeptIds(BaseAgent baseAgent) {
        Assert.notNull(baseAgent.getDeptId(),"代理人ID（deptId）不能为空");
        LambdaQueryWrapper<BaseAgent> agentLqw = Wrappers.lambdaQuery(BaseAgent.class)
                .eq(BaseAgent::getDeptId, baseAgent.getDeptId());

        BaseAgent currentBaseAgent = Optional.ofNullable(baseAgentMapper.selectOne(agentLqw))
                .orElseThrow(()-> new RuntimeException("该代理人不存在"));

        String[] deptIds = currentBaseAgent.getDeptIds().split(",");
        LambdaQueryWrapper<BaseAgent> inLqw = Wrappers.lambdaQuery(BaseAgent.class)
                .in(BaseAgent::getDeptId, Arrays.asList(deptIds));
        return baseAgentMapper.selectList(inLqw);
    }
}
