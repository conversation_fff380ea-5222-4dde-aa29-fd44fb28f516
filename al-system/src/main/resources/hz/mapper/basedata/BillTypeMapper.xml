<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.BillTypeMapper">
    <select id="selectTypeList" resultType="com.gzairports.common.basedata.domain.BaseBillType">
        select id, code, name, remark, domint from base_bill_type
    </select>

    <select id="getCodeByName" resultType="java.lang.String">
        select code from base_bill_type where name = #{name}
    </select>
</mapper>