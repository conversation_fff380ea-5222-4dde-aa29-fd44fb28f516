<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.arrival.mapper.QuickDeliveryMapper">
    <select id="selectListByQuery" resultType="com.gzairports.hz.business.arrival.domain.HzArrQuickDelivery">
        select * from hz_arr_quick_delivery
        <where>
            <if test="startTime != null">
                and end_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and end_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="agent != null and agent != ''">
                and agent like concat('%',#{agent},'%')
            </if>
        </where>
    </select>
    <select id="selectListValidity" resultType="com.gzairports.hz.business.arrival.domain.HzArrQuickDelivery">
        select id, agent, status, start_time, end_time, remark, agent_abb, dept_id
        from hz_arr_quick_delivery
        where status = 1 and
              NOW() BETWEEN start_time AND end_time
    </select>
</mapper>