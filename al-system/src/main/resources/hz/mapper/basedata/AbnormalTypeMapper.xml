<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.AbnormalTypeMapper">
    <insert id="insertAbnormalType" parameterType="baseAbnormalType">
        insert into base_abnormal_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="abnormalType != null and abnormalType != ''">abnormal_type,</if>
            <if test="chineseDescription != null and chineseDescription != ''">chinese_description,</if>
            <if test="englishDescription != null and englishDescription != ''">english_description,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="abnormalType != null and abnormalType != ''">#{abnormalType},</if>
            <if test="chineseDescription != null and chineseDescription != ''">#{chineseDescription},</if>
            <if test="englishDescription != null and englishDescription != ''">#{englishDescription},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>
    <update id="updateAbnormalType" parameterType="baseAbnormalType">
        update base_abnormal_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="abnormalType != null and abnormalType != ''">abnormal_type = #{abnormalType},</if>
            <if test="chineseDescription != null and chineseDescription != ''">chinese_description = #{chineseDescription},</if>
            <if test="englishDescription != null and englishDescription != ''">english_description = #{englishDescription},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null and createTime != ''">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="delAbnormalType" parameterType="long">
        update base_abnormal_type set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <select id="selectAbnormalTypeList" resultType="com.gzairports.common.basedata.domain.BaseAbnormalType">
        select id, abnormal_type, chinese_description, english_description, remark from base_abnormal_type
        <where>
            and is_del = 0
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="abnormalType != null  and abnormalType != ''"> and abnormal_type like concat('%',#{abnormalType},'%')</if>
            <if test="chineseDescription != null and chineseDescription != ''"> and chinese_description like concat('%',#{chineseDescription},'%')</if>
            <if test="englishDescription != null and englishDescription != ''"> and english_description like concat('%',#{englishDescription},'%')</if>
        </where>
    </select>
    <select id="selectAbnormalTypeByType" resultType="com.gzairports.common.basedata.domain.BaseAbnormalType">
        select id, abnormal_type, chinese_description, english_description, remark
        from base_abnormal_type
        where abnormal_type = #{abnormalType} and is_del = 0
    </select>
</mapper>