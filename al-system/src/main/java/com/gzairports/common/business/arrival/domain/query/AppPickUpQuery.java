package com.gzairports.common.business.arrival.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * app提货出库查询参数
 *
 * <AUTHOR>
 * @date 2024-07-22
 */
@Data
public class AppPickUpQuery {

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date execDate;

    /** 航班号 */
    private String code;

    /** 状态 */
    private String status;
}
