package com.gzairports.common.basedata.mapper;

import com.gzairports.common.basedata.domain.BaseAbnormalType;
import com.gzairports.common.basedata.domain.query.AbnormalTypeQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 不正常类型Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Mapper
public interface AbnormalTypeMapper {

    /**
     * 查询不正常类型列表
     *
     * @param query 查询参数
     * @return 不正常类型列表
     */
    List<BaseAbnormalType> selectAbnormalTypeList(AbnormalTypeQuery query);

    /**
     * 根据类型查询不正常类型
     *
     * @param abnormalType 不正常类型
     * @return 结果
     */
    BaseAbnormalType selectAbnormalTypeByType(String abnormalType);

    /**
     * 新增不正常类型
     *
     * @param type 不正常类型
     * @return 结果
     */
    int insertAbnormalType(BaseAbnormalType type);

    /**
     * 修改不正常类型
     *
     * @param type 不正常类型
     * @return 结果
     */
    int updateAbnormalType(BaseAbnormalType type);

    /**
     * 删除不正常类型
     *
     * @param ids 不正常类型id集合
     * @return 结果
     */
    int delAbnormalType(Long[] ids);
}
