package com.gzairports.common.business.arrival.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 已提货办单数据
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Data
public class ArrCostDetailVo {

    /** 总金额 */
    private BigDecimal totalMoney;

    /** 已支付金额 */
    private BigDecimal payMoney;

    /** 已结算金额 */
    private BigDecimal settleMoney;

    /**
     * 总数
     */
    private long total;

    /**
     * 每页显示条数
     */
    private long size;

    /**
     * 当前页
     */
    private long current;

    /** 已提货办单运单数据 */
    private List<ArrCostDetailListVo> vos;

}
