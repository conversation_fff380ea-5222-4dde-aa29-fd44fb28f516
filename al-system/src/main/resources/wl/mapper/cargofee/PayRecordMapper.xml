<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.wl.cargofee.mapper.PayRecordMapper">

    <select id="selectListByQuery" resultType="com.gzairports.wl.cargofee.domain.PayRecord">
        select id, money, payment_time, balance, union_pay, type, status, waybill_code, serial_no, remark from wl_pay_record
        <where>
            and dept_id = #{deptId}
            <if test="startTime != null">
                and payment_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and payment_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="serialNo != null and serialNo != ''">
                and serial_no like '%${serialNo}%'
            </if>
        </where>
        order by payment_time desc
    </select>
    <select id="selectListByQueryForBalance" resultType="com.gzairports.wl.cargofee.domain.PayRecord">
        select id, trade_money as money,create_time as payment_time,
               balance,voucher as  union_pay, type, serial_no,waybill_code,remark, agent_id as deptId
        from base_balance
        <where>
            <if test="deptId != null">
                and agent_id = #{deptId}
            </if>
            <if test="startTime != null">
                and create_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and create_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="serialNo != null and serialNo != ''">
                and serial_no like '%${serialNo}%'
            </if>
        </where>
        order by payment_time desc
    </select>
</mapper>