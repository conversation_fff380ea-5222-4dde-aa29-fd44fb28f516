package com.gzairports.common.charge.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 货站收费项目返回数据
 *
 * <AUTHOR>
 * @date 2024-06-14
 */
@Data
public class HzChargeItemsVo {

    /** 主键id */
    private Long id;

    /** 收费项目简称 */
    @Excel(name = "简称")
    private String chargeAbb;

    /** 收费项目名称 */
    @Excel(name = "收费项目名称")
    private String chargeName;

    /** 进出港类型 ARR 进港 DEP 出港 */
    private String operationType;

    /** 进出港类型 */
    @Excel(name = "进出港类型")
    private String type;

    /** 收费货物类别 */
    @Excel(name = "收费货物类别")
    private String categoryName;

    /** 是否默认收费 0 否 1 是 */
    private Integer isDefault;

    @Excel(name = "是否默认收费")
    private String defaultCharge;

    /** 有效日期开始 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startEffectiveTime;

    /** 有效日期开始 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endEffectiveTime;

    /** 是否可编辑 0 否 1 是 */
    private Integer isEdit;

    @Excel(name = "是否可编辑")
    private String edit;

    /** 收费规则id */
    private List<Long> chargeRuleId;

    /** 收费规则 */
    @Excel(name = "计费规则")
    private String ruleName;

    /** 状态 */
    private Integer status;

    /** 状态中文 */
    @Excel(name = "状态")
    private String statusStr;

    /** 是否服务项目收费 0 否 1 是 */
    private Integer isServiceItem;

    /** 是否服务项目收费 */
    @Excel(name = "是否服务项目收费")
    private String serviceItem;
}
