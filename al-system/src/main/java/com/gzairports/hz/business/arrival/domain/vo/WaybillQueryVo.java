package com.gzairports.hz.business.arrival.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 运单查询返回数据
 * <AUTHOR>
 * @date 2024-07-25
 */
@Data
public class WaybillQueryVo {

    /** 运单id */
    private Long waybillId;

    /** 运单号 */
    private String waybillCode;

    /** 补重单 */
    private Integer replenishBill;

    /** 文件到达 */
    private Integer fileArr;

    /** 预配航班 */
    private String flightNo1;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime flightDate1;

    /** 优先处理 */
    private Integer prioritize;

    /** 中转 */
    private Integer transferBill;

    /** 始发站 */
    private String sourcePort;

    /** 承运人1 */
    private String carrier1;

    /** 到达站1 */
    private String des1;

    /** 承运人2 */
    private String carrier2;

    /** 到达站2 */
    private String des2;

    /** 承运人3 */
    private String carrier3;

    /** 到达站3 */
    private String des3;

    /** 目的站 */
    private String desPort;

    /** 件数 */
    private Integer quantity;

    /** 舱单件数 */
    private Integer cabinQuantity;

    /** 重量 */
    private BigDecimal weight;

    /** 舱单重量 */
    private BigDecimal cabinWeight;

    /** 计费重量 */
    private BigDecimal chargeWeight;

    /** 代理人 */
    private String shipper;

    /** 收货人 */
    private String consign;

    /** 收货人证件号 */
    private String consignIdCar;

    /** 联系电话 */
    private String consignPhone;

    /** 品名 */
    private String cargoName;

    /** 特货代码 */
    private String specialCargoCode1;

    /** 冷藏 */
    private String coldStore;

    /** 海关监管 */
    private Integer customsSupervision;

    /** 转关 */
    private Integer isTransfer;

    /** 海关转关号 */
    private String transferNo;

    /** 到付 */
    private Integer arrPay;

    /** 到付费用 */
    private BigDecimal costSum;

    /** 备注 */
    private String remark;

    /** 填开时间 */
    private LocalDateTime orderTime;

    /** 库存 */
    private String inventoryWeight;
}
