package com.gzairports.common.enums;

/**
 * 业务操作类型
 * 
 * <AUTHOR>
 */
public enum BusinessType
{
    /**
     * 其它
     */
    OTHER,

    /**
     * 新增
     */
    INSERT,

    /**
     * 修改
     */
    UPDATE,

    /**
     * 删除
     */
    DELETE,

    /**
     * 授权
     */
    GRANT,

    /**
     * 导出
     */
    EXPORT,

    /**
     * 导入
     */
    IMPORT,

    /**
     * 强退
     */
    FORCE,

    /**
     * 生成代码
     */
    GENCODE,
    
    /**
     * 清空数据
     */
    CLEAN,

    /**
     * 入库
     */
    HOUSE,

    /**
     * 取消入库
     */
    WAREHOUSE,

    /**
     * 发放
     */
    PROVIDE,

    /**
     * 取消发放
     */
    CANCELGRANT,

    /**
     * 重新发放
     */
    REISSUE,

    /**
     * 销号
     */
    CANCEL,

    /**
     * 作废
     */
    INVALID,

    /**
     * 取消作废
     */
    CANCELVOID,
}
