package com.gzairports.common.business.departure.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 订舱审核记录表
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@Data
@TableName("wl_dep_booking_record")
public class BookingRecord {

    /** 主键id */
    private Long id;

    /** 订舱id */
    private Long bookingId;

    /** 批量审核订舱id */
    @TableField(exist = false)
    private List<Long> bookingIds;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date examineTime;

    /** 审核描述 */
    private String examineRemark;

    /** 审核状态 */
    private Integer status;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;
}
