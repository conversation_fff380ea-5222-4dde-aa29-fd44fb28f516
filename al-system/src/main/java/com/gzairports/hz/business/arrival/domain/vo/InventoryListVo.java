package com.gzairports.hz.business.arrival.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 库存管理运单列表数据
 * <AUTHOR>
 * @date 2024-07-26
 */
@Data
public class InventoryListVo {

    /** 运单id */
    private Long waybillId;

    /** 理货id */
    private Long tallyId;

    /** 提货id */
    private Long pickUpId;

    /** 运单号 */
    @Excel(name = "运单号")
    private String waybillCode;

    /** 开单时间 */
    @Excel(name = "开单时间",dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date writeTime;

    /** 收货代理人 */
    @Excel(name = "收货代理人")
    private String consign;

    /** 件数 */
    @Excel(name = "件数")
    private Integer quantity;

    /** 重量 */
    @Excel(name = "重量")
    private BigDecimal weight;

    /** 理货件数 */
    @Excel(name = "理货件数")
    private Integer tallyQuantity;

    /** 理货重量 */
    @Excel(name = "理货重量")
    private BigDecimal tallyWeight;

    /** 库存件数 */
    @Excel(name = "库存件数")
    private Integer inventoryQuantity;

    /** 库存重量 */
    @Excel(name = "库存重量")
    private BigDecimal inventoryWeight;

    /** 仓库 */
    @Excel(name = "仓库")
    private String store;

    /** 库位 */
    @Excel(name = "库位")
    private String locator;

    @Excel(name = "清仓结果")
    private String clearResult;
}
