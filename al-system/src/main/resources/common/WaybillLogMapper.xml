<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.log.mapper.WaybillLogMapper">

    <select id="isExistLog" resultType="java.lang.Boolean">
        SELECT CASE WHEN COUNT(*) > 0 THEN true ELSE false END
        WHERE waybill_code = #{waybillCode}
        AND flight_no = #{flightNo}
        AND remark like CONCAT('%',#{remark},'%')
    </select>
</mapper>