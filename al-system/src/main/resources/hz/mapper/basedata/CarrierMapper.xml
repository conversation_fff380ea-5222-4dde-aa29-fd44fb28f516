<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.CarrierMapper">
    <insert id="insertCarrier" parameterType="baseCarrier">
        insert into base_carrier
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="code != null and code != ''">code,</if>
            <if test="chineseName != null and chineseName != ''">chinese_name,</if>
            <if test="englishName != null and englishName != ''">english_name,</if>
            <if test="abbreviation != null and abbreviation != ''">abbreviation,</if>
            <if test="prefix != null and prefix != ''">prefix,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="logoUrl != null">logo_url,</if>
            <if test="isShipment != null">is_shipment,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="code != null and code != ''">#{code},</if>
            <if test="chineseName != null and chineseName != ''">#{chineseName},</if>
            <if test="englishName != null and englishName != ''">#{englishName},</if>
            <if test="abbreviation != null and abbreviation != ''">#{abbreviation},</if>
            <if test="prefix != null and prefix != ''">#{prefix},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="logoUrl != null">#{logoUrl},</if>
            <if test="isShipment != null">#{isShipment},</if>
        </trim>
    </insert>
    <update id="updateCarrier" parameterType="baseCarrier">
        update base_carrier
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="chineseName != null and chineseName != ''">chinese_name = #{chineseName},</if>
            <if test="englishName != null ">english_name = #{englishName},</if>
            <if test="englishName = ''">english_name = null,</if>
            <if test="abbreviation != null and abbreviation != ''">abbreviation = #{abbreviation},</if>
            <if test="prefix != null and prefix != ''">prefix = #{prefix},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null and createTime != ''">create_time = #{createTime},</if>
            <if test="logoUrl != null and logoUrl != ''">logo_url = #{logoUrl},</if>
            <if test="isShipment != null">is_shipment = #{isShipment},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="delCarrier" parameterType="long">
        update base_carrier set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <select id="selectCarrierList" resultType="com.gzairports.common.basedata.domain.BaseCarrier">
        select id, code, chinese_name, english_name, abbreviation, prefix, logo_url, is_shipment from base_carrier
        <where>
        and is_del = 0
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="code != null  and code != ''"> and code like concat('%', #{code}, '%')</if>
            <if test="chineseName != null and chineseName != ''"> and chinese_name like concat('%', #{chineseName}, '%')</if>
            <if test="englishName != null and englishName != ''"> and english_name like concat('%', #{englishName}, '%')</if>
            <if test="abbreviation != null and abbreviation != ''"> and abbreviation like concat('%', #{abbreviation}, '%')</if>
            <if test="prefix != null"> and prefix like concat('%', #{prefix}, '%')</if>
            <if test="isShipment != null"> and is_shipment = #{isShipment}</if>
        </where>
    </select>
    <select id="selectByCode" resultType="com.gzairports.common.basedata.domain.BaseCarrier">
        select id, code, chinese_name, english_name, abbreviation, prefix, is_shipment from base_carrier where code = #{code} and is_del = 0
    </select>
    <select id="selectCodeForSamePrefixByCode" resultType="java.lang.String">
        select code from base_carrier
        where prefix = (select prefix from base_carrier where code = #{code})
    </select>
</mapper>
