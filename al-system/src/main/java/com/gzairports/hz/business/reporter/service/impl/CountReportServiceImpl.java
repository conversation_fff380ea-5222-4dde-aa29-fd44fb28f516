package com.gzairports.hz.business.reporter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzairports.common.core.domain.entity.SysRole;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.poi.ReportExcelUtil;
import com.gzairports.hz.business.reporter.domain.*;
import com.gzairports.hz.business.reporter.domain.query.HzReportQuery;
import com.gzairports.hz.business.reporter.domain.vo.FieldKeyValue;
import com.gzairports.hz.business.reporter.domain.vo.JoinTable;
import com.gzairports.hz.business.reporter.domain.vo.ReportConfig;
import com.gzairports.hz.business.reporter.domain.vo.SortField;
import com.gzairports.hz.business.reporter.mapper.*;
import com.gzairports.hz.business.reporter.service.CountReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.script.Bindings;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-03-06
 */
@Service
public class CountReportServiceImpl implements CountReportService {

    @Autowired
    private HzCountReportMapper countReportMapper;

    @Autowired
    private HzReportSetMapper reportSetMapper;

    @Autowired
    private HzReportSetFilterMapper filterMapper;

    @Autowired
    private HzReportSetRelationMapper setRelationMapper;

    @Autowired
    private HzReportSetDisplayMapper displayMapper;

    @Autowired
    private HzReportSetDisplaySubtotalMapper displaySubtotalMapper;

    @Autowired
    private HzReportFieldMapper reportFieldMapper;

    @Autowired
    private HzReportSetCountMapper reportSetCountMapper;

    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\b[a-zA-Z_]\\w*\\b");


    private static final Set<String> ALLOWED_OPERATORS = new HashSet<String>();
    static {
        ALLOWED_OPERATORS.add("=");
        ALLOWED_OPERATORS.add("!=");
        ALLOWED_OPERATORS.add(">");
        ALLOWED_OPERATORS.add("<");
        ALLOWED_OPERATORS.add(">=");
        ALLOWED_OPERATORS.add("<=");
        ALLOWED_OPERATORS.add("LIKE");
        ALLOWED_OPERATORS.add("BETWEEN");
        ALLOWED_OPERATORS.add("IN");
    }


    /**
     * 统计报表信息
     * @return 结果
     */
    @Override
    public List<HzReportSet> selectReportTitle() {
        List<HzReportSet> hzReportSets = reportSetMapper.selectReportTitle();
        List<HzReportSet> list = new ArrayList<>();
        List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
        List<String> roleIdList = roles.stream().map(SysRole::getRoleId).map(String::valueOf).collect(Collectors.toList());
        for (HzReportSet hzReportSet : hzReportSets) {
            List<String> setRoleIds = Arrays.asList(hzReportSet.getReportRole().split(","));
            if(!Collections.disjoint(setRoleIds, roleIdList) || setRoleIds.contains("2")/*任何人*/){
                list.add(hzReportSet);
            }
        }
        return list;
    }

    /**
     * 获取查询字段
     * @param setId 报表设置id
     * @return 查询字段
     */
    @Override
    public List<HzReportSetFilter> getFilter(Long setId) {
        return filterMapper.getFilter(setId);
    }

    /**
     * 获取表格流
     * @param query 查询参数
     * @param response 返回流
     */
    @Override
    public void getReportExcel(HzReportQuery query, HttpServletResponse response) throws IOException {
        ReportConfig config = new ReportConfig();
        HzReportSetRelation hzReportSetRelation = setRelationMapper.selectOne(new QueryWrapper<HzReportSetRelation>().eq("set_id", query.getSetId()));
        HzReportSet hzReportSet = reportSetMapper.selectById(query.getSetId());
        // 主表名
        config.setMainTable(TABLE_MAP.get(hzReportSetRelation.getMasterTable()));

        // 关联子表
        List<JoinTable> joinTables = new ArrayList<>();
        if (hzReportSetRelation.getSlaveTable() != null){
            JoinTable slaveTable = new JoinTable();
            slaveTable.setTableName(TABLE_MAP.get(hzReportSetRelation.getSlaveTable()));
            slaveTable.setJoinCondition(hzReportSetRelation.getRelationFieldName());
            joinTables.add(slaveTable);
        }
        if (hzReportSetRelation.getSlaveTable2() != null){
            JoinTable slaveTable2 = new JoinTable();
            slaveTable2.setTableName(TABLE_MAP.get(hzReportSetRelation.getSlaveTable2()));
            slaveTable2.setJoinCondition(hzReportSetRelation.getRelationFieldName2());
            joinTables.add(slaveTable2);
        }
        for (int i = 0; i < joinTables.size(); i++) {
            JoinTable joinTable = joinTables.get(i);
            String alias = joinTable.getTableName() + "_" + i;
            joinTable.setAlias(alias);
            joinTable.setJoinConditionWithAlias("main." + joinTable.getJoinCondition() + " = " + alias + "." + joinTable.getJoinCondition());
        }
        config.setJoinTables(joinTables);

        List<HzReportSetDisplay> displayList = displayMapper.selectFieldList(query.getSetId());
        if (!displayList.isEmpty()){
            for (int i = 0; i < displayList.size(); i++) {
                HzReportSetDisplay formulaField = displayList.get(i);
                if (StringUtils.isNotEmpty(formulaField.getDesignFormulas()) && !formulaField.getDesignFormulas().equals(formulaField.getFieldName())){
                    String resultField = formulaField.getFieldName() + "_" + i;
                    formulaField.setFieldName(resultField);
                }
            }
            // 查询字段
            List<String> displayFields = displayList.stream().filter(e->(e.getDesignFormulas() == null || e.getFieldName().equals(e.getDesignFormulas())))
                    .sorted(Comparator.comparing(HzReportSetDisplay::getFieldIndex))
                    .map(d -> {
                        String tableAlias;
                        switch (d.getTableField()) {
                            case 0:
                                tableAlias = "main";
                                break;
                            case 1:
                                tableAlias = joinTables.get(0).getAlias();
                                break;
                            case 2:
                                tableAlias = joinTables.get(1).getAlias();
                                break;
                            default:
                                throw new CustomException("Invalid tableIField value: " + d.getTableField());
                        }
                        return tableAlias + "." + d.getFieldName(); // 格式如：main.id, user_0.name
                    })
                    .collect(Collectors.toList());
            config.setDisplayFields(displayFields);

            // 查询字段中文
            List<String> displayFieldsCn = displayList.stream().sorted(Comparator.comparing(HzReportSetDisplay::getFieldIndex)).map(HzReportSetDisplay::getFieldNameCn).collect(Collectors.toList());
            config.setDisplayFieldsCn(displayFieldsCn);

            // 是否只展示小计字段
            List<String> isSubtotalList = displayList.stream().filter(e->e.getIsSubtotal() == 1).map(HzReportSetDisplay::getFieldName).collect(Collectors.toList());
            config.setIsSubtotalList(isSubtotalList);

            // 小计字段
            List<HzReportSetDisplay> subtotalFields = displayList.stream().filter(e->e.getFieldSortSubtotal() != null && e.getFieldSortSubtotal() == 1).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(subtotalFields)){
                // 小计分组字段
                List<String> collect = subtotalFields.stream().map(HzReportSetDisplay::getFieldName).collect(Collectors.toList());
                config.setSubtotalFields(collect);
                List<Long> setDisplayIds = subtotalFields.stream().map(HzReportSetDisplay::getId).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(setDisplayIds)){
                    List<HzReportSetDisplaySubtotal> displaySubtotals = displaySubtotalMapper.selectList(new QueryWrapper<HzReportSetDisplaySubtotal>().in("set_display_id", setDisplayIds));
                    if (!CollectionUtils.isEmpty(displaySubtotals)){
                        // 小计字段
                        config.setSubtotalFieldList(displaySubtotals.stream().map(HzReportSetDisplaySubtotal::getFieldName).collect(Collectors.toList()));
                    }
                }
            }


            // 计算字段
            List<HzReportSetDisplay> formulasFields = displayList.stream().filter(e -> StringUtils.isNotEmpty(e.getDesignFormulas())).collect(Collectors.toList());
            config.setFormulasFields(formulasFields);

            // 合并字段
            List<String> mergeFields = displayList.stream().filter(e->e.getFieldSortMerge() != null && e.getFieldSortMerge() == 1).map(HzReportSetDisplay::getFieldNameCn).collect(Collectors.toList());
            config.setMergeFields(mergeFields);

            // 排序字段
            List<SortField> sortFieldList = new ArrayList<>();
            List<HzReportSetDisplay> sortFields = displayList.stream()
                    .filter(e -> e.getFieldSort() != null && e.getFieldSort() == 1)
                    .collect(Collectors.toList());
            for (HzReportSetDisplay sortField : sortFields) {
                SortField field = new SortField();
                String tableAlias;
                switch (sortField.getTableField()) {
                    case 0:
                        tableAlias = "main";
                        break;
                    case 1:
                        tableAlias = joinTables.get(0).getAlias();
                        break;
                    case 2:
                        tableAlias = joinTables.get(1).getAlias();
                        break;
                    default:
                        throw new IllegalArgumentException("Invalid tableIField value: " + sortField.getTableField());
                }
                field.setField(tableAlias + "." + sortField.getFieldName());
                field.setDirection(sortField.getFieldSortType());
                sortFieldList.add(field);
            }
            config.setSortFields(sortFieldList);
        }
        if (query.getKeyValueList() != null){
            for (FieldKeyValue fieldKeyValue : query.getKeyValueList()) {
                fieldKeyValue.setFieldName("main." + fieldKeyValue.getFieldName());
            }
            for (FieldKeyValue fieldKeyValue : query.getKeyValueList()) {
                switch (fieldKeyValue.getFieldFilterType()){
                    case "0":
                        fieldKeyValue.setFieldFilterType("=");
                        break;
                    case "1":
                        fieldKeyValue.setFieldFilterType(">");
                        break;
                    case "2":
                        fieldKeyValue.setFieldFilterType("<");
                        break;
                    case "3":
                        fieldKeyValue.setFieldFilterType("BETWEEN");
                        break;
                    case "4":
                        fieldKeyValue.setFieldFilterType("LIKE");
                        break;
                    case "5":
                        fieldKeyValue.setFieldFilterType("IN");
                        String fieldValue = fieldKeyValue.getFieldValue();
                        String[] split = fieldValue.split(",");
                        fieldKeyValue.setSelectValue(Arrays.asList(split));
                        break;
                    default:
                        fieldKeyValue.setFieldFilterType(" ");
                        break;
                }
                validateFilter(fieldKeyValue);
            }
        }
        config.setFilters(query.getKeyValueList());
        validateConfig(config, hzReportSetRelation.getMasterTable(), hzReportSetRelation.getSlaveTable(), hzReportSetRelation.getSlaveTable2());
        Page<Map<String,Object>> page = new Page<>(query.getPageNum(), query.getPageSize());
        Long totalCount = countReportMapper.countReportTotal(config);
        if (totalCount == null) {
            totalCount = 0L;
        }
        page.setTotal(totalCount);
        page.setSearchCount(false);
        List<Map<String, Object>> processedData;
        if (!CollectionUtils.isEmpty(config.getIsSubtotalList())){
            Page<Map<String,Object>> allPage = new Page<>(1, -1);
            Page<Map<String,Object>> allCountReport = countReportMapper.generateReport(config,allPage);
            processedData = allCountReport.getRecords();
            getAmountTo(query, processedData, processedData);
        }else {
            Page<Map<String,Object>> countReportData = countReportMapper.generateReport(config,page);
            processedData = countReportData.getRecords();
            if (totalCount > 0){
                int totalPages = (int) Math.ceil((double) totalCount / query.getPageSize());
                if (totalPages == query.getPageNum() || totalPages <= 0) {
                    Page<Map<String,Object>> allPage = new Page<>(1, -1);
                    Page<Map<String,Object>> allCountReport = countReportMapper.generateReport(config,allPage);
                    List<Map<String, Object>> records = allCountReport.getRecords();
                    getAmountTo(query, processedData, records);
                }
            }
        }
        List<Map<String, Object>> reportData = applyFormula(processedData, config.getFormulasFields());
        if (!reportData.isEmpty()){
            if (!CollectionUtils.isEmpty(config.getSubtotalFields()) && !CollectionUtils.isEmpty(config.getSubtotalFieldList())) {
                reportData = addSubtotal(reportData, config.getSubtotalFields(), config.getSubtotalFieldList(), config.getIsSubtotalList());
            }
            List<Map<String, Object>> maps = convertKeysToChinese(reportData, displayList);
            List<Map<String, Object>> orderedMaps = new ArrayList<>();
            int startIndex = 1;
            if (query.getPageSize() != -1){
                startIndex = (query.getPageNum() - 1) * query.getPageSize() + 1;
            }
            AtomicInteger counter = new AtomicInteger(0);
            for (Map<String, Object> map : maps) {
                Map<String, Object> orderedMap = new LinkedHashMap<>();
                if (map.get("序号") == null){
                    orderedMap.put("序号", startIndex + counter.getAndIncrement());
                }
                orderedMap.putAll(map);
                orderedMaps.add(orderedMap);
            }
            if (!CollectionUtils.isEmpty(config.getIsSubtotalList()) && query.getPageSize() > 0){
                orderedMaps = getPage(orderedMaps, query.getPageNum(), query.getPageSize());
            }
            List<String> headers = buildExcelHeader(displayList);
            try {
                ReportExcelUtil reportExcelUtil = new ReportExcelUtil();
                reportExcelUtil.exportToHzExcel(response, orderedMaps, headers, hzReportSet.getReportTitle(), config.getMergeFields());
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 合计
     * @param query 查询条件
     * @param processedData 数据
     * @param records 数据
     */
    private void getAmountTo(HzReportQuery query, List<Map<String, Object>> processedData, List<Map<String, Object>> records) {
        List<HzReportSetCount> countFields = reportSetCountMapper.selectList(new QueryWrapper<HzReportSetCount>().eq("set_id", query.getSetId()));
        if (!countFields.isEmpty()) {
            List<String> collect = countFields.stream().map(HzReportSetCount::getFieldName).collect(Collectors.toList());
            Map<String, BigDecimal> totalReport = new HashMap<>();
            collect.forEach(field -> totalReport.put(field, BigDecimal.ZERO));
            records.forEach(row -> {
                collect.forEach(field -> {
                    Object value = row.get(field);
                    if (value != null) {
                        BigDecimal num = new BigDecimal(value.toString());
                        totalReport.put(field, totalReport.get(field).add(num));
                    }
                });
            });
            Map<String, Object> totalRow = new HashMap<>();
            collect.forEach(field ->
                    totalRow.put(field, totalReport.get(field))
            );
            totalRow.put("合计：", true);
            // 添加合计行到结果末尾
            processedData.add(totalRow);
        }
    }

    /**
     * 分页查询数据
     * @param query 查询参数
     * @return 结果
     */
    @Override
    public Integer pageQuery(HzReportQuery query) {
        ReportConfig config = new ReportConfig();
        HzReportSetRelation hzReportSetRelation = setRelationMapper.selectOne(new QueryWrapper<HzReportSetRelation>().eq("set_id", query.getSetId()));
        // 主表名
        config.setMainTable(TABLE_MAP.get(hzReportSetRelation.getMasterTable()));

        // 关联子表
        List<JoinTable> joinTables = new ArrayList<>();
        if (hzReportSetRelation.getSlaveTable() != null){
            JoinTable slaveTable = new JoinTable();
            slaveTable.setTableName(TABLE_MAP.get(hzReportSetRelation.getSlaveTable()));
            slaveTable.setJoinCondition(hzReportSetRelation.getRelationFieldName());
            joinTables.add(slaveTable);
        }
        if (hzReportSetRelation.getSlaveTable2() != null){
            JoinTable slaveTable2 = new JoinTable();
            slaveTable2.setTableName(TABLE_MAP.get(hzReportSetRelation.getSlaveTable2()));
            slaveTable2.setJoinCondition(hzReportSetRelation.getRelationFieldName2());
            joinTables.add(slaveTable2);
        }
        for (int i = 0; i < joinTables.size(); i++) {
            JoinTable joinTable = joinTables.get(i);
            String alias = joinTable.getTableName() + "_" + i;
            joinTable.setAlias(alias);
            joinTable.setJoinConditionWithAlias("main." + joinTable.getJoinCondition() + " = " + alias + "." + joinTable.getJoinCondition());
        }
        config.setJoinTables(joinTables);

        List<HzReportSetDisplay> displayList = displayMapper.selectFieldList(query.getSetId());
        if (!displayList.isEmpty()){
            for (int i = 0; i < displayList.size(); i++) {
                HzReportSetDisplay formulaField = displayList.get(i);
                if (StringUtils.isNotEmpty(formulaField.getDesignFormulas()) && !formulaField.getDesignFormulas().equals(formulaField.getFieldName())){
                    String resultField = formulaField.getFieldName() + "_" + i;
                    formulaField.setFieldName(resultField);
                }
            }
            // 查询字段
            List<String> displayFields = displayList.stream().filter(e->(e.getDesignFormulas() == null || e.getFieldName().equals(e.getDesignFormulas())))
                    .sorted(Comparator.comparing(HzReportSetDisplay::getFieldIndex))
                    .map(d -> {
                        String tableAlias;
                        switch (d.getTableField()) {
                            case 0:
                                tableAlias = "main";
                                break;
                            case 1:
                                tableAlias = joinTables.get(0).getAlias();
                                break;
                            case 2:
                                tableAlias = joinTables.get(1).getAlias();
                                break;
                            default:
                                throw new CustomException("Invalid tableIField value: " + d.getTableField());
                        }
                        return tableAlias + "." + d.getFieldName(); // 格式如：main.id, user_0.name
                    })
                    .collect(Collectors.toList());
            config.setDisplayFields(displayFields);

            // 查询字段中文
            List<String> displayFieldsCn = displayList.stream().sorted(Comparator.comparing(HzReportSetDisplay::getFieldIndex)).map(HzReportSetDisplay::getFieldNameCn).collect(Collectors.toList());
            config.setDisplayFieldsCn(displayFieldsCn);

            // 是否只展示小计字段
            List<String> isSubtotalList = displayList.stream().filter(e->e.getIsSubtotal() == 1).map(HzReportSetDisplay::getFieldName).collect(Collectors.toList());
            config.setIsSubtotalList(isSubtotalList);

            // 小计字段
            List<HzReportSetDisplay> subtotalFields = displayList.stream().filter(e->e.getFieldSortSubtotal() != null && e.getFieldSortSubtotal() == 1).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(subtotalFields)){
                // 小计分组字段
                List<String> collect = subtotalFields.stream().map(HzReportSetDisplay::getFieldName).collect(Collectors.toList());
                config.setSubtotalFields(collect);
                List<Long> setDisplayIds = subtotalFields.stream().map(HzReportSetDisplay::getId).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(setDisplayIds)){
                    List<HzReportSetDisplaySubtotal> displaySubtotals = displaySubtotalMapper.selectList(new QueryWrapper<HzReportSetDisplaySubtotal>().in("set_display_id", setDisplayIds));
                    if (!CollectionUtils.isEmpty(displaySubtotals)){
                        // 小计字段
                        config.setSubtotalFieldList(displaySubtotals.stream().map(HzReportSetDisplaySubtotal::getFieldName).collect(Collectors.toList()));
                    }
                }
            }


            // 计算字段
            List<HzReportSetDisplay> formulasFields = displayList.stream().filter(e -> StringUtils.isNotEmpty(e.getDesignFormulas())).collect(Collectors.toList());
            config.setFormulasFields(formulasFields);

            // 合并字段
            List<String> mergeFields = displayList.stream().filter(e->e.getFieldSortMerge() != null && e.getFieldSortMerge() == 1).map(HzReportSetDisplay::getFieldNameCn).collect(Collectors.toList());
            config.setMergeFields(mergeFields);

            // 排序字段
            List<SortField> sortFieldList = new ArrayList<>();
            List<HzReportSetDisplay> sortFields = displayList.stream()
                    .filter(e -> e.getFieldSort() != null && e.getFieldSort() == 1)
                    .collect(Collectors.toList());
            for (HzReportSetDisplay sortField : sortFields) {
                SortField field = new SortField();
                String tableAlias;
                switch (sortField.getTableField()) {
                    case 0:
                        tableAlias = "main";
                        break;
                    case 1:
                        tableAlias = joinTables.get(0).getAlias();
                        break;
                    case 2:
                        tableAlias = joinTables.get(1).getAlias();
                        break;
                    default:
                        throw new IllegalArgumentException("Invalid tableIField value: " + sortField.getTableField());
                }
                field.setField(tableAlias + "." + sortField.getFieldName());
                field.setDirection(sortField.getFieldSortType());
                sortFieldList.add(field);
            }
            config.setSortFields(sortFieldList);
        }
        if (query.getKeyValueList() != null){
            for (FieldKeyValue fieldKeyValue : query.getKeyValueList()) {
                fieldKeyValue.setFieldName("main." + fieldKeyValue.getFieldName());
            }
            for (FieldKeyValue fieldKeyValue : query.getKeyValueList()) {
                switch (fieldKeyValue.getFieldFilterType()){
                    case "0":
                        fieldKeyValue.setFieldFilterType("=");
                        break;
                    case "1":
                        fieldKeyValue.setFieldFilterType(">");
                        break;
                    case "2":
                        fieldKeyValue.setFieldFilterType("<");
                        break;
                    case "3":
                        fieldKeyValue.setFieldFilterType("BETWEEN");
                        break;
                    case "4":
                        fieldKeyValue.setFieldFilterType("LIKE");
                        break;
                    case "5":
                        fieldKeyValue.setFieldFilterType("IN");
                        String fieldValue = fieldKeyValue.getFieldValue();
                        String[] split = fieldValue.split(",");
                        fieldKeyValue.setSelectValue(Arrays.asList(split));
                        break;
                    default:
                        fieldKeyValue.setFieldFilterType(" ");
                        break;
                }
                validateFilter(fieldKeyValue);
            }
        }
        config.setFilters(query.getKeyValueList());
        if (!CollectionUtils.isEmpty(config.getIsSubtotalList())){
            Page<Map<String,Object>> allPage = new Page<>(1, -1);
            Page<Map<String,Object>> allCountReport = countReportMapper.generateReport(config,allPage);
            List<Map<String, Object>> records = allCountReport.getRecords();
            if (!CollectionUtils.isEmpty(config.getSubtotalFields()) && !CollectionUtils.isEmpty(config.getSubtotalFieldList())) {
                records = addSubtotal(records, config.getSubtotalFields(), config.getSubtotalFieldList(), config.getIsSubtotalList());
            }
            return records.size();
        }else {
            return countReportMapper.pageQuery(config);
        }
    }

    @Override
    public HzReportSet selectById(Long id) {
        return reportSetMapper.selectById(id);
    }

    private void validateConfig(ReportConfig config,Integer masterTable,Integer slaveTable, Integer slaveTable2) {
        List<String> allTables = new ArrayList<>(TABLE_MAP.values());
        if (!allTables.contains(config.getMainTable())) {
            throw new IllegalArgumentException("非法主表名");
        }

        for (JoinTable joinTable : config.getJoinTables()) {
            if (!allTables.contains(joinTable.getTableName())) {
                throw new IllegalArgumentException("非法关联表名");
            }
        }
        List<String> allField = reportFieldMapper.selectFields(masterTable,slaveTable,slaveTable2);
        allField.add("quantity");
        allField.add("weight");
        for (String field : config.getDisplayFields()) {
            String[] split = field.split("\\.");
            if (!allField.contains(split[1])) {
                throw new IllegalArgumentException("非法字段名");
            }
        }
    }

    private void validateFilter(FieldKeyValue filter) {
        if (!ALLOWED_OPERATORS.contains(filter.getFieldFilterType())) {
            throw new IllegalArgumentException("非法操作符: " + filter.getFieldFilterType());
        }
    }

    public List<Map<String, Object>> convertKeysToChinese(List<Map<String, Object>> data, List<HzReportSetDisplay> displayList) {
        return data.stream().map(row -> {
            Map<String, Object> newRow = new LinkedHashMap<>();
            displayList.forEach(field -> {
                String displayName = field.getFieldNameCn();
                String fieldName = field.getFieldName();
                if (row.get("合计：") != null){
                    newRow.put("序号", "合计");
                }
                if (row.get("小计：") != null){
                    newRow.put("序号", "小计");
                }
                newRow.put(displayName, row.get(fieldName));
            });
            return newRow;
        }).collect(Collectors.toList());
    }

    private List<String> buildExcelHeader(List<HzReportSetDisplay> fieldConfigs) {
        List<String> collect = fieldConfigs.stream().map(HzReportSetDisplay::getFieldNameCn).collect(Collectors.toList());
        collect.add(0,"序号");
        return collect;
    }

    public List<Map<String, Object>> applyFormula(List<Map<String, Object>> data, List<HzReportSetDisplay> formulasFields) {
        if (CollectionUtils.isEmpty(formulasFields)) {
            return data;
        }
        ScriptEngine engine = new ScriptEngineManager().getEngineByName("JavaScript");
        if (engine == null) {
            throw new IllegalStateException("JavaScript引擎不可用");
        }
        data.forEach(row -> {
            if (row.get("合计：") == null){
                formulasFields.forEach(formulaField -> {
                    String formula = formulaField.getDesignFormulas();
                    String resultField = formulaField.getFieldName();
                    Set<String> dependentFields = extractDependentFields(formula);
                    boolean allFieldsValid = dependentFields.stream()
                            .allMatch(field -> row.containsKey(field) && row.get(field) != null);

                    if (!allFieldsValid) {
                        row.put(resultField, null);
                        return;
                    }
                    try {
                        Bindings bindings = engine.createBindings();
                        dependentFields.forEach(field ->
                                bindings.put(field, row.get(field))
                        );
                        Object result = engine.eval(formula, bindings);
                        row.put(resultField, convertToJavaType(result));
                    } catch (ScriptException e) {
                        System.err.println("公式计算错误: " + formula);
                        e.printStackTrace();
                        row.put(resultField, null);
                    }
                });
            }
        });
        return data;
    }

    private Object convertToJavaType(Object result) {
        if (result instanceof Number) {
            double value = ((Number) result).doubleValue();
            BigDecimal bd = new BigDecimal(Double.toString(value));
            bd = bd.setScale(2, RoundingMode.HALF_UP);
            return bd.doubleValue();
        }
        return result;
    }

    private Set<String> extractDependentFields(String formula) {
        Set<String> fields = new HashSet<>();
        Matcher matcher = VARIABLE_PATTERN.matcher(formula);
        while (matcher.find()) {
            String token = matcher.group();
            // 排除数字和运算符
            if (!token.matches("\\d+(\\.\\d+)?|[-+*/%]")) {
                fields.add(token);
            }
        }
        return fields;
    }

    public List<Map<String, Object>> addSubtotal(List<Map<String, Object>> data, List<String> subtotalFields,List<String> subtotalFieldList,List<String> isSubTotalList) {
        List<Map<String, Object>> result = new ArrayList<>();
        // 1. 分离普通数据与合计行
        List<Map<String, Object>> normalData = new ArrayList<>();
        List<Map<String, Object>> totalRows = new ArrayList<>();
        for (Map<String, Object> row : data) {
            if (row.containsKey("合计：") && Boolean.TRUE.equals(row.get("合计："))) {
                totalRows.add(row);
            } else {
                normalData.add(row);
            }
        }
        // 2. 按分组字段聚合数据，处理空值
        Map<String, List<Map<String, Object>>> groupedData = normalData.stream()
                .collect(Collectors.groupingBy(
                        row -> subtotalFields.stream()
                                .map(field -> Optional.ofNullable(row.get(field))
                                        .map(Object::toString)
                                        .orElse("N/A"))
                                .collect(Collectors.joining("-")),
                        LinkedHashMap::new,
                        Collectors.toList()
                ));
        // 2. 处理每个分组
        groupedData.forEach((key, group) -> {
            if (CollectionUtils.isEmpty(isSubTotalList)){
                result.addAll(group);
            }
            Map<String, Object> subtotalRow = new HashMap<>();

            subtotalFields.forEach(field ->
                    subtotalRow.put(field, group.get(0).get(field))
            );
            // 计算每个求和字段的总值
            subtotalFieldList.forEach(sumField -> {
                double sum = group.stream()
                        .mapToDouble(row -> {
                            Object value = row.get(sumField);
                            if (value instanceof Number) {
                                return ((Number) value).doubleValue();
                            } else if (value instanceof String) {
                                try {
                                    // 尝试将字符串转换为double
                                    return Double.parseDouble((String) value);
                                } catch (NumberFormatException e) {
                                    // 如果字符串不是有效的数字格式，则返回0.0或选择其他默认值
                                    return 0.0;
                                }
                            } else {
                                // 对于其他非数值类型，返回0.0或根据需求调整
                                return 0.0;
                            }
                        })
                        .sum();
                subtotalRow.put(sumField, sum);
            });

            subtotalRow.put("小计：", true);
            result.add(subtotalRow);
        });
        result.addAll(totalRows);
        return result;
    }

    public static <T> List<T> getPage(List<T> dataList, int page, int pageSize) {
        if (dataList == null || dataList.isEmpty() || page <= 0 || pageSize <= 0) {
            return new ArrayList<>();
        }

        int fromIndex = (page - 1) * pageSize;
        if (fromIndex >= dataList.size()) {
            return new ArrayList<>();
        }

        int toIndex = fromIndex + pageSize;
        if (toIndex > dataList.size()) {
            toIndex = dataList.size();
        }

        return dataList.subList(fromIndex, toIndex);
    }


    /** 主表 0:分单 1:分单费用数据 2:主单 3:主单收运数据
     4:预支付费用数据 5:配载数据 6:结算费用数据
     7:拉下 8:理货数据 9:提货办单数据 10:进港费用数据
     11:提货出库数据 12:运单保障节点 13:冷藏登记
     14:服务 */
    private static final Map<Integer,String> TABLE_MAP = new HashMap<>();
    static {
        TABLE_MAP.put(0,"all_report_data_hawb");
        TABLE_MAP.put(1,"all_report_data_item");
        TABLE_MAP.put(2,"all_report_data_waybill");
        TABLE_MAP.put(3,"all_report_data_collect");
        TABLE_MAP.put(4,"all_report_dep_pay_cost");
        TABLE_MAP.put(5,"all_report_dep_load");
        TABLE_MAP.put(6,"all_report_dep_settle_cost");
        TABLE_MAP.put(7,"all_report_pull_down");
        TABLE_MAP.put(8,"all_report_tally");
        TABLE_MAP.put(9,"all_report_pick_up");
        TABLE_MAP.put(10,"all_report_arr_cost");
        TABLE_MAP.put(11,"all_report_pick_out");
        TABLE_MAP.put(12,"all_report_node_query");
        TABLE_MAP.put(13,"all_report_cold_register");
        TABLE_MAP.put(14,"all_report_service_request");
        TABLE_MAP.put(15,"all_report_dep_repeat_weight");
    }
}
