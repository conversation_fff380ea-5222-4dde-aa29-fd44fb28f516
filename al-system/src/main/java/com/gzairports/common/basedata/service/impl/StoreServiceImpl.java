package com.gzairports.common.basedata.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.BaseStore;
import com.gzairports.common.basedata.domain.query.StoreQuery;
import com.gzairports.common.basedata.domain.vo.StoreLocatorVo;
import com.gzairports.common.basedata.mapper.StoreMapper;
import com.gzairports.common.basedata.service.IStoreService;
import com.gzairports.common.exception.CustomException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 仓库管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Service
public class StoreServiceImpl extends ServiceImpl<StoreMapper,BaseStore> implements IStoreService {

    @Autowired
    private StoreMapper storeMapper;

    /**
     * 查询仓库管理列表
     *
     * @param query 查询参数
     * @return 仓库管理列表
     */
    @Override
    public List<BaseStore> storeList(StoreQuery query) {
        return storeMapper.storeList(query);
    }

    @Override
    public int add(BaseStore store) {
        BaseStore baseStore = storeMapper.selectOne(new QueryWrapper<BaseStore>()
                .eq("code", store.getCode())
                .eq("locator", store.getLocator()));
        if (baseStore != null){
            throw new CustomException("已存在相同数据");
        }
        return storeMapper.insert(store);
    }

    @Override
    public int edit(BaseStore store) {
        return storeMapper.updateById(store);
    }

    @Override
    public int del(Long id) {
        BaseStore baseStore = storeMapper.selectById(id);
        baseStore.setIsDel(1);
        return storeMapper.updateById(baseStore);
    }

    /**
     * 详情
     * @param id 仓库id
     * @return 结果
     */
    @Override
    public BaseStore getInfo(Long id) {
        return storeMapper.selectById(id);
    }

    /**
     * 查询仓库和库位集合
     * @return 结果
     */
    @Override
    public StoreLocatorVo storeSelect() {
        List<BaseStore> baseStores = storeMapper.storeList(new StoreQuery());
        StoreLocatorVo vo = new StoreLocatorVo();
        vo.setStoreList(new ArrayList<>(baseStores.stream()
                .map(BaseStore::getName)
                .collect(Collectors.toSet())));
        vo.setLocatorList(new ArrayList<>(baseStores.stream()
                .map(BaseStore::getLocator)
                .collect(Collectors.toSet())));
        return vo;
    }
}
