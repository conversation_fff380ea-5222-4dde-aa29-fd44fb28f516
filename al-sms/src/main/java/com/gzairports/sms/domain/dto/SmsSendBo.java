package com.gzairports.sms.domain.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description: 发送短信传输的内容
 * @date 2022/9/26 18:08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SmsSendBo {

    /**
     * 要发送的目标号码
     * **/
    @JSONField
    private String phoneNumber;
    /***
     * 短信内容，模板短信时，为模板的key、value的JSON字符串；普通短信为短信内容
     */
    @JSONField
    private String smsContent;
}
