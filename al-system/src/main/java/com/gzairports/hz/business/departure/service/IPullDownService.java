package com.gzairports.hz.business.departure.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.hz.business.departure.domain.HzDepPullDown;
import com.gzairports.hz.business.departure.domain.query.PullDownQuery;
import com.gzairports.hz.business.departure.domain.vo.PullDownInfoVo;
import com.gzairports.hz.business.departure.domain.vo.PullDownLoadInfoVo;
import com.gzairports.hz.business.departure.domain.vo.PullDownVo;
import com.gzairports.hz.business.departure.domain.vo.WaybillIdAndCodeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 临时拉下Service接口
 *
 * <AUTHOR>
 * @date 2024-07-08
 */
public interface IPullDownService extends IService<HzDepPullDown> {

    /**
     * 查询临时拉下列表
     * @param query 查询条件
     * @return 结果
     */
    PullDownVo selectList(PullDownQuery query);

    /**
     * 新增拉下数据
     * @param pullDown 新增参数
     * @return 结果
     */
    int add(HzDepPullDown pullDown);

    /**
     * 查询航班配载信息
     * @param vo 查询条件
     * @return 结果
     */
    PullDownLoadInfoVo selectLoadInfo(PullDownInfoVo vo);

    /**
     * 根据id查询详情
     * @param id 拉下id
     * @return 详情
     */
    PullDownInfoVo getInfo(Long id);

    /**
     * 修改拉下数据
     * @param pullDown 拉下数据
     * @return 结果
     */
    int edit(HzDepPullDown pullDown);

    /**
     * 根据运单后四位或八位运单号查询运单
     * @param waybillCode 后四位或八位运单号
     * @return 运单列表
     */
    List<WaybillIdAndCodeVo> getWaybill(String waybillCode);
}
