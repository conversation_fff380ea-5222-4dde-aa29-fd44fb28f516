package com.gzairports.common.business.reporter.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-02-19
 */
@Data
@TableName("all_report_dep_load")
public class ReportDepLoad {

    /** 主键id */
    private Long id;

    /** 报表id */
    private Long reportId;

    /** 运单号 */
    private String waybillCode;

    /** 配载航班id */
    @TableField(exist = false)
    private String flightId;

    /** 航班日期 */
    private String flightDate;

    /** 航班状态 */
    private String flightStatus;

    /** 航班号 */
    private String flightNo;

    /** 板号 */
    private String uld;

    /** 件数 */
    @TableField(exist = false)
    private Integer typeQuantity;

    /** 重量 */
    @TableField(exist = false)
    private BigDecimal typeWeight;

    /** 货物配载件数 */
    private Integer quantity;

    /** 货物配载重量 */
    private BigDecimal weight;

    /** 代理人 */
    private String agentCompany;

    /** 目的站 */
    private String desPort;

    /** 承运人1 */
    private String carrier1;

    /** 品名编码 */
    private String cargoCode;

    /** 品名 */
    private String cargoName;

    /** 货品大类 */
    private String categoryName;

    /** 邮件配载件数 */
    private Integer mailLoadQuantity;

    /** 邮件配载重量 */
    private BigDecimal mailLoadWeight;

    /** 配载人（昵称） */
    private String loadUser;

    /** 配载时间 */
    private String loadTime;

    /** 航班配载id */
    @TableField(exist = false)
    private Long flightLoadId;

    @TableField(exist = false)
    private Date startRealTakeoffTime;
}
