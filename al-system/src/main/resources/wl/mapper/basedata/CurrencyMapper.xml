<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.CurrencyMapper">
    <select id="selectCurrencyList" resultType="com.gzairports.common.basedata.domain.BaseCurrency">
        select id, currency, country, currency_unit, currency_symbol from base_currency
        <where>
        and is_del = 0
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="currency != null  and currency != ''"> and currency like concat('%',#{currency},'%')</if>
            <if test="country != null and country != ''"> and country like concat('%',#{country},'%')</if>
        </where>
    </select>
</mapper>
