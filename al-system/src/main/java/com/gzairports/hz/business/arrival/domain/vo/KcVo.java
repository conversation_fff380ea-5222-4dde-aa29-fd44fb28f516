package com.gzairports.hz.business.arrival.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 运单明细库存数据
 * <AUTHOR>
 * @date 2024-07-25
 */
@Data
public class KcVo {

    /** 运单id */
    private Long waybillId;

    /** 理货id */
    private Long tallyId;

    /** 提货办单id */
    private Long pickUpId;

    /** 仓库 */
    private String store;

    /** 库位 */
    private String locator;

    /** 板箱号 */
    private String uld;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 备注 */
    private String remark;
}
