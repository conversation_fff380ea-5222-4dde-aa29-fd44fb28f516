package com.gzairports.wl.departure.domain.query;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 分单费用项目参数
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@Data
public class ItemQuery {

    /** 分单运单号 */
    private String waybillCode;

    /** 重量 */
    private BigDecimal weight;

    /** 体积（M3） */
    private BigDecimal volume;

    /** 件数 */
    private Integer quantity;

    /** 计费重量 */
    private BigDecimal chargeWeight;

    /** 收费项目id集合 */
    private List<Long> chargeItemIds;

    /** 费用总计 */
    private BigDecimal costSum;

    /** 发货人简称 */
    private String shipperAbb;

    /** 需要修改的运价条目id */
    private Long oldItemId;

    /** 航司 */
    private String carrier1;

    private String cargoCode;
}
