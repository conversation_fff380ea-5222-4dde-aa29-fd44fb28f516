package com.gzairports.common.system.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.gzairports.common.core.domain.entity.SysDept;

/**
 * 部门管理 数据层
 * 
 * <AUTHOR>
 */
@Mapper
public interface SysDeptMapper
{
    /**
     * 查询部门管理数据
     * 
     * @param dept 部门信息
     * @return 部门信息集合
     */
    public List<SysDept> selectDeptList(SysDept dept);


    /**
     * 根据名称或者简称查询部门管理数据
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
    public List<SysDept> selectDeptListByDeptNameOrAbb(SysDept dept);

    /**
     * 根据当前登录用户信息查询部门管理数据
     * 拿到最父级的部门id 后拿到该父级部门下的所有子级部门
     *
     * @param deptId 当前登录用户最父级部门信息
     * @return 部门信息集合
     */
//    List<SysDept> selectDeptListForUser(@Param("deptIds") Set<Long> deptIds);
    List<SysDept> selectDeptListForUser(Long deptId);

    /**
     * 根据角色ID查询部门树信息
     * 
     * @param roleId 角色ID
     * @param deptCheckStrictly 部门树选择项是否关联显示
     * @return 选中部门列表
     */
    public List<Long> selectDeptListByRoleId(@Param("roleId") Long roleId, @Param("deptCheckStrictly") boolean deptCheckStrictly);

    /**
     * 根据部门ID查询信息
     * 
     * @param deptId 部门ID
     * @return 部门信息
     */
    public SysDept selectDeptById(Long deptId);

    /**
     * 根据ID查询所有子部门
     * 
     * @param deptId 部门ID
     * @return 部门列表
     */
    public List<SysDept> selectChildrenDeptById(Long deptId);

    /**
     * 根据ID查询所有子部门（正常状态）
     * 
     * @param deptId 部门ID
     * @return 子部门数
     */
    public int selectNormalChildrenDeptById(Long deptId);

    /**
     * 是否存在子节点
     * 
     * @param deptId 部门ID
     * @return 结果
     */
    public int hasChildByDeptId(Long deptId);

    /**
     * 查询部门是否存在用户
     * 
     * @param deptId 部门ID
     * @return 结果
     */
    public int checkDeptExistUser(Long deptId);

    /**
     * 校验部门名称是否唯一
     * 
     * @param deptName 部门名称
     * @param parentId 父部门ID
     * @return 结果
     */
    public SysDept checkDeptNameUnique(@Param("deptName") String deptName, @Param("parentId") Long parentId);

    /**
     * 新增部门信息
     * 
     * @param dept 部门信息
     * @return 结果
     */
    public int insertDept(SysDept dept);

    /**
     * 修改部门信息
     * 
     * @param dept 部门信息
     * @return 结果
     */
    public int updateDept(SysDept dept);

    /**
     * 修改所在部门正常状态
     * 
     * @param deptIds 部门ID组
     */
    public void updateDeptStatusNormal(Long[] deptIds);

    /**
     * 修改子元素关系
     * 
     * @param depts 子元素
     * @return 结果
     */
    public int updateDeptChildren(@Param("depts") List<SysDept> depts);

    /**
     * 删除部门管理信息
     * 
     * @param deptId 部门ID
     * @return 结果
     */
    public int deleteDeptById(Long deptId);

    /**
     * 根据名称查询岗位信息
     * @return 岗位列表
     */
    List<SysDept> selectDeptByName();

//    Long selectParentIdByDeptId(Long deptId);

    List<Long> selectDeptIdsByParentId(Long parentId);

    SysDept getAbbByAgent(Long id);

    /**
     * 查询所有部门
     * @return 部门列表
     */
    List<SysDept> selectList();

    /**
     * 查询代理人-deptName
     * @return 部门列表
     */
    String selectShipper(String agentCode);

    /**
     * 查询代理人代码-deptName
     * @return 部门列表
     */
    String selectAgentcode(String shipper);

    /**
     * 根据代理人名称得到代理人id
     * */
    Long selectDeptIdByDeptName(String agentCompany);

    /**
     * 根据代理人名称获得简称
     * */
    String selectDeptAbbByName(String shipper);

    List<Long> selectDeptListById(Long highParentId);

    /**
     * 查询可查询运单前缀和可能查询航司二字码信息
     * @param deptId
     * @return
     */
    List<SysDept> selectEnabledPrefixAndAirline(Long deptId);

}
