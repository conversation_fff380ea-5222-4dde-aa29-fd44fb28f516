package com.gzairports.wl.ticket.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 单证预警管理表
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Data
@TableName("wl_ticket_alert")
public class TicketAlert {

    /** 主键id */
    private Long id;

    /** 票证来源 */
    private String billSource;

    /** 票证类型 */
    private String billType;

    /** 票证前缀 */
    private String billPrefix;

    /** 是否：包舱单（包租整个货舱的运单） */
    private Integer isChartering;

    /** 预警数量 */
    private Integer numberAlert;

    /** 部门 */
    private Long deptId;

    @TableField(exist = false)
    private String deptName;
}
