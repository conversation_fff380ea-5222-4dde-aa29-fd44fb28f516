<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.reporter.mapper.HzReportSetMapper">
    <select id="selectHzReportSetList" resultType="com.gzairports.hz.business.reporter.domain.HzReportSet">
        select id, report_title, report_domint, report_type, report_role, report_status, is_del
        from hz_report_set
        where
            is_del = 0
            <if test="reportDomint != null and reportDomint != ''">
               and report_domint = #{reportDomint}
            </if>
            <if test="reportType != null and reportType != ''">
               and report_type = #{reportType}
            </if>
    </select>
    <select id="selectReportTitle" resultType="com.gzairports.hz.business.reporter.domain.HzReportSet">
        select id, report_title, report_role
        from hz_report_set where report_status = 1 and is_del = 0
    </select>
    <update id="addFormula">
        ALTER TABLE `cargo_station`.`${tableName}`
        ADD COLUMN `${fieldNameEn}` int NULL DEFAULT 0 COMMENT '${fieldNameCn}'
    </update>
</mapper>