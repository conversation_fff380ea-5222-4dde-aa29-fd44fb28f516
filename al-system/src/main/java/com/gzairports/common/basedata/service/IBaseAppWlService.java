package com.gzairports.common.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.basedata.domain.BaseApp;

import java.util.List;

/**
 * app管理Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-19
 */
public interface IBaseAppWlService extends IService<BaseApp>
{
    /**
     * 查询app管理
     * 
     * @param id app管理ID
     * @return app管理
     */
    BaseApp selectBaseAppById(Long id);

    /**
     * 查询app管理列表
     * 
     * @param baseApp app管理
     * @return app管理集合
     */
    List<BaseApp> selectBaseAppList(BaseApp baseApp);

    /**
     * 新增app管理
     * 
     * @param baseApp app管理
     * @return 结果
     */
    int insertBaseApp(BaseApp baseApp);

    /**
     * 修改app管理
     * 
     * @param baseApp app管理
     * @return 结果
     */
    int updateBaseApp(BaseApp baseApp);

    /**
     * 批量删除app管理
     * 
     * @param ids 需要删除的app管理ID
     * @return 结果
     */
    int deleteBaseAppByIds(Long[] ids);

    /**
     * 删除app管理信息
     * 
     * @param id app管理ID
     * @return 结果
     */
    int deleteBaseAppById(Long id);

    /**
     * 发布app
     *
     * @param id app管理ID
     * @return 结果
     */
    int release(Long id);

    /**
     * app下载
     * @return 下载地址
     */
    String download();

    /**
     * app版本号对比
     * @param version 版本号参数
     * @return 对比结果
     */
    String compare(String version);
}
