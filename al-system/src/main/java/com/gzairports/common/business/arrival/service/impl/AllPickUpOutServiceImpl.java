package com.gzairports.common.business.arrival.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.BaseAgent;
import com.gzairports.common.basedata.domain.BaseBalance;
import com.gzairports.common.basedata.domain.BaseCarrier;
import com.gzairports.common.basedata.mapper.BaseAgentMapper;
import com.gzairports.common.basedata.mapper.BaseBalanceMapper;
import com.gzairports.common.basedata.mapper.CarrierMapper;
import com.gzairports.common.business.arrival.domain.*;
import com.gzairports.common.business.arrival.domain.query.AppPickUpQuery;
import com.gzairports.common.business.arrival.domain.query.PickUpOutQuery;
import com.gzairports.common.business.arrival.domain.vo.*;
import com.gzairports.common.business.arrival.mapper.*;
import com.gzairports.common.business.arrival.service.IAllPickUpOutService;
import com.gzairports.common.business.departure.domain.FlightInfo;
import com.gzairports.common.business.departure.domain.Mawb;
import com.gzairports.common.business.departure.domain.WaybillFee;
import com.gzairports.common.business.departure.mapper.MawbMapper;
import com.gzairports.common.business.departure.mapper.WaybillFeeMapper;
import com.gzairports.common.charge.domain.HzChargeIrRelation;
import com.gzairports.common.charge.domain.HzChargeItemRule;
import com.gzairports.common.charge.domain.HzChargeItems;
import com.gzairports.common.charge.domain.HzChargeRule;
import com.gzairports.common.charge.domain.vo.BillRuleVo;
import com.gzairports.common.charge.mapper.HzChargeIrRelationMapper;
import com.gzairports.common.charge.mapper.HzChargeItemRuleMapper;
import com.gzairports.common.charge.mapper.HzChargeItemsMapper;
import com.gzairports.common.charge.mapper.HzChargeRuleMapper;
import com.gzairports.common.charge.service.BillingRule;
import com.gzairports.common.charge.service.impl.BillingRuleFactory;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.common.log.service.IWaybillLogService;
import com.gzairports.common.system.mapper.SysConfigMapper;
import com.gzairports.common.utils.*;
import com.gzairports.hz.business.arrival.domain.HzArrQuickDelivery;
import com.gzairports.hz.business.arrival.domain.HzArrRecordOrder;
import com.gzairports.hz.business.arrival.mapper.HzArrRecordOrderMapper;
import com.gzairports.hz.business.arrival.mapper.QuickDeliveryMapper;
import com.gzairports.hz.business.cable.domain.HzCable;
import com.gzairports.hz.business.cable.domain.HzCableAddress;
import com.gzairports.hz.business.cable.domain.vo.FSUJsonVO;
import com.gzairports.hz.business.cable.domain.vo.ForwardOriginMsgVO;
import com.gzairports.hz.business.cable.domain.vo.MsgJsonVO;
import com.gzairports.hz.business.cable.domain.vo.StatusDetails;
import com.gzairports.hz.business.cable.mapper.HzCableAddressMapper;
import com.gzairports.hz.business.cable.mapper.HzCableMapper;
import com.gzairports.hz.business.cable.service.impl.HttpServiceImpl;
import com.gzairports.hz.business.departure.domain.AirWaybill;
import com.gzairports.hz.business.departure.domain.HzColdRegister;
import com.gzairports.hz.business.departure.mapper.AllAirWaybillMapper;
import com.gzairports.hz.business.departure.mapper.HzColdRegisterMapper;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Marker;
import org.slf4j.MarkerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 国内进港提货出库Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-19
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class AllPickUpOutServiceImpl extends ServiceImpl<AllPickUpOutMapper, AllPickUpOut> implements IAllPickUpOutService {

    @Autowired
    private PickUpMapper pickUpMapper;

    @Autowired
    private HzArrTallyMapper tallyMapper;

    @Autowired
    private PickUpWaybillMapper pickUpWaybillMapper;

    @Autowired
    private AllAirWaybillMapper airWaybillMapper;

    @Autowired
    private HzArrItemMapper hzArrItemMapper;

    @Autowired
    private AllPickUpOutMapper allPickUpOutMapper;

    @Autowired
    private HzColdRegisterMapper coldRegisterMapper;

    @Autowired
    private HzChargeRuleMapper ruleMapper;

    @Autowired
    private HzChargeIrRelationMapper relationMapper;

    @Autowired
    private HzChargeItemRuleMapper itemRuleMapper;

    @Autowired
    private HzColdRegisterMapper registerMapper;

    @Autowired
    private FlightInfoMapper infoMapper;

    @Autowired
    private IWaybillLogService waybillLogService;

    @Autowired
    private WaybillFeeMapper feeMapper;

    @Autowired
    private BaseBalanceMapper baseBalanceMapper;

    @Autowired
    private HzArrRecordOrderMapper recordOrderMapper;

    @Autowired
    private PickUpWaybillServiceImpl waybillService;

    @Autowired
    private BaseAgentMapper baseAgentMapper;

    @Autowired
    private MawbMapper mawbMapper;

    @Autowired
    private QuickDeliveryMapper quickDeliveryMapper;

    @Autowired
    private HzChargeItemsMapper chargeItemsMapper;

    @Autowired
    private CarrierMapper carrierMapper;

    @Autowired
    private SysConfigMapper sysConfigMapper;

    @Autowired
    private HzCableMapper hzCableMapper;

    @Autowired
    private HzCableAddressMapper cableAddressMapper;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private HttpServiceImpl httpService;

    @Value("${hzCable.account}")
    private String account;

    @Value("${hzCable.loginUrl}")
    private String loginUrl;

    @Value("${hzCable.getMsg}")
    private String getMsg;

    @Value("${hzCable.FTPAddress}")
    private String ftpAddress;

    private static final Marker CABLE_ERROR_MARKER  = MarkerFactory.getMarker("CABLE-ERROR");

    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMdd");


    /**
     * 提货出库查询
     * @param query 查询条件
     * @return 数据
     */
    @Override
    public PickOrderVo selectList(PickUpOutQuery query) {
        if ((query.getPickUpCode() == null || "".equals(query.getPickUpCode()))
                && (query.getSerialNo() == null || "".equals(query.getSerialNo()))){
            throw new CustomException("至少需要一个查询参数");
        }
        query.setQueryDate(new Date());
        PickOrderVo vo = pickUpMapper.selectOutList(query);
        if (vo == null){
            throw new CustomException("无当前提货出库信息");
        }
        List<PickUpVo> list = new ArrayList<>();
        List<PickUpWaybill> waybills = pickUpWaybillMapper.selectList(new QueryWrapper<PickUpWaybill>().eq("pick_up_id", vo.getId()));
        if (CollectionUtils.isEmpty(waybills)) {
            throw new CustomException("无提货运单");
        }
        for (PickUpWaybill waybill : waybills) {
            PickUpVo pickUpVo = new PickUpVo();
            BeanUtils.copyProperties(waybill,pickUpVo);
            pickUpVo.setOrderQuantity(waybill.getCanPickUpQuantity());
            pickUpVo.setOrderWeight(waybill.getCanPickUpWeight());
            AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                    .eq("waybill_code", waybill.getWaybillCode())
                    .eq("type","ARR"));
            pickUpVo.setCargoName(airWaybill.getCargoName());
            pickUpVo.setConsign(airWaybill.getConsign());
            pickUpVo.setQuantity(airWaybill.getQuantity());
            pickUpVo.setWeight(airWaybill.getWeight());
            pickUpVo.setSpecialCargoCode1(airWaybill.getSpecialCargoCode1());
            List<HzArrItem> hzArrItems = hzArrItemMapper.selectList(new QueryWrapper<HzArrItem>()
                    .eq("waybill_code", waybill.getWaybillCode())
                    .eq("tally_id", waybill.getTallyId()));
            if (!CollectionUtils.isEmpty(hzArrItems)){
                BigDecimal reduce = hzArrItems.stream().map(HzArrItem::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                pickUpVo.setCostSum(reduce);
            }
            vo.setConsign(airWaybill.getConsign());
            list.add(pickUpVo);
        }
        vo.setNoStatus(1);
        vo.setPickUpVos(list);
        //是否为快速交付代理人
        List<HzArrQuickDelivery> quickDeliveryList = quickDeliveryMapper.selectListValidity();
        if (quickDeliveryList.size() > 0) {
            List<String> collect = quickDeliveryList.stream().map(HzArrQuickDelivery::getAgent).collect(Collectors.toList());
            if (collect.contains(vo.getConsign())) {
                vo.setIsQuickDelivery(1);
            }else {
                vo.setIsQuickDelivery(0);
            }
        }else {
            vo.setIsQuickDelivery(0);
        }
        return vo;
    }

    /**
     * 提货办单信息
     * @param id 运单id
     * @return 结果
     */
    @Override
    public PickUpOutWaybillVo getInfo(Long id) {
        PickUpOutWaybillVo vo = pickUpWaybillMapper.getInfo(id);
        if (vo != null) {
            Integer count = allPickUpOutMapper.selectCount(new QueryWrapper<AllPickUpOut>().eq("waybill_code", vo.getWaybillCode()));
            vo.setNum(count);
            AllPickUpOut pickUpOut = allPickUpOutMapper.selectOne(new QueryWrapper<AllPickUpOut>()
                    .eq("pick_up_id", vo.getPickUpId())
                    .eq("tally_id", vo.getTallyId()));
            if (pickUpOut == null) {
                vo.setIsTake("否");
            } else {
                if (pickUpOut.getOutTime() != null) {
                    vo.setIsTake("是");
                } else {
                    vo.setIsTake("否");
                }
                if(pickUpOut.getPiecesChanged() != null){
                    vo.setPieces(pickUpOut.getPiecesChanged());
                }else{
                    vo.setPieces(pickUpOut.getPieces());
                }
                if(pickUpOut.getWeightChanged() != null){
                    vo.setWeight(pickUpOut.getWeightChanged());
                }else{
                    vo.setWeight(pickUpOut.getWeight());
                }
                vo.setPickUpCode(pickUpOut.getPickUpCode());
            }
            HzArrTally hzArrTally = tallyMapper.selectById(vo.getTallyId());
            HzArrRecordOrder hzArrRecordOrder = recordOrderMapper.selectById(hzArrTally.getRecordOrderId());
            if(hzArrRecordOrder.getIsSouth() != null){
                vo.setIsTake("转南航");
            }
        }
        return vo;
    }

    /**
     * 保存提货件数和提货重量
     * @param vo 参数
     * @return 结果
     */
    @Override
    public int savePickUpData(PickUpOutWaybillVo vo) {
        PickUp pickUp = pickUpMapper.selectById(vo.getPickUpId());
        if (pickUp == null){
            throw new CustomException("请重新办单");
        }
        if (!vo.getOrderQuantity().equals(vo.getPieces()) || vo.getOrderWeight().compareTo(vo.getWeight()) != 0){
            throw new CustomException("运单提货件数和重量与办单件数和重量不一致");
        }
        AllPickUpOut pickUpOut = allPickUpOutMapper.selectOne(new QueryWrapper<AllPickUpOut>()
                .eq("tally_id", vo.getTallyId())
                .eq("pick_up_id", vo.getPickUpId())
                .eq("waybill_code", vo.getWaybillCode()));
        if (pickUpOut != null){
            pickUpOut.setPieces(vo.getPieces());
            pickUpOut.setWeight(vo.getWeight());
            pickUpOut.setCreateTime(new Date());
            pickUpOut.setUpdateTime(new Date());
            return allPickUpOutMapper.updateById(pickUpOut);
        }else {
            AllPickUpOut out = new AllPickUpOut();
            BeanUtils.copyProperties(vo,out);
            out.setCreateTime(new Date());
            out.setUpdateTime(new Date());
            return allPickUpOutMapper.insert(out);
        }
    }

    /**
     * 费用明细数据
     * @param waybillCode 进港运单号
     * @param tallyId 理货id
     * @return 结果
     */
    @Override
    public HzArrItemVo cost(String waybillCode, Long tallyId) {
        HzArrItemVo vo = mawbMapper.selectByCode(waybillCode);
        vo.setWaybillCode(waybillCode);
        HzArrTally hzArrTally = tallyMapper.selectById(tallyId);
//        List<HzArrItem> items = hzArrItemMapper.selectCostList(waybillCode, tallyId);
        List<HzArrItem> items = hzArrItemMapper.selectCostListByPickUpId(waybillCode, tallyId, hzArrTally.getPickUpId());
        if (!CollectionUtils.isEmpty(items)){
            vo.setItems(items);
            BigDecimal reduce = items.stream().map(HzArrItem::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setTotalCost(reduce);
        }
        return vo;
    }

    /**
     * 费用明细详情
     * @param id 费用明细id
     * @return 详情
     */
    @Override
    public HzArrItem costInfo(Long id) {
        HzArrItem item = hzArrItemMapper.selectCostInfo(id);
        HzChargeIrRelation relation = relationMapper.selectById(item.getIrId());
        item.setChargeItemsId(relation.getItemId());
        return item;
    }

    /**
     * 编辑费用明细数据
     * @param item 更新后的费用明细数据
     * @return 结果
     */
    @Override
    public int editCost(HzArrItem item) {
        List<HzArrItem> items = hzArrItemMapper.selectList(new QueryWrapper<HzArrItem>()
                .eq("waybill_code", item.getWaybillCode())
                .eq("tally_id", item.getTallyId()));
        PickUpWaybill one = pickUpWaybillMapper.selectOne(new QueryWrapper<PickUpWaybill>()
                .eq("waybill_code", item.getWaybillCode())
                .eq("tally_id", item.getTallyId()));
        BigDecimal totalCost = new BigDecimal(0);
        for (HzArrItem hzArrItem : items) {
            totalCost = totalCost.add(hzArrItem.getTotalCharge());
        }
        if (one != null){
            one.setCostSum(totalCost);
            pickUpWaybillMapper.updateById(one);
            List<PickUpWaybill> pickUpWaybills = pickUpWaybillMapper.selectList(new QueryWrapper<PickUpWaybill>()
                    .eq("pick_up_id", one.getPickUpId()));
            PickUp pick = pickUpMapper.selectById(one.getPickUpId());
            BigDecimal costSum = new BigDecimal(0);
            for (PickUpWaybill pickUpWaybill : pickUpWaybills) {
                costSum = costSum.add(pickUpWaybill.getCostSum());
            }
            pick.setTotalCost(costSum);
            pick.setUpdateTime(new Date());
            pickUpMapper.updateById(pick);
        }
        return hzArrItemMapper.updateById(item);
    }

    /**
     * 删除费用明细
     * @param id 费用明细id
     * @return 结果
     */
    @Override
    public int delCost(Long id) {
        HzArrItem item = hzArrItemMapper.selectById(id);
        PickUpWaybill one = pickUpWaybillMapper.selectOne(new QueryWrapper<PickUpWaybill>()
                .eq("waybill_code", item.getWaybillCode())
                .eq("tally_id", item.getTallyId())
                .eq("is_cancel","0"));
        if (one != null){
            BigDecimal add = one.getCostSum().subtract(item.getTotalCharge());
            one.setCostSum(add);
            pickUpWaybillMapper.updateById(one);

            PickUp pick = pickUpMapper.selectById(one.getPickUpId());
            BigDecimal add1 = pick.getTotalCost().subtract(item.getTotalCharge());
            pick.setTotalCost(add1);
            pick.setUpdateTime(new Date());
            pickUpMapper.updateById(pick);
        }
        return hzArrItemMapper.deleteById(id);
    }

    /**
     * 新增费用明细
     * @param item 费用明细数据
     * @return 结果
     */
    @Override
    public int addCost(HzArrItem item) {
        PickUpWaybill one = pickUpWaybillMapper.selectOne(new QueryWrapper<PickUpWaybill>()
                .eq("waybill_code", item.getWaybillCode())
                .eq("tally_id", item.getTallyId())
                .eq("is_cancel","0"));
        BigDecimal costSum = one.getCostSum() == null ? new BigDecimal(0) : one.getCostSum();
        HzChargeIrRelation relation = relationMapper.selectById(item.getIrId());
        HzChargeRule hzChargeRule = ruleMapper.selectById(relation.getRuleId());
        if ("ColdStorageBillingRule.class".equals(hzChargeRule.getClassName())){
            HzArrTally hzArrTally = tallyMapper.selectById(item.getTallyId());
            FlightInfo info = infoMapper.selectInfoByTallyId(item.getTallyId());
            QueryWrapper<HzArrItem> queryWrapper = new QueryWrapper<>();

            queryWrapper.eq("waybill_code", item.getWaybillCode())
                    .eq("tally_id", hzArrTally.getId())
                    .eq("cold_store",item.getColdStore());

            queryWrapper.or(wrapper -> wrapper
                    .eq("waybill_code", item.getWaybillCode())
                    .eq("order_id", hzArrTally.getRecordOrderId())
                    .eq("cold_store",item.getColdStore()));

            HzArrItem arrItem = hzArrItemMapper.selectOne(queryWrapper);
            if (arrItem != null){
                throw new CustomException("该运单已存在冷库收费项目");
            }else {
                AirWaybill airWaybill = airWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                        .eq("waybill_code", item.getWaybillCode())
                        .eq("type", "ARR")
                        .eq("is_del",0));
                HzColdRegister hzColdRegister = new HzColdRegister();
                hzColdRegister.setType("ARR");
                hzColdRegister.setColdStore(item.getColdStore());
                long startTime = item.getStoreStartTime().getTime();
                long endTime = item.getStoreEndTime().getTime();
                double diff = (endTime - startTime) / (60.0 * 60.0 * 1000.0);
                hzColdRegister.setUseTime(new BigDecimal(diff));
                long round = Math.round(diff);
                hzColdRegister.setChargeTime(new BigDecimal(round));
                hzColdRegister.setWareTime(item.getStoreStartTime());
                hzColdRegister.setOutTime(item.getStoreEndTime());
                hzColdRegister.setWaybillCode(item.getWaybillCode());
                hzColdRegister.setCargoName(airWaybill.getCargoName());
                hzColdRegister.setSumMoney(item.getTotalCharge());
                hzColdRegister.setUpdateTime(new Date());
//                hzColdRegister.setFlightDate(info.getExecDate());
//                hzColdRegister.setFlightNo(info.getFlightNo());
                registerMapper.insert(hzColdRegister);
            }
        }
        BigDecimal add = costSum.add(item.getTotalCharge());
        one.setCostSum(add);
        pickUpWaybillMapper.updateById(one);

        PickUp pick = pickUpMapper.selectById(one.getPickUpId());
        BigDecimal totalCost = pick.getTotalCost() == null ? new BigDecimal(0) : pick.getTotalCost();
        BigDecimal add1 = totalCost.add(item.getTotalCharge());
        pick.setTotalCost(add1);
        pick.setUpdateTime(new Date());
        pickUpMapper.updateById(pick);
        return hzArrItemMapper.insert(item);
    }

    /**
     * 计算总费用
     * @param item 计算参数
     * @return 结果
     */
    @Override
    public String countCost(HzArrItem item) {
        HzChargeIrRelation relation = relationMapper.selectById(item.getIrId());
        HzChargeRule hzChargeRule = ruleMapper.selectById(relation.getRuleId());
        List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>().eq("ir_id", item.getIrId()));
        if (CollectionUtils.isEmpty(itemRules)){
            return "0";
        }
        HzChargeItems hzChargeItems = chargeItemsMapper.selectById(relation.getItemId());
        HzArrTally hzArrTally = tallyMapper.selectById(item.getTallyId());
        if (hzArrTally == null){
            throw new CustomException("无当前运单信息");
        }
        BigDecimal decimal;
        Mawb mawb = mawbMapper.selectChargeWeight(item.getWaybillCode());
        BigDecimal weightRateCeiling;
        BigDecimal weightRateFloor;
        BigDecimal chargeWeight1 = mawb.getChargeWeight() == null ? new BigDecimal(0) : mawb.getChargeWeight();
        if (mawb.getWeight() == null || mawb.getWeight().compareTo(new BigDecimal(0)) == 0){
            weightRateCeiling = new BigDecimal(0);
            weightRateFloor = new BigDecimal(0);
        }else {
            BigDecimal bigDecimal = chargeWeight1.divide(mawb.getWeight(),5, RoundingMode.DOWN).multiply(hzArrTally.getWeight());
            weightRateCeiling = bigDecimal.setScale(0, RoundingMode.CEILING);
            weightRateFloor = bigDecimal.setScale(0, RoundingMode.FLOOR);
        }
        BillingRule rule = BillingRuleFactory.createRule(hzChargeRule.getClassName());
        if (item.getTotalCharge() == null){
            BillRuleVo vo1 = rule.calculateFee(itemRules, weightRateCeiling, hzArrTally.getPieces(), item);
            decimal = BigDecimalRoundUtils.bigDecimalRound(hzChargeItems.getRoundRule(),vo1.getTotalCharge());
        }else {
            BillRuleVo vo1 = rule.calculateFee(itemRules, weightRateCeiling, hzArrTally.getPieces(), item);
            if (vo1.getTotalCharge().compareTo(item.getTotalCharge()) >= 0){
                decimal = BigDecimalRoundUtils.bigDecimalRound(hzChargeItems.getRoundRule(),vo1.getTotalCharge());
            }else {
                BillRuleVo vo2 = rule.calculateFee(itemRules, weightRateFloor, hzArrTally.getPieces(), item);
                decimal = BigDecimalRoundUtils.bigDecimalRound(hzChargeItems.getRoundRule(),vo2.getTotalCharge());
            }
        }
        return decimal.toString();
    }

    /**
     * 备出库查询列表
     * @param query 查询参数
     * @return 备出库列表
     */
    @Override
    public List<PickUpVo> planPickUpOut(PickUpOutQuery query) {
        return pickUpWaybillMapper.planPickUpOut(query);
    }

    /**
     * 已提货出库查询列表
     * @param query 查询条件
     * @return 已提货出库列表
     */
    @Override
    public List<PickUpVo> pickedUp(PickUpOutQuery query) {
        return allPickUpOutMapper.pickedUp(query);
    }

    /**
     * app提货出库数据
     * @param query 查询条件
     * @return 提货出库数据
     */
    @Override
    public AppPickOutVo appPickUpList(AppPickUpQuery query) {
        AppPickOutVo outVo = new AppPickOutVo();
        List<AppPickUpVo> vo = pickUpWaybillMapper.appPickUpList(query);
        if (CollectionUtils.isEmpty(vo)){
            return outVo;
        }
        for (AppPickUpVo appPickUpVo : vo) {
            if (appPickUpVo.getOutTime() != null){
                appPickUpVo.setStatus("已出库");
            }else {
                appPickUpVo.setStatus("未出库");
            }
        }
        List<AppPickUpVo> list = new ArrayList<>();
        Map<Long, List<AppPickUpVo>> collect = vo.stream().collect(Collectors.groupingBy(AppPickUpVo::getTallyId));
        for (Map.Entry<Long, List<AppPickUpVo>> longListEntry : collect.entrySet()) {
            List<AppPickUpVo> value = longListEntry.getValue();
            List<AppPickUpVo> collect1 = value.stream().filter(e -> e.getOutTime() != null).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect1)){
                list.addAll(collect1);
            }else {
                list.addAll(value);
            }
        }
        int notOutNum = (int) list.stream().filter(e -> "未出库".equals(e.getStatus())).count();
        outVo.setNotOutNum(notOutNum);
        int outNum = (int) list.stream().filter(e -> "已出库".equals(e.getStatus())).count();
        outVo.setOutNum(outNum);
        outVo.setTotalNum(list.size());
        if ("未出库".equals(query.getStatus())){
            List<AppPickUpVo> collect1 = list.stream().filter(e -> "未出库".equals(e.getStatus())).collect(Collectors.toList());
            outVo.setList(collect1);
            return outVo;
        }
        if ("已出库".equals(query.getStatus())){
            List<AppPickUpVo> collect1 = list.stream().filter(e -> "已出库".equals(e.getStatus())).collect(Collectors.toList());
            outVo.setList(collect1);
            return outVo;
        }
        outVo.setList(list);
        return outVo;
    }

    /**
     * 查询扫描出库数据
     * @param pickUpCode 提货码
     * @return 扫描出库数据
     */
    @Override
    public ScanOutVo scanOut(String pickUpCode) {
        ScanOutVo vo = pickUpMapper.selectByCode(pickUpCode,new Date());
        if(StringUtils.isNull(vo)){
            throw new CustomException("提货码不存在或暂无提货出库数据");
        }
        if (vo.getIsPay() == 2){
            vo.setStatus(5);
            return vo;
        }
        List<PickUpWaybill> pickUpWaybills = pickUpWaybillMapper.selectList(new QueryWrapper<PickUpWaybill>().eq("pick_up_id", vo.getPickUpId()));
        if (CollectionUtils.isEmpty(pickUpWaybills)) {
            throw new CustomException("无提货运单");
        }
        List<ScanWaybillVo> list = pickUpWaybillMapper.selectListByPickUpId(vo.getPickUpId());
        list.forEach(e->{
            //根据办单id去查询是否提货出库
            AllPickUpOut pickUpOutByPickUpId = allPickUpOutMapper.selectOne(new QueryWrapper<AllPickUpOut>()
                    .eq("pick_up_id", vo.getPickUpId())
                    .eq("waybill_code",e.getWaybillCode())
                    .eq("tally_id",e.getTallyId()));
            if (pickUpOutByPickUpId != null){
                e.setOutTime(pickUpOutByPickUpId.getOutTime());
            }
        });
        if (!CollectionUtils.isEmpty(list)){
            ScanWaybillVo scanWaybillVo = list.get(0);
            vo.setVoList(list);
            vo.setConsign(scanWaybillVo.getConsign());
            vo.setShipper(scanWaybillVo.getShipper());
        }
        vo.setStatus(1);
        return vo;
    }

    /**
     * app提货出库详情
     * @param waybillCode 运单号
     * @param tallyId 理货id
     * @return 详情
     */
    @Override
    public PickUpInfoVo pickUpInfo(String waybillCode, Long tallyId) {
        if(tallyId != null){
            PickUpInfoVo pickUpInfoVo = pickUpWaybillMapper.selectPickUpInfo(waybillCode, tallyId);
            AllPickUpOut pickUpOut = allPickUpOutMapper.selectOne(new QueryWrapper<AllPickUpOut>()
                    .eq("tally_id", tallyId)
                    .eq("waybill_code", waybillCode)
                    .eq("is_cancel", 0)
                    .orderByDesc("create_time")
                    .last("limit 1"));
            if (pickUpOut != null){
                //app取出库的件数重量(在交付时填的出库件数重量)
                pickUpInfoVo.setCanPickUpQuantity(pickUpOut.getPieces());
                pickUpInfoVo.setCanPickUpWeight(pickUpOut.getWeight());
                pickUpInfoVo.setRemark(pickUpOut.getRemark());
                pickUpInfoVo.setStatus("已出库");
            }else{
                pickUpInfoVo.setStatus("未出库");
            }
            return pickUpInfoVo;
        }else{
            AllPickUpOut pickUpOut = allPickUpOutMapper.selectOne(new QueryWrapper<AllPickUpOut>()
                    .eq("waybill_code", waybillCode)
                    .eq("is_cancel", 0)
                    .orderByDesc("create_time")
                    .last("limit 1"));
            PickUpInfoVo pickUpInfoVo = pickUpWaybillMapper.selectPickUpInfoForWaybillCode(waybillCode);
            pickUpInfoVo.setIsPay(0);
            if(pickUpOut == null){
                pickUpInfoVo.setStatus("未出库");
            }else{
                pickUpInfoVo.setStatus("已出库");
            }
            return pickUpInfoVo;
        }
    }

    /**
     * app交付
     * @param out 交付数据
     * @return 结果
     */
    @Override
    public int deliver(AllPickUpOut out) {
        try {
            if(out.getCanPickUpQuantity() > out.getPieces() || out.getCanPickUpWeight().compareTo(out.getWeight()) > 0 ){
                throw new CustomException("不可超过运单件数/重量");
            }
            AllPickUpOut pickUpOut = allPickUpOutMapper.selectOne(new QueryWrapper<AllPickUpOut>()
                    .eq("tally_id", out.getTallyId())
                    .eq("waybill_code", out.getWaybillCode())
                    .eq("is_cancel",0));
            if (pickUpOut != null && pickUpOut.getOutTime() != null) {
                throw new CustomException("当前提货已交付");
            }
            LambdaQueryWrapper<AirWaybill> wrapper = new LambdaQueryWrapper<AirWaybill>()
                    .select(AirWaybill::getId, AirWaybill::getConsign, AirWaybill::getSourcePort, AirWaybill::getVersion,
                            AirWaybill::getDesPort, AirWaybill::getWaybillCode, AirWaybill::getQuantity)
                    .eq(AirWaybill::getWaybillCode, out.getWaybillCode())
                    .eq(AirWaybill::getType,"ARR")
                    .eq(AirWaybill::getIsDel,0);
            AirWaybill airWaybill = airWaybillMapper.selectOne(wrapper);
            if(out.getTallyId() != null){
                String token = getToken();
                FlightInfo info = infoMapper.selectArrFlight(out.getTallyId());
                String sendAddress = sysConfigMapper.selectValue("arr.sendAddress");
                MsgJsonVO msgJsonVO = setMsgVo(info, sendAddress);
                try {
                    setFSUMsg(airWaybill,out.getPieces(),out.getWeight(),info,msgJsonVO,token);
                }catch (Exception e){
                    log.error(CABLE_ERROR_MARKER, "运单"+out.getWaybillCode()+"发送FSU-DLV报文失败：" + e.getMessage());
                }
            }
            //是否为快速交付代理人
            List<HzArrQuickDelivery> quickDeliveryList = quickDeliveryMapper.selectListValidity();
            if (quickDeliveryList.size() > 0) {
                List<String> collect = quickDeliveryList.stream().map(HzArrQuickDelivery::getAgent).collect(Collectors.toList());
                if (collect.contains(airWaybill.getConsign())) {
                    WaybillLog log = new WaybillLog();
                    log.setWaybillCode(out.getWaybillCode());
                    log.setOperRemark(0);
                    log.setOperName(SecurityUtils.getNickName());
                    log.setOperWeight(out.getCanPickUpWeight().toString());
                    log.setOperPieces(out.getCanPickUpQuantity().toString());
                    log.setStatus(0);
                    log.setOperParam(JSON.toJSONString(out));
                    log.setOperTime(new Date());
                    log.setType("ARR");
                    log.setJsonResult("msg:操作成功,code:200,data:1");
                    log.setRemark("app快速交付,提货出库件数" + out.getCanPickUpQuantity() + "重量" + out.getCanPickUpWeight());
                    waybillLogService.insertWaybillLog(log);
                    int i = airWaybillMapper.updateDataById(airWaybill.getId(), airWaybill.getVersion());
                    if (i == 0){
                        throw new CustomException("当前运单正在被其他操作，请刷新后再试");
                    }
                    if (pickUpOut != null){
                        pickUpOut.setPieces(out.getCanPickUpQuantity());
                        pickUpOut.setWeight(out.getCanPickUpWeight());
                        pickUpOut.setCreateTime(new Date());
                        pickUpOut.setUpdateTime(new Date());
                        pickUpOut.setOutTime(new Date());
                        return allPickUpOutMapper.updateById(pickUpOut);
                    }else {
                        out.setPieces(out.getCanPickUpQuantity());
                        out.setWeight(out.getCanPickUpWeight());
                        out.setOutTime(new Date());
                        out.setCreateTime(new Date());
                        out.setUpdateTime(new Date());
                        return allPickUpOutMapper.insert(out);
                    }
                }
            }
            PickUp pick = pickUpMapper.selectById(out.getPickUpId());
            if (pick == null) {
                throw new CustomException("当前运单还未办单");
            }
            if (pick.getPickUpCode() == null && pick.getIsPay() != 1) {
                throw new CustomException("当前办单未支付");
            }
            List<PickUpWaybill> waybills = pickUpWaybillMapper.selectList(new QueryWrapper<PickUpWaybill>().eq("pick_up_id", out.getPickUpId()));
            if (CollectionUtils.isEmpty(waybills)) {
                throw new CustomException("无提货运单");
            }
            airWaybillMapper.updateDataById(airWaybill.getId(),airWaybill.getVersion());
            //不是的话要正常走流程
            WaybillLog log = new WaybillLog();
            log.setWaybillCode(out.getWaybillCode());
            log.setOperRemark(0);
            log.setOperName(SecurityUtils.getNickName());
            log.setOperWeight(out.getCanPickUpWeight().toString());
            log.setOperPieces(out.getCanPickUpQuantity().toString());
            log.setStatus(0);
            log.setOperParam(JSON.toJSONString(out));
            log.setOperTime(new Date());
            log.setType("ARR");
            log.setJsonResult("msg:操作成功,code:200,data:1");
            log.setRemark("app快速交付,提货出库件数" + out.getCanPickUpQuantity() + "重量" + out.getCanPickUpWeight());
            waybillLogService.insertWaybillLog(log);
            if (pickUpOut != null){
                pickUpOut.setPieces(out.getCanPickUpQuantity());
                pickUpOut.setWeight(out.getCanPickUpWeight());
                pickUpOut.setCreateTime(new Date());
                pickUpOut.setUpdateTime(new Date());
                pickUpOut.setOutTime(new Date());
                return allPickUpOutMapper.updateById(pickUpOut);
            }else {
                out.setPieces(out.getCanPickUpQuantity());
                out.setWeight(out.getCanPickUpWeight());
                out.setOutTime(new Date());
                out.setUpdateTime(new Date());
                out.setCreateTime(new Date());
                return allPickUpOutMapper.insert(out);
            }
        }catch (Exception e){
            WaybillLog log = new WaybillLog();
            log.setWaybillCode(out.getWaybillCode());
            log.setOperRemark(2);
            log.setOperName(SecurityUtils.getUsername());
            log.setOperWeight(out.getCanPickUpWeight().toString());
            log.setOperPieces(out.getCanPickUpQuantity().toString());
            log.setStatus(1);
            log.setOperParam(JSON.toJSONString(out));
            log.setOperTime(new Date());
            log.setType("ARR");
            log.setErrorMsg(e.getMessage());
            log.setRemark("app快速交付,提货出库件数" + out.getCanPickUpQuantity() + "重量" + out.getCanPickUpWeight());
            waybillLogService.insertWaybillLog(log);
        }
        return 0;
    }

    /**
     *  app快速交付
     * @param query 查询参数
     * @return 快速交付列表
     */
    @Override
    public AppPickOutVo rapidDeliver(AppPickUpQuery query) {
        AppPickOutVo outVo = new AppPickOutVo();
        List<Long> list = pickUpMapper.selectRapidAgent();
        List<AppPickUpVo> vo = pickUpWaybillMapper.rapidDeliver(query,list);
        //可能会有重复 根据运单号去重 并保留有提货数据的项
        ArrayList<AppPickUpVo> appPickUpVos = new ArrayList<>(vo.stream().collect(
                        Collectors.toMap(
                                AppPickUpVo::getWaybillCode,
                                e -> e,
                                (e1, e2) -> e1.getPickUpId() != null ? e1 : e2
                        )).values());
        for (AppPickUpVo appPickUpVo : appPickUpVos) {
            AllPickUpOut pickUpOut = allPickUpOutMapper.selectOutTime(appPickUpVo);
            if (pickUpOut != null){
                appPickUpVo.setStatus("已出库");
            }else {
                appPickUpVo.setStatus("未出库");
            }
        }

        int notOutNum = (int) appPickUpVos.stream().filter(e -> "未出库".equals(e.getStatus())).count();
        outVo.setNotOutNum(notOutNum);
        int outNum = (int) appPickUpVos.stream().filter(e -> "已出库".equals(e.getStatus())).count();
        outVo.setOutNum(outNum);
        outVo.setTotalNum(appPickUpVos.size());

        if ("未出库".equals(query.getStatus())){
            List<AppPickUpVo> collect1 = appPickUpVos.stream().filter(e -> "未出库".equals(e.getStatus())).collect(Collectors.toList());
            outVo.setList(collect1);
            return outVo;
        }
        if ("已出库".equals(query.getStatus())){
            List<AppPickUpVo> collect1 = appPickUpVos.stream().filter(e -> "已出库".equals(e.getStatus())).collect(Collectors.toList());
            outVo.setList(collect1);
            return outVo;
        }
        outVo.setList(appPickUpVos);
        return outVo;
    }

    /**
     * app新增快速交付运单号查询
     * @param waybillCode 运单号
     * @return 详情数据
     */
    @Override
    public PickUpInfoVo waybillCodeQuery(String waybillCode) {
        PickUpInfoVo vo = pickUpWaybillMapper.waybillCodeQuery(waybillCode);
        if (vo != null){
            PickUpInfoVo infoVo = allPickUpOutMapper.getInfo(waybillCode);
            if (infoVo != null){
                vo.setCanPickUpQuantity(infoVo.getCanPickUpQuantity());
                vo.setCanPickUpWeight(infoVo.getCanPickUpWeight());
                vo.setRemark(infoVo.getRemark());
            }
        }
        return vo;
    }

    @Override
    public List<String> checkWaybillCode(String code) {
        return pickUpWaybillMapper.getWaybillCodeByFour(code,"ARR");
    }

    /**
     * 修改提货信息
     * @param vo 提货信息
     * @return 结果
     */
    @Override
    public int editPickUpData(PickUpOutWaybillVo vo) {
        try {
            AllPickUpOut pickUpOut = allPickUpOutMapper.selectOne(new QueryWrapper<AllPickUpOut>()
                    .eq("tally_id", vo.getTallyId())
                    .eq("pick_up_id", vo.getPickUpId())
                    .eq("waybill_code", vo.getWaybillCode()));
            pickUpOut.setPiecesChanged(vo.getPieces());
            pickUpOut.setWeightChanged(vo.getWeight());
            pickUpOut.setPickUpCode(vo.getPickUpCode());
            pickUpOut.setUpdateTime(new Date());

            //同时修改办单数据
            PickUpWaybill pickUpWaybill = pickUpWaybillMapper.selectById(pickUpOut.getId());
            pickUpWaybill.setCanPickUpQuantity(vo.getPieces());
            pickUpWaybill.setCanPickUpWeight(vo.getWeight());
            pickUpWaybill.setUpdateTime(new Date());
            pickUpWaybill.setUpdateBy(SecurityUtils.getUsername());
            pickUpWaybillMapper.updateById(pickUpWaybill);

            //修改提货数据后需要重新结算 多退少补
            settleForPickUpEdit(pickUpOut,pickUpWaybill);


            WaybillLog log = new WaybillLog();
            log.setWaybillCode(vo.getWaybillCode());
            log.setOperRemark(2);
            log.setOperName(SecurityUtils.getNickName());
            log.setOperWeight(vo.getWeight().toString());
            log.setOperPieces(vo.getPieces().toString());
            log.setStatus(0);
            log.setOperParam(JSON.toJSONString(vo));
            log.setOperTime(new Date());
            log.setJsonResult("msg:操作成功,code:200,data:1");
            log.setRemark("提货出库，提货件数："+vo.getPieces()+"，提货重量："+vo.getWeight()+"，提货码："+vo.getPickUpCode()+"");
            waybillLogService.insertWaybillLog(log);
            return allPickUpOutMapper.updateById(pickUpOut);
        }catch (Exception e){
            WaybillLog log = new WaybillLog();
            log.setWaybillCode(vo.getWaybillCode());
            log.setOperRemark(2);
            log.setOperName(SecurityUtils.getUsername());
            log.setOperWeight(vo.getWeight().toString());
            log.setOperPieces(vo.getPieces().toString());
            log.setStatus(1);
            log.setOperParam(JSON.toJSONString(vo));
            log.setOperTime(new Date());
            log.setErrorMsg(e.getMessage());
            log.setRemark("提货出库，提货件数："+vo.getPieces()+"，提货重量："+vo.getWeight()+"，提货码："+vo.getPickUpCode()+"");
            waybillLogService.insertWaybillLog(log);
        }
        return 0;
    }


    private void settleForPickUpEdit(AllPickUpOut pickUpOut, PickUpWaybill upWaybill) {
        if (pickUpOut == null){
            return;
        }
        if (pickUpOut.getWeightChanged() == null){
            return;
        }
        if (Objects.equals(pickUpOut.getWeight(),pickUpOut.getWeightChanged())){
            return;
        }
        BigDecimal totalCost = new BigDecimal(0);
        BigDecimal oldTotalCost = new BigDecimal(0);
        List<HzArrTally> hzArrTallyList = tallyMapper.selectList(new QueryWrapper<HzArrTally>()
                .eq("waybill_code", upWaybill.getWaybillCode())
                .eq("status", "save_out"));
        if(hzArrTallyList.size() == 0){
           return;
        }
        List<Long> tallyIds = hzArrTallyList.stream().map(HzArrTally::getId).collect(Collectors.toList());
        List<HzArrItem> items = hzArrItemMapper.selectCostListByCodeAndTallyList(upWaybill.getWaybillCode(),tallyIds);
        Integer status = mawbMapper.selectBillStatus(upWaybill.getWaybillCode());
        if (!CollectionUtils.isEmpty(items) && status > 4 && status < 9){
            oldTotalCost = items.stream().map(HzArrItem::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        Mawb mawb = mawbMapper.selectOne(new QueryWrapper<Mawb>()
                .eq("waybill_code",upWaybill.getWaybillCode())
                .eq("type","ARR")
                .eq("is_del",0));
        BigDecimal weightRateCeiling;
        BigDecimal weightRateFloor;
        BigDecimal chargeWeight = mawb.getChargeWeight() == null ? new BigDecimal(0) : mawb.getChargeWeight();
        if (mawb.getWeight() == null || mawb.getWeight().compareTo(new BigDecimal(0)) == 0){
            weightRateCeiling = new BigDecimal(0);
            weightRateFloor = new BigDecimal(0);
        }else {
            BigDecimal bigDecimal = chargeWeight.divide(mawb.getWeight(),5, RoundingMode.DOWN).multiply(pickUpOut.getWeight());
            weightRateCeiling = bigDecimal.setScale(0, RoundingMode.CEILING);
            weightRateFloor = bigDecimal.setScale(0, RoundingMode.FLOOR);
        }
        for (HzArrItem item : items) {
            BillRuleVo vo1 = countCost(item, weightRateCeiling, pickUpOut.getPieces());
            BillRuleVo vo2 = countCost(item, weightRateFloor, pickUpOut.getPieces());
            if (vo1.getTotalCharge().compareTo(item.getTotalCharge()) >= 0){
                item.setTotalCharge(vo1.getTotalCharge());
                totalCost = totalCost.add(vo1.getTotalCharge());
            }else {
                item.setTotalCharge(vo2.getTotalCharge());
                totalCost = totalCost.add(vo2.getTotalCharge());
            }
            hzArrItemMapper.updateById(item);
        }
        WaybillFee waybillFee = feeMapper.selectOne(new QueryWrapper<WaybillFee>()
                .eq("waybill_code", mawb.getWaybillCode())
                .eq("dept_id", mawb.getDeptId())
                .eq("type","DEP"));
        BaseAgent agent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", mawb.getDeptId()));
        BigDecimal refund = new BigDecimal(0);
        if (agent != null){
            switch (agent.getSettleMethod()){
                case 0:
                    if (totalCost.compareTo(oldTotalCost) < 0){
                        BigDecimal add = oldTotalCost.subtract(totalCost);
                        updateStatus(mawb, totalCost, waybillFee, 9, add);
                    }else {
                        updateStatus(mawb, totalCost, waybillFee, 5, refund);
                    }
                    break;
                case 1:
                    if (totalCost.compareTo(oldTotalCost) > 0){
                        BigDecimal deduct = totalCost.subtract(oldTotalCost);
                        BigDecimal balance = agent.getBalance() == null ? new BigDecimal(0) : agent.getBalance();
                        BigDecimal subtract = balance.subtract(deduct);
                        if (subtract.compareTo(new BigDecimal(0)) < 0) {
                            throw new CustomException("代理人余额不足");
                        } else {
                            agent.setBalance(subtract);
                            baseAgentMapper.updateBaseAgent(agent);
                            BaseBalance baseBalance = new BaseBalance();
                            baseBalance.setAgentId(agent.getId());
                            baseBalance.setBalance(agent.getBalance());
                            baseBalance.setType("减少余额");
                            baseBalance.setCreateTime(new Date());
                            baseBalance.setCreateBy(SecurityUtils.getNickName());
                            // todo 流水号需从银联支付接口获取
                            //baseBalance.setSerialNo();
                            baseBalance.setTradeMoney(deduct);
                            baseBalance.setWaybillCode(mawb.getWaybillCode());
                            baseBalance.setRemark("修改提货信息");
                            baseBalanceMapper.insertBaseBalance(baseBalance);
                            updateStatus(mawb, totalCost, waybillFee, 6, refund);
                        }
                    }else if (totalCost.compareTo(oldTotalCost) == 0){
                        updateStatus(mawb, totalCost, waybillFee, 6, refund);
                    }else {
                        BigDecimal add = oldTotalCost.subtract(totalCost);
                        BigDecimal balance = agent.getBalance() == null ? new BigDecimal(0) : agent.getBalance();
                        BigDecimal subtract = balance.add(add);
                        agent.setBalance(subtract);
                        baseAgentMapper.updateBaseAgent(agent);
                        BaseBalance baseBalance = new BaseBalance();
                        baseBalance.setAgentId(agent.getId());
                        baseBalance.setBalance(agent.getBalance());
                        baseBalance.setType("增加余额");
                        baseBalance.setCreateTime(new Date());
                        baseBalance.setCreateBy(SecurityUtils.getNickName());
                        // todo 流水号需从银联支付接口获取
                        //baseBalance.setSerialNo();
                        baseBalance.setTradeMoney(add);
                        baseBalance.setWaybillCode(mawb.getWaybillCode());
                        baseBalance.setRemark("修改提货信息");
                        baseBalanceMapper.insertBaseBalance(baseBalance);
                        updateStatus(mawb, totalCost, waybillFee, 10, add);
                    }
                    break;
                case 2:
                    if (totalCost.compareTo(oldTotalCost) < 0){
                        BigDecimal add = oldTotalCost.subtract(totalCost);
                        if (agent.getPayMethod() == 0){
                            updateStatus(mawb, totalCost, waybillFee, 11, add);
                        }else {
                            updateStatus(mawb, totalCost, waybillFee, 12, add);
                        }
                    }else {
                        if (agent.getPayMethod() == 0){
                            updateStatus(mawb, totalCost, waybillFee, 7, refund);
                        }else {
                            updateStatus(mawb, totalCost, waybillFee, 8, refund);
                        }
                    }
                    break;
                default:
                    updateStatus(mawb, totalCost, waybillFee, 8, refund);
                    break;
            }
        }
        upWaybill.setCostSum(totalCost);
        waybillService.updateById(upWaybill);
        List<PickUpWaybill> pickUpWaybills = pickUpWaybillMapper.selectList(new QueryWrapper<PickUpWaybill>()
                .eq("pick_up_id", pickUpOut.getPickUpId()).eq("is_cancel",0));
        BigDecimal costSum = pickUpWaybills.stream()
                .map(PickUpWaybill::getCostSum)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //修改办理提货表的金额
        PickUp pickUp = pickUpMapper.selectById(pickUpOut.getPickUpId());
        if (costSum.compareTo(new BigDecimal(0)) != 0 && costSum.compareTo(pickUp.getTotalCost()) != 0){
            pickUp.setTotalCost(costSum);
        }
        pickUp.setPayStatus(2);
        pickUp.setUpdateTime(new Date());
        pickUpMapper.updateById(pickUp);
    }

    public BillRuleVo countCost(HzArrItem item, BigDecimal weight, Integer quantity) {
        HzChargeIrRelation relation = relationMapper.selectById(item.getIrId());
        HzChargeItems hzChargeItems = chargeItemsMapper.selectById(relation.getItemId());
        HzChargeRule hzChargeRule = ruleMapper.selectById(relation.getRuleId());
        List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>().eq("ir_id", item.getIrId()));
        if (CollectionUtils.isEmpty(itemRules)){
            BillRuleVo vo = new BillRuleVo();
            vo.setTotalCharge(new BigDecimal(0));
            return vo;
        }
        BillingRule rule = BillingRuleFactory.createRule(hzChargeRule.getClassName());
        BillRuleVo ruleVo = rule.calculateFee(itemRules, weight, quantity, item);
        BigDecimal totalCharge = BigDecimalRoundUtils.bigDecimalRound(hzChargeItems.getRoundRule(), ruleVo.getTotalCharge());
        ruleVo.setTotalCharge(totalCharge);
        return ruleVo;
    }

    private void updateStatus(Mawb mawb, BigDecimal costSum, WaybillFee waybillFee, int mawbStatus, BigDecimal refund) {
        if (waybillFee != null) {
            waybillFee.setSettleTime(new Date());
            waybillFee.setSettleMoney(costSum);
            waybillFee.setRefund(refund);
            waybillFee.setStatus(1);
            feeMapper.updateById(waybillFee);
        } else {
            WaybillFee fee = new WaybillFee();
            fee.setSettleMoney(costSum);
            fee.setRefund(refund);
            fee.setSettleTime(new Date());
            fee.setWaybillCode(mawb.getWaybillCode());
            fee.setDeptId(mawb.getDeptId());
            fee.setStatus(1);
            fee.setType("ARR");
            feeMapper.insert(fee);
        }
        mawb.setPayStatus(mawbStatus);
        mawb.setRefund(refund);
        mawb.setSettleTime(new Date());
        mawbMapper.updateById(mawb);
    }


    @Override
    public int transferSouthernAirlines(Long pickUpId) {
        Integer pickUpOutCount = allPickUpOutMapper.selectCount(new QueryWrapper<AllPickUpOut>()
                .eq("pick_up_id", pickUpId));
        if(pickUpOutCount > 0){
            throw new CustomException("已提货，无法转南航");
        }
        //去查询这个单是否转南航
        List<HzArrRecordOrder> hzArrRecordOrders = allPickUpOutMapper.selectTransferSouthByPickUpId(pickUpId);
        if(hzArrRecordOrders.size() > 0){
            hzArrRecordOrders.forEach(e->{
                if(e.getIsSouth()!=null && "1".equals(e.getIsSouth())){
                    throw new CustomException("当前单已转南航");
                }
                allPickUpOutMapper.updateTransferSouth(e.getId());
            });
        }
        return 1;
    }

    @Override
    public PickUpOutVo batchPickUpOut(Long pickUpId) {
        PickUp pickUp = pickUpMapper.selectById(pickUpId);
        if (pickUp == null){
            throw new CustomException("请重新办单");
        }
        if (pickUp.getIsPay() == 2){
            throw new CustomException("当前办单已作废");
        }
        List<PickUpWaybill> pickUpWaybills = pickUpWaybillMapper.selectList(new QueryWrapper<PickUpWaybill>().eq("pick_up_id", pickUpId).eq("is_cancel",0));
        String token = getToken();
        for (PickUpWaybill pickUpWaybill : pickUpWaybills) {
            AllPickUpOut pickUpOut = allPickUpOutMapper.selectOne(new QueryWrapper<AllPickUpOut>()
                    .eq("tally_id", pickUpWaybill.getTallyId())
                    .eq("pick_up_id", pickUpId)
                    .eq("waybill_code", pickUpWaybill.getWaybillCode())
                    .eq("is_cancel",0));
            if (pickUpOut != null){
                pickUpOut.setPieces(pickUpWaybill.getCanPickUpQuantity());
                pickUpOut.setWeight(pickUpWaybill.getCanPickUpWeight());
                pickUpOut.setUpdateTime(new Date());
                allPickUpOutMapper.updateById(pickUpOut);
            }else {
                AllPickUpOut out = new AllPickUpOut();
                out.setTallyId(pickUpWaybill.getTallyId());
                out.setPieces(pickUpWaybill.getCanPickUpQuantity());
                out.setWeight(pickUpWaybill.getCanPickUpWeight());
                out.setWaybillCode(pickUpWaybill.getWaybillCode());
                out.setPickUpCode(pickUp.getPickUpCode());
                out.setPickUpId(pickUpId);
                out.setCreateTime(new Date());
                out.setUpdateTime(new Date());
                allPickUpOutMapper.insert(out);
            }
            LambdaQueryWrapper<AirWaybill> wrapper = new LambdaQueryWrapper<AirWaybill>()
                    .select(AirWaybill::getConsign, AirWaybill::getSourcePort, AirWaybill::getDesPort, AirWaybill::getWaybillCode, AirWaybill::getQuantity)
                    .eq(AirWaybill::getWaybillCode, pickUpWaybill.getWaybillCode())
                    .eq(AirWaybill::getType,"ARR")
                    .eq(AirWaybill::getIsDel,0);
            AirWaybill airWaybill = airWaybillMapper.selectOne(wrapper);
            FlightInfo info = infoMapper.selectArrFlight(pickUpWaybill.getTallyId());
            String sendAddress = sysConfigMapper.selectValue("arr.sendAddress");
            MsgJsonVO msgJsonVO = setMsgVo(info, sendAddress);
            try {
                setFSUMsg(airWaybill,pickUpWaybill.getCanPickUpQuantity(),pickUpWaybill.getCanPickUpWeight(),info,msgJsonVO,token);
            }catch (Exception e){
                log.error(CABLE_ERROR_MARKER, "运单"+airWaybill.getWaybillCode()+"发送FSU-DLV报文失败：" + e.getMessage());
            }
        }
        return pickUpOut(pickUpId);
    }

    /**
     * 提货出库
     * @param pickUpId 办理提货id
     * @return 结果
     */
    @Override
    public PickUpOutVo pickUpOut(Long pickUpId) {
        PickUp pick = pickUpMapper.selectById(pickUpId);
        if (pick == null){
            throw new CustomException("请重新办单");
        }
        if (pick.getIsPay() == 2){
            throw new CustomException("当前办单已作废");
        }
        List<PickUpWaybill> upWaybills = pickUpWaybillMapper.selectList(new QueryWrapper<PickUpWaybill>()
                .eq("pick_up_id", pickUpId)
                .eq("is_cancel",0));
        List<AllPickUpOut> pickUpOuts = allPickUpOutMapper.selectList(new QueryWrapper<AllPickUpOut>()
                .eq("pick_up_id", pickUpId)
                .eq("is_cancel",0));
        if (CollectionUtils.isEmpty(pickUpOuts)){
            throw new CustomException("当前办单数据未核对");
        }
        List<AllPickUpOut> outs = pickUpOuts.stream().filter(e -> e.getOutTime() == null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(outs)){
            throw new CustomException("已提货出库");
        }
        ArrayList<WaybillLog> waybillLogs = new ArrayList<>();
        HttpServletResponse response = ServletUtils.getResponse();
        for (AllPickUpOut out:outs) {
            //运单日志的新增
            WaybillLog waybillLog = waybillLogService.getWaybillLog(
                    out.getWaybillCode(), 1, SecurityUtils.getNickName(),
                    out.getWeight().toString(), out.getPieces().toString(), null,
                    pickUpId.toString(), null, 0, null, new Date(),
                    "提货出库,提货件数:" +  out.getPieces() + ",提货重量:" +  out.getWeight() + ",提货码:" + out.getPickUpCode(),
                    "ARR", null);
            waybillLogs.add(waybillLog);

            //将所有的理货历史状态改为已出库
            List<HzArrTally> hzArrTallyList = tallyMapper.selectList(new QueryWrapper<HzArrTally>()
                    .eq("waybill_code", out.getWaybillCode())
                    .eq("status", "settle"));
            hzArrTallyList.forEach(hzArrTally -> {
                hzArrTally.setStatus("save_out");
                tallyMapper.updateById(hzArrTally);
            });
        }
        try {
            if (!CollectionUtils.isEmpty(upWaybills)) {
                List<PickUpWaybill> pickUpWaybills = upWaybills.stream().filter(e -> pickUpOuts.stream().map(AllPickUpOut::getTallyId).noneMatch(id -> Objects.equals(e.getTallyId(), id))).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(pickUpWaybills)) {
                    StringBuilder builder = new StringBuilder();
                    for (int i = 0; i < pickUpWaybills.size(); i++) {
                        if (i == 0){
                            builder.append("运单号 ").append(pickUpWaybills.get(i).getWaybillCode()).append(" 未核对");
                        }else{
                            builder.append("<br/>" + "运单号 ").append(pickUpWaybills.get(i).getWaybillCode()).append(" 未核对");
                        }
                    }
                    throw new CustomException(builder.toString());
                }
            }
            Date date = new Date();
            PickUpOutVo pickUpOutVo = pickUpMapper.selectPickUpOutVo(pickUpId);
            int sum = upWaybills.stream().mapToInt(PickUpWaybill::getCanPickUpQuantity).sum();
            pickUpOutVo.setTotalQuantity(sum);
            BigDecimal reduce = upWaybills.stream().map(PickUpWaybill::getCanPickUpWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            pickUpOutVo.setTotalWeight(reduce);
            pickUpOutVo.setNum(upWaybills.size());
            List<AllPickUpOut> collect = pickUpOuts.stream().peek(e -> e.setOutTime(new Date())).collect(Collectors.toList());
            this.saveOrUpdateBatch(collect);
            String token = getToken();
            for (AllPickUpOut pickUpOut : pickUpOuts) {
                BigDecimal weight = airWaybillMapper.selectWeight(pickUpOut.getWaybillCode());
                List<AllPickUpOut> waybillList = allPickUpOutMapper.selectList(new QueryWrapper<AllPickUpOut>().eq("waybill_code", pickUpOut.getWaybillCode()));
                BigDecimal allWeight = waybillList.stream()
                        .map(out -> out.getWeightChanged() != null ? out.getWeightChanged() : out.getWeight())
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                if (weight.compareTo(allWeight) <= 0){
                    HzColdRegister hzColdRegister = coldRegisterMapper.selectOne(new QueryWrapper<HzColdRegister>()
                            .eq("waybill_code", pickUpOut.getWaybillCode())
                            .eq("type", "ARR").ne("status",2));
                    if (hzColdRegister != null && hzColdRegister.getOutTime() == null){
                        long out = date.getTime();
                        long ware = hzColdRegister.getWareTime().getTime();
                        long timeDiff = (out - ware) / (1000 * 60 * 60);
                        BigDecimal time = new BigDecimal(timeDiff);
                        BigDecimal bigDecimal = time.setScale(0, RoundingMode.UP);
                        hzColdRegister.setOutTime(date);
                        hzColdRegister.setChargeTime(bigDecimal);
                        hzColdRegister.setUseTime(time);
                        hzColdRegister.setStatus(2);
                        List<HzArrItem> items = hzArrItemMapper.selectList(new QueryWrapper<HzArrItem>()
                                .eq("waybill_code", pickUpOut.getWaybillCode())
                                .eq("service_type", 2)
                                .eq("is_settle",0));
                        if (!CollectionUtils.isEmpty(items)) {
                            BigDecimal costSum = new BigDecimal(0);
                            BigDecimal oldSum = items.stream().map(HzArrItem::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                            for (HzArrItem item : items) {
                                HzChargeIrRelation relation = relationMapper.selectById(item.getIrId());
                                if (relation == null) {
                                    continue;
                                }
                                HzChargeItems hzChargeItems = chargeItemsMapper.selectById(relation.getItemId());
                                item.setStoreEndTime(date);
                                item.setIsSettle(1);
                                item.setIsAuto(0);
                                List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>().eq("ir_id",relation.getId()));
                                BillingRule rule = BillingRuleFactory.createRule("ColdStorageBillingRule.class");
                                BillRuleVo vo1 = rule.calculateFee(itemRules, new BigDecimal(0), 1, item);
                                BigDecimal totalCharge = BigDecimalRoundUtils.bigDecimalRound(hzChargeItems.getRoundRule(), vo1.getTotalCharge());
                                costSum = costSum.add(totalCharge);
                                item.setTotalCharge(totalCharge);
                                hzArrItemMapper.updateById(item);
                            }
                            hzColdRegister.setSumMoney(costSum);
                            BaseAgent agent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", hzColdRegister.getDeptId()));
                            if (costSum.compareTo(oldSum) > 0){
                                BigDecimal deduct = costSum.subtract(oldSum);
                                if (agent != null) {
                                    if (agent.getSettleMethod() == 1) {
                                        BigDecimal balance = agent.getBalance() == null ? new BigDecimal(0) : agent.getBalance();
                                        BigDecimal subtract = balance.subtract(deduct);
                                        if (subtract.compareTo(new BigDecimal(0)) < 0) {
                                            throw new CustomException("当前代理人余额不足");
                                        } else {
                                            agent.setBalance(subtract);
                                            baseAgentMapper.updateBaseAgent(agent);
                                            BaseBalance baseBalance = new BaseBalance();
                                            baseBalance.setAgentId(agent.getId());
                                            baseBalance.setBalance(agent.getBalance());
                                            baseBalance.setType("减少余额");
                                            baseBalance.setCreateTime(new Date());
                                            baseBalance.setCreateBy("系统");
                                            // todo 流水号需从银联支付接口获取
                                            //baseBalance.setSerialNo();
                                            baseBalance.setTradeMoney(deduct);
                                            baseBalance.setWaybillCode(hzColdRegister.getWaybillCode());
                                            baseBalance.setRemark("冷藏申请支付");
                                            baseBalanceMapper.insertBaseBalance(baseBalance);
                                            hzColdRegister.setPayStatus(6);
                                        }
                                    } else if (agent.getSettleMethod() == 0) {
                                        hzColdRegister.setPayStatus(5);
                                    } else {
                                        if (agent.getPayMethod() == 0) {
                                            hzColdRegister.setPayStatus(7);
                                        } else {
                                            hzColdRegister.setPayStatus(8);
                                        }
                                    }
                                } else {
                                    hzColdRegister.setPayStatus(8);
                                }
                            }else if (costSum.compareTo(oldSum) == 0){
                                if (agent != null) {
                                    if (agent.getSettleMethod() == 1) {
                                        hzColdRegister.setPayStatus(6);
                                    } else if (agent.getSettleMethod() == 0) {
                                        hzColdRegister.setPayStatus(5);
                                    } else {
                                        if (agent.getPayMethod() == 0) {
                                            hzColdRegister.setPayStatus(7);
                                        } else {
                                            hzColdRegister.setPayStatus(8);
                                        }
                                    }
                                } else {
                                    hzColdRegister.setPayStatus(8);
                                }
                            }else {
                                BigDecimal add = oldSum.subtract(costSum);
                                if (agent != null){
                                    if (agent.getSettleMethod() == 1) {
                                        BigDecimal balance = agent.getBalance() == null ? new BigDecimal(0) : agent.getBalance();
                                        BigDecimal subtract = balance.add(add);
                                        agent.setBalance(subtract);
                                        baseAgentMapper.updateBaseAgent(agent);
                                        BaseBalance baseBalance = new BaseBalance();
                                        baseBalance.setAgentId(agent.getId());
                                        baseBalance.setBalance(agent.getBalance());
                                        baseBalance.setType("增加余额");
                                        baseBalance.setCreateTime(new Date());
                                        baseBalance.setCreateBy("系统");
                                        // todo 流水号需从银联支付接口获取
                                        //baseBalance.setSerialNo();
                                        baseBalance.setTradeMoney(add);
                                        baseBalance.setWaybillCode(hzColdRegister.getWaybillCode());
                                        baseBalance.setRemark("冷藏申请退款");
                                        baseBalanceMapper.insertBaseBalance(baseBalance);
                                        hzColdRegister.setPayStatus(10);
                                    } else if (agent.getSettleMethod() == 0){
                                        hzColdRegister.setPayStatus(9);
                                    }else {
                                        if (agent.getPayMethod() == 0){
                                            hzColdRegister.setPayStatus(11);
                                        }else {
                                            hzColdRegister.setPayStatus(12);
                                        }
                                    }
                                }else {
                                    hzColdRegister.setPayStatus(12);
                                }
                            }
                        }
                        hzColdRegister.setUpdateTime(new Date());
                        coldRegisterMapper.updateById(hzColdRegister);
                    }
                }
                LambdaQueryWrapper<AirWaybill> wrapper = new LambdaQueryWrapper<AirWaybill>()
                        .select(AirWaybill::getId, AirWaybill::getConsign, AirWaybill::getSourcePort, AirWaybill::getVersion,
                                AirWaybill::getDesPort, AirWaybill::getWaybillCode, AirWaybill::getQuantity)
                        .eq(AirWaybill::getWaybillCode, pickUpOut.getWaybillCode())
                        .eq(AirWaybill::getType,"ARR")
                        .eq(AirWaybill::getIsDel,0);
                AirWaybill airWaybill = airWaybillMapper.selectOne(wrapper);
                FlightInfo info = infoMapper.selectArrFlight(pickUpOut.getTallyId());
                String sendAddress = sysConfigMapper.selectValue("arr.sendAddress");
                MsgJsonVO msgJsonVO = setMsgVo(info, sendAddress);
                try {
                    setFSUMsg(airWaybill,pickUpOut.getPieces(),pickUpOut.getWeight(),info,msgJsonVO,token);
                }catch (Exception e){
                    log.error(CABLE_ERROR_MARKER, "运单"+pickUpOut.getWaybillCode()+"发送FSU-DLV报文失败：" + e.getMessage());
                }
                int i = airWaybillMapper.updateDataById(airWaybill.getId(), airWaybill.getVersion());
                if (i == 0){
                    throw new CustomException("当前运单正在被其他操作，请刷新后再试");
                }
            }
            for (WaybillLog waybillLog:waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作成功" +  "," +
                                "code:" + response.getStatus() + "," +
                                "data:" + pickUpOutVo));
            }
            return pickUpOutVo;
        }catch (Exception e){
            for (WaybillLog waybillLog:waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作失败" +  "," +
                                "code:" + response.getStatus()));
                waybillLog.setErrorMsg(e.getMessage());
                waybillLog.setStatus(1);
            }
            throw new CustomException(e.getMessage());
        }finally {
            for (WaybillLog waybillLog:waybillLogs) {
                waybillLogService.insertWaybillLog(waybillLog);
            }
        }
    }

    private MsgJsonVO setMsgVo(FlightInfo info, String sendAddress){
        MsgJsonVO vo = new MsgJsonVO();
        vo.setCarrier(info.getAirWays());
        vo.setDepartureStation(info.getStartStation());
        vo.setMsgType("FSU");
        vo.setNextStation(info.getTerminalStation());
        vo.setOperationNode("DLV");
        vo.setOperationStation("KWE");
        if (StringUtils.isNotEmpty(sendAddress)) {
            vo.setOrigin(sendAddress.split(","));
        }else {
            vo.setOrigin(new String[]{"KWEFDCN"});
        }
        vo.setPriority(new String[]{"QD"});
        vo.setSourceId("44162409105767715");
        vo.setUniqueId("44162409105767715");
        vo.setMsgVersion("12");
        BaseCarrier baseCarrier = carrierMapper.selectByCode(info.getAirWays());
        if (baseCarrier != null){
            vo.setWaybillPrefix(baseCarrier.getPrefix());
        }
        return vo;
    }

    private String getToken(){
        System.out.println("*********调用登录接口获取token*********");
        String token = "";
        HttpHeaders headers = setHeaders();
        HttpEntity<?> httpEntity = new HttpEntity<>(headers);
        ResponseEntity<JSONObject> exchange = restTemplate.exchange(loginUrl + account, HttpMethod.GET, httpEntity, JSONObject.class);
        if (exchange.getBody() != null) {
            if (!"0".equals(exchange.getBody().getString("code"))) {
                throw new CustomException(exchange.getBody().getString("message"));
            }
            JSONObject data = exchange.getBody().getJSONObject("data");
            token = data.getString("token");
            System.out.println(token);
        }
        return token;
    }

    private HttpHeaders setHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType("application/json;charset=UTF-8"));
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        headers.add("Accept-Charset", "UTF-8");
        return headers;
    }

    private void restExchange(MsgJsonVO vo, String token, StringBuilder sb){
        HttpHeaders header = setHeaders();
        header.add("X-Access-Token", token);
        HttpEntity<?> httpEntity = new HttpEntity<>(vo, header);
        System.out.println("参数：" + JSON.toJSONString(vo));
        ResponseEntity<JSONObject> exchange = restTemplate.exchange(getMsg, HttpMethod.POST, httpEntity, JSONObject.class);
        if (exchange.getBody() != null) {
            if (!"0".equals(exchange.getBody().getString("code"))) {
                throw new CustomException(exchange.getBody().getString("msg"));
            }
            JSONObject data = exchange.getBody().getJSONObject("data");
            String msgContent = data.getString("msgContent");
            sb.append(msgContent).append("\n");
        }
    }

    private void setFSUMsg(AirWaybill airWaybill, Integer pieces, BigDecimal weight,FlightInfo info,MsgJsonVO msgJsonVO,String token){
        List<HzCableAddress> addressList = cableAddressMapper.selectAddressByAirportCode(Collections.singletonList(airWaybill.getDesPort()));
        StringBuilder sb = new StringBuilder();
        FSUJsonVO fsuJsonVO = new FSUJsonVO();
        fsuJsonVO.setDepAirport(airWaybill.getSourcePort());
        fsuJsonVO.setDesAirport(airWaybill.getDesPort());
        if(airWaybill.getWaybillCode().startsWith("AWBA")){
            String substring = airWaybill.getWaybillCode().substring(4);
            StringBuilder stringBuilder = new StringBuilder(substring);
            fsuJsonVO.setMawbNo(stringBuilder.insert(3, "-").toString());
        }else{
            String substring = airWaybill.getWaybillCode().substring(4);
            StringBuilder stringBuilder = new StringBuilder(substring);
            fsuJsonVO.setMawbNo(stringBuilder.insert(2, "-").toString());
        }
        fsuJsonVO.setPieces(pieces.toString());
        if (!airWaybill.getQuantity().equals(pieces)){
            fsuJsonVO.setShipmentDescriptionCode("P");
            fsuJsonVO.setTotalPieces(airWaybill.getQuantity().toString());
        }else {
            fsuJsonVO.setShipmentDescriptionCode("T");
        }
        List<StatusDetails> statusDetails = new ArrayList<>();
        StatusDetails statusDetail = new StatusDetails();
        statusDetail.setMovementAirport(info.getStartStation());
        statusDetail.setMovementArrivalAirport(info.getTerminalStation());
        statusDetail.setMovementCarrier(info.getAirWays());
        statusDetail.setMovementDate(LocalDateTime.now().format(DATE_FORMATTER));
        statusDetail.setMovementTime(LocalDateTime.now().format(TIME_FORMATTER));
        statusDetail.setMovementDepartureAirport(info.getStartStation());
        statusDetail.setMovementFlightNo(info.getFlightNo());
        statusDetail.setPieces(pieces.toString());
        statusDetail.setReceivingCarrier("");
        statusDetail.setReportingAirport("");
        statusDetail.setReportingDate(DATE_FORMAT.format(new Date()));
        statusDetail.setReportingTime("");
        statusDetail.setShipmentDescriptionCode(fsuJsonVO.getShipmentDescriptionCode());
        statusDetail.setStatusCode("DLV");
        statusDetail.setVolume("");
        statusDetail.setVolumeUnit("MC");
        statusDetail.setWeight(weight.toString());
        statusDetail.setWeightUnit("K");
        statusDetails.add(statusDetail);
        fsuJsonVO.setStatusDetails(statusDetails);
        msgJsonVO.setMsgJson(JSON.toJSONString(fsuJsonVO));
        Map<String, List<HzCableAddress>> addressMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(addressList)){
            addressMap = addressList.stream().collect(Collectors.groupingBy(HzCableAddress::getAirportCode));
        }
        List<HzCableAddress> cableAddresses = addressMap.get(airWaybill.getDesPort());
        setInteractionType(cableAddresses,msgJsonVO);
        restExchange(msgJsonVO,token,sb);
        insertCableAndSendMsg(msgJsonVO,info,sb,token);
    }

    public void setInteractionType(List<HzCableAddress> cableAddresses, MsgJsonVO vo) {
        List<String> sendType = new ArrayList<>();
        if (cableAddresses == null || cableAddresses.isEmpty()) {
            sendType.add("FD");
            vo.setSendType("FD");
            vo.setAddress(new String[]{"-"});
            return;
        }
        List<String> sendTypeCn = cableAddresses.stream()
                .map(HzCableAddress::getInteractionTypes)
                .filter(StringUtils::isNotEmpty)
                .map(s -> s.split(","))
                .flatMap(Arrays::stream)
                .map(String::trim)
                .distinct()
                .collect(Collectors.toList());
        for (String type : sendTypeCn) {
            switch (type) {
                case "民航局报文":
                    sendType.add("FD");
                    break;
                case "邮箱报文":
                    sendType.add("MAIL");
                    break;
                case "FTP收发报文":
                    sendType.add("FTP");
                    break;
                case "rabbitmq收发报文":
                    sendType.add("MQ");
                    break;
                default:
                    break;

            }
        }
        if (!sendType.contains("FD")){
            sendType.add("FD");
        }
        vo.setSendType(String.join(",", sendType));
        List<String> emailAddresses = new ArrayList<>();
        List<String> caacAddresses = new ArrayList<>();
        List<String> ftpList = new ArrayList<>();
        List<String> mqQueueList = new ArrayList<>();
        for (HzCableAddress cableAddress : cableAddresses) {
            List<String> interactionTypeList = new ArrayList<>();
            if (StringUtils.isNotEmpty(cableAddress.getInteractionTypes())) {
                interactionTypeList = Arrays.asList(cableAddress.getInteractionTypes().split(","));
            }
            if (!CollectionUtils.isEmpty(interactionTypeList)) {
                for (String type : interactionTypeList) {
                    switch (type) {
                        case "邮箱报文":
                            emailAddresses.add(cableAddress.getEmailAddress());
                            break;
                        case "FTP收发报文":
                            ftpList.add(cableAddress.getFtpList());
                            break;
                        case "民航局报文":
                            caacAddresses.add(cableAddress.getCaacAddress());
                            break;
                        case "rabbitmq收发报文":
                            mqQueueList.add(cableAddress.getMqQueue());
                            break;
                        default:
                            break;
                    }
                }
            }
        }
        if (!emailAddresses.isEmpty()) {
            vo.setReceiveMailAddress(emailAddresses.toArray(new String[0]));
        }
        if (!ftpList.isEmpty()) {
            vo.setReceiveFtpFolder(String.join(",",ftpList));
        }
        if (!mqQueueList.isEmpty()) {
            vo.setReceiveMQQueue(String.join(",",mqQueueList));
        }
        if (!caacAddresses.isEmpty()) {
            vo.setAddress(caacAddresses.toArray(new String[0]));
        }else {
            vo.setAddress(new String[]{"-"});
        }
    }

    private void insertCableAndSendMsg(MsgJsonVO vo, FlightInfo info, StringBuilder builder,String token) {
        if (StringUtils.isEmpty(vo.getSendType())){
            return;
        }
        String[] sendTypes = vo.getSendType().split(",");
        for (String sendType : sendTypes) {
            HzCable cable = new HzCable();
            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("ddHHmm");
            cable.setSerialNo(SerialNumberGenerator.generateSerialNumber());
            cable.setCableNo(sdf.format(date));
            cable.setCableTime(date);
            cable.setCreateBy("系统");
            cable.setIsSend(1);
            cable.setIsAuto(1);
            cable.setType("FSU");
            cable.setVersion("12");
            cable.setPriority("QD");
            cable.setCableAddress(String.join(",", vo.getOrigin()));
            if (StringUtils.isNotEmpty(vo.getAddress())) {
                cable.setReceiveAddress(String.join(",", vo.getAddress()));
            }
            cable.setFlightNo(info.getFlightNo());
            cable.setFlightDate(info.getExecDate());
            cable.setContent(builder.toString());
            hzCableMapper.insert(cable);
            ForwardOriginMsgVO msgVO = new ForwardOriginMsgVO();
            msgVO.setOriginMsg(builder.toString());
            msgVO.setMsgType("FSU");
            msgVO.setSendType(sendType);
            msgVO.setReceiveAddress("-");
            if ("FD".equals(sendType)) {
                if (vo.getAddress() != null) {
                    msgVO.setReceiveAddress(String.join(",", vo.getAddress()));
                }
            }
            if ("MAIL".equals(sendType)) {
                msgVO.setMailAddress(String.join(",",vo.getReceiveMailAddress()));
            }
            if ("FTP".equals(sendType)) {
                msgVO.setReceiveFtpAddress(ftpAddress);
                msgVO.setReceiveFtpFolder(vo.getReceiveFtpFolder());
            }
            if ("MQ".equals(sendType)) {
                msgVO.setReceiveMQQueue(vo.getReceiveMQQueue());
            }
            msgVO.setSendAddress(String.join(",", vo.getOrigin()));
            msgVO.setPriority("QD");
            httpService.sendCable(msgVO, cable, token);
        }
    }
}
