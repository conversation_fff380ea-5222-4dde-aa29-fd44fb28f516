package com.gzairports.common.business.arrival.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 代理人H5提货出库返回数据
 *
 * <AUTHOR>
 * @date 2024-08-06
 */
@Data
public class H5PickUpOutVo {

    /** 办单id */
    private Long pickUpId;

    /** 流水号 */
    private String serialNo;

    /** 办单完成时间 */
    private Date pickUpTime;

    /** 运单数 */
    private Integer totalCount;

    /** 总件数 */
    private Integer totalQuantity;

    /** 总重量 */
    private BigDecimal totalWeight;

    /** 状态 */
    private String status;

    /** 办单人 */
    private String handleBy;

    /** 运单号列表 */
    private List<String> waybillCodeList;

    /** 运单详情 */
    private List<H5OutInfoVo> voList;
}
