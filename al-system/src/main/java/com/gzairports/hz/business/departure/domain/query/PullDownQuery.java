package com.gzairports.hz.business.departure.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 临时拉下查询参数
 *
 * <AUTHOR>
 * @date 2024-07-08
 */
@Data
public class PullDownQuery {

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startExecDate;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endExecDate;

    /** 航班日期 */
    private Date execDate;

    /** 航班号 */
    private String flightNo;

    /** 发货代理人 */
    private String agentCode;

    /** 运单号 */
    private String waybillCode;
}
