package com.gzairports.hz.business.departure.domain.vo;

import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Created by david on 2024/12/6
 * <AUTHOR>
 */
@Data
public class ChargeImportVo {

    private Long waybillId;

    /** 运单号 */
    @Excel(name = "运单号")
    private String waybillCode;

    /** 件数 */
    @Excel(name = "件数",cellType = Excel.ColumnType.NUMERIC)
    private Integer quantity;

    /** 重量 */
    @Excel(name = "重量",cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal weight;

    @Excel(name = "出港重量",cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal depWeight = new BigDecimal(0);

    /** 处置费/出港处置费 */
    @Excel(name = "出港处置费",cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal processingFee = new BigDecimal(0);

    /** 冷藏费 */
    @Excel(name = "冷藏费",cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal refrigerationFee = new BigDecimal(0);

    /** 搬运费 */
    @Excel(name = "搬运费",cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal handlingFee = new BigDecimal(0);

    /** 电报费 */
    @Excel(name = "电报费",cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal cableCharge = new BigDecimal(0);

    /** 危险品检查费 */
    @Excel(name = "危险品检查费",cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal inspectionFee = new BigDecimal(0);

    /** 叉车费 */
    @Excel(name = "叉车费",cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal forkliftCharge = new BigDecimal(0);

    /** 差异化服务费 */
    @Excel(name = "差异化服务费",cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal diffServiceCharge = new BigDecimal(0);

    /** 费用合计 */
    @Excel(name = "费用合计",cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal subtotal = new BigDecimal(0);
}
