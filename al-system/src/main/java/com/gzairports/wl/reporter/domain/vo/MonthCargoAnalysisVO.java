package com.gzairports.wl.reporter.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-18
 */
@Data
public class MonthCargoAnalysisVO {

    /** 主单利润 */
    private BigDecimal mainProfit;

    /** 邮件单利润 */
    private BigDecimal mailProfit;

    /** 主单货量 */
    private BigDecimal mainWeight;

    /** 邮件单货量 */
    private BigDecimal mailWeight;

    /** 利润合计 */
    private BigDecimal totalProfit;

    /** 货量合计 */
    private BigDecimal totalWeight;

    /** 国内主单比例 */
    private BigDecimal dMainRatio;

    /** 国内邮件单比例 */
    private BigDecimal dMailRatio;

    /** 国际主单比例 */
    private BigDecimal iMainRatio;

    /** 客户货量数据 */
    private List<CustomerWeightVO> weightList;
}
