<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.departure.mapper.MawbMapper">
    <update id="updateSecurityUrl">
        update all_air_waybill set security_url = #{securityUrl}, update_time = now() where id = #{id}
    </update>
    <update id="uploadTransportFile">
        update all_air_waybill
        <trim prefix="SET" suffixOverrides=",">
            update_time = now(),
            <if test="transportFile != null">transport_file = #{transportFile},</if>
            <if test="transportFilePdf != null">transport_file_pdf = #{transportFilePdf},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updatePayMoney">
        update all_air_waybill set pay_money = #{payMoney} where id = #{id}
    </update>

    <select id="selectAgent" resultType="java.lang.String">
        select agent from base_agent where agent_abb = #{agentCode}
    </select>
    <select id="selectListByQuery" resultType="com.gzairports.wl.departure.domain.vo.MergeMawbVo">
        select id, waybill_code, merge_status, quantity as totalCount, weight as totalWeight, charge_weight,  carrier1, des_port, shipper, consign, flight_no1 as flight1, write_time
        from all_air_waybill
            <where>
                is_del = 0 and dept_id = #{depId} and type = 'DEP'
                <if test="mawbId != null">
                    and id = #{mawbId}
                </if>
                <if test="waybillCode != null and waybillCode != ''">
                    and waybill_code like '%${waybillCode}%'
                </if>
                <if test="mergeStatus != null">
                    and merge_status = #{mergeStatus}
                </if>
                <if test="carrier != null and carrier != ''">
                    and carrier1 like '%${carrier}%'
                </if>
                <if test="shipper != null and shipper != ''">
                    and shipper like '%${shipper}%'
                </if>
                <if test="consignee != null and consignee != ''">
                    and consign like '%${consignee}%'
                </if>
                <if test="startTime != null">
                    and write_time <![CDATA[>=]]> #{startTime}
                </if>
                <if test="endTime != null">
                    and write_time <![CDATA[<=]]> #{endTime}
                </if>
            </where>
    </select>
    <select id="selectPay" resultType="com.gzairports.wl.departure.domain.vo.OnlineMawbVo">
        select aaw.id, aaw.waybill_code, aaw.source_port, aaw.des_port, aaw.flight_no1, aaw.flight_date1, sd.dept_name as shipper, aaw.special_cargo_code1, aaw.cargo_code,
               aaw.cargo_name, aaw.write_time, aaw.quantity, aaw.weight, aaw.charge_weight, aaw.pay_status, aaw.pay_money, aaw.type, aaw.refund
        from all_air_waybill aaw
            left join sys_dept sd on sd.dept_id = aaw.dept_id
        <where>
            aaw.is_del = 0  and aaw.dept_id = #{query.deptId} and aaw.status != 'staging' and aaw.type = 'DEP'
            <if test="query.startTime != null">
                and aaw.write_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and aaw.write_time <![CDATA[<=]]> #{query.endTime}
            </if>
            <if test="query.waybillCode != null and query.waybillCode != ''">
                and aaw.waybill_code like '%${query.waybillCode}%'
            </if>
            <if test="query.payStatus == 4">
                and aaw.pay_status in (1,2,3,4)
            </if>
            <if test="query.payStatus == 8">
                and aaw.pay_status in (5,6,7,8)
            </if>
            <if test="query.payStatus == 0">
                and aaw.pay_status = 0
            </if>
            <if test="query.payStatus == 9">
                and aaw.pay_status in (9,10,11,12)
            </if>
            <if test="query.specialCargoCode1 != null and query.specialCargoCode1 != ''">
                and aaw.special_cargo_code1 like '%${query.specialCargoCode1}%'
            </if>
            <if test="query.cargoCode != null and query.cargoCode != ''">
                and aaw.cargo_code like '%${query.cargoCode}%'
            </if>
            <if test="query.desPort != null and query.desPort != ''">
                and aaw.des_port like '%${query.desPort}%'
            </if>
            order by aaw.write_time desc
        </where>
    </select>
    <select id="selectInfo" resultType="com.gzairports.wl.departure.domain.vo.OnlineInfoVo">
        select id, waybill_code, source_port, des_port, flight_no1, flight_date1, shipper, special_cargo_code1, cargo_code, cargo_name,
               quantity, weight, charge_weight, pay_status, pay_money, type, pay_time, settle_time
        from all_air_waybill
        where id = #{id} and is_del = 0
    </select>
    <select id="selectByIds" resultType="com.gzairports.wl.departure.domain.vo.TransferMawbVo">
        select id, waybill_code, des_port, flight_no1 as flight, cargo_code, cargo_name, danger_code, quantity, weight, charge_weight, status
        from all_air_waybill where is_del = 0 and dept_id = #{deptId} and id in
        <foreach collection="mawbIds" item="id" open="(" separator="," close=")">
             #{id}
        </foreach>
    </select>
    <select id="selectCodeById" resultType="java.lang.String">
        select waybill_code from all_air_waybill where id = #{mawbId} and is_del = 0 and status = 'been_sent'
        <if test="deptId != null and deptId != ''">
              and dept_id = #{deptId}
        </if>
    </select>
    <select id="selectCodeByCode" resultType="com.gzairports.wl.departure.domain.vo.WaybillTraceVo">
        select id, waybill_code as masterWaybillCode, source_port, des_port, shipper, shipper_phone, consign,
               consign_phone, write_time, writer, flight_no1 as flightNo, flight_date1 as flightDate, cargo_name,
               quantity as totalQuantity, weight as totalWeight
        from all_air_waybill where waybill_code = #{waybillCode} and dept_id = #{deptId} and is_del = 0 and type = 'DEP'
    </select>

    <select id="selectAllCodeByCode" resultType="com.gzairports.wl.departure.domain.vo.WaybillTraceVo">
        select id, waybill_code as masterWaybillCode, type as businessType, status, source_port, des_port, shipper, shipper_phone, consign,
               consign_phone, write_time, writer, flight_no1 as flightNo, flight_date1 as flightDate, cargo_name,
               quantity as totalQuantity, weight as totalWeight
        from all_air_waybill where is_del = 0 and waybill_code = #{waybillCode}
    </select>

    <select id="queryList" resultType="com.gzairports.wl.departure.domain.vo.MawbQueryVo">
        select id, waybill_code, des_port, carrier1, flight_no1,flight_date1, cargo_code, cargo_name,
               quantity, weight, charge_weight, write_time, remark, dept_id,shipper,status, is_notify
        from all_air_waybill
        <where>
            and dept_id = #{deptId} and is_del = 0 and waybill_type = 'AWBA' and type = 'DEP'
            <if test="startTime != null">
                and write_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and write_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="waybillCode != null and waybillCode != ''">
                and waybill_code like '%${waybillCode}%'
            </if>
            <if test="cargoCode != null and cargoCode != ''">
                and cargo_code = #{cargoCode}
            </if>
            <if test="carrierCode != null and carrierCode != ''">
                and carrier1 = #{carrierCode}
            </if>
            <if test="desPort != null and desPort != ''">
                and des_port = #{desPort}
            </if>
            <if test="status != null and status != ''">
                <choose>
                    <when test="status == 'pre_pay'">
                        and pay_status in ('1', '2', '3', '4')
                    </when>
                    <otherwise>
                        and status = #{status}
                    </otherwise>
                </choose>
            </if>
            <if test="shipper != null and shipper != ''">
                and shipper like '%${shipper}%'
            </if>
            <if test="flightNo != null and flightNo != ''">
                and flight_no1 like '%${flightNo}%'
            </if>
        </where>
    </select>
    <select id="getWaybillList" resultType="com.gzairports.wl.departure.domain.vo.TransferMawbVo">
        select id, waybill_code, des_port, flight_no1 as flight, cargo_code, cargo_name, quantity, weight, charge_weight, status
        from all_air_waybill where is_del = 0 and dept_id = #{deptId} and status = 'been_sent'
        <if test="startTime != null">
            and write_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            and write_time <![CDATA[<=]]> #{endTime}
        </if>
        <if test="waybillCode != null and waybillCode != ''">
            and waybill_code = #{waybillCode}
        </if>
    </select>
    <select id="batch" resultType="com.gzairports.common.business.arrival.domain.vo.PickUpVo">
      select waybill_code, consign, cargo_code, cargo_name, quantity, weight, charge_weight from all_air_waybill
        <where>
            and is_del = 0 and type = 'ARR'
            <if test="startTime != null">
                and write_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and write_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="agent != null and agent != ''">
                and agent_code = #{agent}
            </if>
            <if test="consign != null and consign != ''">
                and consign = #{consign}
            </if>
        </where>
    </select>
    <select id="selectWaybillList" resultType="com.gzairports.hz.business.departure.domain.vo.CableMawbVo">
        select id, waybill_code, concat_ws('/',flight_no1,flight_date1) as flightInfo, danger_code, special_cargo_code1 as specialCode,
        weight, cargo_name, des_port, quantity from all_air_waybill
        <where>
            is_del = 0 and type = 'DEP'
            <if test="flightNo != null  and flightNo != ''"> and flight_no1 like '%${flightNo}%'</if>
            <if test="flightDate != null"> and flight_date1 = #{flightDate}</if>
            <if test="waybillCode != null  and waybillCode != ''"> and waybill_code = #{waybillCode}</if>
            <if test="specialCode != null  and specialCode != ''"> and special_cargo_code1 = #{specialCode}</if>
        </where>
    </select>
    <select id="printMawb" resultType="com.gzairports.wl.departure.domain.vo.PrintMawbVo">
        select waybill_code, weight, quantity, flight_no1 as flightNo, des_port, source_port,
               quantity as printNum, dept_id
        from all_air_waybill
        where is_del = 0 and type = 'DEP' and waybill_code = #{waybillCode}
    </select>
    <select id="selectTransportFile" resultType="com.gzairports.wl.departure.domain.vo.TransportFileVo">
        select transport_file,transport_file_pdf
        from all_air_waybill
        where id = #{id}
    </select>
    <select id="selectDesPort" resultType="java.lang.String">
        select des_port from all_air_waybill where waybill_code = #{waybillCode} and (type = 'DEP' or (type = 'ARR' and transfer_bill = 1)) and is_del = 0
    </select>
    <select id="selectSpecialWaybillListByIds"
            resultType="com.gzairports.hz.business.departure.domain.vo.SpecialCargoVo">
        select id, waybill_code, special_cargo_code1, danger_code, emergent_contact, contact_phone, cargo_name, flight_no1 as flightNo, flight_date1 as execDate,
               quantity, weight, volume, source_port, des_port from all_air_waybill where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectDeptId" resultType="com.gzairports.common.business.arrival.domain.vo.WaybillItemVo">
        select dept_id, category_name, cargo_name from all_air_waybill where type = 'ARR' and is_del = 0 and waybill_code = #{waybillCode}
    </select>
    <select id="selectWriteTime" resultType="java.util.Date">
        select write_time from all_air_waybill where  type = 'DEP' and is_del = 0 and waybill_code = #{waybillCode}
    </select>
    <select id="selectWaybillItemInfo"
            resultType="com.gzairports.common.business.departure.domain.vo.ItemWaybillVo">
        select dept_id, category_name, cargo_name, cargo_code from all_air_waybill where waybill_code = #{waybillCode} and type = #{type} and is_del  = 0
    </select>
    <select id="selectByCode" resultType="com.gzairports.common.business.arrival.domain.vo.HzArrItemVo">
        select waybill_code, cargo_code, cargo_name, quantity, weight from all_air_waybill where waybill_code = #{waybillCode} and type = 'ARR' and is_del  = 0
    </select>
    <select id="selectColdStore" resultType="java.lang.String">
        select cold_store from all_air_waybill where waybill_code = #{waybillCode} and type = 'ARR' and is_del  = 0
    </select>
    <select id="getInfo" resultType="com.gzairports.common.securitySubmit.domain.SecurityWaybillInfo">
        select all_air_waybill.id, waybill_code, special_cargo_code1,cargo_code, cargo_name, quantity, category_name, cold_store, pressure_chamber,
        weight, charge_weight, source_port, des_port, flight_no1, flight_date1, volume, size, pack, pack_code, carrier1, des1, carrier2, des2,
        carrier3, des3, shipper, shipper_phone, shipper_address, shipper_region, consign, consign_phone, consign_address, consign_region,
        agent_company, all_air_waybill.agent_code, write_location, customs_supervision, security_submit, write_time, write_location, writer, remark, cross_air,
        is_load, sys_dept.dept_name as agent, all_air_waybill.status, pdf_url,
        security_url, transport_file,transport_file_pdf
        from all_air_waybill left join sys_dept on sys_dept.dept_id = all_air_waybill.dept_id
        <where>
            and all_air_waybill.type = 'DEP'
            <if test="waybillCode != null  and waybillCode != ''"> and waybill_code = #{waybillCode}</if>
        </where>
    </select>
    <select id="selectIdByCode" resultType="com.gzairports.common.business.departure.domain.Mawb">
        select id, origin_bill from all_air_waybill where type = 'DEP' and waybill_code  = #{waybillCode} limit 1
    </select>
    <select id="selectId" resultType="java.lang.Long">
        select deptId from all_air_waybill where type = 'ARR' and waybill_code  = #{waybillCode}
    </select>
    <select id="selectBillStatus" resultType="java.lang.Integer">
        select pay_status from all_air_waybill where type = 'ARR' and waybill_code  = #{waybillCode}
    </select>
    <select id="selectInfoByCode" resultType="com.gzairports.common.business.departure.domain.Mawb">
        select id, status from all_air_waybill where type = 'DEP' and waybill_code  = #{originBill}
    </select>
    <select id="selectChargeWeight" resultType="com.gzairports.common.business.departure.domain.Mawb">
        select weight, charge_weight from all_air_waybill where waybill_code = #{waybillCode} and type = 'ARR' and is_del  = 0
    </select>
    <select id="selectWeight" resultType="com.gzairports.common.business.departure.domain.Mawb">
        select id, weight, charge_weight from all_air_waybill where waybill_code = #{waybillCode} and dept_id = #{deptId} and type = 'DEP' and is_del  = 0
    </select>
    <select id="selectComeInfo" resultType="com.gzairports.common.business.departure.domain.Mawb">
        select id, agent_company, waybill_code, quantity, cargo_code, cross_air, is_south, weight, charge_weight, pay_money, dept_id
        from all_air_waybill
        where waybill_code = #{waybillCode}
          and type = #{type}
          <if test="deptId != null">
              and dept_id = #{deptId}
          </if>
          and is_del = 0
    </select>
    <select id="selectComeInfoNew" resultType="com.gzairports.common.business.departure.domain.Mawb">
        select id, agent_company, waybill_code, quantity, cargo_code, cross_air, is_south, weight, charge_weight, pay_money, dept_id
        from all_air_waybill
        where waybill_code = #{waybillCode}
        and type = #{type}
        <if test="deptIds != null">
            and dept_id in
            <foreach item="deptId" collection="deptIds" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        and is_del = 0
    </select>
    <select id="selectPayCount" resultType="java.lang.Integer">
        select count(1) from all_air_waybill
        <where>
            is_del = 0  and dept_id = #{query.deptId} and status != 'staging' and type = 'DEP'
            <if test="query.startTime != null">
                and write_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and write_time <![CDATA[<=]]> #{query.endTime}
            </if>
            <if test="query.waybillCode != null and query.waybillCode != ''">
                and waybill_code like '%${query.waybillCode}%'
            </if>
            <if test="query.payStatus == 4">
                and pay_status in (1,2,3,4)
            </if>
            <if test="query.payStatus == 8">
                and pay_status in (5,6,7,8)
            </if>
            <if test="query.payStatus == 0">
                and pay_status = 0
            </if>
            <if test="query.payStatus == 9">
                and pay_status in (9,10,11,12)
            </if>
            <if test="query.specialCargoCode1 != null and query.specialCargoCode1 != ''">
                and special_cargo_code1 like '%${query.specialCargoCode1}%'
            </if>
            <if test="query.cargoCode != null and query.cargoCode != ''">
                and cargo_code like '%${query.cargoCode}%'
            </if>
            <if test="query.desPort != null and query.desPort != ''">
                and des_port like '%${query.desPort}%'
            </if>
        </where>
    </select>
    <select id="selectByStatus" resultType="java.lang.String">
        select waybill_code from all_air_waybill
        <where>
            is_del = 0  and dept_id = #{query.deptId} and status != 'staging' and type = 'DEP'
            <if test="query.startTime != null">
                and write_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and write_time <![CDATA[<=]]> #{query.endTime}
            </if>
            <if test="query.waybillCode != null and query.waybillCode != ''">
                and waybill_code like '%${query.waybillCode}%'
            </if>
            <if test="status == 4">
                and pay_status in (1,2,3,4)
            </if>
            <if test="status == 8">
                and pay_status in (5,6,7,8)
            </if>
            <if test="status == 0">
                and pay_status = 0
            </if>
            <if test="status == 9">
                and pay_status in (9,10,11,12)
            </if>
            <if test="query.specialCargoCode1 != null and query.specialCargoCode1 != ''">
                and special_cargo_code1 like '%${query.specialCargoCode1}%'
            </if>
            <if test="query.cargoCode != null and query.cargoCode != ''">
                and cargo_code like '%${query.cargoCode}%'
            </if>
            <if test="query.desPort != null and query.desPort != ''">
                and des_port like '%${query.desPort}%'
            </if>
        </where>
    </select>
    <select id="selectRefund" resultType="java.math.BigDecimal">
        select sum(refund) from all_air_waybill
        <where>
            is_del = 0  and dept_id = #{query.deptId} and status != 'staging' and type = 'DEP'
            <if test="query.startTime != null">
                and write_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and write_time <![CDATA[<=]]> #{query.endTime}
            </if>
            <if test="query.waybillCode != null and query.waybillCode != ''">
                and waybill_code like '%${query.waybillCode}%'
            </if>
            <if test="query.payStatus == 4">
                and pay_status in (1,2,3,4)
            </if>
            <if test="query.payStatus == 8">
                and pay_status in (5,6,7,8)
            </if>
            <if test="query.payStatus == 0">
                and pay_status = 0
            </if>
            <if test="query.payStatus == 9">
                and pay_status in (9,10,11,12)
            </if>
            <if test="query.specialCargoCode1 != null and query.specialCargoCode1 != ''">
                and special_cargo_code1 like '%${query.specialCargoCode1}%'
            </if>
            <if test="query.cargoCode != null and query.cargoCode != ''">
                and cargo_code like '%${query.cargoCode}%'
            </if>
            <if test="query.desPort != null and query.desPort != ''">
                and des_port like '%${query.desPort}%'
            </if>
        </where>
    </select>
    <select id="queryListCount" resultType="java.lang.Integer">
        select count(1)
        FROM
        all_air_waybill aaw
        left join all_flight_info afi on (CONCAT(afi.air_ways,afi.flight_no) = aaw.flight_no1 and afi.exec_date = DATE(aaw.flight_date1))
        <where>
            and aaw.dept_id = #{query.deptId} and aaw.is_del = 0
            and aaw.waybill_type = 'AWBA' and aaw.type = 'DEP' and afi.is_offin = 'D'
            <if test="query.startTime != null">
                and aaw.write_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and aaw.write_time <![CDATA[<=]]> #{query.endTime}
            </if>
            <if test="query.startTakeoffTime != null">
                and afi.start_scheme_takeoff_time <![CDATA[>=]]> #{query.startTakeoffTime}
            </if>
            <if test="query.endTakeoffTime != null">
                and afi.start_scheme_takeoff_time <![CDATA[<=]]> #{query.endTakeoffTime}
            </if>
            <if test="query.waybillCode != null and query.waybillCode != ''">
                and aaw.waybill_code like '%${query.waybillCode}%'
            </if>
            <if test="query.cargoCode != null and query.cargoCode != ''">
                and aaw.cargo_code = #{query.cargoCode}
            </if>
            <if test="query.carrierCode != null and query.carrierCode != ''">
                and aaw.carrier1 = #{query.carrierCode}
            </if>
            <if test="query.desPort != null and query.desPort != ''">
                and aaw.des_port = #{query.desPort}
            </if>
            <if test="query.status != null and query.status != ''">
                <choose>
                    <when test="query.status == 'pre_pay'">
                        and aaw.pay_status in ('1', '2', '3', '4')
                    </when>
                    <otherwise>
                        and aaw.status = #{query.status}
                    </otherwise>
                </choose>
            </if>
            <if test="query.shipper != null and query.shipper != ''">
                and aaw.shipper like '%${query.shipper}%'
            </if>
            <if test="query.flightNo != null and query.flightNo != ''">
                and aaw.flight_no1 like '%${query.flightNo}%'
            </if>
        </where>
    </select>
    <select id="queryListQuantity" resultType="java.lang.Integer">
        select sum(quantity)
        FROM
        all_air_waybill aaw
        left join all_flight_info afi on (CONCAT(afi.air_ways,afi.flight_no) = aaw.flight_no1 and afi.exec_date = DATE(aaw.flight_date1))
        <where>
            and aaw.dept_id = #{query.deptId} and aaw.is_del = 0
            and aaw.waybill_type = 'AWBA' and aaw.type = 'DEP' and afi.is_offin = 'D'
            <if test="query.startTime != null">
                and aaw.write_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and aaw.write_time <![CDATA[<=]]> #{query.endTime}
            </if>
            <if test="query.startTakeoffTime != null">
                and afi.start_scheme_takeoff_time <![CDATA[>=]]> #{query.startTakeoffTime}
            </if>
            <if test="query.endTakeoffTime != null">
                and afi.start_scheme_takeoff_time <![CDATA[<=]]> #{query.endTakeoffTime}
            </if>
            <if test="query.waybillCode != null and query.waybillCode != ''">
                and aaw.waybill_code like '%${query.waybillCode}%'
            </if>
            <if test="query.cargoCode != null and query.cargoCode != ''">
                and aaw.cargo_code = #{query.cargoCode}
            </if>
            <if test="query.carrierCode != null and query.carrierCode != ''">
                and aaw.carrier1 = #{query.carrierCode}
            </if>
            <if test="query.desPort != null and query.desPort != ''">
                and aaw.des_port = #{query.desPort}
            </if>
            <if test="query.status != null and query.status != ''">
                <choose>
                    <when test="query.status == 'pre_pay'">
                        and aaw.pay_status in ('1', '2', '3', '4')
                    </when>
                    <otherwise>
                        and aaw.status = #{query.status}
                    </otherwise>
                </choose>
            </if>
            <if test="query.shipper != null and query.shipper != ''">
                and aaw.shipper like '%${query.shipper}%'
            </if>
            <if test="query.flightNo != null and query.flightNo != ''">
                and aaw.flight_no1 like '%${query.flightNo}%'
            </if>
        </where>
    </select>
    <select id="queryListWeight" resultType="java.math.BigDecimal">
        select sum(weight)
        FROM
        all_air_waybill aaw
        left join all_flight_info afi on (CONCAT(afi.air_ways,afi.flight_no) = aaw.flight_no1 and afi.exec_date = DATE(aaw.flight_date1))
        <where>
            and aaw.dept_id = #{query.deptId} and aaw.is_del = 0
            and aaw.waybill_type = 'AWBA' and aaw.type = 'DEP'
            and afi.is_offin = 'D'
            <if test="query.startTime != null">
                and aaw.write_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and aaw.write_time <![CDATA[<=]]> #{query.endTime}
            </if>
            <if test="query.startTakeoffTime != null">
                and afi.start_scheme_takeoff_time <![CDATA[>=]]> #{query.startTakeoffTime}
            </if>
            <if test="query.endTakeoffTime != null">
                and afi.start_scheme_takeoff_time <![CDATA[<=]]> #{query.endTakeoffTime}
            </if>
            <if test="query.waybillCode != null and query.waybillCode != ''">
                and aaw.waybill_code like '%${query.waybillCode}%'
            </if>
            <if test="query.cargoCode != null and query.cargoCode != ''">
                and aaw.cargo_code = #{query.cargoCode}
            </if>
            <if test="query.carrierCode != null and query.carrierCode != ''">
                and aaw.carrier1 = #{query.carrierCode}
            </if>
            <if test="query.desPort != null and query.desPort != ''">
                and aaw.des_port = #{query.desPort}
            </if>
            <if test="query.status != null and query.status != ''">
                <choose>
                    <when test="query.status == 'pre_pay'">
                        and aaw.pay_status in ('1', '2', '3', '4')
                    </when>
                    <otherwise>
                        and aaw.status = #{query.status}
                    </otherwise>
                </choose>
            </if>
            <if test="query.shipper != null and query.shipper != ''">
                and aaw.shipper like '%${query.shipper}%'
            </if>
            <if test="query.flightNo != null and query.flightNo != ''">
                and aaw.flight_no1 like '%${query.flightNo}%'
            </if>
        </where>
    </select>
    <select id="queryListNew" resultType="com.gzairports.wl.departure.domain.vo.MawbQueryVo">
        select aaw.id, aaw.waybill_code, aaw.des_port, aaw.carrier1, aaw.flight_no1,
                aaw.flight_date1, aaw.cargo_code, aaw.cargo_name, aaw.quantity,
                aaw.weight, aaw.charge_weight, aaw.write_time, aaw.remark, aaw.dept_id,
                aaw.shipper, aaw.status, aaw.is_notify,
                afi.start_scheme_takeoff_time AS flightPlanTime,
                afi.start_real_takeoff_time AS flightFlyTime
        FROM
        all_air_waybill aaw
            left join all_flight_info afi on (CONCAT(afi.air_ways,afi.flight_no) = aaw.flight_no1 and afi.exec_date = DATE(aaw.flight_date1))
        <where>
            and aaw.dept_id = #{query.deptId} and aaw.is_del = 0
            and aaw.waybill_type = 'AWBA' and aaw.type = 'DEP' and afi.is_offin = 'D'
            <if test="query.startTime != null">
                and aaw.write_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and aaw.write_time <![CDATA[<=]]> #{query.endTime}
            </if>
            <if test="query.startTakeoffTime != null">
                and afi.start_scheme_takeoff_time <![CDATA[>=]]> #{query.startTakeoffTime}
            </if>
            <if test="query.endTakeoffTime != null">
                and afi.start_scheme_takeoff_time <![CDATA[<=]]> #{query.endTakeoffTime}
            </if>
            <if test="query.waybillCode != null and query.waybillCode != ''">
                and aaw.waybill_code like '%${query.waybillCode}%'
            </if>
            <if test="query.cargoCode != null and query.cargoCode != ''">
                and aaw.cargo_code = #{query.cargoCode}
            </if>
            <if test="query.carrierCode != null and query.carrierCode != ''">
                and aaw.carrier1 = #{query.carrierCode}
            </if>
            <if test="query.desPort != null and query.desPort != ''">
                and aaw.des_port = #{query.desPort}
            </if>
            <if test="query.status != null and query.status != ''">
                <choose>
                    <when test="query.status == 'pre_pay'">
                        and aaw.pay_status in ('1', '2', '3', '4')
                    </when>
                    <otherwise>
                        and aaw.status = #{query.status}
                    </otherwise>
                </choose>
            </if>
            <if test="query.shipper != null and query.shipper != ''">
                and aaw.shipper like '%${query.shipper}%'
            </if>
            <if test="query.flightNo != null and query.flightNo != ''">
                and aaw.flight_no1 like '%${query.flightNo}%'
            </if>
            order by afi.start_scheme_takeoff_time desc
        </where>
    </select>
    <select id="waybillQueryListNew" resultType="com.gzairports.wl.departure.domain.vo.MawbQueryVo">
        select aaw.id, aaw.waybill_code, aaw.des_port, aaw.carrier1, aaw.flight_no1,
                aaw.flight_date1, aaw.cargo_code, aaw.cargo_name, aaw.quantity,
                aaw.weight, aaw.charge_weight, aaw.write_time, aaw.remark, aaw.dept_id,
                aaw.shipper, aaw.status, aaw.is_notify
        from all_air_waybill aaw
        <where>
            and aaw.dept_id = #{query.deptId} and aaw.is_del = 0
            and aaw.waybill_type = 'AWBA' and aaw.type = 'DEP'
            <if test="query.startTime != null">
                and aaw.write_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and aaw.write_time <![CDATA[<=]]> #{query.endTime}
            </if>
            <if test="query.waybillCode != null and query.waybillCode != ''">
                and aaw.waybill_code like '%${query.waybillCode}%'
            </if>
            <if test="query.cargoCode != null and query.cargoCode != ''">
                and aaw.cargo_code = #{query.cargoCode}
            </if>
            <if test="query.carrierCode != null and query.carrierCode != ''">
                and aaw.carrier1 = #{query.carrierCode}
            </if>
            <if test="query.desPort != null and query.desPort != ''">
                and aaw.des_port = #{query.desPort}
            </if>
            <if test="query.status != null and query.status != ''">
                <choose>
                    <when test="query.status == 'pre_pay'">
                        and aaw.pay_status in ('1', '2', '3', '4')
                    </when>
                    <otherwise>
                        and aaw.status = #{query.status}
                    </otherwise>
                </choose>
            </if>
            <if test="query.shipper != null and query.shipper != ''">
                and aaw.shipper like '%${query.shipper}%'
            </if>
            <if test="query.flightNo != null and query.flightNo != ''">
                and aaw.flight_no1 like '%${query.flightNo}%'
            </if>
        </where>
        order by aaw.write_time desc
    </select>
    <select id="waybillQueryInfo" resultType="com.gzairports.wl.departure.domain.vo.MawbQueryVo">
        select exec_date, start_scheme_takeoff_time as flightPlanTime, start_real_takeoff_time as flightFlyTime
        from all_flight_info
        where is_offin = 'D' and CONCAT(air_ways,flight_no) = #{vo.flightNo1} and exec_date = DATE(#{vo.flightDate1})
        <if test="query.startTakeoffTime != null">
            and start_scheme_takeoff_time <![CDATA[>=]]> #{query.startTakeoffTime}
        </if>
        <if test="query.endTakeoffTime != null">
            and start_scheme_takeoff_time <![CDATA[<=]]> #{query.endTakeoffTime}
        </if>
        limit 1
    </select>
    <select id="selectCargoName" resultType="com.gzairports.hz.business.departure.domain.HzColdRegister">
        select id, cargo_name
        from all_air_waybill
        where waybill_code = #{waybillCode} and type = #{type}
        <if test="deptId != null">
            and dept_id = #{deptId}
        </if>
    </select>
    <select id="selectInfoByWaybill" resultType="com.gzairports.common.business.departure.domain.Mawb">
        select waybill_code, weight, quantity, charge_weight, cargo_code, dept_id, category_name
        from all_air_waybill
        where waybill_code = #{waybillCode} and type = 'ARR' and is_del = 0
    </select>
    <select id="selectDeptIdByCode" resultType="java.lang.Long">
        select dept_id from all_air_waybill where waybill_code = #{waybillCode} and type = 'ARR' and is_del = 0
    </select>
    <select id="selectpullWaybillInfo" resultType="com.gzairports.common.business.departure.domain.Mawb">
        select id, waybill_code, charge_weight, switch_bill, flight_date1, flight_no1, dept_id
        from all_air_waybill where waybill_code = #{waybillCode} and type = 'DEP'
    </select>
    <select id="selectPayStatus" resultType="java.lang.Integer">
        select pay_status from all_air_waybill where waybill_code = #{waybillCode} and dept_id = #{deptId} and type = 'DEP' and is_del = 0
    </select>
    <select id="listWaybill" resultType="com.gzairports.common.business.departure.domain.Mawb">
        select aaw.*
        from all_air_waybill aaw
                 left join hz_arr_tally hat on aaw.waybill_code = hat.waybill_code
        <where>
            <if test="query.deptId != null">
                and aaw.dept_id = #{query.deptId}
            </if>
            <if test="query.tallyStartTime != null and query.tallyEndTime != null">
                and hat.tally_time between #{query.tallyStartTime} and #{query.tallyEndTime}
            </if>
        </where>
    </select>
    <select id="queryFlightInfo" resultType="com.gzairports.wl.departure.domain.vo.MawbQueryVo">
        select exec_date, start_scheme_takeoff_time as flightPlanTime, start_real_takeoff_time as flightFlyTime,
               terminal_scheme_land_in_time
        from all_flight_info
        where is_offin = 'D' and CONCAT(air_ways,flight_no) = #{flightNo} and exec_date = DATE(#{flightDate})
        limit 1
    </select>
    <select id="selectNewTraceList" resultType="com.gzairports.wl.departure.domain.vo.NewWaybillTraceVO">
        select waybill_code, type, concat_ws('/',quantity,weight) as qAndW, source_port, des_port from all_air_waybill
        where waybill_code in
        <foreach collection="codeList" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>
</mapper>
