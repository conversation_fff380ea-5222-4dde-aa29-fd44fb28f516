package com.gzairports.common.basedata.domain.query;

import lombok.Data;

import java.util.List;

/**
 * 集装器管理查询参数
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Data
public class CargoUldQuery {

    /** 主键id集合 */
    private List<Long> ids;

    /** 编号 */
    private String code;

    /** 类型 */
    private String type;

    /** 航司 */
    private String airCompany;

    /** 入场时间 */
    private String entranceTime;

    /** 出场时间 */
    private String exitTime;
}
