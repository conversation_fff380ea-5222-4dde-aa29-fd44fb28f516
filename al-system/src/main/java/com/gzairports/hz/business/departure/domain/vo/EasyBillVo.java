package com.gzairports.hz.business.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 简易开单列表返回数据
 * <AUTHOR>
 * @date 2024-06-20
 */
@Data
public class EasyBillVo {

    /** 主键id */
    private Long id;

    /** 运单号 */
    private String waybillCode;

    /** 发货代理人 */
    private String agentCode;

    /** 货品大类 */
    private String categoryName;

    /** 特货代码 */
    private String specialCargoCode1;

    /** 品名 */
    private String cargoName;

    /** 填开时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date writeTime;
}
