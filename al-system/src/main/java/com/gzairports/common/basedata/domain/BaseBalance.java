package com.gzairports.common.basedata.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import com.gzairports.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 代理人配置余额明细对象 base_balance
 *
 * <AUTHOR>
 * @date 2024-06-17
 */
public class BaseBalance extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 代理人配置id */
//    @Excel(name = "代理人配置id")
    private Long agentId;

    /** 代理人 */
    @TableField(exist = false)
    @Excel(name = "代理人名称", width = 32)
    private String agent;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", dateFormat = "yyyy-MM-dd HH:mm:ss", width = 24)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 运单号 */
    @Excel(name = "运单号")
    private String waybillCode;

    /** 描述 */
    @Excel(name = "描述")
    private String remark;

    /** 流水号 */
    private String serialNo;

    /** 类型 */
    @Excel(name = "类型")
    private String type;

    /** 交易金额 */
    @Excel(name = "交易金额", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal tradeMoney;

    /** 余额 */
    @Excel(name = "余额")
    private BigDecimal balance;

    /** 凭证 */
    @Excel(name = "凭证")
    private String voucher;

    /** 凭证 */
    @Excel(name = "凭证")
    @TableField(value = "voucher_PDF")
    private String voucherPDF;

    /** 增加余额类型  0 1充值 2 */
    private Integer addType;




    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setAgentId(Long agentId)
    {
        this.agentId = agentId;
    }

    public Long getAgentId()
    {
        return agentId;
    }
    public void setSerialNo(String serialNo)
    {
        this.serialNo = serialNo;
    }

    public String getSerialNo()
    {
        return serialNo;
    }
    public void setType(String type)
    {
        this.type = type;
    }

    public String getType()
    {
        return type;
    }
    public void setTradeMoney(BigDecimal tradeMoney)
    {
        this.tradeMoney = tradeMoney;
    }

    public BigDecimal getTradeMoney()
    {
        return tradeMoney;
    }
    public void setBalance(BigDecimal balance)
    {
        this.balance = balance;
    }

    public BigDecimal getBalance()
    {
        return balance;
    }
    public void setVoucher(String voucher)
    {
        this.voucher = voucher;
    }

    public String getVoucher()
    {
        return voucher;
    }

    public String getWaybillCode() {
        return waybillCode;
    }

    public void setWaybillCode(String waybillCode) {
        this.waybillCode = waybillCode;
    }

    public void setVoucherPDF(String voucherPDF)
    {
        this.voucherPDF = voucherPDF;
    }

    public String getVoucherPDF()
    {
        return voucherPDF;
    }

    public void setAddType(Integer addType)
    {
        this.addType = addType;
    }

    public Integer getAddType()
    {
        return addType;
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }


    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getAgent() {
        return agent;
    }

    public void setAgent(String agent) {
        this.agent = agent;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("agentId", getAgentId())
            .append("serialNo", getSerialNo())
            .append("type", getType())
            .append("tradeMoney", getTradeMoney())
            .append("balance", getBalance())
            .append("voucher", getVoucher())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .append("waybillCode", getWaybillCode())
            .append("remark", getRemark())
            .append("voucherPDF", getVoucherPDF())
            .append("addType", getAddType())
            .toString();
    }
}
