package com.gzairports.common.business.arrival.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.*;
import com.gzairports.common.basedata.domain.query.WayBillH5Query;
import com.gzairports.common.basedata.mapper.*;
import com.gzairports.common.business.arrival.domain.*;
import com.gzairports.common.business.arrival.domain.query.*;
import com.gzairports.common.business.arrival.domain.vo.*;
import com.gzairports.common.business.arrival.mapper.*;
import com.gzairports.common.business.arrival.service.IPickUpService;
import com.gzairports.common.business.departure.domain.FlightInfo;
import com.gzairports.common.business.departure.domain.Mawb;
import com.gzairports.common.business.departure.domain.WaybillFee;
import com.gzairports.common.business.departure.domain.vo.ItemDetailVo;
import com.gzairports.common.business.departure.domain.vo.ItemWaybillVo;
import com.gzairports.common.business.departure.mapper.MawbMapper;
import com.gzairports.common.business.departure.mapper.WaybillFeeMapper;
import com.gzairports.common.business.wrong.domain.Wrong;
import com.gzairports.common.business.wrong.mapper.WrongMapper;
import com.gzairports.common.charge.domain.HzChargeIrRelation;
import com.gzairports.common.charge.domain.HzChargeItemRule;
import com.gzairports.common.charge.domain.HzChargeItems;
import com.gzairports.common.charge.domain.HzChargeRule;
import com.gzairports.common.charge.domain.query.ItemsQuery;
import com.gzairports.common.charge.domain.vo.BillRuleVo;
import com.gzairports.common.charge.domain.vo.HzItemsVo;
import com.gzairports.common.charge.domain.vo.IrRelationVo;
import com.gzairports.common.charge.mapper.HzChargeIrRelationMapper;
import com.gzairports.common.charge.mapper.HzChargeItemRuleMapper;
import com.gzairports.common.charge.mapper.HzChargeItemsMapper;
import com.gzairports.common.charge.mapper.HzChargeRuleMapper;
import com.gzairports.common.charge.service.BillingRule;
import com.gzairports.common.charge.service.impl.BillingRuleFactory;
import com.gzairports.common.constant.CacheConstants;
import com.gzairports.common.constant.Constants;
import com.gzairports.common.core.domain.entity.SysDept;
import com.gzairports.common.core.domain.entity.SysDictData;
import com.gzairports.common.core.redis.RedisCache;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.common.log.service.IWaybillLogService;
import com.gzairports.common.serviceRequest.domain.ServiceRequest;
import com.gzairports.common.serviceRequest.mapper.ServiceRequestMapper;
import com.gzairports.common.system.domain.SysConfig;
import com.gzairports.common.system.mapper.SysConfigMapper;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.system.service.ISysDictTypeService;
import com.gzairports.common.utils.*;
import com.gzairports.common.utils.spring.SpringUtils;
import com.gzairports.hz.business.arrival.domain.HzArrRecordOrder;
import com.gzairports.hz.business.arrival.domain.query.TallyWaybillKey;
import com.gzairports.hz.business.arrival.domain.vo.TallyWaybillVo;
import com.gzairports.hz.business.arrival.mapper.HzArrRecordOrderMapper;
import com.gzairports.hz.business.departure.domain.HzColdRegister;
import com.gzairports.hz.business.departure.mapper.HzColdRegisterMapper;
import com.gzairports.sms.domain.dto.SmsSendParameter;
import com.gzairports.sms.service.impl.GzaSmsSendServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.*;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 国内进港提货办理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-03
 */
@Service
public class PickUpServiceImpl extends ServiceImpl<PickUpMapper, PickUp> implements IPickUpService {

    @Autowired
    private PickUpMapper pickUpMapper;

    @Autowired
    private SerialNumberService serialNumberService;

    @Autowired
    private MawbMapper mawbMapper;

    @Autowired
    private AllPickUpOutMapper allPickUpOutMapper;

    @Autowired
    private HzArrTallyMapper hzArrTallyMapper;

    @Autowired
    private HzArrRecordOrderMapper recordOrderMapper;

    @Autowired
    private HzArrItemMapper itemMapper;

    @Autowired
    private HzChargeItemsMapper chargeItemsMapper;

    @Autowired
    private SysConfigMapper sysConfigMapper;

    @Autowired
    private HzChargeIrRelationMapper relationMapper;

    @Autowired
    private HzChargeRuleMapper ruleMapper;

    @Autowired
    private HzChargeItemRuleMapper itemRuleMapper;

    @Autowired
    private PickUpWaybillServiceImpl waybillService;

    @Autowired
    private PickUpWaybillMapper pickUpWaybillMapper;

    @Autowired
    private ApplyEditMapper applyEditMapper;

    @Autowired
    private FlightInfoMapper infoMapper;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private HzColdRegisterMapper registerMapper;

    @Autowired
    private BaseAgentMapper baseAgentMapper;

    @Autowired
    private SysDeptMapper deptMapper;

    @Autowired
    private WaybillFeeMapper feeMapper;

    @Autowired
    private IWaybillLogService waybillLogService;

    @Autowired
    private ServiceRequestMapper serviceRequestMapper;

    @Autowired
    private BaseBalanceMapper baseBalanceMapper;

    @Autowired
    private CargoCodeMapper cargoCodeMapper;

    @Autowired
    private CustomerMapper customerMapper;

    @Autowired
    private AirportCodeMapper airportCodeMapper;

    @Autowired
    private WrongMapper wrongMapper;

    @Autowired
    private HzArrTallyMapper tallyMapper;

    @Autowired
    private ISysDictTypeService dictTypeService;

    private static final ThreadLocalRandom RANDOM = ThreadLocalRandom.current();

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
    private static final DecimalFormat DF_TWO = new DecimalFormat("#.##");

    /**
     * 根据运单号查询进港运单
     * @param query 查询参数
     * @return 结果
     */
    @Override
    public PickOrderVo one(PickUpClickQuery query,String deptIds) {
        List<String> deptIdList = null;
        if(StringUtils.isNotEmpty(deptIds)){
            deptIdList = Arrays.asList(deptIds.split(","));
        }
        PickOrderVo orderVo = new PickOrderVo();
        QueryWrapper<Mawb> wrapper = new QueryWrapper<>();
        wrapper.eq("waybill_code", query.getWaybillCode());
        wrapper.eq("type","ARR");
        wrapper.eq("is_del",0);
        if (!CollectionUtils.isEmpty(deptIdList)){
            wrapper.in("dept_id",deptIdList);
        }
        Mawb waybill = mawbMapper.selectOne(wrapper);
        if (waybill == null){
            throw new CustomException("无当前运单信息");
        }

        boolean waybillCodeMatch = isWaybillCodeMatch(waybill);
        if(waybillCodeMatch){
            throw new CustomException(waybill.getWaybillCode() +"运单在不正常货邮已设置不可提货办单");
        }
        if (query.getTallyId() != null){
            HzArrTally hzArrTally = hzArrTallyMapper.selectById(query.getTallyId());
            Mawb oldWaybill = mawbMapper.selectOne(new QueryWrapper<Mawb>()
                    .eq("waybill_code", hzArrTally.getWaybillCode())
                    .eq("type","ARR")
                    .eq("is_del",0));
            if(oldWaybill != null){
                if ("wl".equals(query.getType()) && !CollectionUtils.isEmpty(deptIdList)){
                    List<BaseAgent> deptList = baseAgentMapper.selectList(new QueryWrapper<BaseAgent>().in("dept_id",deptIdList));
                    if (!CollectionUtils.isEmpty(deptList)){
                        List<String> nameList = deptList.stream().map(BaseAgent::getAgent).collect(Collectors.toList());
                        List<Long> codeList = deptList.stream().map(BaseAgent::getDeptId).collect(Collectors.toList());
                        if (!nameList.contains(waybill.getConsign())) {
                            throw new CustomException("运单 "+ waybill.getWaybillCode() +" 收货人与所选不一致，请重新挑单");
                        }
                        if (!codeList.contains(waybill.getDeptId())) {
                            throw new CustomException("运单 "+ waybill.getWaybillCode() +" 代理人与所选不一致，请重新挑单");
                        }
                    }
                }else {
                    if (!oldWaybill.getConsign().equals(waybill.getConsign())) {
                        throw new CustomException("运单 "+ waybill.getWaybillCode() +" 收货人与所选不一致，请重新挑单");
                    }
                    if (!oldWaybill.getAgentCode().equals(waybill.getAgentCode())) {
                        throw new CustomException("运单 "+ waybill.getWaybillCode() +" 代理人与所选不一致，请重新挑单");
                    }
                }
                orderVo.setConsign(oldWaybill.getConsign());
                orderVo.setConsignPhone(oldWaybill.getConsignPhone());
            }
        }else {
            orderVo.setConsign(waybill.getConsign());
            orderVo.setConsignPhone(waybill.getConsignPhone());
        }
        BaseAgent baseAgent;
        if ("wl".equals(query.getType())){
            baseAgent = baseAgentMapper.selectOne(
                    new LambdaQueryWrapper<BaseAgent>()
                            .eq(BaseAgent::getDeptId, SecurityUtils.getDeptId()));
        }else {
            baseAgent = determineSettlementClient(waybill.getDeptId());
        }
        if (baseAgent != null && baseAgent.getSettleMethod() != null){
            if (baseAgent.getSettleMethod() != 2){
                orderVo.setSettleMethod(baseAgent.getSettleMethod());
            }else {
                SysDept dept = deptMapper.selectDeptById(waybill.getDeptId());
                if (dept != null && StringUtils.isNotEmpty(dept.getDeptNameAbbIn())){
                    orderVo.setSettleMethod(2);
                }
            }
            orderVo.setPayMethod("线上");
            orderVo.setSettleUser(baseAgent.getAgent());
        }else {
            orderVo.setPayMethod("现金");
        }
        String value = sysConfigMapper.selectValue("bill.examine");
        List<PickUpVo> list = hzArrTallyMapper.selectPickUpVo(query,deptIdList,value);
        if (!CollectionUtils.isEmpty(list)){
            List<AllPickUpOut> pickUpOuts = allPickUpOutMapper.selectList(new QueryWrapper<AllPickUpOut>().eq("waybill_code", query.getWaybillCode()));
            if (!CollectionUtils.isEmpty(pickUpOuts)){
                int sum = pickUpOuts.stream().mapToInt(AllPickUpOut::getPieces).sum();
                BigDecimal reduce = pickUpOuts.stream().map(AllPickUpOut::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                list.forEach(e->{e.setPickedUpQuantity(sum);e.setPickedUpWeight(reduce);});
            }
            //获取航班号
            String flightNo = getFlightNo(query.getWaybillCode());
            String flightDate = getFlightDate(query.getWaybillCode());

            for (PickUpVo pickUpVo : list) {
                pickUpVo.setFlightNo(flightNo);
                pickUpVo.setFlightDate(flightDate);
                pickUpVo.setRemark(waybill.getRemark());
                List<HzArrTally> hzArrTallyList = hzArrTallyMapper.selectList(new QueryWrapper<HzArrTally>()
                        .eq("waybill_code", pickUpVo.getWaybillCode())
                        .eq("status","lh_comp"));
                BigDecimal weightTotal = hzArrTallyList.stream().map(HzArrTally::getWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                pickUpVo.setCanPickUpWeight(weightTotal);
                int sum = hzArrTallyList.stream().mapToInt(HzArrTally::getPieces).sum();
                pickUpVo.setCanPickUpQuantity(sum);
                BigDecimal reduce = new BigDecimal(0);
                HzArrItemVo vo = mawbMapper.selectByCode(query.getWaybillCode());
                List<HzArrItem> items = new ArrayList<>();
                List<HzChargeItems> hzChargeItems = chargeItemsMapper.selectList(new QueryWrapper<HzChargeItems>()
                        .eq("operation_type", "ARR")
                        .eq("is_default", 1)
                        .eq("status",1)
                        .le("start_effective_time",new Date())
                        .ge("end_effective_time",new Date())
                        .eq("is_del",0));
                SysConfig sysConfig = sysConfigMapper.selectConfigById(16L);
                Date date = new Date();
                Instant startInstant = date.toInstant();
                LocalTime startTime = startInstant.atZone(ZoneId.systemDefault()).toLocalTime();
                long aLong = Long.parseLong(sysConfig.getConfigValue());
                long times = aLong * 60 * 60;
                Instant endInstant = startInstant.plusSeconds(times);
                Instant addTime = startInstant.plusSeconds(1);
                LocalTime endTime = addTime.atZone(ZoneId.systemDefault()).toLocalTime();
                Date storeEndTime = Date.from(endInstant);
                BigDecimal weightRate;
                BigDecimal chargeWeight = waybill.getChargeWeight() == null ? new BigDecimal(0) : waybill.getChargeWeight();
                if (weightTotal == null || weightTotal.compareTo(new BigDecimal(0)) == 0){
                    weightRate = new BigDecimal(0);
                }else {
                    BigDecimal bigDecimal = chargeWeight.divide(waybill.getWeight(),5, RoundingMode.DOWN).multiply(weightTotal);
                    weightRate = bigDecimal.setScale(0, RoundingMode.CEILING);
                }
                for (HzChargeItems hzChargeItem : hzChargeItems) {
                    List<HzChargeIrRelation> relations = relationMapper.selectList(new QueryWrapper<HzChargeIrRelation>().eq("item_id", hzChargeItem.getId()).eq("is_del",0));
                    int maxMatchCount = 0;
                    List<HzChargeIrRelation> ruleList = new ArrayList<>();
                    for (HzChargeIrRelation hzChargeRule : relations) {
                        int matchCount = 0;
                        if (StringUtils.isNotEmpty(hzChargeRule.getNoCharge()) && !hzChargeRule.getNoCharge().contains(waybill.getDeptId().toString())){
                            continue;
                        }
                        if (StringUtils.isNotEmpty(hzChargeRule.getPrefix()) && !hzChargeRule.getPrefix().contains(waybill.getWaybillCode().substring(4,7))){
                            continue;
                        }
                        if (StringUtils.isNotEmpty(hzChargeRule.getCategory()) && !hzChargeRule.getCategory().contains(waybill.getCategoryName())){
                            continue;
                        }
                        if (hzChargeRule.getIsSouth() == 1){
                            continue;
                        }
                        if (hzChargeRule.getIsExit() == 1){
                            continue;
                        }
                        if (hzChargeRule.getCrossAir() == 1){
                            continue;
                        }
                        int cargoMatchCount = isCargoCodeMatch(hzChargeRule, waybill.getCargoCode());
                        if (cargoMatchCount >= 0) {
                            matchCount += cargoMatchCount;
                        }
                        if (matchCount > 0) {
                            if (matchCount > maxMatchCount) {
                                maxMatchCount = matchCount;
                                ruleList.clear();
                                ruleList.add(hzChargeRule);
                            } else if (matchCount == maxMatchCount) {
                                ruleList.add(hzChargeRule);
                            }
                        }
                    }
                    if (!CollectionUtils.isEmpty(ruleList)){
                        HzChargeIrRelation relation = ruleList.stream().max(Comparator.comparing(HzChargeIrRelation::getPriority)).orElse(null);
                        if (relation != null){
                            HzChargeRule rule1 = ruleMapper.selectById(relation.getRuleId());
                            List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>()
                                    .eq("ir_id", relation.getId()));
                            if (!CollectionUtils.isEmpty(itemRules)) {
                                if ("ColdStorageBillingRule.class".equals(rule1.getClassName())){
                                    continue;
                                }
                                HzArrItem item = new HzArrItem();
                                item.setWaybillCode(waybill.getWaybillCode());
                                item.setIrId(relation.getId());
                                item.setRuleName(rule1.getRuleName());
                                item.setClassName(rule1.getClassName());
                                item.setChargeName(hzChargeItem.getChargeName());
                                item.setChargeItemsId(hzChargeItem.getId());
                                item.setUnit(1);
                                item.setSmallItem(1);
                                item.setLargeItem(1);
                                item.setSuperLargeItem(1);
                                item.setStartTime(startTime);
                                item.setEndTime(endTime);
                                //计算货物仓库存储天数
                                Date orderTime = tallyMapper.selectTimeById(pickUpVo.getTallyId());
                                LocalDateTime wareTime = LocalDateTimeUtil.of(orderTime);
                                Duration between = LocalDateTimeUtil.between(wareTime, LocalDateTime.now());
                                double storageDays = between.getSeconds() / (double) (24 * 60 * 60);
                                //仓储费:货物已全部到齐,取最新的理货时间
                                if("货物仓储费".equals(item.getChargeName())){
                                    storageDays = getStorageTotalDays(hzArrTallyList);
                                }
                                item.setDaysInStorage(storageDays);

                                item.setStoreStartTime(date);
                                item.setTallyId(pickUpVo.getTallyId());
                                item.setOrderId(pickUpVo.getOrderId());
                                item.setStoreEndTime(storeEndTime);
                                item.setPointTime(LocalTime.now().withSecond(0).withNano(0));
                                BillingRule rule = BillingRuleFactory.createRule(rule1.getClassName());
                                BillRuleVo vo1 = rule.calculateFee(itemRules, weightRate, vo.getQuantity(), item);
                                BigDecimal totalCharge = BigDecimalRoundUtils.bigDecimalRound(hzChargeItem.getRoundRule(), vo1.getTotalCharge());
                                item.setTotalCharge(totalCharge);
                                if("进港处置费".equals(hzChargeItem.getChargeName())){
                                    pickUpVo.setProcessingFeeRate(vo1.getRate());
                                    item.setFeeRate(vo1.getRate());
                                }
                                reduce = reduce.add(totalCharge);
                                items.add(item);
                            }
                        }
                    }
                }
                vo.setItems(items);
                vo.setTotalCost(reduce);
                pickUpVo.setCostSum(reduce);
                pickUpVo.setItemVo(vo);
            }
            orderVo.setPickUpVos(list);
        }
        return orderVo;
    }

    public BaseAgent determineSettlementClient(Long deptId) {
        BaseAgent directAgent = baseAgentMapper.selectOne(
                new LambdaQueryWrapper<BaseAgent>()
                        .eq(BaseAgent::getDeptId, deptId)
        );
        List<BaseAgent> allEligibleAgents = baseAgentMapper.selectList(
                new LambdaQueryWrapper<BaseAgent>()
                        .eq(BaseAgent::getDeptId, deptId)
                        .or()
                        .apply("FIND_IN_SET({0}, dept_ids) > 0", deptId)
        );
        if (allEligibleAgents.isEmpty()) {
            return null;
        }
        if (allEligibleAgents.size() == 1) {
            return allEligibleAgents.get(0);
        }
        List<BaseAgent> filteredAgents = allEligibleAgents.stream()
                .filter(agent -> directAgent == null || !agent.getId().equals(directAgent.getId()))
                .collect(Collectors.toList());
        return filteredAgents.isEmpty() ? allEligibleAgents.get(0) : filteredAgents.get(0);
    }

    /**
     * 根据理货数据的设置成可提货办单的时间 计算货物仓库总存储天数
     *
     * @param waybillTallyData 理货数据
     * @return 总存储天数
     */
    private double getStorageTotalDays(List<HzArrTally> waybillTallyData) {
        // 对每次理货数据单独计算仓储费，不正常货邮处理为可办理提货时开始为开始时间，一次性理货和分批次理货完都是最后一次理货的理货时间为开始时间 AWBA00120252113
        if (CollectionUtil.isEmpty(waybillTallyData)) {
            return 0.0;
        }
        List<LocalDateTime> isPickupTimeList = waybillTallyData.stream()
                .map(HzArrTally::getIsPickUpTime)
                .collect(Collectors.toList());
        List<Date> tallyTimeList = waybillTallyData.stream()
                .filter(v -> v.getIsPickUpTime() == null && v.getTallyTime() != null)
                .map(HzArrTally::getTallyTime)
                .sorted()
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(tallyTimeList)) {
            // 分批次理货完，取最后一次理货理货数据的理货时间
            Date endTallyTime = tallyTimeList.get(tallyTimeList.size() - 1);
            isPickupTimeList.add(LocalDateTimeUtil.of(endTallyTime));
        }
        //计算所有理货记录的总保管天数,
        return isPickupTimeList.stream()
                .filter(ObjectUtil::isNotNull)
                .map(v -> {
                    Duration between = LocalDateTimeUtil.between(v, LocalDateTime.now());
                    return between.getSeconds() / (double) (24 * 60 * 60);
                }).reduce(0.0, Double::sum);
    }

    /**
     * 根据运单号查询航班号
     * @param waybillCode 运单号
     * @return 航班号
     */
    private String getFlightNo(String waybillCode) {
        //查询航班信息
        LambdaQueryWrapper<HzArrRecordOrder> lqw = Wrappers.lambdaQuery(HzArrRecordOrder.class)
                .select(HzArrRecordOrder::getLegId)
                .eq(HzArrRecordOrder::getWaybillCode, waybillCode);
        List<HzArrRecordOrder> recordOrders = recordOrderMapper.selectList(lqw);
        StringBuilder flightNo = new StringBuilder();
        if (!CollectionUtils.isEmpty(recordOrders)) {
            List<String> flightNos = recordOrders.stream()
                    .map(order -> {
                        TallyWaybillVo tallyInfo = recordOrderMapper.getTallyInfo(waybillCode, order.getLegId());
                        return Optional.ofNullable(tallyInfo)
                                .orElseGet(TallyWaybillVo::new)
                                .getFlightNo();
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            flightNo.append(String.join(",", flightNos));
        }
        return flightNo.toString();
    }

    /**
     * 根据运单号查询航班号
     * @param waybillCode 运单号
     * @return 航班号
     */
    private String getFlightDate(String waybillCode) {
        //查询航班信息
        LambdaQueryWrapper<HzArrRecordOrder> lqw = Wrappers.lambdaQuery(HzArrRecordOrder.class)
                .select(HzArrRecordOrder::getLegId)
                .eq(HzArrRecordOrder::getWaybillCode, waybillCode);
        List<HzArrRecordOrder> recordOrders = recordOrderMapper.selectList(lqw);
        StringBuilder flightDate = new StringBuilder();
        if (!CollectionUtils.isEmpty(recordOrders)) {
            List<String> flightNos = recordOrders.stream()
                    .map(order -> {
                        TallyWaybillVo tallyInfo = recordOrderMapper.getTallyInfo(waybillCode, order.getLegId());
                        Date execDate = Optional.ofNullable(tallyInfo)
                                .orElseGet(TallyWaybillVo::new)
                                .getExecDate();
                        return DATE_FORMAT.format(execDate);
                    })
                    .collect(Collectors.toList());

            flightDate.append(String.join(",", flightNos));
        }
        return flightDate.toString();
    }

    /**
     * 根据运单号查询航班号
     * @param waybillCode 运单号
     * @return 航班号
     */
    private String getFlightInfo(String waybillCode) {
        // 查询航班信息
        LambdaQueryWrapper<HzArrRecordOrder> lqw = Wrappers.lambdaQuery(HzArrRecordOrder.class)
                .select(HzArrRecordOrder::getLegId)
                .eq(HzArrRecordOrder::getWaybillCode, waybillCode);
        List<HzArrRecordOrder> recordOrders = recordOrderMapper.selectList(lqw);
        StringBuilder flightNo = new StringBuilder();
        if (!CollectionUtils.isEmpty(recordOrders)) {
            List<String> flightInfos = recordOrders.stream()
                    .map(order -> {
                        TallyWaybillVo tallyInfo = recordOrderMapper.getTallyInfo(waybillCode, order.getLegId());
                        return Optional.ofNullable(tallyInfo)
                                .orElseGet(TallyWaybillVo::new);
                    })
                    .filter(info -> info.getFlightNo() != null || info.getFlightDate() != null)
                    .map(info -> {
                        String flightNoStr = StringUtils.defaultString(info.getFlightNo());
                        String flightDateStr = info.getExecDate() != null ? DATE_FORMAT.format(info.getExecDate()) : "";
                        return flightNoStr + "/" + flightDateStr;
                    })
                    .collect(Collectors.toList());

            flightNo.append(String.join(",", flightInfos));
        }
        return flightNo.toString();
    }

    /**
     * 判断该运单号是否可挑单
     * */
    private boolean isWaybillCodeMatch(Mawb mawb) {
        NotPickedUpVo notPickedUpVo = hzArrTallyMapper.selectNotPickedUp(mawb.getWaybillCode());
        if (notPickedUpVo == null) {
            throw new CustomException("无挑单数据");
        }
        List<Wrong> wrongs = wrongMapper.selectList(new QueryWrapper<Wrong>()
                .eq("waybill_code", mawb.getWaybillCode())
                .eq("wrong_type","理货数量异常")
                .eq("type", "ARR"));
        if (wrongs.size() > 0) {
            //理货件数重量相等 不正常货邮没操作 则可以挑单
            if (Objects.equals(mawb.getQuantity(), notPickedUpVo.getCanQuantity())
                    && Objects.equals(mawb.getWeight(), notPickedUpVo.getCanWeight())) {
                for (Wrong wrong : wrongs) {
                    if (wrong.getStatus() != 0 && wrong.getIsPickUp() == 0) {
                        return true;
                    }
                }
            } else {
                //不相等且没有选择可提货办单则不行
                for (Wrong wrong : wrongs) {
                    if (wrong.getIsPickUp() == 0) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 费用明细数据
     * @param waybillCode 进港运单号
     * @param tallyId 理货id
     * @return 结果
     */
    @Override
    public HzArrItemVo cost(String waybillCode, Long tallyId) {
        HzArrItemVo vo = mawbMapper.selectByCode(waybillCode);
        vo.setWaybillCode(waybillCode);
        List<HzArrItem> items = itemMapper.selectCostList(waybillCode, tallyId);
        if (!CollectionUtils.isEmpty(items)){
            vo.setItems(items);
            BigDecimal reduce = items.stream()
                    .map(hzArrItem -> hzArrItem.getEditCharge() != null ? hzArrItem.getEditCharge() : hzArrItem.getTotalCharge())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setTotalCost(reduce);
        }
        List<HzArrTally> hzArrTallyList = hzArrTallyMapper.selectList(new QueryWrapper<HzArrTally>()
                .eq("waybill_code", waybillCode)
                .eq("status","lh_comp"));
        BigDecimal weightTotal = hzArrTallyList.stream().map(HzArrTally::getWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        vo.setCanPickUpWeight(weightTotal);
        int sum = hzArrTallyList.stream().mapToInt(HzArrTally::getPieces).sum();
        vo.setCanPickUpQuantity(sum);
        return vo;
    }

    /**
     * 挑单费用明细详情
     * @param id 费用明细id
     * @return 详情
     */
    @Override
    public HzArrItem costInfo(Long id) {
        HzArrItem item = itemMapper.selectCostInfo(id);
        HzChargeIrRelation relation = relationMapper.selectById(item.getIrId());
        item.setChargeItemsId(relation.getItemId());
        return item;
    }

    /**
     * 保存提货办单数据
     * @param vo 提货办单数据
     * @return 结果
     */
    @Override
    public PickUp add(PickOrderVo vo) {
        if (vo == null){
            throw new CustomException("无新增数据");
        }
        List<PickUpVo> pickUpVos = vo.getPickUpVos();
        if (CollectionUtils.isEmpty(pickUpVos)){
            throw new CustomException("无挑单信息");
        }
        Map<Long, Long> tallyIdCountMap = pickUpVos.stream()
                .collect(Collectors.groupingBy(PickUpVo::getTallyId, Collectors.counting()));

        boolean hasDuplicateTallyId = tallyIdCountMap.values().stream()
                .anyMatch(count -> count > 1);
        if (hasDuplicateTallyId){
            throw new CustomException("办单数据中存在重复运单");
        }
        List<String> waybillCodes = pickUpVos.stream().map(PickUpVo::getWaybillCode).collect(Collectors.toList());
        List<Mawb> mawb = mawbMapper.selectList(new QueryWrapper<Mawb>()
                .in("waybill_code", waybillCodes)
                .eq("type","ARR")
                .eq("is_del",0));
        if ("wl".equals(vo.getType())){
            List<String> deptIds = new ArrayList<>();
            BaseAgent baseAgent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", vo.getDeptId()));
            if(baseAgent != null){
                if(StringUtils.isNotEmpty(baseAgent.getDeptIds())){
                    deptIds = Arrays.asList(baseAgent.getDeptIds().split(","));
                }
            }
            List<BaseAgent> deptList = baseAgentMapper.selectList(new QueryWrapper<BaseAgent>().in("dept_id",deptIds));
            List<String> nameList = deptList.stream().map(BaseAgent::getAgent).collect(Collectors.toList());
            List<String> consignList = mawb.stream().map(Mawb::getConsign).collect(Collectors.toList());
            boolean consginHasDifference = consignList.stream()
                    .anyMatch(name -> !nameList.contains(name));
            if (consginHasDifference){
                throw new CustomException("所选运单收货人存在不一致，请重新挑单");
            }
            List<Long> codeList = deptList.stream().map(BaseAgent::getDeptId).collect(Collectors.toList());
            List<Long> agentCodeList = mawb.stream().map(Mawb::getDeptId).collect(Collectors.toList());
            boolean codeHasDifference = agentCodeList.stream()
                    .anyMatch(code -> !codeList.contains(code));
            if (codeHasDifference){
                throw new CustomException("所选运单代理人存在不一致，请重新挑单");
            }
            //物流网页端、H5办单时：取登录人的部门名称
            SysDept sysDept = deptMapper.selectDeptById(SecurityUtils.getDeptId());
            vo.setSettleUser(ObjectUtil.isNotNull(sysDept) ? sysDept.getDeptName() : "");
            vo.setSettleUserAbb(ObjectUtil.isNotNull(sysDept) ? sysDept.getDeptNameAbb() : "");
        }else {
            Set<Long> uniqueAgentCodes = mawb.stream()
                    .map(Mawb::getDeptId)
                    .collect(Collectors.toSet());
            Iterator<Long> iterator = uniqueAgentCodes.iterator();
            Long firstAgentCode = iterator.hasNext() ? iterator.next() : null;
            if (uniqueAgentCodes.size() != 1){
                throw new CustomException("所选运单代理人不一致，请重新挑单");
            }else if (firstAgentCode == null){
                Set<String> uniqueConsigns = mawb.stream()
                        .map(Mawb::getConsign)
                        .collect(Collectors.toSet());
                if (uniqueConsigns.size() != 1){
                    throw new CustomException("所选运单收货人不一致，请重新挑单");
                }
            }
        }
        HttpServletResponse response = ServletUtils.getResponse();
        ArrayList<WaybillLog> waybillLogs = new ArrayList<>();
        try{
            int payMethod = 0;
            if ("wl".equals(vo.getType())){
                BaseAgent agent = baseAgentMapper.selectByDeptId(vo.getDeptId().toString());
                if (agent != null){
                    switch (agent.getSettleMethod()){
                        case 0:
                            payMethod = 3;
                            break;
                        case 1:
                            payMethod = 2;
                            break;
                        case 2:
                            if (agent.getPayMethod() != null){
                                if (agent.getPayMethod() == 0){
                                    payMethod = 1;
                                }
                            }
                            break;
                    }
                }
            }else {
                if (!"现金".equals(vo.getPayMethod())){
                    payMethod = 1;
                }
            }
            PickUp up = new PickUp();
            BeanUtils.copyProperties(vo,up);
            BigDecimal reduce = pickUpVos.stream().map(PickUpVo::getCanPickUpWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            if ("hz".equals(vo.getType())){
                up.setDeptId(mawb.get(0).getDeptId());
            }
            up.setTotalWeight(reduce);
            up.setTotalChargeWeight(reduce);
            up.setTotalQuantity(vo.getTotalCanPickUpQuantity());
            up.setSerialNo(serialNumberService.generateSerialNumber("GN"));
            up.setHandleBy(SecurityUtils.getLoginUser().getUser().getNickName());
            up.setPickUpTime(new Date());
            up.setPayMethod(String.valueOf(payMethod));
            pickUpMapper.insert(up);
            for (PickUpVo pickUpVo : pickUpVos) {
                String s = pickUpWaybillMapper.selectReportPayTime(pickUpVo.getTallyId(), pickUpVo.getWaybillCode());
                if (StringUtils.isNotEmpty(s)){
                    throw new CustomException("当前理货数据已办单，请勿重复提交");
                }
                List<HzArrTally> hzArrTallyList = hzArrTallyMapper.selectList(new QueryWrapper<HzArrTally>()
                        .eq("waybill_code",pickUpVo.getWaybillCode())
                        .eq("status","lh_comp"));
                hzArrTallyList.forEach(e-> {
                    e.setStatus("save");
                    e.setPickUpId(up.getId());
                    hzArrTallyMapper.updateById(e);
                });
                PickUpWaybill waybill = new PickUpWaybill();
                waybill.setPickUpId(up.getId());
                waybill.setTallyId(pickUpVo.getTallyId());
                waybill.setWaybillCode(pickUpVo.getWaybillCode());
                waybill.setCanPickUpWeight(pickUpVo.getCanPickUpWeight());
                waybill.setCanPickUpQuantity(pickUpVo.getCanPickUpQuantity());
                waybill.setCostSum(pickUpVo.getCostSum());
                waybill.setCreateBy(SecurityUtils.getUsername());
                waybill.setCreateTime(new Date());
                waybillService.save(waybill);
                HzArrItemVo itemVo = pickUpVo.getItemVo();
                if (itemVo != null){
                    for (HzArrItem item : itemVo.getItems()) {
                        item.setPickUpId(up.getId());
                        item.setPayMethod(payMethod);
                        itemMapper.insert(item);
                    }
                }
                //运单日志
                WaybillLog waybillLog = waybillLogService.getWaybillLog(
                        pickUpVo.getWaybillCode(), 0, SecurityUtils.getNickName(),
                        pickUpVo.getCanPickUpWeight().toString(), pickUpVo.getCanPickUpQuantity().toString(), null,
                        vo, up.toString(), 0, null, new Date(),
                        "提货办单,办单件数:" + pickUpVo.getCanPickUpQuantity() + ",办单重量:"+ pickUpVo.getCanPickUpWeight() + "预授权支付金额:" + pickUpVo.getCostSum(),
                        "ARR", null);
                waybillLogs.add(waybillLog);
            }
            return up;
        }catch (Exception e){
            for (WaybillLog waybillLog:waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作失败" +  "," +
                                "code:" + response.getStatus()));
                waybillLog.setErrorMsg(e.getMessage());
                waybillLog.setStatus(1);
            }
            throw new CustomException(e.getMessage());
        }finally {
            for (WaybillLog waybillLog:waybillLogs) {
                waybillLogService.insertWaybillLog(waybillLog);
            }
        }
    }

    /**
     * 修改提货办单数据
     * @param vo 提货办单数据
     * @return 结果
     */
    @Override
    public int edit(PickOrderVo vo) {
        PickUp pickUp = pickUpMapper.selectById(vo.getId());
        if(pickUp == null){
            throw new CustomException("暂无办单信息");
        }
        pickUp.setCustomerName(vo.getCustomerName());
        pickUp.setCustomerIdType(vo.getCustomerIdType());
        pickUp.setCustomerIdNo(vo.getCustomerIdNo());
        pickUp.setCustomerPhone(vo.getCustomerPhone());
        pickUp.setConsign(vo.getConsign());
        pickUp.setConsignIdNo(vo.getConsignIdNo());
        pickUp.setConsignPhone(vo.getConsignPhone());
        pickUp.setUpdateTime(new Date());
        return pickUpMapper.updateById(pickUp);
    }

    @Override
    public H5BillVo billList(BillOfLadingQuery query,String deptIds) {
        H5BillVo billVo = new H5BillVo();
        if (query.getQueryTime() != null){
            LocalDate localDate = query.getQueryTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

            LocalDateTime startOfDay = localDate.atStartOfDay();
            Date startDate = Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
            query.setStartTime(startDate);

            LocalDateTime endOfDay = localDate.atTime(LocalTime.MAX);
            Date endDate = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
            query.setEndTime(endDate);
        }
        List<NotBillListVo> notPickedUpNewVos = new ArrayList<>();
        List<String> deptIdList = null;
        if(StringUtils.isNotEmpty(deptIds)){
            deptIdList = Arrays.asList(deptIds.split(","));
        }
        String value = sysConfigMapper.selectValue("bill.examine");
        List<NotBillListVo> notList = hzArrTallyMapper.notBillList(query,deptIdList,value);
        if (!CollectionUtils.isEmpty(notList)){
            notPickedUpNewVos = notList.stream()
                    .map(vo -> new NotBillListVo(){{
                        setWaybillCode(vo.getWaybillCode());
                        setQuantity(vo.getQuantity());
                        setWeight(vo.getWeight());
                        setCanBill(vo.getCanBill());
                        setOrderTime(vo.getOrderTime());}
                    }).collect(Collectors.toList());
            notPickedUpNewVos = notPickedUpNewVos.stream()
                    .collect(Collectors.collectingAndThen(Collectors.toCollection(()
                            -> new TreeSet<>(Comparator.comparing(NotBillListVo::getWaybillCode))), ArrayList::new));
            notPickedUpNewVos.forEach(e -> {
                e.setCanBill(BigDecimal.ZERO);});
            for (NotBillListVo pickUpVo : notList) {
                for (NotBillListVo pickUpVoNew:notPickedUpNewVos) {
                    if(pickUpVoNew.getWaybillCode().equals(pickUpVo.getWaybillCode())){
                        BigDecimal newCanBill = pickUpVoNew.getCanBill() == null ? new BigDecimal(0) : pickUpVoNew.getCanBill();
                        BigDecimal canBill = pickUpVo.getCanBill() == null ? new BigDecimal(0) : pickUpVo.getCanBill();
                        pickUpVoNew.setCanBill(newCanBill.add(canBill));
                    }
                }
            }
        }
        billVo.setNotNumber(notPickedUpNewVos.size());
        List<BillListVo> list =  pickUpMapper.selectBillList(query,deptIdList);
        billVo.setOrderNumber(list.size());
        if (query.getType() == 0){
            billVo.setNotList(notPickedUpNewVos);
        }else {
            billVo.setList(list);
        }
        return billVo;
    }

    @Override
    public PrintOutOrderVo h5PrintOutOrder(String pickUpCode, String platformType) {
        PrintOutOrderVo vo = pickUpMapper.selectH5PrintData(pickUpCode,new Date());
        if(StringUtils.isNull(vo)){
            throw new CustomException("未查询到提货数据");
        }
        List<OutOrderWaybillVo> waybillVos = waybillService.selectOutOrderById(vo.getId());
        setPrintData(vo, waybillVos);
        //物流平台打印不加打印次数
        if (!(StrUtil.isNotBlank(platformType) && StrUtil.equals(platformType, "wl"))) {
            pickUpMapper.updatePrintCount(vo.getId());
        }
        return vo;
    }

    @Override
    public PrintOutOrderVo printOutCount(String pickUpCode){
        return pickUpMapper.selectH5PrintData(pickUpCode,new Date());
    }

    private void setPrintData(PrintOutOrderVo vo, List<OutOrderWaybillVo> waybillVos) {
        if (!CollectionUtils.isEmpty(waybillVos)) {
            for (OutOrderWaybillVo waybillVo : waybillVos) {
                //将sourcePort由英文换成中文
                String sourcePort = waybillVo.getSourcePort();
                if (sourcePort != null) {
                    BaseAirportCode baseAirportCode = airportCodeMapper.selectByCode(sourcePort);
                    if (baseAirportCode != null) {
                        waybillVo.setSourcePort(baseAirportCode.getChineseName());
                    }
                }
                List<ArrItemVo> itemVos = itemMapper.selectItemVo(waybillVo.getTallyId(),waybillVo.getWaybillCode());
                if (!CollectionUtils.isEmpty(itemVos)) {
                    BigDecimal totalCost = itemVos.stream()
                            .map(hzArrItem -> hzArrItem.getEditCharge() != null ? hzArrItem.getEditCharge() : hzArrItem.getTotalCharge())
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    Map<String, List<ArrItemVo>> collect = itemVos.stream().collect(Collectors.groupingBy(ArrItemVo::getChargeAbb));
                    for (Map.Entry<String, List<ArrItemVo>> stringListEntry : collect.entrySet()) {
                        BigDecimal reduce = stringListEntry.getValue().stream()
                                .map(hzArrItem -> hzArrItem.getEditCharge() != null ? hzArrItem.getEditCharge() : hzArrItem.getTotalCharge())
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        switch (stringListEntry.getKey()) {
                            case "处置费":
                                waybillVo.setProcessingFee(reduce);
                                break;
                            case "仓储费":
                                waybillVo.setStorageFee(reduce);
                                break;
                            case "冷藏费":
                                waybillVo.setRefrigerationFee(reduce);
                                break;
                            case "搬运费":
                                waybillVo.setHandlingFee(reduce);
                                break;
                            case "电报费":
                                waybillVo.setCableCharge(reduce);
                                break;
                            default:
                                System.out.println("不展示当前收费项目");
                                break;
                        }
                    }
                    waybillVo.setSubtotal(totalCost);
                }
                if (StringUtils.isNotBlank(waybillVo.getRemark())) {
                    waybillVo.setWaybillCode(String.format("%s/%s", waybillVo.getWaybillCode(), waybillVo.getRemark()));
                }

                waybillVo.setQuantityStr(String.format("%s/%s", waybillVo.getQuantity(), waybillVo.getWaybillQuantity()));
                String chargeWeightStr = String.format("%s/%s",
                        DF_TWO.format(waybillVo.getChargeWeight()), DF_TWO.format(waybillVo.getWaybillWeight()));
                waybillVo.setChargeWeightStr(chargeWeightStr);
            }
//            vo.setOutTime(new Date());
            vo.setWaybillVos(waybillVos);
        }
        try {
            String s = QRCodeGenerator.generateQRCodeBase64(vo.getPickUpCode());
            vo.setQrCode(s);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 流水号查询
     * @param query 查询参数
     * @return 结果
     */
    @Override
    public PickOrderVo serial(PickUpCodeQuery query) {
        PickOrderVo orderVo = new PickOrderVo();
        if (query.getDeptId() != null){
            List<String> deptIds;
            BaseAgent baseAgent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", query.getDeptId()));
            if(baseAgent != null){
                if(StringUtils.isNotEmpty(baseAgent.getDeptIds())){
                    deptIds = Arrays.asList(baseAgent.getDeptIds().split(","));
                    query.setDeptIdList(deptIds);
                }
            }
        }
        query.setQueryDate(new Date());
        PickUp pick = pickUpMapper.selectBySerial(query);
        if (pick == null){
            throw new CustomException("无当前流水号信息");
        }
        List<AllPickUpOut> pickUpOuts = allPickUpOutMapper.selectList(new QueryWrapper<AllPickUpOut>()
                .eq("pick_up_id", pick.getId()));
        if(!CollectionUtils.isEmpty(pickUpOuts)){
            orderVo.setIsOut(1);
        }
        BeanUtils.copyProperties(pick,orderVo);
        if ("现金".equals(pick.getPayMethod()) || "0".equals(pick.getPayMethod())){
            orderVo.setPayMethod("现金");
        }else {
            orderVo.setPayMethod("线上");
        }
        List<ApplyEdit> applyEdits = applyEditMapper.selectList(new QueryWrapper<ApplyEdit>()
                .eq("serial_no", pick.getSerialNo()));
        if (!CollectionUtils.isEmpty(applyEdits)){
            ApplyEdit applyEdit = applyEdits.stream().max(Comparator.comparing(ApplyEdit::getApplyTime)).orElse(null);
            if (applyEdit != null){
                orderVo.setApplyEdit(applyEdit.getApplyEdit());
                orderVo.setRefuseEdit(applyEdit.getRefuseEdit());
            }
        }
        List<PickUpVo> list = new ArrayList<>();
        setPickUpVo(pick.getId(),list);
        orderVo.setPickUpVos(list);
        return orderVo;
    }

    /**
     * 流水号查询
     * @param serialNo 查询参数
     * @return 结果
     */
    @Override
    public PickOrderVo serialNo(String serialNo,Long deptId) {
        PickOrderVo orderVo = new PickOrderVo();
        PickUp pick;
        if (StringUtils.isNull(deptId)){
            pick = pickUpMapper.selectOne(new QueryWrapper<PickUp>()
                    .eq("serial_no", serialNo));
        }else{
            pick = pickUpMapper.selectOne(new QueryWrapper<PickUp>()
                    .eq("serial_no", serialNo)
                    .eq("dept_id", deptId));
        }
        List<AllPickUpOut> pickUpOuts = allPickUpOutMapper.selectList(new QueryWrapper<AllPickUpOut>()
                .eq("pick_up_id", pick.getId()));
        if(!CollectionUtils.isEmpty(pickUpOuts)){
            orderVo.setIsOut(1);
        }
        BeanUtils.copyProperties(pick,orderVo);
        List<ApplyEdit> applyEdits = applyEditMapper.selectList(new QueryWrapper<ApplyEdit>()
                .eq("serial_no", serialNo));
        if (!CollectionUtils.isEmpty(applyEdits)){
            ApplyEdit applyEdit = applyEdits.stream().max(Comparator.comparing(ApplyEdit::getApplyTime)).orElse(null);
            if (applyEdit != null){
                orderVo.setApplyEdit(applyEdit.getApplyEdit());
                orderVo.setRefuseEdit(applyEdit.getRefuseEdit());
            }
        }
        List<PickUpVo> list = new ArrayList<>();
        setPickUpVo(pick.getId(),list);
        orderVo.setPickUpVos(list);
        return orderVo;
    }

    /**
     * 货站结算
     * @param waybillCode 运单号
     * @param totalCost 总费用
     * @return 结果
     */
    @Override
    public SettleVo hzSettle(String waybillCode, String totalCost) {
        SettleVo vo = new SettleVo();
        WaybillItemVo itemVo = mawbMapper.selectDeptId(waybillCode);
        BaseAgent agent = determineSettlementClient(Long.valueOf(itemVo.getDeptId()));

        if (agent != null && agent.getSettleMethod() != null) {
            vo.setSettleMethod(agent.getSettleMethod());
            if (agent.getSettleMethod().equals(1)) {
                BigDecimal bigDecimal = new BigDecimal(totalCost);
                if (agent.getBalance().compareTo(bigDecimal) < 0){
                    throw new CustomException("代理人余额不足，无法支付！");
                }
                vo.setBalance(agent.getBalance());
            }
        }
        return vo;
    }

    /**
     * 批量挑单
     * @param query 查询参数
     * @return 运单数据
     */
    @Override
    public List<PickUpVo> batch(PickUpQuery query,String deptIds) {
        List<String> deptIdList = null;
        if(StringUtils.isNotEmpty(deptIds)){
            deptIdList = Arrays.asList(deptIds.split(","));
        }
        String value = sysConfigMapper.selectValue("bill.examine");
        List<PickUpVo> pickUpVos = hzArrTallyMapper.batchPickUp(query,deptIdList,value);
        if (CollectionUtils.isEmpty(pickUpVos)){
            return pickUpVos;
        }
        List<TallyWaybillKey> keys = new ArrayList<>();
        pickUpVos.stream().filter(vo -> vo.getTallyId() != null && StringUtils.isNotBlank(vo.getWaybillCode()))
                .forEach(pick ->
                        keys.add(new TallyWaybillKey(pick.getTallyId(), pick.getWaybillCode()))
                );
        List<HzArrItem> items = itemMapper.selectListByKey(keys);
        Map<String, List<HzArrItem>> outMap = items.stream()
                .collect(Collectors.groupingBy(
                        out -> out.getTallyId() + "_" + out.getWaybillCode()));
        for (PickUpVo pickUpVo : pickUpVos) {
            List<HzArrItem> itemList = outMap.getOrDefault(pickUpVo.getTallyId() + "_" + pickUpVo.getWaybillCode(), Collections.emptyList());
            if (!CollectionUtils.isEmpty(itemList)){
                BigDecimal reduce = itemList.stream()
                        .map(hzArrItem -> hzArrItem.getEditCharge() != null ? hzArrItem.getEditCharge() : hzArrItem.getTotalCharge())
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                pickUpVo.setCostSum(reduce);
            }
        }
        List<PickUpVo> listNew = new ArrayList<>();
        Map<String, List<PickUpVo>> collect = pickUpVos.stream().collect(Collectors.groupingBy(PickUpVo::getWaybillCode));
        for (Map.Entry<String, List<PickUpVo>> stringListEntry : collect.entrySet()) {
            PickUpVo pickUpVo = stringListEntry.getValue().get(0);
            int sum = stringListEntry.getValue().stream().filter(e->e.getCanPickUpQuantity() != null).mapToInt(PickUpVo::getCanPickUpQuantity).sum();
            pickUpVo.setCanPickUpQuantity(sum);
            BigDecimal reduce = stringListEntry.getValue().stream().map(PickUpVo::getCanPickUpWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            pickUpVo.setCanPickUpWeight(reduce);
            int quantity = stringListEntry.getValue().stream().filter(e->e.getPickedUpQuantity() != null).mapToInt(PickUpVo::getPickedUpQuantity).sum();
            pickUpVo.setPickedUpQuantity(quantity);
            BigDecimal weight = stringListEntry.getValue().stream().map(PickUpVo::getPickedUpWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            pickUpVo.setPickedUpWeight(weight);
            BigDecimal costSum = stringListEntry.getValue().stream().map(PickUpVo::getCostSum).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            pickUpVo.setCostSum(costSum);
            listNew.add(pickUpVo);
        }
        return listNew;
    }

    /**
     * 加入提货列表
     * @param vo 理货id集合
     * @return 办单数据
     */
    @Override
    public PickOrderVo pickList(PickUpListVo vo) {
        if (vo.getTallyIds() == null || vo.getTallyIds().length <= 0){
            throw new CustomException("无批量挑单数据");
        }
        PickOrderVo orderVo = new PickOrderVo();
        List<PickUpVo> pickUpVos = hzArrTallyMapper.selectPickUpVoList(vo.getTallyIds());
        if (CollectionUtils.isEmpty(pickUpVos)){
            throw new CustomException("无理货运单信息");
        }
        List<String> waybillCodes = pickUpVos.stream().map(PickUpVo::getWaybillCode).distinct().collect(Collectors.toList());

        BaseAgent baseAgent;
        Long deptId;
        if ("wl".equals(vo.getType())){
            baseAgent = baseAgentMapper.selectOne(
                    new LambdaQueryWrapper<BaseAgent>()
                            .eq(BaseAgent::getDeptId, SecurityUtils.getDeptId()));
            deptId = SecurityUtils.getDeptId();
        }else {
            PickUpVo pickUpVo = pickUpVos.get(0);
            deptId = mawbMapper.selectDeptIdByCode(pickUpVo.getWaybillCode());
            baseAgent = determineSettlementClient(deptId);
        }
        List<String> deptIds = new ArrayList<>();
        if(baseAgent != null){
            if(StringUtils.isNotEmpty(baseAgent.getDeptIds())){
                deptIds = Arrays.asList(baseAgent.getDeptIds().split(","));
            }
        }

        pickUpVos.forEach(e -> {
            Mawb mawb = new Mawb();
            BeanUtils.copyProperties(e,mawb);
            boolean waybillCodeMatch = isWaybillCodeMatch(mawb);
            if(waybillCodeMatch){
                throw new CustomException(mawb.getWaybillCode() +"运单在不正常货邮已设置不可提货办单");
            }
        });
        if (vo.getTallyId() != null){
            HzArrTally hzArrTally = hzArrTallyMapper.selectById(vo.getTallyId());
            Mawb mawb = mawbMapper.selectOne(new QueryWrapper<Mawb>()
                    .eq("waybill_code", hzArrTally.getWaybillCode())
                    .eq("type","ARR")
                    .eq("is_del",0));
            List<Mawb> waybills = mawbMapper.selectList(new QueryWrapper<Mawb>()
                    .in("waybill_code", waybillCodes)
                    .eq("type","ARR")
                    .eq("is_del",0));
            if(mawb != null){
                if ("wl".equals(vo.getType())){
                    List<BaseAgent> deptList = baseAgentMapper.selectList(new QueryWrapper<BaseAgent>().in("dept_id",deptIds));
                    List<String> nameList = deptList.stream().map(BaseAgent::getAgent).collect(Collectors.toList());
                    List<String> consignList = waybills.stream().map(Mawb::getConsign).collect(Collectors.toList());
                    boolean hasDifference = consignList.stream()
                            .anyMatch(name -> !nameList.contains(name));
                    if (hasDifference){
                        throw new CustomException("所选运单存在收货人与所选不一致，请重新挑单");
                    }
                }else {
                    for (Mawb waybill : waybills) {
                        if (!mawb.getConsign().equals(waybill.getConsign())) {
                            throw new CustomException("运单 "+ waybill.getWaybillCode() +" 收货人与所选不一致，请重新挑单");
                        }
                    }
                }
                orderVo.setConsign(mawb.getConsign());
                orderVo.setConsignPhone(mawb.getConsignPhone());
            }
        }else {
            List<Mawb> mawb = mawbMapper.selectList(new QueryWrapper<Mawb>()
                    .in("waybill_code", waybillCodes)
                    .eq("type","ARR")
                    .eq("is_del",0));
            if ("wl".equals(vo.getType())){
                List<BaseAgent> deptList = baseAgentMapper.selectList(new QueryWrapper<BaseAgent>().in("dept_id",deptIds));
                List<String> nameList = deptList.stream().map(BaseAgent::getAgent).collect(Collectors.toList());
                List<String> consignList = mawb.stream().map(Mawb::getConsign).collect(Collectors.toList());
                boolean consginHasDifference = consignList.stream()
                        .anyMatch(name -> !nameList.contains(name));
                if (consginHasDifference){
                    throw new CustomException("所选运单收货人存在不一致，请重新挑单");
                }
                List<Long> codeList = deptList.stream().map(BaseAgent::getDeptId).collect(Collectors.toList());
                List<Long> agentCodeList = mawb.stream().map(Mawb::getDeptId).collect(Collectors.toList());
                boolean codeHasDifference = agentCodeList.stream()
                        .anyMatch(code -> !codeList.contains(code));
                if (codeHasDifference){
                    throw new CustomException("所选运单代理人存在不一致，请重新挑单");
                }
            }else {
                Set<String> uniqueAgentCodes = mawb.stream()
                        .map(Mawb::getAgentCode)
                        .map(code -> StringUtils.isEmpty(code) ? null : code)
                        .collect(Collectors.toSet());
                String firstAgentCode = uniqueAgentCodes.isEmpty() ? null : uniqueAgentCodes.iterator().next();
                if (uniqueAgentCodes.size() != 1){
                    throw new CustomException("所选运单代理人不一致，请重新挑单");
                }else if (firstAgentCode == null){
                    Set<String> uniqueConsigns = mawb.stream()
                            .map(Mawb::getConsign)
                            .collect(Collectors.toSet());
                    if (uniqueConsigns.size() != 1){
                        throw new CustomException("所选运单收货人不一致，请重新挑单");
                    }
                }
            }
            orderVo.setConsign(mawb.get(0).getConsign());
            orderVo.setConsignPhone(mawb.get(0).getConsignPhone());
        }
        if (baseAgent != null){
            if (baseAgent.getSettleMethod() != 2){
                orderVo.setSettleMethod(baseAgent.getSettleMethod());
            }else {
                SysDept dept = deptMapper.selectDeptById(deptId);
                if (dept != null && StringUtils.isNotEmpty(dept.getDeptNameAbbIn())){
                    orderVo.setSettleMethod(2);
                }
            }
            orderVo.setPayMethod("线上");
            orderVo.setSettleUser(baseAgent.getAgent());
        }else {
            orderVo.setPayMethod("现金");
        }
        for (PickUpVo pick : pickUpVos) {
            //获取航班号
            String flightNo = getFlightNo(pick.getWaybillCode());
            String flightDate = getFlightDate(pick.getWaybillCode());
            pick.setFlightNo(flightNo);
            pick.setFlightDate(flightDate);
            setPickUpVo(pick);
        }
        orderVo.setPickUpVos(pickUpVos);
        return orderVo;
    }

    /**
     * 设置挑单运单数据的值
     * @param pick 挑单运单数据
     */
    private void setPickUpVo(PickUpVo pick) {
        List<AllPickUpOut> hzArrPickUps = allPickUpOutMapper.selectList(new QueryWrapper<AllPickUpOut>().eq("waybill_code", pick.getWaybillCode()));
        if (!CollectionUtils.isEmpty(hzArrPickUps)) {
            int sum = hzArrPickUps.stream().mapToInt(AllPickUpOut::getPieces).sum();
            pick.setPickedUpQuantity(sum);
            BigDecimal reduce = hzArrPickUps.stream().map(AllPickUpOut::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            pick.setPickedUpWeight(reduce);
        }
        //由于前端只传了有费用的理货历史,其他没费用的理货历史的可提货件数和重量没有加起来,这里需要加一下
        List<HzArrTally> arrTallyListForRecordOrderId = hzArrTallyMapper.selectList(new QueryWrapper<HzArrTally>()
                .eq("waybill_code", pick.getWaybillCode())
                .eq("status", "lh_comp"));
        for (HzArrTally hzArrTally:arrTallyListForRecordOrderId) {
            if(Objects.equals(pick.getTallyId(), hzArrTally.getId())){
                continue;
            }
            pick.setCanPickUpQuantity(pick.getCanPickUpQuantity() + hzArrTally.getPieces());
            pick.setCanPickUpWeight(pick.getCanPickUpWeight().add(hzArrTally.getWeight()));
        }
        Mawb waybill = mawbMapper.selectInfoByWaybill(pick.getWaybillCode());
        BigDecimal reduce = new BigDecimal(0);
        HzArrItemVo vo = mawbMapper.selectByCode(pick.getWaybillCode());
        List<HzArrItem> items = new ArrayList<>();
        List<HzChargeItems> hzChargeItems = chargeItemsMapper.selectList(new QueryWrapper<HzChargeItems>()
                .eq("operation_type", "ARR")
                .eq("is_default", 1)
                .eq("status",1)
                .le("start_effective_time",new Date())
                .ge("end_effective_time",new Date())
                .eq("is_del",0));
        SysConfig sysConfig = sysConfigMapper.selectConfigById(16L);
        Date date = new Date();
        Instant startInstant = date.toInstant();
        LocalTime startTime = startInstant.atZone(ZoneId.systemDefault()).toLocalTime();
        long aLong = Long.parseLong(sysConfig.getConfigValue());
        long times = aLong * 60 * 60;
        Instant endInstant = startInstant.plusSeconds(times);
        Instant addTime = startInstant.plusSeconds(1);
        LocalTime endTime = addTime.atZone(ZoneId.systemDefault()).toLocalTime();
        Date storeEndTime = Date.from(endInstant);
        BigDecimal weightRate;
        BigDecimal chargeWeight = waybill.getChargeWeight() == null ? new BigDecimal(0) : waybill.getChargeWeight();
        if (pick.getCanPickUpWeight() == null || pick.getCanPickUpWeight().compareTo(new BigDecimal(0)) == 0){
            weightRate = new BigDecimal(0);
        }else {
            BigDecimal bigDecimal = chargeWeight.divide(waybill.getWeight(),5, RoundingMode.DOWN).multiply(pick.getCanPickUpWeight());
            weightRate = bigDecimal.setScale(0, RoundingMode.CEILING);
        }
        for (HzChargeItems hzChargeItem : hzChargeItems) {
            List<HzChargeIrRelation> relations = relationMapper.selectList(new QueryWrapper<HzChargeIrRelation>().eq("item_id", hzChargeItem.getId()).eq("is_del",0));
            int maxMatchCount = 0;
            List<HzChargeIrRelation> ruleList = new ArrayList<>();
            for (HzChargeIrRelation hzChargeRule : relations) {
                int matchCount = 0;
                if (StringUtils.isNotEmpty(hzChargeRule.getNoCharge()) && !hzChargeRule.getNoCharge().contains(waybill.getDeptId().toString())){
                    continue;
                }
                if (StringUtils.isNotEmpty(hzChargeRule.getPrefix()) && !hzChargeRule.getPrefix().contains(waybill.getWaybillCode().substring(4,7))){
                    continue;
                }
                if (StringUtils.isNotEmpty(hzChargeRule.getCategory()) && !hzChargeRule.getCategory().contains(waybill.getCategoryName())){
                    continue;
                }
                if (hzChargeRule.getIsSouth() == 1){
                    continue;
                }
                if (hzChargeRule.getIsExit() == 1){
                    continue;
                }
                if (hzChargeRule.getCrossAir() == 1){
                    continue;
                }
                int cargoMatchCount = isCargoCodeMatch(hzChargeRule, waybill.getCargoCode());
                if (cargoMatchCount >= 0) {
                    matchCount += cargoMatchCount;
                }
                if (matchCount > 0) {
                    if (matchCount > maxMatchCount) {
                        maxMatchCount = matchCount;
                        ruleList.clear();
                        ruleList.add(hzChargeRule);
                    } else if (matchCount == maxMatchCount) {
                        ruleList.add(hzChargeRule);
                    }
                }
            }
            if (!CollectionUtils.isEmpty(ruleList)){
                HzChargeIrRelation relation = ruleList.stream().max(Comparator.comparing(HzChargeIrRelation::getPriority)).orElse(null);
                if (relation != null){
                    HzChargeRule rule1 = ruleMapper.selectById(relation.getRuleId());
                    List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>()
                            .eq("ir_id", relation.getId()));
                    if (!CollectionUtils.isEmpty(itemRules)) {
                        if ("ColdStorageBillingRule.class".equals(rule1.getClassName())){
                            continue;
                        }
                        HzArrItem item = new HzArrItem();
                        item.setWaybillCode(pick.getWaybillCode());
                        item.setIrId(relation.getId());
                        item.setRuleName(rule1.getRuleName());
                        item.setClassName(rule1.getClassName());
                        item.setChargeName(hzChargeItem.getChargeName());
                        item.setChargeItemsId(hzChargeItem.getId());
                        item.setUnit(1);
                        item.setSmallItem(1);
                        item.setLargeItem(1);
                        item.setSuperLargeItem(1);
                        item.setStartTime(startTime);
                        item.setEndTime(endTime);
                        //计算货物仓库存储天数
                        Date orderTime = tallyMapper.selectTimeById(pick.getTallyId());
                        //仓储费:货物已全部到齐,取最新的理货时间
                        if("货物仓储费".equals(item.getChargeName())){
                            Integer quantity = waybill.getQuantity();
                            BigDecimal weight = waybill.getWeight();
                            NotPickedUpVo notPickedUpVo = tallyMapper.selectTallyData(waybill.getWaybillCode());
                            if(Objects.equals(quantity, notPickedUpVo.getCanQuantity()) && Objects.equals(weight, notPickedUpVo.getCanWeight())){
                                orderTime = tallyMapper.selectById(pick.getTallyId()).getTallyTime();
                            }else{
                                item.setTotalCharge(BigDecimal.ZERO);
                                items.add(item);
                                continue;
                            }
                        }
                        LocalDateTime wareTime = LocalDateTimeUtil.of(orderTime);
                        Duration between = LocalDateTimeUtil.between(wareTime, LocalDateTime.now());
                        double diffDays = between.getSeconds() / (double) (24 * 60 * 60);
                        item.setDaysInStorage(diffDays);
                        item.setStoreStartTime(date);
                        item.setTallyId(pick.getTallyId());
                        item.setOrderId(pick.getOrderId());
                        item.setStoreEndTime(storeEndTime);
                        item.setPointTime(LocalTime.now().withSecond(0).withNano(0));
                        BillingRule rule = BillingRuleFactory.createRule(rule1.getClassName());
                        BillRuleVo vo1 = rule.calculateFee(itemRules, weightRate, vo.getQuantity(), item);
                        BigDecimal totalCharge = BigDecimalRoundUtils.bigDecimalRound(hzChargeItem.getRoundRule(), vo1.getTotalCharge());
                        item.setTotalCharge(totalCharge);
                        if("进港处置费".equals(hzChargeItem.getChargeName())){
                            pick.setProcessingFeeRate(vo1.getRate());
                            item.setFeeRate(vo1.getRate());
                        }
                        reduce = reduce.add(totalCharge);
                        items.add(item);
                    }
                }
            }
        }
        vo.setItems(items);
        vo.setTotalCost(reduce);
        pick.setCostSum(reduce);
        pick.setItemVo(vo);
    }

    /**
     * 查询已提货办单数据
     * @param query 查询参数
     * @return 已提货办单数据
     */
    @Override
    public PickedOrderVo selectByQuery(PickedUpQuery query) {
        PickedOrderVo vo = new PickedOrderVo();
        if (query.getDeptId() != null){
            List<String> deptIds = new ArrayList<>();
            BaseAgent baseAgent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", query.getDeptId()));
            if(baseAgent != null){
                if(StringUtils.isNotEmpty(baseAgent.getDeptIds())){
                    deptIds = Arrays.asList(baseAgent.getDeptIds().split(","));
                    query.setDeptIdList(deptIds);
                }
            }
        }
        List<PickedUpVo> list =  pickUpMapper.selectByQuery(query);
        if (CollectionUtils.isEmpty(list)){
            return vo;
        }
        List<Long> idList = list.stream().map(PickedUpVo::getId).collect(Collectors.toList());
        List<AllPickUpOut> pickUpOuts = allPickUpOutMapper.selectBatchById(idList);
        Map<Long, List<AllPickUpOut>> pickUpMap = pickUpOuts.stream().collect(Collectors.groupingBy(AllPickUpOut::getId));
        list.forEach(e ->{
            List<AllPickUpOut> pickUpId = pickUpMap.getOrDefault(e.getId(), Collections.emptyList());
            if(pickUpId.size() > 0){
                //拿最后的一条
                e.setOutTime(pickUpId.get(pickUpId.size() - 1).getOutTime());
            }
        });
        vo.setVos(list);
        // 25-06-13不取总的件数重量了
//        for (PickedUpVo pickedUpVo : list) {
//            List<PickUpWaybill> waybills = waybillService.selectWaybill(pickedUpVo.getId());
//            if (!CollectionUtils.isEmpty(waybills)){
//                int sum = waybills.stream().mapToInt(PickUpWaybill::getCanPickUpQuantity).sum();
//                pickedUpVo.setTotalQuantity(sum);
//                BigDecimal reduce = waybills.stream().map(PickUpWaybill::getCanPickUpWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
//                pickedUpVo.setTotalWeight(reduce);
//            }
//        }

        BigDecimal totalMoney = list.stream().map(PickedUpVo::getTotalCost).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        vo.setTotalMoney(totalMoney);

        List<PickedUpVo> collect = list.stream().filter(e -> e.getPayStatus() > 4).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)){
            BigDecimal settleMoney = collect.stream().map(PickedUpVo::getTotalCost).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setSettleMoney(settleMoney);
        }

        List<PickedUpVo> collect1 = list.stream().filter(e -> e.getPayStatus() > 0 && e.getPayStatus() <= 4).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect1)){
            BigDecimal payMoney = collect1.stream().map(PickedUpVo::getTotalCost).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setPayMoney(payMoney);
        }
        return vo;
    }


    /**
     * 数据导出
     * @param query 查询条件
     * @return 结果
     */
    @Override
    public List<PickedUpVo> exportList(PickedUpQuery query) {
        if (query.getDeptId() != null){
            List<String> deptIds = new ArrayList<>();
            BaseAgent baseAgent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", query.getDeptId()));
            if(baseAgent != null){
                if(StringUtils.isNotEmpty(baseAgent.getDeptIds())){
                    deptIds = Arrays.asList(baseAgent.getDeptIds().split(","));
                    query.setDeptIdList(deptIds);
                }
            }
        }
        List<PickedUpVo> pickedUpVos = pickUpMapper.selectByQuery(query);
        for (PickedUpVo pickedUpVo : pickedUpVos) {
            List<String> flightNoList = pickUpWaybillMapper.selectFlightNo(pickedUpVo.getId());
            if (!CollectionUtils.isEmpty(flightNoList)){
                String flightNo = String.join(",",flightNoList);
                pickedUpVo.setFlightNo(flightNo);
            }
            String waybillCode = pickedUpVo.getWaybillCode();
            if(waybillCode.contains("DN")){
                pickedUpVo.setShortWaybillCode(waybillCode.substring(4,6) + "-" + waybillCode.substring(6));
            }else{
                pickedUpVo.setShortWaybillCode(waybillCode.substring(4,7) + "-" + waybillCode.substring(7));
            }
        }
        return pickedUpVos;
    }


    /**
     * 已提货办单运单数据导出
     * */
    @Override
    public List<PickedUpWaybillVo> exportWaybillList(PickedUpQuery query){
        if (query.getDeptId() != null){
            List<String> deptIds = new ArrayList<>();
            BaseAgent baseAgent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", query.getDeptId()));
            if(baseAgent != null){
                if(StringUtils.isNotEmpty(baseAgent.getDeptIds())){
                    deptIds = Arrays.asList(baseAgent.getDeptIds().split(","));
                    query.setDeptIdList(deptIds);
                }
            }
        }
        List<PickedUpWaybillVo> pickedUpVos = pickUpMapper.selectWaybillByQuery(query);
        for(PickedUpWaybillVo vo : pickedUpVos){
            List<ArrItemVo> itemVos = itemMapper.selectItemVo(vo.getTallyId(),vo.getWaybillCode());
            if (!CollectionUtils.isEmpty(itemVos)){
                Map<String, List<ArrItemVo>> collect = itemVos.stream().collect(Collectors.groupingBy(ArrItemVo::getChargeAbb));
                for (Map.Entry<String, List<ArrItemVo>> stringListEntry : collect.entrySet()) {
                    BigDecimal reduce = stringListEntry.getValue().stream()
                            .map(hzArrItem -> hzArrItem.getEditCharge() != null ? hzArrItem.getEditCharge() : hzArrItem.getTotalCharge())
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    switch (stringListEntry.getKey()){
                        case "处置费":
                            vo.setProcessingFee(reduce);
                            break;
                        case "仓储费":
                            vo.setStorageFee(reduce);
                        case "冷藏费":
                            vo.setRefrigerationFee(reduce);
                            break;
                        case "搬运费":
                            vo.setHandlingFee(reduce);
                            break;
                        case "电报费":
                            vo.setCableCharge(reduce);
                            break;
                        default:
                            System.out.println("不展示当前收费项目");
                            break;
                    }
                }
            }
        }
        return pickedUpVos;
    }


    /**
     * 查询未提货办单数据
     * @param query 查询条件
     * @return 未提货办单数据
     */
    @Override
    public List<NotPickedUpVo> notPickedUp(PickedUpQuery query,String deptIds) {
        List<String> deptIdList = null;
        if(StringUtils.isNotEmpty(deptIds)){
            deptIdList = Arrays.asList(deptIds.split(","));
        }
        String value = sysConfigMapper.selectValue("bill.examine");
        List<NotPickedUpVo> notPickedUpVos = hzArrTallyMapper.notPickedUp(query,deptIdList,value);
        Set<String> waybillCodeSet = new HashSet<>();
        List<NotPickedUpVo> notPickedUpNewVos = new ArrayList<>();
        for(NotPickedUpVo vo : notPickedUpVos){
            if(waybillCodeSet.add(vo.getWaybillCode())){
                notPickedUpNewVos.add(vo);
            }
        }
        List<NotPickedUpVo> notPickedUpVosNew = new ArrayList<>();
        Map<String,List<NotPickedUpVo>> notPickedUpVoMap = new HashMap<>();
        Map<String,List<PickUpWaybill>> pickUpWaybillMap = new HashMap<>();
        if (!notPickedUpNewVos.isEmpty()){
            List<String> waybillCodeList = notPickedUpNewVos.stream().map(NotPickedUpVo::getWaybillCode).collect(Collectors.toList());
            List<NotPickedUpVo> notPickedUpVoList = hzArrTallyMapper.selectTallyList(waybillCodeList);
            List<PickUpWaybill> pickUpWaybills = pickUpWaybillMapper.selectPickWaybillList(waybillCodeList);
            if (!notPickedUpVoList.isEmpty()){
                notPickedUpVoMap = notPickedUpVoList.stream().collect(Collectors.groupingBy(NotPickedUpVo::getWaybillCode));
            }
            if (!pickUpWaybills.isEmpty()){
                pickUpWaybillMap = pickUpWaybills.stream().collect(Collectors.groupingBy(PickUpWaybill::getWaybillCode));
            }
        }
        for(NotPickedUpVo e :notPickedUpNewVos){
            String flightNo = getFlightInfo(e.getWaybillCode());
            e.setFlightNo(flightNo);
            List<NotPickedUpVo> notPickedUpVoList = notPickedUpVoMap.get(e.getWaybillCode());
            if (!CollectionUtils.isEmpty(notPickedUpVoList)){
                int sum = notPickedUpVoList.stream().mapToInt(NotPickedUpVo::getCanQuantity).sum();
                BigDecimal weight = notPickedUpVoList.stream().map(NotPickedUpVo::getCanWeight).reduce(BigDecimal.ZERO,BigDecimal::add);
                e.setCanQuantity(sum);
                e.setCanWeight(weight);
                List<String> tallyTimes = notPickedUpVoList.stream()
                        .map(order -> {
                            Date tallyTime = Optional.ofNullable(order)
                                    .orElseGet(NotPickedUpVo::new)
                                    .getTallyTime1();
                            return DATE_FORMAT.format(tallyTime);
                        })
                        .collect(Collectors.toList());

                e.setTallyTime(String.join(",", tallyTimes));
            }
            List<PickUpWaybill> pickUpWaybills = pickUpWaybillMap.get(e.getWaybillCode());
            if(!CollectionUtils.isEmpty(pickUpWaybills)){
                Map<String, Long> waybillTallyMap = pickUpWaybills.stream()
                        .collect(Collectors.toMap(
                                PickUpWaybill::getWaybillCode,
                                PickUpWaybill::getTallyId,
                                (existing, replacement) -> existing
                        ));
                Set<String> waybillCodes = waybillTallyMap.keySet();
                List<PickUpWaybill> pickupList = pickUpWaybillMapper.selectList(new LambdaQueryWrapper<PickUpWaybill>()
                        .in(PickUpWaybill::getWaybillCode, waybillCodes)
                        .eq(PickUpWaybill::getIsCancel, 0)
                        .select(PickUpWaybill::getWaybillCode, PickUpWaybill::getTallyId));
                Map<String, Integer> pickupCountMap = pickupList.stream()
                        .collect(Collectors.groupingBy(
                                PickUpWaybill::getWaybillCode,
                                Collectors.summingInt(v -> 1)
                        ));
                List<HzArrTally> tallyList = hzArrTallyMapper.selectList(new LambdaQueryWrapper<HzArrTally>()
                        .in(HzArrTally::getWaybillCode, waybillCodes)
                        .eq(HzArrTally::getStatus, "lh_comp")
                        .select(HzArrTally::getWaybillCode));

                Map<String, Integer> tallyCountMap = tallyList.stream()
                        .collect(Collectors.groupingBy(
                                HzArrTally::getWaybillCode,
                                Collectors.summingInt(v -> 1)
                        ));
                for (Map.Entry<String, Long> entry : waybillTallyMap.entrySet()) {
                    String waybillCode = entry.getKey();
                    Long tallyId = entry.getValue();

                    Integer pickupCount = pickupCountMap.getOrDefault(waybillCode, 0);
                    Integer tallyCount = tallyCountMap.getOrDefault(waybillCode, 0);

                    if (pickupCount == 0 || tallyCount > 0) {
                        notPickedUpVosNew.add(e);
                    }
                }

            } else {
                notPickedUpVosNew.add(e);
            }

        }
        return notPickedUpVosNew;
    }
    /**
     * 申请修改
     * @param applyEdit 申请参数
     * @return 结果
     */
    @Override
    public int applyEdit(ApplyEdit applyEdit) {
        Date date = new Date();
        applyEdit.setApplyTime(date);
        applyEdit.setCreateTime(date);
        applyEdit.setCreateBy(SecurityUtils.getUsername());
        applyEdit.setApplyStatus("申请");
        return applyEditMapper.insert(applyEdit);
    }

    /**
     * 查看申请修改列表
     * @param query 查询参数
     * @return 列表
     */
    @Override
    public List<ApplyEditVo> selectApplyList(ApplyEditQuery query) {
        List<ApplyEditVo> list =  pickUpMapper.selectApplyList(query);
        for (ApplyEditVo applyEditVo : list) {
            List<PickUpWaybill> waybills = waybillService.list(new QueryWrapper<PickUpWaybill>().eq("pick_up_id", applyEditVo.getId()));
            if (!CollectionUtils.isEmpty(waybills)){
                List<String> waybillCodes = waybills.stream().map(PickUpWaybill::getWaybillCode).distinct().collect(Collectors.toList());
                List<Mawb> mawb = mawbMapper.selectList(new QueryWrapper<Mawb>()
                        .in("waybill_code", waybillCodes)
                        .eq("type","ARR")
                        .eq("is_del",0));
                if (!CollectionUtils.isEmpty(mawb)){
                    String collect = mawb.stream().map(Mawb::getAgentCompany).filter(Objects::nonNull).collect(Collectors.joining(","));
                    applyEditVo.setAgentCompany(collect);
                }
            }
        }
        return list;
    }

    /**
     * 打印出库单
     * @param pickUpId 办理提货id
     * @return 出库单数据
     */
    @Override
    public PrintOutOrderVo printOutOrder(Long pickUpId) {
        PrintOutOrderVo vo = pickUpMapper.selectOutOrderById(pickUpId);
        List<OutOrderWaybillVo> waybillVos = waybillService.selectOutOrderById(pickUpId);
        waybillVos.forEach(e -> {
            e.setQuantityStr(String.format("%s/%s", e.getQuantity(), e.getWaybillQuantity()));
            String chargeWeightStr = String.format("%s/%s", DF_TWO.format(e.getChargeWeight()), DF_TWO.format(e.getWaybillWeight()));
            e.setChargeWeightStr(chargeWeightStr);
            //记录日志
            WaybillLog waybillLog = waybillLogService.getWaybillLog(
                    e.getWaybillCode(), 0, SecurityUtils.getNickName(),
                    null, null, null,null, null, 0,
                    null, new Date(), "打印出库单", "ARR", null);
            waybillLogService.insertWaybillLog(waybillLog);
        });
        setPrintData(vo, waybillVos);
        return vo;
    }


    /**
     * 自动办单机办单
     * @param query 查询条件
     * @return 运单数据
     */
    @Override
    public AutoOrderVo autoOrder(AutoOrderQuery query) {
        AutoOrderVo vo = hzArrTallyMapper.autoOrder(query);
        int count = allPickUpOutMapper.selectCount(new QueryWrapper<AllPickUpOut>().eq("waybill_code", query.getWaybillCode()));
        vo.setTallyNum(count);
        List<HzArrItem> hzArrItems = itemMapper.selectList(new QueryWrapper<HzArrItem>()
                .eq("waybill_code", query.getWaybillCode())
                .eq("is_del", 0));
        if (!CollectionUtils.isEmpty(hzArrItems)) {
            vo.setItems(hzArrItems);
            BigDecimal reduce = hzArrItems.stream()
                    .map(hzArrItem -> hzArrItem.getEditCharge() != null ? hzArrItem.getEditCharge() : hzArrItem.getTotalCharge())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setTotalCostSum(reduce);
        }
        return vo;
    }

    /**
     * 发送验证码
     * @param query 发送参数
     * @return 结果
     */
    @Override
    public String sendCode(AutoOrderQuery query) {
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < 4; i++) {
            String s = String.valueOf(randomChar());
            code.append(s);
        }
        String content = "【贵州航空港物流】验证码"+ code.toString() +"，用于身份验证。泄露有风险，如非本人操作，请忽略本条短信";
        SmsSendParameter param = new SmsSendParameter();
        param.setMobile(query.getPhone());
        param.setSmsParameter(JSON.toJSONString(content));
        param.setModuleName("验证码");
        SpringUtils.getBean(GzaSmsSendServiceImpl.class).sendSms(param);
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + query.getPhone();
        redisCache.setCacheObject(verifyKey, code, Constants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);
        return "验证码已发送";
    }

    /**
     * 散客H5提货办单（提交并查询运单）
     * @param query 查询参数
     * @return 运单列表
     */
    @Override
    public List<RetailVo> submitAndSelect(AutoOrderQuery query) {
        List<RetailVo> list = hzArrTallyMapper.selectRetailList(query);
        if (!CollectionUtils.isEmpty(list)){
            List<Long> ids = list.stream().map(RetailVo::getTallyId).collect(Collectors.toList());
            List<RetailVo> retailVos = list.stream().peek(e -> e.setStatus("未办单")).collect(Collectors.toList());
            List<Long> tallyIds = waybillService.selectTallyIds(ids);
            if (!CollectionUtils.isEmpty(tallyIds)){
                List<RetailVo> voList = retailVos.stream()
                        .filter(e -> tallyIds.stream()
                                .noneMatch(id -> Objects.equals(e.getTallyId(), id)))
                        .collect(Collectors.toList());
                for (RetailVo retailVo : voList) {
                    retailVo.setStatus("未提货");
                }
            }
            List<AllPickUpOut> pickUpOuts = allPickUpOutMapper.selectList(new QueryWrapper<AllPickUpOut>().in("tally_id", ids));
            if (!CollectionUtils.isEmpty(pickUpOuts)){
                List<RetailVo> voList = retailVos.stream()
                        .filter(e -> pickUpOuts.stream()
                        .map(AllPickUpOut::getTallyId)
                        .noneMatch(id -> Objects.equals(e.getTallyId(), id)))
                        .collect(Collectors.toList());
                for (RetailVo retailVo : voList) {
                    retailVo.setStatus("已提货");
                    retailVo.setCanPickUpWeight(new BigDecimal(0));
                    retailVo.setCanPickUpQuantity(0);
                }
            }
        }
        return list;
    }

    /**
     * 修改详情
     * @param id 办理提货id
     * @return 修改参数
     */
    @Override
    public HzApplyEditVo hzApplyEdit(Long id) {
        HzApplyEditVo vo = pickUpMapper.selectEditById(id);
        List<PickUpWaybill> waybills = waybillService.list(new QueryWrapper<PickUpWaybill>().eq("pick_up_id", id));
        if (!CollectionUtils.isEmpty(waybills)){
            List<String> waybillCodes = waybills.stream().map(PickUpWaybill::getWaybillCode).distinct().collect(Collectors.toList());
            List<Mawb> mawb = mawbMapper.selectList(new QueryWrapper<Mawb>()
                    .in("waybill_code", waybillCodes)
                    .eq("type","ARR")
                    .eq("is_del",0));
            if (!CollectionUtils.isEmpty(mawb)){
                String collect = mawb.stream().map(Mawb::getAgentCompany).filter(Objects::nonNull).collect(Collectors.joining(","));
                vo.setAgent(collect);
            }
        }
        List<PickUpVo> list = new ArrayList<>();
        setPickUpVo(id, list);
        vo.setPickUpVos(list);
        return vo;
    }

    /**
     * 根据流水号查询修改提货办单需要的数据
     * @param serialNo 流水号
     */
    @Override
    public HzApplyEditVo getEditDataInfo(Long serialNo) {
        HzApplyEditVo vo = pickUpMapper.selectEditBySerialNo(serialNo);
        List<PickUpWaybill> waybills = waybillService.list(new QueryWrapper<PickUpWaybill>().eq("pick_up_id", vo.getId()));
        if (!CollectionUtils.isEmpty(waybills)){
            List<String> waybillCodes = waybills.stream().map(PickUpWaybill::getWaybillCode).distinct().collect(Collectors.toList());
            List<Mawb> mawb = mawbMapper.selectList(new QueryWrapper<Mawb>()
                    .in("waybill_code", waybillCodes)
                    .eq("type","ARR")
                    .eq("is_del",0));
            if (!CollectionUtils.isEmpty(mawb)){
                String collect = mawb.stream().map(Mawb::getAgentCompany).filter(Objects::nonNull).collect(Collectors.joining(","));
                vo.setAgent(collect);
            }
        }
        List<PickUpVo> list = new ArrayList<>();
        setPickUpVo(vo.getId(), list);
        vo.setPickUpVos(list);
        return vo;
    }



    /**
     * 编辑费用明细数据
     * @param item 更新后的费用明细数据
     * @return 结果
     */
    @Override
    public int editCost(HzArrItem item) {
        item.setIsAuto(1);
        int i = itemMapper.updateById(item);
        List<HzArrItem> items = itemMapper.selectList(new QueryWrapper<HzArrItem>()
                .eq("waybill_code", item.getWaybillCode())
                .eq("tally_id", item.getTallyId()).eq("is_del",0));
        QueryWrapper<PickUpWaybill> wrapper = new QueryWrapper<PickUpWaybill>();
        wrapper.eq("tally_id", item.getTallyId())
                .eq("is_cancel",0);
        if (item.getPickUpId() != null){
            wrapper.eq("pick_up_id", item.getPickUpId());
        }
        PickUpWaybill one = waybillService.getOne(wrapper);
        BigDecimal totalCost = new BigDecimal(0);
        for (HzArrItem hzArrItem : items) {
            BigDecimal decimal = hzArrItem.getEditCharge() != null ? hzArrItem.getEditCharge() : hzArrItem.getTotalCharge();
            totalCost = totalCost.add(decimal);
        }
        if (one != null){
            one.setCostSum(totalCost);
            waybillService.updateById(one);
            List<PickUpWaybill> pickUpWaybills = waybillService.getBaseMapper().selectList(new QueryWrapper<PickUpWaybill>()
                    .eq("pick_up_id", one.getPickUpId()));
            PickUp pick = pickUpMapper.selectById(one.getPickUpId());
            BigDecimal costSum = new BigDecimal(0);
            for (PickUpWaybill pickUpWaybill : pickUpWaybills) {
                costSum = costSum.add(pickUpWaybill.getCostSum());
            }
            pick.setTotalCost(costSum);
            pick.setUpdateTime(new Date());
            pickUpMapper.updateById(pick);
        }
        return i;
    }

    /**
     * 删除费用明细
     * @param id 费用明细id
     * @return 结果
     */
    @Override
    public int delCost(Long id) {
        HzArrItem item = itemMapper.selectById(id);
        QueryWrapper<PickUpWaybill> wrapper = new QueryWrapper<PickUpWaybill>();
        wrapper.eq("tally_id", item.getTallyId())
                .eq("is_cancel",0);
        if (item.getPickUpId() != null){
            wrapper.eq("pick_up_id", item.getPickUpId());
        }
        PickUpWaybill one = waybillService.getOne(wrapper);
        if (one != null){
            BigDecimal add = one.getCostSum().subtract(item.getEditCharge() != null ? item.getEditCharge() : item.getTotalCharge());
            one.setCostSum(add);
            waybillService.updateById(one);
            PickUp pick = pickUpMapper.selectById(one.getPickUpId());
            BigDecimal add1 = pick.getTotalCost().subtract(item.getEditCharge() != null ? item.getEditCharge() : item.getTotalCharge());
            pick.setTotalCost(add1);
            pickUpMapper.updateById(pick);
        }
        return itemMapper.deleteById(id);
    }

    /**
     * 新增费用明细
     * @param item 费用明细数据
     * @return 结果
     */
    @Override
    public int addCost(HzArrItem item) {
        HzChargeIrRelation relation = relationMapper.selectById(item.getIrId());
        HzChargeRule hzChargeRule = ruleMapper.selectById(relation.getRuleId());
        if ("ColdStorageBillingRule.class".equals(hzChargeRule.getClassName())){
            HzArrTally hzArrTally = hzArrTallyMapper.selectById(item.getTallyId());
            FlightInfo info = infoMapper.selectInfoByTallyId(item.getTallyId());
            QueryWrapper<HzArrItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("waybill_code", item.getWaybillCode())
                    .eq("tally_id", hzArrTally.getId())
                    .eq("cold_store",item.getColdStore());
            queryWrapper.or(wrapper -> wrapper
                    .eq("waybill_code", item.getWaybillCode())
                    .eq("order_id", hzArrTally.getRecordOrderId())
                    .eq("cold_store",item.getColdStore()));
            HzArrItem arrItem = itemMapper.selectOne(queryWrapper);
            if (arrItem != null){
                throw new CustomException("该运单已存在冷库收费项目");
            }else {
                Mawb airWaybill = mawbMapper.selectOne(new QueryWrapper<Mawb>()
                        .eq("waybill_code", item.getWaybillCode())
                        .eq("type", "ARR")
                        .eq("is_del",0));
                HzColdRegister hzColdRegister = new HzColdRegister();
                hzColdRegister.setType("ARR");
                hzColdRegister.setColdStore(item.getColdStore());
                long startTime = item.getStoreStartTime().getTime();
                long endTime = item.getStoreEndTime().getTime();
                double diff = (endTime - startTime) / (60.0 * 60.0 * 1000.0);
                hzColdRegister.setUseTime(new BigDecimal(diff));
                long round = Math.round(diff);
                hzColdRegister.setChargeTime(new BigDecimal(round));
                hzColdRegister.setWareTime(item.getStoreStartTime());
                hzColdRegister.setOutTime(item.getStoreEndTime());
                hzColdRegister.setWaybillCode(item.getWaybillCode());
                hzColdRegister.setCargoName(airWaybill.getCargoName());
                hzColdRegister.setSumMoney(item.getEditCharge() != null ? item.getEditCharge() : item.getTotalCharge());
//                hzColdRegister.setFlightNo(info.getFlightNo());
//                hzColdRegister.setFlightDate(info.getExecDate());
                hzColdRegister.setUpdateTime(new Date());
                registerMapper.insert(hzColdRegister);
            }
        }
        QueryWrapper<PickUpWaybill> wrapper = new QueryWrapper<PickUpWaybill>();
        wrapper.eq("waybill_code", item.getWaybillCode())
                .eq("tally_id", item.getTallyId())
                .eq("is_cancel",0);
        if (item.getPickUpId() != null){
             wrapper.eq("pick_up_id", item.getPickUpId());
        }
        PickUpWaybill one = waybillService.getOne(wrapper);
        if (one != null){
            BigDecimal costSum = one.getCostSum() == null ? new BigDecimal(0) : one.getCostSum();
            BigDecimal add = costSum.add(item.getEditCharge() != null ? item.getEditCharge() : item.getTotalCharge());
            one.setCostSum(add);
            waybillService.updateById(one);
            PickUp pick = pickUpMapper.selectById(one.getPickUpId());
            BigDecimal totalCost = pick.getTotalCost() == null ? new BigDecimal(0) : pick.getTotalCost();
            BigDecimal add1 = totalCost.add(item.getEditCharge() != null ? item.getEditCharge() : item.getTotalCharge());
            pick.setTotalCost(add1);
            pick.setUpdateTime(new Date());
            pickUpMapper.updateById(pick);
        }
        item.setIsAuto(1);
        return itemMapper.insert(item);
    }

    /**
     * 计算总费用
     * @param item 计算参数
     * @return 结果
     */
    @Override
    public String countCost(HzArrItem item) {
        if (StringUtils.isEmpty(item.getColdStore())){
            String coldStore = mawbMapper.selectColdStore(item.getWaybillCode());
            if (StringUtils.isNotEmpty(coldStore)){
                item.setColdStore(coldStore);
            }
        }
        LocalTime time = LocalTime.now();
        item.setPointTime(time);
        HzChargeIrRelation relation = relationMapper.selectById(item.getIrId());
        HzChargeRule hzChargeRule = ruleMapper.selectById(relation.getRuleId());
        List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>().eq("ir_id", item.getIrId()));
        if (CollectionUtils.isEmpty(itemRules)){
            return "0";
        }
        HzChargeItems hzChargeItems = chargeItemsMapper.selectById(relation.getItemId());
//        HzArrTally hzArrTally = hzArrTallyMapper.selectById(item.getTallyId());
        NotPickedUpVo notPickedUpVo = hzArrTallyMapper.selectNotPickedUp(item.getWaybillCode());
        BigDecimal decimal = new BigDecimal(0);
        if (notPickedUpVo != null){
            Mawb mawb = mawbMapper.selectChargeWeight(item.getWaybillCode());
            BigDecimal weightRateCeiling;
            BigDecimal weightRateFloor;
            BigDecimal chargeWeight1 = mawb.getChargeWeight() == null ? new BigDecimal(0) : mawb.getChargeWeight();
            if (mawb.getWeight() == null || mawb.getWeight().compareTo(new BigDecimal(0)) == 0){
                weightRateCeiling = new BigDecimal(0);
                weightRateFloor = new BigDecimal(0);
            }else {
//                BigDecimal bigDecimal = chargeWeight1.divide(mawb.getWeight(),5, RoundingMode.DOWN).multiply(hzArrTally.getWeight());
                BigDecimal bigDecimal = chargeWeight1.divide(mawb.getWeight(),5, RoundingMode.DOWN).multiply(notPickedUpVo.getCanWeight());
                weightRateCeiling = bigDecimal.setScale(0, RoundingMode.CEILING);
                weightRateFloor = bigDecimal.setScale(0, RoundingMode.FLOOR);
            }
            BillingRule rule = BillingRuleFactory.createRule(hzChargeRule.getClassName());
            if (item.getTotalCharge() == null){
//                BillRuleVo vo1 = rule.calculateFee(itemRules, weightRateCeiling, hzArrTally.getPieces(), item);
                BillRuleVo vo1 = rule.calculateFee(itemRules, weightRateCeiling, notPickedUpVo.getCanQuantity(), item);
                decimal = BigDecimalRoundUtils.bigDecimalRound(hzChargeItems.getRoundRule(),vo1.getTotalCharge());
            }else {
//                BillRuleVo vo1 = rule.calculateFee(itemRules, weightRateCeiling, hzArrTally.getPieces(), item);
                BillRuleVo vo1 = rule.calculateFee(itemRules, weightRateCeiling, notPickedUpVo.getCanQuantity(), item);
                if (vo1.getTotalCharge().compareTo(item.getTotalCharge()) >= 0){
                    decimal = BigDecimalRoundUtils.bigDecimalRound(hzChargeItems.getRoundRule(),vo1.getTotalCharge());
                }else {
//                    BillRuleVo vo2 = rule.calculateFee(itemRules, weightRateFloor, hzArrTally.getPieces(), item);
                    BillRuleVo vo2 = rule.calculateFee(itemRules, weightRateFloor, notPickedUpVo.getCanQuantity(), item);
                    decimal = BigDecimalRoundUtils.bigDecimalRound(hzChargeItems.getRoundRule(),vo2.getTotalCharge());
                }
            }
        }
        return decimal.toString();
    }

    /**
     * 结算
     * @param waybillCode 运单号
     * @param type 进出港类型
     * @return 结果
     */
    @Override
    public SettleVo settle(String waybillCode, String type) {
        SettleVo vo = new SettleVo();
        if ("WL".equals(type)){
            BaseAgent agent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", SecurityUtils.getDeptId()));
            if (agent != null && agent.getSettleMethod() != null) {
                vo.setBalance(agent.getBalance());
                vo.setPayMethod(agent.getSettleMethod().toString());
            }
        }else {
            WaybillItemVo itemVo = mawbMapper.selectDeptId(waybillCode);
            BaseAgent agent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", itemVo.getDeptId()));
            if (agent != null && agent.getSettleMethod() != null) {
                vo.setSettleMethod(agent.getSettleMethod());
                if (agent.getSettleMethod().equals(1)) {
                    vo.setBalance(agent.getBalance());
                }
            }
        }
        return vo;
    }

    /**
     * 散客在线提货办单
     * @param tallyId 理货id
     * @return 结果
     */
    @Override
    public OrderProVo orderPro(Long tallyId) {
        OrderProVo vo = hzArrTallyMapper.selectOrderData(tallyId);
        PickUp pick = waybillService.selectTallyId(tallyId);
        if (pick != null){
            vo.setPickUpId(pick.getId());
            vo.setSerialNo(pick.getSerialNo());
        }
        AllPickUpOut pickUpOut = allPickUpOutMapper.selectOne(new QueryWrapper<AllPickUpOut>().eq("tally_id", tallyId));
        if (pickUpOut != null){
            vo.setCanPickQuantity(0);
            vo.setCanPickWeight(new BigDecimal(0));
        }
        List<HzArrItem> list = itemMapper.selectList(new QueryWrapper<HzArrItem>().eq("tally_id",tallyId));
        if (!CollectionUtils.isEmpty(list)){
            BigDecimal totalCost = list.stream()
                    .map(hzArrItem -> hzArrItem.getEditCharge() != null ? hzArrItem.getEditCharge() : hzArrItem.getTotalCharge())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setTotalCost(totalCost);
            vo.setHzArrItems(list);
        }
        return vo;
    }

    /**
     * 根据id查询修改操作区数据
     * @param id 主键id
     * @return 操作区数据
     */
    @Override
    public OperDataVo operData(Long id) {
        OperDataVo vo = new OperDataVo();
        PickUpWaybill pickUpWaybill = waybillService.getById(id);
        if (pickUpWaybill == null){
            throw new CustomException("无当前提货信息");
        }
        int count = allPickUpOutMapper.selectCount(new QueryWrapper<AllPickUpOut>().eq("waybill_code",pickUpWaybill.getWaybillCode()));
        vo.setNum(count);
        vo.setId(pickUpWaybill.getId());
        vo.setTallyId(pickUpWaybill.getTallyId());
        vo.setWaybillCode(pickUpWaybill.getWaybillCode());
        vo.setTallyQuantity(pickUpWaybill.getCanPickUpQuantity());
        vo.setTallyWeight(pickUpWaybill.getCanPickUpWeight());
        return vo;
    }

    /**
     * 修改完成
     * @param vo 修改数据
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int editComp(OperDataVo vo) {
        HttpServletResponse response = ServletUtils.getResponse();
        WaybillLog waybillLog = new WaybillLog();
        try {
            PickUpWaybill byId = waybillService.getById(vo.getId());
            //修改办单数据
        byId.setCanPickUpQuantity(vo.getPickedUpQuantity());
        byId.setCanPickUpWeight(vo.getPickedUpWeight());
        byId.setUpdateTime(new Date());
        byId.setUpdateBy(SecurityUtils.getUsername());
        waybillService.updateById(byId);

        //修改理货数据 但是有多条就不能修改
        List<HzArrTally> hzArrTallyList = hzArrTallyMapper.selectList(new QueryWrapper<HzArrTally>()
                    .eq("waybill_code", vo.getWaybillCode()));
        if(hzArrTallyList.size() > 1){
            throw new CustomException("该单有多次理货记录 请到理货页面进行修改");
        }
        HzArrTally hzArrTally = hzArrTallyList.get(0);
        hzArrTally.setPieces(vo.getTallyQuantity());
        hzArrTally.setWeight(vo.getTallyWeight());
        hzArrTallyMapper.updateById(hzArrTally);

            //修改出库数据的提货件数和重量
            AllPickUpOut allPickUpOut = allPickUpOutMapper.selectById(vo.getId());
            if (allPickUpOut == null) {
                throw new CustomException("无当前提货出库信息");
            }
            allPickUpOut.setPiecesChanged(vo.getPickedUpQuantity());
            allPickUpOut.setWeightChanged(vo.getPickedUpWeight());
            allPickUpOut.setUpdateTime(new Date());
            allPickUpOutMapper.updateById(allPickUpOut);
            //修改提货数据后需要重新结算 多退少补
            settleForPickUpEdit(allPickUpOut,byId);
            if(vo.getEditId() != null){
                ApplyEdit applyEdit = applyEditMapper.selectById(vo.getEditId());
                applyEdit.setApplyStatus("已修改");
                applyEdit.setUpdateBy(SecurityUtils.getUsername());
                applyEdit.setUpdateTime(new Date());
                applyEditMapper.updateById(applyEdit);
            }
            //运单日志的新增
            waybillLog = waybillLogService.getWaybillLog(
                    allPickUpOut.getWaybillCode(), 0, SecurityUtils.getNickName(),
                    vo.getPickedUpWeight().toString(), vo.getPickedUpWeight().toString(), null,
                    vo, null, 0, null, new Date(),
                    "提货出库，提货件数："+vo.getPickedUpQuantity()+"，提货重量："+vo.getPickedUpWeight(),
                    "ARR", null);
            return 1;
        }catch(Exception e){
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作失败" +  "," +
                                "code:" + response.getStatus()));
                waybillLog.setErrorMsg(e.getMessage());
                waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        }finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }

    private void settleForPickUpEdit(AllPickUpOut pickUpOut, PickUpWaybill upWaybill) {
        if (pickUpOut == null){
            return;
        }
        if (pickUpOut.getWeightChanged() == null){
            return;
        }
        if (Objects.equals(pickUpOut.getWeight(),pickUpOut.getWeightChanged())){
            return;
        }
        BigDecimal totalCost = new BigDecimal(0);
        BigDecimal oldTotalCost = new BigDecimal(0);
        List<HzArrTally> hzArrTallyList = hzArrTallyMapper.selectList(new QueryWrapper<HzArrTally>()
                .eq("waybill_code", upWaybill.getWaybillCode())
                .eq("status", "save_out"));
        if(hzArrTallyList.size() == 0){
            return;
        }
        List<Long> tallyIds = hzArrTallyList.stream().map(HzArrTally::getId).collect(Collectors.toList());
        List<HzArrItem> items = itemMapper.selectCostListByCodeAndTallyList(upWaybill.getWaybillCode(),tallyIds);
        Integer status = mawbMapper.selectBillStatus(upWaybill.getWaybillCode());
        if (!CollectionUtils.isEmpty(items) && status > 4 && status < 9){
            oldTotalCost = items.stream().map(HzArrItem::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        Mawb mawb = mawbMapper.selectOne(new QueryWrapper<Mawb>()
                .eq("waybill_code",upWaybill.getWaybillCode())
                .eq("type","ARR")
                .eq("is_del",0));
        Mawb updateMawb = new Mawb();
        updateMawb.setId(mawb.getId());
        updateMawb.setVersion(mawb.getVersion());
        updateMawb.setDeptId(mawb.getDeptId());
        updateMawb.setWaybillCode(mawb.getWaybillCode());
        BigDecimal weightRateCeiling;
        BigDecimal weightRateFloor;
        BigDecimal chargeWeight = mawb.getChargeWeight() == null ? new BigDecimal(0) : mawb.getChargeWeight();
        if (mawb.getWeight() == null || mawb.getWeight().compareTo(new BigDecimal(0)) == 0){
            weightRateCeiling = new BigDecimal(0);
            weightRateFloor = new BigDecimal(0);
        }else {
            BigDecimal bigDecimal = chargeWeight.divide(mawb.getWeight(),5, RoundingMode.DOWN).multiply(pickUpOut.getWeight());
            weightRateCeiling = bigDecimal.setScale(0, RoundingMode.CEILING);
            weightRateFloor = bigDecimal.setScale(0, RoundingMode.FLOOR);
        }
        for (HzArrItem item : items) {
            BillRuleVo vo1 = countCost(item, weightRateCeiling, pickUpOut.getPieces());
            BillRuleVo vo2 = countCost(item, weightRateFloor, pickUpOut.getPieces());
            if (vo1.getTotalCharge().compareTo(item.getTotalCharge()) >= 0){
                item.setTotalCharge(vo1.getTotalCharge());
                totalCost = totalCost.add(vo1.getTotalCharge());
            }else {
                item.setTotalCharge(vo2.getTotalCharge());
                totalCost = totalCost.add(vo2.getTotalCharge());
            }
            itemMapper.updateById(item);
        }
        WaybillFee waybillFee = feeMapper.selectOne(new QueryWrapper<WaybillFee>()
                .eq("waybill_code", mawb.getWaybillCode())
                .eq("dept_id", mawb.getDeptId())
                .eq("type","DEP"));
        BaseAgent agent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", mawb.getDeptId()));
        BigDecimal refund = new BigDecimal(0);
        if (agent != null){
            switch (agent.getSettleMethod()){
                case 0:
                    if (totalCost.compareTo(oldTotalCost) < 0){
                        BigDecimal add = oldTotalCost.subtract(totalCost);
                        updateStatus(updateMawb, totalCost, waybillFee, 9, add);
                    }else {
                        updateStatus(updateMawb, totalCost, waybillFee, 5, refund);
                    }
                    break;
                case 1:
                    if (totalCost.compareTo(oldTotalCost) > 0){
                        BigDecimal deduct = totalCost.subtract(oldTotalCost);
                        BigDecimal balance = agent.getBalance() == null ? new BigDecimal(0) : agent.getBalance();
                        BigDecimal subtract = balance.subtract(deduct);
                        if (subtract.compareTo(new BigDecimal(0)) < 0) {
                            throw new CustomException("代理人余额不足");
                        } else {
                            agent.setBalance(subtract);
                            baseAgentMapper.updateBaseAgent(agent);
                            BaseBalance baseBalance = new BaseBalance();
                            baseBalance.setAgentId(agent.getId());
                            baseBalance.setBalance(agent.getBalance());
                            baseBalance.setType("减少余额");
                            baseBalance.setCreateTime(new Date());
                            baseBalance.setCreateBy(SecurityUtils.getNickName());
                            // todo 流水号需从银联支付接口获取
                            //baseBalance.setSerialNo();
                            baseBalance.setTradeMoney(deduct);
                            baseBalance.setWaybillCode(mawb.getWaybillCode());
                            baseBalance.setRemark("修改提货信息");
                            baseBalanceMapper.insertBaseBalance(baseBalance);
                            updateStatus(updateMawb, totalCost, waybillFee, 6, refund);
                        }
                    }else if (totalCost.compareTo(oldTotalCost) == 0){
                        updateStatus(updateMawb, totalCost, waybillFee, 6, refund);
                    }else {
                        BigDecimal add = oldTotalCost.subtract(totalCost);
                        BigDecimal balance = agent.getBalance() == null ? new BigDecimal(0) : agent.getBalance();
                        BigDecimal subtract = balance.add(add);
                        agent.setBalance(subtract);
                        baseAgentMapper.updateBaseAgent(agent);
                        BaseBalance baseBalance = new BaseBalance();
                        baseBalance.setAgentId(agent.getId());
                        baseBalance.setBalance(agent.getBalance());
                        baseBalance.setType("增加余额");
                        baseBalance.setCreateTime(new Date());
                        baseBalance.setCreateBy(SecurityUtils.getNickName());
                        // todo 流水号需从银联支付接口获取
                        //baseBalance.setSerialNo();
                        baseBalance.setTradeMoney(add);
                        baseBalance.setWaybillCode(mawb.getWaybillCode());
                        baseBalance.setRemark("修改提货信息");
                        baseBalanceMapper.insertBaseBalance(baseBalance);
                        updateStatus(updateMawb, totalCost, waybillFee, 10, add);
                    }
                    break;
                case 2:
                    if (totalCost.compareTo(oldTotalCost) < 0){
                        BigDecimal add = oldTotalCost.subtract(totalCost);
                        if (agent.getPayMethod() == 0){
                            updateStatus(updateMawb, totalCost, waybillFee, 11, add);
                        }else {
                            updateStatus(updateMawb, totalCost, waybillFee, 12, add);
                        }
                    }else {
                        if (agent.getPayMethod() == 0){
                            updateStatus(updateMawb, totalCost, waybillFee, 7, refund);
                        }else {
                            updateStatus(updateMawb, totalCost, waybillFee, 8, refund);
                        }
                    }
                    break;
                default:
                    updateStatus(updateMawb, totalCost, waybillFee, 8, refund);
                    break;
            }
        }
        upWaybill.setCostSum(totalCost);
        waybillService.updateById(upWaybill);
        List<PickUpWaybill> pickUpWaybills = pickUpWaybillMapper.selectList(new QueryWrapper<PickUpWaybill>()
                .eq("pick_up_id", pickUpOut.getPickUpId()));
        BigDecimal costSum = pickUpWaybills.stream()
                .map(PickUpWaybill::getCostSum)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //修改办理提货表的金额
        PickUp pickUp = pickUpMapper.selectById(pickUpOut.getPickUpId());
        if (costSum.compareTo(new BigDecimal(0)) != 0 && costSum.compareTo(pickUp.getTotalCost()) != 0){
            pickUp.setTotalCost(costSum);
        }
        pickUp.setPayStatus(2);
        pickUp.setUpdateTime(new Date());
        pickUpMapper.updateById(pickUp);
    }

    public BillRuleVo countCost(HzArrItem item, BigDecimal weight,Integer quantity) {
        HzChargeIrRelation relation = relationMapper.selectById(item.getIrId());
        HzChargeItems hzChargeItems = chargeItemsMapper.selectById(relation.getItemId());
        HzChargeRule hzChargeRule = ruleMapper.selectById(relation.getRuleId());
        List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>().eq("ir_id", item.getIrId()));
        if (CollectionUtils.isEmpty(itemRules)){
            BillRuleVo vo = new BillRuleVo();
            vo.setTotalCharge(new BigDecimal(0));
            return vo;
        }
        BillingRule rule = BillingRuleFactory.createRule(hzChargeRule.getClassName());
        BillRuleVo ruleVo = rule.calculateFee(itemRules, weight, quantity, item);
        BigDecimal totalCharge = BigDecimalRoundUtils.bigDecimalRound(hzChargeItems.getRoundRule(), ruleVo.getTotalCharge());
        ruleVo.setTotalCharge(totalCharge);
        return ruleVo;
    }

    private void updateStatus(Mawb mawb, BigDecimal costSum, WaybillFee waybillFee, int mawbStatus, BigDecimal refund) {
        if (waybillFee != null) {
            waybillFee.setSettleTime(new Date());
            waybillFee.setSettleMoney(costSum);
            waybillFee.setRefund(refund);
            waybillFee.setStatus(1);
            if (StringUtils.isNotEmpty(mawb.getSerialNo())){
                waybillFee.setSerialNo(mawb.getSerialNo());
            }
            feeMapper.updateById(waybillFee);
        } else {
            WaybillFee fee = new WaybillFee();
            fee.setSettleMoney(costSum);
            fee.setSettleTime(new Date());
            fee.setRefund(refund);
            fee.setWaybillCode(mawb.getWaybillCode());
            fee.setDeptId(mawb.getDeptId());
            fee.setStatus(1);
            fee.setType("ARR");
            if (StringUtils.isNotEmpty(mawb.getSerialNo())){
                fee.setSerialNo(mawb.getSerialNo());
            }
            feeMapper.insert(fee);
        }
        mawb.setPayStatus(mawbStatus);
        mawb.setRefund(refund);
        mawb.setPayMoney(costSum);
        mawb.setSettleTime(new Date());
        int i = mawbMapper.updateById(mawb);
        if (i == 0){
            throw new CustomException("当前运单正在被其他操作，请刷新后再试");
        }
    }



    /**
     * 拒绝修改
     * @param applyEdit 拒绝信息
     * @return 结果
     */
    @Override
    public int refuseEdit(ApplyEdit applyEdit) {
        //applyEdit传的id是all_in_pick_up里面的id,要转一下,转为all_in_apply_edit的id 通过流水号吧
        PickUp pickUpForId = pickUpMapper.selectOne(new QueryWrapper<PickUp>()
                .eq("id", applyEdit.getId()));
        /*ApplyEdit edit = applyEditMapper.selectById(applyEdit.getId());*/
        /*edit.setRefuseEdit(applyEdit.getRefuseEdit());*/
        ApplyEdit edit = applyEditMapper.selectOne(new QueryWrapper<ApplyEdit>()
                .eq("serial_no", pickUpForId.getSerialNo()));
        edit.setRefuseEdit(applyEdit.getRefuseEdit());
        edit.setRefuseTime(new Date());
        edit.setApplyStatus("已拒绝");
        return applyEditMapper.updateById(edit);
    }

    /**
     * 查看提货码
     * @param pickUpId 提货id
     * @return 结果
     */
    @Override
    public PickUpCode pickUpCode(Long pickUpId) {
        PickUpCode code = new PickUpCode();
        PickUp pick = pickUpMapper.selectById(pickUpId);
        code.setPickUpCode(pick.getPickUpCode());
        try {
            String s = QRCodeGenerator.generateQRCodeBase64(pick.getPickUpCode());
            code.setQrCode(s);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return code;
    }

    /**
     * 代理人H5提货出库列表
     * @param query 查询条件
     * @return 列表数据
     */
    @Override
    public List<H5PickUpOutVo> arrH5List(H5PickUpOutQuery query) {
        Date pickUpTime = query.getPickUpTime();
        if (pickUpTime != null){
            LocalDate localDate = pickUpTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

            LocalDateTime startOfDay = localDate.atStartOfDay();
            Date startDate = Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
            query.setStartTime(startDate);

            LocalDateTime endOfDay = localDate.atTime(LocalTime.MAX);
            Date endDate = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
            query.setEndTime(endDate);
        }
        query.setDeptId(SecurityUtils.getHighParentId());
        List<H5PickUpOutVo> h5PickUpOutVo = pickUpMapper.arrH5List(query);
        for (H5PickUpOutVo pickUpOutVo : h5PickUpOutVo) {
            Integer count = allPickUpOutMapper.selectCount(new QueryWrapper<AllPickUpOut>().eq("pick_up_id", pickUpOutVo.getPickUpId()));
            if (count > 0){
                pickUpOutVo.setStatus("已提货");
            }else {
                pickUpOutVo.setStatus("未提货");
            }
            List<String> waybillCodeList = pickUpWaybillMapper.selectWaybillCode(pickUpOutVo.getPickUpId());
            pickUpOutVo.setWaybillCodeList(waybillCodeList);
        }
       return h5PickUpOutVo;
    }

    /**
     * 办单详情
     * @param pickUpId 办单id
     * @return 详情
     */
    @Override
    public H5PickUpOutVo getInfo(Long pickUpId) {
        H5PickUpOutVo vo = pickUpMapper.selectH5Info(pickUpId);
        Integer count = allPickUpOutMapper.selectCount(new QueryWrapper<AllPickUpOut>().eq("pick_up_id", vo.getPickUpId()));
        if (count > 0){
            vo.setStatus("已提货");
        }else {
            vo.setStatus("未提货");
        }
        List<H5OutInfoVo> voList = waybillService.selectH5Info(vo.getPickUpId());
        if (!CollectionUtils.isEmpty(voList)){
            vo.setVoList(voList);
        }
        return vo;
    }

    /**
     * 验证验证码
     * @param query 验证参数
     * @return 结果
     */
    @Override
    public String verifyCode(AutoOrderQuery query) {
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(query.getPhone(), "");
        String code = redisCache.getCacheObject(verifyKey);
        redisCache.deleteObject(verifyKey);
        if (code == null)
        {
            throw new CustomException("验证码已失效");
        }
        if (!query.getCode().equalsIgnoreCase(code))
        {
            throw new CustomException("验证码错误");
        }
        return "验证成功";
    }

    /**
     * 余额支付
     * @param vo 办单数据
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized PickUp balance(PickOrderVo vo) {
        PickUp up = add(vo);
        Date date = new Date();
        String code = generateUniqueCode();
        ArrayList<WaybillLog> waybillLogs = new ArrayList<>();
        HttpServletResponse response = ServletUtils.getResponse();
        try {
            List<PickUpWaybill> upWaybills = waybillService.list(new QueryWrapper<PickUpWaybill>().eq("pick_up_id", up.getId()));
            BigDecimal totalCost = new BigDecimal(0);
            PickUpWaybill pickUpWaybill = upWaybills.get(0);
            Mawb mawb = mawbMapper.selectOne(new QueryWrapper<Mawb>()
                    .eq("waybill_code", pickUpWaybill.getWaybillCode())
                    .eq("type", "ARR"));
            BaseAgent agent;
            if ("wl".equals(vo.getType())){
                agent = baseAgentMapper.selectOne(
                        new LambdaQueryWrapper<BaseAgent>()
                                .eq(BaseAgent::getDeptId, SecurityUtils.getDeptId()));
            }else {
                agent = determineSettlementClient(mawb.getDeptId());
            }
            for (PickUpWaybill upWaybill : upWaybills) {
                List<HzArrItem> items = itemMapper.selectCostListByPickUpId(upWaybill.getWaybillCode(), upWaybill.getTallyId(),up.getId());
                BigDecimal reduce = new BigDecimal(0);
                if (!CollectionUtils.isEmpty(items)) {
                    reduce = items.stream()
                            .map(hzArrItem -> hzArrItem.getEditCharge() != null ? hzArrItem.getEditCharge() : hzArrItem.getTotalCharge())
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    totalCost = totalCost.add(reduce);
                    List<HzArrItem> itemList = itemMapper.selectItemByTallyId(upWaybill.getWaybillCode());
                    if (!CollectionUtils.isEmpty(itemList)){
                        itemList.forEach(e->{
                            e.setPickUpId(up.getId());
                            e.setTallyId(upWaybill.getTallyId());
                            itemMapper.updateById(e);
                        });
                    }
                }
                //将理货历史的状态改为settle
                List<HzArrTally> hzArrTallyListForSave = hzArrTallyMapper.selectList(new QueryWrapper<HzArrTally>()
                        .eq("waybill_code", upWaybill.getWaybillCode())
                        .eq("status", "save"));
                hzArrTallyListForSave.forEach(hzArrTally -> {
                    hzArrTally.setStatus("settle");
                    hzArrTallyMapper.updateById(hzArrTally);
                });
                Mawb mawb1 = mawbMapper.selectOne(new QueryWrapper<Mawb>()
                        .eq("waybill_code", upWaybill.getWaybillCode())
                        .eq("type", "ARR"));
                Mawb updateMawb = new Mawb();
                updateMawb.setId(mawb1.getId());
                updateMawb.setVersion(mawb1.getVersion());
                updateMawb.setDeptId(mawb1.getDeptId());
                updateMawb.setStatus("comp_order");
                updateMawb.setWaybillCode(mawb1.getWaybillCode());
                updateMawb.setSerialNo(up.getSerialNo());
                WaybillFee waybillFee = feeMapper.selectOne(new QueryWrapper<WaybillFee>()
                        .eq("waybill_code", mawb1.getWaybillCode())
                        .eq("dept_id", mawb1.getDeptId())
                        .eq("type", "ARR"));
                BigDecimal refund = new BigDecimal(0);
                if (agent != null){
                    if ("hz".equals(vo.getType())){
                        if ("线上".equals(vo.getPayMethod())){
                            if (agent.getSettleMethod() == 2 && agent.getPayMethod() == 0){
                                updateStatus(updateMawb, reduce, waybillFee, 4, refund);
                            }else {
                                throw new CustomException("仅支持现金支付或者扫码支付");
                            }
                        }else {
                            updateStatus(updateMawb, reduce, waybillFee, 3, refund);
                        }
                    }
                    if ("wl".equals(vo.getType())) {
                        if (agent.getSettleMethod() == 1) {
                            BigDecimal balance = agent.getBalance() == null ? new BigDecimal(0) : agent.getBalance();
                            BigDecimal subtract = balance.subtract(reduce);
                            if (subtract.compareTo(new BigDecimal(0)) < 0) {
                                throw new CustomException("代理人余额不足，无法支付！");
                            } else {
                                agent.setBalance(subtract);
                                baseAgentMapper.updateBaseAgent(agent);
                                BaseBalance baseBalance = new BaseBalance();
                                baseBalance.setAgentId(agent.getId());
                                baseBalance.setBalance(agent.getBalance());
                                baseBalance.setType("减少余额");
                                baseBalance.setCreateTime(new Date());
                                baseBalance.setCreateBy(SecurityUtils.getUsername());
                                // todo 流水号需从银联支付接口获取
                                //baseBalance.setSerialNo();
                                baseBalance.setTradeMoney(reduce);
                                baseBalance.setWaybillCode(upWaybill.getWaybillCode());
                                baseBalance.setRemark("办单支付");
                                baseBalanceMapper.insertBaseBalance(baseBalance);
                                updateStatus(updateMawb, reduce, waybillFee, 2, refund);
                            }
                        } else if (agent.getSettleMethod() == 0){
                            updateStatus(updateMawb, reduce, waybillFee, 1, refund);
                        }else {
                            if (agent.getPayMethod() == 0){
                                updateStatus(updateMawb, reduce, waybillFee, 4, refund);
                            }else {
                                updateStatus(updateMawb, reduce, waybillFee, 3, refund);
                            }
                        }
                    }
                    //运单日志的新增
                    WaybillLog waybillLog = waybillLogService.getWaybillLog(
                            upWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                            upWaybill.getCanPickUpWeight().toString(), upWaybill.getCanPickUpQuantity().toString(), null,
                            up.getId().toString(), null, 0, null, new Date(),
                            "进港结算,结算支付金额:" + upWaybill.getCostSum(),
                            "ARR", null);
                    waybillLogs.add(waybillLog);
                }else {
                    updateStatus(updateMawb, reduce, waybillFee, 3, refund);
                    //运单日志的新增
                    WaybillLog waybillLog = waybillLogService.getWaybillLog(
                            upWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                            upWaybill.getCanPickUpWeight().toString(), upWaybill.getCanPickUpQuantity().toString(), null,
                            up.getId().toString(), null, 0, null, new Date(),
                            "散客进港结算,结算支付金额:" + upWaybill.getCostSum(),
                            "ARR", null);
                    waybillLogs.add(waybillLog);
                }

            }
            up.setIsPay(1);
            if (agent != null){
                if ("hz".equals(vo.getType())){
                    if (agent.getSettleMethod() == 2 && agent.getPayMethod() == 0){
                        up.setPayStatus(4);
                    }else {
                        up.setPayStatus(3);
                    }
                }else {
                    if (agent.getSettleMethod() == 1) {
                        up.setPayStatus(2);
                    } else if (agent.getSettleMethod() == 0){
                        up.setPayStatus(1);
                    }else {
                        if (agent.getPayMethod() == 0){
                            up.setPayStatus(4);
                        }else {
                            up.setPayStatus(3);
                        }
                    }
                }
            }else {
                up.setPayStatus(3);
            }
            up.setPayTime(date);
            up.setTotalCost(totalCost);
            up.setPickUpCode(code);
            up.setOutTime(date);
            up.setUpdateTime(new Date());
            pickUpMapper.updateById(up);
            //同步修改服务申请/审核的预授权支付时间和金额
            ServiceRequest serviceRequest = serviceRequestMapper.selectOne(new QueryWrapper<ServiceRequest>()
                    .eq("waybill_code", mawb.getWaybillCode())
                    .like("service_item", mawb.getColdStore()));
            if (serviceRequest != null) {
                if ("UNAUDITED".equals(serviceRequest.getStatus())) {
                    throw new CustomException("该运单冷库服务申请未审批");
                }
                if ("NOPASS".equals(serviceRequest.getStatus())) {
                    throw new CustomException("该运单冷库服务申请未通过");
                }
                serviceRequest.setSettleTime(new Date());
                serviceRequest.setEndMoney(totalCost);
                serviceRequest.setUpdateTime(new Date());
                serviceRequestMapper.updateById(serviceRequest);
            }
            for (WaybillLog waybillLog:waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作成功" +  "," +
                                "code:" + response.getStatus()));
            }
            return up;
        }catch (Exception e){
            for (WaybillLog waybillLog:waybillLogs) {
                waybillLog.setJsonResult(waybillLogService.getJson(
                        "msg:" + "操作失败" +  "," +
                                "code:" + response.getStatus()));
                waybillLog.setErrorMsg(e.getMessage());
                waybillLog.setStatus(1);
            }
            throw new CustomException(e.getMessage());
        }finally {
            for (WaybillLog waybillLog:waybillLogs) {
                waybillLogService.insertWaybillLog(waybillLog);
            }
        }
    }

    private String generateUniqueCode(){
        String code;
        int attempts = 0;
        do {
            if (attempts++ > 50){
                throw new CustomException("生成提货码失败，请稍后再试");
            }
            code = String.format("%06d", RANDOM.nextInt(1_000_000));
        }while (isCodeUsed(code));
        return code;
    }

    private boolean isCodeUsed(String code) {
        return pickUpMapper.existsByCodeAndTime(code, new Date());
    }

    /**
     * 根据收费项目id查询最高优先级规则
     * @param vo 收费项目id
     * @return 最该优先级规则
     */
    @Override
    public ChargeRuleVo getHighRule(ItemDetailVo vo) {
        ChargeRuleVo ruleVo = new ChargeRuleVo();
        ruleVo.setIsNoCharge(0);
        List<HzChargeIrRelation> relations = relationMapper.selectList(new QueryWrapper<HzChargeIrRelation>().eq("item_id", vo.getItemId()).eq("is_del",0));
        if (CollectionUtils.isEmpty(relations)){
            return ruleVo;
        }
        ItemWaybillVo waybillVo = mawbMapper.selectWaybillItemInfo(vo);
        int maxMatchCount = 0;
        List<HzChargeIrRelation> ruleList = new ArrayList<>();
        for (HzChargeIrRelation hzChargeRule : relations) {
            int matchCount = 0;
            if (StringUtils.isNotEmpty(hzChargeRule.getNoCharge()) && !hzChargeRule.getNoCharge().contains(waybillVo.getDeptId().toString())){
                continue;
            }
            if (StringUtils.isNotEmpty(hzChargeRule.getPrefix()) && !hzChargeRule.getPrefix().contains(vo.getWaybillCode().substring(4,7))){
                continue;
            }
            if (StringUtils.isNotEmpty(hzChargeRule.getCategory()) && !hzChargeRule.getCategory().contains(waybillVo.getCategoryName())){
                continue;
            }
            if (hzChargeRule.getIsSouth() == 1){
                continue;
            }
            if (hzChargeRule.getIsExit() == 1){
                continue;
            }
            if (hzChargeRule.getCrossAir() == 1){
                continue;
            }
            int cargoMatchCount = isCargoCodeMatch(hzChargeRule, waybillVo.getCargoCode());
            if (cargoMatchCount >= 0) {
                matchCount += cargoMatchCount;
            }

            if (matchCount > 0) {
                if (matchCount > maxMatchCount) {
                    maxMatchCount = matchCount;
                    ruleList.clear();
                    ruleList.add(hzChargeRule);
                } else if (matchCount == maxMatchCount) {
                    ruleList.add(hzChargeRule);
                }
            }
        }
        if (ruleVo.getIsNoCharge() == 1){
            throw new CustomException("该费用项目设置不收此代理人");
        }
        if (CollectionUtils.isEmpty(ruleList)){
            throw new CustomException("该费用项目不适配本运单");
        }
        HzChargeIrRelation relation = ruleList.stream().max(Comparator.comparing(HzChargeIrRelation::getPriority)).orElse(null);
        if (relation == null){
            return ruleVo;
        }
        HzChargeRule rule = ruleMapper.selectById(relation.getRuleId());
        ruleVo.setIrId(relation.getId());
        ruleVo.setClassName(rule.getClassName());
        ruleVo.setRuleName(rule.getRuleName());
        return ruleVo;
    }


    private void setPickUpVo(Long id, List<PickUpVo> list) {
        List<PickUpWaybill> waybills = waybillService.list(new QueryWrapper<PickUpWaybill>().eq("pick_up_id", id));
        for (PickUpWaybill waybill : waybills) {
            BigDecimal rate = itemMapper.selectFeeRate(waybill.getWaybillCode());
            PickUpVo pickUpVo = new PickUpVo();
            List<AllPickUpOut> pickUpOuts = allPickUpOutMapper.selectList(new QueryWrapper<AllPickUpOut>().eq("waybill_code", waybill.getWaybillCode()));
            if (!CollectionUtils.isEmpty(pickUpOuts)){
                int sum = pickUpOuts.stream()
                        .mapToInt(item -> item.getPiecesChanged() != null ? item.getPiecesChanged() : item.getPieces())
                        .sum();
                pickUpVo.setPickedUpQuantity(sum);
                BigDecimal reduce = pickUpOuts
                        .stream().map(item -> item.getWeightChanged() != null ? item.getWeightChanged() : item.getWeight())
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                pickUpVo.setPickedUpWeight(reduce);
            }
            BeanUtils.copyProperties(waybill, pickUpVo);
            Mawb mawb = mawbMapper.selectOne(new QueryWrapper<Mawb>()
                    .eq("waybill_code", waybill.getWaybillCode())
                    .eq("type","ARR"));
            String flightNo = getFlightNo(waybill.getWaybillCode());
            String flightDate = getFlightDate(waybill.getWaybillCode());
            pickUpVo.setFlightNo(flightNo);
            pickUpVo.setFlightDate(flightDate);
            pickUpVo.setProcessingFeeRate(rate);
            if (mawb != null){
                pickUpVo.setCargoName(mawb.getCargoName());
                pickUpVo.setConsign(mawb.getConsign());
                pickUpVo.setQuantity(mawb.getQuantity());
                pickUpVo.setWeight(mawb.getWeight());
                pickUpVo.setChargeWeight(mawb.getChargeWeight());
                pickUpVo.setSpecialCargoCode1(mawb.getSpecialCargoCode1());
                pickUpVo.setAgentCode(mawb.getAgentCompany());
                list.add(pickUpVo);
            }
        }
    }

    //随机数字
    private static char randomChar() {
        String str = "0123456789";
        return str.charAt(RANDOM.nextInt(str.length()));
    }

    @Override
    public List<Customer> getCustomerList() {
        return customerMapper.selectList(new QueryWrapper<Customer>()
                .eq("dept_id", SecurityUtils.getHighParentId()));
    }

    /**
     * 过滤收费项目
     * @param itemQuery 查询条件
     * @return 结果
     */
    @Override
    public List<HzItemsVo> selectHzChargeItemsList(ItemsQuery itemQuery) {
        Date date;
        if ("ARR".equals(itemQuery.getType())){
            HzArrTally hzArrTally = hzArrTallyMapper.selectById(itemQuery.getTallyId());
            HzArrRecordOrder recordOrder = recordOrderMapper.selectById(hzArrTally.getRecordOrderId());
            date = recordOrder.getOrderTime();
        }else {
            date = mawbMapper.selectWriteTime(itemQuery.getWaybillCode());
        }
        itemQuery.setEffectiveTime(date);
        return chargeItemsMapper.selectHzChargeItems(itemQuery);
    }

    /**
     * 取消提货办单数据
     * @param pickUpId 办单id
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cancel(Long pickUpId){
        List<PickUpWaybill> pickUpWaybills = pickUpWaybillMapper.selectList(new QueryWrapper<PickUpWaybill>()
                .eq("pick_up_id", pickUpId));
        PickUp pickUp = pickUpMapper.selectById(pickUpId);
        if(pickUp.getIsPay() == 2){
            throw new CustomException("该流水号已作废");
        }
        pickUpWaybills.forEach(e->{
            if(pickUp.getIsPay() == 1){
                //退回费用
                BigDecimal totalCost = pickUp.getTotalCost();
                String waybillCode = e.getWaybillCode();
                Mawb mawb = mawbMapper.selectOne(new QueryWrapper<Mawb>()
                        .eq("waybill_code",waybillCode)
                        .eq("type","ARR")
                        .eq("is_del",0));
                Mawb updateMawb = new Mawb();
                updateMawb.setId(mawb.getId());
                updateMawb.setVersion(mawb.getVersion());
                updateMawb.setDeptId(mawb.getDeptId());
                updateMawb.setStatus("tally_comp");
                updateMawb.setWaybillCode(mawb.getWaybillCode());
                Long deptId = 0L;
                if ("2".equals(pickUp.getPayMethod())){
                    if (pickUp.getDeptId() != null){
                        deptId = pickUp.getDeptId();
                    }else {
                        deptId = mawb.getDeptId();
                    }
                }
                WaybillFee waybillFee = feeMapper.selectOne(new QueryWrapper<WaybillFee>()
                        .eq("waybill_code", mawb.getWaybillCode())
                        .eq("dept_id", mawb.getDeptId())
                        .eq("type","ARR")
                        .last("limit 1"));
                BaseAgent agent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>()
                        .eq("dept_id", deptId));
                if (agent != null){
                    switch (agent.getSettleMethod()){
                        case 0:
                            pickUp.setPayStatus(9);
                            updateStatus(updateMawb, totalCost, waybillFee, 9, totalCost);
                            break;
                        case 1:
                            BigDecimal balance = agent.getBalance() == null ? new BigDecimal(0) : agent.getBalance();
                            agent.setBalance(balance.add(e.getCostSum()));
                            baseAgentMapper.updateBaseAgent(agent);
                            BaseBalance baseBalance = new BaseBalance();
                            baseBalance.setAgentId(agent.getId());
                            baseBalance.setBalance(agent.getBalance());
                            baseBalance.setType("增加余额");
                            baseBalance.setCreateTime(new Date());
                            baseBalance.setCreateBy("系统");
                            // todo 流水号需从银联支付接口获取
                            //baseBalance.setSerialNo();
//                            baseBalance.setTradeMoney(totalCost);
                            baseBalance.setTradeMoney(e.getCostSum());
                            baseBalance.setWaybillCode(mawb.getWaybillCode());
                            baseBalance.setRemark("办单作废");
                            baseBalanceMapper.insertBaseBalance(baseBalance);
                            baseAgentMapper.updateBaseAgent(agent);
                            updateStatus(updateMawb, totalCost, waybillFee, 10, totalCost);
                            pickUp.setPayStatus(10);
                            break;
                        case 2:
                            if (agent.getPayMethod() == 0){
                                pickUp.setPayStatus(11);
                                updateStatus(updateMawb, totalCost, waybillFee, 11, totalCost);
                            }else {
                                pickUp.setPayStatus(12);
                                updateStatus(updateMawb, totalCost, waybillFee, 12, totalCost);
                            }
                            break;
                        default:
                            pickUp.setPayStatus(12);
                            updateStatus(updateMawb, totalCost, waybillFee, 12, totalCost);
                            break;
                    }
                }else {
                  if ("0".equals(pickUp.getPayMethod())){
                      pickUp.setPayStatus(12);
                      updateStatus(updateMawb, totalCost, waybillFee, 12, totalCost);
                  }
                    if ("1".equals(pickUp.getPayMethod())){
                        pickUp.setPayStatus(11);
                        updateStatus(updateMawb, totalCost, waybillFee, 11, totalCost);
                    }
                }
            }
            List<HzArrTally> hzArrTallyList = hzArrTallyMapper.selectList(new QueryWrapper<HzArrTally>()
                    .eq("pick_up_id",pickUpId)
                    .eq("waybill_code", e.getWaybillCode())
                    .in("status","save", "settle", "save_out"));
            hzArrTallyList.forEach(hzArrTally -> {
                hzArrTally.setStatus("lh_comp");
                hzArrTallyMapper.updateById(hzArrTally);
            });
            List<HzArrItem> items = itemMapper.selectList(new QueryWrapper<HzArrItem>()
                    .eq("pick_up_id",pickUpId)
                    .eq("waybill_code", e.getWaybillCode()));
            items.forEach(item->{
                item.setIsDel(1);
                itemMapper.updateById(item);
            });
            e.setIsCancel(1);
            WaybillLog waybillLog = waybillLogService.getWaybillLog(
                    e.getWaybillCode(), 0, SecurityUtils.getNickName(),
                    null, null, null, e.getWaybillCode(), null, 0, null, new Date(),
                    "办单作废,流水号:" + pickUp.getSerialNo(), "ARR", null);
            waybillLogService.insertWaybillLog(waybillLog);
            pickUpWaybillMapper.updatePickUpWaybillById(e);
        });

        List<AllPickUpOut> pickUpOuts = allPickUpOutMapper.selectList(new QueryWrapper<AllPickUpOut>()
                .eq("pick_up_id", pickUpId));
        if(!CollectionUtils.isEmpty(pickUpOuts)){
            for (AllPickUpOut pickUpOut : pickUpOuts) {
                pickUpOut.setIsCancel(1);
                pickUpOut.setUpdateTime(new Date());
                allPickUpOutMapper.updateAllPickUpOutById(pickUpOut);
            }
        }

        pickUp.setIsPay(2);
        pickUp.setUpdateTime(new Date());
        return pickUpMapper.updateById(pickUp);
    }

    private int isCargoCodeMatch(HzChargeIrRelation hzChargeRule, String waybillCargoCode) {
        if (StringUtils.isEmpty(hzChargeRule.getCargoName())) {
            return 1;
        }else if (StringUtils.isEmpty(hzChargeRule.getCategory())){
            if (hzChargeRule.getCargoName().contains(waybillCargoCode)){
                return 1;
            }
            return 0;
        }
        List<IrRelationVo> relationVos = Arrays.stream(hzChargeRule.getCargoName().split(","))
                .map(cargoCodeMapper::selectIrByName)
                .collect(Collectors.toList());

        List<IrRelationVo> cargoNames = Arrays.stream(hzChargeRule.getCategory().split(","))
                .flatMap(category -> cargoCodeMapper.selectListByCategory(Collections.singletonList(category)).stream())
                .collect(Collectors.toList());

        Map<String, List<IrRelationVo>> relationVosMap = relationVos.stream()
                .collect(Collectors.groupingBy(IrRelationVo::getCategoryCode));

        List<IrRelationVo> combinedList = new ArrayList<>();
        Set<String> addedCodes = new HashSet<>();
        for (IrRelationVo vo : cargoNames) {
            String code = vo.getCategoryCode();
            if (relationVosMap.containsKey(code) && !addedCodes.contains(code)) {
                combinedList.addAll(relationVosMap.get(code));
                addedCodes.add(code);
            } else if (!addedCodes.contains(code)) {
                combinedList.add(vo);
            }
        }
        List<String> collect1 = combinedList.stream().map(IrRelationVo::getCode).collect(Collectors.toList());
        if (collect1.stream().anyMatch(cargoCode -> cargoCode.equals(waybillCargoCode))) {
            String category = hzChargeRule.getCategory();
            StringBuilder stringBuffer = new StringBuilder();
            if(category != null){
                String[] split = category.split(",");
                for (String categoryCode:split) {
                    List<BaseCargoCode> cargoCodeList = cargoCodeMapper.selectList(new QueryWrapper<>(new BaseCargoCode())
                            .eq("category_code", categoryCode)
                            .eq("is_del",0));
                    if(cargoCodeList!=null && cargoCodeList.size() > 0){
                        for (BaseCargoCode e:cargoCodeList) {
                            stringBuffer.append(e.getCode());
                        }
                    }
                }
            }else{
                return 1;
            }
            if (stringBuffer.toString().contains(waybillCargoCode)) {
                return 2;
            } else {
                return 1;
            }
        }
        return 0;
    }

    @Override
    public Page<Mawb> listWaybill(WayBillH5Query query) {
        return mawbMapper.listWaybill(new Page<>(query.getPageNum(), query.getPageSize()), query);
    }


    @Override
    public ArrCostDetailVo selectCostDetail(CostDetailQuery query) {
        //设置部门代理人
        QueryWrapper<BaseAgent> deptIdLqw = new QueryWrapper<BaseAgent>().eq("dept_id", query.getDeptId());
        BaseAgent baseAgent = baseAgentMapper.selectOne(deptIdLqw);
        if (baseAgent != null && StringUtils.isNotBlank(baseAgent.getDeptIds())) {
            query.setDeptIdList(Arrays.asList(baseAgent.getDeptIds().split(",")));
        }

        IPage<ArrCostDetailListVo> pageParam = new Page<>(query.getPageNum(), query.getPageSize());
        IPage<ArrCostDetailListVo> page = itemMapper.selectCostDetail(query, pageParam);
        List<ArrCostDetailListVo> list = page.getRecords();
        if (CollectionUtils.isEmpty(list)) {
            return new ArrCostDetailVo();
        }

        ArrCostDetailVo vo = new ArrCostDetailVo();
        vo.setTotal(page.getTotal());
        vo.setCurrent(page.getCurrent());
        vo.setSize(page.getSize());
        vo.setVos(list);

        BigDecimal totalMoney = list.stream().map(ArrCostDetailListVo::getTotalCost).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        vo.setTotalMoney(totalMoney);

        List<ArrCostDetailListVo> collect = list.stream().filter(e -> e.getPayStatus() > 4).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            BigDecimal settleMoney = collect.stream().map(ArrCostDetailListVo::getTotalCost).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setSettleMoney(settleMoney);
        }

        List<ArrCostDetailListVo> collect1 = list.stream().filter(e -> e.getPayStatus() > 0 && e.getPayStatus() <= 4).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect1)) {
            BigDecimal payMoney = collect1.stream().map(ArrCostDetailListVo::getTotalCost).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setPayMoney(payMoney);
        }
        return vo;
    }

    @Override
    public List<ArrCostDetailListVo> selectCostDetailExport(CostDetailQuery query) {
        //设置部门代理人
        QueryWrapper<BaseAgent> deptIdLqw = new QueryWrapper<BaseAgent>().eq("dept_id", query.getDeptId());
        BaseAgent baseAgent = baseAgentMapper.selectOne(deptIdLqw);
        if (baseAgent != null && StringUtils.isNotBlank(baseAgent.getDeptIds())) {
            query.setDeptIdList(Arrays.asList(baseAgent.getDeptIds().split(",")));
        }
        IPage<ArrCostDetailListVo> pageParam = new Page<>(query.getPageNum(), query.getPageSize());
        IPage<ArrCostDetailListVo> arrCostDetailListVoIPage = itemMapper.selectCostDetail(query, pageParam);
        List<ArrCostDetailListVo> records = arrCostDetailListVoIPage.getRecords();
        //处理结算状态字典
        List<SysDictData> data = dictTypeService.selectDictDataByType("bus_pay_status");
        Map<String, String> valueToLabelMap = data.stream()
                .collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
        records.forEach((vo) -> {
            if("AWBM".equals(vo.getWaybillType())){
                vo.setWaybillCode("DN" + "-" + vo.getWaybillCode().substring(7));
            }else {
                vo.setWaybillCode(vo.getWaybillCode().substring(4, 7) + "-" + vo.getWaybillCode().substring(7));
            }
            vo.setPayStatusLabel(valueToLabelMap.getOrDefault(vo.getPayStatus().toString(),""));
        });
        return records;
    }

    private ArrCostDetailVo getCostDetailVo(CostDetailQuery query) {
        //        CostDetailVo vo = new CostDetailVo();
//        List<CostDetailListVo> vos = new ArrayList<>();
////        cost(query.getWaybillCode(), query.getTallyId());bus_pay_status
////        HzArrItemVo vo = mawbMapper.selectByCode(waybillCode);
////        List<HzArrItem> items = itemMapper.selectCostDetail(query);
//        List<CostDetailListVo> items = itemMapper.selectCostDetail(query);
//        if (!CollectionUtils.isEmpty(items)){
//            vo.setVos(items);
//            BigDecimal reduce = items.stream()
//                    .map(hzArrItem -> hzArrItem.getEditCharge() != null ? hzArrItem.getEditCharge() : hzArrItem.getTotalCharge())
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//            vo.setTotalCost(reduce);
//        }
//        List<HzArrTally> hzArrTallyList = hzArrTallyMapper.selectList(new QueryWrapper<HzArrTally>()
//                .eq("waybill_code", waybillCode)
//                .eq("status","lh_comp"));
//        BigDecimal weightTotal = hzArrTallyList.stream().map(HzArrTally::getWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
//        vo.setCanPickUpWeight(weightTotal);
//        int sum = hzArrTallyList.stream().mapToInt(HzArrTally::getPieces).sum();
//        vo.setCanPickUpQuantity(sum);
//        return vo;
        return null;
    }
}
