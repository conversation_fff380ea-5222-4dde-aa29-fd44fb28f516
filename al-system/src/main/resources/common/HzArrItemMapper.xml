<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.arrival.mapper.HzArrItemMapper">

    <select id="selectCostList" resultType="com.gzairports.common.business.arrival.domain.HzArrItem">
        select hawi.*, hci.charge_name, hcr.rule_name, hcr.class_name
        from hz_arr_waybill_item hawi
                 left join hz_charge_ir_relation hcir on hcir.id = hawi.ir_id
                 left join hz_charge_items hci on hci.id = hcir.item_id
                 left join hz_charge_rule hcr on hcr.id = hcir.rule_id
        where hawi.is_del = 0 and hawi.waybill_code = #{waybillCode} and hawi.tally_id = #{tallyId}
    </select>

    <select id="selectCostListByCodeAndTallyList"
            resultType="com.gzairports.common.business.arrival.domain.HzArrItem">
        select hawi.*, hci.charge_name, hcr.rule_name, hcr.class_name
        from hz_arr_waybill_item hawi
                 left join hz_charge_ir_relation hcir on hcir.id = hawi.ir_id
                 left join hz_charge_items hci on hci.id = hcir.item_id
                 left join hz_charge_rule hcr on hcr.id = hcir.rule_id
        where hawi.is_del = 0 and hawi.service_type = 0 and hawi.waybill_code = #{waybillCode} and hawi.tally_id in
            <foreach collection="tallyIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
    </select>

    <select id="selectCostInfo" resultType="com.gzairports.common.business.arrival.domain.HzArrItem">
        select hawi.id, hawi.total_charge, hawi.tally_id, hawi.ir_id, hci.charge_name, hcr.rule_name, hcr.class_name, hawi.remark, hawi.unit,
               hawi.small_item, hawi.large_item, hawi.super_large_item, hawi.start_time, hawi.end_time, hawi.days_in_storage, hawi.edit_charge,
               hawi.cold_store, hawi.store_start_time, hawi.store_end_time
        from hz_arr_waybill_item hawi
                 left join hz_charge_ir_relation hcir on hcir.id = hawi.ir_id
                 left join hz_charge_items hci on hci.id = hcir.item_id
                 left join hz_charge_rule hcr on hcr.id = hcir.rule_id
        where hawi.id = #{id} and hawi.is_del = 0
    </select>
    <!--    <select id="selectItemVo" resultType="com.gzairports.common.business.arrival.domain.vo.ArrItemVo">-->
    <!--        select hawi.total_charge, hawi.tally_id, hci.charge_abb, hawi.edit_charge-->
    <!--        from hz_arr_waybill_item hawi-->
    <!--                 left join hz_charge_ir_relation hcir on hcir.id = hawi.ir_id-->
    <!--                 left join hz_charge_items hci on hci.id = hcir.item_id-->
    <!--        where hawi.tally_id = #{tallyId} and hawi.is_del = 0-->
    <!--    </select>-->
    <select id="selectItemVo" resultType="com.gzairports.common.business.arrival.domain.vo.ArrItemVo">
        select hawi.total_charge, hawi.tally_id, hci.charge_abb, hawi.edit_charge
        from hz_arr_waybill_item hawi
                 left join hz_charge_ir_relation hcir on hcir.id = hawi.ir_id
                 left join hz_charge_items hci on hci.id = hcir.item_id
        where  hawi.is_del = 0 and hawi.waybill_code = #{waybillCode} and hawi.pick_up_id =
                                   (select pick_up_id from hz_arr_tally where id = #{tallyId} )
    </select>
    <select id="selectPayOrSettleList" resultType="com.gzairports.common.business.arrival.domain.HzArrItem">
        select hawi.total_charge, hawi.edit_charge, hawi.remark,
                hci.charge_name, hci.charge_abb, hcr.rule_name
        from hz_arr_waybill_item hawi
        left join hz_charge_ir_relation hcir on hcir.id = hawi.ir_id
        left join hz_charge_items hci on hci.id = hcir.item_id
        left join hz_charge_rule hcr  on hcr.id = hcir.rule_id
        where hawi.waybill_code = #{waybillCode} and hawi.is_del = 0
    </select>
    <select id="selectCostListByPickUpId" resultType="com.gzairports.common.business.arrival.domain.HzArrItem">
        select hawi.*, hci.charge_name, hcr.rule_name, hcr.class_name
        from hz_arr_waybill_item hawi
                 left join hz_charge_ir_relation hcir on hcir.id = hawi.ir_id
                 left join hz_charge_items hci on hci.id = hcir.item_id
                 left join hz_charge_rule hcr on hcr.id = hcir.rule_id
        where hawi.is_del = 0 and hawi.waybill_code = #{waybillCode} and hawi.pick_up_id = #{pickUpId}
    </select>
    <select id="selectReportList" resultType="com.gzairports.common.business.reporter.domain.ReportArrCost">
        select aaw.id as reportId, hawi.waybill_code, aipu.serial_no, hci.charge_name as rateName, hat.pieces as arrQuantity,
               hawi.total_charge, aipu.pick_up_time,hawi.is_settle, hawi.service_type
        from hz_arr_waybill_item hawi
            left join hz_charge_ir_relation hcir on hcir.id = hawi.ir_id
            left join hz_charge_items hci on hci.id = hcir.item_id
            left join hz_arr_tally hat on hat.id = hawi.tally_id
            left join all_in_pick_up aipu on hawi.pick_up_id = aipu.id
            left join all_air_waybill aaw on aaw.waybill_code = hawi.waybill_code
        where aaw.type = 'ARR' and hawi.is_del = 0 and hawi.waybill_code in
        <foreach collection="waybillCodes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>
    <select id="selectFeeRate" resultType="java.math.BigDecimal">
        select hawi.fee_rate from hz_arr_waybill_item hawi
              left join hz_charge_ir_relation hcir on hcir.id = hawi.ir_id
              left join hz_charge_items hci on hci.id = hcir.item_id
        where hci.charge_abb = '处置费'  and waybill_code = #{waybillCode} limit 1
    </select>
    <select id="selectItemList" resultType="com.gzairports.common.business.arrival.domain.HzArrItem">
        select * from hz_arr_waybill_item where pay_method is not null and waybill_code = #{waybillCode} and service_id = #{id} and service_type = #{type}
    </select>
    <select id="selectItemByTallyId" resultType="com.gzairports.common.business.arrival.domain.HzArrItem">
        select *
        from hz_arr_waybill_item
        where is_del = 0 and service_type = 1 and waybill_code = #{waybillCode} and pick_up_id is null
    </select>
    <select id="selectCostDetail"
            resultType="com.gzairports.common.business.arrival.domain.vo.ArrCostDetailListVo">

        select aipu.id,
        aipu.serial_no,
        aipu.total_count,
        aipu.total_cost,
        aipu.customer_name,
        aipu.customer_id_no,
        aipu.handle_by,
        aipu.pick_up_time,
        aaw.waybill_code,
        aipu.is_pay,
        aipu.pay_status,
        aaw.flight_no1 as flight_no,
        aaw.flight_date1 as flight_date,
        CONCAT(aaw.flight_no1, '/', DATE_FORMAT(aaw.flight_date1, '%Y-%m-%d')) as flightNoAndDate,
        aaw.waybill_code,
        aaw.waybill_type,
        aaw.consign as consignee,
        aaw.quantity,
        aaw.weight,
        aw.can_pick_up_weight as waybillWeight,
        aw.can_pick_up_quantity as waybillQuantity,
        aw.cost_sum as waybillCost,
        aipu.out_time
        from all_in_pick_up aipu
        left join all_pick_up_waybill aw on aipu.id = aw.pick_up_id
        left join all_air_waybill aaw on aaw.waybill_code = aw.waybill_code

        <where>
            aaw.is_del = 0
            <if test="query.type != null and query.type != ''">
                and aaw.type = #{query.type}
            </if>
            <if test="query.waybillCode != null and query.waybillCode != ''">
                and aaw.waybill_code like #{query.waybillCode}
            </if>
            <if test="query.serialNo != null and query.serialNo != ''">
                and aipu.serial_no like '%${query.serialNo}%'
            </if>
            <if test="query.consign != null and query.consign != ''">
                and aaw.consign = #{query.consign}
             </if>
            <if test="query.settleType != null">
                and aaw.pay_status = #{query.settleType}
            </if>
            <if test="query.startTime != null and query.endTime != null">
                and aipu.out_time between #{query.startTime} and #{query.endTime}
            </if>
            <if test="query.handleBy != null and query.handleBy != ''">
                and aipu.handle_by = #{query.handleBy}
            </if>
            <if test="query.deptIdList != null and query.deptIdList.size() > 0">
                and aipu.dept_id in
                <foreach item="deptId" index="index" collection="query.deptIdList" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectAllItemVos" resultType="com.gzairports.common.business.arrival.domain.vo.ArrItemVo">
        select hawi.total_charge, hawi.pick_up_id, hci.charge_abb, hawi.edit_charge, hawi.waybill_code
        from hz_arr_waybill_item hawi
        left join hz_charge_ir_relation hcir on hcir.id = hawi.ir_id
        left join hz_charge_items hci on hci.id = hcir.item_id
        where hawi.is_del = 0 and (hawi.pick_up_id, hawi.waybill_code) in
        <foreach collection="keys" item="key" open="(" separator="," close=")">
            (#{key.pickUpId}, #{key.waybillCode})
        </foreach>
    </select>
    <select id="selectAllItemVosNew" resultType="com.gzairports.common.business.arrival.domain.vo.ArrItemVo">
        select hawi.total_charge, hawi.pick_up_id, hci.charge_abb, hawi.edit_charge, hawi.waybill_code
        from hz_arr_waybill_item hawi
        left join hz_charge_ir_relation hcir on hcir.id = hawi.ir_id
        left join hz_charge_items hci on hci.id = hcir.item_id
        where hawi.is_del = 0
        <if test="payMethod == 2">
            and (hawi.pay_method = 2 or hawi.pay_method IS NULL)
        </if>
        <if test="payMethod == 1">
            and (hawi.pay_method = 1 or hawi.pay_method = 0)
        </if>
          and hawi.waybill_code in
        <foreach collection="keys" item="key" open="(" separator="," close=")">
            (#{key})
        </foreach>
    </select>
    <select id="selectBatch" resultType="com.gzairports.common.business.arrival.domain.vo.ArrItemVo">
        select *
        from arr_item
        where (pick_up_id, waybill_code) in
        <foreach collection="keys" item="key" open="(" separator="," close=")">
            (#{key.pickUpId}, #{key.waybillCode})
        </foreach>
    </select>
    <select id="selectListByKey" resultType="com.gzairports.common.business.arrival.domain.HzArrItem">
        select waybill_code, tally_id, edit_charge, total_charge from hz_arr_waybill_item
        where is_del = 0 and (tally_id, waybill_code) in
        <foreach collection="keys" item="key" open="(" separator="," close=")">
            (#{key.pickUpId}, #{key.waybillCode})
        </foreach>
    </select>
    <select id="selectItemNotPayByWaybillCode"
            resultType="com.gzairports.common.business.arrival.domain.HzArrItem">
        select hci.charge_name, hcr.rule_name, hcr.class_name, hci.id as chargeItemsId,
               hawi.id,hawi.days_in_storage,hawi.end_time,hawi.start_time,hawi.ir_id,hawi.large_item,hawi.order_id,hawi.point_time,
               hawi.small_item,hawi.store_start_time,hawi.store_end_time,hawi.super_large_item,hawi.tally_id,hawi.unit,hawi.waybill_code,
               hawi.is_lock,hawi.is_pay,hawi.is_add,hawi.total_charge,hawi.edit_charge
        from hz_arr_waybill_item hawi
                 left join hz_charge_ir_relation hcir on hcir.id = hawi.ir_id
                 left join hz_charge_items hci on hci.id = hcir.item_id
                 left join hz_charge_rule hcr on hcr.id = hcir.rule_id
        where hawi.is_pay = 0 and hawi.is_lock = 1 and hawi.waybill_code = #{waybillCode}
    </select>
    <select id="selectListByQuery"
            resultType="com.gzairports.hz.business.departure.domain.vo.ChargeBillArrSettleExportVO">
        select aipu.serial_no, aipu.settle_user, aipu.consign, aaw.waybill_code,aw.can_pick_up_weight as weight, aaw.charge_weight,hci.charge_abb,
               hawi.total_charge as subtotal,aw.create_time as settleTime,aaw.status, hawi.id,aaw.dept_id, ba.agent, aw.can_pick_up_quantity as quantity,
               aaw.cargo_name
        from hz_arr_waybill_item hawi
            left join hz_charge_ir_relation hcir on hcir.id = hawi.ir_id
            left join hz_charge_items hci on hci.id = hcir.item_id
            left join all_air_waybill aaw on aaw.waybill_code = hawi.waybill_code
            left join base_agent ba on ba.dept_id = aaw.dept_id
            left join all_pick_up_waybill aw on hawi.waybill_code = aw.waybill_code
            left join all_in_pick_up aipu on aipu.id = aw.pick_up_id
        where hawi.is_del = 0 and hawi.total_charge > 0 and aw.is_cancel = 0
        <if test="startTime != null">
            and aw.create_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="startTime != null">
            and aw.create_time <![CDATA[<=]]> #{endTime}
        </if>
        <if test="deptIds != null and deptIds.size() > 0">
            and aaw.dept_id in
            <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
    </select>
</mapper>