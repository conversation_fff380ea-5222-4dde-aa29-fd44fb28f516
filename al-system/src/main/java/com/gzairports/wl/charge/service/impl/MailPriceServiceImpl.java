package com.gzairports.wl.charge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.BaseAirportCode;
import com.gzairports.common.basedata.domain.BaseBillType;
import com.gzairports.common.basedata.domain.BaseCargoCategory;
import com.gzairports.common.basedata.domain.BaseCargoCode;
import com.gzairports.common.basedata.mapper.AirportCodeMapper;
import com.gzairports.common.basedata.mapper.BillTypeMapper;
import com.gzairports.common.basedata.mapper.CargoCategoryMapper;
import com.gzairports.common.basedata.mapper.CargoCodeMapper;
import com.gzairports.common.exception.ServiceException;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.bean.BeanValidators;
import com.gzairports.wl.charge.domain.*;
import com.gzairports.wl.charge.domain.query.FreightRateAirQuery;
import com.gzairports.wl.charge.domain.vo.FreightRateAirVO;
import com.gzairports.wl.charge.domain.vo.MailPriceItemVo;
import com.gzairports.wl.charge.domain.vo.MailPriceVo;
import com.gzairports.wl.charge.mapper.MailPriceItemMapper;
import com.gzairports.wl.charge.mapper.MailPriceMapper;
import com.gzairports.wl.charge.service.IMailPriceService;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.Validator;
import java.util.*;

/**
 * 邮件运价service业务层处理
 *
 * <AUTHOR>
 * @date 2024-035-30
 */
@Service
public class MailPriceServiceImpl extends ServiceImpl<MailPriceMapper, MailPrice> implements IMailPriceService {

    @Autowired
    private MailPriceMapper mailPriceMapper;

    @Autowired
    private MailPriceItemMapper itemMapper;

    @Autowired
    private BillTypeMapper billTypeMapper;

    @Autowired
    private Validator validator;

    @Autowired
    private CargoCategoryMapper cargoCategoryMapper;

    @Autowired
    private CargoCodeMapper cargoCodeMapper;

    @Autowired
    private AirportCodeMapper airportCodeMapper;


    /**
     * 查询邮件运价列表
     *
     * @param query 查询参数
     * @return 邮件运价列表
     */
    @Override
    public List<MailPriceVo> selectList(FreightRateAirQuery query) {
        //查询票证类型并存入集合,仅查询一次
        if (DOCUMENTMAP.isEmpty()) {
            selectTypeList();
        }
        query.setDeptId(SecurityUtils.getHighParentId());
        List<MailPriceVo> mailPriceVos = mailPriceMapper.selectListByQuery(query);
        for (MailPriceVo mailPriceVo : mailPriceVos) {
            mailPriceVo.setApplicable(DOCUMENTMAP.get(mailPriceVo.getApplicableDocuments()));
            mailPriceVo.setAutomatic(mailPriceVo.getAutomaticFromLow() == 0 ? "否":"是");
            mailPriceVo.setType(mailPriceVo.getRateType() == 0 ? "普通运价":"特货运价");
            mailPriceVo.setRounding(ROUNDRULE.get(mailPriceVo.getRoundingMethod()));
        }
        return mailPriceVos;
    }

    /**
     * 查询邮件运价想
     *
     * @param id 邮件运价主键
     * @return 邮件运价
     */
    @Override
    public MailPriceVo selectRateAirById(Long id) {
        MailPriceVo vo = new MailPriceVo();
        MailPrice mailPrice = mailPriceMapper.selectById(id);
        BeanUtils.copyProperties(mailPrice,vo);
        return vo;
    }

    /**
     * 新增邮件运价
     *
     * @param price 邮件运价
     * @return 结果
     */
    @Override
    public Long insertFreightRateAir(MailPrice price) {
        List<MailPrice> list = mailPriceMapper.selectList(new QueryWrapper<MailPrice>()
                .eq("carrier_code", price.getCarrierCode())
                .eq("rate_type", price.getRateType())
                .eq("dept_id", SecurityUtils.getHighParentId())
                .eq("is_del",0));
        for (MailPrice freightRateCustom : list) {
//            if (freightRateCustom.getEffectiveDate().before(price.getEffectiveDate()) || freightRateCustom.getExpirationDate().after(price.getExpirationDate()))
            if((freightRateCustom.getEffectiveDate().before(price.getEffectiveDate()) &&
                    freightRateCustom.getExpirationDate().after(price.getEffectiveDate()))
                    || (freightRateCustom.getEffectiveDate().before(price.getExpirationDate()) &&
                    freightRateCustom.getExpirationDate().after(price.getExpirationDate())))
            {
                throw new CustomException("相同客户的运价生效和截止日期不能重叠");
            }
        }
        if (price.getEffectiveDate().after(price.getExpirationDate())){
            throw new CustomException("生效时间不能晚于截止时间");
        }
        price.setCreateBy(SecurityUtils.getUsername());
        price.setCreateTime(DateUtils.getNowDate());
        price.setDeptId(SecurityUtils.getHighParentId());
        mailPriceMapper.insert(price);
        return price.getId();
    }

    /**
     * 修改邮件运价
     *
     * @param price 邮件运价
     * @return 结果
     */
    @Override
    public int updateFreightRateAir(MailPrice price) {
        price.setUpdateBy(SecurityUtils.getUsername());
        price.setUpdateTime(new Date());
        return mailPriceMapper.updateById(price);
    }

    /**
     * 查看运价条目
     *
     * @param id 邮件运价id
     * @return 邮件运价条目集合
     */
    @Override
    public List<MailPriceItemVo> selectRateItemList(Long id) {
        return itemMapper.selectRateItemList(id);
    }

    /**
     * 删除邮件运价
     *
     * @param id 邮件运价主键
     * @return 结果
     */
    @Override
    public int deleteFreightRateAir(Long id) {
        MailPrice mailPrice = mailPriceMapper.selectById(id);
        Date date = new Date();
        if (mailPrice.getEffectiveDate().before(date) && mailPrice.getExpirationDate().after(date)){
            throw new CustomException("在有效期内的运价不能删除");
        }
        return mailPriceMapper.removeRateAir(id);
    }

    /**
     * 导入邮件运价
     * */
    @Override
    public String importMailPrice(List<MailPriceVo> mailPriceVos, boolean updateSupport) {
        //查询票证类型并存入集合,仅查询一次
        if (DOCUMENTMAP.isEmpty()) {
            selectTypeList();
        }
        if (StringUtils.isNull(mailPriceVos) || mailPriceVos.size() == 0)
        {
            throw new ServiceException("导入邮件运价数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        MailPrice mailPrice = new MailPrice();
        for (MailPriceVo mailPriceVo : mailPriceVos) {
            try {
                BeanUtils.copyProperties(mailPriceVo,mailPrice);
                mailPrice.setRateType(mailPriceVo.getType().equals("普通运价") ? 0 : 1);
                mailPrice.setAutomaticFromLow(mailPriceVo.getAutomatic().equals("否") ? 0 : 1);
                //传进来的是显示的值 要存的是数据库的值(map中通过值找键)
                mailPrice.setApplicableDocuments(getKey(DOCUMENTMAP, mailPriceVo.getApplicable()));
                mailPrice.setRoundingMethod(getKey(ROUNDRULE, mailPriceVo.getRounding()));
                // 验证是否存在相同的邮件运价
                MailPrice mailPriceNew = mailPriceMapper.selectOne(new QueryWrapper<MailPrice>()
                        .eq("carrier_code", mailPrice.getCarrierCode())
                        .eq("applicable_documents", mailPrice.getApplicableDocuments())
                        .eq("effective_date", mailPrice.getEffectiveDate())
                        .eq("dept_id", SecurityUtils.getHighParentId())
                        .eq("is_del",0));

                if (StringUtils.isNull(mailPriceNew)) {
                    BeanValidators.validateWithException(validator, mailPrice);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    mailPrice.setCreateBy(loginUser.getUsername());
                    mailPrice.setCreateTime(DateUtils.getNowDate());
                    mailPriceMapper.insert(mailPrice);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、代码 " + mailPrice.getRateName() + " 导入成功");
                }
                else if (updateSupport) {
                    BeanValidators.validateWithException(validator, mailPrice);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    mailPrice.setId(mailPriceVo.getId());
                    mailPrice.setUpdateBy(loginUser.getUsername());
                    mailPrice.setUpdateTime(DateUtils.getNowDate());
                    mailPriceMapper.updateById(mailPrice);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、代码 " + mailPrice.getRateName() + " 更新成功");
                }
                else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、代码 " + mailPrice.getRateName() + " 已存在");
                }
            }catch (Exception e){
                failureNum++;
                String msg = "<br/>" + failureNum + "、代码 " + mailPrice.getRateName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 导入航司运价条目
     * @param list 文件数据
     * @param rateId 导入所需数据
     * @param updateSupport 导入所需数据
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long importItem(List<MailPriceItem> list, Long rateId, boolean updateSupport) {
        if (StringUtils.isNull(list) || list.size() == 0)
        {
            throw new ServiceException("导入航司运价条目数据不能为空！");
        }
        for (int i = 0; i < list.size(); i++) {
            int j = i + 1;
            if (list.get(i).getDepartureCity() == null){
                throw new ServiceException("第" + j + "条数据导入失败，始发城市为空");
            }
            if (list.get(i).getDestinationCity() == null){
                throw new ServiceException("第" + j + "条数据导入失败，目的城市为空");
            }
            if (list.get(i).getMinimumFreight() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算M为空");
            }
            if (list.get(i).getFaceMinimumFreight() == null){
                throw new ServiceException("第" + j + "条数据导入失败，票面M为空");
            }
            if (list.get(i).getReceivableMinimumFreight() == null){
                throw new ServiceException("第" + j + "条数据导入失败，应收M为空");
            }
            if (list.get(i).getRateWeightRange5() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算5为空");
            }
            if (list.get(i).getRateWeightRange10() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算10为空");
            }
            if (list.get(i).getBaseRateN() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算N为空");
            }
            if (list.get(i).getRateWeightRange45() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算45为空");
            }
            if (list.get(i).getRateWeightRange100() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算100为空");
            }
            if (list.get(i).getRateWeightRange300() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算300为空");
            }
            if (list.get(i).getRateWeightRange500() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算500为空");
            }
            if (list.get(i).getRateWeightRange1000() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算1000为空");
            }
            if (list.get(i).getFaceWeightRange5() == null){
                throw new ServiceException("第" + j + "条数据导入失败，票面5为空");
            }
            if (list.get(i).getFaceWeightRange10() == null){
                throw new ServiceException("第" + j + "条数据导入失败，票面10为空");
            }
            if (list.get(i).getFaceWeightRangeN() == null){
                throw new ServiceException("第" + j + "条数据导入失败，票面N为空");
            }
            if (list.get(i).getFaceWeightRange45() == null){
                throw new ServiceException("第" + j + "条数据导入失败，票面45为空");
            }
            if (list.get(i).getFaceWeightRange100() == null){
                throw new ServiceException("第" + j + "条数据导入失败，票面100为空");
            }
            if (list.get(i).getFaceWeightRange300() == null){
                throw new ServiceException("第" + j + "条数据导入失败，票面300为空");
            }
            if (list.get(i).getFaceWeightRange500() == null){
                throw new ServiceException("第" + j + "条数据导入失败，票面500为空");
            }
            if (list.get(i).getFaceWeightRange1000() == null){
                throw new ServiceException("第" + j + "条数据导入失败，票面1000为空");
            }
            if (list.get(i).getReceivableWeightRange5() == null){
                throw new ServiceException("第" + j + "条数据导入失败，应收5为空");
            }
            if (list.get(i).getReceivableWeightRange10() == null){
                throw new ServiceException("第" + j + "条数据导入失败，应收10为空");
            }
            if (list.get(i).getBaseReceivableN() == null){
                throw new ServiceException("第" + j + "条数据导入失败，应收N为空");
            }
            if (list.get(i).getReceivableWeightRange45() == null){
                throw new ServiceException("第" + j + "条数据导入失败，应收45为空");
            }
            if (list.get(i).getReceivableWeightRange100() == null){
                throw new ServiceException("第" + j + "条数据导入失败，应收100为空");
            }
            if (list.get(i).getReceivableWeightRange300() == null){
                throw new ServiceException("第" + j + "条数据导入失败，应收300为空");
            }
            if (list.get(i).getReceivableWeightRange500() == null){
                throw new ServiceException("第" + j + "条数据导入失败，应收500为空");
            }
            if (list.get(i).getReceivableWeightRange1000() == null){
                throw new ServiceException("第" + j + "条数据导入失败，应收1000为空");
            }
//            List<BaseAirportCode> desPort = airportCodeMapper.selectCityByName(list.get(i).getDestinationCity());
            BaseAirportCode desPort = airportCodeMapper.selectOne(new QueryWrapper<BaseAirportCode>()
                    .eq("chinese_name", list.get(i).getDestinationCity())
                    .eq("is_del",0));
            if (desPort == null){
                throw new ServiceException("第" + j + "条数据导入失败、目的城市 " + list.get(i).getDestinationCity() + " 基础数据机场代码里未维护");
            }
//            for (BaseAirportCode baseAirportCode : desPort) {
                MailPriceItem mailPriceItem = itemMapper.selectOne(new QueryWrapper<MailPriceItem>()
                        .eq("clause_name", list.get(i).getClauseName())
                        .eq("rate_id",rateId));
                if (mailPriceItem != null){
                    throw new ServiceException("第" + j + "条数据导入失败、条目名称 " + list.get(i).getClauseName() + " 已存在");
                }
                MailPriceItem priceItem = new MailPriceItem();
                BeanUtils.copyProperties(list.get(i),priceItem);
                LoginUser loginUser = SecurityUtils.getLoginUser();
                priceItem.setRateId(rateId);
                priceItem.setDepartureCity("KWE");
                priceItem.setDestinationCity(desPort.getCode());
                priceItem.setCreateBy(loginUser.getUsername());
                priceItem.setCreateTime(DateUtils.getNowDate());
                itemMapper.insert(priceItem);
        }
//    }
        return rateId;
    }


    /**
     * 查询所有票证类型
     *
     * @return 结果
     */
    public void selectTypeList() {
        List<BaseBillType> baseBillTypes = billTypeMapper.selectTypeList();
        for (BaseBillType baseBillType:baseBillTypes) {
            String code = baseBillType.getCode();
            String name = baseBillType.getName();
            DOCUMENTMAP.put(code,name);
        }
    }
    private static final Map<String,String> DOCUMENTMAP = new HashMap<>();
 /*   static {
        DOCUMENTMAP.put("guonei_fendan","国内分单");
        DOCUMENTMAP.put("guoji_fendan","国际分单");
        DOCUMENTMAP.put("guonei_zhudan","国内主单");
        DOCUMENTMAP.put("guoji_zhudan","国际主单");
        DOCUMENTMAP.put("youjiandan","邮件单");
    }*/

    private static final Map<Integer,String> ROUNDRULE = new HashMap<>();
    static {
        ROUNDRULE.put(0,"实际");
        ROUNDRULE.put(1,"四舍五入至个位");
        ROUNDRULE.put(2,"四舍五入至0.1");
        ROUNDRULE.put(3,"四舍五入至0.01");
        ROUNDRULE.put(4,"四舍五入至0.001");
        ROUNDRULE.put(5,"直接进位到0.1");
        ROUNDRULE.put(6,"直接舍位到0.1");
        ROUNDRULE.put(7,"直接进位到个位");
        ROUNDRULE.put(8,"直接舍位到个位");
        ROUNDRULE.put(9,"直接进位到0.5");
        ROUNDRULE.put(10,"直接舍位到0.5");
    }

    /**
     * 通过值找键
     */
    public <K, V> K getKey(Map<K, V> map, V value) {
        for (Map.Entry<K, V> entry : map.entrySet()) {
            if (entry.getValue().equals(value)) {
                return entry.getKey();
            }
        }
        return null;
    }
}
