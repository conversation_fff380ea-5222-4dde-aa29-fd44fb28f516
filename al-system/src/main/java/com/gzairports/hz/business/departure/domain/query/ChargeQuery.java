package com.gzairports.hz.business.departure.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 收费管理查询参数
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
public class ChargeQuery {

    /** 配载航班时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date flightLoadStartTime;

    /** 配载航班时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date flightLoadEndTime;

    /** 入库时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 入库时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 结算时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTimeSettle;

    /** 结算时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTimeSettle;

    /** 制单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date writeStartTime;

    /** 制单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date writeEndTime;

    /** 运单号 */
    private String waybillCode;

    /** 发货代理人 */
    private List<String> agentCode;

    /** 运单状态 */
    private String status;

    /** 特货代码1 */
    private String specialCargoCode1;

    /** 代理人 */
    private String agent;

    /** 流水号 */
    private String serialNo;

    /** 支付状态 */
    private List<Integer> payStatus;

    private Integer pageNum = 1;

    private Integer pageSize = 10;
}
