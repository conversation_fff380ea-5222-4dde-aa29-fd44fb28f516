package com.gzairports.common.business.reporter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.reporter.domain.ReportPullDown;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ReportPullDownMapper extends BaseMapper<ReportPullDown> {
    void deleteBatch(@Param("reportIds") List<Long> reportIds);

    int insertBatchSomeColumn(List<ReportPullDown> pullDowns);
}
