package com.gzairports.hz.business.departure.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.*;
import com.gzairports.common.basedata.mapper.*;
import com.gzairports.common.business.arrival.domain.FlightLoadUldWaybill;
import com.gzairports.common.business.arrival.domain.FlightLoadWaybill;
import com.gzairports.common.business.arrival.mapper.FlightInfoMapper;
import com.gzairports.common.business.arrival.mapper.FlightLoadUldWaybillMapper;
import com.gzairports.common.business.arrival.mapper.FlightLoadWaybillMapper;
import com.gzairports.common.business.departure.domain.*;
import com.gzairports.common.business.departure.mapper.*;
import com.gzairports.common.business.departure.service.impl.WaybillTraceServiceImpl;
import com.gzairports.common.business.wrong.domain.Wrong;
import com.gzairports.common.business.wrong.mapper.WrongMapper;
import com.gzairports.common.charge.domain.HzChargeIrRelation;
import com.gzairports.common.charge.domain.HzChargeItemRule;
import com.gzairports.common.charge.domain.HzChargeItems;
import com.gzairports.common.charge.domain.HzChargeRule;
import com.gzairports.common.charge.domain.vo.BillRuleVo;
import com.gzairports.common.charge.domain.vo.IrRelationVo;
import com.gzairports.common.charge.mapper.HzChargeIrRelationMapper;
import com.gzairports.common.charge.mapper.HzChargeItemRuleMapper;
import com.gzairports.common.charge.mapper.HzChargeItemsMapper;
import com.gzairports.common.charge.mapper.HzChargeRuleMapper;
import com.gzairports.common.charge.service.BillingRule;
import com.gzairports.common.charge.service.impl.BillingRuleFactory;
import com.gzairports.common.core.domain.PageQuery;
import com.gzairports.common.core.domain.entity.SysUser;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.common.log.service.IWaybillLogService;
import com.gzairports.common.message.domain.Message;
import com.gzairports.common.message.domain.vo.SocketMessageVo;
import com.gzairports.common.message.mapper.MessageMapper;
import com.gzairports.common.rabbitmq.SecurityProducer;
import com.gzairports.common.securitySubmit.domain.AllSecurityWaybill;
import com.gzairports.common.securitySubmit.domain.SecuritySubmitSendSecond;
import com.gzairports.common.securitySubmit.domain.WaybillCollectData;
import com.gzairports.common.securitySubmit.mapper.AllSecurityUrlMapper;
import com.gzairports.common.securitySubmit.mapper.AllSecurityWaybillMapper;
import com.gzairports.common.serviceRequest.domain.ServiceRequest;
import com.gzairports.common.serviceRequest.mapper.ServiceRequestMapper;
import com.gzairports.common.system.domain.SysConfig;
import com.gzairports.common.system.mapper.SysConfigMapper;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.system.mapper.SysUserMapper;
import com.gzairports.common.utils.*;
import com.gzairports.hz.business.cable.domain.HzCable;
import com.gzairports.hz.business.cable.domain.HzCableAddress;
import com.gzairports.hz.business.cable.domain.vo.*;
import com.gzairports.hz.business.cable.mapper.HzCableAddressMapper;
import com.gzairports.hz.business.cable.mapper.HzCableMapper;
import com.gzairports.hz.business.cable.service.impl.HttpServiceImpl;
import com.gzairports.hz.business.departure.domain.*;
import com.gzairports.hz.business.departure.domain.query.*;
import com.gzairports.hz.business.departure.domain.vo.*;
import com.gzairports.hz.business.departure.mapper.*;
import com.gzairports.hz.business.departure.rabbitmq.WaybillMessageProducer;
import com.gzairports.hz.business.departure.service.IHzCollectWaybillService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Marker;
import org.slf4j.MarkerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 运单收运Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Service
@Slf4j
public class HzCollectWaybillServiceImpl extends ServiceImpl<HzCollectWaybillMapper, HzCollectWaybill> implements IHzCollectWaybillService {

    @Autowired
    private HzCollectWaybillMapper collectWaybillMapper;

    @Autowired
    private TransferWaybillMapper transferWaybillMapper;

    @Autowired
    private HzCollectWeightMapper collectWeightMapper;

    @Autowired
    private MessageMapper messageMapper;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    @Autowired
    private HzDisBoardMapper disBoardMapper;

    @Autowired
    private AllAirWaybillMapper waybillMapper;

    @Autowired
    private TransferMapper transferMapper;

    @Autowired
    private SpecialTraceServiceImpl specialTraceService;

    @Autowired
    private WaybillTraceServiceImpl waybillTraceService;

    @Autowired
    private HzColdRegisterServiceImpl registerService;

    @Autowired
    private ServiceRequestMapper serviceRequestMapper;

    @Autowired
    private SpecialCodeMapper specialCodeMapper;

    @Autowired
    private CargoCodeMapper cargoCodeMapper;

    @Autowired
    private FlightLoadUldWaybillMapper loadUldWaybillMapper;

    @Autowired
    private FlightLoadUldMapper loadUldMapper;

    @Autowired
    private FlightLoadWaybillMapper loadWaybillMapper;

    @Autowired
    private HzDepGroupWaybillMapper groupWaybillMapper;

    @Autowired
    private HzDepGroupUldWaybillMapper groupUldWaybillMapper;

    @Autowired
    private HzDepGroupUldMapper groupUldMapper;

    @Autowired
    private UldMapper uldMapper;

    @Autowired
    private TruckMapper truckMapper;

    @Autowired
    private HzChargeItemsMapper chargeItemsMapper;

    @Autowired
    private SysConfigMapper sysConfigMapper;

    @Autowired
    private PullDownMapper pullDownMapper;

    @Autowired
    private RepeatWeightMapper repeatWeightMapper;

    @Autowired
    private IWaybillLogService waybillLogService;

    @Autowired
    private WaybillMessageProducer waybillMessageProducer;

    @Autowired
    private FlightInfoMapper flightInfoMapper;

    @Autowired
    private RedisTemplate<String,String> redisTemplate;

    @Autowired
    private SecurityProducer securityProducer;

    @Autowired
    private AllSecurityUrlMapper allSecurityUrlMapper;

    @Autowired
    private AllSecurityWaybillMapper allSecurityWaybillMapper;

    @Autowired
    private HzChargeIrRelationMapper relationMapper;

    @Autowired
    private HzChargeItemRuleMapper itemRuleMapper;

    @Autowired
    private HzChargeRuleMapper ruleMapper;

    @Autowired
    private CostDetailMapper costDetailMapper;

    @Autowired
    private WaybillFeeMapper feeMapper;

    @Autowired
    private BaseAgentMapper baseAgentMapper;

    @Autowired
    private BaseBalanceMapper baseBalanceMapper;

    @Autowired
    private ExitCargoMapper exitCargoMapper;

    @Autowired
    private WrongMapper wrongMapper;

    @Autowired
    private HzCableMapper hzCableMapper;

    @Autowired
    private HzCableAddressMapper cableAddressMapper;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private HttpServiceImpl httpService;

    @Autowired
    private SysUserMapper userMapper;

    @Value("${hzCable.account}")
    private String account;

    @Value("${hzCable.loginUrl}")
    private String loginUrl;

    @Value("${hzCable.getMsg}")
    private String getMsg;

    @Value("${hzCable.FTPAddress}")
    private String ftpAddress;

    private static final Marker CABLE_ERROR_MARKER  = MarkerFactory.getMarker("CABLE-ERROR");

    /**
     * 宽体机维护列表
     */
    private static final String WIDE_BODY = "airc-master:wide_body:1001";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");


    /**
     * 查询运单收运列表
     * @param query 查询参数
     * @return 结果
     */
    @Override
    public List<HzTransferVo> selectCollectWaybillList(HzTransferQuery query) {
        List<HzTransferVo> hzTransferVos = transferMapper.selectCollectWaybillList(query);
        hzTransferVos.forEach(e->{
            if (e.getType().equals(1)){
                e.setWaybillNum(1);
            }else {
                Integer count = transferWaybillMapper.selectCount(new QueryWrapper<TransferWaybill>().eq("transfer_id", e.getId()));
                e.setWaybillNum(count);
            }
            switch (e.getStatus()){
                case "SUBMIT":
                    e.setStatus("提交（未入库）");
                    break;
                case "HANDLED":
                    e.setStatus("已处理");
                    break;
                default:
                    e.setStatus("无");
                    break;
            }
        });
        return hzTransferVos;
    }

    /**
     * 运单收运
     * @param id 货物交接id
     * @return 结果
     */
    @Override
    public HzTransferVo collect(Long id) {
        HzTransferVo vo = transferMapper.selectByIds(id);
        List<TransferWaybill> transferWaybills = transferWaybillMapper.selectList(new QueryWrapper<TransferWaybill>()
                .eq("transfer_id", vo.getId()));
        List<TransferWaybillVo> list = new ArrayList<>();
        for (TransferWaybill transferWaybill : transferWaybills) {
            TransferWaybillVo waybillVo = waybillMapper.selectByWaybillId(transferWaybill.getMawbId());
            if (transferWaybill.getStoreQuantity() != null){
                waybillVo.setStoreQuantity(transferWaybill.getStoreQuantity());
            }
            if (transferWaybill.getStoreWeight() != null){
                waybillVo.setStoreWeight(transferWaybill.getStoreWeight());
            }
            list.add(waybillVo);
        }
        if (vo.getWaybillCode() != null){
            AirWaybill airWaybill = waybillMapper.selectOne(new QueryWrapper<AirWaybill>().eq("waybill_code", vo.getWaybillCode()).eq("type", "DEP").eq("is_del", 0));
            if (airWaybill != null){
                vo.setWaybillId(airWaybill.getId());
            }
        }
        if (!CollectionUtils.isEmpty(list)){
            vo.setVos(list);
        }
        return vo;
    }

    /**
     * 运单收运操作
     * @param waybillId 运单id
     * @return 结果
     */
    @Override
    public WaybillInfoVo collectWaybill(Long waybillId) {
        WaybillInfoVo vo = waybillMapper.getInfoById(waybillId);
        if (vo != null){
            List<HzCollectWaybill> collectWaybills = collectWaybillMapper.selectList(new QueryWrapper<HzCollectWaybill>().eq("waybill_id", vo.getId()));
            List<DetailedVo> detailedVos = new ArrayList<>();
            if (!CollectionUtils.isEmpty(collectWaybills)){
                for (HzCollectWaybill collectWaybill : collectWaybills) {
                    DetailedVo detailedVo = new DetailedVo();
                    detailedVo.setId(collectWaybill.getWaybillId());
                    detailedVo.setQuantity(collectWaybill.getQuantity());
                    detailedVo.setWeight(collectWaybill.getWeight());
                    detailedVo.setUld(collectWaybill.getUld());
                    detailedVo.setOperName(collectWaybill.getOperName());
                    detailedVo.setStatus(collectWaybill.getStatus());
                    detailedVo.setOperTime(collectWaybill.getCollectTime());
                    detailedVos.add(detailedVo);
                }
                vo.setDetailedVos(detailedVos);
            }
        }
        return vo;
    }

    /**
     * 新增运单收运
     * @param vo 新增参数
     * @return 结果
     */
    @Override
    public String addCollectWaybill(CollectWaybillVo vo) {
        StringBuilder stringBuilder = new StringBuilder();
        HttpServletResponse response = ServletUtils.getResponse();
        WaybillLog waybillLog = new WaybillLog();
        try {
            if (vo == null) {
                throw new CustomException("无新增数据信息");
            }
            if (CollectionUtils.isEmpty(vo.getWeights())) {
                throw new CustomException("当前运单收运过磅数据不能为空");
            }
            Integer voQuantity = vo.getQuantity();
            BigDecimal voWeight = vo.getWeight();
            AirWaybill airWaybill = waybillMapper.selectById(vo.getWaybillId());
            Integer payStatus = airWaybill.getPayStatus();

            if (payStatus == 0 || payStatus == 14){
                throw new CustomException("该运单未支付");
            }

            //运单日志
            waybillLog = waybillLogService.getWaybillLog(
                    airWaybill.getWaybillCode(), 1, SecurityUtils.getNickName(),
                    vo.getWeight().toString(), vo.getQuantity().toString(), airWaybill.getFlightNo1(),
                    vo, null, 0, null, new Date(),
                    "收运入库，件数:" + vo.getQuantity() + "，重量:" + vo.getWeight() + " KG",
                    airWaybill.getType(), vo.getUld());

            //不能抛出异常
            if (airWaybill.getQuantity() < voQuantity) {
                stringBuilder.append("quantity");
            }
            if((airWaybill.getWeight().compareTo(voWeight)) < 0){
                throw new CustomException("该运单收运重量超过运单开单重量");
            }
            List<HzCollectWaybill> collectWaybills = collectWaybillMapper.selectList(new QueryWrapper<HzCollectWaybill>().eq("waybill_id", vo.getWaybillId()));
            if (!CollectionUtils.isEmpty(collectWaybills)) {
                int quantity = collectWaybills.stream().mapToInt(HzCollectWaybill::getQuantity).sum();
                int totalCollectQuantity = quantity + vo.getQuantity();
                if (airWaybill.getQuantity() < totalCollectQuantity) {
                    stringBuilder.append("quantity");
                }
                voQuantity = voQuantity + quantity;
                BigDecimal weight = collectWaybills.stream().map(HzCollectWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                voWeight = voWeight.add(weight);
                if((airWaybill.getWeight().compareTo(voWeight)) < 0){
                    throw new CustomException("该运单收运重量超过运单开单重量");
                }
            }
            WaybillFee waybillFee = feeMapper.selectOne(new QueryWrapper<WaybillFee>()
                    .eq("waybill_code", airWaybill.getWaybillCode())
                    .eq("dept_id", airWaybill.getDeptId())
                    .eq("type", "DEP"));
            BaseAgent agent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", airWaybill.getDeptId()));
            List<CostDetail> details = costDetailMapper.depAutoSettleTask(airWaybill.getWaybillCode(), airWaybill.getDeptId());
            // 出港处置费
            List<String> exceptionValues = Arrays.asList("处置费", "电报费", "冷藏费");
            List<CostDetail> handleSumList = details.stream()
                    .filter(e -> !exceptionValues.contains(e.getChargeAbb()))
                    .collect(Collectors.toList());
            BigDecimal costSum = new BigDecimal(0);
            for (CostDetail detail : handleSumList) {
                detail.setType(1);
                detail.setIsSettle(1);
                detail.setFlightId(-1L);
                detail.setSettleDepQuantity(airWaybill.getQuantity());
                detail.setSettleDepWeight(airWaybill.getWeight());
                detail.setCreateTime(new Date());
                detail.setId(null);
                costSum = costSum.add(detail.getTotalCharge());
                costDetailMapper.insert(detail);
            }
            if (!CollectionUtils.isEmpty(handleSumList)){
                if (agent != null) {
                    if (agent.getSettleMethod() == 1) {
                        updateStatus(airWaybill.getWaybillCode(), airWaybill.getDeptId(), costSum, waybillFee);
                    }else if (agent.getSettleMethod() == 0) {
                        updateStatus(airWaybill.getWaybillCode(), airWaybill.getDeptId(), costSum, waybillFee);
                    } else {
                        if (agent.getPayMethod() == 0) {
                            updateStatus(airWaybill.getWaybillCode(), airWaybill.getDeptId(), costSum, waybillFee);
                        } else {
                            updateStatus(airWaybill.getWaybillCode(), airWaybill.getDeptId(), costSum, waybillFee);
                        }
                    }
                } else {
                    updateStatus(airWaybill.getWaybillCode(), airWaybill.getDeptId(), costSum, waybillFee);
                }
            }
            airWaybill.setStatus("put_in");
            airWaybill.setUpdateTime(new Date());
            airWaybill.setPayStatus(payStatus);
            airWaybill.setCollectStatus(3);
            waybillMapper.updateById(airWaybill);

            // 新增收运数据
            HzCollectWaybill collectWaybill = new HzCollectWaybill();
            BeanUtils.copyProperties(vo, collectWaybill);
            collectWaybill.setCollectTime(new Date());
            collectWaybill.setStatus("REAL");
            collectWaybill.setIsReal(1);
            collectWaybill.setWaybillCode(airWaybill.getWaybillCode());
            collectWaybill.setOperName(SecurityUtils.getUsername());
        // 发送消息
        BigDecimal abs = voWeight.subtract(airWaybill.getWeight()).abs();
        BigDecimal percentage = new BigDecimal(0);
        if (airWaybill.getWeight() != null && airWaybill.getWeight().compareTo(BigDecimal.ZERO) != 0){
            percentage = abs.divide(airWaybill.getWeight(), 4, RoundingMode.DOWN).multiply(new BigDecimal(100));
        }
        if ((abs.compareTo(new BigDecimal(20)) <= 0 && percentage.compareTo(new BigDecimal(3)) <= 0)
        || (abs.compareTo(new BigDecimal(5)) <= 0 && percentage.compareTo(new BigDecimal(3)) >= 0)){
            collectWaybill.setWeight(airWaybill.getWeight().subtract(voWeight).add(vo.getWeight()));
            if(!CollectionUtils.isEmpty(vo.getWeights())){
                HzCollectWeight remove = vo.getWeights().remove(vo.getWeights().size()-1);
                BigDecimal reduce = vo.getWeights().stream().map(HzCollectWeight::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                remove.setWeight(collectWaybill.getWeight().subtract(reduce));
                vo.getWeights().add(remove);
            }
        }
        if (airWaybill.getWeight() != null && airWaybill.getWeight().compareTo(vo.getWeight()) != 0){
            //发送消息
            sendMessage(airWaybill.getWeight(),airWaybill.getWaybillCode(),airWaybill.getAgentCompany(),vo.getWeight());
        }

        //仅能收运一次
        isWaybillCollected(airWaybill.getId());

        int insert = collectWaybillMapper.insert(collectWaybill);
            // 新增特货跟踪数据
            specialTraceService.addSpecialTrace(vo.getWeights(), voQuantity, voWeight, airWaybill);
            // 服务审核判断
            ServiceRequest serviceRequest = serviceRequestMapper.selectOne(new QueryWrapper<ServiceRequest>()
                    .eq("waybill_code", airWaybill.getWaybillCode())
                    .like("service_item", airWaybill.getColdStore()));
            if (serviceRequest != null) {
                if ("UNAUDITED".equals(serviceRequest.getStatus())) {
                    throw new CustomException("该运单冷库服务申请未审批");
                }
                if ("NOPASS".equals(serviceRequest.getStatus())) {
                    throw new CustomException("该运单冷库服务申请未通过");
                }
                HzChargeItems hzChargeItems = chargeItemsMapper.selectOne(new QueryWrapper<HzChargeItems>()
                        .eq("code", "A001")
                        .eq("is_del", 0));
                if (hzChargeItems == null) {
                    throw new CustomException("无出港冷藏收费项目");
                }
                registerService.addColdRegister(airWaybill);
            }
            if (!CollectionUtils.isEmpty(vo.getWeights())) {
                for (HzCollectWeight weight : vo.getWeights()) {
                    weight.setId(null);
                    weight.setCollectId(collectWaybill.getId());
                    weight.setWeightTime(new Date());
                    weight.setDesPort(airWaybill.getDesPort());
                    weight.setAirWays(airWaybill.getCarrier1());
                    BigDecimal gb = weight.getWeight() == null ? new BigDecimal(0) : weight.getWeight();
                    BigDecimal bzz = weight.getBoardWeight() == null ? new BigDecimal(0) : weight.getBoardWeight();
                    BigDecimal db = weight.getPlateWeight() == null ? new BigDecimal(0) : weight.getPlateWeight();
                    weight.setTotalWeight(gb.add(bzz).add(db));
                    collectWeightMapper.insert(weight);
                }
            }
            // 修改交接单中运单数据和状态
            TransferWaybill transferWaybill = transferWaybillMapper.selectOne(new QueryWrapper<TransferWaybill>()
                    .eq("mawb_id", vo.getWaybillId()));
            if (transferWaybill != null) {
                transferWaybill.setStoreQuantity(voQuantity);
                transferWaybill.setStoreWeight(voWeight);
                transferWaybill.setIsCollect(1);
                transferWaybillMapper.updateById(transferWaybill);
                Transfer transfer = transferMapper.selectById(transferWaybill.getTransferId());
                if (transfer.getType().equals(1)) {
                    transfer.setStatus("HANDLED");
                    transfer.setStoreKeeper(SecurityUtils.getNickName());
                    transferMapper.updateById(transfer);
                } else {
                    List<TransferWaybill> waybillList = transferWaybillMapper.selectList(new QueryWrapper<TransferWaybill>()
                            .eq("transfer_id", transfer.getId()));
                    if (!CollectionUtils.isEmpty(waybillList)) {
                        List<TransferWaybill> noCollect = waybillList.stream()
                                .filter(e -> e.getIsCollect().equals(0)).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(noCollect)) {
                            transfer.setStatus("HANDLED");
                            transfer.setStoreKeeper(SecurityUtils.getNickName());
                            transferMapper.updateById(transfer);
                        }
                    }
                }
            }
            String token = getToken();
            try {
                senCable(airWaybill,vo.getQuantity(),vo.getWeight(),token);
            }catch (Exception e){
                log.error(CABLE_ERROR_MARKER, "运单"+airWaybill.getWaybillCode()+"发送FSU-FOH报文失败：" + e.getMessage());
            }
            try {
                sendFWBMsg(airWaybill, token);
            }catch (Exception e){
                log.error(CABLE_ERROR_MARKER, "运单"+airWaybill.getWaybillCode()+"发送FWB报文失败：" + e.getMessage());
            }
            WaybillTrace waybillTrace = new WaybillTrace();
            waybillTrace.setOperTime(new Date());
            waybillTrace.setOperPieces(vo.getQuantity());
            waybillTrace.setOperWeight(vo.getWeight());
            waybillTrace.setWaybillCode(airWaybill.getWaybillCode());
            waybillTrace.setNodeName("货站入库");
            waybillTraceService.insertWaybillTrace(waybillTrace);

            String uld = vo.getUld();
            if(uld != null && !"".equals(uld)){
                if("CAR".equals(uld.substring(0,3))){
                    BaseFlatbedTruck baseFlatbedTruck = truckMapper.selectByCode(uld.substring(3));
                    if (StringUtils.isNotNull(baseFlatbedTruck)){
                        baseFlatbedTruck.setUseTime(new Date());
                        baseFlatbedTruck.setUseStatus(1);
                        baseFlatbedTruck.setDesPort(airWaybill.getDesPort());
                        baseFlatbedTruck.setLcStatus("been_collect");
                        truckMapper.updateById(baseFlatbedTruck);
                    }
                }else{
                    BaseCargoUld baseCargoUld = uldMapper.selectByCode(uld);
                    if (StringUtils.isNotNull(baseCargoUld)){
                        baseCargoUld.setUseTime(new Date());
                        baseCargoUld.setUseStatus(1);
                        baseCargoUld.setDesPort(airWaybill.getDesPort());
                        baseCargoUld.setStatus("been_collect");
                        uldMapper.updateById(baseCargoUld);
                    }
                }
            }

            WaybillCollectData waybillCollectData = new WaybillCollectData();
            waybillCollectData.setType(1);
            waybillCollectData.setWaybillCode(airWaybill.getWaybillCode().substring(4));
            waybillCollectData.setQuantity(vo.getQuantity());
            waybillCollectData.setWeight(vo.getWeight());
            securityProducer.sendOtherWaybill(waybillCollectData, airWaybill.getWaybillCode(), "货站收运完成,推送收运数据");

            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作成功" +  "," +
                            "code:" + response.getStatus() + "," +
                            "data:" + insert));
            return stringBuilder.toString();
        }catch (Exception e){
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" +  "," +
                            "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        }finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }

    private void updateStatus(String waybillCode, Long deptId,  BigDecimal costSum, WaybillFee waybillFee) {
        if (waybillFee != null) {
            waybillFee.setSettleTime(new Date());
            waybillFee.setSettleMoney(costSum);
            waybillFee.setRefund(new BigDecimal(0));
            waybillFee.setStatus(1);
            feeMapper.updateById(waybillFee);
        } else {
            WaybillFee fee = new WaybillFee();
            fee.setSettleMoney(costSum);
            fee.setRefund(new BigDecimal(0));
            fee.setSettleTime(new Date());
            fee.setWaybillCode(waybillCode);
            fee.setDeptId(deptId);
            fee.setStatus(1);
            fee.setType("DEP");
            feeMapper.insert(fee);
        }
    }


    /**
     * 虚拟收运
     * @param waybill 收运数据
     * @return 结果
     */
    @Override
    public String virtualCollect(HzCollectWaybill waybill) {
        StringBuilder stringBuilder = new StringBuilder();
        HttpServletResponse response = ServletUtils.getResponse();
        WaybillLog waybillLog = new WaybillLog();
        try {
            if (waybill == null) {
                throw new CustomException("无新增数据信息");
            }
            Integer voQuantity = waybill.getQuantity();
            BigDecimal voWeight = waybill.getWeight();
            AirWaybill airWaybill = waybillMapper.selectById(waybill.getWaybillId());

            isWaybillCollected(airWaybill.getId());

            if (airWaybill.getPayStatus() == 0 || airWaybill.getPayStatus() == 14){
                throw new CustomException("该运单未支付");
            }
            if (airWaybill.getQuantity() < voQuantity) {
//            throw new CustomException("该运单收运件数超过运单开单件数");
                stringBuilder.append("quantity");
            }
            if ((airWaybill.getWeight().compareTo(voWeight)) < 0) {
                throw new CustomException("该运单收运重量超过运单开单重量");
//            stringBuilder.append("weight");
            }
            List<HzCollectWaybill> collectWaybills = collectWaybillMapper.selectList(new QueryWrapper<HzCollectWaybill>().eq("waybill_id", waybill.getWaybillId()));
            if (!CollectionUtils.isEmpty(collectWaybills)) {
                int quantity = collectWaybills.stream().mapToInt(HzCollectWaybill::getQuantity).sum();
                int totalCollectQuantity = quantity + waybill.getQuantity();
                if (airWaybill.getQuantity() < totalCollectQuantity) {
//                throw new CustomException("该运单收运件数超过运单开单件数");
                    stringBuilder.append("quantity");
                }
                voQuantity = voQuantity + quantity;
                BigDecimal weight = collectWaybills.stream().map(HzCollectWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                voWeight = voWeight.add(weight);
                if ((airWaybill.getWeight().compareTo(voWeight)) < 0) {
                    throw new CustomException("该运单收运重量超过运单开单重量");
//                stringBuilder.append("weight");
                }
            }
            airWaybill.setStatus("put_in");
            airWaybill.setUpdateTime(new Date());
            airWaybill.setCollectStatus(2);
            waybillMapper.updateById(airWaybill);

            // 新增特货跟踪数据
            specialTraceService.addSpecialTrace(null, voQuantity, voWeight, airWaybill);

            // 服务审核判断
            ServiceRequest serviceRequest = serviceRequestMapper.selectOne(new QueryWrapper<ServiceRequest>()
                    .eq("waybill_code", airWaybill.getWaybillCode())
                    .like("service_item", airWaybill.getColdStore()));
            if (serviceRequest != null) {
                if ("UNAUDITED".equals(serviceRequest.getStatus())) {
                    throw new CustomException("该运单冷库服务申请未审批");
                }
                if ("NOPASS".equals(serviceRequest.getStatus())) {
                    throw new CustomException("该运单冷库服务申请未通过");
                }
                HzChargeItems hzChargeItems = chargeItemsMapper.selectOne(new QueryWrapper<HzChargeItems>().eq("code", "A001"));
                if (hzChargeItems == null) {
                    throw new CustomException("无出港冷藏收费项目");
                }
                registerService.addColdRegister(airWaybill);
            }
            // 修改交接单中运单数据和状态
            TransferWaybill transferWaybill = transferWaybillMapper.selectOne(new QueryWrapper<TransferWaybill>()
                    .eq("mawb_id", waybill.getWaybillId()));
            if (transferWaybill != null) {
                transferWaybill.setStoreQuantity(voQuantity);
                transferWaybill.setStoreWeight(voWeight);
                transferWaybill.setIsCollect(1);
                transferWaybillMapper.updateById(transferWaybill);
                Transfer transfer = transferMapper.selectById(transferWaybill.getTransferId());
                if (transfer.getType().equals(1)) {
                    transfer.setStatus("HANDLED");
                    transferMapper.updateById(transfer);
                } else {
                    List<TransferWaybill> waybillList = transferWaybillMapper.selectList(new QueryWrapper<TransferWaybill>()
                            .eq("transfer_id", transfer.getId()));
                    if (!CollectionUtils.isEmpty(waybillList)) {
                        List<TransferWaybill> noCollect = waybillList.stream()
                                .filter(e -> e.getIsCollect().equals(0)).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(noCollect)) {
                            transfer.setStatus("HANDLED");
                            transferMapper.updateById(transfer);
                        }
                    }
                }
            }

            WaybillTrace waybillTrace = new WaybillTrace();
            waybillTrace.setOperTime(new Date());
            waybillTrace.setOperPieces(waybill.getQuantity());
            waybillTrace.setOperWeight(waybill.getWeight());
            waybillTrace.setWaybillCode(airWaybill.getWaybillCode());
            waybillTrace.setNodeName("货站入库");
            waybillTraceService.insertWaybillTrace(waybillTrace);

            // 新增收运数据
            waybill.setOperName(SecurityUtils.getUsername());
            waybill.setCollectTime(new Date());
            waybill.setIsReal(0);
            waybill.setStatus("VIRTUAL");
            collectWaybillMapper.insert(waybill);
            HzCollectWeight weight = new HzCollectWeight();
            weight.setCollectId(waybill.getId());
            weight.setWeightTime(new Date());
            weight.setDesPort(airWaybill.getDesPort());
            weight.setTotalWeight(waybill.getWeight());
            weight.setQuantity(waybill.getQuantity());
            weight.setWeight(waybill.getWeight());
            collectWeightMapper.insert(weight);

            //运单日志
            waybillLog = waybillLogService.getWaybillLog(
                    airWaybill.getWaybillCode(), 1, SecurityUtils.getNickName(),
                    waybill.getWeight().toString(), waybill.getQuantity().toString(), airWaybill.getFlightNo1(),
                    waybill, null, 0, null, new Date(),
                    "收运入库，件数:" + waybill.getQuantity() + "，重量:" + waybill.getWeight() + " KG",
                    airWaybill.getType(), waybill.getUld());
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作成功" +  "," +
                            "code:" + response.getStatus() + "," +
                            "data:"));

            return stringBuilder.toString();
        }
        catch(Exception e){
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" +  "," +
                            "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        }finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }

    /**
     * 批量虚拟收运
     * */
    @Override
    public int virtualCollectBatch(Long[] ids) {
        for(Long id : ids){
            AirWaybill airWaybill = waybillMapper.selectById(id);
            //1.8一个运单只能一次虚拟收运
            List<HzCollectWaybill> hzCollectWaybills = collectWaybillMapper.selectList(new QueryWrapper<HzCollectWaybill>()
                    .eq("waybill_id", airWaybill.getId())
                    .eq("status", "VIRTUAL"));
            if(hzCollectWaybills.size() > 0){
                throw new CustomException(airWaybill.getWaybillCode() + "运单已经虚拟收运");
            }
            if (airWaybill.getPayStatus() == 0 || airWaybill.getPayStatus() == 14){
                throw new CustomException("运单"+airWaybill.getWaybillCode()+"未支付");
            }
            airWaybill.setStatus("put_in");
            airWaybill.setUpdateTime(new Date());
            airWaybill.setCollectStatus(2);
            waybillMapper.updateById(airWaybill);

            WaybillLog waybillLog = waybillLogService.getWaybillLog(
                    airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                    airWaybill.getWeight()!=null ? airWaybill.getWeight().toString() : null,
                    airWaybill.getQuantity()!=null ? airWaybill.getQuantity().toString() : null,
                    airWaybill.getFlightNo1(), airWaybill.getWaybillCode(), null, 0, null, new Date(),
                    "虚拟收运", "DEP", null);
            waybillLogService.insertWaybillLog(waybillLog);

            // 新增特货跟踪数据
            specialTraceService.addSpecialTrace(null, airWaybill.getQuantity(), airWaybill.getWeight(), airWaybill);

            // 服务审核判断
            ServiceRequest serviceRequest = serviceRequestMapper.selectOne(new QueryWrapper<ServiceRequest>()
                    .eq("waybill_code", airWaybill.getWaybillCode())
                    .like("service_item", airWaybill.getColdStore()));
            if (serviceRequest != null) {
                if ("UNAUDITED".equals(serviceRequest.getStatus())) {
                    throw new CustomException("该运单冷库服务申请未审批");
                }
                if ("NOPASS".equals(serviceRequest.getStatus())) {
                    throw new CustomException("该运单冷库服务申请未通过");
                }
                HzChargeItems hzChargeItems = chargeItemsMapper.selectOne(new QueryWrapper<HzChargeItems>().eq("code", "A001"));
                if (hzChargeItems == null) {
                    throw new CustomException("无出港冷藏收费项目");
                }
                registerService.addColdRegister(airWaybill);
            }

            // 修改交接单中运单数据和状态
            TransferWaybill transferWaybill = transferWaybillMapper.selectOne(new QueryWrapper<TransferWaybill>()
                    .eq("mawb_id", airWaybill.getId()));
            if (transferWaybill != null) {
                transferWaybill.setStoreQuantity(airWaybill.getQuantity());
                transferWaybill.setStoreWeight(airWaybill.getWeight());
                transferWaybill.setIsCollect(1);
                transferWaybillMapper.updateById(transferWaybill);
                Transfer transfer = transferMapper.selectById(transferWaybill.getTransferId());
                if (transfer.getType().equals(1)) {
                    transfer.setStatus("HANDLED");
                    transferMapper.updateById(transfer);
                } else {
                    List<TransferWaybill> waybillList = transferWaybillMapper.selectList(new QueryWrapper<TransferWaybill>()
                            .eq("transfer_id", transfer.getId()));
                    if (!CollectionUtils.isEmpty(waybillList)) {
                        List<TransferWaybill> noCollect = waybillList.stream()
                                .filter(e -> e.getIsCollect().equals(0)).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(noCollect)) {
                            transfer.setStatus("HANDLED");
                            transferMapper.updateById(transfer);
                        }
                    }
                }
            }

            WaybillTrace waybillTrace = new WaybillTrace();
            waybillTrace.setOperTime(new Date());
            waybillTrace.setOperPieces(airWaybill.getQuantity());
            waybillTrace.setOperWeight(airWaybill.getWeight());
            waybillTrace.setWaybillCode(airWaybill.getWaybillCode());
            waybillTrace.setNodeName("货站入库");
            waybillTraceService.insertWaybillTrace(waybillTrace);

            isWaybillCollected(airWaybill.getId());
            // 新增收运数据
            HzCollectWaybill waybill = new HzCollectWaybill();
            waybill.setWaybillId(airWaybill.getId());
            waybill.setWaybillCode(airWaybill.getWaybillCode());
            waybill.setQuantity(airWaybill.getQuantity());
            waybill.setWeight(airWaybill.getWeight());
            waybill.setOperName(SecurityUtils.getUsername());
            waybill.setCollectTime(new Date());
            waybill.setIsReal(0);
            waybill.setStatus("VIRTUAL");
            collectWaybillMapper.insert(waybill);
            HzCollectWeight weight = new HzCollectWeight();
            weight.setCollectId(waybill.getId());
            weight.setWeightTime(new Date());
            weight.setDesPort(airWaybill.getDesPort());
            weight.setTotalWeight(waybill.getWeight());
            weight.setQuantity(waybill.getQuantity());
            weight.setWeight(waybill.getWeight());
            collectWeightMapper.insert(weight);
        }
        return 1;
    }

    /**
     * 拒绝收运
     * @param waybill 收运数据
     * @return 结果
     */
    @Override
    public int refuseCollect(HzCollectWaybill waybill) {
        AirWaybill airWaybill = waybillMapper.selectById(waybill.getWaybillId());
        airWaybill.setStatus("refuse_collect");
        waybillMapper.updateById(airWaybill);

        //将货物交接表的状态改为已处理,加一个判断,其他订单的状态没有been_sent就改为已处理
        TransferWaybill transferWaybill = transferWaybillMapper.selectOne(new QueryWrapper<TransferWaybill>()
                .eq("mawb_id", waybill.getWaybillId()));
        Transfer transfer = transferMapper.selectById(transferWaybill.getTransferId());

        List<TransferWaybill> transferWaybillList = transferWaybillMapper.selectList(new QueryWrapper<TransferWaybill>()
                .eq("transfer_id", transfer.getId()));
        int i = 0;
        for (TransferWaybill t:transferWaybillList) {
            AirWaybill airWaybill1 = waybillMapper.selectOne(new QueryWrapper<AirWaybill>().eq("id", t.getMawbId()));
            if ("been_sent".equals(airWaybill1.getStatus())){
                i++;
            }
        }
        if (i == 0){
            transfer.setStatus("HANDLED");
            transferMapper.updateById(transfer);
        }

        waybill.setOperName(SecurityUtils.getUsername());
        waybill.setWaybillCode(airWaybill.getWaybillCode());
        waybill.setQuantity(airWaybill.getQuantity());
        waybill.setWeight(airWaybill.getWeight());
        waybill.setStatus("REFUSE");
        waybill.setUld("-");
        return collectWaybillMapper.insert(waybill);
    }

    private void isWaybillCollected(Long waybillId){
        List<HzCollectWaybill> waybillCollectedList = collectWaybillMapper.selectList(new QueryWrapper<HzCollectWaybill>()
                .eq("waybill_id", waybillId));
        if(waybillCollectedList.size() > 0){
            throw new CustomException("运单已收运");
        }
    }

    /**
     * 货站安检提交
     * @param query 安检数据
     * @return 结果
     */
    @Override
    public int securitySubmit(SecuritySubmitQuery query) {
        AirWaybill airWaybill = waybillMapper.selectById(query.getWaybillId());
        AllSecurityWaybill allSecurityWaybill = allSecurityWaybillMapper.selectById(query.getWaybillId());
        if(airWaybill == null){
            allSecurityWaybill.setDeclarationConsistent(query.getDeclarationConsistent());
            allSecurityWaybill.setIsExamine(query.getIsExamine());
            if(query.getDeclarationConsistent() == 0){
                //审单结果为退回(0) 则安检提交状态赋值为-1表示退回
                allSecurityWaybill.setSecuritySubmit(-1);
                allSecurityWaybill.setSecuritySubmitWl(-1);
            }else{
                allSecurityWaybill.setSecuritySubmit(0);
                allSecurityWaybill.setSecuritySubmitWl(2);
            }
            //往货检系统发消息
            SecuritySubmitSendSecond vo = getSecuritySubmitSendSecond(allSecurityWaybill);
            securityProducer.sendSecond(vo);

            //往安检申报历史表新增
            AllSecurityUrl allSecurityUrl = new AllSecurityUrl();
            allSecurityUrl.setWaybillId(allSecurityWaybill.getId());
            allSecurityUrl.setWaybillCode(allSecurityWaybill.getWaybillCode());
            allSecurityUrl.setSecurityUrl(allSecurityWaybill.getSecurityUrl());
            allSecurityUrl.setCreateBy(SecurityUtils.getNickName());
            allSecurityUrl.setCreateTime(new Date());
            allSecurityUrlMapper.insert(allSecurityUrl);

            return allSecurityWaybillMapper.updateById(allSecurityWaybill);
        }else{
            airWaybill.setDeclarationConsistent(query.getDeclarationConsistent());
            airWaybill.setIsExamine(query.getIsExamine());
            if(query.getDeclarationConsistent() == 0){
                //审单结果为退回(0) 则安检提交状态赋值为-1表示退回
                airWaybill.setSecuritySubmit(-1);
                airWaybill.setSecuritySubmitWl(-1);
            }else{
                airWaybill.setSecuritySubmit(0);
                airWaybill.setSecuritySubmitWl(2);
            }
            //往货检系统发消息
            SecuritySubmitSendSecond vo = getSecuritySubmitSendSecond(airWaybill);
            securityProducer.sendSecond(vo);

            //往安检申报历史表新增
            AllSecurityUrl allSecurityUrl = new AllSecurityUrl();
            allSecurityUrl.setWaybillId(airWaybill.getId());
            allSecurityUrl.setWaybillCode(airWaybill.getWaybillCode());
            allSecurityUrl.setSecurityUrl(airWaybill.getSecurityUrl());
            allSecurityUrl.setCreateBy(SecurityUtils.getNickName());
            allSecurityUrl.setCreateTime(new Date());
            allSecurityUrlMapper.insert(allSecurityUrl);
            if(allSecurityWaybill!=null){
                allSecurityWaybill.setDeclarationConsistent(query.getDeclarationConsistent());
                allSecurityWaybill.setIsExamine(query.getIsExamine());
                if(query.getDeclarationConsistent() == 0){
                    //审单结果为退回(0) 则安检提交状态赋值为-1表示退回
                    allSecurityWaybill.setSecuritySubmit(-1);
                    allSecurityWaybill.setSecuritySubmitWl(-1);
                }else{
                    allSecurityWaybill.setSecuritySubmit(0);
                    allSecurityWaybill.setSecuritySubmitWl(2);
                }
                allSecurityWaybillMapper.updateById(allSecurityWaybill);
            }
            WaybillLog waybillLog = waybillLogService.getWaybillLog(
                    airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                    airWaybill.getWeight()!=null ? airWaybill.getWeight().toString() : null,
                    airWaybill.getQuantity()!=null ? airWaybill.getQuantity().toString() : null,
                    airWaybill.getFlightNo1(), airWaybill.getWaybillCode(), null, 0, null, new Date(),
                    "安检提交(货站操作)", "DEP", null);
            waybillLogService.insertWaybillLog(waybillLog);
            return waybillMapper.updateById(airWaybill);
        }

    }

    /**
     * 组装第二次需要发送的数据
     * */
    private SecuritySubmitSendSecond getSecuritySubmitSendSecond(AirWaybill airWaybill){
        SecuritySubmitSendSecond securitySubmitSendSecond = new SecuritySubmitSendSecond();
        securitySubmitSendSecond.setFlightNo(airWaybill.getFlightNo1());
        securitySubmitSendSecond.setExecDate(airWaybill.getFlightDate1());
        securitySubmitSendSecond.setWaybillCode(airWaybill.getWaybillCode().substring(4));
        securitySubmitSendSecond.setCheckTime(new Date());
        securitySubmitSendSecond.setCollectName(SecurityUtils.getNickName());
        securitySubmitSendSecond.setCheckInference(airWaybill.getDeclarationConsistent() == 1 ? "符合运输":"退回");
        securitySubmitSendSecond.setMessageType("审单结果");

        List<String> securityPdfUrls = new ArrayList<>();
        String[] securityPdfUrlsSplit = airWaybill.getSecurityUrl().split(",");
        for(String s:securityPdfUrlsSplit){
            securityPdfUrls.add(s.trim());
        }
        securitySubmitSendSecond.setSecuritypdfUrls(securityPdfUrls);

        return securitySubmitSendSecond;
    }

    /**
     * 组装第二次需要发送的数据 运单不存在时
     * */
    private SecuritySubmitSendSecond getSecuritySubmitSendSecond(AllSecurityWaybill allSecurityWaybill){
        SecuritySubmitSendSecond securitySubmitSendSecond = new SecuritySubmitSendSecond();
        securitySubmitSendSecond.setFlightNo(allSecurityWaybill.getFlightNo1());
        securitySubmitSendSecond.setExecDate(allSecurityWaybill.getFlightDate1());
        securitySubmitSendSecond.setWaybillCode(allSecurityWaybill.getWaybillCode().substring(4));
        securitySubmitSendSecond.setCheckTime(new Date());
        securitySubmitSendSecond.setCollectName(SecurityUtils.getNickName());
        securitySubmitSendSecond.setCheckInference(allSecurityWaybill.getDeclarationConsistent() == 1 ? "符合运输":"退回");
        securitySubmitSendSecond.setMessageType("审单结果");

        List<String> securityPdfUrls = new ArrayList<>();
        String[] securityPdfUrlsSplit = allSecurityWaybill.getSecurityUrl().split(",");
        for(String s:securityPdfUrlsSplit){
            securityPdfUrls.add(s.trim());
        }
        securitySubmitSendSecond.setSecuritypdfUrls(securityPdfUrls);

        return securitySubmitSendSecond;
    }


    /**
     * 根据运单号查询运单详情
     * @param waybillCode 运单号
     * @return 运单详情
     */
    @Override
    public WaybillInfoVo getInfo(String waybillCode) {
        if(StrUtil.isBlank(waybillCode)){
            throw new CustomException("运单号不能为空");
        }
        WaybillInfoQuery query = new WaybillInfoQuery();
        query.setWaybillCode(waybillCode);
        WaybillInfoVo vo = waybillMapper.getInfo(query);
        if (vo != null){
            if("INVALID".equals(vo.getStatus())){
                throw new CustomException("该运单已作废");
            }

            BigDecimal weightSum = loadWaybillMapper.selectWaybillByWaybillId(vo.getId());


            Integer count = exitCargoMapper.selectCount(new QueryWrapper<HzDepExitCargo>().eq("waybill_code", waybillCode));
            if (count > 0){
                vo.setIsExit(1);
            }else {
                vo.setIsExit(0);
            }
            TransferWaybill transferWaybill = transferWaybillMapper.selectOne(new QueryWrapper<TransferWaybill>().eq("mawb_id", vo.getId()));
            if (transferWaybill != null){
                Transfer transfer = transferMapper.selectById(transferWaybill.getTransferId());
                vo.setTransferNum(transfer.getNum());
            }
            List<HzCollectWaybill> collectWaybills = collectWaybillMapper.selectList(new QueryWrapper<HzCollectWaybill>()
                    .eq("waybill_id", vo.getId()));
            List<DetailedVo> detailedVos = new ArrayList<>();
            if (!CollectionUtils.isEmpty(collectWaybills)){
                int size = (int) collectWaybills.stream().filter(e -> e.getIsReal() == 0).count();
                if (size == 0){
                    vo.setIsReal(1);
                    vo.setCollectStatus(3);
                }else {
                    vo.setIsReal(0);
                    vo.setCollectStatus(2);
                }
                for (HzCollectWaybill collectWaybill : collectWaybills) {
                    DetailedVo detailedVo = new DetailedVo();
                    detailedVo.setId(collectWaybill.getId());
                    detailedVo.setWaybillId(collectWaybill.getWaybillId());
                    detailedVo.setQuantity(collectWaybill.getQuantity());
                    detailedVo.setWeight(collectWaybill.getWeight());
                    if (StringUtils.isNull(collectWaybill.getUld())){
                        List<HzCollectWeight> collectId = collectWeightMapper.selectList(new QueryWrapper<HzCollectWeight>()
                                .eq("collect_id", collectWaybill.getId()));
                        if (!CollectionUtils.isEmpty(collectId)){
                            detailedVo.setUld(collectId.get(0).getUld());
                            detailedVo.setLocator(collectId.get(0).getLocator());
                            detailedVo.setStoreName(collectId.get(0).getStore());
                        }
                    }else{
                        detailedVo.setUld(collectWaybill.getUld());
                    }
                    String operName = collectWaybill.getOperName();
                    SysUser sysUser = userMapper.selectUserByUserName(operName);
                    detailedVo.setOperName(Optional.ofNullable(sysUser).map(SysUser::getNickName).orElse(operName));
                    detailedVo.setStatus(collectWaybill.getStatus());
                    detailedVo.setOperTime(collectWaybill.getCollectTime());
                    detailedVos.add(detailedVo);
                }
                vo.setDetailedVos(detailedVos);
            }
            //中文描述,通过特货代码获取
            BaseSpecialCode baseSpecialCode = specialCodeMapper.selectSpecialByCode(vo.getSpecialCargoCode1());
            if (StringUtils.isNotNull(baseSpecialCode)){
            vo.setChineseDescription(baseSpecialCode.getChineseDescription());
            }
            //大类,通过货品代码获取
            BaseCargoCode baseCargoCode =  cargoCodeMapper.selectOne(new QueryWrapper<BaseCargoCode>()
                    .eq("code", vo.getCargoCode()).eq("is_del",0));
            if(StringUtils.isNotNull(baseCargoCode)){
            vo.setCategoryCode(baseCargoCode.getCategoryCode());
            }
            Set<Long> pullDownTotal = isPullDownTotal(waybillCode, vo.getQuantity(), vo.getWeight());
            if(pullDownTotal.size() > 0){
                vo.setIsPullDownTotal(1);
            }else{
                vo.setIsPullDownTotal(0);
            }
        }
        return vo;
    }



    /**
     * 根据运单号查询运单详情 --平板端
     * @param waybillCode 运单号
     * @return 运单详情
     */
    @Override
    public WaybillInfoVo getInfoApp(String waybillCode) {
        WaybillInfoQuery query = new WaybillInfoQuery();
        query.setWaybillCode(waybillCode);
        WaybillInfoVo vo = waybillMapper.getInfo(query);
        if (vo != null){
            if("INVALID".equals(vo.getStatus())){
                throw new CustomException("该运单已作废");
            }
            Integer count = exitCargoMapper.selectCount(new QueryWrapper<HzDepExitCargo>().eq("waybill_code", waybillCode));
            if (count > 0){
                vo.setIsExit(1);
            }else {
                vo.setIsExit(0);
            }
            TransferWaybill transferWaybill = transferWaybillMapper.selectOne(new QueryWrapper<TransferWaybill>().eq("mawb_id", vo.getId()));
            if (transferWaybill != null){
                Transfer transfer = transferMapper.selectById(transferWaybill.getTransferId());
                vo.setTransferNum(transfer.getNum());
            }
            List<HzCollectWaybill> collectWaybills = collectWaybillMapper.selectList(new QueryWrapper<HzCollectWaybill>()
                    .eq("waybill_id", vo.getId()));
            List<DetailedVo> detailedVos = new ArrayList<>();
            if (!CollectionUtils.isEmpty(collectWaybills)){
                int size = (int) collectWaybills.stream().filter(e -> e.getIsReal() == 0).count();
                if (size == 0){
                    vo.setIsReal(1);
                }else {
                    vo.setIsReal(0);
                }
                for (HzCollectWaybill collectWaybill : collectWaybills) {
                    DetailedVo detailedVo = new DetailedVo();
                    detailedVo.setId(collectWaybill.getId());
                    detailedVo.setWaybillId(collectWaybill.getWaybillId());
                    detailedVo.setQuantity(collectWaybill.getQuantity());
                    detailedVo.setWeight(collectWaybill.getWeight());
                    if (StringUtils.isNull(collectWaybill.getUld())){
                        List<HzCollectWeight> collectId = collectWeightMapper.selectList(new QueryWrapper<HzCollectWeight>()
                                .eq("collect_id", collectWaybill.getId()));
                        if (!CollectionUtils.isEmpty(collectId)){
                            detailedVo.setUld(collectId.get(0).getUld());
                            detailedVo.setLocator(collectId.get(0).getLocator());
                            detailedVo.setStoreName(collectId.get(0).getStore());
                        }
                    }else{
                        detailedVo.setUld(collectWaybill.getUld());
                    }
                    detailedVo.setOperName(collectWaybill.getOperName());
                    detailedVo.setStatus(collectWaybill.getStatus());
                    detailedVo.setOperTime(collectWaybill.getCollectTime());
                    detailedVos.add(detailedVo);
                }
                vo.setDetailedVos(detailedVos);
            }
            //中文描述,通过特货代码获取
            BaseSpecialCode baseSpecialCode = specialCodeMapper.selectSpecialByCode(vo.getSpecialCargoCode1());
            if (StringUtils.isNotNull(baseSpecialCode)){
            vo.setChineseDescription(baseSpecialCode.getChineseDescription());
            }
            //大类,通过货品代码获取
            BaseCargoCode baseCargoCode =  cargoCodeMapper.selectOne(new QueryWrapper<BaseCargoCode>()
                    .eq("code", vo.getCargoCode()).eq("is_del",0));
            if(StringUtils.isNotNull(baseCargoCode)){
            vo.setCategoryCode(baseCargoCode.getCategoryCode());
            }
            Set<Long> pullDownTotal = isPullDownTotal(waybillCode, vo.getQuantity(), vo.getWeight());
            if(pullDownTotal.size() > 0){
                vo.setIsPullDownTotal(1);
            }else{
                vo.setIsPullDownTotal(0);
            }

            AirWaybill airWaybill = waybillMapper.selectById(vo.getId());
            if(airWaybill.getCollectStatus() != 3){
                airWaybill.setCollectStatus(1);
                waybillMapper.updateById(airWaybill);
            }
        }
        return vo;
    }

    private Set<Long> isPullDownTotal(String waybillCode,Integer quantity,BigDecimal weight){
        //如果该运单是全拉状态(拉下件数重量等于制单件数重量 + 代理人未处理) + 没有配载 则前端可以取消收运
        List<Wrong> wrongs = wrongMapper.selectList(new QueryWrapper<Wrong>()
                .eq("waybill_code", waybillCode).eq("status", 1)
                .eq("pro_method", 0).eq("is_over", 0));
        Set<Long> integerSet = new HashSet<>();
        if(wrongs.size() > 0){
            List<HzDepPullDown> pullDownList = pullDownMapper.selectList(new QueryWrapper<HzDepPullDown>()
                    .in("id", wrongs.stream().map(Wrong::getPullId).toArray())
                    .eq("is_load",0));
            if(quantity == pullDownList.stream().mapToInt(HzDepPullDown::getQuantity).sum()
                    && Objects.equals(weight, pullDownList.stream().map(HzDepPullDown::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add))){
                integerSet.addAll(wrongs.stream().map(Wrong::getId).collect(Collectors.toList()));
                return integerSet;
            }
        }
        return integerSet;
    }

    private static final Map<String, String> STATUSMAP = new HashMap<>();
    static {
        STATUSMAP.put("not_in", "been_sent");
        STATUSMAP.put("not_pre", "put_in");
        STATUSMAP.put("al_pre", "been_pre");
        STATUSMAP.put("fz_out", "been_out");
        STATUSMAP.put("pull_down", "pull_down");
        STATUSMAP.put("change_order", "order_change");
        STATUSMAP.put("a_in", "record_order");
        STATUSMAP.put("lh_comp", "tally_comp");
        STATUSMAP.put("ta_tx", "out_stock");
    }

    /**
     * 运单保存
     * @param vo 运单修改参数
     * @return 结果
     */
    @Override
    public int saveWaybill(WaybillInfoVo vo) {
        AirWaybill airWaybill = waybillMapper.selectById(vo.getId());

        if (StrUtil.isNotBlank(vo.getArrWaybillCode())) {
            handleRelationArrWaybill(airWaybill, vo.getArrWaybillCode());
        }

        if (vo.getIsSouth() == 1){
            Integer count = exitCargoMapper.selectCount(new QueryWrapper<HzDepExitCargo>().eq("waybill_code", vo.getWaybillCode()));
            if (vo.getIsLoad() == 1 || count > 0){
                throw new CustomException("该运单已退货/配载 不可选");
            }
            BaseCargoCode baseCargoCode = cargoCodeMapper.selectByCode(airWaybill.getCargoCode());
            Date date = new Date();
            List<HzChargeItems> hzChargeItems = chargeItemsMapper.selectList(new QueryWrapper<HzChargeItems>()
                    .eq("operation_type", "DEP")
                    .eq("is_default", 1).eq("status",1)
                    .le("start_effective_time",date)
                    .ge("end_effective_time",date)
                    .eq("is_del",0));
            SysConfig sysConfig = sysConfigMapper.selectConfigById(16L);
            LocalTime startTime;
            LocalTime endTime;
            if (airWaybill.getFlightDate1() == null){
                startTime = LocalTime.of(6, 0,0);
                endTime = LocalTime.of(6, 0,1);
            }else {
                Instant instant = airWaybill.getFlightDate1().toInstant();
                LocalDateTime takeoffTime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
                LocalDateTime time = takeoffTime.plusSeconds(1);
                endTime = time.toLocalTime();
                startTime = takeoffTime.toLocalTime();
            }
            Instant startInstant = date.toInstant();
            long aLong = Long.parseLong(sysConfig.getConfigValue());
            long times = aLong * 60 * 60;
            Instant endInstant = startInstant.plusSeconds(times);
            Date storeEndTime = Date.from(endInstant);
            BigDecimal costSum = new BigDecimal(0);
            BigDecimal weightRate;
            BigDecimal chargeWeight = airWaybill.getChargeWeight() == null ? new BigDecimal(0) : airWaybill.getChargeWeight();
            if (airWaybill.getWeight() == null || airWaybill.getWeight().compareTo(new BigDecimal(0)) == 0){
                weightRate = new BigDecimal(0);
            }else {
                BigDecimal bigDecimal = chargeWeight.divide(airWaybill.getWeight(),5, RoundingMode.DOWN).multiply(airWaybill.getWeight());
                weightRate = bigDecimal.setScale(0, RoundingMode.CEILING);
            }
            for (HzChargeItems hzChargeItem : hzChargeItems) {
                List<HzChargeIrRelation> relations = relationMapper.selectList(new QueryWrapper<HzChargeIrRelation>().eq("item_id", hzChargeItem.getId()).eq("is_del",0));
                int maxMatchCount = 0;
                List<HzChargeIrRelation> ruleList = new ArrayList<>();
                for (HzChargeIrRelation hzChargeRule : relations) {
                    if (hzChargeRule.getIsSouth() != 1){
                        continue;
                    }
                    if (hzChargeRule.getIsExit() == 1){
                        continue;
                    }
                    if (StringUtils.isNotEmpty(hzChargeRule.getNoCharge()) && !hzChargeRule.getNoCharge().contains(airWaybill.getDeptId().toString())){
                        continue;
                    }
                    if (StringUtils.isNotEmpty(hzChargeRule.getPrefix()) && !hzChargeRule.getPrefix().contains(vo.getWaybillCode().substring(4,7))){
                        continue;
                    }
                    if (StringUtils.isNotEmpty(hzChargeRule.getCategory()) && !hzChargeRule.getCategory().contains(baseCargoCode.getCategoryCode())){
                        continue;
                    }
                    if (!hzChargeRule.getCrossAir().equals(vo.getCrossAir())){
                        continue;
                    }
                    int matchCount = 0;

                    // 根据判断货品代码
                    int cargoMatchCount = isCargoCodeMatch(hzChargeRule, vo.getCargoCode());

                    if (cargoMatchCount >= 0) {
                        matchCount += cargoMatchCount;
                    }

                    if (matchCount > 0) {
                        if (matchCount > maxMatchCount) {
                            maxMatchCount = matchCount;
                            ruleList.clear();
                            ruleList.add(hzChargeRule);
                        } else if (matchCount == maxMatchCount) {
                            ruleList.add(hzChargeRule);
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(ruleList)){
                    HzChargeIrRelation relation = ruleList.stream().max(Comparator.comparing(HzChargeIrRelation::getPriority)).orElse(null);
                    if (relation != null){
                        HzChargeRule rule1 = ruleMapper.selectById(relation.getRuleId());
                        List<HzChargeItemRule> itemRules = itemRuleMapper.selectList(new QueryWrapper<HzChargeItemRule>().eq("ir_id", relation.getId()));
                        if ("ColdStorageBillingRule.class".equals(rule1.getClassName()) && StringUtils.isEmpty(vo.getColdStore())){
                            continue;
                        }
                        CostDetail detail = new CostDetail();
                        detail.setWaybillCode(vo.getWaybillCode());
                        detail.setIrId(relation.getId());
                        detail.setColdStore(vo.getColdStore());
                        detail.setUnit(1);
                        detail.setSmallItem(1);
                        detail.setLargeItem(1);
                        detail.setSuperLargeItem(1);
                        detail.setStartTime(startTime);
                        detail.setEndTime(endTime);
                        detail.setDaysInStorage(1.0);
                        detail.setStoreStartTime(date);
                        detail.setStoreEndTime(storeEndTime);
                        detail.setPointTime(startTime);
                        BillingRule rule = BillingRuleFactory.createRule(rule1.getClassName());
                        BillRuleVo vo1 = rule.calculateFee(itemRules, weightRate, vo.getQuantity(), detail);
                        BigDecimal totalCharge = BigDecimalRoundUtils.bigDecimalRound(hzChargeItem.getRoundRule(), vo1.getTotalCharge());
                        detail.setTotalCharge(totalCharge);
                        detail.setDeptId(airWaybill.getDeptId());
                        detail.setQuantity(vo1.getQuantity());
                        detail.setRate(vo1.getRate());
                        costSum = costSum.add(totalCharge);
                        costDetailMapper.insert(detail);
                    }
                }
            }
        }
        airWaybill.setSourcePort(vo.getSourcePort());
        airWaybill.setDesPort(vo.getDesPort());
        airWaybill.setCarrier1(vo.getCarrier1());
        airWaybill.setDes1(vo.getDes1());
        airWaybill.setCarrier2(vo.getCarrier2());
        airWaybill.setDes2(vo.getDes2());
        airWaybill.setCarrier3(vo.getCarrier3());
        airWaybill.setDes3(vo.getDes3());
        airWaybill.setSpecialCargoCode1(vo.getSpecialCargoCode1());
        airWaybill.setRemark(vo.getChineseDescription());
        airWaybill.setSize(vo.getSize());
        airWaybill.setVolume(vo.getVolume());
        airWaybill.setColdStore(vo.getColdStore());
        airWaybill.setCargoName(vo.getCargoName());
        airWaybill.setPressureChamber(vo.getPressureChamber());
        airWaybill.setPack(vo.getPack());
        airWaybill.setShipper(vo.getShipper());
        airWaybill.setFlightNo1(vo.getFlightNo1());
        airWaybill.setFlightDate1(vo.getFlightDate1());
        airWaybill.setWriter(vo.getWriter());
        airWaybill.setStorageTransportNotes(vo.getStorageTransportNotes());
        airWaybill.setIsSouth(vo.getIsSouth());
        airWaybill.setUpdateTime(new Date());

        WaybillLog waybillLog = waybillLogService.getWaybillLog(
                airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                airWaybill.getWeight()!=null ? airWaybill.getWeight().toString() : null,
                airWaybill.getQuantity()!=null ? airWaybill.getQuantity().toString() : null,
                airWaybill.getFlightNo1(), vo, null, 0, null, new Date(),
                "保存运单(货站操作)", "DEP", null);
        waybillLogService.insertWaybillLog(waybillLog);

        return waybillMapper.updateById(airWaybill);
    }

    /**
     * 处理关联的进港运单
     * @param depWaybill 出港运单
     * @param arrWaybillCode 进港运单号
     */
    private void handleRelationArrWaybill(AirWaybill depWaybill, String arrWaybillCode) {
        LambdaQueryWrapper<AirWaybill> lqw = Wrappers.lambdaQuery();
        lqw.select(AirWaybill::getId, AirWaybill::getQuantity, AirWaybill::getWeight, AirWaybill::getAgentCompany);
        lqw.eq(AirWaybill::getType, "ARR");
        lqw.eq(AirWaybill::getWaybillCode, arrWaybillCode);
        AirWaybill arrWaybill = waybillMapper.selectOne(lqw);
        if(ObjectUtil.isNull(arrWaybill)){
            throw new CustomException("该进港运单不存在");
        }

        if (!arrWaybill.getQuantity().equals(depWaybill.getQuantity())) {
            throw new CustomException("该出港运单与进港运单的件数不一致");
        }

        if (!arrWaybill.getWeight().equals(depWaybill.getWeight())) {
            throw new CustomException("该出港运单与进港运单的重量不一致");
        }

        if (!arrWaybill.getAgentCompany().equals(depWaybill.getAgentCompany())) {
            throw new CustomException("该出港运单与进港运单的代理人不一致");
        }

        arrWaybill.setQuantity(0);
        arrWaybill.setWeight(BigDecimal.ZERO);
        waybillMapper.updateById(arrWaybill);

        depWaybill.setArrWaybillCode(arrWaybillCode);
    }

    @Override
    public void saveWaybillUrl(WaybillInfoVo vo) {
        AirWaybill airWaybill = waybillMapper.selectById(vo.getId());
        if (vo.getSecurityUrl() != null){
            airWaybill.setSecurityUrl(vo.getSecurityUrl());
        }
        if (vo.getPdfUrl() != null){
            airWaybill.setPdfUrl(vo.getPdfUrl());
        }
        waybillMapper.updateById(airWaybill);
    }

    /**
     * 安检申报列表
     * @param query 查询参数
     * @return 列表
     */
    @Override
    public PageQuery<List<SecurityVo>> securitySubmitList(SecurityQuery query) {
        List<SecurityVo> securityVos = waybillMapper.securitySubmitList(query);
        Map<String, SecurityVo> securityVoMaps = new HashMap<>();
        securityVos.forEach(e->{
            if(!securityVoMaps.containsKey(e.getWaybillCode())){
                securityVoMaps.put(e.getWaybillCode(), e);
            }else{
                SecurityVo securityVo = securityVoMaps.get(e.getWaybillCode());
                //这里依旧展示安检申报准备新增的数据 不关联主单表的数据
                if(securityVo.getType() == 1 && e.getType() == 0){
                    securityVoMaps.put(e.getWaybillCode(),e);
                }
            }
        });
        List<SecurityVo> list = new ArrayList<>(securityVoMaps.values());
        int total = list.size();
        int pageNum = query.getStartRow();
        int pageSize = query.getEndRow();
        int fromIndex = (pageNum - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, total);
        List<SecurityVo> pagedSecurityVos = list.subList(fromIndex, toIndex);
        return new PageQuery<List<SecurityVo>>(
                pagedSecurityVos,
                pageNum,
                pageSize,
                total
        );
    }

    /**
     * 保存生成的安检申报单
     * */
    @Override
    public int saveWaybillForSecurity(WaybillInfoVo query) {
        AirWaybill airWaybill = waybillMapper.selectById(query.getId());
        airWaybill.setSecurityUrl(query.getSecurityUrl());
        return waybillMapper.updateById(airWaybill);
    }

    /**
     * 根据id查询运单数据
     * @param id 查询参数
     * @return 安检申报返回参数
     */
    @Override
    public SecurityVo selectWaybillbyid(String id) {
        return waybillMapper.selectWaybillbyid(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int realCollect(Long id) {
        HzCollectWaybill collectWaybill = collectWaybillMapper.selectById(id);
        AirWaybill airWaybill = waybillMapper.selectById(collectWaybill.getWaybillId());
        if ("VIRTUAL".equals(collectWaybill.getStatus())){
            WaybillFee waybillFee = feeMapper.selectOne(new QueryWrapper<WaybillFee>()
                    .eq("waybill_code", airWaybill.getWaybillCode())
                    .eq("dept_id", airWaybill.getDeptId())
                    .eq("type", "DEP"));
            BaseAgent agent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", airWaybill.getDeptId()));
            List<CostDetail> details = costDetailMapper.depAutoSettleTask(airWaybill.getWaybillCode(), airWaybill.getDeptId());
            // 出港处置费
            List<String> exceptionValues = Arrays.asList("处置费", "电报费", "冷藏费");
            List<CostDetail> handleSumList = details.stream()
                    .filter(e -> !exceptionValues.contains(e.getChargeAbb()))
                    .collect(Collectors.toList());
            BigDecimal costSum = new BigDecimal(0);
            for (CostDetail detail : handleSumList) {
                detail.setType(1);
                detail.setIsSettle(1);
                detail.setFlightId(-1L);
                detail.setSettleDepWeight(airWaybill.getWeight());
                detail.setSettleDepQuantity(airWaybill.getQuantity());
                detail.setCreateTime(new Date());
                detail.setId(null);
                costSum = costSum.add(detail.getTotalCharge());
                costDetailMapper.insert(detail);
            }
            if (!CollectionUtils.isEmpty(handleSumList)){
                if (agent != null) {
                    if (agent.getSettleMethod() == 1) {
                        updateStatus(airWaybill.getWaybillCode(), airWaybill.getDeptId(), costSum, waybillFee);
                    }else if (agent.getSettleMethod() == 0) {
                        updateStatus(airWaybill.getWaybillCode(), airWaybill.getDeptId(), costSum, waybillFee);
                    } else {
                        if (agent.getPayMethod() == 0) {
                            updateStatus(airWaybill.getWaybillCode(), airWaybill.getDeptId(), costSum, waybillFee);
                        } else {
                            updateStatus(airWaybill.getWaybillCode(), airWaybill.getDeptId(), costSum, waybillFee);
                        }
                    }
                } else {
                    updateStatus(airWaybill.getWaybillCode(), airWaybill.getDeptId(), costSum, waybillFee);
                }
            }
            String token = getToken();
            try {
                senCable(airWaybill,collectWaybill.getQuantity(),collectWaybill.getWeight(),token);
            }catch (Exception e){
                log.error(CABLE_ERROR_MARKER, "运单"+airWaybill.getWaybillCode()+"发送FSU-FOH报文失败：" + e.getMessage());
            }
            try {
                sendFWBMsg(airWaybill, token);
            }catch (Exception e){
                log.error(CABLE_ERROR_MARKER, "运单"+airWaybill.getWaybillCode()+"发送FWB报文失败：" + e.getMessage());
            }
        }
        collectWaybill.setIsReal(1);
        collectWaybill.setStatus("REAL");

        airWaybill.setCollectStatus(3);
        waybillMapper.updateById(airWaybill);

        WaybillCollectData waybillCollectData = new WaybillCollectData();
        waybillCollectData.setType(1);
        waybillCollectData.setWaybillCode(collectWaybill.getWaybillCode().substring(4));
        waybillCollectData.setQuantity(collectWaybill.getQuantity());
        waybillCollectData.setWeight(collectWaybill.getWeight());
        securityProducer.sendOtherWaybill(waybillCollectData, collectWaybill.getWaybillCode(), "货站真实收运,推送收运数据");

        return collectWaybillMapper.updateById(collectWaybill);
    }

    /**
     * 查询该航班是否为宽体机
     * @param query 航班号 date 航班时间
     * @return
     * */
    @Override
    public Boolean getCraftType(AddQuery query) {
        String s = redisTemplate.opsForValue().get(WIDE_BODY);
        if(s == null || StringUtils.isEmpty(s)){
            return false;
        }
        List<String> collect = Arrays.stream(s.split(",")).collect(Collectors.toList());

        LoadFlightVo loadFlightVo = flightInfoMapper.selectLoadVo(query);
        if(loadFlightVo != null){
            String craftType = loadFlightVo.getCraftType();
            if(collect.contains(craftType)){
                return true;
            }
        }
        return false;
    }


    /**
     * 查看随附文件
     * @param waybillId 运单id
     * @return 文件
     */
    @Override
    public String viewTransportFile(Long waybillId) {
        AirWaybill airWaybill = waybillMapper.selectById(waybillId);
        String transportFile = airWaybill.getTransportFile();
        if(StringUtils.isNull(transportFile)){
            throw new CustomException("无随附文件");
        }
        return transportFile;
    }

    /**
     * 虚拟收运列表
     * @param query 查询参数
     * @return 列表
     */
    @Override
    public List<VirtualListVo> virtualList(VirtualListQuery query) {
        return waybillMapper.virtualList(query);
    }


    /**
     * 查询库存列表
     * @param query 查询条件
     * @return 结果
     */
    @Override
    public InventoryRequestVo selectList(InventoryQuery query) {
        InventoryRequestVo vo = new InventoryRequestVo();
        List<InventoryVo> vos = getVos(query);
        if (!CollectionUtils.isEmpty(vos)){
            vo.setTotalNum(vos.size());
            int sum = vos.stream().mapToInt(InventoryVo::getQuantity).sum();
            vo.setTotalQuantity(sum);
            BigDecimal reduce = vos.stream().map(InventoryVo::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setTotalWeight(reduce);
            vo.setVos(vos);
        }
        return vo;
    }

    /**
     * 导出库存列表
     * @param query 查询条件
     * @return 结果
     */
    @Override
    public List<InventoryVo> selectListByQuery(InventoryQuery query) {
        return  getVos(query);
    }

    /**
     * 获取库存数据
     * @param query 查询条件
     * @return 库存数据
     */
    @NotNull
    private List<InventoryVo> getVos(InventoryQuery query) {
        List<InventoryVo> inventoryVos = collectWaybillMapper.selectListByQuery(query);
        for (InventoryVo inventoryVo : inventoryVos) {
            int inventoryQuantity = inventoryVo.getCollectQuantity();
            BigDecimal inventoryWeight = inventoryVo.getCollectWeight();
            //这里是板箱运单和散舱数据都找了 要减去一下运单的件数重量
            List<FlightLoadWaybill> uldWaybills = loadUldWaybillMapper.selectLoadList(inventoryVo.getId());
            if (!CollectionUtils.isEmpty(uldWaybills)) {
                Map<Long, List<FlightLoadWaybill>> collect = uldWaybills.stream().collect(Collectors.groupingBy(FlightLoadWaybill::getFlightLoadId));
                for (Map.Entry<Long, List<FlightLoadWaybill>> longListEntry : collect.entrySet()) {
                    int sum = longListEntry.getValue().stream().mapToInt(FlightLoadWaybill::getQuantity).sum();
                    BigDecimal reduce = longListEntry.getValue().stream().map(FlightLoadWaybill::getWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    List<HzDepRepeatWeight> repeatWeights = repeatWeightMapper.selectList(new QueryWrapper<HzDepRepeatWeight>().eq("flight_load_id", longListEntry.getKey()));
                    if (!CollectionUtils.isEmpty(repeatWeights)) {
                        List<HzDepRepeatWeight> weightList = repeatWeights.stream()
                                .filter(e -> e.getWeight() == null)
                                .collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(weightList)) {
                            inventoryQuantity = inventoryQuantity - sum;
                            inventoryWeight = inventoryWeight.subtract(reduce);
                        }
                    }
                }
            }
            AirWaybill airWaybill = waybillMapper.selectByIdSome(inventoryVo.getWaybillId());
            if (airWaybill.getSwitchBill() == 1){
                inventoryQuantity = 0;
                inventoryWeight = BigDecimal.ZERO;
            }else {
                List<HzDepPullDown> pullDowns = pullDownMapper.selectWaybillList(airWaybill.getWaybillCode());
                if (!CollectionUtils.isEmpty(pullDowns)){
                    int pullDownQuantity = pullDowns.stream().mapToInt(HzDepPullDown::getQuantity).sum();
                    inventoryQuantity += pullDownQuantity;
                    BigDecimal pullDownWeight = pullDowns.stream().map(HzDepPullDown::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                    inventoryWeight = inventoryWeight.add(pullDownWeight);
                }
                if(inventoryQuantity < 0){
                    inventoryQuantity = 0;
                }
                if(inventoryWeight.compareTo(BigDecimal.ZERO) < 0){
                    inventoryWeight = BigDecimal.ZERO;
                }
            }
            inventoryVo.setInventoryQuantity(inventoryQuantity);
            inventoryVo.setInventoryWeight(inventoryWeight);
        }
        return inventoryVos;
    }

    /**
     * 取消运单
     * @param vo 取消参数
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String cancelWaybill(WaybillInfoVo vo) {
        List<HzCollectWaybill> collectWaybills = collectWaybillMapper.selectList(new QueryWrapper<HzCollectWaybill>()
                .eq("waybill_id", vo.getId()));
        Integer integer = wrongMapper.isOperateWrong(collectWaybills.get(0).getWaybillCode());
        if(integer > 0){
            throw new CustomException("该运单已操作不正常货邮,取消收运失败");
        }
        if (!CollectionUtils.isEmpty(collectWaybills)){
            int sum;
            BigDecimal reduce;
            TransferWaybill waybill = transferWaybillMapper.selectOne(new QueryWrapper<TransferWaybill>()
                    .eq("mawb_id", vo.getId()));
            List<HzCollectWaybill> virtual = collectWaybills.stream()
                    .filter(e -> "VIRTUAL".equals(e.getStatus()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(virtual)){
                List<Long> ids = virtual.stream().map(HzCollectWaybill::getId).collect(Collectors.toList());
                ids.forEach(e ->{
                    HzCollectWeight collectWeight = collectWeightMapper.selectOne(new QueryWrapper<HzCollectWeight>().eq("collect_id", e));
                    collectWeightMapper.deleteById(collectWeight.getId());
                });
                sum = virtual.stream().mapToInt(HzCollectWaybill::getQuantity).sum();
                reduce = virtual.stream().map(HzCollectWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (waybill != null){
                    //更新 交接单与运单 表的数据
                    Integer storeQuantity = waybill.getStoreQuantity() - sum;
                    BigDecimal storeWeight = waybill.getStoreWeight().subtract(reduce);
                    waybill.setStoreQuantity(storeQuantity);
                    waybill.setStoreWeight(storeWeight);
                    waybill.setIsCollect(0);
                    transferWaybillMapper.updateById(waybill);
                    //用主单号找到对应的运单收运总界面数据的id 把交接单的状态改为已提交
                    Long transferId = waybill.getTransferId();
                    Transfer transfer = transferMapper.selectById(transferId);
                    transfer.setStatus("SUBMIT");
                    transferMapper.updateById(transfer);
                }
                collectWaybillMapper.deleteBatchIds(ids);

            }
            List<HzCollectWaybill> real = collectWaybills.stream()
                    .filter(e -> "REAL".equals(e.getStatus()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(real)){
                List<Long> ids = real.stream().map(HzCollectWaybill::getId).collect(Collectors.toList());
                ids.forEach(e ->{
                    List<HzCollectWeight> weightList = collectWeightMapper.selectList(new QueryWrapper<HzCollectWeight>().eq("collect_id", e));
                    collectWeightMapper.deleteBatchIds(weightList.stream().map(HzCollectWeight::getId).collect(Collectors.toList()));
                });
                sum = real.stream().mapToInt(HzCollectWaybill::getQuantity).sum();
                reduce = real.stream().map(HzCollectWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (waybill != null){
                    //更新 交接单与运单 表的数据
                    Integer storeQuantity = waybill.getStoreQuantity() - sum;
                    BigDecimal storeWeight = waybill.getStoreWeight().subtract(reduce);
                    waybill.setStoreQuantity(storeQuantity);
                    waybill.setStoreWeight(storeWeight);
                    waybill.setIsCollect(0);
                    transferWaybillMapper.updateById(waybill);
                    //用主单号找到对应的运单收运总界面数据的id 把交接单的状态改为已提交
                    Long transferId = waybill.getTransferId();
                    Transfer transfer = transferMapper.selectById(transferId);
                    transfer.setStatus("SUBMIT");
                    transferMapper.updateById(transfer);
                }
                collectWaybillMapper.deleteBatchIds(ids);
                if(waybill != null){
                    //用主单号找到对应的运单收运总界面数据的id 把交接单的状态改为已提交
                    Long transferId = waybill.getTransferId();
                    Transfer transfer = transferMapper.selectById(transferId);
                    transfer.setStatus("SUBMIT");
                    transferMapper.updateById(transfer);
                }
            }
            disBoardMapper.delete(new QueryWrapper<HzDisBoard>().eq("waybill_id",vo.getId()));


            //根据主单id去修改all_air_waybill中的status字段,修改为已发送状态
            AirWaybill airWaybill = waybillMapper.selectById(vo.getId());
            airWaybill.setStatus("been_sent");
            airWaybill.setIsLoad(0);

            //全拉 + 代理人未处理 = 删除不正常货邮
            Set<Long> pullDownTotal = isPullDownTotal(airWaybill.getWaybillCode(), airWaybill.getQuantity(), airWaybill.getWeight());
            if(pullDownTotal.size() > 0){
                List<Wrong> wrongs = wrongMapper.selectBatchIds(pullDownTotal);
                if (!CollectionUtils.isEmpty(wrongs)){
                    List<HzDepPullDown> pullDownList = pullDownMapper.selectList(new QueryWrapper<HzDepPullDown>()
                            .in("id", wrongs.stream().map(Wrong::getPullId).toArray()));
                    if (pullDownList != null) {
                        List<Long> collect = pullDownList.stream().map(HzDepPullDown::getId).collect(Collectors.toList());
                        pullDownMapper.deleteBatchIds(collect);
                    }
                }
                wrongMapper.deleteBatchIds(pullDownTotal);
            }

            //运单日志
            WaybillLog waybillLog = waybillLogService.getWaybillLog(
                    airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                    airWaybill.getWeight() != null ? airWaybill.getWeight().toString() : null,
                    airWaybill.getQuantity() != null ? airWaybill.getQuantity().toString() : null,
                    airWaybill.getFlightNo1(), vo, "操作成功", 0, null, new Date(),
                    "取消收运", "DEP", null);
            waybillLogService.insertWaybillLog(waybillLog);

            waybillMapper.updateById(airWaybill);
        }
        return null;
    }

    /**
     * 特货代码列表
     * @return 结果
     */
    @Override
    public List<String> selectSpecialCargoCode() {
        return specialCodeMapper.selectCodeList();
    }

    /**
     * 根据特货代码得到中文描述
     * @param specialCargoCode 特货代码
     * @return 结果
     */
    @Override
    public String selectChineseDescriptionBySpecialCargoCode(String specialCargoCode) {
        String s = specialCodeMapper.selectChineseDescriptionBySpecialCargoCode(specialCargoCode);
        return StringUtils.isNull(s) ? "-" : s;
    }

    @Override
    public List<String> selectCategoryCodeList(String cargoCode) {
        return cargoCodeMapper.selectCategoryCodeList(cargoCode);
    }

    /**
     * 根据货品大类查询品名和中文名称
     * */
    @Override
    public List<BaseCargoCode> selectCargoCodeList(String categoryCode) {
        return cargoCodeMapper.selectCargoCodeListByCategoryCode(categoryCode);
    }

    @Override
    public String selectCargoNameByCategoryCode(String cargoCode) {
        return cargoCodeMapper.selectCargoNameByCategoryCode(cargoCode);
    }

    @Override
    public BaseCargoCode selectCargoInfo(String cargoCode) {
        return cargoCodeMapper.selectCargoInfo(cargoCode);
    }

    @Override
    public List<AllSecurityUrl> getHistoryList(Long id) {
        List<AllSecurityUrl> urlList = allSecurityUrlMapper.selectList(new QueryWrapper<AllSecurityUrl>()
                .eq("waybill_id", id));
        AllSecurityWaybill allSecurityWaybill = allSecurityWaybillMapper.selectById(id);
        if(allSecurityWaybill != null){
            List<AllSecurityUrl> urlList2 = allSecurityUrlMapper.selectList(new QueryWrapper<AllSecurityUrl>()
                    .eq("waybill_code", allSecurityWaybill.getWaybillCode()));
            urlList.addAll(urlList2);
        }
        Map<Long, AllSecurityUrl> urlListVoMaps = new HashMap<>();
        urlList.forEach(e->{
            if(!urlListVoMaps.containsKey(e.getId())){
                urlListVoMaps.put(e.getId(), e);
            }else{
                AllSecurityUrl urlVo = urlListVoMaps.get(e.getId());
                //这里依旧展示安检申报准备新增的数据 不关联主单表的数据
                if(urlVo.getWaybillId() != null && e.getWaybillId() == null){
                    urlListVoMaps.put(e.getId(),e);
                }
            }
        });
        return new ArrayList<>(urlListVoMaps.values());
    }

    @Override
    public int appRealCollect(Long id) {
        List<HzCollectWaybill> collectWaybills = collectWaybillMapper.selectList(new QueryWrapper<HzCollectWaybill>()
                .eq("waybill_id",id));
        if (!CollectionUtils.isEmpty(collectWaybills)){
            List<HzCollectWaybill> collect = collectWaybills.stream().filter(e -> e.getIsReal() == 0).collect(Collectors.toList());
            collect.forEach(e->{
                if ("VIRTUAL".equals(e.getStatus())){
                    AirWaybill airWaybill = waybillMapper.selectById(e.getWaybillId());
                    WaybillFee waybillFee = feeMapper.selectOne(new QueryWrapper<WaybillFee>()
                            .eq("waybill_code", airWaybill.getWaybillCode())
                            .eq("dept_id", airWaybill.getDeptId())
                            .eq("type", "DEP"));
                    BaseAgent agent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", airWaybill.getDeptId()));
                    List<CostDetail> details = costDetailMapper.depAutoSettleTask(airWaybill.getWaybillCode(), airWaybill.getDeptId());
                    // 出港处置费
                    List<String> exceptionValues = Arrays.asList("处置费", "电报费", "冷藏费");
                    List<CostDetail> handleSumList = details.stream()
                            .filter(detail -> !exceptionValues.contains(detail.getChargeAbb()))
                            .collect(Collectors.toList());
                    BigDecimal costSum = new BigDecimal(0);
                    for (CostDetail detail : handleSumList) {
                        detail.setType(1);
                        detail.setIsSettle(1);
                        detail.setFlightId(-1L);
                        detail.setSettleDepQuantity(airWaybill.getQuantity());
                        detail.setSettleDepWeight(airWaybill.getWeight());
                        detail.setCreateTime(new Date());
                        detail.setId(null);
                        costSum = costSum.add(detail.getTotalCharge());
                        costDetailMapper.insert(detail);
                    }
                    if (agent != null) {
                        if (agent.getSettleMethod() == 1) {
                            updateStatus(airWaybill.getWaybillCode(), airWaybill.getDeptId(), costSum, waybillFee);
                        }else if (agent.getSettleMethod() == 0) {
                            updateStatus(airWaybill.getWaybillCode(), airWaybill.getDeptId(), costSum, waybillFee);
                        } else {
                            if (agent.getPayMethod() == 0) {
                                updateStatus(airWaybill.getWaybillCode(), airWaybill.getDeptId(), costSum, waybillFee);
                            } else {
                                updateStatus(airWaybill.getWaybillCode(), airWaybill.getDeptId(), costSum, waybillFee);
                            }
                        }
                    } else {
                        updateStatus(airWaybill.getWaybillCode(), airWaybill.getDeptId(), costSum, waybillFee);
                    }
                    airWaybill.setRefund(new BigDecimal(0));
                    airWaybill.setSettleTime(new Date());
                    airWaybill.setUpdateTime(new Date());
                    airWaybill.setCollectStatus(3);
                    waybillMapper.updateById(airWaybill);
                }
                e.setIsReal(1);
                e.setStatus("REAL");
                collectWaybillMapper.updateById(e);
            });
        }
        return 1;
    }

    @Override
    public AppCollectVo selectCollectWaybillAppList(HzTransferQuery query) {
        AppCollectVo vo = new AppCollectVo();
        List<HzTransferVo> hzTransferVos = transferMapper.selectAppList(query);
        hzTransferVos.forEach(e->{
            if (e.getType().equals(1)){
                e.setWaybillNum(1);
            }else {
                Integer count = transferWaybillMapper.selectCount(new QueryWrapper<TransferWaybill>().eq("transfer_id", e.getId()));
                e.setWaybillNum(count);
            }
            switch (e.getStatusStr()){
                case "SUBMIT":
                    e.setStatus("提交（未入库）");
                    break;
                case "HANDLED":
                    e.setStatus("已处理");
                    break;
                default:
                    e.setStatus("无");
                    break;
            }
            Long transferId = e.getId();
            List<TransferWaybill> transferList = transferWaybillMapper.selectList(new QueryWrapper<TransferWaybill>()
                    .eq("transfer_id", transferId));
            transferList.forEach(t->{
                List<HzCollectWaybill> waybillList = collectWaybillMapper.selectList(new QueryWrapper<HzCollectWaybill>().eq("waybill_id", t.getMawbId()));
                if(waybillList.size() > 0){
                    List<String> collect = waybillList.stream().map(HzCollectWaybill::getStatus).collect(Collectors.toList());
                    if(collect.contains("VIRTUAL")){
                        e.setStatus("提交（未入库）");
                        e.setStatusStr("SUBMIT");
                    }
                }
                AirWaybill airWaybill = waybillMapper.selectById(t.getMawbId());
                if(airWaybill!=null){
                    t.setWaybillQuantity(airWaybill.getQuantity());
                    t.setWaybillWeight(airWaybill.getWeight());
                }
            });
            //计算这个交接单下总的件数重量
            Integer quantity = transferList.stream().map(TransferWaybill::getWaybillQuantity).filter(Objects::nonNull).reduce(0, Integer::sum);
            BigDecimal weight = transferList.stream().map(TransferWaybill::getWaybillWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            e.setTotalQuantity(quantity);
            e.setTotalWeight(weight);
        });
        if (!CollectionUtils.isEmpty(hzTransferVos)){
            vo.setTotalCount(hzTransferVos.size());
            vo.setTotalList(hzTransferVos);
            vo.setTotalQuantity(hzTransferVos.stream().map(HzTransferVo::getTotalQuantity).reduce(0, Integer::sum));
            vo.setTotalWeight(hzTransferVos.stream().map(HzTransferVo::getTotalWeight).reduce(BigDecimal.ZERO, BigDecimal::add));
            List<HzTransferVo> submitList = hzTransferVos.stream().filter(e -> "SUBMIT".equals(e.getStatusStr())).collect(Collectors.toList());
            vo.setSubmitCount(submitList.size());
            vo.setSubmitList(submitList);
            vo.setSubmitQuantity(submitList.stream().map(HzTransferVo::getTotalQuantity).reduce(0, Integer::sum));
            vo.setSubmitWeight(submitList.stream().map(HzTransferVo::getTotalWeight).reduce(BigDecimal.ZERO, BigDecimal::add));
            List<HzTransferVo> handledList = hzTransferVos.stream().filter(e -> "HANDLED".equals(e.getStatusStr())).collect(Collectors.toList());
            vo.setHandledCount(handledList.size());
            vo.setHandleList(handledList);
            vo.setHandledQuantity(handledList.stream().map(HzTransferVo::getTotalQuantity).reduce(0, Integer::sum));
            vo.setHandledWeight(handledList.stream().map(HzTransferVo::getTotalWeight).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        return vo;
    }


    /**
     * 查看修改详情
     * @param id 收运id
     * @return 详情
     */
    @Override
    public CollectWaybillVo editInfo(Long id) {
        CollectWaybillVo vo = collectWaybillMapper.selectEditInfoById(id);
        if (vo != null){
            List<HzCollectWeight> list = collectWeightMapper.selectList(new QueryWrapper<HzCollectWeight>().eq("collect_id",vo.getId()));
            if (!CollectionUtils.isEmpty(list)){
                vo.setWeights(list);
            }
        }
        return vo;
    }

    /**
     * 修改
     * @param vo 修改数据
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String edit(CollectWaybillVo vo) {
        StringBuilder stringBuilder = new StringBuilder();
        HzCollectWaybill hzCollectWaybill = collectWaybillMapper.selectById(vo.getId());
        AirWaybill airWaybill = waybillMapper.selectById(vo.getWaybillId());
        Integer voQuantity = vo.getQuantity();
        BigDecimal voWeight = vo.getWeight();
        if (airWaybill.getQuantity() < voQuantity){
            stringBuilder.append("quantity");
        }
        if((airWaybill.getWeight().compareTo(voWeight)) < 0){
            throw new CustomException("该运单收运重量超过运单开单重量");
        }
        List<HzCollectWaybill> collectWaybills = collectWaybillMapper.selectList(new QueryWrapper<HzCollectWaybill>()
                .eq("waybill_id", vo.getWaybillId()));
        if (!CollectionUtils.isEmpty(collectWaybills)){
            int quantity = collectWaybills.stream().mapToInt(HzCollectWaybill::getQuantity).sum();
            //这里拿到了收运记录里面总件数和 应该减去修改之前的件数 再加上修改之后的件数 再与运单总件数比对
            int totalCollectQuantity = quantity - hzCollectWaybill.getQuantity() + vo.getQuantity();
            if (airWaybill.getQuantity() < totalCollectQuantity){
                stringBuilder.append("quantity");
            }
            BigDecimal weight = collectWaybills.stream().map(HzCollectWaybill::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            voWeight = voWeight.subtract(hzCollectWaybill.getWeight()).add(weight);
            if((airWaybill.getWeight().compareTo(voWeight)) < 0){
                throw new CustomException("该运单收运重量超过运单开单重量");
            }
        }
        collectWeightMapper.delete(new QueryWrapper<HzCollectWeight>()
                .eq("collect_id",vo.getId()));
        //如果该收运记录下面的过磅记录为空 即没有板车收运 也没有散舱收运;就把这条收运记录也删掉
        if(CollectionUtils.isEmpty(vo.getWeights())){
            collectWaybillMapper.deleteById(vo.getId());
            return stringBuilder.toString();
        }

        BigDecimal abs = voWeight.subtract(airWaybill.getWeight()).abs();
        BigDecimal percentage = new BigDecimal(0);
        if (airWaybill.getWeight() != null && airWaybill.getWeight().compareTo(BigDecimal.ZERO) != 0){
            percentage = abs.divide(airWaybill.getWeight(), 4, RoundingMode.DOWN).multiply(new BigDecimal(100));
        }
        if ((abs.compareTo(new BigDecimal(20)) <= 0 && percentage.compareTo(new BigDecimal(3)) <= 0)
                || (abs.compareTo(new BigDecimal(5)) <= 0 && percentage.compareTo(new BigDecimal(3)) >= 0)){
            hzCollectWaybill.setWeight(airWaybill.getWeight()
                    .subtract(voWeight != null ? voWeight : BigDecimal.ZERO)
                    .add(vo.getWeight() != null ? vo.getWeight() : BigDecimal.ZERO));
            if(!CollectionUtils.isEmpty(vo.getWeights())){
                HzCollectWeight remove = vo.getWeights().remove(vo.getWeights().size()-1);
                BigDecimal reduce = vo.getWeights().stream().map(HzCollectWeight::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                remove.setWeight(hzCollectWaybill.getWeight().subtract(reduce));
                vo.getWeights().add(remove);
            }
        }else{
            hzCollectWaybill.setWeight(vo.getWeight());
        }

        for (HzCollectWeight weight : vo.getWeights()) {
            weight.setCollectId(vo.getId());
            weight.setWeightTime(new Date());
            weight.setDesPort(airWaybill.getDesPort());
            BigDecimal gb = weight.getWeight() == null ? new BigDecimal(0) : weight.getWeight();
            BigDecimal bzz = weight.getBoardWeight() == null ? new BigDecimal(0) : weight.getBoardWeight();
            BigDecimal db = weight.getPlateWeight() == null ? new BigDecimal(0) : weight.getPlateWeight();
            weight.setTotalWeight(gb.add(bzz).add(db));
            collectWeightMapper.insert(weight);
        }
        hzCollectWaybill.setQuantity(vo.getQuantity());
//        hzCollectWaybill.setWeight(vo.getWeight());
        hzCollectWaybill.setUld(vo.getUld());
        collectWaybillMapper.updateById(hzCollectWaybill);
        return stringBuilder.toString();
    }

    /**
     * 保存uld
     * @param uldForSave uldForSave数据
     * @return 结果
     */
    @Override
    public int saveUld(BaseCargoUldForSave uldForSave) {
        AirWaybill airWaybill = waybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                .eq("waybill_code", uldForSave.getWaybillCode())
                .eq("is_del", 0)
                .eq("type", "DEP"));
        if ("CAR".equals(uldForSave.getType())){
            BaseFlatbedTruck truck = truckMapper.selectOne(new QueryWrapper<BaseFlatbedTruck>().eq("code", uldForSave.getBaseCargoUld().getCode()).eq("is_del", 0));
            if (truck == null){
                BaseFlatbedTruck flatbedTruck = new BaseFlatbedTruck();
                BeanUtils.copyProperties(uldForSave.getBaseCargoUld(),flatbedTruck);
                flatbedTruck.setType("板车");
                flatbedTruck.setStatus("正常");
                flatbedTruck.setCreateTime(new Date());
                flatbedTruck.setCreateBy(SecurityUtils.getUsername());
                truckMapper.insert(flatbedTruck);
            }else {
                // 正在使用的板车判断
                if (truck.getUseStatus() == 1){
                    HzCollectWaybill collectWaybill = collectWaybillMapper.selectOne(new QueryWrapper<HzCollectWaybill>()
                            .eq("waybill_code",uldForSave.getWaybillCode())
                            .eq("collect_time",truck.getUseTime())
                            .eq("uld", uldForSave.getType() + uldForSave.getCode()));
                    if (collectWaybill == null){
                        List<HzCollectWaybill> waybill = collectWaybillMapper.selectList(new QueryWrapper<HzCollectWaybill>()
                                .eq("collect_time",truck.getUseTime())
                                .eq("uld", uldForSave.getType() + uldForSave.getBaseCargoUld().getCode()));
                        if (!CollectionUtils.isEmpty(waybill)){
                            AirWaybill oldWaybill = waybillMapper.selectById(waybill.get(0).getWaybillId());
                            //航班号航班日期目的地不一致,当前板箱上的运单放散舱 然后修改板车状态为未使用
                            if(!Objects.equals(oldWaybill.getFlightNo1(),airWaybill.getFlightNo1())
                                    || !Objects.equals(oldWaybill.getFlightDate1(),airWaybill.getFlightDate1())
                                    || !Objects.equals(oldWaybill.getDesPort(), airWaybill.getDesPort())
                                    || !Objects.equals(oldWaybill.getCarrier1(),airWaybill.getCarrier1())){
                                judgeStatus(waybill, truck.getLcStatus());
                            }
                        }
                    }
                }
            }
        }else {
            BaseCargoUld uld = uldMapper.selectOne(new QueryWrapper<BaseCargoUld>()
                    .eq("code", uldForSave.getBaseCargoUld().getCode())
                    .eq("type",uldForSave.getType())
                    .eq("is_del", 0));
            if (uld == null){
                BaseCargoUld baseCargoUld = new BaseCargoUld();
                BeanUtils.copyProperties(uldForSave.getBaseCargoUld(),baseCargoUld);
                baseCargoUld.setCreateTime(new Date());
                baseCargoUld.setCreateBy(SecurityUtils.getUsername());
                uldMapper.insert(baseCargoUld);
            }else {
                // 正在使用的板车判断
                if (uld.getUseStatus() == 1){
                    HzCollectWaybill collectWaybill = collectWaybillMapper.selectOne(new QueryWrapper<HzCollectWaybill>()
                            .eq("waybill_code",uldForSave.getWaybillCode())
                            .eq("collect_time",uld.getUseTime())
                            .eq("uld", uldForSave.getType() + uldForSave.getCode()));
                    if (collectWaybill == null){
                        List<HzCollectWaybill> waybill = collectWaybillMapper.selectList(new QueryWrapper<HzCollectWaybill>()
                                .eq("collect_time",uld.getUseTime())
                                .eq("uld", uldForSave.getType() + uldForSave.getBaseCargoUld().getCode()));
                        if (!CollectionUtils.isEmpty(waybill)){
                            AirWaybill oldWaybill = waybillMapper.selectById(waybill.get(0).getWaybillId());
                            //航班号航班日期目的地不一致,当前板箱上的运单放散舱 然后修改板车状态为未使用
                            if(!oldWaybill.getFlightNo1().equals(airWaybill.getFlightNo1())
                                    || !oldWaybill.getFlightDate1().equals(airWaybill.getFlightDate1())
                                    || !oldWaybill.getDesPort().equals(airWaybill.getDesPort())
                                    || !Objects.equals(oldWaybill.getCarrier1(),airWaybill.getCarrier1())){
                                judgeStatus(waybill, uld.getStatus());
                            }
                        }
                    }
                }
            }
        }
        return 1;
    }

    private int isCargoCodeMatch(HzChargeIrRelation hzChargeRule, String waybillCargoCode) {
        if (StringUtils.isEmpty(hzChargeRule.getCargoName())) {
            return 1;
        }else if (StringUtils.isEmpty(hzChargeRule.getCategory())){
            if (hzChargeRule.getCargoName().contains(waybillCargoCode)){
                return 1;
            }
            return 0;
        }
        List<IrRelationVo> relationVos = Arrays.stream(hzChargeRule.getCargoName().split(","))
                .map(cargoCodeMapper::selectIrByName)
                .collect(Collectors.toList());

        List<IrRelationVo> cargoNames = Arrays.stream(hzChargeRule.getCategory().split(","))
                .flatMap(category -> cargoCodeMapper.selectListByCategory(Collections.singletonList(category)).stream())
                .collect(Collectors.toList());

        Map<String, List<IrRelationVo>> relationVosMap = relationVos.stream()
                .collect(Collectors.groupingBy(IrRelationVo::getCategoryCode));

        List<IrRelationVo> combinedList = new ArrayList<>();
        Set<String> addedCodes = new HashSet<>();
        for (IrRelationVo vo : cargoNames) {
            String code = vo.getCategoryCode();
            if (relationVosMap.containsKey(code) && !addedCodes.contains(code)) {
                combinedList.addAll(relationVosMap.get(code));
                addedCodes.add(code);
            } else if (!addedCodes.contains(code)) {
                combinedList.add(vo);
            }
        }
        List<String> collect1 = combinedList.stream().map(IrRelationVo::getCode).collect(Collectors.toList());
        if (collect1.stream().anyMatch(cargoCode -> cargoCode.equals(waybillCargoCode))) {
            String category = hzChargeRule.getCategory();
            StringBuilder stringBuffer = new StringBuilder();
            if(category != null){
                String[] split = category.split(",");
                for (String categoryCode:split) {
                    List<BaseCargoCode> cargoCodeList = cargoCodeMapper.selectList(new QueryWrapper<>(new BaseCargoCode())
                            .eq("category_code", categoryCode)
                            .eq("is_del",0));
                    if(cargoCodeList!=null && cargoCodeList.size() > 0){
                        for (BaseCargoCode e:cargoCodeList) {
                            stringBuffer.append(e.getCode());
                        }
                    }
                }
            }else{
                return 1;
            }
            if (stringBuffer.toString().contains(waybillCargoCode)) {
                return 2;
            } else {
                return 1;
            }
        }
        return 0;
    }

    /**
     * 流程状态判断
     * @param waybill 收运集合
     * @param lcStatus 流程状态
     */
    private void judgeStatus(List<HzCollectWaybill> waybill, String lcStatus) {
        switch (lcStatus) {
            case "been_collect":
                for (HzCollectWaybill hzCollectWaybill : waybill) {
                    hzCollectWaybill.setUld("");
                    collectWaybillMapper.updateById(hzCollectWaybill);
                    List<HzCollectWeight> list = collectWeightMapper.selectList(new QueryWrapper<HzCollectWeight>().eq("collect_id", hzCollectWaybill.getId()));
                    for (HzCollectWeight weight : list) {
                        weight.setUld("");
                        collectWeightMapper.updateById(weight);
                    }
                }
                break;
            case "been_pre":
                for (HzCollectWaybill hzCollectWaybill : waybill) {
                    List<FlightLoadUldWaybill> uldWaybills = loadUldWaybillMapper.selectList(new QueryWrapper<FlightLoadUldWaybill>().eq("collect_id", hzCollectWaybill.getId()));
                    for (FlightLoadUldWaybill uldWaybill : uldWaybills) {
                        FlightLoadUld flightLoadUld = loadUldMapper.selectById(uldWaybill.getLoadUldId());
                        FlightLoadWaybill loadWaybill = new FlightLoadWaybill();
                        BeanUtils.copyProperties(uldWaybill, loadWaybill);
                        loadWaybill.setId(null);
                        loadWaybill.setFlightLoadId(flightLoadUld.getFlightLoadId());
                        loadWaybillMapper.insert(loadWaybill);
                        loadUldWaybillMapper.deleteById(uldWaybill.getId());
                    }
                }
                break;
            case "been_group":
                for (HzCollectWaybill hzCollectWaybill : waybill) {
                    AirWaybill airWaybill1 = waybillMapper.selectById(hzCollectWaybill.getWaybillId());
                    HzDepGroupUldWaybill groupUldWaybill = groupUldWaybillMapper.selectOne(new QueryWrapper<HzDepGroupUldWaybill>().eq("waybill_code", airWaybill1.getWaybillCode()));
                    if (groupUldWaybill != null) {
                        HzDepGroupUld groupUld = groupUldMapper.selectById(groupUldWaybill.getGroupUldId());
                        HzDepGroupWaybill groupWaybill = new HzDepGroupWaybill();
                        BeanUtils.copyProperties(groupUldWaybill, groupWaybill);
                        groupWaybill.setId(null);
                        groupWaybill.setFlightLoadId(groupUld.getFlightLoadId());
                        groupWaybillMapper.insert(groupWaybill);
                        groupUldWaybillMapper.deleteById(groupUldWaybill.getId());
                    }
                }
                break;
            default:
                throw new CustomException("无当前运单状态");
        }
    }

    /**
     * 查询当前uld使用状态
     * @param baseCargoUld uld数据
     * @return 是否
     */
    @Override
    public boolean selectStatus(BaseCargoUldForSave baseCargoUld) {
        if (baseCargoUld.getType() == null){
            return false;
        }
        if ("CAR".equals(baseCargoUld.getType())){
            BaseFlatbedTruck truck1 = truckMapper.selectOne(new QueryWrapper<BaseFlatbedTruck>()
                    .eq("code", baseCargoUld.getBaseCargoUld().getCode())
                    .eq("is_del",0));
            if (truck1 != null){
                return truck1.getUseStatus() == 1 && truck1.getDesPort() != null;
            }
        }else {
            BaseCargoUld cargoUld = uldMapper.selectOne(new QueryWrapper<BaseCargoUld>()
                    .eq("type", baseCargoUld.getType())
                    .eq("code", baseCargoUld.getBaseCargoUld().getCode()));
            if (cargoUld != null){
                return "OUT".equals(cargoUld.getInOut()) && cargoUld.getDesPort() != null;
            }
        }
        return false;
    }

    /**
     * 消息时运异常通知
     * @param weight 运单重量
     * @param waybillCode 运单号
     */
    @Async
    public void sendMessage(BigDecimal weight, String waybillCode,String agentCompany , BigDecimal collectWeight){
        String message = "    运单"+waybillCode+"收运异常  \n" +
                "警告！运单"+ waybillCode +" 制单重量："+ weight +"，" +
                "收运重量："+ collectWeight +"，收运与制单重量不符。";
        SocketMessageVo vo = new SocketMessageVo();
        vo.setMessage(message);
        vo.setType(1);
        vo.setDeptId(sysDeptMapper.selectDeptIdByDeptName(agentCompany));

        Message mes = new Message();
        mes.setContent(message);
        mes.setCreateTime(new Date());
        mes.setIsHandle(0);
        mes.setPostId(SecurityUtils.getDeptId());
        mes.setTitle("警告提醒");
        messageMapper.insert(mes);
        waybillMessageProducer.send(vo);
    }

    @Override
    public int appUpdateStorageTransportNotes(WaybillInfoVo vo) {
        HttpServletResponse response = ServletUtils.getResponse();
        WaybillLog waybillLog = new WaybillLog();
        try{
        AirWaybill airWaybill = waybillMapper.selectById(vo.getId());
        airWaybill.setStorageTransportNotes(vo.getStorageTransportNotes());

            waybillLog = waybillLogService.getWaybillLog(
                    airWaybill.getWaybillCode(), 1, SecurityUtils.getNickName(),
                    airWaybill.getWeight().toString(), airWaybill.getQuantity().toString(), airWaybill.getFlightNo1(),
                    vo, null, 0, null, new Date(),
                    "运单"+airWaybill.getWaybillCode()+"修改储运事项",
                    airWaybill.getType(), null);
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作成功" +  "," +
                            "code:" + response.getStatus() + "," +
                            "data:"));
            return waybillMapper.updateById(airWaybill);
        }catch (Exception e){
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" +  "," +
                            "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        }finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }

    @Override
    public int cancelPay(Long waybillId) {
        HttpServletResponse response = ServletUtils.getResponse();
        WaybillLog waybillLog = new WaybillLog();
        try {
            List<HzCollectWaybill> collectWaybills = collectWaybillMapper.selectList(new QueryWrapper<HzCollectWaybill>()
                    .eq("waybill_id", waybillId));
            if (!CollectionUtils.isEmpty(collectWaybills)) {
                throw new CustomException("运单已经收运不能取消支付");
            }
            AirWaybill airWaybill = waybillMapper.selectById(waybillId);
            BaseAgent agent = baseAgentMapper.selectOne(new QueryWrapper<BaseAgent>().eq("dept_id", airWaybill.getDeptId()));
            BigDecimal costSum = new BigDecimal(0);
            List<CostDetail> details = costDetailMapper.selectPayOrSettleList(airWaybill.getWaybillCode(), 0, 1, airWaybill.getDeptId());
            if (!CollectionUtils.isEmpty(details)) {
                costSum = details.stream().map(CostDetail::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            for (CostDetail detail : details) {
                detail.setIsSettle(2);
                costDetailMapper.updateById(detail);
            }
            if (agent != null && agent.getSettleMethod() == 1) {
                BigDecimal balance = agent.getBalance() == null ? new BigDecimal(0) : agent.getBalance();
                BigDecimal subtract = balance.add(costSum);
                agent.setBalance(subtract);
                baseAgentMapper.updateBaseAgent(agent);
                BaseBalance baseBalance = new BaseBalance();
                baseBalance.setAgentId(agent.getId());
                baseBalance.setBalance(agent.getBalance());
                baseBalance.setType("增加余额");
                baseBalance.setCreateTime(new Date());
                baseBalance.setCreateBy(SecurityUtils.getNickName());
                // todo 流水号需从银联支付接口获取
                //baseBalance.setSerialNo();
                baseBalance.setTradeMoney(costSum);
                baseBalance.setWaybillCode(airWaybill.getWaybillCode());
                baseBalance.setRemark("取消支付退款");
                baseBalanceMapper.insertBaseBalance(baseBalance);
            }
            airWaybill.setPayStatus(14);
            airWaybill.setRefund(costSum);
            if("AWBA".equals(airWaybill.getWaybillType())){
                airWaybill.setSecuritySubmitWl(0);
                airWaybill.setSecuritySubmit(0);
            }
            waybillLog = waybillLogService.getWaybillLog(
                    airWaybill.getWaybillCode(), 0, SecurityUtils.getNickName(),
                    airWaybill.getWeight().toString(), airWaybill.getQuantity().toString(), airWaybill.getFlightNo1(),
                    airWaybill.getWaybillCode(), null, 0, null, new Date(),
                    "运单取消支付(代理人操作)", "DEP", null);
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作成功" +  "," +
                            "code:" + response.getStatus() + "," +
                            "data:" + 1));
            return waybillMapper.updateById(airWaybill);
        }catch(Exception e){
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" +  "," +
                            "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        }finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }

    private void senCable(AirWaybill airWaybill,Integer quantity,BigDecimal weight, String token){
        List<HzCableAddress> addressList = cableAddressMapper.selectAddressByAirportCode(Collections.singletonList(airWaybill.getDesPort()));
        StringBuilder sb = new StringBuilder();
        String sendAddress = sysConfigMapper.selectValue("dep.sendAddress");
        MsgJsonVO msgJsonVO = new MsgJsonVO();
        if (StringUtils.isNotEmpty(sendAddress)) {
            msgJsonVO.setOrigin(sendAddress.split(","));
        }else {
            msgJsonVO.setOrigin(new String[]{"KWEFDCN"});
        }
        msgJsonVO.setCarrier(airWaybill.getCarrier1());
        msgJsonVO.setDepartureStation(airWaybill.getSourcePort());
        msgJsonVO.setMsgType("FSU");
        msgJsonVO.setMsgVersion("12");
        msgJsonVO.setNextStation(airWaybill.getDesPort());
        msgJsonVO.setOperationNode("FOH");
        msgJsonVO.setOperationStation(airWaybill.getSourcePort());
        msgJsonVO.setPriority(new String[]{"QD"});
        msgJsonVO.setSourceId("44162409105767715");
        msgJsonVO.setUniqueId("44162409105767715");
        FSUJsonVO fsuJsonVO = new FSUJsonVO();
        if(airWaybill.getWaybillCode().startsWith("AWBA")){
            msgJsonVO.setWaybillPrefix(airWaybill.getWaybillCode().substring(4,7));
            String substring = airWaybill.getWaybillCode().substring(4);
            StringBuilder stringBuilder1 = new StringBuilder(substring);
            fsuJsonVO.setMawbNo(stringBuilder1.insert(3, "-").toString());
        }else{
            String substring = airWaybill.getWaybillCode().substring(4);
            StringBuilder stringBuilder1 = new StringBuilder(substring);
            fsuJsonVO.setMawbNo(stringBuilder1.insert(2, "-").toString());
        }
        fsuJsonVO.setDepAirport(airWaybill.getSourcePort());
        fsuJsonVO.setDesAirport(airWaybill.getDesPort());
        fsuJsonVO.setPieces(quantity.toString());
        if (!airWaybill.getQuantity().equals(quantity)){
            fsuJsonVO.setShipmentDescriptionCode("P");
            fsuJsonVO.setTotalPieces(airWaybill.getQuantity().toString());
        }else {
            fsuJsonVO.setShipmentDescriptionCode("T");
        }
        List<StatusDetails> statusDetails = new ArrayList<>();
        StatusDetails statusDetail = new StatusDetails();
        statusDetail.setMovementAirport(airWaybill.getSourcePort());
        statusDetail.setMovementDate(LocalDateTime.now().format(DATE_FORMATTER));
        statusDetail.setMovementTime(LocalDateTime.now().format(TIME_FORMATTER));
        statusDetail.setPieces(quantity.toString());
        statusDetail.setShipmentDescriptionCode(fsuJsonVO.getShipmentDescriptionCode());
        statusDetail.setStatusCode("FOH");
        statusDetail.setWeight(weight.toString());
        statusDetail.setWeightUnit("K");
        statusDetails.add(statusDetail);
        fsuJsonVO.setStatusDetails(statusDetails);
        fsuJsonVO.setWeight(weight.toString());
        fsuJsonVO.setWeightUnit("K");
        msgJsonVO.setMsgJson(JSON.toJSONString(fsuJsonVO));
        Map<String, List<HzCableAddress>> addressMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(addressList)){
            addressMap = addressList.stream().collect(Collectors.groupingBy(HzCableAddress::getAirportCode));
        }
        List<HzCableAddress> cableAddresses = addressMap.get(airWaybill.getDesPort());
        setInteractionType(cableAddresses,msgJsonVO);
        restExchange(msgJsonVO,token,sb);
        insertCableAndSendMsg(msgJsonVO,airWaybill,token,sb);
    }

    private void sendFWBMsg(AirWaybill airWaybill, String token){
        List<HzCableAddress> addressList = cableAddressMapper.selectAddressByAirportCode(Collections.singletonList(airWaybill.getDesPort()));
        StringBuilder sb = new StringBuilder();
        String sendAddress = sysConfigMapper.selectValue("dep.sendAddress");
        MsgJsonVO msgJsonVO = new MsgJsonVO();
        if (StringUtils.isNotEmpty(sendAddress)) {
            msgJsonVO.setOrigin(sendAddress.split(","));
        }else {
            msgJsonVO.setOrigin(new String[]{"KWEFDCN"});
        }
        msgJsonVO.setCarrier(airWaybill.getCarrier1());
        msgJsonVO.setDepartureStation(airWaybill.getSourcePort());
        msgJsonVO.setMsgType("FWB");
        msgJsonVO.setMsgVersion("12");
        msgJsonVO.setNextStation(airWaybill.getDesPort());
        msgJsonVO.setPriority(new String[]{"QD"});
        msgJsonVO.setSourceId("1911659653490081794");
        msgJsonVO.setUniqueId("1911659653490081794");
        msgJsonVO.setWaybillPrefix(airWaybill.getWaybillCode().substring(4,7));
        FWBJsonVO fwbJsonVO = new FWBJsonVO();
        fwbJsonVO.setAwbType(airWaybill.getWaybillType().substring(0,4));
        fwbJsonVO.setCarrier(airWaybill.getCarrier1());
        if (airWaybill.getRatePerKg() != null){
            fwbJsonVO.setChargeOrRate(airWaybill.getRatePerKg().toString());
        }
        fwbJsonVO.setChargePieces(airWaybill.getQuantity().toString());
        fwbJsonVO.setChargeWeight(airWaybill.getChargeWeight().toString());
        fwbJsonVO.setChargegrossWeight(airWaybill.getChargeWeight().toString());
        fwbJsonVO.setChargegrossWeightcode("K");
        fwbJsonVO.setCommodityItemNo(airWaybill.getCargoCode());
        fwbJsonVO.setConsigneeAddr(airWaybill.getConsignAddress() == null ? airWaybill.getDesPort() : airWaybill.getConsignAddress());
        fwbJsonVO.setConsigneeCity(airWaybill.getConsignRegion() == null ? airWaybill.getDesPort() : airWaybill.getConsignRegion());
        fwbJsonVO.setConsigneeCountry("CN");
        fwbJsonVO.setConsigneeName(airWaybill.getConsign());
        fwbJsonVO.setConsigneeTel(airWaybill.getConsignPhone());
        fwbJsonVO.setCurrency("CNY");
        fwbJsonVO.setDepAirport(airWaybill.getSourcePort());
        fwbJsonVO.setDesAirport(airWaybill.getDesPort());
        fwbJsonVO.setDestination(airWaybill.getDesPort());
        fwbJsonVO.setExecuteDate(DATE_FORMATTER.format(LocalDateTime.now()));
        fwbJsonVO.setExecutePlace(airWaybill.getSourcePort());
        fwbJsonVO.setFlightDate(DATE_FORMAT.format(airWaybill.getFlightDate1()));
        fwbJsonVO.setFlightNo(airWaybill.getFlightNo1());
        fwbJsonVO.setGoodsDesc(airWaybill.getCargoName());
        fwbJsonVO.setMawbId(airWaybill.getId().toString());
        fwbJsonVO.setOther("P");
        fwbJsonVO.setDeclaredValueCarriage("NVD");
        fwbJsonVO.setDeclaredValueCustoms("NCV");
        fwbJsonVO.setInsuranceAmount("XXX");
        if(airWaybill.getWaybillCode().startsWith("AWBA")){
            String substring = airWaybill.getWaybillCode().substring(4);
            StringBuilder stringBuilder = new StringBuilder(substring);
            fwbJsonVO.setMawbNo(stringBuilder.insert(3, "-").toString());
        }else{
            String substring = airWaybill.getWaybillCode().substring(4);
            StringBuilder stringBuilder = new StringBuilder(substring);
            fwbJsonVO.setMawbNo(stringBuilder.insert(2, "-").toString());
        }
        fwbJsonVO.setPieces(airWaybill.getQuantity().toString());
        fwbJsonVO.setPrepaid(airWaybill.getPayMoney().toString());
        fwbJsonVO.setRateClass(airWaybill.getRateType());
        if (airWaybill.getVolume() != null){
            fwbJsonVO.setRatedescVolume(airWaybill.getVolume().toString());
            fwbJsonVO.setRatedescVolumecode("MC");
        }
        fwbJsonVO.setSenderOfficeAirport(airWaybill.getSourcePort());
        fwbJsonVO.setSenderOfficeCompanyDesignator(airWaybill.getCarrier1());
        fwbJsonVO.setSenderOfficeFunctionDesignator(airWaybill.getSourcePort());
        String[] shcArray = Stream.of(
                airWaybill.getSpecialCargoCode1(),
                airWaybill.getSpecialCargoCode2(),
                airWaybill.getSpecialCargoCode3(),
                airWaybill.getOtherSpecialCargoCode()
        ).filter(s -> s != null && !s.trim().isEmpty()).toArray(String[]::new);
        fwbJsonVO.setShc(shcArray);;
        fwbJsonVO.setShipperAddr(airWaybill.getShipperAddress() == null ? airWaybill.getSourcePort() : airWaybill.getShipperAddress());
        fwbJsonVO.setShipperCity(airWaybill.getShipperRegion() == null ? airWaybill.getSourcePort() : airWaybill.getShipperRegion());
        fwbJsonVO.setShipperCountry("CN");
        fwbJsonVO.setShipperName(airWaybill.getShipper());
        fwbJsonVO.setShipperTel(airWaybill.getShipperPhone());
        fwbJsonVO.setSlac(airWaybill.getQuantity().toString());
        fwbJsonVO.setTotal(airWaybill.getCarrier1());
        fwbJsonVO.setTotalChargeamount(airWaybill.getPayMoney().toString());
        fwbJsonVO.setTotalChargesAgent(airWaybill.getCarrier1());
        fwbJsonVO.setTotalChargesCarrier(airWaybill.getCarrier1());
        fwbJsonVO.setTotalPrepaid(airWaybill.getCarrier1());
        if (airWaybill.getVolume() != null){
            fwbJsonVO.setVolume(airWaybill.getVolume().toString());
            fwbJsonVO.setVolumeUnit("MC");
        }
        fwbJsonVO.setWeight(airWaybill.getWeight().toString());
        fwbJsonVO.setWeightUnit("K");
        fwbJsonVO.setWtVal(airWaybill.getCarrier1());
        msgJsonVO.setMsgJson(JSON.toJSONString(fwbJsonVO));
        Map<String, List<HzCableAddress>> addressMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(addressList)){
            addressMap = addressList.stream().collect(Collectors.groupingBy(HzCableAddress::getAirportCode));
        }
        List<HzCableAddress> cableAddresses = addressMap.get(airWaybill.getDesPort());
        setInteractionType(cableAddresses,msgJsonVO);
        restExchange(msgJsonVO,token,sb);
        insertCableAndSendMsg(msgJsonVO,airWaybill,token,sb);
    }

    private void insertCableAndSendMsg(MsgJsonVO vo, AirWaybill airWaybill, String token, StringBuilder sb) {
        if (StringUtils.isEmpty(vo.getSendType())){
            return;
        }
        String[] sendTypes = vo.getSendType().split(",");
        for (String sendType : sendTypes) {
            HzCable cable = new HzCable();
            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("ddHHmm");
            cable.setSerialNo(SerialNumberGenerator.generateSerialNumber());
            cable.setCableNo(sdf.format(date));
            cable.setCableTime(date);
            cable.setCreateBy("系统");
            cable.setIsSend(1);
            cable.setIsAuto(1);
            cable.setType("FSU");
            cable.setVersion("12");
            cable.setPriority("QD");
            cable.setCableAddress(String.join(",", vo.getOrigin()));
            if (StringUtils.isNotEmpty(vo.getAddress())){
                cable.setReceiveAddress(String.join(",", vo.getAddress()));
            }
            cable.setFlightNo(airWaybill.getFlightNo1());
            cable.setFlightDate(airWaybill.getFlightDate1());
            cable.setContent(sb.toString());
            hzCableMapper.insert(cable);
            ForwardOriginMsgVO msgVO = new ForwardOriginMsgVO();
            msgVO.setOriginMsg(sb.toString());
            msgVO.setMsgType("FSU");
            msgVO.setSendType(sendType);
            msgVO.setReceiveAddress("-");
            if ("FD".equals(sendType)) {
                if (vo.getAddress() != null){
                    msgVO.setReceiveAddress(String.join(",",vo.getAddress()));
                }
            }
            if ("MAIL".equals(sendType)){
                msgVO.setMailAddress(String.join(",",vo.getReceiveMailAddress()));
            }
            if ("FTP".equals(sendType)){
                msgVO.setReceiveFtpAddress(ftpAddress);
                msgVO.setReceiveFtpFolder(vo.getReceiveFtpFolder());
            }
            if ("MQ".equals(sendType)){
                msgVO.setReceiveMQQueue(vo.getReceiveMQQueue());
            }
            msgVO.setSendAddress(String.join(",", vo.getOrigin()));
            msgVO.setPriority("QD");
            httpService.sendCable(msgVO, cable, token);
        }
    }

    public void setInteractionType(List<HzCableAddress> cableAddresses, MsgJsonVO vo) {
        List<String> sendType = new ArrayList<>();
        if (cableAddresses == null || cableAddresses.isEmpty()) {
            sendType.add("FD");
            vo.setSendType("FD");
            vo.setAddress(new String[]{"-"});
            return;
        }
        List<String> sendTypeCn = cableAddresses.stream()
                .map(HzCableAddress::getInteractionTypes)
                .filter(StringUtils::isNotEmpty)
                .map(s -> s.split(","))
                .flatMap(Arrays::stream)
                .map(String::trim)
                .distinct()
                .collect(Collectors.toList());
        for (String type : sendTypeCn) {
            switch (type) {
                case "民航局报文":
                    sendType.add("FD");
                    break;
                case "邮箱报文":
                    sendType.add("MAIL");
                    break;
                case "FTP收发报文":
                    sendType.add("FTP");
                    break;
                case "rabbitmq收发报文":
                    sendType.add("MQ");
                    break;
                default:
                    break;
            }
        }
        if (!sendType.contains("FD")){
            sendType.add("FD");
        }
        vo.setSendType(String.join(",", sendType));
        List<String> emailAddresses = new ArrayList<>();
        List<String> ftpList = new ArrayList<>();
        List<String> mqQueueList = new ArrayList<>();
        List<String> caacAddresses = new ArrayList<>();
        for (HzCableAddress cableAddress : cableAddresses) {
            List<String> interactionTypeList = new ArrayList<>();
            if (StringUtils.isNotEmpty(cableAddress.getInteractionTypes())) {
                interactionTypeList = Arrays.asList(cableAddress.getInteractionTypes().split(","));
            }
            if (!CollectionUtils.isEmpty(interactionTypeList)) {
                for (String type : interactionTypeList) {
                    switch (type) {
                        case "邮箱报文":
                            emailAddresses.add(cableAddress.getEmailAddress());
                            break;
                        case "FTP收发报文":
                            ftpList.add(cableAddress.getFtpList());
                            break;
                        case "民航局报文":
                            caacAddresses.add(cableAddress.getCaacAddress());
                            break;
                        case "rabbitmq收发报文":
                            mqQueueList.add(cableAddress.getMqQueue());
                            break;
                        default:
                            break;
                    }
                }
            }
        }
        if (!emailAddresses.isEmpty()) {
            vo.setReceiveMailAddress(emailAddresses.toArray(new String[0]));
        }
        if (!ftpList.isEmpty()) {
            vo.setReceiveFtpFolder(String.join(",",ftpList));
        }
        if (!mqQueueList.isEmpty()) {
            vo.setReceiveMQQueue(String.join(",",mqQueueList));
        }
        if (!caacAddresses.isEmpty()) {
            vo.setAddress(caacAddresses.toArray(new String[0]));
        }else {
            vo.setAddress(new String[]{"-"});
        }
    }

    private String getToken(){
        System.out.println("*********调用登录接口获取token*********");
        String token = "";
        HttpHeaders headers = setHeaders();
        HttpEntity<?> httpEntity = new HttpEntity<>(headers);
        ResponseEntity<JSONObject> exchange = restTemplate.exchange(loginUrl + account, HttpMethod.GET, httpEntity, JSONObject.class);
        if (exchange.getBody() != null) {
            if (!"0".equals(exchange.getBody().getString("code"))) {
                throw new CustomException(exchange.getBody().getString("message"));
            }
            JSONObject data = exchange.getBody().getJSONObject("data");
            token = data.getString("token");
            System.out.println(token);
        }
        return token;
    }

    private HttpHeaders setHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType("application/json;charset=UTF-8"));
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        headers.add("Accept-Charset", "UTF-8");
        return headers;
    }

    private void restExchange(MsgJsonVO vo,String token,StringBuilder sb){
        HttpHeaders header = setHeaders();
        header.add("X-Access-Token", token);
        HttpEntity<?> httpEntity = new HttpEntity<>(vo, header);
        System.out.println("参数：" + JSON.toJSONString(vo));
        ResponseEntity<JSONObject> exchange = restTemplate.exchange(getMsg, HttpMethod.POST, httpEntity, JSONObject.class);
        if (exchange.getBody() != null) {
            if (!"0".equals(exchange.getBody().getString("code"))) {
                throw new CustomException(exchange.getBody().getString("msg"));
            }
            JSONObject data = exchange.getBody().getJSONObject("data");
            String msgContent = data.getString("msgContent");
            sb.append(msgContent).append("\n");
        }
    }
}
