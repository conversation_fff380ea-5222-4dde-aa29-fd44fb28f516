package com.gzairports.common.business.wrong.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 不正常货邮处理记录表
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@Data
@TableName("all_wrong_record")
public class WrongRecord {

    /** 主键id */
    private Long id;

    /** 不正常货邮表id */
    private Long wrongId;

    /** 处理记录时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recordTime;

    /** 处理记录描述 */
    private String recordRemark;

    /** 操作人 */
    private String username;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
