package com.gzairports.hz.business.departure.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @author: lan
 * @Desc: 安检申报查询参数
 * @create: 2024-10-11 16:58
 **/
@Data
public class SecurityQuery {
    /** 航班日期 */
    private Date flightDate1;

    /** 代理人 */
    private String agent;

    /** 到达站1 */
    private String des1;

    /**开始时间*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**结束时间*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**安检状态*/
    private String securitySubmit;

    /** 运单号 */
    private String waybillCode;

    private Integer startRow;

    private Integer endRow;


}
