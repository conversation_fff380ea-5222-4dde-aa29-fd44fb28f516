package com.gzairports.hz.business.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.pdf.PdfPrintAnnotation;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static com.gzairports.common.pdf.PdfPrintAnnotation.PdfFieldType.*;

/**
 * 运单明细返回数据
 * <AUTHOR>
 * @date 2024-06-24
 */
@Data
public class WaybillInfoVo {

    /** 主键id */
    private Long id;

    /** 运单号 */
    private String waybillCode;

    /** 关联进港运单号 */
    private String arrWaybillCode;

    /** 代理人 */
    private String agent;

    /** 交接单编号 */
    private String transferNum;

    /** 海关监管 */
    private Integer customsSupervision;

    /** 转关 */
    private Integer transit;

    /** 特货代码1 */
    private String specialCargoCode1;

    /** 特货代码中文描述 */
    private String chineseDescription;

    /** 大类 */
    private String categoryCode;

    /** 货品代码 */
    private String cargoCode;

    /** 冷藏 */
    private String coldStore;

    /** 压仓 */
    private Integer pressureChamber;

    /** 安检提交最终状态 0未通过 1货检通过 -1货站不合格 -2货检不合格 */
    private Integer securitySubmit;

    /** 安检提交流程状态 0未提交 1物流提交 2货站提交 3货检通过 -1货站不通过 -2货检不通过 */
    private Integer securitySubmitWl;

    /** 是否跨航司承运 0 否 1 是 */
    private Integer crossAir;

    /** 货品大类 */
    private String categoryName;

    /** 备注 */
    private String remark;

    /** 储运注意事项 */
    private String storageTransportNotes;

    /** 状态 */
    private String status;

    /** 支付状态 */
    private Integer payStatus;

    /** 是否收运导入 */
    private Integer isLoad;

    /** 是否整单拉下 */
    private Integer isPullDownTotal;

    /** 电子运单 */
    private String pdfUrl;

    /** 安检申报单 */
    private String securityUrl;

    /** 随附文件 */
    private String transportFile;

    /** 随附文件pdf */
    private String transportFilePdf;

    /** 运单收运列表 */
    private List<DetailedVo> detailedVos;

    /** 危险品UN编号 */
    private String dangerCode;

    /** 运单号 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "waybillCode")
    private String waybillCodeAbb;

    /** 始发站 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "sourcePort")
    private String sourcePort;

    /** 目的站 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "desPort")
    private String desPort;

    /** 航班号 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "flightNo1")
    private String flightNo1;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date flightDate1;

    /** 航班日期 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "flightDate1")
    private String flightDate1Str;

    /** 航班号 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "flightNo2")
    private String flightNo2;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date flightDate2;

    /** 航班日期 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "flightDate2")
    private String flightDate2Str;

    /** 承运人1 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "carrier1")
    private String carrier1;

    /** 到达站1 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "des1")
    private String des1;

    /** 承运人2 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "carrier2")
    private String carrier2;

    /** 到达站2 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "des2")
    private String des2;

    /** 承运人3 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "carrier3")
    private String carrier3;

    /** 到达站3 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "des3")
    private String des3;

    /** 发货人 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipper")
    private String shipper;

    /** 发货人电话 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipperPhone")
    private String shipperPhone;

    /** 发货人地址 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipperAddress")
    private String shipperAddress;

    /** 发货人地区 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipperRegion")
    private String shipperRegion;

    /** 收货人 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "consign")
    private String consign;

    /** 收货人电话 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "consigneePhone")
    private String consignPhone;

    /** 收货人地址 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "consigneeAddress")
    private String consignAddress;

    /** 收货人地区 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "consigneeRegion")
    private String consignRegion;

    /** 代理人公司 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "agentCompany")
    private String agentCompany;

    /** 代理人识别码 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "agentCode")
    private String agentCode;

    /** 品名 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "cargoName")
    private String cargoName;

    /** 包装 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "pack")
    private String pack;

    /** 包装code */
    private String packCode;

    /** 件数 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "quantity")
    private Integer quantity;

    /** 重量 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "weight")
    private BigDecimal weight;

    /** 计费重量 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "chargeWeight")
    private BigDecimal chargeWeight;

    /** 可补货重量 */
    private BigDecimal canRestockWeight;

    /** 体积（M3） */
    private BigDecimal volume;

    /** 尺寸 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "size")
    private String size;

    /** 填开时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "writeTime")
    private Date writeTime;

    /** 填开时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "writeTime")
    private String writeTimeStr;

    /** 填开地点 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "writeLocation")
    private String writeLocation;

    /** 填开人 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "writer")
    private String writer;

    @PdfPrintAnnotation(pdfFieldType = CHOICE, pdfFieldName = "pthw")
    private String pthw;

    @PdfPrintAnnotation(pdfFieldType = CHOICE, pdfFieldName = "tzhw")
    private String tzhw;

    @PdfPrintAnnotation(pdfFieldType = CHOICE, pdfFieldName = "wxp")
    private String wxp;

    @PdfPrintAnnotation(pdfFieldType = CHOICE, pdfFieldName = "hkkj")
    private String hkkj;

    @PdfPrintAnnotation(pdfFieldType = IMAGE, pdfFieldName = "sealUrl")
    private String sealUrl;

    /** 是否真实收运 */
    private Integer isReal;

    /** 收运状态 */
    private Integer collectStatus;

    /** 转南航 */
    private Integer isSouth;

    /** 是否退货 */
    private Integer isExit;
}
