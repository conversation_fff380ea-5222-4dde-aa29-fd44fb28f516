package com.gzairports.hz.business.departure.service.impl;


import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.business.departure.domain.Mawb;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.spring.SpringUtils;
import com.gzairports.common.business.departure.domain.FlightInfo;
import com.gzairports.hz.business.departure.domain.query.FlightInfoQuery;
import com.gzairports.hz.business.departure.domain.vo.FlightInfoVO;
import com.gzairports.common.business.arrival.mapper.FlightInfoMapper;
import com.gzairports.common.business.departure.mapper.MawbMapper;
import com.gzairports.hz.business.departure.service.IFlightInfoService;
import com.gzairports.sms.domain.dto.SmsSendParameter;
import com.gzairports.sms.domain.dto.SmsWarehousingTemplate;
import com.gzairports.sms.service.impl.GzaSmsSendServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;


/**
 * 航班信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-09-18
 */
@Service
public class FlightInfoServiceImpl extends ServiceImpl<FlightInfoMapper, FlightInfo> implements IFlightInfoService
{
    @Autowired
    private FlightInfoMapper businessFlightInfoMapper;

    @Autowired
    private MawbMapper mawbMapper;

    @Autowired
    private RedisTemplate<String,String> redisTemplate;

    @Value("${sms.ruKuConfigCode}")
    private String ruKuConfigCode;

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
    private static final DateTimeFormatter FMT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 宽体机维护列表
     */
    private static final String WIDE_BODY = "airc-master:wide_body:1001";

    /**
     * 查询航班详情信息
     * 
     * @param flightId 航班信息ID
     * @return 航班信息
     */
    @Override
    public FlightInfoVO selectBusinessFlightInfoById(Long flightId)
    {
        FlightInfoVO vo = new FlightInfoVO();
        FlightInfo businessFlightInfo = businessFlightInfoMapper.selectBusinessFlightInfoById(flightId);
        BeanUtils.copyProperties(businessFlightInfo, vo);
        String state = StringUtils.isEmpty(vo.getProvidingState()) ? vo.getAbnormalState(): vo.getProvidingState();
        vo.setFlightState(FLIGHT_STATUS.get(state));
        if ("CAN".equals(state)){
            vo.setStatus(1);
        }else {
            vo.setStatus(0);
        }
        if (StringUtils.isNotEmpty(vo.getShareviaCn())){
            vo.setTerminalStationCn(vo.getShareviaCn() + "-" + vo.getTerminalStationCn());
        }
        return vo;
    }

    /**
     * 查询航班信息列表
     * 
     * @param query 航班信息
     * @return 航班信息
     */
    @Override
    public List<FlightInfoVO> selectBusinessFlightInfoList(FlightInfoQuery query)
    {
        if (query.getExecDate() == null){
            Date now = new Date();
            String format = DATE_FORMAT.format(now);
            query.setExecDate(format);
        }
        List<FlightInfoVO> list = businessFlightInfoMapper.selectBusinessFlightInfoList(query);
        List<FlightInfoVO> flightInfoVOS = new LinkedList<>();
        for (FlightInfoVO flightInfoVO : list) {
                if(StringUtils.isNull(query.getWideis())){
                    flightInfoVOS.addAll(list);
                    break;
                }
                else if (query.getWideis().equals("0")) {
                    //前端选择否
                    //新集合需要加入该方法返回为false的数据
                    if (!determine(flightInfoVO.getCraftType())) {
                        flightInfoVOS.add(flightInfoVO);
                    }
                } else if (query.getWideis().equals("1")) {
                    //前端选择是
                    //新集合需要加入该方法返回为false的数据
                    if (determine(flightInfoVO.getCraftType())) {
                        flightInfoVOS.add(flightInfoVO);
                    }
                }
        }
        for (FlightInfoVO flightInfoVO : flightInfoVOS) {
            String state = StringUtils.isEmpty(flightInfoVO.getProvidingState()) ? flightInfoVO.getAbnormalState(): flightInfoVO.getProvidingState();
            flightInfoVO.setFlightState(FLIGHT_STATUS.get(state));
            if ("CAN".equals(state)){
                flightInfoVO.setStatus(1);
            }else {
                flightInfoVO.setStatus(0);
            }
            if (StringUtils.isNotEmpty(flightInfoVO.getShareviaCn())){
                flightInfoVO.setTerminalStationCn(flightInfoVO.getShareviaCn() + "-" + flightInfoVO.getTerminalStationCn());
            }
        }
        return flightInfoVOS;
    }

    /**
     * 修改航班信息
     * 
     * @param businessFlightInfo 航班信息
     * @return 结果
     */
    @Override
    public int updateBusinessFlightInfo(FlightInfo businessFlightInfo)
    {
        businessFlightInfo.setIsSms(0);
        return getBaseMapper().updateBusinessFlightInfo(businessFlightInfo);
    }


    /**
     * 生成排班
     *
     * @param date 时间参数
     * @return 结果
     */
    @Override
    public String generate(String date) {
        List<FlightInfo> list = businessFlightInfoMapper.selectList(new QueryWrapper<FlightInfo>()
                .eq("exec_date",date)
                .eq("is_offin","D")
                .ne("air_ways","CZ"));
        if (CollectionUtils.isEmpty(list)){
            throw new CustomException("没有航班数据，生成失败");
        }
        //获取每天的九点时间
        Calendar c = Calendar.getInstance();
        c.set(Calendar.HOUR_OF_DAY, 9);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        Date nine = c.getTime();
        LocalDateTime nineTime = nine.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

        //获取每天的4点时间
        c.set(Calendar.HOUR_OF_DAY, 4);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        Date four = c.getTime();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String format = df.format(four);

        for (FlightInfo flightInfo : list) {
            LocalDateTime time =flightInfo.getStartSchemeTakeoffTime();
            if (time != null){
                // 判断当前航班预计起飞时间是否小于等于9点
                if (time.isBefore(nineTime) || time.isEqual(nineTime)){
                    flightInfo.setStartStorageTime(format);
                }else {
                    // 判断是否是宽体机
                    if (determine(flightInfo.getCraftType())){
                        LocalDateTime start = time.minusHours(8);
                        flightInfo.setStartStorageTime(start.format(FMT));
                        flightInfo.setEndStorageTime(time.minusHours(4).format(FMT));
                    }else {
                        LocalDateTime start = time.minusHours(6);
                        flightInfo.setStartStorageTime(start.format(FMT));
                        flightInfo.setEndStorageTime(time.minusMinutes(150).format(FMT));
                    }
                }
            }
            flightInfo.setIsManual(1);
            flightInfo.setIsSms(0);
            this.updateById(flightInfo);
        }
        return "入库排班完成";
    }

    /**
     * 生成排班公共方法
     *
     * @param flightInfo 航班对象
     */
    private void warehousing(FlightInfo flightInfo) {
        LocalDateTime time = flightInfo.getStartSchemeTakeoffTime();
        List<Mawb> waybillList = mawbMapper.selectList(new QueryWrapper<Mawb>()
                .eq("flight_no1", flightInfo.getFlightNo()).eq("flight_date1",flightInfo.getExecDate()));
        if (CollectionUtils.isEmpty(waybillList)){
           return;
        }
        for (Mawb waybill : waybillList) {
            SmsWarehousingTemplate template = new SmsWarehousingTemplate();
            template.setBillId(waybill.getWaybillCode());
            template.setFlightNo(DATE_FORMAT.format(flightInfo.getExecDate()) +" "+flightInfo.getAirWays() + flightInfo.getFlightNo());
            if (time.getMinute() < 10){
                template.setStartSchemeTakeoffTime(time.getHour() + ":0" + time.getMinute());
            }else {
                template.setStartSchemeTakeoffTime(time.getHour() + ":" + time.getMinute());
            }
            if (flightInfo.getEndStorageTime() != null){
                template.setWarehousingTime(flightInfo.getStartStorageTime() + "-" + flightInfo.getEndStorageTime().split(" ")[1]);
            }else {
                template.setWarehousingTime(flightInfo.getStartStorageTime());
            }
            String content = "尊敬的客户，您好！您的货运单"+template.getBillId()+"将预配于"+template.getFlightNo()+"航班，计划"+template.getStartSchemeTakeoffTime()+"起飞。" +
                    "请在"+template.getWarehousingTime()+"将货物送至贵州航空港物流产业发展有限公司货站运行部的安检通道口。" +
                    "如有任何问题或需要协助，请随时联系我们TEL:TEL:0851-5497438";
            // 排班通知发送给发货代理人
            if (!StringUtils.isEmpty(waybill.getShipperAbb())){
                SmsSendParameter param = new SmsSendParameter();
                param.setMobile(waybill.getShipperPhone());
                param.setBillId(waybill.getWaybillCode());
                param.setSmsParameter(JSON.toJSONString(template));
                param.setModuleName("排班通知");
                param.setConfigCode(ruKuConfigCode);
                param.setContent(content);
                SpringUtils.getBean(GzaSmsSendServiceImpl.class).sendSms(param);
                flightInfo.setIsSms(1);

            }
        }
    }

    /**
     * 排班显示
     *
     * @return 当天未起飞航班
     */
    @Override
    public List<FlightInfoVO> show() {
        List<FlightInfoVO> list = businessFlightInfoMapper.selectShowList();
        list.forEach(e->{
            String state = StringUtils.isEmpty(e.getProvidingState()) ? e.getAbnormalState(): e.getProvidingState();
            if ("CAN".equals(state)){
                e.setStatus(1);
            }else {
                e.setStatus(0);
            }
            if (StringUtils.isNotEmpty(e.getShareviaCn())){
                e.setTerminalStationCn(e.getShareviaCn() + "-" + e.getTerminalStationCn());
            }
        });
        return list;
    }

    /**
     * 入库排班短信发送接口
     * @param flightId 航班主键id
     * @return 结果
     */
    @Override
    public String sendSms(Long flightId) {
        FlightInfo flightInfo = businessFlightInfoMapper.selectById(flightId);
        if (flightInfo.getStartStorageTime() != null){
            warehousing(flightInfo);
        }
        businessFlightInfoMapper.updateById(flightInfo);
        return "短信已发送给代理人";
    }

    /**
     * 入库排班未通知短信发送接口
     * @param query 未通知短信发送参数
     * @return 结果
     */
    @Override
    public String noSendSms(FlightInfoQuery query) {
        Date now = new Date();
        List<FlightInfo> existList = businessFlightInfoMapper.selectList(new QueryWrapper<FlightInfo>()
                .eq("exec_date",DATE_FORMAT.format(now))
                .eq("is_offin","D")
                .eq("is_manual",1));
        if (CollectionUtils.isEmpty(existList)){
            throw new CustomException("当前还未生成排班");
        }
        List<FlightInfo> list = businessFlightInfoMapper.selectList(new QueryWrapper<FlightInfo>()
                .ge("exec_date",query.getStartExecDate())
                .le("exec_date",query.getStopExecDate())
                .eq("is_sms",0));
        for (FlightInfo businessFlightInfo : list) {
            warehousing(businessFlightInfo);
            businessFlightInfoMapper.updateById(businessFlightInfo);
        }
        return "短信已发送给代理人";
    }

    /**
     * 修改宽体机维护列表
     * @return 结果
     */
    @Override
    public int updateWideBody(String[] strings) {
        StringBuilder b = new StringBuilder();
        for (String string : strings) {
            b.append(string).append(",");
        }
        redisTemplate.opsForValue().set(WIDE_BODY, b.toString());
        return 1;
    }

    /**
     * 获取宽体机维护列表
     * @return 结果
     */
    @Override
    public String[] getWideBody() {
        String s = redisTemplate.opsForValue().get(WIDE_BODY);
        if(s == null || StringUtils.isEmpty(s)){
            return null;
        }
        return s.split(",");
    }

    /**
     * 根据航班号获取最新航班日期
     * @return 结果
     */
    @Override
    public String getDateByNo(String flightNo) {
        return businessFlightInfoMapper.getDateByNo(flightNo);
    }

    /**
     * 航班状态集合
     */
    private static final Map<String,String> FLIGHT_STATUS  = new LinkedHashMap<>();
    static {
        FLIGHT_STATUS.put("ALT","备降");
        FLIGHT_STATUS.put("ARR","到达");
        FLIGHT_STATUS.put("BAK","滑回");
        FLIGHT_STATUS.put("BOR","登机");
        FLIGHT_STATUS.put("CAN","取消");
        FLIGHT_STATUS.put("CKI","正在值机");
        FLIGHT_STATUS.put("CKO","值机截止");
        FLIGHT_STATUS.put("DEP","起飞");
        FLIGHT_STATUS.put("DLY","延误");
        FLIGHT_STATUS.put("LBD","登机催促");
        FLIGHT_STATUS.put("NST","到下站");
        FLIGHT_STATUS.put("ONR","前方起飞");
        FLIGHT_STATUS.put("POK","登机结束");
        FLIGHT_STATUS.put("RTN","返航");
        FLIGHT_STATUS.put("TBR","过站登机");
        FLIGHT_STATUS.put("TST","测试状态");
        FLIGHT_STATUS.put("BOT","推出");
    }

    /**
     * 判断是否为宽体机方法
     * @return
     */
    public Boolean determine(String craftType){
        String s = redisTemplate.opsForValue().get(WIDE_BODY);
        if (s != null && !StringUtils.isEmpty(s)){
            for (String s1 : s.split(",")) {
                String[] split = s1.split("-");
                if (craftType.equals(split[0])){
                    return true;
                }
            }
        }
        return false;
    }
}
