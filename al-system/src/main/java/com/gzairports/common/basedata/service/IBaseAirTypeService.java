package com.gzairports.common.basedata.service;

import com.gzairports.common.basedata.domain.BaseAirType;

import java.util.List;

/**
 * 机型管理Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-14
 */
public interface IBaseAirTypeService 
{
    /**
     * 查询机型管理
     * 
     * @param id 机型管理主键
     * @return 机型管理
     */
    public BaseAirType selectBaseAirTypeById(Long id);

    /**
     * 查询机型管理列表
     * 
     * @param baseAirType 机型管理
     * @return 机型管理集合
     */
    public List<BaseAirType> selectBaseAirTypeList(BaseAirType baseAirType);

    /**
     * 新增机型管理
     * 
     * @param baseAirType 机型管理
     * @return 结果
     */
    public int insertBaseAirType(BaseAirType baseAirType);

    /**
     * 修改机型管理
     * 
     * @param baseAirType 机型管理
     * @return 结果
     */
    public int updateBaseAirType(BaseAirType baseAirType);

    /**
     * 批量删除机型管理
     * 
     * @param ids 需要删除的机型管理主键集合
     * @return 结果
     */
    public int deleteBaseAirTypeByIds(Long[] ids);

    /**
     * 删除机型管理信息
     * 
     * @param id 机型管理主键
     * @return 结果
     */
    public int deleteBaseAirTypeById(Long id);

    /**
     * 导入机型管理
     *
     * @param types 机型管理列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    String importAirType(List<BaseAirType> types, boolean updateSupport);
}
