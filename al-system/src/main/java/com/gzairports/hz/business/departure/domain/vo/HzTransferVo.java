package com.gzairports.hz.business.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 运单收运返回数据
 * <AUTHOR>
 * @date 2024-06-24
 */
@Data
public class HzTransferVo {

    /** 主键id */
    private Long id;

    /** 交接单编号 */
    private String transferNum;

    /** 运单id */
    private Long waybillId;

    /** 代理人 */
    @Excel(name = "代理人")
    private String agent;

    /** 运单数 */
    @Excel(name = "运单数")
    private Integer waybillNum;

    /** 提交时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "提交时间")
    private Date submitTime;

    /** 提交人 */
    @Excel(name = "提交人")
    private String submitter;

    /** 入库操作人 */
    @Excel(name = "入库操作人")
    private String storeKeeper;

    /** 状态（提交 暂存 取消 已处理） */
    private String statusStr;

    /** 状态（提交 暂存 取消 已处理） */
    @Excel(name = "状态")
    private String status;

    /** 日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "日期")
    private Date date;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 类型 0 普货交接单 1 特货交接单 */
    private Integer type;

    /** 航班号 */
    private String flightNo;

    /** 目的站 */
    private String des;

    /** 装机地点 */
    private String loading;

    /** 卸机地点 */
    private String unloading;

    /** 运单号 */
    private String waybillCode;

    /** 件数 */
    private String quantity;

    /** 重量 */
    private String weight;

    /** 总件数 */
    private Integer totalQuantity;

    /** 总重量 */
    private BigDecimal totalWeight;

    /** 包装方式 */
    private String packWay;

    /** 货品编码 */
    private String cargoCode;

    /** 品名 */
    private String cargoName;

    /** 货物情况 */
    private String cargoCondition;

    /** 交货人 */
    private String delivery;

    /** 交货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deliveryTime;

    /** 交货备注 */
    private String deliveryRemark;

    /** 第一接货人 */
    private String firstReceiver;

    /** 第一接货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date firstReceiverTime;

    /** 第一接货备注 */
    private String firstReceiverRemark;

    /** 第二接货人 */
    private String secondReceiver;

    /** 第二接货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date secondReceiverTime;

    /** 第二接货备注 */
    private String secondReceiverRemark;

    /** 第三接货人 */
    private String thirdReceiver;

    /** 第三接货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date thirdReceiverTime;

    /** 第三接货备注 */
    private String thirdReceiverRemark;

    /** 货物交接运单列表 */
    private List<TransferWaybillVo> vos;
}
