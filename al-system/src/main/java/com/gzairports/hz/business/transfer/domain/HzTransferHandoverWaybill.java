package com.gzairports.hz.business.transfer.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gzairports.common.annotation.Excel;
import com.gzairports.common.core.domain.BizEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * 交接单与运单关联对象 hz_transfer_handover_waybill
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
@Data
@TableName("hz_transfer_handover_waybill")
public class HzTransferHandoverWaybill {

    /** 主键id */
    private Long id;

    /** 货物中转交接id */
    @Excel(name = "货物中转交接id")
    private Long transferId;

    /** 运单id */
    @Excel(name = "运单id")
    private Long waybillId;

    /** 是否代运导入 0 否 1 是 */
    private Integer isLoad;
}
