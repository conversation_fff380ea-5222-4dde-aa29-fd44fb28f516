package com.gzairports.common.basedata.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.basedata.domain.BaseCargoUld;
import com.gzairports.common.basedata.domain.query.CargoUldQuery;
import com.gzairports.hz.business.cable.domain.vo.CableUldVo;
import com.gzairports.hz.business.cable.domain.vo.UldVO;
import com.gzairports.hz.business.departure.domain.query.CableMawbQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 集装器管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Mapper
public interface UldMapper extends BaseMapper<BaseCargoUld> {

    /**
     * 查询集装器管理列表
     *
     * @param query 查询参数
     * @return 集装器管理列表
     */
    List<BaseCargoUld> ULDList(CargoUldQuery query);

    /**
     * 新增集装器
     *
     * @param uld 集装器
     * @return 结果
     */
    int addULD(BaseCargoUld uld);

    /**
     * 修改集装器
     *
     * @param uld 集装器
     * @return 结果
     */
    int editULD(BaseCargoUld uld);

    /**
     * 删除集装器
     * @param id 集装器id
     * @return 结果
     */
    int removeUld(Long id);

    /**
     * 集装器详情
     * @param id 集装器id
     * @return 结果
     */
    BaseCargoUld getInfo(Long id);

    /**
     * 根据code查询集装器数据
     * @param code 集装器code
     * @return 结果
     */
    BaseCargoUld selectByCode(String code);

    /**
     * 报文数据收集集装器列表数据
     * @param query 查询参数
     * @return 集装器列表
     */
    List<CableUldVo> selectUldList(CableMawbQuery query);

    /**
     * 根据uld查询板车信息
     * @param uld uld号
     * @return 结果
     */
    Integer selectOneByUld(String uld);

    List<UldVO> selectUldByFlightId(@Param("flightId") Long flightId,@Param("waybillIds") List<Long> waybillIds);
}
