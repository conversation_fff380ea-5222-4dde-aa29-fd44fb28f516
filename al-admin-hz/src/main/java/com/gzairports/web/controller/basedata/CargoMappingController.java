package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BaseCargoMapping;
import com.gzairports.common.basedata.domain.query.CargoCodeMappingQuery;
import com.gzairports.common.basedata.service.ICargoMappingService;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 货品代码映射Controller
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@RestController
@RequestMapping("/base/cargoMapping")
@Api(tags = "货品代码映射数据接口")
public class CargoMappingController extends BaseController {

    @Autowired
    private ICargoMappingService cargoMappingService;

    /**
     * 查询货品代码映射列表
     */
//    @PreAuthorize("@ss.hasPermi('base:cargoMapping:cargoCodeMappingList')")
    @GetMapping("/cargoCodeMappingList")
    @ApiOperation(value = "查询货品代码映射列表")
    public TableDataInfo cargoCodeMappingList(CargoCodeMappingQuery query)
    {
        startPage();
        List<BaseCargoMapping> list = cargoMappingService.selectCargoCodeMappingList(query);
        return getDataTable(list);
    }

    /**
     * 导出货品代码映射
     */
    @PreAuthorize("@ss.hasPermi('base:cargoMapping:exportCargoCodeMapping')")
    @Log(title = "导出货品代码映射", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCargoCodeMapping")
    @ApiOperation(value = "导出货品代码映射")
    public void exportCargoCodeMapping(HttpServletResponse response, CargoCodeMappingQuery query)
    {
        List<BaseCargoMapping> list = cargoMappingService.selectCargoCodeMappingList(query);
        ExcelUtil<BaseCargoMapping> util = new ExcelUtil<BaseCargoMapping>(BaseCargoMapping.class);
        util.exportExcel(response, list, "货品代码映射");
    }

    /**
     * 导出货品代码映射模板
     */
    @PreAuthorize("@ss.hasPermi('base:cargoMapping:exportCargoCodeMapping')")
    @Log(title = "导出货品代码映射", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCargoCodeMappingNull")
    @ApiOperation(value = "导出货品代码映射模板")
    public void exportCargoCodeMappingNull(HttpServletResponse response)
    {
        List<BaseCargoMapping> list = new ArrayList<>();
        ExcelUtil<BaseCargoMapping> util = new ExcelUtil<BaseCargoMapping>(BaseCargoMapping.class);
        util.exportExcel(response, list, "货品代码映射模板");
    }

    /**
     * 导入货品代码映射
     */
    @PreAuthorize("@ss.hasPermi('base:cargoMapping:importCargoMapping')")
    @Log(title = "导入货品代码映射", businessType = BusinessType.IMPORT)
    @PostMapping("/importCargoMapping")
    @ApiOperation(value = "导入货品代码映射")
    public AjaxResult importCargoMapping(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<BaseCargoMapping> util = new ExcelUtil<BaseCargoMapping>(BaseCargoMapping.class);
        List<BaseCargoMapping> cargoMappings = util.importExcel(file.getInputStream());
        String message = cargoMappingService.importCargoMapping(cargoMappings, updateSupport);
        return success(message);
    }

    /**
     * 新增货品代码映射
     */
    @PreAuthorize("@ss.hasPermi('base:cargoMapping:addCargoMapping')")
    @Log(title = "新增货品代码映射", businessType = BusinessType.INSERT)
    @PostMapping("/addCargoMapping")
    @ApiOperation(value = "新增货品代码映射")
    public AjaxResult addCargoMapping(@RequestBody BaseCargoMapping cargoMapping)
    {
        return toAjax(cargoMappingService.addCargoMapping(cargoMapping));
    }

    /**
     * 修改货品代码映射
     */
    @PreAuthorize("@ss.hasPermi('base:cargoMapping:editCargoMapping')")
    @Log(title = "修改货品代码映射", businessType = BusinessType.UPDATE)
    @PostMapping("/editCargoMapping")
    @ApiOperation(value = "修改货品代码映射")
    public AjaxResult editCargoMapping(@RequestBody BaseCargoMapping cargoMapping)
    {
        return toAjax(cargoMappingService.editCargoMapping(cargoMapping));
    }

    /**
     * 删除货品代码映射
     */
    @PreAuthorize("@ss.hasPermi('base:cargoMapping:delCargoMapping')")
    @Log(title = "删除货品代码映射", businessType = BusinessType.UPDATE)
    @GetMapping("/delCargoMapping/{ids}")
    @ApiOperation(value = "删除货品代码映射")
    public AjaxResult delCargoMapping(@PathVariable("ids") Long[] ids)
    {
        return toAjax(cargoMappingService.delCargoMapping(ids));
    }
}
