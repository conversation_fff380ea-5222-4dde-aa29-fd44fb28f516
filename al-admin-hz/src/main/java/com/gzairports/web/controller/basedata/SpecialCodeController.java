package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BaseSpecialCode;
import com.gzairports.common.basedata.domain.query.SpecialCodeQuery;
import com.gzairports.common.basedata.service.ISpecialCodeService;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 货物特殊处理代码Controller
 *
 * <AUTHOR>
 * @date 2024-02-20
 */
@RestController
@RequestMapping("/base/specialCargo")
@Api(tags = "货物特殊处理代码数据接口")
public class SpecialCodeController extends BaseController {

    @Autowired
    private ISpecialCodeService specialCodeService;

    /**
     * 查询货物特殊处理代码列表
     */
//    @PreAuthorize("@ss.hasPermi('base:specialCargo:specialCodeList')")
    @GetMapping("/specialCodeList")
    @ApiOperation(value = "查询货物特殊处理代码列表")
    public TableDataInfo specialCodeList(SpecialCodeQuery query)
    {
        startPage();
        List<BaseSpecialCode> list = specialCodeService.selectSpecialCodeList(query);
        return getDataTable(list);
    }

    /**
     * 导出货物特殊处理代码
     */
    @PreAuthorize("@ss.hasPermi('base:specialCargo:exportSpecialCode')")
    @Log(title = "导出货物特殊处理代码", businessType = BusinessType.EXPORT)
    @PostMapping("/exportSpecialCode")
    @ApiOperation(value = "导出货物特殊处理代码")
    public void exportSpecialCode(HttpServletResponse response, SpecialCodeQuery query)
    {
        List<BaseSpecialCode> list = specialCodeService.selectSpecialCodeList(query);
        ExcelUtil<BaseSpecialCode> util = new ExcelUtil<BaseSpecialCode>(BaseSpecialCode.class);
        util.exportExcel(response, list, "货物特殊处理代码");
    }

    /**
     * 导出货物特殊处理代码模板
     */
    @PreAuthorize("@ss.hasPermi('base:specialCargo:exportSpecialCode')")
    @Log(title = "导出货物特殊处理代码模板", businessType = BusinessType.EXPORT)
    @PostMapping("/exportSpecialCodeNull")
    @ApiOperation(value = "导出货物特殊处理代码模板")
    public void exportSpecialCodeNull(HttpServletResponse response)
    {
        List<BaseSpecialCode> list = new ArrayList<>();
        ExcelUtil<BaseSpecialCode> util = new ExcelUtil<BaseSpecialCode>(BaseSpecialCode.class);
        util.exportExcel(response, list, "货物特殊处理代码模板");
    }

    /**
     * 导入货物特殊处理代码
     */
    @PreAuthorize("@ss.hasPermi('base:specialCargo:importSpecialCode')")
    @Log(title = "导入货物特殊处理代码", businessType = BusinessType.IMPORT)
    @PostMapping("/importSpecialCode")
    @ApiOperation(value = "导入货物特殊处理代码")
    public AjaxResult importSpecialCode(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<BaseSpecialCode> util = new ExcelUtil<BaseSpecialCode>(BaseSpecialCode.class);
        List<BaseSpecialCode> specialCodes = util.importExcel(file.getInputStream());
        String message = specialCodeService.importSpecialCode(specialCodes, updateSupport);
        return success(message);
    }

    /**
     * 新增货物特殊处理代码
     */
    @PreAuthorize("@ss.hasPermi('base:specialCargo:addSpecialCode')")
    @Log(title = "新增货物特殊处理代码", businessType = BusinessType.INSERT)
    @PostMapping("/addSpecialCode")
    @ApiOperation(value = "新增货物特殊处理代码")
    public AjaxResult addSpecialCode(@RequestBody BaseSpecialCode specialCode)
    {
        return toAjax(specialCodeService.addSpecialCode(specialCode));
    }

    /**
     * 修改城市代码
     */
    @PreAuthorize("@ss.hasPermi('base:specialCargo:editSpecialCode')")
    @Log(title = "修改货物特殊处理代码", businessType = BusinessType.UPDATE)
    @PostMapping("/editSpecialCode")
    @ApiOperation(value = "修改货物特殊处理代码")
    public AjaxResult editSpecialCode(@RequestBody BaseSpecialCode specialCode)
    {
        return toAjax(specialCodeService.editSpecialCode(specialCode));
    }

    /**
     * 删除货物特殊处理代码
     */
    @PreAuthorize("@ss.hasPermi('base:specialCargo:delSpecialCode')")
    @Log(title = "删除货物特殊处理代码", businessType = BusinessType.UPDATE)
    @GetMapping("/delSpecialCode/{ids}")
    @ApiOperation(value = "删除货物特殊处理代码")
    public AjaxResult delSpecialCode(@PathVariable("ids") Long[] ids)
    {
        return toAjax(specialCodeService.delSpecialCode(ids));
    }
}
