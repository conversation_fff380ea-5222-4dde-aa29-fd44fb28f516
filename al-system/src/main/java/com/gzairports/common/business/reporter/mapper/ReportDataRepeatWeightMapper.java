package com.gzairports.common.business.reporter.mapper;

import com.gzairports.common.business.reporter.domain.ReportDepRepeatWeight;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 复重统计数据同步Mapper
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Mapper
public interface ReportDataRepeatWeightMapper {

    /**
     * 保存或修改复重统计数据
     * @param repeatWeightList 待同步复重数据
     */
    int insertBatchOrUpdate(@Param("repeatWeightList") List<ReportDepRepeatWeight> repeatWeightList);

}
