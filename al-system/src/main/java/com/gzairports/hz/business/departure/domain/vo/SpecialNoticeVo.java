package com.gzairports.hz.business.departure.domain.vo;

import com.gzairports.common.pdf.PdfPrintAnnotation;
import lombok.Data;

import java.math.BigDecimal;

import static com.gzairports.common.pdf.PdfPrintAnnotation.PdfFieldType.TEXT;

/**
 * Created by david on 2024/11/5
 */
@Data
public class SpecialNoticeVo {

    /** 卸机站 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "desPort")
    private String desPort;

    /** 货运单号 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "waybillCode")
    private String waybillCode;

    /** 品名 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "cargoName")
    private String cargoName;

    /** 件数 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "quantity")
    private Integer quantity;

    /** 重量 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "weight")
    private BigDecimal weight;

    /** uld号 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "uld")
    private String uld;

    /** 舱位 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "cabin")
    private String cabin;

    /** 特货代码1 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "specialCargoCode1")
    private String specialCargoCode1;
}
