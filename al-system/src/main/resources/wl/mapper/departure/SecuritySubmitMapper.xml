<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.wl.departure.mapper.SecuritySubmitMapper">
    <select id="securitySubmitList" resultType="com.gzairports.wl.departure.domain.vo.SecurityVoWl">
        select id, waybill_code, shipping_agent as agent, special_cargo_code1,danger_code,
        cargo_name, quantity, weight, flight_no1,flight_date1,
        security_submit, security_submit_wl, security_url, declaration_consistent, is_examine,
        null as cargo_type,null as is_special, 1 as type
        from all_air_waybill
        <where>
            is_del = 0  and type = 'DEP' and status != 'staging'
            <if test="securitySubmitWl == 1"> and security_submit_wl = '1' or security_submit_wl = '2'</if>
            <if test="securitySubmitWl != null and securitySubmitWl != '' and securitySubmitWl != '1'"> and security_submit_wl = #{securitySubmitWl}</if>
            <if test="startTime != null">
                and write_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and write_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="waybillCode != null and waybillCode != ''"> and waybill_code = #{waybillCode}</if>
            <if test="deptId != null and deptId != ''"> and dept_id = #{deptId}</if>
            <if test="isSpecial == 1"> and special_cargo_code1 is not null</if>
        </where>

        union

        select id, waybill_code, agent,null as special_cargo_code1,null as danger_code,
        cargo_name, quantity, weight, flight_no1,flight_date1,
        security_submit, security_submit_wl, security_url, declaration_consistent, is_examine,
        cargo_type,is_special, 0 as type
        from all_security_waybill
        <where>
            <if test="securitySubmitWl == 1"> and security_submit_wl = '1' or security_submit_wl = '2'</if>
            <if test="securitySubmitWl != null and securitySubmitWl != '' and securitySubmitWl != '1'"> and security_submit_wl = #{securitySubmitWl}</if>
            <if test="startTime != null">
                and create_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and create_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="waybillCode != null and waybillCode != ''"> and waybill_code = #{waybillCode}</if>
            <if test="deptId != null and deptId != ''"> and dept_id = #{deptId}</if>
            <if test="isSpecial != null and isSpecial != ''"> and is_special =  #{isSpecial}</if>
        </where>
    </select>
    <select id="selectInfoById" resultType="com.gzairports.wl.departure.domain.vo.SecurityInfoWl">
        select id, waybill_code, shipping_agent as agent, shipper, special_cargo_code1,danger_code,
               cargo_name, quantity, weight, flight_no1,flight_date1,
               security_submit, security_url, declaration_consistent, is_examine, security_url,
               source_port, des_port, delivery_file_photo,delivery_id_no, delivery_profile_photo,
               shipper_signature, agent_signature, delivery_cargo_names, delivery_cargo_names_pdf
        from all_air_waybill
        where id = #{id}
    </select>
</mapper>
