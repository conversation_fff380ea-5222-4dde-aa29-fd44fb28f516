package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BaseCityCode;
import com.gzairports.common.basedata.domain.query.CityCodeQuery;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.basedata.service.ICityCodeService;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.common.basedata.service.ICityCodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 城市代码Controller
 *
 * <AUTHOR>
 * @date 2024-02-20
 */
@RestController
@RequestMapping("/base/city")
@Api(tags = "城市代码数据接口")
public class CityCodeController extends BaseController {

    @Autowired
    private ICityCodeService cityCodeService;

    /**
     * 查询城市代码列表
     */
//    @PreAuthorize("@ss.hasPermi('base:city:cityCodeList')")
    @GetMapping("/cityCodeList")
    @ApiOperation(value = "查询城市代码列表")
    public TableDataInfo cityCodeList(CityCodeQuery query)
    {
        startPage();
        List<BaseCityCode> list = cityCodeService.selectCityCodeList(query);
        return getDataTable(list);
    }

    /**
     * 导出城市代码
     */
    @PreAuthorize("@ss.hasPermi('base:city:exportCityCode')")
    @Log(title = "导出城市代码", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCityCode")
    @ApiOperation(value = "导出城市代码")
    public void exportCityCode(HttpServletResponse response, CityCodeQuery query)
    {
        List<BaseCityCode> list = cityCodeService.selectCityCodeList(query);
        ExcelUtil<BaseCityCode> util = new ExcelUtil<BaseCityCode>(BaseCityCode.class);
        util.exportExcel(response, list, "城市代码");
    }
}
