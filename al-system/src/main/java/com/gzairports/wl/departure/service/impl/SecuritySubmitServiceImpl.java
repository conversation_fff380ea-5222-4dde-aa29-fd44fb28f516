package com.gzairports.wl.departure.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.basedata.domain.AllSecurityUrl;
import com.gzairports.common.basedata.domain.BaseAgent;
import com.gzairports.common.basedata.service.IBaseAgentService;
import com.gzairports.common.business.departure.domain.Mawb;
import com.gzairports.common.business.departure.domain.vo.CargoInfoVo;
import com.gzairports.common.business.departure.domain.vo.IdentityInfoVo;
import com.gzairports.common.business.departure.mapper.MawbMapper;
import com.gzairports.common.core.domain.PageQuery;
import com.gzairports.common.core.domain.entity.SysDept;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.common.log.service.IWaybillLogService;
import com.gzairports.common.pdf.PdfPrintHelper;
import com.gzairports.common.rabbitmq.SecurityProducer;
import com.gzairports.common.securitySubmit.domain.AllSecurityWaybill;
import com.gzairports.common.securitySubmit.domain.SecuritySubmitSendFirst;
import com.gzairports.common.securitySubmit.domain.WaybillCargoListData;
import com.gzairports.common.securitySubmit.mapper.AllSecurityUrlMapper;
import com.gzairports.common.securitySubmit.mapper.AllSecurityWaybillMapper;
import com.gzairports.common.system.domain.SysConfig;
import com.gzairports.common.system.mapper.SysConfigMapper;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.sign.Base64;
import com.gzairports.common.utils.spring.SpringUtils;
import com.gzairports.hz.business.departure.domain.vo.SecurityVo;
import com.gzairports.hz.business.departure.domain.vo.WaybillInfoVo;
import com.gzairports.hz.business.departure.service.IHzCollectWaybillService;
import com.gzairports.wl.departure.domain.query.SecurityQueryWl;
import com.gzairports.wl.departure.domain.vo.SecurityInfoWl;
import com.gzairports.wl.departure.domain.vo.SecurityVoWl;
import com.gzairports.wl.departure.mapper.SecuritySubmitMapper;
import com.gzairports.wl.departure.service.ISecuritySubmitService;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: lan
 * @Desc: Service业务层处理
 * @create: 2024-11-14 12:00
 **/

@Service
public class SecuritySubmitServiceImpl implements ISecuritySubmitService {
    @Autowired
    private SecuritySubmitMapper securitySubmitMapper;

    @Autowired
    private MawbMapper mawbMapper;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    @Autowired
    private SecurityProducer securityProducer;

    @Autowired
    private AllSecurityUrlMapper allSecurityUrlMapper;

    @Autowired
    private AllSecurityWaybillMapper allSecurityWaybillMapper;

    @Autowired
    private SysConfigMapper configMapper;

    @Autowired
    private IWaybillLogService waybillLogService;

    private static final Logger logger = LoggerFactory.getLogger(SecuritySubmitServiceImpl.class);

    /**
     * 安检申报列表
     * */
    @Override
    public PageQuery<List<SecurityVoWl>> securitySubmitList(SecurityQueryWl query) {
        query.setDeptId(SecurityUtils.getHighParentId());
        List<SecurityVoWl> securityVoWls = securitySubmitMapper.securitySubmitList(query);
        Map<String, SecurityVoWl> securityVoWlMaps = new HashMap<>();
        securityVoWls.forEach(e->{
            if(StringUtils.isNull(e.getCargoType())){
                e.setCargoType(0);
                if(StringUtils.isNotEmpty(e.getSpecialCargoCode1())){
                    e.setCargoType(1);
                    e.setIsSpecial(1);
                }
                if(StringUtils.isNotEmpty(e.getDangerCode())){
                    e.setCargoType(2);
                }
            }
            if(!securityVoWlMaps.containsKey(e.getWaybillCode())){
                securityVoWlMaps.put(e.getWaybillCode(), e);
            }else{
                SecurityVoWl securityVoWl = securityVoWlMaps.get(e.getWaybillCode());
                //这里依旧展示安检申报准备新增的数据 不关联主单表的数据
                if(securityVoWl.getType() == 1 && e.getType() == 0){
                    securityVoWlMaps.put(e.getWaybillCode(),e);
                }
            }
            if(e.getSecuritySubmit() == 0 && (e.getSecuritySubmitWl() == 1 || e.getSecuritySubmitWl() == 2)){
                e.setIsRepeatSubmit(1);
            }else{
                e.setIsRepeatSubmit(0);
            }
        });
        List<SecurityVoWl> securityVoWlList = new ArrayList<>(securityVoWlMaps.values());
        int total = securityVoWlList.size();
        int pageNum = query.getStartRow();
        int pageSize = query.getEndRow();
        int start =(pageNum-1)*pageSize;
        int end = Math.min(start+pageSize,total);
        List<SecurityVoWl> pagedSecurityVos = securityVoWlList.subList(start, end);
        return new PageQuery<List<SecurityVoWl>>(
                pagedSecurityVos,
                pageNum,
                pageSize,
                total
        );
    }

    /**
     * 安检申报详情
     * */
    @Override
    public SecurityInfoWl getInfo(Long id) {
        SecurityInfoWl securityInfoWl = securitySubmitMapper.selectInfoById(id);
        if(securityInfoWl == null){
            SecurityInfoWl securityInfoWl1 = new SecurityInfoWl();
            AllSecurityWaybill allSecurityWaybill = allSecurityWaybillMapper.selectById(id);
            BeanUtils.copyProperties(allSecurityWaybill,securityInfoWl1);
            return securityInfoWl1;
        }
        securityInfoWl.setCargoType(0);
        securityInfoWl.setIsSpecial(0);
        if(StringUtils.isNotEmpty(securityInfoWl.getSpecialCargoCode1())){
            securityInfoWl.setCargoType(1);
            securityInfoWl.setIsSpecial(1);
        }
        if(StringUtils.isNotEmpty(securityInfoWl.getDangerCode())){
            securityInfoWl.setCargoType(2);
        }
        return securityInfoWl;
    }

    /**
     * 物流提交安检申报
     * */
    @Override
    public int submit(SecurityInfoWl securityInfoWl) {
        Mawb mawb = mawbMapper.selectById(securityInfoWl.getId());
        AllSecurityWaybill allSecurityWaybill = allSecurityWaybillMapper.selectOne(new QueryWrapper<AllSecurityWaybill>().eq("waybill_code", securityInfoWl.getWaybillCode()));
        if(mawb == null){
            allSecurityWaybill.setSecuritySubmit(0);
            allSecurityWaybill.setSecuritySubmitWl(1);
            allSecurityWaybillMapper.updateById(allSecurityWaybill);
            //往货检系统发消息
            SecuritySubmitSendFirst securitySubmitSendFirst = getSecuritySubmitSendFirstNew(allSecurityWaybill,securityInfoWl);
            securityProducer.sendFirst(securitySubmitSendFirst);
            //记录到历史
            AllSecurityUrl allSecurityUrl = new AllSecurityUrl();
            allSecurityUrl.setWaybillId(null);
            allSecurityUrl.setWaybillCode(allSecurityWaybill.getWaybillCode());
            allSecurityUrl.setSecurityUrl(allSecurityWaybill.getSecurityUrl());
            allSecurityUrl.setCreateBy(SecurityUtils.getNickName());
            allSecurityUrl.setCreateTime(new Date());
            allSecurityUrlMapper.insert(allSecurityUrl);
            return 1;
        }else {
            //状态改为已提交
            mawb.setSecuritySubmit(0);
            mawb.setSecuritySubmitWl(1);
            mawbMapper.updateById(mawb);
            if(allSecurityWaybill != null){
                allSecurityWaybill.setSecuritySubmit(0);
                allSecurityWaybill.setSecuritySubmitWl(1);
                allSecurityWaybillMapper.updateById(allSecurityWaybill);
            }
            //往货检系统发消息
            SecuritySubmitSendFirst securitySubmitSendFirst = getSecuritySubmitSendFirst(mawb, securityInfoWl);
            securityProducer.sendFirst(securitySubmitSendFirst);
            //记录到历史
            AllSecurityUrl allSecurityUrl = new AllSecurityUrl();
            allSecurityUrl.setWaybillId(mawb.getId());
            allSecurityUrl.setWaybillCode(mawb.getWaybillCode());
            allSecurityUrl.setSecurityUrl(mawb.getSecurityUrl());
            allSecurityUrl.setCreateBy(SecurityUtils.getNickName());
            allSecurityUrl.setCreateTime(new Date());
            allSecurityUrlMapper.insert(allSecurityUrl);

            WaybillLog waybillLog = waybillLogService.getWaybillLog(
                    mawb.getWaybillCode(), 0, SecurityUtils.getNickName(),
                    mawb.getWeight()!=null ? mawb.getWeight().toString() : null,
                    mawb.getQuantity()!=null ? mawb.getQuantity().toString() : null,
                    mawb.getFlightNo1(), mawb.getWaybillCode(), null, 0, null, new Date(),
                    "安检提交(代理人操作)", "DEP", null);
            waybillLogService.insertWaybillLog(waybillLog);

            return 1;
        }
    }


    /**
     * 代理人重复提交品名清单
     * */
    @Override
    public int submitRepeat(SecurityInfoWl securityInfoWl) {
        Mawb mawb = mawbMapper.selectById(securityInfoWl.getId());
        WaybillCargoListData waybillCargoListData = new WaybillCargoListData();
        waybillCargoListData.setType(6);
        waybillCargoListData.setWaybillCode(mawb.getWaybillCode().substring(4));
        List<String> cargoNameList = new ArrayList<>();
        if(securityInfoWl.getDeliveryCargoNames() != null){
            String[] fileSplit = securityInfoWl.getDeliveryCargoNames().split(",");
            for(String s:fileSplit){
                cargoNameList.add(s.trim());
            }
        }
        if(securityInfoWl.getDeliveryCargoNamesPdf() != null){
            String[] filePdfsSplit = securityInfoWl.getDeliveryCargoNamesPdf().split(",");
            for(String s:filePdfsSplit){
                cargoNameList.add(s.trim());
            }
        }
        waybillCargoListData.setCargoNameList(cargoNameList);
        securityProducer.sendOtherWaybill(waybillCargoListData,mawb.getWaybillCode(),"代理人重复提交品名清单附件");
        mawb.setDeliveryCargoNames(securityInfoWl.getDeliveryCargoNames());
        mawb.setDeliveryCargoNamesPdf(securityInfoWl.getDeliveryCargoNamesPdf());
        return mawbMapper.updateById(mawb);
    }

    @Override
    public List<AllSecurityUrl> getHistoryList(Long id) {
        List<AllSecurityUrl> urlList = allSecurityUrlMapper.selectList(new QueryWrapper<AllSecurityUrl>()
                .eq("waybill_id", id));
        AllSecurityWaybill allSecurityWaybill = allSecurityWaybillMapper.selectById(id);
        if(allSecurityWaybill != null){
            List<AllSecurityUrl> urlList2 = allSecurityUrlMapper.selectList(new QueryWrapper<AllSecurityUrl>()
                    .eq("waybill_code", allSecurityWaybill.getWaybillCode()));
            urlList.addAll(urlList2);
        }
        Map<Long, AllSecurityUrl> urlListVoMaps = new HashMap<>();
        urlList.forEach(e->{
            if(!urlListVoMaps.containsKey(e.getId())){
                urlListVoMaps.put(e.getId(), e);
            }else{
                AllSecurityUrl urlVo = urlListVoMaps.get(e.getId());
                //这里依旧展示安检申报准备新增的数据 不关联主单表的数据
                if(urlVo.getWaybillId() != null && e.getWaybillId() == null){
                    urlListVoMaps.put(e.getId(),e);
                }
            }
        });
        return new ArrayList<>(urlListVoMaps.values());
    }

    @Override
    public int addSecurity(AllSecurityWaybill allSecurityWaybill) {
        Mawb mawb = mawbMapper.selectOne(new QueryWrapper<Mawb>().eq("waybill_code", allSecurityWaybill.getWaybillCode()));
        AllSecurityWaybill waybill = allSecurityWaybillMapper.selectOne(new QueryWrapper<AllSecurityWaybill>().eq("waybill_code", allSecurityWaybill.getWaybillCode()));
        if(mawb != null || waybill != null){
            throw new CustomException("当前运单号已存在,新增失败");
        }
        allSecurityWaybill.setDeptId(SecurityUtils.getHighParentId());
        allSecurityWaybill.setCreateBy(SecurityUtils.getNickName());
        allSecurityWaybill.setCreateTime(new Date());
        return allSecurityWaybillMapper.insert(allSecurityWaybill);
    }

    /**
     * 组装第一次需要发送的数据
     * */
    private SecuritySubmitSendFirst getSecuritySubmitSendFirst(Mawb mawb,SecurityInfoWl securityInfoWl){
        SecuritySubmitSendFirst securitySubmitSendFirst = new SecuritySubmitSendFirst();
        SysDept dept = sysDeptMapper.selectDeptById(mawb.getDeptId());
        if(mawb.getDomint() == null){
            securitySubmitSendFirst.setFreightStation("0");
        }else{
            securitySubmitSendFirst.setFreightStation(mawb.getDomint().equals("I")?"1":"0");
        }
        securitySubmitSendFirst.setFlightNo(mawb.getFlightNo1());
        securitySubmitSendFirst.setExecDate(mawb.getFlightDate1());
        securitySubmitSendFirst.setWaybillCode(mawb.getWaybillCode().substring(4));
        securitySubmitSendFirst.setNature("1");
        if(StringUtils.isNotEmpty(mawb.getSpecialCargoCode1())){
            securitySubmitSendFirst.setNature("2");
        }
        if(StringUtils.isNotEmpty(mawb.getDangerCode())){
            securitySubmitSendFirst.setNature("3");
        }
        securitySubmitSendFirst.setAgentName(StringUtils.isNotEmpty(mawb.getShippingAgent()) ? mawb.getShippingAgent() : mawb.getAgentCompany());
        securitySubmitSendFirst.setShipper(mawb.getAgentCompany());

//        List<String> securityPdfUrls = new ArrayList<>();
//        String[] securityPdfUrlsSplit = mawb.getSecurityUrl().split(",");
//        for(String s:securityPdfUrlsSplit){
//            securityPdfUrls.add(s.trim());
//        }
//        securitySubmitSendFirst.setSecurityPdfUrls(securityPdfUrls);

//        SysConfig sysConfig = configMapper.checkConfigKeyUnique("security.submit.createUser");
//        if (sysConfig != null){
//            securitySubmitSendFirst.setCreateUser(sysConfig.getConfigValue());
//        }else {
//            securitySubmitSendFirst.setCreateUser(SecurityUtils.getNickName());
//        }
//        securitySubmitSendFirst.setCreateUser(SecurityUtils.getNickName());
        securitySubmitSendFirst.setCreateUser(dept.getCreateUser());

        List<String> transportFilePdfs = new ArrayList<>();
        if(securityInfoWl.getDeliveryFilePhoto() != null){
            String[] transportFilePdfsSplit = securityInfoWl.getDeliveryFilePhoto().split(",");
            for(String s:transportFilePdfsSplit){
                transportFilePdfs.add(s.trim());
            }
        }
        securitySubmitSendFirst.setTransportFilePdfs(transportFilePdfs);

        List<String> cargoNameList = new ArrayList<>();
        if(securityInfoWl.getDeliveryCargoNames() != null){
            String[] fileSplit = securityInfoWl.getDeliveryCargoNames().split(",");
            for(String s:fileSplit){
                cargoNameList.add(s.trim());
            }
        }
        if(securityInfoWl.getDeliveryCargoNamesPdf() != null){
            String[] filePdfsSplit = securityInfoWl.getDeliveryCargoNamesPdf().split(",");
            for(String s:filePdfsSplit){
                cargoNameList.add(s.trim());
            }
        }
        securitySubmitSendFirst.setCargoNameList(cargoNameList);

        IdentityInfoVo identityInfoVo = new IdentityInfoVo();
        SysDept sysDept = sysDeptMapper.selectDeptById(mawb.getDeptId());
        identityInfoVo.setUsername(sysDept.getDeptName());
        identityInfoVo.setNation(null);
        identityInfoVo.setBirthday(null);
        identityInfoVo.setIdNumber(null);
        identityInfoVo.setIdUrl(null);
        identityInfoVo.setSex(SecurityUtils.getLoginUser().getUser().getSex());
        identityInfoVo.setAddress(sysDept.getAddress());
        securitySubmitSendFirst.setIdentityInfoVo(identityInfoVo);

        List<CargoInfoVo> cargoInfoVos = new ArrayList<>();
        CargoInfoVo cargoInfoVo = new CargoInfoVo();
        cargoInfoVo.setCargoName(mawb.getCargoName());
        cargoInfoVo.setQuantity(mawb.getQuantity());
        cargoInfoVo.setWeight(mawb.getWeight());
        cargoInfoVo.setDesPort(mawb.getDesPort());
        cargoInfoVo.setStartPort(mawb.getSourcePort());
        cargoInfoVos.add(cargoInfoVo);
        securitySubmitSendFirst.setCargoInfoVos(cargoInfoVos);
        securitySubmitSendFirst.setMessageType("货单数据");
        return securitySubmitSendFirst;
    }

    /**
     * 组装第一次发送需要的数据
     * */
    private SecuritySubmitSendFirst getSecuritySubmitSendFirstNew(AllSecurityWaybill allSecurityWaybill, SecurityInfoWl securityInfoWl) {
        SecuritySubmitSendFirst securitySubmitSendFirst = new SecuritySubmitSendFirst();
        securitySubmitSendFirst.setFlightNo(allSecurityWaybill.getFlightNo1());
        securitySubmitSendFirst.setExecDate(allSecurityWaybill.getFlightDate1());
        securitySubmitSendFirst.setWaybillCode(allSecurityWaybill.getWaybillCode().substring(4));
        if("普通货物".equals(allSecurityWaybill.getCargoType())){
            securitySubmitSendFirst.setNature("1");
        } else if ("特殊货物".equals(allSecurityWaybill.getCargoType())) {
            securitySubmitSendFirst.setNature("2");
        } else if ("危险品".equals(allSecurityWaybill.getCargoType())) {
            securitySubmitSendFirst.setNature("3");
        }
        securitySubmitSendFirst.setAgentName(allSecurityWaybill.getAgent());
        securitySubmitSendFirst.setShipper(allSecurityWaybill.getShipper());

//        List<String> securityPdfUrls = new ArrayList<>();
//        String[] securityPdfUrlsSplit = mawb.getSecurityUrl().split(",");
//        for(String s:securityPdfUrlsSplit){
//            securityPdfUrls.add(s.trim());
//        }
//        securitySubmitSendFirst.setSecurityPdfUrls(securityPdfUrls);

//        SysConfig sysConfig = configMapper.checkConfigKeyUnique("security.submit.createUser");
//        if (sysConfig != null){
//            securitySubmitSendFirst.setCreateUser(sysConfig.getConfigValue());
//        }else {
//            securitySubmitSendFirst.setCreateUser(allSecurityWaybill.getCreateBy());
//        }
        securitySubmitSendFirst.setCreateUser(SecurityUtils.getNickName());

        List<String> transportFilePdfs = new ArrayList<>();
        if(securityInfoWl.getDeliveryFilePhoto() != null){
            String[] transportFilePdfsSplit = securityInfoWl.getDeliveryFilePhoto().split(",");
            for(String s:transportFilePdfsSplit){
                transportFilePdfs.add(s.trim());
            }
        }
        securitySubmitSendFirst.setTransportFilePdfs(transportFilePdfs);

        List<String> cargoNameList = new ArrayList<>();
        if(securityInfoWl.getDeliveryCargoNames() != null){
            String[] fileSplit = securityInfoWl.getDeliveryCargoNames().split(",");
            for(String s:fileSplit){
                cargoNameList.add(s.trim());
            }
        }
        if(securityInfoWl.getDeliveryCargoNamesPdf() != null){
            String[] filePdfsSplit = securityInfoWl.getDeliveryCargoNamesPdf().split(",");
            for(String s:filePdfsSplit){
                cargoNameList.add(s.trim());
            }
        }
        securitySubmitSendFirst.setCargoNameList(cargoNameList);

        IdentityInfoVo identityInfoVo = new IdentityInfoVo();
        SysDept sysDept = sysDeptMapper.selectDeptById(allSecurityWaybill.getDeptId());
        identityInfoVo.setUsername(sysDept.getDeptName());
        identityInfoVo.setNation(null);
        identityInfoVo.setBirthday(null);
        identityInfoVo.setIdNumber(null);
        identityInfoVo.setIdUrl(null);
        identityInfoVo.setSex(SecurityUtils.getLoginUser().getUser().getSex());
        identityInfoVo.setAddress(sysDept.getAddress());
        securitySubmitSendFirst.setIdentityInfoVo(identityInfoVo);

        List<CargoInfoVo> cargoInfoVos = new ArrayList<>();
        CargoInfoVo cargoInfoVo = new CargoInfoVo();
        cargoInfoVo.setCargoName(allSecurityWaybill.getCargoName());
        cargoInfoVo.setQuantity(allSecurityWaybill.getQuantity());
        cargoInfoVo.setWeight(allSecurityWaybill.getWeight());
        cargoInfoVo.setDesPort(allSecurityWaybill.getDesPort());
        cargoInfoVo.setStartPort(allSecurityWaybill.getSourcePort());
        cargoInfoVos.add(cargoInfoVo);
        securitySubmitSendFirst.setCargoInfoVos(cargoInfoVos);
        securitySubmitSendFirst.setMessageType("货单数据");
        return securitySubmitSendFirst;
    }

}
