package com.gzairports.hz.business.cable.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.poi.ss.formula.functions.T;

import java.util.Date;
import java.util.List;

/**
 * @author: lan
 * @Desc: QHDB弃货电报 GMDB改名电报 FSH特种货物报文 公用对象
 * @create: 2025-04-15 13:41
 **/

@Data
public class QHDBJsonVO {
    /** 承运人 */
    private String carrier;

    /** 航班号 */
    private String flightNo;

    /** 预计起飞时间 */
    private String etd;

    /** 起运机场 */
    private String oriAirport;

    /** 到达机场 */
    private String arrivalAirport;

    private List<QHDBDetailVO> consignmentDetail;

    /** 弃货详情 */
    private String qhInfo;

    /** 改名报文详情 */
    private String chgInfo;

}
