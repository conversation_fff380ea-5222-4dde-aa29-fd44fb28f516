package com.gzairports.wl.charge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.basedata.domain.BaseAirportCode;
import com.gzairports.common.basedata.domain.BaseBillType;
import com.gzairports.common.basedata.domain.BaseCargoCategory;
import com.gzairports.common.basedata.domain.BaseCargoCode;
import com.gzairports.common.basedata.mapper.AirportCodeMapper;
import com.gzairports.common.basedata.mapper.BillTypeMapper;
import com.gzairports.common.basedata.mapper.CargoCategoryMapper;
import com.gzairports.common.basedata.mapper.CargoCodeMapper;
import com.gzairports.common.exception.ServiceException;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.wl.charge.domain.FreightRate;
import com.gzairports.wl.charge.domain.FreightRateAirItem;
import com.gzairports.wl.charge.domain.FreightRateCustom;
import com.gzairports.wl.charge.domain.FreightRateItem;
import com.gzairports.wl.charge.domain.query.FreightRateQuery;
import com.gzairports.wl.charge.domain.vo.FreightRateItemVO;
import com.gzairports.wl.charge.domain.vo.FreightRateVO;
import com.gzairports.wl.charge.mapper.FreightRateItemMapper;
import com.gzairports.wl.charge.mapper.FreightRateMapper;
import com.gzairports.wl.charge.service.FreightRateService;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 公布运价service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@Service
public class FreightRateServiceImpl implements FreightRateService {

    @Autowired
    private FreightRateMapper rateMapper;

    @Autowired
    private FreightRateItemMapper rateItemMapper;

    @Autowired
    private BillTypeMapper billTypeMapper;

    @Autowired
    private CargoCategoryMapper cargoCategoryMapper;

    @Autowired
    private CargoCodeMapper cargoCodeMapper;

    @Autowired
    private AirportCodeMapper airportCodeMapper;


    /**
     * 查询公布运价列表
     *
     * @param query 查询参数
     * @return 公布运价列表
     */
    @Override
    public List<FreightRateVO> selectList(FreightRateQuery query) {
        //查询票证类型并存入集合,仅查询一次
        if (DOCUMENTMAP.isEmpty()) {
            selectTypeList();
        }
        query.setDeptId(SecurityUtils.getHighParentId());
        List<FreightRateVO> freightRateVOS = rateMapper.selectListByQuery(query);
        for (FreightRateVO freightRateVO : freightRateVOS) {
            freightRateVO.setApplicable(DOCUMENTMAP.get(freightRateVO.getApplicableDocuments()));
            freightRateVO.setAutomatic(freightRateVO.getAutomaticFromLow() == 0 ? "否":"是");
            freightRateVO.setEdit(freightRateVO.getEditable() == 0 ? "否":"是");
            freightRateVO.setRounding(ROUNDRULE.get(freightRateVO.getRoundingMethod()));
        }
        return freightRateVOS;
    }

    /**
     * 查询公布运价
     *
     * @param id 公布运价主键
     * @return 公布运价
     */
    @Override
    public FreightRateVO selectRateById(Long id) {
        return rateMapper.selectRateById(id);
    }

    /**
     * 新增公布运价
     *
     * @param rate 公布运价
     * @return 结果
     */
    @Override
    public Long insertFreightRate(FreightRate rate) {
        //适用单证类型与生效时间不能同时一致
        List<FreightRate> list = rateMapper.selectList(new QueryWrapper<FreightRate>()
                .eq("dept_id",SecurityUtils.getHighParentId())
                .eq("is_del",0));

        if (rate.getEffectiveDate().after(rate.getExpirationDate())){
            throw new CustomException("生效时间不能晚于截止时间");
        }

        for (FreightRate freightRateCustom : list) {
            if ((freightRateCustom.getEffectiveDate().before(rate.getEffectiveDate()) &&
                    freightRateCustom.getExpirationDate().after(rate.getEffectiveDate()))
                    || (freightRateCustom.getEffectiveDate().before(rate.getExpirationDate()) &&
                            freightRateCustom.getExpirationDate().after(rate.getExpirationDate()))
            ){
                throw new CustomException("运价生效和截止日期不能重叠");
            }
        }


        rate.setCreateBy(SecurityUtils.getUsername());
        rate.setCreateTime(DateUtils.getNowDate());
        rate.setDeptId(SecurityUtils.getHighParentId());
        rateMapper.insertFreightRate(rate);
        return rate.getId();
    }

    /**
     * 修改公布运价
     *
     * @param rate 公布运价
     * @return 结果
     */
    @Override
    public int updateFreightRate(FreightRate rate) {
        //适用单证类型与生效时间不能同时一致
        List<FreightRate> list = rateMapper.selectList(new QueryWrapper<FreightRate>()
                .eq("dept_id",SecurityUtils.getHighParentId())
                .eq("is_del",0));
        //生效时间必须早于截止时间
        if (rate.getEffectiveDate().after(rate.getExpirationDate())){
            throw new CustomException("生效时间不能晚于截止时间");
        }
        if (!CollectionUtils.isEmpty(list)){
            List<FreightRate> collect = list.stream().filter(e -> !e.getId().equals(rate.getId())).collect(Collectors.toList());
            for (FreightRate freightRateCustom : collect) {
                if ((freightRateCustom.getEffectiveDate().before(rate.getEffectiveDate()) &&
                        freightRateCustom.getExpirationDate().after(rate.getEffectiveDate()))
                        || (freightRateCustom.getEffectiveDate().before(rate.getExpirationDate()) &&
                        freightRateCustom.getExpirationDate().after(rate.getExpirationDate()))
                ){
                    throw new CustomException("运价生效和截止日期不能重叠");
                }
            }
        }

        rate.setUpdateBy(SecurityUtils.getUsername());
        rate.setUpdateTime(DateUtils.getNowDate());
        return rateMapper.updateFreightRate(rate);
    }

    /**
     * 删除公布运价
     *
     * @param id 公布运价主键
     * @return 结果
     */
    @Override
    public int deleteFreightRate(Long id) {
        FreightRate freightRate = rateMapper.selectById(id);
        Date date = new Date();
        if (freightRate.getEffectiveDate().before(date) && freightRate.getExpirationDate().after(date)){
            throw new CustomException("在有效期内的运价不能删除");
        }
        return rateMapper.removeFreightRate(id);
    }

    /**
     * 查看公布运价条目
     *
     * @param id 公布运价id
     * @return 公布运价条目集合
     */
    @Override
    public List<FreightRateItemVO> selectRateItemList(Long id) {
        return rateItemMapper.selectRateItemList(id);
    }

    /**
     * 导入公布运价条目
     * @param list 文件数据
     * @param rateId 导入所需数据
     * @param updateSupport 导入所需数据
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long importItem(List<FreightRateItem> list, Long rateId, boolean updateSupport) {
        if (StringUtils.isNull(list) || list.size() == 0)
        {
            throw new ServiceException("导入公布运价条目数据不能为空！");
        }
        for (int i = 0; i < list.size(); i++) {
            int j = i + 1;
            if (list.get(i).getDepartureCity() == null){
                throw new ServiceException("第" + j + "条数据导入失败，始发城市为空");
            }
            if (list.get(i).getDestinationCity() == null){
                throw new ServiceException("第" + j + "条数据导入失败，目的城市为空");
            }
            if (list.get(i).getMinimumFreight() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算M为空");
            }
            if (list.get(i).getRateWeightRange5() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算5为空");
            }
            if (list.get(i).getRateWeightRange10() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算10为空");
            }
            if (list.get(i).getBaseRateN() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算N为空");
            }
            if (list.get(i).getRateWeightRange45() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算45为空");
            }
            if (list.get(i).getRateWeightRange100() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算100为空");
            }
            if (list.get(i).getRateWeightRange300() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算300为空");
            }
            if (list.get(i).getRateWeightRange500() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算500为空");
            }
            if (list.get(i).getRateWeightRange1000() == null){
                throw new ServiceException("第" + j + "条数据导入失败，结算1000为空");
            }
//            List<BaseAirportCode> desPort = airportCodeMapper.selectCityByName(list.get(i).getDestinationCity());
            BaseAirportCode desPort = airportCodeMapper.selectOne(new QueryWrapper<BaseAirportCode>()
                    .eq("chinese_name", list.get(i).getDestinationCity()));
            if(desPort == null){
                throw new ServiceException("第" + j + "条数据导入失败，目的城市 " + list.get(i).getDestinationCity() + " 基础数据机场代码里未维护");
            }
//            for (BaseAirportCode baseAirportCode : desPort) {
                FreightRateItem rateItem = rateItemMapper.selectOne(new QueryWrapper<FreightRateItem>()
                        .eq("clause_name", list.get(i).getClauseName())
                        .eq("rate_id",rateId));
                if (rateItem != null){
                    throw new ServiceException("第" + j + "条数据导入失败、条目名称 " + list.get(i).getClauseName() + " 已存在");
                }
                FreightRateItem item = new FreightRateItem();
                BeanUtils.copyProperties(list.get(i),item);
                LoginUser loginUser = SecurityUtils.getLoginUser();
                item.setRateId(rateId);
                item.setDepartureCity("KWE");
                item.setDestinationCity(desPort.getCode());
                item.setCreateBy(loginUser.getUsername());
                item.setCreateTime(DateUtils.getNowDate());
                item.setDeptId(SecurityUtils.getHighParentId());
                if(list.get(i).getProductCode() != null && !"".equals(list.get(i).getProductCode())){
                    String[] split = list.get(i).getProductCode().split(";");
                    List<String> cargoCategoryList = new ArrayList<>();
                    List<String> productCodeList = new ArrayList<>();
                    for(String s:split){
                        BaseCargoCode cargoCode = cargoCodeMapper.selectCodeByName(s);
                        if(cargoCode != null){
                            //表格里面的货品代码填的是具体货品
                            BaseCargoCategory code = cargoCategoryMapper.selectOne(new QueryWrapper<BaseCargoCategory>()
                                    .eq("code", cargoCode.getCategoryCode()));
                            cargoCategoryList.add(code.getCode());
                            productCodeList.add(cargoCode.getCode());
                        }else{
                            List<BaseCargoCategory> code = cargoCategoryMapper.selectList(new QueryWrapper<BaseCargoCategory>()
                                    .eq("chinese_name", s));
                            if(code != null && code.size() > 0){
                                //表格里面填的是大类
                                List<String> cargoCategoryCollect = code.stream().map(BaseCargoCategory::getCode).collect(Collectors.toList());
                                cargoCategoryList.addAll(cargoCategoryCollect);
                            }else{
                                throw new ServiceException("导入失败，第"+ j +"条数据识别失败，货物种类 "+ s +" 不存在，请修改后重新导入");
                            }
                        }
                    }
                    List<String> collect = cargoCategoryList.stream().distinct().collect(Collectors.toList());
                    List<String> collect2 = productCodeList.stream().distinct().collect(Collectors.toList());
                    item.setCargoCategory(String.join(",",collect));
                    item.setProductCode(String.join(",",collect2));
                }
                item.setSpecialCargoCode(list.get(i).getSpecialCargoCode());
                rateItemMapper.insert(item);
//                if (StringUtils.isEmpty(list.get(i).getProductCode())){
//                    FreightRateItem item = new FreightRateItem();
//                    BeanUtils.copyProperties(list.get(i),item);
//                    LoginUser loginUser = SecurityUtils.getLoginUser();
//                    item.setRateId(rateId);
//                    item.setDepartureCity("KWE");
//                    item.setDestinationCity(baseAirportCode.getCode());
//                    item.setCreateBy(loginUser.getUsername());
//                    item.setCreateTime(DateUtils.getNowDate());
//                    item.setDeptId(SecurityUtils.getHighParentId());
//                    rateItemMapper.insert(item);
//                }else {
//                    String[] split = list.get(i).getProductCode().split(";");
//                    for (String s : split) {
//                        List<BaseCargoCategory> categories = cargoCategoryMapper.selectList(new QueryWrapper<BaseCargoCategory>().eq("chinese_name", s));
//                        if (!CollectionUtils.isEmpty(categories)){
//                            for (BaseCargoCategory category : categories) {
//                                FreightRateItem item = new FreightRateItem();
//                                BeanUtils.copyProperties(list.get(i),item);
//                                LoginUser loginUser = SecurityUtils.getLoginUser();
//                                item.setRateId(rateId);
//                                item.setDepartureCity("KWE");
//                                item.setCargoCategory(category.getCode());
//                                item.setProductCode(null);
//                                item.setDestinationCity(baseAirportCode.getCode());
//                                item.setCreateBy(loginUser.getUsername());
//                                item.setCreateTime(DateUtils.getNowDate());
//                                item.setDeptId(SecurityUtils.getHighParentId());
//                                rateItemMapper.insert(item);
//                            }
//                        }else {
//                            List<String> codes = new ArrayList<>();
//                            List<String> categoryCodes = new ArrayList<>();
//                            BaseCargoCode cargoCode = cargoCodeMapper.selectCodeByName(s);
//                            if (cargoCode == null){
//                                throw new ServiceException("导入失败，第"+ j +"条数据识别失败，货物种类 "+ s +" 不存在，请修改后重新导入");
//                            }else {
//                                codes.add(cargoCode.getCode());
//                                categoryCodes.add(cargoCode.getCategoryCode());
//                            }
//                            FreightRateItem item = new FreightRateItem();
//                            BeanUtils.copyProperties(list.get(i),item);
//                            LoginUser loginUser = SecurityUtils.getLoginUser();
//                            item.setRateId(rateId);
//                            item.setDepartureCity("KWE");
//                            item.setProductCode(String.join(",",codes));
//                            item.setCargoCategory(String.join(",",categoryCodes));
//                            item.setDestinationCity(baseAirportCode.getCode());
//                            item.setCreateBy(loginUser.getUsername());
//                            item.setCreateTime(DateUtils.getNowDate());
//                            item.setDeptId(SecurityUtils.getHighParentId());
//                            rateItemMapper.insert(item);
//                        }
//                    }
//                }
//         }
        }
        return rateId;
    }

    /**
     * 查询所有票证类型
     *
     * @return 结果
     */
    public void selectTypeList() {
        List<BaseBillType> baseBillTypes = billTypeMapper.selectTypeList();
        for (BaseBillType baseBillType:baseBillTypes) {
            String code = baseBillType.getCode();
            String name = baseBillType.getName();
            DOCUMENTMAP.put(code,name);
        }
    }

    private static final Map<String,String> DOCUMENTMAP = new HashMap<>();


    private static final Map<Integer,String> ROUNDRULE = new HashMap<>();
    static {
        ROUNDRULE.put(0,"实际");
        ROUNDRULE.put(1,"四舍五入至个位");
        ROUNDRULE.put(2,"四舍五入至0.1");
        ROUNDRULE.put(3,"四舍五入至0.01");
        ROUNDRULE.put(4,"四舍五入至0.001");
        ROUNDRULE.put(5,"直接进位到0.1");
        ROUNDRULE.put(6,"直接舍位到0.1");
        ROUNDRULE.put(7,"直接进位到个位");
        ROUNDRULE.put(8,"直接舍位到个位");
        ROUNDRULE.put(9,"直接进位到0.5");
        ROUNDRULE.put(10,"直接舍位到0.5");
    }
}
