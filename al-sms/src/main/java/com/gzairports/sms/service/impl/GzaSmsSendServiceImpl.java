package com.gzairports.sms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.utils.bean.BeanUtils;
import com.gzairports.common.utils.sign.Md5Utils;
import com.gzairports.sms.domain.HzAgentUser;
import com.gzairports.sms.domain.SmsSendLogPo;
import com.gzairports.sms.domain.dto.*;
import com.gzairports.sms.domain.vo.SmsReceiveUserVo;
import com.gzairports.sms.mapper.sms.SmsSendMapper;
import com.gzairports.sms.service.ISmsSendService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Description: 贵州机场集团发短信实现类
 * @date 2022/6/1 17:04
 */
@Slf4j
@Component
public class GzaSmsSendServiceImpl implements ISmsSendService {

    /**
     * 短信请求url
     */
    @Value("${sms.address}")
    private String SMS_SEND_ADDRESS;

    /**
     * appid
     */
    @Value("${sms.appId}")
    private String SMS_APP_ID;

    /***
     * appSercret
     */
    @Value("${sms.appSecret}")
    private String SMS_APP_SECRET;

    @Autowired
    private SmsSendMapper smsSendMapper;

    @Autowired
    private SmsSendLogService smsSendLogService;

    /**
     * 通过模板的方式发送短信
     * @param param 消息发送参数
     */
    @Override
    public void sendSms(SmsSendParameter param){
        long timestamp = System.currentTimeMillis();
        String authCode = Md5Utils.hash(SMS_APP_ID+SMS_APP_SECRET+timestamp);
        SmsSendBo smsSendBo = new SmsSendBo(param.getMobile(), param.getSmsParameter());
        String url = SMS_SEND_ADDRESS+"?appId="+SMS_APP_ID+"&timestamp="+timestamp+"&authCode="+authCode+"&configCode="+param.getConfigCode();
        HttpResponse response = HttpRequest.post(url).body(JSON.toJSONString(smsSendBo)).execute();
        String resultBody = response.body();
        log.info("发起http请求，url：{}，方法：POST，参数：{}，返回信息：{}",url,JSON.toJSONString(smsSendBo),resultBody);
        SmsSendLogPo smsSendLogPo = new SmsSendLogPo();
        smsSendLogPo.setSmsParameter(param.getSmsParameter());
        smsSendLogPo.setContent(param.getContent());
        smsSendLogPo.setMobile(param.getMobile());
        if (param.getBillId() != null){
            smsSendLogPo.setBillId(param.getBillId());
        }
        smsSendLogPo.setModuleName(param.getModuleName());
        smsSendLogPo.setMsgType(SmsSendLogPo.TEMPLATE_MSG);
        JSONObject resultObject = JSON.parseObject(resultBody);

        smsSendLogPo.setResultCode(resultObject.getInteger("code"));
        smsSendLogPo.setResultMsg(resultBody);
        smsSendLogPo.setSmsConfigCode(param.getConfigCode());
        smsSendLogPo.setCreateTime(new Date());
        smsSendLogService.insertSmsSendLog(smsSendLogPo);
    }

    /**
     * 记录发送日志
     *
     * @param smsSendLogPo 消息发送日志对象
     * @return
     */
    @Override
    public int insertSmsSendLog(SmsSendLogPo smsSendLogPo) {
        //todo 没有记录发送日志
        //return smsSendLogMapper.insert(smsSendLogPo);
        return 0;
    }

    /**
     * 查询短信发送历史记录列表
     *
     * @param query 短信发送历史记录
     * @return 短信发送历史记录列表
     */
    @Override
    public List<SmsSendLogPo> selectSmsSendLogList(SmsSendQuery query) {
        return smsSendLogService.selectSmsSendLogList(query);
    }

    /**
     * 查询短信发送历史记录
     *
     * @param smsSendLogId 短信发送历史记录ID
     * @return 短信发送历史记录
     */
    @Override
    public SmsSendLogPo selectSmsSendLogById(Long smsSendLogId) {
        return smsSendLogService.selectSmsSendLogById(smsSendLogId);
    }

    /**
     * 重新发送消息
     * @param po 消息发送对象
     * @return 接口消息
     */
    @Override
    public String resend(SmsSendLogPo po) {
        long timestamp = System.currentTimeMillis();
        String authCode = Md5Utils.hash(SMS_APP_ID+SMS_APP_SECRET+timestamp);
        SmsSendBo smsSendBo = new SmsSendBo(po.getMobile(),po.getSmsParameter());
        String url = SMS_SEND_ADDRESS+"?appId="+SMS_APP_ID+"&timestamp="+timestamp+"&authCode="+authCode+"&configCode="+po.getSmsConfigCode();
        HttpResponse response = HttpRequest.post(url).body(JSON.toJSONString(smsSendBo)).execute();
        String resultBody = response.body();
        log.info("发起http请求，url：{}，方法：POST，参数：{}，返回信息：{}",url,JSON.toJSONString(smsSendBo),resultBody);
        po.setMsgType(SmsSendLogPo.TEMPLATE_MSG);
        JSONObject resultObject = JSON.parseObject(resultBody);

        po.setResultCode(resultObject.getInteger("code"));
        po.setResultMsg(resultBody);
        po.setUpdateTime(new Date());
        smsSendLogService.saveOrUpdate(po);
        return resultBody;
    }

    @Override
    public void configAgentSendSmsUser(List<HzAgentUserDto> agentUserDtoList) {
        if(CollUtil.isNotEmpty(agentUserDtoList)){
            List<Long> agentIds = agentUserDtoList.stream().map(HzAgentUserDto::getAgentId)
                    .distinct()
                    .collect(Collectors.toList());
            //删除旧配置
            smsSendMapper.deleteAgentSendSmsConfig(agentIds);
        }else {
            return;
        }
        List<HzAgentUser> agentUserList = BeanUtils.copyBeanList(agentUserDtoList, HzAgentUser.class);
        int isSuccess = smsSendMapper.agentSendSmsConfig(agentUserList);
        if(!SqlHelper.retBool(isSuccess)){
            throw new CustomException("代理人配置发送短信用户失败");
        }
    }

    @Override
    public List<SmsReceiveUserVo> listSmsReceiveUser(SmsReceiveUserDto smsReceiveUserDto) {
        List<SmsReceiveUserVo> smsReceiveUserVoList = smsSendMapper.selectSmsReceiveUser(smsReceiveUserDto);
        //todo 标记已配置用户
        return smsReceiveUserVoList;
    }

    private static final Map<String,String> FLIGHT_STATUS  = new LinkedHashMap<>();
    static {
        FLIGHT_STATUS.put("ALT","备降");
        FLIGHT_STATUS.put("ARR","到达");
        FLIGHT_STATUS.put("BAK","滑回");
        FLIGHT_STATUS.put("BOR","登机");
        FLIGHT_STATUS.put("CAN","取消");
        FLIGHT_STATUS.put("CKI","正在值机");
        FLIGHT_STATUS.put("CKO","值机截止");
        FLIGHT_STATUS.put("DEP","起飞");
        FLIGHT_STATUS.put("DLY","延误");
        FLIGHT_STATUS.put("LBD","登机催促");
        FLIGHT_STATUS.put("NST","到下站");
        FLIGHT_STATUS.put("ONR","前方起飞");
        FLIGHT_STATUS.put("POK","登机结束");
        FLIGHT_STATUS.put("RTN","返航");
        FLIGHT_STATUS.put("TBR","过站登机");
        FLIGHT_STATUS.put("TST","测试状态");
        FLIGHT_STATUS.put("BOT","推出");
    }

}
