package com.gzairports.wl.departure.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.BaseAirportCode;
import com.gzairports.common.basedata.domain.BaseCargoCode;
import com.gzairports.common.basedata.domain.BaseTransportValue;
import com.gzairports.common.basedata.mapper.*;
import com.gzairports.common.business.arrival.domain.HzArrItem;
import com.gzairports.common.business.departure.domain.Mawb;
import com.gzairports.common.business.departure.mapper.MawbMapper;
import com.gzairports.common.core.domain.entity.SysDept;
import com.gzairports.common.log.service.impl.WaybillLogServiceImpl;
import com.gzairports.common.pdf.PdfPrintHelper;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.system.mapper.SysUserMapper;
import com.gzairports.common.utils.QRCodeGenerator;
import com.gzairports.common.utils.sign.Base64;
import com.gzairports.common.utils.spring.SpringUtils;
import com.gzairports.wl.charge.domain.*;
import com.gzairports.wl.charge.domain.query.FreightItemQuery;
import com.gzairports.wl.charge.domain.vo.FareAirItemVo;
import com.gzairports.wl.charge.mapper.*;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.SerialNumberGenerator;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.wl.departure.domain.*;
import com.gzairports.wl.departure.domain.query.FareQuery;
import com.gzairports.wl.departure.domain.query.HawbQuery;
import com.gzairports.wl.departure.domain.query.ItemQuery;
import com.gzairports.wl.departure.domain.vo.*;
import com.gzairports.wl.departure.mapper.BillMapper;
import com.gzairports.wl.departure.mapper.ConsignMapper;
import com.gzairports.wl.departure.mapper.HawbMapper;
import com.gzairports.wl.departure.mapper.MergeHawbMawbMapper;
import com.gzairports.wl.departure.service.IHawbService;
import com.gzairports.common.basedata.domain.Customer;
import com.gzairports.wl.ticket.domain.TicketCtrl;
import com.gzairports.wl.ticket.domain.TicketNum;
import com.gzairports.wl.ticket.mapper.OperateMapper;
import com.gzairports.wl.ticket.mapper.TicketAlertMapper;
import com.gzairports.wl.ticket.mapper.TicketMapper;
import com.gzairports.wl.ticket.mapper.TicketNumMapper;
import net.sf.jsqlparser.expression.operators.relational.ItemsList;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分单制单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class HawbServiceImpl extends ServiceImpl<HawbMapper, Hawb> implements IHawbService {

    @Autowired
    private HawbMapper hawbMapper;

    @Autowired
    private TicketNumMapper ticketNumMapper;

    @Autowired
    private TicketMapper ticketMapper;

    @Autowired
    private OperateMapper operateMapper;

    @Autowired
    private ChargeItemMapper chargeItemMapper;

    @Autowired
    private CustomerMapper customerMapper;

    @Autowired
    private FreightRateMapper freightRateMapper;

    @Autowired
    private FreightRateItemMapper freightRateItemMapper;

    @Autowired
    private FreightRateCustomMapper rateCustomMapper;

    @Autowired
    private FreightRateCustomItemMapper customItemMapper;

    @Autowired
    private FreightRateAirMapper rateAirMapper;

    @Autowired
    private FreightRateCustomAirMapper customAirMapper;

    @Autowired
    private FreightRateAirItemMapper rateAirItemMapper;

    @Autowired
    private HawbItemServiceImpl itemService;

    @Autowired
    private HawbErrorRemarkServiceImpl errorRemarkService;

    @Autowired
    private BillMapper billMapper;

    @Autowired
    private MawbMapper mawbMapper;

    @Autowired
    private MergeHawbMawbMapper mergeHawbMawbMapper;

    @Autowired
    private TransportValueMapper valueMapper;

    @Autowired
    private ConsignMapper consignMapper;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    @Autowired
    private AirportCodeMapper airportCodeMapper;

    @Autowired
    private CargoCodeMapper cargoCodeMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Override
    public Map<String,String> add(HawbVo vo) {
        try {
            check(vo.getWaybillCode());
        }catch (CustomException e){
            throw new CustomException(e.getMessage());
        }
        Integer ticketNum = Integer.valueOf(vo.getWaybillCode().substring(4,11));
        Long ticketId = ticketMapper.selectCheckHawb(ticketNum);
        if (ticketId != null){
            TicketNum num = ticketNumMapper.selectOne(new QueryWrapper<TicketNum>().eq("ticket_id", ticketId).eq("num", ticketNum));
            if (num != null){
                num.setStatus("USED");
                num.setUseBy(SecurityUtils.getUserId());
                num.setUseTime(LocalDateTime.now());
                ticketNumMapper.updateById(num);
            }
        }
        Long deptId = SecurityUtils.getHighParentId();
        Hawb hawb = new Hawb();
        BeanUtils.copyProperties(vo,hawb);
        hawb.setStatus("NORMAL");
        hawb.setDeptId(deptId);
        if (hawb.getId() != null){
            hawb.setUpdateTime(new Date());
        }
        saveOrUpdate(hawb);
        String hawbMawbId = setMerge(vo, hawb.getId());
        if (!CollectionUtils.isEmpty(vo.getItems())){
            for (HawbItem item : vo.getItems()) {
                item.setHawbId(hawb.getId());
                item.setDeptId(deptId);
                item.setWaybillCode(hawb.getWaybillCode());
                itemService.saveOrUpdate(item);
            }
        }
        if (!CollectionUtils.isEmpty(vo.getErrorRemarks())){
            for (HawbErrorRemark errorRemark : vo.getErrorRemarks()) {
                errorRemark.setHawbId(hawb.getId());
                errorRemarkService.saveOrUpdate(errorRemark);
            }
        }
        Map<String, String> stringStringHashMap = new HashMap<>();
        stringStringHashMap.put("hawbId",String.valueOf(hawb.getId()));
        if(StringUtils.isNotEmpty(hawbMawbId)){
            stringStringHashMap.put("hawbMawbId",hawbMawbId);
        }
        return stringStringHashMap;
    }

    /**
     * 关联主单号拼单
     * @param vo 分单数据
     * @param hawbId 分单id
     */
    private String setMerge(HawbVo vo, Long hawbId) {
        if (StringUtils.isNotEmpty(vo.getMasterWaybillCode())) {
            Mawb mawb = mawbMapper.selectOne(new QueryWrapper<Mawb>()
                    .eq("waybill_code", vo.getMasterWaybillCode())
                    .eq("type","DEP")
                    .eq("is_del", 0));
            if (mawb == null) {
//                throw new CustomException("当前关联主单号不存在");
                MergeHawbMawb merge = new MergeHawbMawb();
                merge.setHawbId(hawbId);
                merge.setMergeNum(vo.getQuantity());
                merge.setMergeWeight(vo.getWeight());
                merge.setRemainWeight(new BigDecimal(0));
                merge.setRemainNum(0);
                mergeHawbMawbMapper.insert(merge);
                return merge.getId().toString();
            } else {
                if (!mawb.getDesPort().equals(vo.getDesPort())) {
                    throw new CustomException("当前主单不可关联");
                }
                if (!mawb.getCarrier1().equals(vo.getCarrier1())) {
                    throw new CustomException("当前主单不可关联");
                }
                if (!mawb.getDeptId().equals(SecurityUtils.getHighParentId())) {
                    throw new CustomException("当前主单不可关联");
                }
                //这里要去找 是否存在 存在就更新关联的主单
                MergeHawbMawb mergeOld = mergeHawbMawbMapper.selectOne(new QueryWrapper<MergeHawbMawb>()
                        .eq("hawb_id", hawbId));
                if(mergeOld == null){
                    MergeHawbMawb merge = new MergeHawbMawb();
                    merge.setMawbId(mawb.getId());
                    merge.setHawbId(hawbId);
                    merge.setMergeNum(vo.getQuantity());
                    merge.setMergeWeight(vo.getWeight());
                    merge.setRemainWeight(new BigDecimal(0));
                    merge.setRemainNum(0);
                    mawb.setMergeStatus(1);
                    mergeHawbMawbMapper.insert(merge);
                }else{
                    mergeOld.setMawbId(mawb.getId());
                    mergeOld.setMergeNum(vo.getQuantity());
                    mergeOld.setMergeWeight(vo.getWeight());
                    mawb.setMergeStatus(1);
                    mergeHawbMawbMapper.updateById(mergeOld);
                }

                mawbMapper.updateById(mawb);

            }
        }
        return null;
    }

    /**
     * 根据运单号以及单证控制校验运单号
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    @Override
    public HawbVo check(String waybillCode) {
        if (waybillCode == null){
            throw new CustomException("运单号不能为空");
        }
        if (waybillCode.length() != 11){
            throw new CustomException("运单号格式错误");
        }
        Hawb hawb = hawbMapper.selectOne(new QueryWrapper<Hawb>()
                .eq("waybill_code", waybillCode)
                .eq("dept_id", SecurityUtils.getHighParentId())
                .eq("is_del",0));
//        if (hawb != null && "INVALID".equals(hawb.getStatus())){
//            throw new CustomException("当前运单已作废");
//        }
//        if (hawb != null && "NORMAL".equals(hawb.getStatus())){
//            return getInfo(waybillCode);
//        }

        TicketCtrl ctrl = operateMapper.selectOne(new QueryWrapper<TicketCtrl>()
                .eq("code", "HAWB")
                .eq("domint",'D'));
        Long deptId = SecurityUtils.getHighParentId();
        Integer checkNum = Integer.valueOf(waybillCode.substring(4,11)) % 9;
        Integer ticketNum = Integer.valueOf(waybillCode.substring(4,11));
        if (ctrl != null && ctrl.getDeptIds().contains(deptId.toString())){
            if (StringUtils.isEmpty(ctrl.getPrefix())){
                if (checkCtrl(checkNum, ticketNum, hawb)) return getInfo(waybillCode);
            }else if (ctrl.getPrefix().contains(waybillCode.substring(4,7))){
                if (checkCtrl(checkNum, ticketNum, hawb)) return getInfo(waybillCode);
            }else {
                return getInfo(waybillCode);
            }
        }else {
            if (hawb != null){
                return getInfo(waybillCode);
            }
            Long ticketId = ticketMapper.selectCheckHawb(ticketNum);
            if (ticketId == null){
                return null;
            }
            TicketNum num = ticketNumMapper.selectOne(new QueryWrapper<TicketNum>()
                    .eq("ticket_id", ticketId)
                    .eq("num", ticketNum));
            if (num == null){
                return null;
            }
            //还未发放
            if(num.getDeptId()==null){
                return null;
            }
            //校验当前登录账号是否可以领单
            checkUser(num.getDeptId());

            if ("NOTUSED".equals(num.getStatus())){
                return null;
            }
        }

        return null;
    }

    /**
     * 校验单号
     * @param checkNum 单证校验号
     * @param ticketNum 单证号
     * @param hawb 分单信息
     * @return 结果
     */
    private boolean checkCtrl(Integer checkNum, Integer ticketNum, Hawb hawb) {
        if (hawb != null && "INVALID".equals(hawb.getStatus())) {
            throw new CustomException("当前运单已作废");
        }
        if (hawb != null && "NORMAL".equals(hawb.getStatus())) {
            return true;
        }
        Long ticketId = ticketMapper.selectCheckHawb(ticketNum);
        if (ticketId == null) {
            throw new CustomException("无当前单证信息不可保存，请更改运单号");
        }
        TicketNum num = ticketNumMapper.selectOne(new QueryWrapper<TicketNum>()
                .eq("ticket_id", ticketId)
                .eq("num", ticketNum));
        if (num == null || num.getStatus().equals("NOTGRANT")) {
            throw new CustomException("当前运单未发放");
        }
        //校验当前登录账号是否可以领单
        checkUser(num.getDeptId());

        if (!checkNum.equals(num.getCode())) {
            throw new CustomException("运单校验失败");
        }
        if ("USED".equals(num.getStatus())) {
            return true;
        }
        if ("CANCEL".equals(num.getStatus())) {
            throw new CustomException("当前运单已销号");
        }
        if ("INVALID".equals(num.getStatus())) {
            throw new CustomException("当前运单已作废");
        }
        return false;
    }

    private void checkUser(Long deptId) {
        //校验当前登录账号是否可以领单
        SysDept dept = sysUserMapper.selectUserById(SecurityUtils.getUserId()).getDept();
        //登录人部门的祖级列表 是否包含发放的部门id,如果包含,说明发放的部门是父级部门,则可以使用
        //祖级列表还不够,需要加上当前部门的id和所有子级部门的id
        StringBuilder stringBuilder = new StringBuilder(dept.getAncestors());
        List<SysDept> sysDepts = sysDeptMapper.selectDeptListForUser(SecurityUtils.getDeptId());
        for (SysDept sysDept : sysDepts) {
            stringBuilder.append(",").append(sysDept.getDeptId());
        }
        String s = new String(stringBuilder);
        boolean contains = s.contains(deptId.toString());
        if (!contains) {
            throw new CustomException("与当前运单领单人不符");
        }
    }

    /**
     * 根据默认收费方式计算运单运价
     *
     * @param query 新增运单信息
     * @return 返回结果
     */
    @Override
    public FareVo fare(FareQuery query) {
        itemService.remove(new QueryWrapper<HawbItem>().eq("waybill_code",query.getWaybillCode()));
        FareVo vo = new FareVo();
        Long deptId = SecurityUtils.getHighParentId();
        BaseCargoCode cargoCode = cargoCodeMapper.selectByCode(query.getCargoCode());
        Customer customer = customerMapper.selectOne(new QueryWrapper<Customer>()
                .eq("abbreviation", query.getShipperAbb())
                .eq("dept_id", deptId));
        // 费用总计
        BigDecimal bigDecimal = new BigDecimal(0);
        String code = query.getWaybillCode().substring(0,4);
        // 出港费用列表
        List<HawbItem> items = new ArrayList<>();
        // 散客运费（公布运价）
        if (customer == null){
            FreightRate freightRate = freightRateMapper.selectByCode(deptId,code);
            if (freightRate != null){
                List<FreightRateItem> freightRateItem = freightRateItemMapper.selectList(new QueryWrapper<FreightRateItem>()
                        .eq("rate_id", freightRate.getId())
                        .eq("departure_city", query.getSourcePort())
                        .eq("destination_city", query.getDesPort())
                        .eq("is_delete",0));
                if (!CollectionUtils.isEmpty(freightRateItem)){
                    int maxMatchCount = 0;
                    List<FreightRateItem> itemList = new ArrayList<>();
                    for (FreightRateItem item : freightRateItem) {
                        if (item.getCargoCategory() != null && !item.getCargoCategory().isEmpty()) {
                            Set<String> categorySet = new HashSet<>(Arrays.asList(item.getCargoCategory().split(",")));
                            if (!categorySet.contains(cargoCode.getCategoryCode())){
                                continue;
                            }
                        }
                        int matchCount = 1;
                        if (item.getProductCode() != null && Arrays.asList(item.getProductCode().split(",")).contains(query.getCargoCode())) {
                            matchCount++;
                        }
                        if (matchCount > 0) {
                            if (matchCount > maxMatchCount) {
                                maxMatchCount = matchCount;
                                itemList.clear();
                                itemList.add(item);
                            } else if (matchCount == maxMatchCount) {
                                itemList.add(item);
                            }
                        }
                    }
                    if (!CollectionUtils.isEmpty(itemList) && itemList.size() == 1){
                        FreightRateItem rateItem = itemList.get(0);
                        FareVo multiply = getInterval(rateItem, query.getWeight());
                        vo.setRateType(multiply.getRateType());
                        // 取舍方式计算
                        BigDecimal round = bigDecimalRound(freightRate.getRoundingMethod(), multiply.getCostSum());
                        HawbItem hawbItem = new HawbItem();
                        hawbItem.setWaybillCode(query.getWaybillCode());
                        hawbItem.setChargeItemId(rateItem.getId());
                        hawbItem.setDeptId(deptId);
                        hawbItem.setRate(multiply.getRate());
                        hawbItem.setCostType(freightRate.getRateName());
                        hawbItem.setFreightType("航空运费");
                        hawbItem.setSettleType("应收");
                        if (round.compareTo(rateItem.getMinimumFreight()) <= 0 ){
                            hawbItem.setCharging(rateItem.getMinimumFreight());
                            items.add(hawbItem);
                            bigDecimal = bigDecimal.add(rateItem.getMinimumFreight());
                            vo.setRateType("M");
                        }else {
                            hawbItem.setCharging(round);
                            items.add(hawbItem);
                            bigDecimal = bigDecimal.add(round);
                        }
                        vo.setItemId(rateItem.getId());
                        vo.setChargeWeight(multiply.getChargeWeight());
                    }
                }
            }
        }else {
            LocalDate currentDate = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String formattedDate = currentDate.format(formatter);
            // 查询客户运价
            FreightRateCustom rateCustom = rateCustomMapper.selectOne(new QueryWrapper<FreightRateCustom>()
                    .eq("applicable_documents", code)
                    .eq("customer",customer.getName())
                    .eq("dept_id",deptId)
                    .le("effective_date",formattedDate)
                    .ge("expiration_date",formattedDate)
                    .eq("is_del",0));
            // 查询客户运价条目
            if (rateCustom != null){
                FreightRateCustomItem customItem = new FreightRateCustomItem();
                if (rateCustom.getRateModel() == 1){
                    List<FreightRateCustomAir> list = customAirMapper.selectList(new QueryWrapper<FreightRateCustomAir>().eq("custom_price_id",rateCustom.getId()).eq("is_del",0));
                    List<FreightRateCustomAir> customAirList = new ArrayList<>();
                    int maxCount = 0;
                    for (FreightRateCustomAir customAir : list) {
                        int matchCount = 0;
                        if (customAir.getCargoCategory() != null && !customAir.getCargoCategory().isEmpty()) {
                            Set<String> categorySet = new HashSet<>(Arrays.asList(customAir.getCargoCategory().split(",")));
                            if (!categorySet.contains(cargoCode.getCategoryCode())) {
                                continue;
                            }else {
                                matchCount+=2;
                            }
                        }else {
                            matchCount++;
                        }
                        if (customAir.getCargoCode() == null) {
                            matchCount++;
                        }else if (Arrays.asList(customAir.getCargoCode().split(",")).contains(query.getCargoCode())) {
                            matchCount+=2;
                        }
                        if (matchCount > 0) {
                            if (matchCount > maxCount) {
                                maxCount = matchCount;
                                customAirList.clear();
                                customAirList.add(customAir);
                            } else if (matchCount == maxCount) {
                                customAirList.add(customAir);
                            }
                        }
                    }
                    if (!CollectionUtils.isEmpty(customAirList) && customAirList.size() == 1){
                        FreightRateCustomAir customAir = customAirList.get(0);
                        FreightRateAir rateAir = rateAirMapper.selectOne(new QueryWrapper<FreightRateAir>()
                                .eq("carrier_code", query.getCarrier1())
                                .eq("dept_id", deptId)
                                .le("effective_date", formattedDate)
                                .ge("expiration_date", formattedDate)
                                .eq("is_del", 0)
                                .last("limit 1"));
                        if (rateAir != null){
                            List<FreightRateAirItem> airItems = rateAirItemMapper.selectList(new QueryWrapper<FreightRateAirItem>()
                                    .eq("rate_id", rateAir.getId())
                                    .eq("departure_city", query.getSourcePort())
                                    .eq("destination_city", query.getDesPort())
                                    .eq("is_del",0));
                            if (!CollectionUtils.isEmpty(airItems)) {
                                int maxMatchCount = 0;
                                List<FreightRateAirItem> itemList = new ArrayList<>();
                                for (FreightRateAirItem item : airItems) {
                                    if (item.getCargoCategory() != null && !item.getCargoCategory().isEmpty()) {
                                        Set<String> categorySet = new HashSet<>(Arrays.asList(item.getCargoCategory().split(",")));
                                        if (!categorySet.contains(cargoCode.getCategoryCode())) {
                                            continue;
                                        }
                                    }
                                    List<String> list1 = Arrays.asList(item.getCargoCode().split(","));
                                    if (!CollectionUtils.isEmpty(list1) && list1.size() == 1){
                                        if (!list1.get(0).equals(query.getCargoCode())){
                                            continue;
                                        }
                                    }
                                    int matchCount = 1;
                                    if (StringUtils.isEmpty(item.getCargoCode()) && StringUtils.isEmpty(item.getCargoCategory()) && StringUtils.isEmpty(item.getSpecialCode())){
                                        matchCount++;
                                    }
                                    if (StringUtils.isNotEmpty(item.getCargoCode()) && list1.contains(query.getCargoCode())) {
                                        matchCount++;
                                    }
                                    if (StringUtils.isNotEmpty(item.getSpecialCode()) && item.getSpecialCode().equals(query.getSpecialCargoCode1())){
                                        matchCount++;
                                    }
                                    if (matchCount > 0) {
                                        if (matchCount > maxMatchCount) {
                                            maxMatchCount = matchCount;
                                            itemList.clear();
                                            itemList.add(item);
                                        } else if (matchCount == maxMatchCount) {
                                            itemList.add(item);
                                        }
                                    }
                                }
                                if (!CollectionUtils.isEmpty(itemList) && itemList.size() == 1){
                                    FreightRateAirItem airItem = itemList.get(0);
                                    customItem.setId(airItem.getId());
                                    customItem.setMinimumFreight(customAir.getMinimumFreight().add(airItem.getMinimumFreight()));
                                    customItem.setRateWeightRange5(customAir.getRateWeightRange5().add(airItem.getRateWeightRange5()));
                                    customItem.setRateWeightRange10(customAir.getRateWeightRange10().add(airItem.getRateWeightRange10()));
                                    customItem.setBaseRateN(customAir.getBaseRateN().add(airItem.getBaseRateN()));
                                    customItem.setRateWeightRange45(customAir.getRateWeightRange45().add(airItem.getRateWeightRange45()));
                                    customItem.setRateWeightRange100(customAir.getRateWeightRange100().add(airItem.getRateWeightRange100()));
                                    customItem.setRateWeightRange300(customAir.getRateWeightRange300().add(airItem.getRateWeightRange300()));
                                    customItem.setRateWeightRange500(customAir.getRateWeightRange500().add(airItem.getRateWeightRange500()));
                                    customItem.setRateWeightRange1000(customAir.getRateWeightRange1000().add(airItem.getRateWeightRange1000()));
                                    vo.setItemId(airItem.getId());
                                }else {
                                    customItem = null;
                                }
                            }else {
                                customItem = null;
                            }
                        }else {
                            customItem = null;
                        }
                    }else {
                        customItem = null;
                    }
                }else {
                    List<FreightRateCustomItem> customItems = customItemMapper.selectList(new QueryWrapper<FreightRateCustomItem>()
                            .eq("rate_id", rateCustom.getId())
                            .eq("applicable_documents", code)
                            .eq("departure_city", query.getSourcePort())
                            .eq("destination_city", query.getDesPort())
                            .eq("is_del",0));
                    if (!CollectionUtils.isEmpty(customItems)){
                        int maxMatchCount = 0;
                        List<FreightRateCustomItem> itemList = new ArrayList<>();
                        for (FreightRateCustomItem item : customItems) {
                            if (item.getCargoCategory() != null && !item.getCargoCategory().isEmpty()) {
                                Set<String> categorySet = new HashSet<>(Arrays.asList(item.getCargoCategory().split(",")));
                                if (!categorySet.contains(cargoCode.getCategoryCode())) {
                                    continue;
                                }
                            }
                            int matchCount = 1;
                            if (item.getProductCode() != null && Arrays.asList(item.getProductCode().split(",")).contains(query.getCargoCode())) {
                                matchCount++;
                            }
                            if (matchCount > 0) {
                                if (matchCount > maxMatchCount) {
                                    maxMatchCount = matchCount;
                                    itemList.clear();
                                    itemList.add(item);
                                } else if (matchCount == maxMatchCount) {
                                    itemList.add(item);
                                }
                            }
                        }
                        if (!CollectionUtils.isEmpty(itemList) && itemList.size() == 1){
                            customItem = itemList.get(0);
                            vo.setItemId(itemList.get(0).getId());
                        }else {
                            customItem = null;
                        }
                    }else {
                        customItem = null;
                    }
                }
                if (customItem != null){
                    FareVo multiply = getInterval(customItem, query.getWeight());
                    vo.setRateType(multiply.getRateType());
                    // 取舍方式计算
                    BigDecimal round = bigDecimalRound(rateCustom.getRoundingMethod(), multiply.getCostSum());
                    HawbItem hawbItem = new HawbItem();
                    hawbItem.setWaybillCode(query.getWaybillCode());
                    hawbItem.setChargeItemId(customItem.getId());
                    hawbItem.setDeptId(deptId);
                    hawbItem.setRate(multiply.getRate());
                    hawbItem.setCostType(rateCustom.getRateName());
                    hawbItem.setFreightType("航空运费");
                    hawbItem.setSettleType("应收");
                    if (round.compareTo(customItem.getMinimumFreight()) <= 0 ){
                        hawbItem.setCharging(customItem.getMinimumFreight());
                        vo.setRateType("M");
                        items.add(hawbItem);
                        bigDecimal = bigDecimal.add(customItem.getMinimumFreight());
                    }else {
                        hawbItem.setCharging(round);
                        items.add(hawbItem);
                        bigDecimal = bigDecimal.add(round);
                    }
                    vo.setChargeWeight(multiply.getChargeWeight());
                }
            }
        }
        if (query.getTransportInsureValue() != null){
            List<BaseTransportValue> transportValues = valueMapper.selectOneByQuery(deptId);
            List<BaseTransportValue> itemList = new ArrayList<>();
            int maxMatchCount = 0;
            for (BaseTransportValue transportValue : transportValues) {
                int matchCount = 0;
                if (transportValue.getCargoCategory() != null && !transportValue.getCargoCategory().isEmpty()) {
                    Set<String> categorySet = new HashSet<>(Arrays.asList(transportValue.getCargoCategory().split(",")));
                    if (!categorySet.contains(cargoCode.getCategoryCode())) {
                        continue;
                    }else {
                        matchCount++;
                    }
                }else {
                    matchCount++;
                }
                if (transportValue.getCargoCode() == null) {
                    matchCount++;
                }else if (Arrays.asList(transportValue.getCargoCode().split(",")).contains(query.getCargoCode())) {
                    matchCount+=2;
                }
                if (matchCount > 0) {
                    if (matchCount > maxMatchCount) {
                        maxMatchCount = matchCount;
                        itemList.clear();
                        itemList.add(transportValue);
                    } else if (matchCount == maxMatchCount) {
                        itemList.add(transportValue);
                    }
                }
            }
            if (!CollectionUtils.isEmpty(itemList) && itemList.size() == 1){
                BaseTransportValue baseTransportValue = itemList.get(0);
                BigDecimal multiply = query.getTransportInsureValue().multiply(baseTransportValue.getRate());
                bigDecimal = bigDecimal.add(multiply);
                HawbItem hawbItem = new HawbItem();
                hawbItem.setWaybillCode(query.getWaybillCode());
                hawbItem.setChargeItemId(baseTransportValue.getId());
                hawbItem.setDeptId(deptId);
                hawbItem.setRate(baseTransportValue.getRate());
                hawbItem.setCostType("运输保险价值");
                hawbItem.setFreightType("保险费");
                hawbItem.setSettleType("应收");
                hawbItem.setCharging(multiply);
                items.add(hawbItem);
            }
        }

        // 收费项目默认计费
        List<ChargeItem> chargeItem = chargeItemMapper.selectByDept(deptId,code);
        List<ChargeItem> collect = chargeItem.stream()
                .filter(e -> StringUtils.isEmpty(e.getCode()) || query.getCarrier1().equals(e.getCode()))
                .collect(Collectors.toList());
        for (ChargeItem item : collect) {
            if(vo.getChargeWeight() == null){
                vo.setChargeWeight(query.getWeight());
            }
            BigDecimal decimal = getBigDecimal(item, item.getDefaultCostRate(), vo.getChargeWeight(), query.getVolume(),query.getQuantity());
            HawbItem hawbItem = new HawbItem();
            hawbItem.setWaybillCode(query.getWaybillCode());
            hawbItem.setChargeItemId(item.getId());
            hawbItem.setDeptId(deptId);
            hawbItem.setRate(item.getDefaultCostRate());
            hawbItem.setCostType(item.getName());
            hawbItem.setFreightType("收费项目");
            hawbItem.setSettleType(item.getPurpose());
            hawbItem.setCharging(decimal);
            items.add(hawbItem);
            bigDecimal = bigDecimal.add(decimal);
        }
        vo.setHawbItems(items);
        vo.setCostSum(bigDecimal);
        return vo;
    }

    /**
     * 添加收费管理
     *
     * @param query 分单收费项目参数
     * @return 返回结果
     */
    @Override
    public FareVo addFees(ItemQuery query) {
        FareVo vo = new FareVo();
        BigDecimal costSum = query.getCostSum() == null ? new BigDecimal(0) : query.getCostSum();
        if (CollectionUtils.isEmpty(query.getChargeItemIds())){
            return null;
        }
        List<ChargeItem> chargeItems = chargeItemMapper.selectBatchIds(query.getChargeItemIds());
        if (CollectionUtils.isEmpty(chargeItems)){
            return null;
        }
        Hawb hawb = hawbMapper.selectOne(new QueryWrapper<Hawb>()
                .eq("waybill_code", query.getWaybillCode())
                .eq("dept_id", SecurityUtils.getHighParentId())
                .eq("is_del",0));
        List<HawbItem> list = new ArrayList<>();
        for (ChargeItem chargeItem : chargeItems) {
            HawbItem one = itemService.getOne(new QueryWrapper<HawbItem>()
                    .eq("waybill_code",query.getWaybillCode())
                    .eq("charge_item_id",chargeItem.getId())
                    .eq("is_del",0));
            if (one == null) {
                HawbItem item = new HawbItem();
                item.setWaybillCode(query.getWaybillCode());
                BigDecimal decimal = getBigDecimal(chargeItem, chargeItem.getDefaultCostRate(), query.getChargeWeight(), query.getVolume(), query.getQuantity());
                item.setChargeItemId(chargeItem.getId());
                item.setDeptId(chargeItem.getDeptId());
                item.setRate(chargeItem.getDefaultCostRate());
                item.setCostType(chargeItem.getName());
                item.setFreightType("收费项目");
                item.setSettleType(chargeItem.getPurpose());
                item.setSettleMethod("现金");
                item.setCharging(decimal);
                if (hawb != null){
                    itemService.save(item);
                }
                list.add(item);
                costSum = costSum.add(decimal);
            }else {
                throw new CustomException("该运单已存在相同的收费项目");
            }
            if (hawb != null){
                hawb.setCostSum(costSum);
                hawbMapper.updateById(hawb);
            }
        }
        vo.setHawbItems(list);
        vo.setCostSum(costSum);
        vo.setChargeWeight(query.getChargeWeight());
        return vo;
    }


    /**
     * 根据运单code查询运单详情
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    @Override
    public HawbVo getInfo(String waybillCode) {
        HawbVo vo = new HawbVo();
        Consign consign = consignMapper.selectOne(new QueryWrapper<Consign>().eq("waybill_code", waybillCode));
        if (consign != null){
            vo.setConsignCode(consign.getCode());
            vo.setConsignId(consign.getId());
        }
        Hawb hawb = hawbMapper.selectOne(new QueryWrapper<Hawb>().eq("waybill_code", waybillCode)
                .eq("dept_id",SecurityUtils.getHighParentId())
                .eq("is_del",0));
        if (hawb != null){
            BeanUtils.copyProperties(hawb,vo);
            List<HawbItem> list = itemService.list(new QueryWrapper<HawbItem>()
                    .eq("waybill_code", waybillCode)
                    .eq("dept_id",SecurityUtils.getHighParentId())
                    .eq("is_del",0));
            List<HawbItem> collect = list.stream().filter(e -> !"收费项目".equals(e.getFreightType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect)){
                vo.setItemId(collect.get(0).getChargeItemId());
            }
            vo.setItems(list);
            List<HawbErrorRemark> errorRemarks = errorRemarkService.list(new QueryWrapper<HawbErrorRemark>().eq("waybill_code", waybillCode));
            vo.setErrorRemarks(errorRemarks);
        }
        return vo;
    }

    /**
     * 编辑运单
     *
     * @param vo 运单详情信息
     * @return 返回结果
     */
    @Override
    public int edit(HawbVo vo) {
        Hawb hawb = new Hawb();
        BeanUtils.copyProperties(vo,hawb);

        if (!CollectionUtils.isEmpty(vo.getItems())){
            for (HawbItem item : vo.getItems()) {
                itemService.saveOrUpdate(item);
            }
        }

        if (!CollectionUtils.isEmpty(vo.getErrorRemarks())){
            for (HawbErrorRemark errorRemark : vo.getErrorRemarks()) {
                errorRemarkService.saveOrUpdate(errorRemark);
            }
        }
        return hawbMapper.updateById(hawb);
    }

    /**
     * 修改费用项
     *
     * @param item 分单收费项目
     * @return 返回结果
     */
    @Override
    public FareVo editFee(HawbItem item) {
        FareVo vo = new FareVo();
        BigDecimal decimal;
        Hawb hawb = hawbMapper.selectOne(new QueryWrapper<Hawb>()
                .eq("waybill_code", item.getWaybillCode())
                .eq("dept_id", SecurityUtils.getHighParentId())
                .eq("is_del", 0));
        if (item.getChargeItemId() == null){
            decimal = item.getRate().multiply(item.getChargeWeight());
        }else {
            switch (item.getFreightType()){
                case "收费项目":
                    if (item.getChargeItemId() == null){
                        decimal = item.getRate().multiply(item.getChargeWeight());
                    }else {
                        ChargeItem chargeItem = chargeItemMapper.selectById(item.getChargeItemId());
                        decimal = getBigDecimal(chargeItem, item.getRate(), item.getChargeWeight(), item.getVolume(), item.getQuantity());
                    }
                    break;
                case "航空运费":
                    Long highParentId = SecurityUtils.getHighParentId();
                    Customer customer = customerMapper.selectOne(new QueryWrapper<Customer>()
                            .eq("abbreviation", item.getShipperAbb())
                            .eq("dept_id", highParentId));
                    if (customer == null){
                        FreightRateItem rateItem = freightRateItemMapper.selectById(item.getChargeItemId());
                        FreightRate freightRate = freightRateMapper.selectById(rateItem.getRateId());
                        FareVo multiply = getInterval(rateItem, item.getChargeWeight());
                        vo.setRateType(multiply.getRateType());
                        decimal = bigDecimalRound(freightRate.getRoundingMethod(), multiply.getCostSum());
                    }else {
                        FreightRateCustomItem customItem = customItemMapper.selectById(item.getChargeItemId());
                        if (customItem == null){
                            FreightRateAirItem rateAirItem = rateAirItemMapper.selectById(item.getChargeItemId());
                            FreightRateAir rateAir = rateAirMapper.selectById(rateAirItem.getRateId());
                            FareVo airCost = getInterval(rateAirItem, item.getChargeWeight());
                            vo.setRateType(airCost.getRateType());
                            decimal = bigDecimalRound(rateAir.getRoundingMethod(), airCost.getCostSum());
                        }else {
                            FreightRateCustom custom = rateCustomMapper.selectById(customItem.getRateId());
                            FareVo customCost = getInterval(customItem, item.getChargeWeight());
                            vo.setRateType(customCost.getRateType());
                            decimal = bigDecimalRound(custom.getRoundingMethod(), customCost.getCostSum());
                        }
                    }
                    break;
                case "保险费":
                    decimal = item.getRate().multiply(item.getChargeWeight());
                    break;
                default:
                    decimal = new BigDecimal(0);
                    break;
            }
        }
        BigDecimal sum = new BigDecimal(0);
        BigDecimal costSum = item.getCostSum() == null ? new BigDecimal(0) : item.getCostSum();
        if (item.getOldCharging() == null){
            sum = costSum.add(decimal.subtract(item.getCharging()));
            item.setCharging(item.getCharging());
        }
        if (item.getOldCharging().compareTo(item.getCharging()) < 0) {
            sum = costSum.add(item.getCharging().subtract(item.getOldCharging()));
            item.setCharging(item.getCharging());
        }
        if (item.getOldCharging().compareTo(item.getCharging()) > 0) {
            sum = costSum.subtract(item.getOldCharging().subtract(item.getCharging()));
            item.setCharging(item.getCharging());
        }
        if (item.getOldCharging().compareTo(item.getCharging()) == 0 && item.getOldRate().compareTo(item.getRate()) != 0) {
            sum = costSum.add(decimal.subtract(item.getCharging()));
            item.setCharging(decimal);
        }
        if (hawb != null) {
            hawb.setCostSum(sum);
            hawbMapper.updateById(hawb);
            vo.setCostSum(hawb.getCostSum());
        } else {
            vo.setCostSum(sum);
        }
        itemService.updateById(item);
        vo.setChargeWeight(item.getChargeWeight());
        vo.setHawbItem(item);
        return vo;
    }

    /**
     * 删除费用项
     *
     * @param item 分单收费项目
     * @return 返回结果
     */
    @Override
    public String delFee(HawbItem item) {
        Hawb hawb = hawbMapper.selectOne(new QueryWrapper<Hawb>()
                .eq("waybill_code", item.getWaybillCode())
                .eq("dept_id", SecurityUtils.getHighParentId())
                .eq("is_del",0));

        BigDecimal subtract = item.getCostSum().subtract(item.getCharging());
        if (subtract.compareTo(new BigDecimal(0)) <= 0){
            subtract = new BigDecimal(0);
        }
        if (hawb != null){
            hawb.setCostSum(subtract);
            hawbMapper.updateById(hawb);
        }
        if (item.getId() != null){
            itemService.removeById(item.getId());
        }
       return subtract.toString();
    }

    /**
     * 运单取消作废
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    @Override
    public int cancelVoid(String waybillCode) {
        if (waybillCode == null){
            throw new CustomException("运单号不能为空");
        }
        if (waybillCode.length() != 11){
            throw new CustomException("运单号格式错误");
        }

        Hawb hawb = hawbMapper.selectOne(new QueryWrapper<Hawb>()
                .eq("waybill_code", waybillCode)
                .eq("dept_id", SecurityUtils.getHighParentId())
                .eq("is_del",0));

        if (hawb == null){
            throw new CustomException("无当前分单信息");
        }

        if ("NORMAL".equals(hawb.getStatus())){
            throw new CustomException("当前运单未处于作废状态");
        }

        Integer ticketNum = Integer.valueOf(waybillCode.substring(7,14));
        String code = waybillCode.substring(0,4);
        Long ticketId = ticketMapper.selectCheck(code, waybillCode.substring(4,7), ticketNum);
        if (ticketId != null){
            TicketNum num = ticketNumMapper.selectOne(new QueryWrapper<TicketNum>().eq("ticket_id", ticketId).eq("num", ticketNum));
            if (num == null){
                throw new CustomException("当前运单未发放");
            }
            if (!SecurityUtils.getUserId().equals(num.getUseBy())){
                throw new CustomException("与当前运单领单人不符");
            }
            num.setStatus("NOTUSED");
            ticketNumMapper.updateById(num);
        }

        List<HawbItem> items = itemService.list(new QueryWrapper<HawbItem>().eq("waybill_code", waybillCode));
        items.forEach(e->{
            e.setIsDel(1);
            itemService.updateById(e);
        });

        List<HawbErrorRemark> errorRemarks = errorRemarkService.list(new QueryWrapper<HawbErrorRemark>().eq("waybill_code", waybillCode));
        errorRemarks.forEach(e->{
            e.setIsDel(1);
            errorRemarkService.updateById(e);
        });

        hawb.setIsDel(1);
        return hawbMapper.updateById(hawb);
    }

    /**
     * 新增运单异常备注
     *
     * @param remark 运单异常备注
     * @return 返回结果
     */
    @Override
    public int addRemark(HawbErrorRemark remark) {
        Hawb hawb = hawbMapper.selectOne(new QueryWrapper<Hawb>()
                .eq("waybill_code", remark.getWaybillCode())
                .eq("dept_id", SecurityUtils.getHighParentId())
                .eq("is_del",0)
                .eq("status", "NORMAL"));
        if (hawb != null){
            remark.setHawbId(hawb.getId());
        }
        remark.setRemarkTime(new Date());
        remark.setUserName(SecurityUtils.getUsername());
        boolean save = errorRemarkService.save(remark);
        if (save){
            return 1;
        }
        return 0;
    }

    /**
     * 根据运单号查询异常备注列表
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    @Override
    public List<HawbErrorRemark> remarkList(String waybillCode) {
        return errorRemarkService.list(new QueryWrapper<HawbErrorRemark>().eq("waybill_code",waybillCode)
                .eq("is_del",0));
    }

    /**
     * 国内分单查询
     * @param query 国内分单查询参数
     * @return 结果
     */
    @Override
    public HawbQueryVo queryList(HawbQuery query) {
        HawbQueryVo vo = new HawbQueryVo();
        query.setDeptId(SecurityUtils.getHighParentId());
        Page<HawbQueryVo1> pageOne = new Page<>(query.getPageNum(),query.getPageSize());
        Page<HawbQueryVo1> hawb = hawbMapper.queryVosList(query,pageOne);
        Page<HawbQueryVo1> page = new Page<>(1,-1);
        Integer count = hawbMapper.queryListCount(query, page);
        Integer quantity = hawbMapper.queryListQuantity(query, page);
        BigDecimal weight = hawbMapper.queryListWeight(query, page);
        if (!CollectionUtils.isEmpty(hawb.getRecords())){
            hawb.getRecords().forEach(e->{
                Mawb mawb = hawbMapper.selectMawbInfoById(e.getId());
                if(mawb != null){
                    e.setMasterWaybillCode(mawb.getWaybillCode());
                }
            });
            for (HawbQueryVo1 hawb1 : hawb.getRecords()) {
                BigDecimal totalCost = new BigDecimal(0);
                List<HawbItem> list = itemService.list(new QueryWrapper<HawbItem>()
                        .eq("hawb_id", hawb1.getId())
                        .eq("is_del", 0));
                if (!CollectionUtils.isEmpty(list)){
                    List<HawbItem> items = list.stream().filter(e -> "收费项目".equals(e.getFreightType())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(items)){
                        List<HawbItem> fuelCostList = items.stream().filter(e -> e.getCostType().contains("燃油费")).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(fuelCostList)){
                            BigDecimal reduce = fuelCostList.stream().map(HawbItem::getCharging).reduce(BigDecimal.ZERO, BigDecimal::add);
                            hawb1.setFuelCost(reduce);
                            totalCost = totalCost.add(reduce);
                        }
                        List<HawbItem> premiumList = items.stream().filter(e -> e.getCostType().contains("保险费")).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(premiumList)){
                            BigDecimal reduce = premiumList.stream().map(HawbItem::getCharging).reduce(BigDecimal.ZERO, BigDecimal::add);
                            hawb1.setPremium(reduce);
                            totalCost = totalCost.add(reduce);
                        }
                        List<HawbItem> packCostList = items.stream().filter(e -> e.getCostType().contains("包装费")).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(packCostList)){
                            BigDecimal reduce = packCostList.stream().map(HawbItem::getCharging).reduce(BigDecimal.ZERO, BigDecimal::add);
                            hawb1.setPackCost(reduce);
                            totalCost = totalCost.add(reduce);
                        }
                        List<HawbItem> otherCostList = items.stream().filter(e -> !e.getCostType().contains("包装费") && !e.getCostType().contains("保险费") && !e.getCostType().contains("燃油费")).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(otherCostList)){
                            BigDecimal reduce = otherCostList.stream().map(HawbItem::getCharging).reduce(BigDecimal.ZERO, BigDecimal::add);
                            hawb1.setOtherCost(reduce);
                            totalCost = totalCost.add(reduce);
                        }
                    }
                    List<HawbItem> airItem = list.stream().filter(e -> !"收费项目".equals(e.getFreightType())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(airItem)){
                        HawbItem item = airItem.get(0);
                        hawb1.setAirRate(item.getRate());
                        hawb1.setAirCost(item.getCharging());
                        totalCost = totalCost.add(item.getCharging());
                    }
                }
                hawb1.setTotalCost(totalCost);
            }
            vo.setTotalQuantity(quantity);
            vo.setTotalWeight(weight);
            vo.setTotalOrder(count);
            vo.setVo1List(hawb);
            return vo;
        }
        return vo;
    }

    /**
     * 国内分单销售查询
     * @param query 国内分单销售查询参数
     * @return 结果
     */
    @Override
    public HawbQueryVo saleQuery(HawbQuery query) {
        HawbQueryVo vo = new HawbQueryVo();
        query.setDeptId(SecurityUtils.getHighParentId());
        List<Hawb> hawb = hawbMapper.queryList(query);
        hawb.forEach(e->{
            Mawb mawb = hawbMapper.selectMawbInfoById(e.getId());
            if(mawb != null){
                e.setMasterWaybillCode(mawb.getWaybillCode());
            }
        });
        if (!CollectionUtils.isEmpty(hawb)){
            vo.setTotalOrder(hawb.size());
            Integer num = 0;
            BigDecimal bigDecimal = new BigDecimal(0);
            for (Hawb hawb1 : hawb) {
                num += hawb1.getQuantity();
                bigDecimal = bigDecimal.add(hawb1.getWeight());
            }

            // 总费用
            BigDecimal reduce = hawb.stream().map(Hawb::getCostSum).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setTotalMoney(reduce);

            // 未支付费用
            List<Hawb> unPayList = hawb.stream().filter(e -> e.getPayStatus() == 0).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(unPayList)){
                BigDecimal unPay = unPayList.stream().map(Hawb::getMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                vo.setUnPay(unPay);
            }

            // 已支付费用
            List<Hawb> payList = hawb.stream().filter(e -> e.getPayStatus() == 1).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(payList)){
                BigDecimal pay = payList.stream().map(Hawb::getMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                vo.setPay(pay);
            }

            List<Hawb> settleList = hawb.stream().filter(e -> e.getPayStatus() == 2).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(settleList)){
                // 已退还费用
                BigDecimal refund = settleList.stream().map(Hawb::getMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                vo.setRefund(refund);
            }
            vo.setTotalQuantity(num);
            vo.setTotalWeight(bigDecimal);
            vo.setList(hawb);
            return vo;
        }
        return vo;
    }

    /**
     * 生成账单
     * @param ids 国内分单主键id
     * @return 结果
     */
    @Override
    public HawbQueryVo generateBill(Long[] ids) {
        HawbQueryVo vo = new HawbQueryVo();
        Long deptId = SecurityUtils.getHighParentId();
        List<Hawb> list = hawbMapper.selectByIds(ids,deptId);
        if (CollectionUtils.isEmpty(list)){
            return vo;
        }
        String shipper = list.get(0).getShipper();
        boolean b = list.stream().allMatch(e -> Objects.equals(shipper, e.getShipper()));
        if (!b){
           throw new CustomException("生成账单客户需要一致");
        }
        List<Hawb> collect = list.stream().filter(e -> e.getSerialNo() != null).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)){
            throw new CustomException("当前账单存在已有账单的运单");
        }

        // 已支付费用
        List<Hawb> payList = list.stream().filter(e -> e.getPayStatus() == 1 || e.getPayStatus() == 2).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(payList)){
            throw new CustomException("当前账单存在已支付或者已退款运单");
        }

        vo.setTotalOrder(list.size());
        Integer num = 0;
        BigDecimal bigDecimal = new BigDecimal(0);
        for (Hawb hawb1 : list) {
            num += hawb1.getQuantity();
            bigDecimal = bigDecimal.add(hawb1.getWeight());
        }

        // 总费用
        BigDecimal reduce = list.stream().map(Hawb::getCostSum).reduce(BigDecimal.ZERO, BigDecimal::add);
        vo.setTotalMoney(reduce);

        BigDecimal reduce1 = list.stream().map(Hawb::getChargeWeight).reduce(BigDecimal.ZERO, BigDecimal::add);

        vo.setTotalChargeWeight(reduce1);
        vo.setShipper(list.get(0).getShipper());
        vo.setTotalQuantity(num);
        vo.setTotalWeight(bigDecimal);
        vo.setList(list);
        return vo;
    }

    /**
     * 生成账单确认按钮
     * @param vo 生成账单参数
     * @return 结果
     */
    @Override
    public int confirm(HawbQueryVo vo) {
        if (vo == null){
            throw new CustomException("无当前账单信息");
        }

        String serialNo = SerialNumberGenerator.generateSerialNumber();

        List<Long> ids = vo.getList().stream().map(Hawb::getId).collect(Collectors.toList());
        hawbMapper.updateSerialNo(ids,serialNo);

        Bill bill = new Bill();
        bill.setShipper(vo.getShipper());
        bill.setDeptId(SecurityUtils.getHighParentId());
        bill.setSerialNo(serialNo);
        bill.setPayStatus("UNPAID");
        bill.setCreateTime(new Date());
        bill.setCreateBy(SecurityUtils.getUsername());
        return billMapper.insert(bill);
    }

    /**
     * 打印分单
     * @param response 返回流
     * @param id 分单id
     */
    @Override
    public void printHawb(HttpServletResponse response, Long id) throws Exception {
        Hawb hawb = hawbMapper.selectById(id);
        HawbVo vo = new HawbVo();
        if (hawb != null){
            BeanUtils.copyProperties(hawb,vo);
            switch (vo.getPaymentMethod()){
                case 0:
                    vo.setPaymentMethodStr("现付");
                    break;
                case 1:
                    vo.setPaymentMethodStr("周期结算");
                    break;
                default:
                    vo.setPaymentMethodStr("无");
                    break;
            }
            if (StringUtils.isEmpty(vo.getWriter())){
                vo.setWriter(SecurityUtils.getUsername());
            }
            if (vo.getWriteTime() == null){
                vo.setWriteTime(new Date());
            }
            BaseAirportCode desPort = airportCodeMapper.selectByCode(hawb.getDesPort());
            vo.setDesPortStr(desPort.getChineseName());
            vo.setSourcePortStr("贵阳");
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            if (vo.getFlightDate() != null){
                vo.setFlightDateStr(simpleDateFormat.format(vo.getFlightDate()));
            }
            if (vo.getWriteTime() != null){
                vo.setWriteTimeStr(simpleDateFormat.format(vo.getWriteTime()));
            }
            if (!StringUtils.isEmpty(vo.getMasterWaybillCode())){
                String substring = vo.getMasterWaybillCode().substring(4);
                String waybillCodeAbb = substring.substring(0, 3) + "-" + substring.substring(3);
                vo.setMasterAbb(waybillCodeAbb);
            }
            if (vo.getTransportInsureValue() == null){
                vo.setInsurance("无");
            }else {
                vo.setInsurance(vo.getTransportInsureValue().toString());
            }
            vo.setDeclaredValue("无");
            vo.setGroundCost(new BigDecimal(0).setScale(2, RoundingMode.DOWN));
//            vo.setCargoInfo((vo.getSize() == null ? "" : vo.getSize()) + " " + (vo.getCargoName() == null ? "" : vo.getCargoName()) + " " + (vo.getPack() == null ? "" : vo.getPack()) + " " + (vo.getVolume() == null ? "" : vo.getVolume().toString()));
            vo.setSizeVolume((vo.getSize() == null ? "" : vo.getSize()) + " " + (vo.getVolume() == null ? "" : vo.getVolume() + "m³"));
            vo.setChargeWeight(vo.getChargeWeight().setScale(0, RoundingMode.DOWN));
            //去费用表查询 这里前端并不会传过来
            List<HawbItem> list = itemService.list(new QueryWrapper<HawbItem>().eq("hawb_id", id).eq("is_del", 0));
            if(list.size() > 0){
                if(!CollectionUtils.isEmpty(vo.getItems())){
                    vo.getItems().addAll(list);
                }else{
                    vo.setItems(list);
                }
            }
            if (!CollectionUtils.isEmpty(vo.getItems())){
                List<HawbItem> airRates = vo.getItems().stream().filter(e -> "航空运费".equals(e.getFreightType())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(airRates)){
                    HawbItem item = airRates.get(0);
                    vo.setAirCost(item.getCharging().setScale(2, RoundingMode.DOWN));
                    vo.setAirRate(item.getRate());
                    vo.setCostType(item.getCostType());
                }
                List<HawbItem> otherRates = vo.getItems().stream()
                        .filter(e -> "收费项目".equals(e.getFreightType()))
                        .collect(Collectors.toList());
                otherRates.addAll(vo.getItems().stream()
                        .filter(e -> "保险费".equals(e.getFreightType()))
                        .collect(Collectors.toList()));
                StringBuilder builder = new StringBuilder();
                for (HawbItem otherRate : otherRates) {
                    builder.append(otherRate.getCostType()).append(": ").append(otherRate.getCharging().setScale(2, RoundingMode.DOWN)).append(" ");
                }
                vo.setOtherInfo(builder.toString());
                if (!CollectionUtils.isEmpty(otherRates)){
                    BigDecimal reduce = otherRates.stream().map(HawbItem::getCharging).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    vo.setOtherCost(reduce.setScale(2, RoundingMode.DOWN));
                }
            }
            if (vo.getAirCost() == null){
                vo.setAirRate(new BigDecimal(0).setScale(2, RoundingMode.DOWN));
            }
            if (vo.getOtherCost() == null){
                vo.setOtherCost(new BigDecimal(0).setScale(2, RoundingMode.DOWN));
            }
            BigDecimal airCost = vo.getAirCost() == null ? new BigDecimal(0) : vo.getAirCost();
            BigDecimal otherCost = vo.getOtherCost() == null ? new BigDecimal(0) : vo.getOtherCost();
            vo.setTotalCost(airCost.add(otherCost).setScale(2, RoundingMode.DOWN));
            SysDept dept = sysDeptMapper.selectDeptById(SecurityUtils.getHighParentId());
            if (dept != null && StringUtils.isNotEmpty(dept.getLogoUrlBig())){
                byte[] bytes = downloadFileFromUrl(dept.getLogoUrlBig());
                String airLogo = Base64.encode(bytes);
                vo.setAirLogo(airLogo);
            }
        //重量不显示小数点
            vo.setWeight(vo.getWeight().setScale(0, RoundingMode.DOWN));
            vo.setPrePay("YES");
        }
        ClassPathResource resource = new ClassPathResource("template/hawb.pdf");
        if (resource.exists()){
            String path = resource.getPath();
            byte[] bytes = PdfPrintHelper.getPdfDataFromTemplate(vo,path);
            // 设置响应头
            response.reset();
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "inline; filename=example.pdf");
            // 获取输出流并写入字节数据
            response.getOutputStream().write(bytes);
            response.getOutputStream().flush();
        }
    }

    /**
     * 打印托运书
     * @param response 返回流
     * @param consign 托运书
     */
    @Override
    public void printConsign(HttpServletResponse response, Consign consign) throws Exception {
        ClassPathResource resource = new ClassPathResource("template/consign.pdf");
        if (resource.exists()){
            String path = resource.getPath();
            byte[] bytes = PdfPrintHelper.getPdfDataFromTemplate(consign,path);
            // 设置响应头
            response.reset();
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "inline; filename=example.pdf");
            // 获取输出流并写入字节数据
            response.getOutputStream().write(bytes);
            response.getOutputStream().flush();
        }
    }

    @Override
    public List<FareAirItemVo> selectItemList(FreightItemQuery query) {
        List<FareAirItemVo> list = new ArrayList<>();
        Long deptId = SecurityUtils.getHighParentId();
        Customer customer = customerMapper.selectOne(new QueryWrapper<Customer>()
                .eq("abbreviation", query.getShipperAbb())
                .eq("dept_id", deptId));
        BaseCargoCode cargoCode = cargoCodeMapper.selectByCode(query.getCargoCode());
        if (customer == null){
            FreightRate freightRate = freightRateMapper.selectByCode(deptId,"HAWB");
            if (freightRate != null) {
                List<FreightRateItem> freightRateItems = freightRateItemMapper.selectList(new QueryWrapper<FreightRateItem>()
                        .eq("rate_id", freightRate.getId())
                        .eq("departure_city", query.getSourcePort())
                        .eq("destination_city", query.getDesPort())
                        .eq("is_delete", 0));
                for (FreightRateItem rateItem : freightRateItems) {
                    setList(query, list, rateItem.getCargoCategory(), rateItem.getProductCode(), rateItem.getSpecialCargoCode(), rateItem.getId(), rateItem.getClauseName());
                }
            }
        }else {
            LocalDate currentDate = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String formattedDate = currentDate.format(formatter);
            FreightRateCustom rateCustom = rateCustomMapper.selectOne(new QueryWrapper<FreightRateCustom>()
                    .eq("applicable_documents", "HAWB")
                    .eq("customer",customer.getName())
                    .eq("dept_id",deptId)
                    .le("effective_date",formattedDate)
                    .ge("expiration_date",formattedDate)
                    .eq("is_del",0));
            if (rateCustom != null) {
                if (rateCustom.getRateModel() == 1) {
                    FreightRateAir rateAir = rateAirMapper.selectOne(new QueryWrapper<FreightRateAir>()
                            .eq("carrier_code", query.getCarrier1())
                            .eq("dept_id", deptId)
                            .le("effective_date", formattedDate)
                            .ge("expiration_date", formattedDate)
                            .eq("is_del", 0)
                            .last("limit 1"));
                    if (rateAir != null) {
                        List<FreightRateAirItem> airItems = rateAirItemMapper.selectList(new QueryWrapper<FreightRateAirItem>()
                                .eq("rate_id", rateAir.getId())
                                .eq("departure_city", query.getSourcePort())
                                .eq("destination_city", query.getDesPort())
                                .eq("is_del", 0));
                        for (FreightRateAirItem rateAirItem : airItems) {
                            setList(query, list, rateAirItem.getCargoCategory(), rateAirItem.getCargoCode(), rateAirItem.getSpecialCode(), rateAirItem.getId(), rateAirItem.getClauseName());
                        }
                    }
                }else {
                    List<FreightRateCustomItem> customItems = customItemMapper.selectList(new QueryWrapper<FreightRateCustomItem>()
                            .eq("rate_id", rateCustom.getId())
                            .eq("applicable_documents", "HAWB")
                            .eq("departure_city", query.getSourcePort())
                            .eq("destination_city", query.getDesPort())
                            .eq("is_del",0));
                    for (FreightRateCustomItem customItem : customItems) {
                        setList(query, list, customItem.getCargoCategory(), customItem.getProductCode(), customItem.getSpecialCargoCode(), customItem.getId(), customItem.getClauseName());
                    }
                }
            }
        }
        return list;
    }

    private void setList(FreightItemQuery query, List<FareAirItemVo> list, String cargoCategory, String productCode, String specialCargoCode, Long id, String clauseName) {
        if (StringUtils.isEmpty(productCode) && StringUtils.isEmpty(cargoCategory) && StringUtils.isEmpty(specialCargoCode)) {
            FareAirItemVo vo1 = new FareAirItemVo();
            vo1.setId(id);
            vo1.setClauseName(clauseName);
            list.add(vo1);
        }
        if (productCode != null && Arrays.asList(productCode.split(",")).contains(query.getCargoCode())) {
            FareAirItemVo vo2 = new FareAirItemVo();
            vo2.setId(id);
            vo2.setClauseName(clauseName);
            list.add(vo2);
        }
        if (specialCargoCode != null && specialCargoCode.equals(query.getSpecialCargoCode1())) {
            FareAirItemVo vo3 = new FareAirItemVo();
            vo3.setId(id);
            vo3.setClauseName(clauseName);
            list.add(vo3);
        }
    }

    /**
     * 添加运价条目
     * @param query 添加参数
     * @return 结果
     */
    @Override
    public FareVo addItems(ItemQuery query) {
        FareVo vo = new FareVo();
        BaseCargoCode cargoCode = cargoCodeMapper.selectByCode(query.getCargoCode());
        List<HawbItem> list = new ArrayList<>();
        Long deptId = SecurityUtils.getHighParentId();
        BigDecimal costSum = query.getCostSum() == null ? new BigDecimal(0) : query.getCostSum();
        if (CollectionUtils.isEmpty(query.getChargeItemIds())){
            return null;
        }
        List<HawbItem> items = itemService.list(new QueryWrapper<HawbItem>()
                .eq("waybill_code", query.getWaybillCode())
                .eq("is_del", 0));
        if (!CollectionUtils.isEmpty(items)){
            List<HawbItem> collect = items.stream().filter(e -> "收费项目".equals(e.getFreightType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect)){
                list.addAll(collect);
            }
            List<HawbItem> collect1 = items.stream().filter(e -> !"收费项目".equals(e.getFreightType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect1)){
                itemService.removeById(collect1.get(0).getId());
            }
        }else {
            // 收费项目默认计费
            List<ChargeItem> chargeItem = chargeItemMapper.selectByDept(deptId,"HAWB");
            List<ChargeItem> itemList = chargeItem.stream()
                    .filter(e -> StringUtils.isEmpty(e.getCode()) || query.getCarrier1().equals(e.getCode()))
                    .collect(Collectors.toList());
            for (ChargeItem item : itemList) {
                BigDecimal decimal = getBigDecimal(item, item.getDefaultCostRate(), query.getChargeWeight(), query.getVolume(),query.getQuantity());
                HawbItem hawbItem = new HawbItem();
                hawbItem.setWaybillCode(query.getWaybillCode());
                hawbItem.setChargeItemId(item.getId());
                hawbItem.setDeptId(deptId);
                hawbItem.setRate(item.getDefaultCostRate());
                hawbItem.setCostType(item.getName());
                hawbItem.setFreightType("收费项目");
                hawbItem.setSettleType(item.getPurpose());
                hawbItem.setCharging(decimal);
                list.add(hawbItem);
            }
        }
        Customer customer = customerMapper.selectOne(new QueryWrapper<Customer>()
                .eq("abbreviation", query.getShipperAbb())
                .eq("dept_id", deptId));
        if (customer == null){
            if (query.getOldItemId() != null){
                FreightRateItem oldRateItem = freightRateItemMapper.selectById(query.getOldItemId());
                FreightRate freightRate = freightRateMapper.selectById(oldRateItem.getRateId());
                FareVo multiply = getInterval(oldRateItem, query.getChargeWeight());
                vo.setRateType(multiply.getRateType());
                // 取舍方式计算
                BigDecimal round = bigDecimalRound(freightRate.getRoundingMethod(), multiply.getCostSum());
                if (round.compareTo(oldRateItem.getMinimumFreight()) <= 0 ){
                    costSum = costSum.subtract(oldRateItem.getMinimumFreight());
                }else {
                    costSum = costSum.subtract(round);
                }
            }
            FreightRateItem rateItem = freightRateItemMapper.selectById(query.getChargeItemIds().get(0));
            FreightRate freightRate = freightRateMapper.selectById(rateItem.getRateId());
            FareVo multiply = getInterval(rateItem, query.getChargeWeight());
            vo.setRateType(multiply.getRateType());
            // 取舍方式计算
            BigDecimal round = bigDecimalRound(freightRate.getRoundingMethod(), multiply.getCostSum());
            HawbItem hawbItem = new HawbItem();
            hawbItem.setWaybillCode(query.getWaybillCode());
            hawbItem.setChargeItemId(rateItem.getId());
            hawbItem.setDeptId(deptId);
            hawbItem.setRate(multiply.getRate());
            hawbItem.setCostType(freightRate.getRateName());
            hawbItem.setFreightType("航空运费");
            hawbItem.setSettleType("应收");
            if (round.compareTo(rateItem.getMinimumFreight()) <= 0 ){
                hawbItem.setCharging(rateItem.getMinimumFreight());
                vo.setRateType("M");
                list.add(hawbItem);
                costSum = costSum.add(rateItem.getMinimumFreight());
            }else {
                hawbItem.setCharging(round);
                list.add(hawbItem);
                costSum = costSum.add(round);
            }
        }else {
            LocalDate currentDate = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String formattedDate = currentDate.format(formatter);
            FreightRateCustom rateCustom = rateCustomMapper.selectOne(new QueryWrapper<FreightRateCustom>()
                    .eq("applicable_documents", "HAWB")
                    .eq("customer",customer.getName())
                    .eq("dept_id",deptId)
                    .le("effective_date",formattedDate)
                    .ge("expiration_date",formattedDate)
                    .eq("is_del",0));
            if (rateCustom != null){
                FreightRateCustomItem customItem = new FreightRateCustomItem();
                if (rateCustom.getRateModel() == 1){
                    List<FreightRateCustomAir> customAirs = customAirMapper.selectList(new QueryWrapper<FreightRateCustomAir>().eq("custom_price_id",rateCustom.getId()));
                    List<FreightRateCustomAir> customAirList = new ArrayList<>();
                    int maxCount = 0;
                    for (FreightRateCustomAir customAir : customAirs) {
                        int matchCount = 0;
                        if (customAir.getCargoCategory() != null && !customAir.getCargoCategory().isEmpty()) {
                            Set<String> categorySet = new HashSet<>(Arrays.asList(customAir.getCargoCategory().split(",")));
                            if (!categorySet.contains(cargoCode.getCategoryCode())) {
                                continue;
                            }else {
                                matchCount++;
                            }
                        }else {
                            matchCount++;
                        }
                        if (customAir.getCargoCode() == null) {
                            matchCount++;
                        }else if (Arrays.asList(customAir.getCargoCode().split(",")).contains(query.getCargoCode())) {
                            matchCount+=2;
                        }
                        if (matchCount > 0) {
                            if (matchCount > maxCount) {
                                maxCount = matchCount;
                                customAirList.clear();
                                customAirList.add(customAir);
                            } else if (matchCount == maxCount) {
                                customAirList.add(customAir);
                            }
                        }
                    }
                    if (!CollectionUtils.isEmpty(customAirList) && customAirList.size() == 1){
                        FreightRateCustomAir customAir = customAirList.get(0);
                        FreightRateAirItem airItem = rateAirItemMapper.selectById(query.getChargeItemIds().get(0));
                        customItem.setId(airItem.getId());
                        customItem.setMinimumFreight(customAir.getMinimumFreight().add(airItem.getMinimumFreight()));
                        customItem.setRateWeightRange5(customAir.getRateWeightRange5().add(airItem.getRateWeightRange5()));
                        customItem.setRateWeightRange10(customAir.getRateWeightRange10().add(airItem.getRateWeightRange10()));
                        customItem.setBaseRateN(customAir.getBaseRateN().add(airItem.getBaseRateN()));
                        customItem.setRateWeightRange45(customAir.getRateWeightRange45().add(airItem.getRateWeightRange45()));
                        customItem.setRateWeightRange100(customAir.getRateWeightRange100().add(airItem.getRateWeightRange100()));
                        customItem.setRateWeightRange300(customAir.getRateWeightRange300().add(airItem.getRateWeightRange300()));
                        customItem.setRateWeightRange500(customAir.getRateWeightRange500().add(airItem.getRateWeightRange500()));
                        customItem.setRateWeightRange1000(customAir.getRateWeightRange1000().add(airItem.getRateWeightRange1000()));
                        if (query.getOldItemId() != null){
                            FreightRateAirItem oldRateItem = rateAirItemMapper.selectById(query.getOldItemId());
                            FreightRateAir freightRate = rateAirMapper.selectById(oldRateItem.getRateId());
                            FareVo multiply = getInterval(oldRateItem, query.getChargeWeight());
                            vo.setRateType(multiply.getRateType());
                            // 取舍方式计算
                            BigDecimal round = bigDecimalRound(freightRate.getRoundingMethod(), multiply.getCostSum());
                            if (round.compareTo(oldRateItem.getMinimumFreight()) <= 0 ){
                                costSum = costSum.subtract(oldRateItem.getMinimumFreight());
                            }else {
                                costSum = costSum.subtract(round);
                            }
                        }
                    }
                }else {
                    customItem = customItemMapper.selectById(query.getChargeItemIds().get(0));
                    if (query.getOldItemId() != null){
                        FreightRateCustomItem oldRateItem = customItemMapper.selectById(query.getOldItemId());
                        FreightRateCustom freightRate = rateCustomMapper.selectById(oldRateItem.getRateId());
                        FareVo multiply = getInterval(oldRateItem, query.getChargeWeight());
                        vo.setRateType(multiply.getRateType());
                        // 取舍方式计算
                        BigDecimal round = bigDecimalRound(freightRate.getRoundingMethod(), multiply.getCostSum());
                        if (round.compareTo(oldRateItem.getMinimumFreight()) <= 0 ){
                            vo.setRateType("M");
                            costSum = costSum.subtract(oldRateItem.getMinimumFreight());
                        }else {
                            costSum = costSum.subtract(round);
                        }
                    }
                }
                FareVo multiply = getInterval(customItem, query.getChargeWeight());
                vo.setRateType(multiply.getRateType());
                // 取舍方式计算
                BigDecimal round = bigDecimalRound(rateCustom.getRoundingMethod(), multiply.getCostSum());
                HawbItem hawbItem = new HawbItem();
                hawbItem.setWaybillCode(query.getWaybillCode());
                hawbItem.setChargeItemId(customItem.getId());
                hawbItem.setDeptId(deptId);
                hawbItem.setRate(multiply.getRate());
                hawbItem.setCostType(rateCustom.getRateName());
                hawbItem.setFreightType("航空运费");
                hawbItem.setSettleType("应收");
                if (round.compareTo(customItem.getMinimumFreight()) <= 0 ){
                    hawbItem.setCharging(customItem.getMinimumFreight());
                    list.add(hawbItem);
                    vo.setRateType("M");
                    costSum = costSum.add(customItem.getMinimumFreight());
                }else {
                    hawbItem.setCharging(round);
                    list.add(hawbItem);
                    costSum = costSum.add(round);
                }
            }
        }
        Hawb hawb = hawbMapper.selectOne(new QueryWrapper<Hawb>()
                .eq("waybill_code", query.getWaybillCode())
                .eq("dept_id", deptId).eq("is_del", 0));
        if (hawb != null){
            for (HawbItem hawbItem : list) {
                if (hawbItem.getId() != null){
                    itemService.updateById(hawbItem);
                }else {
                    itemService.save(hawbItem);
                }
            }
            hawb.setCostSum(costSum);
            hawbMapper.updateById(hawb);
        }
        vo.setHawbItems(list);
        vo.setItemId(query.getChargeItemIds().get(0));
        vo.setCostSum(costSum);
        vo.setChargeWeight(query.getChargeWeight());
        return vo;
    }

    /**
     * 更换运单号
     * @param editWaybillCode 更换参数
     * @return 结果
     */
    @Override
    public int editWaybillCode(EditWaybillCode editWaybillCode) {
        if(!(editWaybillCode.getWaybillCode().length() == 16 && editWaybillCode.getWaybillCode().endsWith("B"))){
            check(editWaybillCode.getWaybillCode());
        }
        Hawb hawb = hawbMapper.selectById(editWaybillCode.getId());
        String code = hawb.getWaybillCode().substring(0, 4);
        Integer checkNum = Integer.valueOf(hawb.getWaybillCode().substring(hawb.getWaybillCode().length() - 1));
        Integer ticketNum = Integer.valueOf(hawb.getWaybillCode().substring(4,12));
        Long ticketId = ticketMapper.selectHawbCheck(code, ticketNum);
        TicketNum num = ticketNumMapper.selectOne(new QueryWrapper<TicketNum>().eq("ticket_id", ticketId).eq("num", ticketNum));
        if (num != null) {
            if (!checkNum.equals(num.getCode())) {
                throw new CustomException("运单校验失败");
            }
            num.setStatus("NOTUSED");
            ticketNumMapper.updateById(num);
        }
        hawb.setWaybillCode(editWaybillCode.getWaybillCode());
        return hawbMapper.updateById(hawb);
    }

    @Override
    public List<HawbQueryVo1> exportList(HawbQuery query) {
        query.setDeptId(SecurityUtils.getHighParentId());
        List<HawbQueryVo1> hawb = hawbMapper.queryVos(query);
        hawb.forEach(e->{
            Mawb mawb = hawbMapper.selectMawbInfoById(e.getId());
            if(mawb != null){
                e.setMasterWaybillCode(mawb.getWaybillCode());
            }
        });
        if (!CollectionUtils.isEmpty(hawb)) {
            for (HawbQueryVo1 hawb1 : hawb) {
                BigDecimal totalCost = new BigDecimal(0);
                List<HawbItem> list = itemService.list(new QueryWrapper<HawbItem>()
                        .eq("hawb_id", hawb1.getId())
                        .eq("is_del", 0));
                if (!CollectionUtils.isEmpty(list)) {
                    List<HawbItem> items = list.stream().filter(e -> "收费项目".equals(e.getFreightType())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(items)) {
                        List<HawbItem> fuelCostList = items.stream().filter(e -> e.getCostType().contains("燃油费")).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(fuelCostList)) {
                            BigDecimal reduce = fuelCostList.stream().map(HawbItem::getCharging).reduce(BigDecimal.ZERO, BigDecimal::add);
                            hawb1.setFuelCost(reduce);
                            totalCost = totalCost.add(reduce);
                        }
                        List<HawbItem> premiumList = items.stream().filter(e -> e.getCostType().contains("保险费")).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(premiumList)) {
                            BigDecimal reduce = premiumList.stream().map(HawbItem::getCharging).reduce(BigDecimal.ZERO, BigDecimal::add);
                            hawb1.setPremium(reduce);
                            totalCost = totalCost.add(reduce);
                        }
                        List<HawbItem> packCostList = items.stream().filter(e -> e.getCostType().contains("包装费")).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(packCostList)) {
                            BigDecimal reduce = packCostList.stream().map(HawbItem::getCharging).reduce(BigDecimal.ZERO, BigDecimal::add);
                            hawb1.setPackCost(reduce);
                            totalCost = totalCost.add(reduce);
                        }
                        List<HawbItem> otherCostList = items.stream().filter(e -> !e.getCostType().contains("包装费") && !e.getCostType().contains("保险费") && !e.getCostType().contains("燃油费")).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(otherCostList)) {
                            BigDecimal reduce = otherCostList.stream().map(HawbItem::getCharging).reduce(BigDecimal.ZERO, BigDecimal::add);
                            hawb1.setOtherCost(reduce);
                            totalCost = totalCost.add(reduce);
                        }
                    }
                    List<HawbItem> airItem = list.stream().filter(e -> !"收费项目".equals(e.getFreightType())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(airItem)) {
                        HawbItem item = airItem.get(0);
                        hawb1.setAirRate(item.getRate());
                        hawb1.setAirCost(item.getCharging());
                        totalCost = totalCost.add(item.getCharging());
                    }
                }
                hawb1.setTotalCost(totalCost);
            }
            HawbQueryVo1 vo = new HawbQueryVo1();
            vo.setWaybillCode("合计");
            int quantity = hawb.stream().mapToInt(HawbQueryVo1::getQuantity).sum();
            vo.setQuantity(quantity);
            BigDecimal weight = hawb.stream().map(HawbQueryVo1::getWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setWeight(weight);
            BigDecimal chargeWeight = hawb.stream().map(HawbQueryVo1::getChargeWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setChargeWeight(chargeWeight);
            BigDecimal airCost = hawb.stream().map(HawbQueryVo1::getAirCost).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setAirCost(airCost);
            BigDecimal fuelCost = hawb.stream().map(HawbQueryVo1::getFuelCost).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setFuelCost(fuelCost);
            BigDecimal premium = hawb.stream().map(HawbQueryVo1::getPremium).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setPremium(premium);
            BigDecimal packCost = hawb.stream().map(HawbQueryVo1::getPackCost).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setPackCost(packCost);
            BigDecimal otherCost = hawb.stream().map(HawbQueryVo1::getOtherCost).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setOtherCost(otherCost);
            BigDecimal totalCost = hawb.stream().map(HawbQueryVo1::getTotalCost).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setTotalCost(totalCost);
            hawb.add(vo);
        }
        return hawb;
    }

    @Override
    public int payComp(Long id, String money) {
        Hawb hawb = hawbMapper.selectById(id);
        if (StringUtils.isEmpty(money)){
            BigDecimal payMoney = new BigDecimal(money);
            hawb.setMoney(payMoney);
        }
        hawb.setPayStatus(1);
        hawb.setPayTime(new Date());
        return hawbMapper.updateById(hawb);
    }

    /**
     * 查询是否启用单证控制
     * @return 0 否 1 是
     */
    @Override
    public String isCtrl() {
        Long deptId = SecurityUtils.getHighParentId();
        TicketCtrl ctrl = operateMapper.selectOne(new QueryWrapper<TicketCtrl>()
                .eq("code", "HAWB")
                .eq("domint",'D'));
//                .eq("dept_id",deptId));
//        if (ctrl == null || ctrl.getControlEnabled().equals(0)){
        if (ctrl == null || !ctrl.getDeptIds().contains(deptId.toString())){
            String waybillCode = null;
            boolean isExit = true;
            while (isExit){
                long currentTimeMillis = System.currentTimeMillis();
                String sevenDigitNumber = String.format("%07d", currentTimeMillis % 10000000);
                waybillCode = "HAWB" + sevenDigitNumber;
                HawbVo info = getInfo(waybillCode);
                if (info.getId() == null){
                    isExit = false;
                }
            }
            return waybillCode;
        }else {
            return "0";
        }
    }

    /**
     * 运单作废
     *
     * @param waybillCode 运单号
     * @return 返回结果
     */
    @Override
    public int invalid(String waybillCode) {

        if (waybillCode == null){
            throw new CustomException("运单号不能为空");
        }
        if (waybillCode.length() != 11){
            throw new CustomException("运单号格式错误");
        }
        Hawb hawb = hawbMapper.selectOne(new QueryWrapper<Hawb>()
                .eq("waybill_code", waybillCode)
                .eq("dept_id", SecurityUtils.getHighParentId())
                .eq("is_del",0)
                .eq("status", "NORMAL"));
        if (hawb == null){
            throw new CustomException("无当前分单信息");
        }
        hawb.setStatus("INVALID");
        return hawbMapper.updateById(hawb);
    }

    /**
     * 获取计算之后价格
     * @param chargeItem 收费项目数据
     * @param defaultCostRate 默认费用/费率
     * @param weight2 重量
     * @param volume2 体积
     * @return 价格
     */
    private BigDecimal getBigDecimal(ChargeItem chargeItem, BigDecimal defaultCostRate, BigDecimal weight2, BigDecimal volume2,Integer quantity2) {
        BigDecimal decimal = new BigDecimal(0);
        Integer defaultBillingMethod = chargeItem.getDefaultBillingMethod();
        switch (defaultBillingMethod) {
            case 0:
                decimal = bigDecimalRound(chargeItem.getRoundRule(), defaultCostRate);
                break;
            case 1:
                BigDecimal weight = defaultCostRate.multiply(weight2);
                decimal = bigDecimalRound(chargeItem.getRoundRule(), weight);
                break;
            case 2:
                BigDecimal volume = defaultCostRate.multiply(volume2);
                decimal = bigDecimalRound(chargeItem.getRoundRule(), volume);
                break;
            case 3:
                BigDecimal quantity = defaultCostRate.multiply(new BigDecimal(quantity2));
                decimal = bigDecimalRound(chargeItem.getRoundRule(),quantity);
            default:
                break;
        }
        return decimal;
    }

    /**
     * bigDecimal类型四舍五入方法
     *
     * @param roundRule 收费项目id
     * @param defaultCostRate 收费项目id
     * @return 返回四舍五入之后的BigDecimal
     */
    private BigDecimal bigDecimalRound(Integer roundRule, BigDecimal defaultCostRate){
        switch (roundRule){
            case 0:
                // 实际
                return defaultCostRate;
            case 1:
            case 7:
            case 10:
                // 直接舍位到0.5
                // 直接进位到个位
                // 四舍五入至个位
                return defaultCostRate.setScale(0, RoundingMode.HALF_UP);
            case 2:
                // 四舍五入至0.1
                return defaultCostRate.setScale(1, BigDecimal.ROUND_HALF_UP);
            case 3:
                // 四舍五入至0.01
                return defaultCostRate.setScale(2, BigDecimal.ROUND_HALF_UP);
            case 4:
                // 四舍五入至0.001
                return defaultCostRate.setScale(3, BigDecimal.ROUND_HALF_UP);
            case 5:
            case 6:
                // 直接舍位到0.1
                // 直接进位到0.1
                return defaultCostRate.multiply(BigDecimal.TEN).setScale(0,RoundingMode.HALF_UP).divide(BigDecimal.TEN);
            case 8:
                // 直接舍位到个位
                return defaultCostRate.setScale(0, BigDecimal.ROUND_DOWN);
            case 9:
                // 直接进位到0.5
                return defaultCostRate.round(new MathContext(1, RoundingMode.HALF_UP));
            default:
                return defaultCostRate;
        }
    }

    /**
     * 计算计费重量以及价格
     *
     * @param rateItem 计费区间费率
     * @param weight 实际重量
     * @return 自动运价返回参数
     */
    private FareVo getInterval(RateItem rateItem, BigDecimal weight){
        FareVo vo = new FareVo();
        // 初始化最接近的值为null
        BigDecimal closestValue = null;
        BigDecimal chargeWeight = null;
        BigDecimal rate = null;
        String type = null;
        // 实际重量计算出来的费用
        BigDecimal multiply = new BigDecimal(0);
        String rateType = null;
        Map<RateItemVo, BigDecimal> lowest = lowest(rateItem);
        // (0,5)区间实际重量价格
        if (weight.compareTo(new BigDecimal(0)) > 0 && weight.compareTo(new BigDecimal(5)) < 0){
            multiply = weight.multiply(rateItem.getRateWeightRange5() == null ? new BigDecimal(0) : rateItem.getRateWeightRange5());
            rate = rateItem.getRateWeightRange5();
            rateType = "N";
        }

        // [5,10)区间实际重量价格
        if (weight.compareTo(new BigDecimal(5)) >= 0 && weight.compareTo(new BigDecimal(10)) < 0){
            multiply = weight.multiply(rateItem.getRateWeightRange10() == null ? new BigDecimal(0) : rateItem.getRateWeightRange10());
            rate = rateItem.getRateWeightRange10();
            rateType = "N";
        }

        // [10,45)区间实际重量价格
        if (weight.compareTo(new BigDecimal(10)) >= 0 && weight.compareTo(new BigDecimal(45)) < 0){
            multiply = weight.multiply(rateItem.getBaseRateN() == null ? new BigDecimal(0) : rateItem.getBaseRateN());
            rate = rateItem.getBaseRateN();
            rateType = "N";
        }

        // [45,100)区间实际重量价格
        if (weight.compareTo(new BigDecimal(45)) >= 0 && weight.compareTo(new BigDecimal(100)) < 0){
            multiply = weight.multiply(rateItem.getRateWeightRange45() == null ? new BigDecimal(0) : rateItem.getRateWeightRange45());
            rate = rateItem.getRateWeightRange45();
            rateType = "Q";
        }

        // [100,300)区间实际重量价格
        if (weight.compareTo(new BigDecimal(100)) >= 0 && weight.compareTo(new BigDecimal(300)) < 0){
            multiply = weight.multiply(rateItem.getRateWeightRange100() == null ? new BigDecimal(0) : rateItem.getRateWeightRange100());
            rate = rateItem.getRateWeightRange100();
            rateType = "Q";
        }

        // [300,500)区间实际重量价格
        if (weight.compareTo(new BigDecimal(300)) >= 0 && weight.compareTo(new BigDecimal(500)) < 0){
            multiply = weight.multiply(rateItem.getRateWeightRange300() == null ? new BigDecimal(0) : rateItem.getRateWeightRange300());
            rate = rateItem.getRateWeightRange300();
            rateType = "Q";
        }

        // [500,1000)区间实际重量价格
        if (weight.compareTo(new BigDecimal(500)) >= 0 && weight.compareTo(new BigDecimal(1000)) < 0){
            multiply = weight.multiply(rateItem.getRateWeightRange500() == null ? new BigDecimal(0) : rateItem.getRateWeightRange500());
            rate = rateItem.getRateWeightRange500();
            rateType = "Q";
        }

        if (weight.compareTo(new BigDecimal(1000)) >= 0){
            multiply = weight.multiply(rateItem.getRateWeightRange1000() == null ? new BigDecimal(0) : rateItem.getRateWeightRange1000());
            rate = rateItem.getRateWeightRange1000();
            rateType = "Q";
        }

        if (rateItem.getRateWeightRange5() == null
                && rateItem.getRateWeightRange10() == null
                && weight.compareTo(new BigDecimal(45)) <= 0){
            multiply = weight.multiply(rateItem.getBaseRateN() == null ? new BigDecimal(0) : rateItem.getBaseRateN());
            rate = rateItem.getBaseRateN();
            rateType = "N";
        }
        for (Map.Entry<RateItemVo, BigDecimal> bigDecimal : lowest.entrySet()) {
            RateItemVo key = bigDecimal.getKey();
            BigDecimal value = bigDecimal.getValue();
            boolean withinRange;
            if (key.getEndData() != null) {
                withinRange = weight.compareTo(key.getBeginData()) >= 0 && weight.compareTo(key.getEndData()) <= 0;
            } else {
                withinRange = weight.compareTo(key.getBeginData()) >= 0;
            }
            boolean beginDataGreaterThanWeight = key.getBeginData().compareTo(weight) >= 0;
            if ((withinRange || beginDataGreaterThanWeight)&& value.compareTo(multiply) <= 0) {
                if (closestValue == null || value.compareTo(closestValue) < 0) {
                    if (weight.compareTo(key.getBeginData()) >= 0 && weight.compareTo(key.getEndData()) <= 0){
                        chargeWeight = weight;
                        closestValue = multiply;
                        type = rateType;
                    }else {
                        chargeWeight = key.getBeginData();
                        closestValue = value;
                        rate = key.getRate();
                        type = key.getRateType();
                    }
                }
            }
        }
        vo.setCostSum(closestValue);
        vo.setChargeWeight(chargeWeight);
        vo.setRate(rate);
        vo.setRateType(type);
        return vo;
    }

    /**
     * 计算重量区间最低计费价格
     *
     * @param rateItem 计费区间费率
     * @return 计费区间价格
     */
    private Map<RateItemVo,BigDecimal> lowest(RateItem rateItem) {
        Map<RateItemVo,BigDecimal> map = new HashMap<>();
        // (0,5)区间最低价格
        BigDecimal multiply1 = new BigDecimal(1).multiply(rateItem.getRateWeightRange5() == null ? new BigDecimal(0) : rateItem.getRateWeightRange5());
        RateItemVo vo1 = new RateItemVo();
        vo1.setRate(rateItem.getRateWeightRange5());
        vo1.setBeginData(new BigDecimal(1));
        vo1.setEndData(new BigDecimal(5));
        vo1.setRateType("N");
        map.put(vo1,multiply1);

        // [5,10)区间最低价格
        BigDecimal multiply5 = new BigDecimal(5).multiply(rateItem.getRateWeightRange10() == null ? new BigDecimal(0) : rateItem.getRateWeightRange10());
        RateItemVo vo2 = new RateItemVo();
        vo2.setRate(rateItem.getRateWeightRange10());
        vo2.setBeginData(new BigDecimal(5));
        vo2.setEndData(new BigDecimal(10));
        vo2.setRateType("N");
        map.put(vo2,multiply5);

        // [10,45)区间最低价格
        BigDecimal multiply10 = new BigDecimal(10).multiply(rateItem.getBaseRateN() == null ? new BigDecimal(0) : rateItem.getBaseRateN());
        RateItemVo vo3 = new RateItemVo();
        vo3.setRate(rateItem.getBaseRateN());
        vo3.setBeginData(new BigDecimal(10));
        vo3.setEndData(new BigDecimal(45));
        vo3.setRateType("N");
        map.put(vo3,multiply10);

        // [45,100)区间最低价格
        BigDecimal multiply45 = new BigDecimal(45).multiply(rateItem.getRateWeightRange45() == null ? new BigDecimal(0) : rateItem.getRateWeightRange45());
        RateItemVo vo4 = new RateItemVo();
        vo4.setRate(rateItem.getRateWeightRange45());
        vo4.setBeginData(new BigDecimal(45));
        vo4.setEndData(new BigDecimal(100));
        vo4.setRateType("Q");
        map.put(vo4,multiply45);

        // [100,300)区间最低价格
        BigDecimal multiply100 = new BigDecimal(100).multiply(rateItem.getRateWeightRange100() == null ? new BigDecimal(0) : rateItem.getRateWeightRange100());
        RateItemVo vo5 = new RateItemVo();
        vo5.setRate(rateItem.getRateWeightRange100());
        vo5.setBeginData(new BigDecimal(100));
        vo5.setEndData(new BigDecimal(300));
        vo5.setRateType("Q");
        map.put(vo5,multiply100);

        // [300,500)区间最低价格
        BigDecimal multiply300 = new BigDecimal(300).multiply(rateItem.getRateWeightRange300() == null ? new BigDecimal(0) : rateItem.getRateWeightRange300());
        RateItemVo vo6 = new RateItemVo();
        vo6.setRate(rateItem.getRateWeightRange300());
        vo6.setBeginData(new BigDecimal(300));
        vo6.setEndData(new BigDecimal(500));
        vo6.setRateType("Q");
        map.put(vo6,multiply300);

        // [500,1000)区间最低价格
        BigDecimal multiply500 = new BigDecimal(500).multiply(rateItem.getRateWeightRange500() == null ? new BigDecimal(0) : rateItem.getRateWeightRange500());
        RateItemVo vo7 = new RateItemVo();
        vo7.setRate(rateItem.getRateWeightRange500());
        vo7.setBeginData(new BigDecimal(500));
        vo7.setEndData(new BigDecimal(1000));
        vo7.setRateType("Q");
        map.put(vo7,multiply500);

        // [1000,∞)区间最低价格
        BigDecimal multiply1000 = new BigDecimal(1000).multiply(rateItem.getRateWeightRange1000() == null ? new BigDecimal(0) : rateItem.getRateWeightRange1000());
        RateItemVo vo8 = new RateItemVo();
        vo8.setRate(rateItem.getRateWeightRange1000());
        vo8.setBeginData(new BigDecimal(1000));
        vo8.setEndData(new BigDecimal(Integer.MAX_VALUE));
        vo8.setRateType("Q");
        map.put(vo8,multiply1000);

        return map;
    }

    private byte[] downloadFileFromUrl(String urlStr) throws Exception {
        URL url = new URL(urlStr);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(5000);
        connection.setReadTimeout(5000);

        if (connection.getResponseCode() != 200) {
            throw new RuntimeException("Failed to connect, HTTP error code : " + connection.getResponseCode());
        }

        try (InputStream inputStream = connection.getInputStream();
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            return baos.toByteArray();
        }
    }
}
