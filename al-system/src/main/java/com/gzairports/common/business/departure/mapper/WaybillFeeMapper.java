package com.gzairports.common.business.departure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.departure.domain.WaybillFee;
import com.gzairports.wl.cargofee.domain.query.WaybillFeeQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 运单费用明细Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@Mapper
public interface WaybillFeeMapper extends BaseMapper<WaybillFee> {

    /**
     * 查询运单费用明细列表
     * @param query 查询参数
     * @return 运单费用明细列表
     */
    List<WaybillFee> selectListByQuery(WaybillFeeQuery query);
}
