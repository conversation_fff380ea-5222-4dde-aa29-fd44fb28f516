package com.gzairports.common.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.Customer;
import com.gzairports.common.basedata.mapper.CustomerMapper;
import com.gzairports.common.basedata.service.ICustomerService;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 客户Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-31
 */
@Service
public class CustomerServiceImpl extends ServiceImpl<CustomerMapper, Customer> implements ICustomerService
{
    @Autowired
    private CustomerMapper customerMapper;

    @Autowired
    private SysDeptMapper deptMapper;

    /**
     * 查询客户
     * 
     * @param id 客户主键
     * @return 客户
     */
    @Override
    public Customer selectCustomerById(Long id)
    {
        return customerMapper.selectCustomerById(id);
    }

    /**
     * 查询客户列表
     * 
     * @param customer 客户
     * @return 客户
     */
    @Override
    public List<Customer> selectCustomerList(Customer customer)
    {
        customer.setDeptId(SecurityUtils.getHighParentId());
        return customerMapper.selectCustomerList(customer);
    }

    /**
     * 新增客户
     * 
     * @param customer 客户
     * @return 结果
     */
    @Override
    public int insertCustomer(Customer customer)
    {
        Long deptId = SecurityUtils.getHighParentId();
        List<Customer> custom = customerMapper.selectList(new QueryWrapper<Customer>()
                .eq("abbreviation", customer.getAbbreviation())
                .eq("dept_id", deptId)
                .eq("is_del", 0));
        if (!CollectionUtils.isEmpty(custom)){
            throw new CustomException("客户简称不能重复");
        }
        customer.setCreateTime(DateUtils.getNowDate());

//        while(true){
//            Long parentId = deptMapper.selectParentIdByDeptId(deptId);
//            if (StringUtils.isNotNull(parentId)){
//                deptId = parentId;
//                if (deptMapper.selectParentIdByDeptId(deptId) == 0){
//                    break;
//                }
//            }
//        }
        customer.setDeptId(deptId);
        return customerMapper.insertCustomer(customer);
    }

    /**
     * 修改客户
     * 
     * @param customer 客户
     * @return 结果
     */
    @Override
    public int updateCustomer(Customer customer)
    {
        Long deptId = SecurityUtils.getLoginUser().getDeptId();
        Customer custom = customerMapper.selectOne(new QueryWrapper<Customer>()
                .eq("abbreviation", customer.getAbbreviation())
                .eq("dept_id", deptId));
        if (custom != null && !Objects.equals(custom.getId(), customer.getId())){
            throw new CustomException("客户简称不能重复");
        }
        return customerMapper.updateCustomer(customer);
    }

    /**
     * 批量删除客户
     * 
     * @param ids 需要删除的客户主键
     * @return 结果
     */
    @Override
    public int deleteCustomerByIds(Long[] ids)
    {
        return customerMapper.deleteCustomerByIds(ids);
    }

    /**
     * 删除客户信息
     * 
     * @param id 客户主键
     * @return 结果
     */
    @Override
    public int deleteCustomerById(Long id)
    {
        return customerMapper.deleteCustomerById(id);
    }

    /**
     * 根据简称查询客户信息
     *
     * @param consigneeAbb 收货人（客户）简称
     * @return 收货人（客户）信息
     */
    @Override
    public Customer abb(String consigneeAbb) {
        //拿到最父级的id
        Long deptId = SecurityUtils.getHighParentId();
//        while(true){
//            Long parentId = deptMapper.selectParentIdByDeptId(deptId);
//            if (StringUtils.isNotNull(parentId)){
//                deptId = parentId;
//                if (deptMapper.selectParentIdByDeptId(deptId) == 0){
//                    break;
//                }
//            }
//        }
        return customerMapper.selectOne(new QueryWrapper<Customer>()
                .eq("abbreviation",consigneeAbb)
                .eq("dept_id",deptId)
                .eq("is_del",0));
    }
}
