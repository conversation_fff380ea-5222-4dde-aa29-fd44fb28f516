package com.gzairports.wl.reporter.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lan
 * @Desc: 主分单收入汇总返回参数
 * @create: 2025-03-12 11:24
 **/

@Data
public class AHIncomeTotalVO {
    /** 主单号 */
    private String masterWaybillCode;

    /** 分单号 */
    private String waybillCode;

    /** 承运航班 */
    private String flightNo;

    /** 利润 */
    private String profit;

    /** 制单日期 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date writeTime;

    /** 制单日期 */
    private String writeTimeStr;

    /** 起点站 */
    private String sourcePort;

    /** 目的站 */
    private String desPort;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 计费重量 */
    private BigDecimal chargeWeight;

    /** 特货代码 */
    private String specialCargoCode1;

    /** 品名 */
    private String cargoName;

    /** 分单件数 */
    private Integer HawbQuantity;

    /** 分单重量 */
    private BigDecimal HawbWeight;

    /** 分单计费重量 */
    private BigDecimal HawbChargeWeight;

    /** 航空公司结算金额 */
    private String AirCharge;

    /** 货站结算金额 */
    private String HzCharge;

    /** 收发货人结算金额 ->分单收的总金额 */
    private String SfCharge;


}
