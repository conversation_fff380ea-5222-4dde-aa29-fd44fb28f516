<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.infoquery.mapper.FlightMapper">
    <select id="selectFlightList" resultType="com.gzairports.common.infoquery.domain.Flight">
        select flight_id as id, exec_date as flightDate, CONCAT(air_ways,flight_no) AS flightNo, air_ways as air,
               craft_type, craft_no, start_station as sourcePort,start_station_cn as sourcePortChinese,
               terminal_station as desPort,terminal_station_cn as desPortChinese,
               start_scheme_takeoff_time as planTakeoffTime, start_alterate_takeoff_time as expectTakeoffTime,
               start_real_takeoff_time as realityTakeoffTime, terminal_scheme_land_in_time as planLandingTime,
               terminal_reall_and_in_time as expectLandingTime, terminal_alteratel_and_in_time as realityLandingTime,
               providing_state as status, is_offin
        from all_flight_info
        <where>
            <if test="flightDate != null ">
                AND exec_date = #{flightDate}
            </if>
            <if test="flightNo != null and flightNo != ''">
                AND CONCAT(air_ways,flight_no) like concat('%',#{flightNo},'%')
            </if>
            <if test="air != null and air != ''">
                AND air_ways like concat('%',#{air},'%')
            </if>
            <if test="type != null and type != ''">
                AND is_offin = #{type}
            </if>
            <if test="desPort != null and desPort != ''">
                AND terminal_station = #{desPort}
            </if>
        </where>
    </select>
    <select id="selectFlightBookList" resultType="com.gzairports.common.infoquery.domain.Flight">
        select flight_id as id, exec_date as flightDate, CONCAT(air_ways,flight_no) AS flightNo, start_scheme_takeoff_time as planTakeoffTime
        from all_flight_info
        <where>
            <if test="flightDate != null ">
                AND exec_date = #{flightDate}
            </if>
            <if test="desPort != null and desPort != ''">
                AND is_offin = 'D' and air_line_short like concat('%',#{desPort},'%')
            </if>
        </where>
    </select>

    <select id="selectACWList" resultType="com.gzairports.common.infoquery.domain.vo.AirlineWaybillInfoVO">
        select aaw.waybill_code,
        aaw.status,
        aaw.des_port,
        aaw.flight_no1 as flightNo,
        aaw.flight_date1 as flightDate,
        aaw.shipper,
        aaw.cargo_name,
        aaw.cargo_code,
        aaw.write_time,
        aaw.quantity,
        aaw.weight,
        aaw.charge_weight,
        aipu.pick_up_code
        from all_air_waybill aaw
        left join wl_waybill_fee wwf on aaw.waybill_code = wwf.waybill_code
        left join all_in_pick_up aipu on wwf.serial_no = aipu.serial_no
        <where>
            <if test="query.waybillCode != null and query.waybillCode != ''">
                AND aaw.waybill_code = #{query.waybillCode}
            </if>

            <if test="query.type != null and query.type != ''">
                AND aaw.type = #{query.type}
            </if>

            <if test="query.waybillType != null and query.waybillType != ''">
                AND aaw.waybill_type = #{query.waybillType}
            </if>

            <if test="query.enabledWaybillCodePrefixList != null and query.enabledWaybillCodePrefixList.size() > 0">
                AND (
                <foreach collection="query.enabledWaybillCodePrefixList" item="prefix" separator=" OR ">
                    aaw.waybill_code like CONCAT('____', #{prefix}, '%')
                </foreach>
                )
            </if>

            <if test="query.inOutType != null and query.inOutType != ''">
                AND aaw.in_out_type = #{query.inOutType}
            </if>

            <if test="query.status != null and query.status != ''">
                AND aaw.status = #{query.status}
            </if>

            <if test="query.specialCargoCode != null and query.specialCargoCode != ''">
                AND aaw.special_cargo_code1 = #{query.specialCargoCode}
            </if>

            <if test="query.cargoCode != null and query.cargoCode != ''">
                AND aaw.cargo_code = #{query.cargoCode}
            </if>

            <if test="query.desPort != null and query.desPort != ''">
                AND aaw.des_port = #{query.desPort}
            </if>

            <if test="query.flightNo != null and query.flightNo != ''">
                AND aaw.flight_no1 = #{query.flightNo}
            </if>

            <if test="query.writeTimeStart != null">
                AND aaw.write_time &gt;= #{query.writeTimeStart}
            </if>

            <if test="query.writeTimeEnd != null">
                AND aaw.write_time &lt;= #{query.writeTimeEnd}
            </if>
        </where>
    </select>
    <select id="selectByWaybillId" resultType="com.gzairports.common.infoquery.domain.Flight">
        select afi.*
        from hz_flight_load_uld_waybill lum
                 left join hz_flight_load_uld lu on lu.id = lum.load_uld_id
                 left join hz_flight_load hfl on hfl.id = lu.flight_load_id
                 left join all_flight_info afi on afi.flight_id = hfl.flight_id
        where lum.waybill_id = #{waybillId}
    </select>
</mapper>