package com.gzairports.hz.business.departure.domain.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 收费管理列表返回参数
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
public class ChargeVo {

    /** 总单数 */
    private Integer totalOrder;

    /** 未授权支付单数 */
    private Integer unPayOrder;

    /** 已授权支付单数 */
    private Integer payOrder;

    /** 已结算单数 */
    private Integer settleOrder;

    /** 未授权支付费用 */
    private BigDecimal unPay;

    /** 已授权支付费用 */
    private BigDecimal pay;

    /** 已结算费用 */
    private BigDecimal settle;

    /** 未结算费用 */
    private BigDecimal notSettle;

    /** 退款费用 */
    private BigDecimal refund;

    /** 收费管理运单列表 */
    private Page<ChargeWaybillVo> vos;
}
