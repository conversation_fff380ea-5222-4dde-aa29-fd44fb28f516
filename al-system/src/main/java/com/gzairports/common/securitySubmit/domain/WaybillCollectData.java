package com.gzairports.common.securitySubmit.domain;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 1.货站推送收运数据
 * @date 2025-06-17
 **/

@Data
public class WaybillCollectData {
    /** 类型
     * 1.货站推送收运数据
     * 2.货站完成航班/定时任务自动关闭航班
     * 3.物流平台选择换单，将原单号、新单号推送货检系统
     * 4.代理人不正常货邮选择换单
     * 5.货运系统完成退单
     * 6.代理人重复提交品名清单附件
     **/
    private Integer type;

    /** 运单号 */
    private String waybillCode;

    /** 收运件数 */
    private Integer quantity;

    /** 收运重量 */
    private BigDecimal weight;
}
