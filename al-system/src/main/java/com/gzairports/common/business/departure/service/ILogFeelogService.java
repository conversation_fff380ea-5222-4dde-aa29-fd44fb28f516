package com.gzairports.common.business.departure.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.business.departure.domain.vo.TLogFeelog;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 运单日志Service接口
 *
 * <AUTHOR>
 * @date 2023-09-25
 */
public interface ILogFeelogService extends IService<TLogFeelog> {


    /***
     * 根据运单号查询
     * @param stocknoSet
     * @return
     */
    Map<String, List<TLogFeelog>> listByStocks(Collection<String> stocknoSet);

    Map<String, List<TLogFeelog>> listByTime(Date startDate, Date endDate);
}
