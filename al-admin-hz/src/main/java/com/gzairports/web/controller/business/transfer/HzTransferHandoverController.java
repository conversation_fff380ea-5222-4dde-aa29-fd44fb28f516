package com.gzairports.web.controller.business.transfer;

import java.util.List;

import com.gzairports.hz.business.arrival.domain.query.FlightFileQuery;
import com.gzairports.hz.business.transfer.domain.HzTransferHandover;
import com.gzairports.hz.business.transfer.domain.query.FileAndCargoTransferQuery;
import com.gzairports.hz.business.transfer.domain.query.TransferPickQuery;
import com.gzairports.hz.business.transfer.domain.vo.FileAndCargoHandoverVo;
import com.gzairports.hz.business.transfer.domain.vo.TransferHandoverVo;
import com.gzairports.hz.business.transfer.service.IHzTransferHandoverService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.common.core.page.TableDataInfo;

import javax.servlet.http.HttpServletResponse;

/**
 * 中转交接Controller
 * 
 * <AUTHOR>
 * @date 2024-07-27
 */
@RestController
@RequestMapping("/transfer/handover")
@Api(tags = "中转交接")
public class HzTransferHandoverController extends BaseController
{
    @Autowired
    private IHzTransferHandoverService hzTransferHandoverService;

    /**
     * 查询中转交接列表
     */
//    @PreAuthorize("@ss.hasPermi('transfer:handover:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询中转交接列表")
    public TableDataInfo list(HzTransferHandover hzTransferHandover)
    {
        startPage();
        List<HzTransferHandover> list = hzTransferHandoverService.selectHzTransferHandoverList(hzTransferHandover);
        return getDataTable(list);
    }

    /**
     * 导出中转交接列表
     */
    @PreAuthorize("@ss.hasPermi('transfer:handover:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出中转交接列表")
    public void export(HttpServletResponse response, HzTransferHandover hzTransferHandover)
    {

        List<HzTransferHandover> list = hzTransferHandoverService.selectHzTransferHandoverList(hzTransferHandover);
        ExcelUtil<HzTransferHandover> util = new ExcelUtil<HzTransferHandover>(HzTransferHandover.class);
        util.exportExcel(response, list, "货站收费项目数据");
    }

    /**
     * 获取中转交接详细信息
     */
    @PreAuthorize("@ss.hasPermi('transfer:handover:getInfoById')")
    @GetMapping(value = "/getInfoById/{id}")
    @ApiOperation(value = "获取中转交接详细信息")
    public AjaxResult getInfoById(@PathVariable("id") Long id)
    {
        return AjaxResult.success(hzTransferHandoverService.selectHzTransferHandoverById(id));
    }

    /**
     * 根据流水号查询中转交接详细信息
     */
    @PreAuthorize("@ss.hasPermi('transfer:handover:getInfoByNo')")
    @GetMapping(value = "/getInfoByNo/{handoverNo}")
    @ApiOperation(value = "根据流水号查询中转交接详细信息")
    public AjaxResult getInfoByNo(@PathVariable("handoverNo") String handoverNo)
    {
        return AjaxResult.success(hzTransferHandoverService.getInfoByNo(handoverNo));
    }

    /**
     * 保存中转交接
     */
    @PreAuthorize("@ss.hasPermi('transfer:handover:add')")
    @PostMapping
    @ApiOperation(value = "保存中转交接")
    public AjaxResult add(@RequestBody TransferHandoverVo vo)
    {
        return AjaxResult.success(hzTransferHandoverService.insertHzTransferHandover(vo));
    }

    /**
     * 挑单
     */
    @PreAuthorize("@ss.hasPermi('transfer:handover:pickOne')")
    @GetMapping("/pickOne/{waybillCode}")
    @ApiOperation(value = "挑单")
    public AjaxResult pickOne(@PathVariable("waybillCode") String waybillCode){
        return AjaxResult.success(hzTransferHandoverService.pickOne(waybillCode));
    }

    /**
     * 批量挑单
     */
    @PreAuthorize("@ss.hasPermi('transfer:handover:batchPick')")
    @PostMapping("/batchPick")
    @ApiOperation(value = "批量挑单")
    public AjaxResult batchPick(@RequestBody TransferPickQuery query){
        return AjaxResult.success(hzTransferHandoverService.batchPick(query));
    }

    /**
     * 加入提货列表
     */
    @PreAuthorize("@ss.hasPermi('transfer:handover:pickList')")
    @PostMapping("/pickList")
    @ApiOperation(value = "加入提货列表")
    public AjaxResult pickList(@RequestBody Long[] waybillIds){
        return AjaxResult.success(hzTransferHandoverService.pickList(waybillIds));
    }

    /**
     * 删除中转交接数据
     */
    @PreAuthorize("@ss.hasPermi('transfer:handover:delete')")
    @GetMapping("/delete/{id}")
    @ApiOperation(value = "删除中转交接数据")
    public AjaxResult delete(@PathVariable("id") Long id){
        return toAjax(hzTransferHandoverService.delete(id));
    }

    /**
     * 文件或货物中转交接
     */
    @PreAuthorize("@ss.hasPermi('transfer:handover:fileTransfer')")
    @GetMapping("/fileTransfer/{id}")
    @ApiOperation(value = "文件或货物中转交接")
    public AjaxResult fileTransfer(@PathVariable("id") Long id){
        return AjaxResult.success(hzTransferHandoverService.fileTransfer(id));
    }

    /**
     * 根据条件查询文件中转或货物中转数据
     */
    @PreAuthorize("@ss.hasPermi('transfer:handover:getInfoByQuery')")
    @PostMapping("/getInfoByQuery")
    @ApiOperation(value = "根据条件查询文件中转或货物中转数据")
    public AjaxResult getInfoByQuery(@RequestBody FileAndCargoTransferQuery query){
        return AjaxResult.success(hzTransferHandoverService.getInfoByQuery(query));
    }

    /**
     * 根据录单id查询运单详情
     */
    @PreAuthorize("@ss.hasPermi('transfer:handover:getWaybillInfo')")
    @GetMapping("/getWaybillInfo/{orderId}")
    @ApiOperation(value = "根据录单id查询运单详情")
    public AjaxResult getWaybillInfo(@PathVariable Long orderId){
        return AjaxResult.success(hzTransferHandoverService.getWaybillInfo(orderId));
    }

    /**
     * 文件交接
     */
    @PreAuthorize("@ss.hasPermi('transfer:handover:fileHandover')")
    @PostMapping("/fileHandover")
    @ApiOperation(value = "文件交接")
    public AjaxResult fileHandover(@RequestBody FileAndCargoHandoverVo vo){
        return toAjax(hzTransferHandoverService.fileHandover(vo));
    }

    /**
     * 取消文件交接
     */
    @PreAuthorize("@ss.hasPermi('transfer:handover:cancelFileHandover')")
    @GetMapping("/cancelFileHandover/{id}")
    @ApiOperation(value = "取消文件交接")
    public AjaxResult cancelFileHandover(@PathVariable("id") Long id){
        return toAjax(hzTransferHandoverService.cancelFileHandover(id));
    }

    /**
     * 文件接收
     */
    @PreAuthorize("@ss.hasPermi('transfer:handover:fileReceive')")
    @GetMapping("/fileReceive/{id}")
    @ApiOperation(value = "文件接收")
    public AjaxResult fileReceive(@PathVariable("id") Long id){
        return toAjax(hzTransferHandoverService.fileReceive(id));
    }

    /**
     * 货物交接
     */
    @PreAuthorize("@ss.hasPermi('transfer:handover:cargoHandover')")
    @PostMapping("/cargoHandover")
    @ApiOperation(value = "货物交接")
    public AjaxResult cargoHandover(@RequestBody FileAndCargoHandoverVo vo){
        return toAjax(hzTransferHandoverService.cargoHandover(vo));
    }

    /**
     * 取消货物交接
     */
    @PreAuthorize("@ss.hasPermi('transfer:handover:cancelCargoHandover')")
    @GetMapping("/cancelCargoHandover/{id}")
    @ApiOperation(value = "取消货物交接")
    public AjaxResult cancelCargoHandover(@PathVariable("id") Long id){
        return toAjax(hzTransferHandoverService.cancelCargoHandover(id));
    }

    /**
     * 货物接收
     */
    @PreAuthorize("@ss.hasPermi('transfer:handover:cargoReceive')")
    @GetMapping("/cargoReceive/{id}")
    @ApiOperation(value = "货物接收")
    public AjaxResult cargoReceive(@PathVariable("id") Long id){
        return toAjax(hzTransferHandoverService.cargoReceive(id));
    }
}
