package com.gzairports.hz.business.arrival.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gzairports.common.business.arrival.domain.AllPickUpOut;
import com.gzairports.common.business.arrival.domain.HzArrTally;
import com.gzairports.common.business.arrival.mapper.AllPickUpOutMapper;
import com.gzairports.common.business.arrival.mapper.HzArrTallyMapper;
import com.gzairports.common.business.arrival.mapper.PickUpMapper;
import com.gzairports.common.business.departure.domain.Mawb;
import com.gzairports.common.business.departure.mapper.MawbMapper;
import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.common.log.service.impl.WaybillLogServiceImpl;
import com.gzairports.hz.business.arrival.domain.query.WaybillQuery;
import com.gzairports.hz.business.arrival.domain.vo.*;
import com.gzairports.hz.business.arrival.mapper.HzArrRecordOrderMapper;
import com.gzairports.hz.business.arrival.service.IWaybillQueryService;
import com.gzairports.hz.business.departure.domain.AirWaybill;
import com.gzairports.hz.business.departure.mapper.AllAirWaybillMapper;
import com.gzairports.hz.business.transfer.domain.HzTransferHandover;
import com.gzairports.hz.business.transfer.domain.HzTransferHandoverWaybill;
import com.gzairports.hz.business.transfer.mapper.HzTransferHandoverMapper;
import com.gzairports.hz.business.transfer.mapper.HzTransferHandoverWaybillMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 运单查询Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
@Service
public class WaybillQueryServiceImpl implements IWaybillQueryService {

    @Autowired
    private MawbMapper mawbMapper;

    @Autowired
    private AllAirWaybillMapper airWaybillMapper;

    @Autowired
    private HzArrRecordOrderMapper recordOrderMapper;

    @Autowired
    private HzArrTallyMapper tallyMapper;

    @Autowired
    private PickUpMapper pickUpMapper;

    @Autowired
    private AllPickUpOutMapper pickUpOutMapper;

    @Autowired
    private WaybillLogServiceImpl logService;

    @Autowired
    private HzTransferHandoverMapper hzTransferHandoverMapper;

    @Autowired
    private HzTransferHandoverWaybillMapper handoverWaybillMapper;

    /**
     * 运单查询列表
     * @param query 查询参数
     * @return 运单查询列表
     */
    @Override
    public List<WaybillQueryVo> selectList(WaybillQuery query) {
        List<WaybillQueryVo> waybillQueryVos = airWaybillMapper.selectQueryList(query);
        waybillQueryVos.forEach(vo -> {
            List<HzArrTally> hzArrTallyList = tallyMapper.selectList(new QueryWrapper<HzArrTally>()
                    .eq("waybill_code", vo.getWaybillCode()));
            List<AllPickUpOut> pickUpOuts = pickUpOutMapper.selectList(new QueryWrapper<AllPickUpOut>()
                    .eq("waybill_code", vo.getWaybillCode())
                    .eq("is_cancel", 0));
            BigDecimal tallyWeight = hzArrTallyList.stream().map(HzArrTally::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal reduce = pickUpOuts.stream().map(AllPickUpOut::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal subtract = tallyWeight.subtract(reduce);
            vo.setInventoryWeight(String.format("%s/%s", subtract.compareTo(BigDecimal.ZERO) < 0 ? "0" :  subtract, vo.getWeight()));
        });
        return waybillQueryVos;
    }

    /**
     * 运单明细查询
     * @param waybillCode 运单号
     * @return 运单明细
     */
    @Override
    public WaybillDetailVo detail(String waybillCode) {
        WaybillDetailVo vo = airWaybillMapper.detail(waybillCode);
        if (vo != null){
            List<ArrFlightVo> recordOrders = recordOrderMapper.selectArrFlights(vo.getWaybillCode());
            if (!CollectionUtils.isEmpty(recordOrders)){
                vo.setFlightVos(recordOrders);
            }
            List<LhVo> lhVos = tallyMapper.selectLhVos(vo.getWaybillCode());
            if (!CollectionUtils.isEmpty(lhVos)){
                int lhQuantity = lhVos.stream().mapToInt(LhVo::getLhQuantity).sum();
                BigDecimal lhWeight = lhVos.stream().map(LhVo::getLhWeight).reduce(BigDecimal.ZERO,BigDecimal::add);
                vo.setLhQuantity(lhQuantity);
                vo.setLhWeight(lhWeight);
                vo.setLhVos(lhVos);
            }
            List<BdVo> bdVos = pickUpMapper.selectBdList(waybillCode);
            if (!CollectionUtils.isEmpty(bdVos)){
                int bdQuantity = bdVos.stream().mapToInt(BdVo::getOrderQuantity).sum();
                BigDecimal bdWeight = bdVos.stream().map(BdVo::getOrderWeight).reduce(BigDecimal.ZERO,BigDecimal::add);
                vo.setBdQuantity(bdQuantity);
                vo.setBdWeight(bdWeight);
            }
            List<ThVo> thVos = pickUpOutMapper.selectThList(waybillCode);
            if (!CollectionUtils.isEmpty(thVos)){
                int thQuantity = thVos.stream().mapToInt(ThVo::getPieces).sum();
                BigDecimal thWeight = thVos.stream().map(ThVo::getWeight).reduce(BigDecimal.ZERO,BigDecimal::add);
                vo.setThQuantity(thQuantity);
                vo.setThWeight(thWeight);
            }
        }
        return vo;
    }

    /**
     * 运单明细办单列表查询
     * @param waybillCode 运单号
     * @return 办单列表
     */
    @Override
    public List<BdVo> getBdList(String waybillCode) {
        Integer count = airWaybillMapper.selectCount(new QueryWrapper<AirWaybill>()
                .eq("waybill_code", waybillCode)
                .eq("type", "ARR")
                .eq("is_del", 0));
        if (count.equals(0)){
            return null;
        }
        return pickUpMapper.selectBdList(waybillCode);
    }

    /**
     * 运单明细提货列表查询
     * @param waybillCode 运单号
     * @return 提货列表
     */
    @Override
    public List<ThVo> getThList(String waybillCode) {
        Integer count = airWaybillMapper.selectCount(new QueryWrapper<AirWaybill>()
                .eq("waybill_code", waybillCode)
                .eq("type", "ARR")
                .eq("is_del", 0));
        if (count.equals(0)){
            return null;
        }
        return pickUpOutMapper.selectThList(waybillCode);
    }

    /**
     * 运单明细库存列表查询
     * @param waybillCode 运单号
     * @return 库存列表
     */
    @Override
    public List<KcVo> getKcList(String waybillCode) {
        Integer count = airWaybillMapper.selectCount(new QueryWrapper<AirWaybill>()
                .eq("waybill_code", waybillCode)
                .eq("type", "ARR")
                .eq("is_del", 0));
        if (count.equals(0)){
            return null;
        }
        List<KcVo> list = tallyMapper.selectKcList(waybillCode);
        if (CollectionUtils.isEmpty(list)){
            return null;
        }
        List<KcVo> kcVoList = new ArrayList<>();
        for (KcVo kcVo : list) {
            boolean isTransfer = true;
            HzTransferHandoverWaybill handoverWaybill = handoverWaybillMapper.selectOne(new QueryWrapper<HzTransferHandoverWaybill>().eq("waybill_id", kcVo.getWaybillId()));
            if (handoverWaybill != null){
                HzTransferHandover hzTransferHandover = hzTransferHandoverMapper.selectById(handoverWaybill.getTransferId());
                if (hzTransferHandover != null) {
                    if (hzTransferHandover.getFileStatus() == 2 || hzTransferHandover.getCargoStatus() == 2){
                        isTransfer = false;
                    }
                }
            }
            if (isTransfer){
                kcVoList.add(kcVo);
            }
        }
        List<Long> tallyIds = pickUpOutMapper.selectTallyIdtList(waybillCode);
        if (!CollectionUtils.isEmpty(tallyIds)){
            return kcVoList.stream().filter(e -> tallyIds.stream().noneMatch(id -> Objects.equals(e.getPickUpId(), id))).collect(Collectors.toList());
        }

        return kcVoList;
    }

    /**
     * 运单日志查询列表
     * @param waybillCode 运单号
     * @return 日志列表
     */
    @Override
    public List<WaybillLog> selectLogList(String waybillCode) {
        List<WaybillLog> waybillLogs = logService.selectList(waybillCode, "ARR");
        LambdaQueryWrapper<Mawb> lqwWaybill = Wrappers.lambdaQuery(Mawb.class)
                .select(Mawb::getFlightDate1)
                .eq(Mawb::getWaybillCode, waybillCode)
                .eq(Mawb::getType,"ARR");
        Mawb mawb = mawbMapper.selectOne(lqwWaybill);
        waybillLogs.forEach(v->v.setFlightDate(LocalDateTimeUtil.of(mawb.getFlightDate1())));
        return waybillLogs;
    }
}
