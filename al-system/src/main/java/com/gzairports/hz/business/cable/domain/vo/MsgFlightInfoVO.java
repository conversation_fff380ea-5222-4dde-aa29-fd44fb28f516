package com.gzairports.hz.business.cable.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-04-07
 */
@Data
public class MsgFlightInfoVO {

    /** 拉下/卸下id */
    private Long id;

    /** 航班id */
    private Long flightId;

    /** 运单号 */
    private String waybillCode;

    /** 承运人（FFM SCM UCM FBL SPH FWB FHL必填） */
    private String carrier;

    /** 出发航站 （FFM SCM UCM FBL SPH FWB FHL必填） */
    private String departureStation;

    /** 航班日期 */
    private String flightDate;

    /** 航班号 */
    private String flightNo;

    /** 航班类型 */
    private String flightType;

    /** 下一到达站/目的航站 （FFM SCM UCM FBL SPH必填） */
    private String nextStation;

    /** 飞机注册号 */
    private String aircraftRegistration;

    /** 批序列号 */
    private String batchSerialNo;

    /** 国家代码 */
    private String countryCode;

    /** 预计到达时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date eta;

    /** 预计起飞时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date etd;

    /** 计划起飞时间 */
    private LocalDateTime startSchemeTakeoffTime;

}
