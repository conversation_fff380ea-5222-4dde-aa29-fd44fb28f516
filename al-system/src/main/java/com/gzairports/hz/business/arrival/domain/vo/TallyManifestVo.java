package com.gzairports.hz.business.arrival.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 航班文件理货舱单返回参数
 * <AUTHOR>
 * @date 2024-07-18
 */
@Data
public class TallyManifestVo {

    /** 航班日期 */
    private Date execDate;

    /** 航班号 */
    private String flightNo;

    /** 始发站 */
    private String startStation;

    /** 机号 */
    private String craftNo;

    /** 理货次数 */
    private Integer tallyNum;

    /** 分拣员 */
    private String sorter;

    /** 理货员 */
    private String tallyClerk;

    /** 时间 */
    private Date date;

    /** 实际降落时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date terminalAlteratelAndInTime;

    /** 舱单运单列表 */
    private List<FlightFileWaybillVo> waybillVos;

}
