package com.gzairports.common.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.basedata.domain.BasePack;
import com.gzairports.common.basedata.domain.query.BasePackQuery;

import java.util.List;

/**
 * 包装管理Service接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
public interface IPackService extends IService<BasePack> {

    /**
     * 查询包装管理列表
     *
     * @param query 查询参数
     * @return 包装管理列表
     */
    List<BasePack> selectBasePackList(BasePackQuery query);

    /**
     * 新增包装管理
     *
     * @param basePack 包装管理
     * @return 结果
     */
    int addBasePack(BasePack basePack);

    /**
     * 修改包装管理
     *
     * @param basePack 包装管理
     * @return 结果
     */
    int editBasePack(BasePack basePack);

    /**
     * 删除包装管理
     *
     * @param id 包装管理id集合
     * @return 结果
     */
    int delBasePack(Long id);

    /**
     * 根据id获取包装管理
     *
     * @param id 包装管理id
     * @return 包装管理
     */
    BasePack getInfo(Long id);
}
