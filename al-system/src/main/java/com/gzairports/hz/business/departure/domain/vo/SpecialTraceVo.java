package com.gzairports.hz.business.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 特货跟踪列表返回参数
 *
 * <AUTHOR>
 * @date 2024-07-12
 */
@Data
public class SpecialTraceVo {

    /** 主键id */
    private Long id;

    /** 入库时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date wareTime;

    /** 运单号 */
    private String waybillCode;

    /** 航班号 */
    private String flightNo;

    /** 特货代码 */
    private String specialCode;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 始发站 */
    private String sourcePort;

    /** 到达站 */
    private String desPort;

    /** 品名 */
    private String cargoName;

    /** 存放库位 */
    private String wareLocation;

    /** 入库经办人 */
    private String wareUser;

    /** 入库备注 */
    private String wareRemark;

    /** 出库时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date outTime;

    /** 出库航班 */
    private String outFlight;

    /** 出库件数 */
    private Integer outPiece;

    /** 出库重量 */
    private BigDecimal outWeight;

    /** 出库库位 */
    private String outLocation;

    /** 出库经办人 */
    private String outUser;

    /** 出库备注 */
    private String outRemark;
}
