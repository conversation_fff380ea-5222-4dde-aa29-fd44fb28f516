package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BaseWeight;
import com.gzairports.common.basedata.domain.query.WeightUnitQuery;
import com.gzairports.common.basedata.service.IWeightService;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 多重量单位Controller
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@RestController
@RequestMapping("/base/weight")
@Api(tags = "多重量单位数据接口")
public class WeightController extends BaseController {

    @Autowired
    private IWeightService weightService;
    /**
     * 查询重量单位列表
     */
//    @PreAuthorize("@ss.hasPermi('base:weight:weightUnitList')")
    @GetMapping("/weightUnitList")
    @ApiOperation(value = "查询重量单位列表")
    public TableDataInfo weightUnitList(WeightUnitQuery query)
    {
        startPage();
        List<BaseWeight> list = weightService.selectWeightUnitList(query);
        return getDataTable(list);
    }

    /**
     * 导出重量单位
     */
    @PreAuthorize("@ss.hasPermi('base:weight:exportWeightUnit')")
    @Log(title = "导出重量单位", businessType = BusinessType.EXPORT)
    @PostMapping("/exportWeightUnit")
    @ApiOperation(value = "导出重量单位")
    public void exportWeightUnit(HttpServletResponse response, WeightUnitQuery query)
    {
        List<BaseWeight> list = weightService.selectWeightUnitList(query);
        ExcelUtil<BaseWeight> util = new ExcelUtil<BaseWeight>(BaseWeight.class);
        util.exportExcel(response, list, "重量单位");
    }


    /**
     * 导出重量单位下载模板
     */
    @PreAuthorize("@ss.hasPermi('base:weight:exportWeightUnit')")
    @Log(title = "导出重量单位", businessType = BusinessType.EXPORT)
    @PostMapping("/exportWeightUnitNull")
    @ApiOperation(value = "导出重量单位下载模板")
    public void exportWeightUnitNull(HttpServletResponse response)
    {
        List<BaseWeight> list = new ArrayList<>();
        ExcelUtil<BaseWeight> util = new ExcelUtil<BaseWeight>(BaseWeight.class);
        util.exportExcel(response, list, "重量单位下载模板");
    }

    /**
     * 导入重量单位
     */
    @PreAuthorize("@ss.hasPermi('base:weight:importWeight')")
    @Log(title = "导入重量单位", businessType = BusinessType.IMPORT)
    @PostMapping("/importWeight")
    @ApiOperation(value = "导入重量单位")
    public AjaxResult importWeight(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<BaseWeight> util = new ExcelUtil<BaseWeight>(BaseWeight.class);
        List<BaseWeight> weights = util.importExcel(file.getInputStream());
        String message = weightService.importWeight(weights, updateSupport);
        return success(message);
    }

    /**
     * 新增重量单位
     */
    @PreAuthorize("@ss.hasPermi('base:weight:addWeight')")
    @Log(title = "新增重量单位", businessType = BusinessType.INSERT)
    @PostMapping("/addWeight")
    @ApiOperation(value = "新增重量单位")
    public AjaxResult addWeight(@RequestBody BaseWeight weight)
    {
        return toAjax(weightService.addWeight(weight));
    }

    /**
     * 修改重量单位
     */
    @PreAuthorize("@ss.hasPermi('base:weight:editWeight')")
    @Log(title = "修改重量单位", businessType = BusinessType.UPDATE)
    @PostMapping("/editWeight")
    @ApiOperation(value = "修改重量单位")
    public AjaxResult editWeight(@RequestBody BaseWeight weight)
    {
        return toAjax(weightService.editWeight(weight));
    }

    /**
     * 删除重量单位
     */
    @PreAuthorize("@ss.hasPermi('base:weight:delWeight')")
    @Log(title = "删除重量单位", businessType = BusinessType.UPDATE)
    @GetMapping("/delWeight/{ids}")
    @ApiOperation(value = "删除重量单位")
    public AjaxResult delWeight(@PathVariable("ids") Long[] ids)
    {
        return toAjax(weightService.delWeight(ids));
    }

}
