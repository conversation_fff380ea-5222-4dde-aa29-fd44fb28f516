package com.gzairports.wl.coldRegister.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.hz.business.departure.domain.HzColdRegister;
import com.gzairports.hz.business.departure.domain.query.HzColdRegisterQuery;
import com.gzairports.hz.business.departure.domain.vo.ColdQueryVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * Created by david on 2025/1/3
 */
@Mapper
public interface WlColdRegisterMapper extends BaseMapper<HzColdRegister> {

    /**
     * 根据运单号后四位去查询
     * @param waybillCode 后四位运单号
     * @return 冷藏登记列表
     */
    List<String> getWaybillCodeByFour(String waybillCode);

    /**
     * 查询冷藏登记列表
     * @param query 查询参数
     * @return 冷藏登记列表
     */
    List<ColdQueryVo> selectNewListByQuery(HzColdRegisterQuery query);
}
