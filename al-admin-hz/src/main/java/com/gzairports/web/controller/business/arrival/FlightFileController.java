package com.gzairports.web.controller.business.arrival;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.common.log.service.IWaybillLogService;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.ServletUtils;
import com.gzairports.hz.business.arrival.domain.query.FlightFileQuery;
import com.gzairports.hz.business.arrival.domain.query.TallyManifestQuery;
import com.gzairports.hz.business.arrival.domain.vo.EnterWaybillVo;
import com.gzairports.hz.business.arrival.domain.vo.TallyHistoryVo;
import com.gzairports.hz.business.arrival.domain.vo.TallyWaybillVo;
import com.gzairports.hz.business.arrival.service.IFlightFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * 航班文件Controller
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
@RestController
@RequestMapping("/arr/flightFile")
@Api(tags = "航班文件")
public class FlightFileController extends BaseController {

    @Autowired
    private IFlightFileService flightFileService;

    @Autowired
    private IWaybillLogService waybillLogService;


    /**
     * 查询运单列表
     */
//    @PreAuthorize("@ss.hasPermi('dep:flightFile:waybillList')")
    @PostMapping("/waybillList")
    @ApiOperation(value = "查询运单列表")
    public AjaxResult waybillList(@RequestBody FlightFileQuery query){
        return AjaxResult.success(flightFileService.waybillList(query));
    }

    /**
     * 航段查询
     */
//    @PreAuthorize("@ss.hasPermi('dep:flightFile:getLeg')")
    @PostMapping("/getLeg")
    @ApiOperation(value = "航段查询")
    public AjaxResult getLeg(@RequestBody FlightFileQuery query){
        return AjaxResult.success(flightFileService.getLeg(query));
    }

    /**
     * 航司查询
     */
    @PostMapping("/getPrefix")
    @ApiOperation(value = "航段查询")
    public AjaxResult getPrefix(@RequestBody FlightFileQuery query){
        return AjaxResult.success(flightFileService.getPrefix(query));
    }

    /**
     * 运单录入
     */
    @PreAuthorize("@ss.hasPermi('dep:flightFile:enter')")
    @PostMapping("/enter")
    @ApiOperation(value = "新增")
    public AjaxResult enter(@RequestBody EnterWaybillVo vo){
        //运单日志的新增
        HttpServletResponse response = ServletUtils.getResponse();
        WaybillLog waybillLog = waybillLogService.getWaybillLog(
                vo.getWaybillCode(), 1, SecurityUtils.getNickName(),
                vo.getWeight().toString(), vo.getQuantity().toString(), vo.getFlightNo(),
                vo, null, 0, null, new Date(),
                "进港录单保存", "ARR", null);
        try{
        int enter = flightFileService.enter(vo);
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作成功" + "," +
                            "code:" + response.getStatus() + "," +
                            "data:" + enter));
        return toAjax(enter);
        }catch (Exception e)
        {
            waybillLog.setJsonResult(waybillLogService.getJson(
                    "msg:" + "操作失败" +  "," +
                            "code:" + response.getStatus()));
            waybillLog.setErrorMsg(e.getMessage());
            waybillLog.setStatus(1);
            throw new CustomException(e.getMessage());
        }finally {
            waybillLogService.insertWaybillLog(waybillLog);
        }
    }

    /**
     * 根据运单号查询录入运单
     */
    @PreAuthorize("@ss.hasPermi('dep:flightFile:getWaybillInfo')")
    @PostMapping("/getWaybillInfo")
    @ApiOperation(value = "根据运单号查询录入运单")
    public AjaxResult getWaybillInfo(@RequestBody FlightFileQuery query){
        return AjaxResult.success(flightFileService.getWaybillInfo(query));
    }

    /**
     * 保存
     */
    @PreAuthorize("@ss.hasPermi('dep:flightFile:edit')")
    @PostMapping("/edit")
    @ApiOperation(value = "保存")
    public AjaxResult edit(@RequestBody EnterWaybillVo vo){
        return toAjax(flightFileService.edit(vo));
    }

    /**
     * 删除
     */
    @PreAuthorize("@ss.hasPermi('dep:flightFile:edit')")
    @GetMapping("/del")
    @ApiOperation(value = "删除")
    public AjaxResult del(@RequestParam Long id, @RequestParam Long legId){
        return toAjax(flightFileService.del(id,legId));
    }

    /**
     * 保存航班操作
     */
    @PreAuthorize("@ss.hasPermi('dep:flightFile:saveFlightOper')")
    @PostMapping("/saveFlightOper")
    @ApiOperation(value = "保存航班操作")
    public AjaxResult saveFlightOper(@RequestBody FlightFileQuery query){
        return toAjax(flightFileService.saveFlightOper(query));
    }

    /**
     * 理货舱单
     */
    @PreAuthorize("@ss.hasPermi('dep:flightFile:tallyManifest')")
    @PostMapping("/tallyManifest")
    @ApiOperation(value = "理货舱单")
    public AjaxResult tallyManifest(@RequestBody TallyManifestQuery query){
        return AjaxResult.success(flightFileService.tallyManifest(query));
    }

    /**
     * 生成理货舱单
     */
    @PreAuthorize("@ss.hasPermi('dep:flightFile:tallyManifest')")
    @PostMapping("/createTallyManifest")
    @ApiOperation(value = "生成理货舱单")
    public AjaxResult createTallyManifest(@RequestBody TallyManifestQuery query){
        return toAjax(flightFileService.createTallyManifest(query));
    }

    /**
     * 理货列表查询
     */
//    @PreAuthorize("@ss.hasPermi('dep:flightFile:tallyList')")
    @PostMapping("/tallyList")
    @ApiOperation(value = "理货列表查询")
    public AjaxResult tallyList(@RequestBody FlightFileQuery query){
        return AjaxResult.success(flightFileService.tallyList(query));
    }

    /**
     * 根据运单号查询理货运单(app共用)
     */
    @PreAuthorize("@ss.hasPermi('dep:flightFile:getTallyInfo')")
    @PostMapping("/getTallyInfo")
    @ApiOperation(value = "根据运单号查询理货运单(app共用)")
    public AjaxResult getTallyInfo(@RequestBody FlightFileQuery query){
        return AjaxResult.success(flightFileService.getTallyInfo(query));
    }

    /**
     * 理货保存
     */
    @PreAuthorize("@ss.hasPermi('dep:flightFile:tallySave')")
    @PostMapping("/tallySave")
    @ApiOperation(value = "理货保存")
    public AjaxResult tallySave(@RequestBody TallyWaybillVo vo){
        return toAjax(flightFileService.tallySave(vo));
    }

    /**
     * 修改理货历史
     */
    @PostMapping("/tallyUpdate")
    @ApiOperation(value = "修改理货历史")
    public AjaxResult tallyUpdate(@RequestBody TallyHistoryVo vo){
        return toAjax(flightFileService.tallyUpdate(vo));
    }


    /**
     * 取消理货历史
     */
    @PreAuthorize("@ss.hasPermi('dep:flightFile:tallyCancel')")
    @PostMapping("/tallyCancel")
    @ApiOperation(value = "取消理货历史")
    public AjaxResult tallyCancel(@RequestBody TallyHistoryVo vo){
        return toAjax(flightFileService.tallyCancel(vo));
    }

    /**
     * app航班列表
     */
    @PreAuthorize("@ss.hasPermi('dep:flightFile:appFlightList')")
    @GetMapping("/appFlightList")
    @ApiOperation(value = "app航班列表")
    public AjaxResult appFlightList(FlightFileQuery query){
        return AjaxResult.success(flightFileService.appFlightList(query));
    }

    /**
     * app根据航班id查询运单列表
     */
    @PreAuthorize("@ss.hasPermi('dep:flightFile:appWaybillList')")
    @GetMapping("/appWaybillList/{flightId}")
    @ApiOperation(value = "app根据航班id查询运单列表")
    public AjaxResult appWaybillList(@PathVariable("flightId") Long flightId){
        return AjaxResult.success(flightFileService.appWaybillList(flightId));
    }

    /**
     * app理货完成
     */
    @PreAuthorize("@ss.hasPermi('dep:flightFile:appTallyComp')")
    @GetMapping("/appTallyComp/{flightId}")
    @ApiOperation(value = "app理货完成")
    public AjaxResult appTallyComp(@PathVariable("flightId") Long flightId){
        return toAjax(flightFileService.appTallyComp(flightId));
    }

    /**
     * 根据代理人代码得到代理人名称
     * */
    @PostMapping("/shipperForAgentCode")
    @ApiOperation(value = "根据代理人代码得到代理人名称")
    public AjaxResult shipperForAgentCode(@RequestBody String agentCode){
        return AjaxResult.success("操作成功",flightFileService.shipperForAgentCode(agentCode));
    }

    /**
     * 根据代理人代码得到代理人名称
     * */
    @PostMapping("/agentCodeForShipper")
    @ApiOperation(value = "根据代理人代码得到代理人名称")
    public AjaxResult agentCodeForShipper(@RequestBody String shipper){
        return AjaxResult.success("操作成功",flightFileService.agentCodeForShipper(shipper));
    }

    /**
     * 批量理货
     * */
    @PostMapping("/batchTally")
    @ApiOperation(value = "批量理货")
    public AjaxResult batchTally(@RequestBody List<String> tallyIds){
        return AjaxResult.success("操作成功",flightFileService.batchTally(tallyIds));
    }


    /**
     * 根据航班号和运单号找大类 在得到默认的库位信息
     * */
    @PostMapping("/getLocator")
    @ApiOperation(value = "根据航班号和运单号找大类 在得到默认的库位信息")
    public AjaxResult getLocator(@RequestBody FlightFileQuery query){
        return AjaxResult.success(flightFileService.getLocator(query));
    }

    /**
     * 航班结束
     *
     **/
    @PreAuthorize("@ss.hasPermi('dep:flightFile:compFlight')")
    @GetMapping("/compFlight/{flightId}")
    @ApiOperation(value = "航班结束")
    public AjaxResult compFlight(@PathVariable("flightId") Long flightId)
    {
        return toAjax(flightFileService.compFlight(flightId));
    }

    /**
     * 打开航班
     *
     **/
    @PreAuthorize("@ss.hasPermi('dep:flightFile:openFlight')")
    @GetMapping("/openFlight/{flightId}")
    @ApiOperation(value = "打开航班")
    public AjaxResult openFlight(@PathVariable("flightId") Long flightId)
    {
        int i = flightFileService.openFlight(flightId);
        if (i == 403){
            return AjaxResult.error(i,"当前操作没有权限");
        }
        return toAjax(i);
    }

    @PreAuthorize("@ss.hasPermi('dep:flightFile:examine')")
    @GetMapping("/examine/{orderId}")
    @ApiOperation(value = "审核")
    public AjaxResult examine(@PathVariable("orderId") Long orderId)
    {
        return toAjax(flightFileService.examine(orderId));
    }

    @PreAuthorize("@ss.hasPermi('dep:flightFile:cancelExamine')")
    @GetMapping("/cancelExamine/{orderId}")
    @ApiOperation(value = "取消审核")
    public AjaxResult cancelExamine(@PathVariable("orderId") Long orderId)
    {
        return toAjax(flightFileService.cancelExamine(orderId));
    }
}
