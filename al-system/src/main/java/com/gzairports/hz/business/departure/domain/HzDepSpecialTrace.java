package com.gzairports.hz.business.departure.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 特货跟踪表
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
@TableName("hz_dep_special_trace")
public class HzDepSpecialTrace {

    /** 主键id */
    private Long id;

    /** 运单号 */
    private String waybillCode;

    /** 入库时间 */
    private Date wareTime;

    /** 出库件数 */
    private Integer warePiece;

    /** 出库重量 */
    private BigDecimal wareWeight;

    /** 入库库位 */
    private String wareLocation;

    /** 入库经办人 */
    private String wareUser;

    /** 入库备注 */
    private String wareRemark;

    /** 出库时间 */
    private Date outTime;

    /** 出库航班 */
    private String outFlight;

    /** 出库件数 */
    private Integer outPiece;

    /** 出库重量 */
    private BigDecimal outWeight;

    /** 出库库位 */
    private String outLocation;

    /** 出库经办人 */
    private String outUser;

    /** 出库备注 */
    private String outRemark;
}
