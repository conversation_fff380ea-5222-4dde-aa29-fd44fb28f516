package com.gzairports.web.controller.business.departure;

import com.gzairports.common.business.departure.domain.CostDetail;
import com.gzairports.common.business.departure.domain.vo.CountCostVo;
import com.gzairports.common.business.departure.domain.vo.HandSettleVo;
import com.gzairports.common.business.departure.domain.vo.ItemDetailVo;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.poi.*;
import com.gzairports.hz.business.departure.domain.query.BillExportQuery;
import com.gzairports.hz.business.departure.domain.query.ChargeQuery;
import com.gzairports.hz.business.departure.domain.vo.*;
import com.gzairports.hz.business.departure.service.IChargeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 收费管理Controller
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@RestController
@RequestMapping("/dep/charge")
@Api(tags = "收费管理")
public class ChargeController extends BaseController {

    @Autowired
    private IChargeService chargeService;

    /**
     * 收费管理列表
     */
//    @PreAuthorize("@ss.hasPermi('dep:charge:list')")
    @GetMapping("/list")
    @ApiOperation(value = "收费管理列表")
    public AjaxResult list(ChargeQuery query){
        ChargeVo list = chargeService.selectList(query);
        return AjaxResult.success(list);
    }

    /**
     * 导出收费管理列表
     */
//    @PreAuthorize("@ss.hasPermi('dep:charge:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出收费管理列表")
    public void export(HttpServletResponse response, ChargeQuery query){
        List<ChargeWaybillVo> list = chargeService.importData(query);
        if(!CollectionUtils.isEmpty(list)){
            for (ChargeWaybillVo chargeWaybillVo : list) {
                if(StringUtils.isNotEmpty(chargeWaybillVo.getWaybillCode())){
                    String waybillCode = chargeWaybillVo.getWaybillCode();
                    if(waybillCode.contains("AWBA")){
                        chargeWaybillVo.setWaybillCode(waybillCode.substring(4,7) + "-" + waybillCode.substring(7));
                    }else{
                        chargeWaybillVo.setWaybillCode(waybillCode.substring(4,6) + "-" + waybillCode.substring(6));
                    }
                }
                chargeWaybillVo.setWriteDate(chargeWaybillVo.getWriteTime());
            }
        }
        ExcelUtil<ChargeWaybillVo> util = new ExcelUtil<ChargeWaybillVo>(ChargeWaybillVo.class);
        util.exportExcel(response, list, "收费管理");
    }

    /**
     * 费用导出
     */
//    @PreAuthorize("@ss.hasPermi('dep:charge:chargeExport')")
    @PostMapping("/chargeExport")
    @ApiOperation(value = "费用导出")
    public void chargeExport(HttpServletResponse response, ChargeQuery query){
        List<ChargeImportVo> list = chargeService.chargeExport(query);
        ExcelUtil<ChargeImportVo> util = new ExcelUtil<ChargeImportVo>(ChargeImportVo.class);
        util.exportExcel(response, list, "费用明细", "贵阳机场出港收费清单", SecurityUtils.getNickName());
    }


    /**
     * 结算明细费用导出
     */
    @PostMapping("/chargeSettleExport")
    @ApiOperation(value = "结算明细费用导出")
    public void chargeSettleExport(HttpServletResponse response, ChargeQuery query){
        List<ChargeSettleWaybillVo> list = chargeService.chargeSettleExport(query);
        ExcelUtil<ChargeSettleWaybillVo> util = new ExcelUtil<ChargeSettleWaybillVo>(ChargeSettleWaybillVo.class);
        util.exportExcel(response, list, "结算明细费用导出");
    }

    /**
     * 账单导出
     */
    @PreAuthorize("@ss.hasPermi('dep:charge:billExport')")
    @PostMapping("/bill/export")
    @ApiOperation(value = "账单导出")
    public void billExport(HttpServletResponse response, @Validated BillExportQuery query) {
        query.setPageNum(null);
        query.setPageSize(null);
        // 第四版
        if (!CollectionUtils.isEmpty(query.getAgentCode()) && query.getAgentCode().size() == 1){
            List<String> sheetNames = Arrays.asList("账单", "当期退款", "非当期退款", "已结算", "非当期已结算", "未结算", "非当期未结算", "未来航班", "进港已结算", "总未结算");
            BilExportVO vo = chargeService.selectBillNewExportData(query);
            ExcelUtilBillNew<BilExportVO> util = new ExcelUtilBillNew<>(BilExportVO.class);
            util.exportBillExcel(response, vo, "账单");
        }else{
            BillExportVoNewAgent vo = chargeService.selectBillExportDataNewAgent(query);
            ExcelUtilBillDoubleSheet<ChargeBillExportVoAgent, ChargeBillExportNotSettleVoAgent> util = new ExcelUtilBillDoubleSheet<>(ChargeBillExportVoAgent.class, ChargeBillExportNotSettleVoAgent.class);
            util.exportBillExcelAgent(response, vo.getChargeBilExportVOS(), vo.getChargeBilExportVOSNotSettle(),"出港明细");
        }
//        BilExportVO vo = chargeService.selectBillExportData(query);
        //第一版
//        ExcelUtil<ChargeBilExportVO> util = new ExcelUtil<>(ChargeBilExportVO.class);
//        util.exportBillExcel(response,vo.getChargeBilExportVOS(), vo.getChargeBilExportVOSNotSettle(), vo, "账单");
//        第二版
//        ExcelUtilBill<ChargeBilExportVO,ChargeBilExportNotSettleVO> util = new ExcelUtilBill<>(ChargeBilExportVO.class,ChargeBilExportNotSettleVO.class);
//        util.exportBillExcel(response,vo.getChargeBilExportVOS(), vo.getChargeBilExportVOSNotSettle(), vo, "账单");
//        //第三版
//        if (StringUtils.isNotEmpty(query.getAgentCode()) && query.getAgentCode().size() == 1) {
//            query.setAgent(query.getAgentCode().get(0));
//            BillExportVoNew vo = chargeService.selectBillExportDataNew(query);
//            ExcelUtilBillDoubleSheet<ChargeBilExportVO, ChargeBilExportNotSettleVO> util = new ExcelUtilBillDoubleSheet<>(ChargeBilExportVO.class, ChargeBilExportNotSettleVO.class);
//            util.exportBillExcel(response, vo.getChargeBilExportVOS(), vo.getChargeBilExportVOSNotSettle(), vo, "系统对账报表", "出港明细");
//        }else{
//            BillExportVoNewAgent vo = chargeService.selectBillExportDataNewAgent(query);
//            ExcelUtilBillDoubleSheet<ChargeBillExportVoAgent, ChargeBillExportNotSettleVoAgent> util = new ExcelUtilBillDoubleSheet<>(ChargeBillExportVoAgent.class, ChargeBillExportNotSettleVoAgent.class);
//            util.exportBillExcelAgent(response, vo.getChargeBilExportVOS(), vo.getChargeBilExportVOSNotSettle(),"出港明细");
//        }
    }

    /**
     * 财务报表导出
     */
    @PreAuthorize("@ss.hasPermi('dep:charge:financeExport')")
    @PostMapping("/finance/export")
    @ApiOperation(value = "财务报表导出")
    public void financeExport(HttpServletResponse response, @Validated BillExportQuery query){
        query.setPageNum(null);
        query.setPageSize(null);
        if (!CollectionUtils.isEmpty(query.getAgentCode()) && query.getAgentCode().size() == 1){
            BillExportVoFinance vo = chargeService.selectBillFinance(query);
            ExcelUtilFinance<BillExportVoFinance> util = new ExcelUtilFinance<>(BillExportVoFinance.class);
            util.exportBillExcel(response, vo, "财务报表");
        }else{
            BillExportVoNewAgent vo = chargeService.selectBillExportDataNewAgent(query);
            ExcelUtilBillNew<BillExportVoNewAgent> utilBill = new ExcelUtilBillNew<>(BillExportVoNewAgent.class);
            utilBill.exportBillExcelMoreAgent(response, vo,"出港结算明细");
        }
    }

    /**
     * 收费明细
     */
    @PreAuthorize("@ss.hasPermi('dep:charge:getInfo')")
    @GetMapping(value = "/getInfo/{id}")
    @ApiOperation(value = "收费明细")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(chargeService.getInfo(id));
    }

    /**
     * 费用明细详情
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:costInfo')")
    @GetMapping("/costInfo/{id}")
    @ApiOperation(value = "费用明细详情")
    public AjaxResult costInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(chargeService.costInfo(id));
    }

    /**
     * 编辑费用明细
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:editCost')")
    @PostMapping("/editCost")
    @ApiOperation(value = "编辑费用明细")
    public AjaxResult editCost(@RequestBody CostDetail detail)
    {
        return toAjax(chargeService.editCost(detail));
    }

    /**
     * 删除费用明细
     */
    @PreAuthorize("@ss.hasPermi('arr:handlePickUp:delCost')")
    @GetMapping("/delCost/{id}")
    @ApiOperation(value = "删除费用明细")
    public AjaxResult delCost(@PathVariable("id") Long id)
    {
        return toAjax(chargeService.delCost(id));
    }

    /**
     * 新增收费明细
     */
    @PreAuthorize("@ss.hasPermi('dep:charge:add')")
    @PostMapping(value = "/add")
    @ApiOperation(value = "新增收费明细")
    public AjaxResult add(@RequestBody CostDetail detail)
    {
        return toAjax(chargeService.add(detail));
    }

    /**
     * 计算总费用
     */
    @PreAuthorize("@ss.hasPermi('dep:charge:countCost')")
    @PostMapping("/countCost")
    @ApiOperation(value = "计算总费用")
    public AjaxResult countCost(@RequestBody CostDetail detail)
    {
        return AjaxResult.success(chargeService.countCost(detail));
    }

    /**
     * 根据收费项目id查询优先级最高的收费规则
     */
//    @PreAuthorize("@ss.hasPermi('dep:chargeService:getHighRule')")
    @PostMapping("/getHighRule")
    @ApiOperation(value = "根据收费项目id查询优先级最高的收费规则")
    public AjaxResult getHighRule(@RequestBody ItemDetailVo vo){
        return AjaxResult.success(chargeService.getHighRule(vo));
    }

    @PostMapping("/handSettle")
    @ApiOperation(value = "手动结算")
    public AjaxResult handSettle(@RequestBody HandSettleVo vo){
        return toAjax(chargeService.handSettle(vo));
    }

    @PostMapping("/errorDataCost")
    @ApiOperation(value = "计算费用")
    public AjaxResult errorDataCost(@RequestBody CountCostVo vo){
        return toAjax(chargeService.errorDataCost(vo));
    }

}
