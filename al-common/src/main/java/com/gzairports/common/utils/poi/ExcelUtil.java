package com.gzairports.common.utils.poi;

import com.gzairports.common.annotation.Excel;
import com.gzairports.common.annotation.Excel.ColumnType;
import com.gzairports.common.annotation.Excel.Type;
import com.gzairports.common.annotation.Excels;
import com.gzairports.common.config.RuoYiConfig;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.text.Convert;
import com.gzairports.common.exception.UtilException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.DictUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.file.FileTypeUtils;
import com.gzairports.common.utils.file.FileUtils;
import com.gzairports.common.utils.file.ImageUtils;
import com.gzairports.common.utils.reflect.ReflectUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.RegExUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ooxml.POIXMLDocumentPart;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.*;
import org.openxmlformats.schemas.drawingml.x2006.spreadsheetDrawing.CTMarker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Excel相关处理
 *
 * <AUTHOR>
 */
public class ExcelUtil<T> {
    private static final Logger log = LoggerFactory.getLogger(ExcelUtil.class);

    public static final String FORMULA_REGEX_STR = "=|-|\\+|@";

    public static final String[] FORMULA_STR = {"=", "-", "+", "@"};

    /**
     * 用于dictType属性数据存储，避免重复查缓存
     */
    public Map<String, String> sysDictMap = new HashMap<String, String>();

    /**
     * Excel sheet最大行数，默认65536
     */
    public static final int sheetSize = 65536;

    /**
     * 工作表名称
     */
    private String sheetName;

    /**
     * 导出类型（EXPORT:导出数据；IMPORT：导入模板）
     */
    private Type type;

    /**
     * 工作薄对象
     */
    private Workbook wb;

    /**
     * 工作表对象
     */
    private Sheet sheet;

    /**
     * 样式列表
     */
    private Map<String, CellStyle> styles;

    /**
     * 导入导出数据列表
     */
    private List<T> list;

    /**
     * 注解列表
     */
    private List<Object[]> fields;

    /**
     * 当前行号
     */
    private int rownum;

    /**
     * 标题
     */
    private String title;

    /**
     * 最大高度
     */
    private short maxHeight;

    /**
     * 合并后最后行数
     */
    private int subMergedLastRowNum = 0;

    /**
     * 合并后开始行数
     */
    private int subMergedFirstRowNum = 1;

    /**
     * 对象的子列表方法
     */
    private Method subMethod;

    /**
     * 对象的子列表属性
     */
    private List<Field> subFields;

    /**
     * 统计列表
     */
    private Map<Integer, Double> statistics = new HashMap<Integer, Double>();

    /**
     * 数字格式
     */
    private static final DecimalFormat DOUBLE_FORMAT = new DecimalFormat("######0.00");

    /**
     * 实体对象
     */
    public Class<T> clazz;

    /**
     * 需要排除列属性
     */
    public String[] excludeFields;

    public ExcelUtil(Class<T> clazz) {
        this.clazz = clazz;
    }

    /**
     * 隐藏Excel中列属性
     *
     * @param fields 列属性名 示例[单个"name"/多个"id","name"]
     * @throws Exception
     */
    public void hideColumn(String... fields) {
        this.excludeFields = fields;
    }

    public void init(List<T> list, String sheetName, String title, Type type) {
        if (list == null) {
            list = new ArrayList<T>();
        }
        this.list = list;
        this.sheetName = sheetName;
        this.type = type;
        this.title = title;
        createExcelField();
        createWorkbook();
        createTitle();
        createSubHead();
    }

    public <T, U> void initTwo(List<T> list, List<U> list2, String sheetName, String title, Type type) {
        if (list == null) {
            list = new ArrayList<T>();
        }
        if (list2 == null) {
            list2 = new ArrayList<U>();
        }
        this.sheetName = sheetName;
        this.type = type;
        this.title = title;
        createExcelField();
        createWorkbook();

//        // 第一部分数据
//        if (!list.isEmpty()) {
//            createCustomTitle("已结算");
//            createSubHead();
//            writeSheet(list);
//            rownum += 3; // 空三行
//        }
//
//        // 第二组数据
//        if (!list2.isEmpty()) {
//            createCustomTitle("未结算");
//            createSubHead();
//            writeSheet(list2);
//        }
    }

    private void setCellValue(Cell cell, Object val, HorizontalAlignment align) {
        if (val instanceof String) {
            cell.setCellValue((String) val);
        } else if (val instanceof Number) {
            cell.setCellValue(((Number) val).doubleValue());
        } else if (val instanceof Boolean) {
            cell.setCellValue((Boolean) val);
        } else if (val == null) {
            cell.setCellValue("");
        } else {
            cell.setCellValue(val.toString());
        }

        CellStyle style = cell.getCellStyle();
        if (style == null) {
            style = cell.getSheet().getWorkbook().createCellStyle();
        }

        // 克隆已有样式后再设置对齐方式
        CellStyle newStyle = cell.getSheet().getWorkbook().createCellStyle();
        newStyle.cloneStyleFrom(style);
        newStyle.setAlignment(align);
        cell.setCellStyle(newStyle);
    }

    private Object getCellVal(T object, Field field, Excel excel) {
        try {
            // 设置访问权限为true，以便可以访问私有字段
            field.setAccessible(true);

            Object value = field.get(object);

            if (value == null) {
                return "";
            }

            // 处理特定类型的字段
            if (value instanceof Date) {
                String dateFormat = excel.dateFormat();
                if (StringUtils.isNotEmpty(dateFormat)) {
                    SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
                    return sdf.format(value);
                } else {
                    return value.toString();
                }
            } else if (value instanceof Boolean) {
                return (Boolean) value ? "是" : "否";
            } else if (value instanceof BigDecimal) {
                return DOUBLE_FORMAT.format(((BigDecimal) value).doubleValue());
            } else if (value instanceof Number) {
                return value.toString();
            } else {
                return value.toString();
            }
        } catch (IllegalAccessException e) {
            log.error("无法获取字段值: {}", e.getMessage());
            throw new UtilException(e.getMessage());
        }
    }

    /**
     * 自定义创建标题行（不会重复合并）
     */
    private void createCustomTitle(String subTitle) {
        if (StringUtils.isNotEmpty(subTitle)) {
            int currentRow = rownum;
            boolean isMerged = false;

            // 检查当前行是否已经被合并
            for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
                CellRangeAddress region = sheet.getMergedRegion(i);
                if (region.getFirstRow() == currentRow && region.getLastRow() == currentRow) {
                    isMerged = true;
                    break;
                }
            }

            if (!isMerged) {
                Row titleRow = sheet.createRow(rownum++);
                titleRow.setHeightInPoints(25);

                Cell titleCell = titleRow.createCell(0);
                titleCell.setCellValue(subTitle);
                titleCell.setCellStyle(styles.get("title"));

                int lastColIndex = this.fields.size() - 1;
                sheet.addMergedRegion(new CellRangeAddress(
                        titleRow.getRowNum(), titleRow.getRowNum(), 0, lastColIndex));
            } else {
                rownum++; // 跳过该行，避免写入内容到已合并但为空的行
            }
        }
    }

    public void init(List<T> list, String sheetName, String title, Type type, String printBy) {
        if (list == null) {
            list = new ArrayList<T>();
        }
        this.list = list;
        this.sheetName = sheetName;
        this.type = type;
        this.title = title;
        createExcelField();
        createWorkbook();
        createTitle();
        createPrintedBy(printBy);
        createSubHead();
        if (!"费用明细".equals(sheetName)) {
            clearedBy(list.size());
        }
    }

    public void init(List<T> list, String sheetName, String title, Type type, String printBy, String agent, String startTime, String endTime) {
        if (list == null) {
            list = new ArrayList<T>();
        }
        this.list = list;
        this.sheetName = sheetName;
        this.type = type;
        this.title = title;
        createExcelField();
        createWorkbook();
        createTwoTitle(printBy, agent, startTime, endTime);
        createSubHead();
    }

    /**
     * 创建excel第一行标题
     */
    public void createTitle() {
        if (StringUtils.isNotEmpty(title)) {
            subMergedFirstRowNum++;
            subMergedLastRowNum++;
            int mainFieldCount;
            if (isSubList()){
               mainFieldCount = this.fields.size() - 1;
            }else {
                mainFieldCount = this.fields.size();
            }

            int subFieldCount = isSubList() ? subFields.size() : 0;
            int totalCols = mainFieldCount + subFieldCount - 1;
            // 创建标题行
            Row titleRow = sheet.createRow(rownum == 0 ? rownum++ : 0);
            titleRow.setHeightInPoints(30);

            // 设置主标题
            Cell mainTitleCell = titleRow.createCell(0);
            mainTitleCell.setCellValue(title);
            mainTitleCell.setCellStyle(styles.get("title"));
            sheet.addMergedRegion(new CellRangeAddress(
                    titleRow.getRowNum(), titleRow.getRowNum(),
                    0, mainFieldCount - 1));

            // 如果有子列表，则添加子标题
            if (isSubList()) {
                Cell subTitleCell = titleRow.createCell(mainFieldCount);
                subTitleCell.setCellValue(title + "明细");
                subTitleCell.setCellStyle(styles.get("subTitle"));
                sheet.addMergedRegion(new CellRangeAddress(
                        titleRow.getRowNum(), titleRow.getRowNum(),
                        mainFieldCount, totalCols));
            }
        }
    }

    /**
     * 创建excel第二行打印人信息
     */
    public void createPrintedBy(String printedBy) {
        if (StringUtils.isNotEmpty(printedBy)) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            int printLastCol = this.fields.size() - 1;
            if (isSubList()) {
                printLastCol = printLastCol + subFields.size() - 1;
            }
            Row printRow = sheet.createRow(rownum++);
            printRow.setHeightInPoints(20);
            Cell printCell = printRow.createCell(1);
            printCell.setCellStyle(styles.get("printBy"));
            printCell.setCellValue("打印人: " + printedBy + "   打印时间：" + format.format(new Date()));
            sheet.addMergedRegion(new CellRangeAddress(printRow.getRowNum(), printRow.getRowNum(), 1, printLastCol));
        }
    }

    /**
     * 创建excel第二行打印人信息
     */
    public void createTwoTitle(String printedBy, String agent, String startTime, String endTime) {
        if (StringUtils.isNotEmpty(printedBy)) {
            int printLastCol = this.fields.size() - 1;
            if (isSubList()) {
                printLastCol = printLastCol + subFields.size() - 1;
            }
            Row printRow = sheet.createRow(rownum++);
            printRow.setHeightInPoints(20);
            Cell printCell = printRow.createCell(0);
            printCell.setCellStyle(styles.get("two"));
            if (StringUtils.isNotEmpty(agent)) {
                printCell.setCellValue("代理人名称：" + agent + "  制表人: " + printedBy + "  统计时间 " + startTime + "-" + endTime);
            } else {
                printCell.setCellValue("制表人: " + printedBy + "  统计时间 " + startTime + "-" + endTime);
            }
            sheet.addMergedRegion(new CellRangeAddress(printRow.getRowNum(), printRow.getRowNum(), 0, printLastCol));
        }
    }

    /**
     * 创建excel清库人信息
     */
    public void clearedBy(Integer sheetSize) {
        int printLastCol = this.fields.size() - 1;
        if (isSubList()) {
            printLastCol = printLastCol + subFields.size() - 1;
        }
        Row printRow = sheet.createRow(sheetSize + 3);
        printRow.setHeightInPoints(20);
        Cell printCell = printRow.createCell(1);
        printCell.setCellStyle(styles.get("clearedBy"));
        printCell.setCellValue("清库人: " + " ");
        sheet.addMergedRegion(new CellRangeAddress(printRow.getRowNum(), printRow.getRowNum(), 1, printLastCol));

    }


    /**
     * 创建对象的子列表名称
     */
    public void createSubHead() {
        if (isSubList()) {
            subMergedFirstRowNum++;
            subMergedLastRowNum++;
            Row subRow = sheet.createRow(rownum);
            int excelNum = 0;
            boolean isMerge = false;
            for (Object[] objects : fields) {
                Field field = (Field) objects[0];
                Excel attr = (Excel) objects[1];
                Cell headCell1 = subRow.createCell(excelNum);
                if (Collection.class.isAssignableFrom(field.getType()) && StringUtils.isNotEmpty(attr.name())){
                    isMerge = true;
                    headCell1.setCellValue(attr.name());
                    headCell1.setCellStyle(styles.get(StringUtils.format("header_{}_{}", attr.headerColor(), attr.headerBackgroundColor())));
                }
                excelNum++;
            }
            int headFirstRow = excelNum - 1;
            int headLastRow = headFirstRow + subFields.size() - 1;
            if (headLastRow > headFirstRow && isMerge) {
                sheet.addMergedRegion(new CellRangeAddress(rownum, rownum, headFirstRow, headLastRow));
            }
            rownum++;
        }
    }

    /**
     * 对excel表单默认第一个索引名转换成list
     *
     * @param is 输入流
     * @return 转换后集合
     */
    public List<T> importExcel(InputStream is) {
        List<T> list = null;
        try {
            list = importExcel(is, 0);
        } catch (Exception e) {
            log.error("导入Excel异常{}", e.getMessage());
            throw new UtilException(e.getMessage());
        } finally {
            IOUtils.closeQuietly(is);
        }
        return list;
    }

    /**
     * 对excel表单默认第一个索引名转换成list
     *
     * @param is 输入流
     * @return 转换后集合
     */
    public List<T> importRateExcel(InputStream is, Integer titleNum) {
        List<T> list = null;
        try {
            list = importExcel(is, titleNum);
        } catch (Exception e) {
            log.error("导入Excel异常{}", e.getMessage());
            throw new UtilException(e.getMessage());
        } finally {
            IOUtils.closeQuietly(is);
        }
        return list;
    }

    /**
     * 对excel表单默认第一个索引名转换成list
     *
     * @param is       输入流
     * @param titleNum 标题占用行数
     * @return 转换后集合
     */
    public List<T> importExcel(InputStream is, int titleNum) throws Exception {
        return importExcel(StringUtils.EMPTY, is, titleNum);
    }

    /**
     * 对excel表单指定表格索引名转换成list
     *
     * @param sheetName 表格索引名
     * @param titleNum  标题占用行数
     * @param is        输入流
     * @return 转换后集合
     */
    public List<T> importExcel(String sheetName, InputStream is, int titleNum) throws Exception {
        this.type = Type.IMPORT;
        this.wb = WorkbookFactory.create(is);
        List<T> list = new ArrayList<T>();
        // 如果指定sheet名,则取指定sheet中的内容 否则默认指向第1个sheet
        Sheet sheet = StringUtils.isNotEmpty(sheetName) ? wb.getSheet(sheetName) : wb.getSheetAt(0);
        if (sheet == null) {
            throw new IOException("文件sheet不存在");
        }
        boolean isXSSFWorkbook = !(wb instanceof HSSFWorkbook);
        Map<String, PictureData> pictures;
        if (isXSSFWorkbook) {
            pictures = getSheetPictures07((XSSFSheet) sheet, (XSSFWorkbook) wb);
        } else {
            pictures = getSheetPictures03((HSSFSheet) sheet, (HSSFWorkbook) wb);
        }
        // 获取最后一个非空行的行下标，比如总行数为n，则返回的为n-1
        int rows = sheet.getLastRowNum();
        if (rows > 0) {
            // 定义一个map用于存放excel列的序号和field.
            Map<String, Integer> cellMap = new HashMap<String, Integer>();
            // 获取表头
            Row heard = sheet.getRow(titleNum);
            for (int i = 0; i < heard.getPhysicalNumberOfCells(); i++) {
                Cell cell = heard.getCell(i);
                if (StringUtils.isNotNull(cell)) {
                    String value = this.getCellValue(heard, i).toString();
                    cellMap.put(value, i);
                } else {
                    cellMap.put(null, i);
                }
            }
            // 有数据时才处理 得到类的所有field.
            List<Object[]> fields = this.getFields();
            Map<Integer, Object[]> fieldsMap = new HashMap<Integer, Object[]>();
            for (Object[] objects : fields) {
                Excel attr = (Excel) objects[1];
                Integer column = cellMap.get(attr.name());
                if (column != null) {
                    fieldsMap.put(column, objects);
                }
            }
            for (int i = titleNum + 1; i <= rows; i++) {
                // 从第2行开始取数据,默认第一行是表头.
                Row row = sheet.getRow(i);
                // 判断当前行是否是空行
                if (isRowEmpty(row)) {
                    continue;
                }
                T entity = null;
                for (Map.Entry<Integer, Object[]> entry : fieldsMap.entrySet()) {
                    Object val = this.getCellValue(row, entry.getKey());

                    // 如果不存在实例则新建.
                    entity = (entity == null ? clazz.newInstance() : entity);
                    // 从map中得到对应列的field.
                    Field field = (Field) entry.getValue()[0];
                    Excel attr = (Excel) entry.getValue()[1];
                    // 取得类型,并根据对象类型设置值.
                    Class<?> fieldType = field.getType();
                    if (String.class == fieldType) {
                        String s = Convert.toStr(val);
                        if (StringUtils.endsWith(s, ".0")) {
                            val = StringUtils.substringBefore(s, ".0");
                        } else {
                            String dateFormat = field.getAnnotation(Excel.class).dateFormat();
                            if (StringUtils.isNotEmpty(dateFormat)) {
                                val = parseDateToStr(dateFormat, val);
                            } else {
                                val = Convert.toStr(val);
                            }
                        }
                    } else if ((Integer.TYPE == fieldType || Integer.class == fieldType) && StringUtils.isNumeric(Convert.toStr(val))) {
                        val = Convert.toInt(val);
                    } else if ((Long.TYPE == fieldType || Long.class == fieldType) && StringUtils.isNumeric(Convert.toStr(val))) {
                        val = Convert.toLong(val);
                    } else if (Double.TYPE == fieldType || Double.class == fieldType) {
                        val = Convert.toDouble(val);
                    } else if (Float.TYPE == fieldType || Float.class == fieldType) {
                        val = Convert.toFloat(val);
                    } else if (BigDecimal.class == fieldType) {
                        val = Convert.toBigDecimal(val);
                    } else if (Date.class == fieldType) {
                        if (val instanceof String) {
                            val = DateUtils.parseDate(val);
                        } else if (val instanceof Double) {
                            val = DateUtil.getJavaDate((Double) val);
                        }
                    } else if (Boolean.TYPE == fieldType || Boolean.class == fieldType) {
                        val = Convert.toBool(val, false);
                    }
                    if (StringUtils.isNotNull(fieldType)) {
                        String propertyName = field.getName();
                        if (StringUtils.isNotEmpty(attr.targetAttr())) {
                            propertyName = field.getName() + "." + attr.targetAttr();
                        }
                        if (StringUtils.isNotEmpty(attr.readConverterExp())) {
                            val = reverseByExp(Convert.toStr(val), attr.readConverterExp(), attr.separator());
                        } else if (StringUtils.isNotEmpty(attr.dictType())) {
                            val = reverseDictByExp(Convert.toStr(val), attr.dictType(), attr.separator());
                        } else if (!attr.handler().equals(ExcelHandlerAdapter.class)) {
                            val = dataFormatHandlerAdapter(val, attr, null);
                        } else if (ColumnType.IMAGE == attr.cellType() && StringUtils.isNotEmpty(pictures)) {
                            PictureData image = pictures.get(row.getRowNum() + "_" + entry.getKey());
                            if (image == null) {
                                val = "";
                            } else {
                                byte[] data = image.getData();
                                val = FileUtils.writeImportBytes(data);
                            }
                        }
                        ReflectUtils.invokeSetter(entity, propertyName, val);
                    }
                }
                list.add(entity);
            }
        }
        return list;
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param list      导出数据集合
     * @param sheetName 工作表的名称
     * @return 结果
     */
    public AjaxResult exportExcel(List<T> list, String sheetName) {
        return exportExcel(list, sheetName, StringUtils.EMPTY);
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param list      导出数据集合
     * @param sheetName 工作表的名称
     * @param title     标题
     * @return 结果
     */
    public AjaxResult exportExcel(List<T> list, String sheetName, String title) {
        this.init(list, sheetName, title, Type.EXPORT);
        return exportExcel();
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param response  返回数据
     * @param list      导出数据集合
     * @param sheetName 工作表的名称
     * @return 结果
     */
    public void exportExcel(HttpServletResponse response, List<T> list, String sheetName) {
        exportExcel(response, list, sheetName, StringUtils.EMPTY);
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param response  返回数据
     * @param list      导出数据集合
     * @param sheetName 工作表的名称
     * @param title     标题
     * @return 结果
     */
    public void exportExcel(HttpServletResponse response, List<T> list, String sheetName, String title) {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        this.init(list, sheetName, title, Type.EXPORT);
        exportExcel(response);
    }

    /**
     * 邮件单账单导出
     *
     * @param response  返回数据
     * @param list      导出数据集合
     * @param sheetName 工作表的名称
     * @param title     标题
     * @return 结果
     */
    public void exportAWBMExcel(HttpServletResponse response, List<T> list, String sheetName, String title) {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        this.initAWBMExport(list, sheetName, title, Type.EXPORT);
        exportExcel(response);
    }

    private void initAWBMExport(List<T> list, String sheetName, String title, Type type) {
        if (list == null) {
            list = new ArrayList<T>();
        }
        this.list = list;
        this.sheetName = sheetName;
        this.type = type;
        this.title = title;
        createExcelField();
        createWorkbook();
        createTitleAWBM();
        createSubHead();
    }

    private void createTitleAWBM() {
        if (StringUtils.isNotEmpty(title)) {
            subMergedFirstRowNum++;
            subMergedLastRowNum++;
            int mainFieldCount;
            if (isSubList()){
                mainFieldCount = this.fields.size() - 1;
            }else {
                mainFieldCount = this.fields.size();
            }

            int subFieldCount = isSubList() ? subFields.size() : 0;
            int totalCols = mainFieldCount + subFieldCount - 1;
            // 创建标题行
            Row titleRow = sheet.createRow(rownum == 0 ? rownum++ : 0);
            titleRow.setHeightInPoints(30);

            // 设置主标题
            Cell mainTitleCell = titleRow.createCell(0);
            mainTitleCell.setCellValue(title);
            mainTitleCell.setCellStyle(styles.get("title"));
            sheet.addMergedRegion(new CellRangeAddress(
                    titleRow.getRowNum(), titleRow.getRowNum(),
                    0, totalCols));

            // 如果有子列表，则添加子标题
//            if (isSubList()) {
//                Cell subTitleCell = titleRow.createCell(mainFieldCount);
//                subTitleCell.setCellValue(title + "明细");
//                subTitleCell.setCellStyle(styles.get("subTitle"));
//                sheet.addMergedRegion(new CellRangeAddress(
//                        titleRow.getRowNum(), titleRow.getRowNum(),
//                        mainFieldCount, totalCols));
//            }
        }
    }

    public void exportExcel(HttpServletResponse response, List<T> list, String sheetName, String title, String printBy) {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        this.init(list, sheetName, title, Type.EXPORT, printBy);
        exportExcel(response);
    }

    public void exportExcel(HttpServletResponse response, List<T> list, String sheetName, String printBy, String agent, String startTime, String endTime) {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        this.init(list, sheetName, title, Type.EXPORT, printBy, agent, startTime, endTime);
        exportExcel(response);
    }

    public void exportExcelArrCost(HttpServletResponse response, List<T> list, String sheetName, String title) {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        this.init(list, sheetName, title, Type.EXPORT);
        exportExcelArrCost(response);
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param sheetName 工作表的名称
     * @return 结果
     */
    public AjaxResult importTemplateExcel(String sheetName) {
        return importTemplateExcel(sheetName, StringUtils.EMPTY);
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param sheetName 工作表的名称
     * @param title     标题
     * @return 结果
     */
    public AjaxResult importTemplateExcel(String sheetName, String title) {
        this.init(null, sheetName, title, Type.IMPORT);
        return exportExcel();
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param sheetName 工作表的名称
     * @return 结果
     */
    public void importTemplateExcel(HttpServletResponse response, String sheetName) {
        importTemplateExcel(response, sheetName, StringUtils.EMPTY);
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param sheetName 工作表的名称
     * @param title     标题
     * @return 结果
     */
    public void importTemplateExcel(HttpServletResponse response, String sheetName, String title) {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        this.init(null, sheetName, title, Type.IMPORT);
        exportExcel(response);
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @return 结果
     */
    public void exportExcel(HttpServletResponse response) {
        try {
            writeSheet();
            wb.write(response.getOutputStream());
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(wb);
        }
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单 -> 进港费用管理导出
     *
     * @return 结果
     */
    public void exportExcelArrCost(HttpServletResponse response) {
        try {
            writeSheetArrCost();
            wb.write(response.getOutputStream());
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(wb);
        }
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @return 结果
     */
    public AjaxResult exportExcel() {
        OutputStream out = null;
        try {
            writeSheet();
            String filename = encodingFilename(sheetName);
            out = new FileOutputStream(getAbsoluteFile(filename));
            wb.write(out);
            return AjaxResult.success(filename);
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
            throw new UtilException("导出Excel失败，请联系网站管理员！");
        } finally {
            IOUtils.closeQuietly(wb);
            IOUtils.closeQuietly(out);
        }
    }

    /**
     * 创建写入数据到Sheet
     */
    public void writeSheet() {
        // 取出一共有多少个sheet.
        int sheetNo = Math.max(1, (int) Math.ceil(list.size() * 1.0 / sheetSize));
        for (int index = 0; index < sheetNo; index++) {
            createSheet(sheetNo, index);

            // 产生一行
            Row row = sheet.createRow(rownum);
            int column = 0;
            // 写入各个字段的列头名称
            for (Object[] os : fields) {
                Field field = (Field) os[0];
                Excel excel = (Excel) os[1];
                if (Collection.class.isAssignableFrom(field.getType())) {
                    for (Field subField : subFields) {
                        Excel subExcel = subField.getAnnotation(Excel.class);
                        this.createHeadCell(subExcel, row, column++);
                    }
                } else {
                    this.createHeadCell(excel, row, column++);
                }
            }
            if (Type.EXPORT.equals(type)) {
                fillExcelData(index, row);
                addStatisticsRow();
            }
        }
    }


    /**
     * 创建写入数据到Sheet -> 进港费用导出
     */
    public void writeSheetArrCost() {
        // 取出一共有多少个sheet.
        int sheetNo = Math.max(1, (int) Math.ceil(list.size() * 1.0 / sheetSize));
        for (int index = 0; index < sheetNo; index++) {
            createSheet(sheetNo, index);

            // 产生一行
            Row row = sheet.createRow(rownum);
            int column = 0;
            // 写入各个字段的列头名称
            for (Object[] os : fields) {
                Field field = (Field) os[0];
                Excel excel = (Excel) os[1];
                if (Collection.class.isAssignableFrom(field.getType())) {
                    for (Field subField : subFields) {
                        Excel subExcel = subField.getAnnotation(Excel.class);
                        this.createHeadCell(subExcel, row, column++);
                    }
                } else {
                    this.createHeadCell(excel, row, column++);
                }
            }
            if (Type.EXPORT.equals(type)) {
                fillExcelDataArrCost(index, row);
                addStatisticsRow();
            }
        }
    }

    /**
     * 将指定的数据写入当前 Sheet
     *
     * @param dataList 要写入的数据列表
     */
    public <T> void writeSheet(List<T> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        // 分页写入
        int sheetNo = Math.max(1, (int) Math.ceil(dataList.size() * 1.0 / sheetSize));

        for (int index = 0; index < sheetNo; index++) {
            createSheet(sheetNo, index);

            // 写表头
            Row headerRow = sheet.createRow(rownum);
            int column = 0;

            for (Object[] os : fields) {
                Field field = (Field) os[0];
                Excel excel = (Excel) os[1];

                if (Collection.class.isAssignableFrom(field.getType())) {
                    for (Field subField : subFields) {
                        Excel subExcel = subField.getAnnotation(Excel.class);
                        this.createHeadCell(subExcel, headerRow, column++);
                    }
                } else {
                    this.createHeadCell(excel, headerRow, column++);
                }
            }

            // 填充数据
            if (Type.EXPORT.equals(type)) {
                fillExcelData(index, headerRow, dataList);
                addStatisticsRow();
            }

            rownum++; // 表头后移一行
        }
    }

    public <U> void writeSheet2(List<U> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        // 分页写入
        int sheetNo = Math.max(1, (int) Math.ceil(dataList.size() * 1.0 / sheetSize));

        for (int index = 0; index < sheetNo; index++) {
            createSheet(sheetNo, index);

            // 写表头
            Row headerRow = sheet.createRow(rownum);
            int column = 0;

            for (Object[] os : fields) {
                Field field = (Field) os[0];
                Excel excel = (Excel) os[1];

                if (Collection.class.isAssignableFrom(field.getType())) {
                    for (Field subField : subFields) {
                        Excel subExcel = subField.getAnnotation(Excel.class);
                        this.createHeadCell(subExcel, headerRow, column++);
                    }
                } else {
                    this.createHeadCell(excel, headerRow, column++);
                }
            }

            // 填充数据
            if (Type.EXPORT.equals(type)) {
                fillExcelData2(index, headerRow, dataList);
                addStatisticsRow();
            }

            rownum++; // 表头后移一行
        }
    }

    protected <T> void fillExcelData(int pageIndex, Row headerRow, List<T> dataList) {
        int start = pageIndex * sheetSize;
        int end = Math.min(start + sheetSize, dataList.size());

        for (int i = start; i < end; i++) {
            T t = dataList.get(i);
            rownum++;
            Row row = sheet.createRow(rownum);

            int column = 0;
            for (Object[] os : fields) {
                Field field = (Field) os[0];
                Excel excel = (Excel) os[1];

                field.setAccessible(true);
                try {
                    Object val = field.get(t);
                    Cell cell = row.createCell(column++);
                    setCellValue(cell, val, excel.align());
                } catch (IllegalAccessException e) {
                    throw new RuntimeException("Excel反射写入失败", e);
                }
            }
        }
    }

    protected <U> void fillExcelData2(int pageIndex, Row headerRow, List<U> dataList) {
        int start = pageIndex * sheetSize;
        int end = Math.min(start + sheetSize, dataList.size());

        for (int i = start; i < end; i++) {
            U t = dataList.get(i);
            rownum++;
            Row row = sheet.createRow(rownum);

            int column = 0;
            for (Object[] os : fields) {
                Field field = (Field) os[0];
                Excel excel = (Excel) os[1];

                field.setAccessible(true);
                try {
                    Object val = field.get(t);
                    Cell cell = row.createCell(column++);
                    setCellValue(cell, val, excel.align());
                } catch (IllegalAccessException e) {
                    throw new RuntimeException("Excel反射写入失败", e);
                }
            }
        }
    }

    /**
     * 填充excel数据
     *
     * @param index 序号
     * @param row   单元格行
     */
    @SuppressWarnings("unchecked")
    public void fillExcelData(int index, Row row) {
        int startNo = index * sheetSize;
        int endNo = Math.min(startNo + sheetSize, list.size());
        int rowNo = (1 + rownum) - startNo;
        for (int i = startNo; i < endNo; i++) {
            rowNo = isSubList() ? (i > 1 ? rowNo + 1 : rowNo + i) : i + 1 + rownum - startNo;
            row = sheet.createRow(rowNo);
            // 得到导出对象.
            T vo = (T) list.get(i);
            Collection<?> subList = null;
            if (isSubList()) {
                if (isSubListValue(vo)) {
                    subList = getListCellValue(vo);
                    subMergedLastRowNum = subMergedLastRowNum + subList.size();
                } else {
                    subMergedFirstRowNum++;
                    subMergedLastRowNum++;
                }
            }
            int column = 0;
            for (Object[] os : fields) {
                Field field = (Field) os[0];
                Excel excel = (Excel) os[1];
                if (Collection.class.isAssignableFrom(field.getType()) && StringUtils.isNotNull(subList)) {
                    boolean subFirst = false;
                    int subStartNum = row.getRowNum();
                    for (Object obj : subList) {
                        if (subFirst) {
                            rowNo++;
                            row = sheet.createRow(rowNo);
                        }
                        List<Field> subFields = FieldUtils.getFieldsListWithAnnotation(obj.getClass(), Excel.class);
                        int subIndex = 0;
                        for (Field subField : subFields) {
                            if (subField.isAnnotationPresent(Excel.class)) {
                                subField.setAccessible(true);
                                Excel attr = subField.getAnnotation(Excel.class);
                                this.addCell(attr, row, (T) obj, subField, column + subIndex);
                            }
                            subIndex++;
                        }
                        subFirst = true;
                    }
                    //合并父对象的单元格
                    if (subStartNum != row.getRowNum()) {
                        int size = fields.size();
                        for (int i1 = 0; i1 < size - 1; i1++) {
                            // 起始行 结束行  起始列  结束列
                            CellRangeAddress region = new CellRangeAddress(subStartNum, row.getRowNum(), i1, i1);
                            boolean overlap = false;
                            for (int x = 0; x < sheet.getNumMergedRegions(); x++) {
                                CellRangeAddress existing = sheet.getMergedRegion(x);
                                if (isOverlapping(existing, region)) {
                                    overlap = true;
                                    break;
                                }
                            }

                            if (!overlap) {
                                sheet.addMergedRegion(region);
                            }
                        }
                    }
                    this.subMergedFirstRowNum = this.subMergedFirstRowNum + subList.size();
                } else {
                    this.addCell(excel, row, vo, field, column++);
                }
            }
        }
    }

    /**
     * 判断两个合并区域是否有重叠
     */
    public static boolean isOverlapping(CellRangeAddress region1, CellRangeAddress region2) {
        int r1Top = region1.getFirstRow();
        int r1Bottom = region1.getLastRow();
        int c1Left = region1.getFirstColumn();
        int c1Right = region1.getLastColumn();

        int r2Top = region2.getFirstRow();
        int r2Bottom = region2.getLastRow();
        int c2Left = region2.getFirstColumn();
        int c2Right = region2.getLastColumn();

        // 判断两个矩形是否相交（合并单元格是矩形）
        return !(
                r1Bottom < r2Top ||   // region1 在 region2 上方
                        r1Top > r2Bottom ||   // region1 在 region2 下方
                        c1Right < c2Left ||   // region1 在 region2 左边
                        c1Left > c2Right      // region1 在 region2 右边
        );
    }

    /**
     * 填充excel数据 进港费用管理用
     *
     * @param index 序号
     * @param row   单元格行
     */
    public void fillExcelDataArrCost(int index, Row row) {
        final Set<Integer> mergeColumns = new HashSet<>(Arrays.asList(1, 4, 5, 9, 10, 15));
        int headerRowOffset = 2;
        int actualRowNo = 0;

        int startNo = index * sheetSize;
        int endNo = Math.min(startNo + sheetSize, list.size());
        if (startNo >= list.size()) {
            return;
        }

        Object lastGroupKey = null;
        int groupStartRow = headerRowOffset + 1;

        Map<Integer, Object> groupPrevValues = new HashMap<>();
        Map<Integer, Integer> groupStartRows = new HashMap<>();
        for (Integer col : mergeColumns) {
            groupPrevValues.put(col, null);
            groupStartRows.put(col, groupStartRow);
        }

        for (int i = startNo; i < endNo; i++) {
            actualRowNo = headerRowOffset + (i - startNo);
            row = sheet.createRow(actualRowNo);

            @SuppressWarnings("unchecked")
            T vo = (T) list.get(i);

            int column = 0;
            Object currentGroupKey = null;

            for (Object[] os : fields) {
                Field field = (Field) os[0];
                Excel excel = (Excel) os[1];
                int currentColumn = column;

                this.addCellArrCost(excel, row, vo, field, currentColumn);
                Cell cell = row.getCell(currentColumn);

                Object currentValue = null;
                if (cell != null) {
                    switch (cell.getCellType()) {
                        case STRING: currentValue = cell.getStringCellValue(); break;
                        case NUMERIC: currentValue = cell.getNumericCellValue(); break;
                        case BOOLEAN: currentValue = cell.getBooleanCellValue(); break;
                        default: currentValue = null;
                    }
                }

                if (mergeColumns.contains(currentColumn)) {
                    // 第0列用于分组
                    if (currentColumn == 1) {
                        currentGroupKey = currentValue;
                        boolean groupChanged = lastGroupKey == null || !lastGroupKey.equals(currentGroupKey);

                        if (groupChanged) {
                            // 合并上一组的第0列
                            if (actualRowNo - groupStartRow > 1) {
                                sheet.addMergedRegion(new CellRangeAddress(groupStartRow, actualRowNo - 1, 1, 1));
                            }

                            // 合并其他列
                            for (int col : mergeColumns) {
                                if (col == 0) continue;
                                Object prevVal = groupPrevValues.get(col);
                                int startRow = groupStartRows.get(col);
                                if (prevVal != null && actualRowNo - startRow > 1) {
                                    CellRangeAddress region = new CellRangeAddress(startRow, actualRowNo - 1, col, col);
                                    sheet.addMergedRegion(region);
                                    clearMergedRegionContent(sheet, region);
                                }
                                // 重置起始值
                                groupPrevValues.put(col, null);
                                groupStartRows.put(col, actualRowNo);
                            }

                            // 更新新组状态
                            groupStartRow = actualRowNo;
                            lastGroupKey = currentGroupKey;
                            groupStartRows.put(0, actualRowNo);
                        }

                        groupPrevValues.put(0, currentValue);
                    } else {
                        // 处理非第0列合并（组内）
                        Object prevVal = groupPrevValues.get(currentColumn);
                        int startRow = groupStartRows.get(currentColumn);

                        if (prevVal == null || !prevVal.equals(currentValue)) {
                            if (prevVal != null && actualRowNo - startRow > 1) {
                                CellRangeAddress region = new CellRangeAddress(startRow, actualRowNo - 1, currentColumn, currentColumn);
                                sheet.addMergedRegion(region);
                                clearMergedRegionContent(sheet, region);
                            }
                            groupStartRows.put(currentColumn, actualRowNo);
                        }

                        groupPrevValues.put(currentColumn, currentValue);
                    }
                }

                column++;
            }
        }

        // 收尾处理最后一组的合并
//        if (actualRowNo >= groupStartRow + 1) {
//            int startRow = groupStartRow;
//            int endRow = actualRowNo - 1;
//            if (startRow < endRow) {
//                CellRangeAddress region = new CellRangeAddress(startRow, endRow, 1, 1);
//                sheet.addMergedRegion(region);
//                clearMergedRegionContent(sheet, region);
//            }
//            for (int col : mergeColumns) {
//                if (col == 1) continue;
//                Object prevVal = groupPrevValues.get(col);
//                int startRowForCol = groupStartRows.get(col);
//                if (prevVal != null && actualRowNo - startRowForCol >= 1) {
//                    int endRowForCol = actualRowNo;
//                    if (startRowForCol < endRowForCol) {
//                        CellRangeAddress region2 = new CellRangeAddress(startRowForCol, endRowForCol, col, col);
//                        sheet.addMergedRegion(region2);
//                        clearMergedRegionContent(sheet, region2);
//                    }
//                }
//            }
//        }
        if (actualRowNo > groupStartRow) {
            // 合并第一列（列索引1）
            CellRangeAddress region = new CellRangeAddress(groupStartRow, actualRowNo, 1, 1);
            sheet.addMergedRegion(region);
            clearMergedRegionContent(sheet, region);

            // 合并其他列
            for (int col : mergeColumns) {
                if (col == 1) continue;  // 第一列已处理过
                Object prevVal = groupPrevValues.get(col);
                int startRowForCol = groupStartRows.get(col);
                if (prevVal != null && actualRowNo >= startRowForCol) {
                    CellRangeAddress region2 = new CellRangeAddress(startRowForCol, actualRowNo, col, col);
                    sheet.addMergedRegion(region2);
                    clearMergedRegionContent(sheet, region2);
                }
            }
        }

    }

    private void clearMergedRegionContent(Sheet sheet, CellRangeAddress region) {
        int firstRow = region.getFirstRow();
        int lastRow = region.getLastRow();
        int firstCol = region.getFirstColumn();
        int lastCol = region.getLastColumn();

        // 只处理单列合并
        if (firstCol == lastCol) {
            // 从合并区域的第二行开始清除（保留首行）
            for (int r = firstRow + 1; r <= lastRow; r++) {
                Row rowInRegion = sheet.getRow(r);
                if (rowInRegion == null) continue;

                Cell cell = rowInRegion.getCell(firstCol);
                if (cell == null) continue;
                CellStyle style = cell.getCellStyle();
                cell.setBlank();
                cell.setCellStyle(style);
            }
        }
    }



    /**
     * 创建表格样式
     *
     * @param wb 工作薄对象
     * @return 样式列表
     */
    private Map<String, CellStyle> createStyles(Workbook wb) {
        // 写入各条记录,每条记录对应excel表中的一行
        Map<String, CellStyle> styles = new HashMap<String, CellStyle>();
        CellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font titleFont = wb.createFont();
        titleFont.setFontName("Arial");
        titleFont.setFontHeightInPoints((short) 16);
        titleFont.setBold(true);
        style.setFont(titleFont);
        styles.put("title", style);

        // 子标题样式
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font subTitleFont = wb.createFont();
        subTitleFont.setFontHeightInPoints((short) 12);
        style.setFont(subTitleFont);
        styles.put("subTitle",style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font printByFont = wb.createFont();
        printByFont.setFontHeightInPoints((short) 12);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setFont(printByFont);
        styles.put("printBy", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font clearedByFont = wb.createFont();
        clearedByFont.setFontHeightInPoints((short) 12);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setFont(clearedByFont);
        styles.put("clearedBy", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font twoByFont = wb.createFont();
        twoByFont.setFontHeightInPoints((short) 12);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setFont(twoByFont);
        styles.put("two", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        Font dataFont = wb.createFont();
        dataFont.setFontName("Arial");
        dataFont.setFontHeightInPoints((short) 10);
        style.setFont(dataFont);
        styles.put("data", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font totalFont = wb.createFont();
        totalFont.setFontName("Arial");
        totalFont.setFontHeightInPoints((short) 10);
        style.setFont(totalFont);
        styles.put("total", style);

        styles.putAll(annotationHeaderStyles(wb, styles));

        styles.putAll(annotationDataStyles(wb));

        return styles;
    }

    /**
     * 根据Excel注解创建表格头样式
     *
     * @param wb 工作薄对象
     * @return 自定义样式列表
     */
    private Map<String, CellStyle> annotationHeaderStyles(Workbook wb, Map<String, CellStyle> styles) {
        Map<String, CellStyle> headerStyles = new HashMap<String, CellStyle>();
        for (Object[] os : fields) {
            Excel excel = (Excel) os[1];
            String key = StringUtils.format("header_{}_{}", excel.headerColor(), excel.headerBackgroundColor());
            if (!headerStyles.containsKey(key)) {
                CellStyle style = wb.createCellStyle();
                style.cloneStyleFrom(styles.get("data"));
                style.setAlignment(HorizontalAlignment.CENTER);
                style.setVerticalAlignment(VerticalAlignment.CENTER);
                style.setFillForegroundColor(excel.headerBackgroundColor().index);
                style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                Font headerFont = wb.createFont();
                headerFont.setFontName("Arial");
                headerFont.setFontHeightInPoints((short) 10);
                headerFont.setBold(true);
                headerFont.setColor(excel.headerColor().index);
                style.setFont(headerFont);
                headerStyles.put(key, style);
            }
        }
        return headerStyles;
    }

    /**
     * 根据Excel注解创建表格列样式
     *
     * @param wb 工作薄对象
     * @return 自定义样式列表
     */
    private Map<String, CellStyle> annotationDataStyles(Workbook wb) {
        Map<String, CellStyle> styles = new HashMap<String, CellStyle>();
        for (Object[] os : fields) {
            Excel excel = (Excel) os[1];
            String key = StringUtils.format("data_{}_{}_{}", excel.align(), excel.color(), excel.backgroundColor());
            if (!styles.containsKey(key)) {
                CellStyle style = wb.createCellStyle();
                style.setAlignment(excel.align());
                style.setVerticalAlignment(VerticalAlignment.CENTER);
                style.setBorderRight(BorderStyle.THIN);
                style.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
                style.setBorderLeft(BorderStyle.THIN);
                style.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
                style.setBorderTop(BorderStyle.THIN);
                style.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
                style.setBorderBottom(BorderStyle.THIN);
                style.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
                style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                style.setFillForegroundColor(excel.backgroundColor().getIndex());
                Font dataFont = wb.createFont();
                dataFont.setFontName("Arial");
                dataFont.setFontHeightInPoints((short) 10);
                dataFont.setColor(excel.color().index);
                style.setFont(dataFont);
                styles.put(key, style);
            }
        }
        return styles;
    }

    /**
     * 创建单元格（支持合并标题）
     */
    public Cell createHeadCell(Excel attr, Row row, int column) {
        // 如果需要合并
        if (isSubList() && attr.needMerge()) {
            // 1. 先添加合并区域（合并上一行和当前行）
            CellRangeAddress region = new CellRangeAddress(rownum - 1, rownum, column, column);
            sheet.addMergedRegion(region);

            // 2. 在合并区域的第一行第一列创建单元格并设置值
            Row headerRow = sheet.getRow(rownum - 1); // 上一行是标题行
            if (headerRow == null) {
                headerRow = sheet.createRow(rownum - 1);
            }

            Cell cell = headerRow.createCell(column);
            cell.setCellValue(attr.name());

            // 3. 设置样式
            cell.setCellStyle(styles.get(StringUtils.format("header_{}_{}", attr.headerColor(), attr.headerBackgroundColor())));

            // 4. 设置默认列样式（可选）
            sheet.setDefaultColumnStyle(column, styles.get(StringUtils.format("data_{}_{}_{}", attr.align(), attr.color(), attr.backgroundColor())));

            return cell;
        } else {
            // 不合并的情况，按原逻辑处理
            Cell cell = row.createCell(column);
            cell.setCellValue(attr.name());
            setDataValidation(attr, row, column);
            cell.setCellStyle(styles.get(StringUtils.format("header_{}_{}", attr.headerColor(), attr.headerBackgroundColor())));
            sheet.setDefaultColumnStyle(column, styles.get(StringUtils.format("data_{}_{}_{}", attr.align(), attr.color(), attr.backgroundColor())));
            return cell;
        }
    }

    /**
     * 设置单元格信息
     *
     * @param value 单元格值
     * @param attr  注解相关
     * @param cell  单元格信息
     */
    public void setCellVo(Object value, Excel attr, Cell cell) {
        if (ColumnType.STRING == attr.cellType()) {
            String cellValue = Convert.toStr(value);
            // 对于任何以表达式触发字符 =-+@开头的单元格，直接使用tab字符作为前缀，防止CSV注入。
            if (StringUtils.startsWithAny(cellValue, FORMULA_STR)) {
                cellValue = RegExUtils.replaceFirst(cellValue, FORMULA_REGEX_STR, "\t$0");
            }
            if (value instanceof Collection && StringUtils.equals("[]", cellValue)) {
                cellValue = StringUtils.EMPTY;
            }
            cell.setCellValue(StringUtils.isNull(cellValue) ? attr.defaultValue() : cellValue + attr.suffix());
        } else if (ColumnType.NUMERIC == attr.cellType()) {
            if (StringUtils.isNotNull(value)) {
                cell.setCellValue(StringUtils.contains(Convert.toStr(value), ".") ? Convert.toDouble(value) : Convert.toInt(value));
            }
        } else if (ColumnType.IMAGE == attr.cellType()) {
            ClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0, (short) cell.getColumnIndex(), cell.getRow().getRowNum(), (short) (cell.getColumnIndex() + 1), cell.getRow().getRowNum() + 1);
            String imagePath = Convert.toStr(value);
            if (StringUtils.isNotEmpty(imagePath)) {
                byte[] data = ImageUtils.getImage(imagePath);
                getDrawingPatriarch(cell.getSheet()).createPicture(anchor,
                        cell.getSheet().getWorkbook().addPicture(data, getImageType(data)));
            }
        }
    }

    /**
     * 获取画布
     */
    public static Drawing<?> getDrawingPatriarch(Sheet sheet) {
        if (sheet.getDrawingPatriarch() == null) {
            sheet.createDrawingPatriarch();
        }
        return sheet.getDrawingPatriarch();
    }

    /**
     * 获取图片类型,设置图片插入类型
     */
    public int getImageType(byte[] value) {
        String type = FileTypeUtils.getFileExtendName(value);
        if ("JPG".equalsIgnoreCase(type)) {
            return Workbook.PICTURE_TYPE_JPEG;
        } else if ("PNG".equalsIgnoreCase(type)) {
            return Workbook.PICTURE_TYPE_PNG;
        }
        return Workbook.PICTURE_TYPE_JPEG;
    }

    /**
     * 创建表格样式
     */
    public void setDataValidation(Excel attr, Row row, int column) {
        if (attr.name().indexOf("注：") >= 0) {
            sheet.setColumnWidth(column, 6000);
        } else {
            // 设置列宽
            sheet.setColumnWidth(column, (int) ((attr.width() + 0.72) * 256));
        }
        if (StringUtils.isNotEmpty(attr.prompt()) || attr.combo().length > 0) {
            if (attr.combo().length > 15 || StringUtils.join(attr.combo()).length() > 255) {
                // 如果下拉数大于15或字符串长度大于255，则使用一个新sheet存储，避免生成的模板下拉值获取不到
                setXSSFValidationWithHidden(sheet, attr.combo(), attr.prompt(), 1, 100, column, column);
            } else {
                // 提示信息或只能选择不能输入的列内容.
                setPromptOrValidation(sheet, attr.combo(), attr.prompt(), 1, 100, column, column);
            }
        }
    }

    /**
     * 添加单元格
     */
    public Cell addCell(Excel attr, Row row, T vo, Field field, int column) {
        Cell cell = null;
        try {
            // 设置行高
            row.setHeight(maxHeight);
            // 根据Excel中设置情况决定是否导出,有些情况需要保持为空,希望用户填写这一列.
            if (attr.isExport()) {
                // 创建cell
                cell = row.createCell(column);
                if (isSubListValue(vo) && getListCellValue(vo).size() > 1 && attr.needMerge()) {
                    CellRangeAddress cellAddress = new CellRangeAddress(subMergedFirstRowNum, subMergedLastRowNum, column, column);
                    sheet.addMergedRegion(cellAddress);
                }
                cell.setCellStyle(styles.get(StringUtils.format("data_{}_{}_{}", attr.align(), attr.color(), attr.backgroundColor())));

                // 用于读取对象中的属性
                Object value = getTargetValue(vo, field, attr);
                String dateFormat = attr.dateFormat();
                String readConverterExp = attr.readConverterExp();
                String separator = attr.separator();
                String dictType = attr.dictType();
                if (StringUtils.isNotEmpty(dateFormat) && StringUtils.isNotNull(value)) {
                    cell.setCellValue(parseDateToStr(dateFormat, value));
                } else if (StringUtils.isNotEmpty(readConverterExp) && StringUtils.isNotNull(value)) {
                    cell.setCellValue(convertByExp(Convert.toStr(value), readConverterExp, separator));
                } else if (StringUtils.isNotEmpty(dictType) && StringUtils.isNotNull(value)) {
                    if (!sysDictMap.containsKey(dictType + value)) {
                        String lable = convertDictByExp(Convert.toStr(value), dictType, separator);
                        sysDictMap.put(dictType + value, lable);
                    }
                    cell.setCellValue(sysDictMap.get(dictType + value));
                } else if (value instanceof BigDecimal && -1 != attr.scale()) {
                    cell.setCellValue((((BigDecimal) value).setScale(attr.scale(), attr.roundingMode())).doubleValue());
                } else if (!attr.handler().equals(ExcelHandlerAdapter.class)) {
                    cell.setCellValue(dataFormatHandlerAdapter(value, attr, cell));
                } else {
                    // 设置列类型
                    setCellVo(value, attr, cell);
                }
                addStatisticsData(column, Convert.toStr(value), attr);
            }
        } catch (Exception e) {
            log.error("导出Excel失败{}", e);
        }
        return cell;
    }


    /**
     * 添加单元格
     */
    public Cell addCellArrCost(Excel attr, Row row, T vo, Field field, int column) {
        Cell cell = null;
        try {
            row.setHeight(maxHeight);

            if (attr.isExport()) {
                cell = row.createCell(column);

                if (isSubListValue(vo) && getListCellValue(vo).size() > 1 && attr.needMerge()) {
                    CellRangeAddress cellAddress = new CellRangeAddress(subMergedFirstRowNum, subMergedLastRowNum, column, column);
                    sheet.addMergedRegion(cellAddress);
                }

                cell.setCellStyle(styles.get(StringUtils.format("data_{}_{}_{}", attr.align(), attr.color(), attr.backgroundColor())));

                Object value = getTargetValue(vo, field, attr);
                String dateFormat = attr.dateFormat();
                String readConverterExp = attr.readConverterExp();
                String separator = attr.separator();
                String dictType = attr.dictType();

                if (StringUtils.isNotEmpty(dateFormat) && StringUtils.isNotNull(value)) {
                    cell.setCellValue(parseDateToStr(dateFormat, value));
                } else if (StringUtils.isNotEmpty(readConverterExp) && StringUtils.isNotNull(value)) {
                    cell.setCellValue(convertByExp(Convert.toStr(value), readConverterExp, separator));
                } else if (StringUtils.isNotEmpty(dictType) && StringUtils.isNotNull(value)) {
                    if (!sysDictMap.containsKey(dictType + value)) {
                        String lable = convertDictByExp(Convert.toStr(value), dictType, separator);
                        sysDictMap.put(dictType + value, lable);
                    }
                    cell.setCellValue(sysDictMap.get(dictType + value));
                } else if (value instanceof BigDecimal && -1 != attr.scale()) {
                    cell.setCellValue((((BigDecimal) value).setScale(attr.scale(), attr.roundingMode())).doubleValue());
                } else if (!attr.handler().equals(ExcelHandlerAdapter.class)) {
                    cell.setCellValue(dataFormatHandlerAdapter(value, attr, cell));
                } else {
                    if (value == null) {
                        cell.setBlank();
                    } else if (value instanceof Number) {
                        cell.setCellValue(((Number) value).doubleValue());
                    } else if (value instanceof Date) {
                        DataFormat format = cell.getSheet().getWorkbook().createDataFormat();
                        CellStyle dateStyle = cell.getSheet().getWorkbook().createCellStyle();
                        String pattern = "yyyy-MM-dd HH:mm:ss";
                        if (StringUtils.isNotEmpty(dateFormat)) {
                            pattern = dateFormat;
                        }
                        dateStyle.setDataFormat(format.getFormat(pattern));
                        cell.setCellStyle(dateStyle);
                        cell.setCellValue((Date) value);
                    } else if (value instanceof Boolean) {
                        cell.setCellValue((Boolean) value);
                    } else if (value instanceof LocalDate || value instanceof LocalDateTime) {
                        cell.setCellValue(value.toString());
                    } else {
                        cell.setCellValue(value.toString());
                    }
                }

                addStatisticsData(column, Convert.toStr(value), attr);
            }
        } catch (Exception e) {
            log.error("导出Excel失败{}", e);
        }
        return cell;
    }


    /**
     * 设置 POI XSSFSheet 单元格提示或选择框
     *
     * @param sheet         表单
     * @param textlist      下拉框显示的内容
     * @param promptContent 提示内容
     * @param firstRow      开始行
     * @param endRow        结束行
     * @param firstCol      开始列
     * @param endCol        结束列
     */
    public void setPromptOrValidation(Sheet sheet, String[] textlist, String promptContent, int firstRow, int endRow,
                                      int firstCol, int endCol) {
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = textlist.length > 0 ? helper.createExplicitListConstraint(textlist) : helper.createCustomConstraint("DD1");
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);
        DataValidation dataValidation = helper.createValidation(constraint, regions);
        if (StringUtils.isNotEmpty(promptContent)) {
            // 如果设置了提示信息则鼠标放上去提示
            dataValidation.createPromptBox("", promptContent);
            dataValidation.setShowPromptBox(true);
        }
        // 处理Excel兼容性问题
        if (dataValidation instanceof XSSFDataValidation) {
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.setShowErrorBox(true);
        } else {
            dataValidation.setSuppressDropDownArrow(false);
        }
        sheet.addValidationData(dataValidation);
    }

    /**
     * 设置某些列的值只能输入预制的数据,显示下拉框（兼容超出一定数量的下拉框）.
     *
     * @param sheet         要设置的sheet.
     * @param textlist      下拉框显示的内容
     * @param promptContent 提示内容
     * @param firstRow      开始行
     * @param endRow        结束行
     * @param firstCol      开始列
     * @param endCol        结束列
     */
    public void setXSSFValidationWithHidden(Sheet sheet, String[] textlist, String promptContent, int firstRow, int endRow, int firstCol, int endCol) {
        String hideSheetName = "combo_" + firstCol + "_" + endCol;
        Sheet hideSheet = wb.createSheet(hideSheetName); // 用于存储 下拉菜单数据
        for (int i = 0; i < textlist.length; i++) {
            hideSheet.createRow(i).createCell(0).setCellValue(textlist[i]);
        }
        // 创建名称，可被其他单元格引用
        Name name = wb.createName();
        name.setNameName(hideSheetName + "_data");
        name.setRefersToFormula(hideSheetName + "!$A$1:$A$" + textlist.length);
        DataValidationHelper helper = sheet.getDataValidationHelper();
        // 加载下拉列表内容
        DataValidationConstraint constraint = helper.createFormulaListConstraint(hideSheetName + "_data");
        // 设置数据有效性加载在哪个单元格上,四个参数分别是：起始行、终止行、起始列、终止列
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);
        // 数据有效性对象
        DataValidation dataValidation = helper.createValidation(constraint, regions);
        if (StringUtils.isNotEmpty(promptContent)) {
            // 如果设置了提示信息则鼠标放上去提示
            dataValidation.createPromptBox("", promptContent);
            dataValidation.setShowPromptBox(true);
        }
        // 处理Excel兼容性问题
        if (dataValidation instanceof XSSFDataValidation) {
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.setShowErrorBox(true);
        } else {
            dataValidation.setSuppressDropDownArrow(false);
        }

        sheet.addValidationData(dataValidation);
        // 设置hiddenSheet隐藏
        wb.setSheetHidden(wb.getSheetIndex(hideSheet), true);
    }

    /**
     * 解析导出值 0=男,1=女,2=未知
     *
     * @param propertyValue 参数值
     * @param converterExp  翻译注解
     * @param separator     分隔符
     * @return 解析后值
     */
    public static String convertByExp(String propertyValue, String converterExp, String separator) {
        StringBuilder propertyString = new StringBuilder();
        String[] convertSource = converterExp.split(",");
        for (String item : convertSource) {
            String[] itemArray = item.split("=");
            if (StringUtils.containsAny(propertyValue, separator)) {
                for (String value : propertyValue.split(separator)) {
                    if (itemArray[0].equals(value)) {
                        propertyString.append(itemArray[1] + separator);
                        break;
                    }
                }
            } else {
                if (itemArray[0].equals(propertyValue)) {
                    return itemArray[1];
                }
            }
        }
        return StringUtils.stripEnd(propertyString.toString(), separator);
    }

    /**
     * 反向解析值 男=0,女=1,未知=2
     *
     * @param propertyValue 参数值
     * @param converterExp  翻译注解
     * @param separator     分隔符
     * @return 解析后值
     */
    public static String reverseByExp(String propertyValue, String converterExp, String separator) {
        StringBuilder propertyString = new StringBuilder();
        String[] convertSource = converterExp.split(",");
        for (String item : convertSource) {
            String[] itemArray = item.split("=");
            if (StringUtils.containsAny(propertyValue, separator)) {
                for (String value : propertyValue.split(separator)) {
                    if (itemArray[1].equals(value)) {
                        propertyString.append(itemArray[0] + separator);
                        break;
                    }
                }
            } else {
                if (itemArray[1].equals(propertyValue)) {
                    return itemArray[0];
                }
            }
        }
        return StringUtils.stripEnd(propertyString.toString(), separator);
    }

    /**
     * 解析字典值
     *
     * @param dictValue 字典值
     * @param dictType  字典类型
     * @param separator 分隔符
     * @return 字典标签
     */
    public static String convertDictByExp(String dictValue, String dictType, String separator) {
        return DictUtils.getDictLabel(dictType, dictValue, separator);
    }

    /**
     * 反向解析值字典值
     *
     * @param dictLabel 字典标签
     * @param dictType  字典类型
     * @param separator 分隔符
     * @return 字典值
     */
    public static String reverseDictByExp(String dictLabel, String dictType, String separator) {
        return DictUtils.getDictValue(dictType, dictLabel, separator);
    }

    /**
     * 数据处理器
     *
     * @param value 数据值
     * @param excel 数据注解
     * @return
     */
    public String dataFormatHandlerAdapter(Object value, Excel excel, Cell cell) {
        try {
            Object instance = excel.handler().newInstance();
            Method formatMethod = excel.handler().getMethod("format", new Class[]{Object.class, String[].class, Cell.class, Workbook.class});
            value = formatMethod.invoke(instance, value, excel.args(), cell, this.wb);
        } catch (Exception e) {
            log.error("不能格式化数据 " + excel.handler(), e.getMessage());
        }
        return Convert.toStr(value);
    }

    /**
     * 合计统计信息
     */
    private void addStatisticsData(Integer index, String text, Excel entity) {
        if (entity != null && entity.isStatistics()) {
            Double temp = 0D;
            if (!statistics.containsKey(index)) {
                statistics.put(index, temp);
            }
            try {
                temp = Double.valueOf(text);
            } catch (NumberFormatException e) {
            }
            statistics.put(index, statistics.get(index) + temp);
        }
    }

    /**
     * 创建统计行
     */
    public void addStatisticsRow() {
        if (statistics.size() > 0) {
            Row row = sheet.createRow(sheet.getLastRowNum() + 1);
            Set<Integer> keys = statistics.keySet();
            Cell cell = row.createCell(0);
            cell.setCellStyle(styles.get("total"));
            cell.setCellValue("合计");

            for (Integer key : keys) {
                cell = row.createCell(key);
                cell.setCellStyle(styles.get("total"));
                cell.setCellValue(DOUBLE_FORMAT.format(statistics.get(key)));
            }
            statistics.clear();
        }
    }

    /**
     * 编码文件名
     */
    public String encodingFilename(String filename) {
        filename = UUID.randomUUID() + "_" + filename + ".xlsx";
        return filename;
    }

    /**
     * 获取下载路径
     *
     * @param filename 文件名称
     */
    public String getAbsoluteFile(String filename) {
        String downloadPath = RuoYiConfig.getDownloadPath() + filename;
        File desc = new File(downloadPath);
        if (!desc.getParentFile().exists()) {
            desc.getParentFile().mkdirs();
        }
        return downloadPath;
    }

    /**
     * 获取bean中的属性值
     *
     * @param vo    实体对象
     * @param field 字段
     * @param excel 注解
     * @return 最终的属性值
     * @throws Exception
     */
    private Object getTargetValue(T vo, Field field, Excel excel) throws Exception {
        Object o = field.get(vo);
        if (StringUtils.isNotEmpty(excel.targetAttr())) {
            String target = excel.targetAttr();
            if (target.contains(".")) {
                String[] targets = target.split("[.]");
                for (String name : targets) {
                    o = getValue(o, name);
                }
            } else {
                o = getValue(o, target);
            }
        }
        return o;
    }

    /**
     * 以类的属性的get方法方法形式获取值
     *
     * @param o
     * @param name
     * @return value
     * @throws Exception
     */
    private Object getValue(Object o, String name) throws Exception {
        if (StringUtils.isNotNull(o) && StringUtils.isNotEmpty(name)) {
            Class<?> clazz = o.getClass();
            Field field = clazz.getDeclaredField(name);
            field.setAccessible(true);
            o = field.get(o);
        }
        return o;
    }

    /**
     * 得到所有定义字段
     */
    private void createExcelField() {
        this.fields = getFields();
        this.fields = this.fields.stream().sorted(Comparator.comparing(objects -> ((Excel) objects[1]).sort())).collect(Collectors.toList());
        this.maxHeight = getRowHeight();
    }

    /**
     * 获取字段注解信息
     */
    public List<Object[]> getFields() {
        List<Object[]> fields = new ArrayList<Object[]>();
        List<Field> tempFields = new ArrayList<>();
        tempFields.addAll(Arrays.asList(clazz.getSuperclass().getDeclaredFields()));
        tempFields.addAll(Arrays.asList(clazz.getDeclaredFields()));
        for (Field field : tempFields) {
            if (!ArrayUtils.contains(this.excludeFields, field.getName())) {
                // 单注解
                if (field.isAnnotationPresent(Excel.class)) {
                    Excel attr = field.getAnnotation(Excel.class);
                    if (attr != null && (attr.type() == Type.ALL || attr.type() == type)) {
                        field.setAccessible(true);
                        fields.add(new Object[]{field, attr});
                    }
                    if (Collection.class.isAssignableFrom(field.getType())) {
                        subMethod = getSubMethod(field.getName(), clazz);
                        ParameterizedType pt = (ParameterizedType) field.getGenericType();
                        Class<?> subClass = (Class<?>) pt.getActualTypeArguments()[0];
                        this.subFields = FieldUtils.getFieldsListWithAnnotation(subClass, Excel.class);
                    }
                }

                // 多注解
                if (field.isAnnotationPresent(Excels.class)) {
                    Excels attrs = field.getAnnotation(Excels.class);
                    Excel[] excels = attrs.value();
                    for (Excel attr : excels) {
                        if (!ArrayUtils.contains(this.excludeFields, field.getName() + "." + attr.targetAttr())
                                && (attr != null && (attr.type() == Type.ALL || attr.type() == type))) {
                            field.setAccessible(true);
                            fields.add(new Object[]{field, attr});
                        }
                    }
                }
            }
        }
        return fields;
    }

    /**
     * 根据注解获取最大行高
     */
    public short getRowHeight() {
        double maxHeight = 0;
        for (Object[] os : this.fields) {
            Excel excel = (Excel) os[1];
            maxHeight = Math.max(maxHeight, excel.height());
        }
        return (short) (maxHeight * 20);
    }

    /**
     * 创建一个工作簿
     */
    public void createWorkbook() {
        this.wb = new SXSSFWorkbook(500);
        this.sheet = wb.createSheet();
        wb.setSheetName(0, sheetName);
        this.styles = createStyles(wb);
    }

    /**
     * 创建工作表
     *
     * @param sheetNo sheet数量
     * @param index   序号
     */
    public void createSheet(int sheetNo, int index) {
        // 设置工作表的名称.
        if (sheetNo > 1 && index > 0) {
            this.sheet = wb.createSheet();
            this.createTitle();
            wb.setSheetName(index, sheetName + index);
        }
    }

    /**
     * 获取单元格值
     *
     * @param row    获取的行
     * @param column 获取单元格列号
     * @return 单元格值
     */
    public Object getCellValue(Row row, int column) {
        if (row == null) {
            return row;
        }
        Object val = "";
        try {
            Cell cell = row.getCell(column);
            if (StringUtils.isNotNull(cell)) {
                if (cell.getCellType() == CellType.NUMERIC || cell.getCellType() == CellType.FORMULA) {
                    val = cell.getNumericCellValue();
                    if (DateUtil.isCellDateFormatted(cell)) {
                        val = DateUtil.getJavaDate((Double) val); // POI Excel 日期格式转换
                    } else {
                        if ((Double) val % 1 != 0) {
                            val = new BigDecimal(val.toString());
                        } else {
                            val = new DecimalFormat("0").format(val);
                        }
                    }
                } else if (cell.getCellType() == CellType.STRING) {
                    val = cell.getStringCellValue();
                } else if (cell.getCellType() == CellType.BOOLEAN) {
                    val = cell.getBooleanCellValue();
                } else if (cell.getCellType() == CellType.ERROR) {
                    val = cell.getErrorCellValue();
                }

            }
        } catch (Exception e) {
            return val;
        }
        return val;
    }

    /**
     * 判断是否是空行
     *
     * @param row 判断的行
     * @return
     */
    private boolean isRowEmpty(Row row) {
        if (row == null) {
            return true;
        }
        for (int i = row.getFirstCellNum(); i < row.getLastCellNum(); i++) {
            Cell cell = row.getCell(i);
            if (cell != null && cell.getCellType() != CellType.BLANK) {
                return false;
            }
        }
        return true;
    }

    /**
     * 获取Excel2003图片
     *
     * @param sheet    当前sheet对象
     * @param workbook 工作簿对象
     * @return Map key:图片单元格索引（1_1）String，value:图片流PictureData
     */
    public static Map<String, PictureData> getSheetPictures03(HSSFSheet sheet, HSSFWorkbook workbook) {
        Map<String, PictureData> sheetIndexPicMap = new HashMap<String, PictureData>();
        List<HSSFPictureData> pictures = workbook.getAllPictures();
        if (!pictures.isEmpty()) {
            for (HSSFShape shape : sheet.getDrawingPatriarch().getChildren()) {
                HSSFClientAnchor anchor = (HSSFClientAnchor) shape.getAnchor();
                if (shape instanceof HSSFPicture) {
                    HSSFPicture pic = (HSSFPicture) shape;
                    int pictureIndex = pic.getPictureIndex() - 1;
                    HSSFPictureData picData = pictures.get(pictureIndex);
                    String picIndex = anchor.getRow1() + "_" + anchor.getCol1();
                    sheetIndexPicMap.put(picIndex, picData);
                }
            }
            return sheetIndexPicMap;
        } else {
            return sheetIndexPicMap;
        }
    }

    /**
     * 获取Excel2007图片
     *
     * @param sheet    当前sheet对象
     * @param workbook 工作簿对象
     * @return Map key:图片单元格索引（1_1）String，value:图片流PictureData
     */
    public static Map<String, PictureData> getSheetPictures07(XSSFSheet sheet, XSSFWorkbook workbook) {
        Map<String, PictureData> sheetIndexPicMap = new HashMap<String, PictureData>();
        for (POIXMLDocumentPart dr : sheet.getRelations()) {
            if (dr instanceof XSSFDrawing) {
                XSSFDrawing drawing = (XSSFDrawing) dr;
                List<XSSFShape> shapes = drawing.getShapes();
                for (XSSFShape shape : shapes) {
                    if (shape instanceof XSSFPicture) {
                        XSSFPicture pic = (XSSFPicture) shape;
                        XSSFClientAnchor anchor = pic.getPreferredSize();
                        CTMarker ctMarker = anchor.getFrom();
                        String picIndex = ctMarker.getRow() + "_" + ctMarker.getCol();
                        sheetIndexPicMap.put(picIndex, pic.getPictureData());
                    }
                }
            }
        }
        return sheetIndexPicMap;
    }

    /**
     * 格式化不同类型的日期对象
     *
     * @param dateFormat 日期格式
     * @param val        被格式化的日期对象
     * @return 格式化后的日期字符
     */
    public String parseDateToStr(String dateFormat, Object val) {
        if (val == null) {
            return "";
        }
        String str;
        if (val instanceof Date) {
            str = DateUtils.parseDateToStr(dateFormat, (Date) val);
        } else if (val instanceof LocalDateTime) {
            str = DateUtils.parseDateToStr(dateFormat, DateUtils.toDate((LocalDateTime) val));
        } else if (val instanceof LocalDate) {
            str = DateUtils.parseDateToStr(dateFormat, DateUtils.toDate((LocalDate) val));
        } else {
            str = val.toString();
        }
        return str;
    }

    /**
     * 是否有对象的子列表
     */
    public boolean isSubList() {
        return StringUtils.isNotNull(subFields) && subFields.size() > 0;
    }

    /**
     * 是否有对象的子列表，集合不为空
     */
    public boolean isSubListValue(T vo) {
        return StringUtils.isNotNull(subFields) && subFields.size() > 0 && StringUtils.isNotNull(getListCellValue(vo)) && getListCellValue(vo).size() > 0;
    }

    /**
     * 获取集合的值
     */
    public Collection<?> getListCellValue(Object obj) {
        Object value;
        try {
            value = subMethod.invoke(obj, new Object[]{});
        } catch (Exception e) {
            return new ArrayList<Object>();
        }
        return (Collection<?>) value;
    }

    /**
     * 获取对象的子列表方法
     *
     * @param name      名称
     * @param pojoClass 类对象
     * @return 子列表方法
     */
    public Method getSubMethod(String name, Class<?> pojoClass) {
        StringBuffer getMethodName = new StringBuffer("get");
        getMethodName.append(name.substring(0, 1).toUpperCase());
        getMethodName.append(name.substring(1));
        Method method = null;
        try {
            method = pojoClass.getMethod(getMethodName.toString(), new Class[]{});
        } catch (Exception e) {
            log.error("获取对象异常{}", e.getMessage());
        }
        return method;
    }

    /**
     * 导出Excel数据
     *
     * @param outputStream 提供输出流
     * @param list         导出数据集合
     * @param sheetName    sheetName
     */
    public void exportExcelToOutputStream(OutputStream outputStream, List<T> list, String sheetName) {
        this.init(list, sheetName, StringUtils.EMPTY, Type.EXPORT);
        try {
            writeSheet();
            wb.write(outputStream);
        } catch (IOException e) {
            throw new RuntimeException("导出Excel异常" + e);
        } finally {
            IOUtils.closeQuietly(wb);
        }
    }

//    /**
//     * 费用导出
//     * @param response
//     * @param list
//     * @param upObj
//     * @param sheetName
//     */
//    public void exportBillExcel(HttpServletResponse response, List<T> list, Object upObj, String sheetName) {
//        this.init(list, sheetName, StringUtils.EMPTY, Type.EXPORT);
//
//        try {
//            Class<?> clazz = upObj.getClass();
//            int rowIdx = 0;
//
//            //标题
//            Field titleFd = clazz.getDeclaredField("title");
//            titleFd.setAccessible(true);
//            String title = (String) titleFd.get(upObj);
//            Row titleRow = sheet.createRow(rowIdx++);
//            titleRow.setHeightInPoints(30);
//            Cell titleCell = titleRow.createCell(0);
//            titleCell.setCellStyle(styles.get("title"));
//            titleCell.setCellValue(title);
//            sheet.addMergedRegion(new CellRangeAddress(titleRow.getRowNum(), titleRow.getRowNum(), 0, 10));
//
//            Field[] declaredFields = clazz.getDeclaredFields();
//            Row total1Row = sheet.createRow(rowIdx++);
//            Row total2Row = sheet.createRow(rowIdx++);
//            total2Row.setHeight((short) 1500);
//            int colIdx = 0;
//            for (Field declaredField : declaredFields) {
//                declaredField.setAccessible(true);
//                if ("title".equals(declaredField.getName()) || "chargeBilExportVOS".equals(declaredField.getName()))
//                    continue;
//
//                Excel excelAnno = declaredField.getAnnotation(Excel.class);
//                String columnName = excelAnno.name();
//                Object val = declaredField.get(upObj);
//                Cell cellUp = total1Row.createCell(colIdx);
//                Cell cellDDown = total2Row.createCell(colIdx);
//                cellUp.setCellValue(columnName);
//                cellDDown.setCellValue(val.toString());
//                CellStyle style = centerStyle();
//                cellUp.setCellStyle(style);
//                cellDDown.setCellStyle(style);
//                if ("balance".equals(declaredField.getName())) {
//                    colIdx++;
//                    continue;
//                }
//                sheet.addMergedRegion(new CellRangeAddress(total1Row.getRowNum(), total1Row.getRowNum(), colIdx, colIdx + 1));
//                sheet.addMergedRegion(new CellRangeAddress(total2Row.getRowNum(), total2Row.getRowNum(), colIdx, colIdx + 1));
//                colIdx += 2;
//
//            }
//
//            this.rownum = rowIdx + 1;
//            writeSheet();
//            wb.write(response.getOutputStream());
//        } catch (IOException e) {
//            throw new RuntimeException("导出Excel异常" + e);
//        } catch (NoSuchFieldException | IllegalAccessException e) {
//            throw new RuntimeException(e);
//        } finally {
//            IOUtils.closeQuietly(wb);
//        }
//    }

    private boolean isRegionOverlapping(CellRangeAddress newRegion, Sheet sheet) {
        for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
            CellRangeAddress existingRegion = sheet.getMergedRegion(i);
            if (existingRegion.intersects(newRegion)) {
                return true;
            }
        }
        return false;
    }

    private void addMergedRegionIfNotOverlapping(CellRangeAddress region, Sheet sheet) {
        if (!isRegionOverlapping(region, sheet)) {
            sheet.addMergedRegion(region);
        } else {
            log.warn("Attempted to add overlapping merged region: {}", region.formatAsString());
        }
    }

    /**
     * 账单导出
     *
     * @param response
     * @param list
     * @param upObj
     * @param sheetName
     */
    public <T, U> void exportBillExcel(HttpServletResponse response, List<T> list, List<U> list2, Object upObj, String sheetName) {
        this.initTwo(list, list2, sheetName, StringUtils.EMPTY, Type.EXPORT);
//        this.init(list, sheetName, StringUtils.EMPTY, Type.EXPORT);

        try {
            Class<?> clazz = upObj.getClass();
            int rowIdx = 0;
            // 标题行
            Field titleFd = clazz.getDeclaredField("title");
            titleFd.setAccessible(true);
            String title = (String) titleFd.get(upObj);
            Row titleRow = sheet.createRow(rowIdx++);
            titleRow.setHeightInPoints(30);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellStyle(styles.get("title"));
            titleCell.setCellValue(title);
            CellRangeAddress mergedRegion = new CellRangeAddress(titleRow.getRowNum(), titleRow.getRowNum(), 0, 11);
            mergeCellSetBorder(mergedRegion);
            addMergedRegionIfNotOverlapping(mergedRegion, sheet);
//            sheet.addMergedRegion(mergedRegion);

            // 第一行：结算周期、备注
            Row firstDataRow = sheet.createRow(rowIdx++);
            firstDataRow.setHeight((short) 400);
            // 结算周期
            Field settlementCycleFd = clazz.getDeclaredField("settlementCycle");
            settlementCycleFd.setAccessible(true);
            String settlementCycle = (String) settlementCycleFd.get(upObj);
            Cell settlementCycleLabelCell = firstDataRow.createCell(0);
            settlementCycleLabelCell.setCellValue("结算周期");
            settlementCycleLabelCell.setCellStyle(centerStyle());
            sheet.addMergedRegion(new CellRangeAddress(firstDataRow.getRowNum(), firstDataRow.getRowNum(), 0, 1)); // 合并结算周期的值
            Cell settlementCycleValueCell = firstDataRow.createCell(2); // 值放在第2列
            settlementCycleValueCell.setCellValue(settlementCycle);
            settlementCycleValueCell.setCellStyle(centerStyle());
            sheet.addMergedRegion(new CellRangeAddress(firstDataRow.getRowNum(), firstDataRow.getRowNum(), 2, 6)); // 合并结算周期的值

            // 备注
            Field remarksFd = clazz.getDeclaredField("remarks");
            remarksFd.setAccessible(true);
            String remarks = (String) remarksFd.get(upObj);
            Cell remarksLabelCell = firstDataRow.createCell(7); // 标签放在第7列
            remarksLabelCell.setCellValue("备注");
            remarksLabelCell.setCellStyle(centerStyle());
            Cell remarksValueCell = firstDataRow.createCell(8); // 值放在第8列
            remarksValueCell.setCellValue(remarks);
            remarksValueCell.setCellStyle(centerStyle());
            CellRangeAddress remarkRegion = new CellRangeAddress(firstDataRow.getRowNum(), firstDataRow.getRowNum(), 8, 11);
            mergeCellSetBorder(remarkRegion);
            sheet.addMergedRegion(remarkRegion); // 合并备注的值

            // 第二行：充值金额、上期余额、当期退款、非当期退款、已结算、非当期结算、未结算、非当期未结算、未来航班、当期余额 总未结算
            Row secondDataRow = sheet.createRow(rowIdx++);
            secondDataRow.setHeight((short) 400);
            int colIdx = 0;
            for (Field declaredField : clazz.getDeclaredFields()) {
                declaredField.setAccessible(true);
                if ("title".equals(declaredField.getName()) || "chargeBilExportVOS".equals(declaredField.getName()) || "chargeBilExportVOSNotSettle".equals(declaredField.getName())
                        || "settlementCycle".equals(declaredField.getName()) || "remarks".equals(declaredField.getName())) {
                    continue;
                }

                Excel excelAnno = declaredField.getAnnotation(Excel.class);
                String columnName = excelAnno.name();
                Cell cellUp = secondDataRow.createCell(colIdx);
                cellUp.setCellValue(columnName);
                cellUp.setCellStyle(centerStyle());

                colIdx++;
            }

            // 第三行：填充数据
            Row thirdDataRow = sheet.createRow(rowIdx++);
            thirdDataRow.setHeight((short) 400);
            colIdx = 0;
            for (Field declaredField : clazz.getDeclaredFields()) {
                declaredField.setAccessible(true);
                if ("title".equals(declaredField.getName()) || "chargeBilExportVOS".equals(declaredField.getName()) || "chargeBilExportVOSNotSettle".equals(declaredField.getName())
                        || "settlementCycle".equals(declaredField.getName()) || "remarks".equals(declaredField.getName())) {
                    continue;
                }
                Object val = declaredField.get(upObj);
                Cell cellDown = thirdDataRow.createCell(colIdx);
                if (val instanceof BigDecimal) {
                    cellDown.setCellValue(((BigDecimal) val).doubleValue());
                    cellDown.setCellType(CellType.NUMERIC); // 设置单元格类型为数值类型
                } else if (val instanceof Integer) {
                    cellDown.setCellValue(((Integer) val).intValue());
                    cellDown.setCellType(CellType.NUMERIC);
                } else if (val instanceof Double) {
                    cellDown.setCellValue(((Double) val));
                    cellDown.setCellType(CellType.NUMERIC);
                } else {
                    // 对于非数值类型的字段，仍可以按原样设置为字符串
                    cellDown.setCellValue(val.toString());
                }
                cellDown.setCellStyle(centerStyle());

                colIdx++;
            }
            this.rownum = rowIdx + 1;
//            writeSheet();

            // 第一部分数据
            if (!list.isEmpty()) {
                createCustomTitle("已结算");
                createSubHead();
                writeSheet(list);
                rownum += 3;
            }

            // 第二组数据
            if (!list2.isEmpty()) {
                createCustomTitle("未结算");
                createSubHead();
                writeSheet2(list2);
            }

            wb.write(response.getOutputStream());
        } catch (IOException e) {
            throw new RuntimeException("导出Excel异常" + e);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException(e);
        } finally {
            IOUtils.closeQuietly(wb);
        }
    }

    private void mergeCellSetBorder(CellRangeAddress mergedRegion) {
        // 使用RegionUtil设置边框
        RegionUtil.setBorderTop(BorderStyle.THIN, mergedRegion, sheet);
        RegionUtil.setBorderBottom(BorderStyle.THIN, mergedRegion, sheet);
        RegionUtil.setBorderLeft(BorderStyle.THIN, mergedRegion, sheet);
        RegionUtil.setBorderRight(BorderStyle.THIN, mergedRegion, sheet);
    }

    private CellStyle centerStyle() {
        CellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        return style;
    }

    /**
     * 为指定区域添加外边框（不影响内部单元格原有样式）
     */
    private static void addOuterBorder(Sheet sheet, int firstRow, int lastRow, int firstCol, int lastCol) {
        Workbook workbook = sheet.getWorkbook();

        // 创建边框样式
        CellStyle borderStyle = workbook.createCellStyle();
        borderStyle.setBorderTop(BorderStyle.MEDIUM);
        borderStyle.setBorderBottom(BorderStyle.MEDIUM);
        borderStyle.setBorderLeft(BorderStyle.MEDIUM);
        borderStyle.setBorderRight(BorderStyle.MEDIUM);

        // 处理区域边界上的单元格
        for (int rowNum = firstRow; rowNum <= lastRow; rowNum++) {
            Row row = sheet.getRow(rowNum);
            if (row == null) continue;

            for (int colNum = firstCol; colNum <= lastCol; colNum++) {
                Cell cell = row.getCell(colNum);
                if (cell == null) continue;

                // 克隆原有样式（保留原有格式）
                CellStyle newStyle = workbook.createCellStyle();
                newStyle.cloneStyleFrom(cell.getCellStyle());

                // 判断单元格位置并添加相应边框
                if (rowNum == firstRow) newStyle.setBorderTop(borderStyle.getBorderTop());
                if (rowNum == lastRow) newStyle.setBorderBottom(borderStyle.getBorderBottom());
                if (colNum == firstCol) newStyle.setBorderLeft(borderStyle.getBorderLeft());
                if (colNum == lastCol) newStyle.setBorderRight(borderStyle.getBorderRight());

                // 应用新样式
                cell.setCellStyle(newStyle);
            }
        }
    }
}
