package com.gzairports.hz.business.departure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.hz.business.departure.domain.HzDepSpecialTrace;
import com.gzairports.hz.business.departure.domain.query.SpecialTraceQuery;
import com.gzairports.hz.business.departure.domain.vo.SpecialTraceVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 特货跟踪Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Mapper
public interface SpecialTraceMapper extends BaseMapper<HzDepSpecialTrace> {

    /**
     * 特货跟踪列表查询
     * @param query 查询参数
     * @return 列表
     */
    List<SpecialTraceVo> selectListByQuery(SpecialTraceQuery query);
}
