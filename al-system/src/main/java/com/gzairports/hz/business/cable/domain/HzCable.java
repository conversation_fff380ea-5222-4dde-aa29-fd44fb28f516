package com.gzairports.hz.business.cable.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.hz.business.cable.domain.vo.CableMsgTypeVO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 电报数据对象 hz_cable
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
@Data
@TableName("hz_cable")
public class HzCable {
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 电报号 */
    private String cableNo;

    /** 业务流水号 */
    private String serialNo;

    /** 发报地址 */
    private String cableAddress;

    /** 接收地址 */
    private String receiveAddress;

    /** 发报时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cableTime;

    /** 类型 */
    private String type;

    /** 状态 0 手动 1 自动 */
    private Integer isAuto;

    /** 状态 0 接收 1 发送 */
    private Integer isSend;

    /** 状态 0 失败 1 成功 */
    private Integer status;

    /** 版本号 */
    private String version;

    /** 优先级 */
    private String priority;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date flightDate;

    /** 航班号 */
    private String flightNo;

    /** 电报内容 */
    private String content;

    /** 主题 */
    private String theme;

    /** 是否删除 0 否 1 是 */
    private Integer isDel;

    /** 创建人 */
    private String createBy;

    /** 备注 */
    private String remark;

    /** 添加地址 */
    @TableField(exist = false)
    private String addressReceive;

    /** 已录入接收地址 */
    @TableField(exist = false)
    private List<HzCableAddress> addressList;

    @TableField(exist = false)
    private List<CableMsgTypeVO> typeVoList;

    /** 生成报文的id */
    @TableField(exist = false)
    private List<Long> idList;
}
