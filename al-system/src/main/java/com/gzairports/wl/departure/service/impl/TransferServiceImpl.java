package com.gzairports.wl.departure.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.BaseAirportCode;
import com.gzairports.common.basedata.mapper.AirportCodeMapper;
import com.gzairports.common.business.departure.domain.Transfer;
import com.gzairports.common.business.departure.domain.TransferWaybill;
import com.gzairports.common.business.departure.domain.vo.PrintTransferVo;
import com.gzairports.common.business.departure.mapper.MawbMapper;
import com.gzairports.common.business.departure.mapper.TransferMapper;
import com.gzairports.common.business.departure.mapper.TransferWaybillMapper;
import com.gzairports.common.core.domain.entity.SysDept;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.pdf.PdfPrintHelper;
import com.gzairports.common.system.mapper.SysDeptMapper;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.SerialNumberGenerator;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.sign.Base64;
import com.gzairports.hz.business.departure.domain.AirWaybill;
import com.gzairports.hz.business.departure.mapper.AllAirWaybillMapper;
import com.gzairports.wl.departure.domain.query.TransFerWaybillsVo;
import com.gzairports.wl.departure.domain.query.TransferInfoQuery;
import com.gzairports.wl.departure.domain.query.TransferQuery;
import com.gzairports.wl.departure.domain.vo.TransferMawbVo;
import com.gzairports.wl.departure.domain.vo.TransferVo;
import com.gzairports.wl.departure.service.ITransferService;
import com.itextpdf.text.Document;
import com.itextpdf.text.pdf.PdfCopy;
import com.itextpdf.text.pdf.PdfImportedPage;
import com.itextpdf.text.pdf.PdfReader;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 货站出港货物交接Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Service
public class TransferServiceImpl extends ServiceImpl<TransferMapper, Transfer> implements ITransferService {

    @Autowired
    private TransferMapper transferMapper;

    @Autowired
    private TransferWaybillMapper transferWaybillMapper;

    @Autowired
    private MawbMapper mawbMapper;

    @Autowired
    private AllAirWaybillMapper allAirWaybillMapper;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    @Autowired
    private AirportCodeMapper airportCodeMapper;

    /**
     * 根据条件查询货物交接列表
     * @param query 查询条件
     * @return 货物交接列表
     */
    @Override
    public List<TransferVo> selectList(TransferQuery query) {
        query.setDeptId(SecurityUtils.getHighParentId());
        List<TransferVo> list = transferMapper.selectListByQuery(query);
        for (TransferVo transferVo : list) {
            if (transferVo.getType().equals(1)){
                transferVo.setWaybillNum(1);
            }else {
                Integer count = transferWaybillMapper.selectCount(new QueryWrapper<TransferWaybill>().eq("transfer_id", transferVo.getId()));
                transferVo.setWaybillNum(count);
            }
        }
        return list;
    }

    /**
     * 新增出港货物交接单
     * @param transfer 出港货物交接单数据
     * @return 结果
     */
    @Override
    public int add(Transfer transfer) {
        List<Long> waybillIds = transfer.getWaybillIds();
        if (transfer.getType() == 0){
            if (CollectionUtils.isEmpty(waybillIds)){
                throw new CustomException("请选择至少一个运单");
            }
        }
        for (Long waybillId:waybillIds) {
            AirWaybill airWaybill = allAirWaybillMapper.selectById(waybillId);
            if(airWaybill.getPayStatus() == 0 || airWaybill.getPayStatus() == 14){
                throw new CustomException("运单未支付");
            }
            TransferWaybill transferWaybillById = transferWaybillMapper.selectOne(new QueryWrapper<TransferWaybill>()
                    .eq("mawb_id", waybillId));
            if(transferWaybillById!=null){
                throw new CustomException("运单已有交接单");
            }

            Date writeTime = airWaybill.getWriteTime();
            Date updateTime = airWaybill.getUpdateTime();
            if (StringUtils.isNotNull(updateTime)){
                //updateTime比writeTime晚一个月
                if (updateTime.getTime() > writeTime.getTime() + 30L * 24 * 60 * 60 * 1000){
                    throw new CustomException("填开时间过久");
                }
            }
            //writeTime距离现在的时间超过一个月
            if (System.currentTimeMillis() > writeTime.getTime()  + 30L * 24 * 60 * 60 * 1000){
                throw new CustomException("填开时间过久");
            }
        }

        transfer.setDeptId(SecurityUtils.getHighParentId());
        //提交人
        transfer.setSubmitter(SecurityUtils.getNickName());
        if (transfer.getType().equals(0)){
            transferMapper.insert(transfer);
            for (Long waybillId : waybillIds) {
                TransferWaybill transferWaybill = new TransferWaybill();
                transferWaybill.setTransferId(transfer.getId());
                transferWaybill.setMawbId(waybillId);
                transferWaybillMapper.insert(transferWaybill);
            }
        }else {
            transferMapper.insert(transfer);
        }
        return 1;
    }

    /**
     * 查询出港货物交接单详情
     * @param id 出港货物交接单数据
     * @return 出港货物交接单详情
     */
    @Override
    public TransferVo getInfo(Long id) {
        TransferVo vo = transferMapper.selectByIds(id,SecurityUtils.getHighParentId());
        List<TransferWaybill> transferWaybills = transferWaybillMapper.selectList(new QueryWrapper<TransferWaybill>()
                .eq("transfer_id", vo.getId()));
        if (!CollectionUtils.isEmpty(transferWaybills)){
            List<Long> mawbIds = transferWaybills.stream().map(TransferWaybill::getMawbId).collect(Collectors.toList());
            List<TransferMawbVo> list = mawbMapper.selectByIds(mawbIds, SecurityUtils.getHighParentId());
            if (!CollectionUtils.isEmpty(list)){
                vo.setVos(list);
            }
        }
        return vo;
    }

    /**
     * 删除出港货物交接单
     * @param id 出港货物交接单id
     * @return 结果
     */
    @Override
    public int del(Long id) {
        transferWaybillMapper.delete(new QueryWrapper<TransferWaybill>().eq("transfer_id", id));
        Transfer transfer = transferMapper.selectById(id);
        transfer.setIsDel(1);
        return transferMapper.updateById(transfer);
    }

    /**
     * 编辑出港货物交接单
     * @param transfer 出港货物交接单数据
     * @return 结果
     */
    @Override
    public int edit(Transfer transfer) {
        if (!CollectionUtils.isEmpty(transfer.getWaybillIds())){
            transferWaybillMapper.delete(new QueryWrapper<TransferWaybill>().eq("transfer_id", transfer.getId()));
            for (Long waybillId : transfer.getWaybillIds()) {
                AirWaybill airWaybill = allAirWaybillMapper.selectById(waybillId);
                if(airWaybill.getPayStatus() == 0 || airWaybill.getPayStatus() == 14){
                    throw new CustomException("运单未支付");
                }
                TransferWaybill transferWaybill = new TransferWaybill();
                transferWaybill.setTransferId(transfer.getId());
                transferWaybill.setMawbId(waybillId);
                transferWaybillMapper.insert(transferWaybill);
            }
        }
        return transferMapper.updateById(transfer);
    }

    /**
     * 出港货物交接增加运单
     * @param query 出港货物交接数据
     * @return 结果
     */
    @Override
    public List<TransferMawbVo> addWaybill(TransferInfoQuery query) {
        TransferWaybill transferWaybill = new TransferWaybill();
        transferWaybill.setTransferId(query.getTransferId());
        for (Long aLong : query.getMawbId()) {
            transferWaybill.setMawbId(aLong);
            transferWaybillMapper.insert(transferWaybill);
        }
        List<TransferWaybill> transferWaybills = transferWaybillMapper.selectList(new QueryWrapper<TransferWaybill>()
                .eq("transfer_id", query.getTransferId()));
        List<Long> collect = transferWaybills.stream().map(TransferWaybill::getMawbId).collect(Collectors.toList());
        return mawbMapper.selectByIds(collect,SecurityUtils.getHighParentId());
    }

    /**
     * 出港货物交接删除运单
     * @param query 出港货物交接数据
     * @return 结果
     */
    @Override
    public int delWaybill(TransferInfoQuery query) {
        for (Long aLong : query.getMawbId()) {
            transferWaybillMapper.delete(new QueryWrapper<TransferWaybill>()
                    .eq("transfer_id",query.getTransferId()).eq("mawb_id",aLong));
        }
        return 1;
    }

    /**
     * 获取交接单头部信息
     * @return 交接单头部信息
     */
    @Override
    public Transfer getTransfer() {
        Transfer transfer = new Transfer();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date time = new Date();
        String date = format.format(time);
        Date parse = null;
        try {
            parse = format.parse(date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        transfer.setDate(parse);
        String num = SerialNumberGenerator.generateSerialNumber();
        transfer.setNum(num);
        String username = SecurityUtils.getUsername();
        transfer.setAgent(username);
        transfer.setSubmitTime(time);
        Transfer transfer1 = transferMapper.selectOne(new QueryWrapper<Transfer>().eq("num", num));
        if (transfer1 != null){
            return transfer1;
        }
        return transfer;
    }

    /**
     * 获取可交接运单
     * @return 交接运单列表
     */
    @Override
    public List<TransferMawbVo> getWaybill(TransFerWaybillsVo vo) {
        vo.setDeptId(SecurityUtils.getHighParentId());
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(vo.getWriteTime());
        Date startOfDay = calendar.getTime();

        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        Date endOfDay = calendar.getTime();

        vo.setStartTime(startOfDay);
        vo.setEndTime(endOfDay);
        List<TransferMawbVo> list = mawbMapper.getWaybillList(vo);
        for (TransferMawbVo transferMawbVo : list) {
            TransferWaybill transferWaybill = transferWaybillMapper.selectOne(new QueryWrapper<TransferWaybill>()
                    .eq("mawb_id", transferMawbVo.getId()));
            if (transferWaybill == null){
                //使用主单的id去查询该主单是否为拼单 如果是 要加上拼单的件数和重量
                AirWaybill airWaybill = allAirWaybillMapper.selectById(transferMawbVo.getId());
                if (airWaybill.getSwitchBill() == 1){
                    String originBill = airWaybill.getOriginBill();
                    AirWaybill airWaybillForOrigin = allAirWaybillMapper.selectOne(new QueryWrapper<AirWaybill>()
                            .eq("waybill_code", originBill)
                            .ne("status", "INVALID"));
                    if (airWaybillForOrigin!=null){
                        transferMawbVo.setQuantity(airWaybillForOrigin.getQuantity() + transferMawbVo.getQuantity());
                        transferMawbVo.setWeight(airWaybillForOrigin.getWeight().add(transferMawbVo.getWeight()));
                        transferMawbVo.setChargeWeight(airWaybillForOrigin.getChargeWeight().add(transferMawbVo.getChargeWeight()));
                    }
                }
            }
        }
        return list;
    }

    /**
     * 打印货物交接单数据
     * @param id 交接单id
     */
    @Override
    public void printTransfer(HttpServletResponse response, Long id) throws Exception {
        PrintTransferVo vo = transferMapper.selectPrintTransferData(id);
        vo.setSourcePort("贵阳");
        vo.setTitle(vo.getDeptName() + "交接单");
        vo.setIsDanger("无危险品");
        if (StringUtils.isNotEmpty(vo.getSealUrl())){
            byte[] bytes = downloadFileFromUrl(vo.getSealUrl());
            String encode = Base64.encode(bytes);
            vo.setSealUrl(encode);
        }
        List<TransferWaybill> transferWaybills = transferWaybillMapper.selectList(new QueryWrapper<TransferWaybill>()
                .eq("transfer_id", id));
        List<byte[]> pages = new ArrayList<>();
        if (!CollectionUtils.isEmpty(transferWaybills)){
            List<Long> mawbIds = transferWaybills.stream().map(TransferWaybill::getMawbId).collect(Collectors.toList());
            List<TransferMawbVo> list = mawbMapper.selectByIds(mawbIds, SecurityUtils.getHighParentId());
            if (!CollectionUtils.isEmpty(list)){
                List<TransferMawbVo> collect = list.stream().filter(e -> StringUtils.isNotEmpty(e.getDangerCode())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(collect)){
                    vo.setIsDanger("有危险品");
                }
                List<List<TransferMawbVo>> lists = splitListIntoSubLists(list, 6);
                for (int page = 1; page <= lists.size(); page++) {
                    List<TransferMawbVo> currentTransfers = lists.get(page - 1);
                    for (TransferMawbVo currentTransfer : currentTransfers) {
                        BaseAirportCode baseAirportCode = airportCodeMapper.selectByCode(currentTransfer.getDesPort());
                        currentTransfer.setDesPort(baseAirportCode.getChineseName());
                        String substring = currentTransfer.getWaybillCode().substring(4);
                        String waybillCodeAbb;
                        if(substring.contains("DN")){
                            waybillCodeAbb = substring.substring(0, 2) + "-" + substring.substring(2);
                        }else{
                            waybillCodeAbb = substring.substring(0, 3) + "-" + substring.substring(3);
                        }
                        currentTransfer.setWaybillCode(waybillCodeAbb);
                    }
                    vo.setVos(currentTransfers);
                    ClassPathResource resource = new ClassPathResource("template/transfer.pdf");
                    if (resource.exists()){
                        String path = resource.getPath();
                        byte[] pdfDataFromTemplate = PdfPrintHelper.getPdfDataFromTemplate(vo, path);
                        pages.add(pdfDataFromTemplate);
                    }
                }
            }
        }
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        Document document = new Document();
        PdfCopy copy = new PdfCopy(document, baos);
        document.open();
        if (CollectionUtils.isEmpty(pages)) {
            ClassPathResource resource = new ClassPathResource("template/transfer.pdf");
            if (resource.exists()) {
                PrintTransferVo transferVo = new PrintTransferVo();
                SysDept dept = sysDeptMapper.selectDeptById(SecurityUtils.getHighParentId());
                transferVo.setSourcePort("贵阳");
                transferVo.setDate(new Date());
                transferVo.setTitle(dept.getDeptName() + "交接单");
                if (StringUtils.isNotEmpty(dept.getSealUrl())){
                    byte[] bytes = downloadFileFromUrl(dept.getSealUrl());
                    String encode = Base64.encode(bytes);
                    transferVo.setSealUrl(encode);
                }
                transferVo.setIsDanger("无危险品");
                String path = resource.getPath();
                byte[] bytes = PdfPrintHelper.getPdfDataFromTemplate(transferVo, path);
                try (ByteArrayInputStream bais = new ByteArrayInputStream(bytes)) {
                    PdfReader reader = new PdfReader(bais);
                    for (int i = 1; i <= reader.getNumberOfPages(); i++) {
                        PdfImportedPage page = copy.getImportedPage(reader, i);
                        copy.addPage(page);
                    }
                    reader.close();
                }
            }
        }else {
            for (byte[] pageContent : pages) {
                try (ByteArrayInputStream bais = new ByteArrayInputStream(pageContent)) {
                    PdfReader reader = new PdfReader(bais);
                    for (int i = 1; i <= reader.getNumberOfPages(); i++) {
                        PdfImportedPage page = copy.getImportedPage(reader, i);
                        copy.addPage(page);
                    }
                    reader.close();
                }
            }
        }
        document.close();
        // 设置响应头
        response.reset();
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "inline; filename=example.pdf");
        // 获取输出流并写入字节数据
        response.getOutputStream().write(baos.toByteArray());
        response.getOutputStream().flush();
    }

    /**
     * 获取特货交接单需要回显的主单信息
     * @param id 主单id
     * */
    @Override
    public TransferVo getTransferByMawbId(Long id) {
        AirWaybill airWaybill = allAirWaybillMapper.selectById(id);
        TransferVo transferVo = new TransferVo();
        BeanUtils.copyProperties(airWaybill,transferVo);
        transferVo.setFlightNo(airWaybill.getFlightNo1());
        transferVo.setDes(airWaybill.getDes1());
        transferVo.setPackWay(airWaybill.getPack());
        transferVo.setQuantity(airWaybill.getQuantity().toString());
        transferVo.setWeight(airWaybill.getWeight().toString());
        return transferVo;
    }

    public static <T> List<List<T>> splitListIntoSubLists(List<T> list, int subListSize) {
        List<List<T>> subLists = new ArrayList<>();
        int size = list.size();
        for (int start = 0; start < size; start += subListSize) {
            int end = Math.min(size, start + subListSize);
            subLists.add(new ArrayList<>(list.subList(start, end)));
        }
        return subLists;
    }

    private byte[] downloadFileFromUrl(String urlStr) throws Exception {
        URL url = new URL(urlStr);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(5000);
        connection.setReadTimeout(5000);

        if (connection.getResponseCode() != 200) {
            throw new RuntimeException("Failed to connect, HTTP error code : " + connection.getResponseCode());
        }

        try (InputStream inputStream = connection.getInputStream();
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            return baos.toByteArray();
        }
    }

}
