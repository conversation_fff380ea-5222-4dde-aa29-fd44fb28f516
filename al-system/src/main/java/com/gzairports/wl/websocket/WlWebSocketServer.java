package com.gzairports.wl.websocket;

import com.alibaba.fastjson2.JSON;
import com.gzairports.common.core.redis.RedisCache;
import com.gzairports.common.message.domain.vo.SocketMessageVo;
import com.gzairports.hz.websocket.HzWebSocketServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * webSocket 实现
 * <AUTHOR>
 */

@ServerEndpoint("/webSocket/wlMessage/{userId}")
@Component
public class WlWebSocketServer {

    private static RedisCache redisCache;

    @Autowired
    private void setRedisCache(RedisCache redisCache) {
        WlWebSocketServer.redisCache = redisCache;
    }

    /**
     * WebSocket服务端
     * <AUTHOR>
     */

        /**
         * 存放所有在线的客户端
         */
        private static Map<String, Session> clients = new ConcurrentHashMap<>();

        /**
         * 保存用户角色的映射
         */
        private static final Map<Session, Long> userDepts = new ConcurrentHashMap<>();

        private static final Logger log = LoggerFactory.getLogger(WlWebSocketServer.class);

        @OnOpen
        public void onOpen(Session session, @PathParam("userId") Long userId) {
            log.info("有新的客户端连接了: {}", session.getId());
            //将新用户存入在线的组
            clients.put(session.getId(), session);
            userDepts.put(session,userId);
            getMessageByRedis(userId);
        }
        /**
         * 客户端关闭
         * @param session session
         */
        @OnClose
        public void onClose(Session session) {
            log.info("有用户断开了, id为:{}", session.getId());
            //将掉线的用户移除在线的组里
            clients.remove(session.getId());
            userDepts.remove(session);
        }

        /**
         * 发生错误
         * @param throwable e
         */
        @OnError
        public void onError(Throwable throwable) {
            throwable.printStackTrace();
        }

        /**
         * 收到客户端发来消息
         * @param message  消息对象
         */
        @OnMessage
        public void onMessage(String message) {
            log.info("服务端收到客户端发来的消息: {}", message);
            this.sendAll(message);
        }

        /**
         * 群发消息
         * @param message 消息内容
         */
        public void sendAll(String message) {
            for (Map.Entry<String, Session> sessionEntry : clients.entrySet()) {
                sessionEntry.getValue().getAsyncRemote().sendText(message);
            }
        }

    /**
     * 群发消息给特定部门的方法
     * @param deptId 部门
     * @param vo 消息对象
     */
    public void sendMessageToDept(Long deptId, SocketMessageVo vo) {
        Set<Session> sessions = userDepts.entrySet().stream()
                .filter(entry -> entry.getValue().equals(deptId))
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());

        sessions.forEach(session -> {
            try {
                session.getBasicRemote().sendText(JSON.toJSONString(vo));
            } catch (IOException e) {
                e.printStackTrace();
            }
        });
    }


    /**
     * @author: lan
     * @description: 用户刚登陆时从redis拉取所有的消息
     * @date: 2024/10/23
     */
    public void getMessageByRedis(Long userId) {
        //从redis读取key的userId的数据,其中redis采用的数据结构为集合
        ListOperations listOperations = redisCache.redisTemplate.opsForList();
        List<String> range = listOperations.range("airc-message:" + userId, 0, -1);
//        List<String> range = redisCache.redisTemplate.opsForList().range(userId.toString(), 0, -1);
        if (range != null) {
            range.forEach(message -> {
                Set<Session> sessions = userDepts.entrySet().stream()
                        .filter(entry -> entry.getValue().equals(userId))
                        .map(Map.Entry::getKey)
                        .collect(Collectors.toSet());

                sessions.forEach(session -> {
                    try {
                        session.getBasicRemote().sendText(JSON.toJSONString(message));
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                });
            });
            redisCache.redisTemplate.delete("airc-message:" + userId);
        }
    }
}
