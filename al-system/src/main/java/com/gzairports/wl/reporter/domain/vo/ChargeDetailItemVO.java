package com.gzairports.wl.reporter.domain.vo;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class ChargeDetailItemVO {
    /**
     * 主单id
     * */
    private Long id;
    /**
     * 分单号 ->一个主单挂多个分单时 每个分单有自己的收费项目
     * */
    private String hawbWaybillCode;
    /**
     * 分单件数
     * */
    private String hawbQuantity;
    /**
     * 分单重量
     * */
    private String hawbWeight;
    /**
     * 分单计费重量
     * */
    private String hawbChargeWeight;
    /**
     * 结算对象 ?
     */
    private String settlementObject;
    /**
     * 费用项目
     */
    private String chargeName;

    /**
     * 结算类型 ?
     */
    private String settlementType;

    /**
     * 费率
     */
    private BigDecimal rate;

    /**
     * 结算金额
     */
    private BigDecimal totalCharge;

    /**
     * 结算金额
     * */
    private String totalChargeStr;

    /**
     * 计费时间
     */
//    @JsonFormat(pattern = "yyyy-MM-dd")
    private String billingTime;

    /**
     * 计费人 ?
     */
    private String billingPerson;

    /**
     * 运单号
     */
    private String waybillCode;

    /**
     * 运单类型
     * */
    private String waybillType;
    /**
     * 起点站
     */
    private String sourcePort;

    /**
     * 目的站
     */
    private String desPort;
    /**
     * 品名
     */
    private String cargoName;
    /**
     * 特货代码1
     */
    private String specialCargoCode1;

    public ChargeDetailItemVO(String settlementObject, String chargeName, String settlementType, BigDecimal rate, BigDecimal totalCharge, String billingTime, String billingPerson, String waybillCode, String waybillType) {
        this.settlementObject = settlementObject;
        this.chargeName = chargeName;
        this.settlementType = settlementType;
        this.rate = rate;
        this.totalCharge = totalCharge;
        this.billingTime = billingTime;
        this.billingPerson = billingPerson;
        this.waybillCode = waybillCode;
        this.waybillType = waybillType;
    }

    public ChargeDetailItemVO() {
    }
}

