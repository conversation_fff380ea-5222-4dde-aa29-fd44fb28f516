package com.gzairports.wl.departure.domain.vo;

import com.gzairports.common.annotation.Excel;
import com.gzairports.common.pdf.PdfPrintAnnotation;
import lombok.Data;

import java.math.BigDecimal;

import static com.gzairports.common.pdf.PdfPrintAnnotation.PdfFieldType.TEXT;

/**
 * 邮件单查询返回参数
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Data
public class MailWaybillVo {

    /** 主键id */
    private Long id;

    /** 运单号 */
    @Excel(name = "邮件单号")
    private String waybillCode;

    /** 运单状态 */
    @Excel(name = "运单状态")
    private String status;

    /** 始发站 */
    @Excel(name = "始发站")
    private String sourcePort;

    /** 目的站 */
    @Excel(name = "目的站")
    private String desPort;

    /** 邮件种类 */
    @Excel(name = "邮件种类")
    private String cargoName;

    /** 邮件品名 */
    @Excel(name = "邮件品名")
    private String categoryName;

    /** 托运局名称 */
    private String shipper;

    /** 接收局名称 */
    private String consign;

    /** 承运人1 */
    private String carrier1;

    /** 航班号1 */
    @Excel(name = "航班号1")
    private String flightNo1;

    /** 件数 */
    @Excel(name = "件数")
    private Integer quantity;

    /** 重量 */
    @Excel(name = "重量")
    private BigDecimal weight;

    /** 计费重量 */
    @Excel(name = "计费重量")
    private BigDecimal chargeWeight;

    /** 体积（M3） */
    private BigDecimal volume;

    /** 制单地点 */
    private String creationLocation;

    /** 票面费率 */
    @Excel(name = "票面费率")
    private BigDecimal ratePerKg;

    /** 票面运价 */
    @Excel(name = "票面运价")
    private BigDecimal costSum;

    /** 应付费率 */
    @Excel(name = "应付费率")
    private BigDecimal wRate;

    /** 应付运价 */
    @Excel(name = "应付运价")
    private BigDecimal wCostSum;

    /** 应收费率 */
    @Excel(name = "应收费率")
    private BigDecimal rRate;

    /** 应收运价 */
    @Excel(name = "应收运价")
    private BigDecimal rCostSum;

    /** 利润 */
    @Excel(name = "利润")
    private BigDecimal profit;
}
