package com.gzairports.common.business.departure.domain.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 运单收费日志对象 t_log_awblog
 *
 * <AUTHOR>
 * @date 2023-09-25
 */
@Data
@TableName(schema = "KWECFPS", value = "T_LOG_FEELOG")
public class TLogFeelog {
    private static final long serialVersionUID = 1L;

    /** 记录ID */
    private Long recid;

    /** 日志状态/操作代码 */
    @Excel(name = "日志状态/操作代码")
    private String statusid;

    /** 日志状态/操作代码子代码 */
    @Excel(name = "日志状态/操作代码子代码")
    private String subid;

    /** 运单类型 */
    @Excel(name = "运单类型")
    private String stocktypeid;

    /** 运单前缀 */
    @Excel(name = "运单前缀")
    private String stockpre;

    /** 运单号 */
    @Excel(name = "运单号")
    private String stockno;

    /** 计费重量 */
    @Excel(name = "计费重量")
    private BigDecimal feewgt;
    /**收费流水号*/
    @Excel(name = "收费流水号")
    private String chargeseq;
    /**FeeXML(包含收费项目\费用\结算方式)*/
    @Excel(name = "FeeXML(包含收费项目\\费用\\结算方式)")
    private  String particularinfo;
    /**客户(发\提\取货人)*/
    @Excel(name = "客户(发\\提\\取货人)")
    private String customerid;
    /**客户(业务操作人)*/
    @Excel(name = "业务操作人")
    private String operator;
    /**业务操作营业点*/
    @Excel(name = "业务操作营业点")
    private String opedepartid;
    @Excel(name = "业务操作时间")
    private Date opetime;
    @Excel(name = "备注")
    private String remark;



}
