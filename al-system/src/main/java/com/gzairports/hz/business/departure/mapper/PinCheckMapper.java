package com.gzairports.hz.business.departure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.hz.business.departure.domain.PinCheck;
import com.gzairports.hz.business.departure.domain.query.PinCheckQuery;
import com.gzairports.hz.business.departure.domain.vo.PinCheckVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 开箱抽查Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Mapper
public interface PinCheckMapper extends BaseMapper<PinCheck> {

    /**
     * 开箱抽查查询列表
     * @param query 查询条件
     * @return 结果
     */
    List<PinCheckVo> selectPinCheckList(PinCheckQuery query);

    /**
     * 查询详情
     * @param id 抽检id
     * @return 结果
     */
    PinCheckVo getInfo(Long id);

    /**
     * H5端查询开箱检查数据
     * @param query 查询条件
     * @return 结果
     */
    List<PinCheckVo> selectH5List(@Param("query") PinCheckQuery query,@Param("userId") Long userId);

    /**
     * 根据条件查询暂存列表
     * @param userId 登录人id
     * @param query 查询条件
     * @return 暂存列表
     */
    List<PinCheckVo> selectStagingList(@Param("userId") Long userId,@Param("query") PinCheckQuery query);
}
