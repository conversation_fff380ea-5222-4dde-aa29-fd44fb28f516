package com.gzairports.common.basedata.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lan
 * @create: 2025-04-11 13:55
 **/

@Data
public class BaseCargoUldVo {

    /** 主键id */
    private Long id;

    /** 编号 */
    private String code;

    /** 类型 */
    private String type;

    /** 航司 */
    private String airCompany;

    /** 集装器检查时间-年月日 */
    private String checkDate;

    /** 集装器检查时间-时分 */
    private String checkTime;

    /** 集装器航班 */
    private String flightNo;

    /** 航班表日期 */
    private String flightDate;

    /** 出发航站 */
    private String departureStation;

    /** 航班目的站 */
    private String desPort;

    /** 航班id */
    private Long flightId;

    /** IN/OUT */
    private String inOut;

    /** 分组复合键值 */
    private String groupKey;
}
