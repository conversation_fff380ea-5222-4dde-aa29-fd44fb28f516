package com.gzairports.common.basedata.mapper;

import com.gzairports.common.basedata.domain.BaseSpecialCargoCode;
import com.gzairports.common.basedata.domain.query.SpecialCargoCodeQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 装载特货代码Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Mapper
public interface SpecialCargoCodeMapper {

    /**
     * 查询装载特货代码列表
     *
     * @param query 查询参数
     * @return 装载特货代码列表
     */
    List<BaseSpecialCargoCode> selectSpecialCargoCodeList(SpecialCargoCodeQuery query);

    /**
     * 根据特殊处理代码查询装载特货代码
     *
     * @param handleCode 特殊处理代码
     * @return 结果
     */
    BaseSpecialCargoCode selectSpecialCargoCodeByCode(@Param("handleCode") String handleCode,
                                                      @Param("airCompany") String airCompany,
                                                      @Param("isDanger") Integer isDanger,
                                                      @Param("carrierCode") String carrierCode);

    /**
     * 新增装载特货代码
     *
     * @param code 装载特货代码
     * @return 结果
     */
    int insertSpecialCargoCode(BaseSpecialCargoCode code);

    /**
     * 修改装载特货代码
     *
     * @param code 装载特货代码
     * @return 结果
     */
    int updateSpecialCargoCode(BaseSpecialCargoCode code);

    /**
     * 删除装载特货代码
     *
     * @param ids 装载特货代码id集合
     * @return 结果
     */
    int delSpecialCargoCode(Long[] ids);
}
