package com.gzairports.wl.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lan
 * @Desc: 安检申报返回参数
 * @create: 2024-10-11 17:17
 **/
@Data
public class SecurityVoWl {
    /** 主键id（运单id） */
    private Long id;

    /** 运单号 */
    private String waybillCode;

    /** 代理人 */
    private String agent;

    /** 品名 */
    private String cargoName;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 航班号 */
    private String flightNo1;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date flightDate1;

    /** 危险品编码 */
    private String dangerCode;

    /** 特货代码 */
    private String specialCargoCode1;


    /** 货物类型 0普货 1特货 2危险品 */
    private Integer cargoType;

    /** 是否特货 0否 1是  */
    private Integer isSpecial;

    /** 最终安检提交状态 0 未通过 1 通过  -1退回 -2不合格 */
    private Integer securitySubmit;

    /** 物流端安检提交状态 0 未提交 1 物流提交 2货站提交 3合格 -1退回 -2不合格 */
    private Integer securitySubmitWl;

    /** 安检申报pdf地址 */
    private String securityUrl;

    /** 是否与申报一致(审单结果) 0 否(退回) 1 是(符合运输) */
    private Integer declarationConsistent;

    /** 是否审核 0 否 1 是  */
    private Integer isExamine;

    /** 用于区分该条数据来自主单表还是来自新增安检申报表 */
    private Integer type;

    /** 是否可以重复提交品名清单 1显示 0不显示 */
    private Integer isRepeatSubmit;
}
