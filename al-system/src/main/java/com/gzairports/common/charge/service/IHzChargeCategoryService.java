package com.gzairports.common.charge.service;


import com.gzairports.common.charge.domain.HzChargeCategory;

import java.util.List;

/**
 * 收费货物类别Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-13
 */
public interface IHzChargeCategoryService 
{
    /**
     * 查询收费货物类别
     * 
     * @param id 收费货物类别主键
     * @return 收费货物类别
     */
    public HzChargeCategory selectHzChargeCategoryById(Long id);

    /**
     * 查询收费货物类别列表
     * 
     * @param hzChargeCategory 收费货物类别
     * @return 收费货物类别集合
     */
    public List<HzChargeCategory> selectHzChargeCategoryList(HzChargeCategory hzChargeCategory);

    /**
     * 新增收费货物类别
     * 
     * @param hzChargeCategory 收费货物类别
     * @return 结果
     */
    public int insertHzChargeCategory(HzChargeCategory hzChargeCategory);

    /**
     * 修改收费货物类别
     * 
     * @param hzChargeCategory 收费货物类别
     * @return 结果
     */
    public int updateHzChargeCategory(HzChargeCategory hzChargeCategory);

    /**
     * 删除收费货物类别信息
     *
     * @param id 收费货物类别主键
     * @return 结果
     */
    public int deleteHzChargeCategoryById(Long id);
}
