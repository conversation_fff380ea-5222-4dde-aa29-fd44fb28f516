<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.departure.mapper.BookingRecordMapper">
    <update id="updateStatus">
        update wl_dep_booking set status = #{status} where id = #{bookingId}
    </update>
    <update id="updateStatusBatch">
        update wl_dep_booking set  status = #{status} where id in
        <foreach collection="bookingIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>