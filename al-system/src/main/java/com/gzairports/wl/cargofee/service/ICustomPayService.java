package com.gzairports.wl.cargofee.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.wl.cargofee.domain.CustomPay;
import com.gzairports.wl.cargofee.domain.query.PayRecordQuery;

import java.util.List;

/**
 * 客户支付流水Service接口
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
public interface ICustomPayService extends IService<CustomPay> {

    /**
     * 查询客户支付流水数据
     * @param query 查询参数
     * @return 客户支付流水数据
     */
    List<CustomPay> selectList(PayRecordQuery query);
}
