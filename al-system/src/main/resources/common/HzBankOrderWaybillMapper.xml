<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.bank.mapper.HzBankOrderWaybillMapper">

    <select id="selectByOrderNo" resultType="com.gzairports.common.bank.domain.HzBankOrderWaybill">
        select id, order_no, waybill_id, waybill_code, type
        from hz_bank_order_waybill
        <where>
            <if test="orderNo != null and orderNo != ''">
                and order_no = #{orderNo}
            </if>
            <if test="waybillId != null and waybillId != ''">
                and waybill_id = #{waybillId}
            </if>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
        </where>
        limit 1
    </select>
</mapper>