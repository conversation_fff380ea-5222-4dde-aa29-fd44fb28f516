package com.gzairports.hz.business.departure.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 站坪交接表
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
@Data
@TableName("hz_dep_handover")
public class HzDepHandover {

    /** 主键id */
    private Long id;

    /** 业务航班号 */
    private Long flightLoadId;

    /** 板车数量 */
    private Integer uldCount;

    /** 业务袋数 */
    private Integer bagCount;

    /** 是否有机长通知单 0 否 1 是 */
    private Integer captainNotice;

    /** 站坪交接人签字（图片） */
    private String signUrl;

    /** 交接时间 */
    private Date handoverTime;
}
