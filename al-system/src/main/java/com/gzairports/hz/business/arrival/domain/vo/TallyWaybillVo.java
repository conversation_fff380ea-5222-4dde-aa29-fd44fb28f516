package com.gzairports.hz.business.arrival.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 航班文件运单理货参数
 * <AUTHOR>
 * @date 2024-07-18
 */
@Data
public class TallyWaybillVo {

    /** 理货id */
    private Long tallyId;

    /** 航段id */
    private Long legId;

    /** 航班id */
    private Long flightId;

    /** 运单号 */
    private String waybillCode;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 舱单件数 */
    private Integer cabinQuantity;

    /** 舱单重量 */
    private BigDecimal cabinWeight;

    /** 特货代码 */
    private String specialCargoCode1;

    /** 理货次数 */
    private Integer tallyNum;

    /** 累计理货件数 */
    private Integer totalTallyQuantity;

    /** 累计理货重量 */
    private BigDecimal totalTallyWeight;

    /** 航班号 */
    private String flightNo;

    private String flightDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date execDate;

    /** 理货件数 */
    private Integer tallyQuantity;

    /** 理货重量 */
    private BigDecimal tallyWeight;

    /** 理货人 */
    private String username;

    /** 理货不正常情况 */
    private String abnormal;

    /** 仓库 */
    private String store;

    /** 库位 */
    private String locator;

    /** 板箱号 */
    private String uld;

    /** 代理人名称 */
    private String deptName;

    /** 理货历史记录 */
    private List<TallyHistoryVo> historyVos;
}
