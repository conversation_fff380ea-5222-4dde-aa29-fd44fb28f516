package com.gzairports.common.business.arrival.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by david on 2024/12/6
 * <AUTHOR>
 */
@Data
public class NotBillListVo {

    /** 理货id */
    private Long tallyId;

    /** 录单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;

    /** 运单号 */
    private String waybillCode;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 可办单重量 */
    private BigDecimal canBill;
}
