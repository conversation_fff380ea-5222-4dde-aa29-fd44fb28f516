package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BaseCargoDanger;
import com.gzairports.common.basedata.domain.query.CargoDangerQuery;
import com.gzairports.common.basedata.service.ICargoDangerService;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 危险品管理Controller
 *
 * <AUTHOR>
 * @date 2024-06-14
 */
@RestController
@RequestMapping("/base/cargoDanger")
@Api(tags = "危险品管理")
public class CargoDangerController extends BaseController {

    @Autowired
    private ICargoDangerService dangerService;

    /**
     * 查询危险品管理列表
     */
//    @PreAuthorize("@ss.hasPermi('base:cargoDanger:dangerList')")
    @GetMapping("/dangerList")
    @ApiOperation(value = "查询危险品管理列表")
    public TableDataInfo dangerList(CargoDangerQuery query)
    {
        startPage();
        List<BaseCargoDanger> list = dangerService.selectDangerList(query);
        return getDataTable(list);
    }

    /**
     * 新增危险品管理
     */
    @PreAuthorize("@ss.hasPermi('base:cargoDanger:addDanger')")
    @Log(title = "新增危险品管理", businessType = BusinessType.INSERT)
    @PostMapping("/addDanger")
    @ApiOperation(value = "新增危险品管理")
    public AjaxResult addDanger(@RequestBody BaseCargoDanger danger)
    {
        return toAjax(dangerService.addDanger(danger));
    }

    /**
     * 修改危险品管理
     */
    @PreAuthorize("@ss.hasPermi('base:cargoDanger:editDanger')")
    @Log(title = "修改危险品管理", businessType = BusinessType.UPDATE)
    @PostMapping("/editDanger")
    @ApiOperation(value = "修改危险品管理")
    public AjaxResult editDanger(@RequestBody BaseCargoDanger danger)
    {
        return toAjax(dangerService.editDanger(danger));
    }

    /**
     * 删除危险品管理
     */
    @PreAuthorize("@ss.hasPermi('base:cargoDanger:delDanger')")
    @Log(title = "删除危险品管理", businessType = BusinessType.UPDATE)
    @GetMapping("/delDanger/{ids}")
    @ApiOperation(value = "删除危险品管理")
    public AjaxResult delDanger(@PathVariable("ids") Long[] ids)
    {
        return toAjax(dangerService.delDanger(ids));
    }
}
