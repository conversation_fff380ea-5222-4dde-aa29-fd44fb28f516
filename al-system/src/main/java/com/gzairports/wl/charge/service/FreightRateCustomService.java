package com.gzairports.wl.charge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.wl.charge.domain.FreightRateAirItem;
import com.gzairports.wl.charge.domain.FreightRateCustom;
import com.gzairports.wl.charge.domain.FreightRateCustomItem;
import com.gzairports.wl.charge.domain.query.FreightRateCustomQuery;
import com.gzairports.wl.charge.domain.vo.FreightRateCustomVO;
import com.gzairports.wl.charge.domain.vo.FreightRateItemVO;
import com.gzairports.wl.charge.domain.vo.RateItemImportVo;

import java.util.List;

/**
 * 客户运价Service接口
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
public interface FreightRateCustomService extends IService<FreightRateCustom> {

    /**
     * 查询客户运价列表
     *
     * @param query 查询参数
     * @return 客户运价列表
     */
    List<FreightRateCustomVO> selectList(FreightRateCustomQuery query);

    /**
     * 查询客户运价详情
     *
     * @param id 客户运价主键
     * @return 客户运价
     */
    FreightRateCustomVO selectRateById(Long id);

    /**
     * 新增客户运价
     *
     * @param custom 客户运价
     * @return 结果
     */
    Long insertFreightRateCustom(FreightRateCustomVO custom);

    /**
     * 修改客户运价
     *
     * @param vo 客户运价
     * @return 结果
     */
    int updateFreightRateCustom(FreightRateCustomVO vo);

    /**
     * 查看运价条目
     *
     * @param id 客户运价id
     * @return 客户运价条目集合
     */
    List<FreightRateItemVO> selectRateItemList(Long id);

    /**
     * 删除客户运价
     *
     * @param id 客户运价主键
     * @return 结果
     */
    int deleteFreightRateCustom(Long id);

    /**
     * 根据选中的id查询客户运价列表
     *
     * @param ids 查询参数
     * @return 客户运价列表
     */
    List<FreightRateCustomVO> selectListByIds(List<Long> ids);

    /**
     * 导入航司运价条目
     * @param list 文件数据
     * @param rateId 导入所需数据
     * @param updateSupport 导入所需数据
     * @return 结果
     */
    Long importItem(List<FreightRateCustomItem> list, Long rateId, boolean updateSupport);

    int delCustomAir(Long id);
}
