package com.gzairports.wl.departure.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 分单费用项目表
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@Data
@TableName("wl_dep_hawb_item")
public class HawbItem {

    /** 主键id */
    private Long id;

    /** 分单id */
    private Long hawbId;

    /** 收费项目id */
    private Long chargeItemId;

    /** 分单运单号 */
    private String waybillCode;

    /** 费用类型 */
    private String costType;

    /** 结算类型 */
    private String settleType;

    /** 结算方式 */
    private String settleMethod;

    /** 运价类型 */
    private String freightType;

    /** 费率 */
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal rate;

    /** 旧费率 */
    @JsonSerialize(using = ToStringSerializer.class)
    @TableField(exist = false)
    private BigDecimal oldRate;

    /** 计费金额 */
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal charging;

    /** 旧计费金额 */
    @JsonSerialize(using = ToStringSerializer.class)
    @TableField(exist = false)
    private BigDecimal oldCharging;

    /** 运输保险价值 */
    @TableField(exist = false)
    private BigDecimal transportInsureValue;

    /** 所属单位 */
    private Long deptId;

    /** 是否删除 0 否 1 是 */
    private Integer isDel;

    /** 费用总计 */
    @TableField(exist = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal costSum;

    /** 计费重量 */
    @TableField(exist = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal chargeWeight;

    /** 重量 */
    @TableField(exist = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal weight;

    /** 体积 */
    @TableField(exist = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal volume;

    /** 件数 */
    @TableField(exist = false)
    private Integer quantity;

    /** 客户简称 */
    @TableField(exist = false)
    private String shipperAbb;
}
