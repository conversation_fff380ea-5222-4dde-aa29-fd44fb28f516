<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.departure.mapper.TransferMapper">

    <select id="selectListByQuery" resultType="com.gzairports.wl.departure.domain.vo.TransferVo">
        select wdt.id, sd.dept_name as agent, wdt.submit_time, wdt.submitter, wdt.store_keeper, wdt.status, wdt.type, wdt.date, wdt.remark
        from wl_dep_transfer wdt left join sys_dept sd on sd.dept_id = wdt.dept_id
        <where>
                and wdt.is_del = 0 and wdt.dept_id = #{deptId}
            <if test="status != null and status != ''">
                and wdt.status = #{status}
            </if>
            <if test="startTime != null">
                and wdt.submit_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and wdt.submit_time <![CDATA[<=]]> #{endTime}
            </if>
        </where>
    </select>
    <select id="selectByIds" resultType="com.gzairports.wl.departure.domain.vo.TransferVo">
        select wdt.id, wdt.num, sd.dept_name_abb as agent, wdt.submit_time, wdt.submitter, wdt.store_keeper, wdt.status, wdt.type, wdt.date, wdt.remark,
               wdt.flight_no, wdt.des, wdt. loading, wdt.unloading, wdt.waybill_code, wdt.quantity, wdt.weight, wdt.pack_way, wdt.cargo_code,
               wdt.cargo_name, wdt.cargo_condition, wdt.delivery, wdt.delivery_time, wdt.delivery_remark, wdt.first_receiver, wdt.first_receiver_time,
               wdt.first_receiver_time, wdt.first_receiver_remark, wdt.second_receiver, wdt.second_receiver_time, wdt.second_receiver_remark, wdt.third_receiver,
               wdt.third_receiver_time, wdt.third_receiver_remark
        from wl_dep_transfer wdt left join sys_dept sd on sd.dept_id = wdt.dept_id
        where wdt.is_del = 0 and wdt.dept_id = #{deptId} and wdt.id = #{id}
    </select>
    <select id="selectPrintTransferData"
            resultType="com.gzairports.common.business.departure.domain.vo.PrintTransferVo">
        select sd.dept_name, wdt.date, sd.seal_url from wl_dep_transfer wdt
            left join sys_dept sd on wdt.dept_id = sd.dept_id
        where wdt.id = #{id}
    </select>
</mapper>