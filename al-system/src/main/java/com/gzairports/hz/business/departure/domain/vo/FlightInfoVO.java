package com.gzairports.hz.business.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.models.auth.In;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 入库排班返回数据
 * @date 2024-06-24
 * <AUTHOR>
 */
@Data
public class FlightInfoVO {

    /** 航班主键ID */
    private Long flightId;

    /** 航班号 */
    private String flightNo;

    /** 机型 */
    private String craftType;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date execDate;

    /** 经停站中文 */
    private String shareviaCn;

    /** 到达机场名称 */
    private String terminalStationCn;

    /** 计飞 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startSchemeTakeoffTime;

    /** 最早入库时间 */
    private String startStorageTime;

    /** 最晚入库时间 */
    private String endStorageTime;

    /** 航班状态 */
    private String flightState;

    /** 航班特殊状态 */
    private String abnormalState;

    /** 航班对外发布状态 */
    private String providingState;

    /** 是否发送短信 */
    private Integer isSms;

    /** 航班是否取消 */
    private Integer status;

    /** 是否关闭航班 */
    private Integer isComp;
}
