package com.gzairports.common.basedata.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gzairports.common.basedata.domain.BaseCurrency;
import com.gzairports.common.basedata.domain.query.CurrencyQuery;
import com.gzairports.common.basedata.mapper.CurrencyMapper;
import com.gzairports.common.basedata.service.ICurrencyService;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.exception.ServiceException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.bean.BeanValidators;
import com.gzairports.wl.departure.domain.Consign;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.validation.Validator;
import java.util.List;

/**
 * 币种管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Service
public class CurrencyServiceImpl implements ICurrencyService {

    private static final Logger log = LoggerFactory.getLogger(WeightServiceImpl.class);

    @Autowired
    private Validator validator;

    @Autowired
    private CurrencyMapper currencyMapper;

    /**
     * 查询币种管理列表
     *
     * @param query 查询参数
     * @return 币种管理列表
     */
    @Override
    public List<BaseCurrency> selectCurrencyList(CurrencyQuery query) {
        return currencyMapper.selectCurrencyList(query);
    }

    /**
     * 导入币种
     *
     * @param currencies 币种列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importCurrency(List<BaseCurrency> currencies, boolean updateSupport) {
        if (StringUtils.isNull(currencies) || currencies.size() == 0)
        {
            throw new ServiceException("导入币种数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (BaseCurrency currency : currencies) {
            try {
                // 验证是否存在相同的币种代码
                BaseCurrency baseCurrency = currencyMapper.selectCurrencyByCode(currency.getCurrency());
                if (StringUtils.isNull(baseCurrency)) {
                    BeanValidators.validateWithException(validator, currency);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    currency.setCreateBy(loginUser.getUsername());
                    currency.setCreateTime(DateUtils.getNowDate());
                    currencyMapper.insertCurrency(currency);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、币种代码 " + currency.getCurrency() + " 导入成功");
                }
                else if (updateSupport) {
                    BeanValidators.validateWithException(validator, currency);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    currency.setId(baseCurrency.getId());
                    currency.setUpdateBy(loginUser.getUsername());
                    currency.setUpdateTime(DateUtils.getNowDate());
                    currencyMapper.updateCurrency(currency);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、币种代码 " + currency.getCurrency() + " 更新成功");
                }
                else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、币种代码 " + currency.getCurrency() + " 已存在");
                }
            }catch (Exception e){
                failureNum++;
                String msg = "<br/>" + failureNum + "、币种代码 " + currency.getCurrency() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 新增币种
     *
     * @param currency 币种
     * @return 结果
     */
    @Override
    public int addCurrency(BaseCurrency currency) {
        BaseCurrency baseCurrency1 = currencyMapper.selectOne(new QueryWrapper<BaseCurrency>()
                .eq("currency", currency.getCurrency())
                .eq("is_del", 0));

        BaseCurrency baseCurrency2 = currencyMapper.selectOne(new QueryWrapper<BaseCurrency>()
                .eq("currency_unit", currency.getCurrencyUnit())
                .eq("is_del", 0));

        BaseCurrency baseCurrency3 = currencyMapper.selectOne(new QueryWrapper<BaseCurrency>()
                .eq("currency_symbol", currency.getCurrencySymbol())
                .eq("is_del", 0));

        if (StringUtils.isNotNull(baseCurrency1)
                || StringUtils.isNotNull(baseCurrency2)
                || StringUtils.isNotNull(baseCurrency3)){
            throw new CustomException("币种信息重复");
        }

        LoginUser loginUser = SecurityUtils.getLoginUser();
        currency.setCreateBy(loginUser.getUsername());
        currency.setCreateTime(DateUtils.getNowDate());
        return currencyMapper.insertCurrency(currency);
    }

    /**
     * 修改币种
     *
     * @param currency 币种
     * @return 结果
     */
    @Override
    public int editCurrency(BaseCurrency currency) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        currency.setUpdateBy(loginUser.getUsername());
        currency.setUpdateTime(DateUtils.getNowDate());
        return currencyMapper.updateCurrency(currency);
    }

    /**
     * 删除币种
     *
     * @param ids 币种id集合
     * @return 结果
     */
    @Override
    public int delCurrency(Long[] ids) {
        return currencyMapper.delCurrency(ids);
    }
}
