package com.gzairports.hz.business.arrival.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 运单明细理货数据
 * <AUTHOR>
 * @date 2024-07-25
 */
@Data
public class LhVo {

    /** 运单件数 */
    private Integer quantity;

    /** 运单重量 */
    private BigDecimal weight;

    /** 理货件数 */
    private Integer lhQuantity;

    /** 理货重量 */
    private BigDecimal lhWeight;

    /** 理货人 */
    private String username;

    /** 仓库 */
    private String store;

    /** 库位 */
    private String locator;

    /** 板箱号 */
    private String uld;

    /** 理货时间 */
    private Date tallyTime;

    /** 不正常情况 */
    private String abnormal;
}
