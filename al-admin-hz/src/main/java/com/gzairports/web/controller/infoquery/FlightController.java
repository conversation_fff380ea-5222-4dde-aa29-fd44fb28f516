package com.gzairports.web.controller.infoquery;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.infoquery.domain.Flight;
import com.gzairports.common.infoquery.domain.query.FlightQuery;
import com.gzairports.common.infoquery.service.IFlightService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 航班信息Controller
 *
 * <AUTHOR>
 * @date 2024-05-08
 */
@RestController
@RequestMapping("/info/flight")
@Api(tags = "航班信息")
public class FlightController extends BaseController {

    @Autowired
    private IFlightService flightService;

    /**
     * 获取航班列表
     */
//    @PreAuthorize("@ss.hasPermi('info:flight:list')")
    @GetMapping("/list")
    @ApiOperation(value = "获取航班列表")
    public TableDataInfo list(FlightQuery query)
    {
        startPage();
        List<Flight> list = flightService.selectFlightList(query);
        return getDataTable(list);
    }

    /**
     * 新增航班
     */
    @PreAuthorize("@ss.hasPermi('info:flight:add')")
    @PostMapping("/add")
    @ApiOperation(value = "新增航班")
    public AjaxResult add(@RequestBody Flight flight)
    {
       return toAjax(flightService.add(flight));
    }

    /**
     * 修改航班
     * */
    @PreAuthorize("@ss.hasPermi('info:flight:update')")
    @PostMapping("/update")
    @ApiOperation(value = "修改航班")
    public AjaxResult update(@RequestBody Flight flight)
    {
        return toAjax(flightService.update(flight));
    }


}
