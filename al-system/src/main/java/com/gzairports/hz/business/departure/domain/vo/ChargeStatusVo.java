package com.gzairports.hz.business.departure.domain.vo;

import com.gzairports.common.business.departure.domain.CostDetail;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 运单明细查询收费数据
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
public class ChargeStatusVo {

    /** 缴费状态 0 未授权支付 1 已授权支付 2 已结算 */
    private Integer payStatus;

    /** 预授权费用 */
    private BigDecimal totalPay;

    /** 结算总费用 */
    private BigDecimal totalSettle;

    /** 结算时间 */
    private Date settleTime;

    /** 预授权支付时间 */
    private Date payTime;

    private Long deptId;

    /** 预授权费用明细 */
    List<CostDetail> payList;

    /** 结算费用明细 */
    List<CostDetail> settleList;
}
