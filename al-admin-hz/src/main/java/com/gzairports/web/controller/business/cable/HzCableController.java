package com.gzairports.web.controller.business.cable;

import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.hz.business.cable.domain.HzCable;
import com.gzairports.hz.business.cable.domain.query.GenerateCableQuery;
import com.gzairports.hz.business.cable.domain.query.HzCableQuery;
import com.gzairports.hz.business.cable.domain.vo.CableUldVo;
import com.gzairports.hz.business.cable.service.IHzCableService;
import com.gzairports.hz.business.departure.domain.query.CableMawbQuery;
import com.gzairports.hz.business.departure.domain.vo.CableMawbVo;
import com.gzairports.hz.business.departure.domain.vo.PullCargoVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 电报数据Controller
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
@RestController
@RequestMapping("/bus/cable")
@Api(tags = "电报数据")
public class HzCableController extends BaseController
{
    @Autowired
    private IHzCableService hzCableService;

    /**
     * 查询电报数据列表
     */
//    @PreAuthorize("@ss.hasPermi('bus:cable:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询电报数据列表")
    public TableDataInfo list(HzCableQuery query)
    {
        startPage();
        List<HzCable> list = hzCableService.selectHzCableList(query);
        return getDataTable(list);
    }

    /**
     * 获取电报数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('bus:cable:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取电报数据详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(hzCableService.selectHzCableById(id));
    }

    /**
     * 新增电报数据
     */
    @PreAuthorize("@ss.hasPermi('bus:cable:add')")
    @Log(title = "电报数据", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ApiOperation(value = "新增电报数据（发送）")
    public AjaxResult add(@RequestBody HzCable hzCable)
    {
        return toAjax(hzCableService.insertHzCable(hzCable));
    }

    /**
     * 修改电报数据
     */
    @PreAuthorize("@ss.hasPermi('bus:cable:edit')")
    @Log(title = "电报数据", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ApiOperation(value = "修改电报数据")
    public AjaxResult edit(@RequestBody HzCable hzCable)
    {
        return toAjax(hzCableService.updateHzCable(hzCable));
    }

    /**
     * 删除电报数据
     */
    @PreAuthorize("@ss.hasPermi('bus:cable:remove')")
    @Log(title = "电报数据", businessType = BusinessType.DELETE)
	@GetMapping("/remove/{id}")
    @ApiOperation(value = "删除电报数据")
    public AjaxResult remove(@PathVariable("id") Long id)
    {
        return toAjax(hzCableService.deleteHzCableById(id));
    }

    /**
     * 报文数据收集查询运单信息
     */
    @PreAuthorize("@ss.hasPermi('bus:cable:waybillList')")
    @PostMapping("/waybillList")
    @ApiOperation(value = "报文数据收集查询运单信息")
    public AjaxResult waybillList(@RequestBody CableMawbQuery query)
    {
        List<CableMawbVo> list = hzCableService.selectWaybillList(query);
        return AjaxResult.success(list);
    }

    /**
     * 出港拉货报文查询
     */
//    @PreAuthorize("@ss.hasPermi('bus:cable:pullCargoList')")
    @PostMapping("/pullCargoList")
    @ApiOperation(value = "出港拉货报文查询")
    public TableDataInfo pullCargoList(@RequestBody CableMawbQuery query)
    {
        startPage();
        List<PullCargoVo> list = hzCableService.selectPullCargoList(query);
        return getDataTable(list);
    }

    /**
     * 生成报文
     */
    @PreAuthorize("@ss.hasPermi('bus:cable:generateCable')")
    @PostMapping("/generateCable")
    @ApiOperation(value = "生成报文")
    public AjaxResult generateCable(@RequestBody GenerateCableQuery query)
    {
        return AjaxResult.success(hzCableService.generateCable(query));
    }

    /**
     * 报文数据收集查询集装器信息
     */
    @PreAuthorize("@ss.hasPermi('bus:cable:uldList')")
    @PostMapping("/uldList")
    @ApiOperation(value = "报文数据收集查询集装器信息")
    public AjaxResult uldList(@RequestBody CableMawbQuery query)
    {
        List<CableUldVo> list = hzCableService.uldList(query);
        return AjaxResult.success(list);
    }


    /**
     * 根据报文类型查询对应的模板
     * */
    @PostMapping("/selectTemplateByType")
    @ApiOperation(value = "根据报文类型查询对应的模板")
    public AjaxResult selectTemplateByType(@RequestBody String type)
    {
        return AjaxResult.success(hzCableService.selectTemplateByType(type));
    }


}
