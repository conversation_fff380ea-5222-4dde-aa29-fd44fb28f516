package com.gzairports.web.controller.business.arrival;

import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.hz.business.arrival.domain.query.WaybillQuery;
import com.gzairports.hz.business.arrival.domain.vo.WaybillQueryVo;
import com.gzairports.hz.business.arrival.service.IWaybillQueryService;
import com.gzairports.common.log.domain.WaybillLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 运单查询Controller
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
@RestController
@RequestMapping("/arr/query")
@Api(tags = "运单查询")
public class WaybillQueryController extends BaseController {

    @Autowired
    private IWaybillQueryService queryService;

    /**
     * 运单查询列表
     */
//    @PreAuthorize("@ss.hasPermi('arr:query:list')")
    @GetMapping("/list")
    @ApiOperation(value = "运单查询列表")
    public TableDataInfo list(WaybillQuery query)
    {
        startPage();
        List<WaybillQueryVo> list = queryService.selectList(query);
        return getDataTable(list);
    }

    /**
     * 运单明细查询列表
     */
//    @PreAuthorize("@ss.hasPermi('arr:query:detail')")
    @GetMapping("/detail/{waybillCode}")
    @ApiOperation(value = "运单明细查询列表")
    public AjaxResult detail(@NotNull @PathVariable("waybillCode") String waybillCode)
    {
       return AjaxResult.success(queryService.detail(waybillCode));
    }

    /**
     * 办单列表
     */
//    @PreAuthorize("@ss.hasPermi('arr:query:getBdList')")
    @GetMapping("/getBdList/{waybillCode}")
    @ApiOperation(value = "办单列表")
    public AjaxResult getBdList(@NotNull @PathVariable("waybillCode") String waybillCode)
    {
        return AjaxResult.success(queryService.getBdList(waybillCode));
    }

    /**
     * 提货列表
     */
//    @PreAuthorize("@ss.hasPermi('arr:query:getThList')")
    @GetMapping("/getThList/{waybillCode}")
    @ApiOperation(value = "提货列表")
    public AjaxResult getThList(@NotNull @PathVariable("waybillCode") String waybillCode)
    {
        return AjaxResult.success(queryService.getThList(waybillCode));
    }

    /**
     * 库存列表
     */
//    @PreAuthorize("@ss.hasPermi('arr:query:getKcList')")
    @GetMapping("/getKcList/{waybillCode}")
    @ApiOperation(value = "库存列表")
    public AjaxResult getKcList(@NotNull @PathVariable("waybillCode") String waybillCode)
    {
        return AjaxResult.success(queryService.getKcList(waybillCode));
    }

    /**
     * 运单日志查询列表
     */
//    @PreAuthorize("@ss.hasPermi('arr:query:logList')")
    @GetMapping("/logList/{waybillCode}")
    @ApiOperation(value = "运单日志查询列表")
    public AjaxResult logList(@PathVariable("waybillCode") String waybillCode)
    {
        List<WaybillLog> list = queryService.selectLogList(waybillCode);
        return AjaxResult.success(list);
    }


}
