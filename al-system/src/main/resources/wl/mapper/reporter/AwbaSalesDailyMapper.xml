<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.wl.reporter.mapper.AwbaSalesDailyMapper">

    <select id="selectFieldByName" resultType="com.gzairports.common.business.reporter.domain.ReportDataWaybill">
        select ${fieldNames} from all_report_data
        <where>
            waybill_type = 'AWBA'
            <if test="query.waybillCode != null and query.waybillCode != ''">
                and waybill_code like concat('%', #{query.waybillCode}, '%')
            </if>
            <if test="query.masterWaybillCode != null and query.masterWaybillCode != ''">
                and master_waybill_code like concat('%', #{query.masterWaybillCode}, '%')
            </if>
            <if test="query.carrier1 != null and query.carrier1 != ''">
                and carrier1 like concat('%', #{query.carrier1}, '%')
            </if>
            <if test="query.sourcePort != null and query.sourcePort != ''">
                and source_port = #{query.sourcePort}
            </if>
            <if test="query.cargoName != null and query.cargoName != ''">
                and cargo_name = #{query.cargoName}
            </if>
            <if test="query.desPort != null and query.desPort != ''">
                and des_port = #{query.desPort}
            </if>
            <if test="query.shipperAbb != null and query.shipperAbb != ''">
                and shipper_abb = #{query.shipperAbb}
            </if>
            <if test="query.shipper != null and query.shipper != ''">
                and shipper = #{query.shipper}
            </if>
            <if test="query.consignAbb != null and query.consignAbb != ''">
                and consignAbb = #{query.consignAbb}
            </if>
            <if test="query.consign != null and query.consign != ''">
                and consign = #{query.consign}
            </if>
            <if test="query.startWriteTime != null">
                and write_time <![CDATA[>=]]>  #{query.startWriteTime}
            </if>
            <if test="query.endWriteTime">
                and write_time <![CDATA[<=]]>  #{query.endWriteTime}
            </if>
            <if test="query.writer != null and query.writer != ''">
                and writer = #{query.writer}
            </if>

        </where>
    </select>
    <select id="selectAwbaPageData" resultType="com.gzairports.common.business.reporter.domain.ReportDataWaybill">
        select id from all_report_data
        <where>
            waybill_type = 'AWBA'
            <if test="query.waybillCode != null and query.waybillCode != ''">
                and waybill_code like concat('%', #{query.waybillCode}, '%')
            </if>
            <if test="query.masterWaybillCode != null and query.masterWaybillCode != ''">
                and master_waybill_code like concat('%', #{query.masterWaybillCode}, '%')
            </if>
            <if test="query.carrier1 != null and query.carrier1 != ''">
                and carrier1 like concat('%', #{query.carrier1}, '%')
            </if>
            <if test="query.sourcePort != null and query.sourcePort != ''">
                and source_port = #{query.sourcePort}
            </if>
            <if test="query.cargoName != null and query.cargoName != ''">
                and cargo_name = #{query.cargoName}
            </if>
            <if test="query.desPort != null and query.desPort != ''">
                and des_port = #{query.desPort}
            </if>
            <if test="query.shipperAbb != null and query.shipperAbb != ''">
                and shipper_abb = #{query.shipperAbb}
            </if>
            <if test="query.shipper != null and query.shipper != ''">
                and shipper = #{query.shipper}
            </if>
            <if test="query.consignAbb != null and query.consignAbb != ''">
                and consignAbb = #{query.consignAbb}
            </if>
            <if test="query.consign != null and query.consign != ''">
                and consign = #{query.consign}
            </if>
            <if test="query.startWriteTime != null">
                and write_time <![CDATA[>=]]>  #{query.startWriteTime}
            </if>
            <if test="query.endWriteTime">
                and write_time <![CDATA[<=]]>  #{query.endWriteTime}
            </if>
            <if test="query.writer != null and query.writer != ''">
                and writer = #{query.writer}
            </if>
        </where>
    </select>
    <select id="selectTotal" resultType="com.gzairports.wl.reporter.domain.vo.ReportTotalVo">
        select count(id) as count, sum(quantity) as quantity, sum(weight) as weight, sum(cost_sum) as costSum
        from all_report_data
        where waybill_type = 'AWBA'
        <if test="query.waybillCode != null and query.waybillCode != ''">
            and waybill_code like concat('%', #{query.waybillCode}, '%')
        </if>
        <if test="query.masterWaybillCode != null and query.masterWaybillCode != ''">
            and master_waybill_code like concat('%', #{query.masterWaybillCode}, '%')
        </if>
        <if test="query.carrier1 != null and query.carrier1 != ''">
            and carrier1 like concat('%', #{query.carrier1}, '%')
        </if>
        <if test="query.sourcePort != null and query.sourcePort != ''">
            and source_port = #{query.sourcePort}
        </if>
        <if test="query.cargoName != null and query.cargoName != ''">
            and cargo_name = #{query.cargoName}
        </if>
        <if test="query.desPort != null and query.desPort != ''">
            and des_port = #{query.desPort}
        </if>
        <if test="query.shipperAbb != null and query.shipperAbb != ''">
            and shipper_abb = #{query.shipperAbb}
        </if>
        <if test="query.shipper != null and query.shipper != ''">
            and shipper = #{query.shipper}
        </if>
        <if test="query.consignAbb != null and query.consignAbb != ''">
            and consignAbb = #{query.consignAbb}
        </if>
        <if test="query.consign != null and query.consign != ''">
            and consign = #{query.consign}
        </if>
        <if test="query.startWriteTime != null">
            and write_time <![CDATA[>=]]>  #{query.startWriteTime}
        </if>
        <if test="query.endWriteTime">
            and write_time <![CDATA[<=]]>  #{query.endWriteTime}
        </if>
        <if test="query.writer != null and query.writer != ''">
            and writer = #{query.writer}
        </if>
    </select>
</mapper>