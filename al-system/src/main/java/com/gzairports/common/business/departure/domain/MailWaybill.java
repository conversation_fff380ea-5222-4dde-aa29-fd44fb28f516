package com.gzairports.common.business.departure.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.pdf.PdfPrintAnnotation;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import static com.gzairports.common.pdf.PdfPrintAnnotation.PdfFieldType.IMAGE;
import static com.gzairports.common.pdf.PdfPrintAnnotation.PdfFieldType.TEXT;

/**
 * 邮件单表
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@Data
@TableName("all_air_waybill")
public class MailWaybill {

    /** 主键id */
    private Long id;

    /** 运单号 */
    private String waybillCode;

    /** 运单号 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "waybillCode")
    private String waybillCodeAbb;

    /** 始发站 */
    private String sourcePort;

    /** 目的站 */
    private String desPort;

    /** 始发站 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "sourcePort")
    private String sourcePortStr;

    /** 目的站 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "desPort")
    private String desPortStr;

    /** 品名 */
    private String cargoName;

    /** 大类名称 */
    private String categoryName;

    /** 大类 */
    @TableField(exist = false)
    private String categoryCode;

    /** 邮件品名打印字段 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "cargoName")
    private String cargoNameStr;

    /** 邮件编码 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "cargoCode")
    private String cargoCode;

    /** 托运局名称 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipperAbb")
    private String shipperAbb;

    /** 托运局电话 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipperPhone")
    private String shipperPhone;

    /** 托运局联系人 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipper")
    private String shipper;

    /** 托运局所在地区 */
    private String shipperRegion;

    /** 托运局地址 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "shipperAddress")
    private String shipperAddress;

    /** 接收局名称 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "consignAbb")
    private String consignAbb;

    /** 接收局电话 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "consignPhone")
    private String consignPhone;

    /** 接收局联系人 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "consign")
    private String consign;

    /** 接收局所在地区 */
    private String consignRegion;

    /** 接收局地址 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "consignAddress")
    private String consignAddress;

    /** 承运人1 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "carrier1")
    private String carrier1;

    /** 航班号1 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "flightNo1")
    private String flightNo1;

    /** 航班日期1 */
    private Date flightDate1;

    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "flightDate1")
    private String execDate;

    /** 航班日期2 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "flightInfo")
    private String flightInfo;

    /** 到达站1 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "des1")
    private String des1;

    /** 承运人2 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "carrier2")
    private String carrier2;

    /** 航班号2 */
    private String flightNo2;

    /** 航班日期2 */
    private Date flightDate2;

    /** 航班日期2 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "flightDate2")
    private String flightInfo2;

    /** 到达站2 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "des2")
    private String des2;

    /** 承运人3 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "carrier3")
    private String carrier3;

    /** 航班号3 */
    private String flightNo3;

    /** 航班日期3 */
    private Date flightDate3;

    /** 航班日期2 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "flightDate3")
    private String flightInfo3;

    /** 到达站3 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "des3")
    private String des3;

    /** 件数 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "quantity")
    private Integer quantity;

    /** 重量 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "weight")
    private BigDecimal weight;

    /** 计费重量 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "chargeWeight")
    private BigDecimal chargeWeight;

    /** 体积（M3） */
    private BigDecimal volume;

    /** 加单位的体积 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "volume")
    private String volumeUnit;

    /** 尺寸 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "size")
    private String size;

    /** 费率/公斤 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "tRatePerKg")
    private BigDecimal tRatePerKg;

    /** 费用总额 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "tCostSum")
    private BigDecimal tCostSum;

    /** 费率/公斤 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "pRatePerKg")
    private BigDecimal pRatePerKg;

    /** 费用总额 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "pCostSum")
    private BigDecimal pCostSum;

    /** 费率/公斤 */
    private BigDecimal ratePerKg;

    /** 费用总额 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "costSum")
    private BigDecimal costSum;

    /** 结算费率/公斤 */
    private BigDecimal wRate;

    /** 结算费用总额 */
    private BigDecimal wCostSum;

    /** 应收费率/公斤 */
    private BigDecimal rRate;

    /** 应收费用总额 */
    private BigDecimal rCostSum;

    /** 储运注意事项 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "storageTransportNotes")
    private String storageTransportNotes;

    /** 制单人 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "writer")
    private String writer;

    /** 制单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date writeTime;

    /** 制单时间 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "writeTime")
    private String writeTimeStr;

    /** 制单地点 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "writeLocation")
    private String writeLocation;

    /** 制单单位 */
    private Long deptId;

    /** 电子分单pdf地址 */
    private String pdfUrl;

    /** 安检申报单地址 */
    private String securityUrl;

    /** 是否删除 */
    private Integer isDel;

    /** 运单出港状态 staging: 暂存 been_sent: 已发送 pre_pay: 预授权支付 put_in:货站入库
     * refuse_collect 拒绝收运 been_pre: 已预配
     * been_out: 已出库 been_dep: 已出港 pull_down: 临时拉下 been_return: 已退货 been_voided 已作废
     * order_change: 已换单 been_settle: 已结算
     * 运单进港状态 record_order 录单 tally_comp 理货完成 comp_order 已办单 out_stock 已出库*/
    private String status;

    /** 运单类型  A 主单 M 邮件单*/
    private String waybillType;

    /** 类型 DEP 离港 ARR 进港 */
    private String type;

    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = IMAGE, pdfFieldName = "sealUrl")
    private String sealUrl;

    private BigDecimal payMoney;

    /** 汇总路单号 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "sumCode")
    private String sumCode;

    /** 打印时 始发站航方接收邮件单位取值 */
    @TableField(exist = false)
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "configSourcePort")
    private String configSourcePort;

    /** 代理人 */
    private String agentCompany;

    /**代理人简称*/
    private String agentCode;


    /** 以下字段仅在新增时与安检申报新增的数据同步时使用 */

    /** 最终安检提交状态 0 未提交 1 已提交  -1退回 -2不合格 */
    private Integer securitySubmit;
    /** 物流端安检提交状态 0 未提交 1 物流提交 2货站提交 -1退回 -2不合格 */
    private Integer securitySubmitWl;
    /** 货站选择安检提交时的操作人 */
    private String securitySubmitOperator;
    /** 代理人签章 */
    private String agentSignature;
    /** 托运人签章 */
    private String shipperSignature;
    /** 交货人信息 身份证 */
    private String deliveryIdNo;
    /** 交货人信息 头像拍照 */
    private String deliveryProfilePhoto;
    /** 交货人信息 随附文件拍照 */
    private String deliveryFilePhoto;
    /** 是否与申报一致 0 否 1 是  -> 审单结果 0退回 1符合运输 */
    private Integer declarationConsistent;

    private Integer payStatus;

    private BigDecimal refund;

    private Date settleTime;
    private Date updateTime;

    /** 控制版本号 */
    @TableField("version")
    @Version
    private Integer version;


    /** 收运状态 0未收运 1开始收运 2虚拟收运 3收运结束 */
    private Integer collectStatus;

}
