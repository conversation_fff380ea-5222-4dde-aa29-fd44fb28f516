package com.gzairports.common.basedata.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.basedata.domain.BaseDutySchedule;

import java.util.List;

/**
 * 值班人员信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-01
 */
public interface BaseDutyScheduleMapper extends BaseMapper<BaseDutySchedule>
{
    /**
     * 查询值班人员信息
     * 
     * @param id 值班人员信息ID
     * @return 值班人员信息
     */
    BaseDutySchedule selectHzDutyScheduleById(Long id);

    /**
     * 查询值班人员信息列表
     * 
     * @param baseDutySchedule 值班人员信息
     * @return 值班人员信息集合
     */
    List<BaseDutySchedule> selectHzDutyScheduleList(BaseDutySchedule baseDutySchedule);

    /**
     * 新增值班人员信息
     * 
     * @param baseDutySchedule 值班人员信息
     * @return 结果
     */
    int insertHzDutySchedule(BaseDutySchedule baseDutySchedule);

    /**
     * 修改值班人员信息
     * 
     * @param baseDutySchedule 值班人员信息
     * @return 结果
     */
    int updateHzDutySchedule(BaseDutySchedule baseDutySchedule);

    /**
     * 删除值班人员信息
     * 
     * @param id 值班人员信息ID
     * @return 结果
     */
    int deleteHzDutyScheduleById(Long id);

    /**
     * 批量删除值班人员信息
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteHzDutyScheduleByIds(Long[] ids);
}
