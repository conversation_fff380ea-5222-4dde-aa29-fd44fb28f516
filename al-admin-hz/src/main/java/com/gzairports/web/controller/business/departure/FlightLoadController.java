package com.gzairports.web.controller.business.departure;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.system.domain.SysConfig;
import com.gzairports.common.system.mapper.SysConfigMapper;
import com.gzairports.hz.business.departure.domain.query.*;
import com.gzairports.hz.business.departure.domain.vo.FlightLoadVo;
import com.gzairports.hz.business.departure.domain.vo.ForwardImportVo;
import com.gzairports.hz.business.departure.domain.vo.LoadFlightVo;
import com.gzairports.hz.business.departure.service.IFlightLoadService;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 航班配载Controller
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@RestController
@RequestMapping("/dep/flightLoad")
@Api(tags = "航班配载")
public class FlightLoadController extends BaseController {

    @Autowired
    private IFlightLoadService flightLoadService;

    @Autowired
    private SysConfigMapper sysConfigMapper;

    /**
     * 查询航班配载列表
     */
//    @PreAuthorize("@ss.hasPermi('dep:flightLoad:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询航班配载列表")
    public TableDataInfo list(FlightLoadQuery query)
    {
        //参数配置查找不显示航司
        SysConfig sysConfig = sysConfigMapper.checkConfigKeyUnique("carrier.notDisplayed");
        String[] split = null;
        if(sysConfig!=null){
            String configValue = sysConfig.getConfigValue();
            if(configValue!=null && !"".equals(configValue)){
                split = configValue.split("[,;]");
            }
        }
        startPage();
        List<FlightLoadVo> list = flightLoadService.selectList(query,split);
        return getDataTable(list);
    }

    /**
     * 无货
     */
    @GetMapping("/editState/{id}")
    @ApiOperation(value = "无货")
    public AjaxResult editState(@PathVariable("id") Long id)
    {
        return toAjax(flightLoadService.editState(id));
    }

    /**
     * 配载
     */
    @PreAuthorize("@ss.hasPermi('dep:flightLoad:load')")
    @GetMapping("/load/{id}")
    @ApiOperation(value = "配载")
    public AjaxResult load(@PathVariable("id") Long id)
    {
        ForwardImportVo vo = flightLoadService.load(id);
        return AjaxResult.success(vo);
    }

    /**
     * 新增查询
     */
    @Log(title = "查询航班配载数据", businessType = BusinessType.OTHER)
//    @PreAuthorize("@ss.hasPermi('dep:flightLoad:addQuery')")
    @PostMapping("/addQuery")
    @ApiOperation(value = "新增查询")
    public AjaxResult addQuery(@RequestBody AddQuery query)
    {
        ForwardImportVo vo = flightLoadService.addQuery(query);
        return AjaxResult.success(vo);
    }

    /**
     * 航段查询
     */
    @Log(title = "航段查询", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('dep:flightLoad:legQuery')")
    @PostMapping("/legQuery")
    @ApiOperation(value = "航段查询")
    public AjaxResult legQuery(@RequestBody AddQuery query)
    {
        LoadFlightVo vo = flightLoadService.legQuery(query);
        return AjaxResult.success(vo);
    }

    /**
     * 代运导入
     */
    @Log(title = "查询代运导入", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('dep:flightLoad:forwardImportWaybill')")
    @PostMapping("/forwardImportWaybill")
    @ApiOperation(value = "代运导入")
    public AjaxResult forwardImportWaybill(@RequestBody ForwardImportQuery query)
    {
        ForwardImportVo vo = flightLoadService.forwardImport(query);
        return AjaxResult.success(vo);
    }

    /**
     * 装配
     */
    @Log(title = "装配", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('dep:flightLoad:assemble')")
    @PostMapping("/assemble")
    @ApiOperation(value = "装配")
    public AjaxResult assemble(@RequestBody AssembleQuery query)
    {
        ForwardImportVo vo = flightLoadService.assemble(query);
        return AjaxResult.success(vo);
    }

    /**
     * 更新舱位
     */
    @Log(title = "更新舱位", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('dep:flightLoad:updateCabin')")
    @GetMapping("/updateCabin")
    @ApiOperation(value = "更新舱位")
    public AjaxResult updateCabin(@RequestParam("id") Long id,@RequestParam(value = "cabin",defaultValue = "" ) String cabin)
    {
        return toAjax(flightLoadService.updateCabin(id,cabin));
    }

    /**
     * 装箱
     */
    @Log(title = "装箱", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('dep:flightLoad:pack')")
    @PostMapping("/pack")
    @ApiOperation(value = "装箱")
    public AjaxResult pack(@RequestBody FlightLoadPackQuery query)
    {
        return AjaxResult.success(flightLoadService.packing(query));
    }

    /**
     * 部分装箱
     */
    @Log(title = "部分装箱", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('dep:flightLoad:partPack')")
    @PostMapping("/partPack")
    @ApiOperation(value = "部分装箱")
    public AjaxResult partPack(@RequestBody FlightLoadPackQuery query)
    {
        return AjaxResult.success(flightLoadService.partPack(query));
    }

    /**
     * 放散舱
     */
    @Log(title = "放散舱", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('dep:flightLoad:looseCabin')")
    @PostMapping("/looseCabin")
    @ApiOperation(value = "放散舱")
    public AjaxResult looseCabin(@RequestBody FlightLoadPackQuery query)
    {
        return AjaxResult.success(flightLoadService.looseCabin(query));
    }

    /**
     * 部分放散舱
     */
    @Log(title = "部分放散舱", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('dep:flightLoad:partLooseCabin')")
    @PostMapping("/partLooseCabin")
    @ApiOperation(value = "部分放散舱")
    public AjaxResult partLooseCabin(@RequestBody FlightLoadPackQuery query)
    {
        return AjaxResult.success(flightLoadService.partLooseCabin(query));
    }

    /**
     * 卸下
     */
    @Log(title = "卸下", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('dep:flightLoad:remove')")
    @PostMapping("/remove")
    @ApiOperation(value = "卸下")
    public AjaxResult remove(@RequestBody FlightLoadPackQuery query)
    {
        return AjaxResult.success(flightLoadService.removeByQuery(query));
    }

    /**
     * 航班配载拉下
     */
    @Log(title = "拉下", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('dep:flightLoad:pullDown')")
    @PostMapping("/pullDown")
    @ApiOperation(value = "航班配载拉下")
    public AjaxResult pullDown(@RequestBody FlightLoadPackQuery query){
        return AjaxResult.success(flightLoadService.pullDown(query));
    }

    /**
     * 根据id查询运单库位信息
     */
    @PreAuthorize("@ss.hasPermi('dep:flightLoad:getLocator')")
    @GetMapping("/getLocator/{waybillCode}")
    @ApiOperation(value = "根据id查询运单库位信息")
    public AjaxResult getLocator(@PathVariable("waybillCode") String waybillCode)
    {
        return AjaxResult.success(flightLoadService.getLocator(waybillCode));
    }

    /**
     * 板箱配上
     */
    @Log(title = "板箱配上", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('dep:flightLoad:uldAdd')")
    @PostMapping("/uldAdd")
    @ApiOperation(value = "板箱配上")
    public AjaxResult uldAdd(@RequestBody UldAddQuery query)
    {
        return AjaxResult.success(flightLoadService.uldAdd(query));
    }

    /**
     * 正式舱单
     */
    @PreAuthorize("@ss.hasPermi('dep:flightLoad:formalManifest')")
    @GetMapping("/formalManifest/{id}")
    @ApiOperation(value = "正式舱单")
    public AjaxResult formalManifest(@PathVariable("id") Long id)
    {
        return AjaxResult.success(flightLoadService.formalManifest(id));
    }

    /**
     * 通过id查询正式舱单数据
     */
    @PostMapping("/formalManifestById")
    @ApiOperation(value = "通过id查询正式舱单数据")
    public AjaxResult formalManifestById(@RequestBody FormalManifestIds ids)
    {
        return AjaxResult.success(flightLoadService.formalManifestById(ids));
    }

    /**
     * 舱单打印
     */
    @Log(title = "舱单打印", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('dep:flightLoad:printManifest')")
    @GetMapping("/printManifest/{id}")
    @ApiOperation(value = "舱单打印")
    public void printManifest( @PathVariable("id") Long id) throws Exception
    {
        flightLoadService.printManifest(id);
    }

    /**
     * 打印特货机长通知单
     */
    @Log(title = "打印特货机长通知单", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('dep:flightLoad:printManifest')")
    @GetMapping("/printSpecialCaptain/{id}")
    @ApiOperation(value = "打印特货机长通知单")
    public void printSpecialCaptain(HttpServletResponse response,@PathVariable("id") Long id) throws Exception
    {
        flightLoadService.printCaptain(response,id,"special");
    }

    /**
     * 打印危险品机长通知单
     */
    @Log(title = "打印危险品机长通知单", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('dep:flightLoad:printManifest')")
    @GetMapping("/printDangerousCaptain/{id}")
    @ApiOperation(value = "打印危险品机长通知单")
    public void printDangerousCaptain(HttpServletResponse response,@PathVariable("id") Long id) throws Exception
    {
        flightLoadService.printCaptain(response,id,"dangerous");
    }

    /**
     * 远程打印舱单
     */
    @Log(title = "远程打印舱单", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('dep:flightLoad:remotePrint')")
    @GetMapping("/remotePrint/{id}")
    @ApiOperation(value = "远程打印舱单")
    public void remotePrint(@PathVariable("id") Long id) throws Exception
    {
        flightLoadService.remotePrint(id);
    }

    /**
    * 根据四位航班号查出航司二字码
    * */
    @GetMapping("/getAirLinesByCode")
    @ApiOperation(value = "根据四位航班号查出航司二字码")
    public AjaxResult getAirLinesByCode(@RequestParam("flightNo") String airlinesCode,@RequestParam("flightDate") String flightDate)
    {
        return AjaxResult.success(flightLoadService.getAirLinesByCode(airlinesCode,flightDate));
    }

    /**
     * 根据四位航班号查出航司二字码
     * */
    @GetMapping("/openCreate/{flightId}")
    @ApiOperation(value = "重新开放制单")
    public AjaxResult openCreate(@PathVariable("flightId") Long flightId)
    {
        return toAjax(flightLoadService.openCreate(flightId));
    }

    /**
     * 完成航班
     *
     **/
    @Log(title = "完成航班", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('dep:flightLoad:compFlight')")
    @GetMapping("/compFlight/{flightId}")
    @ApiOperation(value = "完成航班")
    public AjaxResult compFlight(@PathVariable("flightId") Long flightId) throws JsonProcessingException {
        return toAjax(flightLoadService.compFlight(flightId));
    }

    /**
     * 打开航班
     *
     **/
    @Log(title = "打开航班", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('dep:flightLoad:openFlight')")
    @GetMapping("/openFlight/{flightId}")
    @ApiOperation(value = "打开航班")
    public AjaxResult openFlight(@PathVariable("flightId") Long flightId)
    {
        int i = flightLoadService.openFlight(flightId);
        if (i == 403){
            return AjaxResult.error(i,"当前操作没有权限");
        }
        return toAjax(i);
    }

}
