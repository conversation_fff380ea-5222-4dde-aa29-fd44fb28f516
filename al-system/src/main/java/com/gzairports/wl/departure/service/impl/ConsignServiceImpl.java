package com.gzairports.wl.departure.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.BaseCargoCode;
import com.gzairports.common.basedata.mapper.CargoCodeMapper;
import com.gzairports.common.business.departure.domain.Mawb;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.pdf.PdfPrintHelper;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.SerialNumberGenerator;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.sign.Base64;
import com.gzairports.wl.departure.domain.Consign;
import com.gzairports.wl.departure.domain.query.ConsignQuery;
import com.gzairports.wl.departure.domain.query.EditAuthQuery;
import com.gzairports.wl.departure.mapper.ConsignMapper;
import com.gzairports.wl.departure.service.IConsignService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 托运管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-08
 */
@Service
public class ConsignServiceImpl extends ServiceImpl<ConsignMapper, Consign> implements IConsignService {

    @Autowired
    private ConsignMapper consignMapper;

    @Autowired
    private CargoCodeMapper cargoCodeMapper;

    /**
     * 新增托运
     *
     * @param consign 托运数据
     * @return 返回结果
     */
    @Override
    public int add(Consign consign) {
        if (consign.getDeptId() == null){
            Long deptId = SecurityUtils.getHighParentId();
            consign.setDeptId(deptId);
        }
        Consign one = consignMapper.selectOne(new QueryWrapper<Consign>()
                .eq("waybill_code", consign.getWaybillCode()).eq("dept_id", consign.getDeptId())
                .eq("is_del", 0));
        if (one != null){
            throw new CustomException("当前托运书已存在");
        }

//        if (StringUtils.isNotNull(consign.getConsignTime())) {
//            if (consign.getConsignTime().getTime() < System.currentTimeMillis()) {
//                throw new CustomException("托运时间不能早于当前时间");
//            }
//        }
        if (consign.getCode() == null){
            String s = SerialNumberGenerator.generateSerialNumber();
            String substring = s.substring(8);
            consign.setCreateTime(new Date());
            consign.setCode(substring);
        }
        consign.setCreateTime(new Date());
        return consignMapper.insert(consign);
    }

    /**
     * 新增托运
     *
     * @param consign 托运数据
     * @return 返回结果
     */
    @Override
    public int h5Add(Consign consign) {
        Consign one = consignMapper.selectOne(new QueryWrapper<Consign>()
                .eq("waybill_code", consign.getWaybillCode())
                .eq("dept_id", consign.getDeptId())
                .eq("is_del", 0));
        if (one != null){
            throw new CustomException("当前托运书已存在");
        }

//        if (StringUtils.isNotNull(consign.getConsignTime())) {
//            if (consign.getConsignTime().getTime() < System.currentTimeMillis()) {
//                throw new CustomException("托运时间不能早于当前时间");
//            }
//        }
        String s = SerialNumberGenerator.generateSerialNumber();
        String substring = s.substring(8);
        consign.setCreateTime(new Date());
        consign.setCode(substring);
        return consignMapper.insert(consign);
    }

    /**
     * 查询托运数据
     *
     * @param query 查询参数
     * @return 托运列表
     */
    @Override
    public List<Consign> selectList(ConsignQuery query) {
        if (query.getDeptId() == null){
            query.setDeptId(SecurityUtils.getHighParentId());
        }
        List<Consign> consigns = consignMapper.selectConsignList(query);
        for (Consign consign : consigns) {
            if(StringUtils.isNotNull(consign.getIsDanger())){
                consign.setDanger(consign.getIsDanger() == 0 ? "否" : "是");
            }
            consign.setValuable(consign.getIsValuable() == 0 ? "否" : "是");
            consign.setInsure(consign.getInsurance() == 0 ? "否" : "是");
        }
        return consigns;
    }

    /**
     * 柜台录入托运数据
     *
     * @param consign 柜台录入数据
     * @return 结果
     */
    @Override
    public int edit(Consign consign) {
       /* if (StringUtils.isNotNull(consign.getConsignTime())) {
            if (consign.getConsignTime().getTime() < System.currentTimeMillis()) {
                throw new CustomException("托运时间不能早于当前时间");
            }
        }*/
        return consignMapper.updateById(consign);
    }

    /**
     * 更改客户权限
     *
     * @param query 更改参数
     * @return 结果
     */
    @Override
    public int editAuth(EditAuthQuery query) {
        Consign consign = consignMapper.selectById(query.getConsignId());
        consign.setIsEdit(query.getStatus());
        return consignMapper.updateById(consign);
    }

    /**
     * 根据id查询托运详情
     *
     * @param id 托运id
     * @return 托运详情
     */
    @Override
    public Consign getInfo(Long id) {
        Consign consign = consignMapper.selectById(id);
        BaseCargoCode cargoCode = cargoCodeMapper.selectOne(new QueryWrapper<BaseCargoCode>()
                .eq("chinese_name", consign.getCargoName())
                .eq("is_del", 0)
                .last("limit 1"));
        if(cargoCode != null){
            consign.setCargoCode(cargoCode.getCode());
        }
        return consign;
    }

    /**
     * 根据分单运单号查询托运详情
     *
     * @param waybillCode 分单运单号
     * @return 电子托运书pdf地址
     */
    @Override
    public String pdfUrl(String waybillCode) {
        Consign consign = consignMapper.selectOne(new QueryWrapper<Consign>()
                .eq("waybill_code", waybillCode).eq("dept_id", SecurityUtils.getHighParentId())
                .eq("is_del", 0));
        if (consign == null){
            return null;
        }
        return consign.getPdfUrl();
    }

    /**
     * 根据身份证号和目的站查询历史记录
     * @param shipperIdCard 身份证号
     * @param desPort 目的站
     * @return 历史记录
     */
    @Override
    public Consign getHistory(String shipperIdCard, String desPort) {
        List<Consign> list = consignMapper.selectList(new QueryWrapper<Consign>().eq("shipper_id_card",shipperIdCard).eq("des_port",desPort));
        if (CollectionUtils.isEmpty(list)){
            return null;
        }
        List<Consign> sortList = list.stream().filter(e->e.getCreateTime() != null).sorted(Comparator.comparing(Consign::getCreateTime)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sortList)){
            return null;
        }
        return sortList.get(sortList.size() - 1);
    }

    /**
     * 打印托运书
     * @param response 返回流
     * @param id 托运书id
     */
    @Override
    public void printConsign(HttpServletResponse response, Long id) throws Exception {
        Consign consign = consignMapper.selectById(id);
//        if (StringUtils.isNotEmpty(consign.getWaybillCode())){
//            String substring = consign.getWaybillCode().substring(4);
//            String waybillCodeAbb = substring.substring(0, 3) + "-" + substring.substring(3);
//            consign.setWaybillCodeAbb(waybillCodeAbb);
//        }
        if(consign.getInsurance() == 1){
            consign.setInsure("是");
        }
        if(consign.getIsDanger() == 1){
            consign.setDanger("YES");
        }
        if(consign.getIsValuable() == 1){
            consign.setValuable("YES");
        }
//        consign.setInsure(consign.getInsurance() == 0 ? "否":"是");
//        consign.setDanger(consign.getIsDanger() == 0 ? "NO":"YES");
//        consign.setValuable(consign.getIsValuable() == 0 ? "NO":"YES");
        BigDecimal actualWeight = consign.getActualWeight();
        consign.setActualWeightStr(actualWeight.toString());
        if(StringUtils.isNotEmpty(consign.getWaybillCode())){
            String substring = consign.getWaybillCode().substring(4);
            String waybillCodeAbb = substring.substring(0, 3) + "-" + substring.substring(3);
            consign.setMasterWaybillCode(waybillCodeAbb);
        }
        if (consign.getChargeWeight() != null){
            consign.setChargeWeightStr(consign.getChargeWeight().toString());
        }
        if (consign.getInsuranceValue() != null){
            consign.setInsuranceValueStr(consign.getInsuranceValue().toString());
        }
        if (StringUtils.isNotEmpty(consign.getShipperSign())) {
            byte[] bytes = downloadFileFromUrl(consign.getShipperSign());
            String signImage = Base64.encode(bytes);
            consign.setSignImage(signImage);
        }
        ClassPathResource resource = new ClassPathResource("template/consign.pdf");
        if (resource.exists()){
            String path = resource.getPath();
            byte[] bytes = PdfPrintHelper.getPdfDataFromTemplate(consign,path);
            // 设置响应头
            response.reset();
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "inline; filename=example.pdf");
            // 获取输出流并写入字节数据
            response.getOutputStream().write(bytes);
            response.getOutputStream().flush();
        }
    }

    private byte[] downloadFileFromUrl(String urlStr) throws Exception {
        URL url = new URL(urlStr);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(5000);
        connection.setReadTimeout(5000);

        if (connection.getResponseCode() != 200) {
            throw new RuntimeException("Failed to connect, HTTP error code : " + connection.getResponseCode());
        }

        try (InputStream inputStream = connection.getInputStream();
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            return baos.toByteArray();
        }
    }

}
