package com.gzairports.hz.business.departure.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 临时拉下返回参数
 *
 * <AUTHOR>
 * @date 2024-07-08
 */
@Data
public class PullDownVo {

    /** 总单数 */
    private Integer totalNum;

    /** 总配载件数 */
    private Integer totalLoadQuantity;

    /** 总配载重量 */
    private BigDecimal totalLoadWeight;

    /** 总拉下件数 */
    private Integer totalPullQuantity;

    /** 总拉下重量 */
    private BigDecimal totalPullWeight;

    /** 运单拉下列表 */
    private List<PullDownInfoVo> vos;
}
