<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.charge.mapper.HzChargeItemsMapper">
    
    <resultMap type="HzChargeItems" id="HzChargeItemsResult">
        <result property="id"    column="id"    />
        <result property="chargeAbb"    column="charge_abb"    />
        <result property="chargeName"    column="charge_name"    />
        <result property="operationType"    column="operation_type"    />
        <result property="isDefault"    column="is_default"    />
        <result property="isEdit"    column="is_edit"    />
        <result property="remark"    column="remark"    />
        <result property="isDel"    column="is_del"    />
        <result property="startEffectiveTime"    column="start_effective_time"    />
        <result property="endEffectiveTime"    column="end_effective_time"    />
        <result property="isServiceItem"    column="is_service_item"    />
        <result property="roundRule"    column="round_rule"    />
    </resultMap>

    <sql id="selectHzChargeItemsVo">
        select id, charge_abb, charge_name, operation_type, is_default, settle_obj, is_edit, remark, is_del,
               start_effective_time, end_effective_time, is_service_item, status, round_rule from hz_charge_items
    </sql>
    
    <select id="selectHzChargeItemsById" parameterType="Long" resultMap="HzChargeItemsResult">
        <include refid="selectHzChargeItemsVo"/>
        where id = #{id}
    </select>
    <select id="selectHzChargeItemsList" resultType="com.gzairports.common.charge.domain.vo.HzChargeItemsVo">
        select id, charge_abb, charge_name, operation_type, is_default, start_effective_time, end_effective_time,
        is_edit, remark, is_del, status, is_service_item, round_rule
        from hz_charge_items
        <where>
            is_del = 0
            <if test="chargeAbb != null  and chargeAbb != ''"> and charge_abb like concat('%', #{chargeAbb}, '%')</if>
            <if test="chargeName != null  and chargeName != ''"> and charge_name like concat('%', #{chargeName}, '%')</if>
            <if test="operationType != null  and operationType != ''"> and operation_type = #{operationType}</if>
            <if test="status == 0 or status == 1"> and status = #{status} and end_effective_time > NOW()</if>
            <if test="status == 2"> and end_effective_time &lt; NOW()</if>
        </where>
    </select>
    <select id="selectColdItem" resultType="com.gzairports.common.charge.domain.vo.HzChargeItemsVo">
        select hci.id, hci.charge_abb, hci.charge_name
        from hz_charge_items hci
            left join hz_charge_rule hcr on hci.charge_rule_id = hcr.id
        where hci.is_del = 0 and hcr.class_name = 'ColdStorageBillingRule.class'
    </select>
    <select id="selectHzChargeItems" resultType="com.gzairports.common.charge.domain.vo.HzItemsVo">
        select id, charge_abb, charge_name
        from hz_charge_items
        <where>
            is_del = 0
            <if test="type != null  and type != ''"> and operation_type = #{type}</if>
<!--            <if test="effectiveTime != null"> and start_effective_time <![CDATA[<=]]> #{effectiveTime} and end_effective_time <![CDATA[>=]]> #{effectiveTime}</if>-->
        </where>
    </select>

    <insert id="insertHzChargeItems" parameterType="HzChargeItems" useGeneratedKeys="true" keyProperty="id">
        insert into hz_charge_items
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="chargeAbb != null">charge_abb,</if>
            <if test="chargeName != null and chargeName != ''">charge_name,</if>
            <if test="operationType != null">operation_type,</if>
            <if test="isDefault != null">is_default,</if>
            <if test="isEdit != null">is_edit,</if>
            <if test="remark != null">remark,</if>
            <if test="isDel != null">is_del,</if>
            <if test="status == 0 or status == 1">status,</if>
            <if test="startEffectiveTime != null">start_effective_time,</if>
            <if test="endEffectiveTime != null">end_effective_time,</if>
            <if test="isServiceItem != null">is_service_item,</if>
            <if test="roundRule != null">round_rule,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="chargeAbb != null">#{chargeAbb},</if>
            <if test="chargeName != null and chargeName != ''">#{chargeName},</if>
            <if test="operationType != null">#{operationType},</if>
            <if test="isDefault != null">#{isDefault},</if>
            <if test="isEdit != null">#{isEdit},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="status == 0 or status == 1">#{status},</if>
            <if test="startEffectiveTime != null">#{startEffectiveTime},</if>
            <if test="endEffectiveTime != null">#{endEffectiveTime},</if>
            <if test="isServiceItem != null">#{isServiceItem},</if>
            <if test="roundRule != null">#{roundRule},</if>
         </trim>
    </insert>

    <update id="updateHzChargeItems" parameterType="HzChargeItems">
        update hz_charge_items
        <trim prefix="SET" suffixOverrides=",">
            <if test="chargeAbb != null">charge_abb = #{chargeAbb},</if>
            <if test="chargeName != null and chargeName != ''">charge_name = #{chargeName},</if>
            <if test="operationType != null">operation_type = #{operationType},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
            <if test="isEdit != null">is_edit = #{isEdit},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="status == 0 or status == 1">status = #{status},</if>
            <if test="startEffectiveTime != null">start_effective_time = #{startEffectiveTime},</if>
            <if test="endEffectiveTime != null">end_effective_time = #{endEffectiveTime},</if>
            <if test="isServiceItem != null">is_service_item = #{isServiceItem},</if>
            <if test="roundRule != null">round_rule = #{roundRule},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteHzChargeItemsById" parameterType="Long">
        update hz_charge_items
        set is_del = 1
        where id = #{id}
    </update>

    <update id="deleteHzChargeItemsByIds" parameterType="String">
        update hz_charge_items
        set is_del = 1
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>