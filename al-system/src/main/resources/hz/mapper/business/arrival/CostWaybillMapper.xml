<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.arrival.mapper.CostWaybillMapper">

    <select id="selectList" resultType="com.gzairports.hz.business.arrival.domain.vo.CostWaybillVo">
        with RankedData as (
        select aipu.serial_no, aaw.waybill_code, aipu.consign, aipu.settle_user, aaw.agent_company, aaw.quantity, aaw.weight,
        aipu.total_cost, aipu.total_quantity as handleQuantity, aipu.total_weight as handleWeight,
        case
        when aipu.pay_method = '现金' or aipu.pay_method = '0' then '现金支付'
        when aipu.pay_method = '线上' or aipu.pay_method = '1' then '月结支付'
        when aipu.pay_method = '2' then '余额支付'
        when aipu.pay_method = '3' then '预授权支付'
        when aipu.pay_method is null then '空'
        end as billingMethod, aaw.cargo_code, aaw.cargo_name,aipu.handle_by, aipu.pick_up_time as handleTime,
        aipu.is_pay, aipu.id, aaw.dept_id,
        ROW_NUMBER() OVER (partition by aaw.waybill_code, aipu.serial_no order by aipu.pick_up_time desc) as rn
        from all_in_pick_up aipu
        left join all_pick_up_waybill apuw on apuw.pick_up_id = aipu.id
        left join hz_arr_record_order haro on apuw.waybill_code = haro.waybill_code
        left join all_air_waybill aaw on aaw.waybill_code = apuw.waybill_code
        where aaw.type = 'ARR'
        <if test="query.waybillCode != null and query.waybillCode != ''">
            and aaw.waybill_code like concat('%',#{query.waybillCode},'%')
        </if>
        <if test="query.startTime != null">
            and aipu.pick_up_time <![CDATA[>=]]> #{query.startTime}
        </if>
        <if test="query.endTime != null">
            and aipu.pick_up_time <![CDATA[<=]]> #{query.endTime}
        </if>
        <if test="(query.settleUserArr != null and query.settleUserArr.length > 0) or (query.settleUserAbbArr != null and query.settleUserAbbArr.length > 0)">
            and
            (
                <if test="query.settleUserArr != null and query.settleUserArr.length > 0">
                     aipu.settle_user in
                    <foreach collection="query.settleUserArr" item="settleUser" open="(" separator="," close=")">
                        #{settleUser}
                    </foreach>
                </if>
                <if test="query.settleUserAbbArr != null and query.settleUserAbbArr.length > 0">
                    <if test="query.settleUserArr != null and query.settleUserArr.length > 0">or</if>
                    aipu.settle_user_abb in
                    <foreach collection="query.settleUserAbbArr" item="settleUserAbb" open="(" separator="," close=")">
                        #{settleUserAbb}
                    </foreach>
                </if>
              )
        </if>
        <if test="query.agent != null and query.agent != ''">
            and aaw.agent_company = #{query.agent}
        </if>
        <if test="query.serialNo != null and query.serialNo != ''">
            and aipu.serial_no = #{query.serialNo}
        </if>
        <if test="query.payMethod != null and query.payMethod != ''">
            and (
            <choose>
                <when test="query.payMethod == '现金支付'">
                    (aipu.pay_method = '现金' or aipu.pay_method = '0')
                </when>
                <when test="query.payMethod == '月结支付'">
                    (aipu.pay_method = '线上' or aipu.pay_method = '1')
                </when>
                <when test="query.payMethod == '余额支付'">
                    aipu.pay_method = '2'
                </when>
                <when test="query.payMethod == '预授权支付'">
                    aipu.pay_method = '3'
                </when>
                <when test='query.payMethod == "空"'>
                    aipu.pay_method is null
                </when>
            </choose>
            )
        </if>
        <if test="query.isCancel != null and query.isCancel != ''">
            and (
            <choose>
                <when test="query.isCancel == '正常'">
                    (aipu.is_pay = '0' or aipu.is_pay = '1')
                </when>
                <when test="query.isCancel == '作废'">
                    (aipu.is_pay = '2')
                </when>
            </choose>
            )
        </if>
        )
        select *
        from RankedData
        where rn = 1
        order by handleTime desc
        <if test="query.pageSize != null and offset != null ">
            limit #{offset}, #{query.pageSize}
        </if>
    </select>
    <select id="chargeExportList" resultType="com.gzairports.hz.business.arrival.domain.vo.ArrChargeImportVo">
        select aipu.id, aipu.serial_no, apuw.tally_id, aaw.waybill_code, aaw.quantity, aaw.weight, aipu.total_weight as handleWeight,aipu.handle_by
        from all_in_pick_up aipu
        left join all_pick_up_waybill apuw on apuw.pick_up_id = aipu.id
        left join hz_arr_record_order haro on apuw.waybill_code = haro.waybill_code
        left join all_air_waybill aaw on aaw.waybill_code = apuw.waybill_code
        where aaw.type = 'ARR'
        <if test="waybillCode != null and waybillCode != ''">
            and aaw.waybill_code = #{waybillCode}
        </if>
        <if test="startTime != null">
            and aipu.pick_up_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            and aipu.pick_up_time <![CDATA[<=]]> #{endTime}
        </if>
        <if test="agent != null and agent != ''">
            and aaw.agent_company = #{agent}
        </if>
        <if test="serialNo != null and serialNo != ''">
            and aipu.serial_no = #{serialNo}
        </if>
    </select>
    <select id="chargeCostExportList"
            resultType="com.gzairports.hz.business.arrival.domain.vo.ArrChargeExportNewVo">
        select aipu.id, aipu.serial_no, aaw.waybill_code, aipu.total_cost,aipu.total_quantity, aipu.total_weight,
               aipu.total_charge_weight, aaw.agent_company, aaw.consign, aipu.pick_up_time, aipu.settle_user
        from all_in_pick_up aipu
        left join all_pick_up_waybill apuw on apuw.pick_up_id = aipu.id
        left join hz_arr_record_order haro on apuw.waybill_code = haro.waybill_code
        left join all_air_waybill aaw on aaw.waybill_code = apuw.waybill_code
        where aaw.type = 'ARR'
        <if test="query.waybillCode != null and query.waybillCode != ''">
            and aaw.waybill_code like concat('%',#{query.waybillCode},'%')
        </if>
        <if test="query.startTime != null">
            and aipu.pick_up_time <![CDATA[>=]]> #{query.startTime}
        </if>
        <if test="query.endTime != null">
            and aipu.pick_up_time <![CDATA[<=]]> #{query.endTime}
        </if>
        <if test="query.settleUserArr != null and query.settleUserArr.length > 0">
            and aipu.settle_user in
            <foreach collection="query.settleUserArr" item="settleUser" open="(" separator="," close=")">
                #{settleUser}
            </foreach>
        </if>
        <if test="query.agent != null and query.agent != ''">
            and aaw.agent_company = #{query.agent}
        </if>
        <if test="query.serialNo != null and query.serialNo != ''">
            and aipu.serial_no = #{query.serialNo}
        </if>
        <if test="query.payMethod != null and query.payMethod != ''">
            and (
            <choose>
                <when test="query.payMethod == '现金支付'">
                    (aipu.pay_method = '现金' or aipu.pay_method = '0')
                </when>
                <when test="query.payMethod == '月结支付'">
                    (aipu.pay_method = '线上' or aipu.pay_method = '1')
                </when>
                <when test="query.payMethod == '余额支付'">
                    aipu.pay_method = '2'
                </when>
                <when test="query.payMethod == '预授权支付'">
                    aipu.pay_method = '3'
                </when>
                <when test='query.payMethod == "空"'>
                    aipu.pay_method is null
                </when>
            </choose>
            )
        </if>
        <if test="query.isCancel != null and query.isCancel != ''">
            and (
            <choose>
                <when test="query.isCancel == '正常'">
                    (aipu.is_pay = '0' or aipu.is_pay = '1')
                </when>
                <when test="query.isCancel == '作废'">
                    (aipu.is_pay = '2')
                </when>
            </choose>
            )
        </if>
    </select>
    <select id="selectListCount" resultType="com.gzairports.hz.business.arrival.domain.vo.CostWaybillVo">
        with RankedData as (
        select aipu.serial_no, aaw.waybill_code,aipu.total_cost,aipu.pick_up_time as handleTime,aipu.is_pay, aipu.id, aaw.dept_id,
        ROW_NUMBER() OVER (partition by aaw.waybill_code, aipu.serial_no order by aipu.pick_up_time desc) as rn
        from all_in_pick_up aipu
        left join all_pick_up_waybill apuw on apuw.pick_up_id = aipu.id
        left join hz_arr_record_order haro on apuw.waybill_code = haro.waybill_code
        left join all_air_waybill aaw on aaw.waybill_code = apuw.waybill_code
        where aaw.type = 'ARR'
        <if test="query.waybillCode != null and query.waybillCode != ''">
            and aaw.waybill_code like concat('%',#{query.waybillCode},'%')
        </if>
        <if test="query.startTime != null">
            and aipu.pick_up_time <![CDATA[>=]]> #{query.startTime}
        </if>
        <if test="query.endTime != null">
            and aipu.pick_up_time <![CDATA[<=]]> #{query.endTime}
        </if>
        <if test="query.settleUserArr != null and query.settleUserArr.length > 0">
            and aipu.settle_user in
            <foreach collection="query.settleUserArr" item="settleUser" open="(" separator="," close=")">
                #{settleUser}
            </foreach>
        </if>
        <if test="query.agent != null and query.agent != ''">
            and aaw.agent_company = #{query.agent}
        </if>
        <if test="query.serialNo != null and query.serialNo != ''">
            and aipu.serial_no = #{query.serialNo}
        </if>
        <if test="query.payMethod != null and query.payMethod != ''">
            and (
            <choose>
                <when test="query.payMethod == '现金支付'">
                    (aipu.pay_method = '现金' or aipu.pay_method = '0')
                </when>
                <when test="query.payMethod == '月结支付'">
                    (aipu.pay_method = '线上' or aipu.pay_method = '1')
                </when>
                <when test="query.payMethod == '余额支付'">
                    aipu.pay_method = '2'
                </when>
                <when test="query.payMethod == '预授权支付'">
                    aipu.pay_method = '3'
                </when>
                <when test='query.payMethod == "空"'>
                    aipu.pay_method is null
                </when>
            </choose>
            )
        </if>
        <if test="query.isCancel != null and query.isCancel != ''">
            and (
            <choose>
                <when test="query.isCancel == '正常'">
                    (aipu.is_pay = '0' or aipu.is_pay = '1')
                </when>
                <when test="query.isCancel == '作废'">
                    (aipu.is_pay = '2')
                </when>
            </choose>
            )
        </if>
        )
        select *
        from RankedData
        where rn = 1
        order by serial_no
    </select>
</mapper>