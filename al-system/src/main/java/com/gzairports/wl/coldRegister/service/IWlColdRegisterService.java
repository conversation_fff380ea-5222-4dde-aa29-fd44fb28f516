package com.gzairports.wl.coldRegister.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.common.charge.domain.vo.HzChargeItemsVo;
import com.gzairports.hz.business.departure.domain.HzColdRegister;
import com.gzairports.hz.business.departure.domain.query.HzColdRegisterQuery;
import com.gzairports.hz.business.departure.domain.vo.ColdQueryVo;
import com.gzairports.hz.business.departure.domain.vo.ColdRegisterVo;
import com.gzairports.hz.business.departure.domain.vo.HzColdRegisterVo;
import com.gzairports.hz.business.departure.domain.vo.TotalChargeVo;

import java.util.List;

public interface IWlColdRegisterService extends IService<HzColdRegister> {

    /**
     * 查询冷藏登记列表
     * @param query 查询参数
     * @return 冷藏登记列表
     */
    HzColdRegisterVo selectList(HzColdRegisterQuery query);

    /**
     * 导出冷藏登记数据
     * @param query 查询条件
     * @return 结果
     */
    List<ColdQueryVo> selectListByQuery(HzColdRegisterQuery query);

    /**
     * 新增冷藏登记
     * @param register 冷藏登记参数
     * @return 结果
     */
    int add(HzColdRegister register);

    /**
     * 根据运单号查询品名
     * @param waybillCode 运单号
     * @param type 进出港类型
     * @return 品名
     */
    HzColdRegister selectCargoName(String waybillCode,String type,Long deptId);

    /**
     * 根据类型以及计费时间计算计费金额
     * @param vo 计费数据
     * @return 结果
     */
    TotalChargeVo countSum(ColdRegisterVo vo);

    /**
     * 查看详情接口
     * @param id 登记id
     * @return 详情
     */
    ColdQueryVo getInfo(Long id);

    List<String> getWaybillCodeByFour(String waybillCode);
}
