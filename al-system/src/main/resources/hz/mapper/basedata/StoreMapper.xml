<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.StoreMapper">
    <select id="storeList" resultType="com.gzairports.common.basedata.domain.BaseStore">
        select id, code, name, locator, remark, create_time from base_store
        <where>
        and is_del = 0
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="code != null  and code != ''"> and code like '%${code}%'</if>
            <if test="name != null and name != ''"> and name like '%${name}%'</if>
            <if test="locator != null and locator != ''"> and locator like '%${locator}%'</if>
        </where>
    </select>

</mapper>