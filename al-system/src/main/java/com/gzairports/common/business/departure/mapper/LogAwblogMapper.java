package com.gzairports.common.business.departure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.business.departure.domain.vo.LogAwblog;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 运单日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-09-25
 */
@Mapper
public interface LogAwblogMapper extends BaseMapper<LogAwblog>
{
    /**
     * 查询运单日志
     * 
     * @param recid 运单日志ID
     * @return 运单日志
     */
    public LogAwblog selectLogAwblogById(Long recid);

    /**
     * 查询运单日志列表
     * 
     * @param logAwblog 运单日志
     * @return 运单日志集合
     */
    public List<LogAwblog> selectLogAwblogList(LogAwblog logAwblog);

}
