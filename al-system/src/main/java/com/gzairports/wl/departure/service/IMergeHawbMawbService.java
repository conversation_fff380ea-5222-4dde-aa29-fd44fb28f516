package com.gzairports.wl.departure.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.wl.departure.domain.MergeHawbMawb;
import com.gzairports.wl.departure.domain.query.CancelMergeQuery;
import com.gzairports.wl.departure.domain.query.MergeHawbQuery;
import com.gzairports.wl.departure.domain.query.MergeQuery;
import com.gzairports.wl.departure.domain.vo.MergeHawbVo;
import com.gzairports.wl.departure.domain.vo.MergeMawbVo;

import java.util.List;

/**
 * 主分单拼单Service接口
 *
 * <AUTHOR>
 * @date 2024-05-23
 */
public interface IMergeHawbMawbService extends IService<MergeHawbMawb> {

    /**
     * 查询主分单拼单列表
     * @param query 查询参数
     * @return 主分单拼单列表
     */
    List<MergeMawbVo> selectMawbList(MergeQuery query);

    /**
     * 根据主单id查询已拼单信息
     * @param mawbId 主单id
     * @return 已拼单列表
     */
    List<MergeHawbVo> selectHawbList(Long mawbId);


    /**
     * 拼单取消
     * @param mawbId 主单id
     * @return 结果
     */
    int cancel(Long mawbId);

    /**
     * 取消拼单
     * @param query 取消参数
     * @return 结果
     */
    int cancelMerge(CancelMergeQuery query);

    /**
     * 根据运单号查询可拼单信息
     * @param query 查询参数
     * @return 可拼单列表
     */
    List<MergeHawbVo> selectCanList(CancelMergeQuery query);

    /**
     * 拼单
     * @param query 拼单参数
     * @return 结果
     */
    int merge(MergeHawbQuery query);
}
