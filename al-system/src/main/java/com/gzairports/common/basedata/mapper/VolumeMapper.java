package com.gzairports.common.basedata.mapper;


import com.gzairports.common.basedata.domain.BaseVolume;
import com.gzairports.common.basedata.domain.query.VolumeUnitQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 多体积单位Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Mapper
public interface VolumeMapper {

    /**
     * 查询体积单位列表
     *
     * @param query 查询参数
     * @return 体积单位列表
     */
    List<BaseVolume> selectVolumeUnitList(VolumeUnitQuery query);

    /**
     * 根据体积单位查询体积单位数据
     *
     * @param volumeUnit 体积单位
     * @return 结果
     */
    BaseVolume selectVolumeByVolumeUnit(String volumeUnit);

    /**
     * 新增体积单位
     *
     * @param volume 体积单位
     * @return 结果
     */
    int insertVolume(BaseVolume volume);

    /**
     * 修改体积单位
     *
     * @param volume 体积单位
     * @return 结果
     */
    int updateVolume(BaseVolume volume);

    /**
     * 删除体积单位
     *
     * @param ids 体积单位id集合
     * @return 结果
     */
    int delVolume(Long[] ids);
}
