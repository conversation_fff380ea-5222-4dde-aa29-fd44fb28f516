package com.gzairports.common.business.departure.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 订舱表
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@Data
@TableName("wl_dep_booking")
public class Booking {

    /** 主键id */
    private Long id;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date flightDate;

    /** 航班号 */
    private String flightNo;

    /** 运单号 */
    private String waybillCode;

    /** 计划起飞 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planTakeoffTime;

    /** 货物品名 */
    private String cargoName;

    /** 机型 */
    private String craftType;

    /** 机号 */
    private String craftNo;

    /** 代理人代码 */
    private String agentCode;

    /** 订舱件数 */
    private Integer quantity;

    /** 特货代码 */
    private String specialCode;

    /** 订舱重量 */
    private BigDecimal weight;

    /** 计费重量 */
    private BigDecimal chargeWeight;

    /** 订舱体积 */
    private BigDecimal volume;

    /** 订舱号 */
    private String bookingNo;

    /** 保障等级 */
    private String ensureLevel;

    /** 舱位等级 */
    private String berthLevel;

    /** 飞机号限制 */
    private String flightLimit;

    /** 状态 */
    private Integer status;

    /** 创建时间 */
    private Date createTime;

    /** 创建人 */
    private String createBy;

    /** 更新时间 */
    private Date updateTime;

    /** 更新人 */
    private String updateBy;

    /** 备注 */
    private String remark;

    /** 是否删除 0 否 1 是 */
    private Integer isDel;

    /** 所属单位 */
    private Long deptId;
}
