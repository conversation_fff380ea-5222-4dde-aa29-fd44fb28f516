package com.gzairports.common.basedata.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gzairports.common.annotation.Excel;
import com.gzairports.common.core.domain.BizEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.time.LocalDate;


/**
 * 值班人员信息对象 hz_duty_schedule
 *
 * <AUTHOR>
 * @date 2025-04-01
 */
@Data
public class BaseDutySchedule extends BizEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Excel(name = "主键")
    private Long id;

    /** 公司名称 */
    @Excel(name = "公司名称")
    private String companyName;

    /** 公司电话 */
    @Excel(name = "公司电话")
    private String companyPhone;

    /**
     * 值班日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate dutyTime;

    /** 值班内容 */
    @Excel(name = "值班内容")
    private String dutyContent;

    /** 值班时长 */
    @Excel(name = "值班时长")
    private String dutyDuration;

    /** 一级值班人姓名 */
    @Excel(name = "一级值班人姓名")
    private String primaryDutyName;

    /** 一级值班人电话 */
    @Excel(name = "一级值班人电话")
    private String primaryDutyPhone;

    /** 二级值班人姓名 */
    @Excel(name = "二级值班人姓名")
    private String secondaryDutyName;

    /** 二级值班人电话 */
    @Excel(name = "二级值班人电话")
    private String secondaryDutyPhone;

    /** 国内货站值班人姓名 */
    @Excel(name = "国内货站值班人姓名")
    private String domesticCargoDutyName;

    /** 国内货站值班人电话 */
    @Excel(name = "国内货站值班人电话")
    private String domesticCargoDutyPhone;

    /** 国际货站值班人姓名 */
    @Excel(name = "国际货站值班人姓名")
    private String internationalCargoDutyName;

    /** 国际货站值班人电话 */
    @Excel(name = "国际货站值班人电话")
    private String internationalCargoDutyPhone;















    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("companyName", getCompanyName())
            .append("dutyDuration", getDutyDuration())
            .append("primaryDutyName", getPrimaryDutyName())
            .append("primaryDutyPhone", getPrimaryDutyPhone())
            .append("secondaryDutyName", getSecondaryDutyName())
            .append("secondaryDutyPhone", getSecondaryDutyPhone())
            .append("domesticCargoDutyName", getDomesticCargoDutyName())
            .append("domesticCargoDutyPhone", getDomesticCargoDutyPhone())
            .append("internationalCargoDutyName", getInternationalCargoDutyName())
            .append("internationalCargoDutyPhone", getInternationalCargoDutyPhone())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
