package com.gzairports.common.basedata.domain;

import com.gzairports.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 国家或地区
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Data
public class BaseRegionCode {

    /** 主键id */
    private Long id;

    /** 国家或地区代码 */
    @Excel(name = "国家或地区代码")
    private String code;

    /** 所属洲 */
    @Excel(name = "所属洲")
    private String continent;

    /** 中文描述 */
    @Excel(name = "中文描述")
    private String remark;

    /** 英文描述 */
    @Excel(name = "英文描述")
    private String englishRemark;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;
}
