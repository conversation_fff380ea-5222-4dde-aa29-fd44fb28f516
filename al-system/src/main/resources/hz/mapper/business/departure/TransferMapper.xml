<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.departure.mapper.TransferMapper">
    <select id="selectCollectWaybillList"
            resultType="com.gzairports.hz.business.departure.domain.vo.HzTransferVo">
        select wdt.id, wdt.num as transferNum, sd.dept_name as agent, wdt.submit_time, wdt.submitter, wdt.store_keeper, wdt.status, wdt.type, wdt.date, wdt.remark
        from wl_dep_transfer wdt left join sys_dept sd on sd.dept_id = wdt.dept_id
        <where>
            wdt.is_del = 0
            <if test="status != null and status != ''">
                and wdt.status = #{status}
            </if>
            <if test="agent != null and agent != ''">
                and (sd.dept_name like CONCAT('%',#{agent},'%') or sd.dept_name_abb like CONCAT('%',#{agent},'%'))
            </if>
            <if test="storeKeeper != null and storeKeeper != ''">
                and wdt.store_keeper = #{storeKeeper}
            </if>
            <if test="status == null or status == ''">
                and wdt.status != 'STAGING'
            </if>
            <if test="startTime != null">
                and wdt.submit_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and wdt.submit_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="transferNum != null">
                and wdt.num like CONCAT('%',#{transferNum},'%')
            </if>
        </where>
    </select>
    <select id="selectByIds" resultType="com.gzairports.hz.business.departure.domain.vo.HzTransferVo">
        select wdt.id, wdt.num, sd.dept_name_abb as agent, wdt.submit_time, wdt.submitter, wdt.store_keeper, wdt.status, wdt.type, wdt.date, wdt.remark,
               wdt.flight_no, wdt.des, wdt. loading, wdt.unloading, wdt.waybill_code, wdt.quantity, wdt.weight, wdt.pack_way, wdt.cargo_code,
               wdt.cargo_name, wdt.cargo_condition, wdt.delivery, wdt.delivery_time, wdt.delivery_remark, wdt.first_receiver, wdt.first_receiver_time,
               wdt.first_receiver_time, wdt.first_receiver_remark, wdt.second_receiver, wdt.second_receiver_time, wdt.second_receiver_remark, wdt.third_receiver,
               wdt.third_receiver_time, wdt.third_receiver_remark
        from wl_dep_transfer wdt left join sys_dept sd on sd.dept_id = wdt.dept_id
        where wdt.is_del = 0 and wdt.id = #{id}
    </select>
    <select id="selectAppList" resultType="com.gzairports.hz.business.departure.domain.vo.HzTransferVo">
        select wdt.id, wdt.num as transferNum, sd.dept_name as agent,
               wdt.submit_time, wdt.submitter, wdt.store_keeper,
               wdt.status as statusStr, wdt.type, wdt.date, wdt.remark
        from wl_dep_transfer wdt left join sys_dept sd on sd.dept_id = wdt.dept_id
        <where>
            wdt.is_del = 0
            <if test="agent != null and agent != ''">
                and (sd.dept_name like CONCAT('%',#{agent},'%') or sd.dept_name_abb like CONCAT('%',#{agent},'%'))
            </if>
            <if test="startTime != null">
                and wdt.submit_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and wdt.submit_time <![CDATA[<=]]> #{endTime}
            </if>
        </where>
    </select>
</mapper>