<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.wl.charge.mapper.ChargeMethodMapper">
    <insert id="insertChargeMethod" parameterType="chargeMethod" useGeneratedKeys="true" keyProperty="id">
        insert into wl_sf_custom_pay_method
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="customerName != null and customerName != ''">customer_name,</if>
            <if test="paymentMethod != null and paymentMethod != ''">payment_method,</if>
            <if test="cycle != null and cycle != ''">cycle,</if>
            <if test="enabled != null">enabled,</if>
            <if test="effectiveTime != null">effective_time,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="customerName != null and customerName != ''">#{customerName},</if>
            <if test="paymentMethod != null and paymentMethod != ''">#{paymentMethod},</if>
            <if test="cycle != null and cycle != ''">#{cycle},</if>
            <if test="enabled != null">#{enabled},</if>
            <if test="effectiveTime != null">#{effectiveTime},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
        </trim>
    </insert>
    <update id="updateChargeMethod" parameterType="chargeMethod">
        update wl_sf_custom_pay_method
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerName != null and customerName != ''">customer_name = #{customerName},</if>
            <if test="paymentMethod != null and paymentMethod != ''">payment_method = #{paymentMethod},</if>
            <if test="cycle != null and cycle != ''">cycle = #{cycle},</if>
            <if test="enabled != null">enabled = #{enabled},</if>
            <if test="effectiveTime != null">effective_time = #{effectiveTime},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="removeChargeMethod">
        update wl_sf_custom_pay_method set is_del = 1 where id = #{id}
    </update>
    <select id="selectList" resultType="com.gzairports.wl.charge.domain.vo.ChargeMethodVO">
        select wcm.id, wcm.customer_name, wcm.cycle, wcm.payment_method, wcm.enabled, wcm.effective_time, sd.dept_name as dept
        from wl_sf_custom_pay_method wcm
        left join sys_dept sd on sd.dept_id = wcm.dept_id
        <where>
            wcm.is_del = 0 and wcm.dept_id = #{deptId}
            <if test="ids != null  and ids.size() > 0"> and wcm.id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="customerName != null  and customerName != ''"> and wcm.customer_name like concat('%',#{customerName},'%')</if>
            <if test="paymentMethod != null and paymentMethod != ''"> and wcm.payment_method = #{paymentMethod}</if>
        </where>
    </select>
    <select id="selectChargeById" resultType="com.gzairports.wl.charge.domain.vo.ChargeMethodVO">
        select wcm.id, wcm.customer_name, wcm.cycle, wcm.payment_method, wcm.enabled, wcm.effective_time, sd.dept_name as dept
        from wl_sf_custom_pay_method wcm
                 left join sys_dept sd on sd.dept_id = wcm.dept_id where wcm.id = #{id}
    </select>
</mapper>