package com.gzairports.common.basedata.mapper;


import com.gzairports.common.basedata.domain.BaseWeight;
import com.gzairports.common.basedata.domain.query.WeightUnitQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 多重量单位Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Mapper
public interface WeightMapper {

    /**
     * 查询重量单位列表
     *
     * @param query 查询参数
     * @return 重量单位列表
     */
    List<BaseWeight> selectWeightUnitList(WeightUnitQuery query);

    /**
     * 根据重量单位查询重量单位数据
     *
     * @param weightUnit 重量单位
     * @return 结果
     */
    BaseWeight selectWeightByWeightUnit(String weightUnit);

    /**
     * 新增重量单位
     *
     * @param weight 重量单位
     * @return 结果
     */
    int insertWeight(BaseWeight weight);

    /**
     * 修改重量单位
     *
     * @param weight 重量单位
     * @return 结果
     */
    int updateWeight(BaseWeight weight);

    /**
     * 删除重量单位
     *
     * @param ids 重量单位id集合
     * @return 结果
     */
    int delWeight(Long[] ids);
}
