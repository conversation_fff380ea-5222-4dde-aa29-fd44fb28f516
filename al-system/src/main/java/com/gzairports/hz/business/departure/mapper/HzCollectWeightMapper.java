package com.gzairports.hz.business.departure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.gzairports.hz.business.departure.domain.HzCollectWeight;
import com.gzairports.hz.business.departure.domain.query.ForwardImportQuery;
import com.gzairports.hz.business.departure.domain.vo.ForwardImportUldVo;
import com.gzairports.hz.business.departure.domain.vo.ForwardImportWaybillVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 过磅记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Mapper
public interface HzCollectWeightMapper extends BaseMapper<HzCollectWeight> {

    /**
     * 查询代运导入板箱
     * @param query 查询条件
     * @return 结果
     */
    List<ForwardImportUldVo> selectCollectList(@Param("query") ForwardImportQuery query);

    /**
     * 根据id集合查询时运板箱
     * @param collectUld 收运板箱uld集合
     * @return 结果
     */
    List<ForwardImportUldVo> selectListByIds(@Param("collectUld") List<String> collectUld);

    /**
     * 根据运单id查询库位信息
     * @param id 运单id
     * @return 结果
     */
    List<HzCollectWeight> selectListByWaybillId(Long id);

    /**
     * 查询代运导入运单
     * @param query 查询条件
     * @return 结果
     */
    List<ForwardImportWaybillVo> selectCollectWaybillList(@Param("query") ForwardImportQuery query);

    /**
     * 根据uld查询收运id
     * @param uld uld号
     * @return 结果
     */
    List<Long> selectCollectIds(String uld);

    List<Long> selectImportDataByCode(Long waybillId);

}
