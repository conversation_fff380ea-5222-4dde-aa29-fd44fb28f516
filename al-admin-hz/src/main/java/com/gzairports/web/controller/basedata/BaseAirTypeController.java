package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BaseAirType;
import com.gzairports.common.basedata.service.IBaseAirTypeService;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 机型管理Controller
 * 
 * <AUTHOR>
 * @date 2024-06-14
 */
@RestController
@RequestMapping("/basedata/type")
@Api(tags = "机型管理")
public class BaseAirTypeController extends BaseController
{
    @Autowired
    private IBaseAirTypeService baseAirTypeService;

    /**
     * 查询机型管理列表
     */
//    @PreAuthorize("@ss.hasPermi('basedata:type:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询机型管理列表")
    public TableDataInfo list(BaseAirType baseAirType)
    {
        startPage();
        List<BaseAirType> list = baseAirTypeService.selectBaseAirTypeList(baseAirType);
        return getDataTable(list);
    }

    /**
     * 导出机型管理列表
     */
    @PreAuthorize("@ss.hasPermi('basedata:type:export')")
    @Log(title = "机型管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出机型管理列表")
    public void export(HttpServletResponse response, BaseAirType baseAirType)
    {
        List<BaseAirType> list = baseAirTypeService.selectBaseAirTypeList(baseAirType);
        ExcelUtil<BaseAirType> util = new ExcelUtil<BaseAirType>(BaseAirType.class);
        util.exportExcel(response, list, "机型管理数据");
    }


    /**
     * 导出机型管理列表模板
     */
    @PreAuthorize("@ss.hasPermi('basedata:type:export')")
    @Log(title = "机型管理模板", businessType = BusinessType.EXPORT)
    @PostMapping("/exportNull")
    @ApiOperation(value = "导出机型管理模板")
    public void exportNull(HttpServletResponse response)
    {
        List<BaseAirType> list = new ArrayList<>();
        ExcelUtil<BaseAirType> util = new ExcelUtil<BaseAirType>(BaseAirType.class);
        util.exportExcel(response, list, "机型管理数据模板");
    }

    /**
     * 导入机型管理
     */
    @PreAuthorize("@ss.hasPermi('base:type:import')")
    @Log(title = "导入机型管理", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    @ApiOperation(value = "导入机型管理")
    public AjaxResult importAirType(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<BaseAirType> util = new ExcelUtil<BaseAirType>(BaseAirType.class);
        List<BaseAirType> types = util.importExcel(file.getInputStream());
        String message = baseAirTypeService.importAirType(types, updateSupport);
        return success(message);
    }

    /**
     * 获取机型管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('basedata:type:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取机型管理详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(baseAirTypeService.selectBaseAirTypeById(id));
    }

    /**
     * 新增机型管理
     */
    @PreAuthorize("@ss.hasPermi('basedata:type:add')")
    @Log(title = "机型管理", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增机型管理")
    public AjaxResult add(@RequestBody BaseAirType baseAirType)
    {
        return toAjax(baseAirTypeService.insertBaseAirType(baseAirType));
    }

    /**
     * 修改机型管理
     */
    @PreAuthorize("@ss.hasPermi('basedata:type:edit')")
    @Log(title = "机型管理", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ApiOperation(value = "修改机型管理")
    public AjaxResult edit(@RequestBody BaseAirType baseAirType)
    {
        return toAjax(baseAirTypeService.updateBaseAirType(baseAirType));
    }

    /**
     * 删除机型管理
     */
    @PreAuthorize("@ss.hasPermi('basedata:type:remove')")
    @Log(title = "机型管理", businessType = BusinessType.DELETE)
	@GetMapping("/remove/{id}")
    @ApiOperation(value = "删除机型管理")
    public AjaxResult remove(@PathVariable Long id)
    {
        return toAjax(baseAirTypeService.deleteBaseAirTypeById(id));
    }
}
