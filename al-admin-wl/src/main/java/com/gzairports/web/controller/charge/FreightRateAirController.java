package com.gzairports.web.controller.charge;

import com.gzairports.common.annotation.Log;
import com.gzairports.wl.charge.domain.FreightRateAir;
import com.gzairports.wl.charge.domain.FreightRateAirItem;
import com.gzairports.wl.charge.domain.query.FreightRateAirQuery;
import com.gzairports.wl.charge.domain.vo.FreightRateAirItemVO;
import com.gzairports.wl.charge.domain.vo.FreightRateAirVO;
import com.gzairports.wl.charge.domain.vo.RateItemImportVo;
import com.gzairports.wl.charge.service.FreightRateAirService;
import com.gzairports.common.annotation.OperLog;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.wl.log.service.WlOperLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 航司运价Controller
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@RestController
@RequestMapping("/freightAir")
@Api(tags = "航司运价接口")
public class FreightRateAirController extends BaseController {
    
    @Autowired
    private FreightRateAirService airService;

    @Autowired
    private WlOperLogService logService;

    /**
     * 查询航司运价列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询航司运价列表")
    public TableDataInfo list(FreightRateAirQuery query){
        startPage();
        List<FreightRateAirVO> list = airService.selectList(query);
        return getDataTable(list);
    }

    /**
     * 导出航司运价列表
     */
    @PreAuthorize("@ss.hasPermi('charge:freightAir:export')")
    @PostMapping("/export")
    @ApiOperation(value = "导出航司运价")
    public void export(HttpServletResponse response, FreightRateAirQuery query){
        List<FreightRateAirVO> list = airService.selectList(query);
        ExcelUtil<FreightRateAirVO> util = new ExcelUtil<FreightRateAirVO>(FreightRateAirVO.class);
        util.exportExcel(response, list, "航司运价");
    }

    /**
     * 导入航司运价条目
     */
    @PreAuthorize("@ss.hasPermi('charge:freightAir:importItem')")
    @OperLog(title = "航司运价", businessType = BusinessType.IMPORT)
    @PostMapping("/importItem")
    @ApiOperation(value = "导入航司运价条目")
    public AjaxResult importItem(@RequestParam("file") MultipartFile file, @RequestParam("rateId") Long rateId, @RequestParam("updateSupport") boolean updateSupport) throws Exception
    {
        if (file == null){
            return warn("请上传文件");
        }
        ExcelUtil<FreightRateAirItem> util = new ExcelUtil<FreightRateAirItem>(FreightRateAirItem.class);
        List<FreightRateAirItem> airportRateItems = util.importRateExcel(file.getInputStream(),1);
        Long message = airService.importItem(airportRateItems,rateId, updateSupport);
        return success(message);
    }

    /**
     * 导出航司运价列表下载模板
     */
    @PreAuthorize("@ss.hasPermi('charge:freightAir:export')")
    @PostMapping("/exportNull")
    @ApiOperation(value = "导出航司运价下载模板")
    public void exportNull(HttpServletResponse response){
        List<FreightRateAirVO> list = new ArrayList<>();
        ExcelUtil<FreightRateAirVO> util = new ExcelUtil<FreightRateAirVO>(FreightRateAirVO.class);
        util.exportExcel(response, list, "航司运价下载模板");
    }

    /**
     * 导入航司运价
     */
    @PreAuthorize("@ss.hasPermi('charge:freightAir:importAirlines')")
    @Log(title = "航司运价", businessType = BusinessType.IMPORT)
    @PostMapping("/importAirlines")
    @ApiOperation(value = "导入航司运价")
    public AjaxResult importAirlines(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<FreightRateAirVO> util = new ExcelUtil<FreightRateAirVO>(FreightRateAirVO.class);
        List<FreightRateAirVO> freightRateAirVos = util.importExcel(file.getInputStream());
        String message = airService.importAirlines(freightRateAirVos, updateSupport);
        return success(message);
    }


    /**
     * 获取航司运价详细信息
     */
    @PreAuthorize("@ss.hasPermi('charge:freightAir:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取航司运价详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(airService.selectRateAirById(id));
    }

    /**
     * 新增航司运价
     */
    @PreAuthorize("@ss.hasPermi('charge:freightAir:add')")
    @OperLog(title = "航司运价", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ApiOperation(value = "新增航司运价")
    public AjaxResult add(@RequestBody FreightRateAir air)
    {
        return success(airService.insertFreightRateAir(air));
    }

    /**
     * 修改航司运价
     */
    @PreAuthorize("@ss.hasPermi('charge:freightAir:edit')")
    @OperLog(title = "航司运价", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ApiOperation(value = "修改航司运价")
    public AjaxResult edit(@RequestBody FreightRateAir air)
    {
        return toAjax(airService.updateFreightRateAir(air));
    }

    /**
     * 查看运价条目
     */
    @PreAuthorize("@ss.hasPermi('charge:freightAir:rateItemList')")
    @GetMapping("/rateItemList/{id}")
    @ApiOperation(value = "查看运价条目")
    public TableDataInfo rateItemList(@PathVariable Long id)
    {
        startPage();
        List<FreightRateAirItemVO> vos = airService.selectRateItemList(id);
        return getDataTable(vos);
    }

    /**
     * 删除航司运价
     */
    @PreAuthorize("@ss.hasPermi('charge:freightAir:del')")
    @OperLog(title = "航司运价", businessType = BusinessType.DELETE)
    @GetMapping("/del/{id}")
    @ApiOperation(value = "删除航司运价")
    public AjaxResult del(@PathVariable Long id)
    {
        return toAjax(airService.deleteFreightRateAir(id));
    }

    /**
     * 航司运价操作日志查询
     */
    @PreAuthorize("@ss.hasPermi('charge:freightAir:logList')")
    @GetMapping("/logList/{id}")
    @ApiOperation(value = "航司运价操作日志查询")
    public AjaxResult logList(@PathVariable Long id)
    {
        return success(logService.log("航司运价",id));
    }

    /**
     * 航司运价操作日志详情
     */
    @PreAuthorize("@ss.hasPermi('charge:freightAir:logInfo')")
    @GetMapping("/logInfo/{operId}")
    @ApiOperation(value = "航司运价操作日志详情")
    public AjaxResult logInfo(@PathVariable Long operId)
    {
        return success(logService.logInfo(operId));
    }

    /**
     * 批量删除航司运价
     */
    @PreAuthorize("@ss.hasPermi('charge:freightAir:deleteAll')")
    @OperLog(title = "航司运价", businessType = BusinessType.DELETE)
    @GetMapping("deleteAll/{ids}")
    @ApiOperation(value = "批量删除航司运价")
    public AjaxResult deleteAll(@PathVariable("ids") Long[] ids)
    {
        return toAjax(airService.deleteAll(ids));
    }
}
