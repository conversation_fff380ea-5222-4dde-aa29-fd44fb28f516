package com.gzairports.hz.business.cable.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 电报数据查询参数
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@Data
public class HzCableQuery {

    /** 主键id */
    private Long id;

    /** 电报号 */
    private String cableNo;

    /** 业务流水号 */
    private String serialNo;

    /** 发报地址 */
    private String cableAddress;

    /** 接收地址 */
    private String receiveAddress;

    /** 发报时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 发报时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 类型 */
    private String type;

    /** 状态 0 接收 1 发送 */
    private Integer isSend;

    /** 状态 0 失败 1 成功 */
    private Integer status;

    /** 航班号 */
    private String flightNo;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date flightDate;

    /** 电报内容 */
    private String content;
}
