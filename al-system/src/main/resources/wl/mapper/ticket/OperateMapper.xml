<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.wl.ticket.mapper.OperateMapper">

    <resultMap id="TicketCtrlResult" type="com.gzairports.wl.ticket.domain.TicketCtrl">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="controlEnabled" column="control_enabled"/>
        <result property="prefix" column="prefix"/>
        <result property="domint" column="domint"/>
    </resultMap>

    <select id="selectCtrlList" resultMap="TicketCtrlResult">
        select id, code, control_enabled, prefix, domint,dept_ids from wl_ticket_ctrl
    </select>
</mapper>
