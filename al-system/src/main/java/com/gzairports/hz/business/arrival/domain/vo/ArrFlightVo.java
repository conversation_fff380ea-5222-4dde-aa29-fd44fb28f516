package com.gzairports.hz.business.arrival.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 运单明细进港航班返回数据
 * <AUTHOR>
 * @date 2024-07-25
 */
@Data
public class ArrFlightVo {

    /** 录单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;

    /** 航班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date execDate;

    /** 航班号 */
    private String flightNo;
}
