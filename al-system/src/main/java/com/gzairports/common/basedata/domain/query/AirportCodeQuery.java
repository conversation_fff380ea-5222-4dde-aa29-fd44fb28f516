package com.gzairports.common.basedata.domain.query;

import lombok.Data;

import java.util.List;

/**
 * 机场代码查询参数
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Data
public class AirportCodeQuery {

    /** 主键id集合 */
    private List<Long> ids;

    /** 机场代码 */
    private String code;

    /** 机场四字码 */
    private String fourCode;

    /** 城市代码 */
    private String cityCode;

    /** 城市名称 */
    private String cityName;

    /** 中文名称 */
    private String chineseName;

    /** 英文名称 */
    private String englishName;

    /** 国际国内 */
    private String isDomestic;
}
