package com.gzairports.common.basedata.service;


import com.gzairports.common.basedata.domain.BaseAirCargoCode;
import com.gzairports.common.basedata.domain.query.AirCargoCodeQuery;

import java.util.List;

/**
 * 航空公司货品代码Service接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
public interface IAirCargoCodeService {

    /**
     * 查询航空公司货品代码列表
     *
     * @param query 查询参数
     * @return 航空公司货品代码列表
     */
    List<BaseAirCargoCode> selectAirCargoCodeList(AirCargoCodeQuery query);

    /**
     * 导入航空公司货品代码
     *
     * @param airCargoCodes 航空公司货品代码列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    String importAirCargoCode(List<BaseAirCargoCode> airCargoCodes, boolean updateSupport);

    /**
     * 新增航空公司货品代码
     *
     * @param airCargoCode 航空公司货品代码
     * @return 结果
     */
    int addAirCargoCode(BaseAirCargoCode airCargoCode);

    /**
     * 修改航空公司货品代码
     *
     * @param airCargoCode 航空公司货品代码
     * @return 结果
     */
    int editAirCargoCode(BaseAirCargoCode airCargoCode);

    /**
     * 删除航空公司货品代码
     *
     * @param ids 航空公司货品代码id集合
     * @return 结果
     */
    int delAirCargoCode(Long[] ids);

    /**
     * 删除航空公司货品代码
     *
     * @param id 航空公司货品代码id集合
     * @return 结果
     */
    int delAirCargoCode(Long id);

    /**
     * 根据id获取航空公司货品代码
     *
     * @param id 航空公司货品代码id
     * @return 航空公司货品代码
     */
    BaseAirCargoCode getInfo(Long id);
}
