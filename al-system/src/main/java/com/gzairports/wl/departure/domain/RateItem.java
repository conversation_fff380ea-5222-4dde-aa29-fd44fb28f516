package com.gzairports.wl.departure.domain;


import java.math.BigDecimal;

/**
 * Created by david on 2024/5/20
 * <AUTHOR>
 */
public interface RateItem {

    BigDecimal getMinimumFreight();

    BigDecimal getRateWeightRange5();

    BigDecimal getRateWeightRange10();

    BigDecimal getBaseRateN();

    BigDecimal getRateWeightRange45();

    BigDecimal getRateWeightRange100();

    BigDecimal getRateWeightRange300();

    BigDecimal getRateWeightRange500();

    BigDecimal getRateWeightRange1000();
}
