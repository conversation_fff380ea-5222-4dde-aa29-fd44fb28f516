package com.gzairports.wl.departure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.wl.departure.domain.Consign;
import com.gzairports.wl.departure.domain.query.ConsignQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 托运管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-08
 */
@Mapper
public interface ConsignMapper extends BaseMapper<Consign> {

    /**
     * 查询托运数据
     *
     * @param query 查询参数
     * @return 托运列表
     */
    List<Consign> selectConsignList(ConsignQuery query);
}
