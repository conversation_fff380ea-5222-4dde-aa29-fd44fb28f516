package com.gzairports.wl.reporter.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzairports.common.basedata.domain.BaseAgent;
import com.gzairports.common.basedata.mapper.BaseAgentMapper;
import com.gzairports.common.business.departure.mapper.CostDetailMapper;
import com.gzairports.common.business.reporter.domain.ReportDataWaybill;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.poi.ExcelToPdfUtil;
import com.gzairports.common.utils.poi.ReportExcelUtil;
import com.gzairports.hz.business.departure.mapper.AllAirWaybillMapper;
import com.gzairports.wl.reporter.domain.ReportSetField;
import com.gzairports.wl.reporter.domain.query.AwbaSalesQuery;
import com.gzairports.wl.reporter.domain.query.ReportExportData;
import com.gzairports.wl.reporter.domain.vo.ReportTotalVo;
import com.gzairports.wl.reporter.mapper.HawbSalesDailyMapper;
import com.gzairports.wl.reporter.mapper.ReportSetFieldMapper;
import com.gzairports.wl.reporter.service.IAwbaSalesDailyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-02-20
 */
@Service
public class AwbaSalesDailyServiceImpl implements IAwbaSalesDailyService {
    @Autowired
    private ReportSetFieldMapper setFieldMapper;

    @Autowired
    private HawbSalesDailyMapper salesDailyMapper;

    @Autowired
    private BaseAgentMapper baseAgentMapper;

    @Autowired
    private AllAirWaybillMapper allAirWaybillMapper;

    @Autowired
    private CostDetailMapper costDetailMapper;


    @Override
    public ReportExportData queryExportDataList(AwbaSalesQuery query) {
        List<ReportSetField> reportDataFields = setFieldMapper.selectReportDataFields(query.getReportTitle());
        if (CollectionUtils.isEmpty(reportDataFields)) {
            return ReportExportData.builder().build();
        }
        //过滤掉配置的的而表中没有的字段
        List<String> allColumnName = salesDailyMapper.selectAllColumnByTableName("all_report_data_waybill");
        reportDataFields = reportDataFields.stream().filter(v->allColumnName.contains(v.getFieldName())).collect(Collectors.toList());
        //查询统计报表的用户信息
        StringBuilder reportMsg = new StringBuilder();

        LambdaQueryWrapper<BaseAgent> lqwAgent = Wrappers.lambdaQuery(BaseAgent.class).eq(BaseAgent::getDeptId, SecurityUtils.getDeptId());
        String agent = Optional.ofNullable(baseAgentMapper.selectOne(lqwAgent)).orElseGet(BaseAgent::new).getAgent();
        String spaces = "   ";
        reportMsg.append("代理人公司名称：").append(StrUtil.isEmpty(agent) ? spaces : agent).append(spaces);
        String nickName = SecurityUtils.getNickName();
        reportMsg.append("制单人：").append(StrUtil.isEmpty(nickName) ? spaces : nickName).append(spaces);
        String now = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        reportMsg.append("统计时间：").append(now);

        //可排序字段
        String sortFieldsStr = reportDataFields.stream()
                .filter(e -> e.getSortType() != 0)
                .map(reportSetField -> reportSetField.getFieldName() + (reportSetField.getSortType() == 1 ? " ASC" : " DESC"))
                .collect(Collectors.joining(","));
        Page<ReportDataWaybill> page = (query.getPageNum() == null || query.getPageSize() == null) ?
                new Page<>(1, -1) :
                new Page<>(query.getPageNum(), query.getPageSize());

        List<String> fieldNames = reportDataFields.stream()
                .map(ReportSetField::getFieldName)
                .collect(Collectors.toList());
        String fieldNamesStr = String.join(",", fieldNames);
//        query.setWaybillType("AWBM".equals(query.getWaybillType()) ? "邮件单" : "主单");
        Page<ReportDataWaybill> pageData = StrUtil.isNotBlank(fieldNamesStr) ? salesDailyMapper.selectAwbaFieldByName(fieldNamesStr, query, page, sortFieldsStr) : new Page<>();
        List<Map<String, Object>> columnNameToValueList = filterFields(pageData.getRecords(), fieldNames);
//convertRowMapDataList(pageData.getRecords(), fieldNames);
        if (pageData.getPages() == pageData.getCurrent() || pageData.getSize() == -1) {
            ReportTotalVo vo = salesDailyMapper.selectAwbaTotal(query);
            Map<String, Object> totalReport = new LinkedHashMap<>();
            totalReport.put("lastCount", "运单总数：" + vo.getCount());
            totalReport.put("lastQuantity", "总件数：" + (fieldNames.contains("quantity") ? vo.getQuantity() : 0));
            totalReport.put("lastWeight", "总量：" + (fieldNames.contains("weight") ? vo.getWeight() : 0));
            totalReport.put("lastCostSum", "总金额：" + (fieldNames.contains("cost_sum") ? vo.getCostSum() : 0));
            columnNameToValueList.add(totalReport);
        }

        List<String> fieldNameCnList = reportDataFields.stream()
                .map(ReportSetField::getFieldNameCn)
                .collect(Collectors.toList());
        fieldNameCnList.add(0, "序号");

        return ReportExportData.builder()
                .reportTitle(query.getReportTitle())
                .reportMsg(reportMsg.toString())
                .fieldNameCnList(fieldNameCnList)
                .columnNameToValueList(columnNameToValueList)
                .build();
    }

    /**
     * 分页查询报表数据
     *
     * @param query 查询参数
     * @return 结果
     */
    @Override
    public IPage<ReportDataWaybill> pageQuery(AwbaSalesQuery query) {
        List<ReportSetField> reportDataFields = setFieldMapper.selectReportDataFields(query.getReportTitle());
        if (CollectionUtils.isEmpty(reportDataFields)) {
            return null;
        }
        List<String> allColumnName = salesDailyMapper.selectAllColumnByTableName("all_report_data_waybill");
        String fieldNames = reportDataFields.stream()
                .map(ReportSetField::getFieldName)
                .filter(allColumnName::contains)
                .collect(Collectors.joining(","));
        Page<ReportDataWaybill> pageParam = new Page<>(query.getPageNum() != null ? query.getPageNum() : 1, query.getPageSize() != null ? query.getPageSize() : 10);
//        query.setWaybillType("AWBM".equals(query.getWaybillType()) ? "邮件单" : "主单");

        if (StrUtil.isBlank(fieldNames)) {
            return new Page<>();
        }
        return salesDailyMapper.selectAwbaPageData(pageParam, fieldNames, query);
    }

    @Override
    public void export(ReportExportData reportExportData, HttpServletResponse response) {
        try {
            ReportExcelUtil.exportToExcel(response.getOutputStream(), reportExportData.getColumnNameToValueList(), reportExportData.getFieldNameCnList(), reportExportData.getReportTitle(), reportExportData.getReportMsg());

            //对文件名进行 URL 编码
            String fileName = reportExportData.getReportTitle() + "-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + encodedFileName + ".xlsx");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

//        ExcelUtil<ReportData> util = new ExcelUtil<>(ReportData.class);
//        util.exportExcel(response, pageData.getRecords(), query.getReportTitle());
    }

    @Override
    public void printPDF(AwbaSalesQuery query, HttpServletResponse response) {
        ReportExportData reportExportData = this.queryExportDataList(query);
        // 设置响应头
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            ReportExcelUtil.exportToExcel(baos, reportExportData.getColumnNameToValueList(), reportExportData.getFieldNameCnList(), reportExportData.getReportTitle(), reportExportData.getReportMsg());

            //对文件名进行 URL 编码
            String fileName = query.getReportTitle() + "-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "attachment; filename=" + encodedFileName + ".pdf");

            // 从 ByteArrayOutputStream 获取 ByteArrayInputStream
            ByteArrayInputStream inputStream = new ByteArrayInputStream(baos.toByteArray());
            ExcelToPdfUtil.excelToPdf(inputStream, response.getOutputStream(), ".xlsx");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 根据给定的数据库字段名称列表，从ReportData列表中提取相应的字段。
     *
     * @param reportDataList 包含ReportData对象的列表
     * @param dbFieldNames   数据库字段名称列表
     * @return 包含过滤后数据的列表，每项为一个Map，键是数据库字段名，值是字段值
     */
    public static List<Map<String, Object>> filterFields(List<ReportDataWaybill> reportDataList, List<String> dbFieldNames) {
        List<Map<String, Object>> filteredDataList = new ArrayList<>();
        for (ReportDataWaybill reportData : reportDataList) {
            //需要保证顺序插入
            Map<String, Object> filteredData = new LinkedHashMap<>();
            Class<?> clazz = reportData.getClass();
            for (String dbFieldName : dbFieldNames) {
                if (StringUtils.isEmpty(dbFieldName)) {
                    continue;
                }
                try {
                    String camelCaseFieldName = StringUtils.underscoreToCamelCase(dbFieldName);
                    Field field = clazz.getDeclaredField(camelCaseFieldName);
                    field.setAccessible(true);
                    filteredData.put(dbFieldName, field.get(reportData));
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    throw new CustomException("处理字段 " + dbFieldName + " 时发生错误: " + e.getMessage());
                }
            }
            filteredDataList.add(filteredData);
        }
        return filteredDataList;
    }

}
