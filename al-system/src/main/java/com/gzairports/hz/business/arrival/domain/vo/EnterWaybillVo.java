package com.gzairports.hz.business.arrival.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 航班文件运单录入参数
 * <AUTHOR>
 * @date 2024-07-16
 */
@Data
public class EnterWaybillVo {

    /** 运单id */
    private Long id;

    /** 录单id */
    private Long orderId;

    /** 航段id */
    private Long legId;

    /** 理货id */
    private Long tallyId;

    /** 运单号 */
    private String waybillCode;

    /** 是否补货单 */
    private Integer replenishBill;

    /** 文件到达 */
    private Integer fileArr;

    /** 优先处理 */
    private Integer prioritize;

    /** 进港中转 */
    private Integer transferBill;

    /** 始发站 */
    private String sourcePort;

    /** 承运人1 */
    private String carrier1;

    /** 到达站1 */
    private String des1;

    /** 承运人2 */
    private String carrier2;

    /** 到达站2 */
    private String des2;

    /** 承运人3 */
    private String carrier3;

    /** 到达站3 */
    private String des3;

    /** 目的站 */
    private String desPort;

    /** 件数 */
    private Integer quantity;

    /** 舱单件数 */
    private Integer cabinQuantity;

    /** 重量 */
    private BigDecimal weight;

    /** 舱单重量 */
    private BigDecimal cabinWeight;

    /** 计费重量 */
    private BigDecimal chargeWeight;

    /** 航班号1 */
    private String flightNo1;

    private String flightNo;

    /** 航班日期1 */
    private Date flightDate1;


    /** 运单出港状态 staging: 暂存 been_sent: 已发送 pre_pay: 预授权支付 put_in: 货站入库 been_pre: 已预配
     * been_out: 已出库 been_dep: 已出港 pull_down: 临时拉下 been_return: 已退货 been_voided
     * 已作废 order_change: 已换单 been_settle: 已结算  运单进港状态 record_order 录单 tally_comp 理货完成
     * comp_order 已办单 out_stock 已出库*/
    private String status;

    /** 代理人code */
    private String agentCode;

    /** 代理人 */
    private String shipper;

    /** 收货人 */
    private String consign;

    /** 收货人证件号 */
    private String consignIdCar;

    /** 联系电话 */
    private String consignPhone;

    /** 品名编码 */
    private String cargoCode;

    /** 品名（邮件种类） */
    private String cargoName;

    /** 货品大类名称 */
    private String categoryName;

    /** 特货代码 */
    private String specialCargoCode1;

    /** 是否需要冷藏 0 否 1 是 */
    private Integer isCold;

    /** 冷藏库 */
    private String coldStore;

    /** 海关监管 */
    private Integer customsSupervision;

    /** 转关 */
    private Integer isTransfer;

    /** 海关转关号 */
    private String transferNo;

    /** 到付 */
    private Integer arrPay;

    /** 到付费用 */
    private BigDecimal costSum;

    /** 填开时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date writeTime;

    /** 填开地点 */
    private String writeLocation;

    /** 填开人 */
    private String writer;

    /** 备注 */
    private String remark;

    /** 类型 DEP 出港 ARR 进港 */
    private String type;

    /** 是否审核 0 否 1 是 */
    private Integer isExamine;

    /** 录单时间 */
    private Date orderTime;
}
