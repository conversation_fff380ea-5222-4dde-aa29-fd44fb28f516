package com.gzairports.common.basedata.domain.query;

import lombok.Data;

import java.util.List;

/**
 * 不正常类型查询参数
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Data
public class AbnormalTypeQuery {

    /** 主键id集合 */
    private List<Long> ids;

    /** 不正常类型 */
    private String abnormalType;

    /** 中文描述 */
    private String chineseDescription;

    /** 英文描述 */
    private String englishDescription;
}
