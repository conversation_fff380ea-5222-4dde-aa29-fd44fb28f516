package com.gzairports.common.basedata.domain.query;

import com.gzairports.wl.departure.domain.query.BasePageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
public class WayBillH5Query extends BasePageQuery {

    /**
     * 代理人
     */
    private Integer deptId;

    /**
     * 理货开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tallyStartTime;

    /**
     * 理货结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tallyEndTime;

}
