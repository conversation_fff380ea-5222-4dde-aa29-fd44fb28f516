package com.gzairports.wl.departure.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.BaseCarrier;
import com.gzairports.common.basedata.mapper.CarrierMapper;
import com.gzairports.common.business.departure.mapper.MawbMapper;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.wl.departure.domain.Hawb;
import com.gzairports.common.business.departure.domain.Mawb;
import com.gzairports.wl.departure.domain.MergeHawbMawb;
import com.gzairports.wl.departure.domain.query.CancelMergeQuery;
import com.gzairports.wl.departure.domain.query.MergeHawbQuery;
import com.gzairports.wl.departure.domain.query.MergeQuery;
import com.gzairports.wl.departure.domain.vo.MergeHawbVo;
import com.gzairports.wl.departure.domain.vo.MergeMawbVo;
import com.gzairports.wl.departure.mapper.HawbMapper;
import com.gzairports.wl.departure.mapper.MergeHawbMawbMapper;
import com.gzairports.wl.departure.service.IMergeHawbMawbService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 主分单拼单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-23
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class MergeHawbMawbServiceImpl extends ServiceImpl<MergeHawbMawbMapper, MergeHawbMawb> implements IMergeHawbMawbService {

    @Autowired
    private MergeHawbMawbMapper mergeMapper;

    @Autowired
    private HawbMapper hawbMapper;

    @Autowired
    private MawbMapper mawbMapper;

    @Autowired
    private CarrierMapper carrierMapper;

    /**
     * 查询主分单拼单列表
     * @param query 查询参数
     * @return 主分单拼单列表
     */
    @Override
    public List<MergeMawbVo> selectMawbList(MergeQuery query) {
        query.setDepId(SecurityUtils.getHighParentId());
        List<MergeMawbVo> mawbVos = mawbMapper.selectListByQuery(query);
        for (MergeMawbVo mawbVo : mawbVos) {
            //将承运人中的中文描述转换为承运人代码,不是中文则跳过
            String carrierForSelect = mawbVo.getCarrier1();
            if (StringUtils.isNotNull(carrierForSelect)) {
                BaseCarrier baseCarrier = carrierMapper.selectOne(new QueryWrapper<BaseCarrier>().
                        like("chinese_name", carrierForSelect).
                        eq("is_del", 0));
                if (baseCarrier != null) {
                    mawbVo.setCarrier1(baseCarrier.getCode());
                }
            }
            BigDecimal totalMergeWeight = BigDecimal.ZERO;
            BigDecimal chargeWeight;
            BigDecimal weight = mawbVo.getTotalWeight();
            Integer quantity = mawbVo.getTotalCount();
            if(mawbVo.getChargeWeight() == null || mawbVo.getChargeWeight().compareTo(BigDecimal.ZERO) == 0){
                chargeWeight = mawbVo.getChargeWeight();
            }else{
                chargeWeight = mawbVo.getTotalWeight();
            }
            List<MergeHawbMawb> merge = mergeMapper.selectList(new QueryWrapper<MergeHawbMawb>()
                    .eq("mawb_id", mawbVo.getId()));
            for (MergeHawbMawb mergeHawbMawb : merge) {
//                quantity  = quantity + mergeHawbMawb.getMergeNum();
//                weight = weight.add(mergeHawbMawb.getMergeWeight());
                totalMergeWeight = totalMergeWeight.add(mergeHawbMawb.getMergeWeight());
//                chargeWeight = chargeWeight.add(mergeHawbMawb.getMergeWeight());
            }
            mawbVo.setTotalMergeWeight(totalMergeWeight);
            mawbVo.setTotalCount(quantity);
            mawbVo.setTotalWeight(weight);
            mawbVo.setChargeWeight(chargeWeight);
            mawbVo.setTotalMerge(merge.size());
        }
        return mawbVos;
    }

    /**
     * 根据主单id查询已拼单信息
     * @param mawbId 主单id
     * @return 已拼单列表
     */
    @Override
    public List<MergeHawbVo> selectHawbList(Long mawbId) {
        if (mawbId == null){
            throw new CustomException("请传入需要查询的主单");
        }
        List<MergeHawbVo> list = new ArrayList<>();
        Long deptId = SecurityUtils.getHighParentId();
        List<MergeHawbMawb> merge = mergeMapper.selectList(new QueryWrapper<MergeHawbMawb>().eq("mawb_id", mawbId));
        for (MergeHawbMawb mergeHawbMawb : merge) {
            MergeHawbVo mergeHawbVo = hawbMapper.selectMergeList(mergeHawbMawb.getHawbId(), deptId);
            mergeHawbVo.setMergeWeight(mergeHawbMawb.getMergeWeight());
            mergeHawbVo.setMergeCount(mergeHawbMawb.getMergeNum());
            mergeHawbVo.setToMergeWeight(mergeHawbMawb.getRemainWeight());
            mergeHawbVo.setToMergeCount(mergeHawbMawb.getRemainNum());
            list.add(mergeHawbVo);
        }
        return list;
    }


    /**
     * 拼单取消
     * @param mawbId 主单id集合
     * @return 结果
     */
    @Override
    public int cancel(Long mawbId) {
        remove(new QueryWrapper<MergeHawbMawb>().eq("mawb_id", mawbId));

        Mawb mawb = mawbMapper.selectById(mawbId);
        mawb.setMergeStatus(0);
        return mawbMapper.updateById(mawb);
    }

    /**
     * 取消拼单
     * @param query 取消参数
     * @return 结果
     */
    @Override
    public int cancelMerge(CancelMergeQuery query) {
        if (CollectionUtils.isEmpty(query.getHawbIds())){
            throw new CustomException("没有需要取消拼单的数据");
        }
        for (Long hawbId : query.getHawbIds()) {
            remove(new QueryWrapper<MergeHawbMawb>().eq("mawb_id",query.getMawbId()).eq("hawb_id",hawbId));
            //将分单表的 关联主单号置空
            Hawb hawb = hawbMapper.selectById(hawbId);
            hawb.setMasterWaybillCode(null);
            hawbMapper.updateById(hawb);
        }
        List<MergeHawbMawb> mawbList = mergeMapper.selectList(new QueryWrapper<MergeHawbMawb>().eq("mawb_id", query.getMawbId()));
        if (CollectionUtils.isEmpty(mawbList)){
            Mawb mawb = mawbMapper.selectById(query.getMawbId());
            mawb.setMergeStatus(0);
            mawbMapper.updateById(mawb);
        }
        return 1;
    }

    /**
     * 查询可拼单信息
     * @param query 查询参数
     * @return 可拼单列表
     */
    @Override
    public List<MergeHawbVo> selectCanList(CancelMergeQuery query) {
        if (query.getMawbId() == null){
            throw new CustomException("请选择需要查询的主单");
        }
        Mawb mawb = mawbMapper.selectById(query.getMawbId());
        String deptName = SecurityUtils.getLoginUser().getUser().getDept().getDeptName();
        QueryWrapper<Hawb> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("carrier1", mawb.getCarrier1());
        queryWrapper.eq("des_port", mawb.getDesPort());
        queryWrapper.eq("dept_id",SecurityUtils.getHighParentId());
        if (query.getWaybillCode() != null){
            queryWrapper.like("waybill_code",query.getWaybillCode());
        }
        queryWrapper.eq("status","NORMAL");
        queryWrapper.eq("is_del",0);

        List<Hawb> hawbs = hawbMapper.selectList(queryWrapper);

        List<MergeHawbVo> list = new ArrayList<>();

        for (Hawb hawb : hawbs) {

            List<MergeHawbMawb> merge = mergeMapper.selectList(new QueryWrapper<MergeHawbMawb>()
                    .eq("hawb_id", hawb.getId())
                    .or()
                    .ne("remain_num",0)
                    .ne("remain_weight",0));
            if (!CollectionUtils.isEmpty(merge)){
                continue;
            }

            MergeHawbVo vo = new MergeHawbVo();
            BeanUtils.copyProperties(hawb,vo);
            vo.setCarrier(hawb.getCarrier1());
            vo.setDept(deptName);
            MergeHawbMawb mergeHawbMawb = mergeMapper.selectOne(new QueryWrapper<MergeHawbMawb>()
                    .eq("hawb_id", hawb.getId())
                    .eq("mawb_id", query.getMawbId()));
            if (mergeHawbMawb == null){
                vo.setMergeCount(0);
                vo.setMergeWeight(new BigDecimal(0));
                vo.setToMergeCount(hawb.getQuantity());
                vo.setToMergeWeight(hawb.getWeight());
            }else {
                vo.setMergeCount(mergeHawbMawb.getMergeNum());
                vo.setMergeWeight(mergeHawbMawb.getMergeWeight());
                vo.setToMergeCount(mergeHawbMawb.getRemainNum());
                vo.setToMergeWeight(mergeHawbMawb.getRemainWeight());
            }
            list.add(vo);
        }
        return list;
    }

    /**
     * 拼单
     * @param query 拼单参数
     * @return 结果
     */
    @Override
    public int merge(MergeHawbQuery query) {
        if (query.getVos() == null){
            throw new CustomException("请选择可拼分单");
        }
        Mawb mawb = mawbMapper.selectById(query.getMawbId());
        //选择要拼的分单总重不能超过主单重量
        List<MergeHawbMawb> mawbList = mergeMapper.selectList(new QueryWrapper<MergeHawbMawb>().eq("mawb_id", query.getMawbId()));
        BigDecimal totalWeight = new BigDecimal(0);
        if(mawbList.size() > 0){
            totalWeight = mawbList.stream().map(MergeHawbMawb::getMergeWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        BigDecimal totalWeightByQuery = query.getVos().stream().map(MergeHawbVo::getToMergeWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
        if(mawb.getWeight().compareTo(totalWeightByQuery.add(totalWeight)) < 0){
            throw new CustomException("拼单总重量不能超过主单重量");
        }
        for (MergeHawbVo vo : query.getVos()) {
            MergeHawbMawb mergeHawbMawb = mergeMapper.selectOne(new QueryWrapper<MergeHawbMawb>().eq("mawb_id", query.getMawbId()).eq("hawb_id", vo.getId()));
            if (mergeHawbMawb != null){
                BigDecimal weight = mergeHawbMawb.getMergeWeight().add(vo.getToMergeWeight());
                mergeHawbMawb.setMergeWeight(weight);
                Integer num = mergeHawbMawb.getMergeNum() + vo.getToMergeCount();
                mergeHawbMawb.setMergeNum(num);
                BigDecimal toWeight = mergeHawbMawb.getRemainWeight().subtract(vo.getToMergeWeight());
                if (toWeight.compareTo(new BigDecimal(0)) <= 0){
                    toWeight = new BigDecimal(0);
                }
                mergeHawbMawb.setRemainWeight(toWeight);
                int toNum = mergeHawbMawb.getRemainNum() - vo.getToMergeCount();
                if (toNum <= 0){
                    toNum = 0;
                }
                mergeHawbMawb.setRemainNum(toNum);
                return mergeMapper.updateById(mergeHawbMawb);
            }
            MergeHawbMawb merge = new MergeHawbMawb();
            merge.setMawbId(query.getMawbId());
            merge.setHawbId(vo.getId());
            merge.setMergeNum(vo.getToMergeCount());
            merge.setMergeWeight(vo.getToMergeWeight());
            merge.setRemainWeight(new BigDecimal(0));
            merge.setRemainNum(0);


            mawb.setMergeStatus(1);
            mawbMapper.updateById(mawb);
            mergeMapper.insert(merge);
        }
        return 1;
    }
}
