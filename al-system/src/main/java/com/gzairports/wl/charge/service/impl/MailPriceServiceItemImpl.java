package com.gzairports.wl.charge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.exception.CustomException;
import com.gzairports.wl.charge.domain.*;
import com.gzairports.wl.charge.domain.query.PriceItemQuery;
import com.gzairports.wl.charge.domain.vo.MailPriceItemVo;
import com.gzairports.wl.charge.mapper.MailPriceItemMapper;
import com.gzairports.wl.charge.mapper.MailPriceMapper;
import com.gzairports.wl.charge.service.IMailPriceItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 邮件运价条目service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@Service
public class MailPriceServiceItemImpl extends ServiceImpl<MailPriceItemMapper, MailPriceItem> implements IMailPriceItemService {

    @Autowired
    private MailPriceItemMapper itemMapper;

    @Autowired
    private MailPriceMapper priceMapper;

    /**
     * 查询运价条目列表
     *
     * @param query 运价条目查询参数
     * @return 结果
     */
    @Override
    public List<MailPriceItemVo> selectList(PriceItemQuery query) {
        List<MailPriceItemVo> mailPriceItemVos = itemMapper.selectAirList(query);
        mailPriceItemVos.forEach(item -> {
            if((item.getStartTime().before(new Date()) || item.getStartTime().equals(new Date())) &&
                    (item.getEndTime().after(new Date()) || item.getEndTime().equals(new Date()))){
                item.setIsEffective(1);
            }else{
               item.setIsEffective(0);
            }
        });
        return mailPriceItemVos;
    }

    /**
     * 查询运价条目详情
     *
     * @param id 运价条目主键
     * @return 运价条目详情
     */
    @Override
    public MailPriceItem selectAirItemById(Long id) {
        MailPriceItem mailPriceItem = itemMapper.selectById(id);
        MailPrice price = priceMapper.selectById(mailPriceItem.getRateId());
        mailPriceItem.setRateName(price.getRateName());
        return mailPriceItem;
    }

    /**
     * 新增运价条目
     *
     * @param item 运价条目
     * @return 结果
     */
    @Override
    public int insertAirItem(MailPriceItem item) {
        MailPriceItem freightRateCustomItem = itemMapper.selectOne(new QueryWrapper<MailPriceItem>()
                .eq("rate_id",item.getRateId())
                .eq("departure_city", item.getDepartureCity())
                .eq("destination_city", item.getDestinationCity())
                .eq("carrier_code", item.getCarrierCode()));
        if (freightRateCustomItem != null){
            throw new CustomException("已存在相同运价条目数据");
        }
        return itemMapper.insert(item);
    }

    /**
     * 修改运价条目
     *
     * @param item 运价条目
     * @return 结果
     */
    @Override
    public int updateAirItem(MailPriceItem item) {
        return itemMapper.updateById(item);
    }

    /**
     * 删除运价条目
     *
     * @param id 运价条目主键
     * @return 结果
     */
    @Override
    public int removeAirItem(Long id) {
        return itemMapper.removeAirItem(id);
    }

    @Override
    public void delAll(Long[] ids) {
        if (ids != null){
            Date date = new Date();
            Long id = ids[0];
            MailPriceItem mailPriceItem = itemMapper.selectById(id);
            MailPrice mailPrice = priceMapper.selectById(mailPriceItem.getRateId());
            if (mailPrice.getEffectiveDate().before(date) && mailPrice.getExpirationDate().after(date)){
                throw new CustomException("在有效期内的运价不能删除");
            }
            int i = itemMapper.deleteBatchIds(Arrays.asList(ids));
        }
    }
}
