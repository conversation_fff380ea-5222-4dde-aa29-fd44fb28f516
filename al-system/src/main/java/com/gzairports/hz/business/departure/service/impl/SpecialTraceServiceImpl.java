package com.gzairports.hz.business.departure.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.hz.business.departure.domain.AirWaybill;
import com.gzairports.hz.business.departure.domain.HzCollectWeight;
import com.gzairports.hz.business.departure.domain.HzDepSpecialTrace;
import com.gzairports.hz.business.departure.domain.query.SpecialTraceQuery;
import com.gzairports.hz.business.departure.domain.vo.SpecialTraceVo;
import com.gzairports.hz.business.departure.mapper.SpecialTraceMapper;
import com.gzairports.hz.business.departure.service.ISpecialTraceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 特货跟踪Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Service
public class SpecialTraceServiceImpl extends ServiceImpl<SpecialTraceMapper, HzDepSpecialTrace> implements ISpecialTraceService {

    @Autowired
    private SpecialTraceMapper specialTraceMapper;

    /**
     * 特货跟踪列表查询
     * @param query 查询参数
     * @return 列表
     */
    @Override
    public List<SpecialTraceVo> selectList(SpecialTraceQuery query) {
        return specialTraceMapper.selectListByQuery(query);
    }

    @Async
    @Transactional(rollbackFor = Exception.class)
    public void addSpecialTrace(List<HzCollectWeight> weights, Integer voQuantity, BigDecimal voWeight, AirWaybill airWaybill) {
        if (airWaybill.getSpecialCargoCode1() != null || airWaybill.getSpecialCargoCode2() != null || airWaybill.getSpecialCargoCode3() != null){
            String wareLocation = null;
            if (!CollectionUtils.isEmpty(weights)){
                wareLocation = weights.stream().map(m -> m.getStore() + "-" + m.getLocator()).collect(Collectors.joining(","));
            }
            HzDepSpecialTrace specialTrace1 = specialTraceMapper.selectOne(new QueryWrapper<HzDepSpecialTrace>().eq("waybill_code", airWaybill.getWaybillCode()));
            if (specialTrace1 != null){
                specialTrace1.setWarePiece(voQuantity);
                specialTrace1.setWareWeight(voWeight);
                specialTrace1.setWareTime(new Date());
                specialTrace1.setWareUser("系统");
                StringBuilder location = new StringBuilder(specialTrace1.getWareLocation());
                if (wareLocation != null){
                    String[] split = wareLocation.split(",");
                    for (String s : split) {
                        if (!specialTrace1.getWareLocation().contains(s)){
                            location.append(",").append(s);
                        }
                    }
                }

                specialTrace1.setWareLocation(location.toString());
                specialTraceMapper.updateById(specialTrace1);
            }else {
                HzDepSpecialTrace specialTrace = new HzDepSpecialTrace();
                specialTrace.setWarePiece(voQuantity);
                specialTrace.setWareWeight(voWeight);
                specialTrace.setWaybillCode(airWaybill.getWaybillCode());
                specialTrace.setWareLocation(wareLocation);
                specialTrace.setWareUser("系统");
                specialTrace.setWareTime(new Date());
                specialTraceMapper.insert(specialTrace);
            }
        }
    }
}
