package com.gzairports.wl.reporter.domain.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-13
 */
@Data
public class YearCargoQuantityVO {

    /** 国际出港 */
    private MonthQuantityVO iDep;

    /** 国际进港 */
    private MonthQuantityVO iArr;

    /** 国际合计 */
    private MonthQuantityVO iTotal;

    /** 国内出港 */
    private MonthQuantityVO dDep;

    /** 国内进港 */
    private MonthQuantityVO dArr;

    /** 国内合计 */
    private MonthQuantityVO dTotal;

    /** 月总货量 */
    private MonthQuantityVO monthTotal;

    /** 月累计量 */
    private MonthQuantityVO monthAccumulate;
}
