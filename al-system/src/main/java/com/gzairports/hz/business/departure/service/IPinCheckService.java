package com.gzairports.hz.business.departure.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gzairports.hz.business.departure.domain.PinCheck;
import com.gzairports.hz.business.departure.domain.query.PinCheckQuery;
import com.gzairports.hz.business.departure.domain.vo.PinCheckVo;
import com.gzairports.hz.business.departure.domain.vo.WaybillIdAndCodeVo;

import java.util.List;

/**
 * 开箱抽查Service接口
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
public interface IPinCheckService extends IService<PinCheck> {

    /**
     * 开箱抽查查询列表
     * @param query 查询条件
     * @return 结果
     */
    List<PinCheckVo> selectPinCheckList(PinCheckQuery query);

    /**
     * 查询详情
     * @param id 抽检id
     * @return 结果
     */
    PinCheckVo getInfo(Long id);

    /**
     * 修改开箱抽查
     * @param vo 开箱抽查参数
     * @return 结果
     */
    int edit(PinCheckVo vo);

    /**
     * H5端查询开箱检查数据
     * @param query 查询条件
     * @return 结果
     */
    List<PinCheckVo> selectH5List(PinCheckQuery query);

    /**
     * H5端新增开箱抽查
     * @param pinCheck 新增数据
     * @return 结果
     */
    int addH5PinCheck(PinCheck pinCheck);

    /**
     * 根据四位查询运单号
     * @param fourCode 运单号四位
     * @return 结果
     */
    List<WaybillIdAndCodeVo> getFourCode(String fourCode);

    /**
     * 根据运单id查询运单信息
     * @param waybillId 运单id
     * @return 结果
     */
    PinCheckVo waybillInfo(Long waybillId);

    /**
     * 新增暂存
     * @param check 暂存数据
     * @return 结果
     */
    boolean addStaging(PinCheck check);

    /**
     * 暂存列表查询
     * @param query 查询参数
     * @return 暂存列表
     */
    List<PinCheckVo> selectStagingList(PinCheckQuery query);

    /**
     * 查询暂存详情
     * @param checkId 检查id
     * @return 结果
     */
    PinCheck stagingDetail(Long checkId);

    /**
     * 暂存删除
     * @param checkId 检查id
     * @return 结果
     */
    boolean delStaging(Long checkId);

    /**
     * 暂存修改
     * @param check 修改参数
     * @return 结果
     */
    boolean editStaging(PinCheck check);

    /**
     * 根据运单后八位查询
     * @param code 运单八位code
     * @return 结果
     */
    List<WaybillIdAndCodeVo> getEightCode(String code);

    PinCheckVo waybillGetInfo(String waybillCode);
}
