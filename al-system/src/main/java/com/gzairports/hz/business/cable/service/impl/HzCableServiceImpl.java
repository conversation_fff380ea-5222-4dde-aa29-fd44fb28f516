package com.gzairports.hz.business.cable.service.impl;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.gzairports.common.basedata.domain.BaseCargoUld;
import com.gzairports.common.basedata.domain.vo.BaseCargoUldVo;
import com.gzairports.common.business.arrival.mapper.FlightInfoMapper;
import com.gzairports.common.business.departure.domain.FlightInfo;
import com.gzairports.common.business.departure.domain.Mawb;
import com.gzairports.common.business.departure.mapper.PullDownMapper;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.securitySubmit.domain.SecuritySubmitBack;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.SerialNumberGenerator;
import com.gzairports.common.basedata.mapper.UldMapper;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.hz.business.cable.domain.HzCable;
import com.gzairports.hz.business.cable.domain.HzCableAddress;
import com.gzairports.hz.business.cable.domain.HzCableType;
import com.gzairports.hz.business.cable.domain.query.GenerateCableQuery;
import com.gzairports.hz.business.cable.domain.query.HzCableQuery;
import com.gzairports.hz.business.cable.domain.query.TypeAndAddressQuery;
import com.gzairports.hz.business.cable.domain.vo.*;
import com.gzairports.hz.business.cable.mapper.HzCableAddressMapper;
import com.gzairports.hz.business.cable.mapper.HzCableMapper;
import com.gzairports.hz.business.cable.mapper.HzCableTypeMapper;
import com.gzairports.hz.business.cable.service.IHzCableService;
import com.gzairports.hz.business.departure.domain.query.CableMawbQuery;
import com.gzairports.hz.business.departure.domain.vo.CableMawbVo;
import com.gzairports.hz.business.departure.domain.vo.LoadUldVo;
import com.gzairports.hz.business.departure.domain.vo.PullCargoVo;
import com.gzairports.hz.business.departure.mapper.FlightBizMapper;
import com.gzairports.common.business.arrival.mapper.FlightLoadUldWaybillMapper;
import com.gzairports.common.business.arrival.mapper.FlightLoadWaybillMapper;
import com.gzairports.common.business.departure.mapper.MawbMapper;
import com.gzairports.hz.business.departure.mapper.FlightLoadMapper;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.encoder.org.apache.commons.lang.ObjectUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.io.FileWriter;
import java.io.IOException;
import java.lang.reflect.Array;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * 电报数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
@Service
@Slf4j
public class HzCableServiceImpl implements IHzCableService
{
    @Autowired
    private HzCableMapper hzCableMapper;

    @Autowired
    private HzCableAddressMapper cableAddressMapper;

    @Autowired
    private MawbMapper mawbMapper;

    @Autowired
    private FlightBizMapper bizMapper;

    @Autowired
    private FlightLoadUldWaybillMapper uldMawbMapper;

    @Autowired
    private FlightLoadWaybillMapper loadMawbMapper;

    @Autowired
    private FlightLoadMapper loadMapper;

    @Autowired
    private UldMapper uldMapper;

    @Autowired
    private HzCableTypeMapper hzCableTypeMapper;

    @Autowired
    private PullDownMapper pullDownMapper;

    @Autowired
    private FlightInfoMapper flightInfoMapper;

    @Autowired
    private RestTemplate restTemplate;


    @Value("${hzCable.account}")
    private String account;

    @Value("${hzCable.loginUrl}")
    private String loginUrl;

    @Value("${hzCable.getMsg}")
    private String getMsg;

    @Value("${hzCable.FTPAddress}")
    private String ftpAddress;



    @Autowired
    private HttpServiceImpl httpService;

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private static final Pattern LETTERS_PATTERN = Pattern.compile("([A-Za-z]+)(\\d+)");


    /**
     * 查询电报数据
     *
     * @param id 电报数据主键
     * @return 电报数据
     */
    @Override
    public HzCable selectHzCableById(Long id)
    {
        return hzCableMapper.selectHzCableById(id);
    }

    /**
     * 查询电报数据列表
     *
     * @param query 电报数据
     * @return 电报数据
     */
    @Override
    public List<HzCable> selectHzCableList(HzCableQuery query)
    {
        //通过前端传递的整型type(实际是id)得到string型的type
        if(query.getType() != null){
        HzCableType hzCableType = hzCableTypeMapper.selectHzCableTypeById(Long.valueOf(query.getType()));
        query.setType(hzCableType.getCableCode());}
        return hzCableMapper.selectHzCableList(query);
    }

    /**
     * 新增电报数据
     *
     * @param hzCable 电报数据
     * @return 结果
     */
    @Override
    public int insertHzCable(HzCable hzCable) {

        if (StringUtils.isEmpty(hzCable.getReceiveAddress())) {
            throw new CustomException("请输入收报地址");
        }
        if (hzCable.getTypeVoList() == null) {
            return 0;
        }
        hzCable.setCreateBy(SecurityUtils.getNickName());
        hzCable.setIsSend(1);
        hzCable.setIsAuto(0);

        if (StringUtils.isNotEmpty(hzCable.getAddressReceive())){
            hzCable.setReceiveAddress(hzCable.getAddressReceive());
        }else {
            hzCable.setReceiveAddress(hzCable.getReceiveAddress());
        }
        Map<Long, List<CableMsgTypeVO>> voMap = hzCable.getTypeVoList().stream()
                .collect(Collectors.groupingBy(CableMsgTypeVO::getId));

        String token = getToken();

        for (Map.Entry<Long, List<CableMsgTypeVO>> voEntry : voMap.entrySet()) {
            HzCableAddress hzCableAddress = cableAddressMapper.selectById(voEntry.getKey());
            if (hzCableAddress == null) continue;

            Set<String> interactionTypes = parseInteractionTypes(hzCableAddress);
            processInteractionTypes(interactionTypes, voEntry.getValue(), hzCable, hzCableAddress, token);
        }
        return 1;
    }

    private Set<String> parseInteractionTypes(HzCableAddress address) {
        Set<String> types = new HashSet<>();
        String interactionTypesStr = address.getInteractionTypes();
        if (StringUtils.isNotEmpty(interactionTypesStr)) {
            types.addAll(Arrays.stream(interactionTypesStr.split(","))
                    .map(String::trim)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toSet()));
        }
        return types;
    }

    private void processInteractionTypes(Set<String> interactionTypes, List<CableMsgTypeVO> typeVos,
                                         HzCable hzCable, HzCableAddress hzCableAddress, String token) {
        boolean hasAirControl = interactionTypes.contains("空管报文");
        boolean hasCaac = interactionTypes.contains("民航局报文");

        // 处理不同类型组合
        if (hasAirControl && hasCaac) {
            handleBothAirAndCaac(interactionTypes, typeVos, hzCable, hzCableAddress, token);
        } else if (hasAirControl) {
            handleOnlyAirControl(interactionTypes, typeVos, hzCable, hzCableAddress, token);
        } else if (hasCaac) {
            handleOnlyCaac(interactionTypes, typeVos, hzCable, hzCableAddress, token);
        } else {
            handleOthers(interactionTypes, typeVos, hzCable, hzCableAddress, token);
        }
    }

    private void handleBothAirAndCaac(Set<String> interactionTypes, List<CableMsgTypeVO> typeVos,
                                      HzCable hzCable, HzCableAddress hzCableAddress, String token) {
        for (String interactionType : interactionTypes) {
            setAftnAddress(hzCable, token, hzCableAddress, typeVos, interactionType);
            setFdAddress(hzCable, token, hzCableAddress, typeVos, interactionType);
            setMailAddress(hzCable, token, hzCableAddress, typeVos, interactionType, "FD");
            setFtpAddress(hzCable, token, hzCableAddress, typeVos, interactionType, "FD");
            setMqAddress(hzCable, token, hzCableAddress, typeVos, interactionType, "FD");
        }
    }

    private void handleOnlyAirControl(Set<String> interactionTypes, List<CableMsgTypeVO> typeVos,
                                      HzCable hzCable, HzCableAddress hzCableAddress, String token) {
        for (String interactionType : interactionTypes) {
            setAftnAddress(hzCable, token, hzCableAddress, typeVos, interactionType);
            setMailAddress(hzCable, token, hzCableAddress, typeVos, interactionType, "AFTN");
            setFtpAddress(hzCable, token, hzCableAddress, typeVos, interactionType, "AFTN");
            setMqAddress(hzCable, token, hzCableAddress, typeVos, interactionType, "AFTN");
        }
    }

    private void handleOnlyCaac(Set<String> interactionTypes, List<CableMsgTypeVO> typeVos,
                                HzCable hzCable, HzCableAddress hzCableAddress, String token) {
        for (String interactionType : interactionTypes) {
            setFdAddress(hzCable, token, hzCableAddress, typeVos, interactionType);
            setMailAddress(hzCable, token, hzCableAddress, typeVos, interactionType, "FD");
            setFtpAddress(hzCable, token, hzCableAddress, typeVos, interactionType, "FD");
            setMqAddress(hzCable, token, hzCableAddress, typeVos, interactionType, "FD");
        }
    }

    private void handleOthers(Set<String> interactionTypes, List<CableMsgTypeVO> typeVos,
                              HzCable hzCable, HzCableAddress hzCableAddress, String token) {
        for (String interactionType : interactionTypes) {
            List<CableMsgTypeVO> FDList = typeVos.stream().filter(e -> "FD".equals(e.getType())).collect(Collectors.toList());
            List<CableMsgTypeVO> AFTNList = typeVos.stream().filter(e -> "AFTN".equals(e.getType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(FDList)){
                setMailAddress(hzCable, token, hzCableAddress, FDList, interactionType, "FD");
                setFtpAddress(hzCable, token, hzCableAddress, FDList, interactionType, "FD");
                setMqAddress(hzCable, token, hzCableAddress, FDList, interactionType, "FD");
            }else {
                setMailAddress(hzCable, token, hzCableAddress, AFTNList, interactionType, "AFTN");
                setFtpAddress(hzCable, token, hzCableAddress, AFTNList, interactionType, "AFTN");
                setMqAddress(hzCable, token, hzCableAddress, AFTNList, interactionType, "AFTN");
            }
        }
    }

    private void setMqAddress(HzCable hzCable, String token, HzCableAddress hzCableAddress, List<CableMsgTypeVO> typeVos, String interactionType, String fd) {
        if ("rabbitmq收发报文".equals(interactionType)) {
            for (CableMsgTypeVO vo : typeVos) {
                if (fd.equals(vo.getType())) {
                    Date date = new Date();
                    SimpleDateFormat sdf = new SimpleDateFormat("ddHHmm");
                    String cableNo = sdf.format(date);
                    hzCable.setSerialNo(SerialNumberGenerator.generateSerialNumber());
                    hzCable.setCableNo(cableNo);
                    hzCable.setCableTime(date);
                    hzCable.setId(null);
                    hzCable.setContent(vo.getCableMsg());
                    hzCableMapper.insert(hzCable);
                    ForwardOriginMsgVO msgVO = new ForwardOriginMsgVO();
                    msgVO.setOriginMsg(hzCable.getContent());
                    msgVO.setMsgType(hzCable.getType());
                    msgVO.setSendAddress(hzCable.getCableAddress());
                    msgVO.setReceiveMQQueue(hzCableAddress.getMqQueue());
                    msgVO.setReceiveAddress("-");
                    msgVO.setSendType("MQ");
                    msgVO.setPriority(hzCable.getPriority());
                    httpService.sendCable(msgVO, hzCable, token);
                }
            }
        }
    }

    private void setFtpAddress(HzCable hzCable, String token, HzCableAddress hzCableAddress, List<CableMsgTypeVO> typeVos, String interactionType, String fd) {
        if ("FTP收发报文".equals(interactionType)) {
            for (CableMsgTypeVO vo : typeVos) {
                if (fd.equals(vo.getType())) {
                    Date date = new Date();
                    SimpleDateFormat sdf = new SimpleDateFormat("ddHHmm");
                    String cableNo = sdf.format(date);
                    hzCable.setSerialNo(SerialNumberGenerator.generateSerialNumber());
                    hzCable.setCableNo(cableNo);
                    hzCable.setCableTime(date);
                    hzCable.setId(null);
                    hzCable.setContent(vo.getCableMsg());
                    hzCableMapper.insert(hzCable);
                    ForwardOriginMsgVO msgVO = new ForwardOriginMsgVO();
                    msgVO.setOriginMsg(hzCable.getContent());
                    msgVO.setMsgType(hzCable.getType());
                    msgVO.setSendAddress(hzCable.getCableAddress());
                    msgVO.setReceiveFtpAddress(ftpAddress);
                    msgVO.setReceiveFtpFolder(hzCableAddress.getFtpList());
                    msgVO.setSendType("FTP");
                    msgVO.setReceiveAddress("-");
                    msgVO.setPriority(hzCable.getPriority());
                    httpService.sendCable(msgVO, hzCable, token);
                }
            }
        }
    }

    private void setMailAddress(HzCable hzCable, String token, HzCableAddress hzCableAddress, List<CableMsgTypeVO> typeVos, String interactionType, String fd) {
        if ("邮箱报文".equals(interactionType)) {
            for (CableMsgTypeVO vo : typeVos) {
                if (fd.equals(vo.getType())) {
                    Date date = new Date();
                    SimpleDateFormat sdf = new SimpleDateFormat("ddHHmm");
                    String cableNo = sdf.format(date);
                    hzCable.setSerialNo(SerialNumberGenerator.generateSerialNumber());
                    hzCable.setCableNo(cableNo);
                    hzCable.setCableTime(date);
                    hzCable.setId(null);
                    hzCable.setContent(vo.getCableMsg());
                    hzCableMapper.insert(hzCable);
                    ForwardOriginMsgVO msgVO = new ForwardOriginMsgVO();
                    msgVO.setOriginMsg(hzCable.getContent());
                    msgVO.setMsgType(hzCable.getType());
                    msgVO.setSendAddress(hzCable.getCableAddress());
                    msgVO.setMailAddress(hzCableAddress.getEmailAddress());
                    msgVO.setSendType("MAIL");
                    msgVO.setReceiveAddress("-");
                    msgVO.setPriority(hzCable.getPriority());
                    httpService.sendCable(msgVO, hzCable, token);
                }
            }
        }
    }

    private void setFdAddress(HzCable hzCable, String token, HzCableAddress hzCableAddress, List<CableMsgTypeVO> typeVos, String interactionType) {
        if ("民航局报文".equals(interactionType)) {
            for (CableMsgTypeVO vo : typeVos) {
                if ("FD".equals(vo.getType())) {
                    Date date = new Date();
                    SimpleDateFormat sdf = new SimpleDateFormat("ddHHmm");
                    String cableNo = sdf.format(date);
                    hzCable.setSerialNo(SerialNumberGenerator.generateSerialNumber());
                    hzCable.setCableNo(cableNo);
                    hzCable.setCableTime(date);
                    hzCable.setId(null);
                    hzCable.setContent(vo.getCableMsg());
                    hzCableMapper.insert(hzCable);
                    ForwardOriginMsgVO msgVO = new ForwardOriginMsgVO();
                    msgVO.setOriginMsg(hzCable.getContent());
                    msgVO.setMsgType(hzCable.getType());
                    msgVO.setSendAddress(hzCable.getCableAddress());
                    msgVO.setReceiveAddress(hzCableAddress.getCaacAddress() == null ? "-" : hzCableAddress.getCaacAddress());
                    msgVO.setSendType("FD");
                    msgVO.setPriority(hzCable.getPriority());
                    httpService.sendCable(msgVO, hzCable, token);
                }
            }
        }
    }

    private void setAftnAddress(HzCable hzCable, String token, HzCableAddress hzCableAddress, List<CableMsgTypeVO> typeVos, String interactionType) {
        if ("空管报文".equals(interactionType)) {
            for (CableMsgTypeVO vo : typeVos) {
                if ("AFTN".equals(vo.getType())) {
                    Date date = new Date();
                    SimpleDateFormat sdf = new SimpleDateFormat("ddHHmm");
                    String cableNo = sdf.format(date);
                    hzCable.setSerialNo(SerialNumberGenerator.generateSerialNumber());
                    hzCable.setCableNo(cableNo);
                    hzCable.setCableTime(date);
                    hzCable.setId(null);
                    hzCable.setContent(vo.getCableMsg());
                    hzCableMapper.insert(hzCable);
                    ForwardOriginMsgVO msgVO = new ForwardOriginMsgVO();
                    msgVO.setOriginMsg(hzCable.getContent());
                    msgVO.setMsgType(hzCable.getType());
                    msgVO.setSendAddress(hzCable.getCableAddress());
                    msgVO.setSendType("AFTN");
                    msgVO.setReceiveAddress(hzCableAddress.getCableAddress());
                    msgVO.setPriority(hzCable.getPriority());
                    httpService.sendCable(msgVO, hzCable, token);
                }
            }
        }
    }

    /**
     * 修改电报数据
     *
     * @param hzCable 电报数据
     * @return 结果
     */
    @Override
    public int updateHzCable(HzCable hzCable)
    {
        return hzCableMapper.updateHzCable(hzCable);
    }

    /**
     * 删除电报数据信息
     *
     * @param id 电报数据主键
     * @return 结果
     */
    @Override
    public int deleteHzCableById(Long id)
    {
        HzCable hzCable = hzCableMapper.selectHzCableById(id);
        hzCable.setIsDel(1);
        return hzCableMapper.updateHzCable(hzCable);
    }

    /**
     * 报文数据收集运单列表数据
     * @param query 查询参数
     * @return 运单列表
     */
    @Override
    public List<CableMawbVo> selectWaybillList(CableMawbQuery query) {
        if (StringUtils.isEmpty(query.getType())){
            throw new CustomException("请选择报文类型");
        }
        List<CableMawbVo> list;
        switch (query.getType()){
            case "FSH":
                list = loadMawbMapper.selectLoadWaybill(query);
                break;
            case "FFM":
                list = loadMawbMapper.selectLoadWaybill(query);
                for (CableMawbVo cableMawbVo : list) {
                    if (cableMawbVo.getDangerCode() != null){
                        cableMawbVo.setIsDanger(1);
                    }else {
                        cableMawbVo.setIsDanger(0);
                    }
                }
                return list;
            default:
                list = mawbMapper.selectWaybillList(query);
                break;
        }
        for (CableMawbVo cableMawbVo : list) {
            String[] split = cableMawbVo.getFlightInfo().split("/");
            Long bizId = bizMapper.selectBizByFlight(split[0],split[1],"KWE-" + cableMawbVo.getDesPort());
            if (bizId != null){
                // 板箱
                List<LoadUldVo> vos = uldMawbMapper.selectListById(bizId);
                if (!CollectionUtils.isEmpty(vos)){
                    LoadUldVo loadUldVo = vos.stream().filter(e -> e.getMawbId().equals(cableMawbVo.getId())).findFirst().orElse(null);
                    if (loadUldVo != null){
                        cableMawbVo.setLoadLocation(loadUldVo.getCabin());
                        cableMawbVo.setUldCode(loadUldVo.getUld());
                    }
                }
                // 散舱
                List<Long> mawbIds = loadMawbMapper.selectListById(bizId);
                if (!CollectionUtils.isEmpty(mawbIds)){
                    boolean b = mawbIds.stream().anyMatch(e -> e.equals(cableMawbVo.getId()));
                    if (b){
                        if (cableMawbVo.getLoadLocation() != null){
                           cableMawbVo.setLoadLocation(cableMawbVo.getLoadLocation() + "," + "BLK");
                        }else {
                            cableMawbVo.setLoadLocation("BLK");
                        }
                    }
                }
            }
            if (cableMawbVo.getDangerCode() != null){
                cableMawbVo.setIsDanger(1);
            }else {
                cableMawbVo.setIsDanger(0);
            }
        }
        return list;
    }

    /**
     * 生成报文
     * @param query 生成报文参数
     * @return 报文
     */
    @Override
    public List<CableMsgTypeVO> generateCable(GenerateCableQuery query) {
        return fillMsgData(query);
    }

    /**
     * 报文数据收集集装器列表数据
     * @param query 查询参数
     * @return 集装器列表
     */
    @Override
    public List<CableUldVo> uldList(CableMawbQuery query) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        if (query.getFlightDate() != null){
            String format = sdf.format(query.getFlightDate());
            query.setQueryDate(format);
        }
        return uldMapper.selectUldList(query);
    }

    /**
     * 查询出港拉货报文运单数据
     * @param query 查询条件
     * @return 运单数据
     */
    @Override
    public List<PullCargoVo> selectPullCargoList(CableMawbQuery query) {
        List<PullCargoVo> pullCargoVos = pullDownMapper.selectPullCargoList(query);
        for (PullCargoVo pullCargoVo : pullCargoVos) {
            pullCargoVo.setFlightInfo(pullCargoVo.getFlightNo() + "/" + DATE_FORMAT.format(pullCargoVo.getExecDate()));
        }
        return pullCargoVos;
    }

    @Override
    public HzCableType selectTemplateByType(String type) {
        return hzCableTypeMapper.selectTemplateByType(type);
    }

//    @RabbitListener(queues = "receive-cable")
    public void receiveCable(Message message, Channel channel) throws IOException {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        try {
            String mes = new String(message.getBody());
            MqReceiveCableVo mqReceiveCableVo = JSONObject.parseObject(mes, MqReceiveCableVo.class);
            if (mqReceiveCableVo == null) {
                channel.basicReject(deliveryTag, false);
                return;
            }
            JSONObject msgJson = JSONObject.parseObject(mqReceiveCableVo.getMsgJson());
            if (msgJson == null) {
                channel.basicReject(deliveryTag, false);
                return;
            }
            String flightNo = "";
            String flightDate = "";
            if ("FFM".equals(mqReceiveCableVo.getMsgType())){
                String carrier = msgJson.getString("carrier");
                String no = msgJson.getString("flightNo");
                flightNo = carrier + no;
                flightDate = msgJson.getString("etd");
            }
            if ("FWB".equals(mqReceiveCableVo.getMsgType())){
                flightNo = msgJson.getString("flightNo");
                flightDate = msgJson.getString("flightDate");
            }
            HzCable cable = new HzCable();
            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("ddHHmm");
            cable.setSerialNo(SerialNumberGenerator.generateSerialNumber());
            cable.setCableNo(sdf.format(date));
            cable.setCableTime(date);
            cable.setCreateBy("系统");
            cable.setIsSend(0);
            cable.setIsAuto(1);
            cable.setStatus(1);
            cable.setType(mqReceiveCableVo.getMsgType());
            cable.setVersion(mqReceiveCableVo.getMsgVersion());
            cable.setPriority("QD");
            if (StringUtils.isNotEmpty(mqReceiveCableVo.getOrigin())){
                cable.setCableAddress(String.join(",", mqReceiveCableVo.getOrigin()));
            }
            if (mqReceiveCableVo.getAddress() != null){
                cable.setReceiveAddress(String.join(",", mqReceiveCableVo.getAddress()));
            }else {
                cable.setReceiveAddress("KWEFDCN");
            }
            cable.setFlightNo(flightNo);
            if (StringUtils.isNotEmpty(flightDate)){
                cable.setFlightDate(DATE_FORMAT.parse(flightDate));
            }
            cable.setContent(mqReceiveCableVo.getOriginMsg());
            hzCableMapper.insert(cable);
            channel.basicAck(deliveryTag, true);
        } catch (Exception e) {
            channel.basicReject(deliveryTag, false);
        }
    }


    /**
     * 填充报文数据
     * 返回值 0 成功 1 失败
     */
    private List<CableMsgTypeVO> fillMsgData(GenerateCableQuery query){
        if (CollectionUtils.isEmpty(query.getIds())){
            return null;
        }
        if (CollectionUtils.isEmpty(query.getTypeAndAddress())){
            throw new CustomException("请输入收报地址");
        }
        List<CableMsgTypeVO> voList;
        String token = getToken();
        List<List<HzCableAddress>> list = new ArrayList<>();
        List<TypeAndAddressQuery> cableAddress = query.getTypeAndAddress().stream().filter(e -> e.getType() == 0).collect(Collectors.toList());
        List<TypeAndAddressQuery> caacAddress = query.getTypeAndAddress().stream().filter(e -> e.getType() == 1).collect(Collectors.toList());
        // 电报地址
        if (!CollectionUtils.isEmpty(cableAddress)){
            List<HzCableAddress> cableAddressList = cableAddressMapper.selectAddress(cableAddress,null);
            if (!CollectionUtils.isEmpty(cableAddressList)){
                list.add(cableAddressList);
            }
        }
        // 民航局地址
        if (!CollectionUtils.isEmpty(caacAddress)){
            List<HzCableAddress> caacAddressList = cableAddressMapper.selectAddress(null,caacAddress);
            if (!CollectionUtils.isEmpty(caacAddressList)){
                list.add(caacAddressList);
            }
        }
        switch (query.getType()){
            case "FFM" :
                voList = getFFMMsg(query,token,list);
                break;
            case "FSU" :
                voList = getFSUMsg(query,token,list);
                break;
            case "FSH" :
                voList = getFSHMsg(query,token,list);
                break;
            case "SCM" :
                voList = getSCMMsg(query,token,list);
                break;
            case "UCM" :
                voList = getUCMMsg(query,token,list);
            default:
                voList = new ArrayList<>();
                break;
        }
        return voList;
    }


    private List<CableMsgTypeVO> getUCMMsg(GenerateCableQuery query, String token,List<List<HzCableAddress>> list) {
        List<CableMsgTypeVO> voList = new ArrayList<>();
        List<BaseCargoUld> ulds = uldMapper.selectBatchIds(query.getIds());
        if (CollectionUtils.isEmpty(ulds)){
            return voList;
        }
        List<BaseCargoUldVo> baseCargoUldVos = getBaseCargoUldVo(ulds);
        Map<String, List<BaseCargoUldVo>> airCompanyMap = baseCargoUldVos.stream()
                .collect(Collectors.groupingBy(BaseCargoUldVo::getGroupKey));
        for(Map.Entry<String, List<BaseCargoUldVo>> entry:airCompanyMap.entrySet()){
            List<BaseCargoUldVo> vos = airCompanyMap.get(entry.getKey());
            MsgJsonVO vo = new MsgJsonVO();
            vo.setOrigin(new String[]{query.getCableAddress()});
            vo.setPriority(new String[]{"QD"});
            vo.setSourceId("1911661412875329538");
            vo.setUniqueId("1911661412875329538");
            vo.setCarrier(vos.get(0).getAirCompany());
            vo.setFlightNo(vos.get(0).getFlightNo().substring(2));
            vo.setFlightDate(vos.get(0).getFlightDate());
            vo.setDepartureStation(vos.get(0).getDepartureStation());
            vo.setNextStation(vos.get(0).getDesPort());
            vo.setMsgType("UCM");
            UCMJsonVO ucmJsonVO = new UCMJsonVO();
            Admission admissionIn = new Admission();
            Admission admissionOut = new Admission();
            if(ObjectUtils.equals("IN",vos.get(0).getInOut())){
                admissionIn.setCarrier(vos.get(0).getAirCompany());
                admissionIn.setFlightNo(vos.get(0).getFlightNo().substring(2));
                admissionIn.setOriAirport(vos.get(0).getDesPort());
                admissionIn.setFlightDate(vos.get(0).getFlightDate());
            }
            if(ObjectUtils.equals("OUT",vos.get(0).getInOut())){
                admissionOut.setCarrier(vos.get(0).getAirCompany());
                admissionOut.setFlightNo(vos.get(0).getFlightNo().substring(2));
                admissionOut.setOriAirport(vos.get(0).getDesPort());
                admissionOut.setFlightDate(vos.get(0).getFlightDate());
            }
            List<AdmissionUld> admissionUldIns = new ArrayList<>();
            List<AdmissionUld> admissionUldOuts = new ArrayList<>();
            for(BaseCargoUldVo uld : vos){
                if(ObjectUtils.equals("IN",uld.getInOut())){
                    AdmissionUld admissionUld = new AdmissionUld();
                    admissionUld.setUldNo(uld.getCode());
                    admissionUld.setUldDestination(uld.getDesPort());
                    admissionUldIns.add(admissionUld);
                }
                if(ObjectUtils.equals("OUT",uld.getInOut())){
                    AdmissionUld admissionUld = new AdmissionUld();
                    admissionUld.setUldNo(uld.getCode());
                    admissionUld.setUldDestination(uld.getDesPort());
                    admissionUldOuts.add(admissionUld);
                }
            }
            admissionIn.setUld(admissionUldIns);
            admissionOut.setUld(admissionUldOuts);
            ucmJsonVO.setIn(admissionIn);
            ucmJsonVO.setOut(admissionOut);
            vo.setMsgJson(JSON.toJSONString(ucmJsonVO));
            for (List<HzCableAddress> addresses : list) {
                List<CableMsgTypeVO> typeVoList = setInteractionType(addresses, vo, token);
                voList.addAll(typeVoList);
            }
        }
        return voList;
    }

    private List<CableMsgTypeVO> getSCMMsg(GenerateCableQuery query, String token,List<List<HzCableAddress>> list) {
        List<CableMsgTypeVO> voList = new ArrayList<>();
        List<BaseCargoUld> ulds = uldMapper.selectBatchIds(query.getIds());
        if (CollectionUtils.isEmpty(ulds)){
            return voList;
        }
        List<BaseCargoUldVo> baseCargoUldVos = getBaseCargoUldVo(ulds);
        Map<String, List<BaseCargoUldVo>> airCompanyMap = baseCargoUldVos.stream()
                .collect(Collectors.groupingBy(BaseCargoUldVo::getGroupKey));
        for (Map.Entry<String, List<BaseCargoUldVo>> entry : airCompanyMap.entrySet()) {
            List<BaseCargoUldVo> vos = airCompanyMap.get(entry.getKey());
            MsgJsonVO vo = new MsgJsonVO();
            vo.setOrigin(new String[]{query.getCableAddress()});
            vo.setPriority(new String[]{"QD"});
            vo.setSourceId("1911661412875329538");
            vo.setUniqueId("1911661412875329538");
            vo.setMsgType("SCM");
            vo.setCarrier(vos.get(0).getAirCompany());
            vo.setFlightNo(vos.get(0).getFlightNo().substring(2));
            vo.setFlightDate(vos.get(0).getFlightDate());
            vo.setDepartureStation(vos.get(0).getDepartureStation());
            vo.setNextStation(vos.get(0).getDesPort());
            SCMJsonVO scmJsonVO = new SCMJsonVO();
            scmJsonVO.setOperAirport("KWE");
            //集装器检查时间 - 暂取入场 出场时间
            scmJsonVO.setCheckDate(vos.get(0).getCheckDate());
            scmJsonVO.setCheckTime(vos.get(0).getCheckTime());
            List<SCMUldVo> scmUldVos = new ArrayList<>();
            for(BaseCargoUldVo uld : vos){
                SCMUldVo scmUldVo = new SCMUldVo();
                scmUldVo.setUldType(uld.getType());
                scmUldVo.setUldNo(uld.getCode());
                if (StringUtils.isNotNull(uld.getFlightId()) && StringUtils.isNotNull(uld.getCode())) {
                    int count = loadMapper.selectCountByUldNo(uld.getCode(), uld.getFlightId());
                    scmUldVo.setTotalDetailfor("T" + count);
                }
                scmUldVos.add(scmUldVo);
            }
            scmJsonVO.setUld(scmUldVos);
            vo.setMsgJson(JSON.toJSONString(scmJsonVO));
           for (List<HzCableAddress> addresses : list) {
               List<CableMsgTypeVO> typeVoList = setInteractionType(addresses, vo, token);
               voList.addAll(typeVoList);
           }
        }
        return voList;
    }

    private List<CableMsgTypeVO> getFSHMsg(GenerateCableQuery query, String token,List<List<HzCableAddress>> list) {
        List<CableMsgTypeVO> voList = new ArrayList<>();
        List<MsgFlightInfoVO> flightInfoVoList = uldMawbMapper.selectFlightInfoById(query.getIds());
        if (CollectionUtils.isEmpty(flightInfoVoList)) {
            return null;
        }
        List<QHDBIdVO> qhdbIdVos = flightInfoMapper.selectFlightIdByWaybillId(query.getIds());
        Map<Long, List<QHDBIdVO>> mapIds = qhdbIdVos.stream()
                .collect(Collectors.groupingBy(QHDBIdVO::getFlightId));
        List<MsgFlightInfoVO> collect = flightInfoVoList.stream().distinct().collect(Collectors.toList());
        for (MsgFlightInfoVO infoVo : collect) {
            MsgJsonVO vo = new MsgJsonVO();
            vo.setMsgType("SPH");
            vo.setOrigin(new String[]{query.getCableAddress()});
            vo.setPriority(new String[]{"QD"});
            vo.setSourceId("1727215332084514819");
            vo.setUniqueId("1727215332084514819");
            vo.setMsgVersion("5");
            vo.setCarrier(infoVo.getCarrier());
            vo.setFlightDate(infoVo.getFlightDate());
            vo.setDepartureStation(infoVo.getDepartureStation());
            vo.setFlightNo(infoVo.getFlightNo());
            vo.setNextStation(infoVo.getNextStation());
            vo.setFlightType(infoVo.getFlightType());
            QHDBJsonVO jsonVo = new QHDBJsonVO();
            jsonVo.setArrivalAirport(infoVo.getNextStation());
            jsonVo.setCarrier(infoVo.getCarrier());
            jsonVo.setEtd(DATE_FORMAT.format(infoVo.getEtd()));
            jsonVo.setFlightNo(infoVo.getFlightNo());
            jsonVo.setOriAirport(infoVo.getDepartureStation());
            List<QHDBDetailVO> details = new ArrayList<>();
            List<QHDBIdVO> waybillIds = mapIds.get(infoVo.getFlightId());
            for (QHDBIdVO qhdbIdVO : waybillIds){
                QHDBDetailVO qhdbDetailVO = new QHDBDetailVO();
                Mawb mawb = mawbMapper.selectById(qhdbIdVO.getWaybillId());
                qhdbDetailVO.setDepAirport(infoVo.getDepartureStation());
                qhdbDetailVO.setDesAirport(infoVo.getNextStation());
                qhdbDetailVO.setGoodsName(mawb.getCargoName());
                qhdbDetailVO.setLoaduldFirst("");
                qhdbDetailVO.setLoaduldOther("");
                String substring = mawb.getWaybillCode().substring(4);
                StringBuilder stringBuilder = new StringBuilder(substring);
                qhdbDetailVO.setMawbNo(stringBuilder.insert(3, "-").toString());
                qhdbDetailVO.setShc("");
                qhdbDetailVO.setShipmentDescriptioncode("T");
                qhdbDetailVO.setSpecialHandlingcodeother("");
                qhdbDetailVO.setTotalPieces(mawb.getQuantity().toString());
                qhdbDetailVO.setWeight(mawb.getWeight().toString());
                qhdbDetailVO.setWeightUnit("K");
                details.add(qhdbDetailVO);
            }
            jsonVo.setConsignmentDetail(details);
            vo.setMsgJson(JSON.toJSONString(jsonVo));
            for (List<HzCableAddress> addresses : list) {
                List<CableMsgTypeVO> typeVoList = setInteractionType(addresses, vo, token);
                voList.addAll(typeVoList);
            }
        }
        return voList;
    }

    private List<CableMsgTypeVO> getFSUMsg(GenerateCableQuery query, String token,List<List<HzCableAddress>> addressList) {
        List<CableMsgTypeVO> voList = new ArrayList<>();
        List<MsgFlightInfoVO> flightInfoVoList = pullDownMapper.selectFlightByWaybillId(query.getIds());
        if (CollectionUtils.isEmpty(flightInfoVoList)) {
            return null;
        }
        List<MsgFlightInfoVO> collect = flightInfoVoList.stream().distinct().collect(Collectors.toList());
        Map<Long, List<MsgFlightInfoVO>> flightInfoMap = collect.stream().collect(Collectors.groupingBy(MsgFlightInfoVO::getFlightId));
        for (Map.Entry<Long, List<MsgFlightInfoVO>> longListEntry : flightInfoMap.entrySet()) {
            FlightInfo info = flightInfoMapper.selectById(longListEntry.getKey());
            MsgJsonVO vo = new MsgJsonVO();
            vo.setMsgType("OFD");
            vo.setOrigin(new String[]{query.getCableAddress()});
            vo.setPriority(new String[]{"QD"});
            vo.setSourceId("1727215332084514819");
            vo.setUniqueId("1727215332084514819");
            vo.setMsgVersion("5");
            vo.setCarrier(info.getAirWays());
            vo.setDepartureStation(info.getStartStation());
            vo.setFlightDate(DATE_FORMAT.format(info.getExecDate()));
            vo.setNextStation(info.getTerminalStation());
            vo.setFlightNo(info.getFlightNo());
            OFDJsonVO ofdJsonVO = new OFDJsonVO();
            ofdJsonVO.setCarrier(info.getAirWays());
            ofdJsonVO.setFlightNo(info.getFlightNo());
            ofdJsonVO.setEtd(info.getStartSchemeTakeoffTime().format(DATE_TIME_FORMATTER));
            List<ConsignmentDetail> list = new ArrayList<>();
            for (MsgFlightInfoVO infoVO : longListEntry.getValue()) {
                FSUJsonVO jsonVO = pullDownMapper.selectFSUDataList(infoVO);
                ConsignmentDetail detail = new ConsignmentDetail();
                if(infoVO.getWaybillCode().startsWith("AWBA")){
                    String substring = infoVO.getWaybillCode().substring(4);
                    StringBuilder stringBuilder = new StringBuilder(substring);
                    detail.setMawbNo(stringBuilder.insert(3, "-").toString());
                }else{
                    String substring = infoVO.getWaybillCode().substring(4);
                    StringBuilder stringBuilder = new StringBuilder(substring);
                    detail.setMawbNo(stringBuilder.insert(2, "-").toString());
                }
                detail.setDepAirport(jsonVO.getDepAirport());
                detail.setDesAirport(jsonVO.getDesAirport());
                detail.setTotalPieces(jsonVO.getPieces());
                detail.setWeight(jsonVO.getWeight());
                list.add(detail);
            }
            ofdJsonVO.setConsignmentDetail(list);
            vo.setMsgJson(JSON.toJSONString(ofdJsonVO));
            for (List<HzCableAddress> addresses : addressList) {
                List<CableMsgTypeVO> typeVoList = setInteractionType(addresses, vo, token);
                voList.addAll(typeVoList);
            }
        }
        return voList;
    }

    private List<CableMsgTypeVO> getFFMMsg(GenerateCableQuery query, String token,List<List<HzCableAddress>> list){
        List<CableMsgTypeVO> voList = new ArrayList<>();
        List<MsgFlightInfoVO> flightInfoVoList = uldMawbMapper.selectFlightInfoById(query.getIds());
        if (CollectionUtils.isEmpty(flightInfoVoList)) {
            return null;
        }
        List<MsgFlightInfoVO> collect = flightInfoVoList.stream().distinct().collect(Collectors.toList());
        for (MsgFlightInfoVO infoVo : collect) {
            MsgJsonVO vo = new MsgJsonVO();
            vo.setMsgType("FFM");
            vo.setOrigin(new String[]{query.getCableAddress()});
            vo.setPriority(new String[]{"QD"});
            vo.setSourceId("1727215332084514819");
            vo.setUniqueId("1727215332084514819");
            vo.setMsgVersion("5");
            vo.setCarrier(infoVo.getCarrier());
            vo.setFlightDate(infoVo.getFlightDate());
            vo.setDepartureStation(infoVo.getDepartureStation());
            vo.setFlightNo(infoVo.getFlightNo());
            vo.setNextStation(infoVo.getNextStation());
            vo.setFlightType(infoVo.getFlightType());
            FFMJsonVO ffmJsonVo = new FFMJsonVO();
            ffmJsonVo.setCarrier(infoVo.getCarrier());
            ffmJsonVo.setAircraftRegistration(infoVo.getAircraftRegistration());
            ffmJsonVo.setEtd(infoVo.getEtd());
            ffmJsonVo.setFlightNo(infoVo.getFlightNo());
            ffmJsonVo.setOriAirport(infoVo.getDepartureStation());
            PointOfUnloading unloading = new PointOfUnloading();
            unloading.setEta(infoVo.getEta());
            unloading.setArrAirport(infoVo.getNextStation());
            String date = infoVo.getStartSchemeTakeoffTime().toLocalDate().toString();
            unloading.setScheduledDepartureDate(date);
            String time = infoVo.getStartSchemeTakeoffTime().toLocalTime().format(TIME_FORMATTER);
            unloading.setScheduledDepartureTime(time);
            List<UldVO> uldVoList = uldMapper.selectUldByFlightId(infoVo.getFlightId(),query.getIds());
            List<ConsignmentDetail> bulk = new ArrayList<>();
            for (UldVO uldVO : uldVoList) {
                String uldNo = uldVO.getUldNo();
                List<ConsignmentDetail> detailList = loadMawbMapper.selectDetailList(query.getIds(), uldNo);
                if (!CollectionUtils.isEmpty(detailList)) {
                    for (ConsignmentDetail detail : detailList) {
                        if (StringUtils.isEmpty(detail.getGoodsName())){
                            detail.setGoodsName("-");
                        }
                        String mawbNo = detail.getMawbNo();
                        if(mawbNo.startsWith("AWBA")){
                            String substring = mawbNo.substring(4);
                            StringBuilder stringBuilder = new StringBuilder(substring);
                            detail.setMawbNo(stringBuilder.insert(3, "-").toString());
                        }else{
                            String substring = mawbNo.substring(4);
                            StringBuilder stringBuilder = new StringBuilder(substring);
                            detail.setMawbNo(stringBuilder.insert(2, "-").toString());
                        }

                        detail.setShipmentDescriptionCode("T");
                        detail.setTotalPieces(null);
                        if (StringUtils.isNotEmpty(detail.getShcStr())) {
                            detail.setShc(detail.getShcStr().split(","));
                        }
                        if (StringUtils.isNotEmpty(detail.getVolume())) {
                            detail.setVolumeUnit("MC");
                        }
                        if (StringUtils.isNotEmpty(detail.getWeight())) {
                            detail.setWeightUnit("KG");
                        }
                    }
                    if ("BLK".equals(uldNo)){
                        bulk.addAll(detailList);
                    }else {
                        Matcher matcher = LETTERS_PATTERN.matcher(uldNo);
                        if (matcher.find()) {
                            String letters = matcher.group(1);
                            uldVO.setUldType(letters);
                            String numbers = matcher.group(2);
                            uldVO.setUldNum(numbers);
                        }
                        uldVO.setUldOwner(infoVo.getCarrier());
                        uldVO.setUldNo(uldNo + infoVo.getCarrier());
                        uldVO.setConsignmentDetail(detailList);
                    }
                }
            }
            if (!bulk.isEmpty()){
                unloading.setBulk(bulk);
            }
            if (!uldVoList.isEmpty()){
                unloading.setUld(uldVoList);
            }
            ffmJsonVo.setPointOfUnloading(Collections.singletonList(unloading));
            vo.setMsgJson(JSON.toJSONString(ffmJsonVo));
            for (List<HzCableAddress> addressList : list) {
                List<CableMsgTypeVO> typeVoList = setInteractionType(addressList, vo, token);
                voList.addAll(typeVoList);
            }
        }
        return voList;
    }

    private List<BaseCargoUldVo> getBaseCargoUldVo(List<BaseCargoUld> ulds){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        SimpleDateFormat formatterDate = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat formatterTime = new SimpleDateFormat("HH:mm");
        List<BaseCargoUldVo> baseCargoUldVos = new ArrayList<>();
        for(BaseCargoUld baseCargoUld : ulds){
            BaseCargoUldVo baseCargoUldVo = new BaseCargoUldVo();
            BeanUtils.copyProperties(baseCargoUld, baseCargoUldVo);
            if(StringUtils.isNotNull(baseCargoUld.getExitFlight())){
                baseCargoUldVo.setFlightNo(baseCargoUld.getExitFlight());
                baseCargoUldVo.setInOut("OUT");
                if(StringUtils.isNotNull(baseCargoUld.getExitTime())){
                    FlightInfo flightInfo = flightInfoMapper.selectByFlightNoAndDate(baseCargoUld.getEntranceFlight(),
                            DATE_FORMAT.format(baseCargoUld.getExitTime()),"D");
                    if(flightInfo != null){
                        baseCargoUldVo.setFlightDate(flightInfo.getStartSchemeTakeoffTime().format(formatter));
                        baseCargoUldVo.setCheckDate(formatterDate.format(baseCargoUld.getExitTime()));
                        baseCargoUldVo.setCheckTime(formatterTime.format(baseCargoUld.getExitTime()));
                        baseCargoUldVo.setDepartureStation(flightInfo.getStartStation());
                        baseCargoUldVo.setDesPort(flightInfo.getTerminalStation());
                        baseCargoUldVo.setFlightId(flightInfo.getFlightId());
                        baseCargoUldVo.setGroupKey(baseCargoUldVo.getAirCompany() +
                                ((baseCargoUldVo.getFlightNo() != null) ? baseCargoUldVo.getFlightNo() : "") +
                                ((baseCargoUldVo.getFlightDate() != null) ? baseCargoUldVo.getFlightDate() : ""));
                        baseCargoUldVos.add(baseCargoUldVo);
                    }
                }
                continue;
            }
            if(StringUtils.isNotNull(baseCargoUld.getEntranceFlight())){
                baseCargoUldVo.setFlightNo(baseCargoUld.getEntranceFlight());
                baseCargoUldVo.setInOut("IN");
                if(StringUtils.isNotNull(baseCargoUld.getEntranceTime())){
                    FlightInfo flightInfo = flightInfoMapper.selectByFlightNoAndDate(baseCargoUld.getEntranceFlight(),
                            DATE_FORMAT.format(baseCargoUld.getEntranceTime()),"A");
                    if(flightInfo != null){
                        baseCargoUldVo.setFlightDate(flightInfo.getStartSchemeTakeoffTime().format(formatter));
                        baseCargoUldVo.setCheckDate(formatterDate.format(baseCargoUld.getEntranceTime()));
                        baseCargoUldVo.setCheckTime(formatterTime.format(baseCargoUld.getEntranceTime()));
                        baseCargoUldVo.setDepartureStation(flightInfo.getStartStation());
                        baseCargoUldVo.setDesPort(flightInfo.getTerminalStation());
                        baseCargoUldVo.setFlightId(flightInfo.getFlightId());
                        baseCargoUldVo.setGroupKey(baseCargoUldVo.getAirCompany() +
                                ((baseCargoUldVo.getFlightNo() != null) ? baseCargoUldVo.getFlightNo() : "") +
                                ((baseCargoUldVo.getFlightDate() != null) ? baseCargoUldVo.getFlightDate() : ""));
                        baseCargoUldVos.add(baseCargoUldVo);
                    }
                }
            }
        }
        return baseCargoUldVos;
    }

    private void restExchange(MsgJsonVO vo,String token,StringBuilder sb){
        HttpHeaders header = setHeaders();
        header.add("X-Access-Token", token);
        HttpEntity<?> httpEntity = new HttpEntity<>(vo, header);
        System.out.println("参数：" + JSON.toJSONString(vo));
        ResponseEntity<JSONObject> exchange = restTemplate.exchange(getMsg, HttpMethod.POST, httpEntity, JSONObject.class);
        if (exchange.getBody() != null) {
            if (!"0".equals(exchange.getBody().getString("code"))) {
                throw new CustomException(exchange.getBody().getString("msg"));
            }
            JSONObject data = exchange.getBody().getJSONObject("data");
            String msgContent = data.getString("msgContent");
            sb.append(msgContent).append("\n");
        }
    }


    private HttpHeaders setHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType("application/json;charset=UTF-8"));
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        headers.add("Accept-Charset", "UTF-8");
        return headers;
    }

    private String getToken(){
        System.out.println("*********调用登录接口获取token*********");
        String token = "";
        HttpHeaders headers = setHeaders();
        HttpEntity<?> httpEntity = new HttpEntity<>(headers);
        ResponseEntity<JSONObject> exchange = restTemplate.exchange(loginUrl + account, HttpMethod.GET, httpEntity, JSONObject.class);
        if (exchange.getBody() != null) {
            if (!"0".equals(exchange.getBody().getString("code"))) {
                throw new CustomException(exchange.getBody().getString("message"));
            }
            JSONObject data = exchange.getBody().getJSONObject("data");
            token = data.getString("token");
            System.out.println(token);
        }
        return token;
    }

    private void writeVoListToJsonFile(List<MsgJsonVO> voList) {
        try (FileWriter writer = new FileWriter("xx")) {
            // 使用 FastJSON2 将 voList 转换为 JSON 字符串
            String jsonString = JSON.toJSONString(voList);
            writer.write(jsonString);
            System.out.println("JSON 数据已成功写入文件: " + "xx");
        } catch (IOException e) {
            System.err.println("写入文件时发生错误: " + e.getMessage());
        }
    }

    public List<CableMsgTypeVO> setInteractionType(List<HzCableAddress> cableAddresses, MsgJsonVO vo, String token) {
        List<CableMsgTypeVO> list = new ArrayList<>();
        if (cableAddresses == null || cableAddresses.isEmpty()) {
            return list;
        }
        for (HzCableAddress address : cableAddresses) {
            List<String> interactionTypes = new ArrayList<>();
            String types = address.getInteractionTypes();
            if (StringUtils.isNotEmpty(types)) {
                interactionTypes = Arrays.stream(types.split(","))
                        .map(String::trim)
                        .filter(StringUtils::isNotEmpty)
                        .collect(Collectors.toList());
            }

            // 处理邮箱、FTP等通用字段
            List<String> emailAddresses = new ArrayList<>();
            List<String> ftpList = new ArrayList<>();
            List<String> mqQueueList = new ArrayList<>();
            List<String> sendTypes = new ArrayList<>();
            for (String type : interactionTypes) {
                switch (type) {
                    case "邮箱报文":
                        if (StringUtils.isNotEmpty(address.getEmailAddress())) {
                            emailAddresses.add(address.getEmailAddress());
                        }
                        break;
                    case "FTP收发报文":
                        if (StringUtils.isNotEmpty(address.getFtpList())) {
                            ftpList.add(address.getFtpList());
                        }
                        break;
                    case "rabbitmq收发报文":
                        if (StringUtils.isNotEmpty(address.getMqQueue())) {
                            mqQueueList.add(address.getMqQueue());
                        }
                        break;
                    default:
                        break;
                }
                if (typeToCodeMap.containsKey(type)) {
                    sendTypes.add(typeToCodeMap.get(type));
                }
            }
            if (!emailAddresses.isEmpty()) {
                vo.setReceiveMailAddress(emailAddresses.toArray(new String[0]));
            }
            if (!ftpList.isEmpty()) {
                vo.setReceiveFtpFolder(String.join(",",ftpList));
            }
            if (!mqQueueList.isEmpty()) {
                vo.setReceiveMQQueue(String.join(",",mqQueueList));
            }
            // 判断当前这个 address 支持哪些交互方式
            boolean hasAirControl = interactionTypes.contains("空管报文");
            boolean hasCaac = interactionTypes.contains("民航局报文");

            if (hasAirControl) {
                sendTypes.add("AFTN");
            }
            if (hasCaac) {
                sendTypes.add("FD");
            }
            vo.setSendType(String.join(",", sendTypes));

            // 场景一：只有空管报文
            if (hasAirControl && !hasCaac) {
                String useAddress = address.getCableAddress();
                if (StringUtils.isEmpty(useAddress)) {
                    useAddress = address.getCaacAddress();
                }
                if (StringUtils.isNotEmpty(useAddress)) {
                    StringBuilder sb = new StringBuilder();
                    vo.setAddress(new String[]{useAddress});
                    restExchange(vo, token, sb);
                    CableMsgTypeVO typeVo = new CableMsgTypeVO();
                    typeVo.setType("AFTN");
                    typeVo.setCableMsg(sb.toString());
                    typeVo.setId(address.getId());
                    list.add(typeVo);
                }

                // 场景二：只有民航局报文
            } else if (!hasAirControl && hasCaac) {
                String useAddress = address.getCaacAddress();
                if (StringUtils.isEmpty(useAddress)) {
                    useAddress = address.getCableAddress();
                }
                if (StringUtils.isNotEmpty(useAddress)) {
                    StringBuilder sb = new StringBuilder();
                    vo.setAddress(new String[]{useAddress});
                    restExchange(vo, token, sb);
                    CableMsgTypeVO typeVo = new CableMsgTypeVO();
                    typeVo.setType("FD");
                    typeVo.setCableMsg(sb.toString());
                    typeVo.setId(address.getId());
                    list.add(typeVo);
                }

                // 场景三：同时有空管和民航局报文
            } else if (hasAirControl) {
                List<String> addressesToUse = new ArrayList<>();

                if (StringUtils.isNotEmpty(address.getCableAddress())) {
                    addressesToUse.add(address.getCableAddress());
                }
                if (StringUtils.isNotEmpty(address.getCaacAddress())) {
                    addressesToUse.add(address.getCaacAddress());
                }

                if (!addressesToUse.isEmpty()) {
                    for (String addressToUse : addressesToUse) {
                        StringBuilder sb = new StringBuilder();
                        vo.setAddress(new String[]{addressToUse});
                        restExchange(vo, token, sb);
                        CableMsgTypeVO typeVo = new CableMsgTypeVO();
                        typeVo.setCableMsg(sb.toString());
                        typeVo.setId(address.getId());
                        if (addressToUse.equals(address.getCableAddress())){
                            typeVo.setType("AFTN");
                            list.add(typeVo);
                        }
                        if (addressToUse.equals(address.getCaacAddress())){
                            typeVo.setType("FD");
                            list.add(typeVo);
                        }
                    }
                }
            }
        }
        return list;
    }

    private static final Map<String, String> typeToCodeMap = new HashMap<>();
    static {
        typeToCodeMap.put("空管报文", "AFTN");
        typeToCodeMap.put("民航局报文", "FD");
        typeToCodeMap.put("邮箱报文", "MAIL");
        typeToCodeMap.put("rabbitmq收发报文", "MQ");
        typeToCodeMap.put("FTP收发报文", "FTP");
    }
}
