package com.gzairports.hz.business.departure.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 运单卸下表
 *
 * <AUTHOR>
 * @date 2024-10-11
 */
@Data
@TableName("hz_dep_dis_board")
public class HzDisBoard {

    /** 组货数据id */
    private Long id;

    /** 航班配载id */
    private Long flightId;

    /** 运单id */
    private Long waybillId;

    /** 卸下件数 */
    private Integer quantity;

    /** 卸下重量 */
    private BigDecimal weight;

    /** 板车号 */
    private String uld;

    /** 操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operTime;

    /** 操作人 */
    private String operName;

    /** 是否配载 */
    private Integer isLoad;

    /** 是否处理*/
    private Integer isHandle;
}
