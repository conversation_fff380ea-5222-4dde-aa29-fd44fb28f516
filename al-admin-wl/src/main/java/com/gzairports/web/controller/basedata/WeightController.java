package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BaseWeight;
import com.gzairports.common.basedata.domain.query.WeightUnitQuery;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.common.basedata.service.IWeightService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 多重量单位Controller
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@RestController
@RequestMapping("/base/weight")
@Api(tags = "多重量单位数据接口")
public class WeightController extends BaseController {

    @Autowired
    private IWeightService weightService;
    /**
     * 查询重量单位列表
     */
//    @PreAuthorize("@ss.hasPermi('base:weight:weightUnitList')")
    @GetMapping("/weightUnitList")
    @ApiOperation(value = "查询重量单位列表")
    public TableDataInfo weightUnitList(WeightUnitQuery query)
    {
        startPage();
        List<BaseWeight> list = weightService.selectWeightUnitList(query);
        return getDataTable(list);
    }

    /**
     * 导出重量单位
     */
    @PreAuthorize("@ss.hasPermi('base:weight:exportWeightUnit')")
    @Log(title = "导出重量单位", businessType = BusinessType.EXPORT)
    @PostMapping("/exportWeightUnit")
    @ApiOperation(value = "导出重量单位")
    public void exportWeightUnit(HttpServletResponse response, WeightUnitQuery query)
    {
        List<BaseWeight> list = weightService.selectWeightUnitList(query);
        ExcelUtil<BaseWeight> util = new ExcelUtil<BaseWeight>(BaseWeight.class);
        util.exportExcel(response, list, "重量单位");
    }

}
