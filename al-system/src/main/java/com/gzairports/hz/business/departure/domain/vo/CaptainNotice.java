package com.gzairports.hz.business.departure.domain.vo;

import com.gzairports.common.pdf.PdfPrintAnnotation;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static com.gzairports.common.pdf.PdfPrintAnnotation.PdfFieldType.LIST;
import static com.gzairports.common.pdf.PdfPrintAnnotation.PdfFieldType.TEXT;

/**
 * 航班配载机长通知单打印
 * <AUTHOR>
 * @date 2024-09-18
 */
@Data
public class CaptainNotice {

    /** 装机站 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "sourcePort")
    private String sourcePort;

    /** 航班号 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "flightNo")
    private String flightNo;

    /** 航班日期 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "execDate")
    private String execDate;

    /** 飞机注册号 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "craftNo")
    private String craftNo;

    /** 危险货物集合 */
    @PdfPrintAnnotation(pdfFieldType = LIST, pdfFieldName = "dangerNoticeVos")
    private List<DangerNoticeVo> dangerNoticeVos;

    /** 特种货物集合 */
    @PdfPrintAnnotation(pdfFieldType = LIST, pdfFieldName = "specialNoticeVos")
    private List<SpecialNoticeVo> specialNoticeVos;

    /** 当前页 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "pageNum")
    private Integer pageNum;

    /** 总页数 */
    @PdfPrintAnnotation(pdfFieldType = TEXT, pdfFieldName = "pageSize")
    private Integer pageSize;
}
