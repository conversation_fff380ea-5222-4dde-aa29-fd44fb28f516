package com.gzairports.common.bank.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.common.bank.domain.HzBankOrderSerial;
import com.gzairports.common.bank.domain.HzBankOrderWaybill;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @author: lan
 * @create: 2025-03-19 11:42
 **/

@Mapper
public interface HzBankOrderSerialMapper extends BaseMapper<HzBankOrderSerial> {

    List<HzBankOrderSerial> selectListByOrderWaybillId(Long id);

    /**
     * 查询上一次网关支付的流水号
     * */
    String selectSerialNoPay(Long id);

    /**
     * 查询上一次退款的流水号
     * */
    String selectSerialNoRefund(Long id);
}
