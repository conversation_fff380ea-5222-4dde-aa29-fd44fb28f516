package com.gzairports.hz.business.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 运单状态返回数据
 * <AUTHOR>
 * @date 2024-06-221
 */
@Data
public class WaybillStatusVo {

    /** 主键id */
    private Long id;

    /** 运单号 */
    private String waybillCode;

    /** 交运代理人 */
    private String shippingAgent;

    /** 换单 */
    private Integer switchBill;

    /** 进港中转 */
    private Integer transferBill;

    /** 状态 STAGING 暂存 NORMAL 正常 INVALID 作废*/
    private String status;

    /** 始发站 */
    private String sourcePort;

    /** 目的站 */
    private String desPort;

    /** 航班号 */
    private String flightNo1;

    /** 航班日期 */
    private Date flightDate1;

    /** 航班号 */
    private String flightNo2;

    /** 航班日期 */
    private Date flightDate2;

    /** 航班号 */
    private String flightNo3;

    /** 航班日期 */
    private Date flightDate3;

    /** 承运人1 */
    private String carrier1;

    /** 到达站1 */
    private String des1;

    /** 承运人2 */
    private String carrier2;

    /** 到达站2 */
    private String des2;

    /** 承运人3 */
    private String carrier3;

    /** 到达站3 */
    private String des3;

    /** 发货人简称 */
    private String shipperAbb;

    /** 发货人 */
    private String shipper;

    /** 发货人电话 */
    private String shipperPhone;

    /** 发货人地址 */
    private String shipperAddress;

    /** 发货人地区 */
    private String shipperRegion;

    /** 收货人简称 */
    private String consignAbb;

    /** 收货人 */
    private String consign;

    /** 收货人电话 */
    private String consignPhone;

    /** 收货人地址 */
    private String consignAddress;

    /** 收货人地区 */
    private String consignRegion;

    /** 代理人公司 */
    private String agentCompany;

    /** 代理人识别码 */
    private String agentCode;

    /** 城市 */
    private String city;

    /** 结算注意事项 */
    private String settlementNotes;

    /** 储运注意事项 */
    private String storageTransportNotes;

    /** 海关监管 */
    private Integer customsSupervision;

    /** 特货代码1 */
    private String specialCargoCode1;

    /** 特货代码2 */
    private String specialCargoCode2;

    /** 特货代码3 */
    private String specialCargoCode3;

    /** 其他特货代码 */
    private String otherSpecialCargoCode;

    /** 品名 */
    private String cargoName;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 计费重量 */
    private BigDecimal chargeWeight;

    /** 体积（M3） */
    private BigDecimal volume;

    /** 尺寸 */
    private String size;

    /** 费率 */
    private BigDecimal ratePerKg;

    /** 航空运费 */
    private BigDecimal airFreight;

    /** 填开时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date writeTime;

    /** 填开地点 */
    private String writeLocation;

    /** 填开人 */
    private String writer;

    /** 所属单位 */
    private String deptName;

    private Long deptId;

}
