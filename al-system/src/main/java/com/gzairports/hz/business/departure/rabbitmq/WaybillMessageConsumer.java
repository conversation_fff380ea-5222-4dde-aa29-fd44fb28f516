package com.gzairports.hz.business.departure.rabbitmq;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.gzairports.common.message.domain.vo.SocketMessageVo;
import com.gzairports.common.system.mapper.SysUserMapper;
import com.gzairports.hz.websocket.HzWebSocketServer;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: lan
 * @Desc: 监听器-消费者
 * @create: 2024-10-22 15:01
 **/

@Component
public class WaybillMessageConsumer {

    @Autowired
    private HzWebSocketServer webSocketServer;

    @Autowired
    private SysUserMapper sysUserMapper;

    private static final Logger logger = LoggerFactory.getLogger(Consumer.class);


    @RabbitListener(queues = "WaybillMessage")
    public void customer(Message message, Channel channel) throws IOException {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        try {
            //提取消息字节数组到字符串
            byte[] bytes = message.getBody();
            String mes = new String(bytes);
            SocketMessageVo vo = JSONObject.parseObject(mes, SocketMessageVo.class);
            //根据type不同得到需要发送消息的用户id集合
            List<Long> userIdList = getUserIdList(vo.getType(), vo.getDeptId());
            //还要分一下 给货站发给物流发 这里应该没影响,只是存入redis,拿的话在物流那边拿一下
            if (userIdList != null) {
                webSocketServer.sendMessageToUser(userIdList, vo);
            }
            channel.basicAck(deliveryTag,true);
        }catch (Exception e){
            logger.error("获取失败：" + e);
            channel.basicReject(deliveryTag,false);
        }
    }

    private List<Long> getUserIdList(Integer type,Long deptId) {
        switch (type) {
            case 1:
                //收运与制单数据不符合消息:代理人、出港库管、吨控、复重
                List<Long> userLists = sysUserMapper.selectUserListForWarehouseManager();
                userLists.addAll(sysUserMapper.selectUserListForTonnageControl());
                userLists.addAll(sysUserMapper.selectUserListForCompoundWeight());
                if (deptId != null && deptId != 0){
                userLists.addAll(sysUserMapper.selectUserListForDeptId(deptId));}
                //对userLists进行去重
                return userLists.stream().distinct().collect(Collectors.toList());
            case 2:
                //服务申请消息:出港库管
                return sysUserMapper.selectUserListForWarehouseManager();
            case 3:
                //服务审核消息:代理人、出港库管
                List<Long> userListForManager = sysUserMapper.selectUserListForWarehouseManager();
                if (deptId != null && deptId != 0){
                    userListForManager.addAll(sysUserMapper.selectUserListForDeptId(deptId));}
                return userListForManager.stream().distinct().collect(Collectors.toList());
            case 4:
                //中转文件交接消息:吨控
                return sysUserMapper.selectUserListForTonnageControl();
            case 5:
                //中转货物交接消息:复重
                return sysUserMapper.selectUserListForCompoundWeight();
            case 6:
                //出港不正常货邮消息:代理人
                return sysUserMapper.selectUserListForDeptId(deptId);
            case 7:
                //不正常货邮处理消息:出港发吨控
                return sysUserMapper.selectUserListForTonnageControl();
            case 8:
                //进港发货站柜台
                return sysUserMapper.selectUserListForHzCounter();
            case 9:
                //航班配载了要制单发吨控
                return sysUserMapper.selectUserListForTonnageControl();
            default:
                return null;
        }
    }
}
