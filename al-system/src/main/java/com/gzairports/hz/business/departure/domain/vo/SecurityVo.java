package com.gzairports.hz.business.departure.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lan
 * @Desc: 安检申报返回参数
 * @create: 2024-10-11 17:17
 **/
@Data
public class SecurityVo {
    /** 主键id（运单id） */
    private Long id;

    /** 运单号 */
    private String waybillCode;

    /** 代理人 */
    private String agent;

    /** 特货代码 */
    private String specialCargoCode1;

    /** 品名 */
    private String cargoName;

    /** 件数 */
    private Integer quantity;

    /** 重量 */
    private BigDecimal weight;

    /** 体积（M3） */
    private BigDecimal volume;

    /** 尺寸 */
    private String size;

    /** 目的站 */
    private String desPort;

    /** 航班号 */
    private String flightNo1;

    /** 储运注意事项 */
    private String storageTransportNotes;

    /** 最终安检提交状态 0 未提交 1 已提交  -1退回 -2不合格 */
    private Integer securitySubmit;

    /** 物流端安检提交状态 0 未提交 1 已提交 -1退回 -2不合格 */
    private Integer securitySubmitWl;

    /** 安检申报pdf地址 */
    private String securityUrl;

    /** 是否与申报一致(审单结果) 0 否(退回) 1 是(符合运输) */
    private Integer declarationConsistent;

    /** 是否审核 0 否 1 是  */
    private Integer isExamine;

    /** 安检申报列表日期 */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date flightDate;

    /** 用于区分数据来自主单表还是来自新增安检申报表 */
    private Integer type;
}
