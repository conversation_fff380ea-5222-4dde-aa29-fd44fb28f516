<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.transfer.mapper.HzTransferHandoverWaybillMapper">
    <update id="updateLoad">
        update hz_transfer_handover_waybill set is_load = 1 where id = #{collectId}
    </update>
    <select id="selectIds" resultType="java.lang.Long">
        select waybill_id from hz_transfer_handover_waybill
    </select>
    <select id="selectImportDataByCode" resultType="java.lang.Long">
        select hthw.id
        from hz_transfer_handover_waybill hthw
        left join hz_transfer_handover hth on hth.id = hthw.transfer_id
        where hthw.is_load = 0 and hthw.waybill_id = #{waybillId} and (hth.file_status = 2 or hth.cargo_status = 2)
    </select>
</mapper>