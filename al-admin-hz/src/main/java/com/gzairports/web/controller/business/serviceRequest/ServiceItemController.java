package com.gzairports.web.controller.business.serviceRequest;

import com.gzairports.common.serviceRequest.domain.ServiceItem;
import com.gzairports.common.serviceRequest.service.IServiceItemService;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 服务项目管理Controller
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@RestController
@RequestMapping("/bus/serviceItem")
@Api(tags = "服务项目管理")
public class ServiceItemController extends BaseController {

    @Autowired
    private IServiceItemService itemService;

    /**
     * 查询服务项目管理列表
     */
//    @PreAuthorize("@ss.hasPermi('bus:serviceItem:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询服务项目管理列表")
    public AjaxResult list()
    {
        List<ServiceItem> list = itemService.selectItemList();
        return AjaxResult.success(list);
    }


    /**
     * 新增服务项目管理
     */
    @PreAuthorize("@ss.hasPermi('bus:serviceItem:add')")
    @Log(title = "服务项目管理", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增服务项目管理")
    public AjaxResult add(@RequestBody ServiceItem serviceItem)
    {
        return toAjax(itemService.insertItem(serviceItem));
    }

    /**
     * 删除服务项目管理
     */
    @PreAuthorize("@ss.hasPermi('bus:serviceItem:remove')")
    @Log(title = "服务项目管理", businessType = BusinessType.DELETE)
    @GetMapping("/remove/{id}")
    @ApiOperation(value = "删除服务项目管理")
    public AjaxResult remove(@PathVariable("id") Long id)
    {
        return toAjax(itemService.deleteItem(id));
    }
}
