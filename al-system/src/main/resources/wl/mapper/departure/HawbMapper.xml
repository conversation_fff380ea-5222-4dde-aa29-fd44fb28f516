<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.wl.departure.mapper.HawbMapper">
    <update id="updateSerialNo">
        update wl_dep_hawb set serial_no = #{serialNo}, pay_status = 3 where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectMergeList" resultType="com.gzairports.wl.departure.domain.vo.MergeHawbVo">
        select id, waybill_code, carrier1 as carrier, des_port, quantity, weight, charge_weight, cost_sum, shipper, consignee, flight_no as flight1,
               write_time, sys_dept.dept_name as dept
        from wl_dep_hawb
        left join sys_dept on sys_dept.dept_id = wl_dep_hawb.dept_id
        where id = #{hawbId} and wl_dep_hawb.dept_id = #{deptId} and is_del = 0
    </select>
    <select id="selectOneByCode" resultType="com.gzairports.wl.departure.domain.vo.WaybillTraceVo">
        select id, waybill_code as slaveWaybillCode, source_port, des_port, shipper, shipper_phone, consignee as consign,
               consignee_phone as consign_phone, write_time, writer, flight_no, flight_date, cargo_name, quantity as totalQuantity,
               weight as totalWeight
        from wl_dep_hawb where waybill_code = #{waybillCode} and dept_id = #{deptId} and is_del = 0 and status = 'NORMAL'
    </select>
    <select id="selectOneById" resultType="java.lang.String">
        select waybill_code from wl_dep_hawb where id = #{hawbId} and dept_id = #{deptId} and is_del = 0 and status = 'NORMAL'
    </select>
    <select id="queryList" resultType="com.gzairports.wl.departure.domain.Hawb">
        select id, waybill_code, master_waybill_code, source_port, des_port, flight_date, shipper, consignee, flight_no, special_cargo_code1, cargo_code, cargo_name,
               quantity, weight, charge_weight, write_time, money, cost_sum , pay_status
        from wl_dep_hawb
        <where>
            and dept_id = #{deptId} and is_del = 0
            <if test="startTime != null">
                and write_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and write_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="waybillCode != null and waybillCode != ''">
                and waybill_code like '%${waybillCode}%'
            </if>
            <if test="shipper != null and shipper != ''">
                and shipper like '%${shipper}%'
            </if>
            <if test="option != null and option == 1">
                and special_cargo_code1 = #{specialCargoCode}
            </if>
            <if test="option != null and option == 2">
                and special_cargo_code2 = #{specialCargoCode}
            </if>
            <if test="option != null and option == 3">
                and special_cargo_code3 = #{specialCargoCode}
            </if>
            <if test="cargoCode != null and cargoCode != ''">
                and cargo_code like '%${cargoCode}%'
            </if>
            <if test="carrierCode != null and carrierCode != ''">
                and substring(flight_no,1,2) = #{carrierCode}
            </if>
            <if test="desPort != null and desPort != ''">
                and des_port = #{desPort}
            </if>
            <if test="payStatus != null and payStatus != ''">
                and pay_status = #{payStatus}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
        </where>
    </select>
    <select id="selectByIds" resultType="com.gzairports.wl.departure.domain.Hawb">
        select id, waybill_code, master_waybill_code, source_port, des_port, flight_date, shipper, consignee, flight_no, special_cargo_code1, cargo_code, cargo_name,
               quantity, weight, charge_weight, write_time, money, cost_sum , pay_status, serial_no from wl_dep_hawb where dept_id = #{deptId} and is_del = 0 and id in
            <foreach collection="ids" index="index" item="id"  open="(" close=")" separator=",">
                #{id}
            </foreach>
    </select>
    <select id="seelectHawbList" resultType="com.gzairports.wl.departure.domain.vo.HawbBillVo">
        select waybill_code, cargo_name, quantity, weight, charge_weight, write_time, cost_sum from wl_dep_hawb where serial_no = #{serialNo}
    </select>
    <select id="selectMawbInfoById" resultType="com.gzairports.common.business.departure.domain.Mawb">
        select aaw.id, aaw.waybill_code
        from wl_dep_hawb wdh
        left join wl_dep_merge_hawb_mawb wdmhm on wdmhm.hawb_id = wdh.id
        left join all_air_waybill aaw on wdmhm.mawb_id = aaw.id
        where wdmhm.hawb_id = #{id}
        limit 1
    </select>
    <select id="queryVos" resultType="com.gzairports.wl.departure.domain.vo.HawbQueryVo1">
        select id, waybill_code, master_waybill_code, des_port, shipper, cargo_name, quantity, weight, charge_weight, write_time,
        remark, consignee
        from wl_dep_hawb
        <where>
            and dept_id = #{deptId} and is_del = 0
            <if test="startTime != null">
                and write_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and write_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="waybillCode != null and waybillCode != ''">
                and waybill_code like '%${waybillCode}%'
            </if>
            <if test="shipper != null and shipper != ''">
                and shipper like '%${shipper}%'
            </if>
            <if test="cargoCode != null and cargoCode != ''">
                and cargo_code like '%${cargoCode}%'
            </if>
            <if test="desPort != null and desPort != ''">
                and des_port = #{desPort}
            </if>
            <if test="payStatus != null and payStatus != ''">
                and pay_status = #{payStatus}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
        </where>
    </select>
    <select id="queryListWeight" resultType="java.math.BigDecimal">
        select sum(weight)
        from wl_dep_hawb
        <where>
            and dept_id = #{query.deptId} and is_del = 0
            <if test="query.startTime != null">
                and write_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and write_time <![CDATA[<=]]> #{query.endTime}
            </if>
            <if test="query.waybillCode != null and query.waybillCode != ''">
                and waybill_code like '%${query.waybillCode}%'
            </if>
            <if test="query.shipper != null and query.shipper != ''">
                and shipper like '%${query.shipper}%'
            </if>
            <if test="query.cargoCode != null and query.cargoCode != ''">
                and cargo_code like '%${query.cargoCode}%'
            </if>
            <if test="query.desPort != null and query.desPort != ''">
                and des_port = #{query.desPort}
            </if>
            <if test="query.payStatus != null and query.payStatus != ''">
                and pay_status = #{query.payStatus}
            </if>
            <if test="query.status != null and query.status != ''">
                and status = #{query.status}
            </if>
        </where>
    </select>
    <select id="queryListCount" resultType="java.lang.Integer">
        select count(1)
        from wl_dep_hawb
        <where>
            and dept_id = #{query.deptId} and is_del = 0
            <if test="query.startTime != null">
                and write_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and write_time <![CDATA[<=]]> #{query.endTime}
            </if>
            <if test="query.waybillCode != null and query.waybillCode != ''">
                and waybill_code like '%${query.waybillCode}%'
            </if>
            <if test="query.shipper != null and query.shipper != ''">
                and shipper like '%${query.shipper}%'
            </if>
            <if test="query.cargoCode != null and query.cargoCode != ''">
                and cargo_code like '%${query.cargoCode}%'
            </if>
            <if test="query.desPort != null and query.desPort != ''">
                and des_port = #{query.desPort}
            </if>
            <if test="query.payStatus != null and query.payStatus != ''">
                and pay_status = #{query.payStatus}
            </if>
            <if test="query.status != null and query.status != ''">
                and status = #{query.status}
            </if>
        </where>
    </select>
    <select id="queryListQuantity" resultType="java.lang.Integer">
        select sum(quantity)
        from wl_dep_hawb
        <where>
            and dept_id = #{query.deptId} and is_del = 0
            <if test="query.startTime != null">
                and write_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and write_time <![CDATA[<=]]> #{query.endTime}
            </if>
            <if test="query.waybillCode != null and query.waybillCode != ''">
                and waybill_code like '%${query.waybillCode}%'
            </if>
            <if test="query.shipper != null and query.shipper != ''">
                and shipper like '%${query.shipper}%'
            </if>
            <if test="query.cargoCode != null and query.cargoCode != ''">
                and cargo_code like '%${query.cargoCode}%'
            </if>
            <if test="query.desPort != null and query.desPort != ''">
                and des_port = #{query.desPort}
            </if>
            <if test="query.payStatus != null and query.payStatus != ''">
                and pay_status = #{query.payStatus}
            </if>
            <if test="query.status != null and query.status != ''">
                and status = #{query.status}
            </if>
        </where>
    </select>
    <select id="queryVosList" resultType="com.gzairports.wl.departure.domain.vo.HawbQueryVo1">
        select id, waybill_code, master_waybill_code, des_port, shipper, cargo_name, quantity, weight, charge_weight, write_time,
        remark, consignee
        from wl_dep_hawb
        <where>
            and dept_id = #{query.deptId} and is_del = 0
            <if test="query.startTime != null">
                and write_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and write_time <![CDATA[<=]]> #{query.endTime}
            </if>
            <if test="query.waybillCode != null and query.waybillCode != ''">
                and waybill_code like '%${query.waybillCode}%'
            </if>
            <if test="query.shipper != null and query.shipper != ''">
                and shipper like '%${query.shipper}%'
            </if>
            <if test="query.cargoCode != null and query.cargoCode != ''">
                and cargo_code like '%${query.cargoCode}%'
            </if>
            <if test="query.desPort != null and query.desPort != ''">
                and des_port = #{query.desPort}
            </if>
            <if test="query.payStatus != null and query.payStatus != ''">
                and pay_status = #{query.payStatus}
            </if>
            <if test="query.status != null and query.status != ''">
                and status = #{query.status}
            </if>
        </where>
    </select>
</mapper>