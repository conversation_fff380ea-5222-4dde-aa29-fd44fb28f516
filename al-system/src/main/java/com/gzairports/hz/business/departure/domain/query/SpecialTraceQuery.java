package com.gzairports.hz.business.departure.domain.query;

import lombok.Data;

import java.util.Date;

/**
 * 特货跟踪查询参数
 * <AUTHOR>
 * @date 2024-07-12
 */
@Data
public class SpecialTraceQuery {

    /** 入库时间 */
    private Date startTime;

    /** 入库时间 */
    private Date endTime;

    /** 运单号 */
    private String waybillCode;

    /** 特货代码 */
    private String specialCode;

    /** 发货代理人 */
    private String agentCode;

    /** 运单状态 */
    private String status;
}
