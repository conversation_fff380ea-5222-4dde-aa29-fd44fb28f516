<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.business.arrival.mapper.FlightInfoMapper">
    
    <resultMap type="FlightInfo" id="BusinessFlightInfoResult">
        <id property="flightId"    column="flight_id"    />
        <result property="assoicatedId"    column="assoicated_id"    />
        <result property="execDate"    column="exec_date"    />
        <result property="airwaysId"    column="airways_id"    />
        <result property="agency"    column="agency"    />
        <result property="airWays"    column="air_ways"    />
        <result property="flightNo"    column="flight_no"    />
        <result property="bigFlightNo"    column="big_flight_no"    />
        <result property="isOffin"    column="is_offin"    />
        <result property="task"    column="task"    />
        <result property="taskcn"    column="taskcn"    />
        <result property="craftNoId"    column="craft_no_id"    />
        <result property="craftNo"    column="craft_no"    />
        <result property="craftType"    column="craft_type"    />
        <result property="wideis"    column="wideis"    />
        <result property="craftSite"    column="craft_site"    />
        <result property="craftNoOld"    column="craft_no_old"    />
        <result property="craftSiteType"    column="craft_site_type"    />
        <result property="providingState"    column="providing_state"    />
        <result property="abnormalState"    column="abnormal_state"    />
        <result property="abnormalReasonCn"    column="abnormal_reason_cn"    />
        <result property="startStationId"    column="start_station_id"    />
        <result property="startStation"    column="start_station"    />
        <result property="startStationCn"    column="start_station_cn"    />
        <result property="startSchemeTakeoffTime"    column="start_scheme_takeoff_time"    />
        <result property="startAlterateTakeoffTime"    column="start_alterate_takeoff_time"    />
        <result property="startRealTakeoffTime"    column="start_real_takeoff_time"    />
        <result property="terminalStationId"    column="terminal_station_id"    />
        <result property="terminalStation"    column="terminal_station"    />
        <result property="terminalStationCn"    column="terminal_station_cn"    />
        <result property="terminalSchemeLandInTime"    column="terminal_scheme_land_in_time"    />
        <result property="terminalReallAndInTime"    column="terminal_reall_and_in_time"    />
        <result property="terminalAlteratelAndInTime"    column="terminal_alteratel_and_in_time"    />
        <result property="frontTakeoffTime"    column="front_takeoff_time"    />
        <result property="isVip"    column="is_vip"    />
        <result property="runWay"    column="run_way"    />
        <result property="resgate"    column="resgate"    />
        <result property="resbelt"    column="resbelt"    />
        <result property="shareFlight"    column="share_flight"    />
        <result property="sharevia"    column="sharevia"    />
        <result property="shareviaCn"    column="sharevia_cn"    />
        <result property="airLineFull"    column="air_line_full"    />
        <result property="airLineFullCn"    column="air_line_full_cn"    />
        <result property="airLineShort"    column="air_line_short"    />
        <result property="airLineShortCn"    column="air_line_short_cn"    />
        <result property="flightType"    column="flight_type"    />
        <result property="terminalinter"    column="terminalinter"    />
        <result property="terminal"    column="terminal"    />
        <result property="airWaysCn"    column="air_ways_cn"    />
        <result property="airLineFullJccn"    column="air_line_full_jccn"    />
        <result property="airLineShortJccn"    column="air_line_short_jccn"    />
        <result property="airLineCn"    column="air_line_cn"    />
        <result property="airLineJccn"    column="air_line_jccn"    />
        <result property="airLine"    column="air_line"    />
        <result property="abnormalReason"    column="abnormal_reason"    />
        <result property="nextStationSchemeLandInTime"    column="next_station_scheme_land_in_time"    />
        <result property="nextStationReallAndInTime"    column="next_station_reall_and_in_time"    />
        <result property="ffid"    column="ffid"    />
        <result property="attribute"    column="attribute"    />
        <result property="attributeCn"    column="attribute_cn"    />
        <result property="startStorageTime"    column="start_storage_time"    />
        <result property="endStorageTime"    column="end_storage_time"    />
        <result property="msgType"    column="msg_type"    />
        <result property="isManual"    column="is_manual"    />
        <result property="isComp"    column="is_comp"    />
        <result property="isSettle"    column="is_settle"    />
    </resultMap>

    <sql id="selectBusinessFlightInfoVo">
        select flight_id, assoicated_id, exec_date, airways_id, agency, air_ways, flight_no, big_flight_no, is_offin, task,
               taskcn, craft_no_id, craft_no, craft_type, wideis, craft_site, craft_no_old, craft_site_type, providing_state,
               abnormal_state, abnormal_reason_cn, start_station_id, start_station, start_station_cn,
               start_scheme_takeoff_time, start_alterate_takeoff_time, start_real_takeoff_time, terminal_station_id,
               terminal_station, terminal_station_cn, terminal_scheme_land_in_time, terminal_reall_and_in_time,
               terminal_alteratel_and_in_time, front_takeoff_time, is_vip, run_way, resgate, resbelt, share_flight,
               sharevia, sharevia_cn, air_line_full, air_line_full_cn, air_line_short, air_line_short_cn, flight_type,
               terminalinter, terminal, air_ways_cn, air_line_full_jccn, air_line_short_jccn, air_line_cn, air_line_jccn,
               air_line, abnormal_reason, next_station_scheme_land_in_time, next_station_reall_and_in_time, ffid,
               attribute, attribute_cn, start_storage_time, end_storage_time, is_manual, is_comp, is_settle from all_flight_info
    </sql>

    <select id="selectBusinessFlightInfoList" parameterType="FlightInfoQuery" resultType="FlightInfoVO">
        select flight_id, exec_date, CONCAT(air_ways,flight_no) AS flight_no, providing_state, abnormal_state, craft_type, start_scheme_takeoff_time,
               terminal_station_cn, start_storage_time, end_storage_time, sharevia_cn,
               is_sms from all_flight_info
        <where>
            and start_storage_time is not null and is_offin = 'D'
            <if test="startExecDate != null">
                AND DATE_FORMAT(exec_date,'%Y%m%d %H:%i:%s') <![CDATA[>=]]> DATE_FORMAT(#{startExecDate, jdbcType=TIMESTAMP},'%Y%m%d %H:%i:%s')
            </if>
            <if test="stopExecDate != null">
                AND DATE_FORMAT(exec_date,'%Y%m%d %H:%i:%s') <![CDATA[<=]]> DATE_FORMAT(#{stopExecDate, jdbcType=TIMESTAMP},'%Y%m%d %H:%i:%s')
            </if>
            <if test="startExecDate == null and stopExecDate == null">
                AND exec_date = #{execDate}
            </if>
            <if test="flightNo != null  and flightNo != ''"> and CONCAT(air_ways,flight_no) like CONCAT('%',#{flightNo},'%')</if>
        </where>
        order by start_scheme_takeoff_time ASC
    </select>
    
    <select id="selectBusinessFlightInfoById" parameterType="Long" resultMap="BusinessFlightInfoResult">
        select flight_id, CONCAT(air_ways,flight_no) AS flight_no, terminal_station_cn, sharevia_cn, start_scheme_takeoff_time,
               providing_state, abnormal_state, start_storage_time, end_storage_time, air_ways, craft_no
        from all_flight_info
        where flight_id = #{flightId}
    </select>

    <select id="selectShowList" resultType="FlightInfoVO">
        select flight_id, exec_date, CONCAT(air_ways,flight_no) AS flight_no, craft_type, providing_state, abnormal_state, start_scheme_takeoff_time,
        terminal_station_cn, sharevia_cn, start_storage_time, end_storage_time from all_flight_info
        where exec_date = curdate() and is_offin = 'D' and start_real_takeoff_time is null and start_storage_time is not null order by start_storage_time asc
    </select>
    <select id="selectFlightIsFly" resultType="java.lang.Boolean">
     select EXISTS (select 1 from all_flight_info where CONCAT(air_ways,flight_no) = #{flightNo1} and exec_date = #{execDate}
                                                     and is_offin = 'D' and start_real_takeoff_time is not null)
    </select>
    <select id="selectByLegId" resultType="com.gzairports.hz.business.arrival.domain.vo.TallyManifestVo">
        select exec_date, CONCAT(air_ways,flight_no) AS flight_no, craft_no, start_station, terminal_alteratel_and_in_time, tally_num
        from all_flight_info afi
            left join hz_flight_load hfl on afi.flight_id = hfl.flight_id where hfl.id = #{legId}
    </select>
    <select id="appFlightList" resultType="com.gzairports.hz.business.arrival.domain.vo.AppFlightListVo">
        select flight_id, exec_date, CONCAT(air_ways,flight_no) AS flight_no, cabin_date as cabinTime, terminal_scheme_land_in_time, tally_num, tally_status
        from all_flight_info
        <where>
            is_offin = 'A'
            <if test="query.execDate != null">
                AND exec_date = #{query.execDate}
            </if>
            <if test="query.flightNo != null  and query.flightNo != ''">
             and CONCAT(air_ways,flight_no) = #{query.flightNo}
            </if>
            <if test="query.tallyStatus != null  and query.tallyStatus != ''">
             and tally_status = #{query.tallyStatus}
            </if>
        </where>
    </select>
    <select id="getFlightOper" resultType="java.lang.String">
        select afi.flight_oper from all_flight_info afi left join hz_flight_load hfl on afi.flight_id = hfl.flight_id where hfl.id = #{legId}
    </select>
    <select id="selectInfoByTallyId" resultType="com.gzairports.common.business.departure.domain.FlightInfo">
        select CONCAT(afi.air_ways,afi.flight_no) AS flight_no, afi.exec_date
        from all_flight_info afi
            left join hz_flight_load hfl on afi.flight_id = hfl.flight_id
            left join hz_arr_record_order haro on haro.leg_id = hfl.id
            left join hz_arr_tally hat on hat.record_order_id = haro.id where hat.id = #{tallyId}
    </select>
    <select id="getDateByNo" resultType="java.lang.String">
        select start_scheme_takeoff_time from all_flight_info
        where CONCAT(air_ways,flight_no) = #{flightNo} and exec_date = curdate()
        limit 1
    </select>
    <select id="selectIdByVo" resultType="java.lang.Long">
        select flight_id from all_flight_info where is_offin = 'D' and CONCAT(air_ways,flight_no) = #{flightNo} and exec_date = #{execDate}
    </select>
    <select id="selectTotalCount" resultType="java.lang.Integer">
        select count(1) from all_flight_info
        <where>
            is_offin = 'A'
            <if test="execDate != null">
                AND exec_date = #{execDate}
            </if>
            <if test="flightNo != null  and flightNo != ''">
                and CONCAT(air_ways,flight_no) = #{flightNo}
            </if>
        </where>
    </select>

    <select id="selectTallyInfoCount" resultType="java.lang.Integer">
        select count(1) from all_flight_info
        <where>
            is_offin = 'A'
            <if test="execDate != null">
                AND exec_date = #{execDate}
            </if>
            <if test="flightNo != null  and flightNo != ''">
                and CONCAT(air_ways,flight_no) = #{flightNo}
            </if>
            <if test="tallyStatus != null  and tallyStatus != ''">
                and tally_status = #{tallyStatus}
            </if>
        </where>
    </select>
    <select id="selectFlightNo" resultType="com.gzairports.common.business.departure.domain.FlightInfo">
        select afi.flight_id, afi.start_scheme_takeoff_time, afi.start_real_takeoff_time, afi.start_station, afi.terminal_station,afi.start_real_takeoff_time,
               afi.air_ways, afi.flight_no, afi.exec_date from all_flight_info afi
            left join hz_flight_load hfl on afi.flight_id = hfl.flight_id where hfl.id = #{legId}
    </select>
    <select id="selectLoadVo" resultType="com.gzairports.hz.business.departure.domain.vo.LoadFlightVo">
        select craft_type, flight_id, craft_no from all_flight_info
        <where>
            is_offin = 'D'
            <if test="flightDate != null">
                AND exec_date = #{flightDate}
            </if>
            <if test="flightNo != null and flightNo != ''">
                AND CONCAT(air_ways,flight_no) = #{flightNo}
            </if>
        </where>
    </select>
    <select id="selectYesterdayList"
            resultType="com.gzairports.common.business.departure.domain.vo.YesterdayFlightInfo">
        select flight_id, start_scheme_takeoff_time, CONCAT(air_ways,flight_no) as flightNo, exec_date from all_flight_info
        where is_comp = 0 and is_settle = 0 and is_offin = 'D'
              and exec_date between '2025-01-08 00:00:00' and #{format}
    </select>
    <select id="selectYesterdayListVo"
            resultType="com.gzairports.common.business.departure.domain.vo.YesterdayFlightInfo">
        select flight_id, start_scheme_takeoff_time, CONCAT(air_ways,flight_no) as flightNo from all_flight_info
        where is_comp = 1 and is_settle = 0 and is_offin = 'D'
          and exec_date <![CDATA[>=]]> '2025-01-08 00:00:00'
    </select>
    <select id="selectStartTime" resultType="java.lang.String">
        select start_real_takeoff_time from all_flight_info where flight_id = #{flightId}
    </select>
    <update id="updateBusinessFlightInfo" parameterType="FlightInfo">
        update all_flight_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="assoicatedId != null">assoicated_id = #{assoicatedId},</if>
            <if test="execDate != null">exec_date = #{execDate},</if>
            <if test="airwaysId != null">airways_id = #{airwaysId},</if>
            <if test="agency != null">agency = #{agency},</if>
            <if test="airWays != null">air_ways = #{airWays},</if>
            <if test="flightNo != null">flight_no = #{flightNo},</if>
            <if test="bigFlightNo != null">big_flight_no = #{bigFlightNo},</if>
            <if test="isOffin != null">is_offin = #{isOffin},</if>
            <if test="task != null">task = #{task},</if>
            <if test="taskcn != null">taskcn = #{taskcn},</if>
            <if test="craftNoId != null">craft_no_id = #{craftNoId},</if>
            <if test="craftNo != null">craft_no = #{craftNo},</if>
            <if test="craftType != null">craft_type = #{craftType},</if>
            <if test="wideis != null">wideis = #{wideis},</if>
            <if test="craftSite != null">craft_site = #{craftSite},</if>
            <if test="craftNoOld != null">craft_no_old = #{craftNoOld},</if>
            <if test="craftSiteType != null">craft_site_type = #{craftSiteType},</if>
            <if test="providingState != null">providing_state = #{providingState},</if>
            <if test="abnormalState != null">abnormal_state = #{abnormalState},</if>
            <if test="abnormalReasonCn != null">abnormal_reason_cn = #{abnormalReasonCn},</if>
            <if test="startStationId != null">start_station_id = #{startStationId},</if>
            <if test="startStation != null">start_station = #{startStation},</if>
            <if test="startStationCn != null">start_station_cn = #{startStationCn},</if>
            <if test="startSchemeTakeoffTime != null">start_scheme_takeoff_time = #{startSchemeTakeoffTime},</if>
            <if test="startAlterateTakeoffTime != null">start_alterate_takeoff_time = #{startAlterateTakeoffTime},</if>
            <if test="startRealTakeoffTime != null">start_real_takeoff_time = #{startRealTakeoffTime},</if>
            <if test="terminalStationId != null">terminal_station_id = #{terminalStationId},</if>
            <if test="terminalStation != null">terminal_station = #{terminalStation},</if>
            <if test="terminalStationCn != null">terminal_station_cn = #{terminalStationCn},</if>
            <if test="terminalSchemeLandInTime != null">terminal_scheme_land_in_time = #{terminalSchemeLandInTime},</if>
            <if test="terminalReallAndInTime != null">terminal_reall_and_in_time = #{terminalReallAndInTime},</if>
            <if test="terminalAlteratelAndInTime != null">terminal_alteratel_and_in_time = #{terminalAlteratelAndInTime},</if>
            <if test="frontTakeoffTime != null">front_takeoff_time = #{frontTakeoffTime},</if>
            <if test="isVip != null">is_vip = #{isVip},</if>
            <if test="runWay != null">run_way = #{runWay},</if>
            <if test="resgate != null">resgate = #{resgate},</if>
            <if test="resbelt != null">resbelt = #{resbelt},</if>
            <if test="shareFlight != null">share_flight = #{shareFlight},</if>
            <if test="sharevia != null">sharevia = #{sharevia},</if>
            <if test="shareviaCn != null">sharevia_cn = #{shareviaCn},</if>
            <if test="airLineFull != null">air_line_full = #{airLineFull},</if>
            <if test="airLineFullCn != null">air_line_full_cn = #{airLineFullCn},</if>
            <if test="airLineShort != null">air_line_short = #{airLineShort},</if>
            <if test="airLineShortCn != null">air_line_short_cn = #{airLineShortCn},</if>
            <if test="flightType != null">flight_type = #{flightType},</if>
            <if test="terminalinter != null">terminalinter = #{terminalinter},</if>
            <if test="terminal != null">terminal = #{terminal},</if>
            <if test="airWaysCn != null">air_ways_cn = #{airWaysCn},</if>
            <if test="airLineFullJccn != null">air_line_full_jccn = #{airLineFullJccn},</if>
            <if test="airLineShortJccn != null">air_line_short_jccn = #{airLineShortJccn},</if>
            <if test="airLineCn != null">air_line_cn = #{airLineCn},</if>
            <if test="airLineJccn != null">air_line_jccn = #{airLineJccn},</if>
            <if test="airLine != null">air_line = #{airLine},</if>
            <if test="abnormalReason != null">abnormal_reason = #{abnormalReason},</if>
            <if test="nextStationSchemeLandInTime != null">next_station_scheme_land_in_time = #{nextStationSchemeLandInTime},</if>
            <if test="nextStationReallAndInTime != null">next_station_reall_and_in_time = #{nextStationReallAndInTime},</if>
            <if test="ffid != null">ffid = #{ffid},</if>
            <if test="attribute != null">attribute = #{attribute},</if>
            <if test="attributeCn != null">attribute_cn = #{attributeCn},</if>
            <if test="startStorageTime != null">start_storage_time = #{startStorageTime},</if>
            <if test="endStorageTime != null">end_storage_time = #{endStorageTime},</if>
        </trim>
        where flight_id = #{flightId}
    </update>
    <update id="openCreate">
        update all_flight_info set is_create = 1 where flight_id = #{flightId}
    </update>
    <update id="closeCreate">
        update all_flight_info set is_create = 2 where flight_id = #{flightId}
    </update>
    <update id="updatePre">
        update all_flight_info set is_pre = 1 where flight_id = #{flightId}
    </update>
    <update id="compFlight">
        update all_flight_info set is_comp = 1, comp_time = NOW() where flight_id = #{flightId}
    </update>
    <update id="openFlight">
        update all_flight_info set is_comp = 0, is_settle = 0 where flight_id = #{flightId}
    </update>
    <update id="updateYesterdayInfo">
        update all_flight_info set is_comp = 1, comp_time = NOW() where flight_id = #{flightId}
    </update>
    <update id="updateYesterdayInfoVo">
        update all_flight_info set is_comp = 1, is_settle = 1 where flight_id = #{flightId}
    </update>
    <select id="selectByFlightNoAndDate"
            resultMap="BusinessFlightInfoResult">
        select flight_id, exec_date, air_ways, flight_no, is_offin, start_scheme_takeoff_time, start_station,
               terminal_station
        from all_flight_info
        where CONCAT(air_ways,flight_no) = #{flightNo} and exec_date = #{execDate} and is_offin = #{isOffin}
    </select>
    <select id="selectFlightIdByWaybillId" resultType="com.gzairports.hz.business.cable.domain.vo.QHDBIdVO">
        (select afi.flight_id,hflw.waybill_id
        from hz_flight_load_waybill hflw
        left join hz_flight_load hfl on hfl.id = hflw.flight_load_id
        left join all_flight_info afi on afi.flight_id = hfl.flight_id
        where hflw.waybill_id in
        <foreach collection="waybillIdList" item="waybillId" open="(" separator="," close=")">
            #{waybillId}
        </foreach>)
        union all
        (select afi.flight_id,hfluw.waybill_id
        from hz_flight_load_uld_waybill hfluw
        left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
        left join hz_flight_load hfl on hfl.id = hflu.flight_load_id
        left join all_flight_info afi on afi.flight_id = hfl.flight_id
        where  hfluw.waybill_id in
        <foreach collection="waybillIdList" item="waybillId" open="(" separator="," close=")">
            #{waybillId}
        </foreach>)
    </select>
    <select id="selectFlightInfoById" resultType="com.gzairports.hz.business.cable.domain.vo.MsgFlightInfoVO">
        select afi.flight_id, afi.air_ways as carrier, afi.exec_date as flightDate, afi.flight_no, afi.start_station as departureStation,
               afi.terminal_station as nextStation, afi.craft_no as aircraftRegistration, afi.terminal_reall_and_in_time as eta,
               afi.start_scheme_takeoff_time as etd, afi.start_scheme_takeoff_time
        from all_flight_info afi left join hz_flight_load hfl on afi.flight_id = hfl.flight_id
        where 1=1
        <if test="flightId != null">
            and afi.flight_id = #{flightId}
        </if>
        <if test="legId != null">
            and hfl.id = #{legId}
        </if>
    </select>
    <select id="selectIsComp" resultType="java.lang.Integer">
        select is_comp from all_flight_info where flight_id = #{id}
    </select>
    <select id="selectInfoVo" resultType="com.gzairports.hz.business.departure.domain.vo.FlightInfoVO">
        select CONCAT(air_ways,flight_no) as flightNo, exec_date, is_comp from all_flight_info where flight_id = #{id}
    </select>
    <select id="selectWaybillDataByFlightId"
            resultType="com.gzairports.common.securitySubmit.domain.WaybillFlightLoadInfoData">
        (select hflw.quantity,hflw.weight,SUBSTRING(aaw.waybill_code FROM 5) as waybillCode
         from hz_flight_load_waybill hflw
                  left join hz_flight_load hfl on hfl.id = hflw.flight_load_id
                  left join all_flight_info afi on afi.flight_id = hfl.flight_id
                  left join all_air_waybill aaw on hflw.waybill_id = aaw.id
         where aaw.type = 'DEP' and afi.flight_id = #{flightId}
        )
        union all
        (
            select hfluw.quantity,hfluw.weight,SUBSTRING(aaw.waybill_code FROM 5) as waybillCode
            from hz_flight_load_uld_waybill hfluw
                     left join hz_flight_load_uld hflu on hflu.id = hfluw.load_uld_id
                     left join hz_flight_load hfl on hfl.id = hflu.flight_load_id
                     left join all_flight_info afi on afi.flight_id = hfl.flight_id
                     left join all_air_waybill aaw on hfluw.waybill_id = aaw.id
            where aaw.type = 'DEP' and afi.flight_id = #{flightId}
        )
    </select>
    <select id="selectArrFlight" resultType="com.gzairports.common.business.departure.domain.FlightInfo">
        select afi.flight_id, afi.start_scheme_takeoff_time, afi.start_real_takeoff_time, afi.start_station, afi.terminal_station,
            afi.air_ways, afi.flight_no, afi.exec_date
        from all_flight_info afi
                 left join hz_flight_load hfl on afi.flight_id = hfl.flight_id
                 left join hz_arr_record_order ho on ho.leg_id = hfl.id
                 left join hz_arr_tally hat on ho.id = hat.record_order_id
        where hat.id = #{tallyId}
    </select>
    <select id="selectFlightIds" resultType="java.lang.Long">
       select flight_id from all_flight_info
       where is_offin = 'D'
       <if test="flightStartTime != null">
           and start_scheme_takeoff_time <![CDATA[>=]]> #{flightStartTime}
       </if>
        <if test="flightEndTime != null">
            AND start_scheme_takeoff_time <![CDATA[<=]]> #{flightEndTime};
        </if>
    </select>
    <select id="selectFlightWaybills"
            resultType="com.gzairports.hz.business.departure.domain.vo.FlightLoadWaybillVO">
        SELECT combined_flights.waybill_id, afi.flight_id, sum(combined_flights.quantity) as quantity, sum(combined_flights.weight) as weight
        FROM all_flight_info afi
                 INNER JOIN (
            SELECT hflw.waybill_id AS waybill_id, hflw.quantity, hflw.weight, hfl1.flight_id
            FROM hz_flight_load_waybill hflw
                     LEFT JOIN hz_flight_load hfl1 ON hfl1.id = hflw.flight_load_id

            UNION ALL

            SELECT hfluw.waybill_id AS waybill_id, hfluw.quantity, hfluw.weight, hfl2.flight_id
            FROM hz_flight_load_uld_waybill hfluw
                     LEFT JOIN hz_flight_load_uld hflu ON hflu.id = hfluw.load_uld_id
                     LEFT JOIN hz_flight_load hfl2 ON hfl2.id = hflu.flight_load_id
        ) combined_flights ON afi.flight_id = combined_flights.flight_id
        where afi.is_offin = 'D'
        <if test="flightStartTime != null">
            AND afi.start_scheme_takeoff_time <![CDATA[>=]]> #{flightStartTime}
        </if>
        <if test="flightEndTime != null">
            AND afi.start_scheme_takeoff_time <![CDATA[<=]]> #{flightEndTime}
        </if>
        group by combined_flights.waybill_id, afi.flight_id
    </select>
</mapper>