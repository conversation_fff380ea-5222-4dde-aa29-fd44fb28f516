<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.common.basedata.mapper.SpecialCargoCodeMapper">
    <insert id="insertSpecialCargoCode" parameterType="baseSpecialCargoCode">
        insert into base_special_cargo_code
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="handleCode != null and handleCode != ''">handle_code,</if>
            <if test="airCompany != null and airCompany != ''">air_company,</if>
            <if test="isDanger != null">is_danger,</if>
            <if test="chineseDescription != null and chineseDescription != ''">chinese_description,</if>
            <if test="englishDescription != null and englishDescription != ''">english_description,</if>
            <if test="carrierCode != null and carrierCode != ''">carrier_code,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="handleCode != null and handleCode != ''">#{handleCode},</if>
            <if test="airCompany != null and airCompany != ''">#{airCompany},</if>
            <if test="isDanger != null">#{isDanger},</if>
            <if test="chineseDescription != null and chineseDescription != ''">#{chineseDescription},</if>
            <if test="englishDescription != null and englishDescription != ''">#{englishDescription},</if>
            <if test="carrierCode != null and carrierCode != ''">#{carrierCode},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>
    <update id="updateSpecialCargoCode" parameterType="baseSpecialCargoCode">
        update base_special_cargo_code
        <trim prefix="SET" suffixOverrides=",">
            <if test="handleCode != null and handleCode != ''">handle_code = #{handleCode},</if>
            <if test="airCompany != null and airCompany != ''">air_company = #{airCompany},</if>
            <if test="isDanger != null">is_danger = #{isDanger},</if>
            <if test="chineseDescription != null and chineseDescription != ''">chinese_description = #{chineseDescription},</if>
            <if test="englishDescription != null and englishDescription != ''">english_description = #{englishDescription},</if>
            <if test="carrierCode != null and carrierCode != ''">carrier_code = #{carrierCode},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null and createTime != ''">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="delSpecialCargoCode" parameterType="long">
        update base_special_cargo_code set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <select id="selectSpecialCargoCodeList" resultType="com.gzairports.common.basedata.domain.BaseSpecialCargoCode">
        select id, handle_code, air_company, is_danger, chinese_description, english_description, carrier_code from base_special_cargo_code
        <where>
        and is_del = 0
            <if test="ids != null  and ids.size() > 0"> and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="airCompany != null  and airCompany != ''"> and air_company like concat('%',#{airCompany},'%')</if>
            <if test="handleCode != null and handleCode != ''"> and handle_code like concat('%',#{handleCode},'%')</if>
            <if test="isDanger != null"> and is_danger = #{isDanger}</if>
        </where>
    </select>
    <select id="selectSpecialCargoCodeByCode" resultType="com.gzairports.common.basedata.domain.BaseSpecialCargoCode">
        select id, handle_code, air_company, is_danger, chinese_description, english_description, carrier_code
        from base_special_cargo_code
        <where>
            and is_del = 0
            <if test="airCompany != null  and airCompany != ''"> and air_company = #{airCompany}</if>
            <if test="handleCode != null and handleCode != ''"> and handle_code = #{handleCode}</if>
            <if test="isDanger != null"> and is_danger = #{isDanger}</if>
            <if test="carrierCode != null and carrierCode != ''"> and carrier_code = #{carrierCode}</if>
        </where>
    </select>
</mapper>