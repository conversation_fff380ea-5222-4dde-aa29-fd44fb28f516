package com.gzairports.framework.aspectj;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.gzairports.common.annotation.OperLog;
import com.gzairports.common.annotation.AllWaybillLog;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.enums.BusinessStatus;
import com.gzairports.common.enums.HttpMethod;
import com.gzairports.common.log.domain.WaybillLog;
import com.gzairports.common.utils.SecurityUtils;
import com.gzairports.common.utils.ServletUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.framework.manager.AsyncManager;
import com.gzairports.framework.manager.factory.AsyncFactory;
import com.gzairports.wl.log.domain.WlOperLog;
import org.apache.xmlbeans.impl.xb.xsdschema.All;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.Map;

/**
 * 运单日志记录处理
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
@Aspect
@Component
public class WaybillLogAspect {

    private static final Logger logger = LoggerFactory.getLogger(OperLogAspect.class);

    /**
     * 处理请求前执行
     */
    @Before(value = "@annotation(log)")
    public void doBefore(JoinPoint joinPoint, AllWaybillLog log)
    {
    }

    /**
     * 处理完请求后执行
     *
     * @param joinPoint 切点
     */
    @AfterReturning(pointcut = "@annotation(log)", returning = "jsonResult")
    public void doAfterReturning(JoinPoint joinPoint, AllWaybillLog log, Object jsonResult)
    {
        handleLog(joinPoint, log, null, jsonResult);
    }

    /**
     * 拦截异常操作
     *
     * @param joinPoint 切点
     * @param e 异常
     */
    @AfterThrowing(value = "@annotation(log)", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, AllWaybillLog log, Exception e)
    {
        handleLog(joinPoint, log, e, null);
    }

    protected void handleLog(final JoinPoint joinPoint, AllWaybillLog log, final Exception e, Object jsonResult)
    {
        try
        {
            // 获取当前的用户
            LoginUser loginUser = SecurityUtils.getLoginUser();

            // *========数据库日志=========*//
            WaybillLog waybillLog = new WaybillLog();
            waybillLog.setStatus(BusinessStatus.SUCCESS.ordinal());
            if (loginUser != null)
            {
                waybillLog.setOperName(loginUser.getUsername());
            }
            if (e != null)
            {
                waybillLog.setStatus(BusinessStatus.FAIL.ordinal());
                waybillLog.setErrorMsg(StringUtils.substring(e.getMessage(), 0, 2000));
            }
            // 设置请求方式
            waybillLog.setRequestMethod(ServletUtils.getRequest().getMethod());
            // 处理设置注解上的参数
            getControllerMethodDescription(joinPoint, log, waybillLog, jsonResult);
            // 保存数据库
            AsyncManager.me().execute(AsyncFactory.waybillLog(waybillLog));
        }
        catch (Exception exp)
        {
            // 记录本地异常日志
            logger.error("异常信息:{}", exp.getMessage());
            exp.printStackTrace();
        }
        finally
        {
            System.out.println("");
        }
    }

    /**
     * 获取注解中对方法的描述信息 用于Controller层注解
     *
     * @param log 日志
     * @param waybillLog 操作日志
     * @throws Exception
     */
    public void getControllerMethodDescription(JoinPoint joinPoint, AllWaybillLog log, WaybillLog waybillLog, Object jsonResult) throws Exception
    {
        // 获取参数的信息，传入到数据库中。
        setRequestValue(joinPoint, waybillLog);

        // 是否需要保存response，参数和值
        if (StringUtils.isNotNull(jsonResult))
        {
            String s = JSON.toJSONString(jsonResult);
            waybillLog.setJsonResult(StringUtils.substring(s, 0, 2000));
        }
    }

    /**
     * 获取请求的参数，放到log中
     *
     * @param waybillLog 操作日志
     * @throws Exception 异常
     */
    private void setRequestValue(JoinPoint joinPoint, WaybillLog waybillLog) throws Exception
    {
        Map<?, ?> paramsMap = ServletUtils.getParamMap(ServletUtils.getRequest());
        String requestMethod = waybillLog.getRequestMethod();
        if (StringUtils.isEmpty(paramsMap) && HttpMethod.POST.name().equals(requestMethod)) {
            String params = argsArrayToString(joinPoint.getArgs());
            waybillLog.setOperParam(StringUtils.substring(params, 0, 2000));
        }
        else
        {
            waybillLog.setOperParam(StringUtils.substring(JSON.toJSONString(paramsMap), 0, 2000));
        }
    }

    /**
     * 参数拼装
     */
    private String argsArrayToString(Object[] paramsArray)
    {
        String params = "";
        if (paramsArray != null && paramsArray.length > 0)
        {
            for (Object o : paramsArray)
            {
                if (StringUtils.isNotNull(o) && !isFilterObject(o))
                {
                    try
                    {
                        String jsonObj = JSON.toJSONString(o);
                        params += jsonObj.toString() + " ";
                    }
                    catch (Exception e)
                    {
                    }
                }
            }
        }
        return params.trim();
    }

    /**
     * 判断是否需要过滤的对象。
     *
     * @param o 对象信息。
     * @return 如果是需要过滤的对象，则返回true；否则返回false。
     */
    @SuppressWarnings("rawtypes")
    public boolean isFilterObject(final Object o)
    {
        Class<?> clazz = o.getClass();
        if (clazz.isArray())
        {
            return clazz.getComponentType().isAssignableFrom(MultipartFile.class);
        }
        else if (Collection.class.isAssignableFrom(clazz))
        {
            Collection collection = (Collection) o;
            for (Object value : collection)
            {
                return value instanceof MultipartFile;
            }
        }
        else if (Map.class.isAssignableFrom(clazz))
        {
            Map map = (Map) o;
            for (Object value : map.entrySet())
            {
                Map.Entry entry = (Map.Entry) value;
                return entry.getValue() instanceof MultipartFile;
            }
        }
        return o instanceof MultipartFile || o instanceof HttpServletRequest || o instanceof HttpServletResponse
                || o instanceof BindingResult;
    }
}
