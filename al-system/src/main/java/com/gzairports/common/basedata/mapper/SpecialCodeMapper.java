package com.gzairports.common.basedata.mapper;


import com.gzairports.common.basedata.domain.BaseSpecialCode;
import com.gzairports.common.basedata.domain.query.SpecialCodeQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 货物特殊处理代码Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Mapper
public interface SpecialCodeMapper {

    /**
     * 查询货物特殊处理代码列表
     *
     * @param query 查询参数
     * @return 货物特殊处理代码列表
     */
    List<BaseSpecialCode> selectSpecialCodeList(SpecialCodeQuery query);

    /**
     * 根据货物特殊处理代码查询货物特殊处理
     *
     * @param code 货物特殊处理代码
     * @return 结果
     */
    BaseSpecialCode selectSpecialByCode(String code);

    /**
     * 新增货物特殊处理代码
     *
     * @param specialCode 货物特殊处理代码
     * @return 结果
     */
    int insertSpecialCode(BaseSpecialCode specialCode);

    /**
     * 修改货物特殊处理代码
     *
     * @param specialCode 货物特殊处理代码
     * @return 结果
     */
    int updateSpecialCode(BaseSpecialCode specialCode);

    /**
     * 删除货物特殊处理代码
     *
     * @param ids 货物特殊处理代码id集合
     * @return 结果
     */
    int delSpecialCode(Long[] ids);

    /**
     * 根据id集合查询特货代码
     *
     * @param specialId id集合
     * @return 货物特殊处理代码列表
     */
    List<String> selectListById(@Param("specialId") List<Long> specialId);

    /**
     * 查询特货代码列表
     * @return 特货代码列表
     */
    List<String> selectCodeList();

    /**
     * 查询特货代码对应的中文描述
     * @return 中文描述
     */
    String selectChineseDescriptionBySpecialCargoCode(String specialCargoCode);

}
