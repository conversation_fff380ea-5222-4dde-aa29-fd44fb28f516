package com.gzairports.wl.charge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzairports.common.basedata.domain.BaseAirportCode;
import com.gzairports.common.basedata.domain.BaseCargoCode;
import com.gzairports.common.basedata.domain.BaseCityCode;
import com.gzairports.common.basedata.mapper.AirportCodeMapper;
import com.gzairports.common.basedata.mapper.CargoCodeMapper;
import com.gzairports.common.basedata.mapper.CityCodeMapper;
import com.gzairports.common.exception.CustomException;
import com.gzairports.wl.charge.domain.FreightRate;
import com.gzairports.wl.charge.domain.FreightRateCustom;
import com.gzairports.wl.charge.domain.FreightRateCustomItem;
import com.gzairports.wl.charge.domain.FreightRateItem;
import com.gzairports.wl.charge.domain.query.FreightRateItemQuery;
import com.gzairports.wl.charge.domain.vo.FreightRateItemVO;
import com.gzairports.wl.charge.domain.vo.FreightRateVO;
import com.gzairports.wl.charge.mapper.FreightRateItemMapper;
import com.gzairports.wl.charge.mapper.FreightRateMapper;
import com.gzairports.wl.charge.service.FreightRateItemService;
import com.gzairports.common.core.domain.model.LoginUser;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 公布运价条目service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@Service
public class FreightRateItemServiceImpl extends ServiceImpl<FreightRateItemMapper, FreightRateItem> implements FreightRateItemService {

    @Autowired
    private FreightRateItemMapper rateItemMapper;

    @Autowired
    private FreightRateMapper rateMapper;


    /**
     * 查询运价条目详情
     *
     * @param id 运价条目主键
     * @return 运价条目详情
     */
    @Override
    public FreightRateItem selectRateItemById(Long id) {
        FreightRateItem freightRateItem = rateItemMapper.selectById(id);
        FreightRateVO freightRateVO = rateMapper.selectRateById(freightRateItem.getRateId());
        freightRateItem.setRateName(freightRateVO.getRateName());
        return freightRateItem;
    }

    /**
     * 新增运价条目
     *
     * @param item 运价条目
     * @return 结果
     */
    @Override
    public int insertRateItem(FreightRateItem item) {
        FreightRateItem rateItem = rateItemMapper.selectOne(new QueryWrapper<FreightRateItem>()
                .eq("rate_id",item.getRateId())
                .eq("departure_city", item.getDepartureCity())
                .eq("destination_city", item.getDestinationCity())
                .eq("dept_id",SecurityUtils.getHighParentId())
                .eq("airline", item.getAirline())
                .eq("is_delete",0));
        if (rateItem != null){
            throw new CustomException("已存在相同运价条目数据");
        }
        item.setCreateBy(SecurityUtils.getUsername());
        item.setCreateTime(DateUtils.getNowDate());
        item.setDeptId(SecurityUtils.getHighParentId());
        return rateItemMapper.insert(item);
    }

    /**
     * 修改运价条目
     *
     * @param item 运价条目
     * @return 结果
     */
    @Override
    public int updateRateItem(FreightRateItem item) {
        item.setUpdateBy(SecurityUtils.getUsername());
        item.setUpdateTime(DateUtils.getNowDate());
        return rateItemMapper.updateById(item);
    }

    /**
     * 删除运价条目
     *
     * @param id 运价条目主键
     * @return 结果
     */
    @Override
    public int removeRateItem(Long id) {
        return rateItemMapper.removeRateItem(id);
    }

    /**
     * 查询运价条目列表
     *
     * @param query 运价条目查询参数
     * @return 结果
     */
    @Override
    public List<FreightRateItemVO> selectList(FreightRateItemQuery query) {
//        if(query.getDepartureCity() != null){
//            List<BaseCityCode> baseAirportCodes = cityCodeMapper.selectCityListByName(query.getDepartureCity());
//            List<String> sourcePort = new ArrayList<>();
//            for (BaseCityCode baseAirportCode : baseAirportCodes) {
//                sourcePort.add(baseAirportCode.getCode());
//            }
//            query.setSourcePort(sourcePort);
//        }
//        if(query.getDestinationCity() != null){
//            List<BaseCityCode> baseAirportCode1 = cityCodeMapper.selectCityListByName(query.getDestinationCity());
//            List<String> desPort = new ArrayList<>();
//            for (BaseCityCode baseAirportCode : baseAirportCode1) {
//                desPort.add(baseAirportCode.getCode());
//            }
//            query.setDesPort(desPort);
//        }
        List<FreightRateItemVO> freightRateItemVOS = rateItemMapper.selectItemList(query);
        freightRateItemVOS.forEach(item -> {
            if((item.getStartTime().before(new Date()) || item.getStartTime().equals(new Date())) &&
                    (item.getEndTime().after(new Date()) || item.getEndTime().equals(new Date()))){
                item.setIsEffective(1);
            }else{
               item.setIsEffective(0);
            }
        });
        return freightRateItemVOS;
    }

    @Override
    public void delAll(Long[] ids) {
        if (ids != null){
            Date date = new Date();
            Long id = ids[0];
            FreightRateItem rateItem = rateItemMapper.selectById(id);
            FreightRate freightRate = rateMapper.selectById(rateItem.getRateId());
            if (freightRate.getEffectiveDate().before(date) && freightRate.getExpirationDate().after(date)){
                throw new CustomException("在有效期内的运价不能删除");
            }
            int i = rateItemMapper.deleteBatchIds(Arrays.asList(ids));
        }
    }
}
