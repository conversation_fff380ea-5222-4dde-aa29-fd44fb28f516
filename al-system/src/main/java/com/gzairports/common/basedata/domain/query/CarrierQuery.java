package com.gzairports.common.basedata.domain.query;

import lombok.Data;

import java.util.List;

/**
 * 承运人管理查询参数
 *
 * <AUTHOR>
 * @date 2024-02-01
 */
@Data
public class CarrierQuery {

    /** 主键id */
    private List<Long> ids;

    /** 城市代码 */
    private String code;

    /** 中文名称 */
    private String chineseName;

    /** 英文名称 */
    private String englishName;

    /** 承运人简称 */
    private String abbreviation;

    /** 运单前缀 */
    private String prefix;

    /** 是否本场承运 */
    private Integer isShipment;
}
