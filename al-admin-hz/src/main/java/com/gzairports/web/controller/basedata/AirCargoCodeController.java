package com.gzairports.web.controller.basedata;

import com.gzairports.common.basedata.domain.BaseAirCargoCode;
import com.gzairports.common.basedata.domain.query.AirCargoCodeQuery;
import com.gzairports.common.annotation.Log;
import com.gzairports.common.core.controller.BaseController;
import com.gzairports.common.core.domain.AjaxResult;
import com.gzairports.common.core.page.TableDataInfo;
import com.gzairports.common.enums.BusinessType;
import com.gzairports.common.utils.poi.ExcelUtil;
import com.gzairports.common.basedata.service.IAirCargoCodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 航空公司货品代码Controller
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@RestController
@RequestMapping("/base/airCargoCode")
@Api(tags = "航空公司货品代码数据接口")
public class AirCargoCodeController extends BaseController {

    @Autowired
    private IAirCargoCodeService airCargoCodeService;

    /**
     * 查询航空公司货品代码列表
     */
    /*@PreAuthorize("@ss.hasPermi('base:airCargoCode:airCargoCodeList')")*/
    @GetMapping("/airCargoCodeList")
    @ApiOperation(value = "查询航空公司货品代码列表")
    public TableDataInfo airCargoCodeList(AirCargoCodeQuery query)
    {
        startPage();
        List<BaseAirCargoCode> list = airCargoCodeService.selectAirCargoCodeList(query);
        return getDataTable(list);
    }

    /**
     * 导出航空公司货品代码
     */
    @PreAuthorize("@ss.hasPermi('base:airCargoCode:exportAirCargoCode')")
    @Log(title = "导出航空公司货品代码", businessType = BusinessType.EXPORT)
    @PostMapping("/exportAirCargoCode")
    @ApiOperation(value = "导出航空公司货品代码")
    public void exportAirCargoCode(HttpServletResponse response, AirCargoCodeQuery query)
    {
        List<BaseAirCargoCode> list = airCargoCodeService.selectAirCargoCodeList(query);
        ExcelUtil<BaseAirCargoCode> util = new ExcelUtil<BaseAirCargoCode>(BaseAirCargoCode.class);
        util.exportExcel(response, list, "航空公司货品代码");
    }


    /**
     * 导出航空公司货品代码下载模板
     */
    @PreAuthorize("@ss.hasPermi('base:airCargoCode:exportAirCargoCode')")
    @Log(title = "导出航空公司货品代码", businessType = BusinessType.EXPORT)
    @PostMapping("/exportAirCargoCodeNull")
    @ApiOperation(value = "导出航空公司货品代码模板")
    public void exportAirCargoCodeNull(HttpServletResponse response)
    {
        List<BaseAirCargoCode> list = new ArrayList<>();
        ExcelUtil<BaseAirCargoCode> util = new ExcelUtil<BaseAirCargoCode>(BaseAirCargoCode.class);
        util.exportExcel(response, list, "航空公司货品代码模板");
    }

    /**
     * 导入航空公司货品代码
     */
    @PreAuthorize("@ss.hasPermi('base:airCargoCode:importAirCargoCode')")
    @Log(title = "导入航空公司货品代码", businessType = BusinessType.IMPORT)
    @PostMapping("/importAirCargoCode")
    @ApiOperation(value = "导入航空公司货品代码")
    public AjaxResult importAirCargoCode(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<BaseAirCargoCode> util = new ExcelUtil<BaseAirCargoCode>(BaseAirCargoCode.class);
        List<BaseAirCargoCode> airCargoCodes = util.importExcel(file.getInputStream());
        String message = airCargoCodeService.importAirCargoCode(airCargoCodes, updateSupport);
        return success(message);
    }

    /**
     * 新增航空公司货品代码
     */
    @PreAuthorize("@ss.hasPermi('base:airCargoCode:addAirCargoCode')")
    @Log(title = "新增航空公司货品代码", businessType = BusinessType.INSERT)
    @PostMapping("/addAirCargoCode")
    @ApiOperation(value = "新增航空公司货品代码")
    public AjaxResult addAirCargoCode(@RequestBody BaseAirCargoCode airCargoCode)
    {
        return toAjax(airCargoCodeService.addAirCargoCode(airCargoCode));
    }

    /**
     * 修改航空公司货品代码
     */
    @PreAuthorize("@ss.hasPermi('base:airCargoCode:editAirCargoCode')")
    @Log(title = "修改航空公司货品代码", businessType = BusinessType.UPDATE)
    @PostMapping("/editAirCargoCode")
    @ApiOperation(value = "修改航空公司货品代码")
    public AjaxResult editAirCargoCode(@RequestBody BaseAirCargoCode airCargoCode)
    {
        return toAjax(airCargoCodeService.editAirCargoCode(airCargoCode));
    }

    /**
     * 删除航空公司货品代码
     */
    @PreAuthorize("@ss.hasPermi('base:airCargoCode:delAirCargoCode')")
    @Log(title = "删除航空公司货品代码", businessType = BusinessType.UPDATE)
    @GetMapping("/delAirCargoCode/{ids}")
    @ApiOperation(value = "删除航空公司货品代码")
    public AjaxResult delAirCargoCode(@PathVariable("ids") Long[] ids)
    {
        return toAjax(airCargoCodeService.delAirCargoCode(ids));
    }
}
