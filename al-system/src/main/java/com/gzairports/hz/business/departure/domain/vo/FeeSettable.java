package com.gzairports.hz.business.departure.domain.vo;

import java.math.BigDecimal;

public interface FeeSettable {
    void setProcessingFee(BigDecimal fee);
    void setRefrigerationFee(BigDecimal fee);
    void setHandlingFee(BigDecimal fee);
    void setCableCharge(BigDecimal fee);
    void setForkliftCharge(BigDecimal fee);
    void setDiffServiceCharge(BigDecimal fee);

    default BigDecimal getProcessingFee() { return BigDecimal.ZERO; }
    default BigDecimal getRefrigerationFee() { return BigDecimal.ZERO; }
    default BigDecimal getHandlingFee() { return BigDecimal.ZERO; }
    default BigDecimal getCableCharge() { return BigDecimal.ZERO; }
    default BigDecimal getForkliftCharge() { return BigDecimal.ZERO; }
    default BigDecimal getDiffServiceCharge() { return BigDecimal.ZERO; }
}
