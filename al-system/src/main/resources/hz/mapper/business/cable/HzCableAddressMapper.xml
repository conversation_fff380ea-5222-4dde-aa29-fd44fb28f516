<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzairports.hz.business.cable.mapper.HzCableAddressMapper">
    
    <resultMap type="HzCableAddress" id="HzCableAddressResult">
        <result property="id"    column="id"    />
        <result property="airportCode"    column="airport_code"    />
        <result property="airlinesCode"    column="airlines_code"    />
        <result property="cableAddress"    column="cable_address"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="isDel"    column="is_del"    />
        <result property="caacAddress"    column="caac_address"    />
        <result property="emailAddress"    column="email_address"    />
        <result property="mqQueue"    column="mq_queue"    />
        <result property="ftpList"    column="ftp_list"    />
        <result property="interactionTypes"    column="interaction_types"    />
    </resultMap>

    <sql id="selectHzCableAddressVo">
        select id, airport_code, airlines_code, cable_address, create_time, create_by, update_time, update_by, is_del, remark,
               caac_address, email_address, mq_queue, ftp_list, interaction_types
        from hz_cable_address
    </sql>

    <select id="selectHzCableAddressList" parameterType="HzCableAddress" resultMap="HzCableAddressResult">
        <include refid="selectHzCableAddressVo"/>
        <where>
            and is_del = 0
            <if test="airportCode != null  and airportCode != ''"> and airport_code = #{airportCode}</if>
            <if test="airlinesCode != null  and airlinesCode != ''"> and airlines_code = #{airlinesCode}</if>
            <if test="cableAddress != null  and cableAddress != ''"> and cable_address = #{cableAddress}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectHzCableAddressById" parameterType="Long" resultMap="HzCableAddressResult">
        <include refid="selectHzCableAddressVo"/>
        where id = #{id}
    </select>
    <select id="selectAddressByCarrier" resultType="com.gzairports.hz.business.cable.domain.HzCableAddress">
        <include refid="selectHzCableAddressVo"/>
        where is_del = 0
        <if test="carriers != null and carriers.size() > 0">
            and airlines_code in
            <foreach collection="carriers" item="carrier" open="(" separator="," close=")">
                #{carrier}
            </foreach>
        </if>
    </select>
    <select id="selectAddressByAirportCode" resultType="com.gzairports.hz.business.cable.domain.HzCableAddress">
        <include refid="selectHzCableAddressVo"/>
        where is_del = 0
        <if test="desPortList != null and desPortList.size() > 0">
            and airport_code in
            <foreach collection="desPortList" item="desPort" open="(" separator="," close=")">
                #{desPort}
            </foreach>
        </if>
    </select>
    <select id="selectAddress" resultType="com.gzairports.hz.business.cable.domain.HzCableAddress">
        <include refid="selectHzCableAddressVo"/>
        where is_del = 0
        <if test="cableAddress != null and cableAddress.size() > 0">
            and cable_address in
            <foreach collection="cableAddress" item="cable" open="(" separator="," close=")">
                #{cable.receiveAddress}
            </foreach>
        </if>
        <if test="caacAddress != null and caacAddress.size() > 0">
            and caac_address in
            <foreach collection="caacAddress" item="caac" open="(" separator="," close=")">
                #{caac.receiveAddress}
            </foreach>
        </if>
    </select>

    <select id="newSelectHzCableAddressList" resultType="com.gzairports.hz.business.cable.domain.query.TypeAndAddressQuery">
        SELECT
            CASE
                WHEN cable_address IS NOT NULL AND cable_address != '' THEN cable_address
                ELSE caac_address
                END AS receiveAddress,
            CASE
                WHEN cable_address IS NOT NULL AND cable_address != '' THEN 0
                ELSE 1
                END AS type
        FROM hz_cable_address;
    </select>

    <insert id="insertHzCableAddress" parameterType="HzCableAddress">
        insert into hz_cable_address
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="airportCode != null">airport_code,</if>
            <if test="airlinesCode != null">airlines_code,</if>
            <if test="cableAddress != null and cableAddress != ''">cable_address,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDel != null">is_del,</if>
            <if test="remark != null">remark,</if>
            <if test="caacAddress != null">caac_address,</if>
            <if test="emailAddress != null">email_address,</if>
            <if test="mqQueue != null">mq_queue,</if>
            <if test="ftpList != null">ftp_list,</if>
            <if test="interactionTypes != null">interaction_types,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="airportCode != null">#{airportCode},</if>
            <if test="airlinesCode != null">#{airlinesCode},</if>
            <if test="cableAddress != null and cableAddress != ''">#{cableAddress},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="remark != null">#{remark},</if>
            <if test="caacAddress != null">#{caacAddress},</if>
            <if test="emailAddress != null">#{emailAddress},</if>
            <if test="mqQueue != null">#{mqQueue},</if>
            <if test="ftpList != null">#{ftpList},</if>
            <if test="interactionTypes != null">#{interactionTypes},</if>
         </trim>
    </insert>

    <update id="updateHzCableAddress" parameterType="HzCableAddress">
        update hz_cable_address
        <trim prefix="SET" suffixOverrides=",">
            <if test="airportCode != null">airport_code = #{airportCode},</if>
            <if test="airlinesCode != null">airlines_code = #{airlinesCode},</if>
            <if test="cableAddress != null and cableAddress != ''">cable_address = #{cableAddress},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="caacAddress != null">caac_address = #{caacAddress},</if>
            <if test="emailAddress != null">email_address = #{emailAddress},</if>
            <if test="mqQueue != null">mq_queue = #{mqQueue},</if>
            <if test="ftpList != null">ftp_list = #{ftpList},</if>
            <if test="interactionTypes != null">interaction_types = #{interactionTypes},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>