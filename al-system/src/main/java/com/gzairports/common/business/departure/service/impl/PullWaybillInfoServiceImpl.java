package com.gzairports.common.business.departure.service.impl;

import com.gzairports.common.business.departure.domain.vo.*;
import com.gzairports.common.business.departure.enums.CargoStatus;
import com.gzairports.common.business.departure.service.IImlDlvService;
import com.gzairports.common.business.departure.service.ILogAwblogService;
import com.gzairports.common.business.departure.service.ILogFeelogService;
import com.gzairports.common.business.departure.service.ITFilAwbinfoService;
import com.gzairports.common.exception.CustomException;
import com.gzairports.common.utils.DateUtils;
import com.gzairports.common.utils.StringUtils;
import com.gzairports.common.utils.spring.SpringUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by david on 2024/9/27
 */

@Service
public class PullWaybillInfoServiceImpl {

    @Autowired
    private ILogFeelogService logFeelogService;

    @Autowired
    private ITFilAwbinfoService filAwbinfoService;

    @Autowired
    private ILogAwblogService logAwblogService;

    @Autowired
    private IImlDlvService imlDlvService;

    public List<AwbinfoVo> loadAWBInfo(Date crtopetime, String billid) {
        //billid不为空，直接查运单表，如果billid为空就先查日志表
        if (crtopetime == null && StringUtils.isBlank(billid)) {
            throw new CustomException("创建时间与订单编号至少一个不为空");
        }
        //

        List<AwbinfoVo> rList = Lists.newArrayList();
        LocalDateTime localDateTime;
        //创建时间为空就只查询本月的 指定订单
        List<TFilAwbinfo> list;
        Map<String, List<LogAwblog>> awblogMap = null;
        Map<String, List<TLogFeelog>> logFeeMap = null;
        Map<String, List<TImpDlv>> dlvMap = null;
        if (StringUtils.isBlank(billid)) {
            if (crtopetime == null){
                crtopetime = new Date();
            }
            localDateTime = DateUtils.date2LocalTime(crtopetime);
            LocalDateTime startTime = localDateTime.toLocalDate().atTime(LocalTime.MIN);
            LocalDateTime endTime = localDateTime.toLocalDate().atTime(LocalTime.MAX);
            Date startDate = DateUtils.localTime2Date(startTime);
            Date endDate = DateUtils.localTime2Date(endTime);
            awblogMap = logAwblogService.listByTime(startDate, endDate);
            list = filAwbinfoService.listByScokNo(awblogMap.keySet());

            logFeeMap = logFeelogService.listByTime(startDate, endDate);
            dlvMap = imlDlvService.listByTime(startDate, endDate);
            List<TFilAwbinfo> feeAwbList = filAwbinfoService.listByScokNo(logFeeMap.keySet());
            for (TFilAwbinfo awbinfo : feeAwbList) {
                if (!awblogMap.containsKey(awbinfo.getStockno())) {
                    list.add(awbinfo);
                }
            }
        } else {
            LocalDateTime startTime = DateUtils.getStartMothTime(LocalDateTime.now());
            LocalDateTime endTime = DateUtils.getEndMothTime(LocalDateTime.now());
            if(billid.length() == 8){
                list = filAwbinfoService.listByScokNo(billid);
            }else{
                list = filAwbinfoService.selectTFilAwbinfoDateBillId(DateUtils.localTime2Date(startTime), DateUtils.localTime2Date(endTime), billid);
            }
            Set<String> stocknoSet = list.stream().map(TFilAwbinfo::getStockno).collect(Collectors.toSet());
            Set<String> billidSet = list.stream().map(TFilAwbinfo::getBillid).collect(Collectors.toSet());
            awblogMap = logAwblogService.listByStocks(stocknoSet);
            logFeeMap = logFeelogService.listByStocks(stocknoSet);
            dlvMap =imlDlvService.listByBillid(billidSet);
        }

        //已经换单的运单  运单前缀-运单号 如902-03733590
        HashMap<String, Date> changeOrderData = new HashMap<>();
        HashMap<String, String> changeOrder = new HashMap<>();

        for (TFilAwbinfo tFilAwbinfo : list) {
            AwbinfoVo vo = new AwbinfoVo();
            BeanUtils.copyProperties(tFilAwbinfo, vo);
            rList.add(vo);
            // 设置运单状态
            setStatus(tFilAwbinfo, vo, awblogMap.get(tFilAwbinfo.getStockno()), logFeeMap.get(tFilAwbinfo.getStockno()),dlvMap.get(tFilAwbinfo.getBillid()));
            if (StringUtils.isNotBlank(tFilAwbinfo.getStoreremark())) {
                changeOrderData.put(tFilAwbinfo.getStoreremark(), tFilAwbinfo.getCrtopetime());
                changeOrder.put(tFilAwbinfo.getStoreremark(), tFilAwbinfo.getBillid());
            }
        }
        //已经换单的 要 设置 已换单状态
        for (AwbinfoVo vo : rList) {
            String key = vo.getStockpre() + "-" + vo.getStockno();
            if (changeOrderData.containsKey(key)) {
                Date date = changeOrderData.get(key);
                vo.setStatus(CargoStatus.change_order.code);
                vo.setStatusDesc(CargoStatus.change_order.desc);
                vo.setStatusTime(date);
                String nowOrder = changeOrder.get(key);
                vo.setNowBillid(nowOrder);
            }

        }
        return rList;
    }

    private void setStatus(TFilAwbinfo tFilAwbinfo, AwbinfoVo vo, List<LogAwblog> logs, List<TLogFeelog> fees, List<TImpDlv> dlvList) {
        String offin = "KWE".equals(tFilAwbinfo.getSairportid()) ? "D" : "A";
        if (logs == null) {
            logs = Lists.newArrayList();
        }
        if (fees == null) {
            fees = Lists.newArrayList();
        }
        List<LogAwblog> awblogs = logs.stream().filter(a -> a.getStocktypeid().equals(tFilAwbinfo.getStocktypeid())
                && a.getStockpre().equals(tFilAwbinfo.getStockpre()))
                .collect(Collectors.toList());

        List<TLogFeelog> feelogs = fees.stream().filter(a -> a.getStocktypeid().equals(tFilAwbinfo.getStocktypeid())
                && a.getStockpre().equals(tFilAwbinfo.getStockpre()))
                .collect(Collectors.toList());

        CargoStatus.setCargoStatusByLogs(offin, vo, awblogs, feelogs,dlvList);
    }
}
