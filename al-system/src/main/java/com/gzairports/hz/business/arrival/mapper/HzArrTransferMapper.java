package com.gzairports.hz.business.arrival.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gzairports.hz.business.arrival.domain.HzArrTransfer;
import com.gzairports.hz.business.arrival.domain.query.HzArrTransferQuery;
import com.gzairports.hz.business.arrival.domain.vo.NodeFlightInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 进港交接Mapper接口
 *
 * <AUTHOR>
 * @date 2023-07-12
 */
@Mapper
public interface HzArrTransferMapper extends BaseMapper<HzArrTransfer> {

    /**
     * 进港交接列表查询
     * @param query 查询参数
     * @return 交接列表
     */
    List<HzArrTransfer> selectListByQuery(HzArrTransferQuery query);

    List<HzArrTransfer> batchSelect(@Param("keys") List<NodeFlightInfoVo> keys);
}
